const path = require('path');
module.exports = function (api) {
    api.cache(true);

    const plugins = [
        [
            '@babel/plugin-proposal-decorators',
            {
                legacy: true
            }
        ],
        [
            'babel-plugin-import',
            {
                libraryName: '@baidu/sui-icon',
                libraryDirectory: 'es/icons',
                camel2DashComponentName: true
            },
            '@baidu/sui-icon'
        ],
        [
            'babel-plugin-import',
            {
                libraryName: '@baidu/xicon-san',
                libraryDirectory: 'es/icons',
                camel2DashComponentName: false
            },
            '@baidu/xicon-san'
        ],
        '@babel/plugin-proposal-class-properties',
        [
            '@babel/plugin-proposal-private-methods',
            {
                loose: true
            }
        ],
        ['@babel/plugin-transform-private-property-in-object', {loose: true}],
        ['import', {libraryName: 'acud', style: true, libraryDirectory: 'es'}, 'acud']
    ];
    const env = {
        production: {
            plugins: ['transform-remove-console']
        }
    };

    return {
        plugins,
        env
    };
};
