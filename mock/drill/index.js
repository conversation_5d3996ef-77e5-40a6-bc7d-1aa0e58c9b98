const delay = require('mocker-api/lib/delay');

module.exports = delay({
    'GET /dc/failover/task': {
        success: true,
        status: 200,
        data: {
            totalCount: 15,
            pageNo: 1,
            pageSize: 10,
            list: [
                {
                    taskId: 'dt-xxxxxxxxxx',
                    name: 'test',
                    taskType: 'StartNow',
                    instanceType: 'dcphy',
                    instanceIds: 'dcphy-xxx',
                    instanceName: 'test',
                    taskDuration: '6h',
                    startTime: '2024-01-24 16:53:39',
                    endTime: '2024-01-24 16:53:39',
                    realDuration: '7h',
                    realStartTime: '2024-01-24 16:53:39',
                    realEndTime: '2024-01-24 16:53:39',
                }
            ]
        }
    },
    'POST /dc/failover/task/create': {
        success: true,
        status: 200,
        data: {
            id: 'dt-xxxxxxxxxx'
        }
    },
    'POST /dc/failover/task/check': {
        success: true,
        status: 200,
        data: null
    },
    'PUT /dc/failover/task/enable/:taskId': (req, res) => {
        const { taskId } = req.params;
        if (!taskId) {
            return res.status(400).json({
                success: false,
                status: 400,
                message: 'taskId is required'
            });
        }
        return res.json({
            success: true,
            status: 200,
            data: null
        });
    },
    'DELETE /dc/failover/task/:taskId': (req, res) => {
        const { taskId } = req.params;
        if (!taskId) {
            return res.status(400).json({
                success: false,
                status: 400,
                message: 'taskId is required'
            });
        }
        return res.json({
            success: true,
            status: 200,
            data: null
        });
    },
    'GET /dc/failover/task/report': {
        success: true,
        status: 200,
        data: {
            reportId: 'rpt-xxxxxxxxxx',
            taskId: 'dt-xxxxxxxxxx',
            region: 'bj',
            name: 'test',
            description: 'test',
            instanceType: 'dcphy',
            instanceIds: [
                {
                    instanceId: 'dcphy-xxx',
                    realDuration: '7h',
                    realStartTime: '2024-01-24 16:53:39',
                    realEndTime: '2024-01-24 16:53:39',
                    interfaceStatus: 'dcphy'
                }
            ]
        }
    }
}, 1500);
