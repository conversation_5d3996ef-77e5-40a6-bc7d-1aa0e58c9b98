import {test as base, expect} from '@base';
import {uuid} from 'src/utils/helper';
import {ListPage} from '../model/vpn/listPage';

const test = base.extend<{listPage: ListPage}>({
    listPage: async ({page, context}, use) => {
        const listPage = new ListPage(page, context);
        await listPage.setUp();
        return listPage;
    }
});

test('侧边栏', async ({page, listPage}: any) => {
    await expect(listPage.sidebar).toBeVisible({timeout: 20000});
    // await expect(page.getByTestId('gre_vpn_list')).toBeVisible({timeout: 40000});
});

test('创建gre私网型网关', async ({page, listPage}: any) => {
    await page.getByTestId('button-vpn_gre_create').click();
    await expect(page).toHaveURL(/\/network\/#\/vpc\/vpn\/create/i, {timeout: 1000});
    await expect(page.getByTestId('formitem-grid-row-vpn_create_gateway_type')).toBeVisible();
    await page.getByTestId('input-vpn_create_name').fill(`gre_vpn_e2e_${uuid()}`);
    await page.getByTestId('select-input-wrapper-vpn_create_select_subnet').click();
    await expect(page.getByTestId('select-popup-vpn_create_select_subnet')).toBeVisible();
    await page.getByTestId('option-vpn_create_select_subnet_option_0').click();
    await page.getByTestId('button-vpn_create_next_btn').click();

    // 勾选协议
    await page.locator('.buy-agreement').getByRole('checkbox').click();
    await page.getTestId('button-vpn_create_submit_btn').click();

    await expect(page).toHaveURL(/finance\/pay\?orderId=.*&accountId=.*&region=.*&fromService=.*$/);
});
