/**
 * @file instance.list.spec.ts
 * @desc BLB 实例列表页测试
 */

import {test as base, expect} from '@base';
import {PageUrl} from '@constants';
import {uuid} from 'src/utils/helper';
import {ListPage} from '../model/dcgw/dcgwListPage';

const test = base.extend<{
    listPage: ListPage;
}>({
    listPage: async ({page, context}, use) => {
        const listPage = new ListPage(page, context);
        await listPage.setUp();
        await use(listPage);
    }
});

// 账号需要保证有数据
// test('列表展示', async ({page, listPage}) => {
//     await expect(listPage.tableRow).toHaveCount(1, {timeout: 10000});
// });

// test('页面跳转', async ({page, listPage}) => {
//     // 跳转创建页面-点击创建按钮
//     await page.getByTestId('button-dcgw_create').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/dcgw\/create/i, {timeout: 1000});
//     await page.getByTestId('appcreatepage-applink-backto').click();
//     await expect(page).toHaveURL(PageUrl.dcgwList, {timeout: 1000});
//     // 跳转详情页-点击名称跳转
//     await page.getByTestId('dcgw_name_0').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('dcgw_name_0').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/dcgw\/detail/i, {timeout: 1000});
//     await page.goBack();
//     await expect(page).toHaveURL(PageUrl.dcgwList, {timeout: 1000});
//     // 跳转详情页-点击管理跳转
//     await page.getByTestId('button-dcgw_health_check_0').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('button-dcgw_health_check_0').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/dcgw\/hc/i, {timeout: 1000});
//     await page.goBack();
//     await expect(page).toHaveURL(PageUrl.dcgwList, {timeout: 1000});
//     // 点击vpc的名称跳转到vpc的详情
//     await page.getByTestId('dcgw_vpc_name_0').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('dcgw_vpc_name_0').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/instance\/detail/i, {timeout: 1000});
//     await page.goBack();
//     await expect(page).toHaveURL(PageUrl.dcgwList, {timeout: 1000});
//     // 点击vpc进行筛选
//     await page.getByTestId('select-dcgw_vpc_select').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('select-dcgw_vpc_select').click();
//     await page.getByTestId('option-dcgw_vpc_select1').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('option-dcgw_vpc_select1').click();
//     await expect(listPage.tableRow).toHaveCount(1, {timeout: 10000});
// });

// test('创建专线网关', async ({page, listPage}) => {
//     // test.setTimeout(120000);
//     // 跳转创建页
//     await page.getByTestId('button-dcgw_create').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/dcgw\/create/i, {timeout: 1000});
//     // 创建专线网关
//     const testName = uuid();
//     await page.getByTestId('input-dcgw_instance_name').click();
//     await page.getByTestId('input-dcgw_instance_name').fill(testName);
//     await page.getByTestId('radio-1-dcgw_checkBind').click();
//     await page.getByTestId('button-dcgw_create_submit').click();
//     await expect(page).toHaveURL(PageUrl.dcgwList, {timeout: 20000});
//     await expect(page.getByTestId('dcgw_name_1')).toBeVisible({timeout: 10000});
//     // 确认第一条数据为新创建的专线网关
//     const newDcgwName = await page.getByTestId('dcgw_name_1').textContent();
//     expect(newDcgwName?.trim()).toEqual(testName);
// });

// test('删除专线网关', async ({page, listPage}) => {
//     const testName = await page.getByTestId('dcgw_name_0').textContent();
//     await listPage.setUp();
//     // 选中第一条数据
//     await page.getByTestId('table-checkbox-0-dcgw_list').getByLabel('').check();
//     // 删除按钮点击
//     await page.getByTestId('button-dcgw_delete').click();
//     // 二次确认弹窗展示
//     await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     // 二次确认弹窗确定按钮点击
//     await page.getByRole('button', {name: '确定'}).click();
//     // 删除后列表加载完成
//     await expect(page.getByTestId('dcgw_name_0')).toBeVisible({timeout: 10000});
//     // 删除成功后列表第一条数据不是新创建的专线网关
//     const dcgwName = await page.getByTestId('dcgw_name_0').textContent();
//     expect(dcgwName?.trim()).not.toEqual(testName);
// });
