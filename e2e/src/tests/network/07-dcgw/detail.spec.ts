/**
 * @file instance.list.spec.ts
 * @desc Route 路由列表页测试
 */

import {test as base, expect} from '@base';
import {uuid} from 'src/utils/helper';
import {DetailPage} from '../model/dcgw/dcgwDetailPage';

const test = base.extend<{
    detailPage: DetailPage;
}>({
    detailPage: async ({page, context}, use) => {
        const detailPage = new DetailPage(page);
        /** 前置操作 */
        await detailPage.setUp();
        await use(detailPage);
    }
});

// test('展示详细信息', async ({page, detailPage}) => {
// await expect(page).toHaveURL(/\/network\/#\/vpc\/dcgw\/detail/i, {timeout: 1000});
// });

// test('修改名称', async ({page, detailPage}) => {
//     await page.getByTestId('dcgw-instance-name').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('popover-trigger-dcgw-instance-name-edit').getByRole('img').click();
//     await page.getByTestId('input-dcgw-instance-name-edit-input').click();
//     await page.getByTestId('input-dcgw-instance-name-edit-input').fill(`dcgw_e2e_test_${uuid()}`);
//     await page.getByTestId('button-dcgw-instance-name-edit-sub').click();
// });
