import {test as base, expect} from '@base';
import {PageUrl} from '@constants';
import {ListPage} from '../model/flowMirror/listPage';

const test = base.extend<{listPage: ListPage}>({
    listPage: async ({page, context}, use) => {
        const listPage = new ListPage(page, context);
        await listPage.setUp();
        await use(listPage);
    }
});

test('列表展示', async ({page, listPage}) => {
    expect(listPage.tableListEmpty).not.toBeVisible({timeout: 20000});
});

test('编辑镜像会话', async ({page, listPage}) => {
    await page.getByTestId('button-flowMirror_list_edit_0').click();
    await expect(page).toHaveURL(/\/vpc\/mirror\/create\?sessionId=/i, {timeout: 2000});
    await page.getByTestId('appcreatepage').waitFor({state: 'visible', timeout: 10000});
    await page.getByTestId('button-instance_submit_btn').click();
    await page.waitForTimeout(10000);
    await expect(page).toHaveURL(/\/vpc\/mirror\/list/i);
});
