/**
 * @file instance.list.spec.ts
 * @desc BLB 实例列表页测试
 */

import {test as base, expect} from '@base';
import {PageUrl} from '@constants';
import {uuid} from 'src/utils/helper';
import {ListPage} from '../model/vpc/vpcListPage';

const test = base.extend<{
    listPage: ListPage;
}>({
    listPage: async ({page, context}, use) => {
        const listPage = new ListPage(page, context);
        await listPage.setUp();
        await use(listPage);
    }
});

// 账号需要保证有数据
test('列表展示', async ({page, listPage}) => {
    await expect(listPage.tableRow).toHaveCount(3, {timeout: 10000});
});

test('侧边栏', async ({page, listPage}) => {
    expect(listPage.sidebar).toBeVisible({timeout: 10000});
    expect(listPage.sidebar.getByText('私有网络 VPC', {exact: true})).toBeVisible();
    expect(listPage.sidebar.getByText('自助问题诊断')).toBeVisible();
    expect(listPage.sidebar.getByText('网络拓扑')).toBeVisible();
    // expect(listPage.sidebar.getByTestId('app-menu-item').getByText('私有网络', {exact: true})).toBeVisible();
    expect(listPage.sidebar.getByText('子网')).toBeVisible();
    expect(listPage.sidebar.getByText('路由表')).toBeVisible();
    expect(listPage.sidebar.getByText('弹性网卡')).toBeVisible();
    expect(listPage.sidebar.getByText('服务网卡')).toBeVisible();
    expect(listPage.sidebar.getByText('高可用虚拟IP')).toBeVisible();
    expect(listPage.sidebar.getByText('安全组')).toBeVisible();
    expect(listPage.sidebar.getByText('ACL')).toBeVisible();
    expect(listPage.sidebar.getByText('参数模版')).toBeVisible();
    expect(listPage.sidebar.getByText('NAT网关')).toBeVisible();
    expect(listPage.sidebar.getByText('IPV6网关')).toBeVisible();
    expect(listPage.sidebar.getByText('VPN网关')).toBeVisible();
    expect(listPage.sidebar.getByText('对等连接')).toBeVisible();
    expect(listPage.sidebar.getByText('专线网关')).toBeVisible();
    expect(listPage.sidebar.getByText('二层网关')).toBeVisible();
    expect(listPage.sidebar.getByText('组播网关')).toBeVisible();
    expect(listPage.sidebar.getByText('专线网关')).toBeVisible();
    expect(listPage.sidebar.getByText('网络诊断服务')).toBeVisible();
    expect(listPage.sidebar.getByText('资源管理')).toBeVisible();
    expect(listPage.sidebar.getByText('标签管理')).toBeVisible();
    expect(listPage.sidebar.getByText('配额管理')).toBeVisible();
});

test('页面跳转', async ({page, listPage}) => {
    // 跳转创建页
    // await page.getByTestId('button-vpc_instance_create').click();
    // await expect(page).toHaveURL(/\/network\/#\/vpc\/instance\/create/i, {timeout: 10000});
    // await page.getByTestId('appcreatepage-applink-backto').click();
    // await expect(page).toHaveURL(PageUrl.vpcList, {timeout: 10000});
    // 跳转详情页
    // await page.getByTestId('vpc_instance_name_0').waitFor({state: 'visible', timeout: 20000});
    // await page.getByTestId('vpc_instance_name_0').click();
    // await expect(page).toHaveURL(/\/network\/#\/vpc\/instance\/detail/i, {timeout: 10000});
    // await page.goBack();
    // await expect(page).toHaveURL(PageUrl.vpcList, {timeout: 1000});
});

test('创建VPC', async ({page, listPage}) => {
    // test.setTimeout(120000);
    // // 跳转创建页
    // await page.getByTestId('button-vpc_instance_create').click();
    // await expect(page).toHaveURL(/\/network\/#\/vpc\/instance\/create/i, {timeout: 1000});
    // // 创建VPC
    // const testName = uuid();
    // await page.getByTestId('input-vpc_instance_name').click();
    // await page.getByTestId('input-vpc_instance_name').fill(testName);
    // const testCidr = '10.0.0.0/8';
    // await page.getByTestId('input-vpc_instance_cidr').click();
    // await page.getByTestId('input-vpc_instance_cidr').fill(testCidr);
    // await page.getByTestId('button-vpc_create_submit').click();
    // await expect(page).toHaveURL(PageUrl.vpcList, {timeout: 20000});
    // await expect(page.getByTestId('vpc_instance_name_0')).toBeVisible({timeout: 10000});
    // // 确认第一条数据为新创建的VPC
    // const newVpcName = await page.getByTestId('vpc_instance_name_0').textContent();
    // expect(newVpcName?.trim()).toEqual(testName);
    // // 搜索VPC
    // await page.getByPlaceholder('请输入实例名称进行搜索').click();
    // await page.getByPlaceholder('请输入实例名称进行搜索').fill(newVpcName || '');
    // await page.locator('s-search').first().click();
    // await expect(page.getByTestId('vpc_instance_name_0')).toBeVisible({timeout: 10000});
    // await expect(listPage.tableRow).toHaveCount(1, {timeout: 10000});
    // await page.getByPlaceholder('请输入实例名称进行搜索').click();
    // await page.getByPlaceholder('请输入实例名称进行搜索').fill('');
    // await page.locator('s-search').first().click();
    // await expect(page.getByTestId('vpc_instance_name_0')).toBeVisible({timeout: 10000});
    // await expect(listPage.tableRow).toHaveCount(4, {timeout: 10000});
    // // 分配辅助网段
    // page.getByTestId('button-vpc_instance_manage_0').click();
    // await expect(page.getByTestId('dialog')).toBeVisible();
    // page.getByTestId('vpc_add_auxiliary_cidr').click();
    // await page.getByTestId('input').click();
    // await page.getByTestId('input').fill('***********/24');
    // await page.getByRole('button', {name: '确定'}).click();
    // await expect(page.getByTestId('dialog')).not.toBeVisible({timeout: 10000});
    // // 分配IPv6网段
    // page.getByTestId('button-vpc_instance_allocation_ipv6_0').click();
    // // 分配成功后出现删除按钮
    // await expect(page.getByTestId('button-vpc_instance_delete_ipv6_0')).toBeVisible({timeout: 10000});
    // // 删除IPv6网段
    // page.getByTestId('button-vpc_instance_delete_ipv6_0').click();
    // // 删除弹窗展示
    // await expect(page.getByTestId('dialog')).toBeVisible();
    // await page.getByRole('button', {name: '确定'}).click();
    // await expect(page.getByTestId('dialog')).not.toBeVisible();
    // // 删除成功后出现分配IPv6网段按钮
    // page.getByTestId('button-vpc_instance_allocation_ipv6_0').click();
});

test('删除VPC', async ({page, listPage}) => {
    // const testName = await page.getByTestId('vpc_instance_name_0').textContent();
    // await listPage.setUp();
    // // 选中第一条数据
    // await page.getByTestId('table-checkbox-0-vpc_instance_list').getByLabel('').check();
    // // 删除按钮点击
    // await page.getByTestId('button-vpc_instance_delete').click();
    // // 二次确认弹窗展示
    // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
    // // 二次确认弹窗确定按钮点击
    // await page.getByRole('button', {name: '确定'}).click();
    // // 删除后列表加载完成
    // await expect(page.getByTestId('vpc_instance_name_0')).toBeVisible({timeout: 10000});
    // // 删除成功后列表第一条数据不是新创建的VPC
    // const vpcName = await page.getByTestId('vpc_instance_name_0').textContent();
    // expect(vpcName?.trim()).not.toEqual(testName);
});
