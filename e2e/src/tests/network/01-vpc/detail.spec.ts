/**
 * @file instance.list.spec.ts
 * @desc BLB 实例列表页测试
 */

import {test as base, expect} from '@base';
import {uuid} from 'src/utils/helper';
import {DetailPage} from '../model/vpc/vpcDetailPage';

const test = base.extend<{
    detailPage: DetailPage;
}>({
    detailPage: async ({page, context}, use) => {
        const detailPage = new DetailPage(page);
        /** 前置操作 */
        await detailPage.setUp();
        await use(detailPage);
    }
});

// test('修改名称', async ({page, detailPage}) => {
//     await page.getByTestId('vpc-instance-name').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('popover-trigger-vpc-instance-name-edit').getByRole('img').click();
//     await page.getByTestId('input-vpc-instance-name-edit-input').click();
//     await page.getByTestId('input-vpc-instance-name-edit-input').fill(`vpc_e2e_test_${uuid()}`);
//     await page.getByTestId('button-vpc-instance-name-edit-sub').click();
// });
