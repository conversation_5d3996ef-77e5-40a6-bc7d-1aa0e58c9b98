import {test as base, expect} from '@base';
import {uuid} from 'src/utils/helper';
import {DetailPage} from '../model/eni/eniDetailPage';

const test = base.extend<{
    detailPage: DetailPage;
}>({
    detailPage: async ({page, context}, use) => {
        const detailPage = new DetailPage(page, context);
        /** 前置操作 */
        await detailPage.setUp();
        await use(detailPage);
    }
});

test('展示实例信息', async ({page, detailPage}) => {
    // await expect(page).toHaveURL(/\/network\#\/vpc\/eni\/detail/i, {timeout: 1000});
    // await expect(page.getByTestId('eni-instance-name')).toBeVisible({timeout: 2000});
    // // 修改名称
    // await page.getByTestId('eni-instance-name').waitFor({state: 'visible', timeout: 10000});
    // await page.getByTestId('eni-instance-name').getByRole('img').click();
    // await page.getByTestId('input-eni-instance-name-edit-input').click();
    // const testName = `vpc_e2e_test_${uuid()}`;
    // await page.getByTestId('input-eni-instance-name-edit-input').fill(testName);
    // await page.getByRole('button', {name: '确定'}).click();
    // const editedName = await page.getByTestId('eni-instance-name').textContent();
    // expect(editedName).toEqual(testName);
});

test('展示IP地址', async ({page, detailPage}) => {
    // await page.getByText('IP地址').click();
    // await expect(page).toHaveURL(/\/network\#\/vpc\/eni\/ip/i, {timeout: 1000});
    // await expect(page.getByTestId('tab-nav-1')).toBeVisible({timeout: 10000});
    // await expect(page.getByTestId('tab-nav-2')).toBeVisible({timeout: 10000});
    // 添加辅助IP
    // await page.getByTestId('button-eni_add_assist_ip').click();
    // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 1000});
    // await page.getByRole('button', {name: '确定'}).click();
    // await expect(page.getByTestId('dialog')).not.toBeVisible({timeout: 1000});
    // await expect(detailPage.tableRow).toHaveCount(2);
    // 切换IPv6地址
    // await page.getByTestId('tab-nav-1').click();
    // await expect(page).toHaveURL(/\/network\#\/vpc\/eni\/ipv6/i, {timeout: 10000});
    // await expect(page.getByTestId('appdetailpage-content')).toBeVisible({timeout: 10000});
});

test('展示安全组', async ({page, detailPage}) => {
    // await page.getByText('安全组').click();
    // await expect(page).toHaveURL(/\/network\#\/vpc\/eni\/security/i, {timeout: 10000});
    // await expect(page.getByTestId('eni_security_group_rule')).toBeVisible({timeout: 10000});
    // await expect(page.getByTestId('eni_security_group_list')).toBeVisible({timeout: 10000});
    // await page.getByTestId('button-eni_security_associate').click();
    // await expect(page.getByText('您选择的需要关联安全组的弹性网卡为')).toBeVisible({timeout: 10000});
});

test('展示监控', async ({page, detailPage}) => {
    // await page.getByText('监控').click();
    // await expect(page).toHaveURL(/\/network\#\/vpc\/eni\/monitor/i, {timeout: 10000});
    // await expect(page.getByTestId('appdetailpage-content')).toBeVisible({timeout: 10000});
});
