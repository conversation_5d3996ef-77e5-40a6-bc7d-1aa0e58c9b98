import {test as base, expect} from '@base';
import {PageUrl} from '@constants';
import {uuid} from 'src/utils/helper';
import {ListPage} from '../model/eni/eniListPage';

const test = base.extend<{listPage: ListPage}>({
    listPage: async ({page, context}, use) => {
        const listPage = new ListPage(page, context);
        await listPage.setUp();
        await use(listPage);
    }
});

test('侧边栏', async ({page, listPage}) => {
    expect(listPage.sidebar).toBeVisible({timeout: 10000});
    expect(listPage.sidebar.getByText('弹性网卡')).toBeVisible();
});

test('列表展示', async ({page, listPage}) => {
    // await page.getByTestId('select-value').waitFor({state: 'visible', timeout: 10000});
    // await page.getByTestId('select-value').click();
    // await page.getByTestId('option').first().click();
    // await expect(listPage.tableRow).toHaveCount(2, {timeout: 10000});
});

test('页面跳转', async ({page, listPage}) => {
    // 跳转创建页
    // await page.getByTestId('button-eni_instance_create').waitFor({state: 'visible', timeout: 1000});
    // await page.getByTestId('button-eni_instance_create').click();
    // await expect(page).toHaveURL(/\/network\/#\/vpc\/eni\/create/i, {timeout: 10000});
    // await page.getByTestId('eni_create_cancel').waitFor({state: 'visible', timeout: 10000});
    // await page.getByTestId('eni_create_cancel').click();
    // await expect(page).toHaveURL(PageUrl.eniList, {timeout: 1000});
    // 跳转详情页
    // await page.getByTestId('eni_instance_name_0').waitFor({state: 'visible', timeout: 10000});
    // await page.getByTestId('eni_instance_name_0').click();
    // await expect(page).toHaveURL(/\/network\/#\/vpc\/eni\/detail/i, {timeout: 1000});
    // await page.goBack();
    // await expect(page).toHaveURL(PageUrl.eniList, {timeout: 1000});
    // 跳转私有网络详情页
    // await page.getByTestId('eni_instance_vpc_0').waitFor({state: 'visible', timeout: 10000});
    // await page.getByTestId('eni_instance_vpc_0').click();
    // await expect(page).toHaveURL(/\/network\/#\/vpc\/instance\/detail/i, {timeout: 1000});
    // await page.goBack();
    // await expect(page).toHaveURL(PageUrl.eniList, {timeout: 1000});
    // 跳转子网详情页
    // await page.getByTestId('eni_instance_subnet_0').waitFor({state: 'visible', timeout: 10000});
    // await page.getByTestId('eni_instance_subnet_0').click();
    // await expect(page).toHaveURL(/\/network\/#\/vpc\/subnet\/detail/i, {timeout: 1000});
    // await page.goBack();
    // await expect(page).toHaveURL(PageUrl.eniList, {timeout: 1000});
});

test('创建弹性网卡', async ({page, listPage}) => {
    test.setTimeout(140000);
    // 跳转创建页
    await page.getByTestId('button-eni_instance_create').click();
    await expect(page).toHaveURL(/\/network\/#\/vpc\/eni\/create/i, {timeout: 1000});

    // 创建弹性网卡
    // const testName = uuid();
    // await page.getByTestId('input-eni_instance_name').click();
    // await page.getByTestId('input-eni_instance_name').fill(testName);
    // await page.getByTestId('select-input-wrapper-eni_instance_subnet').click();
    // await page.getByTestId('select-option-0-eni_instance_subnet').waitFor({state: 'visible', timeout: 10000});
    // await page.getByTestId('select-option-0-eni_instance_subnet').click();
    // await page.getByTestId('select-input-wrapper-eni_instance_security').click();
    // await page.getByTestId('select-option-0-eni_instance_security').click();
    // await page.getByTestId('button-eni_create_submit').click();
    // await expect(page).toHaveURL(PageUrl.eniList, {timeout: 20000});
});

test('删除弹性网卡', async ({page, listPage}) => {
    // const firstEniId = await page.getByTestId('eni_instance_id_0').textContent();
    // await listPage.setUp();
    // await page.getByTestId('button-eni_instance_release_0').click();
    // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
    // await page.getByRole('button', {name: '确定'}).click();
    // await expect(page.getByTestId('dialog')).not.toBeVisible({timeout: 10000});
    // await expect(page.getByTestId('eni_instance_id_0')).toBeVisible({timeout: 10000});
    // const firstEniIdAfterDelete = await page.getByTestId('eni_instance_id_0').textContent({timeout: 10000});
    // expect(firstEniIdAfterDelete).not.toEqual(firstEniId);
    // 监控
    // await page.getByTestId('button-eni_instance_monitor_0').click();
    // await expect(page.getByTestId('drawer')).toBeVisible({timeout: 10000});
    // await page.getByTestId('drawer-close').click();
    // await expect(page.getByTestId('drawer')).not.toBeVisible({timeout: 10000});
    // 挂载主机
    // await page.getByTestId('button-eni_instance_mount_host_0').click();
    // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
    // await page.getByTestId('dialog-icon-close').click();
    // await expect(page.getByTestId('dialog')).not.toBeVisible({timeout: 10000});
});
