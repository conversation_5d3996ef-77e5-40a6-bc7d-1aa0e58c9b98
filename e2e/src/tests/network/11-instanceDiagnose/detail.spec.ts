import {test as base, expect} from '@base';
import {uuid} from 'src/utils/helper';
import {DetailPage} from '../model/instanceDiagnose/detailPage';
import {PageUrl} from '@constants';

const test = base.extend<{
    detailPage: DetailPage;
}>({
    detailPage: async ({page, context}, use) => {
        const detailPage = new DetailPage(page);
        /** 前置操作 */
        await detailPage.setUp();
        await use(detailPage);
    }
});

test('展示实例信息', async ({page, detailPage}) => {
    // await expect(page).toHaveURL(/\#\/vpc\/diagnosis\/detail/i, {timeout: 1000});
    // await expect(page.getByTestId('diagnosis-instance-desc')).toBeVisible({timeout: 2000});

    // // 修改描述
    // await page.getByTestId('diagnosis-instance-desc').waitFor({state: 'visible', timeout: 10000});
    // await page.getByTestId('diagnosis-instance-desc').click();
    // await page.getByTestId('input').click();
    // const testDesc = `diagnose_e2e_test_${uuid()}`;
    // await page.getByTestId('input').fill(testDesc);
    // await page.getByRole('button', {name: '确定'}).click();
    // const editedDesc = await page.getByTestId('diagnosis-instance-desc').textContent();
    // expect(editedDesc).toEqual(testDesc);
});
