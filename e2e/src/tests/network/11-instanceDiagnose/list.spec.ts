import {test as base, expect} from '@base';
import {PageUrl} from '@constants';
import {uuid} from 'src/utils/helper';
import {ListPage} from '../model/instanceDiagnose/listPage';

const test = base.extend<{listPage: ListPage}>({
    listPage: async ({page, context}, use) => {
        const listPage = new ListPage(page, context);
        await listPage.setUp();
        await use(listPage);
    }
});

test('侧边栏', async ({page, listPage}: any) => {
    expect(listPage.sidebar).toBeVisible({timeout: 10000});
    expect(listPage.sidebar.getByTestId('app-menu-item').getByText('实例诊断')).toBeVisible();
});

test('列表展示', async ({page, listPage}) => {
    await expect(listPage.tableRow).toHaveCount(2, {timeout: 10000});
});

test('页面跳转', async ({page, listPage}) => {
    // 跳转详情页
    await page.getByTestId('diagnosis_instance_name_0').waitFor({state: 'visible', timeout: 10000});
    await page.getByTestId('diagnosis_instance_name_0').click();
    await expect(page).toHaveURL(/\/network\/#\/vpc\/diagnosis\/detail/i, {timeout: 1000});
    await page.goBack();
    await expect(page).toHaveURL(PageUrl.diagnoseUrl, {timeout: 1000});
});

test('创建实例诊断', async ({page, listPage}) => {
    // 创建实例诊断
});

test('删除实例诊断', async ({page, listPage}) => {
    // const testName = await page.getByTestId('diagnosis_instance_name_0').textContent();
    // await listPage.setUp();
    // // 选中第一条数据
    // await page.getByTestId('table-checkbox-0-diagnosis_instance_list').getByLabel('').check();
    // // 删除按钮点击
    // await page.getByTestId('button-diagnosis_instance_delete').click();
    // // 二次确认弹窗展示
    // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
    // // 二次确认弹窗确定按钮点击
    // await page.getByRole('button', {name: '确定'}).click();
    // // 删除后列表加载完成
    // await expect(page.getByTestId('diagnosis_instance_name_0')).toBeVisible({timeout: 10000});
    // // 删除成功后列表第一条数据不是新创建的VPC
    // const diagnosisName = await page.getByTestId('diagnosis_instance_name_0').textContent();
    // expect(diagnosisName?.trim()).not.toEqual(testName);
});
