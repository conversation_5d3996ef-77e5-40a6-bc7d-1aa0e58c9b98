/**
 * @file instance.list.spec.ts
 * @desc Route 路由列表页测试
 */

import {test as base, expect} from '@base';
import {uuid} from 'src/utils/helper';
import {DetailPage} from '../model/route/routeDetailPage';

const test = base.extend<{
    detailPage: DetailPage;
}>({
    detailPage: async ({page, context}, use) => {
        const detailPage = new DetailPage(page, context);
        /** 前置操作 */
        await detailPage.setUp();
        await use(detailPage);
    }
});

test('展示详细信息', async ({page, detailPage}) => {});

test('修改名称', async ({page, detailPage}) => {
    // await page.getByTestId('route-instance-name').waitFor({state: 'visible', timeout: 10000});
    // await page.getByTestId('popover-trigger-route-instance-name-edit').getByRole('img').click();
    // await page.getByTestId('input-route-instance-name-edit-input').click();
    // await page.getByTestId('input-route-instance-name-edit-input').fill(`route_e2e_test_${uuid()}`);
    // await page.getByTestId('button-route-instance-name-edit-sub').click();
});
