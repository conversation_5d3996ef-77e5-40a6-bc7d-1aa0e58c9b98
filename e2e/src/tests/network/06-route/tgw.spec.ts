/**
 * @file instance.list.spec.ts
 * @desc Route 路由列表页测试
 */

import {test as base, expect} from '@base';
import {DetailPage} from '../model/route/routeBindTgw';

const test = base.extend<{
    detailPage: DetailPage;
}>({
    detailPage: async ({page, context}, use) => {
        const detailPage = new DetailPage(page);
        /** 前置操作 */
        await detailPage.setUp();
        await use(detailPage);
    }
});

// test('展示页面', async ({page, detailPage}) => {
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/route\/bindTgw/i, {timeout: 1000});
//     await expect(page.getByTestId('route-tgw-title')).toBeVisible({timeout: 2000});
// });
