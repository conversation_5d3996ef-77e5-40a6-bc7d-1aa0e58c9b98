/**
 * @file instance.list.spec.ts
 * @desc BLB 实例列表页测试
 */

import {test as base, expect} from '@base';
import {PageUrl, PAGE_URL_REG_EXP} from '@constants';
import {uuid} from 'src/utils/helper';
import {ListPage} from '../model/route/routeListPage';

const test = base.extend<{
    listPage: ListPage;
}>({
    listPage: async ({page, context}, use) => {
        const listPage = new ListPage(page, context);
        await listPage.setUp();
        await use(listPage);
    }
});

// 账号需要保证有数据
test('列表展示', async ({page, listPage}) => {
    // await expect(listPage.tableRow).toHaveCount(2, {timeout: 10000});
    await expect(page.getByTestId('route_name_0')).toBeVisible({timeout: 10000});
});

// test('侧边栏', async ({page, listPage}) => {
//     expect(listPage.sidebar).toBeVisible({timeout: 10000});
//     expect(listPage.sidebar.getByTestId('app-menu-root').getByText('路由表')).toBeVisible();
// });

// test('页面跳转', async ({page, listPage}) => {
//     // 跳转创建页面-点击创建按钮
//     await page.getByTestId('button-route_create').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/route\/create/i, {timeout: 1000});
//     await page.getByTestId('appcreatepage-applink-backto').click();
//     await expect(page).toHaveURL(PageUrl.routeList, {timeout: 1000});
//     // 跳转详情页-点击名称跳转
//     await page.getByTestId('route_name_0').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('route_name_0').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/route\/detail/i, {timeout: 1000});
//     await page.goBack();
//     await expect(page).toHaveURL(PageUrl.routeList, {timeout: 1000});
//     // 跳转详情页-点击管理跳转
//     await page.getByTestId('button-route_manage_0').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('button-route_manage_0').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/route\/detail/i, {timeout: 1000});
//     await page.goBack();
//     await expect(page).toHaveURL(PageUrl.routeList, {timeout: 1000});
//     // 点击vpc的名称跳转到vpc的详情
//     await page.getByTestId('route_vpc_name_0').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('route_vpc_name_0').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/instance\/detail/i, {timeout: 1000});
//     await page.goBack();
//     await expect(page).toHaveURL(PageUrl.routeList, {timeout: 1000});
//     // 点击vpc进行筛选
//     await page.getByTestId('select-route_vpc_select').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('select-route_vpc_select').click();
//     await page.getByTestId('option-route_vpc_select1').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('option-route_vpc_select1').click();
//     await expect(listPage.tableRow).toHaveCount(1, {timeout: 10000});

//     // 筛选完毕之后保证当前的为所在网络全部私有网络
//     await page.getByTestId('select-route_vpc_select').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('select-route_vpc_select').click();
//     await page.getByTestId('option-route_vpc_select1').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('option-route_vpc_select1').click();
//     await expect(listPage.tableRow).toHaveCount(2, {timeout: 10000});
// });

// 需要特殊的vpc才可以创建，case暂时不补充
// test('创建路由表', async ({page, listPage}) => {
//     test.setTimeout(120000);
//     // 跳转创建页
//     await page.getByTestId('button-route_create').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/route\/create/i, {timeout: 1000});

//     // 创建VPC
//     const testName = uuid();
//     await page.getByTestId('input-route_instance_name').click();
//     await page.getByTestId('input-route_instance_name').fill(testName);

//     await page.getByTestId('button-route_create_submit').click();
//     await expect(page).toHaveURL(PageUrl.routeList, {timeout: 20000});
//     await expect(page.getByTestId('route_name_0')).toBeVisible({timeout: 10000});
//     // 确认第一条数据为新创建的VPC
//     const newRouteName = await page.getByTestId('route_name_0').textContent();
//     expect(newRouteName?.trim()).toEqual(testName);
// });

// test('删除路由表', async ({page, listPage}) => {
//     const testId = await page.getByTestId('route_id_0').textContent();
//     await listPage.setUp();
//     // 点击删除按钮
//     await page.getByTestId('button-route_delete_0').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('button-route_delete_0').click();
//     // 二次确认弹窗展示
//     await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     // 二次确认弹窗确定按钮点击
//     await page.getByRole('button', {name: '确定'}).click();
//     // 删除后列表加载完成
//     await expect(page.getByTestId('route_id_0')).toBeVisible({timeout: 10000});
//     // 删除成功后列表第一条数据不是新创建的VPC
//     const vpcName = await page.getByTestId('route_id_0').textContent();
//     expect(vpcName?.trim()).not.toEqual(testId);
// });

test('绑定TGW', async ({page, listPage}) => {
    await page.getByTestId('button-route_manage_1').click();
    await expect(page).toHaveURL(PAGE_URL_REG_EXP.route.detail);
    await page.getByText('绑定TGW').click();
    await expect(page).toHaveURL(PAGE_URL_REG_EXP.route.bindTgw);
    await page.getByTestId('route-tgw-title').waitFor({state: 'visible', timeout: 1000});
    await page.getByTestId('button-route_bind_tgw').click();
    await expect(page.getByTestId('dialog')).toBeVisible();
    await page.getByTestId('button-route_bind_tgw_sure').click();
    await expect(page.getByTestId('dialog')).not.toBeVisible({timeout: 10000});
    await expect(page.getByTestId('empty-route_bind_tgw_empty')).not.toBeVisible({timeout: 10000});

    // 解绑TGW
    await page.getByTestId('button-route_unbind_tgw_0').click();
    await expect(page.getByTestId('dialog')).toBeVisible();
    await page.getByRole('button', {name: '确定'}).click();
    await expect(page.getByTestId('empty-route_bind_tgw_empty')).toBeVisible({timeout: 10000});
});
