import {test as base, expect} from '@base';
import {DetailPage} from '../model/drill/detailPage';

const test = base.extend<{
    detailPage: DetailPage;
}>({
    detailPage: async ({page, context}, use) => {
        const detailPage = new DetailPage(page, context);
        await detailPage.setUp('plan');
        await use(detailPage);
    }
});

test('展示基本信息', async ({page, detailPage}) => {
    await expect(page.getByTestId('drill_plan_detail')).toBeVisible();
});
