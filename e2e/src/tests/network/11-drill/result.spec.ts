import {test as base, expect} from '@base';
import {ListPage} from '../model/drill/listPage';
import {uuid} from 'src/utils/helper';

const test = base.extend<{
    listPage: ListPage;
}>({
    listPage: async ({page, context}, use) => {
        const listPage = new ListPage(page, context);
        await listPage.setUp('result');
        await use(listPage);
    }
});

test('列表展示', async ({page, listPage}) => {
    await expect(listPage.table).toBeVisible();
    const count = await listPage.tableRow.count();
    await expect(count).toBeGreaterThan(1);
});

test('侧边栏', async ({page, listPage}) => {
    expect(listPage.sidebar).toBeVisible({timeout: 10000});
    expect(listPage.sidebar.getByText('故障演练')).toBeVisible();
});

test('搜索演练任务', async ({page, listPage}) => {
    const searchInput = page.getByTestId('drill_result_search_input');
    const testName = uuid();
    await searchInput.fill(testName);
    await page.keyboard.press('Enter');
    const count = await listPage.tableRow.count();
    await expect(count).toEqual(0);

    // 获取第一行的演练任务名称
    const name = await page.getByTestId('drill_result_name_0').textContent();
    if (!name) {
        test.fail();
        return;
    };
    await searchInput.fill(name);
    await page.keyboard.press('Enter');
    const listCount = await listPage.tableRow.count();
    await expect(listCount).toEqual(1);
});

test('刷新列表', async ({page, listPage}) => {
    const refreshBtn = page.getByTestId('drill_result_refresh_btn');
    await refreshBtn.click();
    await expect(listPage.table).toBeVisible();
    const count = await listPage.tableRow.count();
    await expect(count).toBeGreaterThan(1);
});

test('查看报告', async ({page, listPage}) => {
    const reportBtn = page.getByTestId('drill_result_view_report_btn0');
    await reportBtn.click();
    await expect(page.getByTestId('drill_result_report_drawer')).toBeVisible();
});
