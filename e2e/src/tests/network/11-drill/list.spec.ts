import {test as base, expect} from '@base';
import {ListPage} from '../model/drill/listPage';
import {uuid} from 'src/utils/helper';

const test = base.extend<{
    listPage: ListPage;
}>({
    listPage: async ({page, context}, use) => {
        const listPage = new ListPage(page, context);
        await listPage.setUp('plan');
        await use(listPage);
    }
});

test('列表展示', async ({page, listPage}) => {
    await expect(listPage.table).toBeVisible();
    const count = await listPage.tableRow.count();
    await expect(count).toBeGreaterThan(1);
});

test('侧边栏', async ({page, listPage}) => {
    expect(listPage.sidebar).toBeVisible({timeout: 10000});
    expect(listPage.sidebar.getByText('故障演练')).toBeVisible();
});

test('创建演练任务', async ({page, listPage}) => {
    const createBtn = page.getByTestId('button-drill_plan_create');
    await expect(createBtn).toBeVisible();
    await createBtn.click();
    // 创建演练任务弹窗
    const modal = page.getByTestId('drill_plan_create_modal');
    await expect(modal).toBeVisible();

    // 填写演练任务名称
    const input = modal.getByTestId('drill_plan_input_name');
    const testName = uuid();
    await input.fill(testName);

    // 选择演练资源
    const row = await modal.locator('tr[data-row-key="0"]');
    const checkbox = row.locator('td.acud-table-selection-column input[type="checkbox"]');
    await checkbox.check();

    // 填写演练任务时长
    const durationInput = modal.getByTestId('drill_plan_input_duration');
    await durationInput.fill('2');

    // 点击确认
    const confirmBtn = modal.getByTestId('drill_plan_create_confirm_btn');
    await confirmBtn.click();

    // 断言演练任务创建成功
    await expect(modal).not.toBeVisible();

    // 等待 2 秒列表刷新
    await test.setTimeout(2000);

    // 搜索
    const searchInput = page.getByTestId('drill_plan_search_input');
    await searchInput.fill(testName);
    await page.keyboard.press('Enter');

    // 断言搜索结果数量为 1
    const count = await listPage.tableRow.count();
    await expect(count).toEqual(1);

    // 获取当前行的演练任务名称
    const name = page.getByTestId('drill_plan_name_0');
    await expect(name).toMatchText(testName);
});

test('搜索演练任务', async ({page, listPage}) => {
    const searchInput = page.getByTestId('drill_plan_search_input');
    const testName = uuid();
    await searchInput.fill(testName);
    await page.keyboard.press('Enter');
    const count = await listPage.tableRow.count();
    await expect(count).toEqual(0);

    // 获取第一行的演练任务名称
    const name = await page.getByTestId('drill_plan_name_0').textContent();
    if (!name) {
        test.fail();
        return;
    };
    await searchInput.fill(name);
    await page.keyboard.press('Enter');
    const listCount = await listPage.tableRow.count();
    await expect(listCount).toEqual(1);
});
