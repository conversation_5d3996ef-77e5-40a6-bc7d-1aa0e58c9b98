import {test as base, expect} from '@base';
import {uuid} from 'src/utils/helper';
import {DetailPage} from '../model/snic/detailPage';

const test = base.extend<{
    detailPage: DetailPage;
}>({
    detailPage: async ({page, context}, use) => {
        const detailPage = new DetailPage(page, context);
        /** 前置操作 */
        await detailPage.setUp();
        await use(detailPage);
    }
});

// test('展示实例信息', async ({page, detailPage}) => {
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/endpoint\/detail/i, {timeout: 1000});
//     await expect(page.getByTestId('endpoint-instance-name')).toBeVisible({timeout: 2000});
//     // 修改名称
//     await page.getByTestId('endpoint-instance-name').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('snic-instance-modify-name').click();
//     await page.getByTestId('input').click();
//     const testName = `vpc_e2e_test_${uuid()}`;
//     await page.getByTestId('input').fill(testName);
//     await page.getByRole('button', {name: '确定'}).click();
//     const editedName = await page.getByTestId('endpoint-instance-name').textContent();
//     expect(editedName).toEqual(testName);
// });

// test('展示安全组', async ({page, detailPage}) => {
//     await page.getByText('安全组').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/endpoint\/security/i, {timeout: 1000});
//     await expect(page.getByTestId('eni_security_group_rule')).toBeVisible({timeout: 1000});
//     await expect(page.getByTestId('eni_security_group_list')).toBeVisible({timeout: 1000});
//     await page.getByTestId('button-eni_security_associate').click();
//     await expect(page.getByText('您选择的需要关联安全组的弹性网卡为')).toBeVisible({timeout: 1000});
// });

test('展示监控', async ({page, detailPage}) => {
    // await page.getByText('监控').click();
    // await expect(page).toHaveURL(/\/network\/#\/vpc\/endpoint\/monitor/i, {timeout: 1000});
    // await expect(page.getByTestId('snic_monitor_display')).toBeVisible({timeout: 1000});
});
