import {test as base, expect} from '@base';
import {PageUrl} from '@constants';
import {uuid} from 'src/utils/helper';
import {ListPage} from '../model/snic/listPage';

const test = base.extend<{listPage: ListPage}>({
    listPage: async ({page, context}, use) => {
        const listPage = new ListPage(page, context);
        await listPage.setUp();
        await use(listPage);
    }
});

test('侧边栏', async ({page, listPage}: any) => {
    // expect(listPage.sidebar).toBeVisible({timeout: 10000});
    // expect(listPage.sidebar.getByText('服务网卡')).toBeVisible();
});

test('列表展示', async ({page, listPage}) => {
    // await page.getByTestId('select-value').waitFor({state: 'visible', timeout: 10000});
    // await page.getByTestId('select-value').click();
    // await page.getByTestId('option').first().click();
    // await expect(listPage.tableRow).toHaveCount(2, {timeout: 10000});
});

// test('页面跳转', async ({page, listPage}) => {
//     // 跳转创建页
//     await page.getByTestId('button-snic_instance_create').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/endpoint\/create/i, {timeout: 1000});
//     await page.getByTestId('appcreatepage-applink-backto').click();
//     await expect(page).toHaveURL(PageUrl.snicList, {timeout: 1000});

//     // 跳转详情页
//     await page.getByTestId('snic_instance_name_0').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('snic_instance_name_0').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/endpoint\/detail/i, {timeout: 1000});
//     await page.goBack();
//     await expect(page).toHaveURL(PageUrl.snicList, {timeout: 1000});

//     // 跳转私有网络详情页
//     await page.getByTestId('snic_instance_vpc_0').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('snic_instance_vpc_0').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/instance\/detail/i, {timeout: 1000});
//     await page.goBack();
//     await expect(page).toHaveURL(PageUrl.snicList, {timeout: 1000});

//     // 跳转子网详情页
//     await page.getByTestId('snic_instance_subnet_0').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('snic_instance_subnet_0').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/subnet\/detail/i, {timeout: 1000});
//     await page.goBack();
//     await expect(page).toHaveURL(PageUrl.snicList, {timeout: 1000});
// });

// test('创建服务网卡', async ({page, listPage}) => {
//     test.setTimeout(140000);
//     // 跳转创建页
//     await page.getByTestId('button-snic_instance_create').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/endpoint\/create/i, {timeout: 1000});

//     // 创建服务网卡
//     const testName = uuid();
//     await page.getByTestId('input-snic_instance_name').click();
//     await page.getByTestId('input-snic_instance_name').fill(testName);
//     await page.getByTestId('select-input-wrapper-snic_instance_subnet').click();
//     await page.getByTestId('select-option-0-snic_instance_subnet').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('select-option-0-snic_instance_subnet').click();
//     await page.getByTestId('button-snic_create_confirm').click();
//     await expect(page.getByTestId('button-snic_create_back_pre')).toBeVisible({timeout: 1000});
//     await page.getByRole('checkbox').check();
//     await page.getByTestId('button-snic_create_submit').click();
//     await expect(page).toHaveURL(PageUrl.billingSuccess, {timeout: 20000});
//     await page.getByText('管理控制台').click();
//     await expect(page).toHaveURL().toMatch(PageUrl.snicList, {timeout: 2000});
//     await expect(page.getByTestId('snic_instance_name_0')).toBeVisible({timeout: 10000});
//     // 确认第一条数据为新创建的VPC
//     const newVpcName = await page.getByTestId('vpc_instance_name_0').textContent();
//     expect(newVpcName?.trim()).toEqual(testName);
// });

// test('删除服务网卡', async ({page, listPage}) => {
//     const firstSnicId = await page.getByTestId('snic_instance_id_0').textContent({timeout: 10000});
//     await listPage.setUp();
//     await page.getByTestId('table-checkbox-0').click();
//     await page.getByTestId('button-snic_instance_release').click();
//     await expect(page.getByTestId('dialog')).toBeVisible({timeout: 1000});
//     await page.getByRole('button', {name: '确定'}).click();
//     await expect(page.getByTestId('dialog')).not.toBeVisible({timeout: 10000});
//     await expect(page.getByTestId('snic_instance_id_0')).toBeVisible({timeout: 10000});
//     const firstSnicIdAfterDelete = await page.getByTestId('snic_instance_id_0').textContent({timeout: 10000});
//     expect(firstSnicIdAfterDelete).not.toEqual(firstSnicId);

//     // 监控
//     await page.getByTestId('button-snic_instance_monitor_0').click();
//     await expect(page.getByTestId('drawer')).toBeVisible({timeout: 10000});
//     await page.getByTestId('drawer-close').click();
//     await expect(page.getByTestId('drawer')).not.toBeVisible({timeout: 10000});

//     // 内网带宽调整
//     await page.getByTestId('button-snic_instance_bandwidth_0').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/endpoint\/upgrade/i, {timeout: 1000});
//     await page.getByTestId('inputnumber-increase').click();
//     await page.getByRole('button', {name: '确定'}).click();
//     await expect(page).toHaveURL(PageUrl.snicList, {timeout: 1000});
// });
