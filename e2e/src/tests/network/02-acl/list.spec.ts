/**
 * @file instance.list.spec.ts
 * @desc ACL 实例列表页测试
 */

import {test as base, expect} from '@base';
import {PageUrl} from '@constants';
import {ListPage} from '../model/acl/aclListPage';

const test = base.extend<{
    listPage: ListPage;
}>({
    listPage: async ({page, context}, use) => {
        const listPage = new ListPage(page, context);
        await listPage.setUp();
        await use(listPage);
    }
});

// 账号需要保证有数据
test('列表展示', async ({page, listPage}) => {
    // await expect(listPage.tableRow).toHaveCount(2, {timeout: 10000});
});

test('侧边栏', async ({page, listPage}) => {
    expect(listPage.sidebar).toBeVisible({timeout: 10000});
    expect(listPage.sidebar.getByText('ACL')).toBeVisible();
});

test('页面跳转', async ({page, listPage}) => {
    // 跳转详情页-点击名称跳转
    await page.getByTestId('acl_name_0').waitFor({state: 'visible', timeout: 10000});
    await page.getByTestId('acl_name_0').click();
    await expect(page).toHaveURL(/\/network\/#\/vpc\/acl\/manage/i, {timeout: 1000});
    await page.goBack();
    await expect(page).toHaveURL(PageUrl.aclList, {timeout: 1000});
    // 跳转详情页-点击管理跳转
    await page.getByTestId('button-acl_manage_0').waitFor({state: 'visible', timeout: 10000});
    await page.getByTestId('button-acl_manage_0').click();
    await expect(page).toHaveURL(/\/network\/#\/vpc\/acl\/manage/i, {timeout: 1000});
    await page.goBack();
    await expect(page).toHaveURL(PageUrl.aclList, {timeout: 1000});
    // 点击vpc的名称跳转到vpc的详情
    await page.getByTestId('acl_vpc_name_0').waitFor({state: 'visible', timeout: 10000});
    await page.getByTestId('acl_vpc_name_0').click();
    await expect(page).toHaveURL(/\/network\/#\/vpc\/instance\/detail/i, {timeout: 1000});
    await page.goBack();
    await expect(page).toHaveURL(PageUrl.aclList, {timeout: 1000});
    // 点击vpc进行筛选
    await page.getByTestId('select-input-wrapper').waitFor({state: 'visible', timeout: 10000});
    await page.getByTestId('select-input-wrapper').click();
    await page.getByTestId('option').nth(2).waitFor({state: 'visible', timeout: 10000});
    await page.getByTestId('option').nth(2).click();
    await expect(listPage.tableRow).toHaveCount(1, {timeout: 10000});
});
