/**
 * @desc 普通安全组 详情页测试
 */

import {test as base, expect} from '@base';
import {uuid} from 'src/utils/helper';
import {DetailPage} from '../model/security/securityDetailPage';

const test = base.extend<{
    detailPage: DetailPage;
}>({
    detailPage: async ({page, context}, use) => {
        const detailPage = new DetailPage(page);
        /** 前置操作 */
        await detailPage.setUp();
        await use(detailPage);
    }
});

test('展示详细信息', async ({page, detailPage}) => {
    // await expect(page).toHaveURL(/\/network\/#\/vpc\/security\/detail/i, {timeout: 1000});
    // await expect(page.getByTestId('security_detail_name')).toBeVisible({timeout: 2000});
});

// test('修改名称', async ({page, detailPage}) => {
//     await page.getByTestId('security_detail_name').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('popover-content-security_detail_name_edit').getByRole('img').click();
//     await page.getByTestId('input-security_detail_name_edit_input').click();
//     await page.getByTestId('input-security_detail_name_edit_input').fill(`security_e2e_test_${uuid()}`);
//     await page.getByTestId('button-security_detail_name_edit_sub').click();
//     // 复制ID
//     await page.getByTestId('clipboard-security_detail_copy_id').click();
// });

// test('切换TAB', async ({page, detailPage}) => {
//     // 切换tab 云服务器
//     await page.getByText('关联云服务器').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/security\/detail\/bcc/i, {timeout: 1000});
//     // 绑定实例 云服务器
//     // await page.getByTestId('button-security_detail_bind_bcc').click();
//     // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     // await page.getByRole('button', {name: '确定'}).click();
//     // 解绑实例 云服务器
//     // await page.getByTestId('table-checkbox-0-security_detail_table_bcc').getByLabel('').check();
//     // await page.getByTestId('security_detail_unbind_bcc').click();
//     // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     // await page.getByRole('button', {name: '确定'}).click();
//     // 条件搜索 云服务器
//     // await page.getByPlaceholder('请输入实例名称进行搜索').click();
//     // await page.getByPlaceholder('请输入实例名称进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_bcc').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入实例ID进行搜索').click();
//     // await page.getByPlaceholder('请输入实例ID进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_bcc').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入实例内网IP进行搜索').click();
//     // await page.getByPlaceholder('请输入实例内网IP进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_bcc').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入实例公网IP进行搜索').click();
//     // await page.getByPlaceholder('请输入实例公网IP进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_bcc').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // 刷新
//     // await page.getByTestId('button-security_detail_refresh_bcc').click();
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // //切换tab 弹性网卡
//     // await page.getByText('关联弹性网卡').click();
//     // await expect(page).toHaveURL(/\/network\/#\/vpc\/security\/detail\/eni/i, {timeout: 1000});
//     // // 绑定实例
//     // await page.getByTestId('button-security_detail_bind_eni').click();
//     // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     // await page.getByRole('button', {name: '确定'}).click();
//     // // 解绑实例
//     // await page.getByTestId('table-checkbox-0-security_detail_table_eni').getByLabel('').check();
//     // await page.getByTestId('security_detail_unbind_eni').click();
//     // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     // await page.getByRole('button', {name: '确定'}).click();
//     // // 条件搜索
//     // await page.getByPlaceholder('请输入实例名称进行搜索').click();
//     // await page.getByPlaceholder('请输入实例名称进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_eni').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入实例ID进行搜索').click();
//     // await page.getByPlaceholder('请输入实例ID进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_eni').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入实例内网IP进行搜索').click();
//     // await page.getByPlaceholder('请输入实例内网IP进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_eni').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入实例公网IP进行搜索').click();
//     // await page.getByPlaceholder('请输入实例公网IP进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_eni').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // // 刷新
//     // await page.getByTestId('button-security_detail_refresh_eni').click();
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // // 服务网卡
//     // // 切换tab
//     // await page.getByText('关联服务网卡').click();
//     // await expect(page).toHaveURL(/\/network\/#\/vpc\/security\/detail\/sinc/i, {timeout: 1000});
//     // // 绑定实例
//     // await page.getByTestId('button-security_detail_bind_sinc').click();
//     // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     // await page.getByRole('button', {name: '确定'}).click();
//     // // 解绑实例
//     // await page.getByTestId('table-checkbox-0-security_detail_table_sinc').getByLabel('').check();
//     // await page.getByTestId('security_detail_unbind_sinc').click();
//     // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     // await page.getByRole('button', {name: '确定'}).click();
//     // // 条件搜索
//     // await page.getByPlaceholder('请输入实例名称进行搜索').click();
//     // await page.getByPlaceholder('请输入实例名称进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_sinc').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入实例ID进行搜索').click();
//     // await page.getByPlaceholder('请输入实例ID进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_sinc').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入实例内网IP进行搜索').click();
//     // await page.getByPlaceholder('请输入实例内网IP进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_sinc').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // // 刷新
//     // await page.getByTestId('button-security_detail_refresh_sinc').click();
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // // 弹性裸金属服务器
//     // // 切换tab
//     // await page.getByText('关联弹性裸金属服务器').click();
//     // await expect(page).toHaveURL(/\/network\/#\/vpc\/security\/detail\/bbc/i, {timeout: 1000});
//     // // 绑定实例
//     // await page.getByTestId('button-security_detail_bind_bbc').click();
//     // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     // await page.getByRole('button', {name: '确定'}).click();
//     // // 解绑实例
//     // await page.getByTestId('table-checkbox-0-security_detail_table_bbc').getByLabel('').check();
//     // await page.getByTestId('security_detail_unbind_bbc').click();
//     // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     // await page.getByRole('button', {name: '确定'}).click();
//     // // 条件搜索
//     // await page.getByPlaceholder('请输入实例名称进行搜索').click();
//     // await page.getByPlaceholder('请输入实例名称进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_bbc').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入实例ID进行搜索').click();
//     // await page.getByPlaceholder('请输入实例ID进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_bbc').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入实例内网IP进行搜索').click();
//     // await page.getByPlaceholder('请输入实例内网IP进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_bbc').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入实例公网IP进行搜索').click();
//     // await page.getByPlaceholder('请输入实例公网IP进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_bbc').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // // 刷新
//     // await page.getByTestId('button-security_detail_refresh_bbc').click();
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // // 负载均衡
//     // // 切换tab
//     // await page.getByText('关联负载均衡').click();
//     // await expect(page).toHaveURL(/\/network\/#\/vpc\/security\/detail\/blb/i, {timeout: 1000});
//     // // 绑定实例
//     // await page.getByTestId('button-security_detail_bind_blb').click();
//     // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     // await page.getByRole('button', {name: '确定'}).click();
//     // // 解绑实例
//     // await page.getByTestId('table-checkbox-0-security_detail_table_blb').getByLabel('').check();
//     // await page.getByTestId('security_detail_unbind_blb').click();
//     // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     // await page.getByRole('button', {name: '确定'}).click();
//     // // 条件搜索
//     // await page.getByPlaceholder('请输入实例名称进行搜索').click();
//     // await page.getByPlaceholder('请输入实例名称进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_blb').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入实例ID进行搜索').click();
//     // await page.getByPlaceholder('请输入实例ID进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_blb').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入实例内网IP进行搜索').click();
//     // await page.getByPlaceholder('请输入实例内网IP进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_blb').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入实例公网IP进行搜索').click();
//     // await page.getByPlaceholder('请输入实例公网IP进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_blb').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // // 刷新
//     // await page.getByTestId('button-security_detail_refresh_blb').click();
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // // 云数据库专属集群
//     // // 切换tab
//     // await page.getByText('关联云数据库专属集群').click();
//     // await expect(page).toHaveURL(/\/network\/#\/vpc\/security\/detail\/ddc/i, {timeout: 1000});
//     // // 绑定实例
//     // await page.getByTestId('button-security_detail_bind_ddc').click();
//     // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     // await page.getByRole('button', {name: '确定'}).click();
//     // // 解绑实例
//     // await page.getByTestId('table-checkbox-0-security_detail_table_ddc').getByLabel('').check();
//     // await page.getByTestId('security_detail_unbind_ddc').click();
//     // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     // await page.getByRole('button', {name: '确定'}).click();
//     // // 条件搜索
//     // await page.getByPlaceholder('请输入实例名称进行搜索').click();
//     // await page.getByPlaceholder('请输入实例名称进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_ddc').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入实例ID进行搜索').click();
//     // await page.getByPlaceholder('请输入实例ID进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_ddc').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入实例内网IP进行搜索').click();
//     // await page.getByPlaceholder('请输入实例内网IP进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_ddc').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // // 刷新
//     // await page.getByTestId('button-security_detail_refresh_ddc').click();
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // // 云数据库 Redis
//     // // 切换tab
//     // await page.getByText('关联云数据库 Redis').click();
//     // await expect(page).toHaveURL(/\/network\/#\/vpc\/security\/detail\/scs/i, {timeout: 1000});
//     // // 绑定实例
//     // await page.getByTestId('button-security_detail_bind_scs').click();
//     // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     // await page.getByRole('button', {name: '确定'}).click();
//     // // 解绑实例
//     // await page.getByTestId('table-checkbox-0-security_detail_table_scs').getByLabel('').check();
//     // await page.getByTestId('security_detail_unbind_scs').click();
//     // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     // await page.getByRole('button', {name: '确定'}).click();
//     // // 条件搜索
//     // await page.getByPlaceholder('请输入实例名称进行搜索').click();
//     // await page.getByPlaceholder('请输入实例名称进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_scs').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入实例ID进行搜索').click();
//     // await page.getByPlaceholder('请输入实例ID进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_scs').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入实例内网IP进行搜索').click();
//     // await page.getByPlaceholder('请输入实例内网IP进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_scs').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入实例公网IP进行搜索').click();
//     // await page.getByPlaceholder('请输入实例公网IP进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_scs').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // // 刷新
//     // await page.getByTestId('button-security_detail_refresh_scs').click();
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // // 云数据库RDS
//     // // 切换tab
//     // await page.getByText('关联云数据库RDS').click();
//     // await expect(page).toHaveURL(/\/network\/#\/vpc\/security\/detail\/rds/i, {timeout: 1000});
//     // // 绑定实例
//     // await page.getByTestId('button-security_detail_bind_rds').click();
//     // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     // await page.getByRole('button', {name: '确定'}).click();
//     // // 解绑实例
//     // await page.getByTestId('table-checkbox-0-security_detail_table_rds').getByLabel('').check();
//     // await page.getByTestId('security_detail_unbind_rds').click();
//     // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     // await page.getByRole('button', {name: '确定'}).click();
//     // // 条件搜索
//     // await page.getByPlaceholder('请输入实例名称进行搜索').click();
//     // await page.getByPlaceholder('请输入实例名称进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_rds').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入实例ID进行搜索').click();
//     // await page.getByPlaceholder('请输入实例ID进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_rds').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入实例内网IP进行搜索').click();
//     // await page.getByPlaceholder('请输入实例内网IP进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_rds').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入实例公网IP进行搜索').click();
//     // await page.getByPlaceholder('请输入实例公网IP进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_rds').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // // 刷新
//     // await page.getByTestId('button-security_detail_refresh_rds').click();
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // // 消息服务 for RabbitMQ
//     // // 切换tab
//     // await page.getByText('关联消息服务 for RabbitMQ').click();
//     // await expect(page).toHaveURL(/\/network\/#\/vpc\/security\/detail\/rabbitmq/i, {timeout: 1000});
//     // // 绑定实例
//     // await page.getByTestId('button-security_detail_bind_rabbitmq').click();
//     // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     // await page.getByRole('button', {name: '确定'}).click();
//     // // 解绑实例
//     // await page.getByTestId('table-checkbox-0-security_detail_table_rabbitmq').getByLabel('').check();
//     // await page.getByTestId('security_detail_unbind_rabbitmq').click();
//     // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     // await page.getByRole('button', {name: '确定'}).click();
//     // // 条件搜索
//     // await page.getByPlaceholder('请输入实例名称进行搜索').click();
//     // await page.getByPlaceholder('请输入实例名称进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_rabbitmq').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入实例ID进行搜索').click();
//     // await page.getByPlaceholder('请输入实例ID进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_rabbitmq').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入架构类型进行搜索').click();
//     // await page.getByPlaceholder('请输入架构类型进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_rabbitmq').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // // 刷新
//     // await page.getByTestId('button-security_detail_refresh_rabbitmq').click();
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // // 云数据库GaiaDB-S
//     // // 切换tab
//     // await page.getByText('关联云数据库GaiaDB-S').click();
//     // await expect(page).toHaveURL(/\/network\/#\/vpc\/security\/detail\/gaiadb/i, {timeout: 1000});
//     // // 绑定实例
//     // await page.getByTestId('button-security_detail_bind_gaiadb').click();
//     // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     // await page.getByRole('button', {name: '确定'}).click();
//     // // 解绑实例
//     // await page.getByTestId('table-checkbox-0-security_detail_table_gaiadb').getByLabel('').check();
//     // await page.getByTestId('security_detail_unbind_gaiadb').click();
//     // await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     // await page.getByRole('button', {name: '确定'}).click();
//     // // 条件搜索
//     // await page.getByPlaceholder('请输入集群名称进行搜索').click();
//     // await page.getByPlaceholder('请输入集群名称进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_gaiadb').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入集群ID进行搜索').click();
//     // await page.getByPlaceholder('请输入集群ID进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_gaiadb').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入集群内网IP进行搜索').click();
//     // await page.getByPlaceholder('请输入集群内网I进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_gaiadb').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // await page.getByPlaceholder('请输入集群公网IP进行搜索').click();
//     // await page.getByPlaceholder('请输入集群公网I进行搜索').fill('');
//     // await page.locator('search-input-security_detail_search_gaiadb').first().click();
//     // await expect(page.getByTestId('security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // // 刷新
//     // await page.getByTestId('button-security_detail_refresh_gaiadb').click();
//     // await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
// });
