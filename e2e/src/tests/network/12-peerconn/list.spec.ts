import {test as base, expect} from '@base';
import {PAGE_URL_REG_EXP} from '@constants';
import {ListPage} from '../model/peerconn/listPage';

const test = base.extend<{listPage: ListPage}>({
    listPage: async ({page, context}, use) => {
        const listPage = new ListPage(page, context);
        await listPage.setUp();
        await use(listPage);
    }
});

test('列表展示', async ({page, listPage}) => {
    await expect(page.getByTestId('applistpage')).toBeVisible({});
});

// 2024 Q4 线上case https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/tQvrZcAEPS/GxsYWwEpj5/8705d94b95bb41
test('带宽调整', async ({page, listPage}) => {
    await page.getByTestId('tablcoltgl').click();
    await page.getByTestId('tablecoltgl-item-checkbox-10').click();
    await page.getByTestId('table-checkbox-1-peerconn_list').click();
    await page.getByTestId('select-value-peerconn_list_select').click();
    await page.getByTestId('option-peerconn_list_option_0').click();
    await expect(page).toHaveURL(PAGE_URL_REG_EXP.peerconn.upgrade);
});
