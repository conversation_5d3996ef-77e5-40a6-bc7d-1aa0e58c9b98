import {test as base, expect} from '@base';
import {uuid} from 'src/utils/helper';
import {DetailPage} from '../model/privateNat/detailPage';
import {PageUrl} from '@constants';

const test = base.extend<{
    detailPage: DetailPage;
}>({
    detailPage: async ({page, context}, use) => {
        const detailPage = new DetailPage(page);
        /** 前置操作 */
        await detailPage.setUp();
        await use(detailPage);
    }
});

test('展示实例信息', async ({page, detailPage}) => {
    // await expect(page).toHaveURL(/\/network\/#\/vpc\/nat\/detail/i, {timeout: 1000});
    // await expect(page.getByTestId('nat-instance-name')).toBeVisible({timeout: 2000});
    // // 修改名称
    // await page.getByTestId('nat-instance-name').waitFor({state: 'visible', timeout: 10000});
    // await page.getByTestId('nat-instance-name').click();
    // await page.getByTestId('input').click();
    // const testName = `vpc_e2e_test_${uuid()}`;
    // await page.getByTestId('input').fill(testName);
    // await page.getByRole('button', {name: '确定'}).click();
    // const editedName = await page.getByTestId('nat-instance-name').textContent();
    // expect(editedName).toEqual(testName);
    // // 私网NAT 变配
    // await page.getByTestId('private_nat_detail_change_cu').click();
    // await expect(page).toHaveURL(PageUrl.natUpgrade, {timeout: 1000});
    // await page.getByTestId('inputnumber-increase').click();
    // await expect(page.getByTestId('button-nat_upgrade_confirm')).not.toHaveClass('s-button-disabled');
    // await page.getByTestId('button-nat_upgrade_confirm').click();
    // await expect(page.getByTestId('nat_create_back_pre')).toBeVisible({timeout: 1000});
    // await page.getByRole('checkbox').check();
    // await page.getByTestId('nat_create_submit').click();
    // await expect(page).toHaveURL(PageUrl.natList, {timeout: 20000});
});

test('展示私网NAT IP', async ({page, detailPage}) => {
    await page.getByText('NAT IP').click();
    await expect(page).toHaveURL(/\/network\/#\/vpc\/nat\/natIp/i, {timeout: 1000});
    await expect(page.getByTestId('private_nat_detail_natIp')).toBeVisible({timeout: 1000});
});

test('展示SNAT规则', async ({page, detailPage}) => {
    await page.getByText('SNAT列表').click();
    await expect(page).toHaveURL(/\/network\/#\/vpc\/nat\/snat/i, {timeout: 1000});
    await expect(page.getByTestId('nat_detail_snat')).toBeVisible({timeout: 1000});
});

test('展示DNAT规则', async ({page, detailPage}) => {
    await page.getByText('DNAT列表').click();
    await expect(page).toHaveURL(/\/network\/#\/vpc\/nat\/dnat/i, {timeout: 1000});
    await expect(page.getByTestId('nat_detail_dnat')).toBeVisible({timeout: 1000});
});

test('展示监控', async ({page, detailPage}) => {
    // await page.getByText('监控').click();
    // await expect(page).toHaveURL(/\/network\/#\/vpc\/nat\/monitor/i, {timeout: 1000});
    // await expect(page.getByTestId('nat_detail_monitor')).toBeVisible({timeout: 1000});
});
