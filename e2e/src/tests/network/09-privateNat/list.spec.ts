import {test as base, expect} from '@base';
import {PageUrl} from '@constants';
import {uuid} from 'src/utils/helper';
import {ListPage} from '../model/privateNat/listPage';

const test = base.extend<{listPage: ListPage}>({
    listPage: async ({page, context}, use) => {
        const listPage = new ListPage(page, context);
        await listPage.setUp();
        await use(listPage);
    }
});

test('侧边栏', async ({page, listPage}: any) => {
    expect(listPage.sidebar).toBeVisible({timeout: 30000});
    // expect(listPage.sidebar.getByTestId('app-menu-item').getByText('NAT网关')).toBeVisible();
});

test('列表展示', async ({page, listPage}) => {
    await page.getByText('私网NAT网关', {exact: true}).click();
    await expect(page).toHaveURL(PageUrl.privateNatList);
    // await page.getByTestId('select-value').waitFor({state: 'visible', timeout: 10000});
    // await page.getByTestId('select-value').click();
    // await page.getByTestId('option').first().click();
    // await expect(listPage.tableRow).toHaveCount(2, {timeout: 10000});

    // 监控
    // await page.getByTestId('button-nat_instance_monitor_0').click();
    // await expect(page.getByTestId('drawer')).toBeVisible({timeout: 10000});
    // await page.getByTestId('drawer-close').click();
    // await expect(page.getByTestId('drawer')).not.toBeVisible({timeout: 10000});
});

// test('页面跳转', async ({page, listPage}) => {
//     // 跳转创建页
//     await page.getByTestId('button-nat_instance_create').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/nat\/create/i, {timeout: 1000});
//     await page.getByTestId('appcreatepage-applink-backto').click();
//     await expect(page).toHaveURL(PageUrl.natList, {timeout: 1000});

//     // 跳转详情页
//     await page.getByTestId('nat_instance_name_0').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('nat_instance_name_0').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/nat\/detail/i, {timeout: 1000});
//     await page.goBack();
//     await expect(page).toHaveURL(PageUrl.natList || PageUrl.privateNatList, {timeout: 1000});

//     // 跳转私有网络详情页
//     await page.getByTestId('nat_instance_vpc_0').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('nat_instance_vpc_0').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/instance\/detail/i, {timeout: 1000});
//     await page.goBack();
//     await expect(page).toHaveURL(PageUrl.natList || PageUrl.privateNatList, {timeout: 1000});

//     // 跳转子网详情页
//     await page.getByTestId('nat_instance_subnet_0').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('nat_instance_subnet_0').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/subnet\/detail/i, {timeout: 1000});
//     await page.goBack();
//     await expect(page).toHaveURL(PageUrl.natList || PageUrl.privateNatList, {timeout: 1000});
// });

// test('创建私网NAT网关', async ({page, listPage}) => {
//     test.setTimeout(140000);
//     // 跳转创建页
//     await page.getByTestId('button-nat_instance_create').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/nat\/create/i, {timeout: 1000});

//     // 创建NAT网关
//     const testName = uuid();
//     await page.getByTestId('input-nat_instance_name').click();
//     await page.getByTestId('input-nat_instance_name').fill(testName);
//     await page.getByTestId('select-input-wrapper-nat_instance_subnet').click();
//     await page.getByTestId('option-nat_instance_subnet-0').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('option-nat_instance_subnet-0').click();
//     await page.getByTestId('button-nat_create_confirm').click();
//     await expect(page.getByTestId('button-nat_create_back_pre')).toBeVisible({timeout: 1000});
//     const checkbox = await page.getByRole('checkbox').check();
//     await page.getByTestId('button-nat_create_submit').click();
//     await expect(page).toHaveURL(PageUrl.billingSuccess, {timeout: 20000});
//     await page.getByText('管理控制台').click();
//     await expect(page).toHaveURL(PageUrl.natList, {timeout: 2000});
// });

// test('删除私网NAT网关', async ({page, listPage}) => {
//     // 删除
//     await listPage.setUp();
//     await page.getByTestId('table-filter-icon-private_nat_instance_list').hover();
//     await page.getByTestId('table-filter-menu-item-1-private_nat_instance_list').click();
//     await page.getByTestId('table-checkbox-0-private_nat_instance_list').click();
//     await page.getByText('更多').click();
//     await page.getByText('释放').click();
//     await expect(page.getByTestId('dialog')).toBeVisible({timeout: 1000});
//     await page.getByRole('button', {name: '确定'}).click();
//     await expect(listPage.tableRow).toHaveCount(0, {timeout: 10000});
// });
