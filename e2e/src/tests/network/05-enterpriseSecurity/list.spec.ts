/**
 * @desc 企业安全组列表页测试
 */

import {test as base, expect} from '@base';
import {PageUrl} from '@constants';
import {uuid} from 'src/utils/helper';
import {ListPage} from '../model/vpc/vpcListPage';

const test = base.extend<{
    listPage: ListPage;
}>({
    listPage: async ({page, context}, use) => {
        const listPage = new ListPage(page, context);
        await listPage.setUp();
        await use(listPage);
    }
});

// 账号需要保证有数据
test('列表展示', async ({page, listPage}) => {
    // await expect(listPage.tableRow).toHaveCount(2, {timeout: 10000});
});

test('侧边栏', async ({page, listPage}) => {
    expect(listPage.sidebar).toBeVisible({timeout: 10000});
    // expect(listPage.sidebar.getByTestId('app-menu-root').getByText('安全组')).toBeVisible();
});

// test('页面跳转', async ({page, listPage}) => {
//     // 跳转创建页
//     await page.getByTestId('button-enterprise_security_list_create').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/enterpriseSecurity\/create/i, {timeout: 1000});
//     await page.getByTestId('appcreatepage-applink-backto-enterprise_security_create_back').click();
//     await expect(page).toHaveURL(PageUrl.enterpriseSecurityList, {timeout: 1000});
//     // 跳转企业安全组详情页
//     await page.getByTestId('enterprise_security_list_name_0').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('enterprise_security_list_name_0').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/security\/detail/i, {timeout: 1000});
//     await page.goBack();
//     await expect(page).toHaveURL(PageUrl.enterpriseSecurityList, {timeout: 1000});
// });

// test('创建企业安全组', async ({page, listPage}) => {
//     test.setTimeout(120000);
//     // 跳转创建页
//     await page.getByTestId('button-enterprise_security_list_create').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/enterpriseSecurity\/create/i, {timeout: 1000});

//     // 创建安全组
//     const testName = uuid();
//     await page.getByTestId('input-enterprise_security_name').click();
//     await page.getByTestId('input-enterprise_security_name').fill(testName);
//     await page.getByTestId('select-enterprise_security_temp').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('select-enterprise_security_temp').click();
//     await page.getByTestId('option-enterprise_security_temp0').waitFor({state: 'visible', timeout: 10000});
//     await page.getByTestId('option-enterprise_security_temp0').click();
//     await page.getByTestId('radio-group-enterprise_security_change_rule_type').click();
//     await page.getByTestId('radio-0-enterprise_security_change_rule_type').click();
//     await page.getByTestId('button-enterprise_security_add_rule').click();
//     // 选中规则table中数据
//     await page.getByTestId('table-checkbox-0-security_table_list').getByLabel('').check();
//     await page.getByTestId('button-security_delete_rule').click();
//     // 在table中添加一条规则
//     await expect(page.getByTestId('table-tbody-tr-0')).toBeVisible();
//     await page.getByTestId('enterprise_security_add_rule_submit_0').click();
//     await page.getByTestId('enterprise_security_add_rule_cancel_0').click();
//     // 添加一条规则后，出现编辑按钮
//     await expect(page.getByTestId('enterprise_security_add_rule_edit_0')).toBeVisible();
//     await page.getByTestId('button-enterprise_security_create_submit').click();
//     await expect(page).toHaveURL(PageUrl.enterpriseSecurityList, {timeout: 20000});

//     // 预览规则
//     // 选中一条数据
//     await page.getByTestId('table-checkbox-0-enterprise_security_list').getByLabel('').check();
//     await page.getByTestId('button-enterprise_security_list_preview_rule').click();
//     await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     await page.getByRole('button', {name: '确定'}).click();
//     // 复制企业安全组
//     await page.getByTestId('button-enterprise_security_copy_0').click();
//     await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     await page.getByTestId('input-enterprise_security_copy_name_input').click();
//     await page.getByTestId('input-enterprise_security_copy_name_input').fill(testName);
//     await page.getByRole('button', {name: '确定'}).click();
//     // 删除企业安全组
//     const deleteTestName = await page.getByTestId('enterprise_security_list_name_0').textContent();
//     await page.getByTestId('button-enterprise_security_delete_0').click();
//     await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     await page.getByRole('button', {name: '确定'}).click();
//     await expect(page.getByTestId('enterprise_security_list_name_0')).toBeVisible({timeout: 10000});
//     const securityName = await page.getByTestId('enterprise_security_list_name_0').textContent();
//     expect(securityName?.trim()).not.toEqual(deleteTestName);

//     // 条件搜索
//     await page.getByPlaceholder('请输入安全组名称进行搜索').click();
//     await page.getByPlaceholder('请输入安全组名称进行搜索').fill('');
//     await page.locator('autocomplete-select').first().click();
//     await expect(page.getByTestId('enterprise_security_list_name_0')).toBeVisible({timeout: 10000});
//     await expect(listPage.tableRow).toHaveCount(1, {timeout: 10000});
//     await page.getByPlaceholder('请输入安全组ID进行搜索').click();
//     await page.getByPlaceholder('请输入安全组ID进行搜索').fill('');
//     await page.locator('autocomplete-select').first().click();
//     await expect(page.getByTestId('enterprise_security_list_name_0')).toBeVisible({timeout: 10000});
//     await expect(listPage.tableRow).toHaveCount(1, {timeout: 10000});
// });

// test('删除企业安全组', async ({page, listPage}) => {
//     const testName = await page.getByTestId('enterprise_security_list_name_0').textContent();
//     await listPage.setUp();
//     await page.getByTestId('table-checkbox-0-enterprise_security_list').getByLabel('').check();
//     await page.getByTestId('button-enterprise_security_list_delete').click();
//     await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     await page.getByRole('button', {name: '确定'}).click();
//     await expect(page.getByTestId('enterprise_security_list_name_0')).toBeVisible({timeout: 10000});
//     const securityName = await page.getByTestId('enterprise_security_list_name_0').textContent();
//     expect(securityName?.trim()).not.toEqual(testName);
// });
