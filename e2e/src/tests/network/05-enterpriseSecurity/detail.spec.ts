/**
 * @desc 企业安全组 详情页测试
 */

import {test as base, expect} from '@base';
import {uuid} from 'src/utils/helper';
import {DetailPage} from '../model/enterpriseSecurity/enterpriseSecurityDetailPage';

const test = base.extend<{
    detailPage: DetailPage;
}>({
    detailPage: async ({page, context}, use) => {
        const detailPage = new DetailPage(page);
        /** 前置操作 */
        await detailPage.setUp();
        await use(detailPage);
    }
});

// test('修改名称', async ({page, detailPage}) => {
//     await page.getByTestId('enterprise_security_detail_name').waitFor({state: 'visible', timeout: 10000});
//     // await page.getByTestId('popover-content-security_detail_name_edit').getByRole('img').click();
//     // await page.getByTestId('input-security_detail_name_edit_input').click();
//     // await page.getByTestId('input-security_detail_name_edit_input').fill(`enterprise_security_e2e_test_${uuid()}`);
//     // await page.getByTestId('button-security_detail_name_edit_sub').click();

//     // 复制ID
//     await page.getByTestId('clipboard-enterprise_security_detail_copy_id').click();
// });

// test('切换TAB', async ({page, detailPage}) => {
//     // 切换tab 云服务器
//     await page.getByText('关联云服务器').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/enterpriseSecurity\/detail\/bcc/i, {timeout: 1000});
//     // 绑定实例 云服务器
//     await page.getByTestId('button-enterprise_security_detail_bind_bcc').click();
//     await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     await page.getByRole('button', {name: '确定'}).click();
//     // 解绑实例 云服务器
//     await page.getByTestId('table-checkbox-0-enterprise_security_detail_table_bcc').getByLabel('').check();
//     await page.getByTestId('enterprise_security_detail_unbind_bcc').click();
//     await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     await page.getByRole('button', {name: '确定'}).click();
//     // 条件搜索 云服务器
//     await page.getByPlaceholder('请输入实例名称进行搜索').click();
//     await page.getByPlaceholder('请输入实例名称进行搜索').fill('');
//     await page.locator('search-input-enterprise_security_detail_search_bcc').first().click();
//     await expect(page.getByTestId('enterprise_security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     await page.getByPlaceholder('请输入实例ID进行搜索').click();
//     await page.getByPlaceholder('请输入实例ID进行搜索').fill('');
//     await page.locator('search-input-enterprise_ssecurity_detail_search_bcc').first().click();
//     await expect(page.getByTestId('enterprise_ssecurity_detail_table_name_0')).toBeVisible({timeout: 10000});
//     await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     await page.getByPlaceholder('请输入实例内网IP进行搜索').click();
//     await page.getByPlaceholder('请输入实例内网IP进行搜索').fill('');
//     await page.locator('search-input-enterprise_ssecurity_detail_search_bcc').first().click();
//     await expect(page.getByTestId('enterprise_ssecurity_detail_table_name_0')).toBeVisible({timeout: 10000});
//     await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     await page.getByPlaceholder('请输入实例公网IP进行搜索').click();
//     await page.getByPlaceholder('请输入实例公网IP进行搜索').fill('');
//     await page.locator('search-input-enterprise_ssecurity_detail_search_bcc').first().click();
//     await expect(page.getByTestId('enterprise_ssecurity_detail_table_name_0')).toBeVisible({timeout: 10000});
//     await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // 刷新
//     await page.getByTestId('button-enterprise_ssecurity_detail_refresh_bcc').click();
//     await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     //切换tab 弹性网卡
//     await page.getByText('关联弹性网卡').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/enterpriseSecurity\/detail\/eni/i, {timeout: 1000});
//     // 绑定实例
//     await page.getByTestId('button-enterprise_security_detail_bind_eni').click();
//     await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     await page.getByRole('button', {name: '确定'}).click();
//     // 解绑实例
//     await page.getByTestId('table-checkbox-0-enterprise_security_detail_table_eni').getByLabel('').check();
//     await page.getByTestId('enterprise_security_detail_unbind_eni').click();
//     await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     await page.getByRole('button', {name: '确定'}).click();
//     // 条件搜索
//     await page.getByPlaceholder('请输入实例名称进行搜索').click();
//     await page.getByPlaceholder('请输入实例名称进行搜索').fill('');
//     await page.locator('search-input-enterprise_security_detail_search_eni').first().click();
//     await expect(page.getByTestId('enterprise_security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     await page.getByPlaceholder('请输入实例ID进行搜索').click();
//     await page.getByPlaceholder('请输入实例ID进行搜索').fill('');
//     await page.locator('search-input-enterprise_security_detail_search_eni').first().click();
//     await expect(page.getByTestId('enterprise_security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     await page.getByPlaceholder('请输入实例内网IP进行搜索').click();
//     await page.getByPlaceholder('请输入实例内网IP进行搜索').fill('');
//     await page.locator('search-input-enterprise_security_detail_search_eni').first().click();
//     await expect(page.getByTestId('enterprise_security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     await page.getByPlaceholder('请输入实例公网IP进行搜索').click();
//     await page.getByPlaceholder('请输入实例公网IP进行搜索').fill('');
//     await page.locator('search-input-enterprise_security_detail_search_eni').first().click();
//     await expect(page.getByTestId('enterprise_security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // 刷新
//     await page.getByTestId('button-enterprise_security_detail_refresh_eni').click();
//     await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // 服务网卡
//     // 切换tab
//     await page.getByText('关联服务网卡').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/enterpriseSecurity\/detail\/sinc/i, {timeout: 1000});
//     // 绑定实例
//     await page.getByTestId('button-enterprise_security_detail_bind_sinc').click();
//     await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     await page.getByRole('button', {name: '确定'}).click();
//     // 解绑实例
//     await page.getByTestId('table-checkbox-0-enterprise_security_detail_table_sinc').getByLabel('').check();
//     await page.getByTestId('enterprise_security_detail_unbind_sinc').click();
//     await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     await page.getByRole('button', {name: '确定'}).click();
//     // 条件搜索
//     await page.getByPlaceholder('请输入实例名称进行搜索').click();
//     await page.getByPlaceholder('请输入实例名称进行搜索').fill('');
//     await page.locator('search-input-enterprise_security_detail_search_sinc').first().click();
//     await expect(page.getByTestId('enterprise_security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     await page.getByPlaceholder('请输入实例ID进行搜索').click();
//     await page.getByPlaceholder('请输入实例ID进行搜索').fill('');
//     await page.locator('search-input-enterprise_security_detail_search_sinc').first().click();
//     await expect(page.getByTestId('enterprise_security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     await page.getByPlaceholder('请输入实例内网IP进行搜索').click();
//     await page.getByPlaceholder('请输入实例内网IP进行搜索').fill('');
//     await page.locator('search-input-enterprise_security_detail_search_sinc').first().click();
//     await expect(page.getByTestId('enterprise_security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // 刷新
//     await page.getByTestId('button-enterprise_security_detail_refresh_sinc').click();
//     await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // 弹性裸金属服务器
//     // 切换tab
//     await page.getByText('关联弹性裸金属服务器').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/enterpriseSecurity\/detail\/bbc/i, {timeout: 1000});
//     // 绑定实例
//     await page.getByTestId('button-enterprise_security_detail_bind_bbc').click();
//     await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     await page.getByRole('button', {name: '确定'}).click();
//     // 解绑实例
//     await page.getByTestId('table-checkbox-0-enterprise_security_detail_table_bbc').getByLabel('').check();
//     await page.getByTestId('enterprise_security_detail_unbind_bbc').click();
//     await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     await page.getByRole('button', {name: '确定'}).click();
//     // 条件搜索
//     await page.getByPlaceholder('请输入实例名称进行搜索').click();
//     await page.getByPlaceholder('请输入实例名称进行搜索').fill('');
//     await page.locator('search-input-enterprise_security_detail_search_bbc').first().click();
//     await expect(page.getByTestId('enterprise_security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     await page.getByPlaceholder('请输入实例ID进行搜索').click();
//     await page.getByPlaceholder('请输入实例ID进行搜索').fill('');
//     await page.locator('search-input-enterprise_security_detail_search_bbc').first().click();
//     await expect(page.getByTestId('enterprise_security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     await page.getByPlaceholder('请输入实例内网IP进行搜索').click();
//     await page.getByPlaceholder('请输入实例内网IP进行搜索').fill('');
//     await page.locator('search-input-enterprise_security_detail_search_bbc').first().click();
//     await expect(page.getByTestId('enterprise_security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     await page.getByPlaceholder('请输入实例公网IP进行搜索').click();
//     await page.getByPlaceholder('请输入实例公网IP进行搜索').fill('');
//     await page.locator('search-input-enterprise_security_detail_search_bbc').first().click();
//     await expect(page.getByTestId('enterprise_security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // 刷新
//     await page.getByTestId('button-enterprise_security_detail_refresh_bbc').click();
//     await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // 负载均衡
//     // 切换tab
//     await page.getByText('关联负载均衡').click();
//     await expect(page).toHaveURL(/\/network\/#\/vpc\/enterpriseSecurity\/detail\/blb/i, {timeout: 1000});
//     // 绑定实例
//     await page.getByTestId('button-enterprise_security_detail_bind_blb').click();
//     await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     await page.getByRole('button', {name: '确定'}).click();
//     // 解绑实例
//     await page.getByTestId('table-checkbox-0-enterprise_security_detail_table_blb').getByLabel('').check();
//     await page.getByTestId('enterprise_security_detail_unbind_blb').click();
//     await expect(page.getByTestId('dialog')).toBeVisible({timeout: 10000});
//     await page.getByRole('button', {name: '确定'}).click();
//     // 条件搜索
//     await page.getByPlaceholder('请输入实例名称进行搜索').click();
//     await page.getByPlaceholder('请输入实例名称进行搜索').fill('');
//     await page.locator('search-input-enterprise_security_detail_search_blb').first().click();
//     await expect(page.getByTestId('enterprise_security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     await page.getByPlaceholder('请输入实例ID进行搜索').click();
//     await page.getByPlaceholder('请输入实例ID进行搜索').fill('');
//     await page.locator('search-input-enterprise_security_detail_search_blb').first().click();
//     await expect(page.getByTestId('enterprise_security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     await page.getByPlaceholder('请输入实例内网IP进行搜索').click();
//     await page.getByPlaceholder('请输入实例内网IP进行搜索').fill('');
//     await page.locator('search-input-enterprise_security_detail_search_blb').first().click();
//     await expect(page.getByTestId('enterprise_security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     await page.getByPlaceholder('请输入实例公网IP进行搜索').click();
//     await page.getByPlaceholder('请输入实例公网IP进行搜索').fill('');
//     await page.locator('search-input-enterprise_security_detail_search_blb').first().click();
//     await expect(page.getByTestId('enterprise_security_detail_table_name_0')).toBeVisible({timeout: 10000});
//     await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
//     // 刷新
//     await page.getByTestId('enterprise_button-security_detail_refresh_blb').click();
//     await expect(detailPage.tableRow).toHaveCount(1, {timeout: 10000});
// });
