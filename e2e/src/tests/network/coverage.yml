# VPC产品全量功能清单，用于计算测试用例的覆盖率
- page: '/vpc/instance/list'
  features:
      侧边栏展示: true
      region切换: true
      列表页展示: true
      跳转创建页: true
      跳转详情: true
      创建VPC: true
      删除VPC: true
      查询VPC: true
      管理辅助网段: true
      分配IPV6网段: false
      删除IPV6网段: false
      修改VPC名称: false
- page: '/vpc/instance/create'
  features:
      创建vpc: true
- page: '/vpc/instance/detail'
  features:
      详情页展示: false
      修改名称: false
      资源跳转: false

- page: '/vpc/acl/list'
  features:
      侧边栏展示: true
      region切换: true
      列表页展示: true
      切换所在网络: true
      点击ACL名称跳转详情: true
      点击VPC名称跳转VPC详情: true
      管理: true

- page: '/vpc/eni/list'
  features:
      侧边栏展示: true
      region切换: true
      列表页展示: true
      切换所在网络: true
      查询ENI: true
      删除ENI: true
      修改ENI名称: true
      监控页展示: true
      ENI挂载主机: true
      ENI卸载主机: false
- page: '/vpc/eni/create'
  features:
      跳转创建页: true
      创建ENI: true
- page: '/vpc/eni/detail'
  features:
      跳转详情页: true
      展示实例信息: true
      展示IP地址: true
      IP地址切换: true
      添加辅助IP: true
      释放辅助IP: false
      绑定弹性公网IP: false
      解绑弹性公网IP: false
      展示安全组: true
      关联安全组: false
      展示监控: false

- page: '/vpc/security/list'
  features:
      侧边栏展示: true
      region切换: true
      列表页展示: true
      跳转创建页: true
      切换所在网络: true
      点击安全组名称跳转详情页: true
      点击私有网络名称跳转详情页: true
      复制: true
      删除: true
      编辑标签: false
      预览规则: true
- page: '/vpc/security/create'
  features:
      选择所在网络: true
      输入安全组名称: true
      选择模板: true
      切换端口类型: true
      切换是否允许访问所有端口: true
      添加规则: true
      删除规则: true
      编辑规则: true
      创建安全组: true
- page: '/vpc/security/detail'
  features:
      切换详情侧边栏: true
      关联实例: true
      取消关联: true
      编辑名称: true
      复制id: true
      关联实例弹窗内table: false
- page: '/vpc/enterpriseSecurity/list'
  features:
      侧边栏展示: true
      region切换: true
      列表页展示: true
      跳转创建页: true
      点击安全组名称跳转详情页: true
      复制: true
      删除: true
      编辑标签: false
      预览规则: true
- page: '/vpc/enterpriseSecurity/create'
  features:
      输入安全组名称: true
      选择模板: true
      切换端口类型: true
      切换是否允许访问所有端口: true
      添加规则: true
      删除规则: true
      编辑规则: true
      创建安全组: true
- page: '/vpc/enterpriseSecurity/detail'
  features:
      切换详情侧边栏: true
      关联实例: true
      取消关联: true
      编辑名称: true
      复制id: true
      关联实例弹窗内table: false
- page: '/vpc/route/list'
  features:
      修改名称: false
      修改描述: false
      路由表名称跳转详情: true
      VPC名称跳转VPC详情: true
      路由表管理跳转详情: true
      创建路由表跳转: true
      路由表名称过滤: false
      删除路由表: true
      路由表下载: false
      路由表列表展示: true
- page: '/vpc/route/detail'
  features:
      修改名称: false
      修改描述: false
      添加路由条目: false
      编辑路由条目: false
      删除路由条目: false
      路由条目展示: false
      路由条目类型过滤: false
      路由条目源网段过滤: false
      路由条目下载: false
- page: '/vpc/route/create'
  features:
      创建路由表: true
- page: '/vpc/security/bindTgw'
  features:
      绑定TGW: false
      已绑定TGW的列表: false
- page: '/vpc/dcgw/list'
  features:
      修改名称: false
      修改描述: false
      专线网关名称跳转详情: true
      VPC名称跳转VPC详情: true
      专线网关释放: false
      编辑专线网关的标签: false
      绑定专线: false
      解绑专线: false
      编辑专线网关: false
      专线网关实例名称过滤: false
      专线网关下载: false
      链路探测跳转: true
      编辑资源分组: false
- page: '/vpc/dcgw/detail'
  features:
      修改名称: true
      修改描述: false
      修改出口带宽: false
- page: '/vpc/dcgw/create'
  features:
      创建专线网关: true
- page: '/vpc/dcgw/hc'
  features:
      新增链路探测的规则: false
      链路探测列表: false
- page: '/vpc/dcgw/nat'
  features:
      列表展示: false
      添加规则: false
      释放: false
- page: '/vpc/dcgw/idcnat'
  features:
      列表展示: false
      添加规则: false
      释放: false
- page: '/vpc/dcgw/idcdnat'
  features:
      列表展示: false
      添加规则: false
      释放: false
- page: '/vpc/dcgw/monitor'
  features:
      监控展示: false
      展示监控: true
- page: '/vpc/endpoint/list'
  features:
      侧边栏展示: true
      region切换: true
      列表页展示: true
      切换所在网络: true
      创建服务网卡: true
      查询服务网卡: false
      删除服务网卡: true
      监控页展示: true
      内网带宽调整: true
      编辑资源分组: false
- page: '/vpc/endpoint/create'
  features:
      跳转创建页: true
      创建服务网卡: true
- page: '/vpc/endpoint/detail'
  features:
      跳转详情页: true
      实例信息展示: true
      变更名称: true
      安全组展示: true
      关联安全组: false
      监控页展示: true
- page: '/vpc/nat/list'
  features:
      侧边栏展示: true
      region切换: true
      列表页展示: true
      切换所在网络: true
      查询NAT网关: false
      删除NAT网关: true
      监控页展示: true
      设置SNAT: true
      设置DNAT: true
      绑定公网IP: false
      解绑: false
      网关升级: false
      编辑资源分组: false
- page: '/vpc/nat/create'
  features:
      跳转创建页: true
      创建NAT网关: true
- page: '/vpc/nat/detail'
  features:
      跳转详情页: true
      变更名称: true
      切换SNAT列表: true
      添加SNAT条目: false
      删除SNAT条目: false
      切换DNAT列表: true
      添加DNAT条目: false
      删除DNAT条目: false
      监控页展示: true
      网关流控展示: false
- page: '/vpc/privateNat/list'
  features:
      侧边栏展示: true
      region切换: true
      列表页展示: true
      切换所在网络: true
      查询NAT网关: false
      删除NAT网关: true
      监控页展示: true
      设置SNAT: true
      设置DNAT: true
      性能容量变配: false
      编辑资源分组: false
- page: '/vpc/nat/create'
  features:
      跳转创建页: true
      创建NAT网关: true
- page: '/vpc/nat/detail'
  features:
      跳转详情页: true
      变更名称: true
      切换SNAT列表: true
      添加SNAT条目: false
      删除SNAT条目: false
      切换DNAT列表: true
      添加DNAT条目: false
      删除DNAT条目: false
      监控页展示: true
- page: '/dc/failover/plan'
  features:
      列表展示: true
      侧边栏展示: true
      创建演练任务: true
      搜索演练任务: true
      编辑演练任务: false
      删除演练任务: false
      开始演练任务: false
- page: '/dc/failover/result'
  features:
      列表展示: true
      侧边栏展示: true
      刷新列表: true
      搜索演练任务: true
      查看报告: true
      删除演练任务: false
- page: '/dc/failover/detail'
  features:
      展示基本信息: true
      展示演练资源实例列表: false
      展示报告记录列表: false
