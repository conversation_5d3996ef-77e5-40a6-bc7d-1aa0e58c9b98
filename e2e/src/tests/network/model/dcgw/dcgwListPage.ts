/**
 * @file AccessListPage
 * @desc 专线网关列表模型
 */
import {expect, type Locator, type BrowserContext, type Page} from '@playwright/test';
import {BasePage as BasePageModel} from '@baidu/cbt-playwright-utils';
import {Text, SANBOX_VPC} from '@constants';
import url from 'url';

export class ListPage extends BasePageModel {
    /** 页面地址 */
    readonly url: string;
    readonly sidebar: Locator;
    readonly content: Locator;
    readonly table: Locator;
    readonly tableRow: Locator;

    constructor(page: Page, context: BrowserContext) {
        super(page, context);
        this.url = url.resolve(SANBOX_VPC, '#/vpc/dcgw/list');

        // 侧边栏
        this.sidebar = page.getByTestId('app-menu-root');
        // 内容区
        this.content = page.getByTestId('table-dcgw_list');
        // 表格
        this.table = page.getByTestId('table-dcgw_list');
        this.tableRow = page.getByTestId(/^table-tbody-tr-\d+-dcgw_list$/);
    }

    async setUp() {
        // await this.useGrayVersion('_network_version_');
        await this.page.goto(this.url, {waitUntil: 'commit'});
        await this.page.evaluate(() => window.$storage.set('vpc-console-intro', true));
        await this.sidebar.waitFor({state: 'visible'});
        await this.content.waitFor({state: 'visible'});
        await this.setRegion();
    }

    async reload() {
        return this.page.reload({waitUntil: 'commit'});
    }

    /** 切换地域 */
    async setRegion() {
        const switchRegion = this.page.locator('#region-name');
        await switchRegion.waitFor({state: 'visible'});
        const regionText = await switchRegion.textContent();

        if (regionText !== Text.BJ) {
            await this.page.locator('#region').hover();
            await this.page.getByRole('listitem', {name: Text.BJ}).click();
        }
    }
}
