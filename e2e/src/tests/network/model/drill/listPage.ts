import {expect, type Locator, type BrowserContext, type Page} from '@playwright/test';
import {BasePage as BasePageModel} from '@baidu/cbt-playwright-utils';
import {Text, SANBOX_VPC} from '@constants';
import url from 'url';

export class ListPage extends BasePageModel {
    /** 页面地址 */
    readonly url: string;
    readonly resultUrl: string;
    readonly sidebar: Locator;
    readonly table: Locator;
    readonly tableRow: Locator;
    readonly resultTable: Locator;
    readonly resultTableRow: Locator;

    constructor(page: Page, context: BrowserContext) {
        super(page, context);
        this.url = url.resolve(SANBOX_VPC, '#/dc/failover/plan');
        this.resultUrl = url.resolve(SANBOX_VPC, '#/dc/failover/result');

        // 侧边栏
        this.sidebar = page.getByTestId('app-menu-root');
        // 表格
        this.table = page.getByTestId('table-drill_plan_list');
        this.tableRow = page.getByTestId(/^table-tbody-tr-\d+-drill_plan_list$/);
        // 结果表格
        this.resultTable = page.getByTestId('table-drill_result_list');
        this.resultTableRow = page.getByTestId(/^table-tbody-tr-\d+-drill_result_list$/);
    }

    async setUp(type: 'plan' | 'result') {
        switch (type) {
            case 'plan':
                await this.page.goto(this.url, {waitUntil: 'commit'});
                await expect(this.page).toHaveURL(/dc\/failover\/plan/i, { timeout: 5000 });
                break;
            case 'result':
                await this.page.goto(this.resultUrl, {waitUntil: 'commit'});
                await expect(this.page).toHaveURL(/dc\/failover\/result/i, { timeout: 5000 });
                break;
        }
        // 确保 $storage 已挂载，否则会导致 $storage.set 报错
        await this.page.waitForFunction(() => window.$storage && typeof window.$storage.set === 'function');
        // 跳过 AI 智能助手引导
        await this.page.evaluate(() => window.$storage.set('vpc-console-intro', true));
        await this.sidebar.waitFor({state: 'visible'});
    }

    async reload() {
        return this.page.reload({waitUntil: 'commit'});
    }
}