import {expect, type Locator, type BrowserContext, type Page} from '@playwright/test';
import {BasePage as BasePageModel} from '@baidu/cbt-playwright-utils';
import {Text, SANBOX_VPC} from '@constants';
import url from 'url';

export class DetailPage extends BasePageModel {
    /** 页面地址 */
    readonly url: string;
    readonly resultUrl: string;
    readonly sidebar: Locator;

    constructor(page: Page, context: BrowserContext) {
        super(page, context);
        this.url = url.resolve(SANBOX_VPC, '#/dc/failover/plan');
        this.resultUrl = url.resolve(SANBOX_VPC, '#/dc/failover/result');

        // 侧边栏
        this.sidebar = page.getByTestId('app-menu-root');
    }

    async setUp(type: 'plan' | 'result') {
        let name;
        switch(type) {
            case 'plan':
                await this.page.goto(this.url, {waitUntil: 'commit'});
                // 确保 $storage 已挂载，否则会导致 $storage.set 报错
                await this.page.waitForFunction(() => window.$storage && typeof window.$storage.set === 'function');
                // 跳过 AI 智能助手引导
                await this.page.evaluate(() => window.$storage.set('vpc-console-intro', true));
                await this.sidebar.waitFor({state: 'visible'});
                await expect(this.page).toHaveURL(/dc\/failover\/plan/i, { timeout: 5000 });
                // 显示列表数据
                name = this.page.getByTestId('drill_plan_name_0');
                break;
            case 'result':
                await this.page.goto(this.resultUrl, {waitUntil: 'commit'});
                // 确保 $storage 已挂载，否则会导致 $storage.set 报错
                await this.page.waitForFunction(() => window.$storage && typeof window.$storage.set === 'function');
                // 跳过 AI 智能助手引导
                await this.page.evaluate(() => window.$storage.set('vpc-console-intro', true));
                await this.sidebar.waitFor({state: 'visible'});
                await expect(this.page).toHaveURL(/dc\/failover\/result/i, { timeout: 5000 });
                // 显示列表数据
                name = this.page.getByTestId('drill_result_name_0');
                break;
        }
        await expect(name).toBeVisible();
        // 点击进入详情页
        await name.click({ timeout: 3000 });
        await expect(this.page).toHaveURL(/dc\/failover\/detail/i, { timeout: 5000 });
    }

    async reload() {
        return this.page.reload({waitUntil: 'commit'});
    }
}
