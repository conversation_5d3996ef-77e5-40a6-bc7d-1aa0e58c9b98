/**
 * @desc ACL详情页
 */

import {expect, type Locator, type BrowserContext, type Page} from '@playwright/test';
import {BasePage as BasePageModel} from '@baidu/cbt-playwright-utils';
import {Text, SANBOX_VPC} from '@constants';
import url from 'url';

export class DetailPage extends BasePageModel {
    /** 页面地址 */
    readonly url: string;
    readonly sidebar: Locator;

    constructor(page: Page, context: BrowserContext) {
        super(page, context);
        this.sidebar = page.getByTestId('app-menu-root');
        this.url = url.resolve(SANBOX_VPC, '#/vpc/acl/list');
    }

    async setUp() {
        // await this.useGrayVersion('_network_version_');
        await this.page.goto(this.url, {waitUntil: 'commit'});
        await this.page.evaluate(() => window?.$storage?.set('vpc-console-intro', true));
        await this.page.getByTestId('acl_name_0').waitFor({state: 'visible', timeout: 20000});
        await this.page.getByTestId('acl_name_0').click();
        await expect(this.page).toHaveURL(/\/network\/#\/vpc\/acl\/manage/i, {timeout: 1000});
    }
}
