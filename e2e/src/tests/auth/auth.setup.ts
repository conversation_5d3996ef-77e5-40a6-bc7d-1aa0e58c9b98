import {test as setup, expect, chromium} from '@playwright/test';
import {SANBOX_LOGIN} from '@constants';

require('dotenv').config();

const {USERNAME = '', PASSWORD = ''} = process.env;

setup('登录', async () => {
    const browser = await chromium.launch({headless: true});
    const context = await browser.newContext();
    const passport = await context.newPage();

    // 打开证书1页面
    await passport.goto('https://passport.qatest.baidu.com/passApi/js/wrapper.js');
    // await passport.getByRole('button', {name: '高级'}).click();
    // await passport
    //   .getByRole('link', {name: '继续前往passport.qatest.baidu.com'})
    //   .click();

    const wappass = await context.newPage();

    // 打开证书2页面
    await wappass.goto('https://wappass.qatest.baidu.com/static/waplib/moonshad.js');
    // await wappass.getByRole('button', {name: '高级'}).click();
    // await wappass
    //   .getByRole('link', {name: '继续前往wappass.qatest.baidu.com'})
    //   .click();

    const page = await context.newPage();

    await page.goto(SANBOX_LOGIN);

    // 使用云账号登录
    await page.locator('.uc-tab-btn').first().click();

    const userName = page.locator('#uc-common-account');
    await userName.click();
    await userName.fill(USERNAME);

    const password = page.locator('#ucsl-password-edit');
    await password.click();
    await password.fill(PASSWORD);

    await page.locator('#submit-form').click();

    // const validationError = page.locator('#TANGRAM__PSP_4__error');

    await expect(page.locator('#main').getByText(USERNAME, {exact: true})).toBeVisible();

    // 写入cookies等信息
    await page.context().storageState({path: './auth.json'});
    await page.evaluate(() => {
        // 当前登录账号的userid
        const storageInfoStr = window.localStorage.getItem('ca8294bee9ce4c7f8c061e7034eb9259');
        const storageInfo = JSON.parse(storageInfoStr || '{}');
        storageInfo['vpc-console-intro'] = true;
        window.localStorage.setItem('ca8294bee9ce4c7f8c061e7034eb9259', JSON.stringify(storageInfo));
    });
});
