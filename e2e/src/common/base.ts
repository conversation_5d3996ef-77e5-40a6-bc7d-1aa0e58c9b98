import fs from 'fs';
import path from 'path';
import {test as base} from '@playwright/test';
import {uuid, rootPath} from 'src/utils/helper';

// const USE_ISTANBUL = true;
// const istanbulTempDir = process.env.ISTANBUL_TEMP_DIR
//   ? path.resolve(process.env.ISTANBUL_TEMP_DIR)
//   : path.join(rootPath, '.nyc_output');

// const createTrackIdEngine = () => ({
//   query(root: Document, selector: string) {
//     return root.querySelector(`[data-track-id="${selector}"]`);
//   },

//   queryAll(root: Document, selector: string) {
//     return Array.from(root.querySelectorAll(`[data-track-id="${selector}"]`));
//   }
// });

// export const test = base.extend<{}, {selectorRegistration: void}>({
//   selectorRegistration: [
//     async ({playwright}, use) => {
//       await playwright.selectors.register('trackId', createTrackIdEngine);
//       await use();
//     },
//     {scope: 'worker', auto: true}
//   ],
//   ...(USE_ISTANBUL
//     ? {
//         /**
//          * inspired by
//          * @link https://github.com/microsoft/playwright/issues/7030
//          */
//         context: async ({context}, use) => {
//           await context.addInitScript(() =>
//             window.addEventListener('beforeunload', () =>
//               window.collectIstanbulCoverage(JSON.stringify(window.__coverage__))
//             )
//           );
//           await fs.promises.mkdir(istanbulTempDir, {recursive: true});
//           await context.exposeFunction('collectIstanbulCoverage', (coverageJSON: string) => {
//             if (coverageJSON) {
//               const coverageName = `playwright_coverage_${uuid()}.json`;

//               fs.writeFileSync(path.join(istanbulTempDir, coverageName), coverageJSON);

//               console.log(
//                 `${coverageName}: successfully written coverage report json to %s/%s`,
//                 istanbulTempDir,
//                 coverageName
//               );
//             }
//           });
//           await use(context);
//           for (const page of context.pages()) {
//             await page.evaluate(() => window.collectIstanbulCoverage(JSON.stringify(window.__coverage__)));
//           }
//         }
//       }
//     : {})
// });

// export {expect, request} from '@playwright/test';

export {test, expect, request} from '@baidu/cbt-playwright-utils';
