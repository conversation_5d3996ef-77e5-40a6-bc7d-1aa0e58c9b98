require('dotenv').config();

const {BASE_URL = ''} = process.env;

export const SANBOX_LOGIN = 'https://login.bcetest.baidu.com/';

export const SANBOX_VPC = `${BASE_URL}/network/`;
export const PageUrl = {
    vpcList: '/network/#/vpc/instance/list',
    subnetList: '/network/#/vpc/subnet/list',
    securityList: '/network/#/vpc/security/list',
    aclList: '/network/#/vpc/acl/list',
    eniList: '/network/#/vpc/eni/list',
    routeList: '/network/#/vpc/route/list',
    dcgwList: '/network/#/vpc/dcgw/list',
    enterpriseSecurityList: '/network/#/vpc/enterpriseSecurity/list',
    snicList: '/network/#/vpc/endpoint/list',
    billingSuccess: 'billing/order/success',
    natList: '/network/#/vpc/nat/list',
    privateNatList: '/network/#/vpc/privateNat/list',
    natUpgrade: '/network/#/vpc/nat/upgrade',
    flowMirror: '/network/#/vpc/mirror/list',
    diagnoseUrl: '/network/#/vpc/instance/diagnosis'
};
// 文案映射
export const Text = Object.freeze({
    BJ: '华北 - 北京'
});

export const PAGE_URL_REG_EXP = {
    peerconn: {
        upgrade: /\/vpc\/peerconn\/upgrade/i
    },
    route: {
        detail: /\/vpc\/route\/detail/i,
        bindTgw: /\/vpc\/route\/bindTgw/i
    }
};
