# bce-ui-test

Console UI自动化测试

# 前言

## 账号要求

- 账号以及本地启动后的url保存在`env.local`文件中
- 该账号仅用于ē2e测试，请勿用于日常测试
- BASE_URL可能同自己本地启动的端口号不一致，请根据本地情况自行调整

## 测试case说明

由于BLB是付费类资源，创建 & 释放都是异步操作，生效时间不固定。因此测试用例主要使用已有数据，数据要求如下：
- 普通型BLB列表至少有两条数据：第一条数据：未绑定eip，预付费，无监听器，未关联安全组 第二条数据：预付费，标准型规格
- 应用型BLB列表至少有两条数据：第一条数据：未绑定eip，预付费，无监听器，未关联安全组 第二条数据：无特殊要求
- 普通型ipv6BLB列表至少有两条数据：第一条数据：未绑定eip，预付费，无监听器，未关联安全组 第二条数据：无特殊要求
- 应用型ipv6BLB列表至少有两条数据：第一条数据：未绑定eip，预付费，无监听器，未关联安全组 第二条数据：无特殊要求
- LBDC列表至少有一条数据：无特殊要求
- 服务发布点列表至少有一条数据：无特殊要求

另外，为测试eip绑定解绑逻辑，还需使用两个eip，一个v4版本一个v6版本。且由于eip绑定功能通过eip-sdk提供，暂时不支持写入test-id，先通过eip 地址 + name获取，分别是：
- v4 eip要求：blb-e2e-eip ************* 标准型 状态可用
- v6 eip要求：blb-e2e-eip 2400:da00:e003:2000:0:2f:0:73 标准型 可用

e2e/tests/blb/00-create-release目录的case用于测试多种LB的创建、释放，由于创建释放都是异步操作，保证自动化测试case执行的时候能创建完成or释放完成，所以后续流水线中不执行该case，只在本地执行即可


## 最佳实践

- 元素定位：尽量使用 `data-testid` 或者 `Role` 属性，避免使用 xpath、CSS 定位器（`class`、`id`），提高 CASE 的稳定性
- 利用 codegen 录制用例，提高效率，playwright 会优先选择 role、text test id 定位器，还能使用 VsCode 中的官方插件
- 验证元素可见性：`expect(element).toBeVisible()`，避免使用 `expect(element.toBeVisible()).toBe(true)`
- Debug：多利用断点调试；CI 中调试待建设
- 使用 Soft 断言：断言的失败会导致程序异常退出，使用 Soft 断言可以避免异常退出，它不会中断用例，而是在结束时列出异常项目
- 测试用例的命名：`[组件名]_[动作]_[预期结果]`，比如：`button_click_success

# 项目结构

- 当前路径`tests/`下存放测试文件
- env.local中存放测试环境变量，注意调整本地url端口、用户名密码等信息

# 开发方式

使用无头浏览器运行端到端测试：

```bash
npx playwright test
```

打开浏览器窗口，并运行端到端测试：

```bash
npx playwright test --ui
```

使用特定的浏览器运行端到端测试：

```bash
npx playwright test --project=chromium
```

使用特定的文件运行端到端测试：

```bash
npx playwright test example
```

使用调试模式运行端到端测试：

```bash
npx playwright test --debug
```

使用 Codegen 自动生成测试文件：

```bash
npx playwright test codegen
```

更新全部快照：

```bash
npx playwright test --update-snapshots
```

## 前置准备

在运行E2E测试之前，需要将测试账号的状态重置为初始状态，避免测试失败。

## 镜像制作

- `<tag>`: 版本号，默认使用`latest`

`docker build -t <image-name>:<tag> .`

## 镜像运行

`docker run -it <image-name>:<tag>`
