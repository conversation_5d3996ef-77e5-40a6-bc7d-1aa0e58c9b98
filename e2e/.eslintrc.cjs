module.exports = {
  root: true,
  // 参考：https://www.npmjs.com/package/@ecomfe/eslint-config
  extends: [
    '@ecomfe/eslint-config/strict',
    '@ecomfe/eslint-config/typescript/strict',
    '@ecomfe/eslint-config/import',
    "plugin:playwright/recommended",
    'prettier'
  ],
  /** 需要排除检测的文件 */
  ignorePatterns: ['scripts', '.eslintrc.cjs', 'playwright.config.ts'],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    // ecmaVersion: 2020,
    ecmaVersion: 'latest',
    sourceType: 'module',
    project: './tsconfig.json',
    tsconfigRootDir: __dirname,
    createDefaultProgram: true
  },
  /** 第三方插件定义的额外规则 */
  plugins: [],
  /** 可共享的设置项 */
  settings: {
    'import/resolver': {
      // 配合 eslint-import-resolver-typescript 插件，解决无法解析 typescript 模块的问题
      typescript: {
        project: './tsconfig.json',
        /** always try to resolve types under `<root>@types` directory even it doesn't contain any source code, like `@types/unist` */
        alwaysTryTypes: true
      },
      node: {
        extensions: ['.js', '.ts']
      }
    },
    'import/parsers': {
      '@typescript-eslint/parser': ['.ts', '.tsx']
    }
  },
  /**
   * "off" 或 0 - 关闭规则
   * "warn" 或 1 - 开启规则，使用警告级别的错误：warn (不会导致程序退出)
   * "error" 或 2 - 开启规则，使用错误级别的错误：error (当被触发的时候，程序会退出)
   */
  rules: {
    '@typescript-eslint/no-floating-promises': 2,
    '@typescript-eslint/no-unused-vars': '1'
  },
  /** 覆写的规则，部分文件里需要特别设置 */
  overrides: []
};
