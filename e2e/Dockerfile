# @notice: 构建的根目录就是代码的根目录

# Get the base image of Node version 16
FROM iregistry.baidu-int.com/acg-iaas-fe/node:16

# Get the latest version of Playwright
FROM iregistry.baidu-int.com/acg-iaas-fe/playwright:v1.41.1-jammy

# 不知道为啥iPipe中apt-get无法正常更新，这些工具暂时不需要
# 安装一些基础工具
# RUN apt-get update && apt-get install -y curl wget iputils-ping
# setting up certutil
# RUN apt-get -y install libnss3 libatk-bridge2.0-0 libdrm-dev libxkbcommon-dev libgbm-dev libasound-dev libatspi2.0-0 libxshmfence-dev

WORKDIR /app

# Set the environment path to node_modules/.bin
ENV PATH /app/node_modules/.bin:$PATH

# COPY the needed files to the app folder in Docker image
COPY src/ /app/src/
COPY package.json /app/
COPY tsconfig.json /app/
COPY playwright.config.ts /app/
COPY .env /app/
COPY .npmrc /app/

# Install the dependencies in Node environment
RUN npm install

# run e2e test
ENTRYPOINT npm run test:docker
