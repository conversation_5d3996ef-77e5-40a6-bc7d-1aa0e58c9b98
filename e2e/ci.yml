Global:
  version: 2.0

Default:
  profile: [build]

Profiles:
  - profile:
    name: build
    mode: AGENT
    environment:
      image: DECK_CENTOS7U5_K3
      tools:
        - nodejs: 18.3.0
    check:
      - reuse: TASK
        enable: true
    build:
      command: sh ./scripts/build.sh
    artifacts:
      release: true
  - profile:
    name: buildImage
    mode: AGENT_FOR_IMAGE_BUILD
    environment:
      image: DECK_CENTOS7U5_K3
      resourceType: SMALL
    build:
      command: sh scripts/build-image.sh
    imageBuild:
      images:                                                             # 镜像构建相关配置
        - dockerfile: ./Dockerfile                                            # (必填) Dockerfile 相对代码库根目录的相对路径
          repository: iregistry.baidu-int.com/acg-iaas-fe/console-cfs-e2e     # (必填) 镜像仓库项目地址
          # 镜像版本命名策略，在流水线发布阶段可修改为最终正式版本，支持如下两种：
          # 2. TIMESTAMP_BRANCH_COMMIT_ID: 默认策略，时间戳-分支名-commit ID，如20210815162319-master-a3b2def
          # 1. TIMESTAMP_COMMIT_ID：时间戳-commit ID，如20210815162319-a3b2def
          tagStrategy: TIMESTAMP_BRANCH_COMMIT_ID         # (选填) 镜像tag命名策略
          context: .                                  # (选填) 镜像构建时的上下文，默认是代码库根目录，也可指定其他相对代码库根目录的相对路径
          # 基础镜像拉取策略，支持如下两种：
          # 1. IfNotPresent: 默认策略，当不存在基础镜像时拉取
          # 2. Always: 不管本地有没有基础镜像，都会重新拉取镜像仓库的最新基础镜像
          imagePullStrategy: IfNotPresent                 # (选填) 基础镜像拉取策略
          enableBaseImageCache: true
          push: true
    artifacts:
      release: true
