import os from 'node:os';
import path from 'node:path';
import {defineConfig, devices, type ReporterDescription} from '@playwright/test';
import type {ReporterConfigOptions} from '@baidu/cbt-report';

require('dotenv').config();

const env = process.env;
const {
    CI = 'OFF',
    BASE_URL,
    USE_MOBILE_UI,
    LOCALE,
    TIME_ZONE,
    USER_AGENT,
    USE_CUSTOM_REPORT,
    USE_ISTANBUL,
    CTRF_REPORT_DIR,
    CTRF_REPORT_FILE_NAME,
    PLAYWRIGHT_OUTPUT_DIR = 'test-results'
} = process.env;

const inCI = CI === 'ON';

console.table({
    '运行环境': inCI ? 'CI环境' : '本地开发环境',
    '基础路由': BASE_URL,
    '语言': LOCALE,
    '时区': TIME_ZONE,
    '设备': USE_MOBILE_UI === 'ON' ? '移动端' : 'PC端',
    'user-agent': USER_AGENT,
    '开启CTRF': USE_CUSTOM_REPORT ? '是' : '否',
    '开启覆盖率统计': USE_ISTANBUL ? '是' : '否'
});

const reporter: ReporterDescription[] = [['list', {printSteps: true}], ['html']];

// 开启自定义报告输出，CTRF报告主要是为了将测试数据上报而提供的标准化报告格式
if (USE_CUSTOM_REPORT === 'true') {
    const options: ReporterConfigOptions = {
        appName: env.AGILE_MODULE_NAME ?? 'baidu/bce-console/console-vpc',
        appVersion: env.AGILE_RELEASE_VERSION ?? '*******',
        osPlatform: os.platform(),
        osArch: os.arch(),
        osRelease: os.release(),
        osVersion: os.version(),
        buildName: env.AGILE_PIPELINE_NAME ?? '',
        buildNumber: env?.AGILE_PIPELINE_BUILD_ID ?? '',
        runtimeEnv: inCI && env.AGILE_MODULE_NAME ? 'iPipe' : 'local',
        testEnv: 'sandbox',
        testType: 'e2e',
        screenshot: true,
        outputDir: CTRF_REPORT_DIR,
        outputFile: CTRF_REPORT_FILE_NAME
    };

    reporter.push(['@baidu/cbt-report/playwright-ctrf-json', options]);
}

/**
 * See https://playwright.dev/docs/test-configuration.
 */
export default defineConfig({
    /* 测试文件路径 */
    testDir: './src/tests',
    /* 是否开启并发 */
    fullyParallel: true,
    /* only指令主要用于调试单case，开启后生成环境运行会忽略only指令，避免失误 */
    forbidOnly: inCI,
    /* 用例失败重试次数 */
    retries: 0,
    /* 试时使用的进程数，进程数越多可以同时执行的测试任务就越多。不设置则尽可能多地开启进程。 */
    workers: inCI ? 1 : 1,
    // Limit the number of failures on CI to save resources
    maxFailures: process.env.CI ? 20 : undefined,
    /* 测试报告输出格式 https://playwright.dev/docs/test-reporters */
    reporter: reporter,
    /** 单用例超时时间，默认3min */
    timeout: inCI ? 3 * 60 * 1000 : 30 * 1000,
    /** 断言库的相关配置 */
    expect: {
        /** 断言超时时间，默认是5s，这里调大一点，因为部分场景渲染速度比较慢 */
        timeout: 40 * 1000,
        /** 生成快照配置 */
        toHaveScreenshot: {
            /** 色彩空间相关设置，范围[0, 1) */
            threshold: 0.2,
            /** 可接受像素差上限阈值 */
            maxDiffPixelRatio: 0.04,
            // maxDiffPixels: 100,
            /** 是否允许CSS动画、CSS过渡、Web动画，为了方便测试和提升速度，先关闭 */
            animations: 'disabled',
            /** 是否显示输入光标，没有测试场景，先关闭 */
            caret: 'hide',
            /** 是否开启点对点截图，为了减少设备对快照图片的影响，我们选择开启点对点  */
            scale: 'css',
            /** 截图时应用的css样式，用于去除一些全局挂载的东西 */
            stylePath: path.resolve(__dirname, './screenshot.css')
        },
        toMatchSnapshot: {
            threshold: 0.2,
            maxDiffPixels: 100
        }
    },

    /* 公共配置 https://playwright.dev/docs/api/class-testoptions */
    use: {
        /* 基础路由地址，配置之后执行`await page.goto('/')`会默认进入到该地址 */
        baseURL: BASE_URL,
        /** 是否以无头浏览器模式运行 */
        headless: true,
        /** 获取HTTPS证书错误，沙盒环境通常没有证书，需要下载自签名证书 */
        ignoreHTTPSErrors: true,
        /** 是否在移动端环境 */
        isMobile: USE_MOBILE_UI === 'ON',
        /** 国际化语言，将会影响navigator.language */
        locale: LOCALE,
        /** 测试时使用的时区，默认使用中国时区，https://source.chromium.org/chromium/chromium/deps/icu.git/+/faee8bc70570192d82d2978a71e2a615788597d1:source/data/misc/metaZones.txt */
        timezoneId: TIME_ZONE,
        /** user-agent请求头 */
        userAgent: USER_AGENT,
        /** 浏览器默认视窗大小，测试媒体查询时可以做调整 */
        viewport: {width: 1280, height: 720},
        /* 是否为用例记录trace， https://playwright.dev/docs/trace-viewer */
        trace: 'retain-on-failure',
        /** 是否为用例录像 */
        video: 'retain-on-failure',
        /** 是否测试完成后自动记录快照 */
        screenshot: 'off',
        /** 自定义属性，可以通过`page.getByTestId()`快速定位 */
        testIdAttribute: 'data-testid',
        /** 每个步骤执行的时间，拉长一点避免太快导致用例异常 */
        launchOptions: {
            slowMo: 1_000
        }
    },
    /** 运行测试后是否更新snapshot，只在首次创建执行时创建 */
    updateSnapshots: 'missing',
    /** 测试结果输出的目录 */
    outputDir: PLAYWRIGHT_OUTPUT_DIR,
    /** 是否上报慢运行的测试用例，暂时先不开，等稳定之后可以用于性能检测 */
    reportSlowTests: null,

    /* 对每个浏览器做定制化配置，每个浏览器的use会和上面的use做合并 */
    projects: [
        // Setup project
        {name: 'setup', testMatch: /.*\.setup\.ts/},

        // Teardown project
        // {name: 'teardown', testMatch: /.*\.teardown\.ts/},

        // 计算覆盖率
        {
            name: 'coverage',
            testMatch: /.*\.cov\.ts/
        },
        {
            name: 'chromium',
            use: {
                ...devices['Desktop Chrome'],
                launchOptions: {
                    /** 每个步骤执行的时间，拉长一点避免太快导致用例异常 */
                    slowMo: 1000
                },
                /** 默认鉴权信息存储地址, 主要不要写入全局配置，否则会默认读取找不到auth文件 */
                storageState: './auth.json',
                viewport: {width: 1280, height: 720},
                /** 浏览器权限 */
                permissions: ['clipboard-read', 'clipboard-write']
            },
            // CI中强依赖登录
            dependencies: inCI ? ['setup'] : []
        }
        // {
        //   name: 'firefox',
        //   use: {
        //     ...devices['Desktop Firefox'],
        //     launchOptions: {
        //       /** 每个步骤执行的时间，拉长一点避免太快导致用例异常 */
        //       slowMo: 1000,
        //       /** firefox用户配置，和chromium中的permissions配置有些许差异 */
        //       firefoxUserPrefs: {
        //         'dom.events.asyncClipboard.readText': true,
        //         'dom.events.testing.asyncClipboard': true
        //       }
        //     },
        //     storageState: './auth.json',
        //     viewport: {width: 1280, height: 720}
        //   },
        //   dependencies: inCI ? ['setup'] : []
        // },
        // {
        //   name: 'webkit',
        //   use: {
        //     ...devices['Desktop Safari'],
        //     launchOptions: {
        //       /** 每个步骤执行的时间，拉长一点避免太快导致用例异常 */
        //       slowMo: 1000
        //     },
        //     storageState: './auth.json',
        //     viewport: {width: 1280, height: 720}
        //   },
        //   dependencies: inCI ? ['setup'] : []
        // }

        /* Test against mobile viewports. */
        // {
        //   name: 'Mobile Chrome',
        //   use: { ...devices['Pixel 5'] },
        // },
        // {
        //   name: 'Mobile Safari',
        //   use: { ...devices['iPhone 12'] },
        // },

        /* Test against branded browsers. */
        // {
        //   name: 'Microsoft Edge',
        //   use: { ...devices['Desktop Edge'], channel: 'msedge' },
        // },
        // {
        //   name: 'Google Chrome',
        //   use: { ...devices['Desktop Chrome'], channel: 'chrome' },
        // },
    ]

    /** 可以用于启动Mock服务 */
    // webServer: {
    //   command: 'npm run start',
    //   url: 'http://127.0.0.1:3000',
    //   reuseExistingServer: CI === 'OFF',
    // },
});
