{
  "compilerOptions": {
    /* Basic Options */
    "incremental": true /* 增量编译 */,
    "target": "ESNext",
    "module": "ESNext",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "skipLibCheck": true,
    "allowJs": true,
    "jsx": "react-jsx",
    "declaration": true /* Generates corresponding '.d.ts' file. */,
    "declarationMap": true /* Generates a sourcemap for each corresponding '.d.ts' file. */,
    /* Source Map Options */
    "sourceMap": true /* Generates corresponding '.map' file. */,
    // "rootDir": "",
    /* Strict Type-Checking Options */
    "strict": true,
    "pretty": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "noImplicitThis": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    /* Transpile each file as a separate module (similar to 'ts.transpileModule'). */
    "moduleResolution": "node",
    /* Allows importing modules with a ‘.json’ extension */
    "resolveJsonModule": true,
    "isolatedModules": true,
    /* Do not emit outputs. */
    "noEmit": true,
    "baseUrl": "./",
    "paths": {
      "@constants": ["src/common/constants.ts"],
      "@base": ["src/common/base.ts"],
      "@utils/*": ["src/utils/*"]
    },
    "typeRoots": ["types", "node_modules/@types"],
    /* Enables emit interoperability between CommonJS and ES Modules via creation of namespace objects for all imports. Implies 'allowSyntheticDefaultImports'. */
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    /* Experimental Options */
    "experimentalDecorators": true /* Enables experimental support for ES7 decorators. */,
    "emitDecoratorMetadata": true /* Enables experimental support for emitting type metadata for decorators. */,
    /* Interop Constraints */
    "forceConsistentCasingInFileNames": true /* TypeScript follows the case sensitivity rules of the file system it’s running on. */
  }
}
