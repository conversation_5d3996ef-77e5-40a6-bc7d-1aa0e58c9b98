{"name": "bce-ui-test", "version": "1.0.0", "description": "控制台VPC UI自动化测试", "module": "true", "private": true, "scripts": {"install-binary": "PLAYWRIGHT_BROWSERS_PATH=$HOME/pw-browsers npx playwright install", "list": "PLAYWRIGHT_BROWSERS_PATH=$HOME/pw-browsers playwright test --list", "clean": "PLAYWRIGHT_BROWSERS_PATH=$HOME/pw-browsers dotenv -e .env -- bce-test clean -y", "test": "PLAYWRIGHT_BROWSERS_PATH=$HOME/pw-browsers npm run clean && dotenv -e .env.local playwright test --reporter=html", "test:ui": "PLAYWRIGHT_BROWSERS_PATH=$HOME/pw-browsers npm run clean && dotenv -e .env.local playwright test --ui", "test:ci": "npm run clean && dotenv -e .env.ci -- bce-test test --upload --service-id network", "coverage": "dotenv -e .env -- playwright test coverage.cov.ts", "debug": "PLAYWRIGHT_BROWSERS_PATH=$HOME/pw-browsers dotenv -e .env playwright test --debug", "update-snapshot": "PLAYWRIGHT_BROWSERS_PATH=$HOME/pw-browsers dotenv -e .env playwright test --update-snapshots --grep @visual", "test:docker": "dotenv -e .env.ci playwright test --reporter=html", "report": "PLAYWRIGHT_BROWSERS_PATH=$HOME/pw-browsers playwright show-report", "show-trace": "PLAYWRIGHT_BROWSERS_PATH=$HOME/pw-browsers playwright show-trace", "login": "dotenv -e .env -- playwright test auth.setup.ts", "coverage:istanbul": "dotenv -e .env -- bce-test clean && npx nyc report --report-dir ./coverage --temp-dir .nyc_output --reporter=html --exclude-after-remap false", "check-pw-version": "curl -s GET https://api.github.com/repos/microsoft/playwright/tags\\?per_page\\=1 | jq -r '.[].name'", "check-update": "curl -s GET https://api.github.com/repos/microsoft/playwright/tags\\?per_page\\=1 | jq -r '.[].name'"}, "repository": {"type": "git", "url": "ssh://<EMAIL>:8235/baidu/personal-code/bce-ui-test"}, "author": "lurunze", "license": "ISC", "devDependencies": {"@babel/eslint-parser": "^7.12.13", "@babel/eslint-plugin": "^7.12.13", "@ecomfe/eslint-config": "^7.0.0", "@types/async": "^3.2.24", "@types/node": "^20.11.6", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-playwright": "^1.5.2", "lint-staged": "^13.0.3", "nyc": "^17.0.0", "prettier": "^3.2.4", "typescript": "^4.9.5"}, "dependencies": {"@baidu/cbt-cli": "^1.0.12", "@baidu/cbt-playwright-utils": "^1.0.13-alpha.0", "@baidu/cbt-report": "^1.0.12", "@playwright/test": "1.44.0", "async": "^3.2.5", "dotenv": "^16.4.0", "dotenv-cli": "^7.4.1", "feature-map": "^1.0.0"}}