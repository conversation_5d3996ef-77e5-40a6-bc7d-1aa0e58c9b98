/**
 * @description @baiducloud/httpclient声明
 * @file        types/@baiducloud/httpclient/index.d.ts
 */

/* eslint-disable import/unambiguous */

declare namespace HttpClient {

  type Method = 'GET' | 'POST' | 'DELETE' | 'PUT' | 'HEAD' | 'OPTIONS' | 'PATCH' | 'CONNECT' | 'TRACE';

  type ResponseType = '' | 'arraybuffer' | 'blob' | 'document' | 'json' | 'text';

  interface Options {
      CSRFToken?: boolean;
      onDownloadProgress?: (this: XMLHttpRequest, ev: ProgressEvent<XMLHttpRequestEventTarget>) => any;
      onUploadProgress?: (this: XMLHttpRequestUpload, ev: ProgressEvent<XMLHttpRequestEventTarget>) => any;
      onabort?: ((this: XMLHttpRequest, ev: ProgressEvent<EventTarget>) => any);
      onerror?: ((this: XMLHttpRequest, ev: ProgressEvent<EventTarget>) => any);
      ontimeout?: ((this: XMLHttpRequest, ev: ProgressEvent<EventTarget>) => any);
      method?: Method;
      url?: string;
      data?: any;
      headers?: Record<string, any>;
      timeout?: number;
      responseType?: ResponseType;
      withCredentials?: boolean;
      validateStatus?(status: number): boolean;
  }

  interface ContextPipe {
      getCsrfToken(): any;
      getCurrentRegionId(): any;
      getCurrentRegion(): any;
      getLogoutUrl(): any;
  }

  export default class Client {

      constructor(config: Options, contextPipe: ContextPipe);

      head(url: string, config?: Record<string, any>): Promise<any>;

      options(url: string, config?: Record<string, any>): Promise<any>;

      patch(url: string, data?: Record<string, any>, config?: Record<string, any>): Promise<any>;

      get(url: string, querys?: Record<string, any>, config?: Record<string, any>): Promise<any>;

      delete(url: string, querys?: Record<string, any>, config?: Record<string, any>): Promise<any>;

      post(url: string, data?: Record<string, any>, config?: Record<string, any>): Promise<any>;

      put(url: string, data?: Record<string, any>, config?: Record<string, any>): Promise<any>;
  }
}
