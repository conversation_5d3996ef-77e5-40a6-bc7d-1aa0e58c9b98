Global:
    version: 2.0
    group_email: <EMAIL>
Default:
    profile: [master]
Profiles:
    - profile:
      name: master
      mode: AGENT
      environment:
          image: DECK_STD_CENTOS7 # <------ 配置使用的编译环境，推荐符合CMC规范的DECK_STD_CENTOS7
          tools: # <------ 配置软件版本信息
              - nodejs: 18.latest # <------ 配置软件名称及其对应版本，支持指定具体版本（例如1.18.10、1.19.6等）or 1.19.latest（表示使用当前构建系统支持的对应版本软件的最新小版本）
              - pnpm: 8.3.1
      build:
          command: sh scripts/build.sh public
      artifacts:
          release: true

    - profile:
      name: buildXsProduction
      mode: AGENT
      environment:
          image: DECK_STD_CENTOS7 # <------ 配置使用的编译环境，推荐符合CMC规范的DECK_STD_CENTOS7
          tools: # <------ 配置软件版本信息
              - nodejs: 18.latest # <------ 配置软件名称及其对应版本，支持指定具体版本（例如1.18.10、1.19.6等）or 1.19.latest（表示使用当前构建系统支持的对应版本软件的最新小版本）
              - pnpm: 8.3.1
      build:
          command: sh scripts/build.sh xs
      artifacts:
          release: true

    - profile:
      name: buildCoverage
      mode: AGENT
      environment:
          image: DECK_STD_CENTOS7
          tools:
              - nodejs: 20.latest
              - pnpm: 8.3.1
      build:
          command: sh scripts/build-coverage.sh
      cache:
          enable: false
      artifacts:
          release: true
