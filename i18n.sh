#!/usr/bin/env bash
# 很重要，引入node环境才能执行npx等命令
export PATH=$NODEJS_BIN_V8:$PATH
# 国际化库安装
npm i @baiducloud/i18n
# 定义产出目录，本地执行一下，这个目录里就是提取出的文案了
I18N_OUTPUT_FOLDER="./i18n-terms"
# 执行提取
npx bce-i18n analyse --no-transform --library inf-i18n -d ./fe_source/src -o "$I18N_OUTPUT_FOLDER"/console
# 上传到console center
# 此处的module参数需要设置成本产品在P3M登记的Service类型，查询地址：http://opscenter.bce-sandbox.baidu.com/#/product/service/list
npx bce-i18n upload -d "$I18N_OUTPUT_FOLDER" --module VPC