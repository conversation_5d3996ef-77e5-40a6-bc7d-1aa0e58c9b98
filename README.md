# 百度云控制台前端示例模块

> 百度云控制台项目参考文档：[文档参考](http://sandbox.bce.console.baidu-int.com/bce-docs/fe/console-setup/fe-setup/fe-repo.html)

## 安装环境

- 安装依赖
> 指定`registry`为`http://registry.npm.baidu-int.com`
> 推荐使用 `nrm` 管理 `registry`

```shell
    npm install
```

- 修改`hosts`文件如下

```shell
127.0.0.1 localhost.qasandbox.bcetest.baidu.com
```

## 本地调试

```
    npx bce-cli dev
    // or
    npm run dev
```

## 打开调试地址

```shell
open https://localhost.qasandbox.bcetest.baidu.com:8889/demo/
```

## 相关文件说明

- `package.json`: 必要文件, 其中`name`与`main`为必要属性

## 部署

- [参考文档](http://sandbox.bce.console.baidu-int.com/bce-docs/fe/console-setup/fe-setup/fe-repo.html)

