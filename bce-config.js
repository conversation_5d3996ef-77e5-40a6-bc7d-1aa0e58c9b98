/**
 * @file 工具模块启动配置
 */

const {defineConfig} = require('@baidu/cba-cli');
const apiMocker = require('mocker-api');
const path = require('path');
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
const ReactRefreshWebpackPlugin = require('@pmmmwh/react-refresh-webpack-plugin');
const {ANALYZ, NODE_ENV} = process.env;
const isProd = NODE_ENV === 'production';
const babelPlugins = ['@babel/plugin-proposal-optional-chaining'];
const IS_XS = process.env.CONSOLE_TYPE === 'xs-console';
const isCoverage = !!process.env.COVERAGE;
const isMock = process.env.API_MOCK === 'true';

if (isCoverage) {
    const defaultExtension = ['.js', '.cjs', '.mjs', '.ts', '.tsx', '.jsx'];
    const testFileExtensions = defaultExtension.map(extension => extension.slice(1)).join(',');
    babelPlugins.push([
        // 覆盖率统计插桩
        'istanbul',
        {
            /**
             * @doc: https://github.com/istanbuljs/schema/blob/master/default-exclude.js
             */
            exclude: [
                'coverage/**',
                'packages/*/test{,s}/**',
                '**/*.d.ts',
                'test{,s}/**',
                `test{,-*}.{${testFileExtensions}}`,
                `**/*{.,-}test.{${testFileExtensions}}`,
                '**/__tests__/**',
                /* Exclude common development tool configuration files */
                '**/{ava,babel,nyc}.config.{js,cjs,mjs}',
                '**/jest.config.{js,cjs,mjs,ts}',
                '**/{karma,rollup,webpack}.config.js',
                '**/.{eslint,mocha}rc.{js,cjs}'
            ]
        }
    ]);
}

module.exports = defineConfig({
    appName: 'network',
    appTitle: '私有网络 VPC',
    onlineConfigName: 'online-config.network',
    presets: ['@baidu/cba-preset-console-react'],
    proxyTarget: 'https://qasandbox.bcetest.baidu.com',
    flags: ['network'],
    templateId: IS_XS ? '7606d598-7f0f-4ae3-9d6e-353ebfb18010' : '86089d33-aecc-424f-a241-784d4e94d08e',
    i18n: {
        enabled: isCoverage === false,
        supportedLanguages: ['zh-cn', 'en-us'],
        independent: false,
        sdpDir: isCoverage ? null : path.resolve(__dirname, './src/pages/sanPages'),
        preload: true
    },
    babelOptions: {
        plugins: babelPlugins
    },
    mixSdp: true,
    sdpDependencies: {
        '@baidu/bce-track': 'https://bce.bdstatic.com/lib/@baiducloud/bce-track/1.0.19.1/track',
        '@baiducloud/bce-opt-checker': 'https://bce.bdstatic.com/lib/@baiducloud/bce-opt-checker/1.0.3.1/checker',
        '@baiducloud/bcm-sdk': 'https://bce.bdstatic.com/lib/@baiducloud/fe-bcm-sdk/1.0.5.2/bcm-sdk',
        '@baidu/new-bcm-sdk-san': 'https://bce.bdstatic.com/lib/@baiducloud/fe-bcm-sdk/2.0.13.1/bcm-sdk-san',
        '@baidu/new-bcm-sdk': 'https://bce.bdstatic.com/lib/@baiducloud/fe-bcm-sdk/2.0.13.1/bcm-sdk',
        '@baiducloud/billing-sdk': 'https://bce.bdstatic.com/lib/@baiducloud/fe-billing-sdk/1.0.73.3/billing-sdk',
        '@baiducloud/bos-sdk': 'https://bce.bdstatic.com/lib/@baiducloud/fe-bos-sdk/1.0.1.40/bos-sdk',
        '@baidu/bce-org-sdk': 'https://bce.bdstatic.com/lib/@baiducloud/fe-organization-sdk/1.0.3.2/bce-org-sdk',
        '@baidu/bce-resourcegroup-sdk-san':
            'https://bce.bdstatic.com/lib/@baiducloud/fe-resourcegroup-sdk/1.0.32.1/resourcegroup-sdk-san',
        '@baidu/bce-resourcegroup-sdk':
            'https://bce.bdstatic.com/lib/@baiducloud/fe-resourcegroup-sdk/1.0.32.1/resourcegroup-sdk',
        '@baiducloud/bce-billing-sdk': 'https://bce.bdstatic.com/lib/@baiducloud/fe-billing-sdk/2.1.77.1/billing-sdk',
        '@baiducloud/bce-billing-sdk-san':
            'https://bce.bdstatic.com/lib/@baiducloud/fe-billing-sdk/2.1.77.1/billing-sdk-san',
        '@baidu/bce-tag-sdk': 'https://bce.bdstatic.com/lib/@baiducloud/fe-tag-sdk/2.0.20.1/tag-sdk',
        '@baidu/bce-tag-sdk-san': 'https://bce.bdstatic.com/lib/@baiducloud/fe-tag-sdk/2.0.20.1/tag-sdk-san'
    },
    webpack: (config, merge) => {
        if (config.module.rules && Array.isArray(config.module.rules)) {
            config.module.rules.push({
                test: /\.(t|j|mj)s$/,
                include: [path.resolve(__dirname, './node_modules/@baidu/xicon-san')],
                resolve: {
                    fullySpecified: false
                }
            });
        }
        // 本地开发环境下页面各种报错都会被捕捉到，影响开发体验。
        const webpackRefreshIdx = (config.plugins || []).findIndex(
            plugin => plugin instanceof ReactRefreshWebpackPlugin
        );
        if (~webpackRefreshIdx) {
            config.plugins[webpackRefreshIdx].options.overlay = false;
        }
        return merge(config, {
            externals: [
                'san',
                'framework',
                'lodash',
                'san-router',
                '@baiducloud/i18n',
                '@baidu/bce-track',
                '@baiducloud/runtime',
                '@baiducloud/bcm-sdk',
                '@baidu/bce-org-sdk',
                '@baidu/new-bcm-sdk',
                '@baiducloud/bce-ui/san',
                '@baiducloud/billing-sdk',
                '@baidu/new-bcm-sdk-san',
                '@baidu/bce-org-sdk/san',
                '@baiducloud/httpclient',
                '@baiducloud/bcm-sdk/san',
                '@baiducloud/bos-sdk/san',
                '@baiducloud/billing-sdk/san',
                '@baiducloud/bce-billing-sdk',
                '@baiducloud/bce-opt-checker',
                '@baidu/bce-resourcegroup-sdk',
                '@baiducloud/bce-billing-sdk-san',
                '@baidu/bce-resourcegroup-sdk-san',
                '@baidu/bce-tag-sdk',
                '@baidu/bce-tag-sdk-san'
            ],
            resolve: {
                // 解析别名
                alias: {
                    '@': path.resolve(process.cwd(), './src')
                }
            },
            plugins: [...(ANALYZ ? [new BundleAnalyzerPlugin()] : [])],
            optimization: {
                splitChunks: {
                    cacheGroups: {
                        // default会匹配所有文件
                        default: false,
                        defaultVendors: false,
                        xlsx: {
                            chunks: 'all',
                            minChunks: 1,
                            name: 'xlsx-chunk',
                            test: /[\\/]node_modules[\\/](xlsx)[\\/]/,
                            priority: -10,
                            reuseExistingChunk: true
                        }
                    }
                }
            },
            devServer: {
                port: 8890,
                client: {
                    overlay: {
                        runtimeErrors: error => {
                            if (error.message === 'ResizeObserver loop completed with undelivered notifications.') {
                                return false;
                            }
                            return true;
                        }
                    }
                },
                onBeforeSetupMiddleware: devServer => {
                    const app = devServer.app;
                    if (isMock) {
                        apiMocker(app, path.resolve(__dirname, './mock/index.js'));
                    }
                }
            }
        });
    }
});
