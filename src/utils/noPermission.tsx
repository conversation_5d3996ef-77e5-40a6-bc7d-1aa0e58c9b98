import React from 'react';
import ReactDom from 'react-dom/client';
import {EventBus} from './eventBus';
import {EventName} from './constants';

import NoPermissionComp from '@/components/NoPermission';
import {IPageApiConfig} from '@/types';

// 页面路由
type PageRoute = string;

// 原则：数据展示类的接口，才需要维护，操作类的接口，不需要维护
export const NO_PERMISSION_CODE_URL_MAP: Record<PageRoute, IPageApiConfig> = {
    // 子网详情
    '/vpc/subnet/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/subnet/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/subnet',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/subnet/ip': {
        selector: '.detail-widget',
        backUrl: '#/vpc/subnet/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/subnet',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/subnet/reserve': {
        selector: '.detail-widget',
        backUrl: '#/vpc/subnet/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/subnet',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/subnet/reserveportpools': {
        selector: '.detail-widget',
        backUrl: '#/vpc/subnet/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/subnet',
                code: 'PermissionDeny'
            }
        ]
    },
    // 路由表详情
    '/vpc/route/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/route/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/rule/pagelist',
                code: 'PermissionDeny'
            }
        ]
    },
    // 安全组详情
    // '/vpc/security/detail': {
    //     selector: '.security-detail-main-wrap',
    //     backUrl: '#/vpc/security/list',
    //     apiCodeMap: [
    //         {
    //             path: '/api/network/v1/security/update_field',
    //             code: 'PermissionDeny'
    //         }
    //     ]
    // },
    // 弹性网卡详情
    '/vpc/eni/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/eni/list',
        apiCodeMap: [
            {
                path: '/api/enic',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/eni/ip': {
        selector: '.detail-widget',
        backUrl: '#/vpc/eni/list',
        apiCodeMap: [
            {
                path: '/api/enic',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/eni/ipv6': {
        selector: '.detail-widget',
        backUrl: '#/vpc/eni/list',
        apiCodeMap: [
            {
                path: '/api/enic',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/eni/security': {
        selector: '.detail-widget',
        backUrl: '#/vpc/eni/list',
        apiCodeMap: [
            {
                path: '/api/enic',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/eni/monitor': {
        selector: '.detail-widget',
        backUrl: '#/vpc/eni/list',
        apiCodeMap: [
            {
                path: '/api/enic',
                code: 'PermissionDeny'
            }
        ]
    },
    // 服务网卡详情
    '/vpc/endpoint/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/endpoint/list',
        apiCodeMap: [
            {
                path: '/api/snic/endpoint/detail',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/endpoint/security': {
        selector: '.detail-widget',
        backUrl: '#/vpc/endpoint/list',
        apiCodeMap: [
            {
                path: '/api/snic/endpoint/detail',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/endpoint/monitor': {
        selector: '.detail-widget',
        backUrl: '#/vpc/endpoint/list',
        apiCodeMap: [
            {
                path: '/api/snic/endpoint/detail',
                code: 'PermissionDeny'
            },
            {
                path: '/api/bcm/alarm/state/summary',
                code: 'AUTHORIZATION_ERROR'
            }
        ]
    },
    // HAVIP详情
    '/vpc/havip/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/havip/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/haVip/detail',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/havip/server': {
        selector: '.detail-widget',
        backUrl: '#/vpc/havip/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/haVip/detail',
                code: 'PermissionDeny'
            },
            {
                path: '/api/network/v1/haVip/bind/instances',
                code: 'PermissionDeny'
            }
        ]
    },
    //  企业安全组详情
    '/vpc/enterpriseSecurity/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/enterpriseSecurity/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/enterprise/security/detail',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/enterpriseSecurity/detail/bcc': {
        selector: '.detail-widget',
        backUrl: '#/vpc/enterpriseSecurity/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/enterprise/security/bind/instance/list',
                code: 'PermissionDeny'
            },
            {
                path: '/api/network/v1/enterprise/security/detail',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/enterpriseSecurity/detail/eni': {
        selector: '.detail-widget',
        backUrl: '#/vpc/enterpriseSecurity/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/enterprise/security/bind/instance/list',
                code: 'PermissionDeny'
            },
            {
                path: '/api/network/v1/enterprise/security/detail',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/enterpriseSecurity/detail/snic': {
        selector: '.detail-widget',
        backUrl: '#/vpc/enterpriseSecurity/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/enterprise/security/bind/instance/list',
                code: 'PermissionDeny'
            },
            {
                path: '/api/network/v1/enterprise/security/detail',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/enterpriseSecurity/detail/bbc': {
        selector: '.detail-widget',
        backUrl: '#/vpc/enterpriseSecurity/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/enterprise/security/bind/instance/list',
                code: 'PermissionDeny'
            },
            {
                path: '/api/network/v1/enterprise/security/detail',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/enterpriseSecurity/detail/blb': {
        selector: '.detail-widget',
        backUrl: '#/vpc/enterpriseSecurity/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/enterprise/security/bind/instance/list',
                code: 'PermissionDeny'
            },
            {
                path: '/api/network/v1/enterprise/security/detail',
                code: 'PermissionDeny'
            }
        ]
    },
    // ACL详情
    '/vpc/acl/manage': {
        selector: '.vpc-acl-manage',
        backUrl: '#/vpc/acl/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/acl/detail',
                code: 'PermissionDeny'
            }
        ]
    },
    // 参数模板详情 IP组
    '/vpc/param/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/set/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/ip/set/detail',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/param/address': {
        selector: '.detail-widget',
        backUrl: '#/vpc/set/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/ip/set/detail',
                code: 'PermissionDeny'
            },
            {
                path: '/api/network/v1/ip/address/list',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/param/association': {
        selector: '.detail-widget',
        backUrl: '#/vpc/set/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/ip/set/detail',
                code: 'PermissionDeny'
            },
            {
                path: '/api/network/v1/ip/set/bind/instances',
                code: 'PermissionDeny'
            }
        ]
    },
    // IP族
    '/vpc/group/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/group/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/ip/group/detail',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/group/address': {
        selector: '.detail-widget',
        backUrl: '#/vpc/group/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/ip/group/detail',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/group/association': {
        selector: '.detail-widget',
        backUrl: '#/vpc/group/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/ip/group/detail',
                code: 'PermissionDeny'
            },
            {
                path: '/api/network/v1/ip/group/bind/instances',
                code: 'PermissionDeny'
            }
        ]
    },
    // NAT网关详情
    '/vpc/nat/snat': {
        selector: '.detail-widget',
        backUrl: '#/vpc/nat/list',
        apiCodeMap: [
            {
                path: '/api/nat',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/nat/dnat': {
        selector: '.detail-widget',
        backUrl: '#/vpc/nat/list',
        apiCodeMap: [
            {
                path: '/api/nat/dnatRule/list',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/nat/monitor': {
        selector: '.detail-widget',
        backUrl: '#/vpc/nat/list',
        apiCodeMap: [
            {
                path: '/api/bcm/v3/csm/dimensions/values',
                code: 'AUTHORIZATION_ERROR'
            },
            {
                path: '/api/bcm/alarm/state/summary',
                code: 'AUTHORIZATION_ERROR'
            }
        ]
    },
    '/vpc/nat/gateway': {
        selector: '.detail-widget',
        backUrl: '#/vpc/nat/list',
        apiCodeMap: [
            {
                path: '/api/nat/limitRule',
                code: 'PermissionDeny'
            }
        ]
    },
    // ipv6网关详情
    '/vpc/ipv6gw/detail': {
        selector: '.vpc-ipv6gw-detail',
        backUrl: '#/vpc/nat/list',
        apiCodeMap: [
            {
                path: '/api/ipv6gw/detail',
                code: 'PermissionDeny'
            }
        ]
    },
    // vpn网关详情
    '/vpc/vpn/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/vpn/list',
        apiCodeMap: [
            {
                path: '/api/vpn/vpn/show',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/vpn/nat': {
        selector: '.detail-widget',
        backUrl: '#/vpc/vpn/list',
        apiCodeMap: [
            {
                path: '/api/vpn/vpn/show',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/vpn/idcnat': {
        selector: '.detail-widget',
        backUrl: '#/vpc/vpn/list',
        apiCodeMap: [
            {
                path: '/api/vpn/vpn/show',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/vpn/idcdnat': {
        selector: '.detail-widget',
        backUrl: '#/vpc/vpn/list',
        apiCodeMap: [
            {
                path: '/api/vpn/vpn/show',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/vpn/vpcdnat': {
        selector: '.detail-widget',
        backUrl: '#/vpc/vpn/list',
        apiCodeMap: [
            {
                path: '/api/vpn/vpn/show',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/vpn/monitor': {
        selector: '.detail-widget',
        backUrl: '#/vpc/vpn/list',
        apiCodeMap: [
            {
                path: '/api/vpn/vpn/show',
                code: 'PermissionDeny'
            },
            {
                path: '/api/vpn/vpnConn/list',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/vpn/ssl/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/sslvpn/list',
        apiCodeMap: [
            {
                path: '/api/vpn/vpnConn/show',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/vpn/ssl/user': {
        selector: '.detail-widget',
        backUrl: '#/vpc/sslvpn/list',
        apiCodeMap: [
            {
                path: '/api/vpn/sslvpn/user/list',
                code: 'PermissionDeny'
            },
            {
                path: '/api/vpn/vpnConn/show',
                code: 'PermissionDeny'
            }
        ]
    },

    '/vpc/vpn/ssl/monitor': {
        selector: '.detail-widget',
        backUrl: '#/vpc/sslvpn/list',
        apiCodeMap: [
            {
                path: '/api/vpn/sslvpn/user/list',
                code: 'PermissionDeny'
            },
            {
                path: '/api/vpn/vpnConn/show',
                code: 'PermissionDeny'
            }
        ]
    },
    // 专线网关详情
    '/vpc/dcgw/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/dcgw/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/dc/gw/detail',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/dcgw/hc': {
        selector: '.detail-widget',
        backUrl: '#/vpc/dcgw/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/dc/gw/detail',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/dcgw/nat': {
        selector: '.detail-widget',
        backUrl: '#/vpc/dcgw/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/dc/gw/detail',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/dcgw/idcnat': {
        selector: '.detail-widget',
        backUrl: '#/vpc/dcgw/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/dc/gw/detail',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/dcgw/idcdnat': {
        selector: '.detail-widget',
        backUrl: '#/vpc/dcgw/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/dc/gw/detail',
                code: 'PermissionDeny'
            }
        ]
    },
    '/vpc/dcgw/monitor': {
        selector: '.detail-widget',
        backUrl: '#/vpc/dcgw/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/dc/gw/detail',
                code: 'PermissionDeny'
            },
            {
                path: '/api/bcm/v1/csm/gateway/meta/all',
                code: 'AUTHORIZATION_ERROR'
            }
        ]
    },
    // 专线详情
    '/dc/instance/detail': {
        selector: '.detail-widget',
        backUrl: '#/dc/instance/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/dc/phy/detail',
                code: 'PermissionDeny'
            }
        ]
    },
    '/dc/instance/monitor': {
        selector: '.detail-widget',
        backUrl: '#/dc/instance/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/dc/phy/detail',
                code: 'PermissionDeny'
            }
        ]
    },
    // 通道详情
    '/dc/channel/monitor': {
        selector: '.detail-widget',
        backUrl: '#/dc/channel/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/dc/gw/detail',
                code: 'PermissionDeny'
            }
        ]
    }
};

export const getNoPermissionListener = () => {
    return {
        on: () => {
            EventBus.on(EventName.instanceNoPermission, (options: IPageApiConfig) => {
                const main = document.querySelector('#main');
                const replaceNode = main.querySelector(options.selector);
                if (replaceNode) {
                    replaceNode.innerHTML = '';
                    const root = ReactDom.createRoot(replaceNode);
                    root.render(<NoPermissionComp backUrl={options.backUrl} />);
                }
            });
        },
        un: () => {
            EventBus.un(EventName.instanceNoPermission);
        }
    };
};
