import _ from 'lodash';
import {MenuItem, cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
import {ndsPageUrl, dcPageUrl, vpcModulePages, crossDcPageUrl, CONSOLE_HEADER_TEXT_MAP} from './constants';
import {GlobalContext} from '@/types';
import {a} from 'e2e/playwright-report/trace/assets/testServerConnection-Dj8RHZjQ';

/** 获取路由参数 */
export const parseQuery = (hash: string) => {
    const queryString = hash?.split('?')?.[1];
    const pairs = queryString?.split('&');
    let query = {};
    if (pairs?.length) {
        // eslint-disable-next-line @typescript-eslint/prefer-for-of
        for (let i = 0; i < pairs.length; i++) {
            // 考虑到有可能因为未处理转义问题，
            // 导致value中存在**=**字符，因此不使用`split`函数
            const pair = pairs[i];
            if (!pair) {
                continue;
            }
            const index = pair.indexOf('=');
            // 没有**=**字符则认为值是**true**
            const key = index < 0 ? decodeURIComponent(pair) : decodeURIComponent(pair.slice(0, index));
            const value = index < 0 ? true : decodeURIComponent(pair.slice(index + 1));

            // 已经存在这个参数，且新的值不为空时，把原来的值变成数组
            if (query.hasOwnProperty(key)) {
                if (value !== true) {
                    query[key] = [].concat(query[key], value);
                }
            } else {
                query[key] = value;
            }
        }
    }
    return query;
};

export const getAllModulePages = (modulePages: Record<string, Record<string, string>>) => {
    const modules = Object.values(modulePages);
    if (Array.isArray(modules)) {
        return modules.reduce((res, item) => {
            res = [...res, ...Object.values(item)];
            return res;
        }, []);
    }
    return [];
};

export const getAllModuleIndex = (modulePages: Record<string, Record<string, string>>) => {
    const modules = Object.values(modulePages);
    if (Array.isArray(modules)) {
        return modules.map(item => item.index).filter(item => !!item);
    }
    return [];
};

export const getModuleIndexByHashPath = (url: string) => {
    const getSubModuleIndex = (modulePages: Record<string, Record<string, string>>) => {
        const modules = Object.values(modulePages);
        const subModule = modules.find(module => {
            for (let key in module) {
                // 兼容只声明前缀的场景
                if (module[key] === url || url.startsWith(module[key])) {
                    return true;
                }
            }
        });
        return subModule ? subModule.index : '';
    };
    const curModule = getCurrentSystemByHash();
    let moduleIndex = '';
    switch (curModule) {
        case 'dc': {
            moduleIndex = getSubModuleIndex(dcPageUrl) || dcPageUrl.dc.index;
            break;
        }
        case 'crossdc': {
            moduleIndex = getSubModuleIndex(crossDcPageUrl) || crossDcPageUrl.crossDc.index;
            break;
        }
        case 'diagnosis': {
            moduleIndex = getSubModuleIndex(ndsPageUrl) || ndsPageUrl.flowlog.index;
            break;
        }
        default: {
            moduleIndex = getSubModuleIndex(vpcModulePages) || vpcModulePages.vpc.index;
        }
    }
    return moduleIndex;
};

/** 网络诊断服务控制台判断 */
export const netDiagnosisMatchUrl = (url: string) => {
    const matchUrlArr = getAllModulePages(ndsPageUrl);
    let flag = false;
    if (url) {
        flag = matchUrlArr.some(item => url.includes(item));
    }
    return flag;
};

export const resetGlobalRegion = () => {
    window.$framework.events.fire(window.$framework.EVENTS.ENTER_ACTION_COMPLETE, {
        region: {
            id: null,
            globalOnly: true
        }
    });
};

/** 获取当前url path */
export const getPathByUrlOrHash = (url: string = location.href) => {
    if (typeof url !== 'string') return;
    return url.split('#')?.[1]?.split('?')?.[0];
};

/** 判断当前系统模块 */
export const getCurrentSystemByHash = (hash = location.hash) => {
    if (hash.startsWith('#/dc') || hash.startsWith('#/channel')) {
        resetGlobalRegion();
        return 'dc';
    }
    if (hash.startsWith('#/crossdc')) {
        resetGlobalRegion();
        return 'crossdc';
    }
    if (netDiagnosisMatchUrl(getPathByUrlOrHash(hash))) {
        return 'diagnosis';
    }
    return 'vpc';
};

/** 获取当前控制台title */
export const getAppTitle = () => {
    return CONSOLE_HEADER_TEXT_MAP[getCurrentSystemByHash()] || '';
};

/** 需要隐藏的菜单 */
export const hideMenu = (url: string, menus: MenuItem[]) => {
    menus.forEach((menu: MenuItem) => {
        if (menu.key === url) {
            menu.isNavMenu = false;
        }
        if (menu.children) {
            hideMenu(url, menu.children);
        }
    });
};

/** 网关限速多白名单控制列表页入口动态展示 */
export const redirectGatewayLimitList = (menuList: MenuItem[], whiteAuthMap: GlobalContext) => {
    const {GlrPcWhiteList, GlrEtWhiteList, GlrCsnWhiteList} = whiteAuthMap.commonWhite;
    _.find(menuList, (item: MenuItem, idx) => {
        if (item.key === vpcModulePages.qos.qosParent) {
            _.find(item.children, (child: MenuItem, childIdx) => {
                if (child.key === vpcModulePages.gatewaySpeedLimit.index) {
                    if (
                        [/^#\/vpc\/gateway\/pc\//, /^#\/vpc\/gateway\/et\//, /^#\/vpc\/gateway\/csn\//].some(path =>
                            path.test(location.hash)
                        )
                    ) {
                        if (GlrPcWhiteList) {
                            child.key = vpcModulePages.gatewaySpeedLimit.index;
                            location.hash = `#/${vpcModulePages.gatewaySpeedLimit.index}`;
                        } else if (GlrEtWhiteList) {
                            location.hash = `#/${vpcModulePages.gatewaySpeedLimit.gatewayEt}`;
                            child.key = vpcModulePages.gatewaySpeedLimit.gatewayEt;

                            item.children[childIdx + 2].activeMenuKey = vpcModulePages.gatewaySpeedLimit.gatewayEt;
                        } else if (GlrCsnWhiteList) {
                            child.key = vpcModulePages.gatewaySpeedLimit.gatewayCsn;
                            // location.hash = `#/${vpcModulePages.gatewaySpeedLimit.gatewayCsn}`;
                        } else {
                            // 三个白名单都没有则重置为初始
                            child.key = vpcModulePages.gatewaySpeedLimit.index;
                        }
                    } else {
                        if (GlrPcWhiteList) {
                            child.key = vpcModulePages.gatewaySpeedLimit.index;
                        } else if (GlrEtWhiteList) {
                            child.key = vpcModulePages.gatewaySpeedLimit.gatewayEt;

                            item.children[childIdx + 2].activeMenuKey = vpcModulePages.gatewaySpeedLimit.gatewayEt;
                        } else if (GlrCsnWhiteList) {
                            child.key = vpcModulePages.gatewaySpeedLimit.gatewayCsn;
                        } else {
                            // 三个白名单都没有则重置为初始
                            child.key = vpcModulePages.gatewaySpeedLimit.index;
                        }
                    }
                }
            });
        }
    });
};

/** 寻找子字符串在父字符串中出现的所有下标 */
export const findSonStrAllIdxInParent = (parent, son) => {
    const findIdx = [];
    let startIndex = 0;
    let index;
    while ((index = parent.indexOf(son, startIndex)) !== -1) {
        findIdx.push(index);
        startIndex = index + 1;
    }
    return findIdx;
};

/** 兼容其他产品对VPC页面地址的依赖，只对跨产品跳转生效 */
export const replaceHashSymbol = (hashList: string[]) => {
    let hash = window.location.hash;
    if (hash.includes('~')) {
        hash = hash.replace('~', '?');
    }
    const matchHash = hashList.find(item => hash.includes(item));
    if (matchHash) {
        const findHashIdx = findSonStrAllIdxInParent(hash, matchHash);
        const isMultiHash = findHashIdx.length > 1;
        if (isMultiHash) {
            hash = hash.substring(0, findHashIdx[1]);
        }
    }
    window.location.hash = hash;
};

/**新开浏览器页post请求下载 */
export const postDownload = (url, payload) => {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = url;
    form.target = '_blank';
    for (const key in payload) {
        if (payload.hasOwnProperty(key)) {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = key;
            input.value = payload[key];
            form.appendChild(input);
        }
    }
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
};

/**将对象中的所有属性转成url参数 */
export const ObjectToQuery = obj => {
    if (!obj || typeof obj !== 'object') {
        return '';
    }

    return Object.keys(obj)
        .map(key => {
            const encodedKey = encodeURIComponent(key);
            const encodedValue = encodeURIComponent(obj[key]);
            return `${encodedKey}=${encodedValue}`;
        })
        .join('&');
};

/** 重新设置region */
export const resetNewRegion = (region: string) => {
    if (!!region) {
        const currentRegion = (window as any).$context.getCurrentRegionId();
        if (region !== currentRegion) {
            (window as any).$context.setRegion(region);
        }
    }
};

/**
 * 处理二进制流文件下载
 */
export const handleDownloadBinaryFile = async (downloadUrl: string, mimeType?: string, fileName?: string) => {
    try {
        const res = await fetch(downloadUrl);
        if (!res.ok) {
            throw new Error(`HTTP error! status: ${res.status}`);
        }
        const arrayBuffer = await res.arrayBuffer();
        const blob = new Blob([arrayBuffer], {type: mimeType || 'application/octet-stream'});
        const url = URL.createObjectURL(blob);
        const aDom = document.createElement('a');
        aDom.href = url;
        if (fileName) {
            aDom.download = fileName;
        }
        document.body.appendChild(aDom);
        aDom.click();

        URL.revokeObjectURL(url);
        document.body.removeChild(aDom);
    } catch (error) {
        console.error(i18n.t('下载失败：'), error);
    }
};