/**
 * 修改所有 .s-input.s-input-prefix-container .s-input-area input 元素的 placeholder 为 "Search"
 */

// 修改 placeholder 的函数
function updatePlaceholders() {
    const inputs = document.querySelectorAll('.s-input.s-input-prefix-container .s-input-area input');
    inputs.forEach(input => {
        input.setAttribute('placeholder', 'Search');
    });
}

// 使用 MutationObserver 监听 DOM 变化，确保动态添加的元素也能被修改
function initPlaceholderObserver() {
    // 立即执行一次
    updatePlaceholders();
    
    // 创建观察器实例
    const observer = new MutationObserver(function(mutations) {
        let shouldUpdate = false;
        
        mutations.forEach(function(mutation) {
            // 检查是否有新增的节点
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                mutation.addedNodes.forEach(function(node) {
                    // 如果是元素节点
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // 检查新增的节点或其子节点是否包含目标输入框
                        const hasTargetInputs = node.matches && node.matches('.s-input.s-input-prefix-container .s-input-area input') ||
                                              node.querySelector && node.querySelector('.s-input.s-input-prefix-container .s-input-area input');
                        
                        if (hasTargetInputs) {
                            shouldUpdate = true;
                        }
                    }
                });
            }
            
            // 检查属性变化（如果有输入框的 placeholder 被其他代码修改）
            if (mutation.type === 'attributes' && 
                mutation.attributeName === 'placeholder' && 
                mutation.target.matches('.s-input.s-input-prefix-container .s-input-area input')) {
                shouldUpdate = true;
            }
        });
        
        if (shouldUpdate) {
            // 使用 setTimeout 确保 DOM 更新完成后再执行
            setTimeout(updatePlaceholders, 0);
        }
    });
    
    // 开始观察
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['placeholder']
    });
    
    return observer;
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initPlaceholderObserver);
} else {
    initPlaceholderObserver();
}

// 导出函数供其他模块使用
export { updatePlaceholders, initPlaceholderObserver };
