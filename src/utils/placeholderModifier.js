// 修改所有 .s-input.s-input-prefix-container .s-input-area input 的 placeholder 为 "Search"
function updatePlaceholders() {
    const inputs = document.querySelectorAll('.s-input.s-input-prefix-container .s-input-area input');
    inputs.forEach(input => {
        input.placeholder = 'Search';
    });
}

// 定时检查并更新
setInterval(updatePlaceholders, 1000);

// 页面加载后立即执行一次
document.addEventListener('DOMContentLoaded', updatePlaceholders);
