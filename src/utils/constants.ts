/** 产品自定义事件 */
export const EventName = {
    productActive: 'PRODUCT_ACTIVE', // 子产品激活
    reloadWhiteListStart: 'RELOAD_WHITE_LIST_START', // 开始更新白名单状态
    reloadWhiteListFinish: 'RELOAD_WHITE_LIST_FINISH', // 结束更新白名单状态
    instanceNotFound: 'INSTANCE_NOT_FOUND', // 实例不存在
    instanceNoPermission: 'INSTANCE_NO_PERMISSION', // 没权限访问
    closureByRegion: 'CLOSURE_BY_REGION' //  封禁region
};

export const activeServiceType = {
    nat: 'NAT',
    vpn: 'VPN',
    peerconn: 'PEERCONN',
    dc: 'DC'
};

/** 检查是否为英文环境 */
const isEnglishLocale = () => {
    // 检查URL参数中是否包含locale=en
    if (typeof window !== 'undefined' && window.location) {
        return window.location.search.indexOf('locale=en') > -1;
    }
    return false;
};

/** 为链接添加locale参数 */
const addLocaleToLink = (link: string) => {
    if (!isEnglishLocale()) {
        return link;
    }

    const separator = link.includes('?') ? '&' : '?';
    return `${link}${separator}locale=en-us`;
};

/** 公共服务link */
export const publcServcie = {
    resourceLink: addLocaleToLink('/resource/#/'),
    tagLink: addLocaleToLink('/tag/#/tag/instance/list'),
    quotaLink: addLocaleToLink('/quota_center/#/quota/network/list'),
    csnLink: addLocaleToLink('/csn/#/csn/instance/list'),
    etLink: addLocaleToLink('/network/#/dc/instance/list'),
    smartSerLink: addLocaleToLink('/smart_wan/?_=1733903674569#/sdwan/instance/auditing')
};

/** vpc子产品路由 */
export const vpcModulePages = {
    overview: {
        index: '/vpc/overview'
    },
    selfProblemDiagnose: {
        index: '/vpc/diagnosis'
    },
    topo: {
        index: '/vpc/topology'
    },
    vpc: {
        index: '/vpc/instance/list',
        vpcCreate: '/vpc/instance/create',
        vpcDetail: '/vpc/instance/detail'
    },
    subnet: {
        index: '/vpc/subnet/list',
        subnetCreate: '/vpc/subnet/create',
        subnetDetail: '/vpc/subnet/detail',
        subnetIP: '/vpc/subnet/ip',
        subnetReverse: '/vpc/subnet/reserve',
        subnetPool: '/vpc/subnet/reserveportpools'
    },
    security: {
        index: '/vpc/security/list',
        securityCreate: '/vpc/security/create',
        securityDetail: '/vpc/security/detail'
    },
    enterpriseSec: {
        index: '/vpc/enterpriseSecurity/list',
        enterpriseSecCreate: '/vpc/enterpriseSecurity/create',
        enterpriseSecDetail: '/vpc/enterpriseSecurity/detail'
    },
    route: {
        index: '/vpc/route/list',
        routeDetail: '/vpc/route/detail',
        routeCreate: '/vpc/route/create',
        routeBindTgw: '/vpc/route/bindTgw'
    },
    eni: {
        index: '/vpc/eni/list',
        eniParent: '/vpc/eni/parent', // 唯一key值 隐藏整个菜单用 无实际意义
        eniCreate: '/vpc/eni/create',
        eniDetail: '/vpc/eni/detail',
        eniIpDetail: '/vpc/eni/ip',
        eniIpv6Detail: '/vpc/eni/ipv6',
        eniSecurity: '/vpc/eni/security',
        eniMonitor: '/vpc/eni/monitor'
    },
    endpoint: {
        index: '/vpc/endpoint/list',
        endPointCreate: '/vpc/endpoint/create',
        endPointUpgrade: '/vpc/endpoint/upgrade',
        endpointDetail: '/vpc/endpoint/detail',
        endpointSecurity: '/vpc/endpoint/security',
        endpointMonitor: '/vpc/endpoint/monitor'
    },
    havip: {
        index: '/vpc/havip/list',
        havipDetail: '/vpc/havip/detail',
        havipServer: '/vpc/havip/server'
    },
    acl: {
        index: '/vpc/acl/list',
        aclManage: '/vpc/acl/manage'
    },
    param: {
        index: '/vpc/set/list',
        paramsIpGroupList: '/vpc/group/list',
        paramsIpDetail: '/vpc/param/detail',
        paramsIpDetailMana: '/vpc/param/address',
        paramsIpDetailAssoc: '/vpc/param/association',
        paramsGroupDetail: '/vpc/group/detail',
        paramsIpGroupMana: '/vpc/group/address',
        paramsIpGroupAssoc: '/vpc/group/association'
    },
    paramIpGroup: {
        index: '/vpc/group/list'
    },
    nat: {
        index: '/vpc/nat/list',
        natCreate: '/vpc/nat/create',
        natAuth: '/vpc/nat/auth',
        natDetail: '/vpc/nat/detail',
        natSnat: '/vpc/nat/snat',
        natDnat: '/vpc/nat/dnat',
        natMonitor: '/vpc/nat/monitor',
        natGatewayMonitor: '/vpc/nat/gateway',
        natUpgrade: '/vpc/nat/upgrade'
    },
    privateNat: {
        index: '/vpc/privateNat/list',
        natIp: '/vpc/nat/natIp'
    },
    ipv6: {
        index: '/vpc/ipv6gw/list',
        IPv6List: '/vpc/ipv6gw/list',
        IPv6Create: '/vpc/ipv6gw/create',
        IPv6Detail: '/vpc/ipv6gw/detail',
        IPv6Upgrade: '/vpc/ipv6gw/upgrade'
    },
    vpn: {
        index: '/vpc/vpn/list',
        vpnCreate: '/vpc/vpn/create',
        vpnSslList: '/vpc/sslvpn/list',
        vpnGreList: '/vpc/grevpn/list',
        vpnAuth: '/vpc/vpn/auth',
        vpnDetail: '/vpc/vpn/detail',
        vpnRouteMana: '/vpc/vpn/route',
        vpnDetailNat: '/vpc/vpn/nat',
        vpnDetailIdcnat: '/vpc/vpn/idcnat',
        vpnDetailIdcdnat: '/vpc/vpn/idcdnat',
        vpnDetailVpcdnat: '/vpc/vpn/vpcdnat',
        vpnDetailMonitor: '/vpc/vpn/monitor',
        vpnConnCreate: '/vpc/vpn/conn/create',
        sslVpnConnDetail: '/vpc/vpn/ssl/detail',
        sslVpnConnUser: '/vpc/vpn/ssl/user',
        sslVpnConnMonitor: '/vpc/vpn/ssl/monitor',
        sslVpnUpgrade: '/vpn/ssl/upgradeClient'
    },
    sslVpn: {
        index: '/vpc/sslvpn/list'
    },
    greVpn: {
        index: '/vpc/grevpn/list'
    },
    peerconn: {
        index: '/vpc/peerconn/list',
        peerconnCreate: '/vpc/peerconn/create/v2',
        peerconnCreateCross: '/vpc/peerconn/create_cross',
        peerconnAuth: '/vpc/peerconn/auth',
        peerconnDetail: '/vpc/peerconn/detail',
        peerconnMonitor: '/vpc/peerconn/monitor',
        peerconnUpgrade: '/vpc/peerconn/upgrade',
        peerconnUpgradeSuccess: '/vpc/peerconn/upgrade/success',
        peerconnCrossAudit: '/vpc/peerconn/create/audit',
        peerconnCrossAuditing: '/vpc/peerconn/create/auditing',
        peerconnRecharge: '/vpc/peerconn/recharge'
    },
    crossPeerconn: {
        index: '/vpc/crossPeerconn/list'
    },
    dcgw: {
        index: '/vpc/dcgw/list',
        dcgwCreate: '/vpc/dcgw/create',
        dcgwEdit: '/vpc/dcgw/edit',
        dcgwList: '/vpc/dcgw/list',
        dcgwDetail: '/vpc/dcgw/detail',
        dcgwDetailHc: '/vpc/dcgw/hc',
        dcgwDetailNat: '/vpc/dcgw/nat',
        dcgwDetailIdcnat: '/vpc/dcgw/idcnat',
        dcgwDetailIdcdnat: '/vpc/dcgw/idcdnat',
        dcgwMonitor: '/vpc/dcgw/monitor'
    },
    l2gw: {
        index: '/vpc/l2gw/list',
        l2gwCreate: '/vpc/l2gw/create',
        l2gwDetail: '/vpc/l2gw/detail',
        l2gwMonitor: '/vpc/l2gw/monitor',
        l2gwChannelDetail: '/vpc/l2gw/tunnel/detail',
        l2gwChannelArp: '/vpc/l2gw/tunnel/arp',
        l2gwChannelIp: '/vpc/l2gw/tunnel/ip',
        l2gwChannelMonitor: '/vpc/l2gw/tunnel/monitor',
        l2gwConnCreate: '/vpc/l2gw/conn/create'
    },
    multicasting: {
        index: '/vpc/multicasting/list',
        multicastingCreate: '/vpc/multicasting/create',
        multicastingDetail: '/vpc/multicasting/detail',
        multicastingManage: '/vpc/multicasting/ip'
    },
    qos: {
        index: '/vpc/qos/list',
        qosParent: '/vpc/qos/parent', // 唯一key值 隐藏整个菜单用 无实际意义
        qosCreate: '/vpc/qos/create',
        qosDetail: '/vpc/qos/detail'
    },
    gatewaySpeedLimit: {
        index: '/vpc/gateway/pc/list',
        gatewayEt: '/vpc/gateway/et/list',
        gatewayCsn: '/vpc/gateway/csn/list',
        gatewayCreate: '/vpc/gateway/create'
    },
    gatewayEt: {
        index: '/vpc/gateway/et/list'
    },
    gatewayCsn: {
        index: '/vpc/gateway/csn/list'
    }
};

/** 专线独立控制台 */
export const dcPageUrl = {
    dc: {
        index: '/dc/instance/list',
        dcCreate: '/dc/instance/create',
        dcDetail: '/dc/instance/detail',
        dcMonitor: '/dc/instance/monitor',
        dcChannelList: '/dc/channel/list',
        dcChannelCreate: '/dc/channel/create',
        dcChannelDetail: '/dc/channel/detail',
        dcChannelRoute: '/dc/channel/routelist',
        dcChannelCheck: '/dc/channel/check',
        dcChannelMonitor: '/dc/channel/monitor',
        dcLineList: '/dc/info/list',
        dcPortPay: '/dc/instance/createPort',
        dcAuth: '/dc/landing',
        dcFailoverPlan: '/dc/failover/plan',
        dcFailoverDetail: '/dc/failover/detail'
    }
};

/** 跨境专线独立控制台 */
export const crossDcPageUrl = {
    crossDc: {
        /** 跨境专线 */
        index: '/crossdc/notopened/list',
        openCrossDc: '/crossdc/opened/list',
        userList: '/crossdc/user/list'
    }
};

/** 网络诊断服务独立控制台 */
export const ndsPageUrl = {
    flowlog: {
        index: '/vpc/flowlog/list',
        flowLogCreate: '/vpc/flowlog/create'
    },
    flowMirror: {
        index: '/vpc/mirror/list',
        flowMirrorFilter: '/vpc/filterRuleGroup/list',
        flowMirrorCreate: '/vpc/mirror/create',
        flowMirrorFilterCreate: '/vpc/filterRuleGroup/create',
        flowMirrorFilterDetail: '/vpc/filterRuleGroup/detail',
        flowMirrorFilterSession: '/vpc/filterRuleGroup/detail/mirror'
    },
    flowMirrorFilter: {
        index: '/vpc/filterRuleGroup/list'
    },
    probe: {
        index: '/vpc/probe/list',
        probeCreate: '/vpc/probe/create',
        probeEdit: '/vpc/probe/edit',
        probeDetail: '/vpc/probe/detail',
        probeMonitor: '/vpc/probe/monitor'
    },
    pathAnalysis: {
        index: '/vpc/pathanalise/list',
        pathAnalysisCreate: '/vpc/pathanalise/create',
        pathAnalysisDetail: '/vpc/pathanalise/detail',
        portVerification: '/support/#/port'
    },
    diagnosis: {
        index: '/vpc/instance/diagnosis',
        diagnosisDetail: '/vpc/diagnosis/detail'
    }
};

/** 不同控制台重定向首页url */
export const redirectIndexPageUrl = {
    vpc: vpcModulePages.overview.index,
    dc: dcPageUrl.dc.index,
    crossdc: crossDcPageUrl.crossDc.index,
    diagnosis: ndsPageUrl.flowlog.index
};

/** 智能助手模块url及场景码 */
export const ASSIST_SENSE_MAP = [
    {urlReg: [/^\/vpc\/subnet/], senseCode: 'subnet_console'},
    {urlReg: [/^\/vpc\/route/], senseCode: 'route_console'},
    {urlReg: [/^\/vpc\/eni/], senseCode: 'snic_console'},
    {urlReg: [/^\/vpc\/endpoint/], senseCode: 'enic_console'},
    {urlReg: [/^\/vpc\/havip/], senseCode: 'havip_console'},
    {urlReg: [/^\/vpc\/security/], senseCode: 'security_console'},
    {urlReg: [/^\/vpc\/acl/], senseCode: 'acl_console'},
    {urlReg: [/^\/vpc\/set/, /^\/vpc\/group/, /^\/vpc\/param/], senseCode: 'param_console'},
    {urlReg: [/^\/vpc\/nat/], senseCode: 'nat_console'},
    {urlReg: [/^\/vpc\/ipv6gw/], senseCode: 'ipv6gw_console'},
    {urlReg: [/^\/vpc\/vpn/, /^\/vpc\/grevpn/, /^\/vpc\/sslvpn/], senseCode: 'vpn_console'},
    {urlReg: [/^\/vpc\/peerconn/], senseCode: 'peerconn_console'},
    {urlReg: [/^\/vpc\/dcgw/], senseCode: 'dcgw_console'},
    {urlReg: [/^\/vpc\/flowlog/], senseCode: 'flowlog_console'},
    {urlReg: [/^\/vpc\/gateway/], senseCode: 'gateway_console'},
    {urlReg: [/^\/vpc\/mirror/, /^\/vpc\/filterRuleGroup/], senseCode: 'flowmirror_console'},
    {urlReg: [/^\/vpc\/probe/], senseCode: 'probe_console'},
    {urlReg: [/^\/vpc\/pathanalise/], senseCode: 'path_console'},
    {urlReg: [/^\/vpc\/l2gw/], senseCode: 'l2gw_console'},
    {urlReg: [/^\/dc\/in/, /^\/dc\/landing/], senseCode: 'et_console'},
    {urlReg: [/^\/dc\/chan/], senseCode: 'channel_console'}
];

// 需要校验开通状态的页面
export const NEED_CHECK_ACTIVE_STATUS_PAGE_MAP = [
    {
        path: [/^\/vpc\/peerconn/],
        authKey: 'peerConnSts',
        redirectUrl: '#/vpc/peerconn/auth'
    },
    {
        path: [/^\/vpc\/vpn/, /^\/vpc\/sslvpn/, /^\/vpc\/grevpn/],
        authKey: 'vpnSts',
        redirectUrl: '#/vpc/vpn/auth'
    },
    {
        path: [/^\/vpc\/nat/],
        authKey: 'natSts',
        redirectUrl: '#/vpc/nat/auth'
    },
    {
        path: [/^\/dc/],
        authKey: 'etSts',
        redirectUrl: '#/dc/landing'
    }
];

// 需要校验白名单状态的页面
export const NEED_CHECK_WHITELIST_PAGE_MAP = [
    {
        path: [/^\/vpc\/endpoint/],
        // specialWhite: true表示使用特殊白名单，false表示通用白名单commonWhiteList
        specialWhite: true,
        key: 'endPointWhite',
        redirectUrl: '#/vpc/instance/list'
    },
    {
        path: [/^\/vpc\/eni/],
        specialWhite: true,
        key: 'enicWhite',
        redirectUrl: '#/vpc/instance/list'
    },
    {
        path: [/^\/vpc\/havip/],
        specialWhite: false,
        key: 'HaVipWhiteList',
        redirectUrl: '#/vpc/instance/list'
    },
    {
        path: [/^\/vpc\/topology/],
        specialWhite: true,
        key: 'njRegionWhiteList',
        redirectUrl: '#/vpc/instance/list'
    },
    {
        path: [/^\/vpc\/set/],
        specialWhite: false,
        key: 'IpCollectionWhiteList',
        redirectUrl: '#/vpc/instance/list'
    },
    {
        path: [/^\/vpc\/vpn/],
        specialWhite: true,
        key: 'vpnWhite',
        redirectUrl: '#/vpc/instance/list'
    },
    {
        path: [/^\/vpc\/peerconn/],
        specialWhite: true,
        key: 'peerconnWhite',
        redirectUrl: '#/vpc/instance/list'
    },
    {
        path: [/^\/vpc\/l2gw/],
        specialWhite: true,
        key: 'l2gwSupportRegion',
        redirectUrl: '#/vpc/instance/list'
    },
    {
        path: [/^\/vpc\/qos/],
        specialWhite: true,
        key: 'qosStrategyWhite',
        redirectUrl: '#/vpc/instance/list'
    },
    {
        path: [/^\/vpc\/gateway\/pc/, /^\/vpc\/gateway\/et/, /^\/vpc\/gateway\/csn/],
        specialWhite: true,
        key: 'gatewayLimitSpeedWhite',
        redirectUrl: '#/vpc/instance/list'
    },
    {
        path: [/^\/vpc\/flowlog/],
        specialWhite: false,
        key: 'FlowlogWhiteList',
        redirectUrl: '#/vpc/mirror/list'
    },
    {
        path: [/^\/vpc\/pathanalise/],
        specialWhite: false,
        key: 'pathAnaliseWhitelist',
        redirectUrl: '#/vpc/mirror/list'
    },
    {
        path: [/^\/dc\/info/],
        specialWhite: false,
        key: 'DxmDcPhyInfo',
        redirectUrl: '#/dc/instance/list'
    }
];

/** 控制台菜单头部文案 */
export const CONSOLE_HEADER_TEXT_MAP = {
    vpc: '私有网络 VPC',
    dc: '专线接入',
    diagnosis: '网络诊断服务 NDS',
    crossdc: '跨境专线管理'
};

/** 需要白名单和功能清单判断显隐的菜单 */
export const NEED_CHECK_WHITELIST_MENU = [
    {
        authKey: 'njRegionWhiteList',
        specialWhite: true,
        menuKey: vpcModulePages.topo.index
    },
    {
        authKey: 'netcardWhite',
        specialWhite: true,
        menuKey: vpcModulePages.eni.eniParent
    },
    {
        authKey: 'enicWhite',
        specialWhite: true,
        menuKey: vpcModulePages.eni.index
    },
    {
        authKey: 'endPointWhite',
        specialWhite: true,
        menuKey: vpcModulePages.endpoint.index
    },
    {
        authKey: 'peerconnWhite',
        specialWhite: true,
        menuKey: vpcModulePages.peerconn.index
    },
    {
        authKey: 'HaVipWhiteList',
        specialWhite: false,
        menuKey: vpcModulePages.havip.index
    },
    {
        authKey: 'IpCollectionWhiteList',
        specialWhite: false,
        menuKey: vpcModulePages.param.index
    },
    {
        authKey: 'natAuthFlag',
        specialWhite: true,
        menuKey: vpcModulePages.nat.index
    },
    {
        authKey: 'vpnAuthFlag',
        specialWhite: true,
        menuKey: vpcModulePages.vpn.index
    },
    {
        authKey: 'l2gwSupportRegion',
        specialWhite: true,
        menuKey: vpcModulePages.l2gw.index
    },
    {
        authKey: 'qosWhite',
        specialWhite: true,
        menuKey: vpcModulePages.qos.qosParent
    },
    {
        authKey: 'qosStrategyWhite',
        specialWhite: true,
        menuKey: vpcModulePages.qos.index
    },
    {
        authKey: 'gatewayLimitSpeedWhite',
        specialWhite: true,
        menuKey: vpcModulePages.gatewaySpeedLimit.index
    },
    {
        authKey: 'DxmDcPhyInfo',
        specialWhite: false,
        menuKey: dcPageUrl.dc.dcLineList
    },
    {
        authKey: 'ChinaUnicomWhiteList',
        specialWhite: false,
        menuKey: `/network/#${crossDcPageUrl.crossDc.index}`
    },
    {
        authKey: 'FlowlogWhiteList',
        specialWhite: false,
        menuKey: ndsPageUrl.flowlog.index
    },
    {
        authKey: 'pathAnaliseWhitelist',
        specialWhite: false,
        menuKey: ndsPageUrl.pathAnalysis.index
    },
    {
        authKey: 'NetworkUnSupportXS',
        specialWhite: true,
        menuKey: ndsPageUrl.pathAnalysis.portVerification
    }
];

/** 创建页region平铺 */
export const REGION_FLAT_PAGE_URL = [
    {url: [/^\/vpc\/endpoint\/create/]},
    {url: [/^\/vpc\/mirror\/create/]},
    {url: [/^\/vpc\/ipv6gw\/create/]},
    {url: [/^\/vpc\/nat\/create/]},
    {url: [/^\/vpc\/vpn\/create/]}
];

/** 无需展示region的页面url */
export const NEED_HIDE_REGION_PAGE = [
    /^\/vpc\/pathanalise/,
    /^\/vpc\/instance\/diagnosis/,
    /^\/vpc\/diagnosis\/detail/
];

/** 其他产品跳转VPC时需兼容的hash */
export const NEED_RESOLVE_HASH_LIST = ['#/vpc/subnet/ip', '#/vpc/security/detail', '#/vpc/enterpriseSecurity/detail'];
