import _ from 'lodash';
import Assist from './assist';
import {GlobalContext, NeedActiveAuthSts} from '@/types';
import {
    getCurrentSystemByHash,
    ASSIST_SENSE_MAP,
    NEED_CHECK_ACTIVE_STATUS_PAGE_MAP,
    NEED_CHECK_WHITELIST_PAGE_MAP,
    NEED_HIDE_REGION_PAGE
} from '@/utils';
import FLAG from '@/flags';

/** 获取当前智能助手场景码 */
const getAssistSenseCode = (path: string) => {
    let senseCode = '';
    const module = _.find(ASSIST_SENSE_MAP, item => item.urlReg.some(url => url.test(path)));
    if (module) {
        senseCode = module.senseCode;
    }
    return senseCode;
};

/** 渲染智能助手 */
export const mountAssist = (e: any) => {
    const path = e?.newURL?.split('#')?.[1];
    try {
        Assist.onCloseAIAssist();
        const senseCode = getAssistSenseCode(path);
        if (senseCode) {
            Assist.init(senseCode);
        } else {
            Assist.init();
        }
    } catch (error) {}
};

/** 获取需要跳转的权限key和url */
const getStsRedirectPageKeyUrl = (path: string) => {
    let key = '';
    let url = '';
    const module = _.find(NEED_CHECK_ACTIVE_STATUS_PAGE_MAP, item => item.path.some(url => url.test(path)));
    if (module) {
        const {authKey, redirectUrl} = module;
        key = authKey;
        url = redirectUrl;
    }
    return {key, url};
};

/** 获取需要check的白名单key和url */
const getWhiteListRedirectPageKeyUrl = (path: string) => {
    const module = NEED_CHECK_WHITELIST_PAGE_MAP.find(item => item.path.some(url => url.test(path)));
    if (module) {
        const {key, redirectUrl: url, specialWhite} = module;
        return {key, url, specialWhite};
    }
    return {key: '', url: '', specialWhite: false};
};

// 访问未开通模块，需要重定向到开通页
export const noAuthRedirect = (e: any, authMap: Pick<GlobalContext, NeedActiveAuthSts>) => {
    const path = e.newURL.split('#')[1];
    try {
        const {key, url} = getStsRedirectPageKeyUrl(path);
        if (key && url && !authMap[key]) {
            window.location.hash = url;
        }
    } catch (error) {}
};

// 监听需要白名单才能访问的URL
export const noWhiteRedirect = (e: any, authMap: GlobalContext) => {
    const path = e.newURL.split('#')[1];
    const {key, url, specialWhite} = getWhiteListRedirectPageKeyUrl(path);
    try {
        if (key && url) {
            const inWhite = specialWhite ? authMap[key] : authMap.commonWhite[key];
            if (!inWhite) {
                window.location.hash = url;
            }
        }
    } catch (error) {}
};

/** 切换独立控制台url时重新加载菜单 */
export const switchConsoleReload = (e: HashChangeEvent) => {
    const {oldURL, newURL} = e;
    const oldPath = oldURL?.split?.('#')?.[1];
    const newPath = newURL?.split?.('#')?.[1];
    const newModule = getCurrentSystemByHash('#' + newPath);
    const oldModule = getCurrentSystemByHash('#' + oldPath);

    if (newModule !== oldModule) {
        location.hash = '#' + newPath;
        location.reload();
    }
};

/**隐藏头部region处理*/
export const hideHeaderRegionArea = (e: any) => {
    const {newURL} = e;
    const hash = newURL?.split?.('#')?.[1];
    const isShouldHide = NEED_HIDE_REGION_PAGE.some(item => item.test(hash));
    if (isShouldHide) {
        try {
            // 关闭顶部region栏
            window.$framework.events.fire(window.$framework.EVENTS.HIDE_REGION_SWITCHER);
        } catch (e) {}
    } else {
        window.$framework.events.fire(window.$framework.EVENTS.SHOW_REGION_SWITCHER);
    }
};

export const removeDom = () => {
    const element = document.getElementById('pathanaliseId');
    if (element) {
        // element.style.display = 'none';
    }
}

/** 监听hashchange */
export const getRouteChangeListener = (options: GlobalContext, region?: string) => {
    const changeHandler = (e: any) => {
        if (e.isTrusted) {
            // 重新挂载智能助手
            !FLAG.NetworkSupportXS && mountAssist(e);
            noAuthRedirect(e, options);
            noWhiteRedirect(e, options);
            switchConsoleReload(e);
            hideHeaderRegionArea(e);
        }
    };
    return {
        listen: () => {
            window.addEventListener('hashchange', changeHandler);
        },
        unListen: () => {
            window.removeEventListener('hashchange', changeHandler);
        }
    };
};
