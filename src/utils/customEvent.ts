import {ActionType} from '../types';
import {EventBus, EventName, activeServiceType, dcPageUrl, vpcModulePages} from '@/utils';
import {getWhiteListAll, whiteKeyMap} from '@/hooks';

export const addCustomEvent = (action: (arg: {type: string; payload?: {[key: string]: any}}) => any) => {
    EventBus.on(EventName.productActive, (type: string) => {
        switch (type) {
            case activeServiceType.nat:
                action({type: ActionType.UPDATE_NAT_STS});
                // 由于激活后更新listener是异步的，所以延迟跳转
                setTimeout(() => {
                    window.location.hash = `#${vpcModulePages.nat.index}`;
                }, 100);
                break;
            case activeServiceType.vpn:
                action({type: ActionType.UPDATE_VPN_STS});
                // 由于激活后更新listener是异步的，所以延迟跳转
                setTimeout(() => {
                    window.location.hash = `#${vpcModulePages.vpn.index}`;
                }, 100);
                break;
            case activeServiceType.peerconn:
                action({type: ActionType.UPDATE_PEERCONN_STS});
                // 由于激活后更新listener是异步的，所以延迟跳转
                setTimeout(() => {
                    window.location.hash = `#${vpcModulePages.peerconn.index}`;
                }, 100);
                break;
            case activeServiceType.dc:
                action({type: ActionType.UPDATE_DC_STS});
                // 由于激活后更新listener是异步的，所以延迟跳转
                setTimeout(() => {
                    window.location.hash = `#${dcPageUrl.dc.dcCreate}?from=landing`;
                }, 100);
                break;
            default:
                break;
        }
    });
    EventBus.on(EventName.reloadWhiteListStart, (cb?: () => void) => {
        getWhiteListAll().then(res => {
            let payload = {};
            whiteKeyMap.forEach((key: string, index: number) => {
                if (key === 'commonWhite') {
                    payload[key] = res[index].inWhiteListsResultMap;
                } else {
                    payload[key] = res[index];
                }
            });
            action({
                type: ActionType.UPDATE_WHITE_LIST,
                payload
            });
            if (cb && typeof cb === 'function') {
                cb();
            }
            EventBus.fire(EventName.reloadWhiteListFinish);
        });
    });
    return [EventName.productActive, EventName.reloadWhiteListStart];
};
