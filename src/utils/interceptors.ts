import type {AxiosRequestConfig, AxiosResponse} from 'axios';
import {EventBus} from '@/utils/eventBus';
import {EventName} from '@/utils/constants';

import {NOT_FOUND_CODE_URL_MAP} from '@/utils/notFound';
import {NO_PERMISSION_CODE_URL_MAP} from '@/utils/noPermission';

/** axios req */
export const requestInterceptors = (config: AxiosRequestConfig) => {
    let currentRoute = window.location.hash.split('#')[1];
    if (currentRoute && currentRoute.indexOf('?') > 0) {
        currentRoute = currentRoute.split('?')[0];
    }
    const reqPath = config.url;
    const noPermissionApiCodeMap = NO_PERMISSION_CODE_URL_MAP[currentRoute]?.apiCodeMap || [];
    const notFoundApiCodeMap = NOT_FOUND_CODE_URL_MAP[currentRoute]?.apiCodeMap || [];
    const noInstanceSpecialUrl = notFoundApiCodeMap.find(
        item => reqPath === item.path || reqPath.startsWith(item.path)
    );
    const noPermissionSpecialUrl = noPermissionApiCodeMap.find(
        item => reqPath === item.path || reqPath.startsWith(item.path)
    );
    if (noInstanceSpecialUrl) {
        config['x-silent-codes'] = Array.isArray(noInstanceSpecialUrl?.code)
            ? noInstanceSpecialUrl?.code
            : [noInstanceSpecialUrl.code];
    }

    if (noPermissionSpecialUrl) {
        if (Array.isArray(config['x-silent-codes'])) {
            config['x-silent-codes'] = config['x-silent-codes'].concat(
                Array.isArray(noPermissionSpecialUrl?.code)
                    ? noPermissionSpecialUrl?.code
                    : [noPermissionSpecialUrl.code]
            );
        } else {
            config['x-silent-codes'] = Array.isArray(noPermissionSpecialUrl?.code)
                ? noPermissionSpecialUrl?.code
                : [noPermissionSpecialUrl.code];
        }
    }
};

/** axios res */
export const responseInterceptors = (responseData: AxiosResponse) => {
    const url = responseData?.config?.url || '';
    let currentRoute = window.location.hash.split('#')[1];
    if (currentRoute && currentRoute.indexOf('?') > 0) {
        currentRoute = currentRoute.split('?')[0];
    }
    for (const pageRoute in NOT_FOUND_CODE_URL_MAP) {
        let apiCodeMap = NOT_FOUND_CODE_URL_MAP[pageRoute].apiCodeMap;
        const specialUrl = apiCodeMap.find(item => url === item.path || url.startsWith(item.path));
        // 当前路由和配置的路由一致，并且code也匹配，触发事件
        if (currentRoute === pageRoute && specialUrl && specialUrl.code === responseData.data.code) {
            EventBus.fire(EventName.instanceNotFound, NOT_FOUND_CODE_URL_MAP[pageRoute]);
        }
    }
    for (const pageRoute in NO_PERMISSION_CODE_URL_MAP) {
        let apiCodeMap = NO_PERMISSION_CODE_URL_MAP[pageRoute].apiCodeMap;
        const specialUrl = apiCodeMap.find(item => url === item.path || url.startsWith(item.path));
        // 当前路由和配置的路由一致，并且code也匹配，触发事件
        if (currentRoute === pageRoute && specialUrl && specialUrl.code === responseData.data.code) {
            EventBus.fire(EventName.instanceNoPermission, NO_PERMISSION_CODE_URL_MAP[pageRoute]);
        }
    }
};

const QUERY_PRICE_API = [/^\/api\/price\/v3\/order/i];
/** 拦截原生ajax请求 */
function interceptAllReq() {
    const originalOpen = XMLHttpRequest.prototype.open;
    // 重写 open 方法
    XMLHttpRequest.prototype.open = function (method: string, url: string, async: boolean = true): void {
        const isMatch = QUERY_PRICE_API.some(item => item.test(url));
        if (isMatch) {
            const region = window.$context.getCurrentRegionId();
            EventBus.fire(EventName.closureByRegion, {region, hash: location.hash});
        }
        // 调用原始 open 方法
        return originalOpen.call(this, method, url, async);
    };
}
interceptAllReq();
