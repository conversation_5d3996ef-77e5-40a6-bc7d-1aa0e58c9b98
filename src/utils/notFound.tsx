import React from 'react';
import ReactDom from 'react-dom/client';
import NotFoundComp from '@/components/NotFound';
import {EventBus} from './eventBus';
import {EventName} from './constants';

import {IPageApiConfig} from '@/types';

// 页面路由
type PageRoute = string;

// 原则：数据展示类的接口，才需要维护，操作类的接口，不需要维护
export const NOT_FOUND_CODE_URL_MAP: Record<PageRoute, IPageApiConfig> = {
    // VPC详情
    '/vpc/instance/detail': {
        selector: '.vpc-instance-detail',
        backUrl: '#/vpc/instance/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/vpc/detail',
                code: 'InstanceNotFound'
            }
        ]
    },
    // 子网详情
    '/vpc/subnet/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/subnet/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/subnet',
                code: 'Subnet.SubnetNotFoundException'
            }
        ]
    },
    '/vpc/subnet/ip': {
        selector: '.detail-widget',
        backUrl: '#/vpc/subnet/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/subnet',
                code: 'NoSuchObject'
            },
            {
                path: '/api/network/v1/subnet',
                code: 'Subnet.SubnetNotFoundException'
            }
        ]
    },
    '/vpc/subnet/reserve': {
        selector: '.detail-widget',
        backUrl: '#/vpc/subnet/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/subnet',
                code: 'Subnet.SubnetNotFoundException'
            }
        ]
    },
    '/vpc/subnet/reserveportpools': {
        selector: '.detail-widget',
        backUrl: '#/vpc/subnet/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/subnet',
                code: 'Subnet.SubnetNotFoundException'
            }
        ]
    },
    // 路由表详情
    '/vpc/route/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/route/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/rule/pagelist',
                code: 'BadRequest'
            },
            {
                path: '/api/network/v1/rule/quota',
                code: 'InstanceNotFound'
            },
            {
                path: '/api/network/v1/ipv6Rule/quota',
                code: 'InstanceNotFound'
            }
        ]
    },
    '/vpc/route/bindTgw': {
        selector: '.detail-widget',
        backUrl: '#/vpc/route/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/rule/pagelist',
                code: 'BadRequest'
            },
            {
                path: '/api/network/v1/route/routeTable/pageList',
                code: 'BadRequest'
            }
        ]
    },
    // 弹性网卡详情
    '/vpc/eni/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/eni/list',
        apiCodeMap: [
            {
                path: '/api/enic',
                code: 'NoSuchObject'
            }
        ]
    },
    '/vpc/eni/ip': {
        selector: '.detail-widget',
        backUrl: '#/vpc/eni/list',
        apiCodeMap: [
            {
                path: '/api/enic',
                code: 'NoSuchObject'
            },
            {
                path: '/api/enic',
                code: 'EniNotExistException'
            },
            {
                path: '/api/network/v1/subnet',
                code: 'Subnet.SubnetNotFoundException'
            }
        ]
    },
    '/vpc/eni/ipv6': {
        selector: '.detail-widget',
        backUrl: '#/vpc/eni/list',
        apiCodeMap: [
            {
                path: '/api/enic',
                code: 'NoSuchObject'
            },
            {
                path: '/api/network/v1/subnet',
                code: 'Subnet.SubnetNotFoundException'
            }
        ]
    },
    '/vpc/eni/security': {
        selector: '.detail-widget',
        backUrl: '#/vpc/eni/list',
        apiCodeMap: [
            {
                path: '/api/enic',
                code: 'NoSuchObject'
            }
        ]
    },
    '/vpc/eni/monitor': {
        selector: '.detail-widget',
        backUrl: '#/vpc/eni/list',
        apiCodeMap: [
            {
                path: '/api/enic',
                code: 'NoSuchObject'
            }
        ]
    },
    // 服务网卡详情
    '/vpc/endpoint/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/endpoint/list',
        apiCodeMap: [
            {
                path: '/api/snic/endpoint/detail',
                code: 'NoSuchObject'
            }
        ]
    },
    '/vpc/endpoint/security': {
        selector: '.detail-widget',
        backUrl: '#/vpc/endpoint/list',
        apiCodeMap: [
            {
                path: '/api/snic/endpoint/detail',
                code: 'NoSuchObject'
            }
        ]
    },
    '/vpc/endpoint/monitor': {
        selector: '.detail-widget',
        backUrl: '#/vpc/endpoint/list',
        apiCodeMap: [
            {
                path: '/api/snic/endpoint/detail',
                code: 'NoSuchObject'
            }
        ]
    },
    // HAVIP详情
    '/vpc/havip/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/havip/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/haVip/detail',
                code: 'HaVip.HaVipResourceNotExist'
            }
        ]
    },
    '/vpc/havip/server': {
        selector: '.detail-widget',
        backUrl: '#/vpc/havip/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/haVip/detail',
                code: 'HaVip.HaVipResourceNotExist'
            },
            {
                path: '/api/network/v1/haVip/bind/instances',
                code: 'HaVip.HaVipResourceNotExist'
            }
        ]
    },

    // 安全组详情
    '/vpc/security/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/security/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/security/baseinfo',
                code: 'InstanceNotFound'
            }
        ]
    },
    '/vpc/security/detail/bcc': {
        selector: '.detail-widget',
        backUrl: '#/vpc/security/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/security/baseinfo',
                code: 'InstanceNotFound'
            }
        ]
    },
    '/vpc/security/detail/eni': {
        selector: '.detail-widget',
        backUrl: '#/vpc/security/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/security/baseinfo',
                code: 'InstanceNotFound'
            }
        ]
    },
    '/vpc/security/detail/snic': {
        selector: '.detail-widget',
        backUrl: '#/vpc/security/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/security/baseinfo',
                code: 'InstanceNotFound'
            }
        ]
    },
    '/vpc/security/detail/bbc': {
        selector: '.detail-widget',
        backUrl: '#/vpc/security/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/security/baseinfo',
                code: 'InstanceNotFound'
            }
        ]
    },
    '/vpc/security/detail/blb': {
        selector: '.detail-widget',
        backUrl: '#/vpc/security/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/security/baseinfo',
                code: 'InstanceNotFound'
            }
        ]
    },
    '/vpc/security/detail/ddc': {
        selector: '.detail-widget',
        backUrl: '#/vpc/security/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/security/baseinfo',
                code: 'InstanceNotFound'
            }
        ]
    },
    '/vpc/security/detail/rds': {
        selector: '.detail-widget',
        backUrl: '#/vpc/security/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/security/baseinfo',
                code: 'InstanceNotFound'
            }
        ]
    },
    '/vpc/security/detail/rabbitmq': {
        selector: '.detail-widget',
        backUrl: '#/vpc/security/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/security/baseinfo',
                code: 'InstanceNotFound'
            }
        ]
    },
    '/vpc/security/detail/gaiadb': {
        selector: '.detail-widget',
        backUrl: '#/vpc/security/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/security/baseinfo',
                code: 'InstanceNotFound'
            }
        ]
    },

    //  企业安全组详情
    '/vpc/enterpriseSecurity/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/enterpriseSecurity/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/enterprise/security/detail',
                code: 'Esg.EsgResourceNotExist'
            }
        ]
    },
    '/vpc/enterpriseSecurity/detail/bcc': {
        selector: '.detail-widget',
        backUrl: '#/vpc/enterpriseSecurity/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/enterprise/security/detail',
                code: 'Esg.EsgResourceNotExist'
            }
        ]
    },
    '/vpc/enterpriseSecurity/detail/eni': {
        selector: '.detail-widget',
        backUrl: '#/vpc/enterpriseSecurity/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/enterprise/security/detail',
                code: 'Esg.EsgResourceNotExist'
            }
        ]
    },
    '/vpc/enterpriseSecurity/detail/snic': {
        selector: '.detail-widget',
        backUrl: '#/vpc/enterpriseSecurity/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/enterprise/security/detail',
                code: 'Esg.EsgResourceNotExist'
            }
        ]
    },
    '/vpc/enterpriseSecurity/detail/bbc': {
        selector: '.detail-widget',
        backUrl: '#/vpc/enterpriseSecurity/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/enterprise/security/detail',
                code: 'Esg.EsgResourceNotExist'
            }
        ]
    },
    '/vpc/enterpriseSecurity/detail/blb': {
        selector: '.detail-widget',
        backUrl: '#/vpc/enterpriseSecurity/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/enterprise/security/detail',
                code: 'Esg.EsgResourceNotExist'
            }
        ]
    },

    // ACL详情
    '/vpc/acl/manage': {
        selector: '.vpc-acl-manage',
        backUrl: '#/vpc/acl/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/acl/detail',
                code: 'InstanceNotFound'
            }
        ]
    },

    // 参数模板详情 IP组

    '/vpc/param/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/set/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/ip/set/detail',
                code: 'IpCollection.IpSetResourceNotExist'
            }
        ]
    },
    '/vpc/param/address': {
        selector: '.detail-widget',
        backUrl: '#/vpc/set/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/ip/set/detail',
                code: 'IpCollection.IpSetResourceNotExist'
            },
            {
                path: '/api/network/v1/ip/address/list',
                code: 'IpCollection.IpSetResourceNotExist'
            },
            {
                path: '/api/network/v1/ip/set/ipQuota',
                code: 'IpCollection.IpSetResourceNotExist'
            }
        ]
    },
    '/vpc/param/association': {
        selector: '.detail-widget',
        backUrl: '#/vpc/set/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/ip/set/detail',
                code: 'IpCollection.IpSetResourceNotExist'
            },
            {
                path: '/api/network/v1/ip/set/bind/instances',
                code: 'IpCollection.IpSetResourceNotExist'
            }
        ]
    },
    // IP族

    '/vpc/group/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/group/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/ip/group/detail',
                code: 'IpCollection.IpGroupResourceNotExist'
            }
        ]
    },
    '/vpc/group/address': {
        selector: '.detail-widget',
        backUrl: '#/vpc/group/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/ip/group/detail',
                code: 'IpCollection.IpGroupResourceNotExist'
            },
            {
                path: '/api/network/v1/ip/group/ipSetQuota',
                code: 'IpCollection.IpGroupResourceNotExist'
            }
        ]
    },
    '/vpc/group/association': {
        selector: '.detail-widget',
        backUrl: '#/vpc/group/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/ip/group/detail',
                code: 'IpCollection.IpGroupResourceNotExist'
            },
            {
                path: '/api/network/v1/ip/group/bind/instances',
                code: 'IpCollection.IpGroupResourceNotExist'
            }
        ]
    },

    // NAT网关详情
    '/vpc/nat/snat': {
        selector: '.detail-widget',
        backUrl: '#/vpc/nat/list',
        apiCodeMap: [
            {
                path: '/api/nat',
                code: 'NoSuchNat'
            },
            {
                path: '/api/nat',
                code: 'Exception'
            },
            {
                path: '/api/intranet/nat',
                code: 'IntranetNat.IntranetNatResourceNotExist'
            },
            {
                path: '/api/intranet/nat',
                code: 'Exception'
            }
        ]
    },
    '/vpc/nat/dnat': {
        selector: '.detail-widget',
        backUrl: '#/vpc/nat/list',
        apiCodeMap: [
            {
                path: '/api/nat/dnatRule/list',
                code: 'NoSuchNat'
            },
            {
                path: '/api/nat/dnatRule/quota',
                code: 'Exception'
            },
            {
                path: '/api/intranet/nat',
                code: 'IntranetNat.IntranetNatResourceNotExist'
            },
            {
                path: '/api/intranet/nat',
                code: 'Exception'
            }
        ]
    },

    // '/vpc/nat/monitor' 暂缓
    '/vpc/nat/gateway': {
        selector: '.detail-widget',
        backUrl: '#/vpc/nat/list',
        apiCodeMap: [
            {
                path: '/api/nat/limitRule',
                code: 'NoSuchNat'
            }
        ]
    },

    '/vpc/vpn/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/vpn/list',
        apiCodeMap: [
            {
                path: '/api/vpn/vpn/show',
                code: 'NoSuchObject'
            }
        ]
    },
    '/vpc/vpn/nat': {
        selector: '.detail-widget',
        backUrl: '#/vpc/vpn/list',
        apiCodeMap: [
            {
                path: '/api/vpn/vpn/show',
                code: 'NoSuchObject'
            },
            {
                path: '/api/nat/limitRule',
                code: 'NoSuchObject'
            }
        ]
    },
    '/vpc/vpn/idcnat': {
        selector: '.detail-widget',
        backUrl: '#/vpc/vpn/list',
        apiCodeMap: [
            {
                path: '/api/vpn/vpn/show',
                code: 'NoSuchObject'
            },
            {
                path: '/api/nat/limitRule',
                code: 'NoSuchObject'
            }
        ]
    },
    '/vpc/vpn/idcdnat': {
        selector: '.detail-widget',
        backUrl: '#/vpc/vpn/list',
        apiCodeMap: [
            {
                path: '/api/vpn/vpn/show',
                code: 'NoSuchObject'
            },
            {
                path: '/api/nat/limitRule',
                code: 'NoSuchObject'
            }
        ]
    },
    '/vpc/vpn/vpcdnat': {
        selector: '.detail-widget',
        backUrl: '#/vpc/vpn/list',
        apiCodeMap: [
            {
                path: '/api/vpn/vpn/show',
                code: 'NoSuchObject'
            },
            {
                path: '/api/nat/limitRule',
                code: 'NoSuchObject'
            }
        ]
    },
    '/vpc/vpn/monitor': {
        selector: '.detail-widget',
        backUrl: '#/vpc/vpn/list',
        apiCodeMap: [
            {
                path: '/api/vpn/vpn/show',
                code: 'NoSuchObject'
            },
            {
                path: '/api/vpn/vpnConn/list',
                code: 'NoSuchObject'
            }
        ]
    },
    '/vpc/vpn/ssl/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/sslvpn/list',
        apiCodeMap: [
            {
                path: '/api/vpn/vpnConn/show',
                code: 'NoSuchObject'
            }
        ]
    },
    '/vpc/vpn/ssl/user': {
        selector: '.detail-widget',
        backUrl: '#/vpc/sslvpn/list',
        apiCodeMap: [
            {
                path: '/api/vpn/sslvpn/user/quota',
                code: 'NoSuchObject'
            },
            {
                path: '/api/vpn/sslvpn/user/list',
                code: 'NoSuchObject'
            },
            {
                path: '/api/vpn/vpnConn/show',
                code: 'NoSuchObject'
            }
        ]
    },

    '/vpc/vpn/ssl/monitor': {
        selector: '.detail-widget',
        backUrl: '#/vpc/sslvpn/list',
        apiCodeMap: [
            {
                path: '/api/vpn/sslvpn/user/list',
                code: 'NoSuchObject'
            },
            {
                path: '/api/vpn/vpnConn/show',
                code: 'NoSuchObject'
            }
        ]
    },

    // 专线网关详情
    '/vpc/dcgw/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/dcgw/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/dc/gw/detail',
                code: 'NoSuchObject'
            }
        ]
    },
    '/vpc/dcgw/hc': {
        selector: '.detail-widget',
        backUrl: '#/vpc/dcgw/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/dc/gw/detail',
                code: 'NoSuchObject'
            }
        ]
    },
    '/vpc/dcgw/nat': {
        selector: '.detail-widget',
        backUrl: '#/vpc/dcgw/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/dc/gw/detail',
                code: 'NoSuchObject'
            }
        ]
    },
    '/vpc/dcgw/idcnat': {
        selector: '.detail-widget',
        backUrl: '#/vpc/dcgw/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/dc/gw/detail',
                code: 'NoSuchObject'
            }
        ]
    },
    '/vpc/dcgw/idcdnat': {
        selector: '.detail-widget',
        backUrl: '#/vpc/dcgw/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/dc/gw/detail',
                code: 'NoSuchObject'
            }
        ]
    },
    '/vpc/dcgw/monitor': {
        selector: '.detail-widget',
        backUrl: '#/vpc/dcgw/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/dc/gw/detail',
                code: 'NoSuchObject'
            }
        ]
    },

    // 二层网关的详情
    '/vpc/l2gw/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/l2gw/list',
        apiCodeMap: [
            {
                path: '/api/l2gw/show',
                code: 'NoSuchObject'
            }
        ]
    },
    '/vpc/l2gw/monitor': {
        selector: '.detail-widget',
        backUrl: '#/vpc/l2gw/list',
        apiCodeMap: [
            {
                path: '/api/l2gw/show',
                code: 'NoSuchObject'
            }
        ]
    },

    '/vpc/l2gw/tunnel/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/l2gw/list',
        apiCodeMap: [
            {
                path: '/api/l2gw/tunnel',
                code: 'NoSuchObject'
            }
        ]
    },
    '/vpc/l2gw/tunnel/arp': {
        selector: '.detail-widget',
        backUrl: '#/vpc/l2gw/list',
        apiCodeMap: [
            {
                path: '/api/l2gw/tunnel',
                code: 'NoSuchObject'
            }
        ]
    },
    '/vpc/l2gw/tunnel/ip': {
        selector: '.detail-widget',
        backUrl: '#/vpc/l2gw/list',
        apiCodeMap: [
            {
                path: '/api/l2gw/tunnel',
                code: 'NoSuchObject'
            }
        ]
    },
    '/vpc/l2gw/tunnel/monitor': {
        selector: '.detail-widget',
        backUrl: '#/vpc/l2gw/list',
        apiCodeMap: [
            {
                path: '/api/l2gw/tunnel',
                code: 'NoSuchObject'
            }
        ]
    },
    // Qos详情
    '/vpc/qos/detail': {
        selector: '.vpc-qos-detail',
        backUrl: '#/vpc/qos/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/qos',
                code: 'NoSuchObject'
            }
        ]
    },

    // 专线详情
    '/dc/instance/detail': {
        selector: '.detail-widget',
        backUrl: '#/dc/instance/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/dc/phy/detail',
                code: 'NoSuchObject'
            }
        ]
    },
    '/dc/instance/monitor': {
        selector: '.detail-widget',
        backUrl: '#/dc/instance/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/dc/phy/detail',
                code: 'NoSuchObject'
            }
        ]
    },

    // 通道详情
    '/dc/channel/detail': {
        selector: '.detail-widget',
        backUrl: '#/dc/channel/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/dc/channel',
                code: 'NoSuchObject'
            }
        ]
    },
    // 通道详情
    '/dc/channel/routelist': {
        selector: '.detail-widget',
        backUrl: '#/dc/channel/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/dc/channel',
                code: 'NoSuchObject'
            }
        ]
    },
    // 通道详情
    '/dc/channel/check': {
        selector: '.detail-widget',
        backUrl: '#/dc/channel/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/dc/channel',
                code: 'NoSuchObject'
            }
        ]
    },
    // 通道详情
    '/dc/channel/monitor': {
        selector: '.detail-widget',
        backUrl: '#/dc/channel/list',
        apiCodeMap: [
            {
                path: '/api/network/v1/dc/channel',
                code: 'NoSuchObject'
            }
        ]
    },
    // 流量镜像筛选条件详情
    '/vpc/filterRuleGroup/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/filterRuleGroup/list',
        apiCodeMap: [
            {
                path: '/api/mirror/ruleGroup',
                code: 'InstanceNotExist'
            }
        ]
    },
    '/vpc/filterRuleGroup/detail/mirror': {
        selector: '.detail-widget',
        backUrl: '#/vpc/filterRuleGroup/list',
        apiCodeMap: [
            {
                path: '/api/mirror/ruleGroup',
                code: 'InstanceNotExist'
            }
        ]
    },
    // 网络探测详情
    '/vpc/probe/detail': {
        selector: '.detail-widget',
        backUrl: '#/vpc/probe/list',
        apiCodeMap: [
            {
                path: '/api/network/probe',
                code: 'ProbeIdInvalidException'
            }
        ]
    },
    '/vpc/probe/monitor': {
        selector: '.detail-widget',
        backUrl: '#/vpc/probe/list',
        apiCodeMap: [
            {
                path: '/api/network/probe',
                code: 'ProbeIdInvalidException'
            }
        ]
    },
    // 路径分析详情
    '/vpc/pathanalise/detail': {
        selector: '.vpc-path-detail',
        backUrl: '#/vpc/pathanalise/list',
        apiCodeMap: [
            {
                path: '/api/network/pathanalise',
                code: 'NoSuchObject'
            }
        ]
    },

    // 私网NAT网关的详情
    '/vpc/nat/natIp': {
        selector: '.non-nav-page-content-container',
        backUrl: '#/vpc/privateNat/list',
        apiCodeMap: [
            {
                path: '/api/intranet/nat',
                code: 'IntranetNat.IntranetNatResourceNotExist'
            },
            {
                path: '/api/intranet/nat',
                code: 'NoSuchObject'
            }
        ]
    }
};

export const getNotFoundListener = () => {
    return {
        on: () => {
            EventBus.on(EventName.instanceNotFound, (options: IPageApiConfig) => {
                const {selector, backUrl} = options;
                const main = document.querySelector('#main');
                const replaceNode = main.querySelector(selector);
                if (replaceNode) {
                    replaceNode.innerHTML = '';
                    const root = ReactDom.createRoot(replaceNode);
                    root.render(<NotFoundComp backUrl={backUrl} />);
                }
            });
        },
        un: () => {
            EventBus.un(EventName.instanceNotFound);
        }
    };
};
