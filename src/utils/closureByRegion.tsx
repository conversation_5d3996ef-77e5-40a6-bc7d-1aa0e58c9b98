/**
 * 根据region封禁相关产品
 */
import React from 'react';
import ReactDom from 'react-dom/client';
import ClosureRegion from '@/components/ClosureRegion';
import {ClosurePageConfig} from '@/types';
import {EventBus} from './eventBus';
import {EventName} from './constants';

export const NEED_CLOSURE_PAGE_URL = [
    {
        pageUrl: ['/vpc/endpoint/create'],
        backUrl: '#/vpc/endpoint/list'
    },
    {
        pageUrl: ['/vpc/nat/create', '/vpc/nat/upgrade'],
        backUrl: '#/vpc/nat/list'
    },
    {
        pageUrl: ['/vpc/ipv6gw/create', '/vpc/ipv6gw/upgrade'],
        backUrl: '#/vpc/ipv6gw/list'
    },
    {
        pageUrl: ['/vpc/vpn/create'],
        backUrl: '#/vpc/vpn/list'
    },
    {
        pageUrl: ['/vpn/ssl/upgradeClient'],
        backUrl: '#/vpc/sslvpn/list'
    },
    {
        pageUrl: [
            '/vpc/peerconn/create/v2',
            '/vpc/peerconn/create_cross',
            '/vpc/peerconn/upgrade',
            '/vpc/peerconn/recharge'
        ],
        backUrl: '#/vpc/peerconn/list'
    }
];
const handleCloseModal = () => {
    const closureModal = document.getElementById('closure-region-widget');
    if (closureModal) {
        document.body.removeChild(closureModal);
    }
};
export const getClosureRegionListener = () => {
    return {
        on: () => {
            EventBus.on(EventName.closureByRegion, (options: ClosurePageConfig) => {
                const {region, hash} = options;
                if (region === 'fsh') {
                    let path = hash.split('#')[1];
                    if (path && path.indexOf('?') > 0) {
                        path = path.split('?')[0];
                    }
                    const matchItem = NEED_CLOSURE_PAGE_URL.find(item => item.pageUrl.includes(path));
                    const {backUrl} = matchItem || {};
                    const closureModal = document.getElementById('closure-region-widget');
                    if (closureModal) {
                        return;
                    }
                    // 忽略全局需询价产品
                    const dcRegExp = /^\/dc\//i;
                    if (!dcRegExp.test(path)) {
                        const ele = document.createElement('div');
                        ele.id = 'closure-region-widget';
                        document.body.appendChild(ele);
                        const root = ReactDom.createRoot(ele);
                        root.render(<ClosureRegion onClose={handleCloseModal} backUrl={backUrl} />);
                    }
                }
            });
        },
        un: () => {
            EventBus.un(EventName.closureByRegion);
        }
    };
};
