import {vpcModulePages, ndsPageUrl, EventName, REGION_FLAT_PAGE_URL} from './constants';
import {getAllModuleIndex, getPathByUrlOrHash, getModuleIndexByHashPath} from './helper';

import {EventBus} from './eventBus';

/** 监听region变化 */
export const regionChangeHandler = (region: string) => {
    const hashPath = getPathByUrlOrHash(location.hash);
    const vcpModuleIndexList = getAllModuleIndex(vpcModulePages);
    const ndsModuleIndexList = getAllModuleIndex(ndsPageUrl);
    if (vcpModuleIndexList.includes(hashPath) || ndsModuleIndexList.includes(hashPath)) {
        location.reload();
    } else {
        // 如果跳回模块首页，需要触发白名单刷新
        EventBus.fire(EventName.reloadWhiteListStart, () => {
            // 产品region平铺的创建页独立处理
            if (!REGION_FLAT_PAGE_URL.some(item => item.url.some(reg => reg.test(hashPath)))) {
                // 白名单刷新后，执行的回调
                location.hash = '#' + getModuleIndexByHashPath(hashPath);
            }
        });
    }
};
