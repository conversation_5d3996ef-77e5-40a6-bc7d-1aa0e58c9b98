/*
 * @Author: re<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-05-24 14:53:34
 * @LastEditors: p<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2025-06-04 14:11:10 
 * @FilePath: /console-vpc/utils/assist.ts
 */
import {assistSendFeedback, assistSendMessage, getAssistSendMessage} from '@/apis';
import {request} from '@/apis';
import 'intro.js/introjs.css';
import 'intro.js/themes/introjs-modern.css';
import {cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
import {AssistSDKManager} from '@baidu/bce-ai-assist-sdk';
import Driver from 'driver.js';
import 'driver.js/dist/driver.min.css';

export interface MessageItemProps {
    type: string;
    title: string;
    command: string[];
    moreMessage?: string;
    showMoreMessage?: boolean;
    query?: string;
    upclick?: boolean;
    downclick?: boolean;
    ifDangerous?: boolean;
    hasFooter?: boolean;
    options?: any;
}

class Assist {
    props: any;
    onInputSend: (value: string, options?: any) => void;
    onOpenAIAssist: () => void;
    onCloseAIAssist: () => void;
    constructor() {
        this.props = {
            dom: '#main',
            title: i18n.t('智能助手'),
            exampleDes: i18n.t('如果您对私有网络 VPC 有任何使用及操作上的疑问，您可以查看<a href={{helpUrl}}>帮助文档</a>，或直接向我提出问题。', {
                helpUrl: "https://cloud.baidu.com/doc/VPC/s/qjwvyu0at"
            }),
            examples: [],
            client: request,
            notDestroyOnClose: false,
            MaxErrorNumber: -1,
            inputEnhance: true,
            onlyLastAnswerAllowsReset: true,
            onError: this.onError,
            sendMessage: this.sendMessage,
            feedBackUp: this.feedBackUp,
            feedBackDown: this.feedBackDown,
            formatRenderData: this.formatRenderData,
            getCallBack: (callBack: any) => {
                callBack.onInputSend && (this.onInputSend = callBack.onInputSend);
                callBack.onOpenAIAssist && (this.onOpenAIAssist = callBack.onOpenAIAssist);
                callBack.onCloseAIAssist && (this.onCloseAIAssist = callBack.onCloseAIAssist);
            }
        };
        this.onInputSend = () => {};
        this.onOpenAIAssist = () => {};
        this.onCloseAIAssist = () => {};
    }
    // 获取初始化问题
    init(sceneLabel: string = 'vpc_console') {
        return getAssistSendMessage({sceneLabel, count: 5})
            .then((res: any) => {
                this.props.examples = res.questions || [];
                this.initDom();
            })
            .catch((err: any) => {});
    }
    initDom() {
        const mainDom = document.querySelector(this.props.dom);
        const addistDom = document.createElement('div');
        addistDom.id = 'assist';
        const oldAssistDom = document.querySelector('#assist');
        // 移除旧的dom
        oldAssistDom && oldAssistDom.remove();
        mainDom.appendChild(addistDom);
        const manage = new AssistSDKManager();
        manage.useComponent('Entry', this.props, addistDom);
        this.initIntro();
    }
    // 初始化功能介绍信息
    initIntro() {
        if (window.$storage.get('vpc-console-intro')) return;
        try {
            const observeNodeOb = new MutationObserver(recordList => {
                try {
                    const driver = new Driver({
                        opacity: 0,
                        className: 'assist-driver',
                        showButtons: true
                    });
                    driver.highlight({
                        element: '#entry-btn',
                        popover: {
                            title:i18n.t('<div>AI智能助手上线啦～</div>'),
                            description:
                               `<div>${i18n.t('点击这里，开启智能助手, 快来试试吧～')} <button id="driverClose"">${i18n.t('立即体验')}</button></div>`,
                            position: 'left',
                            closeBtnText: 'x'
                        }
                    });

                    setTimeout(() => {
                        // 等渲染完毕再添加点击事件，要不然获取不到dom
                        const driverCloseButton = document.getElementById('driverClose');
                        if (driverCloseButton) {
                            driverCloseButton.addEventListener('click', () => {
                                driver.reset();
                            });
                        }
                    }, 300);
                    window.$storage.set('vpc-console-intro', true);
                    observeNodeOb.disconnect();
                } catch (error) {
                    observeNodeOb.disconnect();
                }
            });
            const observeNode = document.querySelector('#assist');
            observeNode &&
                observeNodeOb.observe(observeNode, {
                    childList: true // 监听子节点变化（如果subtree为true，则包含子孙节点）
                });
        } catch (error) {}
    }
    // 发送消息
    sendMessage(value: string, options?: any) {
        return assistSendMessage({
            query: value,
            userId: window.$cookie.get('bce-login-userid') || window.$context.getUserId()
        });
    }
    // 本次回答的反馈
    feedBackUp(messge: MessageItemProps, index: number) {
        // 用户提供的反馈
        // 0    -- 正向
        // 1    -- 负向
        return assistSendFeedback({uuid: messge.options.uuid, feedback: 0});
    }
    feedBackDown(messge: MessageItemProps, index: number) {
        return assistSendFeedback({uuid: messge.options.uuid, feedback: 1});
    }
    // 详情中可以直接调起弹框，自动发送问题
    sendMessageToAssist({sceneLabel, message}: {sceneLabel: string; message: string}) {
        return getAssistSendMessage({sceneLabel, count: 5})
            .then(async (res: any) => {
                let examples = res.questions || [];
                this.onOpenAIAssist();
                setTimeout(() => {
                    this.onInputSend(message, {examples});
                }, 100);
            })
            .catch((err: any) => {
                this.onOpenAIAssist();
            });
    }

    onError(err: any) {
        const errMap: any = {
            '429': i18n.t('当前提问数已经达到服务配额，5分钟内支持提问5个问题，请5分钟后重试。'),
            '503': i18n.t('依赖服务器异常，努力修复中，请稍后再试。'),
            '500': i18n.t('系统异常，努力修复中，请稍后再试。')
        };
        if (err.code && errMap[err.code]) {
            return errMap[err.code];
        }
        return i18n.t('系统异常，请稍后再试。');
    }
    // 返回数据格式化
    formatRenderData(data: any) {
        if (!data.answer) {
            throw data;
        }
        return {
            type: 'response',
            ifDangerous: true,
            showMoreMessage: false,
            title: data.answer,
            hasFooter: true,
            options: {
                uuid: data.uuid
            },
            command: [],
            links: data.urls
        };
    }
}

export default new Assist();
