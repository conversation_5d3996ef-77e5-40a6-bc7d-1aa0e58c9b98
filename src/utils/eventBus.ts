class EventTarget {
    events: {[type: string]: Array<(options: any) => any>};
    constructor() {
        this.events = {};
    }
    on(type: string, callback: (arg: any) => void) {
        if (!this.events[type]) {
            this.events[type] = [];
        }
        this.events[type].push(callback);
    }
    un(type: string) {
        if (!this.events[type]) {
            delete this.events[type];
        }
    }
    fire(type?: string, options?: Record<string, any>) {
        if (!type || !this.events[type]) {
            return;
        }
        this.events[type].forEach(callback => {
            callback && callback(options);
        });
    }
}
export const EventBus = new EventTarget();
