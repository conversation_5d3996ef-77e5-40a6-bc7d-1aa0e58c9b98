import 'acud/dist/acud.min.css';
import '@baidu/bce-react-toolkit/es/styles/_overwrite_acud.css';

import {
    AppProvider,
    FrameworkProvider,
    toolkitConfig,
    cbaI18nInstance,
    I18nextProvider
} from '@baidu/bce-react-toolkit';
import React from 'react';
import {createRoot} from 'react-dom/client';
import {GlobalProvider} from '@/context';
import {getAppTitle} from '@/utils';
import App from '@/App';

// @ts-ignore
__webpack_public_path__ = window.appPublicPath || '';

toolkitConfig.init({
    enableI18n: true,
    // @ts-ignore
    publicPath: __webpack_public_path__,
    appTitle: getAppTitle()
});
export async function bootstrap(initData: any) {
    const container = createRoot(document.querySelector('#main'));
    container.render(
        <I18nextProvider i18n={cbaI18nInstance} defaultNS={'translation'}>
            <FrameworkProvider frameworkData={initData}>
                <AppProvider>
                    <GlobalProvider>
                        <App />
                    </GlobalProvider>
                </AppProvider>
            </FrameworkProvider>
        </I18nextProvider>
    );
}
