import _ from 'lodash';
import {useDocumentTitle, AppLayout, useRegion} from '@baidu/bce-react-toolkit';
import React, {useMemo, useEffect} from 'react';
import {HashRouter, Route, Routes, Navigate} from 'react-router-dom';
import {Loading} from 'acud';
import {useBoolean} from 'ahooks';
import {ActionType} from './types';
import {useAuth} from '@/hooks';
import {useGlobalContext} from '@/context';
import {
    getRouteChangeListener,
    noAuthRedirect,
    EventBus,
    noWhiteRedirect,
    addCustomEvent,
    redirectIndexPageUrl,
    getCurrentSystemByHash,
    mountAssist,
    hideMenu,
    NEED_CHECK_WHITELIST_MENU,
    redirectGatewayLimitList,
    getNoPermissionListener,
    getNotFoundListener,
    getClosureRegionListener,
    replaceHashSymbol,
    regionChangeHandler,
    EventName,
    hideHeaderRegionArea,
    NEED_RESOLVE_HASH_LIST
} from '@/utils';
import '@/pages/sanPages/common';

import {
    menuList,
    flattenedMenuList,
    dcMenuList,
    dcFlattenedMenuList,
    ndsMenuList,
    ndsFlattenedMenuList,
    crossDcMenuList,
    crossFlattenedMenuList
} from '@/pages';

import FLAG from '@/flags';

const defaultRoute = redirectIndexPageUrl[getCurrentSystemByHash()];
const moduleMenuMap = {
    vpc: {
        menuList,
        flattenedMenuList
    },
    dc: {
        menuList: dcMenuList,
        flattenedMenuList: dcFlattenedMenuList
    },
    diagnosis: {
        menuList: ndsMenuList,
        flattenedMenuList: ndsFlattenedMenuList
    },
    crossdc: {
        menuList: crossDcMenuList,
        flattenedMenuList: crossFlattenedMenuList
    }
};

export default function App() {
    useDocumentTitle();
    // 兼容其他产品跳转hash、query解析问题
    replaceHashSymbol(NEED_RESOLVE_HASH_LIST);

    /** region变化统一在此处理 */
    const onRegionChange = region => {
        regionChangeHandler(region);
    };
    const {region} = useRegion({onRegionChange});
    const {isFinished, whiteAuthMap} = useAuth();
    const [{globalState, dispatch}] = useGlobalContext();
    const [inited, {setTrue: setInitStatus}] = useBoolean(false);

    useEffect(() => {
        // 初始化一次智能助手 虚商不支持
        !FLAG.NetworkSupportXS && mountAssist({newURL: location.hash});
        hideHeaderRegionArea({newURL: location.hash});
        const notFoundListener = getNotFoundListener();
        const noPermissionListener = getNoPermissionListener();
        const closureByRegionListener = getClosureRegionListener();
        notFoundListener.on();
        noPermissionListener.on();
        closureByRegionListener.on();
        // 添加产品自定义事件监听
        const customEvents = addCustomEvent(dispatch);
        return () => {
            if (Array.isArray(customEvents)) {
                customEvents.forEach(customEvent => {
                    EventBus.un(customEvent);
                });
            }
            notFoundListener.un();
            noPermissionListener.un();
            closureByRegionListener.un();
        };
    }, []);

    useEffect(() => {
        if (isFinished) {
            if (Object.keys(whiteAuthMap).length) {
                // 监听hash变化 初始化时hashchange不会触发 需初始化一次
                noAuthRedirect({newURL: location.hash}, whiteAuthMap);
                noWhiteRedirect({newURL: location.hash}, whiteAuthMap);
                // 更新全局数据
                dispatch({type: ActionType.SET_INIT_STATE, payload: whiteAuthMap});
            }
            setInitStatus();
        }
    }, [isFinished, whiteAuthMap]);

    useEffect(() => {
        if (inited) {
            EventBus.fire(EventName.closureByRegion, {region, hash: location.hash});
            const {listen, unListen} = getRouteChangeListener(globalState, region);
            listen();
            return () => {
                unListen();
            };
        }
    }, [inited, globalState]);

    const menus = useMemo(() => {
        let menuData = [];
        if (inited) {
            const menuList = moduleMenuMap[getCurrentSystemByHash()]?.menuList;
            if (Object.keys(whiteAuthMap).length) {
                /** 网关限速路由入口动态切换 */
                redirectGatewayLimitList(menuList as any, whiteAuthMap);
                /** 左侧菜单显隐 */
                _.forEach(NEED_CHECK_WHITELIST_MENU, item => {
                    const {specialWhite} = item;
                    const authMap = specialWhite ? whiteAuthMap : whiteAuthMap.commonWhite;
                    if (!authMap[item.authKey]) {
                        hideMenu(item.menuKey, menuList as any);
                    }
                });
            }
            menuData = menuList;
        }
        return menuData;
    }, [inited, whiteAuthMap]);

    const routes = useMemo(() => {
        const routeList = moduleMenuMap[getCurrentSystemByHash()]?.flattenedMenuList.filter(item => item.Component);
        return routeList;
    }, []);

    return (
        <HashRouter>
            {inited ? (
                <AppLayout menus={menus} needToVerifyProductActivation={false}>
                    <Routes>
                        {routes.map(({key, Component}) => {
                            const Element = Component;
                            return <Route path={key} key={key} element={<Element />}></Route>;
                        })}
                        <Route path="*" element={<Navigate to={defaultRoute} replace />} />
                    </Routes>
                </AppLayout>
            ) : (
                <Loading loading={true} size="small"></Loading>
            )}
        </HashRouter>
    );
}
