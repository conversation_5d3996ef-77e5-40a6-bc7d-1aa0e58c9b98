import request from './initApi';
import {API_NETWORK_PREFIX} from './index';

// vpc列表
export const getVpcList = (data?: any) => {
    return request({
        url: `${API_NETWORK_PREFIX}/vpcs`,
        method: 'post',
        data,
        silent: true
    });
};

/** 概览页资源数量 */
export const queryResourceNum = (params?: Record<string, any>) => {
    return request({
        url: `${API_NETWORK_PREFIX}/overview`,
        method: 'get',
        params,
        silent: true,
        headers: {region: 'global'}
    });
};

/** 查询资源列表 */
export const queryResourceList = (data?: Record<string, any>) => {
    return request({
        url: `${API_NETWORK_PREFIX}/overview/resources`,
        method: 'post',
        data,
        silent: true,
        headers: {region: 'global'}
    });
};

/** 查询产品动态 */
export const queryProductNews = (data?: Record<string, any>) => {
    return request({
        url: 'https://bce.bdstatic.com/portal-cms/online/console/marketing/pre_VPC.js',
        method: 'get'
    });
};

/** 查询事件中心 */
export const queryEventCenter = (params?: Record<string, any>) => {
    return request({
        url: '/api/bcm/v1/event/list',
        method: 'get',
        params,
        silent: true,
        headers: {region: 'global'}
    });
};
