/**
 * 专线演练接口
 */
import request from './initApi';
import {API_NETWORK_PREFIX} from './index';

export const GET = (url, payload, options) => {
    return request({
        method: 'get',
        url,
        params: payload,
        silent: true,
        ...options
    });
};

export const POST = (url, payload, options) => {
    return request({
        method: 'post',
        url,
        data: payload,
        silent: true,
        ...options
    });
};

export const PUT = (url, payload, options) => {
    return request({
        method: 'put',
        url,
        data: payload,
        silent: true,
        ...options
    });
};

export const DELETE = (url, payload) => {
    return request({
        method: 'delete',
        url,
        data: payload,
        silent: true
    });
};

// 创建专线演练任务
export const createFailoverTask = (params: any) => {
    return POST(`${API_NETWORK_PREFIX}/dc/failover/task/create`, params, {});
};

// 任务列表
export const getFailoverTaskList = (params?: any) => {
    return GET(`${API_NETWORK_PREFIX}/dc/failover/task`, params, {skipResponseInterceptor: true});
};

// 修改演练任务
export const updateFailoverTask = (params: any) => {
    const {taskId} = params;
    return PUT(`${API_NETWORK_PREFIX}/dc/failover/task/${taskId}`, params, {});
};

// 演练任务检查
export const checkFailoverTask = (params: any) => {
    const {instanceType} = params;
    delete params.instanceType;
    return POST(`${API_NETWORK_PREFIX}/dc/failover/task/check/${instanceType}`, params, {});
};

// 演练任务详情
export const getFailoverTaskDetail = (params: any) => {
    const {taskId} = params;
    return GET(`${API_NETWORK_PREFIX}/dc/failover/task/${taskId}`, params, {});
};

// 开始演练任务
export const startFailoverTask = (params: any) => {
    const {taskId} = params;
    return PUT(`${API_NETWORK_PREFIX}/dc/failover/task/enable/${taskId}`, params, {});
};

// 停止演练任务
export const stopFailoverTask = (params: any) => {
    const {taskId} = params;
    return PUT(`${API_NETWORK_PREFIX}/dc/failover/task/disable/${taskId}`, params, {});
};

// 删除演练任务
export const deleteFailoverTask = (params: any) => {
    const {taskId} = params;
    return DELETE(`${API_NETWORK_PREFIX}/dc/failover/task/${taskId}`, params);
};

// 查询演练报告
export const getFailoverTaskReport = (params: any) => {
    return GET(`${API_NETWORK_PREFIX}/dc/failover/task/report`, params, {});
};

// 更新演练报告
export const updateFailoverTaskReport = (params: any) => {
    return POST(`${API_NETWORK_PREFIX}/dc/failover/task/report`, params, {});
};

// 删除报告
export const deleteTaskReport = (params: any) => {
    const {taskId, reportId} = params;
    return DELETE(`${API_NETWORK_PREFIX}/dc/failover/${taskId}/report/${reportId}`, {});
};

// 下载演练任务
export const downloadFailoverTask = (query: any) => {
    window.open(`${API_NETWORK_PREFIX}/dc/failover/task/download?${query}`);
};

// 根据地域获得接入点、运营商以及端口规则的信息
export const getApAddrList = (params: any) => {
    return GET(`${API_NETWORK_PREFIX}/dc/phy/ap/${params}`, {}, {});
};

// 获取物理专线列表
export const getDcList = (params: any) => {
    return POST(`${API_NETWORK_PREFIX}/dc/phy/list`, params, {});
};

// 获取专线通道列表
export const getChannelList = (params: any) => {
    return POST(`${API_NETWORK_PREFIX}/dc/channel/list`, params, {});
};

// 获取专线接入-故障演练配额
export const commonQuota = (params: any) => {
    return POST(`${API_NETWORK_PREFIX}/commonQuota`, params, {});
};
