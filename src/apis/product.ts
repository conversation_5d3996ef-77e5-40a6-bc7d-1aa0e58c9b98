import request from './initApi';

import {API_NETWORK_PREFIX} from './index';

/** 安全组 */
export const getSecurityDetail = (params: any) => {
    return request({
        url: `${API_NETWORK_PREFIX}/security/baseinfo`,
        method: 'get',
        params,
        silent: true
    });
};

export const getEnterpriseSecDetail = (params: any) => {
    return request({
        url: `${API_NETWORK_PREFIX}/enterprise/security/detail`,
        method: 'get',
        params,
        silent: true
    });
};

/** VPN */
export const getVpnDetail = (data: any) => {
    return request({
        url: `/api/vpn/vpn/show`,
        method: 'post',
        data,
        silent: true
    });
};

// ssl vpn隧道详情
export const getSslVpnConnDetail = (data: any) => {
    return request({
        url: `/api/vpn/vpnConn/show`,
        method: 'post',
        data,
        silent: true
    });
};

// 获取vpc信息
export const getVpcInfo = (data: any) => {
    return request({
        url: `${API_NETWORK_PREFIX}/vpcMap`,
        method: 'post',
        data,
        silent: true
    });
};

/** 子网 */
export const getSubnetResourceDetail = ({subnetId}) => {
    return request({
        url: `${API_NETWORK_PREFIX}/subnet/${subnetId}/resource`,
        method: 'get',
        silent: true
    });
};

/** snic */
export const getSnicDetail = (data: any) => {
    return request({
        url: `/api/snic/endpoint/detail`,
        method: 'post',
        data,
        silent: true
    });
};

/** eni */
export const getEniDetail = ({eniId}) => {
    return request({
        url: `/api/enic/${eniId}`,
        method: 'get',
        silent: true
    });
};

/** havip */
export const getHavipDetail = (params: any) => {
    return request({
        url: `${API_NETWORK_PREFIX}/haVip/detail`,
        method: 'get',
        params,
        silent: true
    });
};

/** nat */
export const getNatDetail = (data: any) => {
    return request({
        url: `/api/nat/list`,
        method: 'post',
        data,
        silent: true
    });
};
export const getNatBlackList = () => {
    return request({
        url: '/api/nat/blackList',
        method: 'post'
    });
};
export const getPrivateNatDetail = ({natId}) => {
    return request({
        url: `/api/intranet/nat/${natId}/detail`,
        method: 'get',
        silent: true
    });
};

export const getIPv6NatDetail = ({natId}) => {
    return request({
        url: `/api/nat/ipv6/${natId}/detail`,
        method: 'get',
        silent: true
    });
};

/** param */
export const getParamDetail = (params: any) => {
    return request({
        url: `${API_NETWORK_PREFIX}/ip/set/detail`,
        method: 'get',
        params,
        silent: true
    });
};
export const getParamGroupDetail = (params: any) => {
    return request({
        url: `${API_NETWORK_PREFIX}/ip/group/detail`,
        method: 'get',
        params,
        silent: true
    });
};

/** 创建、修改资源映射信息 */
export const updateResource = (data?: Record<string, any>) => {
    return request({
        url: '/api/network/migrate/resource',
        method: 'put',
        data,
        headers: {region: 'bj'},
        silent: true
    });
};
/** peerconn */
export const getPeerDetail = (data: any) => {
    return request({
        url: '/api/peerconn/peerconn/detail',
        method: 'post',
        data,
        silent: true
    });
};

/**查询用户对等连接审核状态 仅支持bj */
export const getCrossAuditStatus = (data: any) => {
    return request({
        url: '/api/peerconn/crossBorder/audit',
        method: 'get',
        data,
        headers: {region: 'bj'},
        silent: true
    });
};

/** DCGW */
export const getDcgwDetail = (data: any) => {
    return request({
        url: `${API_NETWORK_PREFIX}/dc/gw/detail`,
        method: 'post',
        data,
        silent: true
    });
};

/** probe */
export const getProbeDetail = ({probeId}) => {
    return request({
        url: `/api/network/probe/${probeId}`,
        method: 'get',
        silent: true
    });
};
/** L2gw */
export const getL2gwDetail = (data: any) => {
    return request({
        url: `/api/l2gw/show/${data.l2gwId}`,
        method: 'get',
        params: {},
        silent: true
    });
};

/** flowmirror */
export const getFilterRuleDetail = ({groupId}) => {
    return request({
        url: `/api/mirror/ruleGroup/${groupId}`,
        method: 'get',
        silent: true
    });
};
/** L2gwTunnel */
export const getL2gwTunnelDetail = (data: any) => {
    return request({
        url: `/api/l2gw/tunnel/${data.tunnelId}`,
        method: 'get',
        params: {},
        silent: true
    });
};

// Dc
export const getDcDetail = (data: any) => {
    return request({
        url: `${API_NETWORK_PREFIX}/dc/phy/detail`,
        method: 'post',
        data,
        silent: true
    });
};

// Channel

export const channelDetail = (channelId: string, data: any) => {
    return request({
        url: `${API_NETWORK_PREFIX}/dc/channel/${channelId}`,
        method: 'get',
        params: data,
        silent: true
    });
};

export const multicastingDetail = (data: any) => {
    return request({
        url: '/api/network/multicast/group',
        method: 'get',
        params: data,
        silent: true
    });
};

// 问答助手提问
export const assistSendMessage = (data: any) => {
    return request({
        url: '/api/ih/v1/service/ticket/query',
        method: 'post',
        data,
        silent: true
    });
};

// 用户针对这次提问的反馈
export const assistSendFeedback = (data: any) => {
    return request({
        url: '/api/ih/v1/service/ticket/feedback',
        method: 'post',
        data,
        silent: true
    });
};

// 获取定制问题
export const getAssistSendMessage = (data: any) => {
    return request({
        url: '/api/ih/v1/service/ticket/labelQuestions',
        method: 'post',
        data,
        silent: true
    });
};

// 路由表详情
export const getRouteDetail = (data: any) => {
    return request({
        url: `${API_NETWORK_PREFIX}/rule/pagelist`,
        method: 'post',
        data,
        silent: true
    });
};
