/* eslint-disable prettier/prettier */
import {createRequestInstance} from '@baidu/bce-react-toolkit';
import type {AxiosRequestConfig, AxiosResponse} from 'axios';
import {EventBus} from '@/utils/eventBus';
import {requestInterceptors, responseInterceptors} from '@/utils';

const request = createRequestInstance();
request.interceptors.request.use((config: AxiosRequestConfig): any => {
    config.raw = true;
    requestInterceptors(config);
    return config;
});

request.interceptors.response.use((responseData: AxiosResponse['data']) => {
    responseInterceptors(responseData);
    EventBus.fire('response', responseData);
    const data = responseData.data;
    const url = responseData?.config?.url || '';
    // 跳过响应拦截器
    const skipResponseInterceptor = !!responseData?.config?.skipResponseInterceptor;
    if (!skipResponseInterceptor && data && data.hasOwnProperty('page')) {
        return url.indexOf('/api/iam/authcode/verify') > -1 ? data : data.page;
    }
    if (!skipResponseInterceptor && data && data.hasOwnProperty('result')) {
        return url.indexOf('/api/iam/authcode/verify') > -1 ? data : data.result;
    }
    return data;
});
export default request;
