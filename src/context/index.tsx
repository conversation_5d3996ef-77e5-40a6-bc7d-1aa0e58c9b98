import React, {createContext, ReactNode, useMemo, useReducer, useContext} from 'react';
import {Action, ActionType, GlobalContext} from '@/types';

const initialState: GlobalContext = {
    commonWhite: {},
    vpnWhite: false,
    endPointWhite: false,
    enicWhite: false,
    peerConnSts: false,
    vpnSts: false,
    natSts: false,
    blbSts: false,
    etSts: false,
    csnSts: false,
    natIntroShow: true,
    l2gwSupportRegion: false
};

const reducer = (state: GlobalContext, action: Action) => {
    switch (action.type) {
        case ActionType.SET_INIT_STATE: {
            return {...state, ...action.payload};
        }
        case ActionType.UPDATE_NAT_STS: {
            return {
                ...state,
                natSts: true
            };
        }
        case ActionType.UPDATE_VPN_STS: {
            return {
                ...state,
                vpnSts: true
            };
        }
        case ActionType.UPDATE_PEERCONN_STS: {
            return {
                ...state,
                peerConnSts: true
            };
        }
        case ActionType.UPDATE_DC_STS: {
            return {
                ...state,
                etSts: true
            };
        }
        case ActionType.UPDATE_WHITE_LIST: {
            return {
                ...state,
                commonWhite: action.payload.commonWhite,
                vpnWhite: action.payload.vpnWhite,
                endPointWhite: action.payload.endPointWhite,
                enicWhite: action.payload.enicWhite,
                l2gwSupportRegion: action.payload.l2gwSupportRegion
            };
        }
        default: {
            return state;
        }
    }
};

const CustomContext = createContext<{globalState: GlobalContext; dispatch: React.Dispatch<Action>}>({
    globalState: initialState,
    dispatch: () => initialState
});

export const GlobalProvider = ({children}: {children?: ReactNode}) => {
    const [state, dispatch] = useReducer(reducer, initialState);
    const context = useMemo(() => {
        return {globalState: state, dispatch};
    }, [state]);
    return <CustomContext.Provider value={context}>{children}</CustomContext.Provider>;
};

export const useGlobalContext = () => {
    const context = useContext(CustomContext);
    if (!context) {
        throw new Error(`useGlobalContext must be used within a Provider`);
    }
    return [context];
};
