export default {
    vpc: {
        listCreateBtn: 'vpc_instance_create',
        listDeleteBtn: 'vpc_instance_delete',
        listTable: 'vpc_instance_list',
        listName: 'vpc_instance_name_',
        listId: 'vpc_instance_id_',
        listManageCidr: 'vpc_instance_manage_',
        listDeleteIpv6: 'vpc_instance_delete_ipv6_',
        listAllocationIpv6: 'vpc_instance_allocation_ipv6_',

        detailBack: 'vpc-detail-back',
        detailName: 'vpc-instance-name',
        detailNameEdit: 'vpc-instance-name-edit',
        detailNameEditInput: 'vpc-instance-name-edit-input',
        detailNameEditSub: 'vpc-instance-name-edit-sub',
        detailResourceLink: 'vpc-resource-link',

        createNameInput: 'vpc_instance_name',
        createCidrInput: 'vpc_instance_cidr',
        createSubmit: 'vpc_create_submit',

        auxiliaryAddBtn: 'vpc_add_auxiliary_cidr'
    },
    acl: {
        listTable: 'acl_list',
        listSelect: 'acl_vpc_select',
        listName: 'acl_name_',
        vpcName: 'acl_vpc_name_',
        manageAcl: 'acl_manage_',

        detailBack: 'acl-detail-back',
        detailName: 'acl-instance-name',
        detailNameEdit: 'acl-instance-name-edit',
        detailNameEditInput: 'acl-instance-name-edit-input',
        detailNameEditSub: 'acl-instance-name-edit-sub',
        detailAddRules: 'acl-detail-add-rules-'
    },
    eni: {
        listCreateBtn: 'eni_instance_create',
        listDeleteBtn: 'eni_instance_delete',
        listTable: 'eni_instance_list',
        listName: 'eni_instance_name_',
        listId: 'eni_instance_id_',
        listRelease: 'eni_instance_release_',
        listMountHost: 'eni_instance_mount_host_',
        listUnmountHost: 'eni_instance_unmount_host_',
        listMonitor: 'eni_instance_monitor_',
        listVpc: 'eni_instance_vpc_',
        listSubnet: 'eni_instance_subnet_',

        createNameInput: 'eni_instance_name',
        createSubnetSelect: 'eni_instance_subnet',
        createSecuritySelect: 'eni_instance_security',
        createSubmit: 'eni_create_submit',
        createCancel: 'eni_create_cancel',

        detailBack: 'eni-detail-back',
        detailName: 'eni-instance-name',
        detailNameEdit: 'eni-instance-name-edit',
        detailNameEditInput: 'eni-instance-name-edit-input',
        detailNameEditSub: 'eni-instance-name-edit-sub',

        ipAddAssistIp: 'eni_add_assist_ip',
        ipUnbindEip: 'eni_ip_unbind_eip_',
        ipBindEip: 'eni_ip_bind_eip_',
        ipAddress: 'eni_ip_address',
        ipv6AddressDisplay: 'eni_ipv6_address_display',

        securityGroupRule: 'eni_security_group_rule',
        securityGroupList: 'eni_security_group_list',
        securityAssociate: 'eni_security_associate',

        monitorDisplay: 'eni_monitor_display'
    },
    // 安全组
    security: {
        // 列表页
        listCreateBtn: 'security_list_create',
        listDeleteBtn: 'security_list_delete',
        listTable: 'security_list',
        listPreviewRules: 'security_list_preview_rule',
        listReset: 'security_reset_default_',
        listCopy: 'security_copy_',
        listEditDescription: 'security_list_edit_description_',
        listInstanceName: 'security_list_name_',
        listVPCName: 'security_vpc_name_',
        listVPCSelect: 'security_vpc_select',
        // 创建页
        createSelectVpc: 'security_vpc_instance',
        createBack: 'security_create_back',
        createName: 'security_name',
        createSelectTemp: 'security_temp',
        createChangeRuleType: 'security_change_rule_type',
        createAddRuleBtn: 'security_add_rule',
        createAddRuleSubmit: 'security_add_rule_submit_',
        createAddRuleCancel: 'security_add_rule_cancel_',
        createAddRuleEdit: 'security_add_rule_edit_',
        ruleTable: 'security_table_list',
        createDeleteRule: 'security_delete_rule',
        createSubmit: 'security_create_submit',
        // 详情页
        detailBack: 'security_detail_back',
        detailCopyId: 'security_detail_copy_id',
        detailTab: 'security_detail_',
        detailBindInstance: 'security_detail_bind_',
        detailUnbindInstance: 'security_detail_unbind_',
        detailTable: 'security_detail_table_',
        detailTableName: 'security_detail_table_name_',
        detailSearch: 'security_detail_search_',
        detailRefresh: 'security_detail_refresh_',
        detailSecurityName: 'security_detail_name',
        detailEditName: 'security_detail_name_edit',
        detailEditNameInput: 'security_detail_name_edit_input',
        detailEditNameSub: 'security_detail_name_edit_sub'
    },
    route: {
        listTable: 'route_list',
        listSelect: 'route_vpc_select',
        listName: 'route_name_',
        listId: 'route_id_',
        listCustomManage: 'route_custom_manage',
        createRoute: 'route_create',
        vpcName: 'route_vpc_name_',
        manageRoute: 'route_manage_',
        deleteRoute: 'route_delete_',

        detailBack: 'route-detail-back',
        detailName: 'route-instance-name',
        detailNameEdit: 'route-instance-name-edit',
        detailNameEditInput: 'route-instance-name-edit-input',
        detailNameEditSub: 'route-instance-name-edit-sub',
        detailBindTgw: 'route_bind_tgw',
        detailBindTgwSure: 'route_bind_tgw_sure',
        detailUnbindTgw: 'route_unbind_tgw_',
        detailUnbindTgwSure: 'route_unbind_tgw_sure',
        detailBindTgwTableEmpty: 'route_bind_tgw_empty',

        tgwName: 'route-tgw-title',
        createNameInput: 'route_instance_name',
        createSubmit: 'route_create_submit'
    },
    dcgw: {
        listTable: 'dcgw_list',
        listSelect: 'dcgw_vpc_select',
        listName: 'dcgw_name_',
        listId: 'dcgw_id_',
        createRoute: 'dcgw_create',
        vpcName: 'dcgw_vpc_name_',
        deleteDcgw: 'dcgw_delete',
        unbindDcgw: 'unbind_dcgw_',
        listCreateBtn: 'dcgw_create',
        bindDcgw: 'bind_dcgw_',
        editDcgw: 'edit_dcgw_',
        dcgwHc: 'dcgw_health_check_',
        editDcgwRes: 'dcgw_edit_resource_',

        detailBack: 'dcgw-detail-back',
        detailName: 'dcgw-instance-name',
        detailNameEdit: 'dcgw-instance-name-edit',
        detailNameEditInput: 'dcgw-instance-name-edit-input',
        detailNameEditSub: 'dcgw-instance-name-edit-sub',

        createNameInput: 'dcgw_instance_name',
        dcCheckRadio: 'dcgw_checkBind',
        createSubmit: 'dcgw_create_submit'
    },
    // 企业安全组
    enterpriseSecurity: {
        // 列表页
        listCreateBtn: 'enterprise_security_list_create',
        listDeleteBtn: 'enterprise_security_list_delete',
        listTable: 'enterprise_security_list',
        listPreviewRules: 'enterprise_security_list_preview_rule',
        listCopy: 'enterprise_security_copy_',
        listCopyNameInput: 'enterprise_security_copy_name_input',
        listDelete: 'enterprise_security_delete_',
        listInstanceName: 'enterprise_security_list_name_',
        listEditName: 'list_edit_name_',
        listEditNameInput: 'list_edit_name_input_',
        listEditNameSub: 'list_edit_name_sub_',
        listEditDesc: 'list_edit_desc_',
        listEditDescInput: 'list_edit_desc_input_',
        listEditDescSub: 'list_edit_desc_sub_',
        // 创建页
        createBack: 'enterprise_security_create_back',
        createName: 'enterprise_security_name',
        createSelectTemp: 'enterprise_security_temp',
        createChangeRuleType: 'enterprise_security_change_rule_type',
        createAddRuleBtn: 'enterprise_security_add_rule',
        createAddRuleSubmit: 'enterprise_security_add_rule_submit_',
        createAddRuleCancel: 'enterprise_security_add_rule_cancel_',
        createAddRuleEdit: 'enterprise_security_add_rule_edit_',
        ruleTable: 'enterprise_security_table_list',
        createDeleteRule: 'enterprise_security_delete_rule',
        createSubmit: 'enterprise_security_create_submit',
        // 详情页
        detailBack: 'enterprise_security_detail_back',
        detailCopyId: 'enterprise_security_detail_copy_id',
        detailTab: 'enterprise_security_detail_',
        detailBindInstance: 'enterprise_security_detail_bind_',
        detailUnbindInstance: 'enterprise_security_detail_unbind_',
        detailTable: 'enterprise_security_detail_table_',
        detailTableName: 'enterprise_security_detail_table_name_',
        detailSearch: 'enterprise_security_detail_search_',
        detailRefresh: 'enterprise_security_detail_refresh_',
        detailSecurityName: 'enterprise_security_detail_name',
        detailEditName: 'enterprise_security_detail_name_edit',
        detailEditNameInput: 'enterprise_security_detail_name_edit_input',
        detailEditNameSub: 'enterprise_security_detail_name_edit_sub'
    },
    snic: {
        listCreateBtn: 'snic_instance_create',
        listDeleteBtn: 'snic_instance_delete',
        listTable: 'snic_instance_list',
        listName: 'snic_instance_name_',
        listId: 'snic_instance_id_',
        listReleaseBtn: 'snic_instance_release',
        listMonitor: 'snic_instance_monitor_',
        listBandWidth: 'snic_instance_bandwidth_',
        listVpc: 'snic_instance_vpc_',
        listSubnet: 'snic_instance_subnet_',

        createNameInput: 'snic_instance_name',
        createVpcSelect: 'snic_instance_vpc',
        createSubnetSelect: 'snic_instance_subnet',
        createConfirmOrder: 'snic_create_confirm',
        createSubmitOrder: 'snic_create_submit',
        createBackPre: 'snic_create_back_pre',
        createCancel: 'snic_create_cancel',

        detailBack: 'snic-detail-back',
        detailName: 'snic-instance-name',
        detailNameEdit: 'snic-instance-name-edit',
        detailModifyName: 'snic-instance-modify-name',
        detailNameEditSub: 'snic-instance-name-edit-sub',

        securityGroupDisplay: 'snic_security_group_display',
        securityAssociate: 'snic_security_associate',

        monitorDisplay: 'snic_monitor_display'
    },
    nat: {
        listCreateBtn: 'nat_instance_create',
        listDeleteBtn: 'nat_instance_delete_',
        listTable: 'nat_instance_list',
        listName: 'nat_instance_name_',
        listId: 'nat_instance_id_',
        listMonitor: 'nat_instance_monitor_',
        listBindEip: 'nat_bind_eip_',
        listUnBindEip: 'nat_unbind_eip_',
        listVpc: 'nat_instance_vpc_',
        listSetDnat: 'nat_set_dnat_',
        listSetSnat: 'nat_set_snat_',
        listGatewayUpgrade: 'nat_gateway_upgrade_',

        createNameInput: 'nat_instance_name',
        createVpcSelect: 'nat_instance_vpc',
        createSubnetSelect: 'nat_instance_subnet',
        createConfirmOrder: 'nat_create_confirm',
        createSubmitOrder: 'nat_create_submit',
        createBackPre: 'nat_create_back_pre',
        createCancel: 'nat_create_cancel',

        detailBack: 'nat-detail-back',
        detailName: 'nat-instance-name',
        detailPriChangeCu: 'private_nat_detail_change_cu',
        detailNatIP: 'private_nat_detail_natIp',
        detailSnat: 'nat_detail_snat',
        detailDnat: 'nat_detail_dnat',
        detailMonitor: 'nat_detail_monitor',

        upgradeConfirm: 'nat_upgrade_confirm',

        // 私网nat
        listPriCreateBtn: 'private_nat_instance_create',
        listPriDeleteBtn: 'private_nat_instance_delete_',
        listPriTable: 'private_nat_instance_list',
        listPriName: 'private_nat_instance_name_',
        listPriId: 'private_nat_instance_id_',
        listPriMonitor: 'private_nat_instance_monitor_',
        listPriSetDnat: 'private_nat_set_dnat_',
        listPriSetSnat: 'private_nat_set_snat_',
        listPriChangeCu: 'private_nat_instance_change_cu',
        listPriMore: 'private_nat_instance_more'
    },
    flowMirror: {
        listTable: 'flowMirror_list',
        listEmpty: 'flowMirror_list_empty',
        instanceName: 'flowMirror_create_name',
        instanceEdit: 'flowMirror_list_edit_',
        submitButton: 'instance_submit_btn'
    },
    peerconn: {
        listTable: 'peerconn_list',
        listEmpty: 'peerconn_list_empty',
        listItemInitiator: 'peerconn_list_item_initiator',
        listActionSelect: 'peerconn_list_select',
        listActionOption: 'peerconn_list_option_'
    },
    diagnosis: {
        listCreateBtn: 'diagnosis_instance_create',
        listDeleteBtn: 'diagnosis_instance_delete_',
        listTable: 'diagnosis_instance_list',
        listName: 'diagnosis_instance_name_',
        listId: 'diagnosis_instance_id_',

        detailBack: 'diagnosis-detail-back',
        detailDesc: 'diagnosis-instance-desc'
    },
    vpn: {
        greVpnList: 'gre_vpn_list',
        ipsecVpnList: 'ipsec_vpn_list',
        sslVpnList: 'ssl_vpn_list',
        vpnTableList: 'vpn_instance_list',
        greVpnListCreate: 'vpn_gre_create',
        greVpnListDelete: 'vpn_gre_delete_',
        greVpnListEdit: 'vpn_gre_edit_',
        greVpnListTable: 'vpn_gre_table',
        greVpnListTableName: 'vpn_gre_table_name_',
        greVpnListTableId: 'vpn_gre_table_id_',

        createGatewayType: 'vpn_create_gateway_type',
        createName: 'vpn_create_name',
        createSelectSubnet: 'vpn_create_select_subnet',
        createSelectSubnetOption: 'vpn_create_select_subnet_option_',
        createNextBtn: 'vpn_create_next_btn',
        createSubmitBtn: 'vpn_create_submit_btn',

        detailName: 'vpn_instance_name'
    },
    // 专线演练
    dcDrill: {
        listTable: 'drill_plan_list',
        listName: 'drill_plan_name_',
        listCreateBtn: 'drill_plan_create',
        createModal: 'drill_plan_create_modal',
        createNameInput: 'drill_plan_input_name',
        durationInput: 'drill_plan_input_duration',
        resourceList: 'drill_plan_resource_list',
        createConfirmBtn: 'drill_plan_create_confirm_btn',
        searchInput: 'drill_plan_search_input',
        // 详情页
        detail: 'drill_plan_detail'
    },
    dcDrillResult: {
        listTable: 'drill_result_list',
        listName: 'drill_result_name_',
        listCreateBtn: 'drill_result_create',
        createModal: 'drill_result_create_modal',
        createNameInput: 'drill_result_input_name',
        durationInput: 'drill_result_input_duration',
        resourceList: 'drill_result_resource_list',
        createConfirmBtn: 'drill_result_create_confirm_btn',
        searchInput: 'drill_result_search_input',
        listReloadBtn: 'drill_result_refresh_btn',
        viewReportBtn: 'drill_result_view_report_btn',
        reportDrawer: 'drill_result_report_drawer',
        // 详情页
        detail: 'drill_result_detail'
    }
};
