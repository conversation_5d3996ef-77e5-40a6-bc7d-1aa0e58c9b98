import {useMemo} from 'react';

interface StatusProps {
    statusEnum: any;
    data: any;
}
export const useStatus = (props: StatusProps) => {
    const {statusEnum, data} = props;
    const status = useMemo(() => {
        let config: string[] = [];
        if (data?.status) {
            const {text, styleClass} = statusEnum.fromValue(data.status);
            config = [text, styleClass];
        }
        return config;
    }, [JSON.stringify(data)]);
    return status;
};
