import {useState, useEffect} from 'react';
import _ from 'lodash';
import {StsConfig} from '@/pages/sanPages/utils/config';
import {disable_vpn_region} from '@/pages/sanPages/common/flag';
import FLAG from '@/flags';
import {cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';

const AllRegion = window.$context.getEnum('AllRegion');
const kXhrOptions = {'x-silent': true};

const peerconnRoleName = StsConfig.PEERCONN.roleName;
const vpnRoleName = StsConfig.VPN.roleName;
const natRoleName = StsConfig.NAT.roleName;
const blbRoleName = StsConfig.BLB.roleName;
const etRoleName = StsConfig.DCGW.roleName;
const csnRoleName = StsConfig.CSN.roleName;
const l2gwRoleName = StsConfig.L2GW.roleName;

const getVpnWhiteList = () => {
    const region = window.$context.getCurrentRegionId();
    if (region === AllRegion.HK02) {
        return true;
    }
    return _.indexOf(disable_vpn_region, region) === -1;
};

const getEndPointWhite = () => {
    const region = window.$context.getCurrentRegionId();
    const inRegion = _.includes([AllRegion.BJKS], region);
    return !inRegion;
};

const getEnicWhite = () => {
    const region = window.$context.getCurrentRegionId();
    const inRegion = _.includes([AllRegion.HK, AllRegion.HK02, AllRegion.BJKS], region);
    return !inRegion;
};

const getPeerConnWhite = () => {
    const region = window.$context.getCurrentRegionId();
    const inRegion = _.includes([AllRegion.NJ, AllRegion.YQ, AllRegion.BJKS], region);
    const hasService = window.$context.getAvailableService()?.indexOf('PEERCONN') > -1;
    return !inRegion && hasService;
};

const commonWhiteList = () => {
    return window.$http.commonVpcWhiteList(
        {
            whiteListTypes: [
                'FlowlogWhiteList',
                'QosWhiteList',
                'ChinaUnicomWhiteList',
                'mirrorWhiteList',
                'GlrPcWhiteList',
                'GlrEtWhiteList',
                'GlrCsnWhiteList',
                'NormalNatWhiteList',
                'HaVipWhiteList',
                'DedicatedConnIpv6',
                'DcPhyChannelBfd',
                'DedicatedConnBaidu',
                'LineBuild',
                'DcgwNatWhitelist',
                'eniAuxiliaryIpWhiteList',
                'EriWhiteList',
                'DdcSgWhiteList',
                'FlowlogPcWhiteList',
                'FlowlogEtWhiteList',
                'FlowlogVpnWhiteList',
                'FlowlogPcDenyWhiteList',
                'FlowlogEtDenyWhiteList',
                'FlowlogCsnVpcWhiteList',
                'FlowlogCsnVpcDenyWhiteList',
                'FlowlogEnableFiveTupleWhiteList',
                'FlowlogNeutronWhiteList',
                'NatLimitRuleWhiteList',
                'PeerConnPayByTrafficWhiteList',
                'PeerConnTro95WhiteList',
                'CrossBorderWhiteList',
                'RelayWhiteList',
                'RouteOpenSourceAddress',
                'VpnLocalCidrBlackList',
                'VpnNatWhiteList',
                'HkgSinSslVpnWhite',
                'pathAnaliseWhitelist',
                'DxmDcPhyInfo',
                'EipBlackList',
                'SubnetShowIpUsedNumWhiteList',
                'IpCollectionWhiteList',
                'GaiaDbSgRegionWhiteList',
                'ReservePortPoolWhiteList'
            ]
        },
        kXhrOptions
    );
};

export const whiteKeyMap = [
    'commonWhite',
    'vpnWhite',
    'endPointWhite',
    'enicWhite',
    'l2gwSupportRegion',
    'peerconnWhite'
];

export const getWhiteListAll = () => {
    return Promise.all([
        commonWhiteList(),
        getVpnWhiteList(),
        getEndPointWhite(),
        getEnicWhite(),
        window.$http.getL2gwWhiteList(),
        getPeerConnWhite()
    ])
        .then(res => {
            _.map(res, (item: any, index: number) => {
                if (index === 0) {
                    window.$storage.set('commonWhite', item.inWhiteListsResultMap);
                } else {
                    window.$storage.set(`${whiteKeyMap[index]}`, item);
                }
            });
            return Promise.resolve(res);
        })
        .catch(() => [{inWhiteListsResultMap: {}}, false, false, false, false, false]);
};

export const useAuth = () => {
    const [isFinished, setIsFinished] = useState(false);
    const [whiteAuthMap, setWhiteAuthMap] = useState<any>({});

    const getIamQuery = () => {
        return Promise.all([
            window.$http.iamStsRoleQuery(
                {
                    roleName: peerconnRoleName
                },
                {region: AllRegion.BJ}
            ),
            window.$http.iamStsRoleQuery(
                {
                    roleName: vpnRoleName
                },
                {region: AllRegion.BJ}
            ),
            window.$http.iamStsRoleQuery(
                {
                    roleName: natRoleName
                },
                {region: AllRegion.BJ}
            ),
            window.$http.iamStsRoleQuery(
                {
                    roleName: blbRoleName
                },
                {region: AllRegion.BJ}
            ),
            window.$http.iamStsRoleQuery(
                {
                    roleName: etRoleName
                },
                {region: AllRegion.BJ}
            ),
            window.$http.iamStsRoleQuery(
                {
                    roleName: csnRoleName
                },
                {region: AllRegion.BJ}
            ),
            window.$http.iamStsRoleQuery(
                {
                    roleName: l2gwRoleName
                },
                {region: AllRegion.BJ}
            ),
            window.$http.iamStsRoleQuery(
                {
                    roleName: 'BceServiceRole_bec'
                },
                {region: AllRegion.BJ}
            )
        ])
            .then(result => {
                if (result[0] && result[0].name) {
                    window.$storage.set('peerConnSts', true);
                }
                if (result[1] && result[1].name) {
                    window.$storage.set('vpnSts', true);
                }
                if (result[2] && result[2].name) {
                    window.$storage.set('natSts', true);
                }
                if (result[3] && result[3].name) {
                    window.$storage.set('blbSts', true);
                }
                if (result[4] && result[4].name) {
                    window.$storage.set('etSts', true);
                }
                if (result[5] && result[5].name) {
                    window.$storage.set('csnSts', true);
                }
                if (result[6] && result[6].name) {
                    window.$storage.set('l2gwSts', true);
                }
                if (result[7] && result[7].name) {
                    window.$storage.set('becSts', true);
                }
                // natlist页面intro显隐
                window.$storage.set('natIntroShow', true);
                return Promise.resolve(result);
            })
            .catch(() => {
                return Promise.resolve([]);
            });
    };

    /** 检查是否欠费 */
    const checkAccount = () => {
        return Promise.all([
            window.$http.purchaseValidation(
                {
                    serviceType: 'ET',
                    productType: 'prepay'
                },
                kXhrOptions
            )
        ]).then(result => {
            const invalid = result.find(item => !item.status && item?.reasonCode === 'DEBT' && !FLAG.NetworkSupportXS);
            const state = {
                disabled: !!invalid,
                message: invalid?.failReason
                    ? invalid?.failReason +
                      i18n.t('请及时') + '<a href="/finance/#/finance/account/recharge" target="_blank">' + i18n.t('充值') + '</a>'
                    : ''
            };
            window.$storage.set('accountState', state);
            return state;
        });
    };

    useEffect(() => {
        Promise.all([getWhiteListAll(), getIamQuery(), checkAccount()])
            .then(res => {
                if (res.length) {
                    const whiteRes = res[0];
                    const stsRes = res[1];

                    /** 基础白名单&sts权限 */
                    const authMap = {
                        commonWhite: whiteRes[0]?.inWhiteListsResultMap || {},
                        vpnWhite: whiteRes[1],
                        endPointWhite: whiteRes[2],
                        enicWhite: whiteRes[3],
                        l2gwSupportRegion: whiteRes[4],
                        peerconnWhite: whiteRes[5],
                        peerConnSts: !!stsRes[0]?.name,
                        natAuthFlag: FLAG.NetworkSupportNat,
                        vpnSts: !!stsRes[1]?.name,
                        natSts: !!stsRes[2]?.name,
                        blbSts: !!stsRes[3]?.name,
                        etSts: !!stsRes[4]?.name,
                        csnSts: !!stsRes[5]?.name,
                        natIntroShow: true,
                        njRegionWhiteList: window.$context.getCurrentRegionId() !== 'nj',
                        NetworkUnSupportXS: !FLAG.NetworkSupportXS
                    };
                    const {
                        vpnWhite,
                        enicWhite,
                        endPointWhite,
                        njRegionWhiteList,
                        commonWhite: {QosWhiteList, GlrPcWhiteList, GlrEtWhiteList, GlrCsnWhiteList, HaVipWhiteList}
                    } = authMap as Record<string, any>;

                    /** 自定义联合判断白名单权限 */
                    const vpnAuthFlag = FLAG.NetworkSupportVpn && vpnWhite;
                    const qosStrategyWhite = njRegionWhiteList && QosWhiteList;
                    const netcardWhite = enicWhite || endPointWhite || HaVipWhiteList;
                    const gatewayLimitSpeedWhite = GlrPcWhiteList || GlrEtWhiteList || GlrCsnWhiteList;
                    const qosWhite = qosStrategyWhite || gatewayLimitSpeedWhite;

                    setWhiteAuthMap({
                        ...authMap,
                        qosWhite,
                        vpnAuthFlag,
                        netcardWhite,
                        qosStrategyWhite,
                        gatewayLimitSpeedWhite
                    });
                }
            })
            .finally(() => setIsFinished(true));
    }, []);
    return {isFinished, whiteAuthMap};
};
