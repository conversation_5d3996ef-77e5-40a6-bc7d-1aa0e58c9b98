import React, {FC} from 'react';
import VpcSelect from '@/components/VpcSelect';

interface PageHeaderProps {
    title?: string; // 产品标题
    showVpcSelect?: boolean; // 是否显示VpcSelect，默认展示
    CustomComp?: FC; // 自定义右侧展示组件
}
const PageHeader: FC<PageHeaderProps> = props => {
    const {title = '', showVpcSelect = true, CustomComp} = props;
    return (
        <div className="w-full h-[48px] flex-between bg-[#FFFFFF] px-[16px] sticky top-0 dom-shadow z-50">
            <div>
                <span className="text-[16px] color-[#151B26] font-[500] mr-[12px]">{title}</span>
                {showVpcSelect && <VpcSelect />}
            </div>
            <div>{CustomComp ? <CustomComp /> : null}</div>
        </div>
    );
};
export default PageHeader;
