import React from 'react';
import {FC, useState, useMemo} from 'react';
import {Modal, Button} from 'acud';
import {OutlinedBceWarningCircle} from 'acud-icon';
import './index.less';
import {cbaI18nInstance as i18n,Trans} from '@baidu/bce-react-toolkit';

interface IProps {
    title?: string;
    backUrl?: string;
    onClose?: () => void;
}
const ClosureRegion: FC<IProps> = props => {
    const {title = i18n.t ('产品下线提示'), backUrl, onClose} = props;
    const [visible, setVisible] = useState(true);

    const renderTitle = () => {
        return (
            <div className="title-widget">
                <OutlinedBceWarningCircle style={{color: '#FF9326'}} />
                <span className="title">{title}</span>
            </div>
        );
    };
    const handleAction = () => {
        setVisible(false);
        onClose?.();
        if (backUrl) {
            window.location.hash = backUrl;
        }
    };
    const actionText = backUrl ? i18n.t('返回列表页') : i18n.t('确认');
    return (
        <>
            <Modal
                closable={false}
                className="closure-region-modal"
                title={renderTitle()}
                getContainer={() => document.getElementById('closure-region-widget')}
                visible={visible}
                style={{top: '-20px'}}
                footer={
                    <Button onClick={handleAction} type="primary">
                        {actionText}
                    </Button>
                }
            >
                <div>
                    <Trans >
                 <h3 className="closure-title">停服公告：</h3>
                
                    <p>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;尊敬的百度智能云客户您好，当前Region将于2025年2月4号停止服务，请立即进行就近Region迁移。感谢您的支持，谢谢！
                        <a
                            target="_blank"
                            href={'https://cloud.baidu.com/news/notice_2760dce8-91b9-4d27-8a74-2242d827b69c'}
                        >
                            详情
                        </a>
                        
                    </p>
                      </Trans>
                </div>
            </Modal>
        </>
    );
};

export default ClosureRegion;
