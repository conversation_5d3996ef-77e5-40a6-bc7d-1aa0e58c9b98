import React from 'react';
import { Checkbox, Dropdown } from 'acud';
import './style.less';

const CustomColumn = ({
    children,
    columnList = [] as any,
    visible,
    setVisible,
    value,
    onChange
}) => {
    const menu = (
        <div className='custom-column'>
            <Checkbox.Group
                options={columnList}
                value={value}
                onChange={(values) => {
                    onChange(values);
                    setVisible(true);
                }}
            >
            </Checkbox.Group>
        </div>
    );

    return (
        <Dropdown
            trigger={['click']}
            overlay={menu}
            visible={visible}
            onVisibleChange={setVisible}
        >
            {children}
        </Dropdown>
    )
};

export default CustomColumn;
