import React, {FC, ReactNode, useState, useMemo, useEffect} from 'react';
import {Modal, Button} from 'acud';
import {FilledSuccess, FilledWarn, FilledInfo, FilledError} from 'acud-icon';
import './index.less';
import {cbaI18nInstance as i18n,Trans} from '@baidu/bce-react-toolkit';

/** acud 提供的confirm组件不是太好用 */
interface ConfirmProps {
    type?: 'success' | 'info' | 'warning' | 'error';
    title?: ReactNode;
    visible?: boolean;
    maskClosable?: boolean;
    destroyOnClose?: boolean;
    confirmLoading?: boolean;
    showFooter?: boolean; // 是否展示底部操作按钮
    disabled?: boolean; // 是否禁用确定按钮
    width?: string | number;
    content?: ReactNode;
    zIndex?: number;
    okText?: string;
    cancelText?: string;
    onOk?: () => void;
    onCancel?: () => void;
}
const Confirm: FC<ConfirmProps> = props => {
    const {
        type = 'info',
        title = i18n.t ('提示'),
        showFooter = true,
        content,
        confirmLoading = false,
        disabled = false,
        okText = i18n.t ('确定'),
        cancelText = i18n.t ('取消'),
        onOk,
        onCancel
    } = props;

    const [disable, setDisable] = useState(disabled);
    const [loading, setLoading] = useState(confirmLoading);

    const iconStyle = {
        marginRight: '12px',
        fontSize: '20px'
    };

    const typeMapIcon = {
        success: <FilledSuccess style={iconStyle} color="#30BF13" />,
        warning: <FilledWarn style={iconStyle} color="#FF9326" />,
        info: <FilledInfo style={iconStyle} color="#2468F2" />,
        error: <FilledError style={iconStyle} color="#F33E3E" />
    };

    const ModalTitle = useMemo(() => {
        return (
            <div className="acud-modal-title flex-y-center">
                {typeMapIcon[type]}
                {title}
            </div>
        );
    }, [title, type]);

    const footer = useMemo(() => {
        if (showFooter) {
            return (
                <>
                    <Button onClick={onCancel}>{cancelText}</Button>
                    <Button disabled={disable} loading={loading} onClick={onOk} type="primary">
                        {okText}
                    </Button>
                </>
            );
        } else {
            return null;
        }
    }, [showFooter, loading]);

    // 设置按钮loading
    useEffect(() => {
        setLoading(confirmLoading);
    }, [confirmLoading]);

    // 设置确认按钮的禁用态
    useEffect(() => {
        setDisable(disabled);
    }, [disabled]);

    return (
        <Modal {...props} title={ModalTitle} footer={footer} wrapClassName="acud-confirm-modal-widget">
            {content}
        </Modal>
    );
};
export default Confirm;
