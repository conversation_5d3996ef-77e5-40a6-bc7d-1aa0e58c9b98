import React, {FC, ReactNode, useState} from 'react';
import {Button, Popover, Input} from 'acud';
import {TooltipPlacement} from 'acud/lib/tooltip';
import {twMerge} from 'tailwind-merge';
import _ from 'lodash';
import {cbaI18nInstance as i18n,Trans} from '@baidu/bce-react-toolkit';
interface EditPopoverProps {
    initValue?: string;
    children: ReactNode;
    placement?: TooltipPlacement;
    trigger?: 'click' | 'hover';
    placeholder?: string;
    rule?: Record<string, any>;
    inputType?: 'input' | 'textarea';
    limitLength?: number | undefined;
    width?: number;
    onEdit?: (value: string) => void;
}

const EditPopover: FC<EditPopoverProps> = props => {
    const {
        initValue = '',
        children,
        placement = 'top',
        trigger = 'click',
        placeholder = i18n.t('请输入'),
        rule,
        inputType = 'input',
        limitLength,
        width = 400,
        onEdit
    } = props;
    const [inputVal, setInputVal] = useState(initValue);
    const [visible, setVisible] = useState(false);
    const [disabled, setDisabled] = useState(true);
    const [tip, setTip] = useState(rule.placeholder);

    // 初始化时 提示不高亮
    const tipStyle = twMerge('text-[#84868c] mt-[8px]', disabled && inputVal !== initValue ? 'text-[#F33E3E]' : '');

    const handleHide = () => {
        setVisible(false);
    };

    const handleValidate = (value: string): boolean => {
        if (rule.required && !_.trim(value)) {
            setTip(rule.requiredErrorMessage);
            return false;
        }
        if (rule.pattern && !rule.pattern.test(value)) {
            setTip(rule.patternErrorMessage);
            return false;
        }
        if (rule.maxLength && value.length > rule.maxLength) {
            setTip(rule.patternErrorMessage);
            return false;
        }
        setTip(rule.placeholder);
        return value === initValue ? false : true;
    };

    const handleChange = (e: any) => {
        const value = e.target.value;
        const validateRes = handleValidate(value);
        setInputVal(value);
        setDisabled(!validateRes);
    };

    const handleVisibleChange = (visible: boolean) => {
        setVisible(visible);
    };

    const handleConfirm = () => {
        onEdit?.(inputVal);
    };
    return (
        <Popover
            placement={placement}
            trigger={trigger}
            visible={visible}
            destroyTooltipOnHide
            onVisibleChange={handleVisibleChange}
            content={
                <div className="max-w-[360px]">
                    <div>
                        {inputType === 'input' ? (
                            <Input
                                style={{width: '360px'}}
                                value={inputVal}
                                onChange={handleChange}
                                placeholder={placeholder}
                                limitLength={limitLength}
                            />
                        ) : (
                            <Input.TextArea
                                width={width}
                                value={inputVal}
                                onChange={handleChange}
                                placeholder={placeholder}
                                limitLength={limitLength}
                            />
                        )}
                    </div>
                    {tip && <p className={tipStyle}>{tip}</p>}
                    <div className="mt-[12px]">
                        <Trans>
                        <Button disabled={disabled} className="mr-[10px]" type="primary" onClick={handleConfirm}>
                            确定
                        </Button>
                        <Button onClick={handleHide}>取消</Button>
                        </Trans>
                    </div>
                </div>
            }
        >
            {children}
        </Popover>
    );
};
export default EditPopover;
