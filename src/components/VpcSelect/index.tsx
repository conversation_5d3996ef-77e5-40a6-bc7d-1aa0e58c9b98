import u from 'lodash';
import React, {FC, useState, useEffect} from 'react';
import {Select, Tooltip} from 'acud';
import {getVpcList} from '@/apis/vpc';
import { useTranslation,cbaI18nInstance as i18n } from '@baidu/bce-react-toolkit';

interface VpcSelectProps {
    placement?: 'left' | 'right' | 'top' | 'bottom';
    notSupportAllVpc?: boolean; // 不显示全部私有网络
    onInit?: () => void; // 初始化完后默认选中全部或第一个vpc
    onChange?: (value: string) => void;
}
const VpcSelect: FC<VpcSelectProps> = props => {
        const { t } = useTranslation();
    const {placement = 'top', notSupportAllVpc = false, onInit, onChange} = props;
    const [loading, setLoading] = useState(false);
    const [vpcId, setVpcId] = useState('');
    const [vpcList, setVpcList] = useState<any[]>([]);
    const [vpcInfoList, setVpcInfoList] = useState<any[]>([]);

    const queryVpcList = async () => {
        try {
            setLoading(true);
            const res = (await getVpcList()) as any;
            if (!!res?.length) {
                const formatedRes = u.map(res, item => ({
                    label: `${item.name}（${item.cidr}）`,
                    value: item.vpcId,
                    vpcInfo: item
                }));
                if (!notSupportAllVpc) {
                    formatedRes.unshift({
                        label: i18n.t('所在网络：全部私有网络'),
                        value: '',
                        vpcInfo: null
                    });
                }
                setVpcList(formatedRes);
                setVpcInfoList(res);
                window.$storage.set('vpcList', formatedRes);
                window.$storage.set('vpcInfoList', res);

                const vpcId = window.$storage.get('vpcId');
                const existVpcId = formatedRes.find(item => item.value === vpcId);
                if (vpcId && !!existVpcId) {
                    setVpcId(vpcId);
                    u.each(res, item => {
                        if (vpcId === item.vpcId) {
                            window.$storage.set('vpcInfo', item);
                        }
                    });
                } else {
                    const vpcId = formatedRes[0].value;
                    setVpcId(vpcId);
                    window.$storage.set('vpcId', vpcId);
                    window.$storage.set('vpcInfo', vpcId ? res[0] : {});
                }
                onInit?.();
            }
        } finally {
            setLoading(false);
        }
    };
    useEffect(() => {
        queryVpcList();
    }, []);

    const handleVpcChange = value => {
        setVpcId(value);
        window.$storage.set('vpcId', value);
        if (!value) {
            window.$storage.set('vpcInfo', {});
        } else {
            u.each(vpcInfoList, item => {
                if (value === item.vpcId) {
                    window.$storage.set('vpcInfo', item);
                }
            });
        }
        onChange?.(value);
    };

    const renderOption = (label, value) => {
        return (
            <Tooltip placement={placement} title={label}>
                <span className="text-ellipsis">{label}</span>
            </Tooltip>
        );
    };
    return (
        <>
            <Select
                style={{width: 240}}
                showSearch
                optionFilterProp="label"
                onChange={handleVpcChange}
                options={vpcList}
                loading={loading}
                value={vpcId}
                placeholder={t('请选择')}
                optionRender={renderOption}
            ></Select>
        </>
    );
};
export default VpcSelect;
