import React from 'react';
import {Button} from 'acud';
import './index.less';
import {cbaI18nInstance as i18n,Trans} from '@baidu/bce-react-toolkit';
interface PageProps {
    backUrl?: string;
}
const NoPermission: React.FC<PageProps> = props => {
    const {backUrl} = props;
    const handleBack = () => {
        window.location.hash = backUrl || '';
    };
    return (
        <div className="instance-not-found-class">
            <div className="center-class">
                <div className="no-found-image"></div>
                <Trans>
                <div className="no-found-tip">您访问的资源不存在或者已被删除</div>
                <div className="no-found-desc">请稍候刷新页面重试，或返回资源列表页刷新确认信息</div>
                <Button onClick={handleBack}>返回列表页</Button>
                </Trans>
            </div>
        </div>
    );
};
export default NoPermission;
