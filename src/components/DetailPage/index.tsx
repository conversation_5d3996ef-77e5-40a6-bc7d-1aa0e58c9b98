import React, {<PERSON>} from 'react';
import {Link} from 'acud';
import {OutlinedLeft} from 'acud-icon';
import cn from 'classnames';
import SecurityTabs, {TabProps} from '@/components/TabPage';
import './index.less';
import { Trans } from '@baidu/bce-react-toolkit';

interface DetailPageProps {
    backUrl?: string;
    headerName?: string;
    target?: '_blank' | '_self';
    statusClassName?: string;
    statusText?: string;
    isShowHeader?: boolean;
    tabClassName?: string;
    onBackList?: any; // 返回列表的回调函数
    isSwitchReRender?: boolean;
}

const DetailPage: FC<DetailPageProps & TabProps> = props => {
    const {
        backUrl = '',
        mode = 'horizontal',
        target = '_self',
        headerName,
        panesData,
        statusText = '',
        statusClassName = '',
        isShowHeader = true,
        tabClassName = '',
        isSwitchReRender = false
    } = props;

    return (
        <div className={cn('detail-widget')}>
            {isShowHeader && (
                <div className="detail-widget-header">
                    <Trans>
                    <Link
                        href={backUrl}
                        target={target}
                        type="primary"
                        icon={<OutlinedLeft width={16} height={16} fill={'#84868c'} />}
                    >
                        返回
                    </Link>
                    </Trans>
                    <span className="name">{headerName}</span>
                    <span className={cn('status-badge-common', {[statusClassName]: true})}>{statusText}</span>
                </div>
            )}
            <div className={cn('detail-widget-content', {[tabClassName]: true})}>
                {<SecurityTabs mode={mode} panesData={panesData} isSwitchReRender={isSwitchReRender} />}
            </div>
        </div>
    );
};
export default DetailPage;
