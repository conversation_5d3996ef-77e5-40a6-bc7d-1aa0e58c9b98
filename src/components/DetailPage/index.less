.detail-widget {
    // height: calc(100% - 82px);
    background-color: #f7f7f7;
    &-header {
        display: flex;
        align-items: center;
        flex-shrink: none;
        background-color: #ffffff;
        height: 50px;
        margin: 0;
        padding: 0 16px;
        .acud-link {
            color: #83868c;
            font-size: 14px;
            display: flex;
            align-items: center;
            &:hover {
                color: #2468f2;
                .acudicon-outlined-left {
                    svg {
                        fill: #2468f2;
                    }
                }
            }
        }
        .name {
            margin-left: 16px;
            font-size: 16px;
            font-weight: 500;
            color: #151b26;
        }
        .status-badge-common {
            margin-left: 12px;
            margin-top: 2px;
        }
    }
    &-content {
        margin: 16px;
        min-height: calc(100vh - 132px);
        height: 100%;
        background-color: #ffffff;
    }
}

.no-instance-wrap {
    height: calc(100% - 50px);
    .dcgw-tab-class {
        margin: 0;
    }
}

.page-empty {
    position: relative;
    top: 25%;
    .page-empty-action {
        .page-empty-action-button:nth-child(2n + 2) {
            display: none;
        }
    }
}

.acud-loading-loading-wrapper {
    height: 100%;
    & > * {
        height: 100%;
    }
}
