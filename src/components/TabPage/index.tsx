import React, {useState, useEffect, FC} from 'react';
import {Tabs} from 'acud';
import cn from 'classnames';
import './index.less';

const {TabPane} = Tabs;
interface PaneProps {
    key: string;
    tab: string;
    url?: string;
    content: React.ReactNode;
}
export interface TabProps {
    mode?: 'horizontal' | 'vertical';
    isSwitchReRender?: boolean;
    panesData: PaneProps[];
    onTabChange?: (key: string) => void;
}
const TabPage: FC<TabProps> = props => {
    const {mode = 'horizontal', panesData, isSwitchReRender = false, onTabChange} = props;
    const hash = location.hash;
    const pageUrl = hash.split('#')?.[1]?.split('?')?.[0];
    const [activeKey, setActiveKey] = useState(panesData[0]?.key);

    // 更新url & 渲染对应tabPane
    const initUrlActiveKey = (url: string, ak: string) => {
        const query = location?.hash?.split('?')?.[1] || '';
        window.location.hash = `#${url + (query ? `?${query}` : '')}`;
        setActiveKey(ak);
    };

    // 根据最新的pageUrl渲染对应tabPane
    useEffect(() => {
        if (pageUrl) {
            const targetTabPane = panesData.find((pane: any) => ~pane.key.split(',').indexOf(pageUrl));
            if (targetTabPane) {
                initUrlActiveKey(pageUrl, targetTabPane.key);
            }
        }
    }, [panesData]);

    const handleTabsChange = (key: string) => {
        // 组件bug key没变也会执行
        if (key !== activeKey) {
            const urlKeyArr = key.split(',');
            const urlKey = urlKeyArr[0];
            initUrlActiveKey(urlKey, key);
            onTabChange?.(key);
        }
    };
    return (
        <div className="tab-widget">
            <Tabs
                className={cn({'tab-vertical-widget': mode === 'vertical'})}
                activeKey={activeKey}
                onChange={handleTabsChange}
                data-testid="app-tab-wrapper"
            >
                {panesData.map((pane: any) => (
                    <TabPane tab={pane.tab} key={pane.key}>
                        {isSwitchReRender ? (pane.key === activeKey ? pane.content : null) : pane.content}
                    </TabPane>
                ))}
            </Tabs>
        </div>
    );
};
export default TabPage;
