@media screen and (max-width: 1280px) {
    .IPv6-vpn-wrapper {
        background:
            url('http://bce.bdstatic.com/network-frontend/IPv6-bg-1280.png') no-repeat,
            linear-gradient(180deg, rgba(230, 240, 255, 0) 0%, #ffffff 63%);
        background-size: 100% 100%;
    }
    .endpoint-peerconn-wrapper {
        background:
            url('http://bce.bdstatic.com/network-frontend/endpoint-bg-1280.png') no-repeat,
            linear-gradient(180deg, rgba(230, 240, 255, 0) 0%, #ffffff 63%);
        background-size: 100% 100%;
    }
}
@media screen and (min-width: 1280px) and (max-width: 1440px) {
    .IPv6-vpn-wrapper {
        background:
            url('http://bce.bdstatic.com/network-frontend/IPv6-bg-1440.png') no-repeat,
            linear-gradient(180deg, rgba(230, 240, 255, 0) 0%, #ffffff 63%);
        background-size: 100% 100%;
    }
    .endpoint-peerconn-wrapper {
        background:
            url('http://bce.bdstatic.com/network-frontend/endpoint-bg-1440.png') no-repeat,
            linear-gradient(180deg, rgba(230, 240, 255, 0) 0%, #ffffff 63%);
        background-size: 100% 100%;
    }
}
@media screen and (min-width: 1440px) {
    .IPv6-vpn-wrapper {
        background:
            url('http://bce.bdstatic.com/network-frontend/IPv6-bg-1920.png') no-repeat,
            linear-gradient(180deg, rgba(230, 240, 255, 0) 0%, #ffffff 63%);
        background-size: 100% 100%;
    }
    .endpoint-peerconn-wrapper {
        background:
            url('http://bce.bdstatic.com/network-frontend/endpoint-bg-1920.png') no-repeat,
            linear-gradient(180deg, rgba(230, 240, 255, 0) 0%, #ffffff 63%);
        background-size: 100% 100%;
    }
}
