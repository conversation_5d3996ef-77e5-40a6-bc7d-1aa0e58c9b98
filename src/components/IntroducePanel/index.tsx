import React, {FC, useMemo, useState, useEffect} from 'react';
import {twMerge} from 'tailwind-merge';
import './index.less';
import {cbaI18nInstance as i18n,Trans} from '@baidu/bce-react-toolkit';
interface IntroducePanelProps {
    title?: string;
    description?: string;
    markerDesc?: string[];
    introduceOptions?: Array<{label: string}>;
    isShow?: boolean;
    customClass?: string;
    onToggle?: (visible: boolean) => void;
}
const IntroducePanel: FC<IntroducePanelProps> = props => {
    const {title, description, markerDesc = [], introduceOptions, isShow = true, customClass, onToggle} = props;
    const [visible, setVisible] = useState(false);

    useEffect(() => {
        setVisible(isShow);
    }, [isShow]);

    const hidePanel = () => {
        setVisible(false);
        onToggle?.(false);
    };

    return visible ? (
        <div className={twMerge('relative m-[16px] p-[24px] rounded-[6px] bg-gray-400', customClass)}>
            <Trans>
            <span
                className="absolute top-[24px] right-[24px] inline-block text-[#2468F2] cursor-pointer text-[12px] leading-[17px]"
                onClick={hidePanel}
            >
                {'隐藏'}
            </span>
            </Trans>
            <h4 className="text-[20px] text-[#151b26] font-[500] leading-[28px]">{title}wrwrew</h4>
            {description && (
                <div className="mt-[16px] mb-[24px] text-[12px] text-[#5c5f66] leading-[24px] font-[400]">
                    {description}
                </div>
            )}
            <div className="mt-[24px] ">
                {!!markerDesc?.length &&
                    markerDesc.map((item, index) => (
                        <div className="flex items-center mt-[6px]" key={index}>
                            <div className="inline-block rounded-[50%] w-[8px] h-[8px] mr-[8px] bg-transparent border-solid border-[2px] border-[#2468F2]"></div>
                            <p>{item}</p>
                            {index === 0 && <br />}
                        </div>
                    ))}
            </div>
            <div className="flex">
                {!!introduceOptions?.length &&
                    introduceOptions.map((item, index) => {
                        return (
                            <div className="mr-[32px]" key={index}>
                                <span className="inline-block w-[8px] h-[8px] rounded-[100%] bg-[#FFFFFF] border-solid border-[2px] border-[#2468F2] mr-[4px]"></span>
                                {item.label}
                            </div>
                        );
                    })}
            </div>
        </div>
    ) : null;
};
export default IntroducePanel;
