import React from 'react';
import {Button} from 'acud';
import './index.less';
import {cbaI18nInstance as i18n,Trans} from '@baidu/bce-react-toolkit';

interface PageProps {
    backUrl?: string;
}
const NoPermission: React.FC<PageProps> = props => {
    const {backUrl} = props;

    const handleBack = () => {
        window.location.hash = backUrl || '';
    };
    return (
        <div className="instance-not-found-class">
            <div className="center-class">
                <div className="no-found-image"></div>
                <Trans>
                <div className="no-found-tip">您没有该实例的访问权限，请联系主账户负责人进行添加权限</div>
                <div className="no-found-desc">
                    百度智能云账户下资源的访问权限详情参考
                    <a href="/iam/#/iam/overview" target="_blank">
                        {' '}
                        权限管理{' '}
                    </a>
                </div>
                <Button onClick={handleBack}>返回列表页</Button>
                </Trans>
            </div>
        </div>
    );
};
export default NoPermission;
