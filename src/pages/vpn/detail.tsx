import React, {useEffect, useMemo} from 'react';
import {useRequest} from 'ahooks';
import {getQueryParams, useRegion, cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
import {getVpnDetail} from '@/apis';
import {useStatus} from '@/hooks';
import {VpnStatus} from '@/pages/sanPages/common';
import {useGlobalContext} from '@/context';
import Detail from '@/pages/sanPages/pages/vpn/detail/detail';
import RouteManage from '@/pages/sanPages/pages/vpn/detail/route/list';
import InstanceList from '@/pages/sanPages/pages/vpn/detail/nat/list';
import VpnDetailMonitor from '@/pages/sanPages/pages/vpn/detail/monitorChart/monitor';
import DetailPage from '@/components/DetailPage';

import './index.less';

const VPNTYPE_MAPPING_PATH = {
    ipsec: '#/vpc/vpn/list',
    ssl: '#/vpc/sslvpn/list',
    gre: '#/vpc/grevpn/list'
};
const VpnDetail = () => {
    const params: any = getQueryParams();
    const [{globalState}] = useGlobalContext() as any;

    const onRegionChange = () => {
        if (vpnType === 'ipsec') {
            location.hash = '#/vpc/vpn/list';
        } else if (vpnType === 'ssl') {
            location.hash = '#/vpc/sslvpn/list';
        } else {
            location.hash = '#/vpc/grevpn/list';
        }
    };
    const {region} = useRegion({onRegionChange});
    const isShowRoute = params?.showRoute === 'true';
    const vpnType = params?.vpnType;
    const inNatWhite = globalState?.commonWhite?.VpnNatWhiteList;
    const {data = {} as any, loading, error, run} = useRequest(() => getVpnDetail({vpnId: params.vpnId}));

    const handleUpdate = () => {
        run();
    };

    const panesData = [
        {
            key: '/vpc/vpn/detail',
            tab: i18n.t('实例信息'),
            content: <Detail context={{...params, updateVpnName: handleUpdate}} />
        },
        {
            key: '/vpc/vpn/route',
            tab: i18n.t('路由管理'),
            content: <RouteManage context={{...params, instance: data}} />
        },
        {key: '/vpc/vpn/nat', tab: i18n.t('云端静态NAT'), content: <InstanceList context={params} />},
        {key: '/vpc/vpn/idcnat', tab: i18n.t('IDC端静态NAT'), content: <InstanceList context={params} />},
        {key: '/vpc/vpn/idcdnat', tab: i18n.t('云端IP端口静态NAT'), content: <InstanceList context={params} />},
        {key: '/vpc/vpn/vpcdnat', tab: i18n.t('云端DNAT'), content: <InstanceList context={params} />},
        {key: '/vpc/vpn/monitor', tab: i18n.t('监控'), content: <VpnDetailMonitor context={params} />}
    ];

    const whitedPanesData = useMemo(() => {
        let newPanesData = [...panesData];
        const whiteMenuUrl = ['/vpc/vpn/nat', '/vpc/vpn/idcnat', '/vpc/vpn/idcdnat', '/vpc/vpn/vpcdnat'];
        if (!inNatWhite || vpnType !== 'ipsec') {
            newPanesData = newPanesData.filter(item => !whiteMenuUrl.includes(item.key));
        }
        if (!isShowRoute) {
            newPanesData = newPanesData.filter(item => item.key !== '/vpc/vpn/route');
        }
        // 私网类型网关不展示监控
        if (!(data?.netType === 'internet')) {
            newPanesData = newPanesData.filter(item => item.key !== '/vpc/vpn/monitor');
        }
        return newPanesData;
    }, [isShowRoute, vpnType, inNatWhite, data]);

    const [text, styleClass] = useStatus({statusEnum: VpnStatus, data});

    const backList = () => {
        window.location.hash = VPNTYPE_MAPPING_PATH[params.vpnType];
    };

    return (
        <>
            <DetailPage
                mode="vertical"
                headerName={data.vpnName}
                statusText={text}
                statusClassName={styleClass}
                backUrl={VPNTYPE_MAPPING_PATH[params.vpnType]}
                panesData={whitedPanesData}
                onBackList={backList}
            />
        </>
    );
};
export default VpnDetail;
