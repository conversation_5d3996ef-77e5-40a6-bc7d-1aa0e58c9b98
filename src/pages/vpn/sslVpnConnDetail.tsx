import React, {useEffect, useMemo} from 'react';
import {useRequest} from 'ahooks';
import {getQueryParams, cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
import {getSslVpnConnDetail, getVpcInfo} from '@/apis';
import {useStatus} from '@/hooks';
import {SslConnStatus} from '@/pages/sanPages/common';
import Detail from '@/pages/sanPages/pages/vpn/detail/sslDetail';
import UserManage from '@/pages/sanPages/pages/vpn/detail/userManage/user';
import SslConnDetailMonitor from '@/pages/sanPages/pages/vpn/detail/sslConnMonitor/monitor';
import DetailPage from '@/components/DetailPage';

import './index.less';

const SslVpnConnDetail = () => {
    const params: any = getQueryParams();
    const {vpnId, vpnConnId, vpcId} = params;
    const {data = {} as any, loading, error, run} = useRequest(() => getSslVpnConnDetail({vpnId, vpnConnId}));
    const {
        data: vpcData = {} as any,
        loading: vpcLoading,
        error: vpcError,
        run: vpcRun
    } = useRequest(() => getVpcInfo({vpcId: [vpcId]}));

    const panesData = [
        {
            key: '/vpc/vpn/ssl/detail',
            tab: i18n.t('实例信息'),
            content: <Detail context={{...params, instance: data, vpcInfo: vpcData}} />
        },
        {
            key: '/vpc/vpn/ssl/user',
            tab: i18n.t('用户管理'),
            content: <UserManage context={params} />
        },
        {key: '/vpc/vpn/ssl/monitor', tab: i18n.t('监控'), content: <SslConnDetailMonitor context={params} />}
    ];

    const whitedPanesData = useMemo(() => {
        let newPanesData = [...panesData];
        return newPanesData;
    }, [data, vpcData]);

    const [text, styleClass] = useStatus({statusEnum: SslConnStatus, data});

    const backList = () => {
        window.location.hash = '#/vpc/sslvpn/list';
    };

    return (
        <>
            <DetailPage
                mode="vertical"
                headerName={data.vpnConnName}
                statusText={text}
                statusClassName={styleClass}
                backUrl="#/vpc/sslvpn/list"
                panesData={whitedPanesData}
                onBackList={backList}
                isSwitchReRender={true}
            />
        </>
    );
};
export default SslVpnConnDetail;
