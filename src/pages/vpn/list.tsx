import React, {useEffect, useMemo} from 'react';
import u from 'lodash';
import {useRegion, getQueryParams, cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
import SecurityTabs from '@/components/TabPage';
import {useGlobalContext} from '@/context';
import VpnListComp from '@/pages/sanPages/pages/vpn/components/vpnList';

import './index.less';

const AllRegion = window.$context.getEnum('AllRegion');

const VpnList = () => {
    const urlQuery = getQueryParams() || {};
    const [{globalState}] = useGlobalContext() as any;
    const whiteMap = globalState.commonWhite;
    const onRegionChange = (region: string) => {
        const inRegion = u.contains([AllRegion.HKG, AllRegion.SIN], region);
        const greVpnWhite = !inRegion;
        if (location.hash.startsWith('#/vpc/grevpn/list') && !greVpnWhite) {
            window.location.hash = '#/vpc/vpn/list';
        }
    };
    const {region} = useRegion({onRegionChange});
    const inRegion = u.contains([AllRegion.HKG, AllRegion.SIN], region);

    const panesData = [
        {
            key: '/vpc/vpn/list',
            tab: i18n.t('IPsec VPN网关'),
            content: <VpnListComp context={{vpnType: 'ipsec', vpnId: urlQuery.vpnId}} />
        },
        {
            key: '/vpc/sslvpn/list',
            tab: i18n.t('SSL VPN网关'),
            content: <VpnListComp context={{vpnType: 'ssl', vpnId: urlQuery.vpnId}} />
        },
        {
            key: '/vpc/grevpn/list',
            tab: i18n.t('GRE VPN网关'),
            content: <VpnListComp context={{vpnType: 'gre', vpnId: urlQuery.vpnId}} />
        }
    ];

    const whitedPanesData = useMemo(() => {
        let newPanesData = [...panesData];
        if (inRegion) {
            if (!whiteMap?.HkgSinSslVpnWhite) {
                newPanesData = newPanesData.filter(item => item.key !== '/vpc/sslvpn/list');
            }
        }
        return newPanesData;
    }, [whiteMap]);
    return (
        <div className="vpn-gateway-widget">
            <SecurityTabs isSwitchReRender={false} panesData={whitedPanesData} />
        </div>
    );
};
export default VpnList;
