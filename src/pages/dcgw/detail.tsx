import React, {useState, useEffect, useMemo} from 'react';
import {useRequest} from 'ahooks';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import {getDcgwDetail} from '@/apis';
import {DcGatewayStatus} from '@/pages/sanPages/common';
import {useGlobalContext} from '@/context';
import Detail from '@/pages/sanPages/pages/dcgw/page/detail/detail';
import DcgwNatList from '@/pages/sanPages/pages/dcgw/page/detail/natList';
import DcgwMonitor from '@/pages/sanPages/pages/dcgw/page/detail/monitor';
import HealthCheck from '@/pages/sanPages/pages/dcgw/page/detail/hc';
import DetailPage from '@/components/DetailPage';
import {cbaI18nInstance as i18n,} from '@baidu/bce-react-toolkit';
import './index.less';

const DcgwDetail = () => {
    const params: any = getQueryParams();
    const [{globalState}] = useGlobalContext() as any;
    const [inDdcWhite, setInDdcWhite] = useState<boolean>(true);
    const {data = {} as any, loading, error, run} = useRequest(() => getDcgwDetail({dcgwId: params.dcgwId}));
    const handleUpdate = () => {
        run();
    };
    const panesData = [
        {
            key: '/vpc/dcgw/detail',
            tab: i18n.t('实例信息'),
            content: <Detail context={{...params, getDetail: handleUpdate, instance: data}} />
        },
        {key: '/vpc/dcgw/hc', tab: i18n.t('链路探测'), content: <HealthCheck context={params} />},
        {key: '/vpc/dcgw/nat', tab: i18n.t('云端静态NAT'), content: <DcgwNatList context={params} />},
        {key: '/vpc/dcgw/idcnat', tab: i18n.t('IDC端静态NAT'), content: <DcgwNatList context={params} />},
        {key: '/vpc/dcgw/idcdnat', tab: i18n.t('云端IP端口静态NAT'), content: <DcgwNatList context={params} />},
        {key: '/vpc/dcgw/monitor', tab: i18n.t('监控'), content: <DcgwMonitor context={params} />}
    ];
    const checkWhiteRegion = () => {
        const whiteList = globalState.commonWhite;
        setInDdcWhite(whiteList?.DcgwNatWhitelist);
    };
    useEffect(() => {
        checkWhiteRegion();
    }, []);

    const inDdcWhitePanesData = useMemo(() => {
        const newPanesData = [...panesData];
        if (!inDdcWhite) {
            newPanesData.splice(2, 3);
        }
        return newPanesData;
    }, [inDdcWhite, data]);

    const [text, styleClass] = useMemo(() => {
        let config = [];
        if (data?.status) {
            const {text, styleClass} = DcGatewayStatus.fromValue(data.status);
            config = [text, styleClass];
        }
        return config;
    }, [data]);
    const backList = () => {
        window.location.href = '/network/#/vpc/dcgw/list';
    };
    return (
        <>
            <DetailPage
                mode="vertical"
                headerName={data.name}
                statusText={text}
                statusClassName={styleClass}
                backUrl={'#/vpc/dcgw/list'}
                panesData={inDdcWhitePanesData}
                onBackList={backList}
                tabClassName="dcgw-tab-class"
            />
        </>
    );
};

export default DcgwDetail;
