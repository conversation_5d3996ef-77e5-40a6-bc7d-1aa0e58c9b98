/*
 * @description: 业务补充装饰器
 * @author: <EMAIL>
 */

import _ from 'lodash';
import {decorators, isSanCmpt} from '@baiducloud/runtime';
import * as SanUI from '@baiducloud/bce-ui/san';
import * as sui from '@baidu/sui';
import * as suiBiz from '@baidu/sui-biz';

const {invokeComp, asComponent} = decorators;

const coms = [];
_.each(SanUI, (Com, name) => {
    if (isSanCmpt(Com)) {
        name = '@ui-' + _.kebabCase(name);
        asComponent(name)(Com);
        coms.push(name);
    }
});

let suiComs = [];
_.each(sui, (Com, name) => {
    if (isSanCmpt(Com)) {
        name = '@s-' + _.kebabCase(name);
        asComponent(name)(Com);
        suiComs.push(name);
        let items = Object.keys(Com);
        if (items.length > 0) {
            _.each(items, item => {
                if (isSanCmpt(Com[item])) {
                    let comName = name + '-' + _.kebabCase(item);
                    asComponent(comName)(Com[item]);
                    suiComs.push(comName);
                }
            });
        }
    }
});
let suiBizComs = [];
_.each(suiBiz, (Com, name) => {
    if (isSanCmpt(Com)) {
        name = '@s-' + _.kebabCase(name);
        asComponent(name)(Com);
        suiBizComs.push(name);
        let items = Object.keys(Com);
        if (items.length > 0) {
            _.each(items, item => {
                if (isSanCmpt(Com[item])) {
                    let comName = name + '-' + _.kebabCase(item);
                    asComponent(comName)(Com[item]);
                    suiBizComs.push(comName);
                }
            });
        }
    }
});

/**
 * sui 装饰器
 *
 */
decorators.invokeSUI = invokeComp.apply(this, suiComs);

/**
 * suibiz 装饰器
 *
 */
decorators.invokeSUIBIZ = invokeComp.apply(this, suiBizComs);

/**
 * bce-ui 装饰器
 *
 */
decorators.invokeBceSanUI = invokeComp.apply(this, coms);

/**
 * template 装饰器
 *
 * @param {string} template
 * @return {Function}
 */
decorators.template = template => {
    return Klass => {
        Klass.prototype.template = template;

        return class extends Klass {};
    };
};

decorators.invokeAppComp = invokeComp(
    '@app-create-page',
    '@app-detail-page',
    '@app-list-page',
    '@app-tab',
    '@app-tab-content',
    '@app-tab-page',
    '@app-tab-page-panel',
    '@app-link',
    '@app-legend',
    '@app-row',
    '@app-cell',
    '@app-detail-cell'
);
