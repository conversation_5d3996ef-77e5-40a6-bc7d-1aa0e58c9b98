/**
 * @file vpc/enum
 * <AUTHOR>
 */
import {Enum} from '@baiducloud/runtime';

import {$flag as FLAG} from '@/pages/sanPages/utils/helper';

export let PayType = new Enum(
    {alias: 'PREPAY', text: '预付费', value: 'prepay', desc: '先付费后使用，价格更低廉'},
    {alias: 'POSTPAY', text: '后付费', value: 'postpay', desc: '先使用后付费，按需开通'},
    {alias: 'PACKAGE', text: '合并付款', value: 'package'}
);

export let PriceType = new Enum(
    {alias: 'CPT1', text: 'CPT1', value: 'cpt1'},
    {alias: 'CPT2', text: 'CPT2', value: 'cpt2'},
    {alias: 'CPT3', text: 'CPT3', value: 'cpt3'},
    {alias: 'CPC', text: 'CPC', value: 'cpc'},
    {alias: 'CPT1_CDN_BANDWIDTH', text: 'CPT1_CDN_BANDWIDTH', value: 'cpt1_cdn_bandwidth'}
);

export let Netseg = new Enum(
    {
        alias: 'SEG172',
        text: '172',
        value: '172'
    },
    {
        alias: 'SEG192',
        text: '192',
        value: '192'
    },
    {
        alias: 'SEG10',
        text: '10',
        value: '10'
    }
);

export let Mask = new Enum(
    {
        alias: 'Mask8',
        text: '8',
        value: '8'
    },
    {
        alias: 'Mask9',
        text: '9',
        value: '9'
    },
    {
        alias: 'Mask10',
        text: '10',
        value: '10'
    },
    {
        alias: 'Mask11',
        text: '11',
        value: '11'
    },
    {
        alias: 'Mask12',
        text: '12',
        value: '12'
    },
    {
        alias: 'Mask13',
        text: '13',
        value: '13'
    },
    {
        alias: 'Mask14',
        text: '14',
        value: '14'
    },
    {
        alias: 'Mask15',
        text: '15',
        value: '15'
    },
    {
        alias: 'Mask16',
        text: '16',
        value: '16'
    },
    {
        alias: 'Mask17',
        text: '17',
        value: '17'
    },
    {
        alias: 'Mask18',
        text: '18',
        value: '18'
    },
    {
        alias: 'Mask19',
        text: '19',
        value: '19'
    },
    {
        alias: 'Mask20',
        text: '20',
        value: '20'
    },
    {
        alias: 'Mask21',
        text: '21',
        value: '21'
    },
    {
        alias: 'Mask22',
        text: '22',
        value: '22'
    },
    {
        alias: 'Mask23',
        text: '23',
        value: '23'
    },
    {
        alias: 'Mask24',
        text: '24',
        value: '24'
    },
    {
        alias: 'Mask25',
        text: '25',
        value: '25'
    },
    {
        alias: 'Mask26',
        text: '26',
        value: '26'
    },
    {
        alias: 'Mask27',
        text: '27',
        value: '27'
    },
    {
        alias: 'Mask28',
        text: '28',
        value: '28'
    }
);

export let VpnStatus = new Enum(
    {
        alias: 'BUILDING',
        text: '创建中',
        value: 'building',
        styleClass: 'status warning'
    },
    {
        alias: 'UN_CONFIG',
        text: '未配置',
        value: 'unconfigured',
        styleClass: 'status unavailable'
    },
    {
        alias: 'ACTIVE',
        text: '可用',
        value: 'active',
        styleClass: 'status normal'
    },
    {
        alias: 'DOWN',
        text: '不可用',
        value: 'down',
        styleClass: 'status error'
    },
    {
        alias: 'ERROR',
        text: '状态异常',
        value: 'error',
        styleClass: 'status error'
    },
    {
        alias: 'UPDATING',
        text: '更新中',
        value: 'updating',
        styleClass: 'status warning'
    },
    {
        alias: 'DELETING',
        text: '删除中',
        value: 'deleting',
        styleClass: 'status warning'
    },
    {
        alias: 'DELETED',
        text: '已删除',
        value: 'deleted',
        styleClass: 'status unavailable'
    }
);

export const sdwanStatus = new Enum(
    {alias: 'ONLINE', text: '在线', value: 'online', kclass: 'status normal'},
    {alias: 'OFFLINE', text: '离线', value: 'offline', kclass: 'status unavailable'},
    {alias: 'LOCK', text: '欠费锁定', value: 'lock', kclass: 'status error'},
    {alias: 'PROCESSING', text: '操作中', value: 'processing', kclass: 'status normal'},
    {alias: 'ORDERED', text: '已下单', value: 'ordered', kclass: 'status normal'},
    {alias: 'ACTIVE', text: '已激活', value: 'active', kclass: 'status normal'},
    {alias: 'COMPLETED', text: '已完成', value: 'completed', kclass: 'status normal'}
);

export const ProbeStatus = new Enum({
    alias: 'ACTIVE',
    text: '可用',
    value: 'active',
    styleClass: 'status normal'
});

export const AnalyzeStatus = new Enum(
    {
        alias: 'ANALISED',
        text: '分析完成',
        value: 'ANALISED',
        styleClass: 'status normal'
    },
    {
        alias: 'ANALISING',
        text: '分析中',
        value: 'ANALISING',
        styleClass: 'status rolling'
    },
    {
        alias: 'ANALISE_FAILED',
        text: '分析失败',
        value: 'ANALISE_FAILED',
        styleClass: 'status error'
    }
);

export const ProbeProtocol = new Enum(
    {
        alias: 'ICMP',
        text: 'ICMP',
        value: 'ICMP'
    },
    {
        alias: 'TCP',
        text: 'TCP',
        value: 'TCP'
    },
    {
        alias: 'UDP',
        text: 'UDP',
        value: 'UDP'
    },
    {
        alias: 'DNS',
        text: 'DNS',
        value: 'DNS'
    }
);

export let ProbeSearchType = new Enum(
    {
        alias: 'NAME',
        text: '网络探测名称',
        value: 'name'
    },
    {
        alias: 'ID',
        text: '网络探测ID',
        value: 'probeId'
    }
);

export let PathSearchType = new Enum(
    {
        alias: 'NAME',
        text: '路径名称',
        value: 'name'
    },
    {
        alias: 'ID',
        text: '路径ID',
        value: 'shortId'
    },
    {
        alias: 'SourceRegion',
        text: '源地域',
        value: 'sourceRegion'
    },
    {
        alias: 'SourceVpcId',
        text: '源所在网络',
        value: 'sourceVpcId'
    },
    {
        alias: 'SourceType',
        text: '源实例类型',
        value: 'sourceType'
    },
    {
        alias: 'SourceId',
        text: '源实例ID',
        value: 'sourceId'
    },
    {
        alias: 'DestRegion',
        text: '目的地域',
        value: 'destRegion'
    },
    {
        alias: 'DestVpcId',
        text: '目的所在网络',
        value: 'destVpcId'
    },
    {
        alias: 'DestType',
        text: '目的实例类型',
        value: 'destType'
    },
    {
        alias: 'DestId',
        text: '目的实例ID',
        value: 'destId'
    },
    {
        alias: 'Protocol',
        text: '协议',
        value: 'protocol'
    }
);

export let PathStatus = new Enum({
    alias: 'ACTIVE',
    text: '可用',
    value: 'active',
    styleClass: 'status normal'
});

export let RouteTableStatus = new Enum(
    {
        alias: 'ACTIVE',
        text: '可用',
        value: 'active',
        styleClass: 'status normal'
    },
    {
        alias: 'SNAPSHOT',
        text: '可用',
        value: 'snapshot',
        styleClass: 'status normal'
    },
    {
        alias: 'TEMPLATE',
        text: '可用',
        value: 'template',
        styleClass: 'status normal'
    },
    {
        alias: 'MIGRATING',
        text: '可用',
        value: 'migrating',
        styleClass: 'status normal'
    },
    {
        alias: 'RECHARGE',
        text: '可用',
        value: 'recharge',
        styleClass: 'status normal'
    },
    {
        alias: 'BINDING',
        text: '可用',
        value: 'binding',
        styleClass: 'status normal'
    },
    {
        alias: 'UNBINDING',
        text: '可用',
        value: 'unbinding',
        styleClass: 'status normal'
    },
    {
        alias: 'UNCONFIGURED',
        text: '可用',
        value: 'unconfigured',
        styleClass: 'status normal'
    },
    {
        alias: 'NATACTIVE',
        text: '可用',
        value: 'natactive',
        styleClass: 'status normal'
    }
);

export let RouteStatus = new Enum(
    {
        alias: 'ACTIVE',
        text: '可用',
        value: 'ACTIVE',
        styleClass: 'status normal'
    },
    {
        alias: 'SNAPSHOT',
        text: '可用',
        value: 'SNAPSHOT',
        styleClass: 'status normal'
    },
    {
        alias: 'TEMPLATE',
        text: '可用',
        value: 'TEMPLATE',
        styleClass: 'status normal'
    },
    {
        alias: 'MIGRATING',
        text: '可用',
        value: 'MIGRATING',
        styleClass: 'status normal'
    },
    {
        alias: 'RECHARGE',
        text: '可用',
        value: 'RECHARGE',
        styleClass: 'status normal'
    },
    {
        alias: 'BINDING',
        text: '可用',
        value: 'BINDING',
        styleClass: 'status normal'
    },
    {
        alias: 'UNBINDING',
        text: '可用',
        value: 'UNBINDING',
        styleClass: 'status normal'
    },
    {
        alias: 'UNCONFIGURED',
        text: '可用',
        value: 'unconfigured',
        styleClass: 'status normal'
    },
    {
        alias: 'NATACTIVE',
        text: '可用',
        value: 'active',
        styleClass: 'status normal'
    }
);

export let RouteType = new Enum(
    {
        alias: 'SYSTEM',
        text: '系统',
        value: 'sys'
    },
    {
        alias: 'CUSTOM',
        text: '实例路由',
        value: 'custom'
    },
    {
        alias: 'NAT',
        text: 'NAT网关',
        value: 'nat'
    },
    {
        alias: 'GATEWAY',
        text: '本地网关',
        value: 'defaultGateway'
    },
    {
        alias: 'VPN',
        text: 'VPN网关',
        value: 'vpn'
    },
    {
        alias: 'PEERCONN',
        text: '对等连接',
        value: 'peerConn'
    },
    {
        alias: 'GW',
        text: '专线网关',
        value: 'dcGateway'
    },
    {
        alias: 'IPV6',
        text: 'IPv6公网网关',
        value: 'ipv6gateway'
    },
    {
        alias: 'CSN',
        text: 'CSN路由',
        value: 'CSN'
    },
    {
        alias: 'TGW',
        text: 'TGW路由',
        value: 'vpc2tgw'
    },
    {
        alias: 'ENI',
        text: '弹性网卡',
        value: 'enic'
    },
    {
        alias: 'HAVIP',
        text: '高可用虚拟IP',
        value: 'havip'
    }
);

RouteType.getFiltertype = () => [
    {text: '全部类型', value: ''},
    ...RouteType.toArray('NAT', 'VPN', 'PEERCONN', 'GW', 'CUSTOM', 'GATEWAY', 'TGW', 'ENI', 'HAVIP')
];
RouteType.getIpv6Type = () => [...RouteType.toArray('CUSTOM', 'IPV6', 'PEERCONN')];
RouteType.getIpv4Type = () => [...RouteType.toArray('CUSTOM', 'NAT', 'VPN', 'PEERCONN', 'GW', 'GATEWAY', 'TGW')];

export const ChannelRouteType = new Enum(
    {
        alias: '',
        text: '全部条目',
        value: ''
    },
    {
        alias: 'VPC',
        text: '专线网关',
        value: 'vpc'
    },
    {
        alias: 'CHANNEL',
        text: '专线通道',
        value: 'channel'
    },
    {
        alias: 'CSN',
        text: '云智能网',
        value: 'csn'
    }
);

export const ChannelRouteProtocol = new Enum(
    {
        alias: '',
        text: '全部条目',
        value: ''
    },
    {
        alias: 'STATIC-ROUTE',
        text: '静态路由',
        value: 'static-route'
    },
    {
        alias: 'BGP',
        text: 'bgp路由',
        value: 'bgp'
    }
);

export const ChannelRouteBgpStatus = new Enum(
    {alias: 'UNESTABLISHED', text: 'UnEstablished', value: 'down', kclass: 'status error'},
    {alias: 'ESTABLISHED', text: 'Established', value: 'up', kclass: 'status normal'}
);

export let RouteSourceAll = new Enum({
    alias: 'ALL',
    text: 'all',
    value: '0.0.0.0/0',
    cidr: '0.0.0.0/0'
});

export let ServiceType = new Enum(
    {
        alias: 'BCC',
        text: '通用型',
        value: 1
    },
    {
        alias: 'BBC',
        text: 'BBC专属型',
        value: 2
    },
    {
        alias: 'NAT',
        text: 'NAT专属型',
        value: 3
    },
    {
        alias: 'BBC_NAT',
        text: 'BBC NAT专属型',
        value: 4
    }
);

export let NatFlavor = new Enum(
    {
        alias: 'L',
        text: '小',
        value: 'little',
        desc: '小型NAT网关，最多支持绑定5个公网IP，最大连接数约3万，最大转发能力1Gbps'
    },
    {
        alias: 'M',
        text: '中',
        value: 'medium',
        desc: '中型NAT网关，最多支持绑定10个公网IP，最大连接数约45万，最大转发能力2Gbps'
    },
    {
        alias: 'G',
        text: '大',
        value: 'large',
        desc: '大型NAT网关，最多支持绑定15个公网IP，最大连接数约100万，最大转发能力5Gbps'
    }
);

export const NatClusterMode = new Enum({
    alias: false,
    text: '普通型',
    value: false
});

export const NAT_ENHANCED_MAPPING = new Enum(
    {
        alias: 'IPv4',
        text: '增强型-IPv4',
        value: 'v4'
    },
    {
        alias: 'IPv6',
        text: '增强型-IPv6',
        value: 'v6'
    }
);

export let NatStatus = new Enum(
    {
        alias: 'ACTIVE',
        text: '运行中',
        value: 'active',
        styleClass: 'status normal'
    },
    {
        alias: 'UPDATING',
        text: '更新中',
        value: 'updating',
        styleClass: 'status warning'
    },
    {
        alias: 'UNCONFIGURED',
        text: '未配置',
        value: 'unconfigured',
        styleClass: 'status unavailable'
    },
    {
        alias: 'DOWN',
        text: '不可用',
        value: 'down',
        styleClass: 'status error'
    },
    {
        alias: 'BUILDING',
        text: '创建中',
        value: 'building',
        styleClass: 'status warning'
    },
    {
        alias: 'ERROR',
        text: '状态异常',
        value: 'error',
        styleClass: 'status error'
    },
    {
        alias: 'DELETING',
        text: '删除中',
        value: 'deleting',
        styleClass: 'status warning'
    },
    {
        alias: 'DELETED',
        text: '已删除',
        value: 'deleted',
        styleClass: 'status unavailable'
    },
    {
        alias: 'STARTING',
        text: '启动中',
        value: 'starting',
        styleClass: 'status warning'
    },
    {
        alias: 'CONFIGURING',
        text: '配置中',
        value: 'configuring',
        styleClass: 'status warning'
    },
    {
        alias: 'REBOOTING',
        text: '重启中',
        value: 'rebooting',
        styleClass: 'status warning'
    },
    {
        alias: 'STOPPING',
        text: '停止中',
        value: 'stopping',
        styleClass: 'status warning'
    }
);

export let NatIp = new Enum(
    {
        alias: 'AUTO',
        text: '自动分配',
        value: 'auto'
    },
    {
        alias: 'CUSTOM',
        text: '指定',
        value: 'custom'
    }
);

export let NatType = new Enum(
    {
        alias: 'SNAT',
        text: 'SNAT',
        value: 'snat'
    },
    {
        alias: 'DNAT',
        text: 'DNAT',
        value: 'dnat'
    }
);

export let EipType = new Enum(
    {
        alias: 'NORMAL',
        text: '弹性公网IP',
        value: 'normal'
    },
    {
        alias: 'GROUP',
        text: '共享带宽',
        value: 'group'
    }
);

export let OptType = new Enum(
    {
        alias: 'BIND',
        text: '绑定公网IP',
        value: 'BIND'
    },
    {
        alias: 'UNBIND',
        text: '解绑',
        value: 'UNBIND'
    },
    {
        alias: 'UPGRADE',
        text: '带宽升级',
        value: 'UPGRADE'
    },
    {
        alias: 'RELEASE',
        text: '释放',
        value: 'RELEASE'
    },
    {
        alias: 'ALTER_PRODUCTTYPE',
        text: '计费变更',
        value: 'ALTER_PRODUCTTYPE'
    },
    {
        alias: 'CANCEL_ALTER_PRODUCTTYPE',
        text: '取消计费变更',
        value: 'CANCEL_ALTER_PRODUCTTYPE'
    },
    {
        alias: 'OPEN_DNS_SYNC',
        text: '开启DNS同步',
        value: 'OPEN_DNS_SYNC'
    },
    {
        alias: 'CLOSE_DNS_SYNC',
        text: '关闭DNS同步',
        value: 'CLOSE_DNS_SYNC'
    },
    {
        alias: 'EDIT_TAG',
        text: '编辑标签',
        value: 'EDIT_TAG'
    },
    {
        alias: 'NATUPGRADE',
        text: '网关升级',
        value: 'NATUPGRADE'
    },
    {
        alias: 'EDIT_RES',
        text: '编辑资源分组',
        value: 'EDIT_RES'
    }
);

export let PeerConnStatus = new Enum(
    {
        alias: 'CREATING',
        text: '创建中',
        value: 'creating',
        styleClass: 'status warning'
    },
    {
        alias: 'CONSULTING',
        text: '协商中',
        value: 'consulting',
        styleClass: 'status warning'
    },
    {
        alias: 'CONSULT_FAILED',
        text: '协商失败',
        value: 'consult_failed',
        styleClass: 'status warning'
    },
    {
        alias: 'ACTIVE',
        text: '可用',
        value: 'active',
        styleClass: 'status normal'
    },
    {
        alias: 'DOWN',
        text: '不可用',
        value: 'down',
        styleClass: 'status unavailable'
    },
    {
        alias: 'STARTING',
        text: '启动中',
        value: 'starting',
        styleClass: 'status warning'
    },
    {
        alias: 'STOPPING',
        text: '停止中',
        value: 'stopping',
        styleClass: 'status warning'
    },
    {
        alias: 'DELETING',
        text: '删除中',
        value: 'deleting',
        styleClass: 'status warning'
    },
    {
        alias: 'DELETED',
        text: '已删除',
        value: 'deleted',
        styleClass: 'status unavailable'
    },
    {
        alias: 'EXPIRED',
        text: '已到期',
        value: 'expired',
        styleClass: 'status error'
    },
    {
        alias: 'ERROR',
        text: '异常',
        value: 'error',
        styleClass: 'status error'
    },
    {
        alias: 'UPDATING',
        text: '更新中',
        value: 'updating',
        styleClass: 'status warning'
    },
    {
        alias: 'AUDITING',
        text: '审核中',
        value: 'auditing',
        styleClass: 'status error'
    },
    {
        alias: 'AUDIT_FAILED',
        text: '审核失败',
        value: 'audit_failed',
        styleClass: 'status error'
    },
    {
        alias: 'AUDITOR_PAUSE',
        text: '暂停',
        value: 'auditor_pause',
        styleClass: 'status unavailable'
    },
    {
        alias: 'AUDITOR_PAUSE_DOWN',
        text: '暂停到期',
        value: 'auditor_pause_down',
        styleClass: 'status warning'
    },
    {
        alias: 'STOPED',
        text: '已欠费',
        value: 'stoped',
        styleClass: 'status error'
    }
);

export let PeerConnType = new Enum(
    {
        alias: 'SAME',
        text: '本帐号',
        value: 'same'
    },
    {
        alias: 'DIFF',
        text: '跨帐号',
        value: 'diff'
    }
);

export let PeerConnRole = new Enum(
    {
        alias: 'INITIATOR',
        text: '发起端',
        value: 'initiator'
    },
    {
        alias: 'ACCEPTOR',
        text: '接受端',
        value: 'acceptor'
    }
);

export let DirectionType = new Enum(
    {
        alias: 'INGRESS',
        text: '入站',
        value: 'ingress'
    },
    {
        alias: 'EGRESS',
        text: '出站',
        value: 'egress'
    }
);

export let ActionType = new Enum(
    {
        alias: 'ALLOW',
        text: '允许',
        value: 'allow'
    },
    {
        alias: 'DENY',
        text: '拒绝',
        value: 'deny'
    }
);

export let filterActionType = new Enum(
    {
        alias: 'ALLOW',
        text: '采集',
        value: 'allow'
    },
    {
        alias: 'DENY',
        text: '不采集',
        value: 'deny'
    }
);

// let kAllPorts = '1-65535';
let kAllProtocol = 'all';
let kTCPProtocol = 'tcp';
let kUDPProtocol = 'udp';
let kICMPPrototol = 'icmp';

export let ProtocolType = new Enum(
    {
        alias: 'TCP',
        text: 'TCP',
        value: kTCPProtocol,
        rules: [
            {
                protocol: kTCPProtocol,
                port: ''
            }
        ],
        desc: ''
    },
    {
        alias: 'UDP',
        text: 'UDP',
        value: kUDPProtocol,
        rules: [
            {
                protocol: kUDPProtocol,
                port: ''
            }
        ],
        desc: ''
    },
    {
        alias: 'ICMP',
        text: 'ICMP',
        value: kICMPPrototol,
        rules: [
            {
                protocol: kICMPPrototol,
                port: ''
            }
        ],
        desc: ''
    },
    {
        alias: 'ALL',
        text: '全部协议',
        value: kAllProtocol,
        rules: [
            {
                protocol: kAllProtocol,
                port: kAllProtocol
            }
        ],
        desc: '全部协议'
    },
    {
        alias: 'PING',
        text: 'PING（ICMP）',
        value: 'ping',
        rules: [
            {
                protocol: kICMPPrototol,
                port: ''
            }
        ],
        desc: 'PING请求'
    },
    {
        alias: 'SSH',
        text: 'SSH（TCP）',
        value: 'ssh',
        rules: [
            {
                protocol: kTCPProtocol,
                port: '22'
            }
        ],
        desc: 'SSH远程终端'
    },
    {
        alias: 'HTTP',
        text: 'HTTP（TCP）',
        value: 'http',
        rules: [
            {
                protocol: kTCPProtocol,
                port: '80'
            }
        ],
        desc: 'HTTP服务'
    },
    {
        alias: 'HTTPS',
        text: 'HTTPS（TCP）',
        value: 'https',
        rules: [
            {
                protocol: kTCPProtocol,
                port: '443'
            }
        ],
        desc: 'HTTPS服务'
    },
    {
        alias: 'FTP',
        text: 'FTP（TCP）',
        value: 'ftp',
        rules: [
            {
                protocol: kTCPProtocol,
                port: '21'
            },
            {
                protocol: kTCPProtocol,
                port: '20'
            }
        ],
        desc: 'FTP文件传输'
    },
    {
        alias: 'RDP',
        text: 'RDP（TCP）',
        value: 'rdp',
        rules: [
            {
                protocol: kTCPProtocol,
                port: '3389'
            }
        ],
        desc: 'RDP远程桌面'
    },
    {
        alias: 'DNS',
        text: 'DNS（TCP）',
        value: 'dns',
        rules: [
            {
                protocol: kTCPProtocol,
                port: '53'
            },
            {
                protocol: kUDPProtocol,
                port: '53'
            }
        ],
        desc: 'DNS服务'
    },
    {
        alias: 'POP3',
        text: 'POP3（TCP）',
        value: 'pop3',
        rules: [
            {
                protocol: kTCPProtocol,
                port: '110'
            }
        ],
        desc: 'POP3邮件接收服务'
    },
    {
        alias: 'MYSQL',
        text: 'MYSQL（TCP）',
        value: 'mysql',
        rules: [
            {
                protocol: kTCPProtocol,
                port: '3306'
            }
        ],
        desc: 'MySQL数据库'
    },
    {
        alias: 'SQLServer',
        text: 'SQL Server（TCP）',
        value: 'sqlserver',
        rules: [
            {
                protocol: kTCPProtocol,
                port: '1433'
            }
        ],
        desc: 'SQL Server数据库'
    },
    {
        alias: 'SNMP',
        text: 'SNMP（UDP）',
        value: 'snmp',
        rules: [
            {
                protocol: kUDPProtocol,
                port: '161'
            },
            {
                protocol: kTCPProtocol,
                port: '161'
            }
        ],
        desc: 'SNMP'
    },
    {
        alias: 'SMTP',
        text: 'SMTP（TCP）',
        value: 'smtp',
        rules: [
            {
                protocol: kTCPProtocol,
                port: '25'
            }
        ],
        desc: 'SMTP邮件发送服务'
    },
    {
        alias: 'SNMPTrap',
        text: 'SNMP Trap（UDP）',
        value: 'snmptrap',
        rules: [
            {
                protocol: kUDPProtocol,
                port: '162'
            },
            {
                protocol: kTCPProtocol,
                port: '162'
            }
        ],
        desc: 'SNMP Trap'
    }
);

export let filterProtocolType = new Enum(
    {
        alias: 'ALL',
        text: '全部协议',
        value: kAllProtocol,
        rules: [
            {
                protocol: kAllProtocol,
                port: kAllProtocol
            }
        ],
        desc: '全部协议'
    },
    {
        alias: 'TCP',
        text: 'TCP',
        value: kTCPProtocol,
        rules: [
            {
                protocol: kTCPProtocol,
                port: ''
            }
        ],
        desc: ''
    },
    {
        alias: 'UDP',
        text: 'UDP',
        value: kUDPProtocol,
        rules: [
            {
                protocol: kUDPProtocol,
                port: ''
            }
        ],
        desc: ''
    },
    {
        alias: 'ICMP',
        text: 'ICMP',
        value: kICMPPrototol,
        rules: [
            {
                protocol: kICMPPrototol,
                port: ''
            }
        ],
        desc: ''
    }
);

export let DcGatewayStatus = new Enum(
    {
        alias: 'RUNNING',
        text: '可用',
        value: 'running',
        styleClass: 'status normal'
    },
    {
        alias: 'UNBOUNDED',
        text: '未绑定',
        value: 'unbounded',
        styleClass: 'status warning'
    },
    {
        alias: 'BINDING',
        text: '绑定中',
        value: 'binding',
        styleClass: 'status rolling'
    },
    {
        alias: 'UNBINDING',
        text: '解绑中',
        value: 'unbinding',
        styleClass: 'status rolling'
    },
    {
        alias: 'INAVAILABLE',
        text: '不可用',
        value: 'inavailable',
        styleClass: 'status unavailable'
    }
);

export let healthCheckStatus = new Enum(
    {
        alias: 'AVAILABLE',
        text: '正常',
        value: 'available',
        styleClass: 'status normal'
    },
    {
        alias: 'AVAILABLE_PAUSED',
        text: '正常',
        value: 'available_paused',
        styleClass: 'status normal'
    },
    {
        alias: 'INAVAILABLE_PAUSED',
        text: '异常',
        value: 'inavailable_paused',
        styleClass: 'status error'
    },
    {
        alias: 'INAVAILABLE',
        text: '异常',
        value: 'inavailable',
        styleClass: 'status error'
    },
    {
        alias: 'INIT',
        text: '初始验证中',
        value: 'init',
        styleClass: 'status rolling'
    },
    {
        alias: 'INIT_FAILED',
        text: '初始验证失败',
        value: 'init_failed',
        styleClass: 'status error'
    }
);

export let SubnetsType = new Enum(
    {
        alias: 'VPC',
        text: '本VPC网段',
        value: 'vpc'
    },
    {
        alias: 'CUSTOM',
        text: '自定义',
        value: 'custom'
    }
);

export let DcphyIdType = new Enum(
    {
        alias: 'BIND',
        text: '绑定物理专线',
        value: 'bind'
    },
    {
        alias: 'UNBIND',
        text: '暂不绑定',
        value: 'unbind'
    }
);

export let TimeType = new Enum(
    {alias: 'MONTH', text: '按月', value: 'month'},
    {alias: 'YEAR', text: '按年', value: 'year'}
);

export let Month = new Enum(
    {alias: 'ONE', text: '1个月', value: 1},
    {alias: 'TWO', text: '2个月', value: 2},
    {alias: 'THREE', text: '3个月', value: 3},
    {alias: 'FOUR', text: '4个月', value: 4},
    {alias: 'FIVE', text: '5个月', value: 5},
    {alias: 'SIX', text: '6个月', value: 6},
    {alias: 'SEVEN', text: '7个月', value: 7},
    {alias: 'EIGHT', text: '8个月', value: 8},
    {alias: 'NINE', text: '9个月', value: 9}
);

export let Year = new Enum(
    {alias: 'ONE', text: '1年', value: 1},
    {alias: 'TWO', text: '2年', value: 2},
    {alias: 'THREE', text: '3年', value: 3}
);

export let DNSSyncStatus = new Enum(
    {
        alias: 'OPEN',
        text: '已开启',
        value: 'open'
    },
    {
        alias: 'CLOSE',
        text: '未开启',
        value: 'close'
    },
    {
        alias: 'CLOSING',
        text: '关闭中',
        value: 'closing'
    },
    {
        alias: 'WAIT',
        text: '待同步',
        value: 'wait'
    },
    {
        alias: 'SYNCING',
        text: '同步中',
        value: 'syncing'
    }
);

export let Ipv6Status = new Enum(
    {
        alias: 'AVAILABLE',
        text: '可用',
        value: 'available',
        styleClass: 'status normal'
    },
    {
        alias: 'ERROR',
        text: '异常',
        value: 'error',
        styleClass: 'status error'
    },
    {
        alias: 'STOPPED',
        text: '欠费',
        value: 'stopped',
        styleClass: 'status error'
    }
);

export let Ipv6SubProductType = new Enum(
    {
        alias: 'BANDWIDTH',
        text: '按带宽',
        value: 'bandwidth'
    },
    {
        alias: 'NETRAFFIC',
        text: '按流量',
        value: 'netraffic'
    }
);

export let Ipv6Payment = new Enum(
    {
        alias: 'BANDWIDTH',
        text: '按使用带宽计费',
        value: 'bandwidth'
    },
    {
        alias: 'NETRAFFIC',
        text: '按使用流量计费',
        value: 'netraffic'
    }
);

export let IpVersion = new Enum(
    {
        alias: 'ALL',
        text: '全部网段',
        value: 'all'
    },
    {
        alias: 'IPV4',
        text: 'IPv4',
        value: 4
    },
    {
        alias: 'IPV6',
        text: 'IPv6',
        value: 6
    }
);

export let IpTypeVersion = new Enum(
    {
        alias: 'ALL',
        text: '全部网段',
        value: 'all'
    },
    {
        alias: 'IPV4',
        text: 'IPv4',
        value: '4'
    },
    {
        alias: 'IPV6',
        text: 'IPv6',
        value: '6'
    }
);

export let VpnConnStatus = new Enum(
    {
        alias: 'REACHABLE',
        text: '已连通',
        value: 'reachable',
        styleClass: 'status normal'
    },
    {
        alias: 'UNREACHABLE',
        text: '未连通',
        value: 'unreachable',
        styleClass: 'status unavailable'
    },
    {
        alias: 'UNKNOWN',
        text: '创建中',
        value: 'unknown',
        styleClass: 'status warning'
    }
);

export let l2gwConnStatus = new Enum(
    {
        alias: 'REACHABLE',
        text: '已连通',
        value: 'reachable',
        styleClass: 'status normal'
    },
    {
        alias: 'UNREACHABLE',
        text: '未连通',
        value: 'unreachable',
        styleClass: 'status unavailable'
    }
);

export let ipsecAllSaActive = new Enum(
    {
        alias: true,
        text: '正常',
        value: true,
        styleClass: 'status normal'
    },
    {
        alias: false,
        text: '异常',
        value: false,
        styleClass: 'status error'
    }
);

export let bgpVpnStatus = new Enum(
    {
        alias: 'ESTABLISHED',
        text: '已连接',
        value: 'ESTABLISHED',
        styleClass: 'status normal'
    },
    {
        alias: 'ACTIVE',
        text: '未连接',
        value: 'ACTIVE',
        styleClass: 'status error'
    }
);

export let GreHealthStatus = new Enum(
    {
        alias: 'REACHABLE',
        text: '正常',
        value: 'reachable',
        styleClass: 'status normal'
    },
    {
        alias: 'UNREACHABLE',
        text: '异常',
        value: 'unreachable',
        styleClass: 'status error'
    }
);

export const SslConnStatus = new Enum(
    {
        alias: 'ACTIVE',
        text: '可用',
        value: 'active',
        styleClass: 'status normal'
    },
    {
        alias: 'DOWN',
        text: '未启动',
        value: 'down',
        styleClass: 'status unavailable'
    }
);

export let SecurityIpVersion = new Enum(
    {
        alias: 'IPV4',
        text: 'IPv4',
        value: 'IPv4'
    },
    {
        alias: 'IPV6',
        text: 'IPv6',
        value: 'IPv6'
    }
);

export let EndpointStatus = new Enum(
    {
        alias: 'AVAILABLE',
        text: '可用',
        value: 'available',
        styleClass: 'status normal'
    },
    {
        alias: 'UNAVAILABLE',
        text: '不可用',
        value: 'unavailable',
        styleClass: 'status unavailable'
    },
    {
        alias: 'PAUSED',
        text: '暂停',
        value: 'paused',
        styleClass: 'status warning'
    }
);

export let VpnConnLocalCidr = new Enum({
    alias: 'CUSTOM',
    text: '自定义配置',
    value: 'custom'
});

export let HealthCheckAction = new Enum(
    {
        alias: 'OPEN',
        text: '开启',
        value: 'open'
    },
    {
        alias: 'CLOSE',
        text: '关闭',
        value: 'close'
    },
    {
        alias: 'PAUSE',
        text: '暂停',
        value: 'pause'
    }
);

export let PathType = new Enum(
    {
        alias: 'ECMP',
        text: '负载均衡',
        value: 'ecmp'
    },
    {
        alias: 'ACTIVE',
        text: '主备-主',
        value: 'ha:active'
    },
    {
        alias: 'STANDBY',
        text: '主备-备',
        value: 'ha:standby'
    },
    {
        alias: 'NORMAL',
        text: '普通路由',
        value: 'normal'
    }
);

export let GwRouteType = new Enum(
    {
        alias: 'SINGLE',
        text: '单线路由',
        value: 'single'
    },
    {
        alias: 'MULTI',
        text: '多线路由',
        value: 'multi'
    }
);

export let GwRouteMultiMode = new Enum(
    {
        alias: 'HA',
        text: '主备',
        value: 'ha'
    },
    {
        alias: 'ECMP',
        text: '负载均衡',
        value: 'ecmp'
    }
);

export let EniStatus = new Enum(
    {
        alias: 'AVAILABLE',
        text: '可用',
        value: 'available',
        styleClass: 'status normal'
    },
    {
        alias: 'INUSE',
        text: '已绑定',
        value: 'inuse',
        styleClass: 'status normal'
    },
    {
        alias: 'ATTACHING',
        text: '绑定中',
        value: 'attaching',
        styleClass: 'status warning'
    },
    {
        alias: 'DETACHING',
        text: '解绑中',
        value: 'detaching',
        styleClass: 'status warning'
    }
);

export let HaVipStatus = new Enum(
    {
        alias: 'AVAILABLE',
        text: '可用',
        value: 'available',
        styleClass: 'status normal'
    },
    {
        alias: 'BINDED',
        text: '已绑定',
        value: 'binded',
        styleClass: 'status normal'
    }
);

export let DnatStatus = new Enum(
    {
        alias: 'CONFIGURING',
        text: '配置中',
        value: 'configuring',
        styleClass: 'status warning'
    },
    {
        alias: 'ACTIVE',
        text: '可用',
        value: 'active',
        styleClass: 'status normal'
    }
);

export let SnatStatus = new Enum(
    {
        alias: 'CONFIGURING',
        text: '配置中',
        value: 'configuring',
        styleClass: 'status warning'
    },
    {
        alias: 'ACTIVE',
        text: '可用',
        value: 'active',
        styleClass: 'status normal'
    }
);

export let DnatProtocol = new Enum(
    {
        alias: 'ALL',
        text: '全部协议',
        value: 'all'
    },
    {
        alias: 'TCP',
        text: 'TCP',
        value: 'TCP'
    },
    {
        alias: 'UDP',
        text: 'UDP',
        value: 'UDP'
    }
);

export let ResourceType = new Enum(
    {
        alias: 'VPC',
        text: '私有网络',
        value: 'vpc',
        hidden: 'FlowlogNeutronWhiteList'
    },
    {
        alias: 'SUBNET',
        text: '子网',
        value: 'subnet',
        hidden: 'FlowlogNeutronWhiteList'
    },
    {
        alias: 'DEVICE',
        text: '云服务器',
        value: 'device',
        hidden: 'FlowlogNeutronWhiteList'
    },
    {
        alias: 'PORT',
        text: '网卡',
        value: 'port',
        hidden: 'FlowlogNeutronWhiteList'
    },
    {
        alias: 'EIP',
        text: 'EIP实例',
        value: 'eip'
    },
    {
        alias: 'BLB',
        text: '负载均衡',
        value: 'blb'
    },
    {
        alias: 'NAT',
        text: 'NAT网关',
        value: 'nat'
    },
    {
        alias: 'IPV6',
        text: 'IPv6网关',
        value: 'ipv6gw'
    },
    {
        alias: 'PEERCONN',
        text: '对等连接',
        value: 'peerconn'
    },
    {
        alias: 'ET',
        text: '专线网关',
        value: 'et'
    },
    {
        alias: 'CSN',
        text: '云智能网',
        value: 'csnvpc'
    }
);

export let ResourceTypeRegionList = new Enum(
    {
        alias: 'VPC',
        text: '私有网络',
        value: 'vpc',
        hidden: 'FlowlogNeutronWhiteList'
    },
    {
        alias: 'SUBNET',
        text: '子网',
        value: 'subnet',
        hidden: 'FlowlogNeutronWhiteList'
    },
    {
        alias: 'DEVICE',
        text: '云服务器',
        value: 'device',
        hidden: 'FlowlogNeutronWhiteList'
    },
    {
        alias: 'PORT',
        text: '网卡',
        value: 'port',
        hidden: 'FlowlogNeutronWhiteList'
    },
    {
        alias: 'EIP',
        text: 'EIP实例',
        value: 'eip'
    },
    {
        alias: 'BLB',
        text: '负载均衡-四层',
        value: 'blb'
    },
    {
        alias: 'BLBSEVEN',
        text: '负载均衡-七层',
        value: 'blbSeven'
    },
    {
        alias: 'NAT',
        text: 'NAT网关',
        value: 'nat'
    },
    {
        alias: 'IPV6',
        text: 'IPv6网关',
        value: 'ipv6gw'
    },
    {
        alias: 'PEERCONN',
        text: '对等连接',
        value: 'peerconn'
    },
    {
        alias: 'ET',
        text: '专线网关',
        value: 'et'
    },
    {
        alias: 'CSN',
        text: '云智能网',
        value: 'csnvpc'
    }
);

export let FlowlogSearchType = new Enum(
    {
        alias: 'NAME',
        text: '实例名称',
        value: 'flowlogName'
    },
    {
        alias: 'ID',
        text: '实例ID',
        value: 'flowlogId'
    }
);

export let Action = new Enum(
    {
        alias: 'ALL',
        text: '全部流量',
        value: 'ALL'
    },
    {
        alias: 'ACCEPT',
        text: '允许',
        value: 'ACCEPT'
    },
    {
        alias: 'REJECT',
        text: '拒绝',
        value: 'REJECT'
    }
);

export let SnatProtocol = new Enum(
    {
        alias: 'ALL',
        text: '全部协议',
        value: 'all'
    },
    {
        alias: 'TCP',
        text: 'TCP',
        value: 'TCP'
    },
    {
        alias: 'UDP',
        text: 'UDP',
        value: 'UDP'
    }
);

export const FlowLogStatus = new Enum(
    {
        alias: 'ENABLE',
        text: '启用',
        styleClass: 'status normal',
        value: true
    },
    {
        alias: 'DISABLE',
        text: '暂停',
        styleClass: 'status warning',
        value: false
    }
);

export const flowlogCollectType = new Enum(
    {alias: 'INSTANCE', text: '网络实例', value: 'instance'},
    {alias: 'TGWPEER', text: '跨地域连接', value: 'tgwPeer'}
);

export const flowlogInstanceType = new Enum(
    {alias: 'VPC', text: '私有网络VPC', value: 'vpc'},
    {alias: 'ET', text: '专线通道', value: 'et'}
);

export const flowlogInterworkType = new Enum(
    {alias: 'PEER_CLOUD', text: '云间互通', value: 'PEER_CLOUD'},
    {alias: 'PEER_EDGE', text: '云边互通', value: 'PEER_EDGE'}
);

export const vpnFlavor = new Enum(
    {
        alias: 'NORMAL',
        text: '普通型',
        value: '',
        desc: '最大转发能力 200Mbps'
    },
    {
        alias: 'HIGH',
        text: '增强型',
        value: 'high_performance',
        desc: '最大转发能力 1000Mbps'
    }
);

export const l2gwFlavor = new Enum(
    {
        alias: 'NORMAL',
        text: '普通型',
        value: 'normal'
    },
    {
        alias: 'HIGH',
        text: '增强型',
        value: 'high_performance'
    }
);

export const vpnType = new Enum(
    {
        alias: 'ssl-vpn',
        text: 'SSL VPN',
        value: 'ssl'
    },
    {
        alias: 'ipsec-vpn',
        text: 'IPsec VPN',
        value: 'ipsec'
    },
    {
        alias: 'gre-vpn',
        text: 'GRE VPN',
        value: 'gre'
    }
);

export const ikeConfigVersion = new Enum(
    {
        alias: 'V1',
        text: 'ikev1',
        value: 'v1'
    },
    {
        alias: 'V2',
        text: 'ikev2',
        value: 'v2'
    }
);

export const ikeConfigMode = new Enum(
    {
        alias: 'MAIN',
        text: 'main',
        value: 'main'
    },
    {
        alias: 'AGGRESSIVE',
        text: 'aggressive',
        value: 'aggressive'
    }
);

export const ikeConfigEncAlg = new Enum(
    {
        alias: 'AES',
        text: 'aes',
        value: 'aes'
    },
    {
        alias: 'AES192',
        text: 'aes192',
        value: 'aes192'
    },
    {
        alias: 'AES256',
        text: 'aes256',
        value: 'aes256'
    },
    {
        alias: '3DES',
        text: '3des',
        value: '3des'
    }
);

export const ikeConfigAuthAlg = new Enum(
    {
        alias: 'SHA1',
        text: 'sha1',
        value: 'sha1'
    },
    {
        alias: 'MD5',
        text: 'md5',
        value: 'md5'
    },
    {
        alias: 'SHA2_256',
        name: 'sha2_256',
        value: 'sha2_256'
    },
    {
        alias: 'SHA2_384',
        name: 'sha2_384',
        value: 'sha2_384'
    },
    {
        alias: 'SHA2_512',
        name: 'sha2_512',
        value: 'sha2_512'
    }
);

export const ikeConfigRemoteType = new Enum(
    {
        text: 'IP Address',
        value: 'IP_ADDR',
        alias: 'IP_ADDR'
    },
    {
        text: 'FQDN',
        value: 'FQDN',
        alias: 'FQDN'
    }
);

export const ikeConfigPfs = new Enum(
    {
        alias: 'GROUP2',
        text: 'group2',
        value: 'group2'
    },
    {
        alias: 'GROUP5',
        text: 'group5',
        value: 'group5'
    },
    {
        alias: 'GROUP14',
        text: 'group14',
        value: 'group14'
    },
    {
        alias: 'GROUP24',
        text: 'group24',
        value: 'group24'
    }
);

export const ipsecConfigEncAlg = new Enum(
    {
        alias: 'AES',
        text: 'aes',
        value: 'aes'
    },
    {
        alias: 'AES192',
        text: 'aes192',
        value: 'aes192'
    },
    {
        alias: 'AES256',
        text: 'aes256',
        value: 'aes256'
    },
    {
        alias: '3DES',
        text: '3des',
        value: '3des'
    }
);

export const ipsecConfigAuthAlg = new Enum(
    {
        alias: 'SHA1',
        text: 'sha1',
        value: 'sha1'
    },
    {
        alias: 'MD5',
        text: 'md5',
        value: 'md5'
    },
    {
        alias: 'SHA2_256',
        name: 'sha2_256',
        value: 'sha2_256'
    },
    {
        alias: 'SHA2_384',
        name: 'sha2_384',
        value: 'sha2_384'
    },
    {
        alias: 'SHA2_512',
        name: 'sha2_512',
        value: 'sha2_512'
    }
);

export const ipsecConfigPfs = new Enum(
    {
        alias: 'GROUP2',
        text: 'group2',
        value: 'group2'
    },
    {
        alias: 'GROUP5',
        text: 'group5',
        value: 'group5'
    },
    {
        alias: 'GROUP14',
        text: 'group14',
        value: 'group14'
    },
    {
        alias: 'GROUP24',
        text: 'group24',
        value: 'group24'
    },
    {
        alias: 'DISABLED',
        text: 'disabled',
        value: 'disabled'
    }
);

export let InstanceStatus = new Enum(
    {alias: 'APPLYING', text: '申请中', value: 'ack-wait', kclass: 'status rolling', errorStatusTip: '申请中'},
    {alias: 'ACCEPTED', text: '申请已受理', value: 'accept', kclass: 'status normal', errorStatusTip: '受理中'},
    {alias: 'PAY_WAIT', text: '未支付', value: 'pay-wait', kclass: 'status warning', errorStatusTip: '未支付'},
    {alias: 'REJECTED', text: '审核已拒绝', value: 'reject', kclass: 'status error', errorStatusTip: '审核已拒绝'},
    {alias: 'BUILDING', text: '建设中', value: 'building', kclass: 'status rolling', errorStatusTip: '建设中'},
    {alias: 'ACTIVE', text: '可用', value: 'established', kclass: 'status normal', errorStatusTip: ''},
    {alias: 'DELETED', text: '到期删除中', value: 'deleted', kclass: 'status error', errorStatusTip: '到期删除中'},
    {alias: 'STOPPED', text: '已到期', value: 'stopped', kclass: 'status error', errorStatusTip: '已到期'},
    {alias: 'PENDING', text: '待支付', value: 'pending', kclass: 'status warning', errorStatusTip: '待支付'},
    {
        alias: 'USER_REJECT',
        text: '用户拒绝支付',
        value: 'user-reject',
        kclass: 'status error',
        errorStatusTip: '用户拒绝支付'
    }
);
InstanceStatus.getChannelStatus = () => [
    {text: '全部状态', value: ''},
    {alias: 'STOPPED', text: '用户欠费', value: 'stopped', kclass: 'status error', errorStatusTip: '用户欠费'},
    {
        alias: 'VLAN-CONFLICT',
        text: 'VLAN冲突',
        value: 'vlan-conflict',
        kclass: 'status error',
        errorStatusTip: 'VLAN冲突'
    },
    ...InstanceStatus.toArray('ACTIVE', 'APPLYING', 'BUILDING', 'REJECTED', 'PENDING', 'USER_REJECT')
];
InstanceStatus.getFilterStatus = () => [
    {text: '全部状态', value: ''},
    ...InstanceStatus.toArray('APPLYING', 'ACTIVE', 'BUILDING', 'REJECTED', 'ACCEPTED', 'STOPPED')
];

// 专线运营商枚举
export const ISP = new Enum(
    {alias: 'ISP_CMCC', text: '中国移动', value: 'ISP_CMCC'},
    {alias: 'ISP_CUCC', text: '中国联通', value: 'ISP_CUCC'},
    {alias: 'ISP_CTC', text: '中国电信', value: 'ISP_CTC'},
    {alias: 'ISP_OTHER', text: '其它', value: 'ISP_OTHER'}
);

export const PortType = new Enum(
    {alias: '1G', text: '1G', value: '1G'},
    {alias: '10G', text: '10G', value: '10G'},
    {alias: '40G', text: '40G', value: '40G'},
    {alias: '100G', text: '100G', value: '100G'},
    {alias: '400G', text: '400G', value: '400G'}
);

// 专线接入点枚举
let extraApAddr = [];
if (FLAG.NetworkAccessPointJL) {
    extraApAddr = [
        {alias: 'HZHG', text: '杭州杭钢接入点', value: 'HZHG', region: 'cnhzpro'},
        {alias: 'HZXS', text: '杭州萧山接入点', value: 'HZXS', region: 'cnhzpro'}
    ];
}
if (FLAG.NetworkAccessPointYuXin) {
    extraApAddr = [{alias: 'BJXG', text: '北京星光', value: 'BJXG', region: 'hb'}];
}

export let ApAddr = new Enum(
    {alias: 'BB', text: '海淀-A', value: 'BB', region: 'bj'},
    {alias: 'BJYZ', text: '大兴-A', value: 'BJYZ', region: 'bj'},
    {alias: 'BJFS', text: '房山-A', value: 'BJDD', region: 'bj'},
    {alias: 'BJCY2', text: '朝阳-A', value: 'M1', region: 'bj'},
    {alias: 'BJLSH', text: '丰台-A', value: 'BJLSH', region: 'bj'},
    {alias: 'BDBL', text: '保定-A', value: 'BDBL', region: 'bd'},
    {alias: 'BDDX', text: '保定-B', value: 'BDDX', region: 'bd'},
    {alias: 'M3A', text: '广州-A', value: 'M3A', region: 'gz'},
    {alias: 'GZBH', text: '广州-B', value: 'GZBH', region: 'gz'},
    {alias: 'M3B', text: '广州-深圳-A', value: 'M3B', region: 'gz'},
    {alias: 'M2A', text: '苏州-南京-A', value: 'M2A', region: 'su'},
    {alias: 'M2B', text: '苏州-B', value: 'M2B', region: 'su'},
    {alias: 'SZTH', text: '苏州-C', value: 'SZTH', region: 'su'},
    {alias: 'WXTKY', text: '苏州-无锡-A', value: 'WXTKY', region: 'su'},
    {alias: 'CSZJHZ', text: '苏州-杭州-A', value: 'CSZJHZ', region: 'su'},
    {alias: 'HKG03', text: '香港-A', value: 'HKG03', region: 'hkg'},
    {alias: 'WHGG', text: '武汉金融-A', value: 'WHGG', region: 'fwh'},
    {alias: 'SHWGQ', text: '上海-A', value: 'SHWGQ', region: 'fsh'},
    {alias: 'BJHW', text: '北京华威接入点', value: 'BJHW', region: 'bjks'},
    {alias: 'BDBL_FSG', text: '度小满金融保定-A', value: 'BDBL_FSG', region: 'hb-fsg'},
    {alias: 'BJDD_FSG', text: '度小满金融房山-A', value: 'BJDD_FSG', region: 'bjfsg'},
    {alias: 'SZTH_FSG', text: '度小满金融苏州-B', value: 'SZTH_FSG', region: 'szfsg'},
    {alias: 'GZBH_FSG', text: '度小满金融广州-A', value: 'GZBH_FSG', region: 'gzfsg'},
    {alias: 'SHPBS', text: '上海-B', value: 'SHPBS', region: 'fsh'},
    {alias: 'SHPBS_PDD', text: '苏州-D', value: 'SHPBS_PDD', region: 'su'},
    ...extraApAddr
);

ApAddr.getIspPortByApAddr = () => {
    return {
        BB: {
            isp: ISP.toArray('ISP_CTC', 'ISP_CUCC', 'ISP_CMCC'),
            portType: PortType.toArray()
        },
        BJYZ: {
            isp: ISP.toArray('ISP_CTC', 'ISP_CUCC', 'ISP_CMCC'),
            portType: PortType.toArray('1G', '10G')
        },
        BJDD: {
            isp: ISP.toArray('ISP_CUCC'),
            portType: PortType.toArray()
        },
        M1: {
            isp: ISP.toArray('ISP_CTC', 'ISP_CUCC', 'ISP_CMCC'),
            portType: PortType.toArray()
        },
        BJLSH: {
            isp: ISP.toArray('ISP_CTC'),
            portType: PortType.toArray()
        },
        BDBL: {
            isp: ISP.toArray('ISP_CMCC'),
            portType: PortType.toArray()
        },
        BDDX: {
            isp: ISP.toArray('ISP_CTC', 'ISP_CUCC', 'ISP_CMCC'),
            portType: PortType.toArray()
        },
        M3A: {
            isp: ISP.toArray('ISP_CTC', 'ISP_CUCC', 'ISP_CMCC'),
            portType: PortType.toArray('1G', '10G')
        },
        GZBH: {
            isp: ISP.toArray('ISP_CTC', 'ISP_CUCC', 'ISP_CMCC'),
            portType: PortType.toArray()
        },
        M3B: {
            isp: ISP.toArray('ISP_CTC', 'ISP_CUCC', 'ISP_CMCC'),
            portType: PortType.toArray('1G', '10G')
        },
        M2A: {
            isp: ISP.toArray('ISP_CTC', 'ISP_CUCC', 'ISP_CMCC'),
            portType: PortType.toArray('1G', '10G')
        },
        M2B: {
            isp: ISP.toArray('ISP_CTC', 'ISP_CUCC', 'ISP_CMCC'),
            portType: PortType.toArray()
        },
        SZTH: {
            isp: ISP.toArray('ISP_CTC'),
            portType: PortType.toArray()
        },
        WXTKY: {
            isp: ISP.toArray('ISP_CMCC'),
            portType: PortType.toArray()
        },
        CSZJHZ: {
            isp: ISP.toArray('ISP_CTC', 'ISP_CUCC', 'ISP_CMCC'),
            portType: PortType.toArray()
        },
        SHWGQ: {
            isp: ISP.toArray('ISP_OTHER'),
            portType: PortType.toArray('1G', '10G')
        },
        WHGG: {
            isp: ISP.toArray('ISP_OTHER'),
            portType: PortType.toArray('1G', '10G')
        },
        HKG03: {
            isp: ISP.toArray('ISP_OTHER'),
            portType: PortType.toArray('1G', '10G')
        },
        BJHW: {
            isp: ISP.toArray(),
            portType: PortType.toArray()
        },
        BDBL_FSG: {
            isp: ISP.toArray('ISP_CMCC'),
            portType: PortType.toArray()
        },
        BJDD_FSG: {
            isp: ISP.toArray('ISP_CUCC'),
            portType: PortType.toArray()
        },
        SZTH_FSG: {
            isp: ISP.toArray('ISP_CTC', 'ISP_CUCC', 'ISP_CMCC'),
            portType: PortType.toArray()
        },
        GZBH_FSG: {
            isp: ISP.toArray('ISP_CTC', 'ISP_CUCC', 'ISP_CMCC'),
            portType: PortType.toArray('1G', '10G')
        }
    };
};

// 专线接入端口类型枚举
export const IntfType = new Enum(
    {alias: 'BASE_1000_LX', text: '1000BASE_LX 千兆单模光口 10KM', value: 'BASE_1000_LX'},
    {alias: 'BASE_1000_SX', text: '1000BASE_SX 千兆多模光口', value: 'BASE_1000_SX'},
    {alias: 'BASE_10G_LR', text: '10GBASE_LR 万兆单模光口 10KM', value: 'BASE_10G_LR'},
    {alias: 'BASE_10G_SR', text: '10GBASE_SR 万兆多模光口', value: 'BASE_10G_SR'},
    {alias: 'BASE_OTHER', text: '其它', value: 'BASE_OTHER'}
);

// 专线申请流程
export const ApplyDiagram = new Enum(
    {alias: 'FIRST', text: '申请物理专线。选择普通专线/合作伙伴专线', value: 1},
    {alias: 'SECOND', text: '物理专线建设。完成申请审核，缴费，专线搭建等步骤，最终完成物理专线交付', value: 2},
    {alias: 'THIRD', text: '创建专线通道。在需要专线打通的VPC中建立专线网关并同物理专线绑定', value: 3},
    {
        alias: 'FORTH',
        text: '配置路由表，实现流量互通。在VPC路由表中配置访问IDC网络的路由，同时在IDC侧配置访问VPC的路由，实现两端流量互通',
        value: 4
    }
);

export const AuthorizedUsersType = new Enum(
    {alias: 'SELF', text: '本账户', value: 'self'},
    {alias: 'OTHERS', text: '其他账户', value: 'others'}
);

export const ET_CHANNEL_PAYER = new Enum(
    {alias: 'creator', text: '创建者', value: 'creator'},
    {alias: 'recipient', text: '接受者', value: 'recipient'}
);

export const AuthDistributionType = new Enum(
    {alias: 'MANAGE', text: '管理权', value: 'manage'},
    {alias: 'USE', text: '使用权', value: 'use'}
);

export const RouteParamsType = new Enum(
    {alias: 'STATIC', text: '静态路由', value: 'static-route'},
    {alias: 'BGP', text: '动态路由', value: 'bgp'}
);

export const ApType = new Enum(
    {alias: 'ALIAS', text: '单线', value: 'SINGLE'},
    {alias: 'BAIDU', text: '百度内部', value: '内部专线'}
);

export const CSN_ROUTE_STATUS = new Enum(
    {alias: 'UNPUBLISHED', text: '未发布', value: 'unpublished', kclass: 'status unavailable'},
    {alias: 'PUBLISHED', text: '已发布', value: 'published', kclass: 'status normal'},
    {alias: 'LEARNED', text: '-', value: 'learned', kclass: 'status error'}
);

export const routeStandardList = [
    {text: '北京联通本地2M-1', value: 'BJLT-BD-2M-1'},
    {text: '北京联通本地2M-2', value: 'BJLT-BD-2M-2'},
    {text: '北京联通本地10M-1', value: 'BJLT-BD-10M-1'},
    {text: '北京联通本地10M-2', value: 'BJLT-BD-10M-2'},
    {text: '北京联通长途2M-1', value: 'BJLT-CT-2M-1'},
    {text: '北京联通长途2M-2', value: 'BJLT-CT-2M-2'},
    {text: '北京联通长途4M-1', value: 'BJLT-CT-4M-1'},
    {text: '深圳联通长途2M-1', value: 'SZXLT-CT-2M-1'},
    {text: '深圳联通长途2M-2', value: 'SZXLT-CT-2M-2'},
    {text: '广州电信长途2M-1', value: 'GZDX-CT-2M-1'},
    {text: '广州电信长途4M-1', value: 'GZDX-CT-4M-1'},
    {text: '深圳联通长途10M-1', value: 'SZXLT-CT-10M-1'},
    {text: '苏州联通长途20M-1', value: 'SZVLT-CT-20M-1'},
    {text: '苏州联通长途2M-1', value: 'SZVLT-CT-2M-1'},
    {text: '苏州联通长途2M-2', value: 'SZVLT-CT-2M-2'},
    {text: '苏州联通长途2M-3', value: 'SZVLT-CT-2M-3'},
    {text: '苏州联通长途10M-1', value: 'SZVLT-CT-10M-1'},
    {text: '昆山电信长途2M-1', value: 'KSDX-CT-2M-1'},
    {text: '广州联通长途10M-1', value: 'GZLT-CT-10M-1'},
    {text: '苏州移动长途2M-1', value: 'SZVYD-CT-2M-1'},
    {text: '苏州电信长途2M-1', value: 'SZVDX-CT-2M-1'},
    {text: '苏州电信长途8M-1', value: 'SZVDX-CT-8M-1'},
    {text: '南京电信长途6M-1', value: 'NJDX-CT-6M-1'}
];

// 不同类型nat网关对应的最大连接数和带宽
export const NatTypeSessionBps = new Enum(
    {alias: 'LITTLE', text: '小型NAT网关', value: 'little', maxSession: 10000, bps: 1000},
    {alias: 'MEDIUM', text: '中型NAT网关', value: 'medium', maxSession: 50000, bps: 2000},
    {alias: 'LARGE', text: '大型NAT网关', value: 'large', maxSession: 200000, bps: 5000}
);

// 子网ip地址用途，只维护与v3中不一致资源
export const ipResourceType = new Enum(
    {alias: 'BAE', text: '应用引擎', value: 'bae'},
    {alias: 'EMR', text: 'EMR', value: 'emr'},
    {alias: 'GAIA', text: '云数据库GaiaDB', value: 'gaia'},
    {alias: 'XDB', text: 'XDB', value: 'xdb'},
    {alias: 'PINGO', text: '数据工厂PINGO', value: 'pingo'},
    {alias: 'PALO', text: '数据仓库', value: 'palo'},
    {alias: 'UNKNOWN', text: '未知', value: 'unknown'},
    {alias: 'ET_HEALTH_CHECK', text: '专线链路探测或网络探测', value: 'et_health_check'},
    {alias: 'SYS_OCCUPANCY', text: '系统占用', value: 'sys_occupancy'},
    {alias: 'DHCP', text: 'DNS地址', value: 'dhcp'},
    {alias: 'PFS', text: '并行文件存储', value: 'pfs'},
    {alias: 'DDC', text: '云数据库专属集群', value: 'ddc'},
    {alias: 'BAEPRO', text: '应用引擎', value: 'baepro'},
    {alias: 'LD', text: 'DNS解析器', value: 'ld'},
    {alias: 'NLB', text: 'NLB', value: 'nlb'},
    {alias: 'NAT_CLUSTER', text: 'NAT网关', value: 'nat_cluster'}
);

export let bfdStatus = new Enum(
    {alias: 'UP', text: 'UP', value: 'up', kclass: 'status normal'},
    {alias: 'DOWN', text: 'DOWN', value: 'down', kclass: 'status error'},
    {alias: 'INIT', text: 'INIT', value: 'init', kclass: 'status warning'}
);

export const mirrorSourceType = new Enum(
    {alias: 'EIP', text: '弹性公网IP', value: 'EIP'},
    {alias: 'BCC', text: '云服务器', value: 'BCC'},
    {alias: 'ENI', text: '弹性网卡', value: 'ENI'},
    {alias: 'NAT', text: '增强型NAT网关', value: 'NAT'},
    {alias: 'PEERCONN', text: '对等连接', value: 'PEERCONN'},
    {alias: 'CSN', text: '云智能网', value: 'CSN'},
    {alias: 'DCGW', text: '专线网关', value: 'DCGW'}
);

export const mirrorDestType = new Enum(
    {alias: 'BLB', text: '负载均衡', value: 'BLB'},
    {alias: 'BCC', text: '云服务器', value: 'BCC'},
    {alias: 'ENI', text: '弹性网卡', value: 'ENI'}
);

export const mirrorStatus = new Enum(
    {alias: 'RUNNING', text: '启用', value: 'running', styleClass: 'status normal'},
    {alias: 'PAUSED', text: '暂停', value: 'paused', styleClass: 'status warning'}
);

export const GatewayRuleType = new Enum({alias: 'IPV4', text: 'IPv4', value: '4'});

export const GatewayServiceType = new Enum(
    {alias: 'ET', text: '专线网关', value: 'et'},
    {alias: 'PEERCONN', text: '对等连接', value: 'peerconn'}
);

export const subServiceTypeList = new Enum(
    {alias: 'PEER_CLOUD', text: '云间互通', value: 'PEER_CLOUD'},
    {alias: 'PEER_EDGE', text: '云边互通', value: 'PEER_EDGE'},
    {alias: 'LOCAL', text: '网络实例', value: 'LOCAL'}
);

export const GatewaySearchType = new Enum(
    {
        alias: 'NAME',
        text: '规则名称',
        value: 'name'
    },
    {
        alias: 'ID',
        text: '规则ID',
        value: 'id'
    },
    {
        alias: 'RESOURCEId',
        text: '实例ID',
        value: 'resourceId'
    }
);

export let Payment = new Enum(
    {alias: 'PREPAY', text: '预付费', value: 'prepay'},
    {alias: 'POSTPAY', text: '后付费', value: 'postpay'}
);

/**
 * 实例配置
 *
 * @type {Object}
 */
export let serverInstanceStatus = new Enum(
    {alias: 'ACTIVE', text: '运行中', value: 'ACTIVE', klass: 'normal'},
    {alias: 'SHUTOFF', text: '已关机', value: 'SHUTOFF', klass: 'unavailable'},
    {alias: 'PAUSED', text: '已睡眠', value: 'PAUSED', klass: 'warning'},
    {alias: 'SUSPENDED', text: '已休眠', value: 'SUSPENDED', klass: 'warning'},
    {alias: 'RESCUE', text: '已修复', value: 'RESCUE', klass: 'warning'},
    {alias: 'ERROR', text: '错误', value: 'ERROR', klass: 'error'},
    {alias: 'SOFT_DELETED', text: '已删除', value: 'SOFT_DELETED', klass: 'warning'},
    {alias: 'BUILD', text: '创建中', value: 'BUILD', klass: 'status warning'},
    {alias: 'VERIFY_RESIZE', text: '扩容确认中', value: 'VERIFY_RESIZE', klass: 'warning'},
    {alias: 'REVERT_RESIZE', text: '扩容回滚中', value: 'REVERT_RESIZE', klass: 'warning'},
    {alias: 'EXPIRED', text: '已到期', value: 'EXPIRED', klass: 'error'},
    {alias: 'SHELVED', text: '搁置', value: 'SHELVED', klass: 'warning'},
    {alias: 'SHELVED_OFFLOADED', text: '搁置', value: 'SHELVED_OFFLOADED', klass: 'warning'},
    {alias: 'SNAPSHOT', text: '创建快照中', value: 'SNAPSHOT', klass: 'warning'},
    {alias: 'ROLLBACK_SNAPSHOT', text: '回滚快照中', value: 'ROLLBACK_SNAPSHOT', klass: 'warning'},
    {alias: 'DELETE_SNAPSHOT', text: '删除快照中', value: 'DELETE_SNAPSHOT', klass: 'warning'},
    {alias: 'TEMPLATE', text: '创建镜像中', value: 'TEMPLATE', klass: 'warning'},
    {alias: 'REBOOT', text: '重启中', value: 'REBOOT', klass: 'warning'},
    {alias: 'HARD_REBOOT', text: '重启中', value: 'HARD_REBOOT', klass: 'warning'},
    {alias: 'PASSWORD', text: '重置密码中', value: 'PASSWORD', klass: 'warning'},
    {alias: 'REBUILD', text: '重建中', value: 'REBUILD', klass: 'warning'},
    {alias: 'MIGRATING', text: '迁移中', value: 'MIGRATING', klass: 'warning'},
    {alias: 'RESIZE', text: '资源扩展中或未支付', value: 'RESIZE', klass: 'warning'},
    {alias: 'DELETING', text: '释放中', value: 'DELETING', klass: 'warning'},
    {alias: 'BINDING', text: '绑定中', value: 'BINDING', klass: 'warning'},
    {alias: 'UNBINDING', text: '解绑中', value: 'UNBINDING', klass: 'warning'},
    {alias: 'BILLING_CHANGING', text: '计费变更中', value: 'BILLING_CHANGING', klass: 'warning'},
    {alias: 'OFFLINE', text: '专属服务器离线', value: 'OFFLINE', klass: 'warning'},
    {alias: 'DCC_EXPIRED', text: '专属服务器已到期', value: 'DCC_EXPIRED', klass: 'warning'},
    {alias: 'POWERING_OFF', text: '关机中', value: 'POWERING_OFF', klass: 'warning'},
    {alias: 'POWERING_ON', text: '开机中', value: 'POWERING_ON', klass: 'warning'},
    {alias: 'RECHARGE', text: '续费中', value: 'RECHARGE', klass: 'warning'},
    {alias: 'VOLUME_RESIZE', text: '磁盘扩容中', value: 'VOLUME_RESIZE', klass: 'warning'},
    {alias: 'BINDKEYPAIR', text: '绑定密钥对中', value: 'BINDKEYPAIR', klass: 'warning'},
    {alias: 'UNBINDKEYPAIR', text: '解绑密钥对中', value: 'UNBINDKEYPAIR', klass: 'warning'},
    {alias: 'HWUPGRADE', text: '固件升级中', value: 'HWUPGRADE', klass: 'warning rolling'},
    {alias: 'SHUTOFF_PAUSE_CHARGE', text: '关机不计费', value: 'SHUTOFF_PAUSE_CHARGE', klass: 'unavailable'},
    {alias: 'STOPPED', text: '已关机', value: 'STOPPED', klass: 'unavailable'},
    {alias: 'ATTACHING_PORT', text: '网卡挂载中', value: 'ATTACHING_PORT', klass: 'warning'},
    {alias: 'DETACHING_PORT', text: '网卡卸载中', value: 'DETACHING_PORT', klass: 'warning'}
);

export const ipAddressType = new Enum(
    {alias: 'SET', text: 'IP地址组', value: 'set'},
    {alias: 'GROUP', text: 'IP地址族', value: 'group'}
);

export const ipAddressGroupSearchType = new Enum(
    {alias: 'IP_SET_NAME', text: 'IP地址组名称', value: 'IP_SET_NAME'},
    {alias: 'IP_SET_ID', text: 'IP地址组ID', value: 'IP_SET_ID'}
);

export const ipAddressFamilySearchType = new Enum(
    {alias: 'IP_GROUP_NAME', text: 'IP地址族名称', value: 'IP_GROUP_NAME'},
    {alias: 'IP_GROUP_ID', text: 'IP地址族ID', value: 'IP_GROUP_ID'}
);

export const setInstanceSearchType = new Enum(
    {alias: 'NAME', text: '实例名称', value: 'name'},
    {alias: 'INSTANCEID', text: '实例ID', value: 'instanceId'}
);

export let pathTypeList = new Enum(
    {alias: 'BCC', text: '云服务器 BCC', value: 'BCC'},
    // {alias: 'BBC', text: '弹性裸金属服务器 BBC', value: 'BBC'},
    {alias: 'SUBNET', text: '子网', value: 'SUBNET'},
    {alias: 'DC', text: '专线通道', value: 'DC'},
    // { alias: 'CSN', text: '云智能网', value: 'CSN'},
    {alias: 'VPN_CONN', text: 'VPN通道', value: 'VPN_CONN'}
    // { alias: 'PEERCONN', text: '对等连接', value: 'PEERCONN'},
    // { alias: 'BLB', text: '负载均衡', value: 'BLB'},
);

export let SUBPRODUCTTYPE = new Enum(
    {alias: 'PREPAYEIP', text: '包年包月计费', value: ''},
    {alias: 'BANDWIDTH', text: '后付费-按带宽计费', value: 'bandwidth'},
    {alias: 'NETRAFFIC', text: '后付费-按流量计费', value: 'netraffic'},
    {alias: 'BOS', text: '与BOS合并计费', value: 'PeakBandwidth_Percent_95_A'},
    {alias: 'PEAKBANDWIDTH_PERCENT_95', text: '按增强型95计费', value: 'PeakBandwidth_Percent_95'},
    {alias: 'TRO_PEAKBANDWIDTH_PERCENT_95', text: '按传统型95计费', value: 'Tro_PeakBandwidth_Percent_95'}
);

export const singleLineType = ['ChinaTelcom', 'ChinaUnicom', 'ChinaMobile'];

export let eipLineType = new Enum(
    {alias: 'BGP', text: '标准型BGP', value: 'BGP'},
    {alias: 'BGP_S', text: '增强型BGP', value: 'BGP_S'},
    {alias: 'Static', text: '静态BGP', value: 'Static'},
    {alias: 'ChinaTelcom', text: '电信单线', value: 'ChinaTelcom'},
    {alias: 'ChinaUnicom', text: '联通单线', value: 'ChinaUnicom'},
    {alias: 'ChinaMobile', text: '移动单线', value: 'ChinaMobile'},
    {alias: 'Custom', text: '定制线路', value: 'Custom'},
    {alias: 'BackToOrigin', text: '回源IP', value: 'BackToOrigin'}
);

export let eipStatus = new Enum(
    {alias: 'CREATING', text: '创建中', value: 'creating', kclass: 'status warning'},
    {alias: 'PAUSING', text: '暂停中', value: 'pausing', kclass: 'status error'},
    {alias: 'RECOVERING', text: '恢复中', value: 'recovering', kclass: 'status warning'},
    {alias: 'BINGDING', text: 'EIP绑定中', value: 'binding', kclass: 'status warning'},
    {alias: 'UNBINGDING', text: 'EIP解绑中', value: 'unbinding', kclass: 'status warning'},
    {alias: 'UPDATING', text: '配置更新中', value: 'updating', kclass: 'status warning'},
    {alias: 'AVAILABLE', text: '运行中', value: 'available', kclass: 'status normal'},
    {alias: 'UNAVAILABLE', text: '暂不可用', value: 'unavailable', kclass: 'status warning'},
    {alias: 'PAUSED', text: '已欠费', value: 'paused', kclass: 'status error'},
    {alias: 'EIPPAUSED', text: 'EIP已停用', value: 'eipPaused', kclass: 'status warning'},
    {alias: 'DELETING', text: '释放中', value: 'deleting', kclass: 'status warning'}
);

export const SubnetReserveStatus = new Enum(
    {alias: 'AVAILABLE', text: '可用', value: 'available', kclass: 'status normal'},
    {alias: 'CREATING', text: '创建中', value: 'creating', kclass: 'status rolling '}
);

export let QuotaServiceList = new Enum(
    {alias: 'VPC', text: '私有网络', value: 'vpc'},
    {alias: 'SUBNET', text: '子网', value: 'subnet'},
    {alias: 'ROUTE', text: '路由表', value: 'route'},
    {alias: 'ENI', text: '弹性网卡', value: 'eni'},
    {alias: 'ENDPOINT', text: '服务网卡', value: 'endpoint'},
    {alias: 'SECURITYGROUP', text: '安全组', value: 'securitygroup'},
    {alias: 'ACL', text: 'ACL', value: 'acl'},
    {alias: 'NAT', text: 'NAT网关', value: 'nat'},
    {alias: 'IPV6', text: 'IPv6网关', value: 'ipv6'},
    {alias: 'VPN', text: 'VPN网关', value: 'vpn'},
    {alias: 'PEERCONN', text: '对等连接', value: 'peerconn'},
    {alias: 'DCGW', text: '专线网关', value: 'dcgw'},
    {alias: 'FLOWLOG', text: '流日志', value: 'flowlog'},
    {alias: 'IPGROUP', text: '参数模板', value: 'ipgroup'},
    {alias: 'HAVIP', text: '高可用虚拟IP', value: 'havip'},
    {alias: 'L2GW', text: '二层网关', value: 'l2gw'},
    {alias: 'GWLIMIT', text: '网关限速', value: 'gwlimit'}
);

export let alarmType = new Enum(
    {alias: 'USED', text: '已使用量报警', value: 'USED'},
    {alias: 'SURPLUS', text: '剩余可用量报警', value: 'SURPLUS'}
);

export let alarmConfig = new Enum(
    {alias: 'NUMERICAL', text: '数值', value: 'NUMERICAL'},
    {alias: 'PERCENTAGE', text: '百分比', value: 'PERCENTAGE'}
);

export let eipServiceType = new Enum(
    {
        alias: 'EIP',
        text: '弹性公网IP EIP',
        value: 'eip',
        children: [
            {alias: 'EIP', text: 'EIP实例', value: 'eip'},
            {alias: 'EIPGROUP', text: '共享带宽 EIPGROUP', value: 'eipgroup'},
            {alias: 'EIP_BP', text: '带宽包 EIP_BP', value: 'eip_bp'}
        ]
    },
    {alias: 'BLB', text: '负载均衡 BLB', value: 'blb'},
    {alias: 'DNS', text: '智能云解析 DNS', value: 'dns', children: [{alias: 'LD', text: '内网DNS服务', value: 'ld'}]},
    {
        alias: 'VPC',
        text: '私有网络 VPC',
        value: 'vpc',
        children: [
            {alias: 'VPC', text: '私有网络', value: 'vpc'},
            {alias: 'SUBNET', text: '子网 SUBNET', value: 'subnet'},
            {alias: 'ROUTE', text: '路由表 ROUTE', value: 'route'},
            {alias: 'ENI', text: '弹性网卡 ENI', value: 'eni'},
            {alias: 'ENDPOINT', text: '服务网卡 ENDPOINT', value: 'endpoint'},
            {alias: 'SECURITYGROUP', text: '安全组 SECURITYGROUP', value: 'securitygroup'},
            {alias: 'ACL', text: 'ACL', value: 'acl'},
            {alias: 'IPGROUP', text: '参数模板', value: 'ipgroup'},
            {alias: 'HAVIP', text: '高可用虚拟IP', value: 'havip'},
            {alias: 'NAT', text: 'NAT网关', value: 'nat'},
            {alias: 'IPV6', text: 'IPv6网关', value: 'ipv6'},
            {alias: 'VPN', text: 'VPN网关', value: 'vpn'},
            {alias: 'PEERCONN', text: '对等连接 PEERCONN', value: 'peerconn'},
            {alias: 'DCGW', text: '专线网关 DCGW', value: 'dcgw'},
            {alias: 'FLOWLOG', text: '流日志 FLOWLOG', value: 'flowlog'}
        ]
    },
    {alias: 'ET', text: '专线接入 ET', value: 'et'}
);

export let computProductType = new Enum(
    {alias: 'BCC', text: '云服务器 BCC', value: 'bcc'},
    {alias: 'BBC', text: '弹性裸金属服务器 BBC', value: 'bbc'},
    {alias: 'AS', text: '弹性伸缩 AS', value: 'as'}
);

export let databaseProductType = new Enum(
    {alias: 'SCS', text: '云数据库 SCS for Redis', value: 'scs'},
    {alias: 'RDS', text: '云数据库 RDS', value: 'rds'}
);

export let manageProductType = new Enum({alias: 'COMMON', text: '标签管理', value: 'common'});

export let applyStatus = new Enum(
    {alias: 'ALL', text: '全部', value: ''},
    {alias: 'PROCESSING', text: '处理中', value: 'PROCESSING', kclass: 'status processing'},
    {alias: 'APPROVING', text: '审核中', value: 'APPROVING', kclass: 'status approving'},
    {alias: 'EFFECTING', text: '生效中', value: 'EFFECTING', kclass: 'status effecting'},
    {alias: 'EFFECTED', text: '已生效', value: 'EFFECTED', kclass: 'status normal'},
    {alias: 'REFUSED', text: '拒绝', value: 'REFUSED', kclass: 'status error'},
    {alias: 'APPROVED', text: '审批通过', value: 'APPROVED', kclass: 'status normal'}
);

export let examineStatus = new Enum(
    {alias: 'APPROVED', text: '审核通过', value: 'APPROVED'},
    {alias: 'REFUSED', text: '审核拒绝', value: 'REFUSED'}
);

export let l2gwStatus = new Enum(
    {
        alias: 'BUILDING',
        text: '创建中',
        value: 'building',
        styleClass: 'status warning'
    },
    {
        alias: 'ACTIVE',
        text: '可用',
        value: 'active',
        styleClass: 'status normal'
    },
    {
        alias: 'DOWN',
        text: '不可用',
        value: 'down',
        styleClass: 'status error'
    },
    {
        alias: 'UPDATING',
        text: '更新中',
        value: 'updating',
        styleClass: 'status warning'
    },
    {
        alias: 'SWITCHING',
        text: '主备切换中',
        value: 'switching',
        styleClass: 'status warning'
    },
    {
        alias: 'ERROR',
        text: '状态异常',
        value: 'error',
        styleClass: 'status error'
    },
    {
        alias: 'DELETED',
        text: '已删除',
        value: 'deleted',
        styleClass: 'status unavailable'
    }
);
export const associationProduct = new Enum(
    {
        alias: 'assbcc',
        text: '关联云服务器',
        value: 'assbcc'
    },
    {
        alias: 'asseni',
        text: '关联弹性网卡',
        value: 'asseni'
    },
    {
        alias: 'asssnic',
        text: '关联服务网卡',
        value: 'asssnic'
    },
    {
        alias: 'assbbc',
        text: '关联弹性裸金属服务器',
        value: 'assbbc'
    },
    {
        alias: 'assblb',
        text: '关联负载均衡',
        value: 'assblb'
    },
    {
        alias: 'assddc',
        text: '关联云数据库专属集群',
        value: 'assddc'
    },
    {
        alias: 'assscs',
        text: '关联云数据库 Redis',
        value: 'assscs'
    },
    {
        alias: 'assrds',
        text: '关联云数据库RDS',
        value: 'assrds'
    },
    {
        alias: 'assrabbitmq',
        text: '关联消息服务 for RabbitMQ',
        value: 'assrabbitmq'
    },
    {
        alias: 'assgaiadb',
        text: '关联云数据库GaiaDB-S',
        value: 'assgaiadb'
    }
);
export let DCOptType = new Enum({alias: 'LINKDOWN', text: '端口延迟Down配置', value: 'LINKDOWN'});

export let ChannelOptType = new Enum({alias: 'FAKEASN', text: 'Fake ASN编辑', value: 'FAKEASN'});

// 普通安全组预定义模板
export const securityTemp = new Enum(
    {
        alias: '1',
        text: '入站拒绝所有访问请求，出站允许所有访问请求',
        value: '1',
        temp: {
            in: [],
            out: [
                {
                    ethertype: SecurityIpVersion.IPV4,
                    protocol: 'all',
                    portRange: '1-65535',
                    name: '暴露全部端口到公网和内网'
                },
                {
                    ethertype: SecurityIpVersion.IPV6,
                    protocol: 'all',
                    portRange: '1-65535',
                    name: '暴露全部端口到公网和内网'
                }
            ]
        }
    },
    {
        alias: '2',
        text: '入站允许22，80，443，3389端口和ICMP协议，出站允许所有访问请求',
        value: '2',
        temp: {
            in: [
                {
                    ethertype: SecurityIpVersion.IPV4,
                    protocol: 'tcp',
                    portRange: '3389',
                    name: '放通Windows远程登录'
                },
                {
                    ethertype: SecurityIpVersion.IPV4,
                    protocol: 'tcp',
                    portRange: '22',
                    name: '放通Linux SSH登录'
                },
                {
                    ethertype: SecurityIpVersion.IPV4,
                    protocol: 'tcp',
                    portRange: '80',
                    name: '放通Web服务端口'
                },
                {
                    ethertype: SecurityIpVersion.IPV4,
                    protocol: 'tcp',
                    portRange: '443',
                    name: '放通Web服务端口'
                },
                {
                    ethertype: SecurityIpVersion.IPV4,
                    protocol: 'icmp',
                    portRange: '1-65535',
                    name: '放通Ping服务'
                },
                {
                    ethertype: SecurityIpVersion.IPV4,
                    protocol: 'all',
                    portRange: '1-65535',
                    ksource: '10.0.0.0/8',
                    name: '放通内网'
                },
                {
                    ethertype: SecurityIpVersion.IPV4,
                    protocol: 'all',
                    portRange: '1-65535',
                    ksource: '**********/12',
                    name: '放通内网'
                },
                {
                    ethertype: SecurityIpVersion.IPV4,
                    protocol: 'all',
                    portRange: '1-65535',
                    ksource: '***********/16',
                    name: '放通内网'
                },

                {
                    ethertype: SecurityIpVersion.IPV6,
                    protocol: 'tcp',
                    portRange: '3389',
                    name: '放通Windows远程登录'
                },
                {
                    ethertype: SecurityIpVersion.IPV6,
                    protocol: 'tcp',
                    portRange: '22',
                    name: '放通Linux SSH登录'
                },
                {
                    ethertype: SecurityIpVersion.IPV6,
                    protocol: 'tcp',
                    portRange: '80',
                    name: '放通Web服务端口'
                },
                {
                    ethertype: SecurityIpVersion.IPV6,
                    protocol: 'tcp',
                    portRange: '443',
                    name: '放通Web服务端口'
                },
                {
                    ethertype: SecurityIpVersion.IPV6,
                    protocol: 'icmp',
                    portRange: '1-65535',
                    name: '放通Ping服务'
                }
            ],
            out: [
                {
                    ethertype: SecurityIpVersion.IPV4,
                    protocol: 'all',
                    portRange: '1-65535',
                    name: '暴露全部端口到公网和内网'
                },
                {
                    ethertype: SecurityIpVersion.IPV6,
                    protocol: 'all',
                    portRange: '1-65535',
                    name: '暴露全部端口到公网和内网'
                }
            ]
        }
    },
    {
        alias: '3',
        text: '入站和出站允许所有访问请求',
        value: '3',
        temp: {
            in: [
                {
                    ethertype: SecurityIpVersion.IPV4,
                    protocol: 'all',
                    portRange: '1-65535',
                    name: '暴露全部端口到公网和内网'
                },
                {
                    ethertype: SecurityIpVersion.IPV6,
                    protocol: 'all',
                    portRange: '1-65535',
                    name: '暴露全部端口到公网和内网'
                }
            ],
            out: [
                {
                    ethertype: SecurityIpVersion.IPV4,
                    protocol: 'all',
                    portRange: '1-65535',
                    name: '暴露全部端口到公网和内网'
                },
                {
                    ethertype: SecurityIpVersion.IPV6,
                    protocol: 'all',
                    portRange: '1-65535',
                    name: '暴露全部端口到公网和内网'
                }
            ]
        }
    }
);

// 企业安全组预定义模板
export const enterpriseSecurityTemp = new Enum(
    {
        alias: '1',
        text: '入站拒绝所有访问请求，出站允许所有访问请求',
        value: '1',
        temp: {
            in: [],
            out: [
                {
                    ethertype: SecurityIpVersion.IPV4,
                    protocol: 'all',
                    portRange: '1-65535',
                    priority: '1000',
                    localPortRange: '不涉及',
                    action: 'allow',
                    name: '暴露全部端口到公网和内网'
                },
                {
                    ethertype: SecurityIpVersion.IPV6,
                    protocol: 'all',
                    portRange: '1-65535',
                    priority: '1000',
                    localPortRange: '不涉及',
                    action: 'allow',
                    name: '暴露全部端口到公网和内网'
                }
            ]
        }
    },
    {
        alias: '2',
        text: '入站允许22，80，443，3389端口和ICMP协议，出站允许所有访问请求',
        value: '2',
        temp: {
            in: [
                {
                    ethertype: SecurityIpVersion.IPV4,
                    protocol: 'tcp',
                    portRange: '3389',
                    priority: '1000',
                    localPortRange: '3389',
                    action: 'allow',
                    name: '放通Windows远程登录'
                },
                {
                    ethertype: SecurityIpVersion.IPV4,
                    protocol: 'tcp',
                    portRange: '22',
                    priority: '1000',
                    localPortRange: '22',
                    action: 'allow',
                    name: '放通Linux SSH登录'
                },
                {
                    ethertype: SecurityIpVersion.IPV4,
                    protocol: 'tcp',
                    portRange: '80',
                    priority: '1000',
                    localPortRange: '80',
                    action: 'allow',
                    name: '放通Web服务端口'
                },
                {
                    ethertype: SecurityIpVersion.IPV4,
                    protocol: 'tcp',
                    portRange: '443',
                    priority: '1000',
                    localPortRange: '443',
                    action: 'allow',
                    name: '放通Web服务端口'
                },
                {
                    ethertype: SecurityIpVersion.IPV4,
                    protocol: 'icmp',
                    portRange: '1-65535',
                    priority: '1000',
                    localPortRange: '不涉及',
                    action: 'allow',
                    name: '放通Ping服务'
                },
                {
                    ethertype: SecurityIpVersion.IPV4,
                    protocol: 'all',
                    portRange: '1-65535',
                    priority: '1000',
                    localPortRange: '不涉及',
                    action: 'allow',
                    ksource: '10.0.0.0/8',
                    name: '放通内网'
                },
                {
                    ethertype: SecurityIpVersion.IPV4,
                    protocol: 'all',
                    portRange: '1-65535',
                    priority: '1000',
                    localPortRange: '不涉及',
                    action: 'allow',
                    ksource: '**********/12',
                    name: '放通内网'
                },
                {
                    ethertype: SecurityIpVersion.IPV4,
                    protocol: 'all',
                    portRange: '1-65535',
                    priority: '1000',
                    localPortRange: '不涉及',
                    action: 'allow',
                    ksource: '***********/16',
                    name: '放通内网'
                },

                // IPv6
                {
                    ethertype: SecurityIpVersion.IPV6,
                    protocol: 'tcp',
                    portRange: '3389',
                    priority: '1000',
                    localPortRange: '3389',
                    action: 'allow',
                    name: '放通Windows远程登录'
                },
                {
                    ethertype: SecurityIpVersion.IPV6,
                    protocol: 'tcp',
                    portRange: '22',
                    priority: '1000',
                    localPortRange: '22',
                    action: 'allow',
                    name: '放通Linux SSH登录'
                },
                {
                    ethertype: SecurityIpVersion.IPV6,
                    protocol: 'tcp',
                    portRange: '80',
                    priority: '1000',
                    localPortRange: '80',
                    action: 'allow',
                    name: '放通Web服务端口'
                },
                {
                    ethertype: SecurityIpVersion.IPV6,
                    protocol: 'tcp',
                    portRange: '443',
                    priority: '1000',
                    localPortRange: '443',
                    action: 'allow',
                    name: '放通Web服务端口'
                },
                {
                    ethertype: SecurityIpVersion.IPV6,
                    protocol: 'icmp',
                    portRange: '1-65535',
                    priority: '1000',
                    localPortRange: '不涉及',
                    action: 'allow',
                    name: '放通Ping服务'
                }
            ],
            out: [
                {
                    ethertype: SecurityIpVersion.IPV4,
                    protocol: 'all',
                    portRange: '1-65535',
                    priority: '1000',
                    localPortRange: '不涉及',
                    action: 'allow',
                    name: '暴露全部端口到公网和内网'
                },
                {
                    ethertype: SecurityIpVersion.IPV6,
                    protocol: 'all',
                    portRange: '1-65535',
                    priority: '1000',
                    localPortRange: '不涉及',
                    action: 'allow',
                    name: '暴露全部端口到公网和内网'
                }
            ]
        }
    },
    {
        alias: '3',
        text: '入站和出站允许所有访问请求',
        value: '3',
        temp: {
            in: [
                {
                    ethertype: SecurityIpVersion.IPV4,
                    protocol: 'all',
                    portRange: '1-65535',
                    priority: '1000',
                    localPortRange: '不涉及',
                    action: 'allow',
                    name: '暴露全部端口到公网和内网'
                },
                {
                    ethertype: SecurityIpVersion.IPV6,
                    protocol: 'all',
                    portRange: '1-65535',
                    priority: '1000',
                    localPortRange: '不涉及',
                    action: 'allow',
                    name: '暴露全部端口到公网和内网'
                }
            ],
            out: [
                {
                    ethertype: SecurityIpVersion.IPV4,
                    protocol: 'all',
                    portRange: '1-65535',
                    priority: '1000',
                    localPortRange: '不涉及',
                    action: 'allow',
                    name: '暴露全部端口到公网和内网'
                },
                {
                    ethertype: SecurityIpVersion.IPV6,
                    protocol: 'all',
                    portRange: '1-65535',
                    priority: '1000',
                    localPortRange: '不涉及',
                    action: 'allow',
                    name: '暴露全部端口到公网和内网'
                }
            ]
        }
    }
);

export const diagnosisStatus = new Enum(
    {
        alias: 'DIAGNOSING',
        text: '诊断中',
        value: 'diagnosing',
        styleClass: 'status warning'
    },
    {
        alias: 'DIAGNOSED',
        text: '诊断完成',
        value: 'diagnosed',
        styleClass: 'status normal'
    },
    {
        alias: 'DIAGNOSE_FAIL',
        text: '诊断失败',
        value: 'diagnose_fail',
        styleClass: 'status warning'
    }
);

export const diagnosisInstanceType = new Enum(
    {
        alias: 'CSN',
        text: '云智能网',
        value: 'csn'
    },
    {
        alias: 'EIP',
        text: '弹性公网IP',
        value: 'eip'
    },
    {
        alias: 'ETCHANNEL',
        text: '专线通道',
        value: 'etChannel'
    },
    {
        alias: 'NAT',
        text: 'NAT网关',
        value: 'nat'
    },
    {
        alias: 'VPN',
        text: 'VPN网关',
        value: 'vpn'
    }
);

export const PAY_METHOD_FILED = new Enum(
    {alias: 'BYBANDWIDTH', text: '按日峰值带宽', value: 'ByBandwidth'},
    {alias: 'BYTRAFFIC', text: '按流量', value: 'ByTraffic'},
    {alias: 'BANDWIDTH', text: '按带宽', value: 'bandwidth'},
    {alias: 'PEAKBANDWIDTH_PERCENT_95', text: '按传统型95计费', value: 'PeakBandwidth_Percent_95'},
    {alias: 'ENHANCED_PERCENT_95', text: '按增强型95计费', value: 'Enhanced_Percent_95'}
);

export const CSN_BP_STATUS = new Enum(
    {alias: 'AVAILABLE', text: '可用', value: 'available', kclass: 'status normal'},
    {alias: 'CREATING', text: '创建中', value: 'creating', kclass: 'status warning'},
    {alias: 'STOPPED', text: '已到期', value: 'stopped', kclass: 'status unavailable'},
    {alias: 'AUDITING', text: '审核中', value: 'auditing', kclass: 'status error'},
    {alias: 'AUDIT_FAILED', text: '审核失败', value: 'audit_failed', kclass: 'status error'},
    {alias: 'AUDITOR_PAUSE', text: '暂停', value: 'auditor_pause', styleClass: 'status unavailable'},
    {alias: 'AUDITOR_PAUSE_DOWN', text: '暂停到期', value: 'auditor_pause_down', styleClass: 'status warning'}
);

export const INSTANCE_STATUS = {
    available: 'available',
    binded: 'binded',
    pausing: 'pausing',
    paused: 'paused',
    unbinding: 'unbinding',
    binding: 'binding',
    stoped: 'stoped',
    stopped: 'stopped',
    expired: 'expired',
    creating: 'creating',
    updating: 'updating',
    unavailable: 'unavailable',
    recovering: 'recovering',
    deleting: 'deleting',
    bound: 'bound',
    billingChanging: 'billing_changing',
    error: 'error',
    CAN_RELEASEPOSTPAY: ['available', 'stoped', 'paused'],
    CAN_RELEASEPREPAY: ['expired'],
    CAN_UPGRADE: ['available', 'binded'],
    CAN_UNBIND: ['binded', 'stoped', 'expired'],
    CANNOT_BIND: ['stoped', 'expired'],
    TXT: {
        available: '可用',
        binded: '已绑定',
        bound: '已绑定',
        pausing: '停止中',
        paused: '封禁中',
        unbinding: '解绑中',
        binding: '绑定中',
        stoped: '已欠费',
        stopped: '已欠费',
        expired: '已过期',
        creating: '创建中',
        updating: '更新中',
        unavailable: '暂不可用',
        recovering: '恢复中',
        deleting: '删除中',
        billingChanging: '计费变更中',
        error: '失败'
    },
    CLASS: {
        available: 'status normal',
        binded: 'status normal',
        bound: 'status normal',
        pausing: 'status warning',
        paused: 'status unavailable',
        unbinding: 'status warning',
        binding: 'status warning',
        stoped: 'status error',
        stopped: 'status error',
        expired: 'status error',
        creating: 'status warning',
        updating: 'status warning',
        unavailable: 'status unavailable',
        recovering: 'status warning',
        deleting: 'status warning',
        billingChanging: 'status warning'
    }
};
