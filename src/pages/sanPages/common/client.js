/**
 * client.js
 *
 * @file client.js
 * <AUTHOR>
 */

import {decorators, HttpClient} from '@baiducloud/runtime';
import {API_NETWORK_PREFIX, API_NETWORK_PREFIX_V2} from '../utils/config';
import FLAG from '@/flags';
import ResponsePlugin from './responsePlugin';
import RequestPlugin from './requestPlugin';

@decorators.asService('$http')
export default class Client extends HttpClient {
    constructor() {
        const CSRFToken = true;
        super({CSRFToken}, window.$context);
        this.req.use(RequestPlugin());
        this.res.use(ResponsePlugin());
    }
    /* vpc实例 */
    vpcList(payload, config = {}) {
        return this.post(`${API_NETWORK_PREFIX}/vpcs`, payload, config);
    }
    getAvailAbleRegion() {
        return this.post('/api/region/available');
    }
    vpcSubnetList(payload) {
        return this.post(`${API_NETWORK_PREFIX}/subnet/list`, payload);
    }
    vpcSubnetPageList(payload) {
        return this.post(`${API_NETWORK_PREFIX}/subnet/pageList`, payload);
    }
    getVpcDetailInfo(vpcId, option = {}) {
        return this.get(`${API_NETWORK_PREFIX}/vpc/detail/${vpcId}`, {}, option);
    }
    vpcUpdate(payload) {
        return this.post(`${API_NETWORK_PREFIX}/vpc/update`, payload);
    }
    auxiliaryCidrQuota(vpcId) {
        return this.get(`${API_NETWORK_PREFIX}/vpc/${vpcId}/auxiliaryCidrQuota`);
    }
    vpcDelete(payload) {
        return this.post(`${API_NETWORK_PREFIX}/vpcs/delete`, payload);
    }
    vpcCreate(payload) {
        return this.post(`${API_NETWORK_PREFIX}/vpc/create`, payload);
    }
    vpcInfo(payload) {
        return this.post(`${API_NETWORK_PREFIX}/vpcMap`, payload);
    }
    vpcQuota(payload) {
        return this.post(`${API_NETWORK_PREFIX}/vpc/quota`, payload);
    }
    vpcTagAssign(payload) {
        return this.post(`${API_NETWORK_PREFIX}/tag/assign`, payload);
    }
    getVpcDetail(payload) {
        return this.post(`${API_NETWORK_PREFIX}/vpcMap`, payload);
    }
    vpcCommonWhiteList(payload, config) {
        return this.post(`${API_NETWORK_PREFIX}/commonWhiteList`, payload, config);
    }
    commonVpcWhiteList(payload, config) {
        return this.post(`${API_NETWORK_PREFIX}/common/whiteLists`, payload, config);
    }
    commonBlbWhiteList(payload, config) {
        return this.post('/api/blb/commonWhiteLists', payload, config);
    }
    updateDomain(vpcId, payload) {
        return this.put(`${API_NETWORK_PREFIX}/vpc/${vpcId}/domain`, payload);
    }
    createDomain(vpcId, payload) {
        return this.post(`${API_NETWORK_PREFIX}/vpc/${vpcId}/domain`, payload);
    }
    deleteDomain(vpcId, payload) {
        return this.delete(`${API_NETWORK_PREFIX}/vpc/${vpcId}/domain`, payload);
    }
    createDnsServer(vpcId, payload) {
        return this.post(`${API_NETWORK_PREFIX}/vpc/${vpcId}/dnsServer`, payload);
    }
    updateDnsServer(vpcId, payload) {
        return this.put(`${API_NETWORK_PREFIX}/vpc/${vpcId}/dnsServer`, payload);
    }
    deleteDnsServer(vpcId, payload) {
        return this.delete(`${API_NETWORK_PREFIX}/vpc/${vpcId}/dnsServer`, payload);
    }
    blbCommonWhiteList(param, options = {}) {
        return this.post('/api/blb/commonWhiteList', param);
    }
    getZoneList() {
        return this.post('/api/zone/list/v2');
    }
    ipv6GatewayWhiteList(payload) {
        return this.post(`${API_NETWORK_PREFIX}/commonWhiteList`, payload);
    }
    createSubnets(payload) {
        return this.post(`${API_NETWORK_PREFIX}/subnet/batch/create`, payload);
    }
    subnetBatchCreateQuota() {
        return this.get(`${API_NETWORK_PREFIX}/subnet/batch/create/quota`);
    }
    subnetConflictValidate(payload) {
        return this.post(`${API_NETWORK_PREFIX}/subnet/conflictValidate`, payload);
    }
    checkBroadcast(payload) {
        return this.get(`${API_NETWORK_PREFIX}/vpc/enableBroadcast?vpcId=${payload}`);
    }
    broadcastWhitelist(payload) {
        return this.get(`${API_NETWORK_PREFIX}/vpc/broadcastRegionWhiteList`, payload);
    }
    // 删除vpc的前置校验
    checkVpcBeforeDelete(payload) {
        return this.post(`${API_NETWORK_PREFIX}/vpc/resource/check`, payload);
    }
    // 删除子网前置校验
    checkSubnetBeforeDelete(payload) {
        return this.post(`${API_NETWORK_PREFIX}/subnet/resource/check`, payload);
    }

    // 子网变更可用区
    changeAz(payload) {
        return this.put(`${API_NETWORK_PREFIX}/subnet/change_az`, payload);
    }

    /* 安全组 */

    securityListV3(payload) {
        return this.post(`${API_NETWORK_PREFIX}/security/v3/list`, payload);
    }
    securityDelete(payload) {
        return this.post(`${API_NETWORK_PREFIX}/security/delete`, payload);
    }
    securityBatchDelete(payload) {
        return this.post(`${API_NETWORK_PREFIX}/security/batch_delete`, payload);
    }
    securityCreate(payload) {
        return this.post(`${API_NETWORK_PREFIX}/security/create`, payload);
    }
    securityCopy(payload, options) {
        return this.post(`${API_NETWORK_PREFIX}/security/copy`, payload, {headers: options});
    }
    securityCopyNew(payload) {
        return this.post(`${API_NETWORK_PREFIX}/security/copySg`, payload);
    }
    securityUpdateField(payload) {
        return this.post(`${API_NETWORK_PREFIX}/security/update_field`, payload);
    }
    securityRuleQuota(payload) {
        return this.post(`${API_NETWORK_PREFIX}/security/inRuleQuota`, payload);
    }
    getSecurityListV2(payload) {
        return this.post(`${API_NETWORK_PREFIX}/security/v2/list`, payload);
    }
    getSecurityDetailV2(payload, option = {}) {
        return this.post(`${API_NETWORK_PREFIX_V2}/security/detail`, payload, option);
    }
    securityDeleteRule(payload) {
        return this.put(`${API_NETWORK_PREFIX}/security/rule/batchDelete`, payload);
    }
    securityAddRule(payload) {
        return this.put(`${API_NETWORK_PREFIX}/security/rule/add`, payload);
    }
    securityBatchUnbindInstance(payload) {
        return this.post(`${API_NETWORK_PREFIX}/security/batch/unbind`, payload);
    }

    securityUpdateRule(payload) {
        return this.put(`${API_NETWORK_PREFIX}/security/rule/v2/update`, payload);
    }
    securityCopyRule(payload) {
        return this.get(`${API_NETWORK_PREFIX}/security/securityGroup/rule/copy/` + payload.securityGroupId);
    }
    getSecurityDetail(payload) {
        return this.post(`${API_NETWORK_PREFIX}/security/detail`, payload);
    }
    updateSecurityField(payload) {
        return this.post(`${API_NETWORK_PREFIX}/security/update_field`, payload);
    }

    getSecurityPreview(payload) {
        return this.post(`${API_NETWORK_PREFIX}/security/previewRules`, payload);
    }

    getUnbindInstanceList(payload) {
        let queryStr = '';
        queryStr +=
            '?serverType=' +
            payload.serverType +
            '&securityGroupId=' +
            payload.securityGroupId +
            '&vpcId=' +
            payload.vpcId +
            '&pageNo=' +
            payload.pageNo +
            '&pageSize=' +
            payload.pageSize +
            '&order=' +
            payload.order +
            '&orderBy=' +
            payload.orderBy;
        if (payload.keyword) {
            queryStr += '&keywordType=' + payload.keywordType + '&keyword=' + payload.keyword;
        }
        return this.get(`${API_NETWORK_PREFIX}/security/unbind/server/list` + queryStr);
    }
    getUnbindEniList(payload) {
        let queryStr = '';
        queryStr +=
            '?securityGroupId=' +
            payload.securityGroupId +
            '&vpcId=' +
            payload.vpcId +
            '&pageNo=' +
            payload.pageNo +
            '&pageSize=' +
            payload.pageSize +
            '&order=' +
            payload.order +
            '&orderBy=' +
            payload.orderBy;
        if (payload.keyword) {
            queryStr += '&keywordType=' + payload.keywordType + '&keyword=' + payload.keyword;
        }
        return this.get(`${API_NETWORK_PREFIX}/security/unbind/eni/list` + queryStr);
    }
    getUnbindSecInstanceList(payload) {
        let queryStr = '';
        queryStr +=
            '?serverType=' +
            payload.serverType +
            '&securityGroupId=' +
            payload.securityGroupId +
            '&vpcId=' +
            payload.vpcId +
            '&pageNo=' +
            payload.pageNo +
            '&pageSize=' +
            payload.pageSize +
            '&order=' +
            payload.order +
            '&orderBy=' +
            payload.orderBy;
        if (payload.keyword) {
            queryStr += '&keywordType=' + payload.keywordType + '&keyword=' + payload.keyword;
        }
        return this.get(`${API_NETWORK_PREFIX}/security/unbind/instance/list` + queryStr);
    }
    getUnbindSnicList(payload) {
        let queryStr = '';
        queryStr +=
            '?serverType=' +
            payload.serverType +
            '&securityGroupId=' +
            payload.securityGroupId +
            '&vpcId=' +
            payload.vpcId +
            '&pageNo=' +
            payload.pageNo +
            '&pageSize=' +
            payload.pageSize +
            '&order=' +
            payload.order +
            '&orderBy=' +
            payload.orderBy;
        if (payload.keyword) {
            queryStr += '&keywordType=' + payload.keywordType + '&keyword=' + payload.keyword;
        }
        return this.get(`${API_NETWORK_PREFIX}/security/unbind/instance/list` + queryStr);
    }
    endpointBatchBindSecurity(payload) {
        return this.post(`${API_NETWORK_PREFIX}/security/instance/batch/bind`, payload);
    }
    unbindEndpointSecurity(payload) {
        return this.post(`${API_NETWORK_PREFIX}/security/instance/batch/unbind`, payload);
    }
    getBindInstanceList(payload) {
        let queryStr = '';
        queryStr +=
            '?serverType=' +
            payload.serverType +
            '&securityGroupId=' +
            payload.securityGroupId +
            '&vpcId=' +
            payload.vpcId +
            '&pageNo=' +
            payload.pageNo +
            '&pageSize=' +
            payload.pageSize +
            '&orderBy=' +
            payload.orderBy +
            '&order=' +
            payload.order;
        if (payload.keyword) {
            queryStr += '&keywordType=' + payload.keywordType + '&keyword=' + payload.keyword;
        }
        return this.get(`${API_NETWORK_PREFIX}/security/bind/instance/list` + queryStr);
    }
    getWhiteRegion(payload, options) {
        return this.post(`${API_NETWORK_PREFIX}/commonRegionWhiteList`, payload, options);
    }
    securityBindInstance(payload) {
        return this.put(`${API_NETWORK_PREFIX}/security/append/bindInstance`, payload);
    }
    securityUpdate(payload) {
        return this.post(`${API_NETWORK_PREFIX}/security/update`, payload);
    }
    getRemoteSgRuleQuota({securityGroupUuid}) {
        return this.get(`${API_NETWORK_PREFIX}/security/remoteSgRuleQuota?securityGroupUuid=${securityGroupUuid}`);
    }
    getSearchTagList(payload) {
        return this.post('/api/tag/list', payload);
    }
    subnetQuota(payload) {
        return this.post(`${API_NETWORK_PREFIX}/subnet/quota`, payload);
    }
    subnetUpdate(payload) {
        return this.post(`${API_NETWORK_PREFIX}/subnet/update`, payload);
    }
    subnetCreate(payload) {
        return this.post(`${API_NETWORK_PREFIX}/subnet/create`, payload);
    }
    subnetBroadcastUpdate(payload) {
        return this.post(`${API_NETWORK_PREFIX}/subnet/update`, payload);
    }
    subnetDelete(payload) {
        return this.post(`${API_NETWORK_PREFIX}/subnet/delete`, payload);
    }
    securityQuota(payload) {
        return this.get(`${API_NETWORK_PREFIX}/security/quota?vpcId=` + payload.vpcId);
    }
    resourcegroup(payload) {
        return this.post('/api/resourcegroup/list', payload);
    }
    // 导入接口
    securityRuleUploader(payload) {
        return this.post(`${API_NETWORK_PREFIX}/security/uploader`, payload);
    }
    securityRuleUploaderCheck(payload) {
        return this.post(`${API_NETWORK_PREFIX}/security/check`, payload);
    }
    securityRuleImport(payload) {
        return this.post(`${API_NETWORK_PREFIX}/security/import`, payload);
    }
    // 绑定bbc、bcc返回topN
    getSecurityUnbindTopN(payload) {
        return this.get(`${API_NETWORK_PREFIX}/security/unbind/server/topN`, payload);
    }
    // ACL
    getAclList(url) {
        return this.get(`${API_NETWORK_PREFIX}/acl/list?` + url);
    }
    updateAclName(payload) {
        return this.put(`${API_NETWORK_PREFIX}/acl/update`, payload);
    }
    getAclDetail(payload, option = {}) {
        return this.get(`${API_NETWORK_PREFIX}/acl/detail`, payload, option);
    }
    oldGetAcl(payload) {
        return this.post(`${API_NETWORK_PREFIX}/acl`, payload);
    }
    aclUpload(payload) {
        return this.post(`${API_NETWORK_PREFIX}/acl/upload`, payload);
    }
    aclRuleDelete(payload) {
        return this.post(`${API_NETWORK_PREFIX}/aclrule/delete`, payload);
    }
    aclRuleQuota(payload) {
        return this.post(`${API_NETWORK_PREFIX}/acl/rule/quota`, payload);
    }
    aclRuleUpdate(payload) {
        return this.post(`${API_NETWORK_PREFIX}/aclrule/update`, payload);
    }
    aclRuleCreate(payload) {
        return this.post(`${API_NETWORK_PREFIX}/aclrule/create`, payload);
    }
    // route
    checkCanShutDownRelayVpc(payload) {
        return this.post(`${API_NETWORK_PREFIX}/vpc/checkCanShutDownRelayVpc`, payload);
    }
    shutDownRelay(payload) {
        return this.post(`${API_NETWORK_PREFIX}/vpc/shutDownRelay`, payload);
    }
    openRelay(payload) {
        return this.post(`${API_NETWORK_PREFIX}/vpc/openRelay`, payload);
    }
    updateRouteName(payload) {
        return this.post(`${API_NETWORK_PREFIX}/route/routeTable/update`, payload);
    }
    routeTableList(payload, option = {}) {
        return this.post(`${API_NETWORK_PREFIX}/route/routeTable/pageList`, payload, option);
    }
    rulePageList(payload, option = {}) {
        return this.post(`${API_NETWORK_PREFIX}/rule/pagelist`, payload, option);
    }
    ruleSwitch(payload) {
        return this.post(`${API_NETWORK_PREFIX}/rule/haswitch`, payload);
    }
    ruleDelete(payload) {
        return this.post(`${API_NETWORK_PREFIX}/rule/delete`, payload);
    }
    ruleQuota(payload, option = {}) {
        return this.post(`${API_NETWORK_PREFIX}/rule/quota`, payload, option);
    }
    ruleUpdate(payload) {
        return this.post(`${API_NETWORK_PREFIX}/rule/update`, payload);
    }
    ruleCreate(payload) {
        return this.post(`${API_NETWORK_PREFIX}/rule/create`, payload);
    }
    getGwQuota(id, ipType) {
        if (ipType) {
            return this.get(`${API_NETWORK_PREFIX}/rule/ecmpGroupQuota?routeTableId=${id}&ipVersion=${ipType}`);
        } else {
            return this.get(`${API_NETWORK_PREFIX}/rule/ecmpGroupQuota?routeTableId=${id}`);
        }
    }
    getNatList(payload, options = {}) {
        return this.post('/api/nat/list', payload, options);
    }
    getNatListV2(payload, options = {}) {
        return this.post('/api/nat/v2/list', payload, options);
    }
    peerconnList(payload) {
        return this.post('/api/peerconn/peerconn/list', payload);
    }
    dcgwList(payload, option) {
        return this.post(`${API_NETWORK_PREFIX}/dc/gw/list`, payload, option);
    }
    bccInstanceSubnetsList(payload, option = {}) {
        return this.post('/api/bcc/instance/list/subnets', payload, option);
    }
    ipv6Detail(payload) {
        return this.post('/api/ipv6gw/detail', payload);
    }
    openSourceWhiteList(payload = {}) {
        return this.post(`${API_NETWORK_PREFIX}/rule/openSourceWhiteList`, payload);
    }
    routeEniWhiteList() {
        return this.get(`${API_NETWORK_PREFIX}/route/eniRouteRegionWhiteList`);
    }
    getRouteV6Quota(payload, option = {}) {
        return this.post(`${API_NETWORK_PREFIX}/ipv6Rule/quota`, payload, option);
    }
    customList(vpcUuid, keyword, keywordType, instanceType) {
        return this.get(
            `${API_NETWORK_PREFIX}/route/listNexthopServer?vpcUuid=${vpcUuid}&keyword=${keyword}&keywordType=${keywordType}&instanceType=${instanceType}`
        );
    }
    createCustomeRoute(payload) {
        return this.post(`${API_NETWORK_PREFIX}/route/route_table`, payload);
    }
    deleteCustomeRoute({routeTableId}) {
        return this.delete(`${API_NETWORK_PREFIX}/route/route_table/${routeTableId}`);
    }
    getCustomVpcDetail({vpcId}) {
        return this.get(`${API_NETWORK_PREFIX}/vpc/${vpcId}?needCustomRouteTableInfo=true`);
    }
    bindResource(payload) {
        return this.put(`${API_NETWORK_PREFIX}/route/route_table?action=bind`, payload);
    }
    unBindResource(payload) {
        return this.put(`${API_NETWORK_PREFIX}/route/route_table?action=unbind`, payload);
    }
    getCsnTgwList({vpcId}, option = {}) {
        return this.get(`${API_NETWORK_PREFIX}/route/tgw?vpcId=${vpcId}`, {}, (option = {}));
    }
    getCustomRouteQuota({vpcUuid}) {
        return this.get(`${API_NETWORK_PREFIX}/route/route_table/quota?vpcUuid=${vpcUuid}`);
    }
    getTgwList({csnId}) {
        return this.get(`${API_NETWORK_PREFIX}/route/tgw?csnId=${csnId}`);
    }
    replaceBindRoute(payload) {
        return this.put(`${API_NETWORK_PREFIX}/route/route_table?action=update`, payload);
    }
    // endpoint
    createEndpoint(payload) {
        return this.post('/api/snic/endpoint/create', payload);
    }
    getEndpointQuota(payload) {
        return this.post('/api/snic/endpoint/quota', payload);
    }
    getEndpointList(payload) {
        return this.post('/api/snic/endpoint/list', payload);
    }
    getEndpointSecurity(payload) {
        return this.post(`${API_NETWORK_PREFIX}/security/getSgListByInstanceId`, payload);
    }
    getAllEndpoint(payload) {
        return this.post('/api/snic/endpoints', payload);
    }
    updateEndpoint(payload) {
        return this.post('/api/snic/endpoint/update', payload);
    }
    getEndpointDetail(payload, option = {}) {
        return this.post('/api/snic/endpoint/detail', payload, option);
    }
    deleteEndpoint(id, payload) {
        return this.put(`/api/snic/endpoint/delete/${id}`, payload);
    }
    endpointEipBind(endpointId, action, payload) {
        return this.put(`/api/snic/endpoint/${endpointId}?action=${action}`, payload);
    }
    getEndpointPrice(payload) {
        return this.post('/api/snic/order/price', payload);
    }
    getAlarmSummary(payload) {
        return this.post('/api/bcm/alarm/state/summary', payload);
    }
    bcmMetricDataMetricName(params) {
        return this.post('/api/bcm/metricdata/v2/datas/metricname', params);
    }
    getServiceList() {
        return this.post('/api/snic/service/list');
    }
    updateNat(payload) {
        return this.post('/api/nat/update', payload);
    }
    natCancelAlterProductType(payload) {
        return this.post('/api/nat/cancelChangingCharging', payload);
    }
    natRelease(payload) {
        return this.post('/api/nat/release', payload);
    }
    natBlackList() {
        return this.post('/api/nat/blackList');
    }
    // 增强型IPv6
    releaseNatIPv6({natId}) {
        return this.delete(`/api/nat/ipv6/${natId}/release`);
    }
    updateNatIPv6(payload) {
        return this.put('/api/nat/ipv6/update', payload);
    }
    bindEipNatIPv6(payload) {
        return this.post('/api/nat/ipv6/eip/bind', payload);
    }
    unbindEipNatIPv6(payload) {
        return this.post('/api/nat/ipv6/eip/unbind', payload);
    }
    createIPv6SnatRule(payload) {
        return this.post('/api/nat/ipv6/snatRule/create', payload);
    }
    queryIPv6SnatRuleList(payload) {
        return this.post('/api/nat/ipv6/snatRule/list', payload);
    }
    updateIPv6SnatRule(payload) {
        return this.put('/api/nat/ipv6/snatRule/update', payload);
    }
    queryIPv6SnatRuleQuota(payload, options = {}) {
        return this.get(`/api/nat/ipv6/snat/quota?natId=${payload}`, {}, options);
    }
    deleteIPv6SnatRule(payload) {
        return this.post('/api/nat/ipv6/snatRule/batchDelete', payload);
    }
    createIPv6DnatRule(payload) {
        return this.post('/api/nat/ipv6/dnatRule/create', payload);
    }
    queryIPv6DnatRuleList(payload) {
        return this.post('/api/nat/ipv6/dnatRule/list', payload);
    }
    updateIPv6DnatRule(payload) {
        return this.put('/api/nat/ipv6/dnatRule/update', payload);
    }
    queryIPv6DnatRuleQuota(payload, options = {}) {
        return this.get(`/api/nat/ipv6/dnat/quota?natId=${payload}`, {}, options);
    }
    deleteIPv6DnatRule(payload) {
        return this.post('/api/nat/ipv6/dnatRule/batchDelete', payload);
    }
    getVpcIPv6NatQuota({id}) {
        return this.get(`/api/nat/ipv6/vpcQuota?vpcUuid=${id}`);
    }
    getIPv6NatDetail({natId}) {
        return this.get(`/api/nat/ipv6/${natId}/detail`);
    }

    // 私网NAT
    getPrivateNatList(payload, options = {}) {
        return this.post('/api/intranet/nat/list', payload, options);
    }
    getPrivateNatDetail({natId}) {
        return this.get(`/api/intranet/nat/${natId}/detail`);
    }
    updatePrivateNat(payload) {
        return this.put('/api/intranet/nat/update', payload);
    }
    releasePrivateNat({natId}) {
        return this.delete(`/api/intranet/nat/${natId}/release`);
    }
    getNatIpCidr({natId}, options = {}) {
        return this.get(`/api/intranet/nat/${natId}/segments`, {}, options);
    }
    createNatIpCidr(payload) {
        return this.post('/api/intranet/nat/segments/create', payload);
    }
    updateNatIpCidr(payload) {
        return this.put('/api/intranet/nat/segments/update', payload);
    }
    createNatIp(payload) {
        return this.post('/api/intranet/nat/natIp/create', payload);
    }
    deleteNatIpCidr({natId, segmentId}) {
        return this.delete(`/api/intranet/nat/${natId}/segment/${segmentId}/release`);
    }
    getCidrQuotaPerPrivateNat({natId}, options = {}) {
        return this.get(`/api/intranet/nat/segment/quota?natId=${natId}`, {}, options);
    }
    getVpcPrivateNatQuota({id}) {
        return this.get(`/api/intranet/nat/vpcQuota?vpcUuid=${id}`);
    }
    getUserPrivateNatQuota() {
        return this.get('/api/intranet/nat/userQuota');
    }
    getNatIpQuota({natId, segmentId}, options = {}) {
        return this.get(`/api/intranet/nat/natIp/quota?natId=${natId}&segmentId=${segmentId}`, {}, options);
    }
    getNatIpList(payload) {
        return this.post('/api/intranet/nat/natIp/list', payload);
    }
    deleteNatIp(payload) {
        return this.post('/api/intranet/nat/natIp/delete', payload);
    }
    updateNatIp(payload) {
        return this.put('/api/intranet/nat/natIp/update', payload);
    }
    getNatListForRoute({vpcId}) {
        return this.get(`/api/nat/listForRoute?vpcId=${vpcId}`);
    }
    ipv6gwList(payload) {
        return this.post('/api/ipv6gw/list', payload);
    }
    ipv6gwCancelAlterProductType(payload) {
        return this.post('/api/ipv6gw/order/confirm/cancel_to_postpay', payload);
    }
    ipv6gwDelete(payload) {
        return this.post('/api/ipv6gw/delete', payload);
    }
    ipv6gwVpcList() {
        return this.post('/api/ipv6gw/vpc_list');
    }
    purchaseValidation(payload, options) {
        return this.post('/api/account/purchase_validation', payload, options);
    }
    getIpv6BandwidthQuota() {
        return this.get('/api/ipv6gw/ipv6BandwidthQuota');
    }
    ipv6gwPrice(payload) {
        return this.post('/api/ipv6gw/order/price', payload);
    }
    ipv6gwDetail(payload, option = {}) {
        return this.post('/api/ipv6gw/detail', payload, option);
    }
    ipv6gwResizePrice(payload) {
        return this.post('/api/ipv6gw/order/resize_price', payload);
    }
    ipv6gwQosQuota(payload) {
        return this.post('/api/ipv6gw/qos/quota', payload);
    }
    ipv6gwSegmentQuota(payload) {
        return this.post('/api/ipv6gw/segment/quota', payload);
    }
    ipv6gwQosDelete(payload) {
        return this.post('/api/ipv6gw/qos/batch_delete', payload);
    }
    ipv6SegmentDelete(payload) {
        return this.post('/api/ipv6gw/segment/delete', payload);
    }
    ipv6gwQosUpdate(payload) {
        return this.post('/api/ipv6gw/qos/update', payload);
    }
    ipv6gwSegmentCreate(payload) {
        return this.post('/api/ipv6gw/segment/create', payload);
    }
    ipv6gwUpdate(payload) {
        return this.post('/api/ipv6gw/update', payload);
    }

    getEniWhiteList(payload, config) {
        return this.post('/api/enic/whiteList', payload, config);
    }
    getEniList(payload) {
        return this.post('/api/enic/list', payload);
    }
    eniEipBind(eniId, payload) {
        return this.put('/api/enic/' + eniId + '?action=bind', payload);
    }
    eniServerList(payload) {
        return this.post('/api/enic/serverList', payload);
    }
    detachEni(eniId, payload) {
        return this.put('/api/enic/' + eniId + '?action=detach', payload);
    }
    attachEni(eniId, payload) {
        return this.put('/api/enic/' + eniId + '?action=attach', payload);
    }
    deleteEni(eniId) {
        return this.delete('/api/enic/' + eniId);
    }
    updateEni(eniId, payload) {
        return this.put('/api/enic/' + eniId, payload);
    }
    getEniQuota(payload) {
        return this.post('/api/enic/quota', payload);
    }
    getSubnetList(payload, option = {}) {
        return this.post(`${API_NETWORK_PREFIX}/subnet/list`, payload, option);
    }
    getVpnSubnetList(payload, option = {}) {
        return this.get(`${API_NETWORK_PREFIX}/subnet/forCreateVpn`, payload, option);
    }
    getSubnetDetail(subnetId, option = {}) {
        return this.get(`${API_NETWORK_PREFIX}/subnet/` + subnetId, {}, option);
    }
    createEni(payload) {
        return this.post('/api/enic', payload);
    }
    getEniDetail(payload, option = {}) {
        return this.get('/api/enic/' + payload.eniId, {}, option);
    }
    getEniIpList(eniId, payload, option = {}) {
        return this.post('/api/enic/' + eniId + '/ip', payload, option);
    }
    getEipBindList(payload, param) {
        payload.status = 'available';
        if (payload.eipPurchaseType) {
            payload.keyword = payload.eipPurchaseType;
            payload.keywordType = 'ROUTE_TYPE';
        }
        delete payload.eipPurchaseType;
        let url = '/api/eip/list';
        param && (url = url + '?' + param);
        return this.post(url, payload);
    }
    getEipv6BindList(payload) {
        payload.status = 'available';
        if (payload.eipPurchaseType) {
            payload.keyword = payload.eipPurchaseType;
            payload.keywordType = 'ROUTE_TYPE';
        }
        delete payload.eipPurchaseType;
        let url = '/api/eip/list?ipVersion=ipv6';
        return this.post(url, payload);
    }
    createEniIp(eniId, payload) {
        return this.post('/api/enic/' + eniId + '/privateIp', payload);
    }
    deleteEniIp(eniId, privateIp) {
        return this.delete('/api/enic/' + eniId + '/privateIp/' + privateIp);
    }
    eniSecurityQuit(payload) {
        return this.post(`${API_NETWORK_PREFIX}/security/eni_quit_security_group`, payload);
    }
    getSecurityGroups(payload) {
        return this.get(`${API_NETWORK_PREFIX}/security/security_groups`, payload);
    }
    eniSecurityJoin(payload) {
        return this.post(`${API_NETWORK_PREFIX}/security/eni_join_security_groups`, payload);
    }
    getEipList(payload, param, options = {}) {
        let url = '/api/eip/list';
        param && (url = url + '?' + param);
        return this.post(url, payload, options);
    }
    getEipGroupList(payload) {
        return this.post('/api/eipgroup/list', payload);
    }
    natEipQuota(payload) {
        return this.post('/api/nat/eip/quota', payload);
    }
    getNatPrice(payload) {
        return this.post('/api/nat/order/price', payload);
    }
    getNatQuota(payload) {
        return this.post('/api/nat/quota', payload);
    }
    dnatRuleQuota(payload) {
        return this.post(`/api/nat/dnatRule/quota`, payload);
    }
    snatRuleQuota(payload) {
        return this.get(`/api/nat/${payload.natId}/snatRule/quota`);
    }
    getNatServerList(payload, option = {}) {
        return this.post('/api/nat/servers', payload, option);
    }
    getNatIpInstanceList(payload) {
        return this.post('/api/nat/instances', payload);
    }
    getNatResizePrice(payload) {
        return this.post('/api/nat/order/resizePrice', payload);
    }
    snatRuleList(payload, option = {}) {
        return this.get('/api/nat/' + payload.natGatewayId + '/snatRule', payload, option);
    }
    dnatRuleList(payload, option = {}) {
        return this.post('/api/nat/dnatRule/list', payload, option);
    }
    snatEipQuota(payload) {
        return this.post(' /api/nat/' + payload.natId + '/snatRule/eipQuota', {ruleId: payload.ruleId || ''});
    }
    snatCreate(payload) {
        return this.post('/api/nat/' + payload.natId + '/snatRule', payload);
    }
    editSnatRule(payload) {
        return this.put('/api/nat/' + payload.natId + '/snatRule/' + payload.ruleId, {
            name: payload.name,
            cidr: payload.cidr,
            eips: payload.eips
        });
    }
    natEipBind(payload) {
        return this.post('/api/nat/eip/bind', payload);
    }
    natEipUnbind(payload) {
        return this.post('/api/nat/eip/unbind', payload);
    }
    dnatUpdate(payload) {
        return this.post('/api/nat/dnatRule/update', payload);
    }
    dnatCreate(payload) {
        return this.post('/api/nat/dnatRule/create', payload);
    }
    deleteSnatRule(payload) {
        return this.delete(
            '/api/nat/' + payload.natId + '/snatRule',
            {},
            {
                data: {
                    ruleIds: payload.ruleIds
                }
            }
        );
    }
    ipv6gwQosCreate(payload) {
        return this.post('/api/ipv6gw/qos/create', payload);
    }
    deleteDnatRule(payload) {
        return this.post('/api/nat/dnatRule/delete', payload);
    }
    unbindEniIp(eniId, payload) {
        return this.put('/api/enic/' + eniId + '?action=unbind', payload);
    }

    // dcgw
    dcgwDetail(payload, option = {}) {
        return this.post(`${API_NETWORK_PREFIX}/dc/gw/detail`, payload, option);
    }
    dcgwUpdate(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/gw/update`, payload);
    }
    dcgwDelete(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/gw/delete`, payload);
    }
    dcgwUnbind(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/gw/unbind`, payload);
    }
    dcgwBind(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/gw/bind`, payload);
    }
    getDcgwQuota(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/gw/quota`, payload);
    }
    dcgwCreate(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/gw/create`, payload);
    }
    getBfdlist(dcphyId) {
        return this.get(`${API_NETWORK_PREFIX}/dc/phy/${dcphyId}/bfd`);
    }
    createBfd(dcphyId, channelId, payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/phy/${dcphyId}/channel/${channelId}/bfd`, payload);
    }
    editBfd(dcphyId, channelId, payload) {
        return this.put(`${API_NETWORK_PREFIX}/dc/phy/${dcphyId}/channel/${channelId}/bfd`, payload);
    }
    deleteBfd(dcphyId, channelId) {
        return this.delete(`${API_NETWORK_PREFIX}/dc/phy/${dcphyId}/channel/${channelId}/bfd`);
    }
    bfdWhitelist() {
        return this.post(`${API_NETWORK_PREFIX}/commonWhiteList`, {id: 'DcPhyChannelBfd'});
    }
    iamStsRoleQuery(payload, options) {
        return this.post('/api/iam/sts/role/query', payload, {headers: options});
    }
    iamStsRoleActivate(payload, options) {
        return this.post('/api/iam/sts/role/activate', payload, {headers: options});
    }
    dcgwSpeedQuota() {
        return this.get(`${API_NETWORK_PREFIX}/dc/gw/speed/quota`);
    }
    dcgwUpdateSubnets(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/gw/updatesubnets`, payload);
    }
    dcgwNatRuleQuota(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/gw/nat/rule/quota`, payload);
    }
    dcgwNatRuleDelete(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/gw/nat/rule/delete`, payload);
    }
    dcgwNatRuleList(payload) {
        let queryString =
            '?dcgwId=' +
            payload.dcgwId +
            '&type=' +
            payload.type +
            '&pageNo=' +
            payload.pageNo +
            '&pageSize=' +
            payload.pageSize +
            (payload.keyword
                ? '&keywordType=' + payload.keywordType + '&keyword=' + encodeURIComponent(payload.keyword)
                : '');
        return this.get(`${API_NETWORK_PREFIX}/dc/gw/nat/rule/list` + queryString);
    }
    dcgwNatRuleCreate(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/gw/nat/rule/create`, payload);
    }
    dcgwNatRuleUpdate(payload) {
        return this.put(`${API_NETWORK_PREFIX}/dc/gw/nat/rule/update`, payload);
    }
    getTagList(payload) {
        return this.post('/api/tag/list', payload);
    }
    getDelicatedLineChannel(dcphyId, option = {}) {
        return this.get(`${API_NETWORK_PREFIX}/dc/phy/${dcphyId}/channel`, {}, option);
    }
    userRejectChannel(payload) {
        return this.put(`${API_NETWORK_PREFIX}/dc/channel/user-reject`, payload);
    }

    /* vpn */
    getVpnList(payload, option) {
        return this.post('/api/vpn/vpn/list', payload, option);
    }
    vpnEipBind(payload) {
        return this.post('/api/vpn/vpn/bindEip', payload);
    }
    vpnEipUnbind(payload) {
        return this.post('/api/vpn/vpn/unbindEip', payload);
    }
    releaseVpn(vpnId) {
        return this.post('/api/vpn/vpn/release', {ids: [vpnId]});
    }
    vpnCancelAlterProductType(payload) {
        return this.post('/api/vpn/vpn/order/cancelChangingBilling', payload);
    }
    vpnUpdate(payload) {
        return this.post('/api/vpn/vpn/update', payload);
    }
    getVpnDetail(payload, option = {}) {
        return this.post('/api/vpn/vpn/show', payload, option);
    }
    vpnConnList(payload, region) {
        return this.post('/api/vpn/vpnConn/list', payload, {headers: {region}});
    }
    resetIPsec(vpnId, vpnConnId) {
        return this.put(`/api/vpn/${vpnId}/vpnConn/${vpnConnId}?reset`);
    }
    getVpnConnQuota(payload) {
        return this.post('/api/vpn/vpnconn/quota', payload);
    }
    vpnConnDelete(payload) {
        return this.post('/api/vpn/vpnConn/delete', payload);
    }
    getVpnPrice(payload) {
        return this.post('/api/vpn/vpn/order/price', payload);
    }
    vpnQuota(payload) {
        return this.post('/api/vpn/vpn/quota', payload);
    }
    getVpnConnDetail(payload) {
        return this.post('/api/vpn/vpnConn/show', payload);
    }
    vpnCidrQuota(payload) {
        return this.post('/api/vpn/vpn/cidrQuota', payload);
    }
    addVpnShoppingCart(payload) {
        return this.post('/api/vpn/vpn/order/confirm/new?orderType=NEW&shoppingCart=true', payload);
    }
    vpnConnCreate(payload) {
        return this.post('/api/vpn/vpnConn/create', payload);
    }
    vpnConnUpdate(payload) {
        return this.post('/api/vpn/vpnConn/update', payload);
    }
    vpnConnAccessList(vpnConnId) {
        return this.get(`/api/vpn/vpnConn/${vpnConnId}/access/list`);
    }
    vpnNatWhiteList() {
        return this.post(`${API_NETWORK_PREFIX}/commonWhiteList`, {id: 'VpnNatWhiteList'});
    }
    updateVpnNatRule(payload) {
        return this.post('/api/vpn/nat/rule/update', payload);
    }
    deleteVpnNatRule(payload) {
        return this.post('/api/vpn/nat/rule/batchdelete', payload);
    }
    createVpnNatRule(payload) {
        return this.post('/api/vpn/nat/rule/create', payload);
    }
    getVpnRuleList(payload) {
        return this.post('/api/vpn/nat/rule/list', payload);
    }
    getVpnRuleQuota(payload) {
        return this.post('/api/vpn/nat/rule/quota', payload);
    }
    addNatShoppingCart(payload) {
        return this.post('/api/nat/order/confirm/new?orderType=NEW&shoppingCart=true', payload);
    }
    getSslUserList(payload) {
        return this.post('/api/vpn/sslvpn/user/list', payload);
    }
    createSslUser(payload) {
        return this.post('/api/vpn/sslvpn/user', payload);
    }
    getSslUserQuota(vpnId) {
        return this.get(`/api/vpn/sslvpn/user/quota?vpnId=${vpnId}`);
    }
    editSslUser(vpnId, userId, payload) {
        return this.put(`/api/vpn/${vpnId}/sslvpn/user/${userId}`, payload);
    }
    deleteSslUser(vpnId, userId) {
        return this.delete(`/api/vpn/${vpnId}/sslvpn/user/${userId}`);
    }
    sslHkSinWhiteList() {
        return this.post(`${API_NETWORK_PREFIX}/commonWhiteList`, {id: 'HkgSinSslVpnWhite'});
    }
    getVpnRouteList(payload) {
        return this.get('/api/vpn/vpn/route/list', payload);
    }
    createBgpRoute(payload) {
        return this.post('/api/vpn/vpn/bgpRoute', payload);
    }
    deleteBgpRoute({vpnId, routeId}) {
        return this.delete(`/api/vpn/${vpnId}/bgpRoute/${routeId}`);
    }
    updateVpnBgp({vpnId, enableBgp}) {
        return this.put(`/api/vpn/vpn/${vpnId}/bgpRoute`, {enableBgp});
    }
    getAvailableZone({flavor = 'normal'}) {
        return this.get(`/api/vpn/zone/forCreate?flavor=${flavor}`);
    }
    /* 拓扑图 */
    getAllBlb(payload) {
        return this.post('/api/blb/instance/allBlbList', payload);
    }
    getBucketList(payload) {
        return this.post('/api/blb/instance/bos/listbuckets', payload, {'headers': {region: 'bj'}, 'x-silent': true});
    }
    getFolderList(payload) {
        return this.post('/api/blb/instance/bos/listbucketfolders', payload, {
            'headers': {region: 'bj'},
            'x-silent': true
        });
    }

    /* 流日志 */
    updateFlowlog(payload) {
        return this.post(`${API_NETWORK_PREFIX_V2}/flowlog/update`, payload);
    }
    flowlogList(payload = {}) {
        return this.post(`${API_NETWORK_PREFIX_V2}/flowlog/list`, payload);
    }
    enableFlowlog(payload) {
        return this.post(`${API_NETWORK_PREFIX_V2}/flowlog/enable`, payload);
    }
    getEnableNatList(payload) {
        return this.post(`${API_NETWORK_PREFIX_V2}/flowlog/nat/list`, payload);
    }
    getEnableResource(payload, option = {}) {
        return this.post(`${API_NETWORK_PREFIX_V2}/flowlog/available/resource/list`, payload, option);
    }
    loadResourceIds(url, payload = {}) {
        return this.post(url, payload);
    }
    getCollectors(payload, region) {
        return this.post('/api/bls/v2/log/logstore/list', payload, {'headers': {region}, 'x-silent': true});
    }
    createFlowlogCollector(payload, region) {
        return this.post('/api/bls/v2/log/logstore/create', payload, {'headers': {region}, 'x-silent': true});
    }
    getLogRegionList() {
        return this.get('/api/ld/log/server/storeregion');
    }
    getFlowlogQuota() {
        return this.get(`${API_NETWORK_PREFIX_V2}/flowlog/quota`);
    }
    deleteFlowlog(payload) {
        return this.post(`${API_NETWORK_PREFIX_V2}/flowlog/delete`, payload);
    }
    createFlowlog(payload) {
        return this.post(`${API_NETWORK_PREFIX}/flowlog`, payload);
    }
    getFlowlogBlbList(payload) {
        return this.post('/api/blb/allblbList', payload);
    }
    getFlowlogBlbPortList(payload) {
        return this.post('/api/blb/listener/list', payload);
    }
    getFlowlogAppBlbPortList(payload) {
        return this.post('/api/blb/appblb/listener/list', payload);
    }
    getFlowlogUsedResource(payload) {
        return this.post(`${API_NETWORK_PREFIX_V2}/flowlog/used/resource/list`, payload);
    }
    getKafkaRegion() {
        return this.get(`${API_NETWORK_PREFIX_V2}/flowlog/kafka/regions`);
    }
    getKafkaList(payload, option = {}) {
        return this.post(`${API_NETWORK_PREFIX_V2}/flowlog/kafka/clusters`, payload, option);
    }
    supportKafka(payload, region) {
        return this.post('/api/kafka/v2/order/create_check', payload, {headers: {region}});
    }
    getKafkaTopic(payload) {
        return this.post(`${API_NETWORK_PREFIX_V2}/flowlog/kafka/topics`, payload);
    }
    getCsnCrossPeerRegion({csnId, peerCloud}) {
        return this.get(`${API_NETWORK_PREFIX_V2}/flowlog/csn/${csnId}/tgw_peer/regions?peerCloud=${peerCloud}`);
    }

    /* 对等连接 */
    getPeerWaitList(payload) {
        return this.post('/api/peerconn/peerconn/waiting_list', payload);
    }
    peerAccept(payload) {
        return this.post('/api/peerconn/peerconn/accept', payload);
    }
    peerconnReject(payload) {
        return this.post('/api/peerconn/peerconn/reject', payload);
    }
    getPeerconnBandwidthQuota() {
        return this.get('/api/peerconn/maxbandwidth');
    }
    getPeerDetail(payload, option = {}) {
        return this.post('/api/peerconn/peerconn/detail', payload, option);
    }

    getCrossPeerPrice(payload) {
        return this.post('/api/peerconn/crossBorder/order/resize_price', payload);
    }

    upgradePeerBandWidth(payload) {
        return this.post('/api/peerconn/peerconn/alter_bandwidth', payload);
    }
    getPeerList(payload) {
        return this.post('/api/peerconn/peerconn/list', payload);
    }
    peerUpdate(payload) {
        return this.post('/api/peerconn/peerconn/update', payload);
    }
    deletePeer(payload) {
        return this.post('/api/peerconn/peerconn/delete', payload);
    }
    peerCancelAlterProductType(payload) {
        return this.post('/api/peerconn/peerconn/order/confirm/cancle_to_postpay?orderType=CANCLE_TO_POSTPAY', payload);
    }
    changeDnsStatus(payload) {
        return this.post('/api/peerconn/peerconn/dnsSync', payload);
    }
    // 跨境入口用户白名单接口
    crossPeerconnUserWhiteList() {
        return this.get('/api/peerconn/crossBorder/whiteList?id=CrossBorderWhiteList');
    }
    crossPeerconnAudit(payload) {
        return this.post('/api/peerconn/crossBorder/audit', payload, {headers: {region: 'bj'}});
    }
    addPeerShoppingCart(payload) {
        return this.post('/api/peerconn/peerconn/order/confirm/new?orderType=NEW&shoppingCart=true', payload);
    }
    // 用户查询跨境对等连接审核状态
    // 仅支持bj区域
    quertAuditStatus() {
        return this.get('/api/peerconn/crossBorder/audit', {}, {headers: {region: 'bj'}});
    }
    peerconnCheckVpc(payload) {
        return this.post('/api/peerconn/peerconn/check/peer_vpc', payload);
    }
    peerVpcList(payload, options) {
        return this.post('/api/peerconn/list_vpc_for_peer', payload, options);
    }
    peerconnQuota(payload, options) {
        return this.post('/api/peerconn/peerconn/allQuota', payload, options);
    }
    peerConnDetail(payload, options) {
        return this.post('/api/peerconn/peerconn/detail', payload, options);
    }
    // 跨境对等连接开通自动续费
    openCrossRegionAutoRenew(payload) {
        return this.post('/api/peerconn/crossborder/order/confirm/autorenew/open', payload);
    }
    // 跨境对等连接关闭自动续费
    closeCrossRegionAutoRenew(payload) {
        return this.post('/api/peerconn/crossborder/order/confirm/autorenew/close', payload);
    }

    /* ET专线 */
    getCrossUsers(payload) {
        return this.get('/api/peerconn/crossBorder/users', payload, {headers: {region: 'bj'}});
    }
    // 根据地域获得接入点、运营商以及端口规则的信息
    getApAddrList(payload) {
        return this.get(`${API_NETWORK_PREFIX}/dc/phy/ap/${payload}`);
    }
    // 对等连接列表
    getCrossPeerconns(payload) {
        return this.get('/api/peerconn/crossBorder/peerconns', payload, {headers: {region: 'bj'}});
    }
    // 用户资格的审核
    auditUser(accountId, payload) {
        return this.put('/api/peerconn/crossBorder/audit/' + accountId, payload, {headers: {region: 'bj'}});
    }
    // 对等连接的审核
    auditCrossDc(peerconnId, payload) {
        return this.put('/api/peerconn/crossBorder/' + peerconnId, payload);
    }
    // 获取证件扫描件
    getCertificate(certificate) {
        return this.get(`/api/peerconn/crossBorder/audit/certificate/${certificate}`, {}, {headers: {region: 'bj'}});
    }
    // 联通白名单接口
    crossPeerconnWhiteList(payload, config) {
        return this.get('/api/peerconn/crossBorder/whiteList?id=ChinaUnicomWhiteList', payload, config);
    }
    // 流量镜像白名单接口
    getMirrorWhiteList(payload, config) {
        return this.post('/api/mirror/mirrorWhiteList', payload, config);
    }
    bandWidthQuota() {
        return this.get(`${API_NETWORK_PREFIX}/dc/gw/speed/quota`);
    }
    getBaiduWhiteList(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/baiduwhitelist`, payload);
    }
    dcIsEnterprise(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/enterprise`, payload);
    }
    // 专线套餐列表
    getDcMenuList(payload) {
        return this.get(`${API_NETWORK_PREFIX}/dc/phy/menu`, payload);
    }
    dcCheckAccept(dcphyId, options) {
        return this.put(`${API_NETWORK_PREFIX}/dc/phy/${dcphyId}/check_accept`, {}, {headers: options});
    }
    getDcQuota(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/phy/quota`, payload);
    }
    getDcInitialPrice(options) {
        return this.get(`${API_NETWORK_PREFIX}/dc/phy/order/init/price`, '', {headers: options});
    }
    getDcPortPrice(payload, options) {
        return this.post(`${API_NETWORK_PREFIX}/dc/phy/order/config/price`, payload, {headers: options});
    }
    getDcList(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/phy/list`, payload);
    }
    releaseDc(payload, options) {
        return this.post(`${API_NETWORK_PREFIX}/dc/phy/delete`, payload, {headers: options});
    }
    getDcDetail(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/phy/detail`, payload);
    }
    checkDcInfoWhite() {
        return this.get(
            `${API_NETWORK_PREFIX}/dc/dxm/info/whitelist`,
            {},
            {'headers': {region: 'bj'}, 'x-silent': true}
        );
    }
    getDcInfoList(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/dxm/info/list`, payload, {
            'headers': {region: 'bj'},
            'x-silent': true
        });
    }
    createDcInfo(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/dxm/info`, payload, {'headers': {region: 'bj'}, 'x-silent': true});
    }
    updateDcInfo(id, payload) {
        return this.put(`${API_NETWORK_PREFIX}/dc/dxm/info/${id}`, payload, {
            'headers': {region: 'bj'},
            'x-silent': true
        });
    }
    deleteDcInfo(id) {
        return this.delete(
            `${API_NETWORK_PREFIX}/dc/dxm/info/${id}`,
            {},
            {'headers': {region: 'bj'}, 'x-silent': true}
        );
    }
    dcUpdate(payload, options) {
        return this.post(`${API_NETWORK_PREFIX}/dc/phy/update`, payload, {headers: options});
    }
    dcChannelQuota(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/channel/quota`, payload);
    }
    channelList(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/channel/list`, payload);
    }
    channelDetele(payload, options = {}) {
        return this.post(`${API_NETWORK_PREFIX}/dc/channel/delete`, payload, options);
    }
    channelUpdate(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/channel/update`, payload);
    }
    checkAccount(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/check/accounts`, payload);
    }
    updateChannelUsers(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/channel/updateusers`, payload);
    }
    channelReCreate(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/channel/recreate`, payload);
    }
    channelCreate(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/channel/create`, payload);
    }
    getServerNatWhite(payload) {
        return this.get('/api/bcm/v1/csm/metricData/top', payload);
    }
    getBcmDimensionsV3(payload) {
        return this.post('/api/bcm/v3/csm/dimensions/values', payload);
    }
    editRoutes(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/channel/updateroutes`, payload);
    }
    vpcCheckPrivateIp(params) {
        return this.get(
            `${API_NETWORK_PREFIX}/vpc/` +
                params.vpcId +
                '/vpcIpUseInfo?privateIpAddresses=' +
                params.vpcPrivateIpAddresses
        );
    }
    channelAssociate(channelId, payload) {
        return this.put(`${API_NETWORK_PREFIX}/dc/channel/associate/` + channelId, payload);
    }
    crossChannelAssociate(channelId, payload) {
        return this.put(`${API_NETWORK_PREFIX}/dc/channel/associate/${channelId}?type=available`, payload);
    }
    channelDetail(channelId, payload, option) {
        return this.get(`${API_NETWORK_PREFIX}/dc/channel/${channelId}`, payload, option);
    }
    channelWhiteList() {
        return this.post(`${API_NETWORK_PREFIX}/commonWhiteList`, {id: 'LargeBandwidth'});
    }
    commonQuota(payload) {
        return this.post(`${API_NETWORK_PREFIX}/commonQuota`, payload);
    }
    getRouteStandardPrice(payload, options) {
        return this.post(`${API_NETWORK_PREFIX}/dc/phy/order/line/price`, payload, {headers: options});
    }
    routeStandardWhiteList() {
        return this.post(`${API_NETWORK_PREFIX}/commonWhiteList`, {id: 'LineBuild'});
    }
    getSubnetResourceDetail(subnetId, option = {}) {
        return this.get(`${API_NETWORK_PREFIX}/subnet/${subnetId}/resource`, {}, option);
    }
    getEnicSecondaryIpResourceWhite() {
        return this.get(`${API_NETWORK_PREFIX}/subnet/enicSecondaryIpResourceWhiteList`);
    }
    getSubnetIpList(payload, option = {}) {
        const {subnetUuid, ip, resourceType, pageNo, pageSize} = payload;
        return this.get(
            `${API_NETWORK_PREFIX}/subnet/ipUsedInfo?subnetUuid=${subnetUuid}&ip=${ip}&resourceType=${resourceType}&pageNo=${pageNo}&pageSize=${pageSize}`,
            {},
            option
        );
    }
    getSubnetReserveList(subnetId, payload) {
        return this.post(`${API_NETWORK_PREFIX}/subnet/${subnetId}/ipreserve/list`, payload);
    }
    reserveQuota(subnetId) {
        return this.get(`${API_NETWORK_PREFIX}/subnet/${subnetId}/ipreserve/quota`);
    }
    deleteReserve(id) {
        return this.delete(`${API_NETWORK_PREFIX}/subnet/ipreserve/${id}`);
    }
    addReserve(payload) {
        return this.post(`${API_NETWORK_PREFIX}/subnet/ipreserve`, payload);
    }
    createNatFlowlogs(payload) {
        return this.post(`${API_NETWORK_PREFIX_V2}/flowlog`, payload);
    }
    flowlogBuckets(payload) {
        return this.post(`${API_NETWORK_PREFIX_V2}/flowlog/list_bos_bucket_folders`, payload);
    }
    isOpenService() {
        return this.get('/api/ld/needConfirmOrder');
    }
    openZoneDns() {
        return this.post('/api/ld/order');
    }
    getNatLimitRule(natId, option = {}) {
        return this.get(`/api/nat/limitRule/${natId}`, {}, option);
    }
    addNatLimitRule(payload) {
        return this.post(`/api/nat/limitRule/create`, payload);
    }
    updateNatLimitRule(payload) {
        return this.put(`/api/nat/limitRule/update`, payload);
    }
    delNatLimitRule(payload) {
        return this.put(`/api/nat/limitRule/delete`, payload);
    }
    getVpcNetworkIp(payload, option = {}) {
        return this.get('/api/bcm/v1/csm/gateway/meta/all', payload, option);
    }
    getQosList(payload) {
        return this.post(`${API_NETWORK_PREFIX}/qos/list`, payload);
    }
    getQosDetail(qosId, option = {}) {
        return this.get(`${API_NETWORK_PREFIX}/qos/${qosId}`, {}, option);
    }
    getQosBind() {
        return this.get(`${API_NETWORK_PREFIX}/qos/bind/vpc`);
    }
    createQos(payload) {
        return this.post(`${API_NETWORK_PREFIX}/qos/`, payload);
    }
    updateQos(qosId, payload) {
        return this.put(`${API_NETWORK_PREFIX}/qos/${qosId}`, payload);
    }
    deleteQos(qosId) {
        return this.delete(`${API_NETWORK_PREFIX}/qos/${qosId}`);
    }
    createHealthCheck(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/gw/hc`, payload);
    }
    editHealthCheck(healthCheckId, payload) {
        return this.put(`${API_NETWORK_PREFIX}/dc/gw/hc/${healthCheckId}`, payload);
    }
    healthCheckList(dcgwId, payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/gw/${dcgwId}/hc/list`, payload);
    }
    healthCheckDetail(healthCheckId) {
        return this.get(`${API_NETWORK_PREFIX}/dc/gw/hc/${healthCheckId}`);
    }
    checkChannelHc(channelId) {
        return this.get(`${API_NETWORK_PREFIX}/dc/channel/${channelId}/hc`);
    }
    // 查询TGW实例
    getTgwInstanceList(csnId, payload) {
        return this.get(`/api/csn/${csnId}/tgw`, payload);
    }
    publishRoute(payload) {
        return this.post('/api/csn/route', payload);
    }

    cancelPublishRoute(payload) {
        return this.post('/api/csn/route/delete', payload);
    }

    csnList(url, option = {}) {
        return this.get('/api/csn?' + url, {}, option);
    }

    csnInstanceList(payload) {
        return this.get('/api/csn/instance', payload);
    }

    // 企业安全组接口
    enterpriseSecurityQuota(payload) {
        return this.get(`${API_NETWORK_PREFIX}/enterprise/security/quota`);
    }

    enterpriseSecurityRuleQuota(payload, option = {}) {
        return this.get(`${API_NETWORK_PREFIX}/enterprise/security/ruleQuota`, payload, option);
    }

    enterpriseSecurityCreate(payload) {
        return this.post(`${API_NETWORK_PREFIX}/enterprise/security/create`, payload);
    }

    updateEnterpriseSecurityField(payload) {
        return this.put(`${API_NETWORK_PREFIX}/enterprise/security/update_field`, payload);
    }

    enterpriseSecurityList(payload) {
        return this.post(`${API_NETWORK_PREFIX}/enterprise/security/list`, payload);
    }

    enterpriseSecurityCopy(payload) {
        return this.post(`${API_NETWORK_PREFIX}/enterprise/security/copy`, payload);
    }

    getEnterpriseSecurityDetail(payload, option = {}) {
        return this.get(`${API_NETWORK_PREFIX}/enterprise/security/detail`, payload, option);
    }

    getEnterpriseSecurityRule(payload, option = {}) {
        return this.get(`${API_NETWORK_PREFIX}/enterprise/security/rule`, payload, option);
    }

    enterpriseSecurityDelete(payload) {
        return this.post(`${API_NETWORK_PREFIX}/enterprise/security/batch_delete`, payload);
    }

    enterpriseSecurityDeleteRule(payload) {
        return this.post(`${API_NETWORK_PREFIX}/enterprise/security/rule/batch_delete`, payload);
    }

    enterpriseSecurityAddRule(payload) {
        return this.post(`${API_NETWORK_PREFIX}/enterprise/security/rule/add`, payload);
    }

    enterpriseSecurityUpdateRule(payload) {
        return this.put(`${API_NETWORK_PREFIX}/enterprise/security/rule/update`, payload);
    }
    enterpriseSecurityBindInstance(payload) {
        return this.put(`${API_NETWORK_PREFIX}/enterprise/security/append/bindInstance`, payload);
    }

    enterpriseSecurityBatchUnbindInstance(payload) {
        return this.post(`${API_NETWORK_PREFIX}/enterprise/security/batch/unbind`, payload);
    }

    getEnterpriseBindInstanceList(payload) {
        return this.get(`${API_NETWORK_PREFIX}/enterprise/security/bind/instance/list`, payload);
    }

    getEnterpriseUnbindInstanceList(payload) {
        return this.get(`${API_NETWORK_PREFIX}/enterprise/security/unbind/instance/list`, payload);
    }
    // 实例绑定是否是企业安全组
    checkBindSecurity(payload) {
        return this.get(`${API_NETWORK_PREFIX}/enterprise/security/existForInstance`, payload);
    }

    getEnterpriseSecurityGroups(payload) {
        return this.get(`${API_NETWORK_PREFIX}/enterprise/security/instanceBindedEsgs`, payload);
    }

    bindEnterpriseSecurityGroups(payload) {
        return this.put(`${API_NETWORK_PREFIX}/enterprise/security/replace/bindInstance`, payload);
    }

    getEnterpriseSecurityInstances(payload) {
        return this.get(`${API_NETWORK_PREFIX}/enterprise/security/bind/multiType/instances`, payload);
    }

    getEnterpriseSecurityPreview(payload) {
        return this.post(`${API_NETWORK_PREFIX}/enterprise/security/previewRules`, payload);
    }
    // 企业安全组绑定bbc、bcc返回topN
    getEnterpriseSecurityTopN(payload) {
        return this.get(`${API_NETWORK_PREFIX}/enterprise/security/unbind/server/topN`, payload);
    }
    batchQueryIpSet(payload) {
        return this.post(`${API_NETWORK_PREFIX}/ip/set/batchQuery`, payload);
    }
    batchQueryIpGroup(payload) {
        return this.post(`${API_NETWORK_PREFIX}/ip/group/batchQuery`, payload);
    }

    getAvailAbleNat(payload) {
        return this.get('/api/nat/avaliable/resource', payload);
    }
    ipv6WhiteList() {
        return this.get(`${API_NETWORK_PREFIX}/dc/ipv6WhiteList`);
    }
    channelAddIpv6(payload) {
        return this.put(`${API_NETWORK_PREFIX}/dc/channel/enableIpv6`, payload);
    }
    channelDeleteIpv6(payload) {
        return this.put(`${API_NETWORK_PREFIX}/dc/channel/disableIpv6`, payload);
    }
    createChannelRoute(payload, region) {
        return this.post(`${API_NETWORK_PREFIX}/dc/channel/route`, payload, {headers: {region: region}});
    }
    getChannelRouteList(payload, region) {
        return this.post(`${API_NETWORK_PREFIX}/dc/channel/route/list`, payload, {headers: {region: region}});
    }
    getChannelList(payload) {
        return this.get(`${API_NETWORK_PREFIX}/dc/channel/list/all`, payload);
    }
    channelRouteQuota(payload) {
        return this.post(`${API_NETWORK_PREFIX}/dc/channel/route/quota`, payload);
    }
    deleteChannelRoute(routeId, region) {
        return this.delete(`${API_NETWORK_PREFIX}/dc/channel/route/${routeId}`, {}, {headers: {region: region}});
    }
    updateChannelRoute(routeId, payload, region) {
        return this.put(`${API_NETWORK_PREFIX}/dc/channel/route/${routeId}`, payload, {headers: {region: region}});
    }
    createProbe(payload) {
        return this.post('/api/network/probe', payload);
    }
    editProbe(probeId, payload) {
        return this.put(`/api/network/probe/${probeId}`, payload);
    }
    getProbeQuota(payload) {
        return this.get('/api/network/probe/quota', payload);
    }
    getProbeList(payload) {
        return this.get('/api/network/probe', payload);
    }
    getProbeDetail(probeId, option = {}) {
        return this.get(`/api/network/probe/${probeId}`, {}, option);
    }
    deleteProbe(probeId) {
        return this.delete(`/api/network/probe/${probeId}`);
    }
    isEsgsUse5Dimension(payload) {
        return this.post(`${API_NETWORK_PREFIX}/enterprise/security/esgsUse5Dimension`, payload);
    }
    isEsgsBindedBlbOrSnic(payload) {
        return this.post(`${API_NETWORK_PREFIX}/enterprise/security/esgsBindedBlbOrSnic`, payload);
    }

    // 筛选条件list
    getFilterRuleList(payload) {
        return this.get('/api/mirror/ruleGroup', payload);
    }

    // 修改筛选条件
    putFilterRule(groupId, payload) {
        return this.put(`/api/mirror/ruleGroup/${groupId}`, payload);
    }

    getMirrorSessionList(payload) {
        return this.get('/api/mirror/session', payload);
    }

    getMirrorSessionDetail(sessionId) {
        return this.get(`/api/mirror/session/${sessionId}`);
    }

    deleteMirrorSession(payload) {
        return this.delete('/api/mirror/session', payload);
    }

    updateMirrorSession(sessionId, payload) {
        return this.put(`/api/mirror/session/${sessionId}`, payload);
    }

    createMirrorSession(payload) {
        return this.post(`/api/mirror/session`, payload);
    }

    getMirrorQuota(payload) {
        return this.get(`/api/mirror/quota`, payload);
    }

    // 删除筛选条件
    deleteFilterRule(groupId) {
        return this.delete(`/api/mirror/ruleGroup/${groupId}`);
    }

    // 创建筛选条件
    creatFilterRule(payload) {
        return this.post('/api/mirror/ruleGroup', payload);
    }

    // 获取筛选条件详情
    getFilterRuleDetail(groupId, option = {}) {
        return this.get(`/api/mirror/ruleGroup/${groupId}`, {}, option);
    }

    // 删除筛选条件规则
    deleteGroupRule(groupId, payload) {
        return this.delete(`/api/mirror/ruleGroup/rule/${groupId}`, payload);
    }

    // 创建筛选条件
    creatGroupRule(groupId, payload) {
        return this.post(`/api/mirror/ruleGroup/rule/${groupId}`, payload);
    }

    // 新增筛选条件
    putGroupRule(groupId, payload) {
        return this.put(`/api/mirror/ruleGroup/rule/${groupId}`, payload);
    }

    // 修改筛选条件规则
    changeGroupRule(groupId, payload) {
        return this.put(`/api/mirror/ruleGroup/${groupId}`, payload);
    }

    // 更新镜像状态
    putSessionAction(groupId, payload) {
        return this.put(`/api/mirror/session/${groupId}?action=${payload}`);
    }

    getGatewayList(payload) {
        return this.get('/api/network/gateway/limitrule', payload);
    }
    editGateway(payload) {
        return this.put(`/api/network/gateway/limitrule`, payload);
    }
    deleteGateway(payload) {
        return this.put(`/api/network/gateway/limitrule/delete`, payload);
    }
    getGatewayQuota(payload) {
        return this.get(`/api/network/gateway/limitrule/quota`, payload);
    }
    gatewayResList(payload) {
        return this.post('/api/network/gateway/limitrule/resources', payload);
    }
    createGateWayRule(payload) {
        return this.post('/api/network/gateway/limitrule', payload);
    }
    gatewayLimitRuleDetail(id) {
        return this.get(`/api/network/gateway/limitrule/${id}`);
    }
    gatewayLimitRuleRegions(payload) {
        return this.post(`/api/network/gateway/limitrule/regions`, payload);
    }
    getEdgeNode() {
        return this.get('/api/csn/edgeNode');
    }
    // bec资源校验
    getResource(param, options = {}) {
        return this.get('/api/bec/service/v1/user/activation', param, options);
    }
    getHaVipList(payload) {
        return this.post(`${API_NETWORK_PREFIX}/haVip/list`, payload);
    }
    getHaVipQuota(payload) {
        return this.get(`${API_NETWORK_PREFIX}/haVip/quota`, payload);
    }
    createHaVip(payload) {
        return this.post(`${API_NETWORK_PREFIX}/haVip/create`, payload);
    }
    haVipDetail(payload, option = {}) {
        return this.get(`${API_NETWORK_PREFIX}/haVip/detail`, payload, option);
    }
    deleteHaVip(payload) {
        return this.post(`${API_NETWORK_PREFIX}/haVip/batch_delete`, payload);
    }
    haVipBindEip(payload) {
        return this.post(`${API_NETWORK_PREFIX}/haVip/bindEip`, payload);
    }
    haVipUnBindEip(payload) {
        return this.post(`${API_NETWORK_PREFIX}/haVip/unbindEip`, payload);
    }
    haVipBindList(payload) {
        return this.get(`${API_NETWORK_PREFIX}/haVip/bind/instances`, payload);
    }
    haVipUnbindList(payload) {
        return this.get(`${API_NETWORK_PREFIX}/haVip/unbind/instance/list`, payload);
    }
    haVipBind(payload) {
        return this.post(`${API_NETWORK_PREFIX}/haVip/bindInstance`, payload);
    }
    haVipUnbind(payload) {
        return this.post(`${API_NETWORK_PREFIX}/haVip/unbindInstance`, payload);
    }
    updateHaVip(payload) {
        return this.put(`${API_NETWORK_PREFIX}/haVip/update_field`, payload);
    }
    haVipCheckPrivateIp(payload) {
        return this.get(
            `${API_NETWORK_PREFIX}/vpc/` + payload.vpcUuid + '/vpcIpUseInfo?privateIpAddresses=' + payload.internalIp
        );
    }
    priceV3(data, options = {}) {
        return this.post('/api/price/v3/order', data, options);
    }

    // 参数模板
    getSetDetail(payload, options = {}) {
        return this.get(`${API_NETWORK_PREFIX}/ip/set/detail`, payload, options);
    }
    getGroupDetail(payload, options = {}) {
        return this.get(`${API_NETWORK_PREFIX}/ip/group/detail`, payload, options);
    }
    updateSetInstance(payload) {
        return this.put(`${API_NETWORK_PREFIX}/ip/set/update_field`, payload);
    }
    updateGroupInstance(payload) {
        return this.put(`${API_NETWORK_PREFIX}/ip/group/update_field`, payload);
    }
    updateIpAddress(payload) {
        return this.put(`${API_NETWORK_PREFIX}/ip/set/updateIp`, payload);
    }
    batchDeleteIp(payload) {
        return this.post(`${API_NETWORK_PREFIX}/ip/set/batch_delete_ip`, payload);
    }
    addIp(payload) {
        return this.post(`${API_NETWORK_PREFIX}/ip/set/addIp`, payload);
    }
    setBindInstance(payload) {
        return this.get(`${API_NETWORK_PREFIX}/ip/set/bind/instances`, payload);
    }
    // IP地址组内IP容量配额
    ipQuota(payload) {
        return this.get(`${API_NETWORK_PREFIX}/ip/set/ipQuota`, payload);
    }
    // IP地址族内IP地址组容量配额
    ipGroupSetQuota(payload) {
        return this.get(`${API_NETWORK_PREFIX}/ip/group/ipSetQuota`, payload);
    }

    createIpSet(payload) {
        return this.post(`${API_NETWORK_PREFIX}/ip/set/create`, payload);
    }
    ipSetList(payload, options = {}) {
        return this.get(`${API_NETWORK_PREFIX}/ip/set/list`, payload, options);
    }
    ipSetDownloadIp(payload) {
        return this.get(`${API_NETWORK_PREFIX}/ip/set/downloadIp`, payload);
    }
    ipSetBatchDelete(payload) {
        return this.post(`${API_NETWORK_PREFIX}/ip/set/batch_delete`, payload);
    }

    createIpGroup(payload) {
        return this.post(`${API_NETWORK_PREFIX}/ip/group/create`, payload);
    }
    ipGroupList(payload, options = {}) {
        return this.post(`${API_NETWORK_PREFIX}/ip/group/list`, payload, options);
    }
    ipGroupBatchDelete(payload) {
        return this.post(`${API_NETWORK_PREFIX}/ip/group/batch_delete`, payload);
    }

    ipGroupBindSet(payload) {
        return this.post(`${API_NETWORK_PREFIX}/ip/group/bindSet`, payload);
    }
    ipGroupUnbindSet(payload) {
        return this.post(`${API_NETWORK_PREFIX}/ip/group/unbindSet`, payload);
    }
    ipGroupBindeInstance(payload) {
        return this.get(`${API_NETWORK_PREFIX}/ip/group/bind/instances`, payload);
    }

    ipSetQuota(payload) {
        return this.get(`${API_NETWORK_PREFIX}/ip/set/quota`, payload);
    }
    ipGroupQuota(payload) {
        return this.get(`${API_NETWORK_PREFIX}/ip/group/quota`, payload);
    }
    getIpAddressList(payload) {
        return this.get(`${API_NETWORK_PREFIX}/ip/address/list`, payload);
    }

    //
    enicBandWidthQuota() {
        return this.get('/api/snic/bandwidth/quota');
    }
    batchWhiteList(data, options = {}) {
        return this.post(`${API_NETWORK_PREFIX}/common/whiteLists`, data, options);
    }
    eniBatchDelete(payload) {
        return this.delete(
            '/api/enic/batchDelete',
            {},
            {
                data: payload
            }
        );
    }
    getPathanaliseList(payload) {
        return this.get(`/api/network/pathanalise`, payload, {headers: {region: 'bj'}});
    }
    deletePathanalise(id) {
        return this.delete(`/api/network/pathanalise/${id}`, {}, {headers: {region: 'bj'}});
    }
    editPathanalise(id, payload) {
        return this.put(`/api/network/pathanalise/${id}`, payload, {headers: {region: 'bj'}});
    }
    pathanaliseDetail(id) {
        return this.get(`/api/network/pathanalise/${id}`, {}, {headers: {region: 'bj'}});
    }
    createPathanalise(payload) {
        return this.post(`/api/network/pathanalise`, payload, {headers: {region: 'bj'}});
    }
    startPathanalise(id) {
        return this.post(`/api/network/pathanalise/${id}?action=analise`, {}, {headers: {region: 'bj'}});
    }
    pathanaliseSpecDetail(id) {
        return this.get(`/api/network/pathanalise/analise/${id}`, {}, {headers: {region: 'bj'}});
    }
    pathanaliseHistory(payload) {
        return this.get(`/api/network/pathanalise/history`, payload, {headers: {region: 'bj'}});
    }
    pathanaliseDelete(id) {
        return this.delete(`/api/network/pathanalise/analise/${id}`, {}, {headers: {region: 'bj'}});
    }
    pathanaliseWhite() {
        return this.get(`/api/network/pathanalise/whiteList`, {}, {headers: {region: 'bj'}});
    }
    pathanaliseQuota() {
        return this.get(`/api/network/pathanalise/pathQuota`, {}, {headers: {region: 'bj'}});
    }
    getBbcList(payload, option = {}) {
        return this.post(`/api/bbc/instance/list`, payload, option);
    }
    getBccList(payload, option = {}) {
        return this.post(`/api/bcc/instance/list`, payload, option);
    }
    getBccListBySubnet({subnetUuids}) {
        return this.get(`/api/mirror/list_bcc?subnetUuids=${subnetUuids}`);
    }
    getPathWhite() {
        return this.get('/api/network/pathanalise/whiteList', {}, {headers: {region: 'bj'}});
    }
    getBbcEnics(payload, region) {
        return this.post('/api/bbc/vpc/enis', payload, {headers: {region}});
    }
    getBccEnics(payload, region) {
        return this.post('/api/bcc/vpc/eni/list', payload, {headers: {region}});
    }
    getEniIpv6List(eniId, payload) {
        return this.post(`/api/enic/${eniId}/ipv6Ip`, payload);
    }
    eniIpv6BatchDelete(payload) {
        return this.post('/api/enic/privateIp/batchDel', payload);
    }
    eipGetStaticRouteWhiteList(params) {
        return this.post('/api/eip/staticRouteWhiteList', params);
    }
    eipbgpBlackList() {
        return this.post('/api/eip/bgpBlackList');
    }
    getBgpsWhiteList() {
        return this.get('/api/eip/isBgpsRegion');
    }
    getBgpsNetrafficWhiteList() {
        return this.post('/api/eip/bgpsNetrafficWhiteList');
    }
    eipOrdermaxBandQuota(params, options = {}) {
        return this.post('/api/eip/maxBandQuota', params, options);
    }
    //子网嵌套地址池
    getSubnetReserveportpools(payload) {
        return this.get(`${API_NETWORK_PREFIX}/subnet/reserveportpools`, payload);
    }
    createSubnetReserveportpool(payload) {
        return this.post(`${API_NETWORK_PREFIX}/subnet/reserveportpool`, payload);
    }
    deleteSubnetReserveportpool(subnetId, id) {
        return this.delete(`${API_NETWORK_PREFIX}/subnet/${subnetId}/reserveportpool/${id}`);
    }
    updateSubnetReserveportpool(subnetId, id, payload) {
        return this.put(`${API_NETWORK_PREFIX}/subnet/${subnetId}/reserveportpool/${id}?action=update`, payload);
    }
    getSubenetReserveportpoolQuota(subnetId) {
        return this.get(`${API_NETWORK_PREFIX}/subnet/${subnetId}/reserveportpool/quota`);
    }
    subnetReserveportpoolBindBBC(subnetId, id, action, payload) {
        return this.put(`${API_NETWORK_PREFIX}/subnet/${subnetId}/reserveportpool/${id}?action=${action}`, payload);
    }
    getSubnetReserveportpoolBindList(subnetId, id, payload) {
        return this.get(`${API_NETWORK_PREFIX}/subnet/${subnetId}/reserveportpool/${id}`, payload);
    }
    getInterfaceIam(payload) {
        return this.post(`${API_NETWORK_PREFIX}/common/interface/permission`, payload, {'x-silent': true});
    }
    getAclRules(aclId, payload) {
        return this.get(`${API_NETWORK_PREFIX}/acl/${aclId}/rule`, payload);
    }
    getSecurityBaseInfo(payload, option = {}) {
        return this.get(`${API_NETWORK_PREFIX}/security/baseinfo`, payload, option);
    }
    getSecurityRules(securityGroupUuid, payload, option) {
        return this.get(`${API_NETWORK_PREFIX}/security/${securityGroupUuid}/rule`, payload, option);
    }
    batchUpdateSecurity(payload) {
        return this.put(`${API_NETWORK_PREFIX}/security/rule/batchUpdate`, payload);
    }
    // 子网展示IP使用情况
    getSubnetIPResource(subnetId) {
        return this.get(`${API_NETWORK_PREFIX}/subnet/${subnetId}/resource/ip`);
    }
    getQuotaServiceType(payload) {
        return this.get('/api/quota_center/v1/quota_center', payload);
    }
    getQuotaList(serviceType, region, payload) {
        return this.get(`/api/quota_center/v1/quota_center/QUOTA/${serviceType}/${region}`, payload);
    }
    alarmList(payload) {
        return this.get('/api/quota_center/alert', payload);
    }
    alarmHistory(payload) {
        return this.get('/api/quota_center/alert/history', payload);
    }
    getApplyHistoryList(payload) {
        return this.get('/api/quota_center/apply', payload);
    }

    createAlarm(payload) {
        return this.post('/api/quota_center/alert', payload);
    }

    editAlarm(alertId, payload) {
        return this.put(`/api/quota_center/alert/${alertId}`, payload);
    }

    getRegion(serviceType) {
        return this.get(`/api/quota_center/v1/region/${serviceType}/QUOTA`);
    }
    applyQuota(payload) {
        return this.post('/api/quota_center/v1/quota_center/apply', payload);
    }
    newConfirmOrder(url, data, options = {}) {
        return this.post(url, data, options);
    }

    // 二层网关相关接口
    getL2gwWhiteList() {
        return this.get('/api/l2gw/isL2gwRegion', {}, {'x-silent': true});
    }
    l2gwList(payload) {
        return this.get('/api/l2gw/list', payload);
    }
    l2gwDetail(l2gwId) {
        return this.get(`/api/l2gw/show/${l2gwId}`);
    }
    l2gwUpdate(l2gwId, payload) {
        return this.put(`/api/l2gw/update/${l2gwId}`, payload);
    }
    l2gwCreate(payload) {
        return this.post('/api/l2gw/create', payload);
    }
    l2gwQuota() {
        return this.get('/api/l2gw/quota');
    }
    l2gwRelease(payload) {
        return this.put('/api/l2gw/release', payload);
    }
    createL2gwTunnel(payload) {
        return this.post('/api/l2gw/tunnel', payload);
    }
    l2gwTunnelList(payload) {
        return this.get('/api/l2gw/tunnel', payload);
    }
    l2gwTunnelQuota(payload) {
        return this.get('/api/l2gw/tunnel/quota', payload);
    }
    l2gwTunnelDeatil(tunnelId) {
        return this.get(`/api/l2gw/tunnel/${tunnelId}`);
    }
    l2gwTunnelUpdate(tunnelId, payload) {
        return this.put(`/api/l2gw/tunnel/${tunnelId}`, payload);
    }
    l2gwTunnelRelease(payload) {
        return this.put('/api/l2gw/tunnel', payload);
    }
    getEdgeList(payload, options = {}) {
        return this.get('/api/sdwan/edge', payload, options);
    }
    getSubnetFromZone(payload, options = {}) {
        return this.post('/api/network/v1/subnets', payload, options);
    }
    updateInternalIpBcc(payload, options = {}) {
        return this.post('/api/bcc/instance/updatePrivateIP', payload, options);
    }
    getBccInfos(payload, options = {}) {
        return this.post('/api/bcc/vpc/vpcInfos', payload, options);
    }
    releaseIpBccTunnel(payload, options = {}) {
        return this.post('/api/l2gw/tunnel/migrateIpTask', payload, options);
    }
    deleteIpTunnel(payload, options = {}) {
        return this.put('/api/l2gw/tunnel/migrateIpTask', payload, options);
    }
    getTunnleArpList(tunnelId, payload) {
        return this.get(`/api/l2gw/tunnel/${tunnelId}/idcIps`, payload);
    }
    getUseNestedSgRule(params) {
        return this.post(`${API_NETWORK_PREFIX}/security/useNestedSgRule`, params);
    }
    checkL2gwStock(payload) {
        return this.get(`/api/l2gw/stock`, payload);
    }

    // 组播相关接口
    getMultiCastingList(payload) {
        return this.get('/api/network/multicast/group', payload);
    }
    createMultiCasting(payload) {
        return this.post('/api/network/multicast/group/batchcreate', payload);
    }
    multicastingDelete(payload, options = {}) {
        return this.delete(
            '/api/network/multicast/group/batchdelete',
            {},
            {
                data: payload
            },
            options
        );
    }
    multicastingUpdate(payload, option = {}) {
        return this.put('/api/network/multicast/group/update', payload, option);
    }
    // 组播组
    multicastingSourceCreate(payload, options = {}) {
        return this.post('/api/network/multicast/source/batchcreate', payload, options);
    }
    multicastingSourceDelete(payload, options = {}) {
        return this.delete(
            '/api/network/multicast/source/batchdelete',
            {},
            {
                data: payload
            },
            options
        );
    }
    multicastingSourceUpdate(payload, options = {}) {
        return this.put('/api/network/multicast/source/update', payload, options);
    }
    multicastingSourceList(payload, options = {}) {
        return this.get('/api/network/multicast/source', payload, options);
    }
    // 组播成员
    multicastingMemberCreate(payload, options = {}) {
        return this.post('/api/network/multicast/member/batchcreate', payload, options);
    }
    multicastingMemberDelete(payload, options = {}) {
        return this.delete(
            '/api/network/multicast/member/batchdelete',
            {},
            {
                data: payload
            },
            options
        );
    }
    multicastingMemberUpdate(payload, options = {}) {
        return this.put('/api/network/multicast/member/update', payload, options);
    }
    multicastingMemberList(payload, options = {}) {
        return this.get('/api/network/multicast/member', payload, options);
    }
    getMultiCastingQuota(payload, options = {}) {
        return this.get('/api/network/multicast/group/quota', payload, options);
    }
    getMultiCastingMemberQuota(payload, options = {}) {
        return this.get('/api/network/multicast/member/quota', payload, options);
    }
    getMultiCastingSourceQuota(payload, options = {}) {
        return this.get('/api/network/multicast/source/quota', payload, options);
    }
    natUpdateDeleteProject(payload, options = {}) {
        return this.post('/api/nat/update_deleteProtect', payload, options);
    }
    ipv6gwUpdateDeleteProject(payload, options = {}) {
        return this.post('/api/ipv6gw/update_deleteProtect', payload, options);
    }
    vpnUpdateDeleteProject(payload, options = {}) {
        return this.post('/api/vpn/vpn/update_deleteProtect', payload, options);
    }
    peerconnUpdateDeleteProject(payload, options = {}) {
        return this.post('/api/peerconn/peerconn/update_deleteProtect', payload, options);
    }
    // 问答助手提问
    assistSendMessage(payload) {
        return this.post('/api/ih/v1/service/ticket/query', payload, {'x-silent': true});
    }
    // 用户针对这次提问的反馈
    assistSendFeedback(payload) {
        return this.post('/api/ih/v1/service/ticket/feedback', payload, {'x-silent': true});
    }
    // 获取定制问题
    getAssistSendMessage(payload) {
        return this.post('/api/ih/v1/service/ticket/labelQuestions', payload, {'x-silent': true});
    }
    // 获取私网SNAT规则
    privateSnatRuleList(payload, options = {}) {
        return this.post('/api/intranet/nat/snatRule/list', payload, options);
    }
    // 创建私网SNAT规则
    createPrivateSnat(payload) {
        return this.post('/api/intranet/nat/snatRule/create', payload);
    }
    // 编辑私网SNAT规则
    updatePrivateSnat(payload) {
        return this.put('/api/intranet/nat/snatRule/update', payload);
    }
    // 删除私网SNAT规则
    deletePrivateSnatRule(payload) {
        return this.post('/api/intranet/nat/snatRule/batchDelete', payload);
    }
    // 获取私网DNAT规则
    privateDnatRuleList(payload, options = {}) {
        return this.post('/api/intranet/nat/dnatRule/list', payload, options);
    }
    // 创建私网DNAT规则
    createPrivateDnat(payload) {
        return this.post('/api/intranet/nat/dnatRule/create', payload);
    }
    // 编辑私网DNAT规则
    updatePrivateDnat(payload) {
        return this.put('/api/intranet/nat/dnatRule/update', payload);
    }
    // 删除私网DNAT规则
    deletePrivateDnatRule(payload) {
        return this.post('/api/intranet/nat/dnatRule/batchDelete', payload);
    }
    // 查询私网nat snat规则可关联natIp配额
    snatNatIpQuota(payload, options = {}) {
        return this.get('/api/intranet/nat/snat/natIp/quota', payload, options);
    }
    // 查询私网nat snat规则配额
    privateSnatRuleQuota(payload, options = {}) {
        return this.get(`/api/intranet/nat/snat/quota?natId=${payload}`, {}, options);
    }
    // 查询私网nat dnat规则配额
    privateDnatRuleQuota(payload, options = {}) {
        return this.get(`/api/intranet/nat/dnat/quota?natId=${payload}`, {}, options);
    }
    // 实例诊断相关接口
    getDiagnoseList(payload) {
        return this.post('/api/network/diagnose/list', payload, {headers: {region: 'bj'}});
    }
    getDiagnoseDetail(payload) {
        return this.post('/api/network/diagnose/record/list', payload, {headers: {region: 'bj'}});
    }
    createDiagnoseInstance(payload) {
        return this.post('/api/network/diagnose/create', payload, {headers: {region: 'bj'}});
    }
    deleteDiagnoseInstance(payload) {
        return this.post('/api/network/diagnose/delete', payload, {headers: {region: 'bj'}});
    }
    getDiagnoseRecord(payload) {
        return this.post('/api/network/diagnose/record', payload, {headers: {region: 'bj'}});
    }
    deleteDiagnosisRecord(diagnoseId) {
        return this.delete(`/api/network/diagnose/record/${diagnoseId}`, {}, {headers: {region: 'bj'}});
    }
    executeDiagnosis(payload) {
        return this.post('/api/network/diagnose/execute', payload, {headers: {region: 'bj'}});
    }
    updateDiagnosisInstance(payload) {
        return this.post('/api/network/diagnose/update', payload, {headers: {region: 'bj'}});
    }
    getDiagnosisMonitor(diagnoseId) {
        return this.get(`/api/network/diagnose/record/${diagnoseId}`, {}, {headers: {region: 'bj'}});
    }
    feedBackReport(payload) {
        return this.post('/api/network/diagnose/report/feedback', payload, {headers: {region: 'bj'}});
    }
    hecgEipList(payload, options = {}) {
        return this.post('/api/hceg/list', payload, options);
    }
    blbServiceList(payload) {
        return this.post('/api/blb/userservice/pagelist', payload);
    }
    getUserServiceAuth(userservice) {
        return this.get(`/api/snic/endpoint/userservice/${userservice}/auth`);
    }
    queryResourceList(payload) {
        return this.post('/api/network/migrate/resource/pageList', payload, {headers: {region: 'bj'}});
    }
    editResource(payload) {
        return this.put('/api/network/migrate/resource', payload, {headers: {region: 'bj'}});
    }
}
