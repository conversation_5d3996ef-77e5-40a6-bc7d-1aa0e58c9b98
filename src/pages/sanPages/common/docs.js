/*
 * @Author: p<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-03-25 17:34:45
 * @LastEditTime: 2024-02-05 11:24:13
 * @LastEditors: re<PERSON><PERSON><PERSON> <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: console-vpc/common/docs.js
 */
import {ServiceFactory} from '@baiducloud/runtime';
// 这里配置path即可！！！！！！！！！！！！
// 私有化交付时，文档服务的域名会不同，下面的DocService会为业务动态渲染环境中的文档服务域名
const DOCLINKS = {
    /** 产品首页文档 */
    et_index: '/doc/ET/index.html?from=productToDoc',
    autorenew: '/doc/Finance/s/gjwvysrlu',
    vpcTag: '/doc/VPC/s/Vjwvytykh',
    snicPay: '/doc/VPC/s/Njwvyuqa3#%E6%9C%8D%E5%8A%A1%E7%BD%91%E5%8D%A1%E8%AE%A1%E8%B4%B9',
    nat_index: '/doc/VPC/s/Cjwvyu10x',
    private_nat_doc: '/doc/VPC/s/wm2ife67t',
    bcc_index: '/product/bcc.html',
    dcc_index: '/product/dcc.html',
    vpc_index: '/product/vpc.html',
    peer_index: '/doc/VPC/s/8jwvyu0me',
    peer_pay: '/doc/VPC/s/Njwvyuqa3#%E5%AF%B9%E7%AD%89%E8%BF%9E%E6%8E%A5%E8%AE%A1%E8%B4%B9',
    peer_practice: '/doc/VPC/s/hjwvyu3s0',
    vpn_index: '/doc/VPC/s/9jwvytzz8',
    dc_apAddr: '/doc/ET/s/Olphqmzdb',
    vpc_helpFile: '/doc/VPC/s/qjwvyu0at',
    subnet_helpFile: '/doc/VPC/s/Ujwvytxib',
    route_helpFile: '/doc/VPC/s/jjwvytyw0',
    eni_helpFile: '/doc/VPC/s/0jwvytzll',
    endpoint_helpFile: '/doc/VPC/s/6jwvyu1dn',
    havip_helpFile: '/doc/VPC/s/dlcj263th',
    security_helpFile: '/doc/VPC/s/Vjwvyu1sh',
    acl_helpFile: '/doc/VPC/s/Fjwvytxui',
    ipv6_helpFile: '/doc/VPC/s/jkhoup1ji',
    dcgw_helpFile: '/doc/VPC/s/Jjwvytz9j',
    flowlog_helpFile: '/doc/NDS/s/tlvxu6be3',
    mirror_helpFile: '/doc/NDS/s/Ylvxubrzk',
    probe_helpFile: '/doc/NDS/s/alvxvggzf',
    pathanalise_helpFile: '/doc/NDS/s/Ilvxw0p18',
    ipsec_helpFile: '/doc/VPC/s/9jwvytzz8',
    ssl_helpFile: '/doc/VPC/s/Kkwbx6zyj',
    gre_helpFile: '/doc/VPC/s/olb1yz7b2',
    dc_channel: '/doc/ET/s/Xkssjoeua',
    quota_index: '/doc/QUOTA_CENTER/s/iks9z7yuw',
    bls_pay: '/doc/BLS/s/akz0xh9c6#%E6%94%B6%E8%B4%B9%E6%A0%87%E5%87%86',
    iam_multi: '/doc/IAM/s/Wjxlkh37y',
    acl_index: '/doc/VPC/s/Fjwvytxui',
    security_index: '/doc/VPC/s/Vjwvyu1sh',
    csn_operation: '/doc/CSN/s/Uklrk4o7b',
    et_helpFile: '/product-price/et.html',
    csn_index: '/doc/CSN/s/ukk7yyait',
    l2gw_helpFile: '/doc/VPC/s/Alruew323',
    l2gw_index: '/doc/VPC/s/1ls1qjk3g',
    path_index: '/doc/NDS/s/Ilvxw0p18',
    route_rule: '/doc/VPC/s/jjwvytyw0#%E8%B7%AF%E7%94%B1%E8%A7%84%E5%88%99%E4%BC%98%E5%85%88%E7%BA%A7',
    csnUpgrade: '/doc/CSN/s/xklrnbjzo#%E5%B8%A6%E5%AE%BD%E5%8D%87%E7%BA%A7',
    diagnosis_index: '/doc/NDS/s/gm1ke02o1',
    csnBandWidthManage: '/doc/CSN/s/Wm0aeytzs',
    csnRouteStudy: '/doc/CSN/s/fkya1yv0g#%E5%88%9B%E5%BB%BA%E5%AD%A6%E4%B9%A0%E5%85%B3%E7%B3%BB',
    createRelation: '/doc/CSN/s/fkya1yv0g#%E5%88%9B%E5%BB%BA%E5%85%B3%E8%81%94%E5%85%B3%E7%B3%BB',
    addNetworkInstance: '/doc/CSN/s/Uklrk4o7b#%E6%B7%BB%E5%8A%A0%E7%BD%91%E7%BB%9C%E5%AE%9E%E4%BE%8B'
};
ServiceFactory.register('$doc', ServiceFactory.create('$doc', DOCLINKS));
export const DocService = ServiceFactory.resolve('$doc');
