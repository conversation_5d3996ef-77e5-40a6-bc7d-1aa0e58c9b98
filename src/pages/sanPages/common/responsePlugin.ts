import {EventBus} from '@/utils/eventBus';
import {responseInterceptors} from '@/utils';

export default () =>
    (res: any = {}, next = () => {}) => {
        responseInterceptors(res);
        const {success, code} = res.data;
        const OK = success || code === 'OK';
        const options = res.config;
        // 静默模式如果失败reject
        if (!OK && options['x-silent'] && options['custom-silence']) {
            res.status = 500;
        }
        EventBus.fire('response', res);
        next();
    };
