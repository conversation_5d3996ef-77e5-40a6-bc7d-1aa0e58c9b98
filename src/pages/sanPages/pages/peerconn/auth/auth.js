/**
 * @file 对等连接授权
 * <AUTHOR>
 */
import {Component} from 'san';
import u from 'lodash';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {AppOrderPage} from '@baidu/sui-biz';

import './style.less';
import {StsConfig} from '@/pages/sanPages/utils/config';
import {isOnline} from '@/pages/sanPages/utils/common';
import {EventBus, EventName, activeServiceType} from '@/utils';

const AllRegion = window.$context.getEnum('AllRegion');
const {template, invokeBceSanUI} = decorators;

/* eslint-disable */
const tpl = html`
    <div>
        <div class="auth peerconn-auth">
            <div class="auth-title">对等连接</div>
            <s-order-page
                title="{{title}}"
                desc="{{desc}}"
                logoSrc="{{logoSrc}}"
                process="{{process}}"
                agreed="{{true}}"
                useNewVersion="{{true}}"
                disabled="{{disabled}}"
                openBtnDisabled="{{openBtnDisabled}}"
                openBtnDisabledTip="{{openBtnDisabledTip}}"
                on-click="open"
            />
            <div></div>
        </div>
    </div>
`;
/* eslint-enable */
@template(tpl)
@invokeBceSanUI
class PeerConnAuth extends Component {
    static components = {
        's-order-page': AppOrderPage
    };
    computed = {
        confirmDisabled() {
            return !this.data.get('agreed');
        }
    };
    initData() {
        return {
            title: '对等连接简介',
            desc:
                '对等连接（Peer Connection）为用户提供了VPC级别的网络互联服务，' +
                '使用户实现在不同虚拟网络之间的流量互通，' +
                '实现同区域、跨区域，' +
                '同用户、跨用户之间稳定高速的虚拟网络互联。',
            logoSrc: 'https://bce.bdstatic.com/network-frontend/peerconn-auth-logo.png',
            process: {
                title: '对等连接使用流程',
                content: [
                    {
                        title: '创建对等连接',
                        desc: '同账号，跨账号两种使用方式，填写配置信息'
                    },
                    {
                        title: '配置路由表',
                        desc: '配置对等连接的路由，将指向目标网段的流量转发到VPC内'
                    },
                    {
                        title: '监控报警',
                        desc: '查看带宽、流量、包速率等监控信息，并配置报警策略'
                    }
                ]
            },
            disabled: false,
            openBtnDisabled: false,
            openBtnDisabledTip: ''
        };
    }
    inited() {
        const isSubUser = window.$context.isSubUser();
        if (isSubUser) {
            this.data.set('openBtnDisabled', true);
            this.data.set('openBtnDisabledTip', '当前登录的子账户没有开通服务的权限，请联系主账户开通服务后使用。');
        }
    }
    open() {
        const roleName = StsConfig.PEERCONN.roleName;
        return this.$http
            .iamStsRoleActivate(
                u.extend(
                    {
                        roleName,
                        accountId: window.$context.getUserId()
                    },
                    isOnline() ? StsConfig.PEERCONN.online : StsConfig.PEERCONN.sandbox
                ),
                {region: AllRegion.BJ}
            )
            .then(() => {
                EventBus.fire(EventName.productActive, activeServiceType.peerconn);
                window.$storage.set('peerConnSts', true);
            });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(PeerConnAuth));
