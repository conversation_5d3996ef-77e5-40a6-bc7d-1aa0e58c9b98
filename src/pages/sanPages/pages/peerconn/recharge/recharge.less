.peerconn-recharge-wrap {
    height: 100%;
    width: 100%;
    .s-create-page {
        height: 100%;
    }
    .steps-wrap {
        margin: 20px 0;
    }
    .form-wrap {
        .form-part-wrap {
            border-radius: 6px;
            width: calc(~'100vw - 32px');
            background: #fff;
            margin: 16px;
            padding: 24px;
            box-sizing: border-box;
            .recharge_text {
                font-size: 16px;
                color: #151A26;
                line-height: 24px;
                font-weight: 500;
                margin-bottom: 12px;
            }
            .service_text {
                margin-top: 20px;
                font-size: 12px;
                color: #151A26;
                line-height: 20px;
                font-weight: 400;
            }
            .s-table {
                margin-top: 12px;
            }
        }
        .s-form-item-label {
            color: #666;
            width: 120px;
            height: 30px;
            line-height: 30px;
            float: left;
        }
    }
    .form-recharge {
        margin-top: 16px;
        .purchase_time {
            margin-top: 26px;
            font-size: 12px;
            color: #5E626A;
            line-height: 20px;
            font-weight: 400;
        }
        .s-form-item {
            margin-bottom: 12px !important;
            &-label {
                width: 118px;
            }
        }
        .radio_text {
            font-size: 12px;
            color: #83868C;
            line-height: 20px;
            font-weight: 400;
            margin-top: 8px;
        }
    }
    .expire_time_text {
        color: #666;
        width: 120px;
        height: 30px;
        line-height: 30px;
    }
    .expire_time_key {
        margin-left: 18px;
    }
    .buybucket {
        width: 100%;
        left: 0;
        height: 60px;
        .buybucket-container {
            width: auto;
            float: left;
            height: 48px;
            transform: translateY(12.5%);
            display: flex;
            align-items: center;
            .s-button {
                margin-left: 16px;
            }
        }
    }
    .footer-right {
        float: right;
        display: inline-flex;
        .s-button {
            margin-right: 16px;
        }
    }
}