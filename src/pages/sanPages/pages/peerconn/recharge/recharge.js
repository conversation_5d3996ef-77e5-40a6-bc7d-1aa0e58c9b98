import _ from 'lodash';
import {Component} from 'san';
import {decorators, html, CreatePage, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {BillingSDK, OrderItem} from '@baiducloud/billing-sdk';
import {OrderConfirm, ShoppingCart} from '@baiducloud/billing-sdk/san';
import {convertPrice} from '@/pages/sanPages/utils/helper';
import {TimeType} from '@/pages/sanPages/common/enum';
import {toTime} from '@/pages/sanPages/utils/helper';
import {isMainRegion} from '@/pages/sanPages/utils/common';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';

import './recharge.less';

const {asPage, invokeSUI, invokeSUIBIZ, invokeAppComp, template} = decorators;
const pageTitle = ['产品续费', '确认订单'];
const AllRegion = window.$context.getEnum('AllRegion');
const kXhrOptions = {'X-silence': true};

const tpl = html`
    <div>
        <s-app-create-page
            class="{{klass}}"
            backTo="{{pageNav.backTo}}"
            backToLabel="{{pageNav.backToLabel}}"
            pageTitle="{{pageNav.pageTitle}}"
        >
            <div class="s-step-block">
                <s-steps current="{{step + 1}}">
                    <s-steps-step s-for="i in pageNav.steps" title="{{i.title}}" />
                </s-steps>
            </div>
            <div class="form-wrap" s-if="step == 0">
                <div class="form-part-wrap">
                    <div class="recharge_text">要续费的产品</div>
                    <span class="service_text">跨境对等连接</span>
                    <s-table
                        columns="{{table.columns}}"
                        loading="{{table.loading}}"
                        error="{{table.error}}"
                        datasource="{{table.datasource}}"
                    >
                        <div slot="empty">
                            <s-empty>
                                <div slot="action"></div>
                            </s-empty>
                        </div>
                        <div slot="c-localRegion">
                            <span>{{row.localRegion | getRegionLabel}}</span>
                        </div>
                        <div slot="c-peerRegion">
                            <span>{{row.peerRegion | getRegionLabel}}</span>
                        </div>
                        <div slot="c-expireTime">
                            <span>{{row.expireTime | getTime}}</span>
                        </div>
                    </s-table>
                </div>
            </div>
            <div class="form-wrap form-recharge" s-if="step == 0">
                <div class="form-part-wrap">
                    <div class="recharge_text">续费信息</div>
                    <s-form s-ref="form" data="{=formData=}" rules="{{rules}}" label-align="left">
                        <s-form-item prop="purchaseLength" label="购买时长：">
                            <s-tag-radio-group
                                radioType="button"
                                value="{=formData.purchaseLength=}"
                                datasource="{{purchaseLengthList}}"
                            >
                            </s-tag-radio-group>
                            <span class="radio_text">购买3个月以上才可以备案</span>
                        </s-form-item>
                    </s-form>
                    <span class="expire_time_text">续费后到期时间：</span>
                    <span class="expire_time_key">{{expire_time}}</span>
                </div>
            </div>
            <order-confirm
                s-else
                s-ref="orderConfirm"
                theme="default"
                items="{{items}}"
                sdk="{{sdk}}"
                useCoupon="{{FLAG.NetworkSupportXS}}"
                showAgreementCheckbox
            >
            </order-confirm>
            <div slot="pageFooter" class="buybucket">
                <div class="buybucket-container" s-if="step === 0">
                    <s-button disabled="{{confirmDisabled}}" size="large" skin="primary" on-click="onNext">
                        确认订单
                    </s-button>
                    <s-button size="large" on-click="cancel"> 取消 </s-button>
                </div>
                <div class="buybucket-container" s-if="step === 1">
                    <s-button size="large" on-click="onPre"> 上一步 </s-button>
                    <s-button size="large" skin="primary" on-click="goToConfirm" disabled="{{disabledPay}}">
                        确认订单
                    </s-button>
                    <s-button size="large" on-click="cancel"> 取消 </s-button>
                </div>
            </div>
        </s-app-create-page>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class InstanceCreate extends CreatePage {
    REGION_CHANGE_LOCATION = '#/vpc/peerconn/list';
    static components = {
        'order-confirm': OrderConfirm,
        'shopping-cart': ShoppingCart
    };
    static filters = {
        getRegionLabel(value) {
            return AllRegion.getTextFromValue(value);
        },
        getTime(value) {
            return value ? toTime(value) : '-';
        }
    };
    initData() {
        return {
            FLAG,
            klass: 'peerconn-recharge-wrap',
            pageNav: {
                backTo: '/network/#/vpc/peerconn/list',
                backToLabel: '返回',
                pageTitle: '续费',
                steps: [
                    {title: '产品续费', key: 'RECHARGE_CONFIG'},
                    {title: '确定订单', key: 'ORDER'}
                ]
            },
            step: 0,
            table: {
                loading: false,
                columns: [
                    {name: 'localRegion', label: '本端地域', width: 100},
                    {name: 'localVpcShortId', label: '本端VPC ID', width: 100},
                    {name: 'localIfName', label: '本端接口名称', width: 100},
                    {name: 'localIfId', label: '本端接口ID', width: 100},
                    {name: 'peerRegion', label: '对端地域', width: 100},
                    {name: 'peerVpcShortId', label: '对端VPC ID', width: 100},
                    {name: 'bandwidth', label: '带宽上限', width: 100},
                    {name: 'expireTime', label: '到期时间', width: 100}
                ],
                datasource: []
            },
            purchaseLengthList: [
                {text: '1个月', value: 1},
                {text: '2个月', value: 2},
                {text: '3个月', value: 3},
                {text: '4个月', value: 4},
                {text: '5个月', value: 5},
                {text: '6个月', value: 6},
                {text: '7个月', value: 7},
                {text: '8个月', value: 8},
                {text: '9个月', value: 9},
                {text: '1年', value: 12, mark: '8.3折'},
                {text: '2年', value: 24, mark: '8.3折'},
                {text: '3年', value: 36, mark: '8.3折'}
            ],
            formData: {
                purchaseLength: 1
            },
            expire_time: '',
            confirmDisabled: true,
            urlQuery: getQueryParams()
        };
    }
    async attached() {
        await this.getCrossPeerDedail();
        this.watch('formData.purchaseLength', value => {
            let month = new Date(this.data.get('instance').expireTime).getMonth();
            let time = toTime(new Date(this.data.get('instance').expireTime).setMonth(month + value));
            this.data.set('expire_time', this.data.get('instance').expireTime ? time : '-');
            this.getPrice();
        });
        this.getPrice();
    }

    getCrossPeerDedail() {
        this.data.set('table.loading', true);
        let payload = {
            localIfId: this.data.get('urlQuery.localIfId')
        };
        return this.$http
            .peerConnDetail(payload)
            .then(res => {
                let purchaseLength = this.data.get('formData.purchaseLength');
                let month = new Date(res.expireTime).getMonth();
                let time = toTime(new Date(res.expireTime).setMonth(month + purchaseLength));
                this.data.set('expire_time', res.expireTime ? time : '-');
                this.data.set('table.loading', false);
                this.data.set('table.datasource', [res]);
                this.data.set('instance', res);
            })
            .catch(err => {
                this.data.set('table.loading', false);
            });
    }
    async onNext() {
        await this.getPrice();
        const formData = this.data.get('formData');
        const instance = this.data.get('instance');
        const contextPipe = {
            getCurrentRegion() {
                return window.$context.getCurrentRegionId();
            },
            getCsrfToken() {
                return window.$cookie.get('bce-user-info').replace(/"/g, '');
            },
            SERVICE_TYPE: 'MKT'
        };
        const price = this.data.get('price');
        let unitPrice = convertPrice(+price?.[0]?.perMoney).getPriceOfMinute();
        let unitPriceShow = '￥' + unitPrice;
        let chargeType = '包年包月';
        let configDetail = [
            {
                label: '对端地域：',
                value: AllRegion.getTextFromValue(instance.peerRegion)
            },
            {
                label: '带宽上限：',
                value: `${instance.bandwidth}Mbps`
            },
            {
                label: '计费方式',
                value: chargeType
            }
        ];
        const orderItem = new OrderItem(
            {
                region: instance.localRegion,
                serviceType: 'MKT',
                serviceName: '跨境对等连接',
                duration: formData.purchaseLength,
                regionName: window.$context.getCurrentRegion().label,
                productType: instance.productType,
                timeUnit: TimeType.MONTH,
                type: 'RENEW',
                count: 1,
                configDetail,
                unitPriceText: unitPriceShow,
                flavor: [],
                managePrice: false
            },
            contextPipe
        );
        let money = convertPrice(+this.data.get('price')?.[0]?.price).getPriceOfMinute();
        orderItem.set('price', money);
        orderItem.set('config', this.getConfig());
        this.data.set('items', [orderItem]);
        this.data.set(
            'sdk',
            new BillingSDK(
                {
                    serviceType: 'MKT',
                    region: window.$context.getCurrentRegionId(),
                    serviceName: '跨境对等连接',
                    productType: instance.productType,
                    type: 'NEW'
                },
                contextPipe
            )
        );
        this.data.set('step', 1);
        this.data.set('pageNav.pageTitle', pageTitle[1]);
    }
    getConfig() {
        let formData = this.data.get('formData');
        let instance = this.data.get('instance');
        let data = {
            duration: formData.purchaseLength,
            instanceId: instance.peerConnId,
            serviceType: 'MKT',
            uuid: instance.uuid
        };
        return data;
    }
    onPre() {
        this.data.set('step', 0);
        this.data.set('pageNav.pageTitle', pageTitle[0]);
    }
    getPrice() {
        let formData = this.data.get('formData');
        let instance = this.data.get('instance');
        let currentRegion = window.$context.getCurrentRegionId();
        let queryRegion = isMainRegion(instance.localRegion) ? instance.peerRegion : instance.localRegion;
        let config = {
            serviceType: 'MKT',
            subServiceType: 'default',
            productType: 'RESERVED',
            chargeItem: 'Cpt2',
            region: currentRegion,
            scene: 'NEW',
            orderType: 'NEW',
            currency: 'CNY',
            count: 1,
            duration: formData.purchaseLength,
            timeUnit: 'MONTH',
            flavor: [
                {
                    name: 'subServiceType',
                    value: 'peerconn',
                    scale: 1
                },
                {
                    name: `Mainland-${queryRegion}-bandwidth`,
                    value: instance.bandwidth,
                    scale: 1
                }
            ]
        };
        this.$http.priceV3({configs: [config]}).then(data => {
            this.data.set('price', data);
            this.data.set('confirmDisabled', false);
        });
    }
    async goToConfirm() {
        await this.ref('orderConfirm').validateAgreement();
        const sdk = this.data.get('sdk');
        if (sdk) {
            this.data.set('disabledPay', true);
            sdk.confirmOrder({
                url: '/api/peerconn/crossBorder/order/confirm/renew',
                type: 'RENEW',
                instances: this.data.get('items')
            })
                .then(data => {
                    const url = data.data;
                    this.data.set('disabledPay', false);
                    redirect({
                        module: url.module,
                        path: url.path,
                        params: data.query,
                        paramSeperator: '~'
                    });
                })
                .catch(data => {
                    this.data.set('disabledPay', false);
                    const url = data.data;
                    redirect({
                        module: url.module,
                        path: url.path,
                        params: data.query,
                        paramSeperator: '~'
                    });
                });
        }
    }
    cancel() {
        location.hash = '#/vpc/peerconn/list';
    }
}
export default San2React(Processor.autowireUnCheckCmpt(InstanceCreate));
