/**
 * @file VPC - PEERCONN - Action配置
 *
 * <AUTHOR>
 */

import sanPage from 'inf-ui/sanPage';

/**
 * ER Action 的注册配置，供最外层的 config/action 调用统一注册
 *
 * @type {Array}
 */
export default [
    {
        path: '/vpc/peerconn/auth',
        type: sanPage('network/peerconn/auth/auth')
    },
    {
        path: '/vpc/peerconn/detail',
        type: sanPage('network/peerconn/pages/detail/detail')
    },
    {
        path: '/vpc/peerconn/list',
        type: sanPage('network/peerconn/pages/list/list')
    },
    {
        path: '/vpc/peerconn/monitor',
        type: 'network/peerconn/monitorDetail/Action'
    },
    {
        path: '/vpc/peerconn/upgrade/success',
        type: sanPage('network/peerconn/upgrade/Success')
    },
    {
        path: '/vpc/peerconn/create/v2',
        type: sanPage('network/peerconn/create/create')
    },
    {
        path: '/vpc/peerconn/create/audit',
        type: sanPage('network/peerconn/create/audit/audit')
    },
    {
        path: '/vpc/peerconn/create/auditing',
        type: sanPage('network/peerconn/create/audit/auditing')
    },
    {
        path: '/vpc/peerconn/upgrade',
        type: sanPage('network/peerconn/pages/upgrade/upgrade')
    }
];
