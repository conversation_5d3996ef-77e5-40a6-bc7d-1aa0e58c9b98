/**
 * @file src/network/peerconn/create/create.js
 * <AUTHOR>
 */
import {Component} from 'san';
import u from 'lodash';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {parseQuery, ObjectToQuery} from '@/utils';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {BillingSdk, OrderItem, UPDATE_STATUS} from '@baiducloud/bce-billing-sdk';
import {OrderConfirm, ShoppingCart, TotalPrice, Protocol} from '@baiducloud/bce-billing-sdk-san';
import Client from '@baiducloud/httpclient';
import {ResourceGroupPanel} from '@baidu/bce-resourcegroup-sdk-san';
import {ResourceGroupSDK} from '@baidu/bce-resourcegroup-sdk';
import {TagEditPanel} from '@baidu/bce-tag-sdk-san';
import {Button, Form, Select, Input, Checkbox, Switch, Radio, Slider, InputNumber, Tooltip} from '@baidu/sui';
import {AppCreatePage, Tip, TipButton} from '@baidu/sui-biz';
import {isCrossRegion, isMainRegion, PeerConnDisableRegion} from '@/pages/sanPages/utils/common';
import {getUserId, $flag as FLAG, contextPipe} from '@/pages/sanPages/utils/helper';
import {PeerConnType, TimeType, Month, Year, PayType} from '@/pages/sanPages/common/enum';
import {DocService} from '@/pages/sanPages/common';
import {getMarksByStep} from '@/pages/sanPages/utils/helper';
import rule from '@/pages/sanPages/utils/rule';
import './style.less';

const {invokeAppComp, template, invokeSUI, invokeSUIBIZ} = decorators;
const AllRegion = window.$context.getEnum('AllRegion');

const kXhrOptions = {'X-silence': true};
const formValidator = self => ({
    localIfName: [
        {
            pattern: /^[a-zA-Z][\w\-\_\/\.]{0,64}$/,
            message: '以字母开头，支持大小写字母、数字以及 -_/. 特殊字符'
        }
    ],
    peerIfName: [
        {
            pattern: /^[a-zA-Z][\w\-\_\/\.]{0,64}$/,
            message: '以字母开头，支持大小写字母、数字以及 -_/. 特殊字符'
        }
    ],

    peerVpcId: [
        {required: true, message: '对端网络必填'},
        {
            validator(rule, value, callback) {
                let source = self.data.get('formData');
                let result = source.peerVpcStatus;
                let isDiff = source.connType === PeerConnType.DIFF;
                if (!isDiff && !value) {
                    return callback('对端网络必填');
                } else if (isDiff && !value) {
                    return callback('对端网络必填');
                } else if (!/^[vpc-][a-z\w\-]{15}$/.test(value)) {
                    return callback('对端网络格式错误');
                } else if (value === source.vpcInfo.shortId) {
                    return callback('对端网络不能和当前网络重复');
                }
                return callback();
            }
        }
    ],
    peerRegion: [{required: true, message: '请填写信息确定对端地域'}],
    peerRegionSelect: [{required: true, message: '请填写对端地域'}],
    bandwidthInMbps: [{required: true, message: '请选择带宽上限'}],
    peerAccountId: [
        {
            validator(rule, value, callback) {
                let source = self.data.get('formData');
                if (source.connType !== PeerConnType.DIFF) {
                    return callback();
                } else if (!value) {
                    return callback('请输入对端账户ID');
                } else if (!/^[a-z\w\-]{32,40}$/.test(value)) {
                    return callback('对端账户格式错误');
                } else if (value === getUserId()) {
                    return callback('对端账户ID不能和当前账号相同');
                }
                return callback();
            }
        }
    ],
    desc: [
        {
            required: false
        },
        {
            validator(rule, value, callback) {
                if (!value) {
                    return callback();
                }
                if (value.length > 200) {
                    return callback('长度不能超过200');
                }
                return callback();
            }
        }
    ],
    localRegion: [{required: true}],
    vpcInfo: [{required: true}]
});

const tpl = html`
    <div>
        <s-page
            class="vpc-peerconn-create-v2 {{inCrossWhite && isCross ? 'vpc-peerconn-create-cross' : ''}}"
            withTip="{{withTip}}"
            backTo="{{pageNav.backUrl}}"
            backToLabel="{{pageNav.backLabel}}"
            pageTitle="{{pageNav.title}}"
        >
            <div class="s-step-block">
                <s-steps current="{{stepIndex + 1}}">
                    <s-steps-step s-for="i in steps" title="{{i.title}}" />
                </s-steps>
            </div>
            <div class="form-widget" s-if="stepIndex === 0">
                <div class="peerconn-create-header">
                    <div class="peerconn-create-wrapper">
                        <span class="{{isCross ? '' : 'actived'}}" on-click="toNotCross">非跨境</span>
                        <span s-if="inCrossWhite" class="{{isCross ? 'actived' : ''}}" on-click="toCross"
                            >中国联通跨境</span
                        >
                    </div>
                </div>
                <div class="peerconn-purchase-notes">
                    <h4>{{'购买须知'}}</h4>
                    <p class="billing-instructions">
                        <s-button skin="enhance">计费说明</s-button>
                        <span class="desc"
                            >1. 当对等连接状态变为“可用”时开始计费；2.
                            跨账号互联对等连接由连接发起方支付费用，连接接收方无需付费；</span
                        >
                        <a
                            s-if="!FLAG.NetworkSupportXS"
                            href="{{DocService.peer_pay}}"
                            target="_blank"
                            class="peerconn-price"
                            >{{'对等连接计费详情'}}></a
                        >
                    </p>
                </div>
                <s-form data="{=formData=}" rules="{{rules}}" s-ref="form" label-align="left">
                    <div class="body-part-content form-part-wrap">
                        <h4>{{'付费方式'}}</h4>
                        <s-form-item label="{{'付费方式：'}}" prop="productType">
                            <template slot="label" class="label_class">
                                {{'付费方式：'}}
                                <s-tip
                                    class="inline-tip"
                                    content="同region对等连接仅支持按量付费购买"
                                    skin="question"
                                />
                            </template>
                            <div class="row-line">
                                <ul class="row-line-wrapper">
                                    <li
                                        s-for="item in productTypeList"
                                        class="row-line-item  {{item.value === formData.productType ? 'row-line-selected' : item.disabled ? 'row-line-disabled' : ''}}"
                                        on-click="onSelectPayType(item)"
                                    >
                                        <s-tooltip content="{{item.disabled ? disabledTip : ''}}">
                                            <div class="row-line-inner">
                                                <div class="row-line-version">
                                                    <img class="img" src="{{item.imgSrc}}" alt="" />
                                                    <div class="row-line-widget">
                                                        <p
                                                            class="row-line-name {{item.value === formData.productType ? 'row-line-name-selected' : ''}}"
                                                        >
                                                            {{item.text}}
                                                        </p>
                                                        <p class="row-line-desc">{{item.desc}}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </s-tooltip>
                                    </li>
                                </ul>
                            </div>
                        </s-form-item>
                    </div>
                    <div class="body-part-content">
                        <h4>{{'连接设置'}}</h4>
                        <s-form-item label="{{'连接类型：'}}" prop="connType">
                            <s-radio-group
                                enhanced
                                datasource="{{datasource.connType}}"
                                value="{=formData.connType=}"
                                track-id="ti_vpc_peerconn_create"
                                track-name="连接类型"
                                radioType="button"
                            >
                            </s-radio-group>
                        </s-form-item>
                        <s-form-item
                            s-if="formData.connType==='diff'"
                            label="{{'本端连接角色：'}}"
                            help="{{Rule.NAME.placeholder}}"
                            prop="name"
                        >
                            <div class="initiator">{{'发起端'}}</div>
                        </s-form-item>
                        <s-form-item s-if="formData.connType==='diff'" label="{{'对端账户ID：'}}" prop="peerAccountId">
                            <s-input
                                width="{{220}}"
                                value="{=formData.peerAccountId=}"
                                placeholder="{{'请输入对端账户ID'}}"
                                track-id="ti_peerconn_peerAccountId"
                                track-name="本端端口名称"
                            >
                            </s-input>
                        </s-form-item>
                    </div>
                    <div class="body-part-content">
                        <h4>{{'本端和对端'}}</h4>
                        <div class="peerconn-wrapper">
                            <div class="style-common localend">
                                <h5>本端</h5>
                                <s-form-item prop="localRegion" label="{{'本端地域：'}}">
                                    <template slot="label" class="label_class">
                                        {{'本端地域：'}}
                                        <s-tip
                                            class="inline-tip"
                                            content="{{regionSwitchTipContent}}"
                                            skin="question"
                                        />
                                    </template>
                                    <s-select width="{{240}}" value="{=formData.localRegion=}">
                                        <s-select-option
                                            s-for="item in datasource.region"
                                            value="{{item.value}}"
                                            label="{{item.text}}"
                                        >
                                            <s-tooltip>
                                                <div slot="content">{{item.text}}</div>
                                                <div>{{item.text}}</div>
                                            </s-tooltip>
                                        </s-select-option>
                                    </s-select>
                                </s-form-item>
                                <s-form-item label="{{'本端网络：'}}" prop="vpcInfo">
                                    <s-select
                                        width="{{240}}"
                                        value="{=formData.vpcInfo=}"
                                        track-id="ti_eip_group_create"
                                        track-name="选择本端网络"
                                    >
                                        <s-select-option
                                            s-for="item in datasource.localVpcList"
                                            value="{{item.value}}"
                                            label="{{item.text}}"
                                        >
                                            <s-tooltip>
                                                <div slot="content">{{item.text}}</div>
                                                <div>{{item.text}}</div>
                                            </s-tooltip>
                                        </s-select-option>
                                    </s-select>
                                </s-form-item>
                                <s-form-item
                                    label="{{'本端端口名称：'}}"
                                    help="{{help.localIfName}}"
                                    prop="localIfName"
                                >
                                    <s-input
                                        class="input-with-num"
                                        on-input="handleNameInput('local', $event)"
                                        width="{{236}}"
                                        placeholder="{{'请输入'}}"
                                        value="{=formData.localIfName=}"
                                        track-id="ti_peerconn_localIfName"
                                        track-name="本端端口名称"
                                    >
                                    </s-input>
                                    <span class="input-num-statistics">{{localNameLength+'/'+'65'}}</span>
                                </s-form-item>
                            </div>
                            <div class="peerconn-icon-end">
                                <span class="icon"></span>
                            </div>
                            <div class="style-common peer">
                                <h5>对端</h5>
                                <s-form-item
                                    s-if="formData.connType==='same'"
                                    label="{{'对端地域：'}}"
                                    prop="peerRegionSelect"
                                >
                                    <s-select
                                        width="{{240}}"
                                        datasource="{{datasource.regionList}}"
                                        value="{=formData.peerRegionSelect=}"
                                        on-change="peerRegionChange($event)"
                                        track-id="ti_eip_group_create"
                                        track-name="选择对端地域"
                                    ></s-select>
                                </s-form-item>
                                <s-form-item
                                    class="peerconn-peer-region"
                                    s-if="formData.connType==='diff' && formData.peerRegionLabel"
                                    label="{{'对端地域：'}}"
                                    prop="peerRegion"
                                >
                                    <label s-if="formData.peerRegionLabel">{{formData.peerRegionLabel}}</label>
                                    <s-input
                                        s-if="false"
                                        width="{{80}}"
                                        value="{=formData.peerRegion=}"
                                        track-id="ti_peerconn_peerRegion"
                                        track-name="对端地域"
                                    >
                                    </s-input>
                                </s-form-item>
                                <s-form-item
                                    s-if="formData.connType==='same'"
                                    label="{{'对端网络：'}}"
                                    prop="peerVpcId"
                                >
                                    <s-select
                                        width="{{240}}"
                                        value="{=formData.peerVpcId=}"
                                        track-id="ti_eip_group_create"
                                        track-name="选择对端网络"
                                    >
                                        <s-select-option
                                            s-for="item in datasource.peerVpcList"
                                            value="{{item.value}}"
                                            label="{{item.text}}"
                                        >
                                            <s-tooltip>
                                                <div slot="content">{{item.text}}</div>
                                                <div>{{item.text}}</div>
                                            </s-tooltip>
                                        </s-select-option>
                                    </s-select>
                                    <div s-if="showPeerVpcIdLabel">
                                        <p style="color: #f33e3e" s-if="peerVpcErr">{{peerVpcErr}}</p>
                                        <p s-else>
                                        <!--bca-disable-next-line-->
                                            <span style="color: #f33e3e" s-html="vpcOverlapWarningText"></span>
                                        </p>
                                    </div>
                                </s-form-item>
                                <s-form-item
                                    s-if="formData.connType==='diff'"
                                    label="{{'对端网络：'}}"
                                    prop="peerVpcId"
                                >
                                    <s-input
                                        width="{{240}}"
                                        value="{=formData.peerVpcId=}"
                                        placeholder="{{'请输入对端网络ID'}}"
                                        track-id="ti_peerconn_peerVpcId"
                                        track-name="对端网络"
                                    >
                                    </s-input>
                                    <div s-if="showPeerVpcIdLabel">
                                        <p style="color: #f33e3e" s-if="peerVpcErr">{{peerVpcErr}}</p>
                                        <p s-else>
                                        <!--bca-disable-next-line-->
                                            <span style="color: #f33e3e" s-html="vpcOverlapWarningText"></span>
                                        </p>
                                    </div>
                                </s-form-item>
                                <s-form-item
                                    s-if="formData.connType==='same'"
                                    label="{{'对端端口名称：'}}"
                                    help="{{help.localIfName}}"
                                    prop="peerIfName"
                                >
                                    <s-input
                                        class="input-with-num"
                                        on-input="handleNameInput('peer', $event)"
                                        width="{{236}}"
                                        placeholder="{{'请输入'}}"
                                        value="{=formData.peerIfName=}"
                                        track-id="ti_peerconn_peerIfName"
                                        track-name="对端端口名称"
                                    >
                                    </s-input>
                                    <span class="input-num-statistics">{{peerNameLength+'/'+'65'}}</span>
                                </s-form-item>
                            </div>
                        </div>
                    </div>
                    <div class="body-part-content">
                        <h4>{{'购买信息'}}</h4>
                        <s-form-item s-if="showPeerBilling" inline label="{{'计费方式：'}}" prop="billingMethod">
                            <s-radio-group
                                enhanced
                                radioType="button"
                                s-if="showPeerBilling"
                                datasource="{{datasource.billingMethod}}"
                                value="{=formData.billingMethod=}"
                                track-id="ti_vpc_peerconn_create"
                                track-name="计费方式"
                            >
                            </s-radio-group>
                        </s-form-item>
                        <s-form-item label="{{'带宽上限：'}}" prop="bandwidthInMbps">
                            <s-slider
                                ref="drag"
                                parts="{{8}}"
                                marks="{{marks}}"
                                max="{{datasource.bandwidthInMbps.max}}"
                                min="{{datasource.bandwidthInMbps.min}}"
                                step="{{datasource.bandwidthInMbps.step}}"
                                value="{=formData.bandwidthInMbps=}"
                            />
                            <div class="dragger-input">
                                <s-input-number
                                    value="{=formData.bandwidthInMbps=}"
                                    max="{{datasource.bandwidthInMbps.max}}"
                                    min="{{datasource.bandwidthInMbps.min}}"
                                    on-input="inputchange"
                                />
                                Mbps
                            </div>
                        </s-form-item>
                        <s-form-item
                            s-if="isPrepay || isCrossPrepay"
                            label="{{'购买时长：'}}"
                            prop="purchaseLength"
                            class="purchase-period"
                        >
                            <s-tag-radio-group
                                enhanced
                                radioType="button"
                                datasource="{{datasource.purchaseLength}}"
                                value="{=formData.purchaseLength=}"
                                track-id="ti_eip_group_create"
                                track-name="购买时长"
                            >
                            </s-tag-radio-group>
                        </s-form-item>
                        <s-form-item
                            s-if="(isPrepay || isCrossPrepay) && !FLAG.NetworkSupportXS"
                            label="{{'自动续费：'}}"
                            prop="autoRenew"
                        >
                            <s-radio-radio-group
                                enhanced
                                class="renew-item-wrapper"
                                datasource="{{autoRenewDatasource}}"
                                radioType="button"
                                value="{=formData.autoRenew =}"
                                track-id="ti_eip_group_create"
                                track-name="自动续费"
                            />
                            <a
                                s-if="{{!FLAG.NetworkSupportXS}}"
                                href="{{DocService.autorenew}}"
                                target="_blank"
                                data-track-id="ti_eip_group_create"
                                data-track-name="自动续费文档"
                                class="left_class"
                            >
                                {{'什么是自动续费？'}}
                            </a>
                            <div s-if="formData.autoRenew" class="autorenew-panel">
                                <label>{{'选择续费周期：'}}</label>
                                <s-select
                                    datasource="{{datasource.autoRenewTimeUnit}}"
                                    value="{=formData.autoRenewTimeUnit=}"
                                    track-id="ti_eip_group_create"
                                    track-name="选择续费周期/单位"
                                ></s-select>
                                <s-select
                                    datasource="{{datasource.autoRenewTime}}"
                                    value="{=formData.autoRenewTime=}"
                                    track-id="ti_eip_group_create"
                                    track-name="选择续费周期/时间"
                                ></s-select>
                                <span class="renew-expire-time">{{autoRenewTip}}</span>
                            </div>
                        </s-form-item>
                        <s-form-item label="{{'释放保护：'}}">
                            <s-radio-radio-group
                                enhanced
                                class="renew-item-wrapper"
                                datasource="{{deleteProtectList}}"
                                radioType="button"
                                value="{=deleteProtect=}"
                            />
                        </s-form-item>
                        <s-form-item label="{{'描述：'}}" class="desc" prop="desc">
                            <s-textarea
                                width="{{400}}"
                                height="{{88}}"
                                value="{=formData.desc=}"
                                placeholder="{{'请输入'}}"
                                maxLength="{{200}}"
                                track-id="ti_peerconn_desc"
                                track-name="备注"
                            >
                            </s-textarea>
                        </s-form-item>
                    </div>
                    <div class="resource-form-part-wrap form-part-wrap">
                        <h4>标签</h4>
                        <s-form-item prop="tag" label="绑定标签：">
                            <tag-edit-panel
                                create
                                version="v2"
                                instances="{{defaultInstances}}"
                                options="{{tagListRequster}}"
                                s-ref="tagPanel"
                            />
                        </s-form-item>
                    </div>
                    <div class="resource-form-part-wrap form-part-wrap">
                        <resource-group-panel
                            refreshAvailable="{{true}}"
                            sdk="{{resourceSDK}}"
                            on-change="resourceChange($event)"
                        />
                    </div>
                    <!--<div class="body-part-content">
                        <h4>{{'购买信息：'}}</h4>
                    </div>-->
                </s-form>
            </div>
            <order-confirm
                s-if="stepIndex === 1"
                s-ref="orderConfirm"
                sdk="{{newBillingSdk}}"
                use-coupon="{{isPrepay && FLAG.NetworkSupportXS}}"
                class="{{bucketLegendsClass}}"
                theme="default"
                showAgreementCheckbox
            />
            <div slot="pageFooter" class="buybucket {{inCrossWhite && isCross ? 'buybucket-cross' : ''}}">
                <div class="buybucket-widget" s-if="stepIndex === 0">
                    <div class="agreement" s-if="isCross">
                        <s-checkbox checked="{=agree=}" on-change="changeHandler" />同意<a
                            >《北京联通和客户跨境电路服务协议》</a
                        >
                    </div>
                    <div class="buybucket-container">
                        <s-tooltip placement="top" trigger="{{bucketDisabled && bucketConfirmTip ? 'hover' : ''}}">
                            <!--bca-disable-next-line-->
                            <span slot="content" s-html="{{bucketConfirmTip}}"></span>
                            <s-button
                                skin="primary"
                                size="large"
                                class="no-mg-bt"
                                on-click="onConfirm"
                                disabled="{{bucketDisabled}}"
                                >{{'确认订单'}}</s-button
                            >
                        </s-tooltip>
                        <!--bca-disable-next-line-->
                        <s-tooltip
                            s-if="{{(formData.productType === 'prepay') && !isCross && !FLAG.NetworkSupportXS}}"
                            placement="top"
                            trigger="{{(bucketDisabled || cartConfirming) ? 'hover' : ''}}"
                        >
                            <!--bca-disable-next-line-->
                            <span slot="content" s-html="{{bucketConfirmTip}}"></span>
                            <s-button
                                size="large"
                                on-click="addShoppingCart"
                                disabled="{{(bucketDisabled || cartConfirming)}}"
                                >{{'加入购物车'}}</s-button
                            >
                        </s-tooltip>
                        <s-button size="large" on-click="cancel">取消</s-button>
                        <shopping-cart
                            sdk="{{newBillingSdk}}"
                            on-reset="onReset"
                            addItemToCartAvailable="{{addItemToCartAvailable}}"
                            addItemToCartDisable="{=priceLoading=}"
                            on-change="onShoppingCartChange"
                            theme="default"
                        ></shopping-cart>
                    </div>
                </div>
                <div class="buybucket-widget" s-if="stepIndex === 1">
                    <div class="buybucket-container">
                        <div
                            class="buybucket-container-protocol {{inCrossWhite && isCross ? 'billing-protocol-cross' : ''}}"
                        >
                            <billing-protocol s-if="!FLAG.NetworkSupportXS" s-ref="billingProtocol" />
                            <s-button size="large" on-click="onBack" class="no-mg-bt">{{'上一步'}}</s-button>
                            <s-button size="large" on-click="cancel">取消</s-button>
                            <s-button skin="primary" size="large" disabled="{{disabledPay}}" on-click="onPay">
                                {{'提交订单'}}
                            </s-button>
                        </div>
                        <total-price sdk="{{newBillingSdk}}" s-if="!isCross" />
                    </div>
                </div>
            </div>
        </s-page>
    </div>
`;

@template(tpl)
@invokeAppComp
@invokeSUI
@invokeSUIBIZ
class PeerConnCreate extends Component {
    static components = {
        's-page': AppCreatePage,
        's-form': Form,
        's-form-item': Form.Item,
        's-input': Input,
        's-select': Select,
        's-button': Button,
        's-tip': Tip,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-switch': Switch,
        'order-confirm': OrderConfirm,
        's-checkbox': Checkbox,
        's-tip-button': TipButton,
        's-slider': Slider,
        's-input-number': InputNumber,
        's-tooltip': Tooltip,
        'resource-group-panel': ResourceGroupPanel,
        'shopping-cart': ShoppingCart,
        's-select-option': Select.Option,
        's-textarea': Input.TextArea,
        'total-price': TotalPrice,
        'billing-protocol': Protocol,
        'tag-edit-panel': TagEditPanel
    };
    initData() {
        const region = window.$context.getCurrentRegion();
        return {
            FLAG,
            DocService,
            confirmPageNav: {
                title: '确认订单',
                backLabel: '返回',
                backUrl: '/network/#/vpc/peerconn/list'
            },
            help: {
                localIfName: rule.PEERCONN.NAME.placeholder
            },
            withTip: true,
            rules: formValidator(this),
            isTrafficWhitelist: false,
            peerVpcErr: '',
            formData: {},
            datasource: {
                region: [
                    {
                        text: region.label,
                        value: region.id
                    }
                ],
                connType: PeerConnType.toArray(),
                regionList: [],
                peerVpcList: [],
                localVpcList: [],
                billingMethod: [
                    {text: '按使用流量计费', value: 'ByTraffic'},
                    {text: '按使用带宽计费', value: 'ByBandwidth'}
                ],
                purchaseLength: [
                    {text: '1个月', value: 1},
                    {text: '2', value: 2},
                    {text: '3', value: 3},
                    {text: '4', value: 4},
                    {text: '5', value: 5},
                    {text: '6', value: 6},
                    {text: '7', value: 7},
                    {text: '8', value: 8},
                    {text: '9', value: 9},
                    {text: '1年', value: 12, class: 'new-tip', mark: '8.3折'},
                    {text: '2年', value: 24, class: 'new-tip', mark: '8.3折'},
                    {text: '3年', value: 36, class: 'new-tip', mark: '8.3折'}
                ],
                bandwidthInMbps: {
                    min: 1,
                    max: 1000,
                    mid: 500,
                    step: 1,
                    length: 349,
                    unit: 'Mbps',
                    value: 1
                },
                autoRenewTimeUnit: TimeType.toArray(),
                autoRenewTime: Month.toArray()
            },
            accountPurchaseValidation: '',
            showPeerVpcIdLabel: false,
            quota: '', // 本端配额
            peerQuota: '', // 对端配额
            price: '',
            agree: false,
            listUrl: '#/vpc/peerconn/list',
            vpcInfo: '',
            inCrossWhite: false,
            disabledPay: false,
            auditStatus: 'AUDIT_SUCCESS',
            resourceSDK: new ResourceGroupSDK(window.$http, window.$context),
            bucketItems: [],
            bucketPrice: [],
            pageNav: {
                title: '创建对等连接',
                backLabel: '返回',
                backUrl: '/network/#/vpc/peerconn/list'
            },
            stepIndex: 0,
            steps: [
                {title: '基本配置', key: 'SELECT_CONFIG'},
                {title: '确认订单', key: 'ORDER'}
            ],
            autoRenewDatasource: [
                {
                    label: '开启',
                    value: true
                },
                {
                    label: '关闭',
                    value: false
                }
            ],
            localNameLength: 0,
            peerNameLength: 0,
            priceLoading: true,
            confirmedPay: false,
            addItemToCartAvailable: false,
            deleteProtect: false,
            deleteProtectList: [
                {text: '开启', value: true},
                {text: '关闭', value: false}
            ],
            defaultInstances: [
                {
                    tags: [
                        {
                            tagKey: '默认项目',
                            tagValue: ''
                        }
                    ]
                }
            ],
            tagListRequster: this.tagListRequster.bind(this),
            urlQuery: parseQuery(location.hash),
            bandwidthQuota: {}
        };
    }
    inited() {
        const peerconnType = this.data.get('urlQuery.type');
        this.data.set('vpcId', this.data.get('urlQuery').vpcId);
        if (peerconnType === 'cross') {
            this.data.set('pageNav.backUrl', '/network/#/vpc/crossPeerconn/list');
            this.toCross();
        }
        const client = new Client({}, {}.$context);
        // 创建实例
        const sdk = new BillingSdk({
            client,
            AllRegion: window.$context.getEnum('AllRegion'),
            context: contextPipe(this)
        });
        this.data.set('newBillingSdk', sdk);
        let region = u
            .intersection(u.keys(window.$context.SERVICE_TYPE.NETWORK.region), window.$context.RegionAvailable)
            .filter(item => item !== 'fsh'); // 上海region已下线
        const currentRegion = window.$context.getCurrentRegionId();
        let regionList = [];
        this.initBaseFormData();
        // 初始化计费方式
        this.initBillingMethod();
        if (!this.data.get('isCross')) {
            u.each(region, data => {
                // 如果当前是大陆，则屏蔽所有香港region
                // 如果当前是香港，则屏蔽所有大陆region
                if (!isCrossRegion(currentRegion, data) && !PeerConnDisableRegion.includes(data)) {
                    regionList.push({
                        value: data,
                        text: AllRegion.getTextFromValue(data)
                    });
                }
            });
            this.data.set('datasource.regionList', regionList);
            this.data.set('formData.peerRegionSelect', currentRegion);
        } else {
            u.each(region, data => {
                // 如果当前是大陆，展示所有跨境region
                // 如果当前是跨境，则展示所有大陆region
                if (isCrossRegion(currentRegion, data) && !PeerConnDisableRegion.includes(data)) {
                    regionList.push({
                        value: data,
                        text: AllRegion.getTextFromValue(data)
                    });
                }
            });
            this.data.set('datasource.regionList', regionList);
            regionList[0] && this.data.set('formData.peerRegionSelect', regionList[0].value);
        }
    }
    static computed = {
        isPrepay() {
            return this.data.get('formData').productType === 'prepay' && !this.data.get('isCross');
        },
        isCrossPrepay() {
            return this.data.get('formData').productType === 'prepay' && this.data.get('isCross');
        },
        // 是否为跨境
        isCross() {
            return this.data.get('urlQuery').type === 'cross';
        },
        showPeerBilling() {
            return this.data.get('isTrafficWhitelist') && this.data.get('formData.productType') === PayType.POSTPAY;
        },
        bucketConfirmTip() {
            let quota = this.data.get('quota');
            const peerQuota = this.data.get('peerQuota');
            let localRegion = this.data.get('formData.localRegion');
            let accountPurchaseValidation = this.data.get('accountPurchaseValidation');
            if (localRegion === AllRegion.HK02) {
                return '售罄！请您移步其他地域购买资源。';
            } else if (!window.$context.isVerifyUser()) {
                if (FLAG.NetworkSupportXS) {
                    return '温馨提示：您还没有实名认证，请先完成实名认证';
                } else {
                    return '温馨提示：您还没有实名认证，请立即去<a href="/qualify/#/qualify/index">认证</a>';
                }
            } else if (quota && (quota.vpcRemainQuota <= 0 || quota.accountRemainQuota <= 0)) {
                const module = quota.vpcRemainQuota <= 0 ? '网络' : '账号';
                const quotaName = quota.vpcRemainQuota <= 0 ? 'vpcPeerConnQuota' : 'accountPeerConnQuota';
                return (
                    `温馨提示：当前${module}对等连接超出限额，<a href="/quota_center/#/quota/apply/create?serviceType=PEERCONN` +
                    `&region=${window.$context.getCurrentRegionId()}` +
                    `&cloudCenterQuotaName=${quotaName}" target="_blank">去申请配额</a>`
                );
            } else if (peerQuota && (peerQuota.peerVpcRemainQuota <= 0 || peerQuota.peerAccountRemainQuota <= 0)) {
                const module = peerQuota.peerVpcRemainQuota <= 0 ? '对端网络' : '对端账号';
                const quotaName = peerQuota.peerVpcRemainQuota <= 0 ? 'vpcPeerConnQuota' : 'accountPeerConnQuota';
                return (
                    `温馨提示：当前${module}对等连接超出限额，<a href="/quota_center/#/quota/apply/create?serviceType=PEERCONN` +
                    `&region=${window.$context.getCurrentRegionId()}` +
                    `&cloudCenterQuotaName=${quotaName}" target="_blank">去申请配额</a>`
                );
            } else if (accountPurchaseValidation && !accountPurchaseValidation.status) {
                let link = '<a href="/finance/#/finance/account/recharge" target="_blank">';
                return accountPurchaseValidation.failReason
                    ? accountPurchaseValidation.failReason + `请及时${link}充值</a>`
                    : '';
            } else if (this.data.get('isCross') && !this.data.get('agree')) {
                return '请阅读服务协议并勾选同意';
            }
            return '';
        },
        bucketDisabled() {
            const localRegion = this.data.get('formData.localRegion');
            const quota = this.data.get('quota');
            const {vpcRemainQuota, accountRemainQuota} = quota;
            // 新增校验对端账户和vpc配额
            const peerQuota = this.data.get('peerQuota');
            const {peerVpcRemainQuota, peerAccountRemainQuota} = peerQuota;
            return (
                localRegion === AllRegion.HK02 ||
                !quota ||
                vpcRemainQuota <= 0 ||
                accountRemainQuota <= 0 ||
                !peerQuota ||
                peerVpcRemainQuota <= 0 ||
                peerAccountRemainQuota <= 0 ||
                !window.$context.isVerifyUser() ||
                !this.data.get('accountPurchaseValidation') ||
                !this.data.get('accountPurchaseValidation.status') ||
                (this.data.get('isCross') && !this.data.get('agree')) ||
                this.data.get('priceLoading')
            );
        },
        productTypeList() {
            let formData = this.data.get('formData');
            if (
                !this.data.get('isCross') &&
                formData.localRegion &&
                ((formData.connType === 'diff' &&
                    (!formData.peerRegion || formData.localRegion === formData.peerRegion)) ||
                    (formData.connType === 'same' && formData.localRegion === formData.peerRegionSelect))
            ) {
                return [
                    {
                        text: '包年包月',
                        value: 'prepay',
                        disabled: true,
                        imgSrc: 'https://bce.bdstatic.com/network-frontend/prepay_disabled.png',
                        desc: '先付费后使用，价格更低廉'
                    },
                    {
                        text: '按量付费',
                        value: 'postpay',
                        imgSrc: 'http://bce.bdstatic.com/network-frontend/postpay.png',
                        desc: '先使用后付费，按需开通'
                    }
                ];
            } else if (this.data.get('isCross')) {
                return [
                    {
                        text: '包年包月',
                        value: 'prepay',
                        imgSrc: 'http://bce.bdstatic.com/network-frontend/prepay.png',
                        desc: '先付费后使用，价格更低廉'
                    }
                    // { text: '按量付费', value: 'postpay', disabled: true, imgSrc: POSTPAY_IMG, desc: '先使用后付费，按需开通'  }
                ];
            }
            return [
                {
                    text: '包年包月',
                    value: 'prepay',
                    imgSrc: 'http://bce.bdstatic.com/network-frontend/prepay.png',
                    desc: '先付费后使用，价格更低廉'
                },
                {
                    text: '按量付费',
                    value: 'postpay',
                    imgSrc: 'http://bce.bdstatic.com/network-frontend/postpay.png',
                    desc: '先使用后付费，按需开通'
                }
            ];
        },
        autoRenewTip() {
            let autoRenewTimeUnit = this.data.get('formData.autoRenewTimeUnit');
            let autoRenewTime = this.data.get('formData.autoRenewTime');
            let autoRenwText = autoRenewTimeUnit === TimeType.YEAR ? '年' : '月';
            return `系统将于到期前7天进行扣费，扣费时长为${autoRenewTime}${autoRenwText}`;
        },
        marks() {
            let min = this.data.get('datasource.bandwidthInMbps.min');
            let max = this.data.get('datasource.bandwidthInMbps.max');
            return getMarksByStep(max, min, 4);
        },
        disabledTip() {
            const isCross = this.data.get('isCross');
            const productTypeList = this.data.get('productTypeList');
            const disabledProductType = productTypeList.find(item => item.disabled);
            return isCross
                ? '当前模式下不支持按量付费'
                : disabledProductType
                  ? `本端和对端地域相同，不支持${disabledProductType.text}支付`
                  : '';
        },
        bucketLegendsClass() {
            let formData = this.data.get('formData');
            if (
                formData.billingMethod !== 'ByTraffic' &&
                formData.productType !== 'prepay' &&
                formData.localRegion !== formData.peerRegion
            ) {
                return 'bucket-legends-peerRegion-class';
            }
            if (this.data.get('isCross')) {
                return 'bucket-legends-isCross';
            }
            return '';
        },
        isSameRegion() {
            const connType = this.data.get('formData.connType');
            const localRegion = this.data.get('formData.localRegion');
            const peerRegion = this.data.get('formData.peerRegion');
            const peerRegionSelect = this.data.get('formData.peerRegionSelect');
            return connType === 'same' ? localRegion === peerRegionSelect : localRegion === peerRegion;
        },
        regionSwitchTipContent() {
            return `如需修改购买其他区域产品，请${!FLAG.NetworkSupportXS ? '前往主导航进行切换' : '在顶栏重新选择区域'}`;
        },
        vpcOverlapWarningText() {
            const docUrl = this.data.get('DocService.peer_practice');
            return `本端VPC与对端VPC重叠，请用户注意子网规划和路由配置，可参考<a href="${docUrl}" target="_blank" rel="noopener" track-id="ti_vpc_peerconn_create_doc" track-name="对等连接最佳实践">对等连接最佳实践</a>`;
        },
    };

    initBillingMethod() {
        const whiteList = window.$storage.get('commonWhite');
        if (whiteList?.PeerConnTro95WhiteList) {
            this.data.push('datasource.billingMethod', {text: '按传统型95计费', value: 'ByTraditional95'});
        }
    }

    initBaseFormData() {
        const isCross = this.data.get('isCross');
        const region = window.$context.getCurrentRegion();
        const formData = {
            productType: isCross ? 'prepay' : 'postpay',
            localRegion: region.id,
            localIfName: '',
            peerAccountId: '',
            connType: PeerConnType.SAME,
            peerRegion: '',
            peerVpcId: '',
            peerIfName: '',
            bandwidthInMbps: '1000',
            purchaseLength: 1,
            billingMethod: 'ByBandwidth',
            vpcInfo: {},
            autoRenew: false,
            autoRenewTime: 1,
            autoRenewTimeUnit: TimeType.MONTH
        };
        this.data.set('formData', formData);
    }
    toCross() {
        const {vpcId, auditStatus} = this.data.get('');
        const redirectLink = {
            TO_AUDIT: '#/vpc/peerconn/create/audit',
            AUDITING: '#/vpc/peerconn/create/auditing',
            AUDIT_FAILED: '#/vpc/peerconn/create/auditing',
            AUDIT_SUCCESS: '#/vpc/peerconn/create_cross'
        };
        const link = `${redirectLink[auditStatus]}?vpcId=${vpcId}&type=cross`;
        location.hash = link;
    }
    toNotCross() {
        const {vpcId} = this.data.get('');
        location.hash = `#/vpc/peerconn/create/v2?vpcId=${vpcId}&type=normal`;
    }
    attached() {
        // 获取vpcInfo
        this.getVpcList();
        // 获取带宽上限
        this.getBandWidthMax();
        // 初始化buyket购买配置
        this.setConfigDetail();
        // 获取白名单配置
        const whiteList = window.$storage.get('commonWhite');
        this.data.set('isTrafficWhitelist', whiteList?.PeerConnPayByTrafficWhiteList);

        // 如果有localIfId需要加载下默认的对等连接详情信息
        let localIfId = this.data.get('urlQuery.localIfId');
        if (localIfId) {
            this.peerconnDetail(localIfId);
        }
        // 获取用户跨境对等连接审核状态
        this.crossWhiteList();
        this.checkValidation();
        // 本端VPC配额
        this.watch('formData.vpcInfo', item => {
            this.getPeerVpcList();
            this.getQuota('local');
        });
        this.watch('formData.productType', item => {
            // 1.预付费展示购买信息模块，后付费不展示
            // 2.后付费且isTrafficWhitelist,可选计费方式
            this.setConfigDetail();
            // 3.切换付费方式需要校验账户状态，如预付费下账户欠费不能购买
            this.checkValidation().then(() => this.changeHandler());
        });
        this.watch('formData.purchaseLength', item => {
            this.changeHandler();
        });
        this.watch('formData.billingMethod', item => {
            this.changeHandler();
        });
        this.watch('formData.peerVpcId', item => {
            if (/^[vpc-][a-z\w\-]{15}$/.test(item)) {
                this.checkVpc();
            }
            if (this.data.get('formData.connType') === 'same') {
                this.getQuota('peer');
                this.getPrice();
            } else {
                // 校验对端账户和VPC配额
                const peerAccountId = this.data.get('formData.peerAccountId');
                const peerRegion = this.data.get('formData.peerRegion');
                if (peerAccountId && peerRegion) {
                    this.getQuota('peer');
                }
            }
        });
        this.watch('formData.connType', item => {
            this.data.set('formData.peerRegionLabel', '');
            // 切换连接类型重置为后付费
            !this.data.get('isCross') && this.data.set('formData.productType', 'postpay');
            // 改变购买类型要去拉一下校验vpc的接口
            this.data.set('formData.peerVpcId', '');
            this.checkVpc();
        });
        this.watch('formData.peerAccountId', item => {
            // 输入对端账户id需要检验其合法性
            if (/^[a-z\w\-]{32,40}$/.test(item)) {
                this.checkVpc();
                // 校验对端账户和vpc配额
                const peerVpcId = this.data.get('formData.peerVpcId');
                const peerRegion = this.data.get('formData.peerRegion');
                if (peerVpcId && peerRegion) {
                    this.getQuota('peer');
                }
            }
        });
        this.watch('peerQuota', value => {
            // 对端配额加载完毕再检查下vpc
            if (value) {
                this.checkVpc();
            }
        });
        this.watch('formData.peerRegion', value => {
            if (value) {
                this.getQuota('peer');
            }
        });
        this.watch(
            'formData.bandwidthInMbps',
            u.debounce(item => {
                if (item < this.data.get('datasource.bandwidthInMbps.min')) {
                    this.data.set('formData.bandwidthInMbps', this.data.get('datasource.bandwidthInMbps.min'));
                } else if (item > this.data.get('datasource.bandwidthInMbps.max')) {
                    this.data.set('formData.bandwidthInMbps', this.data.get('datasource.bandwidthInMbps.max'));
                }
                this.changeHandler();
            }, 300)
        );
        this.watch('formData.peerRegionSelect', item => {
            if (this.data.get('formData.vpcInfo.shortId')) {
                this.data.set('datasource.peerVpcList', []);
                this.data.set('formData.peerVpcId', '');
                // 切换对端region重新拉取vpcList
                this.getPeerVpcList();
            }
        });
        this.watch('formData.autoRenewTime', value => {
            this.setConfigDetail();
        });
        this.watch('formData.autoRenewTimeUnit', value => {
            this.setConfigDetail();
            this.data.set('datasource.autoRenewTime', value === TimeType.YEAR ? Year.toArray() : Month.toArray());
            // 单位变了重置周期时间
            this.data.set('formData.autoRenewTime', 1);
        });
        this.watch('isSameRegion', value => {
            this.setBandwidthInMbps(value);
        });
    }

    initFormData() {
        let peerRegion = this.data.get('datasource.regionList[0]');
        this.data.set('formData', {
            productType: this.data.get('isCross') ? 'prepay' : 'postpay',
            localRegion: this.data.get('datasource.region[0].value'),
            localIfName: '',
            peerAccountId: '',
            connType: this.data.get('datasource.connType[0].value'),
            peerRegionSelect: peerRegion ? peerRegion.value : '',
            peerRegion: '',
            peerVpcId: '',
            peerIfName: '',
            bandwidthInMbps: '1000',
            purchaseLength: this.data.get('datasource.purchaseLength[0].value'),
            billingMethod: 'ByBandwidth',
            vpcInfo: this.data.get('datasource.localVpcList')[0].value,
            autoRenew: true,
            autoRenewTime: 1,
            autoRenewTimeUnit: TimeType.MONTH
        });
    }

    cancel() {
        const peerconnType = this.data.get('urlQuery.type');
        const listUrl = peerconnType === 'cross' ? 'crossPeerconn' : 'peerconn';
        location.hash = `#/vpc/${listUrl}/list`;
    }

    // 校验对端地域
    // 跨账号是用户自己输入对端网络，所以需要在这里也限制一下
    // 香港到大陆，大陆到香港的，都不允许
    check() {
        const currentRegion = window.$context.getCurrentRegionId();
        let peerRegion = this.data.get('formData.peerRegion');
        let isCross = this.data.get('isCross');
        if (!peerRegion) {
            return false;
        } else if (isCross && !isCrossRegion(currentRegion, peerRegion)) {
            // 为测试加的逻辑
            // return false;
        } else if (!isCross && isCrossRegion(currentRegion, peerRegion)) {
            // return false;
        }
        return true;
    }

    setBandwidthInMbps(isSameRegion) {
        // bandwidth 同地域带宽  crossBandwidth 跨地域带宽
        const {bandwidth, crossBandwidth} = this.data.get('bandwidthQuota');
        const currBandwidth = isSameRegion ? bandwidth : crossBandwidth;
        this.data.set('datasource.bandwidthInMbps.max', currBandwidth);
        this.data.set('datasource.bandwidthInMbps.mid', Math.floor(currBandwidth / 2));
        this.data.set('formData.bandwidthInMbps', currBandwidth);
    }

    getBandWidthMax() {
        this.$http.getPeerconnBandwidthQuota().then(data => {
            this.data.set('bandwidthQuota', data || {});
            const isSameRegion = this.data.get('isSameRegion');
            this.setBandwidthInMbps(isSameRegion);
        });
    }

    // 校验对端网络场景
    // 1.选择连接类型
    // 2.切换或输入对端网络
    // 3.对端账户ID改变
    // 校验对端网络
    checkVpc(type) {
        if (!this.checkGlobal()) {
            return false;
        }
        let isDiff = this.data.get('formData.connType') === PeerConnType.DIFF;
        let data = {
            connType: this.data.get('formData.connType'),
            localVpcId: this.data.get('formData.vpcInfo.shortId'),
            peerVpcId: this.data.get('formData.peerVpcId')
        };
        if (isDiff) {
            let peerAccountId = this.data.get('formData.peerAccountId');
            // 还要校验下合法性
            if (!/^[a-z\w\-]{32,40}$/.test(peerAccountId)) {
                return false;
            } else if (peerAccountId === window.$context.getUserId()) {
                return false;
            }
            data.peerAccountId = this.data.get('formData.peerAccountId');
        }
        if (data.peerVpcId === data.localVpcId) {
            return false;
        }
        if (!/^[vpc-][a-z\w\-]{15}$/.test(data.peerVpcId)) {
            return false;
        }
        return this.$http.peerconnCheckVpc(data, kXhrOptions).then(result => {
            this.data.set('formData.peerVpcStatus', result);
            this.data.set('peerVpcErr', '');
            this.data.set('showPeerVpcIdLabel', false);
            this.data.set('formData.peerVpcStatus.isCross', this.data.get('isCross'));
            if (!result.cidrValidate) {
                this.data.set('showPeerVpcIdLabel', true);
            } else {
                this.data.set('showPeerVpcIdLabel', false);
            }
            if (isDiff && !result.peerVpcIdValidate) {
                this.data.set('showPeerVpcIdLabel', true);
                this.data.set('peerVpcErr', '输入的对端网络ID不合法');
                return;
            } else if (isDiff && result.peerVpcIsExist) {
                this.data.set('showPeerVpcIdLabel', true);
                this.data.set('peerVpcErr', '输入的对端网络ID重复');
                return;
            } else if (!result.relayVpcValidate) {
                this.data.set('showPeerVpcIdLabel', true);
                this.data.set(
                    'peerVpcErr',
                    '禁止在两个开启了路由中继功能的VPC之间建立对等连接，请至少关闭一端VPC的路由中继功能'
                );
                return;
            }
            if (data.peerAccountId && !result.peerAccountIdValidate) {
                this.data.set('showPeerVpcIdLabel', true);
                this.data.set('peerVpcErr', '对端账户ID不合法');
                return;
            }
            // 为测试加的逻辑
            // if (this.data.get('isCross')
            // && !isCrossRegion(this.data.get('formData.localRegion'), this.data.get('formData.peerRegion'))) {
            //     this.data.set('showPeerVpcIdLabel', true);
            //     this.data.set('peerVpcErr', '该网络非跨境对端网络');
            //     return;
            // }
            // if (!this.data.get('isCross')
            // && isCrossRegion(this.data.get('formData.localRegion'), this.data.get('formData.peerRegion'))) {
            //     this.data.set('showPeerVpcIdLabel', true);
            //     this.data.set('peerVpcErr', '该网络为跨境对端网络，暂不支持跨境对端传输');
            //     return;
            // }
            this.data.set('formData.peerRegionLabel', AllRegion.getTextFromValue(result.peerRegion));
            this.data.set('formData.peerRegion', result.peerRegion);
            if (this.check() && !type) {
                this.getPrice();
            }
        });
    }

    // 全局校验账户
    // 校验对端VPC的时候再拉一次全局校验
    checkGlobal() {
        const localQuota = this.data.get('quota');
        const peerQuota = this.data.get('peerQuota');

        if (!this.data.get('accountPurchaseValidation') || !localQuota) {
            return false;
        }
        if (this.data.get('bucketConfirmTip') && !this.data.get('isCross')) {
            return false;
        }
        return true;
    }

    // 条件改变时改变所选配置信息
    // 1.所选带宽改变
    // 2.带宽计费方式改变
    // 3.改变购买时长
    // 4.切换自动续费开启状态
    // 5.自动续费时长或单位发生变化
    changeHandler() {
        this.setConfigDetail();
        // peerregion只有在对端网络校验完毕之后才会设置，如果没有需要先校验vpc
        if (this.check()) {
            this.getPrice();
        } else {
            this.checkVpc();
        }
    }

    // 询价接口
    // 购买时长，计费方式，所选带宽，自动续费相关变更且通过check校验询价
    // 如有localId在初始化拉完对等连接详情之后也要询价
    // 校验对端vpc通过后也触发询价
    getPrice() {
        const {newBillingSdk, formData} = this.data.get();
        if (!this.data.get('isCross') && formData.productType === PayType.PREPAY) {
            // 后付费带宽与预付费询价一样
            formData.billingMethod = 'ByBandwidth';
        }
        let config;
        let currentRegion = window.$context.getCurrentRegionId();
        const peerRegion =
            this.data.get('formData.connType') === 'same' ? formData.peerRegionSelect : formData.peerRegion;
        let regionStr = [peerRegion, currentRegion].sort((a, b) => {
            return a.localeCompare(b);
        });
        if (!this.data.get('isCross')) {
            // 对等连接无后付费按带宽询价，将后付费按带宽的参数整为按流量
            const chargeItemNameMapping = {
                ByBandwidth: 'Cpt2',
                ByTraffic: `NetTraffic_${AllRegion[regionStr[0]]}_${AllRegion[regionStr[1]]}`,
                ByTraditional95: `Traditional95_${AllRegion[regionStr[0]]}_${AllRegion[regionStr[1]]}`
            };
            config = {
                serviceType: 'PEERCONN',
                serviceName: '对等连接',
                configName: '配置费用',
                chargeItem: chargeItemNameMapping[formData.billingMethod],
                productType: formData.billingMethod === 'ByBandwidth' ? PayType.PREPAY : PayType.POSTPAY,
                region: currentRegion,
                scene: 'NEW',
                orderType: 'NEW',
                count: 1,
                flavor: [
                    {
                        name: 'subServiceType',
                        value: 'default',
                        scale: 1
                    },
                    {
                        name: `${regionStr[0]}-${regionStr[1]}-bandwidth`,
                        value: formData.bandwidthInMbps,
                        scale: 1
                    }
                ]
            };
            if (formData.productType === PayType.PREPAY) {
                config.duration = formData.purchaseLength;
                config.timeUnit = 'MONTH';
                config.configName = '配置费用';
            }
            if (
                (formData.billingMethod === 'ByTraffic' && AllRegion[regionStr[0]] === AllRegion[regionStr[1]]) ||
                (formData.billingMethod !== 'ByTraffic' && formData.productType !== PayType.PREPAY)
            ) {
                config.amount = '1000000000';
                config.chargeItem = `NetTraffic_${AllRegion[regionStr[0]]}_${AllRegion[regionStr[1]]}`;
                config.productType = 'postpay';
                config.flavor.splice(1, 1);
            }
            // if (isExistNewRegion && formData.billingMethod === 'ByBandwidth') {
            //     config.amount = '1000000000';
            //     config.chargeItem = `OutBytes_${AllRegion[regionStr[0]]}_${AllRegion[regionStr[1]]}`;
            //     config.productType = 'postpay';
            //     config.flavor.splice(1, 1);
            // }
            if (formData.billingMethod === 'ByTraffic' && AllRegion[regionStr[0]] !== AllRegion[regionStr[1]]) {
                config.amount = '1000000000';
            }
            formData.billingMethod === 'ByTraffic' && (config.unitText = 'GB');
        } else {
            let queryRegion = isMainRegion(formData.peerRegion) ? currentRegion : formData.peerRegion;
            config = {
                serviceType: 'MKT',
                serviceName: '跨境对等连接',
                configName: '配置费用',
                subServiceType: 'default',
                productType: 'RESERVED',
                chargeItem: 'Cpt2',
                region: currentRegion,
                scene: 'NEW',
                orderType: 'NEW',
                currency: 'CNY',
                count: 1,
                timeUnit: 'MONTH',
                flavor: [
                    {
                        name: 'subServiceType',
                        value: 'peerconn',
                        scale: 1
                    },
                    {
                        name: `Mainland-${queryRegion}-bandwidth`,
                        value: formData.bandwidthInMbps,
                        scale: 1
                    }
                ]
            };
            if (formData.productType === PayType.PREPAY) {
                config.duration = formData.purchaseLength;
            }
        }
        config.configDetail = this.getConfigDetail();
        newBillingSdk.clearItems();
        const orderItem = new OrderItem(config);
        this.data.set('bucketItems', [orderItem]);
        newBillingSdk.addItems([orderItem]);
    }

    getVpcList() {
        return this.$http.vpcList().then(data => {
            let vpcs = u.map(data, item => ({text: `${item.name}（${item.cidr}）`, value: item}));
            this.data.set('datasource.localVpcList', vpcs);
            if (this.data.get('vpcId') && this.data.get('vpcId') !== 'undefined') {
                u.each(vpcs, item => {
                    if (item.value.vpcId === this.data.get('vpcId')) {
                        this.data.set('formData.vpcInfo', item.value);
                    }
                });
            } else {
                this.data.set('formData.vpcInfo', vpcs[0].value);
            }
        });
    }

    // 初始化和切换Region的时候执行
    getPeerVpcList() {
        let region = this.data.get('formData.peerRegionSelect');
        if (!region) {
            // 为测试加的逻辑
            // return;
        }
        this.$http
            .peerVpcList(
                {
                    localVpcId: this.data.get('formData.vpcInfo.vpcId')
                },
                {headers: {region}}
            )
            .then(data => {
                let result = [];
                u.each(data.vpcs, item => {
                    result.push({
                        text: `${item.name}（${item.cidr}）`,
                        value: item.shortId
                    });
                });
                this.data.set('datasource.peerVpcList', result);
                this.checkVpc();
            });
    }

    // 判断账户状态
    // 初始化的时候执行&&切换付费方式的时候执行
    checkValidation() {
        return this.$http
            .purchaseValidation(
                {
                    serviceType: 'PEERCONN',
                    productType: this.data.get('formData.productType')
                },
                kXhrOptions
            )
            .then(data => {
                this.data.set('accountPurchaseValidation', data);
            });
    }

    // 获取配额
    // 初始化的时候执行
    getQuota(type) {
        const localVpcId = this.data.get('formData.vpcInfo.shortId');
        const payload = {
            localVpcId
        };
        if (type === 'peer') {
            const connType = this.data.get('formData.connType');
            const peerRegion =
                connType === 'same' ? this.data.get('formData.peerRegionSelect') : this.data.get('formData.peerRegion');
            const peerVpcId = this.data.get('formData.peerVpcId');
            const peerAccountId = this.data.get('formData.peerAccountId');

            payload.peerRegion = peerRegion;
            payload.peerVpcId = peerVpcId;
            payload.connType = connType;
            payload.peerAccountId = peerAccountId;
        }
        this.$http.peerconnQuota(payload, kXhrOptions).then(data => {
            if (type === 'local') {
                this.data.set('quota', data);
            } else {
                this.data.set('peerQuota', data);
            }
        });
    }

    // 获取已有对等连接详情
    // 初始化时候执行 如果有本地订单id
    peerconnDetail(localIfId) {
        this.$http.peerConnDetail({localIfId}, kXhrOptions).then(data => {
            this.data.set('formData.localIfName', data.localIfName);
            this.data.set('formData.connType', data.connType);
            this.data.set('formData.peerVpcId', data.peerVpcShortId);
            if (data.connType === PeerConnType.DIFF) {
                this.data.set('formData.peerAccountId', data.peerAccountId);
            }
            this.data.set('formData.peerRegion', AllRegion.getTextFromValue(data.peerRegion));
            this.data.set('formData.peerRegion', data.peerRegion);
            this.data.set('formData.desc', data.desc);
            this.getPrice();
        });
    }

    // 获取用户白名单跨境对等连接
    crossWhiteList() {
        const whiteList = window.$storage.get('commonWhite');
        this.data.set('inCrossWhite', whiteList?.CrossBorderWhiteList);
        if (whiteList?.CrossBorderWhiteList) {
            this.getCrossAuditStatus();
        }
    }

    // 获取用户跨境对等连接审核状态
    getCrossAuditStatus() {
        this.$http.quertAuditStatus().then(data => {
            this.data.set('auditStatus', data.auditStatus);
            const peerconnType = this.data.get('urlQuery.type');
            if (peerconnType === 'cross') {
                this.toCross();
            }
        });
    }

    resourceChange({value}) {
        this.data.set('formData.resourceGroupId', value.data.groupId);
    }

    async onConfirm() {
        const form = this.ref('form');
        if (this.data.get('peerVpcErr') || !this.data.get('formData.peerVpcId')) {
            return;
        }
        if (!this.data.get('formData.peerRegion')) {
            await this.checkVpc('confirm');
        }
        await form.validateFields();
        try {
            await this.ref('tagPanel').validate(false);
        } catch (error) {
            return;
        }
        let tagData = await this.ref('tagPanel').getTags();
        this.data.set('tagData', tagData || {});
        const formData = this.data.get('formData');
        let chargeType = '';
        if (formData.productType === PayType.PREPAY) {
            chargeType = '包年包月';
        } else {
            const chargeTypeMap = {
                ByBandwidth: '按带宽计费',
                ByTraffic: '按使用流量计费',
                ByTraditional95: '按传统型95计费'
            };
            chargeType = chargeTypeMap[formData.billingMethod];
        }
        const peerRegion = formData.peerRegion;
        // 确认订单页的字段展示，待补充
        let configDetail = [
            {label: '地域', value: window.$context.getCurrentRegion().label},
            {
                label: '对端地域',
                value: AllRegion.getTextFromValue(peerRegion)
            },
            {
                label: '带宽上限',
                value: `${formData.bandwidthInMbps}Mbps`
            },
            {
                label: '计费方式',
                value: chargeType
            }
        ];
        if (this.data.get('isCross')) {
            configDetail.unshift({
                label: '付费方式',
                value: '预付费'
            });
        }
        const orderItem = this.data.get('bucketItems');
        orderItem[0].updateConfigDetail(configDetail);

        if (formData.billingMethod !== 'ByTraffic' && formData.productType !== 'prepay') {
            let priceError = '';
            if (formData.localRegion !== formData.peerRegion) {
                if (formData.billingMethod === 'ByTraditional95') {
                    priceError = '按传统型95计费';
                } else {
                    priceError = '按日峰值带宽计费';
                }
            } else {
                if (formData.billingMethod === 'ByTraditional95') {
                    priceError = '￥0';
                } else {
                    priceError = '￥0.00/分钟';
                }
            }
            orderItem[0].updatePriceError(priceError);
        }
        this.data.set('stepIndex', 1);
    }

    getConfig() {
        let formData = this.data.get('formData');
        let data = {
            billing: {
                paymentTiming: formData.productType === PayType.PREPAY ? 'Prepaid' : 'Postpaid',
                billingMethod: formData.billingMethod,
                reservation: {
                    reservationLength: u.clone(formData.purchaseLength),
                    reservationTimeUnit: 'month'
                }
            },
            localVpcId: this.data.get('formData.vpcInfo').shortId,
            serviceType: this.data.get('isCross') ? 'MKT' : 'PEERCONN',
            resourceGroupId: formData.resourceGroupId,
            deleteProtect: this.data.get('deleteProtect')
        };
        u.extend(data, formData);
        if (formData.productType === PayType.PREPAY && formData.autoRenew) {
            data.renewReservation = {
                reservationLength: formData.autoRenewTime,
                reservationTimeUnit: formData.autoRenewTimeUnit
            };
            const isCross = this.data.get('isCross');
            if (isCross) {
                // 跨境将按年处理成按月
                if (data?.renewReservation?.reservationTimeUnit === 'year') {
                    data.renewReservation.reservationLength = data.renewReservation.reservationLength * 12;
                }
                delete data.renewReservation.reservationTimeUnit;
            }
        }
        // 为测试加的逻辑
        // if (this.data.get('isCross')) {
        //     data.peerRegion = 'sin';
        // }
        delete data.purchaseLength;
        delete data.region;
        delete data.autoRenew;
        delete data.autoRenewTime;
        delete data.autoRenewTimeUnit;
        delete data.productType;
        delete data.peerVpcStatus;
        delete data.peerRegionLabel;
        delete data.peerRegionSelect;
        delete data.vpcInfo;
        if (data.connType === 'same') {
            delete data.peerAccountId;
        }
        return data;
    }
    onBack() {
        this.data.set('stepIndex', 0);
    }

    async addShoppingCart() {
        const form = this.ref('form');
        if (this.data.get('peerVpcErr')) {
            return;
        }
        if (!this.data.get('formData.peerRegion')) {
            await this.checkVpc();
        }
        await form.validateFields();
        this.data.set('cartConfirming', true);
        this.getPrice().then(() => {
            let config = this.getConfig();
            this.$http
                .addPeerShoppingCart({
                    paymentMethod: [],
                    items: [
                        {
                            config: config,
                            paymentMethod: []
                        }
                    ]
                })
                .then(result => {
                    this.data.set('cartConfirming', false);
                    window.shoppingCart?.showSuccessTip();
                    window.shoppingCart?.refreshCount();
                })
                .catch(result => {
                    this.data.set('cartConfirming', false);
                });
        });
    }

    async onPay() {
        !FLAG.NetworkSupportXS && (await this.ref('billingProtocol').validateAgreement());
        this.data.set('disabledPay', true);
        let {newBillingSdk, bucketItems} = this.data.get();
        let params = {
            items: [
                {
                    config: {
                        tags: this.data.get('tagData'),
                        ...this.getConfig()
                    },
                    paymentMethod: bucketItems[0].couponId ? [{type: 'coupon', values: [bucketItems[0].couponId]}] : []
                }
            ]
        };
        try {
            let confirmUrl = this.data.get('isCross')
                ? '/api/peerconn/crossBorder/order/confirm/new'
                : '/api/peerconn/peerconn/order/confirm/new'; // eslint-disable-line
            let fromService = this.data.get('isCross') ? 'MKT' : 'PEERCONN';
            const data = await this.$http.newConfirmOrder(confirmUrl, params);
            let url = '';
            try {
                const info = await newBillingSdk.checkPayInfo(data);
                url = info.url + `&fromService=${fromService}`;
                info.url && (location.href = url);
            } catch (info) {
                // 跳转到相应页面
                url = info.url + `&fromService=${fromService}`;
                info.url && (location.href = url);
            }
        } catch (err) {}
        this.data.set('disabledPay', false);
    }
    onReset() {
        this.initFormData();
    }
    onRegionChange() {
        const currentRegion = window.$context.getCurrentRegionId();
        if (PeerConnDisableRegion.includes(currentRegion)) {
            location.hash = '#/vpc/instance/list';
        } else {
            location.hash = '#/vpc/peerconn/list';
        }
    }
    peerRegionChange({value}) {
        if (value === window.$context.getCurrentRegionId()) {
            this.data.set('formData.productType', PayType.POSTPAY);
        }
    }
    inputchange(e) {
        let val = +e.value;
        let bandwidthInMbps = this.data.get('datasource.bandwidthInMbps');
        if (isNaN(val)) {
            return;
        }
        val = val < bandwidthInMbps.min ? bandwidthInMbps.min : val;
        val = val > bandwidthInMbps.max ? bandwidthInMbps.max : val;
        this.data.set('formData.bandwidthInMbps', val);
    }
    onSelectPayType(item) {
        const currPayType = this.data.get('formData.productType');
        if (!item.disabled) {
            if (currPayType !== item.value) {
                this.data.set('formData.productType', item.value);
            }
        }
    }
    // 输入网关名称时统计字符时（近似统计）
    handleNameInput(type, {value}) {
        const currNameLength = type === 'local' ? 'localNameLength' : 'peerNameLength';
        if (value?.length <= 65) {
            this.data.set(currNameLength, value.length);
        } else {
            this.data.set(currNameLength, 65);
        }
    }
    // 新增onShoppingCartChange
    onShoppingCartChange(e) {
        this.data.set('priceLoading', UPDATE_STATUS[e] !== UPDATE_STATUS.DONE);
        if (UPDATE_STATUS[e] === UPDATE_STATUS.DONE) {
            const bucketItems = this.data.get('bucketItems');
            const {newBillingSdk, formData} = this.data.get();
            let unitPrice = bucketItems[0]?.unitPrice;
            let priceError = bucketItems[0]?.priceError;
            // 用来判断是否需要二次询价
            if (
                formData.productType === 'postpay' &&
                (unitPrice || unitPrice === 0) &&
                !priceError &&
                formData.billingMethod !== 'ByTraffic'
            ) {
                newBillingSdk.clearItems();
                if (formData.localRegion !== formData.peerRegion) {
                    if (formData.billingMethod === 'ByTraditional95') {
                        bucketItems[0].priceError = '按传统型95计费';
                    } else {
                        bucketItems[0].priceError = '按日峰值带宽计费';
                    }
                } else {
                    if (formData.productType !== PayType.PREPAY) {
                        unitPrice = '0.00';
                    }
                    let priceError = '￥' + (unitPrice || '0.00') + '/分钟';
                    bucketItems[0].priceError = priceError;
                }
                this.data.set('bucketItems', bucketItems);
                newBillingSdk.addItems(bucketItems);
            }
            const isCrossPrepay = this.data.get('isCrossPrepay');
            if (isCrossPrepay) {
                this.nextTick(() => {
                    const el = document.querySelectorAll('.shopping-cart-item-price-wrapper > .price')?.[0];
                    if (el) {
                        // bca-disable-line
                        el.innerHTML = el?.innerHTML?.replace('/月', '');
                    }
                });
            }
        }
    }
    setConfigDetail(unitPrice, sslPrice) {
        let configuration = this.getConfigDetail(unitPrice, sslPrice);
        const orderItem = this.data.get('bucketItems');
        if (!orderItem[0]) {
            return;
        }
        orderItem[0].updateConfigDetail(configuration);
    }
    getConfigDetail() {
        const formData = this.data.get('formData');
        const region = window.$context.getCurrentRegionId();
        let countConfig =
            formData.productType === PayType.PREPAY
                ? `1个 * ${formData.purchaseLength}个月${formData.autoRenew
                    ? `开通自动续费${formData.autoRenewTime}${formData.autoRenewTimeUnit === TimeType.YEAR ? '年' : '个月'}`
                    : ''}`
                : '1个';

        let datasource = [
            {label: '当前地域', value: AllRegion.getTextFromValue(region)},
            {label: '对端地域', value: formData.peerRegion ? AllRegion.getTextFromValue(formData.peerRegion) : '-'},
            {label: '带宽上限', value: formData.bandwidthInMbps ? formData.bandwidthInMbps + 'Mbps' : '-'},
            {label: '购买数量', value: countConfig}
        ];
        return datasource;
    }
    tagListRequster() {
        return this.$http.getSearchTagList({serviceType: [], region: ['global']});
    }
}

export default San2React(Processor.autowireUnCheckCmpt(PeerConnCreate));
