/**
 * @file src/network/peerconn/create/create.js
 * <AUTHOR>
 */
import {Component} from 'san';
import u from 'lodash';
import {WebUploader} from '@baiducloud/bce-ui/san';
import {Button, Form, Select, Input, Cascader} from '@baidu/sui';
import {AppCreatePage} from '@baidu/sui-biz';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {handleDownloadBinaryFile} from '@/utils/helper';

import './style.less';

const {asPage, template} = decorators;
const formValidator = self => ({
    enterpriseName: [
        {required: true, message: '企业名称必填，100字符以内'},
        {
            validator(rule, value, callback) {
                if (!value) {
                    return callback('企业名称必填，100字符以内');
                }
                if (value.length > 100) {
                    return callback('长度不能超过100字符');
                }
                return callback();
            }
        }
    ],
    issuingAuthority: [
        {required: true, message: '发证机关必填，50字符以内'},
        {
            validator(rule, value, callback) {
                if (!value) {
                    return callback('发证机关必填，50字符以内');
                }
                if (value.length > 50) {
                    return callback('长度不能超过50字符');
                }
                return callback();
            }
        }
    ],
    corporation: [
        {required: true, message: '法人/经营者必填，50字符以内'},
        {
            validator(rule, value, callback) {
                if (!value) {
                    return callback('法人/经营者必填，50字符以内');
                }
                if (value.length > 50) {
                    return callback('长度不能超过50字符');
                }
                return callback();
            }
        }
    ],
    registrationNum: [
        {required: true, message: '注册号必填，数字加字母格式，18个字符'},
        {
            validator(rule, value, callback) {
                if (!value) {
                    return callback('注册号必填，数字加字母格式，18个字符');
                }
                if (!/^[a-zA-Z\d]{18}$/.test(value)) {
                    return callback('注册号必填，数字加字母格式，18个字符');
                }
                return callback();
            }
        }
    ],
    address: [
        {required: true, message: '住所/经营场所必填，100字符以内'},
        {
            validator(rule, value, callback) {
                if (!value) {
                    return callback('住所/经营场所必填，100字符以内');
                }
                if (value.length > 100) {
                    return callback('长度不能超过100字符');
                }
                return callback();
            }
        }
    ],
    mailingAddress: [
        {required: true, message: '通讯地址必填，100字符以内'},
        {
            validator(rule, value, callback) {
                if (!value) {
                    return callback('通讯地址必填，100字符以内');
                }
                if (value.length > 100) {
                    return callback('长度不能超过100字符');
                }
                return callback();
            }
        }
    ],
    postcode: [
        {required: true, message: '邮编必填，20字符以内'},
        {
            validator(rule, value, callback) {
                if (!value) {
                    return callback('邮编必填，20字符以内');
                }
                if (value.length > 20) {
                    return callback('长度不能超过20字符');
                }
                return callback();
            }
        }
    ],
    name: [
        {required: true, message: '联系人姓名必填，50字符以内'},
        {
            validator(rule, value, callback) {
                if (!value) {
                    return callback('联系人姓名必填，50字符以内');
                }
                if (value.length > 50) {
                    return callback('长度不能超过50字符');
                }
                return callback();
            }
        }
    ],
    phoneNum: [
        {required: true, message: '请填写联系人电话'},
        {
            validator(rule, value, callback) {
                if (!value) {
                    return callback('请填写联系人电话');
                }
                if (!/^1[\d]{10}$/.test(value)) {
                    return callback('格式错误，请重新输入');
                }
                return callback();
            }
        }
    ],
    mail: [
        {required: true, message: '请填写联系人邮箱'},
        {
            validator(rule, value, callback) {
                if (!value) {
                    return callback('请填写联系人邮箱');
                }
                const pattern = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
                if (!pattern.test(value)) {
                    return callback('邮箱格式不正确');
                }
                return callback();
            }
        }
    ],
    enterpriseType: [{required: true, message: '请选择总部行业分类'}],
    managerName: [
        {required: true, message: '姓名必填，50字符以内'},
        {
            validator(rule, value, callback) {
                if (!value) {
                    return callback('姓名必填，50字符以内');
                }
                if (value.length > 50) {
                    return callback('长度不能超过50字符');
                }
                return callback();
            }
        }
    ],
    certificateAddress: [
        {required: true, message: '证件地址必填，100字符以内'},
        {
            validator(rule, value, callback) {
                if (!value) {
                    return callback('证件地址必填，100字符以内');
                }
                if (value.length > 100) {
                    return callback('长度不能超过100字符');
                }
                return callback();
            }
        }
    ],
    managerPhoneNum: [
        {required: true, message: '必填，数字格式，11字符内'},
        {
            validator(rule, value, callback) {
                if (!value) {
                    return callback('必填，数字格式，11字符内');
                }
                if (!/^1[\d]{10}$/.test(value)) {
                    return callback('格式错误，请重新输入');
                }
                return callback();
            }
        }
    ],
    certificateType: [{required: true, message: '证件类别必选'}],
    certificateNum: [
        {required: true, message: '对应证件类别，填写对应的证件号，18字符以内'},
        {
            validator(rule, value, callback) {
                let source = self.data.get('formData');
                if (!source.certificateType) {
                    return callback('请先选择证件类别');
                } else if (!value) {
                    return callback('请输入证件号');
                }
                switch (source.certificateType) {
                    case 'ID_CARD':
                    case 'HMT_RESIDENCE_CARD':
                    case 'MILITARY_ID_CARD':
                    case 'POLICE_ID_CARD':
                        if (value.length !== 18) {
                            return callback('格式错误，请重新输入');
                        }
                        break;
                    case 'PASSPORT':
                        if (!/^[a-zA-Z][a-zA-Z\d]{8}$/.test(value)) {
                            return callback('格式错误，请重新输入');
                        }
                        break;
                    case 'MACAO_PERMIT':
                        if (!/^[a-zA-Z][a-zA-Z\d]{10}$/.test(value)) {
                            return callback('格式错误，请重新输入');
                        }
                        break;
                    case 'TAIWAN_PERMIT':
                        if (!/^[a-zA-Z\d]{8}$/.test(value)) {
                            return callback('格式错误，请重新输入');
                        }
                        break;
                    case 'MILITARY_OFFICER_CARD':
                        if (value.length > 8) {
                            return callback('格式错误，请重新输入');
                        }
                        break;
                    case 'POLICE_OFFICER_CARD':
                        if (!/^[\d]{1,7}$/.test(value)) {
                            return callback('格式错误，请重新输入');
                        }
                        break;
                }
                return callback();
            }
        }
    ],
    certificateId: [{required: true, message: '请先上传营业执照复印件'}],
    agentIdCard: [{required: true, message: '请先上传经办人身份证复印件'}],
    introductionLetter: [{required: true, message: '请先上传介绍信'}],
    securityCommitmentLetter: [{required: true, message: '请先上传信息安全承诺书'}]
});
const tpl = html`
    <div>
        <s-page class="{{klass}}" withTip="{{withTip}}" style="display: {{confirming ? 'none' : 'block'}}">
            <div slot="pageTitle" class="peerconn-create-header">
                <span on-click="toNotCross">非跨境</span>
                <span class="actived" on-click="toCross">中国联通跨境</span>
            </div>
            <label class="back-label">
                <a href="#/vpc/peerconn/list">
                    <i class="iconfont"></i>
                    返回实例列表
                </a>
            </label>
            <s-form data="{=formData=}" rules="{{rules}}" s-ref="form">
                <div class="tip">跨境产品售卖合规检查</div>
                <div class="body-part-content">
                    <h4>{{'基本信息'}}</h4>
                    <s-form-item s-for="item,index in baseInfo" label="{{item.label}}" prop="{{item.value}}">
                        <s-input
                            s-if="item.type==='input'"
                            placeholder="请输入"
                            width="{{240}}"
                            value="{=formData[item.value]=}"
                            track-id="ti_audit"
                            track-name="企业名称"
                        >
                        </s-input>
                        <s-cascader
                            s-if="item.type==='multipicker'"
                            datasource="{{enterpriseType}}"
                            value="{=formData[item.value]=}"
                            track-id="ti_audit"
                            track-name="选择行业分类"
                            width="{{240}}"
                            on-change="handleCascaderChange"
                        >
                        </s-cascader>
                    </s-form-item>
                </div>
                <div class="body-part-content">
                    <h4>{{'客户经办人/授权人信息'}}</h4>
                    <s-form-item s-for="item,index in managerInfo" label="{{item.label}}" prop="{{item.value}}">
                        <s-input
                            s-if="item.type==='input'"
                            placeholder="请输入"
                            width="{{240}}"
                            value="{=formData[item.value]=}"
                            track-id="ti_audit"
                            track-name="企业名称"
                        >
                        </s-input>
                        <s-select
                            s-if="item.type==='select'"
                            datasource="{{certificateType}}"
                            width="{{240}}"
                            value="{=formData[item.value]=}"
                            track-id="ti_audit"
                            track-name="选择证件类型"
                        >
                        </s-select>
                        <div s-if="item.type==='upload'">
                            <xui-webuploader
                                label="{{'文件上传'}}"
                                url="/api/peerconn/crossBorder/audit/certificate"
                                on-uploader-event="onUploaderEvent($event, item.value)"
                                on-accept="onAccept($event, item.value)"
                                options="{{uploader.options}}"
                            />
                            <span class="file-tip">必传，文件大小不超过10MB</span>
                            <div
                                class="upload-box"
                                s-if="uploadInfo[item.value].uploadDone || uploadInfo[item.value].uploadFail"
                            >
                                <div class="file-name">
                                    {{fileNameDisplay(item.value)}}
                                    <div s-if="uploadInfo[item.value].uploadFail" class="file-fail-icon"></div>
                                </div>
                                <div
                                    class="file-process {{uploadInfo[item.value].uploadDone ? 'file-upload-done' : ''}}"
                                ></div>
                                <div s-if="uploadInfo[item.value].uploadDone" class="file-suc-icon"></div>
                            </div>
                            <div class="peerconn-audit-upload" on-click="handleDownloadTemp(item.value)">
                                <!--bca-disable-next-line-->
                                {{item.tip | raw}}
                            </div>
                        </div>
                    </s-form-item>
                </div>
            </s-form>
            <div slot="pageFooter" class="buybucket">
                <div class="buybucket-container">
                    <s-button skin="primary" size="large" on-click="onConfirm">{{'确认'}}</s-button>
                    <s-button on-click="onCancel" size="large">{{'取消'}}</s-button>
                </div>
            </div>
        </s-page>
    </div>
`;
@template(tpl)
class PeerConnCreateAudit extends Component {
    static components = {
        's-page': AppCreatePage,
        's-form': Form,
        's-form-item': Form.Item,
        's-input': Input,
        's-select': Select,
        's-button': Button,
        's-cascader': Cascader,
        'xui-webuploader': WebUploader
    };

    initData() {
        return {
            klass: ['vpc-peerconn-create-audit'],
            rules: formValidator(this),
            baseInfo: [
                {
                    label: '企业名称',
                    value: 'enterpriseName',
                    type: 'input'
                },
                {
                    label: '发证机关',
                    value: 'issuingAuthority',
                    type: 'input'
                },
                {
                    label: '法人/经营者',
                    value: 'corporation',
                    type: 'input'
                },
                {
                    label: '注册号',
                    value: 'registrationNum',
                    type: 'input'
                },
                {
                    label: '住所/经营场所',
                    value: 'address',
                    type: 'input'
                },
                {
                    label: '通讯地址',
                    value: 'mailingAddress',
                    type: 'input'
                },
                {
                    label: '邮编',
                    value: 'postcode',
                    type: 'input'
                },
                {
                    label: '联系人姓名',
                    value: 'name',
                    type: 'input'
                },
                {
                    label: '联系人电话',
                    value: 'phoneNum',
                    type: 'input'
                },
                {
                    label: '联系人邮箱',
                    value: 'mail',
                    type: 'input'
                },
                {
                    label: '总部行业分类',
                    value: 'enterpriseType',
                    type: 'multipicker'
                }
            ],
            managerInfo: [
                {
                    label: '姓名',
                    value: 'managerName',
                    type: 'input'
                },
                {
                    label: '证件类别',
                    value: 'certificateType',
                    type: 'select'
                },
                {
                    label: '证件号',
                    value: 'certificateNum',
                    type: 'input'
                },
                {
                    label: '证件地址',
                    value: 'certificateAddress',
                    type: 'input'
                },
                {
                    label: '电话',
                    value: 'managerPhoneNum',
                    type: 'input'
                },
                {
                    label: '营业执照复印件',
                    value: 'certificateId',
                    type: 'upload',
                    tip: '请上传加盖过公章的营业执照复印件，文件格式为pdf。'
                },
                {
                    label: '经办人身份证复印件',
                    value: 'agentIdCard',
                    type: 'upload',
                    tip: '请上传加盖过公章的身份证复印件正反两面复印件，文件格式为pdf。'
                },
                {
                    label: '介绍信',
                    value: 'introductionLetter',
                    type: 'upload',
                    tip: '下载<a>《介绍信模版》</a>后，填写签字盖章后上传扫描件，文件格式为pdf。'
                },
                {
                    label: '信息安全承诺书',
                    value: 'securityCommitmentLetter',
                    type: 'upload',
                    tip: '下载<a>《信息安全承诺书》</a>后，签字盖章后上传扫描件，文件格式为pdf。安全承诺书每页都要盖章，需要盖骑缝章'
                }
            ],
            enterpriseType: [
                {
                    text: '金融业',
                    value: '金融业',
                    expandable: true,
                    children: [
                        {text: '银行', value: '银行'},
                        {text: '证券', value: '证券'},
                        {text: '保险', value: '保险'},
                        {text: '其它金融业', value: '其它金融业'}
                    ]
                },
                {
                    text: '党政军部门',
                    value: '党政军部门',
                    expandable: true,
                    children: [
                        {text: '党', value: '党'},
                        {text: '政府', value: '政府'},
                        {text: '军队', value: '军队'}
                    ]
                },
                {
                    text: '科学、教育、文化体育、卫生、出版业',
                    value: '科学、教育、文化体育、卫生、出版业',
                    expandable: true,
                    children: [
                        {text: '科研', value: '科研'},
                        {text: '教育业', value: '教育业'},
                        {text: '文化体育', value: '文化体育'},
                        {text: '卫生业', value: '卫生业'},
                        {text: '广播电影电视业', value: '广播电影电视业'},
                        {text: '新闻出版业', value: '新闻出版业'}
                    ]
                },
                {
                    text: '旅游、饭店、娱乐服务业',
                    value: '旅游、饭店、娱乐服务业',
                    expandable: true,
                    children: [
                        {text: '旅游业', value: '旅游业'},
                        {text: '住宿业', value: '住宿业'},
                        {text: '餐饮业', value: '餐饮业'},
                        {text: '娱乐服务业', value: '娱乐服务业'}
                    ]
                },
                {
                    text: '采掘业和加工、制造业',
                    value: '采掘业和加工、制造业',
                    expandable: true,
                    children: [
                        {text: '采矿业', value: '采矿业'},
                        {text: '通信电子设备制造业', value: '通信电子设备制造业'},
                        {text: '汽车制造业', value: '汽车制造业'},
                        {text: '石油加工和化工制造业', value: '石油加工和化工制造业'},
                        {text: '医药制造业', value: '医药制造业'},
                        {text: '食品制造业', value: '食品制造业'},
                        {text: '服装、鞋、帽制造业', value: '服装、鞋、帽制造业'},
                        {text: '其它制造业', value: '其它制造业'}
                    ]
                },
                {
                    text: '公共服务业',
                    value: '公共服务业',
                    expandable: true,
                    children: [
                        {text: '基础服务业', value: '基础服务业'},
                        {text: '公共设施服务业', value: '公共设施服务业'},
                        {text: '社区服务业', value: '社区服务业'},
                        {text: '社区服务', value: '社区服务'},
                        {text: '信息、咨询服务业', value: '信息、咨询服务业'},
                        {text: '租赁服务', value: '租赁服务'}
                    ]
                },
                {
                    text: '交通运输、仓储和邮政业',
                    value: '交通运输、仓储和邮政业',
                    expandable: true,
                    children: [
                        {text: '交通运输业', value: '交通运输业'},
                        {text: '邮政、快递', value: '邮政、快递'},
                        {text: '仓储业', value: '仓储业'}
                    ]
                },
                {
                    text: '批发零售业',
                    value: '批发零售业',
                    expandable: true,
                    children: [
                        {text: '批发业', value: '批发业'},
                        {text: '零售业', value: '零售业'}
                    ]
                },
                {
                    text: '房地产业',
                    value: '房地产业',
                    expandable: true,
                    children: [
                        {text: '房地产开发经营', value: '房地产开发经营'},
                        {text: '物业管理', value: '物业管理'},
                        {text: '房地产中介服务', value: '房地产中介服务'},
                        {text: '其他地产活动', value: '其他地产活动'}
                    ]
                },
                {
                    text: '建筑业',
                    value: '建筑业',
                    expandable: true,
                    children: [
                        {text: '房屋工程建筑', value: '房屋工程建筑'},
                        {text: '土木工程建筑', value: '土木工程建筑'},
                        {text: '建筑安装业', value: '建筑安装业'},
                        {text: '建筑装饰业', value: '建筑装饰业'},
                        {text: '其他建筑业', value: '其他建筑业'}
                    ]
                },
                {
                    text: '计算机信息业',
                    value: '计算机信息业',
                    expandable: true,
                    children: [
                        {text: '基础通讯业务运营业', value: '基础通讯业务运营业'},
                        {text: '互联网服务', value: '互联网服务'},
                        {text: '增值通信业务运营业', value: '增值通信业务运营业'},
                        {text: '计算机应用服务业', value: '计算机应用服务业'}
                    ]
                },
                {
                    text: '农林牧副渔',
                    value: '农林牧副渔',
                    expandable: true,
                    children: [
                        {text: '农业', value: '农业'},
                        {text: '林业', value: '林业'},
                        {text: '畜牧业', value: '畜牧业'},
                        {text: '渔业', value: '渔业'}
                    ]
                },
                {text: '其他', value: '其他', expandable: true, children: [{text: '其他行业', value: '其他行业'}]}
            ],
            certificateType: [
                {text: '身份证', value: 'ID_CARD'},
                {text: '护照', value: 'PASSPORT'},
                {text: '军人身份证', value: 'MILITARY_ID_CARD'},
                {text: '警察身份证', value: 'POLICE_ID_CARD'},
                {text: '港澳居民往来内地通行证', value: 'MACAO_PERMIT'},
                {text: '台湾居民往来大陆通行证', value: 'TAIWAN_PERMIT'},
                {text: '军官证', value: 'MILITARY_OFFICER_CARD'},
                {text: '警官证', value: 'POLICE_OFFICER_CARD'},
                {text: '港澳台居民居住证', value: 'HMT_RESIDENCE_CARD'}
            ],
            uploader: {
                options: {
                    server: '/api/peerconn/crossBorder/audit/certificate',
                    // 选完文件后，是否自动上传。
                    auto: true,
                    // 只允许选择pdf文件。
                    accept: {
                        title: 'Files',
                        extensions: 'pdf',
                        mimeTypes: 'application/pdf'
                    },
                    sendAsBinary: true,
                    fileNumLimit: 1,
                    fileSingleSizeLimit: 10 * 1024 * 1024,
                    duplicate: true,
                    compress: false,
                    headers: {'X-Region': 'bj'}
                }
            },
            formData: {
                enterpriseType: []
            },
            fileNameMap: {
                certificateId: '',
                agentIdCard: '',
                introductionLetter: '',
                securityCommitmentLetter: ''
            },
            uploadInfo: {
                certificateId: {uploadDone: false, uploadfail: false},
                agentIdCard: {uploadDone: false, uploadfail: false},
                introductionLetter: {uploadDone: false, uploadfail: false},
                securityCommitmentLetter: {uploadDone: false, uploadfail: false}
            },

            urlQuery: getQueryParams()
        };
    }

    inited() {
        const crossPeerconnAuditFileNameMap = window.$storage.get('crossPeerconnAuditFileNameMap');
        const fileName = crossPeerconnAuditFileNameMap ? JSON.parse(crossPeerconnAuditFileNameMap) : null;
        const crossPeerconnAuditEnterpriseType = window.$storage.get('crossPeerconnAuditEnterpriseType');
        const enterpriseType = crossPeerconnAuditEnterpriseType ? JSON.parse(crossPeerconnAuditEnterpriseType) : null;
        const crossPeerconnAuditPayload = window.$storage.get('crossPeerconnAuditPayload');
        const submitParam = crossPeerconnAuditPayload ? JSON.parse(crossPeerconnAuditPayload) : null;
        if (fileName && enterpriseType && submitParam) {
            this.data.set('formData', {...submitParam, enterpriseType: enterpriseType});
            this.handleResetUploadStatus(true, false);
            this.data.set('fileNameMap', fileName);
        }
        // this.getCrossAuditStatus();
    }

    getCrossAuditStatus() {
        this.$http.quertAuditStatus().then(data => {
            console.log(data, 'data');
        });
    }

    fileNameDisplay(type) {
        const fileNameMap = this.data.get('fileNameMap');
        return fileNameMap[type];
    }

    onConfirm() {
        let form = this.ref('form');
        form.validateFields().then(() => {
            let payload = u.cloneDeep(this.data.get('formData'));
            window.$storage.set('crossPeerconnAuditEnterpriseType', JSON.stringify(payload.enterpriseType));
            payload.enterpriseType = payload.enterpriseType[1];
            window.$storage.set('crossPeerconnAuditPayload', JSON.stringify(payload));
            const fileNameMap = this.data.get('fileNameMap');
            window.$storage.set('crossPeerconnAuditFileNameMap', JSON.stringify(fileNameMap));
            // 联调时候先传下accountid
            ((payload.accountId = window.$context.getUserId()),
                this.$http.crossPeerconnAudit(payload).then(() => {
                    location.hash =
                        '#/vpc/peerconn/create/auditing?vpcId=' +
                        (this.data.get('urlQuery.vpcId') || '') +
                        '&type=cross';
                }));
        });
    }

    handleResetUploadStatus(uploadDoneFlag, uploadFailFlag) {
        const uploadInfo = this.data.get('uploadInfo');
        _.each(Object.keys(uploadInfo), item => {
            this.data.set(`uploadInfo.${item}.uploadfail`, uploadFailFlag);
            this.data.set(`uploadInfo.${item}.uploadDone`, uploadDoneFlag);
        });
    }
    onCancel() {
        this.handleResetUploadStatus(false, false);
        this.data.set('formData', {
            enterpriseType: []
        });
        location.hash = '#/vpc/peerconn/create/v2?vpcId=&type=normal';
    }
    onUploaderEvent(event, type) {
        if (event.eventType === 'beforeFileQueued') {
            // this.handleResetUploadStatus(false, false);
            this.data.set(`fileNameMap.${type}`, event.args[0].name);
        } else if (event.eventType === 'uploadBeforeSend') {
            let csrfToken = window.$cookie.get('bce-user-info');
            if (csrfToken) {
                event.args[2].csrftoken = csrfToken.replace(/"/g, '');
            }
        }
    }
    onAccept(event, type) {
        const {ret} = event;
        if (ret && ret.success === true && ret.result) {
            this.data.set(`uploadInfo.${type}.uploadDone`, true);
            this.data.set(`uploadInfo.${type}.uploadfail`, false);
            this.data.set(`formData.${type}`, ret.result.certificateId);
            // 上传成功重新校验一下
            this.nextTick(() => {
                this.ref('form').validateFields([type]);
            });
            return;
        }
        this.data.set(`uploadInfo.${type}.uploadDone`, false);
        this.data.set(`uploadInfo.${type}.uploadfail`, true);
    }
    toCross() {
        const {vpcId} = this.data.get('urlQuery');
        const link = `#/vpc/peerconn/create_cross?vpcId=${vpcId}&type=cross`;
        location.hash = link;
    }
    toNotCross() {
        const {vpcId} = this.data.get('urlQuery');
        location.hash = `#/vpc/peerconn/create/v2?vpcId=${vpcId}&type=normal`;
    }
    onRegionChange() {
        location.hash = '#/vpc/peerconn/list';
    }
    handleDownloadTemp(type) {
        const fileName = type === 'introductionLetter' ? '介绍信模板' : '信息安全承诺书';
        const fileBosId =
            type === 'introductionLetter'
                ? 'crossPeerConnIntroductionLetterDocId'
                : 'crossPerConnSecCommitmentLetterDocId';
        const mimeType =
            type === 'introductionLetter'
                ? 'application/msword'
                : 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        handleDownloadBinaryFile(`/api/peerconn/crossBorder/audit/certificate/${fileBosId}`, mimeType, fileName);
    }
    handleCascaderChange({value}) {
        this.nextTick(() => {
            this.ref('form').validateFields(['enterpriseType']);
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(PeerConnCreateAudit));
