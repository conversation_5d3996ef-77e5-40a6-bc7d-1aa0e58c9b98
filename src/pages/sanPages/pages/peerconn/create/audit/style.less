.vpc-peerconn-create-audit {
    height: 100%;
    overflow: auto;
    position: relative;
    width: 100%;
    .tip {
        width: 1022px;
        margin-left: 16px;
        margin-top: 12px;
        display: block;
        color: #151b26;
        line-height: 24px;
        font-weight: 500;
        font-size: 16px;
    }
    .back-label {
        position: absolute;
        top: 14px;
        left: 14px;
        a {
            font-size: 14px;
            color: #333333;
            cursor: pointer;
        }
        .iconfont {
            font-size: 12px;
            margin-right: 20px;
            &::before {
                content: '\e605';
                position: absolute;
                transform: rotate(90deg);
            }
        }
    }
    .bui-biz-page-tip {
        text-align: left;
        width: 1020px;
        margin: 20px auto !important;
    }
    .bui-biz-page-body {
        padding-bottom: 100px;
    }
    .body-part-content {
        width: calc(~'100vw - 32px');
        background: #fff;
        border-radius: 6px;
        padding: 24px;
        text-align: left;
        margin: 16px;
        h4 {
            margin: 0;
            font-size: 16px;
        }
    }
    .s-form {
        .s-form-item-label {
            width: 128px;
            label {
                float: left;
            }
        }
        .s-form-item-error {
            padding-bottom: 0px;
        }
        .s-cascader {
            .s-cascader-value-arrow {
                top: 25%;
            }
        }
    }
    .bui-form {
        .bui-form-item {
            margin: 20px 0 0 0;
        }
        .bui-form-item-content {
            line-height: 30px;
        }
    }
    .skin-warning-tip {
        margin-left: 10px;
        color: #999 !important;
        &:hover {
            color: #fff !important;
        }
    }
    .inline-item {
        line-height: 30px;
    }
    .bui-webuploader {
        display: inline-block;
        .webuploader-pick {
            background: url('../../../../../../img/uploadicon.svg?url') 10px center no-repeat;
            background-size: 16px 12px;
            padding-left: 30px;
            border-radius: 4px;
            border: 1px solid #dadbdd;
        }
    }
    .file-tip {
        color: #999999;
        font-size: 12px;
        vertical-align: bottom;
    }
    .file-name {
        background: url('../../../../../../img/fileicon.svg?url') 0 center no-repeat;
        background-size: 14px 16px;
        padding-left: 25px;
    }
    .file-process {
        float: left;
        display: inline-block;
        width: 0;
        height: 4px;
        border-radius: 2px;
        background: #2468f2;
        transition: width 0.2s;
    }
    .file-upload-done {
        width: 220px;
    }
    .file-suc-icon {
        float: right;
        width: 20px;
        height: 15px;
        margin-top: -6px;
        background: url('../../../../../../img/uploadsuc.svg?url') center center no-repeat;
        background-size: 100% 100%;
    }
    .file-fail-icon {
        width: 20px;
        height: 15px;
        display: inline-block;
        vertical-align: middle;
        background: url('../../../../../../img/uploadfail.svg?url') center center no-repeat;
        background-size: 100% 100%;
    }
    .upload-tip {
        height: 30px;
        line-height: 30px;
        font-size: 12px;
        color: #999999;
    }
    .peerconn-audit-upload {
        margin-top: 8px;
        cursor: pointer;
    }
    .upload-box {
        width: 250px;
        overflow: hidden;
        padding-left: 5px;
        margin-top: 8px;
        &:hover {
            background: #f3f3f3;
        }
        .file-name {
            margin-bottom: 4px;
        }
    }
    .bui-biz-page-content.bui-biz-page-center-content {
        .bui-biz-page-body {
            margin: 0 auto !important;
            border: none !important;
        }
    }
    .bui-biz-page-title {
        width: 240px;
        margin: 0 auto;
    }
    .bui-biz-page-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        height: 80px;
        background: #fff;
        box-shadow: 0 1px 10px 0 rgba(0, 0, 0, 0.1);
        width: 100%;
        z-index: 99999;
        padding-left: 310px;
        padding-top: 20px;
        box-sizing: border-box;
    }
    .create-confirm {
        display: inline-flex;
        .s-button {
            margin-right: 8px;
        }
    }
    .buybucket {
        width: 100%;
        left: 0;
        height: 60px;
        .buybucket-container {
            width: auto;
            float: left;
            height: 48px;
            transform: translateY(12.5%);
            display: flex;
            align-items: center;
            .s-button {
                margin-left: 16px;
            }
        }
    }
}
.vpc-peerconn-create-auditing {
    width: 100%;
    height: 100%;
    background: #fff;
    position: relative;
    .back-label {
        position: absolute;
        top: 14px;
        left: 14px;
        a {
            font-size: 14px;
            color: #333333;
            cursor: pointer;
        }
        .iconfont {
            font-size: 12px;
            margin-right: 20px;
            &::before {
                content: '\e605';
                position: absolute;
                transform: rotate(90deg);
            }
        }
    }
    .bui-biz-page-title {
        width: 240px;
        margin: 0 auto;
    }
    .audit-box {
        width: 830px;
        margin: 50px auto;
        text-align: center;
        font-size: 16px;
        height: 100%;
        position: relative;
        .audit-title {
            color: #999999;
            display: inline-block;
            margin-right: 80px;
        }
        .audit-item {
            display: inline-block;
            position: relative;
            .audit-step {
                width: 26px;
                height: 26px;
                line-height: 26px;
                text-align: center;
                border-radius: 50%;
                color: #ffffff;
                margin-right: 183px;
                margin-bottom: 20px;
            }
            .black {
                background: #000000;
            }
            .blue {
                background: #2468f2;
            }
            .grey {
                background: #999999;
            }
            .red {
                background: red;
            }
            .line {
                width: 183px;
                height: 2px;
                display: inline-block;
                position: absolute;
                top: 12px;
                left: -183px;
            }
            .audit-text {
                font-size: 12px;
                text-align: center;
                display: inline-block;
                float: left;
                transform: translateX(-27px);
                width: 80px;
            }
            .audit-time {
                color: #999999;
                padding-top: 10px;
                font-size: 12px;
                text-align: center;
                display: inline-block;
                float: left;
                transform: translateX(-47px);
                width: 140px;
            }
            .highlight-reason {
                color: #f33e3e;
            }
        }

        .bui-button {
            position: absolute;
            right: 100px;
        }
    }
    .s-create-page-footer {
        display: none;
    }
}

.vpc-peerconn-create-audit,
.vpc-peerconn-create-auditing {
    .space-header {
        display: flex;
        align-items: center;
        .peerconn-create-header {
            margin: 0 auto;
            display: flex;
            align-items: center;
            height: 100%;
            span {
                height: 100%;
                padding: 0 20px;
                cursor: pointer;
                display: flex;
                font-size: 18px;
                align-items: center;
                border-bottom: 2px solid #fff;
            }
            span.actived {
                border-color: #2468f2;
                color: #2468f2;
            }
        }
    }
    .s-create-page-title {
        .peerconn-create-header {
            margin: 0 auto;
            display: flex;
            align-items: center;
            height: 100%;
            span {
                height: 100%;
                padding: 0 20px;
                cursor: pointer;
                display: flex;
                font-size: 18px;
                align-items: center;
                border-bottom: 2px solid #fff;
            }
            span.actived {
                border-color: #2468f2;
                color: #2468f2;
            }
        }
    }
}
