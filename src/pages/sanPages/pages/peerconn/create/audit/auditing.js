/**
 * @file src/network/peerconn/create/create.js
 * <AUTHOR>
 */
import {Component} from 'san';
import {Button} from '@baidu/sui';
import {AppCreatePage} from '@baidu/sui-biz';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';

import {utcToTime} from '@/pages/sanPages/utils/helper';
import './style.less';

const {asPage, template} = decorators;
const tpl = html`
    <div>
        <s-page
            class="{{klass}}"
            withTip="{{withTip}}"
            backTo="{{pageNav.backUrl}}"
            style="display: {{confirming ? 'none' : 'block'}}"
        >
            <div slot="pageTitle" class="peerconn-create-header">
                <span on-click="toNotCross">非跨境</span>
                <span class="actived" on-click="toCross">中国联通跨境</span>
            </div>
            <label class="back-label">
                <a href="#/vpc/peerconn/list">
                    <i class="iconfont"></i>
                    返回实例列表
                </a>
            </label>
            <div class="audit-box">
                <div class="audit-title">审核进度</div>
                <div class="audit-item" s-for="item,index in process">
                    <div class="audit-step {{item.class}}">{{item.step}}</div>
                    <div s-if="index===1||index===2" class="line {{item.class}}"></div>
                    <div class="audit-text">{{item.text}}</div>
                    <br />
                    <div class="audit-time">{{item.time}}</div>
                    <br />
                    <div class="audit-time highlight-reason">{{item.desc}}</div>
                </div>
                <s-button s-if="isAllowReApply" skin="primary" on-click="reApply">{{'重新申请'}}</s-button>
            </div>
        </s-page>
    </div>
`;
@template(tpl)
class PeerConnAuditing extends Component {
    static components = {
        's-page': AppCreatePage,
        's-button': Button
    };
    static computed = {
        isAllowReApply() {
            let flag = false;
            const auditStatus = this.data.get('auditStatus');
            if (auditStatus === 'AUDIT_FAILED') {
                flag = true;
            }
            return flag;
        }
    };
    initData() {
        return {
            klass: ['vpc-peerconn-create-auditing'],
            process: [
                {step: 1, text: '提交申请', time: '', class: 'black'},
                {step: 2, text: '审核中', time: '', class: 'blue'},
                {step: 3, text: '通过/拒绝', time: '', class: 'grey'}
            ],
            pageNav: {
                // 标题栏
                backUrl: '/network/#/vpc/peerconn/list'
            },
            urlQuery: getQueryParams(),
            auditStatus: ''
        };
    }
    attached() {
        this.getCrossAuditStatus();
    }
    reApply() {
        location.hash = '#/vpc/peerconn/create/audit?vpcId=' + this.data.get('urlQuery.vpcId') + '&type=cross';
    }
    getCrossAuditStatus() {
        this.$http.quertAuditStatus().then(data => {
            this.data.set('auditStatus', data.auditStatus);
            if (data.auditStatus === 'TO_AUDIT') {
                location.hash = '#/vpc/peerconn/create/audit?vpcId=' + this.data.get('urlQuery.vpcId') + '&type=cross';
            } else if (data.auditStatus === 'AUDITING') {
                this.data.set('process[0].time', utcToTime(data.createdTime));
            } else if (data.auditStatus === 'AUDIT_FAILED') {
                this.data.set('process[0].time', utcToTime(data.createdTime));
                this.data.splice('process', [
                    1,
                    2,
                    {step: 2, text: '已审核', time: '', class: 'black'},
                    {
                        step: '×',
                        text: '已拒绝',
                        time: utcToTime(data.updatedTime),
                        class: 'red',
                        desc: `拒绝原因：${data.rejectReason}`
                    }
                ]);
            } else if (data.auditStatus === 'AUDIT_SUCCESS') {
                location.hash = '#/vpc/peerconn/create/v2?vpcId=' + this.data.get('urlQuery.vpcId') + '&type=cross';
            }
        });
    }

    toCross() {
        const {vpcId} = this.data.get('urlQuery');
        const link = `#/vpc/peerconn/create_cross?vpcId=${vpcId}&type=cross`;
        location.hash = link;
    }
    toNotCross() {
        const {vpcId} = this.data.get('urlQuery');
        location.hash = `#/vpc/peerconn/create/v2?vpcId=${vpcId}&type=normal`;
    }
    onRegionChange() {
        location.hash = '#/vpc/peerconn/list';
    }
}
export default San2React(Processor.autowireUnCheckCmpt(PeerConnAuditing));
