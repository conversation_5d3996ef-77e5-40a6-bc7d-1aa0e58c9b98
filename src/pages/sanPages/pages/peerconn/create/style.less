/**
 * @file src/network/peerconn/create/style.less
 * <AUTHOR>
 */

.vpc-peerconn-create {
    width: 100%;
    background-color: #f7f7f9;
    .create-content-wrapper {
        overflow: visible;

        .ui-radioselect {
            overflow: visible;

            .ui-radio-block {
                position: relative;
                overflow: visible;

                .ui-radio-item-hover {
                    height: 52px;
                    top: -71px;
                    text-align: left;
                }
            }
        }
    }

    .buy-bucket-tip {
        color: #f18d36;
        margin: 20px 18px 20px 17px;
    }

    dd > div {
        line-height: 30px;
    }

    .price-link {
        margin-left: 10px;
    }
}

.vpc-peerconn-create-v2 {
    background-color: #f7f7f9;
    height: 100%;
    overflow: auto;
    position: relative;
    width: 100%;
    .s-step-block {
        width: 316px !important;
    }
    .space-header {
        display: flex;
        align-items: center;
        .peerconn-create-header {
            margin: 0 auto;
            display: flex;
            align-items: center;
            height: 100%;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
            span {
                height: 100%;
                padding: 0 16px;
                cursor: pointer;
                display: flex;
                font-weight: 500;
                color: #151b26;
                font-size: 16px;
                align-items: center;
                border-bottom: 2px solid #fff;
            }
            span.actived {
                border-color: #2468f2;
                color: #2468f2;
            }
        }
    }

    .back-label {
        position: absolute;
        top: 14px;
        left: 14px;
        a {
            font-size: 14px;
            color: #333333;
            cursor: pointer;
        }
        .iconfont {
            font-size: 12px;
            margin-right: 20px;
            &::before {
                content: '\e605';
                position: absolute;
                transform: rotate(90deg);
            }
        }
    }

    .agreement {
        margin: 16px 0 16px 0px;
        display: flex;
        align-items: center;
        input[type='checkbox'] {
            transform: scale(0.75);
            margin: -3px 5px 0 0;
            top: 1px;
        }
        label {
            display: flex;
        }
    }

    .bui-viewstep {
        text-align: center;
        margin: 20px 0 15px 0;
    }

    .bui-biz-page-tip {
        text-align: left;
        width: 1020px;
        margin: 20px auto !important;
    }

    .bui-biz-page-body {
        padding-bottom: 100px;
    }

    .resource-form-part-wrap {
        padding-top: 16px !important;
        .resource-group-panel {
            padding: 0;
            margin: 0;
            border: none;
            dt {
                margin-bottom: 24px;
                h4 {
                    padding: 0;
                    font-family: PingFangSC-Medium;
                    font-size: 16px;
                    color: #151b26;
                    line-height: 24px;
                    font-weight: 500;
                }
            }
            .resouce-group-select {
                label {
                    padding-left: 7px;
                }
                .wrapper {
                    .s-button {
                        border-color: #e8e9eb;
                    }
                }
                .s-button-skin-stringfy {
                    padding: 0 4px;
                }
                .footer {
                    line-height: 20px;
                    height: 20px;
                    margin-top: 8px;
                    .tip {
                        color: #84868c;
                        line-height: 20px;
                    }
                }
            }
        }
    }

    .bui-form {
        .bui-form-item {
            margin: 20px 0 0 0;
        }
        .bui-form-item-content {
            line-height: 30px;
        }
    }
    .bui-toastlabel-warning {
        width: 100%;
        box-sizing: border-box;
    }
    .s-form {
        .s-form-item-label {
            width: 96px;
            height: 30px;
            .inline-tip {
                top: 3px;
                position: relative;
                .s-tip-warning {
                    justify-content: center;
                    .warning_class {
                        fill: #999;
                    }
                }
                .s-tip:hover .s-icon path {
                    fill: #2468f2 !important;
                }
            }
        }
        .s-form-item-error {
            padding-bottom: 0px;
        }
        .inline_class {
            display: inline-block;
        }
        .s-radio-button-group {
            .s-radio-text {
                width: 95px;
            }
        }
        .purchase-period {
            .s-radio-button-group {
                .s-radio-text {
                    width: 37px;
                }
            }
        }
        .renew-item-wrapper {
            display: inline-block;
            .s-radio-text {
                width: 37px !important;
            }
        }
        .autorenew-panel {
            margin-top: 10px;
        }
        .s-input {
            border-color: #e8e9eb !important;
            .s-input-area {
                input {
                    box-sizing: border-box;
                }
            }
        }
        .s-textarea textarea {
            border-color: #e8e9eb !important;
        }
        .input-with-num {
            input {
                padding-right: 46px;
            }
        }
        .input-num-statistics {
            font-family: PingFangSC-Regular;
            font-size: 12px;
            color: #84868c;
            text-align: right;
            line-height: 20px;
            font-weight: 400;
            margin-left: -46px;
        }
    }

    .skin-warning-tip {
        margin-left: 10px;
        color: #999 !important;

        &:hover {
            color: #fff !important;
        }
    }

    .inline-item {
        line-height: 30px;
    }

    .bui-radioselect {
        .bui-radio-block.new-tip:after {
            content: 'Sale';
            left: 5px;
        }
    }

    .renew-expire-time {
        margin-left: 10px;
        color: #666;
    }

    .bui-biz-page-title {
        width: 240px;
        margin: 0 auto;
    }

    .bui-biz-page-content.bui-biz-page-center-content {
        .bui-biz-page-body {
            margin: 0 auto !important;
            border: none !important;
        }
    }
    .desc {
        input {
            padding-right: 60px;
        }
    }
    .eip-group-create-confirm {
        .bui-biz-buybucket {
            .bui-biz-buybucket-content {
                float: right;
                margin-right: 0;
            }
        }
    }

    .bui-biz-page-footer {
        .buy-container {
            position: fixed;
            bottom: 0;
            left: 60px;
            height: 80px;
            background: #fff;
            box-shadow: 0 1px 10px 0 rgba(0, 0, 0, 0.1);
            width: calc(~'100% - 60px');
            z-index: 99999;
            .buybucket-content {
                position: absolute;
                left: 160px;
                transform: translateY(50%);
                height: 43px;
                box-sizing: border-box;
            }
            .bui-biz-buybucket {
                position: absolute;
                left: 280px;
                box-shadow: none;
                width: initial;
                z-index: initial;
                .bui-biz-buybucket-container {
                    padding-left: 0 !important;
                }
            }
        }
    }
    .form-widget {
        margin-top: 16px;
        min-width: 1020px;
        padding-bottom: 24px;
        .peerconn-create-header {
            padding: 16px 24px 0px;
            background-color: #ffffff;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
            .peerconn-create-wrapper {
                border-bottom: 1px solid #e8e9eb;
                display: flex;
                span {
                    height: 100%;
                    cursor: pointer;
                    font-size: 16px;
                    align-items: center;
                    display: inline-block;
                    padding-bottom: 10px;
                    font-weight: 500;
                    color: #151b26;
                    font-family: PingFangSC-Medium;
                    border-bottom: 2px solid #fff;
                    &:first-child {
                        margin-right: 32px;
                    }
                }
                span.actived {
                    border-color: #2468f2;
                    color: #2468f2;
                }
            }
        }
        .peerconn-purchase-notes {
            background-color: #ffffff;
            padding: 20px 24px 16px;
            h4 {
                font-family: PingFangSC-Medium;
                font-size: 16px;
                color: #151b26;
                line-height: 24px;
                font-weight: 500;
                margin-bottom: 16px;
            }
            .billing-instructions {
                .s-button {
                    height: 20px;
                    width: 48px;
                    padding: 0px 8px;
                }
                .desc {
                    font-family: PingFangSC-Regular;
                    font-size: 12px;
                    color: #151b26;
                    line-height: 18px;
                    font-weight: 400;
                    margin-left: 10px;
                }
            }
        }
        .body-part-content {
            width: calc(~'100vw - 260px');
            background: #fff;
            border-radius: 6px;
            &:first-child {
                padding: 24px;
            }
            padding: 16px 24px 24px;
            text-align: left;
            min-width: 1020px;
            h4 {
                display: inline-block;
                zoom: 1;
                margin: 0;
                font-family: PingFangSC-Medium;
                font-size: 16px;
                color: #151b26;
                line-height: 24px;
                font-weight: 500;
            }
            .peerconn-wrapper {
                display: flex;
                margin-top: 12px;
                .style-common {
                    background-color: #f7f7f9;
                    border-radius: 4px;
                    padding: 16px;
                    h5 {
                        font-family: PingFangSC-Medium;
                        font-size: 14px;
                        color: #151b26;
                        font-weight: 500;
                    }
                    .s-input {
                        background: #ffffff;
                    }
                    .peerconn-peer-region {
                        .s-form-item-control-wrapper {
                            display: flex;
                            align-items: center;
                        }
                    }
                }
                .peerconn-icon-end {
                    width: 42px;
                    margin: 0 4px;
                    display: flex;
                    align-items: center;
                    .icon {
                        display: inline-block;
                        width: inherit;
                        height: 24px;
                        background: url('../../../../../img/peerconnIconEnd.svg?url') no-repeat;
                    }
                }
                .s-form-item-control-wrapper {
                    padding-left: 10px;
                }
            }
            .initiator {
                line-height: 30px;
            }
        }
        .form-part-wrap {
            border-radius: 6px;
            width: calc(~'100vw - 260px');
            background: #fff;
            padding: 24px;
            &:last-child {
                padding-top: 16px;
            }
            min-width: 1020px;
            h4 {
                display: inline-block;
                border: none;
                zoom: 1;
                color: #333;
                font-size: 16px;
                font-weight: 500;
                margin: 0;
                padding: 0;
            }
            .row-line {
                display: flex;
                align-items: center;
                position: relative;
                .row-line-wrapper {
                    display: flex;
                    flex-wrap: wrap;
                    .row-line-item {
                        background: #ffffff;
                        border: 1px solid #d4d6d9;
                        border-radius: 6px;
                        width: 260px;
                        margin-right: 12px;
                        margin-bottom: 4px;
                        cursor: pointer;
                        &:hover {
                            border-color: #2468f2;
                        }
                        .row-line-icon {
                            width: 20px;
                            height: 20px;
                            margin-right: 10px;
                            background-size: 100% 100%;
                            flex-shrink: 0;
                        }
                        .row-line-inner {
                            padding: 12px 20px;
                            .row-line-version {
                                display: flex;
                                align-items: center;
                                .img {
                                    width: 32px;
                                    height: 32px;
                                }
                                .row-line-widget {
                                    margin-left: 20px;
                                    .row-line-name {
                                        font-size: 14px;
                                        color: #151b26;
                                        font-weight: 500;
                                        line-height: 22px;
                                    }
                                    .row-line-name-selected {
                                        color: #2468f2;
                                    }
                                    .row-line-desc {
                                        font-size: 12px;
                                        color: #5c5f66;
                                        line-height: 20px;
                                        font-weight: 400;
                                        margin-top: 6px;
                                    }
                                }
                            }
                        }
                    }
                    .row-line-selected {
                        background: #eef3fe;
                        border: 1px solid #2468f2;
                    }
                    .row-line-disabled {
                        cursor: not-allowed;
                        background: #f7f7f9;
                        border: 1px solid #e8e9eb;
                        opacity: 0.6;
                        &:hover {
                            border-color: #e8e9eb !important;
                        }
                    }
                }
                .peerconn-price {
                    position: absolute;
                    left: 272px;
                    top: 80px;
                }
            }
            .s-form-item-label {
                width: 96px;
                height: 30px;
            }
            .label_class {
                .inline-tip {
                    top: 3px;
                    position: relative;
                    .s-tip-warning {
                        justify-content: center;
                        .warning_class {
                            fill: #999;
                        }
                    }
                    .s-tip:hover .s-icon path {
                        fill: #2468f2 !important;
                    }
                }
            }
            .center_class {
                .s-row {
                    .s-form-item-control-wrapper {
                        line-height: 30px;
                    }
                }
            }
            .vpc-nat-eip-opt {
                margin-top: 0px;
            }
        }
    }
    .buybucket {
        width: 100%;
        left: 0;
        height: 80px !important;
        display: flex;
        justify-content: center;
        .buybucket-widget {
            width: calc(~'100vw - 240px') !important;
            margin: 0 auto;
            height: 100%;
            .buybucket-container {
                width: auto;
                float: left;
                height: 80px !important;
                transform: translateY(0) !important;
                display: flex;
                align-items: center;
                .no-mg-bt {
                    margin-left: 0 !important;
                }
                .billing-sdk-total-price-wrapper {
                    margin-left: 16px;
                }
            }
            .billing-sdk-protocol-wrapper .buy-agreement {
                margin-bottom: 8px !important;
            }
        }
    }
    .billing-sdk-order-confirm-wrapper-default {
        width: calc(~'100vw - 260px');
        padding: 0;
    }
    .billing-protocol-cross {
        padding-top: 20px;
    }
    .bucket-legends-peerRegion-class {
        .order-item-container {
            .billing-sdk-order-legend {
                .charge-wrapper div:nth-child(2) {
                    display: none;
                }
            }
        }
    }
    .bucket-legends-isCross {
        .order-item-container {
            .billing-sdk-order-legend {
                .content div:nth-child(1) {
                    display: none;
                }
            }
        }
    }
    .buybucket-cross {
        width: 100%;
        left: 0;
        display: flex;
        height: auto !important;
        justify-content: center;
        .buybucket-widget {
            width: calc(~'100vw - 214px');
            height: 100%;
            .buybucket-container {
                width: auto;
                float: left;
                height: 48px;
                transform: translateY(12.5%);
                display: flex;
                align-items: center;
                margin-top: -10px;
                .shopping-cart-detail-container {
                    padding: 0 0 0 36px;
                }
            }
        }
    }
    // 订单确认页样式统一设置 目前仅考虑1280 需适配其他宽度可媒体查询
    .order-confirm {
        margin-top: 16px;
        width: calc(~'100vw - 260px');
        min-width: 1020px;
    }
    .dragger-input {
        display: inline-block;
        position: relative;
        left: 10px;
        top: -10px;
    }
    .tag-edit-panel {
        .s-form-item {
            margin: 0 8px 12px 0 !important;
        }
        .s-form-item-control-wrapper {
            margin-left: 0px !important;
        }
    }
    .tag-v2-panel-container {
        width: 800px !important;
    }
}
.vpc-peerconn-create-cross {
    .s-create-page-content {
        padding-bottom: 124px;
    }
}

.eip-group-create-buybucket {
    .bui-biz-buybucket-layer-content {
        .bui-biz-buybucket-layer-row {
            label {
                width: 80px;
            }
        }
    }
}

.ui-tab {
    min-width: 300px;
    .ui-tab-item {
        float: left;
        height: 50px;
        min-width: 90px;
        padding: 0 20px;
        text-align: center;
        box-sizing: border-box;
        border-radius: 0;
        cursor: pointer;
        transition: all 0.3s;
        a {
            line-height: 50px;
            text-align: center;
            display: inline-block;
            font-size: 18px;
            color: black;
        }
    }
}
.ui-tab-x .ui-tab-navigator .ui-tab-item-active {
    background-color: #fff;
    color: #2468f2;
    a {
        color: #2468f2 !important;
    }
}
.ui-tab-item-active {
    cursor: default;
    position: relative;
    border-bottom: 2px solid #2468f2;
}

.locale-en {
    .vpc-peerconn-create-v2 .form-part-wrap .s-form-item-label {
        width: 186px;
    }
    .vpc-peerconn-create-v2 .s-form .s-form-item-label {
        width: 186px;
    }
    .vpc-peerconn-create-v2 .resouce-group-select .resouce-group-select-main > label {
        width: 186px;
    }
    .vpc-peerconn-create-v2 .resouce-group-select .footer {
        margin-left: 186px;
    }
}
.s-form-item-label-required > label:before {
    left: 0 !important;
}
.bui-layer {
    .bui-tiplayer {
        border-radius: 4px;
        .bui-button-label {
            color: #2468f2;
        }
    }
}
