.peer-detail-wrap {
    .app-tab-page {
        padding: 0;
        background: #f7f7f7 !important;
        .bui-tab-header {
            border-right: 1px solid #ebebeb;
            border-bottom: none;
        }
        .skin-accordion {
            min-height: 562px !important;
            height: auto;
        }
        .app-tab-page-panel {
            margin: 24px;
            .item-key {
                margin-right: 16px !important;
            }
        }
    }
    .s-detail-page-content {
        margin: 0;
        .app-tab-page {
            padding: 16px;
            border-radius: 6px;
            background: #fff;
            .bui-tab {
                border-radius: 6px;
                border: none;
            }
        }
    }
    .page-header {
        display: flex;
        align-items: center;
        .iconfont {
            font-size: 14px;
            color: #84868C;
        }
        .instance-name {
            font-weight: 500;
            color: #151B26;
            padding: 0px 12px 0px 16px;
            font-size: 16px;
        }
    }
    .app-tab-page {
        padding: 0;
        .s-tabnav {
            height: auto !important;
            align-self: stretch;
        }
        .s-tabpane-wrapper {
            flex: 1;
        }
    }
    .bui-tab-content {
        padding: 8px 15px;
    }
}
