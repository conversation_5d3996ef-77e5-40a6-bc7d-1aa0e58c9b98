.inline-blick {
    display: inline-block;
}

.vpc-peer-monitor-box {
    position: relative;
    .search-warp {
        display: flex;
        margin-bottom: 10px;
        .search-item {
            display: flex;
            align-items: center;
            margin-right: 5px;
            .search-label {
                flex-shrink: 0;
            }
        }
        .refresh-button {
            margin: 0 5px;
        }
    }
    .tip-icon-wrap {
        font-size: 14px;
        border: 1px solid #f18823;
        margin-left: 10px;
        color: #f18823;
        &:hover {
            color: #fff;
            background-color: #f18823;
        }
    }
    .inline-tip {
        margin-left: 10px;
    }
    .s-dialog-footer {
        padding: 0 !important;
    }
}

.vpc-peer-conn-info {
    padding: 0 24px;
    dt {
        h4 {
            display: inline-block;
            font-size: 16px;
            margin-bottom: 16px;
            color: #151b26;
            line-height: 24px;
            font-weight: 500;
        }
    }
    &:last-child {
        margin-top: 20px;
    }
    .bui-tip {
        margin-left: 5px;
        color: #999 !important;
    }
    .peer-conn-trends {
        display: flex;
        flex-wrap: wrap;
        .conn-trend-box {
            width: 49%;
            margin-top: 10px;
            .ui-bcmchart {
                width: 100%;
            }
        }
    }
    .options_class {
        display: flex;
        align-items: center;
    }
    .flex_class {
        .tip_class {
            margin-top: 5px;
            margin-left: 8px;
        }
    }
    .check_class {
        .s-checkbox {
            .s-checkbox-input {
                top: 3px;
            }
            .s-radio-text {
                position: relative;
                top: 2px;
            }
        }
    }
    .time_picker_server {
        .s-daterangepicker {
            .s-trigger-container {
                display: inline-flex;
                .s-input-area {
                    display: inline-flex;
                }
            }
        }
    }
    .s-daterangepicker-disabled {
        input {
            &:hover {
                cursor: not-allowed;
            }
        }
    }
}

dd.vpc-peer-conn-server {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    line-height: 40px;
    & > div:nth-child(n + 2) {
        margin-left: 20px;
    }
}
