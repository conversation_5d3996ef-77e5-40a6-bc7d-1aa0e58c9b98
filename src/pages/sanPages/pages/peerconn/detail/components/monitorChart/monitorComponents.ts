import u from 'lodash';
import {Component} from 'san';
import {BcmChartPanel} from '@baidu/new-bcm-sdk-san';
import {BcmSDK} from '@baidu/new-bcm-sdk';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import moment from 'moment';
import {OutlinedRefresh} from '@baidu/sui-icon';

import monitorConfig from '@/pages/sanPages/utils/monitorConfig';
import './style.less';

const {peerMetrics, peerconnMetrics, shortcutItems} = monitorConfig;
const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent} = decorators;

const tpl = html`
    <div>
        <div class="vpc-peer-monitor-box" style="padding: 0;">
            <div class="search-warp">
                <span class="search-item">
                    <label class="search-item-label">监控指标：</label>
                    <s-select
                        value="{=metrics.option=}"
                        datasource="{{ds.options}}"
                        width="{{110}}"
                        on-change="loadMetrics"
                    >
                    </s-select>
                </span>
                <span class="search-item left_class">
                    <label class="search-item-label">统计项：</label>
                    <s-select
                        value="{=metrics.statistics=}"
                        datasource="{{ds.statistics}}"
                        width="{{110}}"
                        on-change="loadMetrics"
                    >
                    </s-select>
                </span>
                <span class="search-item left_class">
                    <label class="search-item-label">时间：</label>
                    <s-date-picker-date-range-picker
                        s-ref="timeRange"
                        value="{=peerTime.timeRange=}"
                        width="{{310}}"
                        range="{{peerTime.range}}"
                        mode="second"
                        on-change="onTimeChange('peerTime', $event)"
                        shortcut="{{shortcutItems}}"
                    />
                    <s-tip
                        class="inline-tip"
                        content="{{'最多支持1440个数据点的查询显示，请选择合适的采样周期和聚合时间段。'}}"
                        placement="top"
                        skin="question"
                        width="150"
                    />
                </span>
                <s-button class="s-icon-button" on-click="onInsTimeRefresh"
                    ><outlined-refresh class="icon-class"
                /></s-button>
                <s-button on-click="toAlarmDetail" class="left_class button-margin-left">报警详情</s-button>
            </div>
            <div class="peer-monitor-wrap">
                <bcm-chart-panel
                    s-ref="bcmChart"
                    api-type="metricName"
                    scope="BCE_PEERCON"
                    height="{{400}}"
                    options="{{options}}"
                    showbigable="{{false}}"
                    statistics="{{metrics.statistics}}"
                    dimensions="InstanceId:{{localIfId}}"
                    metrics="{=metrics.metrics=}"
                    period="{{peerTime.monitorDefaultPeriod}}"
                    unit="{{metrics.unit}}"
                    bitUnit="{{metrics.bitUnit}}"
                    sdk="{{bcmSdk}}"
                    connect-nulls="{{true}}"
                    startTime="{=peerTime.startTime=}"
                    endTime="{=peerTime.endTime=}"
                />
            </div>
        </div>
        <dl s-if="{{instanceMonitorDisplay && showTopN}}" class="vpc-peer-conn-info">
            <dt><h4>对等连接后端实例监控信息</h4></dt>
            <dd class="vpc-peer-conn-server">
                <div class="options_class time_picker_server">
                    时间范围：
                    <s-date-picker-date-range-picker
                        disabled="{{!vpcNetwork.datasource.length}}"
                        value="{=instanceTime.timeRange=}"
                        width="{{310}}"
                        shortcut="{{shortcutItems}}"
                        range="{{instanceTime.range}}"
                        on-change="onTimeChange('instanceTime', $event)"
                        mode="second"
                    />
                </div>
                <div class="options_class">
                    统计方式：
                    <s-select
                        disabled="{{!vpcNetwork.datasource.length}}"
                        value="{=metrics.statisticsMethods=}"
                        datasource="{{ds.statisticsMethods}}"
                    >
                    </s-select>
                </div>
                <div class="flex_class options_class">
                    vpc网段使用IP：
                    <s-select
                        datasource="{{vpcNetwork.datasource}}"
                        value="{=vpcNetwork.value=}"
                        disabled="{{!vpcNetwork.datasource.length}}"
                        width="200"
                        multiple
                        filterable
                        virtualThreshold="10"
                        checkAll="{{!(!displayTopN && vpcNetwork.datasource.length > 10)}}"
                    >
                    </s-select>
                    <s-tip skin="question" class="tip_class" content="{{peerMonitorTip}}" layer-width="300" />
                </div>
                <template>
                    <div class="check_class options_class">
                        <s-checkbox
                            disabled="{{!vpcNetwork.datasource.length}}"
                            label="{{'显示TOPN' | i18n}}"
                            checked="{=displayTopN=}"
                        />
                    </div>
                    <div s-if="{{displayTopN}}" class="options_class">
                        监控项：
                        <s-select datasource="{{ds.instanceMonitor}}" value="{=metrics.instanceMonitor=}" width="100" />
                    </div>
                </template>
                <div class="options_class">
                    <s-button
                        class="s-icon-button"
                        disabled="{{!vpcNetwork.datasource.length}}"
                        on-click="onTimeRefresh"
                    >
                        <outlined-refresh />
                    </s-button>
                </div>
            </dd>
            <div class="peer-conn-trends">
                <div class="conn-trend-box" s-for="item,index in serverChart">
                    <bcm-chart-panel
                        s-ref="peer-conn-alarm-chart-{{index}}"
                        withFilter="{{false}}"
                        scope="{{item.scope}}"
                        dimensions="{{item.dimensions}}"
                        statistics="{{item.statistics}}"
                        title="{{item.title}}"
                        api-type="dimensions"
                        options="{{options}}"
                        startTime="{=instanceTime.startTime=}"
                        endTime="{=instanceTime.endTime=}"
                        period="{=instanceTime.monitorDefaultPeriod=}"
                        metrics="{{item.metrics}}"
                        unit="{{item.unit}}"
                        bitUnit="{{item.bitUnit}}"
                        width="{{'auto'}}"
                        height="{{230}}"
                        sdk="{{bcmSdk}}"
                        proccessor="{{proccessor}}"
                    >
                    </bcm-chart-panel>
                </div>
            </div>
        </dl>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@asComponent('@peerconn-monitor')
class PeerConnMointor extends Component {
    static components = {
        'bcm-chart-panel': BcmChartPanel,
        'outlined-refresh': OutlinedRefresh
    };

    initData() {
        return {
            metrics: {
                option: 'bandwidth',
                statistics: 'average',
                statisticsMethods: 'average',
                instanceMonitor: 'DetailWebOutBytes',
                metrics: [],
                unit: 'bps',
                bitUnit: 1000
            },
            ds: {
                options: [
                    {text: '带宽', value: 'bandwidth'},
                    {text: '流量', value: 'flow'},
                    {text: '包速率', value: 'package'},
                    {text: '带宽使用率', value: 'used'},
                    {text: '限速丢包率', value: 'losePackage'},
                    {text: '限速丢包带宽', value: 'limitSpeedBandwidth'},
                    {text: '限速丢包速率', value: 'limitSpeedRate'}
                ],
                statistics: [
                    {text: '平均值', value: 'average'},
                    {text: '和值', value: 'sum'},
                    {text: '最大值', value: 'maximum'},
                    {text: '最小值', value: 'minimum'},
                    {text: '样本数', value: 'sampleCount'}
                ],
                statisticsMethods: [
                    {text: '平均值', value: 'average'},
                    {text: '和值', value: 'sum'},
                    {text: '最大值', value: 'maximum'},
                    {text: '最小值', value: 'minimum'}
                ],
                instanceMonitor: [
                    {text: '入流量', value: 'DetailWebInBytes'},
                    {text: '出流量', value: 'DetailWebOutBytes'},
                    {text: '入带宽', value: 'DetailWebInBitsPerSecond'},
                    {text: '出带宽', value: 'DetailWebOutBitsPerSecond'},
                    {text: '入包速率', value: 'DetailWebInPkgPerSecond'},
                    {text: '出包速率', value: 'DetailWebOutPkgPerSecond'}
                ]
            },
            instanceMonitorDisplay: false,
            instanceTime: {
                timeRange: {
                    begin: new Date(moment().subtract(1, 'hour').valueOf()),
                    end: new Date(moment().valueOf())
                },
                range: {
                    begin: new Date(moment().subtract(40, 'day').valueOf()),
                    end: new Date(moment().valueOf())
                },
                startTime: moment().subtract(1, 'hour').utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
                endTime: moment().utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
                monitorDefaultPeriod: 60
            },
            peerTime: {
                timeRange: {
                    begin: new Date(moment().subtract(1, 'hour').valueOf()),
                    end: new Date(moment().valueOf())
                },
                range: {
                    begin: new Date(moment().subtract(40, 'day').valueOf()),
                    end: new Date(moment().valueOf())
                },
                startTime: moment().subtract(1, 'hour').utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
                endTime: moment().utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
                monitorDefaultPeriod: 60
            },
            endOriginTime: moment().valueOf(),
            vpcNetwork: {
                value: [],
                datasource: []
            },
            displayTopN: false,
            shortcutItems,
            bcmSdk: new BcmSDK({client: window.$http, context: window.$context}),
            showTopN: false,
            proccessor: this.proccessor.bind(this),
            options: {dataZoom: {start: 0}}
        };
    }

    static computed = {
        peerMonitorTip() {
            if (this.data.get('displayTopN')) {
                return '最多选择10个监控对象';
            }
            // eslint-disable-next-line
            return 'vpc网段使用IP数量少于10个时，可以展示所有vpc网段使用IP的监控信息。超过10个时,用户可自定义选择需要展示的VPC网段使用IP的监控信息，最多可选10个。';
        }
    };

    attached() {
        this.loadMetrics();
        this.loadNetworkIp();
        this.checkDisplayTopN();
        this.watch('peerTime.timeRange', timeRange => {
            this.onTimeChange('peerTime', {value: timeRange});
        });
        this.watch('instanceTime.timeRange', timeRange => {
            this.onTimeChange('instanceTime', {value: timeRange});
        });
        if (this.data.get('instanceMonitorShow')) {
            this.data.set('instanceMonitorDisplay', true);
            this.watch('vpcNetwork.value', value => {
                if (!this.data.get('displayTopN')) {
                    this.updateNatserverDisable(value);
                }
                this.loadMetricsMethods();
            });
            this.watch('metrics.statisticsMethods', () => {
                if (this.data.get('displayTopN')) {
                    this.getPeerConnTopn();
                } else {
                    this.loadMetricsMethods();
                }
            });
            this.watch('displayTopN', val => {
                if (val) {
                    this.getPeerConnTopn();
                } else {
                    const vpcNetwork = this.data.get('vpcNetworks');
                    this.data.set('vpcNetwork.datasource', vpcNetwork);
                    let vpcNetworkDatasource = [];
                    if (vpcNetwork.length > 0) {
                        vpcNetworkDatasource = [vpcNetwork[0].value];
                    }
                    this.data.set('vpcNetwork.value', vpcNetworkDatasource);
                }
            });
            this.watch('metrics.instanceMonitor', async val => {
                if (this.data.get('displayTopN')) {
                    this.getPeerConnTopn();
                } else {
                    const lastVal = this.data.get('vpcNetwork.value');
                    this.loadNetworkIp(lastVal);
                }
            });
        }
    }
    loadNetworkIp(val) {
        return this.$http
            .getVpcNetworkIp({
                region: this.$context.getCurrentRegion().id,
                userId: this.$context.getUserId(),
                namespace: 'BCE_PEERCON',
                metricName: this.data.get('metrics.instanceMonitor'),
                resourceId: this.data.get('localIfId'),
                dimension: 'ClientIp'
            })
            .then(data => {
                let result = [];
                _.each(data || [], item => {
                    result.push({
                        value: item.value,
                        text: item.value
                    });
                });
                this.data.set('vpcNetworks', result);
                this.data.set('vpcNetwork.datasource', result);
                if (val) {
                    let selectedVal = _.filter(val, v => _.findIndex(result, item => item.value === v) > -1);
                    let resultValue = result.length ? [result[0].value] : [];
                    this.data.set('vpcNetwork.value', selectedVal.length > 0 ? selectedVal : resultValue);
                } else {
                    if (result.length > 0) {
                        this.data.set('vpcNetwork.value', [result[0].value]);
                    } else {
                        this.data.set('vpcNetwork.value', []);
                    }
                }
            });
    }
    loadMetrics() {
        let component = this.ref('bcmChart');
        if (component) {
            this.nextTick(() => {
                let option = this.data.get('metrics.option');
                this.data.set('metrics.metrics', peerMetrics[option].metrics);
                this.data.set('metrics.unit', peerMetrics[option].unit);
                this.data.set('metrics.bitUnit', peerMetrics[option].bitUnit);
                component.loadMetrics();
            });
        }
    }

    onInsTimeRefresh() {
        if (this.data.get('peerTime.timeRange.end').valueOf() >= this.data.get('endOriginTime')) {
            this.data.set('peerTime.timeRange.end', new Date(moment().valueOf()));
        } else {
            this.loadMetrics();
        }
    }

    loadMetricsMethods() {
        let chartConfig = [];
        let dimensions = [];
        const statistics = this.data.get('metrics.statisticsMethods');
        _.each(this.data.get('vpcNetwork.value'), value => {
            if (dimensions.length < 10) {
                dimensions.push('InstanceId:' + this.data.get('localIfId') + ';ClientIp:' + value);
            }
        });
        _.each(peerconnMetrics, item => {
            let config = {
                scope: 'BCE_PEERCON',
                period: 60,
                statistics,
                title: item.title,
                unit: item.unit,
                bitUnit: item.bitUnit,
                metrics: item.metrics,
                dimensions
            };
            chartConfig.push(config);
        });
        this.data.set('serverChart', chartConfig);
        this.nextTick(() => {
            this.onRefresh();
        });
    }

    proccessor(data) {
        const statistics = this.data.get('metrics.statisticsMethods');
        if (data && data.series && data.series.length > 0) {
            data.series.forEach(series => {
                series.name = series.name.split(',')[1] || series.name;
                series.data = series.data.map(item => {
                    if (!item || Object.keys(item).length === 0) {
                        let obj = {};
                        obj[statistics] = 0;
                        return obj;
                    }
                    return item;
                });
            });
        }
        return data;
    }

    updateNatserverDisable(value) {
        if (value.length < 10) {
            this.data.set('vpcNetwork.datasource', this.data.get('vpcNetworks'));
            return;
        }
        const datasource = _.cloneDeep(this.data.get('vpcNetwork.datasource'));
        for (let i = 0; i < value.length; i++) {
            for (let j = 0; j < datasource.length; j++) {
                if (datasource[j].value === value[i]) {
                    datasource[j].flag = true;
                }
            }
        }
        for (let i = 0; i < datasource.length; i++) {
            const item = {...datasource[i]};
            const flag = item.flag || false;
            delete item.flag;
            if (!flag) {
                item.disabled = true;
            }
            datasource[i] = {...item};
        }
        this.data.set('vpcNetwork.datasource', [...datasource]);
    }

    onRefresh() {
        let chartConfig = this.data.get('serverChart');
        _.map(chartConfig, (item, i) => {
            this.ref(`peer-conn-alarm-chart-${i}`)?.loadMetrics();
        });
    }

    onTimeRefresh() {
        if (this.data.get('instanceTime.timeRange.end').valueOf() >= this.data.get('endOriginTime')) {
            this.data.set('instanceTime.timeRange.end', new Date(moment().valueOf()));
        } else {
            this.onRefresh();
        }
    }
    async checkDisplayTopN() {
        const whiteList = window.$storage.get('commonWhite');
        this.data.set('showTopN', whiteList?.PeerConnTopNMonitor);
    }
    onTimeChange(type, {value}) {
        let startTime = moment(value.begin).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        let endTime = moment(value.end).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        let ms = moment(value.end) - moment(value.begin);
        let hourTime = Math.round(ms / 86400000);
        switch (true) {
            case hourTime <= 1:
                this.data.set(`${type}.monitorDefaultPeriod`, 60);
                break;
            case hourTime <= 3:
                this.data.set(`${type}.monitorDefaultPeriod`, 300);
                break;
            case hourTime <= 7:
                this.data.set(`${type}.monitorDefaultPeriod`, 600);
                break;
            case hourTime <= 14:
                this.data.set(`${type}.monitorDefaultPeriod`, 1800);
                break;
            case hourTime <= 40:
                this.data.set(`${type}.monitorDefaultPeriod`, 3600);
                break;
            default:
                break;
        }
        this.data.set(`${type}.startTime`, startTime);
        this.data.set(`${type}.endTime`, endTime);
        if (type === 'instanceTime') {
            this.data.get('displayTopN') && this.getPeerConnTopn();
            this.nextTick(() => {
                this.onRefresh();
            });
        } else {
            this.nextTick(() => {
                this.loadMetrics();
            });
        }
    }
    toAlarmDetail() {
        redirect('/bcm/#/bcm/alarm/rule/list~scope=BCE_PEERCON');
    }
    async getPeerConnTopn() {
        const {
            metrics: {statistics},
            instanceTime: {startTime, endTime},
            metrics: {instanceMonitor},
            localIfId
        } = this.data.get();
        const dimension = `InstanceId:${localIfId}`;
        const region = this.$context.getCurrentRegion().id;
        const params = {
            statistics,
            startTime,
            endTime,
            topNum: 10,
            dimension,
            service: 'BCE_PEERCON',
            region,
            userId: this.$context.getUserId(),
            metricName: instanceMonitor
        };
        const [{topDatas} = {topDatas: []}] = await this.$http.getServerNatWhite(params);
        const topNServer = topDatas.map(item => item.dimensions.filter(dimens => dimens.name === 'ClientIp')[0].value);
        let vpcNetworkDatasource = [];
        if (topNServer.length > 0) {
            vpcNetworkDatasource = topNServer.map(item => ({
                text: item,
                value: item
            }));
        }
        this.data.set('vpcNetwork.datasource', vpcNetworkDatasource);

        this.data.set('vpcNetwork.value', topNServer);
    }
}
export default Processor.autowireUnCheckCmpt(PeerConnMointor);
