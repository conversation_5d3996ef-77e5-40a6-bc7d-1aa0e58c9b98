.peer-instance-wrap {
    .icon-edit,
    .icon-copy {
        font-size: 12px;
        margin-left: 5px;
        color: #2468f2;
    }
    .instance-item-box {
        h4 {
            color: #151b26;
            line-height: 24px;
            font-weight: 500;
            font-size: 16px;
            margin-bottom: 16px;
        }
        margin-bottom: 4px;
        .instance-wrap {
            display: flex;
            flex-wrap: wrap;
        }
        .item-box {
            display: flex;
            align-self: center;
            margin-bottom: 16px;
            min-width: 30%;
            .item-key {
                flex-shrink: 0;
                align-self: center;
                display: inline-block;
                vertical-align: top;
                color: #5e626a;
                margin-right: 16px;
                width: 84px;
            }
            .item-value {
                display: inline-block;
                color: #151a26;
                max-width: 80%;
                word-break: break-all;
                position: relative;
                .inline-tip {
                    position: relative;
                    top: 3px;
                    margin-left: 12px;
                    .s-tip-warning:hover path {
                        fill: #ff9326;
                    }
                }
            }
        }
    }
    .text-break {
        word-wrap: break-all;
    }
}
