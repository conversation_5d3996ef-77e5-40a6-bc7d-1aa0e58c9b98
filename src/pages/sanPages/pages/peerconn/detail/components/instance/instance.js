import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {Notification} from '@baidu/sui';
import {San2React} from '@baidu/bce-react-toolkit';
import {OutlinedEditingSquare} from '@baidu/sui-icon';

import {utcToTime} from '@/pages/sanPages/utils/helper';
import {PeerConnStatus, PeerConnType, PeerConnRole} from '@/pages/sanPages/common/enum';
import Rule from '@/pages/sanPages/utils/rule';
const aihcAccountId = '8342d8a8b3654eafba85432324041c47';
import './style.less';

const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent, invokeComp} = decorators;
const AllRegion = window.$context.getEnum('AllRegion');

const tpl = html`
    <div>
        <div class="peer-instance-wrap">
            <div class="instance-item-box">
                <h4>基本信息</h4>
                <div class="instance-wrap">
                    <div class="item-box">
                        <div class="item-key">对等连接ID：</div>
                        <div class="item-value">{{instance.peerConnId}}</div>
                    </div>
                    <div class="item-box">
                        <div class="item-key">状态：</div>
                        <div class="item-value {{instance.status | statusClass}}">{{instance.status | statusText}}</div>
                    </div>
                    <div class="item-box">
                        <div class="item-key">连接类型：</div>
                        <div class="item-value">{{instance.connType | getConnType}}</div>
                    </div>
                    <div class="item-box">
                        <div class="item-key">带宽上限：</div>
                        <div class="item-value">
                            {{instance.bandwidth || 0}}Mbps
                            <s-button s-if="canUpgrade" skin="stringfy" on-click="toUpgrade">带宽升级</s-button>
                        </div>
                    </div>
                    <div class="item-box">
                        <div class="item-key">创建时间：</div>
                        <div class="item-value">{{instance.createTime | getTime}}</div>
                    </div>
                    <div class="item-box">
                        <div class="item-key">连接角色：</div>
                        <div class="item-value">{{instance.role | getConnRole}}</div>
                    </div>
                    <div class="item-box">
                        <div class="item-key">备注：</div>
                        <div class="item-value">
                            <span class="text-break">{{instance.desc}}</span>
                            <edit-popover value="{=instance.desc=}" rule="{{Rule.DESC}}" on-edit="updateDesc">
                                <outlined-editing-square s-if="canEdit" color="#2468f2" />
                            </edit-popover>
                        </div>
                    </div>
                    <div class="item-box" s-if="instance.role === 'initiator'">
                        <div class="item-key">释放保护：</div>
                        <div class="item-value">
                            <s-switch
                                disabled="{{deleteProtectStatus}}"
                                on-change="updateDeleteProtect"
                                checked="{=instance.deleteProtect=}"
                            />
                            <!--<s-tip
                            class="inline-tip"
                            skin="warning"
                            placement="topRight"
                            content="请确认您已解除相关的关联设备"
                        />-->
                        </div>
                    </div>
                </div>
            </div>
            <div class="instance-item-box">
                <h4>本端配置信息</h4>
                <div class="instance-wrap">
                    <div class="item-box">
                        <div class="item-key">本端地域：</div>
                        <div class="item-value">{{region.label}}</div>
                    </div>
                    <div class="item-box">
                        <div class="item-key">本端VPC ID：</div>
                        <div class="item-value">{{instance.localVpcShortId}}</div>
                    </div>
                    <div class="item-box">
                        <div class="item-key">本端接口名称：</div>
                        <div class="item-value">
                            <span class="text-break">{{instance.localIfName}}</span>
                            <edit-popover value="{=instance.localIfName=}" rule="{{Rule.NAME}}" on-edit="updateName">
                                <outlined-editing-square s-if="canEdit" color="#2468f2" />
                            </edit-popover>
                        </div>
                    </div>
                    <div class="item-box">
                        <div class="item-key">本端接口ID：</div>
                        <div class="item-value">
                            {{instance.localIfId}}
                            <s-clip-board text="{{instance.localIfId}}" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="instance-item-box">
                <h4>对端配置信息</h4>
                <div class="instance-wrap">
                    <div class="item-box">
                        <div class="item-key">对端地域：</div>
                        <div class="item-value">{{instance.peerRegion | getRegionLabel}}</div>
                    </div>
                    <div class="item-box">
                        <div class="item-key">对端VPC ID：</div>
                        <div class="item-value">
                            {{instance.peerAccountId === aihcAccountId ? '百舸AIHC' : instance.peerVpcShortId}}
                        </div>
                    </div>
                    <div class="item-box">
                        <div class="item-key">对端账户ID：</div>
                        <div class="item-value">
                            {{instance.peerAccountId === aihcAccountId ? '百舸AIHC' : instance.peerAccountId}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@invokeComp('@edit-popover')
@asComponent('@peerconn-detail')
class PeerConnInstance extends Component {
    static components = {
        'outlined-editing-square': OutlinedEditingSquare
    };
    static filters = {
        statusClass(value) {
            return PeerConnStatus.fromValue(value).styleClass;
        },
        statusText(value) {
            return PeerConnStatus.getTextFromValue(value);
        },
        getConnType(value) {
            return value === aihcAccountId ? '--' : PeerConnType.getTextFromValue(value) || '-';
        },
        getTime(value) {
            return utcToTime(value);
        },
        getConnRole(value) {
            return PeerConnRole.getTextFromValue(value);
        },
        getRegionLabel(value) {
            return AllRegion.getTextFromValue(value);
        }
    };

    static computed = {
        canEdit() {
            const instance = this.data.get('instance');
            if (!instance) {
                return false;
            }
            let cantEdit = [PeerConnStatus.AUDIT_FAILED, PeerConnStatus.AUDITING, PeerConnStatus.AUDITOR_PAUSE];
            return cantEdit.indexOf(instance.stauts) === -1;
        },
        canUpgrade() {
            const instance = this.data.get('instance');
            if (!instance) {
                return false;
            }
            return (
                !instance.orderStatus &&
                instance.status === PeerConnStatus.ACTIVE &&
                instance.role === PeerConnRole.INITIATOR
            );
        }
    };

    initData() {
        return {
            edit: {},
            region: window.$context.getCurrentRegion(),
            Rule: Rule.DETAIL_EDIT,
            deleteProtectStatus: false,
            aihcAccountId
        };
    }

    inited() {
        this.data.set('instance', this.data.get('context').instance);
    }

    toUpgrade() {
        const vpcId = this.data.get('context').vpcId;
        const localIfId = this.data.get('context').localIfId;
        location.hash = `#/vpc/peerconn/upgrade?vpcId=${vpcId}&localIfId=${localIfId}`;
    }

    updateName(value) {
        this.$http
            .peerUpdate({
                peerConnId: this.data.get('context').instance.peerConnId,
                localIfId: this.data.get('context').localIfId,
                localIfName: value
            })
            .then(() => {
                Notification.success('修改成功');
                this.data.set('instance.localIfName', value);
                // this.data.get('context').updateName();
            });
    }

    updateDesc(value) {
        this.$http
            .peerUpdate({
                peerConnId: this.data.get('context').instance.peerConnId,
                localIfId: this.data.get('context').localIfId,
                desc: value
            })
            .then(() => {
                Notification.success('修改成功');
                this.data.set('instance.desc', value);
            });
    }
    // 更新释放保护状态
    updateDeleteProtect({value}) {
        this.data.set('deleteProtectStatus', true);
        this.$http
            .peerconnUpdateDeleteProject({
                peerConnId: this.data.get('instance.peerConnId'),
                deleteProtect: value
            })
            .then(() => {
                this.data.set('deleteProtectStatus', false);
                this.data.set('instance.deleteProtect', value);
            })
            .catch(() => {
                this.data.set('deleteProtectStatus', false);
                this.data.set('instance.deleteProtect', !value);
            });
    }
}

export default San2React(Processor.autowireUnCheckCmpt(PeerConnInstance));
