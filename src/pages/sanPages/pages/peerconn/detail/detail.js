/*
 * @Description:
 * @Author: <EMAIL>
 * @Date: 2022-03-23 21:01:03
 */
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import {PeerConnStatus} from '@/pages/sanPages/common/enum';
import {OutlinedLeft} from '@baidu/sui-icon';
import './style.less';

const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, invokeComp} = decorators;

const tpl = html`
    <div>
        <s-app-detail-page class="peer-detail-wrap siderbar-tab {{instanceFound ? '' : 'no-instance-content'}}">
            <div slot="pageTitle" class="page-header">
                <s-app-link to="/network/#/vpc/peerconn/list" class="page-title-nav"><icon-left />返回</s-app-link>
                <h4 class="instance-name">{{instance.localIfName || '-'}}</h4>
                <span class="{{instance.status | statusClass}}" s-if="instance.status"
                    >{{instance.status | statusText}}</span
                >
            </div>
            <s-loading loading="{{loading}}" class="global-loading-class"></s-loading>
            <app-tab-page skin="accordion" active="{= active =}" s-if="instanceFound && !loading">
                <app-tab-page-panel
                    title="实例详情"
                    url="#/vpc/peerconn/detail?vpcId={{urlQuery.vpcId}}&localIfId={{urlQuery.localIfId}}"
                >
                    <peerconn-detail
                        vpcId="{{urlQuery.vpcId}}"
                        instance="{{instance}}"
                        localIfId="{{urlQuery.localIfId}}"
                        on-updateName="updateName"
                    ></peerconn-detail>
                </app-tab-page-panel>
                <app-tab-page-panel
                    title="监控"
                    url="#/vpc/peerconn/detail?vpcId={{urlQuery.vpcId}}&localIfId={{urlQuery.localIfId}}"
                >
                    <peerconn-monitor
                        instanceMonitorShow="{{true}}"
                        localIfId="{{urlQuery.localIfId}}"
                    ></peerconn-monitor>
                </app-tab-page-panel>
            </app-tab-page>
        </s-app-detail-page>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@invokeComp('@peerconn-detail', '@peerconn-monitor')
class PeerConnMain extends Component {
    static components = {
        'icon-left': OutlinedLeft
    };
    static filters = {
        statusClass(value) {
            return PeerConnStatus.fromValue(value).styleClass;
        },
        statusText(value) {
            return PeerConnStatus.getTextFromValue(value);
        }
    };

    initData() {
        return {
            instanceFound: true,
            loading: true,
            urlQuery: getQueryParams()
        };
    }

    inited() {
        this.getDetail();
    }

    getDetail() {
        this.$http
            .getPeerDetail({localIfId: this.data.get('urlQuery.localIfId')})
            .then(res => {
                if (!res.peerConnId) {
                    this.data.set('instanceFound', false);
                }
                this.data.set('instance', res);
            })
            .finally(e => this.data.set('loading', false));
    }

    onRegionChange() {
        location.hash = '#/vpc/peerconn/list';
    }

    updateName(value) {
        this.data.set('instance.localIfName', value);
    }
}
export default Processor.autowireUnCheckCmpt(PeerConnMain);
