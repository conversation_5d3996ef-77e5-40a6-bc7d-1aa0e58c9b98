import {defineComponent} from 'san';
import u from 'lodash';
import {html} from '@baiducloud/runtime';
import {Button, Form, Input, Table, Select, Dialog, Notification} from '@baidu/sui';
import {Empty} from '@baidu/sui-biz';

import {columns} from './tableField';
import {PeerConnStatus} from '@/pages/sanPages/common/enum';
const aihcAccountId = '8342d8a8b3654eafba85432324041c47';
import './style.less';

const AllRegion = window.$context.getEnum('AllRegion');

const template = html`
    <div>
        <s-dialog
            class="peer-wait-list"
            closeAfterMaskClick="{{false}}"
            open="{=open=}"
            on-close="onClose"
            width="1000"
            title="{{title}}"
        >
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                on-filter="onFilter"
                on-sort="onSort"
                track-id="ti_vpc_instance_table"
                track-name="列表操作"
            >
                <div slot="empty">
                    <s-empty>
                        <div slot="action"></div>
                    </s-empty>
                </div>
                <div slot="c-peerConnId">
                    <span class="text-hidden" title="{{row.peerConnId}}">{{row.peerConnId}}</span>
                </div>
                <div slot="c-peerRegion">{{row | getPeerRegion}}</div>
                <div slot="c-bandwidth">{{row | getBandWidth}}</div>
                <div slot="c-peerVpcShortId">
                    <span
                        class="text-hidden"
                        title="{{row.peerAccountId === aihcAccountId ? '百舸AIHC' : row.peerVpcShortId}}"
                        >{{row.peerAccountId === aihcAccountId ? '百舸AIHC' : row.peerVpcShortId}}</span
                    >
                </div>
                <div slot="c-peerAccountId">
                    <span
                        class="text-hidden"
                        title="{{row.peerAccountId === aihcAccountId ? '百舸AIHC' : row.peerAccountId}}"
                        >{{row.peerAccountId === aihcAccountId ? '百舸AIHC' : row.peerAccountId}}</span
                    >
                </div>
                <div slot="c-port">
                    <div s-if="row.status === 'consulting'">
                        <s-input
                            value="{=row.localIfName=}"
                            on-change="updateLocalData($event, rowIndex)"
                            class="{{row.errorInput ? 'error-tip' : ''}}"
                            placeholder="请输入本端端口名称"
                            width="120"
                        />
                    </div>
                </div>
                <div slot="c-opt">
                    <span s-if="row.status === 'consult_failed' || row.status === 'active'">
                        {{row.status | getOptText}}
                    </span>
                    <span s-if="row.status === 'consulting'">
                        <s-button skin="stringfy" on-click="confirmRole('accept', row, rowIndex)">接受</s-button>
                        <s-button skin="stringfy" on-click="confirmRole('reject', row)">拒绝</s-button>
                    </span>
                </div>
            </s-table>
            <div slot="footer"></div>
        </s-dialog>
    </div>
`;
export default defineComponent({
    template,
    components: {
        's-button': Button,
        's-form': Form,
        's-item': Form.Item,
        's-table': Table,
        's-input': Input,
        's-dialog': Dialog,
        's-select': Select,
        's-empty': Empty
    },
    filters: {
        getPeerRegion(item) {
            return AllRegion.getTextFromValue(item.peerRegion);
        },
        getBandWidth(item) {
            let bandwidth = item.bandwidth / 1000;
            if (bandwidth >= 1) {
                return `${bandwidth}Gbps`;
            }
            return item.bandwidth + 'Mbps';
        },
        getOptText(status) {
            let textMap = {
                consulting: '已拒绝',
                active: '已接受'
            };
            return textMap[status];
        }
    },

    initData() {
        return {
            title: '连接申请列表',
            open: true,
            table: {
                datasource: [],
                columns
            },
            aihcAccountId
        };
    },

    attached() {
        this.loadPage();
    },

    loadPage() {
        this.data.set('table.loading', true);
        let vpcInfo = this.data.get('vpcInfo');
        const payload = {
            localVpcShortId: vpcInfo.shortId,
            pageSize: 10000,
            pageNo: 1
        };
        this.$http.getPeerWaitList(payload).then(data => {
            this.data.set('table.datasource', data.result);
            this.data.set('table.loading', false);
        });
    },

    async confirmRole(type, row, index) {
        type === 'accept' && (await this.checkData(row, index));
        let requestMap = {
            accept: this.acceptRole.bind(this),
            reject: this.rejectRole.bind(this)
        };
        let requeset = requestMap[type];
        requeset(row).then(() => this.data.splice('table.datasource', [index, 1]));
    },

    acceptRole(row) {
        const {peerConnId, localIfId, localIfName} = row;
        return this.$http.peerAccept({peerConnId, localIfId, localIfName});
    },

    rejectRole(row) {
        const {peerConnId, localIfId} = row;
        return this.$http.peerconnReject({peerConnId, localIfId});
    },

    checkData(row, rowIndex) {
        let pattern = /^[a-zA-Z][\w\-\_\/\.]{0,64}$/;
        let hasError = false;
        let name = row.localIfName || '';
        if (row.status === PeerConnStatus.CONSULTING && !pattern.test(name)) {
            hasError = true;
        }
        this.data.set(`table.datasource[${rowIndex}].errorInput`, hasError);
        if (hasError) {
            Notification.error('本端端口名称填写有误，请重新输入');
        }
        return hasError ? Promise.reject() : Promise.resolve();
    },
    updateLocalData({value}, rowIndex) {
        this.data.set(`table.datasource[${rowIndex}].localIfName`, value);
    },

    onClose() {
        this.fire('finsh');
        this.dispose();
    },
    onRegionChange() {
        location.hash = '#/vpc/peerconn/list';
    }
});
