/**
 * @file network/peerconn/upgrade/success.es6
 * <AUTHOR>
 */
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {ContextService} from '@/pages/sanPages/common';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import './style.less';

const {asPage, invokeSUI, template} = decorators;
const tpl = html`
    <div class="vpc-peerconn-upgrade vpc-main-wrap">
        <div class="content-wrap">
            <div class="content-panel success">
                <div class="success-tip">
                    <h2>{{'开通成功！'}}</h2>
                    <p>{{'您购买的服务将在1-5分钟内开通，请耐心等待。'}}</p>
                    <p>
                        <s-button on-click="onClick" skin="stringfy">{{'管理控制台'}}</s-button>
                    </p>
                </div>
                <div class="additional-tips">
                    <h3>{{'温馨提示：'}}</h3>
                    <p>1. 感谢开通{{ProjectName}}服务，如您后续的操作遇到困难，您可以点击导航栏的“工单”提交问题。</p>
                </div>
            </div>
        </div>
    </div>
`;
@template(tpl)
@invokeSUI
class PeerConnUpgradeSuccess extends Component {
    initData() {
        return {
            ProjectName: FLAG.NetworkSupportXS ? '智能云' : ContextService.ProjectName
        };
    }
    onClick() {
        location.hash = '#/vpc/peerconn/list';
    }
}
export default San2React(Processor.autowireUnCheckCmpt(PeerConnUpgradeSuccess));
