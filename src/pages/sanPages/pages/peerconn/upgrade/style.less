.peerconn-upgrade-wrap {
    width: 100%;
    .upgrade-submit-order {
        .order-confirm {
            .order-legend {
                width: calc(100vw - 32px);
            }
        }
    }
    .upgrade-info-wrap {
        padding: 10px 20px;
        margin: 20px 0;
    }
    .upgrade-info-item-wrap {
        margin-top: 16px;
        display: flex;
        flex-wrap: wrap;
        .upgrade-info-item {
            display: flex;
            align-items: center;
            justify-self: center;
            width: 33%;
            margin-bottom: 16px;
            .info-item-key {
                min-width: 60px;
                display: inline-block;
                vertical-align: top;
                color: #5e626a;
                margin-right: 16px;
            }
            .info-item-value {
                color: #151a26;
                max-width: 80%;
                word-break: break-all;
                position: relative;
                .s-inputnumber {
                    width: 90px !important;
                }
            }
        }
        .slider-box,
        .eipcount-box {
            width: 100%;
            .info-item-key {
                width: 100px;
            }
        }
        .slider-box {
            margin-bottom: 20px;
        }
        .slider-tip {
            margin-left: 100px;
        }
    }
    .buybucket-container-wrap {
        width: auto;
        float: right;
        margin-right: 200px;
        .buybucket-content {
            float: right;
            margin-left: 20px;
        }
    }
    .order-confirm {
        // width: 980px;

        margin: 20px auto 0;
    }
    .tip-wrap {
        background-color: #f6f7fb;
        padding: 10px;
    }
    .form-part-wrap {
        border-radius: 6px;
        width: calc(~'100vw - 32px');
        background: #fff;
        margin: 16px;
        padding: 24px;
        h4 {
            display: inline-block;
            border: none;
            zoom: 1;
            color: #333;
            font-size: 16px;
            font-weight: 500;
            margin: 0;
            padding: 0;
        }
        .s-form-item-label {
            width: 130px;
            height: 30px;
        }
        .label_class {
            .inline-tip {
                top: 3px;
                position: relative;
                .s-tip-warning {
                    justify-content: center;
                    .warning_class {
                        fill: #999;
                    }
                }
                .s-tip:hover .s-icon path {
                    fill: #2468f2 !important;
                }
            }
        }
        .center_class {
            .s-row {
                .s-form-item-control-wrapper {
                    line-height: 30px;
                }
            }
        }
    }
    .buybucket {
        width: 100%;
        left: 0;
        height: 60px;
        .buybucket-container {
            width: auto;
            float: left;
            height: 48px;
            transform: translateY(12.5%);
            display: flex;
            align-items: center;
        }
    }
    .info-item-slider {
        display: flex;
        align-items: center;
        .s-slider {
            top: 5px;
        }
        .s-inputnumber-input {
            padding: 0 8px;
            width: calc(100% - 42px);
        }
    }
    // .s-create-page-content {
    //     margin: 0 20px;
    // }
}

.vpc-peerconn-upgrade {
    background-color: #e8ecf0;
    .content-wrap {
        margin: 0;
        width: 100%;
    }
    .buy-bucket-tip {
        color: #f18d36;
        margin: -8px 18px 20px 17px;
    }
    .table-info {
        width: 100%;
        label {
            color: #999;
            display: inline-block;
            width: 100px;
        }
        td {
            padding: 5px 0;
        }
    }
    .success {
        font-size: 14px;
    }
    .content-panel {
        margin: 0;
        padding: 20px;
        border: 1px solid #d0daf3;
        background-color: #fff;
    }
    .additional-tips {
        color: #999;
        line-height: 2em;
        padding: 0;
    }
    .success-tip {
        padding: 1em 0 1em 90px;
        background: url(https://bce.bdstatic.com/network-frontend/success.png) no-repeat 0 25px;
        border: 1px solid #d0daf3;
        border-width: 1px 0 0 0;
        h2 {
            font-size: 20px;
            color: #0786e9;
            line-height: 2em;
        }
        p {
            line-height: 2em;
        }
        a {
            margin-right: 1.5em;
        }
    }
    .skin-stringfy-button {
        padding: 0;
        margin-right: 20px;
        font-size: 14px;
    }
}
