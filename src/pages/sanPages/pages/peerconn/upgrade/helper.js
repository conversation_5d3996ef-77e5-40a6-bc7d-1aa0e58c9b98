import {PayType, PriceType} from '@/pages/sanPages/common/enum';
const AllRegion = window.$context.getEnum('AllRegion');

export const getOrderConfig = (instance, price = {price: 0.00}, network, isCross) => {
    const isPrepay = instance.productType === PayType.PREPAY;
    const isTraffic = instance.productType === PayType.POSTPAY && instance.subProductType === 'netraffic';
    const unit = isPrepay ? '' : isTraffic ? 'GB' : '分钟';
    let chargeType = '包年包月';
    let order = {
        managePrice: false,
        type: 'RESIZE',
        serviceName: isCross ? '跨境对等连接' : '对等连接',
        serviceType: isCross ? 'MKT' : 'PEERCONN',
        productType: instance.productType,
        count: 1,
        price: price.price,
        unit,
        region: window.$context.getCurrentRegionId(),
        chargeType: [PriceType.CPT2],
        configuration: [
            '当前地域：' + window.$context.getCurrentRegion().label,
            '对端地域：' + AllRegion.getTextFromValue(instance.peerRegion),
            '带宽上限：' + network.current + 'Mbps'
        ],
        configDetail: [
            {label: '当前地域', value: window.$context.getCurrentRegion().label, showInConfirm: false},
            {label: '对端地域', value: AllRegion.getTextFromValue(instance.peerRegion)},
            {label: '带宽上限', value: network.current + 'Mbps'}
        ],
        config: {}
    };
    if (isCross) {
        order.configDetail.push({
            label: '计费方式',
            value: chargeType
        });
    }
    return order;
};

export const setInstancePrice = (bucketItems, instance, price, isCross) => {
    const isPrepay = instance.productType === PayType.PREPAY;
    const isTraffic = instance.productType === PayType.POSTPAY && instance.subProductType === 'netraffic';
    const unit = isPrepay ? '' : isTraffic ? 'GB' : '分钟';
    bucketItems.set('productType', instance.productType);
    bucketItems.set('price', price.price);
    bucketItems.set('unit', unit);
    bucketItems.set('priceError', '');
};

export const getConfirmConfig = (instance, network, isCross) => {
    let config = {
            bandwidthInMbps: network,
            localIfId: instance.localIfId,
            peerConnId: instance.peerConnId,
            serviceType: 'PEERCONN'
        };
    if (isCross) {
        delete config.localIfId;
        delete config.serviceType;
    }
    return config;
};
