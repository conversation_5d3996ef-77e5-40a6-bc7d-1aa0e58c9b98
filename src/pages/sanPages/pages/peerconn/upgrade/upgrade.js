import {Component} from 'san';
import u from 'lodash';
import {html, decorators, redirect, ServiceFactory, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {BillingSDK, OrderItem} from '@baiducloud/billing-sdk';
import {OrderConfirm, ShoppingCart} from '@baiducloud/billing-sdk/san';

import {PeerConnStatus, PeerConnType, PayType} from '@/pages/sanPages/common/enum';
import {isCrossRegion} from '@/pages/sanPages/utils/common';
import {getOrderConfig, setInstancePrice, getConfirmConfig} from './helper';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import './style.less';

const {asPage, invokeSUI, invokeSUIBIZ, invokeAppComp, template} = decorators;
const AllRegion = window.$context.getEnum('AllRegion');
const cookie = ServiceFactory.resolve('$cookie');
const aihcAccountId = '8342d8a8b3654eafba85432324041c47';

const tpl = html`
    <div>
        <s-app-create-page
            class="peerconn-upgrade-wrap"
            backTo="{{pageNav.backTo}}"
            backToLabel="{{pageNav.backToLabel}}"
            pageTitle="{{pageNav.title}}"
        >
            <s-steps slot="step" current="{{steps.current}}" type="simple">
                <s-steps-step s-for="i in steps.datasource" title="{{i.title}}" description="{{i.description}}" />
            </s-steps>
            <div class="upgrade-main-content" s-if="{{steps.current === 1}}">
                <div class="upgrade-info-wrap form-part-wrap">
                    <h4>当前配置</h4>
                    <div class="upgrade-info-item-wrap">
                        <div class="upgrade-info-item">
                            <div class="info-item-key">{{'连接状态：'}}</div>
                            <div class="info-item-value">
                                <span class="{{instance.status | statusClass}}">{{instance.status | statusText}}</span>
                            </div>
                        </div>
                        <div class="upgrade-info-item">
                            <div class="info-item-key">付费方式：</div>
                            <div class="info-item-value">{{instance | getProductType}}</div>
                        </div>
                        <div class="upgrade-info-item">
                            <div class="info-item-key">本端地域：</div>
                            <div class="info-item-value">{{instance.localRegion | getRegionLabel}}</div>
                        </div>
                        <div class="upgrade-info-item">
                            <div class="info-item-key">{{'连接类型：'}}</div>
                            <div class="info-item-value">{{instance | getConnType}}</div>
                        </div>
                        <div class="upgrade-info-item">
                            <div class="info-item-key">带宽：</div>
                            <div class="info-item-value">{{instance.bandwidth}}Mbps</div>
                        </div>
                        <div class="upgrade-info-item">
                            <div class="info-item-key">对端地域：</div>
                            <div class="info-item-value">{{instance.peerRegion | getRegionLabel}}</div>
                        </div>
                    </div>
                </div>
                <div class="upgrade-info-wrap form-part-wrap">
                    <h4>变更配置</h4>
                    <div class="upgrade-info-item-wrap">
                        <div class="upgrade-info-item slider-box">
                            <div class="info-item-key">带宽：</div>
                            <div class="info-item-value info-item-slider">
                                <s-slider
                                    style="margin-right: 10px"
                                    value="{=network.current=}"
                                    width="{{450}}"
                                    min="{=network.min=}"
                                    max="{=network.max=}"
                                    marks="{{marks}}"
                                    disabled="{=network.disabled=}"
                                    on-input="onNetworkChange"
                                />
                                <s-input-number
                                    value="{=network.current=}"
                                    min="{=network.min=}"
                                    max="{=network.max=}"
                                    on-blur="onNetworkChange"
                                />
                                Mbps
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tip-wrap form-part-wrap">
                    <div>温馨提示：</div>
                    <div>1、当对等连接状态变为“可用”时开始计费。</div>
                    <div>2、跨账号互联对等连接由连接发起方支付费用，连接接收方无需付费。</div>
                </div>
                <div class="buybucket" slot="footer">
                    <div class="buybucket-container">
                        <s-tip-button
                            isDisabledVisibile
                            skin="primary"
                            size="large"
                            on-click="goToConfirm"
                            content="{{disableTip}}"
                            disabled="{{updating}}"
                        >
                            下一步</s-tip-button
                        >
                        <s-button size="large" on-click="cancel">取消</s-button>
                        <shopping-cart items="{{buyBucketItems}}" on-reset="onReset" />
                    </div>
                </div>
            </div>
            <div class="upgrade-submit-order" s-else>
                <order-confirm
                    s-ref="orderConfirm"
                    items="{{buyBucketItems}}"
                    sdk="{{sdk}}"
                    useCoupon="{{isPrepay && !isCross && hasPrice && FLAG.NetworkSupportXS}}"
                    theme="default"
                    showAgreementCheckbox
                />
                <div class="buybucket" slot="footer">
                    <div class="buybucket-container">
                        <s-button on-click="backToOrder" size="large" disabled="{{confirming}}">上一步</s-button>
                        <s-button size="large" on-click="cancel">取消</s-button>
                        <s-button skin="primary" size="large" on-click="onConfirm">提交订单</s-button>
                    </div>
                </div>
            </div>
        </s-app-create-page>
    </div>
`;
@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class PeerConnUpgrade extends Component {
    static components = {
        'order-confirm': OrderConfirm,
        'shopping-cart': ShoppingCart
    };

    static computed = {
        updating() {
            let loadPrice = this.data.get('loadPrice');
            let bandWidthNoChange = this.data.get('bandWidthNoChange');
            return loadPrice || bandWidthNoChange;
        },
        disableTip() {
            let loadPrice = this.data.get('loadPrice');
            let noChange = this.data.get('bandWidthNoChange');
            if (loadPrice) {
                return '价格获取中';
            }
            if (noChange) {
                return '带宽配置未变更';
            }
            return '';
        },
        bandWidthNoChange() {
            let networkBefore = this.data.get('instance.bandwidth');
            let networkCurrent = this.data.get('network.current');
            return networkBefore === networkCurrent;
        },
        isCross() {
            const instance = this.data.get('instance');
            if (!instance) {
                return false;
            }
            return isCrossRegion(instance.localRegion, instance.peerRegion);
        },
        isPrepay() {
            let isCross = this.data.get('isCross');
            return !isCross && this.data.get('instance.productType') === PayType.PREPAY;
        }
    };

    static filters = {
        statusClass(value) {
            return PeerConnStatus.fromValue(value).styleClass || '';
        },
        statusText(value) {
            return value ? PeerConnStatus.getTextFromValue(value) : '-';
        },
        getProductType(item) {
            if (!item) {
                return;
            }
            let str = PayType.getTextFromValue(item.productType);

            if (item.productType === PayType.POSTPAY) {
                str += !item.subProductType ? '' : item.subProductType === 'bandwidth' ? '-按带宽' : '-按流量';
            }
            return str;
        },
        getRegionLabel(value) {
            return AllRegion.getTextFromValue(value);
        },
        getConnType(item) {
            return item?.peerAccountId === aihcAccountId ? '--' : PeerConnType.getTextFromValue(item?.connType) || '-';
        },
        marks() {
            let network = this.data.get('network');
            let {min, max} = network;
            let middle = Math.floor((max + min) / 2);
            let marks = {};
            marks[min] = min + 'Mbps';
            marks[middle] = middle + 'Mbps';
            marks[max] = max + 'Mbps';
            return marks;
        }
    };

    initData() {
        return {
            FLAG,
            pageNav: {
                // 标题栏
                title: '带宽调整',
                backTo: '/network/#/vpc/peerconn/list',
                backToLabel: '返回'
            },
            steps: {
                datasource: [
                    {
                        title: '对等连接带宽升级'
                    },
                    {
                        title: '创建成功'
                    }
                ],
                current: 1
            },
            network: {
                min: 1,
                max: 1000
            },
            loadPrice: false,
            price: {
                price: 0.0
            },
            urlQuery: getQueryParams()
        };
    }

    inited() {
        this.getInstanceDetail();
    }

    attached() {
        this.watch(
            'network.current',
            u.debounce(value => {
                this.getPrice();
                this.updateBandWidth();
            }, 300)
        );
    }

    cancel() {
        location.hash = '#/vpc/peerconn/list';
    }

    getBandWidthMax(isSameRegion) {
        return this.$http.getPeerconnBandwidthQuota().then(data => {
            const {bandwidth, crossBandwidth} = data || {};
            const currBandwidth = isSameRegion ? bandwidth : crossBandwidth;
            this.data.set('network.max', currBandwidth);
        });
    }

    getInstanceDetail() {
        const localIfId = this.data.get('urlQuery.localIfId');
        this.$http.getPeerDetail({localIfId}).then(async data => {
            this.data.set('instance', data);
            const isCross = this.data.get('isCross');
            const {localRegion, peerRegion} = data;
            await this.getBandWidthMax(localRegion === peerRegion);
            this.data.set('network.current', data.bandwidth);
            if (data.productType === PayType.PREPAY && isCross) {
                this.data.set('network.min', data.bandwidth);
            }
            this.initOrderItem();
        });
    }

    initOrderItem() {
        let instance = this.data.get('instance');
        let price = this.data.get('price');
        let network = this.data.get('network');
        let isCross = this.data.get('isCross');
        let orderConfig = getOrderConfig(instance, price, network, isCross);
        const contextPipe = {
            getOrderSuccessUrl() {
                return window.$context.getOrderSuccessUrl() || {};
            },
            getCurrentRegion() {
                return window.$context.getCurrentRegionId();
            },
            getCsrfToken() {
                // 返回cookie中的信息
                return cookie.get('bce-user-info');
            },
            SERVICE_TYPE: this.data.get('isCross') ? 'MKT' : 'PEERCONN'
        };
        let order = new OrderItem(orderConfig, contextPipe);
        this.data.set('buyBucketItems', [order]);
    }

    getPrice() {
        const instance = this.data.get('instance');
        let isCross = this.data.get('isCross');
        // 后付费按带宽不询价
        if (instance.productType === PayType.POSTPAY && instance.subProductType === 'bandwidth') {
            return Promise.resolve();
        }
        let request = '';
        //是否是跨境
        if (isCross) {
            request = this.getCrossPrice.bind(this);
        } else {
            request = this.getUpdatePrice.bind(this);
        }
        this.data.set('loadPrice', true);
        return request()
            .then(price => {
                if (isCross) {
                    this.data.set('price', price);
                } else {
                    if (price[0].price < 0) {
                        this.data.set('hasPrice', false);
                    } else {
                        this.data.set('hasPrice', true);
                    }
                    this.data.set('price', price[0]);
                }
                this.updateOrderItems();
            })
            .catch(error => {
                const items = this.data.get('buyBucketItems');
                items[0].set('priceError', '询价失败');
            })
            .finally(() => {
                this.data.set('loadPrice', false);
            });
    }

    updateOrderItems() {
        let instance = this.data.get('instance');
        let buyBucketItems = this.data.get('buyBucketItems[0]');
        let isCross = this.data.get('isCross');
        if (!buyBucketItems) {
            return;
        }
        let price = this.data.get('price');
        setInstancePrice(buyBucketItems, instance, price, isCross);
        this.data.set('buyBucketItems', [buyBucketItems]);
    }

    updateBandWidth() {
        let network = this.data.get('network.current');
        let buyBucketItems = this.data.get('buyBucketItems[0]');
        buyBucketItems.updateOrderItem('configDetail[2].value', network + 'Mbps');
        this.data.set('buyBucketItems', [buyBucketItems]);
    }

    getUpdatePrice() {
        const {instance, network} = this.data.get('');
        let regionStr = [instance.localRegion, instance.peerRegion].sort((a, b) => {
            return a.localeCompare(b);
        });
        // 预付费询价
        const config = {
            serviceType: 'PEERCONN',
            count: 1,
            orderType: 'DILATATION',
            productType: instance.productType,
            region: window.$context.getCurrentRegionId(),
            instanceId: instance.peerConnId,
            flavor: [
                {
                    name: 'subServiceType',
                    value: 'default',
                    scale: 1
                },
                {
                    name: `${regionStr[0]}-${regionStr[1]}-bandwidth`,
                    value: network.current,
                    scale: 1
                }
            ]
        };
        if (instance.productType === PayType.POSTPAY) {
            // 后付费流量先用新购的参数
            delete config.orderType;
            delete config.instanceId;
            config.scene = 'NEW';
            config.orderType = 'NEW';
            config.amount = '1000000000';
            config.chargeItem = `NetTraffic_${AllRegion[regionStr[0]]}_${AllRegion[regionStr[1]]}`;
            config.flavor = [
                {
                    name: 'subServiceType',
                    value: 'default',
                    scale: 1
                }
            ];
        }
        return this.$http.priceV3({configs: [config]});
    }

    //跨境询价
    getCrossPrice() {
        // mkt变配不能走v3接口
        const {instance, network} = this.data.get('');
        const payload = {
            peerConnId: instance.peerConnId,
            bandwidthInMbps: network.current
        };
        return this.$http.getCrossPeerPrice(payload);
    }

    upgradePostPay() {
        const {instance, network} = this.data.get('');
        const payload = {
            orderRegion: instance.peerRegion,
            peerConnId: instance.peerConnId,
            localIfId: instance.localIfId,
            bandwidthInMbps: network.current,
            localIfName: instance.localIfName,
            desc: instance.desc
        };
        return this.$http.upgradePeerBandWidth(payload);
    }

    backToOrder() {
        this.data.set('steps.current', 1);
    }

    goToConfirm() {
        const {instance} = this.data.get('');
        // 后付费不走询价
        if (instance.productType === PayType.POSTPAY) {
            this.upgradePostPay().then(() => this.redirectSuccess());
            return;
        }
        const contextPipe = {
            getOrderSuccessUrl() {
                return window.$context.getOrderSuccessUrl() || {};
            },
            getCurrentRegion() {
                return window.$context.getCurrentRegionId();
            },
            getCsrfToken() {
                // 返回cookie中的信息
                return cookie.get('bce-user-info');
            },
            SERVICE_TYPE: this.data.get('isCross') ? 'MKT' : 'PEERCONN'
        };
        const sdkOptions = {
            type: 'RESIZE',
            serviceType: this.data.get('isCross') ? 'MKT' : 'PEERCONN',
            serviceName: this.data.get('isCross') ? '跨境对等连接' : '对等连接',
            productType: instance.productType,
            region: window.$context.getCurrentRegionId()
        };
        const sdk = new BillingSDK(sdkOptions, contextPipe);
        this.data.set('sdk', sdk);
        this.data.set('steps.current', 2);
    }

    // 确认订单的时候 询价
    async onConfirm() {
        await this.ref('orderConfirm').validateAgreement();
        this.getPrice().then(() => {
            const instance = this.data.get('instance');
            const network = this.data.get('network.current');
            const sdk = this.data.get('sdk');
            const isCross = this.data.get('isCross');
            const config = getConfirmConfig(instance, network, isCross);

            let buyBucketItems = this.data.get('buyBucketItems[0]');
            buyBucketItems.set('config', config);
            const payload = {
                url: isCross ? '/api/peerconn/crossBorder/order/resize' : '/api/peerconn/peerconn/order/confirm/resize',
                type: 'RESIZE',
                instances: [buyBucketItems]
            };
            sdk.confirmOrder(payload)
                .then(result => {
                    window.location.href = result.url;
                })
                .catch(result => {
                    result.url && (window.location.href = result.url);
                    this.data.set('confirming', false);
                });
        });
    }

    redirectSuccess() {
        const {vpcId, localIfId} = this.data.get('urlQuery');
        location.hash = `#/vpc/peerconn/upgrade/success?vpcId=${vpcId}&localIfId=${localIfId}`;
    }

    onReset() {
        let instance = this.data.get('instance');
        this.data.set('network.current', instance.bandwidth);
    }
    onRegionChange() {
        location.hash = '#/vpc/peerconn/list';
    }
}
export default San2React(Processor.autowireUnCheckCmpt(PeerConnUpgrade));
