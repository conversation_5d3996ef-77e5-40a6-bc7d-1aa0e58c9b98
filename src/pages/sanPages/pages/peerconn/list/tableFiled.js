import {PayType, PeerConnStatus} from '@/pages/sanPages/common/enum';
export const columns = [
    {
        name: 'peerConnId',
        label: '对等连接ID',
        width: 120,
        fixed: 'left'
    },
    {
        name: 'localIf',
        label: '本端接口名称/ID',
        width: 140
    },
    {
        name: 'status',
        label: '状态',
        width: 90,
        filter: {
            options: [
                {
                    text: '全部',
                    value: ''
                },
                ...PeerConnStatus.toArray()
            ],
            value: ''
        }
    },
    {
        name: 'localVpcShortId',
        label: '所在网络',
        width: 120
    },
    {
        name: 'connType',
        label: '连接类型',
        width: 80
    },
    {
        name: 'peerRegion',
        label: '对端区域',
        width: 100
    },
    {
        name: 'DNSSync',
        label: 'DNS同步',
        width: 100
    },
    {
        name: 'peerVpcShortId',
        label: '对端网络/ID',
        width: 160
    },
    {
        name: 'bandwidth',
        label: '带宽上限',
        width: 120
    },
    {
        name: 'productType',
        label: '支付方式',
        width: 120,
        filter: {
            options: [
                {
                    text: '全部',
                    value: ''
                },
                {
                    text: PayType.getTextFromAlias('PREPAY'),
                    value: PayType.PREPAY
                },
                {
                    text: PayType.getTextFromAlias('POSTPAY'),
                    value: PayType.POSTPAY
                }
            ],
            value: ''
        }
    },
    {
        name: 'role',
        label: '连接角色',
        width: 90
    },
    {
        name: 'createTime',
        label: '创建时间',
        width: 120
    },
    {
        name: 'expireTime',
        label: '到期时间',
        width: 120
    },
    {
        name: 'tag',
        label: '标签',
        sortable: true,
        width: 80
    },
    {
        name: 'resourceGroups',
        label: '资源分组',
        width: 90
    },
    {
        name: 'opt',
        label: '操作',
        width: 148,
        fixed: 'right'
    }
];
