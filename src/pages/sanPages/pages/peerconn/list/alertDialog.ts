import u from 'lodash';
import {Component} from 'san';
import {html, redirect} from '@baiducloud/runtime';
import {Form, Select, Radio, Table, Button, Dialog} from '@baidu/sui';

import {PayType} from '@/pages/sanPages/common/enum';
import './style.less';

const ALERTPPRODUCTLIST = [
  { text: '预付费', value: PayType.PREPAY},
  { text: '后付费-日峰值带宽', value: 'bandwidth' },
  { text: '后付费-按流量', value: 'netraffic' },
  { text: '后付费-传统型月95', value: 'PeakBandwidth_Percent_95' },
];
const tpl = html`
    <div>
        <s-dialog title="{{title}}" open="{{true}}" width="{{600}}" class="peerconn-alert">
            <s-form s-ref="form" data="{=formData=}">
                <s-form-item label="{{'当前计费：'}}" prop="name" class="paytype-wrap">
                    <div>{{currentLabel}}</div>
                </s-form-item>
                <s-form-item label="{{'计费变更为：'}}" prop="productType">
                    <s-radio-group
                        radioType="button"
                        datasource="{{productTypeList}}"
                        value="{=formData.productType=}"
                        track-id="ti_vpc_nat_create"
                        track-name="付费方式"
                    >
                    </s-radio-group>
                </s-form-item>
                <s-form-item label="{{'对等连接ID：'}}" prop="productType">
                    <div>{{currentID}}</div>
                </s-form-item>
            </s-form>
            <div slot="footer">
                <s-button on-click="onClose">取消</s-button>
                <s-button skin="primary" on-click="doSubmit" disabled="{{setConfirmState}}">确定</s-button>
            </div>
        </s-dialog>
    </div>
`;
export default class PeerconnAlter extends Component implements Extracted {
    static template = tpl;
    static components = {
        's-form': Form,
        's-form-item': Form.Item,
        's-radio-group': Radio.RadioGroup,
        's-table': Table,
        's-select': Select,
        's-button': Button,
        's-dialog': Dialog,

    };
    initData() {
        return {
            PayType,
            productTypeList: [],
            setConfirmState: false,
            currentLabel: '',
            currentID: ''
        };
    }
    inited() {
        let item = this.data.get('item');
        // producType 后 or 预
        if (item.productType === PayType.PREPAY) {
            this.data.set('productTypeList', [ALERTPPRODUCTLIST[1], ALERTPPRODUCTLIST[2]]);
        } else {
            const productTypeMap = {
              bandwidth: [ALERTPPRODUCTLIST[0], ALERTPPRODUCTLIST[2]],
              netraffic: [ALERTPPRODUCTLIST[0], ALERTPPRODUCTLIST[1]],
              PeakBandwidth_Percent_95: [ALERTPPRODUCTLIST[1]]
            }
            const whiteList = window.$storage.get('commonWhite');
            if (whiteList?.PeerConnTro95WhiteList) {
              productTypeMap.bandwidth.push(ALERTPPRODUCTLIST[3]);
            }
            this.data.set('productTypeList', productTypeMap[item.subProductType]);
        }
        this.data.set('formData.productType', this.data.get('productTypeList')[0].value);
    }
    static computed = {
        currentLabel() {
            let item = this.data.get('item');
            if (item) {
                if (item.productType === PayType.PREPAY) {
                    return PayType.getTextFromValue(item.productType);
                } else {
                    let str = PayType.getTextFromValue(PayType.POSTPAY);
                    str += item.subProductType === 'bandwidth' ? '-按带宽' : item.subProductType === 'netraffic' ? '-按流量' : '-按传统型月95';
                    return str;
                }
            } else {
                return '';
            }
        },
        currentID() {
          let item = this.data.get('item');
          return item ? item.peerConnId : '-';
        }
    };


    onClose() {
        this.data.set('setConfirmState', false);
        this.dispose();
    }

    async doSubmit() {
        this.data.set('setConfirmState', true);
        let item = this.data.get('item');
        let isPrepay = item.productType === PayType.PREPAY;
        let productType = this.data.get('formData.productType');
        // 是否为后付费转后付费
        let isPosttoPost = !isPrepay && productType !== PayType.PREPAY;
        let type = isPrepay ? 'TO_POSTPAY' : (isPosttoPost ?  'TO_POSTPAY' :'TO_PREPAY');
        let url = isPrepay ? '/api/peerconn/peerconn/order/confirm/to_postpay'
          : (isPosttoPost ? '/api/peerconn/peerconn/order/confirm/to_postpay' : '/api/peerconn/peerconn/order/confirm/to_prepay');
        let param = {
            serviceType: 'PEERCONN',
            instanceIds: item.peerConnId,
            type,
            region: window.$context.getCurrentRegionId(),
            confirmV2Url: url
        };
        u.extend(param, {
            bandwidths: item.bandwidth,
            subProductType: productType === PayType.PREPAY ? 'bandwidth' : productType,
        });
        // 对billing后付费-按流量计费显示的处理
        let billingParam = {
            subProductTypeBefore: item.productType === PayType.PREPAY ? 'bandwidth' : item.subProductType,
            subProductType: productType === PayType.PREPAY ? 'bandwidth' : productType,
            productType: isPrepay ? PayType.POSTPAY : (isPosttoPost ?  PayType.POSTPAY : PayType.PREPAY),
            productTypeBefore: item.productType,
            isPosttoPost,
            data: [item]
        };
        sessionStorage.setItem('PEERCONN_ALTER', JSON.stringify(billingParam));
        if (isPrepay || isPosttoPost) {
            u.extend(param, {
              localVpcShortId: item.localVpcShortId
            });
        }
        redirect({
            module: 'billing',
            path: '/billing/alter/productTypeList',
            paramSeperator: '~',
            params: param
        });
        this.onClose();
    }
}

