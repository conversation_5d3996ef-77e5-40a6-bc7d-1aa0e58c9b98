import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import PeerconnMonitor from '../detail/components/monitorChart/monitorComponents';

const {invokeSUI, invokeAppComp, invokeComp, template, asComponent} = decorators;

const tpl = html`
    <div>
        <s-dialog class="vpc-peer-monitor-box" open="{{true}}" title="监控" width="{{1000}}" height="{{600}}">
            <peerconn-monitor vpcId="{{vpcId}}" localIfId="{{localIfId}}" />
            <div slot="footer"></div>
        </s-dialog>
    </div>
`;

@invokeSUI
@invokeAppComp
@invokeComp('@peerconn-monitor')
@asComponent('@peerconn-list-monitor')
@template(tpl)
class PeerConnBcmDetail extends Component {
    static components = {
        'peerconn-monitor': PeerconnMonitor
    };
}
export default Processor.autowireUnCheckCmpt(PeerConnBcmDetail);
