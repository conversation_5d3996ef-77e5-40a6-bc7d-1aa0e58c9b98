.peerconn-list-wrap {
    width: 100%;
    flex: 1;
    overflow: auto;
    .peerconn-header-wrapper {
        position: absolute;
        z-index: 9999;
        top: 0px;
        color: #151b26;
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
        padding-top: 2px;
        padding-bottom: 4px;
        background-color: #fff;
        width: 100%;
        display: flex;
        left: 0px;
        justify-content: space-between;
        .title {
            display: inline-block;
            margin: 0;
            color: #151b26;
            height: 47px;
            line-height: 47px;
            font-weight: 500;
            font-size: 16px;
            margin: 0 12px 0 16px;
        }
        .vpc-select {
            line-height: 47px;
        }
        .list-header-wrap {
            margin-left: auto;
            // margin-right: 16px;
            height: 48px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #fff;
            .header-left {
                .title {
                    display: inline-block;
                    margin: 0;
                    color: #151b26;
                    margin-right: 12px;
                    height: 47px;
                    line-height: 47px;
                    font-weight: 500;
                    font-size: 16px;
                    margin-left: 16px;
                }
            }
            .header-right {
                display: flex;
                align-items: center;
                margin: 0 16px;
                .help-file {
                    margin-right: 16px;
                    font-size: 12px;
                    font-weight: 400;
                    font-family: PingFangSC-Regular;
                    color: #151b26;
                    line-height: 20px;
                    &:hover {
                        color: #2468f2;
                    }
                    .s-icon {
                        position: relative;
                        top: -1px;
                        right: 4px;
                        margin-right: 0;
                        color: #2468f2;
                        font-size: 14px;
                    }
                }
                .function-introduce {
                    color: #2468f2;
                }
                .button-shortcut {
                    padding: 0;
                }
                .link-wrap {
                    display: flex;
                    align-items: center;
                    margin: 0 16px;
                    .peerconn-tip {
                        background: none;
                        font-size: 12px;
                        font-weight: 400;
                    }
                }
            }
        }
    }
    .remote-text-wrap {
        display: flex;
    }
    .s-table {
        .s-table-row:hover {
            .name-icon {
                display: inline;
            }
        }
        .name-icon {
            display: none;
            font-size: 12px;
            fill: #2468f2;
        }
        .s-table-body {
            max-height: calc(~'100vh - 334px');
            .operations {
                .s-button {
                    padding: 0;
                    margin-right: 16px;
                    &:nth-child(even) {
                        margin-right: 0px;
                    }
                }
            }
        }
        .s-table-empty {
            border-bottom: none;
        }
    }
    .icon-bind,
    .icon-unbind {
        font-size: 16px;
    }
    .icon-renewmanage {
        color: #2468f2;
    }
    .header-button-wrap {
        margin-left: auto;
        display: flex;
        align-items: center;
        .s-button {
            padding: 0;
        }
        .link-wrap {
            margin-left: 8px;
            margin-right: 16px;
        }
        .outlined-link {
            font-size: 14px;
            margin-right: 4px;
        }
    }
    .button-shortcut {
        background-color: #f5f5f5;
        border-color: #ebebeb;
    }
    .peerconn-tip {
        background: #fcf7f1;
        padding: 5px;
        margin-left: 10px;
        color: #f38900;
    }
    .intro-warp {
        display: inline-block;
        margin-left: 8px;
        .placeholder-style {
            input::-webkit-input-placeholder {
                /*WebKit browsers*/
                color: #000;
            }
            input::-moz-input-placeholder {
                /*Mozilla Firefox*/
                color: #000;
            }

            input::-ms-input-placeholder {
                /*Internet Explorer*/
                color: #000;
            }
        }
    }

    .icon-edit,
    .icon-copy {
        font-size: 12px;
        color: #0786e9;
    }
    .tip-icon-wrap {
        font-size: 14px;
        border: 1px solid #f18823;
        color: #f18823;
        &:hover {
            color: #fff;
            background-color: #f18823;
        }
    }
    .help-icon-wrap {
        color: #9e9898;
        border: 1px solid #9e9898;
        margin-left: 10px;
        font-size: 12px;
        &:hover {
            border-color: #2468f2;
        }
    }
    .foot-pager {
        margin-top: 16px;
    }
    .filter-buttons-wrap {
        display: flex;
        align-items: center;
        .s-cascader {
            margin-right: 5px;
        }
        .s-cascader-value {
            vertical-align: middle;
            font-size: 12px;
            padding-top: 0;
            padding-bottom: 0;
            line-height: 30px;
        }
        .s-auto-compelete {
            .s-select {
                input {
                    width: 170px !important;
                }
            }
        }
        .button-item {
            margin-left: 8px;
        }
        .download-item {
            margin-right: 8px;
        }
        .search-content {
            display: flex;
            align-items: center;
            position: relative;
            margin-right: 5px;
        }
        .s-icon.search {
            position: absolute;
            right: 5px;
            color: #615a5a;
        }
        .icon-fresh {
            margin-right: 5px;
        }
    }
    .resource-group-search .s-cascader .s-cascader-value {
        min-width: 112px !important;
    }
}
.peerconn-edit-wrap {
    width: 200px;
}
.peerconn-conn-list-table {
    .s-table-thead {
        .s-table-hcell-sel {
            padding-left: 0px !important;
        }
    }
}

.peerconn-monitor-box {
    .search-box {
        display: flex;
        .search-item {
            display: flex;
            align-items: center;
            margin-right: 5px;
            .search-label {
                flex-shrink: 0;
            }
        }
    }
    .tip-icon-wrap {
        font-size: 14px;
        border: 1px solid #f18823;
        margin-left: 10px;
        color: #f18823;
        &:hover {
            color: #fff;
            background-color: #f18823;
        }
    }
}

.peer-monitor-wrap {
    text-align: center;
}

.peerconn-alert {
    .peerconn-select-tip {
        margin-bottom: 4px;
    }
    .peerconn-alert-custom {
        .paytype-wrap {
            margin-top: 0 !important;
        }
        .s-table-container {
            overflow: hidden;
        }
        .s-popup-content-box {
            left: 507px;
        }
        .s-form-item-control {
            line-height: 30px;
        }
        .s-form-item-label {
            width: 100px;
            text-align: left;
        }
        .renew-expire-time {
            color: #666;
        }
    }
}
