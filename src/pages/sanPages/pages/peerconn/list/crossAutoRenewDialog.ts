import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import './style.less';
import {Dialog, Button, Radio, Loading} from '@baidu/sui';

const tpl = html`
    <div>
        <s-dialog title="{{title}}" open="{{true}}" width="{{600}}" class="peerconn-alert">
            <div>
                <div class="peerconn-select-tip">请选择续费周期：</div>
                <s-radio-group
                    enhanced
                    radioType="button"
                    datasource="{{purchaseLength}}"
                    value="{=currPurchaseLength=}"
                >
                </s-radio-group>
            </div>
            <div slot="footer">
                <s-button on-click="onClose">取消</s-button>
                <s-button loading="{{loading}}" skin="primary" on-click="doSubmit">确定</s-button>
            </div>
        </s-dialog>
    </div>
`;
export default class CrossAutoRenewDialog extends Component {
    static template = tpl;
    static components = {
        's-dialog': Dialog,
        's-button': Button,
        's-radio-group': Radio.RadioGroup
    };
    initData(): Partial<{}> {
        return {
            loading: false,
            currPurchaseLength: 1,
            purchaseLength: [
                {text: '1个月', value: 1},
                {text: '2', value: 2},
                {text: '3', value: 3},
                {text: '4', value: 4},
                {text: '5', value: 5},
                {text: '6', value: 6},
                {text: '7', value: 7},
                {text: '8', value: 8},
                {text: '9', value: 9},
                {text: '1年', value: 12},
                {text: '2年', value: 24},
                {text: '3年', value: 36}
            ]
        };
    }
    onClose() {
        this.dispose();
    }
    async doSubmit() {
        this.data.set('loading', true);
        try {
            const purchaseLength = this.data.get('currPurchaseLength');
            const rowData = this.data.get('rowData');
            const {peerConnId} = rowData;
            const payload = {
                instanceId: peerConnId,
                duration: purchaseLength
            };
            const res = await this.$http.openCrossRegionAutoRenew(payload);
            this.fire('renewSuccess');
            this.onClose();
        } catch (error) {
        } finally {
            this.data.set('loading', false);
        }
    }
}
