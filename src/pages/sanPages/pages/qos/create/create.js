/*
 * @description: 创建qos
 * @file: network/qos/create/create.js
 * @author: <EMAIL>
 */

import _ from 'lodash';
import {html, decorators, redirect, CreatePage, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {TagEditPanel} from '@baidu/bce-tag-sdk-san';
import {Notification} from '@baidu/sui';
import {parseQuery} from '@/utils';
import rule from '@/pages/sanPages/utils/rule';
import './style.less';
import {Select} from '@baidu/sui';
import {kXhrOptions, $flag as FLAG} from '@/pages/sanPages/utils/helper';

const {asPage, invokeSUI, invokeSUIBIZ, template} = decorators;
const tpl = html`
    <template>
        <s-app-create-page class="vpc-qos-create"
            backTo="{{pageNav.backUrl}}"
            backToLabel="{{pageNav.backLabel}}" pageTitle="{{pageNav.title}}">
            <s-form s-ref="form"
                data="{=formData=}"
                rules="{{rules}}"
                label-col="{{labelCol}}"
                wrapper-col="{{wrapperCol}}"
                label-align="left">
                <div class="content-box form-part-wrap">
                    <h4>配置信息</h4>
                    <s-form-item prop="vpcId" label="所在私有网络：">
                        <s-select
                            width="{{320}}"
                            disabled="{{selectQosDisable}}"
                            value="{=formData.vpcId=}"
                            on-change="vpcChange($event)">
                            <s-option
                                s-for="item in vpcList"
                                value="{{item.value}}"
                                label="{{item.label}}"
                                disabled="{{item.disabled}}"
                            ></s-option>
                        </select>
                    </s-form-item>
                    <s-form-item prop="name" label="策略名称：">
                        <s-input width="{{320}}" value="{=formData.name=}" placeholder="请输入名称"></s-input>
                    </s-form-item>
                    <s-form-item prop="gold" label="金牌队列服务值：">
                        <template slot="label" class="label_class">
                            {{'金牌队列服务值：'}}
                            <s-tip
                                class="inline-tip"
                                content="{{'金牌队列适用于关键业务实时数据的传输业务，如在线交易、语音业务等'}}"
                                skin="question"
                                />
                        </template>
                        <s-input width="{{320}}" value="{=formData.gold=}" placeholder="请输入"></s-input>
                    </s-form-item>
                    <s-form-item prop="silver" label="银牌队列服务值：">
                        <template slot="label" class="label_class">
                            {{'银牌队列服务值：'}}
                            <s-tip
                                class="inline-tip"
                                content="{{'银牌队列适用于重要业务的数据产生业务，如游戏加速、视频直播'}}"
                                skin="question"
                                />
                        </template>
                        <s-input width="{{320}}" value="{=formData.silver=}" placeholder="请输入"></s-input>
                    </s-form-item>
                    <s-form-item prop="copper" label="铜牌队列服务值：">
                        <template slot="label" class="label_class">
                            {{'铜牌队列服务值：'}}
                            <s-tip
                                class="inline-tip"
                                content="{{'铜牌队列适用于普通业务的数据传输业务、如非实时数据，点播，存储'}}"
                                skin="question"
                                />
                        </template>
                        <s-input width="{{320}}" value="{=formData.copper=}" placeholder="请输入"></s-input>
                    </s-form-item>
                    <s-form-item prop="defaultQos" label="默认服务质量：">
                        <template slot="label" class="label_class">
                            {{'默认服务质量：'}}
                            <s-tip
                                class="inline-tip"
                                content="{{defaultQosTipContent}}"
                                skin="question"
                                />
                        </template>
                        <s-radio-radio-group
                            class="s-doc-radio"
                            datasource="{{defaultQos}}"
                            value="{=formData.defaultQos=}"
                            on-change="dcphyIdTypeChange($event)"
                            radioType="button"
                        >
                        </s-radio-radio-group>
                    </s-form-item>
                    <s-form-item prop="description" label="描述：">
                        <s-input-text-area
                            maxLength="200"
                            width="320"
                            height="60"
                            value="{=formData.description=}"
                            placeholder="请输入"
                        ></s-input-text-area>
                    </s-form-item>
                </div>
                <div class="content-box form-part-wrap inline-form">
                    <h4>{{'标签'}}</h4>
                    <tag-edit-panel
                        s-ref="tagPanel"
                        instances="{{defaultInstances}}"
                        options="{{tagListRequster}}"
                    />
                </div>
            </s-form>
            <div slot="pageFooter" class="buybucket">
                <div class="buybucket-container">
                <s-button
                    size="large"
                    disabled="{{confirmed}}"
                    skin="primary" on-click="onCreate">确定</s-button>
                <s-button size="large" on-click="cancel">取消</s-button>
                </div>
            </div>
        </s-app-create-page>
    </template>
`;
@template(tpl)
@invokeSUI
@invokeSUIBIZ
class QosCreate extends CreatePage {
    static components = {
        'tag-edit-panel': TagEditPanel,
        's-option': Select.Option
    };

    initData() {
        return {
            flag: FLAG,
            selectQosDisable: false,
            defaultInstances: [
                {
                    tags: [
                        {
                            tagKey: '默认项目',
                            tagValue: ''
                        }
                    ]
                }
            ],
            pageNav: {
                title: '创建策略',
                backUrl: '/network/#/vpc/qos/list',
                backLabel: '返回qos策略列表'
            },
            vpcList: [],
            tagListRequster: this.tagListRequster.bind(this),
            formData: {
                name: '',
                vpcId: '',
                description: '',
                gold: '',
                silver: '',
                copper: '',
                defaultQos: 'gold',
                description: ''
            },
            defaultQos: [
                {label: '金', value: 'gold'},
                {label: '银', value: 'silver'},
                {label: '铜', value: 'copper'}
            ],
            labelCol: {span: 3},
            wrapperCol: {span: 19},
            rules: {
                vpcId: {required: true, message: '请选择所在网络'},
                name: [
                    {required: true, message: '名称必填'},
                    {
                        pattern: /^[a-zA-Z][\w\-\_\/\.]{0,64}$/,
                        message: '支持大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65'
                    }
                ],
                gold: [this.validateServiceQueue()],
                silver: [this.validateServiceQueue()],
                copper: [this.validateServiceQueue()]
            },
            urlQuery: parseQuery(window.location.hash)
        };
    }

    static computed = {
        defaultQosTipContent() {
            const flag = this.data.get('flag');
            return `默认服务质量指的是用户没有填金牌/银牌/铜牌队列服务值时，策略默认走${flag.NetworkSupportXS ? '' : '百度'}智能云的队列服务值。`;
        }
    };

    attached() {
        this.getVpcList();
        this.isEdit();
    }

    vpcChange(e) {
        u.each(this.data.get('vpcList'), item => {
            if (e.value === item.value) {
                this.data.set('vpcInfo', item.vpcInfo);
            }
        });
    }

    cancel() {
        location.hash = '#/vpc/qos/list';
    }

    async isEdit() {
        const qosId = this.data.get('urlQuery.qosId');
        if (qosId) {
            this.data.merge('pageNav', {
                title: '编辑策略'
            });
            this.data.set('selectQosDisable', true);
            let data = await this.$http.getQosDetail(qosId, kXhrOptions.customSilent);
            this.data.set('qosDetail', data);
            this.data.set('formData.vpcId', data.vpcId);
            this.data.set('formData.name', data.name);
            this.data.set('formData.gold', data.gold.join(','));
            this.data.set('formData.silver', data.silver.join(','));
            this.data.set('formData.copper', data.copper.join(','));
            this.data.set('formData.defaultQos', data.defaultQos);
            this.data.set('formData.description', data.description);
            this.data.set('defaultInstances', [
                {
                    tags: data.tags
                }
            ]);
        }
    }

    getSearchCriteria() {
        const {pager} = this.data.get('');
        const searchParam = this.ref('search') ? this.ref('search').getSearchCriteria() : {};
        if (FLAG.NetworkSubnetSupportOrganization) {
            const organizationId = window.$framework.organization.getCurrentOrganization().id;
            const currentResourceGroupIds = window.$framework.organization.getCurrentResourceGroup().id;
            const resourceGroupIds = currentResourceGroupIds === 'all' ? [] : [currentResourceGroupIds];
            u.assign(searchParam, {
                organizationId,
                resourceGroupIds
            });
        }
        return u.extend({}, searchParam, order, {vpcId}, {pageNo: pager.page, pageSize: pager.size});
    }

    async getVpcList() {
        let data = await this.$http.vpcList();
        let list = _.map(data, item => {
            return {
                label: `${item.name}（${item.cidr}）`,
                value: item.shortId,
                vpcInfo: item
            };
        });
        if (this.data.get('urlQuery.qosId')) {
            this.data.set('vpcList', list);
        } else {
            this.getQosBindList().then(bindList => {
                list = _.map(list, item => {
                    if (_.includes(bindList, item.vpcInfo.vpcId)) {
                        return {
                            ...item,
                            disabled: true
                        };
                    } else return item;
                });
                this.data.set('vpcList', list);
                for (let i = 0; i < list.length; i++) {
                    if (!list[i].disabled) {
                        this.data.set('formData.vpcId', list[i].value);
                        break;
                    }
                }
            });
        }
    }

    async onCreate() {
        let tags = '';
        try {
            await this.ref('tagPanel').validate(false);
            await this.ref('form').validateFields();
        } catch (error) {
            return;
        }
        tags = await this.ref('tagPanel').getTags();
        this.data.set('confirmed', true);
        let formData = this.data.get('formData');
        let payload = {
            ...formData,
            gold: formData.gold.trim() ? formData.gold.split(',') : [],
            silver: formData.silver.trim() ? formData.silver.split(',') : [],
            copper: formData.copper.trim() ? formData.copper.split(',') : [],
            tags: tags
        };
        if (this.data.get('urlQuery.qosId')) {
            const qosId = this.data.get('qosDetail.qosUuid');
            this.$http
                .updateQos(qosId, payload)
                .then(res => {
                    location.hash = '#/vpc/qos/list';
                })
                .catch(err => {
                    this.data.set('confirmed', false);
                });
        } else {
            this.$http
                .createQos(payload)
                .then(res => {
                    location.hash = '#/vpc/qos/list';
                })
                .catch(err => {
                    this.data.set('confirmed', false);
                });
        }
    }

    validateServiceQueue() {
        return {
            validator: (rules, val, callback) => {
                let {gold, silver, copper} = this.data.get('formData');
                gold = gold.trim();
                silver = silver.trim();
                copper = copper.trim();
                let value = [gold, silver, copper].filter(item => item !== '').join(',');
                let serviceArr = value ? value.split(',') : [];
                if (_.includes(serviceArr, '')) {
                    return callback('可输入多个0-63的整数，以英文逗号分隔，支持离散或区间，如1,3,12-15');
                }
                serviceArr = _.filter(serviceArr, item => item !== '');
                let sectionMap = new Map();
                for (let i = 0; i < serviceArr.length; i++) {
                    let item = serviceArr[i];
                    if (item.indexOf('-') > -1) {
                        let min = Number(item.split('-')[0]);
                        let max = Number(item.split('-')[1]);
                        if (
                            !new RegExp(rule.qosQueueService).test(min) ||
                            !new RegExp(rule.qosQueueService).test(max)
                        ) {
                            return callback('可输入多个0-63的整数，以英文逗号分隔，支持离散或区间，如1,3,12-15');
                        }
                        if (max <= min) {
                            return callback('区间不合法');
                        }
                        let start = min;
                        while (start <= max) {
                            if (sectionMap.has(start)) {
                                return callback('数值范围重合');
                            }
                            sectionMap.set(start, start);
                            start++;
                        }
                    } else {
                        item = Number(item);
                        if (!new RegExp(rule.qosQueueService).test(item)) {
                            return callback('可输入多个0-63的整数，以英文逗号分隔，支持离散或区间，如1,3,12-15');
                        }
                        if (sectionMap.has(item)) {
                            return callback('数值范围重合');
                        }
                        sectionMap.set(item, item);
                    }
                }
                callback();
            }
        };
    }

    // 查询已关联qos的vpc
    getQosBindList() {
        return this.$http.getQosBind();
    }

    // 请求标签list
    tagListRequster() {
        return this.$http.getSearchTagList({serviceType: [], region: ['global']});
    }
    onRegionChange() {
        location.hash = '#/vpc/qos/list';
    }
}
export default San2React(Processor.autowireUnCheckCmpt(QosCreate));
