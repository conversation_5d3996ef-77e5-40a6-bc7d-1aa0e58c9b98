.vpc-qos-create {
    width: 100%;
    min-height: 100%;
    padding-bottom: 20px;
    background-color: #f7f7f9;
    .content-box {
        text-align: left;
        h4 {
            display: block;
            color: #333;
            font-size: 16px;
            margin: 8px 0;
            border-left: solid 4px #2468f2;
            padding-left: 14px;
        }
        .tip {
            font-size: 12px;
            margin-top: 10px;
            color: #999;
        }
        .tip-icon {
            color: #9e9898;
            border: 1px solid #9e9898;
            margin-left: 10px;
            box-sizing: content-box;
            &:hover {
                border-color: #2468f2;
                color: #2468f2;
            }
        }
    }
    .s-doc-radio {
        display: inline-block;
    }
    .s-radio-button-group {
        .s-radio-text {
            width: 72px;
        }
    }
    .form-part-wrap {
        border-radius: 6px;
        width: calc(~'100vw - 32px');
        background: #fff;
        margin: 16px;
        padding: 24px;
        h4 {
            display: inline-block;
            border: none;
            zoom: 1;
            font-size: 16px;
            color: #151b26;
            font-weight: 500;
            margin: 0;
            padding: 0;
        }
        .s-form-item-label {
            width: 131px;
            height: 30px;
            .inline-tip {
                top: 3px;
                position: relative;
                .s-tip-warning {
                    justify-content: center;
                    .warning_class {
                        fill: #999;
                    }
                }
                .s-tip:hover .s-icon path {
                    fill: #2468f2 !important;
                }
            }
        }
        .label_class {
            .inline-tip {
                top: 3px;
                position: relative;
                .s-tip-warning {
                    justify-content: center;
                    .warning_class {
                        fill: #999;
                    }
                }
                .s-tip:hover .s-icon path {
                    fill: #2468f2 !important;
                }
            }
        }
        .center_class {
            .s-row {
                .s-form-item-control-wrapper {
                    line-height: 30px;
                }
            }
        }
    }
    .inline-form {
        h4 {
            float: left;
        }
        .tag-edit-panel {
            margin-left: 140px;
        }
    }
    .buybucket {
        width: 100%;
        left: 0;
        height: 60px;
        .buybucket-container {
            width: auto;
            float: left;
            height: 48px;
            transform: translateY(12.5%);
            display: flex;
            align-items: center;
            .s-button {
                margin-left: 16px;
            }
        }
    }
}

.locale-en {
    .vpc-qos-create .form-part-wrap .s-form-item-label {
        width: 200px;
        height: 30px;
    }
    .vpc-qos-create .inline-form .tag-edit-panel {
        margin-left: 200px;
    }
}
