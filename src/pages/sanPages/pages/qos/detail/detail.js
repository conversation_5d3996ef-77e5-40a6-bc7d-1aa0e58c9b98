import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import _ from 'lodash';
import {OutlinedLeft, OutlinedPlus} from '@baidu/sui-icon';
import {parseQuery} from '@/utils';
import {toTime} from '@/pages/sanPages/utils/helper';
import './style.less';

const {asPage, invokeSUI, invokeSUIBIZ, invokeAppComp, template, invokeComp} = decorators;

const tpl = html`
    <template>
        <s-app-detail-page class="vpc-qos-detail">
            <div slot="pageTitle" class="instance-info">
                <div class="center_class">
                    <s-app-link to="/network/#/vpc/qos/list" class="page-title-nav"><icon-left />返回</s-app-link>
                    <span class="instance-name">{{detail.name || '-'}}</span>
                </div>
                <s-button on-click="goEdit" class="float_class">编辑</s-button>
            </div>
            <div class="qos-detail-wrap">
                <h4>基本信息</h4>
                <div class="basic-item-wrap">
                    <div class="basic-item-box">
                        <div class="basic-item-key">所在网络：</div>
                        <div class="basic-item-value">
                            <span class="text-hidden">{{detail.vpcName}}</span>
                        </div>
                    </div>
                    <div class="basic-item-box">
                        <div class="basic-item-key">策略名称：</div>
                        <div class="basic-item-value">
                            <span class="text-hidden">{{detail.name}}</span>
                        </div>
                    </div>
                    <div class="basic-item-box">
                        <div class="basic-item-key">策略ID：</div>
                        <div class="basic-item-value">
                            <span class="text-hidden">{{detail.shortId}}</span>
                        </div>
                    </div>
                    <div class="basic-item-box">
                        <div class="basic-item-key">金牌队列服务值：</div>
                        <div class="basic-item-value">
                            <span class="text-hidden">{{detail.gold}}</span>
                        </div>
                    </div>
                    <div class="basic-item-box">
                        <div class="basic-item-key">银牌队列服务值：</div>
                        <div class="basic-item-value">
                            <span class="text-hidden">{{detail.silver}}</span>
                        </div>
                    </div>
                    <div class="basic-item-box">
                        <div class="basic-item-key">铜牌队列服务值：</div>
                        <div class="basic-item-value">
                            <span class="text-hidden">{{detail.copper}}</span>
                        </div>
                    </div>
                    <div class="basic-item-box">
                        <div class="basic-item-key">默认服务质量：</div>
                        <div class="basic-item-value">
                            <span class="text-hidden">{{detail.defaultQos | serviceQuality}}</span>
                        </div>
                    </div>
                    <div class="basic-item-box">
                        <div class="basic-item-key">创建时间：</div>
                        <div class="basic-item-value">
                            <span class="text-hidden">{{detail.createTime | timeFormat}}</span>
                        </div>
                    </div>
                    <div class="basic-item-box">
                        <div class="basic-item-key">标签：</div>
                        <div class="basic-item-value">
                            <span s-if="!detail.tags || detail.tags.length < 1"> - </span>
                            <div s-else s-for="item,index in detail.tags">
                                <span s-if="index <= 1"> {{item.tagKey + ':' + item.tagValue}} </span>
                                <div s-if="detail.tags.length > 2 && index === 1">...</div>
                            </div>
                        </div>
                    </div>
                    <div class="basic-item-box">
                        <div class="basic-item-key">描述：</div>
                        <div class="basic-item-value">
                            <span class="text-hidden">{{detail.description || '-'}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </s-app-detail-page>
    </template>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class QosDetail extends Component {
    static components = {
        'icon-left': OutlinedLeft,
        'outlined-plus': OutlinedPlus
    };
    initData() {
        return {
            result: {},
            urlQuery: parseQuery(window.location.hash)
        };
    }

    static filters = {
        timeFormat(time) {
            return toTime(time);
        },
        serviceQuality(value) {
            if (value === 'gold') {
                return '金';
            } else if (value === 'silver') {
                return '银';
            } else if (value === 'copper') {
                return '铜';
            }
        }
    };

    attached() {
        this.getQosDetail();
    }

    getQosDetail() {
        const qosId = this.data.get('urlQuery.qosId');
        return this.$http.getQosDetail(qosId, {'x-silent-codes': ['NoSuchObject']}).then(res => {
            this.data.set('detail', res);
        });
    }

    onBack() {
        window.location.hash = '#/vpc/qos/list';
    }

    goEdit() {
        location.hash = '#/vpc/qos/edit?qosId=' + this.data.get('detail').qosUuid;
    }
    onRegionChange() {
        location.hash = '#/vpc/qos/list';
    }
}
export default San2React(Processor.autowireUnCheckCmpt(QosDetail));
