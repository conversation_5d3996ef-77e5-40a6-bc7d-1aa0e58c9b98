.vpc-qos-detail {
    min-height: 100%;
    width: 100%;
    .instance-info {
        // line-height: 50px;
        display: flex;
        align-items: center;
        width: 100%;
        justify-content: space-between;
        .instance-name {
            font-size: 16px;
            font-weight: 500;
            color: #151b26;
            padding: 0px 12px 0px 16px;
        }
        .center_class {
            display: flex;
            align-items: center;
            .page-title-nav {
                .s-icon {
                    font-size: 14px;
                    color: #84868c !important;
                }
            }
        }
    }
    .instance-not-found-class {
        height: 100%;
    }
    .s-detail-page-content {
        background: #f7f7f7;
        margin: 0;
        padding: 0 16px;
    }
    .qos-detail-wrap {
        padding: 24px;
        margin: 16px 0;
        border-radius: 6px;
        background: #fff;
        .s-biz-page-header {
            display: none;
        }
        h4 {
            margin-bottom: 16px;
            font-size: 16px;
            height: auto;
            color: #151b26;
            line-height: 24px;
            font-weight: 500;
            padding: 0;
            border-bottom: none;
            display: block;
        }
    }
    .basic-item-wrap {
        display: flex;
        flex-flow: row wrap;
        .basic-item-box {
            display: inline-block;
            width: 33%;
            margin-bottom: 16px;
            vertical-align: top;
            .basic-item-key {
                display: inline-block;
                vertical-align: top;
                color: #5e626a;
                margin-right: 16px;
                width: 96px;
            }
            .basic-item-value {
                display: inline-block;
                color: #151a26;
                max-width: 80%;
                word-break: break-all;
                position: relative;
            }
        }
    }
}
