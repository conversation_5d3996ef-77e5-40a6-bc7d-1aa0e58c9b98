import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import _ from 'lodash';
import {OutlinedPlus, OutlinedSetting} from '@baidu/sui-icon';

import Confirm from '@/pages/sanPages/components/confirm';
import {columns} from './tableField';
import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import IntroducePanel from '@/pages/sanPages/components/introducePanel/introducePanel';

const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, invokeComp} = decorators;

import './style.less';

const tpl = html`
    <div>
        <s-app-list-page class="qos-list-wrap">
            <div class="list-header-wrap" slot="pageTitle">
                <div class="qos-widget">
                    <span class="title">QoS策略</span>
                    <a
                        s-ref="introduce"
                        href="javascript:void(0)"
                        class="help-file function-introduce"
                        on-click="handleShowCard"
                        on-mouseenter="handleMouseEnter('introduce')"
                        on-mouseleave="handleMouseLeave('introduce')"
                        s-if="{{!flag.NetworkSupportXS}}"
                    >
                        <img class="s-icon" src="{{introduceIcon}}" />功能简介
                    </a>
                </div>
                <introduce-panel
                    isShow="{{show}}"
                    klass="{{'endpoint-peerconn-wrapper'}}"
                    title="{{introduceTitle}}"
                    description="{{description}}"
                    introduceOptions="{{introduceOptions}}"
                    on-toggle="handleToggle($event)"
                ></introduce-panel>
            </div>
            <div slot="bulk">
                <s-button skin="primary" on-click="onCreate">
                    <outlined-plus />
                    创建QoS策略
                </s-button>
            </div>
            <div slot="filter">
                <div class="qos-buttons-wrap">
                    <search-res
                        class="search-qos"
                        s-ref="search"
                        serviceType="QOS"
                        searchbox="{=searchbox=}"
                        on-search="onSearch"
                    ></search-res>
                    <s-table-column-toggle
                        class="custom-column-qos s-button icon-column left_class"
                        datasource="{{customColumn.datasource}}"
                        value="{=customColumn.value=}"
                        on-change="onCustomColumns"
                    ></s-table-column-toggle>
                </div>
            </div>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                on-sort="onSort"
            >
                <div slot="empty">
                    <s-empty on-click="onCreate"> </s-empty>
                </div>
                <div slot="c-vpcId">
                    <span class="truncated">
                        <a href="#/vpc/instance/detail?vpcId={{row.vpcId}}&from=qos">{{row.vpcName || '-'}}</a>
                    </span>
                    <br />
                    <span class="truncated">{{row.vpcId || '-'}}</span>
                </div>
                <div slot="c-id">
                    <a href="#/vpc/qos/detail?qosId={{row.qosUuid}}" class="truncated">{{row.name}}</a>
                    <br />
                    <span class="truncated">{{row.shortId}}</span>
                    <s-clip-board class="name-icon" text="{{row.shortId}}" />
                </div>
                <div slot="c-gold">
                    <span class="truncated">{{row.gold | serviceQueue}}</span>
                </div>
                <div slot="c-silver">
                    <span class="truncated">{{row.silver | serviceQueue}}</span>
                </div>
                <div slot="c-copper">
                    <span class="truncated">{{row.copper | serviceQueue}}</span>
                </div>
                <div slot="c-defaultQos">{{row.defaultQos | serviceQuality}}</div>
                <div slot="c-tags">
                    <span s-if="!row.tags || row.tags.length < 1"> - </span>
                    <div s-else s-for="item,index in row.tags">
                        <span s-if="index <= 1"> {{item.tagKey + ':' + item.tagValue}} </span>
                        <div s-if="row.tags.length > 2 && index === 1">...</div>
                    </div>
                </div>
                <div slot="c-description">
                    <span class="truncated" title="{{row.description}}">{{row.description}}</span>
                </div>
                <div slot="c-opt" class="opt-class">
                    <s-button skin="stringfy" on-click="edit(row)">编辑</s-button>
                    <s-button skin="stringfy" on-click="delete(row)">删除</s-button>
                </div>
            </s-table>
            <s-pagination
                s-if="{{pager.total}}"
                slot="pager"
                layout="{{'total, pageSize, pager, go'}}"
                pageSize="{{pager.pageSize}}"
                total="{{pager.total}}"
                page="{{pager.page}}"
                on-pagerChange="onPagerChange"
                on-pagerSizeChange="onPagerSizeChange"
            />
        </s-app-list-page>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@invokeComp('@search-res', '@search-tag')
class QosList extends Component {
    static components = {
        'outlined-plus': OutlinedPlus,
        'outlined-setting': OutlinedSetting,
        'introduce-panel': IntroducePanel
    };
    initData() {
        const allColumns = columns.slice();
        const customColumnDb = allColumns.map(item => ({
            text: item.label,
            value: item.name,
            disabled: item.name === 'id' || item.name === 'opt'
        }));
        return {
            searchbox: {
                keyword: '',
                placeholder: '请输入策略名称进行搜索',
                keywordType: ['name'],
                keywordTypes: [
                    {value: 'name', text: '策略名称'},
                    {value: 'shortId', text: '策略ID'},
                    {value: 'tag', text: '标签'}
                ]
            },
            customColumn: {
                value: ['id', 'vpcId', 'gold', 'silver', 'copper', 'tags', 'description', 'defaultQos', 'opt'],
                datasource: customColumnDb
            },
            table: {
                loading: false,
                allColumns,
                datasource: [],
                selectedItems: []
            },
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            show: true,
            introduceTitle: 'QoS策略简介',
            description:
                'QoS策略指一组用于管理和控制网络资源分配的策略和技术，旨在确保不同网络应用能够按照其重要性和性能需求获得合适的带宽、延迟、丢包率等服务质量指标。QoS策略通过对网络流量进行分类、调度和控制，实现对网络资源的有效管理和优化。',
            introduceIcon: INTRODUCE_ICON_COLOR,
            introduceEle: null
        };
    }

    static filters = {
        serviceQuality(value) {
            if (value === 'gold') {
                return '金';
            } else if (value === 'silver') {
                return '银';
            } else if (value === 'copper') {
                return '铜';
            }
        },
        serviceQueue(value) {
            return value.length ? value : '-';
        }
    };

    inited() {
        this.setTableColumns();
        this.loadPage();
    }

    attached() {
        this.data.set('introduceEle', this.ref('introduce'));
    }

    onSearch() {
        this.loadPage();
    }

    edit(item) {
        location.hash = '#/vpc/qos/create?qosId=' + item.qosUuid;
    }

    delete(item) {
        const dialog = new Confirm({
            data: {
                content: '确定删除策略吗？',
                title: '删除前确认'
            }
        });
        dialog.attach(document.body);
        dialog.on('confirm', () => {
            this.$http.deleteQos(item.qosUuid).then(() => this.loadPage());
        });
    }

    getPayload() {
        const searchParam = this.ref('search') ? this.ref('search').getSearchCriteria() : {};
        const {pager} = this.data.get('');
        let payload = {
            pageNo: pager.page,
            pageSize: pager.pageSize
        };
        return {...payload, ...searchParam};
    }

    loadPage() {
        this.data.set('table.loading', true);
        let payload = this.getPayload();
        this.$http.getQosList(payload).then(res => {
            this.data.set('table.datasource', res.result);
            this.data.set('pager.total', res.totalCount);
            this.data.set('table.loading', false);
        });
    }

    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }

    onPagerSizeChange(e) {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', e.value.pageSize);
        this.loadPage();
    }

    // 自定义表格列
    onCustomColumns({value}) {
        this.data.set('customColumn.value', value);
        this.setTableColumns(value);
    }

    // 设置表格列
    setTableColumns(customColumnNames) {
        customColumnNames = customColumnNames ? customColumnNames : this.data.get('customColumn.value');
        const allColumns = this.data.get('table.allColumns');
        let columns = [];
        allColumns.forEach(item => customColumnNames.indexOf(item.name) > -1 && columns.push(item));
        this.data.set('table.columns', columns);
    }

    onCreate() {
        location.hash = '#/vpc/qos/create';
    }

    onDetail() {
        location.hash = '#/vpc/qos/detail';
    }

    onRegionChange() {
        location.reload();
    }
    handleToggle(flag) {
        this.data.set('show', flag);
        this.data.set('introduceIcon', INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#151B26';
    }
    handleShowCard() {
        if (!this.data.get('show')) {
            this.data.set('show', true);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type) {
        this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#2468F2';
    }
    handleMouseLeave(type) {
        const isShow = this.data.get('show');
        this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        !isShow && (ele.style.color = '#151B26');
    }
}
export default San2React(Processor.autowireUnCheckCmpt(QosList));
