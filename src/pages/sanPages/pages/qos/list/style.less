.qos-list-wrap {
    flex: 1;
    .title {
        display: inline-block;
        color: #151b26;
        margin: 0 10px 0 16px;
        height: 47px;
        line-height: 47px;
        font-weight: 500;
        font-size: 16px;
    }
    .search_wrap {
        display: inline-flex;
    }
    .s-cascader {
        margin-right: 8px;
        .s-cascader-value-arrow {
            top: 25%;
        }
    }
    .custom-column-qos {
        padding: 0 7px !important;
        .s-icon-button {
            border: none;
            padding: 0px;
            margin: 0px;
        }
    }
    .list-header-wrap {
        .qos-widget {
            display: flex;
            align-items: center;
            background: #fff;
            justify-content: space-between;
            .help-file {
                margin-right: 16px;
                font-size: 12px;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                color: #151b26;
                line-height: 20px;
                &:hover {
                    color: #2468f2;
                }
                .s-icon {
                    position: relative;
                    top: -1px;
                    right: 4px;
                    margin-right: 0;
                    color: #2468f2;
                    font-size: 14px;
                }
            }
            .function-introduce {
                color: #2468f2;
            }
        }
    }
    .qos-buttons-wrap {
        display: inline-flex;
        align-items: center;
        .search-res {
            display: inline-flex;
        }
    }
    .s-table .s-table-body {
        max-height: calc(~'100vh - 332px');
    }
    .opt-class {
        .s-button {
            padding: 0;
            margin-right: 12px;
        }
    }
    .search-qos {
        .search-res {
            margin: 0px;
            display: inline-flex;
            .s-cascader {
                margin: 0 8px 0 0;
            }
        }
    }
}
