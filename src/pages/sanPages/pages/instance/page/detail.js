/**
 * VPC实例详情页
 * @file: network/instance/pages/Detail.js
 * @author: <EMAIL>
 */
import u from 'lodash';
import moment from 'moment';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {Notification, Input} from '@baidu/sui';
import {OutlinedEditingSquare, OutlinedExclamation, OutlinedLeft, OutlinedQuestion} from '@baidu/sui-icon';

import rules from '../rule';
import DeleteCheckConfirm from '@/pages/sanPages/components/deleteCheck';
import {ContextService} from '@/pages/sanPages/common';
import {checkRule, ResourceType, ResourceValue, getUrl} from './helper';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import {parseQuery} from '@/utils';
import testID from '@/testId';
import '../style/detail.less';

const dnsTip = `域名服务器默认显示为空，表示使用${FLAG.NetworkSupportXS ? '智能云' : ContextService.ProjectName}默认DNS服务器。如果不使用默认DNS，将无法默认解析${FLAG.NetworkSupportXS ? '智能云' : ContextService.ProjectName}内部服务，比如：NAT、VPN、云数据库 Redis、YUM等。`; // eslint-disable-line
const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, invokeComp} = decorators;
const tpl = html`
<div>
    <s-app-detail-page class="{{klass}} {{instanceFound ? '' : 'no-instance-content'}}">
        <div slot="pageTitle" class="title_class">
            <div class="tltle_left">
                <s-app-link to="{{backUrl}}" data-test-id="${testID.vpc.detailBack}" class="page-title-nav"><icon-left/>返回</s-app-link>
                <span class="vpc-name">{{instance.name || '-'}}</span>
                <span class="status normal" s-if="instance.shortId"></span>
            </div>
            <s-popover trigger="{{deleteVpc.disable ? 'hover' : ''}}" class="float_right">
                <div slot="content">
                    {{deleteVpc.message}}
                </div>
                <s-button on-click="onDelete" disabled= "{{deleteVpc.disable}}">删除</s-button>
            </s-popover>
        </div>
        <div class="content">
            <h4>基本信息</h4>
            <div class="cell detail-part-item">
                <div class="cell-title">名称：</div>
                <div class="cell-content" data-testid="${testID.vpc.detailName}">{{instance.name || '-'}}</div>
                <s-popover data-test-id="${testID.vpc.detailNameEdit}" s-ref="popover-name" placement="right" trigger="click"
                    class="edit-popover-class">
                    <div class="edit-wrap" slot="content">
                        <s-input value="{=edit.name.value=}"
                            width="320"
                            data-test-id="${testID.vpc.detailNameEditInput}"
                            placeholder="{{'请输入名称'}}"
                            on-input="onEditInput($event, 'name')"/>
                        <div class="edit-tip">大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65</div>
                        <s-button skin="primary" data-test-id="${testID.vpc.detailNameEditSub}" s-ref="editBtn-name" disabled="{{true}}"
                            on-click="editConfirm(instance, 'name')">确定</s-button>
                        <s-button on-click="editCancel('name')">取消</s-button>
                    </div>
                    <outlined-editing-square s-if="instance.vpcId && instance.shortId"
                        class="name-icon" on-click="onInstantEdit(instance, 'name')" />
                </s-popover>
            </div>
            <div class="cell detail-part-item">
                <div class="cell-title">ID：</div>
                <div class="cell-content">
                    {{instance.shortId || '-'}}
                </div>
                <s-clip-board class="name-icon" text="{{instance.shortId}}" successMessage="已复制到剪贴板" />
            </div>
            <div class="cell detail-part-item">
                <div class="cell-title">域名：
                    <s-tip
                        s-if="!FLAG.NetworkSupportXS"
                        skin="question"
                        class="tip-margin"
                        placement="right">
                        <outlined-question class="warning_class"></outlined-question>
                        <!--bca-disable-next-line-->
                        <span slot="content" s-html="domainTip"></span>
                    </s-tip>
                </div>
                <div class="cell-content inline_class">
                    <span class="">{{instance.domainName || '-'}}</span>
                    <s-popover s-ref="popover-domainName" placement="right" trigger="click"
                        class="edit-popover-class">
                        <div class="edit-wrap" slot="content">
                            <s-input value="{=edit.domainName.value=}"
                                width="160"
                                on-input="onEditInput($event, 'domainName')"/>
                            <div class="edit-tip">最多支持5个域名，请用英文半角逗号分隔</div>
                            <s-button skin="primary" s-ref="editBtn-domainName" disabled="{{false}}"
                                on-click="changeServer(instance, 'domainName')">确定</s-button>
                            <s-button on-click="editCancel('domainName')">取消</s-button>
                        </div>
                        <outlined-editing-square s-if="instance.vpcId && instance.shortId"
                            class="name-icon" on-click="onInstantEdit(instance, 'domainName')" />
                    </s-popover>
                </div>
            </div>
            <div class="cell detail-part-item">
                <div class="cell-title">网段：</div>
                <div class="cell-content">
                    <p>{{instance.cidr || '-'}}（主）</p>
                    <p s-for="item in instance.auxiliaryCidr">{{item || '-'}}（辅）</p>
                </div>
            </div>
            <div class="cell detail-part-item">
                <div class="cell-title">创建时间：</div>
                <div class="cell-content">
                    <p>{{instance | getCreateTime}}</p>
                </div>
            </div>
            <div class="cell detail-part-item">
                <div class="cell-title">域名服务器：
                    <s-tip
                        s-if="!FLAG.NetworkSupportXS"
                        skin="question"
                        class="tip-margin"
                        placement="right"
                        content="{{dnsTip}}"/>
                </div>
                <div class="cell-content inline_class">
                    <span class="">{{instance.dnsServer || '-'}}</span>
                    <s-popover
                        s-ref="popover-dnsServer" placement="right" trigger="click"
                        class="edit-popover-class">
                        <div class="edit-wrap" slot="content">
                            <s-input value="{=edit.dnsServer.value=}"
                                width="160"
                                on-input="onEditInput($event, 'dnsServer')"/>
                            <div class="edit-tip">最多支持4个IP，用英文半角逗号分隔</div>
                            <s-button skin="primary" s-ref="editBtn-dnsServer" disabled="{{false}}"
                                on-click="changeServer(instance, 'dnsServer')">确定</s-button>
                            <s-button on-click="editCancel('dnsServer')">取消</s-button>
                        </div>
                        <outlined-editing-square s-if="instance.vpcId && instance.shortId"
                            class="name-icon" on-click="onInstantEdit(instance, 'dnsServer')" />
                    </s-popover>
                </div>
            </div>
            <div class="cell detail-part-item">
                <div class="cell-title">描述：</div>
                <div class="cell-content">
                    {{instance.description || '-'}}
                </div>
                <s-popover s-ref="popover-description" placement="right" trigger="click"
                    class="edit-popover-class">
                    <div class="edit-wrap" slot="content">
                        <s-textarea
                            value="{=edit.description.value=}"
                            width="200"
                            height="48"
                            placeholder="请输入"
                            on-input="onEditInput($event, 'description')"/>
                        <div class="edit-tip">描述不能超过200个字符</div>
                        <s-button skin="primary" s-ref="editBtn-description" disabled="{{true}}"
                            on-click="editConfirm(instance, 'description')">确定</s-button>
                        <s-button on-click="editCancel('description')">取消</s-button>
                    </div>
                    <outlined-editing-square s-if="instance.vpcId && instance.shortId"
                        class="name-icon" on-click="onInstantEdit(instance, 'description')" />
                </s-popover>
            </div>
        </div>
        <div class="content content_bottom">
            <h4>包含资源</h4>
            <div class="main" s-for="item in detailType" data-testid="${testID.vpc.detailResourceLink}">
                <h5>{{item.title}}</h5>
                <div class="list-wrap">
                    <a s-for="list in detailNum[item.value]" href="{{list.url}}">
                        <span>{{list.label}}</span>
                        <span>{{list.num}}</span>
                    </a>
                </div>
            </div>
        </div>
    </s-app-detail-page>
</template>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class VpcDetail extends Component {
    static components = {
        'outlined-editing-square': OutlinedEditingSquare,
        's-warning': OutlinedExclamation,
        'icon-left': OutlinedLeft,
        'outlined-question': OutlinedQuestion,
        's-textarea': Input.TextArea
    };
    static filters = {
        getCreateTime(item) {
            return item.defaultVpc ? '系统默认创建' : moment(item.createTime).format('YYYY-MM-DD HH:mm:ss');
        }
    };
    initData() {
        return {
            FLAG,
            klass: ['vpc-instance-detail'],
            instance: {},
            detailNum: {},
            detailType: [
                {title: '基础云资源', value: 'base'},
                {title: '网络资源', value: 'net'}
            ],
            deleteVpc: {
                disable: true,
                message: '数据加载中'
            },
            edit: {},
            dnsTip,
            domainTip: `默认为$region.baidu.internal，表示在该地域主机的默认域名后缀。若自定义为其他域名，将会通过DHCP返回，并通过search list补全对非$region.baidu.internal后缀的主机名查询。自定义主机名记录可通过<a href="https://console.bce.baidu.com/dns/#/dns/zone/list" target="_blank">智能云解析-内网DNS服务设置</a>`,
            sourceList: [],
            urlQuery: parseQuery(location.hash),
            instanceFound: true,
            loading: true,
            backUrl: '/network/#/vpc/instance/list'
        };
    }

    inited() {
        if (
            this.data.get('urlQuery.region') &&
            window.$context.getEnum('AllRegion')[this.data.get('urlQuery.region')] &&
            this.data.get('urlQuery.region') !== window.$context.getCurrentRegionId()
        ) {
            window.$context.setRegion(this.data.get('urlQuery.region'));
        }
        this.initDetailNum();
    }

    attached() {
        this.getWhiteList();
    }

    initDetailNum(data) {
        let resourceType = u.cloneDeep(ResourceType);
        let services = window.$context.getAvailableService();
        let bciRegionList = Object.keys(window.$context.SERVICE_TYPE['BCI'].region);
        let regionEnable = bciRegionList.indexOf(window.$context.getCurrentRegionId()) > -1;
        let availableServiceArray = services && services.split(',');
        const enableBBC = u.indexOf(availableServiceArray, 'BBC') > -1;
        const enableDCC = u.indexOf(availableServiceArray, 'DCC') > -1;
        const enableBCI = u.indexOf(availableServiceArray, 'BCI') > -1;
        if (enableDCC) {
            resourceType.base.push('dcc');
        }
        if (enableBBC) {
            resourceType.base.push('bbc');
        }
        if (enableBCI && regionEnable) {
            resourceType.base.push('bci');
        }
        const vpcId = this.data.get('urlQuery.vpcId');
        const from = this.data.get('urlQuery.from');
        const detail = {
            base: [],
            net: []
        };
        Object.keys(detail).map(key => {
            resourceType[key].map(item => {
                const url = data
                    ? getUrl(item, data, vpcId)
                    : `#/vpc/instance/detail?vpcId=${this.data.get('urlQuery.vpcId')}`;
                if (ResourceValue[item].inWhiteList) {
                    let label = ResourceValue[item].label;
                    if (!(FLAG.NetworkSupportXS && (label === 'BBC' || label === 'BCI'))) {
                        detail[key].push({
                            label,
                            url,
                            num: data ? data[`${item}Num`] : 0
                        });
                    }
                }
            });
        });
        this.data.set('detailNum', detail);
        if (from) {
            this.data.set('backUrl', `/network/#/vpc/${from}/list`);
        }
    }

    getDetail(option = {}) {
        const vpcId = this.data.get('urlQuery.vpcId');
        return this.$http.getVpcDetailInfo(vpcId, option).then(data => {
            this.data.set('instance', data);
            this.initDetailNum(data);
            let {deleteVpc} = checker.check(rules, [data], 'deleteVpc');
            this.data.set('deleteVpc', deleteVpc);
        });
    }

    // 编辑弹框-输入名称/描述
    onEditInput({value}, type) {
        let result = checkRule(type, value);
        this.data.set(`edit.${type}.error`, !result);
        this.data.set(`edit.${type}.value`, value);
        this.ref(`editBtn-${type}`).data.set('disabled', !result);
    }
    getWhiteList() {
        this.getDetail({'x-silent-codes': ['InstanceNotFound']})
            .catch(e => {
                if (e?.result?.vpcId) {
                    this.data.set('instance', e.result);
                    this.initDetailNum(e.result);
                    let {deleteVpc} = checker.check(rules, [e.result], 'deleteVpc');
                    this.data.set('deleteVpc', deleteVpc);
                }
            })
            .finally(e => this.data.set('loading', false));
    }
    ipV6GatewayWhiteList() {
        ResourceValue.ipv6gw.inWhiteList = true;
    }
    // 编辑弹框-提交
    editConfirm(instance, type) {
        let edit = this.data.get(`edit.${type}`);
        if (edit.error) {
            return;
        }
        const key = type === 'name' ? 'description' : 'name';
        this.$http
            .vpcUpdate({
                id: instance.vpcId,
                [type]: edit.value,
                [key]: instance[`${key}`]
            })
            .then(() => {
                this.editCancel(type);
                Notification.success('修改成功');
                this.getDetail();
            });
    }

    // 编辑弹框-取消
    editCancel(type) {
        const extraType = ['domainName', 'dnsServer'];
        const disableBtn = extraType.indexOf(type) === -1;
        this.ref(`editBtn-${type}`).data.set('disabled', disableBtn);
        this.ref(`popover-${type}`).data.set('visible', false);
    }
    // 点击修改icon
    onInstantEdit(row, type) {
        this.data.set(`edit.${type}.value`, row[`${type}`]);
        this.data.set(`edit.${type}.error`, false);
        const typePop = this.ref(`popover-${type}`);
        typePop.data.set('visible', !typePop.data.get('visible'));
    }
    // 删除实例
    async onDelete() {
        let vpcInfo = this.data.get('instance');
        await this.checkSource(vpcInfo);
        let confirm = new DeleteCheckConfirm({
            data: {
                open: true,
                sourceList: this.data.get('sourceList'),
                type: 'vpc'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http
                .vpcDelete({
                    vpcIds: [this.data.get('instance.vpcId')]
                })
                .then(() => {
                    Notification.success('删除成功');
                    window.location.href = '#/vpc/instance/list';
                });
        });
    }
    changeServer(instance, type) {
        const {value} = this.data.get(`edit.${type}`);
        let request = type === 'dnsServer' ? this.updateDns.bind(this) : this.updateDomain.bind(this);
        request(value, instance)
            .then(() => {
                Notification.success('修改成功');
                this.editCancel(type);
                this.getDetail();
            })
            .catch(() => {
                Notification.error('修改失败');
            });
    }

    updateDns(value, instance) {
        const vpcId = this.data.get('urlQuery.vpcId');
        const payload = {
            dnsServer: value
        };
        // 为null调用创建接口,
        if (instance.dnsServer === null) {
            return this.$http.createDnsServer(vpcId, payload);
        } else if (value === '') {
            return this.$http.deleteDnsServer(vpcId);
        }
        return this.$http.updateDnsServer(vpcId, payload);
    }

    updateDomain(value, instance) {
        const vpcId = this.data.get('urlQuery.vpcId');
        const payload = {
            domainName: value
        };
        // 为null调用创建接口,
        if (instance.domainName === null) {
            return this.$http.createDomain(vpcId, payload);
        } else if (value === '') {
            return this.$http.deleteDomain(vpcId);
        }
        return this.$http.updateDomain(vpcId, payload);
    }
    onRegionChange() {
        location.hash = '#/vpc/instance/list';
    }

    checkSource(item) {
        let vpcId = item.vpcId;
        let payload = {
            vpcIds: [vpcId]
        };
        return this.$http
            .checkVpcBeforeDelete(payload)
            .then(res => {
                let sourceList = [];
                if (Object.keys(res.resourceIpCheck[vpcId]).length > 0) {
                    let arr = Object.keys(res.resourceIpCheck[vpcId]).map(i => {
                        return {
                            sourceName: i,
                            sourceNum: res.resourceIpCheck[vpcId][i]
                        };
                    });
                    sourceList.push({
                        name: item.name,
                        source: arr,
                        vpcId
                    });
                }
                this.data.set('sourceList', sourceList);
            })
            .catch(err => {
                this.data.set('sourceList', []);
            });
    }
    onRefresh() {
        this.data.set('instanceFound', true);
        this.getWhiteList();
    }
    onBack() {
        this.onRegionChange();
    }
}
export default San2React(Processor.autowireUnCheckCmpt(VpcDetail));
