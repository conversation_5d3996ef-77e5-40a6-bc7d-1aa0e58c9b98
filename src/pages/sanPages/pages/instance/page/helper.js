import u from 'lodash';
import rule from '@/pages/sanPages/utils/rule';
import {disableCidr} from '@/pages/sanPages/utils/constants';
import {checkIsInSubnet} from '@/pages/sanPages/utils/common';

const {DomainRule, IP} = rule;

const checkNameRule = value => {
    return value !== '' && /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/.test(value);
};

const checkDesRule = value => {
    return value.length <= 200;
};

const checkDnsServerRule = value => {
    const dnsServerList = value.split(',');
    if (!value) {
        return true;
    }
    if (!dnsServerList.length || dnsServerList.length > 4) {
        return false;
    }
    if (u.some(dnsServerList, item => !IP.test(item))) {
        return false;
    }
    return true;
};

const checkDomainRule = value => {
    if (!value) {
        return true;
    }
    const domainList = value.split(',');
    if (!domainList.length || domainList.length > 5) {
        return false;
    }
    if (u.some(domainList, item => !DomainRule.test(item) || item.length > 200)) {
        return false;
    }
    return true;
};

export const checkRule = (type, value) => {
    const RuleMap = {
        name: checkNameRule,
        description: checkDesRule,
        domainName: checkDomainRule,
        dnsServer: checkDnsServerRule
    };
    const currentRule = RuleMap[type];
    return currentRule(value);
};

export const ResourceValue = {
    bcc: {
        inWhiteList: true,
        label: 'BCC',
        url: '/bcc/#/bcc/instance/list'
    },
    dcc: {
        inWhiteList: true,
        label: 'DCC',
        url: '/bcc/#/dcc/instance/list'
    },
    bbc: {
        inWhiteList: true,
        label: 'BBC',
        url: '/bbc/#/bbc/instance/list'
    },
    bci: {
        inWhiteList: true,
        label: 'BCI',
        url: '/bci/#/bci/pod/list'
    },
    subnet: {
        inWhiteList: true,
        label: '子网',
        url: '/network/#/vpc/subnet/list'
    },
    acl: {
        inWhiteList: true,
        label: 'ACL',
        url: '/network/#/vpc/acl/manage'
    },
    routeTable: {
        inWhiteList: true,
        label: '路由表',
        url: '/network/#/vpc/route/detail'
    },
    ipv6gw: {
        inWhiteList: false,
        label: 'IPv6网关',
        url: '/network/#/vpc/ipv6gw/list'
    },
    enic: {
        inWhiteList: true,
        label: '弹性网卡',
        url: '/network/#/vpc/eni/list'
    },
    snic: {
        inWhiteList: true,
        label: '服务网卡',
        url: '/network/#/vpc/endpoint/list'
    },
    securityGroup: {
        inWhiteList: true,
        label: '安全组',
        url: '/network/#/vpc/security/list'
    },
    vpn: {
        inWhiteList: true,
        label: 'VPN网关',
        url: '/network/#/vpc/vpn/list'
    },
    nat: {
        inWhiteList: true,
        label: 'NAT网关',
        url: '/network/#/vpc/nat/list'
    },
    peerconn: {
        inWhiteList: true,
        label: '对等连接',
        url: '/network/#/vpc/peerconn/list'
    },
    dc: {
        inWhiteList: true,
        label: '专线网关',
        url: '/network/#/vpc/dcgw/list'
    }
};

export const getUrl = (type, instance, vpcId) => {
    const {name: vpcName, shortId} = instance;
    const urlMap = {
        bcc: `${ResourceValue[type].url}?action=false&keyword=${shortId}&keywordType=vpcId`,
        bbc: `${ResourceValue[type].url}?keyword=${vpcName}&keywordType=vpcName`,
        dcc: `${ResourceValue[type].url}?keyword=${vpcName}&keywordType=vpcName`,
        other: `${ResourceValue[type].url}?vpcId=${vpcId}`,
        dc: `${ResourceValue[type].url}?vpcId=${vpcId}`,
        bci: ResourceValue[type].url
    };
    return urlMap[type] || urlMap['other']; //eslint-disable-line
};

export const ResourceType = {
    base: ['bcc'],
    net: ['subnet', 'routeTable', 'enic', 'snic', 'vpn', 'securityGroup', 'acl', 'nat', 'ipv6gw', 'peerconn', 'dc']
};

// 检查输入的cidr是否在禁用网段内
export const checkCidrInDisable = str => {
    let res = false;
    for (let i = 0; i < disableCidr.length; i++) {
        res = checkIsInSubnet(str, disableCidr[i]) || checkIsInSubnet(disableCidr[i], str);
        if (res) {
            break;
        }
    }
    return res;
};
