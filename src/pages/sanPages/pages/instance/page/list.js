/*
 * @description: VPC实例列表页
 * @file: network/instance/pages/List.js
 * @author: <PERSON><PERSON><PERSON><PERSON>@baidu.com
 */
import u from 'lodash';
import moment from 'moment';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {Notification, Input} from '@baidu/sui';
import {OutlinedRefresh, OutlinedDownload, OutlinedPlus, OutlinedEditingSquare} from '@baidu/sui-icon';
import {checker} from '@baiducloud/bce-opt-checker';
import {ResourceGroupDialog} from '@baidu/bce-resourcegroup-sdk-san';
import {ResourceGroupSDK} from '@baidu/bce-resourcegroup-sdk';

import {disable_vpc_10cidr as disableVpc10Cidr} from '@/pages/sanPages/common';
import {DocService} from '@/pages/sanPages/common';
import {urlSerialize} from '@/pages/sanPages/utils/helper';
import rules from '../rule';
import CidrDialog from './auxiliaryCidr';
import DeleteCheckConfirm from '@/pages/sanPages/components/deleteCheck';
import Confirm from '@/pages/sanPages/components/confirm';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import testID from '@/testId';
import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import IntroducePanel from '@/pages/sanPages/components/introducePanel/introducePanel';
import {resetNewRegion} from '@/utils/helper';
import '../style/list.less';

const {invokeComp, invokeSUI, invokeSUIBIZ, invokeAppComp, template, service} = decorators;
const tpl = html` <template>
    <s-biz-page class="{{klass}}">
        <div slot="header" class="instance-title">
            <div class="instance-title-widget">
                <span>{{title}}</span>
                <div>
                    <a
                        href="{{DocService.vpc_helpFile}}"
                        target="_blank"
                        class="help-file"
                        s-if="{{!FLAG.NetworkSupportXS}}"
                    >
                        <s-icon name="warning-new" />帮助文档
                    </a>
                    <a
                        s-ref="introduce"
                        href="javascript:void(0)"
                        class="help-file function-introduce"
                        on-click="handleShowCard"
                        on-mouseenter="handleMouseEnter('introduce')"
                        on-mouseleave="handleMouseLeave('introduce')"
                        s-if="{{!FLAG.NetworkSupportXS}}"
                    >
                        <img class="s-icon" src="{{introduceIcon}}" />功能简介
                    </a>
                </div>
            </div>
            <introduce-panel
                isShow="{{show}}"
                klass="{{'endpoint-peerconn-wrapper'}}"
                title="{{introduceTitle}}"
                description="{{description}}"
                introduceOptions="{{introduceOptions}}"
                on-toggle="handleToggle($event)"
            ></introduce-panel>
        </div>
        <div class="list-page-tb-left-toolbar" slot="tb-left">
            <s-tooltip
                trigger="{{createVpc.disable || vpcSinDisable.disable || iamPass.disable ? 'hover' : ''}}"
                placement="top"
            >
                <!--bca-disable-next-line-->
                <div slot="content">{{vpcSinDisable.message || iamPass.message || createVpc.message | raw}}</div>
                <s-button
                    disabled="{{vpcSinDisable.disable || iamPass.disable || createVpc.disable}}"
                    skin="primary"
                    on-click="onCreate"
                    track-id="ti_vpc_instance_create"
                    data-test-id="${testID.vpc.listCreateBtn}"
                    track-name="创建VPC"
                    ><outlined-plus />创建VPC
                </s-button>
            </s-tooltip>
            <s-tooltip trigger="{{deleteVpc.disable ? 'hover' : ''}}" placement="top" class="left_class">
                <!--bca-disable-next-line-->
                <div slot="content">{{deleteVpc.message | raw}}</div>
                <s-button
                    on-click="onDelete"
                    disabled="{{deleteVpc.disable || checkSourceLoading}}"
                    data-test-id="${testID.vpc.listDeleteBtn}"
                    track-id="ti_vpc_instance_delete"
                    track-name="删除"
                    >删除</s-button
                >
            </s-tooltip>
            <edit-tag selectedItems="{{selectedItems}}" on-success="refresh" class="left_class" type="VPC"></edit-tag>
        </div>
        <div slot="tb-right" class="toolbar_right">
            <search-tag s-ref="search" serviceType="VPC" searchbox="{=searchbox=}" on-search="onSearch"></search-tag>
            <s-button on-click="refresh" class="s-icon-button" track-id="ti_vpc_instance_refresh" track-name="刷新"
                ><outlined-refresh class="icon-class"
            /></s-button>
            <s-button on-click="onDownload" class="s-icon-button" track-id="ti_blb_instance_download" track-name="下载"
                ><outlined-download class="icon-class"
            /></s-button>
            <custom-column
                class="left_class"
                columnList="{{customColumn.datasource}}"
                initValue="{{customColumn.value}}"
                type="vpc"
                on-init="initColumns"
                on-change="onCustomColumns"
            >
            </custom-column>
        </div>
        <s-table
            columns="{{table.columns}}"
            loading="{{table.loading}}"
            error="{{table.error}}"
            datasource="{{table.datasource}}"
            on-selected-change="tableSelected($event)"
            on-filter="onFilter"
            on-sort="onSort"
            selection="{=table.selection=}"
            track-id="ti_vpc_instance_table"
            data-test-id="${testID.vpc.listTable}"
            track-name="列表操作"
        >
            <div slot="empty">
                <s-empty class="{{iamPass.disable ? 'create-disable' : ''}}" on-click="onCreate" />
            </div>
            <div slot="error">
                <!--bca-disable-next-line-->
                {{table.error ? table.error : '啊呀，出错了？' | raw}}
                <a href="javascript:;" on-click="refresh">重新加载</a>
            </div>
            <div slot="h-routelayer" class="router_layer">
                <span>路由中继</span>
                <s-tip
                    class="inline-tip"
                    content="{{'开启路由中继功能后，路由表可以转发非源自本VPC的流量。该选项默认关闭，即路由表默认仅会转发源自本VPC的流量。'}}"
                    skin="question"
                />
            </div>
            <div slot="c-id">
                <a
                    href="#/vpc/instance/detail?vpcId={{row.vpcId}}"
                    class="truncated"
                    title="{{row.name}}"
                    track-id="ti_vpc_instance_detail"
                    data-testid="${testID.vpc.listName}{{rowIndex}}"
                    track-name="详情"
                    >{{row.name}}</a
                >
                <s-popover s-ref="popover-name-{{rowIndex}}" placement="top" trigger="click" class="edit-popover-class">
                    <div class="edit-wrap" slot="content">
                        <s-input
                            value="{=edit.name.value=}"
                            width="320"
                            placeholder="请输入名称"
                            on-input="onEditInput($event, rowIndex, 'name')"
                        />
                        <div class="edit-tip">
                            大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65
                        </div>
                        <s-button
                            skin="primary"
                            s-ref="editBtn-name-{{rowIndex}}"
                            disabled="{{true}}"
                            on-click="editConfirm(row, rowIndex, 'name')"
                            >确定</s-button
                        >
                        <s-button on-click="editCancel(rowIndex, 'name')">取消</s-button>
                    </div>
                    <outlined-editing-square
                        s-if="row.vpcId && row.shortId"
                        class="name-icon"
                        on-click="onInstantEdit(row, rowIndex, 'name')"
                    />
                </s-popover>
                <br />
                <span class="truncated" data-testid="${testID.vpc.listId}{{rowIndex}}" title="{{row.shortId}}"
                    >{{row.shortId}}</span
                >
                <s-clip-board class="name-icon" text="{{row.shortId}}" />
            </div>
            <!--bca-disable-next-line-->
            <div slot="c-resourceGroups">{{row | resourceText | raw}}</div>
            <div slot="c-status">
                <span s-if="row.status === 0" class="status normal">可用</span>
                <span s-else-if="row.status === 2" class="status warning">构建中</span>
                <span s-else class="status error">不可用</span>
            </div>
            <!--bca-disable-next-line-->
            <div slot="c-cidr">{{row | getCidr | raw}}</div>
            <div slot="c-ipv6Cidr">{{row.ipv6Cidr || '-'}}</div>
            <div slot="c-routelayer">
                <s-switch
                    disabled="{{row.disableFlag}}"
                    checked="{=row.relay=}"
                    on-change="routeRelayChange($event, row, rowIndex)"
                >
                </s-switch>
            </div>
            <div slot="c-createTime">{{row | getCreateTime}}</div>
            <!--bca-disable-next-line-->
            <div slot="c-tag">{{row.tags | getTag | raw}}</div>
            <div slot="c-csnInfo">
                <span s-if="row.csnId === ''">尚未加入云智能网</span>
                <a s-else-if="isCsnCross(row)" href="/csn#/csn/cross/grantedRule">跨账号：{{row.csnAccountId}}</a>
                <a s-else href="/csn#/csn/detail?csnId={{row.csnId}}&current=detail">{{row.csnId}}</a>
            </div>
            <div slot="c-resource">
                <div s-if="row.resourceGroups && row.resourceGroups.length">
                    <p s-for="item in row.resourceGroups">{{item.name}}</p>
                </div>
                <span s-else>-</span>
            </div>
            <div slot="c-description">
                <span class="truncated" title="{{row.description}}">{{row.description}}</span>
                <s-popover
                    s-ref="popover-description-{{rowIndex}}"
                    placement="top"
                    trigger="click"
                    class="edit-popover-class"
                >
                    <div class="edit-wrap" slot="content">
                        <s-textarea
                            value="{=edit.description.value=}"
                            width="160"
                            height="48"
                            placeholder="请输入"
                            on-input="onEditInput($event, rowIndex, 'description')"
                        />
                        <div class="edit-tip">描述不能超过200个字符</div>
                        <s-button
                            skin="primary"
                            s-ref="editBtn-description-{{rowIndex}}"
                            disabled="{{true}}"
                            on-click="editConfirm(row, rowIndex, 'description')"
                            >确定</s-button
                        >
                        <s-button on-click="editCancel(rowIndex, 'description')">取消</s-button>
                    </div>
                    <outlined-editing-square
                        s-if="!iamPass.disable"
                        on-click="onInstantEdit(row, rowIndex, 'description')"
                        class="name-icon"
                    />
                </s-popover>
            </div>
            <div slot="c-opt" class="operation">
                <s-button
                    on-click="onManageSecondary(row)"
                    skin="stringfy"
                    data-test-id="${testID.vpc.listManageCidr}{{rowIndex}}"
                    track-id="ti_vpc_instance_manage"
                    track-name="管理辅助网段"
                    >{{'管理辅助网段'}}</s-button
                >
                <template>
                    <s-button
                        s-if="row.ipv6Cidr"
                        on-click="onManageIPv6(row)"
                        skin="stringfy"
                        track-id="ti_vpc_instance_ipv6"
                        data-test-id="${testID.vpc.listDeleteIpv6}{{rowIndex}}"
                        track-name="删除IPv6网段"
                        >删除IPv6网段</s-button
                    >
                    <s-button
                        s-else
                        on-click="onManageIPv6(row)"
                        skin="stringfy"
                        track-id="ti_vpc_instance_ipv6"
                        data-test-id="${testID.vpc.listAllocationIpv6}{{rowIndex}}"
                        track-name="分配IPv6网段"
                        >分配IPv6网段</s-button
                    >
                </template>
                <s-button on-click="changeResourceGroup(row)" skin="stringfy">编辑资源分组</s-button>
            </div>
        </s-table>
        <resource-group-dialog
            s-if="{{showResource}}"
            sdk="{{resourceSDK}}"
            resource="{{resource}}"
            on-success="oncommit"
            on-cancel="onCancel"
        />
    </s-biz-page>
</template>`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeComp('@search-tag', '@edit-tag', '@custom-column')
@invokeAppComp
class VpcInstanceIndex extends Component {
    static components = {
        'resource-group-dialog': ResourceGroupDialog,
        'outlined-refresh': OutlinedRefresh,
        'outlined-download': OutlinedDownload,
        'outlined-plus': OutlinedPlus,
        'outlined-editing-square': OutlinedEditingSquare,
        'introduce-panel': IntroducePanel,
        's-textarea': Input.TextArea
    };
    static filters = {
        resourceText(item) {
            if (item.resourceGroups && item.resourceGroups.length) {
                let str = [];
                item.resourceGroups.forEach(resourceGroup => {
                    str.push(u.escape(resourceGroup.resourceGroupName)); //eslint-disable-line
                });
                return str.join('<br>');
            }
            return '-';
        },
        getCidr(item) {
            if (!item) {
                return;
            }
            if (item.auxiliaryCidr) {
                let result = [u.escape(item.cidr) + '（主）'];
                u.each(item.auxiliaryCidr, (data, index) => {
                    result.push(data + '（辅）');
                });
                return result.join('<br>');
            }
            return u.escape(item.cidr);
        },
        getCreateTime(item) {
            if (!item) {
                return;
            }
            return item.defaultVpc ? '系统默认创建' : moment(item.createTime).format('YYYY-MM-DD HH:mm:ss');
        },
        getTag(value) {
            if (!value || value.length < 1) {
                return '-';
            }
            let tagHtml = '';
            let tags = '';
            u.each(value, (item, index) => {
                let tagKey = u.escape(item.tagKey);
                let tagValue = u.escape(item.tagValue);
                tags += '{' + tagKey + ' : ' + tagValue + '} ';
                if (index < 2) {
                    tagHtml += tagKey + ' : ' + tagValue + '<br>';
                }
            });
            value.length > 2 && (tagHtml += '...');
            return '<div title="' + tags + '">' + tagHtml + '</div>';
        }
    };
    initData() {
        let allColumns = [
            {name: 'id', label: '实例名称/ID', minWidth: 200, fixed: 'left'},
            {name: 'status', label: '状态', width: 80},
            {name: 'cidr', label: 'IPv4网段', width: 160},
            {name: 'ipv6Cidr', label: 'IPv6网段', width: 180},
            {name: 'routelayer', label: '路由中继', width: 120},
            {name: 'subnetNum', label: '子网个数', width: 80},
            {name: 'securityGroupNum', label: '安全组个数', width: 100},
            {name: 'csnInfo', label: '加入云智能网', width: 150},
            {name: 'createTime', label: '创建时间', width: 160},
            {name: 'tag', label: '标签', sortable: true, width: 140},
            {name: 'resource', label: '资源分组', width: 90},
            {name: 'description', label: '描述', minWidth: 160},
            {name: 'opt', label: '操作', width: 120, fixed: 'right'}
        ];
        const customColumnDb = allColumns.map(item => ({
            text: item.label,
            value: item.name,
            disabled: item.name === 'id' || item.name === 'opt'
        }));
        return {
            FLAG,
            klass: ['main-wrap-new', 'vpc-instance-list'],
            title: '私有网络',
            createVpc: {},
            deleteVpc: {},
            searchbox: {
                keyword: '',
                placeholder: '请输入实例名称进行搜索',
                defaultLabel: '实例名称',
                keywordType: ['name'],
                keywordTypes: [
                    {value: 'name', text: '实例名称'},
                    {value: 'shortId', text: '实例ID'},
                    {value: 'resGroupId', text: '资源分组'}
                ]
            },
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                allColumns,
                datasource: []
            },
            customColumn: {
                value: [
                    'id',
                    'resourceGroups',
                    'status',
                    'cidr',
                    'ipv6Cidr',
                    'routelayer',
                    'subnetNum',
                    'securityGroupNum',
                    'createTime',
                    'tag',
                    'description',
                    'opt'
                ],
                datasource: customColumnDb
            },
            order: {
                orderBy: 'createTime',
                order: 'desc'
            },
            edit: {
                name: {
                    value: '',
                    error: true,
                    visible: false
                },
                description: {
                    value: '',
                    error: true,
                    visible: false
                }
            },
            vpc10CidrOpen: false,
            selectedItems: [],
            resourceSDK: new ResourceGroupSDK(window.$http, window.$context),
            vpcSinDisable: {},
            iamPass: {},
            DocService,
            sourceList: [],
            checkSourceLoading: false,
            urlQuery: getQueryParams(),
            show: true,
            introduceTitle: '私有网络简介',
            description:
                '私有网络 VPC（Virtual private Cloud）是一个用户能够自定义的虚拟网络，灵活设置网络地址空间，实现私有网络隔离。',
            introduceIcon: INTRODUCE_ICON_COLOR,
            introduceEle: null
        };
    }
    async inited() {
        this.getIamQuery();
        this.checkVpc10CidrOpen();
        this.checkQuota();

        let {createVpc, deleteVpc, vpcSinDisable} = checker.check(rules, []);
        this.data.set('createVpc', createVpc);
        this.data.set('deleteVpc', deleteVpc);
        this.data.set('vpcSinDisable', vpcSinDisable);

        // 需要分配IPV6的私有网络跳转
        const vpcName = this.data.get('urlQuery.vpcName');
        if (vpcName) {
            this.data.set('searchbox.keyword', vpcName);
        }
        const vpcId = this.data.get('urlQuery.id');
        if (vpcId) {
            this.data.set('searchbox.keywordType', ['shortId']);
            this.data.set('searchbox.keyword', vpcId);
        }
        // const isCmcInited = sessionStorage.getItem('cmcVpcInitRegion');
        // if (!isCmcInited) {
        //     const region = this.data.get('urlQuery.region');
        //     resetNewRegion(region);

        //     sessionStorage.setItem('cmcVpcInitRegion', true);
        // }
    }

    attached() {
        this.data.set('introduceEle', this.ref('introduce'));
        if (!FLAG.NetworkVpcSupOrganization) {
            this.getVpcTags();
        }
        this.loadPage();
    }

    // 设置表格列
    setTableColumns(customColumnNames) {
        customColumnNames = customColumnNames ? customColumnNames : this.data.get('customColumn.value');
        const allColumns = this.data.get('table.allColumns');
        let columns = [];
        allColumns.forEach(item => customColumnNames.indexOf(item.name) > -1 && columns.push(item));
        this.data.set('table.columns', columns);
    }
    // 加载页面数据
    loadPage() {
        this.getVpcList();
    }
    // 获取列表数据
    getVpcList() {
        this.data.set('table.loading', true);
        this.data.set('table.selection.selectedIndex', []);
        const param = this.getSearchCriteria();
        if (param.keywordType === 'resGroupId') {
            param.keywordType = 'resourceGroupName';
        }
        this.$http.vpcList(param).then(data => {
            const formatedData = data.map(item => {
                const {auxiliaryCidr, cidr} = item;
                const IPv4CidrList = u.map(auxiliaryCidr || [], cidrItem => {
                    return cidrItem + '（辅）';
                });
                const mainCidr = u.escape(cidr) + (IPv4CidrList.length ? '（主）' : '');
                return {...item, IPv4CidrList: [mainCidr, ...IPv4CidrList]};
            });
            this.data.set('table.datasource', formatedData);
            this.data.set('table.loading', false);
        });
    }
    // 检查配额
    async checkQuota() {
        const data = await this.$http.vpcQuota({});
        // test
        let {createVpc} = checker.check(rules, data, 'createVpc', {quota: data.free});
        this.data.set('createVpc', createVpc);
    }

    filterIpv6Column() {
        let columns = this.data.get('table.columns');
        let customColumn = this.data.get('customColumn');
        let cidrIndex = this.getColumnIndexByName('cidr');
        let ipv6Index = this.getColumnIndexByName('ipv6Cidr');
        columns[cidrIndex].label = '网段';
        customColumn.datasource[cidrIndex].text = '网段';
        customColumn.datasource.splice(ipv6Index, 1);

        columns = u.filter(columns, item => item.name !== 'ipv6Cidr');
        this.data.set('table.columns', columns);
        this.data.set('customColumn', customColumn);
    }

    getColumnIndexByName(name) {
        let customColumn = this.data.get('customColumn');
        return customColumn.datasource.findIndex(item => item.value === name);
    }

    // 获取标签数据源
    getVpcTags() {
        this.ref('search').getTags();
    }

    getSearchCriteria() {
        const searchParam = this.ref('search') ? this.ref('search').getSearchCriteria() : {};
        const order = this.data.get('order');
        if (FLAG.NetworkVpcSupOrganization) {
            const organizationId = window.$framework.organization.getCurrentOrganization().id;
            const currentResourceGroupIds = window.$framework.organization.getCurrentResourceGroup().id;
            const resourceGroupIds = currentResourceGroupIds === 'all' ? [] : [currentResourceGroupIds];
            u.assign(searchParam, {
                organizationId,
                resourceGroupIds
            });
        }
        return u.extend({}, searchParam, order);
    }
    initColumns(value) {
        this.setTableColumns(value);
    }
    // 自定义表格列
    onCustomColumns(value) {
        this.data.set('customColumn.value', value);
        this.setTableColumns(value);
    }
    tableSelected(e) {
        this.data.set('selectedItems', e.value.selectedItems);
        // test
        let {deleteVpc} = checker.check(rules, e.value.selectedItems);
        this.data.set('deleteVpc', deleteVpc);
    }
    onSort(e) {
        this.data.set('order', e.value);
        this.loadPage();
    }
    // 点击修改icon
    onInstantEdit(row, rowIndex, type) {
        this.data.set(`edit.${type}.value`, row[type]);
        this.data.set(`edit.${type}.error`, false);
        const editPop = this.ref(`popover-${type}-${rowIndex}`);
        editPop.data.set('visible', !editPop.data.get('visible'));
    }
    // 编辑弹框-输入名称/描述
    onEditInput(e, rowIndex, type) {
        let result =
            type === 'name'
                ? e.value === '' || !/^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/.test(e.value)
                : e.value.length > 200;
        this.data.set(`edit.${type}.error`, result);
        this.data.set(`edit.${type}.value`, e.value);
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', result);
    }
    // 编辑弹框-提交
    editConfirm(row, rowIndex, type) {
        let edit = this.data.get(`edit.${type}`);
        if (edit.error) {
            return;
        }
        const key = type === 'name' ? 'description' : 'name';
        this.$http
            .vpcUpdate({
                id: row.vpcId,
                [type]: edit.value,
                [key]: row[`${key}`]
            })
            .then(() => {
                this.editCancel(rowIndex, type);
                Notification.success('修改成功');
                this.getVpcList();
            });
    }
    // 编辑弹框-取消
    editCancel(rowIndex, type) {
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', true);
        this.ref(`popover-${type}-${rowIndex}`).data.set('visible', false);
    }
    // 创建实例
    async onCreate() {
        location.hash = '#/vpc/instance/create';
    }
    // 删除实例
    async onDelete() {
        this.data.set('checkSourceLoading', true);
        await this.checkSource();
        let confirm = new DeleteCheckConfirm({
            data: {
                open: true,
                selectedItems: this.data.get('selectedItems'),
                sourceList: this.data.get('sourceList'),
                type: 'vpc'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            let vpcIds = u.map(this.data.get('selectedItems'), o => o.vpcId);
            this.$http.vpcDelete({vpcIds}).then(() => {
                this.refresh();
                this.checkQuota();
                Notification.success('删除成功');
            });
        });
    }
    checkSource() {
        let selectedItems = this.data.get('selectedItems');
        let payload = {
            vpcIds: u.map(selectedItems, o => o.vpcId)
        };
        return this.$http
            .checkVpcBeforeDelete(payload)
            .then(res => {
                let sourceList = [];
                selectedItems.forEach(item => {
                    let id = item.vpcId;
                    if (Object.keys(res.resourceIpCheck[id]).length > 0) {
                        let arr = Object.keys(res.resourceIpCheck[id]).map(i => {
                            return {
                                sourceName: i,
                                sourceNum: res.resourceIpCheck[id][i]
                            };
                        });
                        sourceList.push({
                            name: item.name,
                            source: arr,
                            id
                        });
                    }
                });
                this.data.set('sourceList', sourceList);
                this.data.set('checkSourceLoading', false);
            })
            .catch(err => {
                this.data.set('sourceList', []);
                this.data.set('checkSourceLoading', false);
            });
    }
    async checkVpc10CidrOpen() {
        const vpc10CidrOpen = await this.$http.vpcCommonWhiteList({id: 'Vpc10CidrClose'}).then(data => !data);
        this.data.set('vpc10CidrOpen', vpc10CidrOpen);
        return;
    }
    // 管理辅助网段
    async onManageSecondary(row) {
        const vpc10CidrOpen = this.data.get('vpc10CidrOpen');
        let dialog = new CidrDialog({
            data: {
                open: true,
                title: u.escape(row.name) + ' | ' + u.escape(row.shortId),
                formData: row,
                vpc10CidrOpen
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {
            this.loadPage();
            dialog.dispose && dialog.dispose();
        });
    }
    // 管理IPv6网段
    onManageIPv6(row) {
        let enableIpv6 = row.ipv6Cidr ? 'false' : 'true';
        const {ipv6Cidr} = row;
        if (enableIpv6 === 'false') {
            let confirm = new Confirm({
                data: {
                    open: true,
                    content: `请确定是否要删除该IPv6网段（${ipv6Cidr}）？`
                }
            });
            confirm.attach(document.body);
            confirm.on('confirm', () => {
                this.$http.vpcUpdate({enableIpv6, id: row.vpcId}).then(() => {
                    Notification.success('删除成功');
                    this.loadPage();
                });
            });
        } else {
            this.$http.vpcUpdate({enableIpv6, id: row.vpcId}).then(() => {
                Notification.success('分配成功');
                this.loadPage();
            });
        }
    }
    // 下载
    onDownload() {
        let query = this.getSearchCriteria();
        query = urlSerialize(u.pick(query, o => !!o));
        window.open(`/api/network/v1/vpc/download?${query}`);
    }
    onSearch() {
        this.loadPage();
    }
    refresh() {
        this.loadPage();
        if (FLAG.NetworkSupportTag) {
            this.getVpcTags();
        }
    }
    isCsnCross(row) {
        return window.$context.getUserId() !== row.csnAccountId;
    }

    onRegionChange() {
        location.reload();
    }

    // 编辑资源分组确定后
    oncommit() {
        this.data.set('showResource', false);
        this.loadPage();
    }
    // 编辑资源分组取消后
    onCancel() {
        this.data.set('showResource', false);
    }
    // 编辑资源分组
    changeResourceGroup(row) {
        let resource = {
            resourceId: row.shortId,
            serviceType: 'VPC'
        };
        this.data.set('resource', resource);
        this.data.set('showResource', true);
    }
    getIamQuery() {
        this.$http.getInterfaceIam({interfaceName: 'createVpc'}).then(res => {
            if (!res.requestId && !res.masterAccount) {
                if (!res.interfacePermission) {
                    this.data.set('iamPass', {
                        disable: true,
                        message: '您没有创建私有网络权限，请联系主用户添加'
                    });
                } else {
                    this.data.set('iamPass', {
                        disable: false,
                        message: ''
                    });
                }
            } else {
                this.data.set('iamPass', {
                    disable: false,
                    message: ''
                });
            }
        });
    }
    openRelay(id) {
        return this.$http.openRelay({id});
    }
    checkCanShutDownRelayVpc(id) {
        return this.$http.checkCanShutDownRelayVpc({id});
    }
    shutDownRelay(id, rowIndex) {
        return this.$http.shutDownRelay({id}).then(() => {
            this.data.set(`table.datasource[${rowIndex}].relay`, false);
        });
    }
    routeRelayChange({value}, row, rowIndex) {
        let requester = !row.relay ? this.openRelay.bind(this.$http) : this.checkCanShutDownRelayVpc.bind(this.$http);
        let vpcId = row.vpcUuid || row.vpcId;
        this.data.set(`table.datasource[${rowIndex}].disableFlag`, true);
        requester(vpcId)
            .then(data => {
                if (!value) {
                    if (data && data.canShutDownRelayVpc) {
                        return this.shutDownRelay(vpcId, rowIndex);
                    }
                    let message =
                        '路由表中源网段有全0以及自定义非子网内的网段，则不能关闭中继开关，实例路由不受影响。如需关闭，可先删除相关路由条目。';
                    Notification.error(message, {duration: -1});
                    return Promise.reject();
                } else {
                    // 无感更新数据
                    this.data.set(`table.datasource[${rowIndex}].relay`, value);
                }
            })
            .catch(() => {
                row.relay = !value;
                this.data.splice('table.datasource', [rowIndex, 1, row]);
            })
            .finally(() => {
                this.data.set(`table.datasource[${rowIndex}].disableFlag`, false);
            });
    }
    handleToggle(flag) {
        this.data.set('show', flag);
        this.data.set('introduceIcon', INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#151B26';
    }
    handleShowCard() {
        if (!this.data.get('show')) {
            this.data.set('show', true);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type) {
        this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#2468F2';
    }
    handleMouseLeave(type) {
        const isShow = this.data.get('show');
        this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        !isShow && (ele.style.color = '#151B26');
    }
}
export default San2React(Processor.autowireUnCheckCmpt(VpcInstanceIndex));
