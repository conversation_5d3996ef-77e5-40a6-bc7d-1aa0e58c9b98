/**
 * @file network/instance/pages/CidrDialog.js
 * <AUTHOR>
 */
import u from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Notification, Form, Select, Input, Button, Alert} from '@baidu/sui';
import {Netseg, Mask} from '@/pages/sanPages/common/enum';
import testID from '@/testId';
import {
    getPartDatasource,
    getAvaliableByMask,
    getAvaliableContent,
    convertCidrToBinary,
    checkIsInSubnet
} from '@/pages/sanPages/utils/common';
import rule from '@/pages/sanPages/utils/rule';
import {checkCidrInDisable} from './helper';

const tpl = html`
    <template>
        <s-dialog
            class="network-instance-manage-cidr"
            closeAfterMaskClick="{{false}}"
            open="{=open=}"
            width="680"
            title="{{title}}"
        >
            <s-alert skin="info" showIcon="{{false}}">温馨提示：添加的辅助网段不能与该VPC已有的网段CIDR重叠。</s-alert>
            <s-form formData="{=formData=}" label-align="left">
                <s-form-item label="主网段：">{{primaryCidr || '-'}}</s-form-item>
                <s-form-item label="辅助网段：">
                    <div class="operation">
                        <a
                            s-if="{{secondaryCidrs.length < auxiliaryCidrNum}}"
                            href="javascript:void(0)"
                            on-click="addItem()"
                            data-testid="${testID.vpc.auxiliaryAddBtn}"
                        >
                            <i class="iconfont icon-plus"></i>&nbsp; 添加辅助网段
                        </a>
                        <a class="state-disabled" s-else> <i class="iconfont icon-plus"></i>&nbsp; 添加辅助网段 </a>
                    </div>
                    <ul>
                        <li s-for="item, index in secondaryCidrs" class="cidr-item">
                            <s-input
                                value="{=item.cidr=}"
                                on-input="onInputCidr"
                                placeholder="请输入IPv4网段，如：10.0.0.0/24"
                                class="{{item.error ? 'error-input' : ''}} cidr-input"
                            />
                            <a href="javascript:void(0)" on-click="deleteItem(index)" class="delete-item">
                                <i class="iconfont icon-close"></i>
                            </a>
                            <div s-if="item.tip" class="item-tip">{{item.tip}}</div>
                            <div s-if="item.error" class="item-error">{{item.error}}</div>
                        </li>
                    </ul>
                </s-form-item>
            </s-form>
            <div slot="footer">
                <s-button on-click="close">取消</s-button>
                <s-tooltip>
                    <s-button skin="primary" on-click="submit">确定</s-button>
                </s-tooltip>
            </div>
        </s-dialog>
    </template>
`;
export default class AuxiliaryCidr extends Component {
    static template = tpl;
    static components = {
        's-dialog': Dialog,
        's-button': Button,
        's-form': Form,
        's-form-item': Form.Item,
        's-select': Select,
        's-input': Input,
        's-alert': Alert
    };
    initData() {
        return {
            open: false,
            formData: null,
            primaryCidr: null,
            secondaryCidrs: [],
            segDatasource: [Netseg.fromAlias('SEG192'), Netseg.fromAlias('SEG172'), Netseg.fromAlias('SEG10')]
        };
    }
    inited() {
        let cidrs = this.data.get('formData.auxiliaryCidr');
        let secondaryCidrs = [];
        u.each(cidrs, item => {
            secondaryCidrs.push({cidr: item});
        });
        this.data.set('primaryCidr', this.data.get('formData.cidr'));
        this.data.set('secondaryCidrs', secondaryCidrs);
        this.$http.auxiliaryCidrQuota(this.data.get('formData.vpcId')).then(res => {
            this.data.set('auxiliaryCidrNum', res.total);
        });
    }
    addItem() {
        this.data.push('secondaryCidrs', {cidr: ''});
        const secondaryCidrs = this.data.get('secondaryCidrs');
        if (secondaryCidrs && secondaryCidrs.length === 1 && secondaryCidrs[0].cidr === '') {
            return;
        }
        this.validate();
    }
    onInputCidr() {
        this.nextTick(() => this.validate());
    }
    deleteItem(index) {
        this.data.removeAt('secondaryCidrs', index);
        this.validate();
    }
    validate() {
        let flag = true;
        let primaryCidr = this.data.get('primaryCidr');
        let secondaryCidrs = this.data.get('secondaryCidrs');
        const existCidrs = [];
        const vpc10CidrOpen = this.data.get('vpc10CidrOpen');
        for (let i = 0; i < secondaryCidrs.length; i++) {
            this.data.set(`secondaryCidrs[${i}].error`, null);
        }
        for (let i = 0; i < secondaryCidrs.length; i++) {
            let first = secondaryCidrs[i].cidr;
            if (!new RegExp(rule.IP_CIDR).test(first)) {
                this.data.set(`secondaryCidrs[${i}].error`, 'CIDR不合法');
                flag = false;
                break;
            }
            if (flag) {
                if (existCidrs.indexOf(first) >= 0) {
                    flag = false;
                    this.data.set(`secondaryCidrs[${i}].error`, '辅助网段重复');
                    break;
                }
                existCidrs.push(first);
            }
            if (flag) {
                let valueString = convertCidrToBinary(first);
                let valueMask = first.split('/')[1];
                if (valueString.substring(+valueMask, valueString.length).indexOf('1') > -1) {
                    this.data.set(`secondaryCidrs[${i}].error`, 'CIDR不合法');
                    flag = false;
                    break;
                }
            }
            if (flag) {
                if (checkIsInSubnet(first, primaryCidr) || checkIsInSubnet(primaryCidr, first)) {
                    this.data.set(`secondaryCidrs[${i}].error`, 'CIDR重叠');
                    flag = false;
                    break;
                }
            }
            if (flag) {
                for (let j = i + 1; j < secondaryCidrs.length; j++) {
                    let second = u.pluck(secondaryCidrs[j].part, 'value').join('.') + '/' + secondaryCidrs[j].mask;
                    if (checkIsInSubnet(first, second) || checkIsInSubnet(second, first)) {
                        this.data.set(`secondaryCidrs[${j}].error`, 'CIDR重叠');
                        flag = false;
                        break;
                    }
                }
                if (!flag) {
                    break;
                }
            }
            if (flag) {
                const mask = first.split('/')[1];
                if (checkCidrInDisable(first) || mask > 28) {
                    this.data.set(`secondaryCidrs[${i}].error`, '当前网段不可使用公网私用');
                    flag = false;
                    break;
                }
            }
            if (flag) {
                if (first.startsWith('10') && !first.startsWith('10.0') && !vpc10CidrOpen) {
                    this.data.set(`secondaryCidrs[${i}].error`, '用户VPC10网段未开放');
                    flag = false;
                    break;
                }
            }
        }
        return flag;
    }
    getSecondaryCidrs() {
        const cidrs = u.map(this.data.get('secondaryCidrs') || [], item => item.cidr);
        return cidrs;
    }
    async submit() {
        if (!this.validate()) {
            return;
        }
        const cidrs = this.getSecondaryCidrs();
        try {
            await this.$http.vpcUpdate({
                id: this.data.get('formData.vpcId'),
                description: this.data.get('formData.description'),
                name: this.data.get('formData.name'),
                auxiliaryCidr: cidrs
            });
            this.data.set('open', false);
            this.fire('success');
        } catch (ex) {
            Notification.error('操作失败');
        }
    }
    close() {
        this.data.set('open', false);
        this.fire('cancel');
    }
}
