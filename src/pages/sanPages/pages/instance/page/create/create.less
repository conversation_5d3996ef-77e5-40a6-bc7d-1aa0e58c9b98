.vpc-instance-create {
    background: #fff;
    width: 100%;
    min-height: 100%;
    background-color: #f7f7f9;
    padding-bottom: 20px;
    .cidr-required {
        .s-form-item:nth-child(3) {
            .s-form-item-label-left {
                & > label::before {
                    left: -7px;
                    position: absolute;
                    content: '*';
                    color: #f33e3e;
                    margin-right: 4px;
                }
            }
        }
        .cidr-error {
            margin-top: 5px;
            color: #eb5252;
        }
        .cidr-tip {
            margin-top: 5px;
            color: #999;
        }
        .alert-tip {
            display: inline-flex;
        }
        .cidr-btn {
            padding: 0;
        }
    }
    .inline-tip {
        top: 3px;
        position: relative;
        .s-tip-warning {
            justify-content: center;
            .warning_class {
                fill: #999;
            }
        }
        .s-tip:hover .s-icon path {
            fill: #2468f2 !important;
        }
    }
    .form-part-wrap {
        border-radius: 6px;
        width: calc(~'100vw - 32px');
        background: #fff;
        margin: 16px;
        padding: 24px;
        h4 {
            display: inline-block;
            border: none;
            zoom: 1;
            font-size: 16px;
            color: #151b26;
            font-weight: 500;
            margin: 0;
            padding: 0;
        }
        .s-form-item-label {
            width: 92px;
            height: 30px;
        }
        .label_class {
            .inline-tip {
                top: 3px;
                position: relative;
                .s-tip-warning {
                    justify-content: center;
                    .warning_class {
                        fill: #999;
                    }
                }
                .s-tip:hover .s-icon path {
                    fill: #2468f2 !important;
                }
            }
        }
        .center_class {
            .s-row {
                .s-form-item-control-wrapper {
                    line-height: 30px;
                }
            }
        }
        .resource-form-part-wrap {
            h4 {
                border: none;
                padding-left: 0;
                font-size: 16px;
                font-weight: 500;
            }
            .resource-group-panel {
                border: none;
                padding: 0;
                .resouce-group-select {
                    .footer {
                        margin-left: 100px;
                    }
                }
            }
            margin: 10px auto 0 auto;
        }
        .s-form-item-label-left {
            width: 121px;
        }
    }
    .buybucket {
        width: 100%;
        left: 0;
        height: 60px;
        .buybucket-container {
            width: auto;
            float: left;
            height: 48px;
            transform: translateY(12.5%);
            display: flex;
            align-items: center;
        }
    }
    .CIDR-class {
        display: flex;
        .span-class {
            margin: 0 3px;
        }
        .slash-class {
            position: relative;
            top: 4px;
        }
    }
    .s-tip-warning {
        position: relative;
        top: 3px;
    }
    .cidr-tip-new,
    .cidr-tip-err {
        position: relative;
        font-size: 12px;
        padding-top: 5px;
        color: grey;
    }
    .cidr-tip-err {
        color: #d0021b;
        word-break: normal;
        word-wrap: break-word;
    }
    .cidr-flex {
        display: flex;
    }
    .hide-btn {
        .s-button {
            display: none;
        }
    }
    .assist-tip:after {
        background-color: unset;
    }
    .s-table {
        .s-table-container {
            overflow: visible;
        }
    }
}

.tag-edit-panel {
    .bui-textbox-input-area input {
        font-size: 12px;
    }
}

.locale-en {
    .vpc-instance-create .form-part-wrap .s-form-item-label-left {
        width: 230px;
    }
    .vpc-instance-create .resouce-group-select .resouce-group-select-main > label {
        width: 230px;
    }
    .vpc-instance-create .form-part-wrap .resource-form-part-wrap .resource-group-panel .resouce-group-select .footer {
        margin-left: 230px;
    }
}
