import {html, decorators, CreatePage, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import u from 'lodash';
import {TagEditPanel} from '@baidu/bce-tag-sdk-san';
import {Dialog, Form, Select, Input, Button, Switch, Tooltip, Icon} from '@baidu/sui';
import {Tip} from '@baidu/sui-biz';
import {OutlinedPlus} from '@baidu/sui-icon';
import {ResourceGroupPanel} from '@baidu/bce-resourcegroup-sdk-san';
import {ResourceGroupSDK} from '@baidu/bce-resourcegroup-sdk';
import {checker} from '@baiducloud/bce-opt-checker';
import Assist from '@/utils/assist';

import {Netseg, Mask, ServiceType, RouteSourceAll} from '@/pages/sanPages/common';
import {
    getPartDatasource,
    getAvaliableByMask,
    getAvaliableContent,
    convertCidrToBinary,
    checkIsInSubnet,
    getDifferenceAvaliableNew,
    getMaskNum
} from '@/pages/sanPages/utils/common';
import rule from '@/pages/sanPages/utils/rule';
import {checkCidrInDisable} from '../helper';
import {disable_vpc_10cidr as disableVpc10Cidr} from '@/pages/sanPages/common';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import testID from '@/testId';
import rules from '../../rule';
import zone from '../../../../utils/zone';
import RULE from '../../../../utils/rule';

import './create.less';

const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, service} = decorators;

const tpl = html`
    <template>
        <s-app-create-page
            class="vpc-instance-create"
            backTo="{{pageNav.backUrl}}"
            backToLabel="{{pageNav.backLabel}}"
            pageTitle="{{pageNav.title}}"
        >
            <s-form s-ref="form" data="{=formData=}" rules="{{rules}}" label-align="left" class="cidr-required">
                <div class="form-part-wrap">
                    <h4 class="title_vpc">VPC配置信息</h4>
                    <s-form-item
                        prop="name"
                        label="VPC名称："
                        help="命名规范：1.只能包含大小写字母，数字，中文和'-_ /.'；2.必须以字母或者中文开头；3.长度限制在1-65之间。"
                    >
                        <s-input
                            value="{=formData.name=}"
                            placeholder=""
                            track-id="ti_vpc_create_name"
                            data-test-id="${testID.vpc.createNameInput}"
                            track-name="VPC名称"
                            width="{{400}}"
                        />
                    </s-form-item>
                    <s-alert class="alert-tip">
                        <span>建议您使用内网网段作为私有网络地址段，比如：</span>
                        <s-button on-click="setCidr('***********/16')" class="cidr-btn" skin="stringfy"
                            >***********/16</s-button
                        >，
                        <s-button on-click="setCidr('**********/12')" class="cidr-btn" skin="stringfy"
                            >**********/12</s-button
                        >，
                        <s-button on-click="setCidr('10.0.0.0/8')" class="cidr-btn" skin="stringfy"
                            >10.0.0.0/8</s-button
                        >
                        <span>。请您谨慎使用公网地址段作为私有网络地址段。</span>
                        <a
                            s-if="FLAG.NetworkSupportAI"
                            class="assist-tip"
                            href="javascript:void(0)"
                            on-click="showAssist"
                            s-if="FLAG.NetworkSupportAI"
                            >了解详情</a
                        >
                    </s-alert>
                    <s-form-item prop="cidr" label="IPv4 CIDR：">
                        <s-input
                            value="{=formData.cidr=}"
                            data-test-id="${testID.vpc.createCidrInput}"
                            placeholder="请输入IPv4网段，如：10.0.0.0/24"
                            width="{{400}}"
                        />
                    </s-form-item>
                    <!--<s-form-item s-if="!selfIpCidr" prop="cidr" label="IPv4 CIDR：">
                        <template slot="label" class="label_class">
                            {{'IPv4 CIDR：'}}
                            <s-tip
                                class="inline-tip"
                                content="支持172.16.X.X~172.31.X.X，192.168.X.X，10.0.X.X网段"
                                skin="question"
                                />
                        </template>
                        <s-select datasource="{{segDatasource}}"
                            value="{=cidr.part[0].value=}"
                            width="{{100}}"
                            on-change="onSegChange($event)"
                            track-id="ti_vpc_create_netseg"
                            track-name="CIDR"/>
                        <span> . </span>
                        <s-input s-if="cidr.part[0].value === '10' && vpc10CidrOpen"
                            value="{=cidr.part[1].value=}"
                            disabled="{=cidr.part[1].disabled=}"
                            width="{{78}}"
                            on-focus="onPartFocus($event, 1)"
                            on-blur="onPartBlur($event, 1)"
                            track-id="ti_vpc_create_netseg"
                            track-name="CIDR"/>
                        <s-select s-else
                            datasource="{{partDatasource[cidr.part[0].value]}}"
                            value="{=cidr.part[1].value=}"
                            disabled="{=cidr.part[1].disabled=}"
                            width="{{100}}"
                            on-change="onPartChange($event)"
                            track-id="ti_vpc_create_netseg"
                            track-name="CIDR"/>
                        <span>.</span>
                        <s-input value="{=cidr.part[2].value=}"
                            disabled="{=cidr.part[2].disabled=}"
                            width="{{50}}"
                            on-focus="onPartFocus($event, 2)"
                            on-blur="onPartBlur($event, 2)"
                            track-id="ti_vpc_create_netseg"
                            track-name="CIDR"/>
                        <span>.</span>
                        <s-input value="{=cidr.part[3].value=}"
                            disabled="{=cidr.part[3].disabled=}"
                            width="{{50}}"
                            on-focus="onPartFocus($event, 3)"
                            on-blur="onPartBlur($event, 3)"
                            track-id="ti_vpc_create_netseg"
                            track-name="CIDR"/>
                        <s-select datasource="{{maskDatasource}}"
                            disabled="{{cidr.old}}"
                            value="{=cidr.mask=}"
                            width="{{100}}"
                            on-change="onMaskChange($event)"
                            track-id="ti_vpc_create_netseg"
                            track-name="CIDR掩码"/>
                        <div s-if="cidr.tip" class="cidr-tip">{{cidr.tip}}</div>
                        <div s-if="cidr.error" class="cidr-error">{{cidr.error}}</div>
                    </s-form-item>-->
                    <s-form-item prop="enableIpv6" class="center_class assign_ipv6">
                        <template slot="label" class="label_class">
                            {{'分配IPv6网段：'}}
                            <s-tip
                                class="inline-tip"
                                content="为该VPC统一分配掩码为56的IPv6 CIDR块，用户不可自定义网段，默认不分配。"
                                skin="question"
                            />
                        </template>
                        <s-switch
                            width="{{278}}"
                            checked="{=formData.enableIpv6=}"
                            track-id="ti_vpc_create_ipv6"
                            on-change="ipv6Change($event)"
                            track-name="分配IPv6网段"
                        />
                    </s-form-item>
                    <s-form-item prop="description" label="描述：">
                        <s-textarea
                            width="{{400}}"
                            height="{{100}}"
                            maxLength="{{200}}"
                            value="{=formData.description=}"
                            track-id="ti_vpc_create_description"
                            track-name="描述"
                        />
                    </s-form-item>
                </div>
                <div class="form-part-wrap">
                    <h4 class="title_vpc">子网配置信息</h4>
                    <s-form-item>
                        <s-table
                            style="overflow: visible"
                            columns="{{table.columns}}"
                            datasource="{{table.datasource}}"
                        >
                            <div slot="empty" class="{{createSubnetBatch.disable ? 'hide-btn' : ''}}">
                                <s-empty on-click="addRule" />
                            </div>
                            <div slot="h-name">
                                {{col.label}}
                                <s-tip
                                    class="inline-tip-table"
                                    content="大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65"
                                    skin="warning"
                                />
                            </div>
                            <div slot="h-enableIpv6">
                                {{col.label}}
                                <s-tip
                                    class="inline-tip-table"
                                    content="为该子网统一分配掩码为64的IPv6 CIDR块，
                                      用户不可自定义网段，默认不分配。"
                                    skin="warning"
                                />
                            </div>
                            <div slot="c-name" prop="name">
                                <s-input
                                    width="100"
                                    value="{=name[rowIndex]=}"
                                    track-id="ti_vpc_subnet_create"
                                    on-input="dataInput('name', rowIndex, $event)"
                                    track-name="子网名称"
                                />
                                <p class="cidr-tip-err" s-if="nameErr[rowIndex]">{{nameErr[rowIndex]}}</p>
                            </div>
                            <div slot="c-CIDR">
                                <div class="cidr-flex">
                                    <div prop="CIDR0" class="CIDR-class">
                                        <s-select
                                            width="{{80}}"
                                            datasource="{{cidr0List}}"
                                            disable="{{disableCidr0}}"
                                            value="{=cidr0[rowIndex]=}"
                                            on-change="cidr0Change(rowIndex, $event)"
                                            track-id="ti_vpc_subnet_create"
                                            track-name="CIDR"
                                        />
                                        <span class="span-class"> . </span>
                                    </div>
                                    <div prop="cidr1" class="CIDR-class">
                                        <s-input
                                            width="50"
                                            value="{{cidr1[rowIndex]}}"
                                            on-input="cidrInput(rowIndex, 1, $event)"
                                            on-focus="cidrFocus(1, rowIndex)"
                                            on-blur="cidrBlur(rowIndex)"
                                            name="CIDR1"
                                            disabled="{{disableCidr1}}"
                                            track-id="ti_vpc_subnet_create"
                                            track-name="CIDR"
                                            maxLength="3"
                                        />
                                        <span class="span-class"> . </span>
                                    </div>
                                    <div prop="cidr2" class="CIDR-class">
                                        <s-input
                                            width="50"
                                            value="{{cidr2[rowIndex]}}"
                                            on-input="cidrInput(rowIndex, 2, $event)"
                                            on-focus="cidrFocus(2, rowIndex)"
                                            on-blur="cidrBlur(rowIndex)"
                                            name="CIDR2"
                                            disabled="{{disableCidr2}}"
                                            track-id="ti_vpc_subnet_create"
                                            track-name="CIDR"
                                            maxLength="3"
                                        />
                                        <span class="span-class"> . </span>
                                    </div>
                                    <div prop="cidr3" class="CIDR-class">
                                        <s-input
                                            width="50"
                                            value="{{cidr3[rowIndex]}}"
                                            on-input="cidrInput(rowIndex, 3, $event)"
                                            on-focus="cidrFocus(3, rowIndex)"
                                            on-blur="cidrBlur(rowIndex)"
                                            name="CIDR3"
                                            disabled="{{disableCidr3}}"
                                            track-id="ti_vpc_subnet_create"
                                            track-name="CIDR"
                                            maxLength="3"
                                        />
                                        <span class="span-class slash-class"> / </span>
                                    </div>
                                    <div prop="mask">
                                        <s-select
                                            width="{{60}}"
                                            datasource="{{maskList}}"
                                            value="{=mask[rowIndex]=}"
                                            on-change="maskChange(rowIndex, $event)"
                                            track-id="ti_vpc_subnet_create"
                                            track-name="CIDR"
                                        />
                                    </div>
                                </div>
                                <div>
                                    <div class="cidr-tip-new">{{ipTotalTip[rowIndex]}}</div>
                                    <div class="cidr-tip-new" s-if="showCidrTip[rowIndex]">{{cidrTip[rowIndex]}}</div>
                                    <!--bca-disable-next-line-->
                                    <div class="cidr-tip-err" s-if="cidrErr[rowIndex]">{{cidrErr[rowIndex] | raw}}</div>
                                </div>
                            </div>
                            <div slot="c-zone" prop="zone">
                                <s-select
                                    width="90"
                                    datasource="{{zoneList}}"
                                    disabled="{{haveSet}}"
                                    value="{=zone[rowIndex]=}"
                                    on-change="dataChange('zone', rowIndex, $event)"
                                    track-id="ti_vpc_subnet_create"
                                    track-name="可用区"
                                />
                                <p class="cidr-tip-err" s-if="zoneErr[rowIndex]">{{zoneErr[rowIndex]}}</p>
                            </div>
                            <div slot="c-subnetType" prop="subnetType">
                                <s-select
                                    width="80"
                                    datasource="{{subnetTypeList}}"
                                    value="{=subnetType[rowIndex]=}"
                                    track-id="ti_vpc_subnet_create"
                                    on-change="dataChange('subnetType', rowIndex, $event)"
                                    track-name="设备类型"
                                />
                            </div>
                            <div slot="c-enableIpv6">
                                <s-switch
                                    checked="{=enableIpv6[rowIndex]=}"
                                    track-id="ti_vpc_subnet_create_ipv6"
                                    disabled="{{subnetIpv6Disable}}"
                                    on-change="dataChange('enableIpv6', rowIndex, $event)"
                                    track-name="分配IPv6网段"
                                />
                            </div>
                            <div slot="c-description" prop="description">
                                <s-input
                                    maxLength="200"
                                    placeholder="请输入"
                                    width="130"
                                    value="{=description[rowIndex]=}"
                                    on-input="dataInput('description', rowIndex, $event)"
                                    track-id="ti_vpc_subnet_create"
                                    track-name="描述"
                                />
                            </div>
                            <div slot="c-opt">
                                <s-button skin="stringfy" on-click="deleteRule(rowIndex)">删除</s-button>
                            </div>
                        </s-table>
                        <div style="padding:10px 0 0 10px" s-if="!isEdit">
                            <s-tooltip trigger="{{createSubnetBatch.disable ? 'hover' : ''}}" placement="right">
                                <!--bca-disable-next-line-->
                                <div slot="content">{{createSubnetBatch.message | raw}}</div>
                                <s-button skin="primary" disabled="{{createSubnetBatch.disable}}" on-click="addRule">
                                    <outlined-plus />新增一行
                                </s-button>
                            </s-tooltip>
                        </div>
                    </s-form-item>
                </div>
                <div class="form-part-wrap">
                    <div class="resource-form-part-wrap">
                        <resource-group-panel
                            refreshAvailable="{{true}}"
                            sdk="{{resourceSDK}}"
                            on-change="resourceChange($event)"
                        />
                    </div>
                </div>
                <div class="form-part-wrap">
                    <h4 s-if="!FLAG.NetworkVpcSupOrganization" class="title_vpc">标签</h4>
                    <s-form-item prop="tag" s-if="!FLAG.NetworkVpcSupOrganization" label="绑定标签：">
                        <tag-edit-panel
                            create
                            instances="{{defaultInstances}}"
                            options="{{tagListRequster}}"
                            s-ref="tagPanel"
                        />
                    </s-form-item>
                </div>
            </s-form>
            <div slot="pageFooter" class="buybucket">
                <div class="buybucket-container">
                    <s-tooltip
                        trigger="{{iamPass.disable || vpcSinDisable.disable || createVpc.disable ? 'hover' : ''}}"
                        placement="top"
                    >
                        <s-button
                            size="large"
                            disabled="{{iamPass.disable || vpcSinDisable.disable || createVpc.disable || isCreating}}"
                            skin="primary"
                            data-test-id="${testID.vpc.createSubmit}"
                            on-click="doSubmit"
                        >
                            确定
                        </s-button>
                        <div slot="content">
                            <!--bca-disable-next-line-->
                            {{iamPass.message || vpcSinDisable.message || createVpc.message | raw}}
                        </div>
                    </s-tooltip>
                    <s-button size="large" on-click="close"> 取消 </s-button>
                </div>
            </div>
        </s-app-create-page>
    </template>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class InstanceCreate extends CreatePage {
    static template = tpl;
    static components = {
        's-form': Form,
        's-form-item': Form.Item,
        's-select': Select,
        's-input': Input,
        's-textarea': Input.TextArea,
        's-button': Button,
        's-switch': Switch,
        's-tooltip': Tooltip,
        's-icon': Icon,
        'tag-edit-panel': TagEditPanel,
        'resource-group-panel': ResourceGroupPanel,
        's-tip': Tip,
        'outlined-plus': OutlinedPlus
    };

    static messages = {
        projectChange({value}) {
            value && this.data.set('resourceGroupId', value);
        }
    };

    initData() {
        return {
            FLAG,
            pageNav: {
                title: '创建VPC',
                backUrl: '/network/#/vpc/instance/list',
                backLabel: '返回私有网络列表'
            },
            formData: {
                enableIpv6: false
            },
            cidr: {
                part: [
                    {value: Netseg.SEG192, disabled: false},
                    {value: getPartDatasource(Netseg.SEG192, Mask.Mask16)[0].value, disabled: false},
                    {value: 0, disabled: true},
                    {value: 0, disabled: true}
                ],
                mask: Mask.Mask16
            },
            segDatasource: [Netseg.fromAlias('SEG192'), Netseg.fromAlias('SEG172'), Netseg.fromAlias('SEG10')],
            defaultInstances: [
                {
                    tags: [
                        {
                            tagKey: '默认项目',
                            tagValue: ''
                        }
                    ]
                }
            ],
            tagListRequster: this.tagListRequster.bind(this),
            rules: {
                name: [
                    {required: true, message: '名称必填'},
                    {pattern: /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/, message: '名称不符合规则'},
                    {
                        validator(rule, value, callback) {
                            if (value === 'default') {
                                return callback('VPC名称不能是default');
                            }
                            callback();
                        }
                    }
                ],
                cidr: [{required: true, message: '请填写IPv4网段'}, this.validateWhiteCidr()]
            },
            resourceGroupId: '',
            resourceSDK: new ResourceGroupSDK(window.$http, window.$context),
            vpcSinDisable: {},
            zoneList: [],
            subnetList: [],
            vpcList: [],
            subnetTypeList: [],
            haveSet: false,
            disableCidr0: false,
            disableCidr1: false,
            disableCidr2: false,
            disableCidr3: false,
            cidrTip: [],
            showCidrTip: [],
            disableSubmit: false,
            open: true,
            cidrErr: [],
            nameErr: [],
            zoneErr: [],
            ipTotalTip: [],
            docUrl: '',
            resourceGroupId: '',
            resourceSDK: new ResourceGroupSDK(window.$http, window.$context),
            table: {
                columns: [
                    {name: 'name', label: '子网名称', width: 50},
                    {name: 'zone', label: '可用区', width: 40},
                    {name: 'CIDR', label: 'IPv4 CIDR', width: 130},
                    {name: 'subnetType', label: '设备类型', width: 45},
                    {name: 'enableIpv6', label: '分配IPv6网段', width: 50},
                    {name: 'description', label: '描述', width: 55},
                    {name: 'opt', label: '操作', width: 40}
                ],
                datasource: []
            },
            name: [],
            zone: [],
            subnetType: [],
            enableIpv6: [],
            description: [],
            cidr0: [],
            cidr1: [],
            cidr2: [],
            cidr3: [],
            mask: [],
            cidr0List: [],
            createSubnetBatch: {
                disable: false, //测试
                message: '请先输入VPC IPv4 CIDR'
            },
            tableCidrList: [],
            subnetBatchCreateQuota: 5,
            subnetIpv6Disable: true,
            inBroadcastWhiteList: false,
            iamPass: {},
            createVpc: {},
            cidrErrIndex: 0
        };
    }

    inited() {
        this.checkQuota();
        this.getIamQuery();
        this.checkVpc10CidrOpen();
        this.getBatchCreateSubnetQuota();
        let {vpcSinDisable} = checker.check(rules, []);
        this.data.set('vpcSinDisable', vpcSinDisable);
        // 以下是子网相关逻辑
        this.data.set('subnetTypeList', [ServiceType.fromAlias('BCC')]);
        this.data.set('availableService', window.$context.getAvailableService().split(','));
        // 设置设备类型数据源
        this.setSubnetTypeList();
        // 如果调用方传了zone，将可用区设置为该值，且不可选其它值
        if (this.data.get('zone').length) {
            this.data.set('zone[0]', this.data.get('zone'));
            this.data.set('haveSet', true);
        }
    }

    getBatchCreateSubnetQuota() {
        this.$http.subnetBatchCreateQuota().then(res => {
            this.data.set('subnetBatchCreateQuota', res.total);
        });
    }

    attached() {
        this.$http.getZoneList().then(data => {
            const zoneList = u.map(u.map(data, 'zone'), item => {
                return {
                    value: item,
                    text: zone.getLabel(item)
                };
            });
            this.data.set('zoneList', zoneList);
        });
        this.broadcastWhitelist();
    }

    static computed = {
        partDatasource() {
            return {
                [Netseg.SEG192]: getPartDatasource(Netseg.SEG192, Mask.Mask16),
                [Netseg.SEG172]: getPartDatasource(Netseg.SEG172, Mask.Mask16),
                [Netseg.SEG10]: getPartDatasource(
                    Netseg.SEG10,
                    this.data.get('vpc10CidrOpen') ? Mask.Mask8 : Mask.Mask16
                )
            };
        },
        maskDatasource() {
            const part0 = this.data.get('cidr.part[0].value');
            const db = {
                [Netseg.SEG192]: Mask.toArray(
                    'Mask16',
                    'Mask17',
                    'Mask18',
                    'Mask19',
                    'Mask20',
                    'Mask21',
                    'Mask22',
                    'Mask23',
                    'Mask24',
                    'Mask25',
                    'Mask26',
                    'Mask27',
                    'Mask28'
                ),
                [Netseg.SEG172]: Mask.toArray(
                    'Mask12',
                    'Mask13',
                    'Mask14',
                    'Mask15',
                    'Mask16',
                    'Mask17',
                    'Mask18',
                    'Mask19',
                    'Mask20',
                    'Mask21',
                    'Mask22',
                    'Mask23',
                    'Mask24',
                    'Mask25',
                    'Mask26',
                    'Mask27',
                    'Mask28'
                ),
                [Netseg.SEG10]: this.data.get('vpc10CidrOpen')
                    ? Mask.toArray()
                    : Mask.toArray(
                          'Mask16',
                          'Mask17',
                          'Mask18',
                          'Mask19',
                          'Mask20',
                          'Mask21',
                          'Mask22',
                          'Mask23',
                          'Mask24',
                          'Mask25',
                          'Mask26',
                          'Mask27',
                          'Mask28'
                      )
            };
            return db[part0];
        },
        subnetIpv6Disable() {
            return !this.data.get('formData.enableIpv6');
        },
        createSubnetBatch() {
            let vpcCidr = this.data.get('formData.cidr');
            vpcCidr = vpcCidr && vpcCidr.trim();
            let valueString = vpcCidr && convertCidrToBinary(vpcCidr);
            let valueMask = vpcCidr && new RegExp(rule.IP_CIDR).test(vpcCidr) && vpcCidr.split('/')[1];
            let isDisable =
                !vpcCidr ||
                !new RegExp(rule.IP_CIDR).test(vpcCidr) ||
                valueString.substring(+valueMask, valueString.length).indexOf('1') > -1 ||
                checkCidrInDisable(vpcCidr) ||
                valueMask > 28;
            return {
                disable: isDisable,
                message: isDisable ? '请输入正确的VPC IPv4 CIDR' : ''
            };
        },
        isCreating() {
            let tableDatasource = this.data.get('table.datasource');
            if (tableDatasource.length) {
                tableDatasource.forEach((item, index) => {
                    if (
                        this.data.get(`cidrErr[${index}]`) ||
                        this.data.get(`nameErr[${index}]`) ||
                        this.data.get(`zoneErr[${index}]`)
                    ) {
                        return true;
                    }
                });
            } else {
                return false;
            }
        }
    };

    async broadcastWhitelist() {
        const region = this.$context.getCurrentRegion();
        let result = await this.$http.broadcastWhitelist();
        this.data.set('inBroadcastWhiteList', result.regions.indexOf(region.id) > -1);
    }

    setCidr(cidr) {
        this.data.set('formData.cidr', cidr);
        this.nextTick(() => {
            this.ref('form').validateFields(['cidr']);
        });
    }

    validateWhiteCidr() {
        return {
            validator: (rules, value, callback) => {
                value = value && value.trim();
                if (!value) {
                    return callback('请输入IPv4 CIDR');
                }
                if (!new RegExp(rule.IP_CIDR).test(value)) {
                    return callback('CIDR不合法');
                }
                let valueString = convertCidrToBinary(value);
                let valueMask = value.split('/')[1];
                if (valueString.substring(+valueMask, valueString.length).indexOf('1') > -1) {
                    return callback('CIDR不合法');
                }
                if (checkCidrInDisable(value) || valueMask > 28) {
                    return callback('当前网段不可使用公网私用');
                }
                callback();
            }
        };
    }
    tagListRequster() {
        return this.$http.getSearchTagList({serviceType: [], region: ['global']});
    }
    onSegChange({value}) {
        let datasource = this.data.get('partDatasource')[value];
        this.data.set('cidr.part[1].value', value === Netseg.SEG10 ? '0' : datasource[0].value);

        let cidr = u.clone(this.data.get('cidr'));
        this.data.set('cidr', cidr);

        let mask = this.data.get('cidr.mask');
        this.nextTick(() => {
            const maskDatasource = this.data.get('maskDatasource');
            if (!u.find(maskDatasource, item => +item.value === +mask)) {
                this.data.set('cidr.mask', maskDatasource[0].value);
                this.onMaskChange({value: maskDatasource[0].value});
            }
        });

        this.validateCidr();
    }
    onPartChange() {
        this.validateCidr();
    }
    onMaskChange(e) {
        let value = +e.value;
        if (value <= Mask.Mask8) {
            this.data.set('cidr.part[1].disabled', true);
            this.data.set('cidr.part[1].value', '0');
        } else {
            this.data.set('cidr.part[1].disabled', false);
        }
        if (value > Mask.Mask16) {
            this.data.set('cidr.part[2].disabled', false);
        } else {
            this.data.set('cidr.part[2].disabled', true);
            this.data.set('cidr.part[2].value', '0');
        }
        if (value > Mask.Mask24) {
            this.data.set('cidr.part[3].disabled', false);
        } else {
            this.data.set('cidr.part[3].disabled', true);
            this.data.set('cidr.part[3].value', '0');
        }
        let seg = this.data.get('cidr.part[0].value');
        let datasource = getPartDatasource(seg, value);
        this.data.set(`partDatasource[${seg}]`, datasource);
        if (!u.find(datasource, item => +item.value === +seg)) {
            this.data.set('cidr.part[1].value', datasource[0].value);
        }
        this.validateCidr();
    }
    onPartFocus(e, partIndex) {
        let cidr = this.data.get('cidr');
        let result = getAvaliableByMask(cidr.mask, partIndex);
        this.data.set('cidr.tip', getAvaliableContent(result));
    }
    onPartBlur(e, partIndex) {
        this.data.set('cidr.tip', null);
        // 校验
        let mask = this.data.get('cidr.mask');
        let value = this.data.get(`cidr.part[${partIndex}].value`);
        let result = getAvaliableByMask(+mask, partIndex);
        this.data.set('cidr.error', u.indexOf(result, +value) > -1 ? '' : 'CIDR不合法');
        this.validateCidr();
    }
    validateCidr() {
        this.data.set('cidr.error', null);
        let flag = true;
        let cidr = this.data.get('cidr');
        let first = u.pluck(cidr.part, 'value').join('.') + '/' + cidr.mask;
        if (!new RegExp(rule.IP_CIDR).test(first)) {
            this.data.set('cidr.error', 'CIDR不合法');
            flag = false;
        }
        if (flag) {
            let valueString = convertCidrToBinary(first);
            let valueMask = first.split('/')[1];
            if (valueString.substring(+valueMask, valueString.length).indexOf('1') > -1) {
                this.data.set('cidr.error', 'CIDR不合法');
                flag = false;
            }
        }
        return flag;
    }
    async doSubmit() {
        if (FLAG.NetworkVpcSupOrganization) {
            const projectValid = this.ref('projectConfig').validComponentData();
            if (!projectValid) {
                return;
            }
        } else {
            try {
                await this.ref('tagPanel').validate(false);
                await this.ref('form').validateFields();
            } catch (error) {
                return;
            }
        }
        if (!this.validateCidr()) {
            return;
        }
        let externalPayload = {};
        if (FLAG.NetworkVpcSupOrganization) {
            externalPayload.resourceGroupIds = this.data.get('resourceGroupId');
        } else {
            let tags = await this.ref('tagPanel').getTags();
            externalPayload.tags = tags;
        }
        this.data.set('isCreating', true);
        // 子网部分参数
        if (this.data.get('table.datasource').length) {
            this.data.get('table.datasource').forEach((item, rowIndex) => {
                let cidr0 = this.data.get(`cidr0[${rowIndex}]`);
                let cidr1 = this.data.get(`cidr1[${rowIndex}]`);
                let cidr2 = this.data.get(`cidr2[${rowIndex}]`);
                let cidr3 = this.data.get(`cidr3[${rowIndex}]`);
                let mask = this.data.get(`mask[${rowIndex}]`);
                // 确认创建之前，确认所有子网都在用户最后输入的vpc cidr内
                if (!this.isPrivateNetwork(cidr0 + '.' + cidr1 + '.' + cidr2 + '.' + cidr3 + '/' + mask)) {
                    this.data.set(`cidrErr[${rowIndex}]`, '该子网并非在所创建私有网络内，请重新创建');
                } else {
                    this.data.set(`cidrErr[${rowIndex}]`, '');
                }
            });
            this.cidrCheck();
            let subnets = this.getSubnetPayload(this.data.get('table.datasource'));
            if (!subnets) {
                return;
            }
            externalPayload.subnets = subnets;
        }
        this.vpcCreate(externalPayload);
    }

    getSubnetPayload(tableDatasource) {
        let subnetList = [];
        for (let index = 0; index < tableDatasource.length; index++) {
            if (
                this.data.get(`cidrErr[${index}]`) ||
                this.data.get(`nameErr[${index}]`) ||
                this.data.get(`zoneErr[${index}]`)
            ) {
                return;
            }
            let cidr0 = this.data.get(`cidr0[${index}]`);
            let cidr1 = this.data.get(`cidr1[${index}]`);
            let cidr2 = this.data.get(`cidr2[${index}]`);
            let cidr3 = this.data.get(`cidr3[${index}]`);
            let mask = this.data.get(`mask[${index}]`);
            let cidr = cidr0 + '.' + cidr1 + '.' + cidr2 + '.' + cidr3 + '/' + mask;
            let subnet = {
                name: this.data.get(`name[${index}]`),
                az: this.data.get(`zone[${index}]`),
                cidr: cidr,
                subnetType: this.data.get(`subnetType[${index}]`),
                enableIpv6: this.data.get(`enableIpv6[${index}]`) || false,
                description: this.data.get(`description[${index}]`) || '',
                broadcast: this.data.get('inBroadcastWhiteList') ? 0 : -1
            };
            subnetList.push(subnet);
        }
        return subnetList;
    }

    vpcCreate(externalPayload = {}) {
        const {name, enableIpv6, description, resourceGroupId} = this.data.get('formData');
        let cidr = this.data.get('formData.cidr');
        let payload = {
            name,
            cidr,
            enableIpv6: !!enableIpv6,
            description,
            resourceGroupId,
            ...externalPayload
        };
        return this.$http
            .vpcCreate(payload)
            .then(() => {
                location.hash = '#/vpc/instance/list';
            })
            .catch(err => {
                this.data.set('isCreating', false);
            });
    }

    close() {
        location.hash = '#/vpc/instance/list';
    }
    resourceChange({value}) {
        this.data.set('formData.resourceGroupId', value.data.groupId);
    }
    async checkVpc10CidrOpen() {
        // 第一阶段开放fsh、fsg、hkg、su
        // 去掉白名单，改为黑名单控制
        let vpc10CidrOpen = true;
        const region = window.$context.getCurrentRegionId();
        if (u.contains(disableVpc10Cidr, region)) {
            vpc10CidrOpen = await this.$http.vpcCommonWhiteList({id: 'Vpc10CidrClose'}).then(data => !data);
            this.data.set('vpc10CidrOpen', vpc10CidrOpen);
            return;
        }
        this.data.set('vpc10CidrOpen', vpc10CidrOpen);
        return;
    }
    setSubnetTypeList() {
        // 设置所选类型数据源
        if (u.indexOf(this.data.get('availableService'), 'BBC') > -1) {
            this.data.push('subnetTypeList', ServiceType.fromAlias('BBC'));
        }
        const subnetTypes = this.data.get('subnetTypes');
        if (subnetTypes) {
            let subnetTypeList = this.data.get('subnetTypeList');
            subnetTypeList = u.filter(subnetTypeList, item => u.includes(subnetTypes, item.value));
            this.data.set('subnetTypeList', subnetTypeList);
        }
    }
    addRule() {
        let index = this.data.get('table.datasource').length;
        this.data.push('subnetType', this.data.get('subnetTypeList[0].value'));
        this.data.set(`cidr0[${index}]`, this.data.get('cidr0List[0].value'));
        this.data.set(`nameErr[${index}]`, '名称必填');
        this.data.set(`zoneErr[${index}]`, '可用区必选');
        this.data.push('table.datasource', {});
        this.setVpcCidrList(index);
        let subnetBatchCreateQuota = this.data.get('subnetBatchCreateQuota');
        this.data.set(
            'createSubnetBatch',
            index + 1 >= subnetBatchCreateQuota
                ? {
                      disable: true,
                      message: `最多批量创建${subnetBatchCreateQuota}个子网`
                  }
                : {
                      disable: false,
                      message: ''
                  }
        );
    }
    setVpcCidrList(rowIndex) {
        let formData = this.data.get('formData');
        if (rowIndex) {
            this.updateCidrData(rowIndex, formData.cidr);
            this.ipTotal(rowIndex);
        }
        if (!rowIndex) {
            this.nextTick(() => {
                let tableLength = this.data.get('table.datasource').length;
                if (tableLength) {
                    this.data.get('table.datasource').forEach((item, index) => {
                        this.data.set(`cidrErr[${index}]`, '');
                        this.data.set(`enableIpv6[${index}]`, false);
                        this.data.set(`vpcCidr[${index}]`, this.data.get('formData.cidr'));
                        this.updateCidrData(index, formData.cidr);
                        this.ipTotal(index);
                    });
                }
            });
        }
    }
    getSubnetMask(mask) {
        mask = +mask;
        const result = [];
        for (let i = mask; i <= 29; i++) {
            result.push({
                value: i,
                text: i
            });
        }
        return result;
    }
    ipTotal(rowIndex) {
        const num = 32 - this.data.get(`mask[${rowIndex}]`);
        let totalIp = 1;
        for (let i = 0; i < num; i++) {
            totalIp = totalIp * 2;
        }
        this.data.set(`ipTotalTip[${rowIndex}]`, `当前子网可用IP数${totalIp - 3}个`);
    }
    updateCidrData(rowIndex, cidr) {
        const array = cidr.split('/');
        const cidrPart = array[0].split('.');
        if (cidr === RouteSourceAll.ALL) {
            this.data.set('cidr0List', [Netseg.fromAlias('SEG192'), Netseg.fromAlias('SEG172')]);
            this.data.set(`cidr0[${rowIndex}]`, this.data.get('cidr0List[0].value'));
            this.data.set(`cidr1[${rowIndex}]`, 168);
            this.data.set(`cidr2[${rowIndex}]`, 0);
            this.data.set(`cidr3[${rowIndex}]`, 0);
            this.data.set('cidrMask', this.getSubnetMask(16));
            this.data.set(`mask[${rowIndex}]`, 24);
            this.data.set('maskList', this.data.get('cidrMask'));
            this.cidrCheck();
        } else {
            const cidr0Value = cidrPart[0] || 0;
            this.data.set('cidr0List', [
                {
                    text: cidr0Value,
                    value: cidr0Value
                }
            ]);
            this.data.set(`cidr0[${rowIndex}]`, this.data.get('cidr0List[0].value'));
            this.data.set(`cidr1[${rowIndex}]`, cidrPart[1] || 0);
            this.data.set(`cidr2[${rowIndex}]`, cidrPart[2] || 0);
            this.data.set(`cidr3[${rowIndex}]`, cidrPart[3] || 0);
            const vpcMask = +array[1];
            this.data.set('cidrMask', this.getSubnetMask(vpcMask) || []);
            const final = this.data.get('cidrMask').length;
            if (vpcMask < 23) {
                this.data.set(`mask[${rowIndex}]`, 24);
            } else if (vpcMask < this.data.get(`cidrMask[${final - 1}].value`)) {
                this.data.set(`mask[${rowIndex}]`, vpcMask + 1);
            } else {
                this.data.set(`mask[${rowIndex}]`, this.data.get(`cidrMask[${final - 1}].value`));
            }
            this.data.set('maskList', this.data.get('cidrMask'));
            this.updateCidrInput(rowIndex, this.data.get(`mask[${rowIndex}]`), cidr);
        }
    }
    cidrCheck() {
        let datasource = this.data.get('table.datasource');
        let tableCidrList = [];
        datasource.forEach((item, index) => {
            const cidr0 = this.data.get(`cidr0[${index}]`);
            const cidr1 = this.data.get(`cidr1[${index}]`);
            const cidr2 = parseInt(this.data.get(`cidr2[${index}]`), 10);
            const cidr3 = parseInt(this.data.get(`cidr3[${index}]`), 10);
            const cidr = cidr0 + '.' + cidr1 + '.' + cidr2 + '.' + cidr3 + '/' + this.data.get(`mask[${index}]`);
            tableCidrList.push(cidr);
        });
        // 校验表格内的网段是否有重叠
        if (tableCidrList.length) {
            if (tableCidrList.length > 1) {
                tableCidrList.forEach((item, index, arr) => {
                    let overlapSubnet = arr.filter((sub, subIndex) => {
                        // 重置校验结果
                        this.data.set(`cidrErr[${index}]`, '');
                        // 不和自己本身校验
                        if (index === subIndex) {
                            return false;
                        } else {
                            return checkIsInSubnet(item, sub) || checkIsInSubnet(sub, item);
                        }
                    });

                    if (overlapSubnet && overlapSubnet.length) {
                        let subnetStr = '';
                        if (overlapSubnet.length > 5) {
                            overlapSubnet = overlapSubnet.slice(0, 5);
                            subnetStr = overlapSubnet.map(item => '（' + item + '）').join('<br>') + '...';
                        } else {
                            subnetStr = overlapSubnet.map(item => '（' + item + '）').join('<br>');
                        }
                        this.data.set(
                            `cidrErr[${index}]`,
                            `该子网cidr与当前网段冲突，冲突网段：${overlapSubnet.length > 1 ? '<br>' : ''}${subnetStr}`
                        );
                    } else {
                        this.data.set(`cidrErr[${index}]`, '');
                    }
                });
            } else {
                this.data.set(`cidrErr[${0}]`, '');
            }
        }
    }
    updateCidrInput(rowIndex, mask, cidr) {
        if (!cidr) {
            cidr = this.data.get('vpcCidr[0]');
        }
        const vpcCidrArray = cidr.split('/');
        const vpcMask = parseInt(vpcCidrArray[1], 10);
        const subnetMask = parseInt(mask, 10);
        const vpcMaskNum = getMaskNum(vpcMask);
        const subnetMaskNum = getMaskNum(subnetMask);
        const cidrPart = vpcCidrArray[0].split('.');

        for (let i = 1; i < 4; i++) {
            if (i <= vpcMaskNum) {
                if (i === vpcMaskNum && vpcMask < (i + 1) * 8 && vpcMask !== subnetMask) {
                    this.data.set(`disableCidr${i}`, false);
                } else {
                    this.data.set(`disableCidr${i}`, true);
                    this.data.set(`formCidr${i}`, cidrPart[i]);
                }
            } else if (i > vpcMaskNum && i <= subnetMaskNum) {
                this.data.set(`disableCidr${i}`, false);
            } else if (i > subnetMaskNum) {
                this.data.set(`disableCidr${i}`, true);
                this.data.set(`formCidr${i}`, 0);
            }
        }
        if (!this.data.get(`cidrErr[${rowIndex}]`)) {
            this.cidrCheck();
        }
    }
    getCidrByRowIndex(rowIndex) {
        const cidr0 = this.data.get(`cidr0[${rowIndex}]`);
        const cidr1 = this.data.get(`cidr1[${rowIndex}]`);
        const cidr2 = this.data.get(`cidr2[${rowIndex}]`);
        const cidr3 = this.data.get(`cidr3[${rowIndex}]`);
        const cidr = cidr0 + '.' + cidr1 + '.' + cidr2 + '.' + cidr3 + '/' + this.data.get(`mask[${rowIndex}]`);
        return cidr;
    }

    validateCidrLegal(rowIndex: number) {
        const rowCidr = this.getCidrByRowIndex(rowIndex);
        const valueString = convertCidrToBinary(rowCidr);
        const mask = this.data.get(`mask[${rowIndex}]`);
        if (valueString.substring(+mask, valueString.length).includes('1')) {
            this.data.set(`cidrErr[${rowIndex}]`, 'CIDR不合法');
        }

        return !valueString.substring(+mask, valueString.length).includes('1');
    }
    getAvailableInput = (rowIndex: number, cidrIndex: number) => {
        const mask = this.data.get(`mask[${rowIndex}]`);
        const maskList = this.data.get('maskList');
        const vpcCidr = this.data.get('formData.cidr');
        let result = [];
        if (cidrIndex === 1) {
            const cidr0 = this.data.get(`cidr0[${rowIndex}]`);
            result = getDifferenceAvaliableNew(mask, maskList, cidrIndex, vpcCidr, cidr0);
        } else {
            result = getDifferenceAvaliableNew(mask, maskList, cidrIndex, vpcCidr);
        }
        return result;
    };

    cidrInput(rowIndex, index, e) {
        this.data.set('needValidate', true);
        // 如果存在不合法的校验直接返回
        const cidrErrIndex = this.data.get('cidrErrIndex');
        const isExistErr = this.data.get(`cidrErr[${rowIndex}]`);
        if (isExistErr === 'CIDR不合法' && cidrErrIndex !== index) {
            return;
        }
        if (!e.value) {
            this.data.set(`cidrErr[${rowIndex}]`, '不能为空');
        } else if (!/^[0-9]*$/.test(e.value)) {
            this.data.set(`cidrErr[${rowIndex}]`, '请填写数字');
        } else if (index === 1) {
            const result = this.getAvailableInput(rowIndex, index);
            if (u.indexOf(result, parseInt(e.value, 10)) > -1) {
                this.data.set(`cidr${index}[${rowIndex}]`, Number(e.value));
                const isCidrLegal = this.validateCidrLegal(rowIndex);
                if (isCidrLegal) {
                    this.data.set(`cidrErr[${rowIndex}]`, '');
                    this.cidrCheck();
                }
            } else {
                this.data.set(`cidr${index}[${rowIndex}]`, Number(e.value));
                this.data.set('cidrErrIndex', index);
                this.data.set(`cidrErr[${rowIndex}]`, 'CIDR不合法');
            }
        } else if (index === 2 || index === 3) {
            const result = this.getAvailableInput(rowIndex, index);
            if (u.indexOf(result, parseInt(e.value, 10)) > -1) {
                this.data.set(`cidr${index}[${rowIndex}]`, Number(e.value));
                const isCidrLegal = this.validateCidrLegal(rowIndex);
                if (isCidrLegal) {
                    this.data.set(`cidrErr[${rowIndex}]`, '');
                    this.cidrCheck();
                }
            } else {
                this.data.set(`cidr${index}[${rowIndex}]`, Number(e.value));
                this.data.set('cidrErrIndex', index);
                this.data.set(`cidrErr[${rowIndex}]`, 'CIDR不合法');
            }
        }
    }
    ipv6Change(e) {
        let tableLength = this.data.get('table.datasource').length;
        if (!e.value && tableLength) {
            this.data.get('table.datasource').forEach((item, index) => {
                this.data.set(`enableIpv6[${index}]`, false);
            });
        }
    }
    dataChange(type, rowIndex, e) {
        this.data.set(`${type}[${rowIndex}]`, e.value);
        if (type === 'zone' && e.value) {
            this.data.set(`zoneErr[${rowIndex}]`, '');
        }
    }
    cidr0Change(rowIndex, e) {
        this.data.set('needValidate', true);
        // 当有环回地址时切换再更新cidr1
        let cidr = this.data.get(`vpcCidr${rowIndex}`);
        if (!this.data.get('disableCidr0') && cidr === RouteSourceAll.ALL) {
            const mask = this.data.get(`mask${rowIndex}`);
            const value = getPartDatasource(e.value, mask)[0].value;
            this.data.set(`cidr1${rowIndex}`, value);
        }
    }
    maskChange(rowIndex, e) {
        this.data.set('needValidate', true);
        this.data.set(`mask[${rowIndex}]`, e.value);
        const cidr = this.getCidrByRowIndex(rowIndex);
        var reg = new RegExp(RULE.IP_CIDR);
        let valueString = convertCidrToBinary(cidr);
        if (!reg.test(cidr)) {
            this.data.set(`cidrErr[${rowIndex}]`, 'CIDR不合法');
        } else if (valueString.substring(+e.value, valueString.length).indexOf('1') > -1) {
            this.data.set(`cidrErr[${rowIndex}]`, 'CIDR不合法');
        } else if (!this.isPrivateNetwork(cidr)) {
            this.data.set(`cidrErr[${rowIndex}]`, 'CIDR不合法');
        } else {
            this.data.set(`cidrErr[${rowIndex}]`, '');
        }
        this.ipTotal(rowIndex);
        this.updateCidrInput(rowIndex, e.value);
    }
    cidrFocus(index, rowIndex) {
        const cidr = this.data.get('formData.cidr');
        const mask = this.data.get(`mask[${rowIndex}]`);
        const maskList = this.data.get('maskList');
        if (index === 1) {
            const cidr0 = this.data.get(`cidr0[${rowIndex}]`);
            const result = getDifferenceAvaliableNew(mask, maskList, index, cidr, cidr0);
            this.data.set(`cidrTip[${rowIndex}]`, getAvaliableContent(result));
            this.data.set(`showCidrTip[${rowIndex}]`, true);
        } else {
            const result = getDifferenceAvaliableNew(mask, maskList, index, cidr);
            this.data.set(`cidrTip[${rowIndex}]`, getAvaliableContent(result));
            this.data.set(`showCidrTip[${rowIndex}]`, true);
        }
        const isExistErr = this.data.get(`cidrErr[${rowIndex}]`);
        if (!isExistErr) {
            this.cidrInput(rowIndex, index, {value: this.data.get(`cidr${index}[${rowIndex}]`)});
        }
    }

    cidrBlur(rowIndex) {
        this.data.set(`showCidrTip[${rowIndex}]`, false);
    }
    dataInput(type, rowIndex, e) {
        if (type === 'name') {
            if (!e.value) {
                this.data.set(`nameErr[${rowIndex}]`, '名称必填');
            } else if (e.value === 'default') {
                this.data.set(`nameErr[${rowIndex}]`, '不能为默认名称');
            } else if (!/^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/.test(e.value)) {
                this.data.set(`nameErr[${rowIndex}]`, '名称不符合规则');
            } else {
                this.data.set(`nameErr[${rowIndex}]`, '');
            }
        }
        this.data.set(`${type}[${rowIndex}]`, e.value);
    }
    deleteRule(index) {
        this.data.set('needValidate', true);
        this.nextTick(() => {
            let tableLength = this.data.get('table.datasource').length;
            let subnetBatchCreateQuota = this.data.get('subnetBatchCreateQuota');
            this.data.set(
                'createSubnetBatch',
                tableLength > subnetBatchCreateQuota
                    ? {
                          disable: true,
                          message: `最多批量创建${subnetBatchCreateQuota}个子网`
                      }
                    : {
                          disable: false,
                          message: ''
                      }
            );
        });
        let editItem = [
            'name',
            'zone',
            'subnetType',
            'enableIpv6',
            'description',
            'cidr0',
            'cidr1',
            'cidr2',
            'cidr3',
            'mask'
        ]; //eslint-disable-line
        this.data.splice('tableCidrList', [index, 1]);
        this.data.splice('table.datasource', [index, 1]);
        editItem.forEach(item => {
            this.data.splice(`${item}`, [index, 1]);
            this.data.splice(`${item}Err`, [index, 1]);
        });
        this.data.splice('cidrErr', [index, 1]);
        this.cidrCheck();
    }
    // 判断子网cidr是否在vpc的cidr内 （v4)
    isPrivateNetwork(cidr) {
        let vpcCidr = this.data.get('formData.cidr');
        const vpcParts = vpcCidr.split('/');
        const vpcIp = vpcParts[0];
        const vpcMask = parseInt(vpcParts[1]);
        const subnetParts = cidr.split('/');
        const subnetIp = subnetParts[0];
        const vpcInt = this.intFromIp(vpcIp);
        const subnetInt = this.intFromIp(subnetIp);
        const vpcMaskInt = (Math.pow(2, 32) - Math.pow(2, 32 - vpcMask)) >>> 0;
        return (subnetInt & vpcMaskInt) === (vpcInt & vpcMaskInt);
    }
    intFromIp(ip) {
        let result = 0;
        ip.split('.').forEach((part, i, arr) => {
            result |= parseInt(part) << (24 - i * 8);
        });
        return result >>> 0;
    }
    getIamQuery() {
        this.$http.getInterfaceIam({interfaceName: 'createVpc'}).then(res => {
            if (!res.requestId && !res.masterAccount) {
                if (!res.interfacePermission) {
                    this.data.set('iamPass', {
                        disable: true,
                        message: '您没有创建私有网络权限，请联系主用户添加'
                    });
                } else {
                    this.data.set('iamPass', {
                        disable: false,
                        message: ''
                    });
                }
            } else {
                this.data.set('iamPass', {
                    disable: false,
                    message: ''
                });
            }
        });
    }
    showAssist() {
        Assist.sendMessageToAssist({sceneLabel: 'vpc_create', message: 'IPv4 CIDR填写的基本规则是什么？'});
    }
    // 检查配额
    async checkQuota() {
        const data = await this.$http.vpcQuota({});
        const {createVpc} = checker.check(rules, data, 'createVpc', {quota: data.free});
        this.data.set('createVpc', createVpc);
    }
}
export default San2React(Processor.autowireUnCheckCmpt(InstanceCreate));
