/**
 * @file vpc实例操作禁用配置
 * <AUTHOR>
 */
import u from 'lodash';
import {ContextService} from '@/pages/sanPages/common';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
const AllRegion = ContextService.getEnum('AllRegion');

const currentModule = 'VPC';
export default {
    createVpc: [
        {
            required: false
        },
        {
            custom(data, options = {}) {
                if (window.$context.getCurrentRegionId() === AllRegion.HK02) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: '该地域暂不支持该功能'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `该地域暂不支持该功能。如需使用该功能，请提交<a href="${
                                ContextService.Dmains.ticket
                            }/#/ticket/create" target="_blank">工单</a>`
                        };
                    }
                } else if (options.quota <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: 'VPC配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `VPC配额不足，<a href="${
                                '/quota_center/#/quota/apply/create?serviceType='
                            }${currentModule}&region=${
                                window?.$context?.getCurrentRegionId()
                            }&cloudCenterQuotaName=vpcQuota" target="_blank">去申请配额</a>`
                        };
                    }
                }
            }
        }
    ],
    deleteVpc: [
        {
            required: true,
            message: '请先选中实例'
        },
        {
            custom(data) {
                if (u.some(data, item => item.defaultVpc)) {
                    return {
                        disable: true,
                        message: '默认私有网络不支持删除'
                    };
                }
                if (u.some(data, item => item.csnId)) {
                    return {
                        disable: true,
                        message: '删除已添加或已授权的VPC，请至CSN侧取消关联或授权后，再删除。'
                    };
                }
            }
        }
    ],
    editTag: [
        {
            required: true,
            message: '请先选中实例'
        }
    ],
    changeResource: [
        {
            required: true,
            message: '请先选中实例'
        }
    ],
    vpcSinDisable: [
        {
            required: false
        },
        {
            custom(data) {
                if (window.$context.getCurrentRegionId() === AllRegion.SIN) {
                    return {
                        disable: true,
                        message: '新加坡地域资源售罄，请您切换到其他地域创建'
                    };
                }
            }
        }
    ]
};
