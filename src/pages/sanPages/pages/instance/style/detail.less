.vpc-instance-detail {
    min-height: 100%;
    width: 100%;
    .tip-icon {
        color: #9e9898;
        border: 1px solid #9e9898;
        margin-left: 10px;
        &:hover {
            border-color: #2468f2;
        }
    }
    .s-detail-page-content {
        background: #f7f7f7;
        margin: 0;
        padding: 0 16px;
    }
    .title_class {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .tltle_left {
            display: flex;
            align-items: center;
        }
        .iconfont {
            font-size: 14px;
            color: #84868c;
        }
    }
    .float_right {
        float: right;
    }
    .vpc-instance-head {
        line-height: 50px;
        .backbox {
            height: 28px;
            line-height: 28px;
            border: 1px solid #ccc;
            outline: none;
            font-size: 12px;
            font-weight: 400;
            cursor: pointer;
            text-align: center;
            vertical-align: middle;
            color: #000;
            background-color: #fff;
            font-family:
                Microsoft Yahei,
                微软雅黑,
                Tahoma,
                Arial,
                Helvetica,
                STHeiti;
            display: inline-block;
            width: 20px;
            .iconfont {
                font-size: 12px;
            }
        }
        span {
            vertical-align: middle;
        }
        button {
            margin: 0 20px;
        }
        .vpc-name {
            font-weight: bold;
            margin: 0 10px;
        }
    }
    .content {
        margin-top: 16px;
        padding: 24px;
        background-color: #fff;
        border-radius: 6px;
        h4 {
            display: block;
            color: #151b26;
            line-height: 24px;
            font-weight: 500;
            margin-bottom: 16px;
            font-size: 16px;
        }
        .cell {
            width: 33%;
            display: inline-block;
            margin-bottom: 16px;
            vertical-align: top;
            .cell-title {
                display: inline-block;
                vertical-align: top;
                color: #5e626a;
                margin-right: 16px;
                width: 100px;
            }
            .cell-content {
                display: inline-flex;
                color: #151a26;
                max-width: 80%;
                word-break: break-all;
                position: relative;
            }
            .icon-edit,
            .icon-copy {
                color: #2468f2;
                font-size: 12px;
                margin-left: 10px;
            }
        }
        .main {
            h5 {
                font-size: 14px;
                font-weight: bold;
                margin-top: 25px;
            }
            .list-wrap {
                display: flex;
                flex-wrap: wrap;
                > a {
                    display: block;
                    width: calc(~'20% - 20px');
                    background: #f7f7f7;
                    margin: 20px 20px 0 0;
                    padding: 10px 20px;
                    box-sizing: border-box;
                    display: flex;
                    border-radius: 6px;
                    justify-content: space-between;
                    span:nth-child(1) {
                        color: #000;
                    }
                }
            }
        }
    }
    .popover-change {
        color: #2468f2;
        margin: 0 0 4px 4px;
        cursor: pointer;
    }
    .content_bottom {
        margin-bottom: 16px;
    }
    .name-icon {
        position: relative;
        top: -2px;
        fill: #2468f2;
        color: #2468f2;
    }
    .tip-margin {
        height: 18px;
        margin-left: 8px;
        position: relative;
        top: 3px;
    }
    .detail-part-item {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }
    .inline_class {
        display: inline-flex;
        align-items: center;
    }
    .s-tip {
        justify-content: center;
        .warning_class {
            fill: #999;
        }
    }
    .vpc-name {
        font-size: 16px;
        font-weight: 500;
        color: #151b26;
        padding-left: 16px;
        padding-right: 12px;
    }
}

.vpc-detail-pop {
    .pop-text {
        word-break: break-all;
        width: 190px;
    }
}

.edit-name-warp {
    margin-bottom: 40px;
    .s-button {
        float: right;
        margin: 10px 5px;
    }
    .error-message {
        margin-left: 5px;
        color: #eb5252;
    }
    .edit-name-tip {
        margin-top: 10px;
        color: #83868c;
    }
}

.no-instance-content {
    .s-detail-page-content {
        padding: 0px !important;
    }
}
