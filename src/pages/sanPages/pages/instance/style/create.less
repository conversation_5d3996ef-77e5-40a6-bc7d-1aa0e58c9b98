.vpc-instance-create {
    font-size: 12px;
    h4 {
        font-size: 16px;
        color: #151b26;
        font-weight: 500;
        margin-top: 0;
        &:first-child {
            margin-top: 0;
        }
    }
    .title_vpc {
        font-size: 14px;
        font-weight: 400;
    }
    .s-form {
        .s-form-item {
            margin: 24px 0 0 !important;
        }
        .s-form-item-label {
            width: 120px;
            .inline-tip {
                top: 3px;
                position: relative;
                .s-tip-warning {
                    justify-content: center;
                    .warning_class {
                        fill: #999;
                    }
                }
                .s-tip:hover .s-icon path {
                    fill: #2468f2 !important;
                }
            }
        }
        .center_class {
            .s-row {
                .s-form-item-control-wrapper {
                    line-height: 32px;
                }
            }
        }
        .inline-item {
            display: inline-block;
            .s-form-item-control {
                & > .s-input input {
                    width: 28px !important;
                }
            }
        }
        .note {
            margin-top: 5px;
            font-size: 12px;
            color: #999;
        }
        .tip {
            font-size: 12px;
            color: #999;
            line-height: 18px;
            margin-top: 5px;
        }
        .cidr-error {
            margin-top: 5px;
            color: #eb5252;
        }
        .cidr-tip {
            margin-top: 5px;
            color: #999;
        }
        .label_class {
            .inline-tip {
                top: 3px;
                position: relative;
                .s-tip-warning {
                    justify-content: center;
                    .warning_class {
                        fill: #999;
                    }
                }
                .s-tip:hover .s-icon path {
                    fill: #2468f2 !important;
                }
            }
        }
        .resouce-group-select .resouce-group-select-main > label {
            width: 121px;
            color: #5e626a;
            font-size: 12px;
            line-height: 30px;
            white-space: nowrap;
        }
        .resource-form-part-wrap {
            h4 {
                border: none;
                padding-left: 0;
                font-size: 14px;
                font-weight: 400;
            }
            .resource-group-panel {
                border: none;
                padding: 0;
            }
            margin: 10px auto 0 auto;
        }
        .s-form-item-help {
            padding-bottom: 0;
        }
        .s-form-item-label-left {
            width: 121px;
        }
    }
    .s-row-flex {
        .s-form-item-control-wrapper {
            flex: 1;
        }
    }
    .cidr-required {
        .s-form-item:nth-child(3) {
            .s-form-item-label-left {
                & > label::before {
                    left: -7px;
                    position: absolute;
                    content: '*';
                    color: #f33e3e;
                    margin-right: 4px;
                }
            }
        }
    }
    .resource-group-panel {
        padding: 0px;
        border: 0px;
        .footer {
            display: flex;
            margin-left: 118px;
        }
    }
    .assign_ipv6 {
        .s-form-item-label,
        .s-form-item-control-wrapper {
            height: 32px;
        }
    }
    .tag-edit-panel {
        .s-form-item {
            margin: 0 16px 24px 0 !important;
        }
        .footer {
            .s-button {
                padding: 0;
                background: white;
                color: #2468f2;
                .icon-plus {
                    color: #2468f2;
                    fill: #2468f2;
                }
            }
            .s-button:hover {
                border-color: white;
            }
            .float-right {
                float: none;
            }
        }
        .footer > * {
            margin-right: 16px;
        }
    }
}

.tag-edit-panel {
    .bui-textbox-input-area input {
        font-size: 12px;
    }
}
