@bce-error-color: #eb5252;
.vpc-instance-list {
    min-height: 100%;
    background: #f7f7f9 !important;
    .instance-title {
        background-color: #f7f7f9;
        .instance-title-widget {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 47px !important;
            line-height: 47px !important;
            font-weight: 500 !important;
            font-size: 16px !important;
            padding-left: 16px !important;
            color: #151b26 !important;
            background-color: #ffffff;
            .help-file {
                margin-right: 16px;
                float: right;
                font-size: 12px;
                font-weight: 400;
                .s-icon {
                    position: relative;
                    top: -1px;
                    right: 4px;
                    font-size: 14px;
                    margin-right: 0;
                    color: #2468f2;
                }
            }
        }
    }
    flex: 1;
    height: 100%;
    background: #f7f7f9 !important;
    overflow-y: scroll;
    .s-biz-page-header {
        margin: 0px !important;
        background-color: #fff;
        height: auto !important;
        border-bottom: none !important;
        .s-biz-page-title {
            h2 {
                height: 47px !important;
                line-height: 47px !important;
                font-weight: 500 !important;
                font-size: 16px !important;
                margin-left: 16px !important;
                color: #151b26 !important;
            }
        }
    }
    .s-biz-page-content {
        background-color: #fff;
        border-radius: 6px !important;
        margin: 16px !important;
        padding: 24px;
        .s-biz-page-toolbar {
            margin: 0px;
        }
        .s-biz-page-body {
            margin-top: 16px;
            .s-table {
                .s-table-body {
                    max-height: calc(~'100vh - 284px');
                }
            }
        }
        .s-biz-page-footer {
            display: none;
        }
    }
    .list-page-tb-left-toolbar {
        display: inline-flex;
    }
    .toolbar_right {
        display: inline-flex;
    }
    .quota-tip {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 5px;
        color: #f38900;
        background: #fcf7f1;
    }
    .s-cascader {
        font-size: 0;
    }
    .s-cascader-value {
        vertical-align: middle;
        border: none;
        font-size: 12px;
        padding-top: 0;
        padding-bottom: 0;
        line-height: 30px;
    }
    .s-table {
        .s-table-row:hover {
            .name-icon {
                display: inline;
            }
        }
        .name-icon {
            display: none;
            font-size: 12px;
            fill: #2468f2;
        }
        .operation {
            .s-button {
                padding: 0;
                margin-right: 12px;
            }
        }
        .router_layer {
            display: flex;
            align-items: center;
            .inline-tip {
                top: 3px;
                left: 5px;
                position: relative;
                .s-tip-warning {
                    justify-content: center;
                    .warning_class {
                        fill: #999;
                    }
                }
                .s-tip:hover .s-icon path {
                    fill: #2468f2 !important;
                }
            }
        }
    }
}
.locale-en {
    .vpc-instance-list {
        .s-table {
            overflow: auto !important;
            .s-table-content {
                min-width: 1200px;
            }
        }
    }
    .network-instance-manage-cidr {
        .s-form-item-label {
            width: 160px;
        }
    }
}
.network-instance-manage-cidr {
    font-size: 12px;

    .s-col {
        line-height: 30px;
    }

    .s-form-item-label {
        width: 100px;
    }
    .s-form {
        .s-form-item-label {
            width: 118px;
        }
        .center_class {
            .s-row {
                align-items: center;
            }
        }
    }
    .cidr-item {
        .cidr-input {
            width: 210px;
            .s-input-area {
                input {
                    width: 210px !important;
                }
            }
        }
    }
    .operation {
        margin-bottom: 10px;

        .iconfont {
            font-size: 12px;
        }
    }

    .delete-item {
        color: #999;
        vertical-align: middle;
    }

    .item-tip {
        margin-top: 5px;
        color: #999;
    }

    .cidr-item {
        margin-top: 10px;
    }

    .item-error {
        margin-top: 5px;
        color: @bce-error-color;
    }
    .error-input {
        border-color: #eb5252;
    }
    .error-input:hover {
        border-color: #eb5252;
    }
}
.s-table .s-table-container-scroll .s-table-header {
    margin-bottom: 0 !important;
}

.truncated {
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: bottom;
    max-width: calc(100% - 20px);
}
.instance-cidr-dropdown {
    .s-popover-body {
        .s-popover-content {
            max-width: 264px;
            max-height: 360px;
            padding: 12px !important;
            overflow: auto;
        }
    }
    .instance-cidr-num {
        color: #2468f2;
    }
    .instance-cidr-item {
        line-height: 20px;
        margin-top: 8px;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #151b26;
        line-height: 20px;
        font-weight: 400;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        &:first-child {
            margin-top: 0;
        }
    }
}
