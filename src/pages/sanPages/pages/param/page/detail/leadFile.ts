/*
 * @Description: 导入组件
 */

import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import SheetJS from './SheetJS';
import regs from '@/pages/sanPages/utils/rule';
import {checkIpv6Cidr} from '@/pages/sanPages/utils/common';

import '../../style/leadFile.less';

const {asComponent, invokeSUI, template} = decorators;

const rules = {
    checkIpAddress(source, ethertype) {
        // 校验ip的正确性
        if (ethertype === 'IPv4') {
            var reg = new RegExp(regs.IP_CIDR);
            source = source?.trim();
            if (!reg.test(source)) {
                return false;
            }
            return true;
        } else if (ethertype === 'IPv6') {
            if (!checkIpv6Cidr(source)) {
                return false;
            }
            return true;
        }
        return false;
    },
    checkDesc(desc) {
        // 描述的最大长度200
        if (desc.length > 200) {
            return false;
        }
        return true;
    },
    checkSameAddress(address1, address2, ethertype) {
        // 比较的时候 都默认添加suffix后缀。
        var suffix = ethertype === 'IPv6' ? '/128' : '/32';
        if (address1.indexOf('/') < 0) {
            address1 += suffix;
        }
        if (address2.indexOf('/') < 0) {
            address2 += suffix;
        }
        return address1 === address2;
    },
    checkIsExist(allRules, rule, ethertype) {
        let flag = false;
        for (let i = 0; i < allRules.length; i++) {
            let curRule = allRules[i];
            // 只需要验证IP是否相同
            if (this.checkSameAddress(curRule.ipAddress, rule.ipAddress, ethertype)) {
                flag = true;
                break;
            }
        }
        return flag;
    }
};

const tableSchema = [
    {name: 'ipAddress', label: 'IP地址'},
    {name: 'description', label: '描述'},
    {name: 'status', label: '检查'}
];

const tpl = html`
    <div>
        <s-dialog
            width="600"
            class="ipaddress-lead-file"
            closeAfterMaskClick="{{false}}"
            open="{=open=}"
            title="{{'导入规则'}}"
        >
            <div class="tip-grey">{{quotaTips}}</div>
            <s-form>
                <s-form-item label="选择文件：">
                    <xui-sheetjs s-ref="upload" on-upload="uploadEvent" statusShow="{{false}}"> </xui-sheetjs>
                    <div s-if="{{uploading || fileName || uploadSuccess || uploadFail}}" class="upload-message">
                        <div class="upload-file-name">
                            <s-icon name="link"></s-icon>
                            <div s-if="{{uploading}}" class="upload-progress">
                                <s-progress value="{{progressValue}}" width="80"></s-progress>
                            </div>
                            <span s-if="{{!uploading && fileName}}">{{ fileName }}</span>
                        </div>
                        <span s-if="{{uploadSuccess}}" class="upload-success">{{ '上传成功!'}}</span>
                        <span s-if="{{uploadFail}}" class="upload-fail">{{ '文档解析失败'}}</span>
                        <s-button s-if="{{uploadFail}}" skin="stringfy" on-click="reUpload">
                            <s-icon name="bcmrefresh"></s-icon>
                            {{ '重试'}}
                        </s-button>
                        <s-button class="delete-file" skin="stringfy" on-click="clearUploader">
                            <s-icon name="close"></s-icon>
                        </s-button>
                    </div>
                </s-form-item>
                <s-form-item s-if="{{fileUploaded}}">
                    {{ '查看规则'}}
                    <div class="rule-message">
                        <span>{{ '共' + ruleTotal + '条规则' }}</span>
                        <span s-if="{{ruleWrongTotal}}" class="wrong-rules">
                            {{ '其中' + ruleWrongTotal + '条出错，请修改后重试' }}
                        </span>
                    </div>
                </s-form-item>

                <s-form-item s-if="{{ruleIsShow}}">
                    <s-table
                        columns="{{ruleList.schema}}"
                        loading="{{ruleList.loading}}"
                        datasource="{{ruleList.datasource}}"
                    >
                        <div slot="empty">{{ '暂无数据'}}</div>
                        <div slot="c-ipAddress">
                            <span class="{{row | getClass('ip')}}">{{ row.ipAddress }}</span>
                        </div>
                        <div slot="c-description">
                            <span class="{{row | getClass('desc')}}">{{ row.description || '-' }}</span>
                        </div>
                        <div slot="c-status">
                            <div class="status-warp" s-if="{{row.status}}"><s-icon name="ok-reverse"></s-icon></div>
                            <div class="status-warp" s-else>
                                <s-tooltip s-if="{{row.statusMessage}}" content="{{row.statusMessage}}" position="tc">
                                    <s-icon name="fail-reverse"></s-icon>
                                </s-tooltip>
                                <s-icon s-else name="fail-reverse"></s-icon>
                            </div>
                        </div>
                    </s-table>
                </s-form-item>
            </s-form>
            <div slot="footer">
                <div style="padding-left: 300px; padding-top:20px; display: inline-block;">
                    <span class="repeat_class" s-if="repeatTip">{{ repeatTip }}</span>
                    <s-tooltip class="lead-tooltip" trigger="{{disableMessage ? 'hover' : ''}}" placement="right">
                        <!--bca-disable-next-line-->
                        <div slot="content">{{disableMessage | raw}}</div>
                        <s-button disabled="{{leadDisable || limitDisable}}" skin="primary" on-click="leadSubmit">
                            {{ '确定'}}
                        </s-button>
                    </s-tooltip>
                    <s-button size="larger" on-click="leadCancel"> {{ '取消'}} </s-button>
                </div>
            </div>
        </s-dialog>
    </div>
`;

@asComponent('@ipaddress-lead-in')
@template(tpl)
@invokeSUI
class ipAddressLeadIn extends Component {
    static components = {
        'xui-sheetjs': SheetJS
    };
    initData() {
        return {
            open: false,
            ruleIsShow: false,
            ruleList: {
                datasource: [],
                schema: tableSchema,
                loading: false
            },
            ruleTotal: 0,
            ruleWrongTotal: 0,
            progressValue: 0,
            fileName: '',
            uploading: false,
            uploadSuccess: false,
            uploadFail: false,
            fileUploaded: false,
            leadDisable: false,
            limitDisable: false,
            freeIpNum: 20
        };
    }

    static filters = {
        getClass(row, type) {
            if (type === 'ip') {
                return !rules.checkIpAddress(row.ipAddress, this.data.get('ethertype')) ? 'table-wrong' : '';
            }
            return !rules.checkDesc(row.description) ? 'table-wrong' : '';
        }
    };

    static computed = {
        quotaTips() {
            return `IP地址的规则不超过${this.data.get('freeIpNum')}条，超过数量不允许导入，相同规则不允许重复导入。`;
        }
    };

    /**
     * 上传事件
     *
     * @param {Object} e 事件参数对象
     * @param {string} e.eventType 事件名称
     */
    uploadEvent(e) {
        let eventType = e.eventType;
        let result = e.result;
        if (eventType === 'uploadStart') {
            this.clearUploader();
            this.data.set('fileName', result.name);
        } else if (eventType === 'uploadProgress') {
            let precent = ((result.loaded / result.total) * 100).toFixed(0);
            this.data.set('uploading', true);
            this.data.set('progressValue', precent);
        } else if (eventType === 'uploadSuccess') {
            this.data.set('uploading', false);
            this.data.set('uploadSuccess', true);
            this.getRuleCheckList(result);
        } else if (eventType === 'uploadError') {
            this.uploadError(e);
        }
    }

    /**
     * 上传出错
     *
     * @param {Object} e 事件参数对象
     */
    uploadError(e) {
        this.data.set('uploading', false);
        this.data.set('uploadSuccess', false);
        this.data.set('uploadFail', true);
    }

    /**
     * 重新上传
     */
    reUpload() {
        (this.ref('upload') as any).retry();
    }

    /**
     * 清空当前文件
     */
    clearUploader() {
        this.data.set('ruleIsShow', false);
        this.data.set('ruleList.datasource', []);
        this.data.set('ruleTotal', 0);
        this.data.set('ruleWrongTotal', 0);
        this.data.set('progressValue', 0);
        this.data.set('fileName', '');
        this.data.set('uploading', false);
        this.data.set('uploadSuccess', false);
        this.data.set('uploadFail', false);
        this.data.set('fileUploaded', false);
        // 清除上一次文件的错误信息
        this.data.set('leadDisable', false);
        this.data.set('disableMessage', '');
        this.data.set('limitDisable', false);
        this.data.set('limitMessage', '');
        (this.ref('upload') as any).reset();
        this.fire('enableBtnOK', false);
    }

    /**
     * 得到规则列表
     *
     * @param {string} result 规则列表
     */
    getRuleCheckList(result) {
        let num = 0;
        let allRules = this.data.get('allRules');
        let addAllRules = [];
        let inTotalRules = 0;
        let outTotalRules = 0;
        result.forEach(item => {
            // 转换上传规则key
            tableSchema.forEach(schema => {
                if (!item[schema.name]) {
                    item[schema.name] = '';
                }
                if (item[schema.label] != undefined) {
                    item[schema.name] = item[schema.label];
                    delete item[schema.label];
                }
            });

            if (item) {
                item.direction === 'egress' ? outTotalRules++ : inTotalRules++;
                if (
                    !rules.checkIpAddress(item.ipAddress, this.data.get('ethertype')) ||
                    !rules.checkDesc(item.description)
                ) {
                    num++;
                    item.status = false;
                    item.statusMessage = !rules.checkIpAddress(item.ipAddress, this.data.get('ethertype'))
                        ? 'IP地址格式有误'
                        : '描述字符长度不能超过200';
                    this.data.set('leadDisable', true);
                } else if (rules.checkIsExist(allRules, item, this.data.get('ethertype'))) {
                    num++;
                    item.status = false;
                    item.statusMessage = 'IP地址下已存在相同规则，请修改后重试';
                    this.data.set('leadDisable', true);
                } else if (rules.checkIsExist(addAllRules, item, this.data.get('ethertype'))) {
                    num++;
                    item.status = false;
                    item.statusMessage = '导入规则中存在相同规则，请修改后重试';
                    this.data.set('leadDisable', true);
                    this.data.set('disableMessage', '导入规则中存在相同规则，请修改后重试');
                } else {
                    item.status = true;
                }
                addAllRules.push(item);
            }
        });
        // 无规则校验
        if (!result.length) {
            this.data.set('leadDisable', true);
            this.data.set('disableMessage', '未检测到规则，请先添加');
        }
        this.checkQuota(result, this.data.get('freeIpNum'));
        this.data.set('ruleTotal', result.length);
        this.data.set('ruleWrongTotal', num);
        this.data.set('ruleList.datasource', result);
        this.data.set('fileUploaded', true);
        this.data.set('ruleIsShow', true);
        this.fire('enableBtnOK', !num);
    }

    getRuleImport() {
        this.data.set('leadDisable', true);
        let ipAddresses = this.data.get('ruleList.datasource').map(item => ({
            ipAddress: item.ipAddress,
            description: item.description
        }));
        let payload = {
            ipSetUuid: this.data.get('ipSetUuid'),
            ipAddresses: ipAddresses
        };
        this.$http
            .addIp(payload)
            .then(() => {
                this.data.set('open', false);
                this.fire('leadComplete');
            })
            .catch(e => this.data.set('leadDisable', true));
    }
    leadSubmit() {
        let ruleTotal = this.data.get('ruleTotal');
        let ruleWrongTotal = this.data.get('ruleWrongTotal');
        if (ruleTotal === 0 || ruleWrongTotal > 0) {
            return;
        }
        this.getRuleImport();
    }
    leadCancel() {
        this.data.set('open', false);
    }
    checkQuota(result, freeIpNum) {
        if (result && result.length > freeIpNum) {
            this.data.set('leadDisable', true);
            this.data.set('disableMessage', '超出最大导入条数');
        }
    }
}
export default Processor.autowireUnCheckCmpt(ipAddressLeadIn);
