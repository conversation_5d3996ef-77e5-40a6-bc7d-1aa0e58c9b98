import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {OutlinedPlus, OutlinedRefresh} from '@baidu/sui-icon';
import {Notification} from '@baidu/sui';
import {isNumber} from 'lodash';
import {IPColumns} from '../list/tableField';
import rule from '../../rule';
import LeadFile from './leadFile';
import Confirm from '@/pages/sanPages/components/confirm';
import {checkIpv6Cidr} from '@/pages/sanPages/utils/common';
import RULE from '@/pages/sanPages/utils/rule';

import '../../style/detail.less';

const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent, invokeComp} = decorators;
const tpl = html`
    <div>
        <div class="{{klass}}">
            <div class="param-content-wrap">
                <s-biz-page>
                    <span slot="header">
                        <span class="title">{{title}}</span>
                    </span>
                    <div class="list-page-tb-left-toolbar" slot="tb-left">
                        <s-tooltip trigger="{{createIpSet.disable ? 'hover' : ''}}" placement="top">
                            <div slot="content">
                                <!--bca-disable-next-line-->
                                {{createIpSet.message | raw}}
                            </div>
                            <s-button
                                skin="primary"
                                on-click="onCreateSet"
                                disabled="{{isEdit || createIpSet.disable}}"
                            >
                                <outlined-plus />
                                新增IP地址</s-button
                            >
                        </s-tooltip>

                        <s-tooltip
                            class="left_class"
                            trigger="{{deleteIpAddress.disable ? 'hover' : ''}}"
                            placement="right"
                        >
                            <!--bca-disable-next-line-->
                            <div slot="content">{{deleteIpAddress.message | raw}}</div>
                            <s-button on-click="batchDelete" disabled="{{deleteIpAddress.disable}}"> 删除</s-button>
                        </s-tooltip>
                    </div>
                    <div slot="tb-right" class="right_bar">
                        <s-search
                            width="{{260}}"
                            class="search-wrap"
                            value="{=keyword=}"
                            placeholder="{{payload.placeholder}}"
                            on-search="onSearch($event)"
                        >
                            <s-select slot="options" datasource="{{payload.searchTypes}}" value="{=keywordType=}">
                            </s-select>
                        </s-search>
                        <s-button class="s-icon-button" on-click="refresh" track-name="刷新"
                            ><outlined-refresh class="icon-class"
                        /></s-button>
                        <s-tooltip
                            style="margin-right: 4px;"
                            trigger="{{createIpSet.disable ? 'hover' : ''}}"
                            placement="right"
                        >
                            <div slot="content">
                                <!--bca-disable-next-line-->
                                {{createIpSet.message | raw}}
                            </div>
                            <s-button on-click="leadIn" disabled="{{createIpSet.disable || !instanceDetail.ipSetId}}">
                                {{'导入' }}
                            </s-button>
                        </s-tooltip>
                        <s-button on-click="leadOut">导出</s-button>
                    </div>
                    <s-table
                        s-ref="table"
                        columns="{{table.columns}}"
                        loading="{{table.loading}}"
                        error="{{table.error}}"
                        datasource="{{table.datasource}}"
                        on-selected-change="tableSelected($event)"
                        selection="{=table.selection=}"
                    >
                        <div slot="error">
                            啊呀，出错了?
                            <a href="javascript:;" on-click="refresh">重新加载</a>
                        </div>
                        <div slot="c-name">
                            {{row.name}}
                            <br />
                            {{row.ipSetId}}
                        </div>
                        <div slot="c-ipAddress">
                            <span s-if="!editRow[rowIndex]">{{row.ipAddress}}</span>
                            <s-input
                                s-if="editRow[rowIndex]"
                                value="{=ipAddress[rowIndex]=}"
                                on-input="dataInput($event, rowIndex, 'ip', row)"
                            ></s-input>
                            <p style="color: #d0021b;margin-top: 4px;" s-if="ipAddressErr[rowIndex]">
                                {{ipAddressErr[rowIndex]}}
                            </p>
                        </div>
                        <div slot="c-description">
                            <s-input
                                s-if="editRow[rowIndex]"
                                value="{=description[rowIndex]=}"
                                on-input="dataInput($event, rowIndex, 'desc')"
                            ></s-input>
                            <p style="color: #d0021b" s-if="descErr[rowIndex]">{{descErr[rowIndex]}}</p>
                            <div s-if="!editRow[rowIndex]">
                                <span>{{row.description || '-'}}</span>
                            </div>
                        </div>
                        <div slot="c-option">
                            <span class="operations">
                                <div s-if="!editRow[rowIndex]">
                                    <a href="javascript:void(0)" on-click="onEdit(row, rowIndex)" s-if="!editArr.length"
                                        >编辑</a
                                    >
                                    <span style="color:#999" s-else>编辑</span>
                                </div>
                                <a
                                    href="javascript:void(0)"
                                    on-click="onConfirm(rowIndex, row)"
                                    s-if="editRow[rowIndex]"
                                    >确定</a
                                >
                                <a href="javascript:void(0)" on-click="onCancel(rowIndex)" s-if="editRow[rowIndex]"
                                    >取消</a
                                >
                            </span>
                        </div>
                    </s-table>
                    <s-pagination
                        s-if="{{pager.total}}"
                        slot="footer"
                        layout="{{'total, pageSize, pager, go'}}"
                        pageSize="{{pager.pageSize}}"
                        total="{{pager.total}}"
                        page="{{pager.page}}"
                        on-pagerChange="onPagerChange"
                        on-pagerSizeChange="onPagerSizeChange"
                    />
                </s-biz-page>
            </div>
        </div>
        <s-dialog class="dcgw-confirm" closeAfterMaskClick="{{false}}" open="{=open=}" title="{{title}}">
            <div>
                <s-icon name="warning-new" />
                <!--bca-disable-next-line-->
                {{content | raw}}
            </div>
            <div slot="footer">
                <s-button skin="primary" on-click="close">确定</s-button>
            </div>
        </s-dialog>
    </div>
`;

@template(tpl)
@invokeComp('@ipaddress-lead-in')
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@asComponent('@ip-address')
class ParamDetail extends Component {
    static components = {
        'outlined-plus': OutlinedPlus,
        'outlined-refresh': OutlinedRefresh
    };
    initData() {
        return {
            klass: 'ip-address-wrap',
            title: 'IP地址管理',
            RULE: RULE.DETAIL_EDIT,
            deleteIpAddress: {
                disable: true,
                message: ''
            },
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                columns: IPColumns.slice(),
                datasource: [],
                selectedItems: []
            },
            instanceDetail: {},
            isEdit: false,
            quotaDisable: false,
            freeIpNum: 0,
            editRow: [],
            ipAddress: [],
            description: [],
            ipAddressErr: [],
            descErr: [],
            editArr: [],
            editType: '',
            createIpSet: {
                disable: false
            },
            payload: {
                placeholder: '请根据IP地址进行搜索',
                searchTypes: [{value: 'IP', text: 'IP地址'}]
            },
            keywordType: 'IP',
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            allIp: []
        };
    }

    inited() {
        this.setTable();
        let {deleteIpAddress} = checker.check(rule, [], '');
        this.data.set('deleteIpAddress', deleteIpAddress);
        const ip = this.data.get('context').ip;
        if (ip) {
            this.data.set('keyword', ip);
        }
    }

    attached() {
        this.getQuota();
        this.getIpDetail();
        this.getIpList();
    }

    getQuota() {
        this.$http
            .ipQuota(
                {ipSetUuid: this.data.get('context').ipSetUuid},
                {'x-silent-codes': ['IpCollection.IpSetResourceNotExist']}
            )
            .then(res => {
                this.data.set('freeIpNum', res.free);
                this.data.set('quotaDisable', res.free < 0);
                let {createIpSet} = checker.check(rule, '', 'createIpSet', {quota: res});
                this.data.set('createIpSet', createIpSet);
            });
    }

    // 设置table头
    setTable() {
        this.data.splice('table.columns', [
            0,
            1,
            {
                name: 'ipAddress',
                label: 'IP地址',
                width: 200
            }
        ]);
    }

    getIpList() {
        this.data.set('table.loading', true);
        const {pager, keyword, keywordType} = this.data.get('');
        let payload = {
            ipSetUuid: this.data.get('context').ipSetUuid,
            keyword: keyword || '',
            keywordType,
            pageSize: pager.pageSize,
            pageNo: keyword ? 1 : pager.page
        };
        this.$http
            .getIpAddressList(payload, {'x-silent-codes': ['IpCollection.IpSetResourceNotExist']})
            .then(res => {
                this.data.set('table.loading', false);
                this.data.set('table.datasource', res.result);
                this.data.set('pager.total', res.totalCount);
            })
            .catch(err => {
                this.data.set('table.loading', false);
                this.data.set('table.datasource', []);
                this.data.set('pager.total', 0);
            });
    }

    getIpDetail() {
        this.$http
            .getSetDetail(
                {ipSetUuid: this.data.get('context').ipSetUuid},
                {'x-silent-codes': ['IpCollection.IpSetResourceNotExist']}
            )
            .then(res => {
                this.data.set('instanceDetail', res);
                this.data.set('allIp', res.ipAddresses);
            });
    }

    tableSelected(e) {
        this.data.set('table.selectedItems', e.value.selectedItems);
        let obj = checker.check(rule, e.value.selectedItems, 'deleteIpAddress', this.data.get('instanceDetail'));
        this.data.set('deleteIpAddress', obj.deleteIpAddress);
    }

    async batchDelete() {
        let ipUuids = this.data.get('table.selectedItems').map(item => item.ipUuid);
        this.$http.batchDeleteIp({ipUuids}).then(() => {
            this.data.set('table.selection', {
                mode: 'multi',
                selectedIndex: []
            });
            this.getIpList();
            this.getIpDetail();
            this.getQuota();
            Notification.success('删除成功');
        });
    }

    onCreateSet() {
        this.data.set('isEdit', true);
        this.data.set('editType', 'add');
        // 隐藏复选框
        this.data.set('table.selection', {
            mode: '',
            selectedIndex: [],
            disabledIndex: []
        });
        this.data.unshift('table.datasource', {
            ipAddress: '',
            description: ''
        });
        this.data.set('ipAddressErr[0]', 'IP地址必填');
        this.data.set('editRow[0]', true);
        this.data.set('editArr', [true]);
    }

    dataInput(e, rowIndex, type, row) {
        if (type === 'ip') {
            this.data.set(`ipAddress[${rowIndex}]`, e.value);
            !e.value && this.data.set(`ipAddressErr[${rowIndex}]`, 'IP地址必填');
            if (this.data.get('instanceDetail').ethertype === 'IPv4') {
                let reg = new RegExp(RULE.IP_CIDR);
                !reg.test(e.value) && this.data.set(`ipAddressErr[${rowIndex}]`, 'IP地址格式有误');
                reg.test(e.value) && this.data.set(`ipAddressErr[${rowIndex}]`, '');
            } else {
                !checkIpv6Cidr(e.value) && this.data.set(`ipAddressErr[${rowIndex}]`, 'IP地址格式有误');
                checkIpv6Cidr(e.value) && this.data.set(`ipAddressErr[${rowIndex}]`, '');
            }
            let allIp = this.data.get('allIp') || [];
            allIp = allIp.map(item => item.ipAddress);
            let nowIp = e.value?.trim();
            if (isNumber(this.data.get('editArr')[0])) {
                allIp = allIp.filter(item => item !== row.ipAddress);
            }
            if (allIp.find(item => item === nowIp)) {
                this.data.set(`ipAddressErr[${rowIndex}]`, '当前IP地址已存在');
            } else {
                this.data.set(`ipAddressErr[${rowIndex}]`, '');
            }
        } else {
            this.data.set(`description[${rowIndex}]`, e.value);
            e.value.length > 200 && this.data.set(`descErr[${rowIndex}]`, '字符长度不能超过200');
            e.value.length <= 200 && this.data.set(`descErr[${rowIndex}]`, '');
        }
    }

    async onConfirm(rowIndex, row) {
        if (this.data.get(`ipAddressErr[${rowIndex}]`) || this.data.get(`descErr[${rowIndex}]`)) {
            return false;
        }
        let bindNum = this.data.get('instanceDetail')?.associateNum;
        if (bindNum) {
            let confirm = new Confirm({
                data: {
                    open: true,
                    content:
                        '当前已经关联该IP地址组，您的任何修改将在保存后立即生效。请确认您所设置的安全组规则对当前云服务器的正常服务无任何影响！'
                }
            });
            confirm.attach(document.body);
            confirm.on('confirm', () => {
                this.data.get('editType') === 'add' ? this.addIp(rowIndex) : this.updateIpAddress(rowIndex, row);
            });
        } else {
            this.data.get('editType') === 'add' ? this.addIp(rowIndex) : this.updateIpAddress(rowIndex, row);
        }
    }

    addIp(rowIndex) {
        let payload = {
            ipSetUuid: this.data.get('context').ipSetUuid,
            ipAddresses: [
                {
                    ipAddress: this.data.get('ipAddress')[rowIndex]?.replace(/\s/g, ''),
                    description: this.data.get('description')[rowIndex] || ''
                }
            ]
        };
        this.$http.addIp(payload).then(() => {
            this.data.set('table.selection', {
                mode: 'multi',
                selectedIndex: []
            });
            Notification.success('新增成功');
            this.reset(rowIndex);
            this.data.set(`ipAddressErr[${rowIndex}]`, '');
            this.data.set(`ipAddress[${rowIndex}]`, '');
            this.data.set(`description[${rowIndex}]`, '');
            this.data.set(`descErr[${rowIndex}]`, '');
            this.getIpList();
            this.getIpDetail();
            this.getQuota();
        });
    }

    updateIpAddress(rowIndex, row) {
        let payload = {
            ipAddress: this.data.get('ipAddress')[rowIndex]?.replace(/\s/g, ''),
            description: this.data.get('description')[rowIndex] || ''
        };
        payload.ipUuid = row.ipUuid;
        this.$http.updateIpAddress(payload).then(() => {
            this.data.set('table.selection', {
                mode: 'multi',
                selectedIndex: []
            });
            Notification.success('修改成功');
            this.reset(rowIndex);
            this.data.set(`ipAddressErr[${rowIndex}]`, '');
            this.data.set(`ipAddress[${rowIndex}]`, '');
            this.data.set(`description[${rowIndex}]`, '');
            this.data.set(`descErr[${rowIndex}]`, '');
            this.data.set('keyword', payload.ipAddress);
            this.getIpList();
            this.getIpDetail();
        });
    }

    onCancel(rowIndex) {
        this.reset(rowIndex);
        this.data.set(`ipAddressErr[${rowIndex}]`, '');
        this.data.set(`ipAddress[${rowIndex}]`, '');
        this.data.set(`description[${rowIndex}]`, '');
        this.data.set(`descErr[${rowIndex}]`, '');
        this.data.get('editType') === 'add' && this.data.shift('table.datasource');
        this.data.set('table.selection', {
            mode: 'multi',
            selectedIndex: []
        });
    }

    reset(rowIndex) {
        this.data.set('isEdit', false);
        this.data.set('editArr', []);
        this.data.set(`editRow[${rowIndex}]`, false);
    }

    leadOut() {
        const ipSetUuid = this.data.get('context').ipSetUuid;
        ipSetUuid && window.open(`/api/network/v1/ip/set/downloadIp?ipSetUuid=${ipSetUuid}`);
    }

    // 新增导入功能
    leadIn() {
        let leadDialog = new LeadFile({
            data: {
                open: true,
                allRules: this.data.get('allIp'),
                ipSetUuid: this.data.get('context').ipSetUuid,
                freeIpNum: this.data.get('freeIpNum'),
                ethertype: this.data.get('instanceDetail').ethertype
            }
        });
        leadDialog.attach(document.body);
        leadDialog.on('leadComplete', () => {
            this.getIpList();
            this.getIpDetail();
            this.getQuota();
        });
    }

    close() {
        this.fire('close');
        this.data.set('open', false);
    }

    onEdit(row, rowIndex) {
        this.data.set('table.selection', {
            mode: '',
            selectedIndex: []
        });
        this.data.set('isEdit', true);
        this.data.set('editType', 'edit');
        this.data.set('editArr', [rowIndex]);
        this.data.set(`editRow[${rowIndex}]`, true);
        this.data.set(`ipAddress[${rowIndex}]`, row.ipAddress);
        this.data.set(`description[${rowIndex}]`, row.description);
    }

    onSearch() {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', 10);
        this.getIpList();
    }

    refresh() {
        this.getIpList();
    }

    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.refresh();
    }

    onPagerSizeChange(e) {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', e.value.pageSize);
        this.refresh();
    }
}
export default San2React(Processor.autowireUnCheckCmpt(ParamDetail));
