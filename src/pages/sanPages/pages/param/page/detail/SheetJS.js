/*
 * @Description:
 * @Author: <EMAIL>
 * @Date: 2022-05-07 16:33:55
 */

import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Button, Loading, Notification} from '@baidu/sui';
import {getXlsx} from '@/pages/sanPages/utils/common';
import '../../style/SheetJS.less';

const template = html`
    <div class="sheetjs-panel">
        <s-button
            class="upload-btn"
            icon="{{config.icon}}"
            disabled="{{config.disabled}}"
            size="{{config.size}}"
            skin="{{config.skin}}"
        >
            {{ config.text }}
            <input
                class="file-upload"
                type="file"
                s-ref="fileInput"
                title=""
                on-change="fileUpload"
                value="{=filePath=}"
                disabled="{{config.disabled}}"
                accept="{{accept}}"
            />
        </s-button>
        <div class="status-show" s-if="{{statusShow}}">
            <span s-if="{{fileName}}">{{ fileName }}</span>
            <s-loading size="small" loading="{{uploading}}" />
            <span class="error-message" s-if="{{error}}">{{ error }}</span>
        </div>
    </div>
`;

export default class SheetJS extends Component {
    static template = template;
    static components = {
        's-button': Button,
        's-loading': Loading
    };

    initData() {
        return {
            config: {
                disabled: false,
                icon: 'plus',
                text: '点击选择文件',
                showTip: false,
                size: '',
                skin: 'primary',
                tip: '',
                tipPosition: 'tc'
            },
            uploading: false,
            error: '',
            accept: '.csv',
            statusShow: true,
            files: '',
            filePath: '',
            fileName: '',
            headers: ['IP地址', '以太网协议', '描述']
        };
    }

    fileUpload(e) {
        let files = e.target.files;
        this.data.set('files', files);
        if (files && files[0]) {
            this.fileAnalysis(files[0]);
        } else {
            this.data.set('error', '读取文件出错');
        }
    }

    csvToObject(csvString) {
        let csvarry = csvString.trim().split('\n');
        // 兼容下载文件表头末尾没有\r的问题
        if (csvarry[1] && csvarry[1][csvarry[1].length - 1].charCodeAt(0) === 13) {
            csvarry = csvString.trim().split('\r\n');
            csvarry[0].split('\n');
            csvarry = [...csvarry[0].split('\n'), ...csvarry.slice(1)];
        }
        let headerConfig = this.data.get('headers');
        let datas = [];
        let headers = csvarry[0].split(',');
        let splitChar = ',';
        // 兼容excel保存会将','保存为 String.fromCharCode(9)
        if (headers.length === 1) {
            splitChar = String.fromCharCode(9);
            headers = csvarry[0].split(String.fromCharCode(splitChar));
        }
        // 避免编码不兼容造成的读取失败
        for (let i = 0; i < headerConfig.length; i++) {
            if (headers.indexOf(headerConfig[i]) === -1) {
                headers[i] = headerConfig[i];
            }
        }
        let ipIndex = csvarry[0].split(splitChar).findIndex(item => item === 'IP地址');
        let descIndex = csvarry[0].split(splitChar).findIndex(item => item === '描述');
        for (let i = 1; i < csvarry.length; i++) {
            let data = {};
            let temp = csvarry[i].split(splitChar);
            for (let j = 0; j < temp.length; j++) {
                data[headers[j]] = temp[j];
            }
            data['IP地址'] = csvarry[i].split(splitChar)[ipIndex];
            data['描述'] = csvarry[i].split(splitChar)[descIndex];
            datas.push(data);
        }
        return datas;
    }

    preParse(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            // 开始读取时触发
            reader.onloadstart = () => {
                this.data.set('uploading', true);
            };
            // 读取操作完成时触发
            reader.onload = e => {
                try {
                    let binary = e.target.result;
                    const encodingRight = binary.indexOf('�') === -1;
                    if (encodingRight) {
                        resolve('utf-8');
                    } else {
                        resolve('gbk');
                    }
                } catch (err) {
                    resolve('error');
                }
            };
            // 在读取操作发生错误时触发
            reader.onerror = e => {
                resolve('error');
            };
            reader.readAsText(file);
        });
    }

    async fileAnalysis(file) {
        const reader = new FileReader();
        const XLSX = await getXlsx();
        let isCsv = file.type.indexOf('csv');
        // 开始读取时触发
        reader.onloadstart = () => {
            this.fire('upload', {
                eventType: 'uploadStart',
                result: file
            });
            this.data.set('uploading', true);
            this.data.set('error', null);
        };
        // 读取操作完成时触发
        reader.onload = e => {
            try {
                let data = [];
                if (isCsv) {
                    let binary = e.target.result;
                    data = this.csvToObject(binary);
                } else {
                    const bstr = e.target.result;
                    /* globals XLSX */
                    const wb = XLSX.read(bstr, {type: 'binary', raw: true});
                    // Get first worksheet
                    const wsname = wb.SheetNames[0];
                    const ws = wb.Sheets[wsname];
                    /* globals XLSX */
                    data = XLSX.utils.sheet_to_json(ws);
                }
                if (e.total > 2 * 1024 * 1024) {
                    this.fire('upload', {
                        eventType: 'uploadError',
                        result: []
                    });
                    Notification.error('文件大小不能超过2M');
                    return;
                }
                this.fire('upload', {
                    eventType: 'uploadSuccess',
                    result: data ? data : []
                });
                this.data.set('fileName', file.name);
                this.data.set('filePath', '');
            } catch (err) {
                this.fire('upload', {
                    eventType: 'uploadError',
                    result: err
                });
                this.data.set('error', '读取文件出错');
            }
        };
        // 读取操作结束时触发，不论成功或失败
        reader.onloadend = e => {
            this.fire('upload', {
                eventType: 'uploadLoadend',
                result: e
            });
            this.data.set('uploading', false);
        };
        // 在读取操作发生错误时触发
        reader.onerror = e => {
            this.fire('upload', {
                eventType: 'uploadError',
                result: e
            });
            this.data.set('error', '读取文件出错');
        };
        // 在读取Blob时触发
        reader.onprogress = e => {
            this.fire('upload', {
                eventType: 'uploadProgress',
                result: {
                    loaded: e.loaded,
                    total: e.total
                }
            });
        };
        if (isCsv) {
            let fileType = await this.preParse(file);
            if (fileType === 'error') {
                fileItem.fileStatus = 'error';
                fileItem.message = _('读取文件出错');
                this.data.set('uploading', false);
                this.data.push('files', fileItem);
            } else {
                reader.readAsText(file, fileType);
            }
        } else {
            reader.readAsBinaryString(file);
        }
    }

    reset() {
        this.data.set('files', '');
        this.data.set('filePath', '');
    }
    retry() {
        let files = this.data.get('files');
        if (files && files[0]) {
            this.fileAnalysis(files[0]);
        } else {
            this.data.set('error', '读取文件出错');
        }
    }
}
