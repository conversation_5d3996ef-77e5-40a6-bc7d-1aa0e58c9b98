// 地址组创建弹窗
import {Component, defineComponent} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {OutlinedPlus, FilledWarn, OutlinedQuestion} from '@baidu/sui-icon';
import {Notification} from '@baidu/sui';
import {ContextService} from '@/pages/sanPages/common';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import Assist from '@/utils/assist';
import {updateResource} from '@/apis';
import '../../style/create.less';

const formValidator = self => ({
    name: [
        {required: true, message: '请填写名称'},
        {pattern: /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{0,64}$/, message: '格式不符合要求'}
    ],
    ipType: [{required: true, message: '请选择IP地址类型'}],
    description: [{maxLength: 200}]
});
const {asComponent, invokeSUI, invokeSUIBIZ, template} = decorators;
const tpl = html`
<div>
    <s-dialog
        class="vpc-group-create"
        open="{{true}}"
        width="600"
        title="创建IP地址族"
    >
        <s-form s-ref="form" data="{=formData=}" rules="{{rules}}">
            <s-form-item
                prop="name"
                class="name-wrap"
                help="大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65"
            >
                <template slot="label">
                    {{'名称：'}}
                    <!--<s-tip
                        class="inline-tip"
                        placement="top"
                    >
                        <s-question class="question-class warning-class"></s-question>
                        <div slot="content">
                            IP地址组和IP地址族的关联是什么？
                            <a class="assist-tip" href="javascript:void(0)" on-click="showAssist()">了解详情</a>
                        </div>
                    </s-tip>-->
                </template>
                <s-input width="{{300}}" value="{=formData.name=}"/>
            </s-form-item>
            <s-form-item label="描述：" prop="description">
                <s-input-text-area width="{{300}}"
                    maxLength="200"
                    height="{{100}}"
                    multiline
                    value="{=formData.description=}" />
            </s-form-item>
            <s-form-item label="IP地址族类型：" prop="ipType" class="ip_type_class">
                <s-radio-radio-group
                    datasource="{{ipTypeList}}"
                    radioType="radio"
                    value="{=formData.ipType=}"
                    on-change="ipTypeChange($event)"
                >
                </s-radio-radio-group>
            </s-form-item>
            <s-form-item label="选择IP地址组：" class="param_ip_class"></s-form-item>
            <s-transfer datasource="{{datasource}}" loading="{{loading}}"
                s-if="formData.ipType === 'IPv4'"
                width="400"
                filter="{{filter}}"
                on-change="transferChange"
                titles="{{titles}}"
                class="transfer_form_wrap"/>
                <div slot="render">{{item.label}}</div>
            </s-transfer>
            <s-transfer datasource="{{ipv6DataSource}}" loading="{{loading}}"
                s-if="formData.ipType === 'IPv6'"
                width="400"
                filter="{{filter}}"
                on-change="ipv6TransferChange"
                titles="{{titles}}"
                class="transfer_form_wrap"/>
                <div slot="render">{{item.label}}</div>
            </s-transfer>
        </s-form>
        <div slot="footer">
            <s-button on-click="onClose">取消</s-button>
            <s-tooltip trigger="{{paramDisable.disable ? 'hover' : ''}}" placement="top">
                <s-button skin="primary"
                    disabled="{{paramDisable.disable || disableSub}}"
                    on-click="doSubmit">确定</s-button>
                    <!--bca-disable-next-line-->
                <div slot="content">{{paramDisable.message | raw}}</div>
            </s-tooltip>
        </div>
    </s-dialog>
</div>
`;

@asComponent('@create-group-param')
@template(tpl)
@invokeSUI
@invokeSUIBIZ
class GroupCreat extends Component {
    static components = {
        'outlined-plus': OutlinedPlus,
        's-question': OutlinedQuestion
    };
    initData() {
        return {
            FLAG: FLAG,
            rules: {
                ...formValidator(this)
            },
            numQuota: 10,
            formData: {
                name: '',
                desc: '',
                ipType: 'IPv4'
            },
            formErrors: {},
            ipTypeList: [
                {
                    text: 'IPv4',
                    value: 'IPv4'
                },
                {
                    text: 'IPv6',
                    value: 'IPv6'
                }
            ],
            ContextService,
            paramDisable: {},
            datasource: [],
            ipv6DataSource: [],
            titles: ['IP地址组：', '已选择：'],
            selectedSetIp: [],
            selectedIpv6Ip: [],
            loading: false,
            filter: true
        };
    }

    inited() {
        const cmcConfig = this.data.get('cmcConfig');
        if (cmcConfig && Object.keys(cmcConfig).length) {
            const {name, ipVersion, description, includedIpSet} = cmcConfig || {};
            const initFormData = {
                name,
                ipType: ipVersion,
                description
            };
            this.data.set('formData', initFormData);
            const currIpsetType = ipVersion === 'IPv4' ? 'selectedSetIp' : 'selectedIpv6Ip';
            this.data.set(`${currIpsetType}`, JSON.parse(includedIpSet));
        }
        this.$http.ipGroupSetQuota().then(res => {
            this.data.set('createIpGroupQuota', res);
        });
        this.getIpSet();
    }

    getIpSet() {
        let payload = {
            pageNo: 1,
            pageSize: 10000
        };
        this.data.set('loading', true);
        this.$http.ipSetList(payload).then(res => {
            const cmcIncludedIpset = this.data.get('cmcConfig.includedIpSet');
            const cmcMovedIpset = cmcIncludedIpset ? JSON.parse(cmcIncludedIpset) : [];
            let array = [];
            res.result.forEach(item => {
                const arrItem = {
                    label: item.name + '（' + item.ipSetId + '）',
                    value: item.ipSetUuid,
                    ipType: item.ethertype
                    // show: true
                };
                if (cmcMovedIpset.includes(item.ipSetUuid)) {
                    arrItem.checked = true;
                }
                array.push(arrItem);
            });
            this.data.set('tableDatasource', array);
            this.data.set('loading', false);
            this.setDataSource();
        });
    }

    onClose() {
        this.dispose();
    }
    transferChange({value}) {
        let keyMap = value.map(item => item.value);
        this.data.set('selectedSetIp', keyMap);
    }
    ipv6TransferChange({value}) {
        let keyMap = value.map(item => item.value);
        this.data.set('selectedIpv6Ip', keyMap);
    }

    async handleUpdateCmcRes(resUuid) {
        const cmcConfig = this.data.get('cmcConfig');
        if (cmcConfig && Object.keys(cmcConfig).length) {
            const {sourceCloud, sourceRegion, sourceResourceId, upstreamId, targetRegion} = cmcConfig;
            const updatePayload = {
                resourceList: [
                    {
                        sourceCloud,
                        sourceRegion,
                        sourceResourceId,
                        upstreamId,
                        targetRegion,
                        targetResourceId: resUuid,
                        status: 'migrating',
                        targetResourceDetail: JSON.stringify({})
                    }
                ]
            };
            return updateResource(updatePayload);
        }
        return Promise.resolve();
    }
    doSubmit() {
        let formData = this.data.get('formData');
        let selectedSetIp = this.data.get('selectedSetIp');
        let selectedIpv6Ip = this.data.get('selectedIpv6Ip');
        formData.ipType === 'IPv6' && (selectedSetIp = this.data.get('selectedIpv6Ip'));
        const form = this.ref('form');
        form.validateFields().then(() => {
            let createIpGroupQuota = this.data.get('createIpGroupQuota') || {};
            let quota = createIpGroupQuota.total || 5;
            if (
                (formData.ipType === 'IPv4' && selectedSetIp.length === 0) ||
                (formData.ipType === 'IPv6' && selectedIpv6Ip.length === 0)
            ) {
                Notification.warning('请选择IP地址组');
                return;
            }
            if (
                (formData.ipType === 'IPv4' && selectedSetIp.length > quota) ||
                (formData.ipType === 'IPv6' && selectedIpv6Ip.length > quota)
            ) {
                if (FLAG.NetworkSupportXS) {
                    Notification.warning(`最多选择${quota}个IP地址组，如需增加可提交工单申请`);
                    return;
                } else {
                    let message = `${ContextService.Domains.ticket}/#/ticket/create`;
                    const content = defineComponent({
                        template: /* html */ `
                            <template>
                                <s-icon-fill-wran color="inherit" class="notification-warning-class" />
                                <span>
                                    最多选择{{quota}}个IP地址组，如需增加可提交
                                    <a href="{{message}}" target="_blank">工单</a>
                                </span>
                            </template>
                        `,
                        components: {
                            's-icon-fill-wran': FilledWarn
                        },
                        initData() {
                            return {
                                message,
                                quota
                            };
                        }
                    });
                    Notification.open(content, {duration: 5});
                    return;
                }
            }
            let payload = {
                name: formData.name,
                description: formData.description,
                ethertype: formData.ipType,
                ipSetUuids: selectedSetIp
            };
            this.data.set('disableSub', true);
            this.$http
                .createIpGroup(payload)
                .then(async res => {
                    await this.handleUpdateCmcRes(res.id);
                    this.fire('success');
                    this.onClose();
                })
                .catch(() => {
                    this.data.set('disableSub', false);
                });
        });
    }
    setDataSource(value) {
        let datasource = this.data.get('tableDatasource');
        this.data.set('datasource', []);
        let ipType = value || this.data.get('formData.ipType');
        this.nextTick(() => {
            let dataArray = datasource.filter(item => item.ipType === 'IPv4');
            dataArray.forEach((item, index) => {
                item._key = index;
            });
            let ipv6Array = datasource.filter(item => item.ipType === 'IPv6');
            ipv6Array.forEach((item, index) => {
                item._key = index;
            });
            this.data.set('datasource', dataArray);
            this.data.set('ipv6DataSource', ipv6Array);
        });
    }
    showAssist() {
        Assist.sendMessageToAssist({sceneLabel: 'param_create', message: 'IP地址组和IP地址族的关联是什么？'});
    }
}
export default Processor.autowireUnCheckCmpt(GroupCreat);
