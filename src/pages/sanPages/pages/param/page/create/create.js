import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {OutlinedPlus, OutlinedQuestion} from '@baidu/sui-icon';

import RULE from '@/pages/sanPages/utils/rule';
import {ipAddressType} from '@/pages/sanPages/common/enum';
import {ContextService} from '@/pages/sanPages/common';
import {checkIpv6Cidr, convertCidrToBinary} from '@/pages/sanPages/utils/common';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import Assist from '@/utils/assist';

import '../../style/create.less';
import {updateResource} from '@/apis';
const formValidator = self => ({
    name: [
        {required: true, message: '请填写名称'},
        {pattern: /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{0,64}$/, message: '格式不符合要求'}
    ],
    ipType: [{required: true, message: '请选择IP地址类型'}],
    ipNum: [{required: true, message: '请选择IP地址容量'}],
    description: [{maxLength: 200}]
});
const {asComponent, invokeSUI, invokeSUIBIZ, template, service} = decorators;
const tpl = html`
    <div>
        <s-dialog class="vpc-param-create" open="{{true}}" width="600" title="{{creatTitle}}">
            <s-form s-ref="form" data="{=formData=}" rules="{{rules}}">
                <s-form-item
                    class="name-wrap"
                    prop="name"
                    help="大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65"
                >
                    <template slot="label">
                        {{'名称：'}}
                        <!--<s-tip class="inline-tip" placement="top">
                            <s-question class="question-class warning-class"></s-question>
                            <div slot="content">
                                IP地址组和IP地址族的关联是什么？
                                <a class="assist-tip" href="javascript:void(0)" on-click="showAssist()">了解详情</a>
                            </div>
                        </s-tip>-->
                    </template>
                    <s-input width="{{300}}" value="{=formData.name=}" />
                </s-form-item>
                <s-form-item label="描述：" prop="description">
                    <s-input-text-area
                        width="{{300}}"
                        maxLength="200"
                        height="{{100}}"
                        multiline
                        value="{=formData.description=}"
                    />
                </s-form-item>
                <!--<s-form-item label="IP地址容量：" prop="ipNum">
                <s-input-number
                    min="1"
                    max="{{numQuota}}"
                    value="{=formData.ipNum=}" /><a href="{{ContextService.Domains.ticket}}/#/ticket/create" target="_blank">增加配额</a>
            </s-form-item>-->
                <s-form-item label="IP地址类型：" prop="ipType" class="ip_type_class">
                    <s-radio-radio-group
                        datasource="{{ipTypeList}}"
                        radioType="radio"
                        value="{=formData.ipType=}"
                        on-change="ipTypeChange($event)"
                    >
                    </s-radio-radio-group>
                </s-form-item>
                <s-form-item label="IP地址：" prop="privateIps" class="ip-item center_class">
                    <s-table datasource="{{table.datasource}}" columns="{{table.columns}}">
                        <div slot="empty">
                            <s-empty>
                                <div slot="action"></div>
                            </s-empty>
                        </div>
                        <div slot="c-ip">
                            <s-input
                                class="ip-input"
                                width="{{150}}"
                                value="{=row.privateIp=}"
                                placeholder="请输入IP地址"
                                on-input="inputChange($event, rowIndex)"
                            />
                            <p s-if="row.error" class="ui-form-item-invalid-label">{{row.error}}</p>
                        </div>
                        <div slot="c-desc">
                            <s-input
                                class="ip-input"
                                width="{{120}}"
                                value="{=row.desc=}"
                                on-input="descChange($event, rowIndex)"
                                limit-length="200"
                                placeholder="请输入描述"
                            />
                        </div>
                        <div slot="c-operation">
                            <s-button skin="stringfy" on-click="onRemove(rowIndex)" s-if="rowIndex > 0">删除</s-button>
                        </div>
                    </s-table>
                    <s-tooltip trigger="{{disableAddIp.message ? 'hover' : ''}}" placement="right">
                        <!--bca-disable-next-line-->
                        <div slot="content">{{disableAddIp.message | raw}}</div>
                        <s-button skin="stringfy" on-click="addIp()" disabled="{{disableAddIp.disable}}"
                            ><outlined-plus class="plus_class" />新增一行</s-button
                        >
                    </s-tooltip>
                </s-form-item>
            </s-form>
            <div slot="footer">
                <s-button on-click="onClose">取消</s-button>
                <s-tooltip trigger="{{paramDisable.disable ? 'hover' : ''}}" placement="top">
                    <s-button skin="primary" disabled="{{paramDisable.disable || disableSub}}" on-click="doSubmit"
                        >确定</s-button
                    >
                    <div slot="content">{{paramDisable.message}}</div>
                </s-tooltip>
            </div>
        </s-dialog>
    </div>
`;

@asComponent('@create-param')
@template(tpl)
@invokeSUI
@invokeSUIBIZ
class IpSetCreate extends Component {
    static components = {
        'outlined-plus': OutlinedPlus,
        's-question': OutlinedQuestion
    };

    initData() {
        return {
            FLAG: FLAG,
            rules: {
                ...formValidator(this)
            },
            numQuota: 10,
            formData: {
                name: '',
                desc: '',
                ipNum: 10,
                ipType: 'IPv4'
            },
            formErrors: {},
            loading: false,
            table: {
                datasource: [
                    {
                        desc: '',
                        privateIp: ''
                    }
                ],
                columns: [
                    {name: 'ip', label: 'IP地址', width: 200},
                    {name: 'desc', label: '描述', width: 200},
                    {name: 'operation', label: '操作', width: 100}
                ]
            },
            ipTypeList: [
                {
                    text: 'IPv4',
                    value: 'IPv4'
                },
                {
                    text: 'IPv6',
                    value: 'IPv6'
                }
            ],
            ContextService,
            quotaError: false,
            paramDisable: {}
        };
    }

    static computed = {
        disableAddIp() {
            let datasource = this.data.get('table.datasource');
            let createIpSetQuota = this.data.get('createIpSetQuota') || {};
            let quota = createIpSetQuota.total || 20;
            if (datasource.length >= quota) {
                return {
                    disable: true,
                    message: `IP已达上限，如需更多配额，请提交
                    <a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">工单</a>`
                };
            }
            return {};
        },
        creatTitle() {
            let value = this.data.get('addressType');
            return '创建' + ipAddressType.getTextFromValue(value);
        }
    };

    onClose() {
        this.dispose();
    }
    onRemove(rowIndex) {
        this.data.removeAt('table.datasource', rowIndex);
    }
    addIp() {
        this.data.push('table.datasource', {privateIp: '', desc: ''});
    }
    inputChange(e, index) {
        this.data.set(`table.datasource[${index}].privateIp`, e.value);
        let datasource = this.data.get('table.datasource').map(item => item.privateIp);
        datasource.splice(index, 1);
        if (!e.value) {
            this.data.set(`table.datasource[${index}].error`, '请指定IP地址');
        } else {
            if (this.data.get('formData.ipType') === 'IPv4') {
                var reg = new RegExp(RULE.IP_CIDR);
                if (!reg.test(e.value)) {
                    this.data.set(`table.datasource[${index}].error`, 'IP地址格式有误');
                } else {
                    let valueString = convertCidrToBinary(e.value);
                    let valueMask = e.value.split('/')[1];
                    // 掩码分割的前部分
                    let preValueString = valueString.substring(0, +valueMask);
                    // 掩码分割的前后部分
                    let tailValueString = valueString.substring(+valueMask, valueString.length);
                    if (valueMask && tailValueString.indexOf('1') > -1) {
                        let addLen = 32 - preValueString.length;
                        let fixIpBinary = preValueString + new Array(addLen).fill('0').join('');
                        // 每隔8位按.分割
                        let ipBinaryArr = fixIpBinary
                            .replace(/(.{8})/g, '$1.')
                            .substring(0, 32)
                            .split('.');
                        let ipArr = ipBinaryArr.map(binary => parseInt(binary, 2));
                        let fixIp = ipArr.join('.') + '/' + valueMask;
                        this.data.set(`table.datasource[${index}].error`, `网段与掩码不匹配，建议改为${fixIp}`);
                    } else if (datasource.includes(e.value)) {
                        this.data.set(`table.datasource[${index}].error`, '当前IP地址已重复');
                    } else {
                        this.data.set(`table.datasource[${index}].error`, '');
                    }
                }
            } else {
                if (!checkIpv6Cidr(e.value)) {
                    let valueString = convertCidrToBinary(e.value);
                    let valueMask = e.value.split('/')[1];
                    // 掩码分割的前部分
                    let preValueString = valueString.substring(0, +valueMask);
                    // 掩码分割的前后部分
                    let tailValueString = valueString.substring(+valueMask, valueString.length);
                    if (valueMask && tailValueString.indexOf('1') > -1) {
                        let addLen = 128 - preValueString.length;
                        let fixIpBinary = preValueString + new Array(addLen).fill('0').join('');
                        // 每隔16位按:分割
                        let ipBinaryArr = fixIpBinary
                            .replace(/(.{16})/g, '$1:')
                            .substring(0, 128)
                            .split(':');
                        let ipArr = ipBinaryArr.map(binary => {
                            // 先转为10进制，再转为16进制
                            return parseInt(binary, 2).toString(16);
                        });
                        let fixIp = ipArr.join(':') + '/' + valueMask;
                        // 连续:0替换::
                        let replaceIp = fixIp.replace(/(:0){2,}/g, '::');
                        this.data.set(`table.datasource[${index}].error`, `网段与掩码不匹配，建议改为${replaceIp}`);
                        return;
                    }
                    this.data.set(`table.datasource[${index}].error`, 'IP地址格式有误');
                } else {
                    if (datasource.includes(e.value)) {
                        this.data.set(`table.datasource[${index}].error`, '当前IP地址已重复');
                    } else {
                        this.data.set(`table.datasource[${index}].error`, '');
                    }
                }
            }
        }
    }
    descChange(e, index) {
        this.data.set(`table.datasource[${index}].desc`, e.value);
    }
    ipTypeChange() {
        this.data.set('table.datasource', [
            {
                desc: '',
                privateIp: ''
            }
        ]);
    }

    async handleUpdateCmcRes(resUuid) {
        const cmcConfig = this.data.get('cmcConfig');
        if (cmcConfig && Object.keys(cmcConfig).length) {
            const {sourceCloud, sourceRegion, sourceResourceId, upstreamId, targetRegion} = cmcConfig;
            const updatePayload = {
                resourceList: [
                    {
                        sourceCloud,
                        sourceRegion,
                        sourceResourceId,
                        upstreamId,
                        targetRegion,
                        targetResourceId: resUuid,
                        status: 'migrating',
                        targetResourceDetail: JSON.stringify({})
                    }
                ]
            };
            return updateResource(updatePayload);
        }
        return Promise.resolve();
    }
    doSubmit() {
        let formData = this.data.get('formData');
        const form = this.ref('form');
        let result = {flag: false, num: 0};
        return form.validateFields().then(async () => {
            let privateIps = [];
            for (let i = 0; i < this.data.get('table.datasource').length; i++) {
                let item = this.data.get(`table.datasource[${i}]`);
                let datasource = u.cloneDeep(this.data.get('table.datasource')).map(item => item.privateIp);
                datasource.splice(i, 1);
                if (!item.privateIp) {
                    this.data.set(`table.datasource[${i}].error`, '请指定IP地址');
                    break;
                }
                if (formData.ipType === 'IPv4') {
                    var reg = new RegExp(RULE.IP_CIDR);
                    if (!reg.test(item.privateIp)) {
                        this.data.set(`table.datasource[${i}].error`, 'IP地址格式有误');
                        break;
                    } else {
                        let valueString = convertCidrToBinary(item.privateIp);
                        let valueMask = item.privateIp.split('/')[1];
                        // 掩码分割的前部分
                        let preValueString = valueString.substring(0, +valueMask);
                        // 掩码分割的前后部分
                        let tailValueString = valueString.substring(+valueMask, valueString.length);
                        if (valueMask && tailValueString.indexOf('1') > -1) {
                            let addLen = 32 - preValueString.length;
                            let fixIpBinary = preValueString + new Array(addLen).fill('0').join('');
                            // 每隔8位按.分割
                            let ipBinaryArr = fixIpBinary
                                .replace(/(.{8})/g, '$1.')
                                .substring(0, 32)
                                .split('.');
                            let ipArr = ipBinaryArr.map(binary => parseInt(binary, 2));
                            let fixIp = ipArr.join('.') + '/' + valueMask;
                            this.data.set(`table.datasource[${i}].error`, `网段与掩码不匹配，建议改为${fixIp}`);
                            break;
                        }
                    }
                } else {
                    if (!checkIpv6Cidr(item.privateIp)) {
                        let valueString = convertCidrToBinary(item.privateIp);
                        let valueMask = item.privateIp.split('/')[1];
                        // 掩码分割的前部分
                        let preValueString = valueString.substring(0, +valueMask);
                        // 掩码分割的前后部分
                        let tailValueString = valueString.substring(+valueMask, valueString.length);
                        if (valueMask && tailValueString.indexOf('1') > -1) {
                            let addLen = 128 - preValueString.length;
                            let fixIpBinary = preValueString + new Array(addLen).fill('0').join('');
                            // 每隔16位按:分割
                            let ipBinaryArr = fixIpBinary
                                .replace(/(.{16})/g, '$1:')
                                .substring(0, 128)
                                .split(':');
                            let ipArr = ipBinaryArr.map(binary => {
                                // 先转为10进制，再转为16进制
                                return parseInt(binary, 2).toString(16);
                            });
                            let fixIp = ipArr.join(':') + '/' + valueMask;
                            // 连续:0替换::
                            let replaceIp = fixIp.replace(/(:0){2,}/g, '::');
                            this.data.set(`table.datasource[${i}].error`, `网段与掩码不匹配，建议改为${replaceIp}`);
                        } else {
                            this.data.set(`table.datasource[${i}].error`, 'IP地址格式有误');
                        }
                        break;
                    }
                }
                if (datasource.includes(item.privateIp)) {
                    this.data.set(`table.datasource[${i}].error`, '当前IP地址已重复');
                    break;
                } else {
                    this.data.set(`table.datasource[${i}].error`, '');
                    result.num++;
                }
                privateIps.push({
                    description: item.desc,
                    ipAddress: item.privateIp
                });
                if (i === this.data.get('table.datasource').length - 1) {
                    result.flag = true;
                }
            }
            if (this.data.get('table.datasource').length) {
                if (result.flag && result.num === this.data.get('table.datasource').length) {
                    let payload = {
                        name: formData.name,
                        description: formData.description,
                        ethertype: formData.ipType,
                        ipAddresses: privateIps
                    };
                    this.data.set('disableSub', true);
                    return this.$http
                        .createIpSet(payload)
                        .then(async res => {
                            await this.handleUpdateCmcRes(res.id);
                            this.fire('success');
                            this.onClose();
                        })
                        .catch(() => {
                            this.data.set('disableSub', false);
                        });
                }
            } else {
                let payload = {
                    name: formData.name,
                    description: formData.description,
                    ethertype: formData.ipType
                };
                this.data.set('disableSub', true);
                return this.$http
                    .createIpSet(payload)
                    .then(async res => {
                        await this.handleUpdateCmcRes(res.id);
                        this.fire('success');
                        this.onClose();
                    })
                    .catch(() => {
                        this.data.set('disableSub', false);
                    });
            }

            return Promise.reject();
        });
    }
    inited() {
        const cmcConfig = this.data.get('cmcConfig');
        if (cmcConfig && Object.keys(cmcConfig).length) {
            const {name, ipVersion, description, ipAddressInfo} = cmcConfig || {};
            const initFormData = {
                name,
                ipType: ipVersion,
                description
            };
            const formatedTableDatasource = _.map(ipAddressInfo, item => {
                const {ipAddress, description} = item;
                return {desc: description, privateIp: ipAddress};
            });
            this.data.set('formData', initFormData);
            this.data.set('table.datasource', formatedTableDatasource);
        }
        this.$http.ipQuota().then(res => {
            this.data.set('createIpSetQuota', res);
        });
    }
    showAssist() {
        Assist.sendMessageToAssist({sceneLabel: 'param_create', message: 'IP地址组和IP地址族的关联是什么？'});
    }
}
export default Processor.autowireUnCheckCmpt(IpSetCreate);
