import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {OutlinedPlus} from '@baidu/sui-icon';
import {Notification} from '@baidu/sui';

import {IPColumns} from '../list/tableField';
import Confirm from '@/pages/sanPages/components/confirm';
import rule from '../../rule';
import RULE from '@/pages/sanPages/utils/rule';
import '../../style/detail.less';

const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent} = decorators;
const tpl = html`
    <div>
        <div class="{{klass}}">
            <div class="param-content-wrap">
                <s-biz-page>
                    <span slot="header">
                        <span class="title">{{title}}</span>
                    </span>
                    <div class="list-page-tb-left-toolbar" slot="tb-left">
                        <s-tooltip trigger="{{createIpGroup.disable ? 'hover' : ''}}" placement="top">
                            <div slot="content">
                                <!--bca-disable-next-line-->
                                {{createIpGroup.message | raw}}
                            </div>
                            <s-button skin="primary" on-click="onCreateGroup" disabled="{{isEdit || createIpGroup.disable}}">
                                <outlined-plus />
                                新增IP地址组</s-button
                            >
                        </s-tooltip>
                        <s-tooltip class="left_class" trigger="{{deleteAddress.disable ? 'hover' : ''}}" placement="right">
                            <!--bca-disable-next-line-->
                            <div slot="content">{{deleteAddress.message | raw}}</div>
                            <s-button on-click="batchDelete" disabled="{{deleteAddress.disable}}"> 删除</s-button>
                        </s-tooltip>
                    </div>
                    <div slot="tb-right" class="right_bar">
                        <!--<s-button on-click="leadOut">导出</s-button>-->
                    </div>
                    <s-table
                        s-ref="table"
                        columns="{{table.columns}}"
                        loading="{{table.loading}}"
                        error="{{table.error}}"
                        datasource="{{table.datasource}}"
                        on-selected-change="tableSelected($event)"
                        selection="{=table.selection=}"
                    >
                        <div slot="error">
                            啊呀，出错了?
                            <a href="javascript:;" on-click="refresh">重新加载</a>
                        </div>
                        <div slot="c-name">
                            <div s-if="!editRow[rowIndex]">
                                {{row.name}}
                                <br />
                                <a href="javascript:void(0)">{{row.ipSetId}}</a>
                            </div>
                            <s-select
                                width="220"
                                filterable
                                s-if="editRow[rowIndex]"
                                on-change="groupIpChange($event, rowIndex)"
                                datasource="{{groupIpList}}"
                                value="{=ipAddress[rowIndex]=}"
                            >
                            </s-select>
                            <p style="color: #d0021b" s-if="ipAddressErr[rowIndex]">{{ipAddressErr[rowIndex]}}</p>
                        </div>
                        <div slot="c-description">
                            <!--<s-input
                            s-if="editRow[rowIndex]"
                            value="{=description[rowIndex]=}"
                            on-input="dataInput($event, rowIndex)"></s-input>
                        <p style="color: #d0021b" s-if="descErr[rowIndex]">{{descErr[rowIndex]}}</p>
                        <div s-if="!editRow[rowIndex]">-->
                            <div>
                                <span>{{row.description || '-'}}</span>
                            </div>
                        </div>
                        <div slot="c-option">
                            <span class="operations">
                                <div s-if="!editRow[rowIndex]">
                                    <a href="javascript:void(0)" on-click="onEdit(row, rowIndex)" s-if="!editArr.length">编辑</a>
                                    <span style="color:#999" s-else>编辑</span>
                                </div>
                                <a href="javascript:void(0)" on-click="onConfirm(rowIndex, row)" s-if="editRow[rowIndex]">确定</a>
                                <a href="javascript:void(0)" on-click="onCancel(rowIndex)" s-if="editRow[rowIndex]">取消</a>
                            </span>
                        </div>
                    </s-table>
                </s-biz-page>
            </div>
        </div>
        <s-dialog class="dcgw-confirm" closeAfterMaskClick="{{false}}" open="{=open=}" title="{{title}}">
            <div>
                <s-icon name="warning-new" />
                <!--bca-disable-next-line-->
                {{content | raw}}
            </div>
            <div slot="footer">
                <s-button skin="primary" on-click="close">确定</s-button>
            </div>
        </s-dialog>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@asComponent('@ip-group-address')
class ParamDetail extends Component {
    static components = {
        'outlined-plus': OutlinedPlus
    };
    initData() {
        return {
            klass: 'ip-address-wrap',
            title: 'IP地址组管理',
            RULE: RULE.DETAIL_EDIT,
            deleteAddress: {
                disable: true,
                message: ''
            },
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                columns: IPColumns.slice(),
                datasource: [],
                selectedItems: []
            },
            instance: {},
            isEdit: false,
            quotaDisable: false,
            editRow: [],
            ipAddress: [],
            description: [],
            ipAddressErr: [],
            descErr: [],
            editArr: [],
            editType: '',
            instanceDetail: {},
            groupIpList: []
        };
    }

    inited() {
        this.getQuota();
        this.setTable();
        let {deleteAddress} = checker.check(rule, [], '');
        this.data.set('deleteAddress', deleteAddress);
    }

    attached() {
        this.getIpDetail();
    }

    getQuota() {
        this.$http
            .ipGroupSetQuota({ipGroupUuid: this.data.get('context').ipGroupUuid}, {'x-silent-codes': ['IpCollection.IpGroupResourceNotExist']})
            .then(res => {
                this.data.set('quotaDisable', res.free < 0);
                let {createIpGroup} = checker.check(rule, '', 'createIpGroup', {quota: res});
                this.data.set('createIpGroup', createIpGroup);
            });
    }

    // 设置table头
    setTable() {
        this.data.splice('table.columns', [
            0,
            1,
            {
                name: 'name',
                label: 'IP地址组',
                width: 200
            }
        ]);
    }

    getIpDetail() {
        this.data.set('table.loading', true);
        this.$http
            .getGroupDetail({ipGroupUuid: this.data.get('context').ipGroupUuid}, {'x-silent-codes': ['IpCollection.IpGroupResourceNotExist']})
            .then(res => {
                this.data.set('instanceDetail', res);
                this.getIpSet();
                this.data.set('table.datasource', res.ipSets);
                this.data.set('table.loading', false);
            })
            .catch(() => this.data.set('table.loading', false));
    }

    tableSelected(e) {
        this.data.set('table.selectedItems', e.value.selectedItems);
        let obj = checker.check(rule, e.value.selectedItems, 'deleteAddress', this.data.get('instanceDetail'));
        this.data.set('deleteAddress', obj.deleteAddress);
    }

    async batchDelete() {
        let ipSetUuids = this.data.get('table.selectedItems').map(item => item.ipSetUuid);
        let payload = {
            ipSetUuids,
            ipGroupUuid: this.data.get('context').ipGroupUuid
        };
        this.$http.ipGroupUnbindSet(payload).then(() => {
            this.data.set('table.selection', {
                mode: 'multi',
                selectedIndex: []
            });
            this.getIpDetail();
            this.getQuota();
            Notification.success('删除成功');
        });
    }

    onCreateGroup() {
        let dataSource = this.data.get('table.datasource') || [];
        let groupIpListOrigin = this.data.get('groupIpListOrigin');
        let array = [];
        groupIpListOrigin.forEach(item => {
            let index = dataSource.findIndex(i => i.ipSetUuid === item.value);
            index === -1 && array.push(item);
        });
        this.data.set('groupIpList', array);
        this.data.set('isEdit', true);
        this.data.set('editType', 'add');
        this.data.set('table.selection', {
            mode: '',
            selectedIndex: []
        });
        this.data.unshift('table.datasource', {
            ipAddress: '',
            description: ''
        });
        this.data.set('ipAddressErr[0]', 'IP地址组必选');
        this.data.set('editRow[0]', true);
        this.data.set('editArr', [0]);
    }

    dataInput(e, rowIndex) {
        this.data.set(`description[${rowIndex}]`, e.value);
        e.value.length > 200 && this.data.set(`descErr[${rowIndex}]`, '字符长度不能超过200');
        e.value.length <= 200 && this.data.set(`descErr[${rowIndex}]`, '');
    }

    groupIpChange(e, rowIndex) {
        this.data.set(`ipAddress[${rowIndex}]`, e.value);
        this.data.set(`ipAddressErr[${rowIndex}]`, '');
    }

    async onConfirm(rowIndex, row) {
        if (this.data.get(`ipAddressErr[${rowIndex}]`) || this.data.get(`descErr[${rowIndex}]`)) {
            return false;
        }
        let bindNum = this.data.get('instanceDetail')?.associateNum;
        if (bindNum) {
            let confirm = new Confirm({
                data: {
                    open: true,
                    content: '当前已经关联该IP地址族，您的任何修改将在保存后立即生效。请确认您所设置的安全组规则对当前云服务器的正常服务无任何影响！'
                }
            });
            confirm.attach(document.body);
            confirm.on('confirm', () => {
                this.data.get('editType') === 'add' ? this.addIp(rowIndex) : this.updateIpAddress(rowIndex, row);
            });
        } else {
            this.data.get('editType') === 'add' ? this.addIp(rowIndex) : this.updateIpAddress(rowIndex, row);
        }
    }

    addIp(rowIndex) {
        let payload = {
            ipGroupUuid: this.data.get('context').ipGroupUuid,
            ipSetUuids: [this.data.get('ipAddress')[0]]
        };
        this.$http
            .ipGroupBindSet(payload)
            .then(() => {
                this.data.set('table.selection', {
                    mode: 'multi',
                    selectedIndex: []
                });
                Notification.success('新增成功');
                this.reset(rowIndex);
                this.data.set(`ipAddressErr[${rowIndex}]`, '');
                this.data.set(`ipAddress[${rowIndex}]`, '');
                this.data.set(`description[${rowIndex}]`, '');
                this.data.set(`descErr[${rowIndex}]`, '');
                this.getIpDetail();
                this.getQuota();
            })
            .catch(() => {
                this.data.set('table.selection', {
                    mode: 'multi',
                    selectedIndex: []
                });
                this.reset(rowIndex);
                this.data.shift('table.datasource');
                Notification.error('新增失败');
            });
    }

    updateIpAddress(rowIndex, row) {
        let obj = {
            ipSetUuids: [row.ipSetUuid],
            ipGroupUuid: this.data.get('context').ipGroupUuid
        };
        this.$http.ipGroupUnbindSet(obj).then(() => {
            let payload = {
                ipGroupUuid: this.data.get('context').ipGroupUuid,
                ipSetUuids: [this.data.get('ipAddress')[rowIndex]]
            };
            this.$http
                .ipGroupBindSet(payload)
                .then(() => {
                    this.data.set('table.selection', {
                        mode: 'multi',
                        selectedIndex: []
                    });
                    Notification.success('修改成功');
                    this.reset(rowIndex);
                    this.getIpDetail();
                })
                .catch(() => {
                    this.data.set('table.selection', {
                        mode: 'multi',
                        selectedIndex: []
                    });
                    Notification.error('修改失败');
                    this.reset(rowIndex);
                });
        });
    }

    onCancel(rowIndex) {
        this.reset(rowIndex);
        this.data.set(`ipAddressErr[${rowIndex}]`, '');
        this.data.set(`ipAddress[${rowIndex}]`, '');
        this.data.set(`description[${rowIndex}]`, '');
        this.data.set(`descErr[${rowIndex}]`, '');
        this.data.get('editType') === 'add' && this.data.shift('table.datasource');
        this.data.set('table.selection', {
            mode: 'multi',
            selectedIndex: []
        });
    }

    reset(rowIndex) {
        this.data.set('isEdit', false);
        this.data.set('editArr', []);
        this.data.set(`editRow[${rowIndex}]`, false);
    }

    leadOut() {
        this.data.get('ipSetUuid') && window.open(`/api/network/v1/ip/set/downloadIp/ipSetUuid=${this.data.get('ipSetUuid')}`);
    }

    close() {
        this.fire('close');
        this.data.set('open', false);
    }

    onEdit(row, rowIndex) {
        let dataSource = this.data.get('table.datasource') || [];
        let groupIpListOrigin = this.data.get('groupIpListOrigin');
        let array = [];
        groupIpListOrigin.forEach(item => {
            let index = dataSource.findIndex(i => i.ipSetUuid === item.value);
            index === -1 && array.push(item);
        });
        let index = groupIpListOrigin.findIndex(item => item.value === row.ipSetUuid);
        array.push(groupIpListOrigin[index]);
        this.data.set('groupIpList', array);
        this.data.set('table.selection', {
            mode: '',
            selectedIndex: []
        });
        this.data.set('isEdit', true);
        this.data.set('editType', 'edit');
        this.data.set('editArr', [rowIndex]);
        this.data.set(`editRow[${rowIndex}]`, true);
        this.data.set(`ipAddress[${rowIndex}]`, row.ipSetUuid);
        this.data.set(`description[${rowIndex}]`, row.description);
    }

    getIpSet() {
        let payload = {
            pageNo: 1,
            pageSize: 10000
        };
        let instanceDetail = this.data.get('instanceDetail');
        this.$http.ipSetList(payload).then(res => {
            let array = [];
            res.result.forEach(item => {
                if (instanceDetail.ethertype === item.ethertype) {
                    array.push({
                        text: item.name + '（' + item.ipSetId + '）',
                        value: item.ipSetUuid
                    });
                }
            });
            this.data.set('groupIpList', array);
            this.data.set('groupIpListOrigin', array);
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(ParamDetail));
