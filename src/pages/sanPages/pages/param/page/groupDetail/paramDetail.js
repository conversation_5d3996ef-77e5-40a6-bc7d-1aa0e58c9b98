import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {OutlinedEditingSquare} from '@baidu/sui-icon';

import {Notification} from '@baidu/sui';
import RULE from '@/pages/sanPages/utils/rule';
import rule from '../../rule';

import '../../style/detail.less';
const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent, invokeComp} = decorators;
const tpl = html`
    <div>
        <div class="{{klass}}">
            <div class="param-content-wrap">
                <div class="content-box">
                    <div class="content-header">
                        <h4 class="param-common-label">{{'实例详情'}}</h4>
                    </div>
                    <div class="cell">
                        <div class="cell-title">{{'IP地址族名称：'}}</div>
                        <div class="cell-content" ref="name">
                            <span class="truncated">{{instanceDetail.name}}</span>
                            <edit-popover
                                customClass="{{customClass}}"
                                class="edit_popover_class"
                                value="{=instanceDetail.name=}"
                                rule="{{RuleName}}"
                                tip="大小写字母、数字以及-_/.特殊字符，必须以字母或者中文开头，长度1-65"
                                on-edit="updateInstance($event, 'name')"
                            >
                                <outlined-editing-square color="#2468f2" />
                            </edit-popover>
                        </div>
                    </div>
                    <div class="cell">
                        <div class="cell-title">{{'IP地址族ID：'}}</div>
                        <div class="cell-content">{{instanceDetail.ipGroupId}}</div>
                        <s-clip-board
                            class="name-icon copy_icon"
                            text="{{instanceDetail.ipGroupId}}"
                            successMessage="已复制到剪贴板"
                        />
                    </div>
                    <div class="cell">
                        <div class="cell-title">{{'描述：'}}</div>
                        <div class="cell-content" ref="description">
                            <span class="truncated">{{instanceDetail.description || '-'}}</span>
                            <edit-popover
                                class="edit_popover_class"
                                value="{=instanceDetail.description=}"
                                rule="{{RULE.DESC}}"
                                tip="描述不能超过200个字符"
                                on-edit="updateInstance($event, 'description')"
                            >
                                <outlined-editing-square color="#2468f2" />
                            </edit-popover>
                        </div>
                    </div>
                    <div class="cell">
                        <div class="cell-title">{{'IP地址类型：'}}</div>
                        <div class="cell-content">{{instanceDetail.ethertype}}</div>
                        <s-clip-board
                            class="name-icon copy_icon"
                            text="{{instanceDetail.ethertype}}"
                            successMessage="已复制到剪贴板"
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@asComponent('@param-group-detail')
@invokeComp('@edit-popover')
class ParamDetail extends Component {
    static components = {
        'outlined-editing-square': OutlinedEditingSquare
    };

    initData() {
        return {
            klass: 'instance-detail-wrap',
            instance: {},
            rule,
            RULE: RULE.DETAIL_EDIT,
            RuleName: {
                placeholder: '大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65',
                required: true,
                requiredErrorMessage: '名称必填',
                /* eslint-disable */
                pattern: /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{0,64}$/,
                /* eslint-enable */
                patternErrorMessage: '支持大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65'
            },
            customClass: 'param_instance_popover'
        };
    }

    inited() {
        this.data.set('instanceDetail', this.data.get('context').instanceDetail);
    }

    updateInstance(e, type) {
        let payload = {
            [type]: e
        };
        payload.ipGroupUuid = this.data.get('context').instanceDetail.ipGroupUuid;
        this.$http.updateGroupInstance(payload).then(() => {
            Notification.success('修改成功');
            this.data.set(`instanceDetail.${type}`, e);
            if (type === 'name') {
                this.fire('updateName', e);
                this.data.get('context')?.updateName();
            }
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(ParamDetail));
