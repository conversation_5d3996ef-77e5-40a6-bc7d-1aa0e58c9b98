import {Component} from 'san';
import {decorators, html, Processor} from '@baiducloud/runtime';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import {OutlinedLeft} from '@baidu/sui-icon';

import '../../style/list.less';
const {asPage, invokeSUI, invokeComp, invokeAppComp, template, invokeSUIBIZ} = decorators;

const tpl = html` <template class="group-detail-wrap">
    <s-app-detail-page class="param-detail-main-wrap siderbar-tab">
        <div slot="pageTitle" class="instance-info">
            <s-app-link to="/network/#/vpc/group/list" class="page-title-nav"><icon-left />返回</s-app-link>
            <span class="instance-name">{{instanceDetail.name || '-'}}</span>
        </div>
        <app-tab-page class="param-detail-tab" skin="accordion">
            <app-tab-page-panel title="实例详情" url="#/vpc/group/detail?ipGroupUuid={{urlQuery.ipGroupUuid}}">
                <param-group-detail
                    ipGroupUuid="{{urlQuery.ipGroupUuid}}"
                    instanceDetail="{{instanceDetail}}"
                    on-updateName="updateName"
                ></param-group-detail>
            </app-tab-page-panel>
            <app-tab-page-panel title="{{addressTitle}}" url="#/vpc/group/address?ipGroupUuid={{urlQuery.ipGroupUuid}}">
                <ip-group-address
                    instanceDetail="{{instanceDetail}}"
                    ipGroupUuid="{{urlQuery.ipGroupUuid}}"
                ></ip-group-address>
            </app-tab-page-panel>
            <app-tab-page-panel title="关联实例" url="#/vpc/group/association?ipGroupUuid={{urlQuery.ipGroupUuid}}">
                <associate-group-examples
                    ipGroupUuid="{{urlQuery.ipGroupUuid}}"
                    instanceDetail="{{instanceDetail}}"
                ></associate-group-examples>
            </app-tab-page-panel>
        </app-tab-page>
    </s-app-detail-page>
</template>`;

@template(tpl)
@invokeSUIBIZ
@invokeSUI
@invokeAppComp
@invokeComp('@param-group-detail', '@ip-group-address', '@associate-group-examples')
class GroupDetail extends Component {
    static components = {
        'icon-left': OutlinedLeft
    };
    static filters = {};
    initData() {
        return {
            addressTitle: 'IP地址组管理',
            urlQuery: getQueryParams()
        };
    }

    onRegionChange() {
        location.hash = '#/vpc/group/list';
    }

    onBack() {
        location.hash = '#/vpc/group/list';
    }

    inited() {
        this.getDetail();
    }

    getDetail() {
        this.$http
            .getGroupDetail(
                {ipGroupUuid: this.data.get('urlQuery.ipGroupUuid')},
                {'x-silent-codes': ['IpCollection.IpGroupResourceNotExist']}
            )
            .then(res => {
                this.data.set('instanceDetail', res);
            });
    }
    updateName(value) {
        this.data.set('instanceDetail.name', value);
    }
}
export default Processor.autowireUnCheckCmpt(GroupDetail);
