import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {OutlinedRefresh} from '@baidu/sui-icon';

import {setInstanceSearchType} from '@/pages/sanPages/common/enum';
import {setInstanceColumns} from '../list/tableField';
import '../../style/detail.less';

const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent, invokeComp} = decorators;
const tpl = html`
    <div>
        <div class="{{klass}}">
            <div class="param-content-wrap">
                <s-biz-page>
                    <span slot="header">
                        <span class="title">{{title}}</span>
                    </span>
                    <div slot="tb-right" class="right_bar">
                        <s-search
                            width="{{230}}"
                            class="search-warp"
                            value="{=payload.keyword=}"
                            placeholder="{{payload.placeholder}}"
                            on-search="onSearch()"
                        >
                            <s-select
                                slot="options"
                                width="120"
                                datasource="{{payload.searchTypes}}"
                                value="{=payload.keywordType=}"
                                on-change="searchTypeChange($event)"
                            >
                            </s-select>
                        </s-search>
                        <s-button class="s-icon-button" on-click="refresh" track-name="刷新"
                            ><outlined-refresh class="icon-class"
                        /></s-button>
                    </div>
                    <s-table
                        s-ref="table"
                        columns="{{table.columns}}"
                        loading="{{table.loading}}"
                        error="{{table.error}}"
                        datasource="{{table.datasource}}"
                    >
                        <div slot="error">
                            啊呀，出错了?
                            <a href="javascript:;" on-click="refresh">重新加载</a>
                        </div>
                        <div slot="c-instanceName">
                            <!--当前只有企业安全组，不包含安全组-->
                            <a href="#/vpc/enterpriseSecurity/detail?id={{row.instanceUuid}}">{{row.name}}</a
                            ><!--跳转连接-->
                            <br />
                            <span>{{row.instanceId}}</span>
                        </div>
                        <div slot="c-instanceType">{{row.instanceType | typeText}}</div>
                        <div slot="c-description">{{row.description || '-'}}</div>
                    </s-table>
                    <s-pagination
                        slot="footer"
                        layout="{{'total, pageSize, pager, go'}}"
                        pageSize="{{pager.pageSize}}"
                        total="{{pager.total}}"
                        page="{{pager.page}}"
                        on-pagerChange="onPagerChange"
                        on-pagerSizeChange="onPagerSizeChange"
                    />
                </s-biz-page>
            </div>
        </div>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@asComponent('@associate-group-examples')
@invokeComp('@edit-popover')
class ParamDetail extends Component {
    static components = {
        'outlined-refresh': OutlinedRefresh
    };
    static filters = {
        typeText(value) {
            return value === 'ESG' ? '企业安全组' : '普通安全组';
        }
    };

    initData() {
        return {
            klass: ['associate-examples-wrap', 'ip-address-wrap'],
            title: '关联实例',
            payload: {
                keyword: '',
                placeholder: '请输入实例名称进行搜索',
                keywordType: 'name',
                searchTypes: setInstanceSearchType.toArray()
            },
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                columns: setInstanceColumns.slice(),
                datasource: [],
                selectedItems: []
            },
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            }
        };
    }

    inited() {
        this.loadPage();
    }

    loadPage() {
        const param = this.getSearchCriteria();
        this.data.set('table.loading', true);
        this.$http
            .ipGroupBindeInstance(param, {'x-silent-codes': ['IpCollection.IpGroupResourceNotExist']})
            .then(res => {
                this.data.set('table.loading', false);
                this.data.set('table.datasource', res.result);
                this.data.set('pager.total', res.totalCount);
            });
    }

    searchTypeChange(e) {
        this.data.set('payload.keyword', '');
        this.data.set('payload.placeholder', `请输入${setInstanceSearchType.getTextFromValue(e.value)}进行搜索`);
    }

    onSearch() {
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    getSearchCriteria() {
        let {pager, payload} = this.data.get('');
        let searchParam = {
            pageNo: pager.page,
            pageSize: pager.pageSize,
            keywordType: payload.keywordType,
            keyword: payload.keyword,
            ipGroupUuid: this.data.get('context').ipGroupUuid
        };
        return u.extend({}, searchParam);
    }

    refresh() {
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }

    onPagerSizeChange(e) {
        this.data.set('pager.pageSize', e.value.pageSize);
        this.data.set('pager.page', 1);
        this.loadPage();
    }
}
export default San2React(Processor.autowireUnCheckCmpt(ParamDetail));
