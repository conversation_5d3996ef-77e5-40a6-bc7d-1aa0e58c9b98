.vpc-param-create {
    font-size: 12px;
    .s-dialog-wrapper {
        overflow: inherit !important;
    }
    .project-name {
        width: 97px;
        display: inline-block;
    }
    .s-form-item {
        margin-top: 20px;
        margin-bottom: 0;
        .s-row-flex {
            align-items: flex-start;
        }
        &:first-child {
            margin: 0;
        }
        .s-form-item-label {
            width: 81px !important;
            text-align: left;
        }
    }
    .s-form-item-content {
        margin-left: 80px;
    }
    .ip-radio {
        display: inline-block;
        float: left;
        .s-radio {
            margin: 10px;
        }
    }
    .ip-item {
        .s-form-item-control-wrapper {
            max-width: 510px;
        }
        .s-button {
            padding-left: 0;
            .ip-input {
                float: left;
            }
            .ui-form-item-invalid-label {
                color: #d0021b;
                display: block;
                float: left;
            }
        }
        .text-item {
            line-height: 30px;
        }
        .invalid-label {
            margin-top: 12px;
            color: #eb5252;
        }
        .error_select {
            .s-input-suffix-container {
                border-color: #f33e3e;
            }
        }
        .error_tip {
            color: #f33e3e;
            margin-top: 5px;
        }
        .s-form {
            .s-form-item {
                margin: 24px 0 0 !important;
            }
            .s-form-item-label {
                width: 118px;
            }
            .center_class {
                .s-row {
                    .s-form-item-control-wrapper {
                        line-height: 30px;
                    }
                }
            }
            .s-form-item-label-required > label:before {
                left: -7px;
                position: absolute;
            }
            .label_class {
                .inline-tip {
                    top: 3px;
                    position: relative;
                    .s-tip-warning {
                        justify-content: center;
                        .warning_class {
                            fill: #999;
                        }
                    }
                    .s-tip:hover .s-icon path {
                        fill: #2468f2 !important;
                    }
                }
            }
            .plus_class {
                line-height: 15px;
            }
        }
        .ip-radio {
            .s-radio {
                margin-left: 0;
            }
        }
        .radio_eni_group {
            .s-input {
                display: block;
            }
        }
        .s-form-item-label {
            .inline-tip {
                top: 3px;
                position: relative;
                .s-tip-warning {
                    justify-content: center;
                    .warning_class {
                        fill: #999;
                    }
                }
                .s-tip:hover .s-icon path {
                    fill: #2468f2 !important;
                }
            }
        }
        .s-form-item-label {
            label:before {
                left: -7px;
                position: absolute;
                content: '*';
                color: #e8684a;
                margin-right: 4px;
            }
        }
    }
    .ip_type_class {
        .s-form-item-control-wrapper {
            margin-top: 4px;
        }
    }
    .ui-form-item-invalid-label {
        color: #f33e3e !important;
        display: block;
    }
}

.vpc-group-create {
    .s-form-item {
        margin-top: 20px;
        margin-bottom: 0;
        .s-row-flex {
            align-items: flex-start;
        }
        &:first-child {
            margin: 0;
        }
        .s-form-item-label {
            width: 100px !important;
            text-align: left;
        }
    }
    .ip_type_class {
        .s-form-item-control-wrapper {
            margin-top: 4px;
        }
    }
    .transfer_form_wrap {
        margin-top: 8px;
        .s-transfer-wrapper-left {
            width: 300px !important;
            .s-input-area {
                input {
                    width: 230px !important;
                }
            }
            .s-transfer-content-wrapper {
                height: 100% !important;
            }
            .s-transfer-content {
                .s-transfer-content-wrapper {
                    .s-transfer-content-item > label {
                        overflow: hidden !important;
                        text-overflow: ellipsis !important;
                        white-space: nowrap !important;
                        max-width: 80% !important;
                    }
                }
            }
        }
        .s-transfer-wrapper-right {
            width: 300px !important;
            .s-transfer-content {
                .s-transfer-content-wrapper {
                    .s-transfer-content-item > label {
                        overflow: hidden !important;
                        text-overflow: ellipsis !important;
                        white-space: nowrap !important;
                        max-width: 80% !important;
                    }
                }
            }
        }
    }
    .param_ip_class {
        .s-form-item-label {
            label:before {
                left: -7px;
                position: absolute;
                content: '*';
                color: #e8684a;
                margin-right: 4px;
            }
        }
    }
}

.locale-en {
    .vpc-param-create .s-form-item .s-form-item-label {
        width: 115px !important;
    }
    .vpc-param-create .name-wrap {
        .s-form-item-control-wrapper {
            flex: 1;
        }
    }
    .vpc-group-create .name-wrap {
        .s-form-item-control-wrapper {
            flex: 1;
        }
    }
}

.notification-warning-class {
    fill: #ff9326;
}
