.ip-address-sidebar {
    height: 600px;
    .menu-item a {
        display: block;
        padding: 10px 20px;
        color: #333;
    }
    .menu-item.sidebar-current a {
        border-left: 4px solid #2468f2;
        color: #2468f2;
        background: #fff;
    }
}

.param-detail-wrap {
    .param-detail-main-wrap {
        .s-detail-page-content {
            margin: 16px;
        }
        .param-detail-tab {
            padding: 0px;
        }
        .skin-accordion-tab {
            border: unset;
            .bui-tab-content {
                padding: 0 0;
            }
        }
        .instance-not-found-class {
            height: 100%;
        }
    }
    .instance-info {
        display: flex;
        align-items: center;
        .instance-name {
            padding-left: 16px;
            font-size: 16px;
            font-weight: 500;
            color: #151b26;
        }
    }
}

.group-detail-wrap {
    .param-detail-main-wrap {
        .s-detail-page-content {
            margin: 16px;
        }
        .param-detail-tab {
            padding: 0px;
        }
        .skin-accordion-tab {
            border: unset;
            .bui-tab-content {
                padding: 0 0;
            }
        }
        .instance-not-found-class {
            height: 100%;
        }
    }
    .instance-info {
        display: flex;
        align-items: center;
        .instance-name {
            padding-left: 16px;
            font-size: 16px;
            font-weight: 500;
            color: #151b26;
        }
    }
}

.instance-detail-wrap {
    .param-content-wrap {
        .cell {
            width: 33%;
            display: inline-block;
            margin-bottom: 16px;
            .cell-title {
                display: inline-block;
                vertical-align: top;
                color: #5e626a;
                margin-right: 16px;
                width: 84px;
            }
            .cell-content {
                display: inline-block;
                color: #151a26;
                max-width: 70%;
                word-break: break-all;
                position: relative;
            }
            .icon-edit,
            .icon-copy {
                color: #2468f2;
                font-size: 12px;
                margin-left: 10px;
            }
        }
    }
}
.param-content-wrap {
    display: flex;
    .subsidebar-wrap {
        min-width: 140px;
        background: #f5f5f5;
    }
    .param-common-label {
        display: block;
        color: #151b26;
        line-height: 24px;
        font-weight: 500;
        margin-bottom: 16px;
        font-size: 16px;
        zoom: 1;
    }
    .content-box {
        padding: 24px;
        width: 100%;
    }
}

.ip-address-wrap {
    padding: 24px;
    .s-biz-page-header {
        border-bottom: none !important;
        margin: 0 !important;
        display: inline;
        .title {
            color: #151b26;
            line-height: 24px;
            font-weight: 500;
            font-size: 16px;
        }
    }
    .s-biz-page-content {
        margin: 0 0 !important;
    }
    .search-wrap {
        .s-select {
            .s-input-area {
                input {
                    width: 60px !important;
                }
            }
        }
    }
    .s-icon-button {
        margin-right: 6px;
    }
}
.associate-examples-wrap {
    .s-biz-page-toolbar {
        overflow: hidden;
    }
}

.param_instance_popover {
    .s-popover-content {
        .s-input {
            input {
                width: 320px !important;
            }
        }
    }
}

.no-instance-content .s-detail-page-content {
    margin: 0px !important;
}

.loading-content .s-detail-page-content {
    margin: 0px !important;
}
