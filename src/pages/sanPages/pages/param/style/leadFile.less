.ipaddress-lead-file {
    .s-dialog-content {
        max-width: 980px;
    }
    .s-form {
        font-size: 12px;
        .s-form-item {
            margin-top: 20px;

            .s-form-item-label {
                color: #666;
                height: 30px;
                line-height: 30px;
                label {
                    float: left;
                }
            }

            .icon-ok-reverse {
                color: #2cb663;
                font-size: 12px;
            }
            .icon-fail-reverse {
                color: #eb5252;
                font-size: 12px;
            }

            .webuploader-pick {
                background-color: #108cee;
                color: #ffffff;

                .icon-plus {
                    font-size: 12px;
                    margin: 0 2px;
                }
            }
            .webuploader-pick-hover {
                background-color: #209bfd;
            }

            .rule-message {
                margin-left: 20px;
                display: inline-block;
                vertical-align: middle;
                .wrong-rules {
                    color: #eb5252;
                    margin-left: 10px;
                }
            }

            .upload-message {
                display: inline-block;
                vertical-align: middle;
                height: 30px;
                line-height: 30px;
                margin-left: 10px;

                &:hover {
                    background-color: #f7f7f7;
                }

                .upload-file-name {
                    display: inline-block;
                    margin: 0 4px;
                    .icon-link {
                        font-size: 12px;
                    }
                }

                .upload-progress {
                    display: inline-block;
                    margin-right: 30px;
                }

                .upload-success {
                    color: #2cb663;
                }

                .upload-fail {
                    color: #eb5252;
                }

                .delete-file {
                    display: none;
                }
                &:hover .delete-file {
                    display: inline-block;
                }
                .skin-stringfy-button {
                    color: #000000;
                    .iconfont {
                        color: #000000;
                    }
                }
            }

            .table-wrong {
                color: #eb5252;
            }
            .s-tip {
                border: none;
            }
            .s-tip:hover {
                background: none;
            }
        }
    }
    .status-warp {
        width: 12px;
    }
    .s-form-item-control {
        .s-table {
            table-layout: auto;
            .s-table-content {
                min-width: 900px;
            }
        }
    }
    .repeat_class {
        color: #d0021b;
        margin-right: 12px;
    }
}
