import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {Table} from '@baidu/sui';
import {checker} from '@baiducloud/bce-opt-checker';
import {OutlinedPlus, OutlinedRefresh, OutlinedLink, OutlinedEditingSquare} from '@baidu/sui-icon';

import CreateParam from '../page/create/create';
import Confirm from '@/pages/sanPages/components/confirm';
import CreateGroup from '../page/create/groupCreate';
import {ipAddressType, ipAddressGroupSearchType, ipAddressFamilySearchType} from '@/pages/sanPages/common/enum';
import {columns} from '../page/list/tableField';
import rules from '../rule';
import {decryptUrlBtoa} from '@/pages/sanPages/utils/helper';
import '../style/addressList.less';

const {asComponent, invokeSUI, invokeSUIBIZ, withSidebar, invokeAppComp, template, invokeComp} = decorators;
const tpl = html`
    <div class="param_list_wrap">
        <s-app-list-page class="{{klass}}">
            <div slot="bulk">
                <s-tooltip
                    s-if="addressType === 'set'"
                    trigger="{{createBtn.disable ? 'hover' : ''}}"
                    placement="right"
                    class="add_havip"
                >
                    <!--bca-disable-next-line-->
                    <div slot="content">{{createBtn.message | raw}}</div>
                    <s-button skin="primary" disabled="{{createBtn.disable}}" on-click="onCreate">
                        <outlined-plus />
                        创建{{addressType | addressTitle}}
                    </s-button>
                </s-tooltip>
                <s-tooltip
                    s-if="addressType === 'group'"
                    trigger="{{createGroupBtn.disable ? 'hover' : ''}}"
                    placement="right"
                    class="add_havip"
                >
                    <!--bca-disable-next-line-->
                    <div slot="content">{{createGroupBtn.message | raw}}</div>
                    <s-button skin="primary" disabled="{{createGroupBtn.disable}}" on-click="onCreate">
                        <outlined-plus />
                        创建{{addressType | addressTitle}}
                    </s-button>
                </s-tooltip>
                <s-tooltip class="left_class" trigger="{{batchDelete.disable ? 'hover' : ''}}" placement="top">
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{batchDelete.message | raw}}
                    </div>
                    <s-button on-click="batchDeleteMethod" disabled="{{batchDelete.disable}}"> 删除</s-button>
                </s-tooltip>
            </div>
            <div slot="filter">
                <s-search
                    width="{{180}}"
                    class="search-warp"
                    value="{=keyword=}"
                    placeholder="{{payload.placeholder}}"
                    on-search="onSearch($event)"
                >
                    <s-select
                        slot="options"
                        datasource="{{payload.searchTypes}}"
                        value="{=keywordType=}"
                        on-change="searchTypeChange($event)"
                    >
                    </s-select>
                </s-search>
                <s-button class="s-icon-button" on-click="refresh" track-name="刷新"
                    ><outlined-refresh class="icon-class"
                /></s-button>
            </div>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                on-selected-change="tableSelected($event)"
                selection="{=table.selection=}"
            >
                <div slot="empty">
                    <s-empty on-click="onCreate"> </s-empty>
                </div>
                <div slot="error">
                    啊呀，出错了
                    <a href="javascript:;" on-click="refresh">重新加载</a>
                </div>
                <div slot="c-id">
                    <span class="truncated" title="{{row.name}}">
                        <a href="#/vpc/param/detail?ipSetUuid={{row.ipSetUuid}}" s-if="addressType === 'set'">
                            {{row.name}}
                        </a>
                        <a href="#/vpc/group/detail?ipGroupUuid={{row.ipGroupUuid}}" s-else> {{row.name}} </a>
                    </span>
                    <s-popover
                        s-ref="{{'nameEdit'+rowIndex}}"
                        placement="top"
                        trigger="click"
                        class="edit-popover-class"
                    >
                        <div class="edit-wrap" slot="content">
                            <s-input
                                value="{=instance.name=}"
                                width="320"
                                placeholder="请输入名称"
                                on-input="onEditInput($event, rowIndex, 'name')"
                            />
                            <div class="edit-tip">
                                支持大小写字母，数字，中文和"-_/.”以字母或者中文开头，不超过65个字
                            </div>
                            <s-button
                                skin="primary"
                                s-ref="{{'nameEditBtn' + rowIndex}}"
                                disabled="{{true}}"
                                on-click="editConfirm(row, rowIndex, 'name')"
                            >
                                确定</s-button
                            >
                            <s-button on-click="editCancel(row, rowIndex, 'name')">取消</s-button>
                        </div>
                        <outlined-editing-square class="name-icon" on-click="beforeEdit(row, 'name')" />
                    </s-popover>
                    <br />
                    <span class="truncated">{{row.ipSetId || row.ipGroupId}}</span>
                    <s-clip-board
                        s-if="row.ipSetId || row.ipGroupId"
                        text="{{row.ipSetId || row.ipGroupId}}"
                        class="name-icon"
                    />
                </div>
                <div slot="c-description">
                    {{row.description || '-'}}
                    <s-popover
                        s-ref="{{'descEdit'+rowIndex}}"
                        trigger="click"
                        class="edit-popover-class edit-popover-desc"
                    >
                        <div class="edit-wrap" slot="content">
                            <s-input
                                value="{=instance.desc=}"
                                width="160"
                                placeholder="请输入描述"
                                on-input="onEditInput($event, rowIndex, 'desc')"
                            />
                            <s-button
                                skin="primary"
                                s-ref="{{'descEditBtn'+rowIndex}}"
                                disabled="{{true}}"
                                on-click="editConfirm(row, rowIndex, 'desc')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel(row, rowIndex, 'desc')">取消</s-button>
                        </div>
                        <outlined-editing-square s-if="row.name" class="name-icon" on-click="beforeEdit(row, 'desc')" />
                    </s-popover>
                </div>
                <div slot="c-associateNum">
                    <a href="javascript:void(0)" on-click="onAssociate(row)">{{row.associateNum}}</a
                    ><!--跳转连接-->
                </div>
                <div slot="c-option" class="operation_class">
                    <s-button skin="stringfy" on-click="onIpManage(row)">IP地址管理</s-button>
                    <s-button skin="stringfy" on-click="onAssociate(row)">已关联实例</s-button>
                    <s-tooltip
                        content="当前IP地址组已关联实例，无法删除"
                        trigger="{{row.associateNum ? 'hover' : ''}}"
                        placement="right"
                    >
                        <s-button on-click="onDelete(row)" disabled="{{row.associateNum}}" skin="stringfy">
                            删除</s-button
                        >
                    </s-tooltip>
                </div>
            </s-table>
            <s-pagination
                s-if="{{pager.total}}"
                slot="pager"
                layout="{{'total, pageSize, pager, go'}}"
                pageSize="{{pager.pageSize}}"
                total="{{pager.total}}"
                page="{{pager.page}}"
                on-pagerChange="onPagerChange"
                on-pagerSizeChange="onPagerSizeChange"
            />
        </s-app-list-page>
    </div>
`;

@template(tpl)
@asComponent('@address-group-list')
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@invokeComp('@create-param', '@create-group-param')
@withSidebar({active: 'vpc-param-list'})
class AddressList extends Component {
    initData() {
        return {
            delete: {
                disable: true,
                message: ''
            },
            payload: {
                keyword: '',
                placeholder: '',
                keywordType: '',
                searchTypes: []
            },
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                columns: columns.slice(),
                datasource: [],
                selectedItems: []
            },
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            addressType: '',
            batchDelete: {
                disable: false,
                message: ''
            },
            createBtn: {
                disable: false,
                message: ''
            },
            createGroupBtn: {
                disable: false,
                message: ''
            },
            urlQuery: getQueryParams()
        };
    }
    static components = {
        's-table': Table,
        'outlined-plus': OutlinedPlus,
        'outlined-refresh': OutlinedRefresh,
        'outlined-link': OutlinedLink,
        'outlined-editing-square': OutlinedEditingSquare
    };
    static filters = {
        addressTitle(value) {
            return ipAddressType.getTextFromValue(value);
        }
    };
    inited() {
        this.data.set('addressType', this.data.get('context').addressType);
        this.setTableHeader();
        this.getGroupQuota();
    }
    attached() {
        this.loadPage();
        this.watch('addressType', value => {
            this.loadPage();
        });
        const cmcConfig = this.data.get('urlQuery.cmcConfig');
        if (cmcConfig) {
            const parsedCmcConfig = decryptUrlBtoa(cmcConfig);
            this.onCreate(parsedCmcConfig);
        }
    }

    // 搜索框keywordType切换
    searchTypeChange(e) {
        this.data.set('keyword', '');
        let addressType = this.data.get('addressType');
        let getValue = addressType === 'set' ? ipAddressGroupSearchType : ipAddressFamilySearchType;
        this.data.set('payload.placeholder', `请输入${getValue.getTextFromValue(e.value)}进行搜索`);
    }

    // 创建实例
    async onCreate(cmcConfig = {}) {
        let obj = {
            data: {
                vpcId: window.$storage.get('vpcId'),
                addressType: this.data.get('addressType')
            }
        };
        if (Object.keys(cmcConfig).length && cmcConfig.ipVersion) {
            obj.data.cmcConfig = cmcConfig;
        }
        if (this.data.get('addressType') === 'group') {
            const dialog = new CreateGroup(obj);
            dialog.on('success', () => {
                this.loadPage('quota');
            });
            dialog.attach(document.body);
            return;
        }
        const dialog = new CreateParam(obj);
        dialog.on('success', () => {
            this.loadPage('quota');
        });
        dialog.attach(document.body);
    }

    // 批量删除操作
    batchDeleteMethod() {
        let selectedItems = this.data.get('table.selectedItems');
        let payload = {};
        if (this.data.get('addressType') === 'set') {
            payload.ipSetUuids = selectedItems.map(item => item.ipSetUuid);
        } else {
            payload.ipGroupUuids = selectedItems.map(item => item.ipGroupUuid);
        }
        let confirm = new Confirm({
            data: {
                title: '删除前确认',
                content: '确认删除所选中的实例吗？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            let req = this.data.get('addressType') !== 'group' ? 'ipSetBatchDelete' : 'ipGroupBatchDelete';
            this.$http[req](payload).then(() => this.loadPage('quota'));
        });
    }

    // 刷新操作
    refresh() {
        this.loadPage('quota');
    }

    // 根据tab设置table头，以及搜索的关键词
    setTableHeader() {
        if (this.data.get('addressType') === 'group') {
            this.data.splice('table.columns', [
                0,
                1,
                {
                    name: 'id',
                    label: 'IP地址族名称/ID',
                    width: 200
                }
            ]);
            this.data.set('payload.placeholder', '请输入IP地址族名称进行搜索');
            this.data.set('payload.searchTypes', [
                {value: 'IP_GROUP_NAME', text: 'IP地址族名称'},
                {value: 'IP_GROUP_ID', text: 'IP地址族ID'}
            ]);
            this.data.set('keywordType', 'IP_GROUP_NAME');
        } else {
            this.data.splice('table.columns', [
                0,
                1,
                {
                    name: 'id',
                    label: 'IP地址组名称/ID',
                    width: 200
                }
            ]);
            this.data.set('payload.placeholder', '请输入IP地址组名称进行搜索');
            this.data.set('payload.searchTypes', [
                {value: 'IP_SET_NAME', text: 'IP地址组名称'},
                {value: 'IP_SET_ID', text: 'IP地址组ID'}
            ]);
            this.data.set('keywordType', 'IP_SET_NAME');
        }
    }

    loadPage(quota) {
        this.data.set('table.loading', true);
        let payload = this.getPayload();
        this.resetTable();
        let req = this.data.get('addressType') !== 'group' ? 'ipSetList' : 'ipGroupList';
        this.$http[req](payload).then(res => {
            if (quota) {
                if (this.data.get('addressType') !== 'group') {
                    this.getQuota();
                } else {
                    this.getGroupQuota();
                }
            }
            this.data.set('table.datasource', res.result);
            this.data.set('pager.total', res.totalCount);
            this.data.set('table.loading', false);
        });
    }
    onSearch() {
        this.data.set('pager.page', 1);
        this.loadPage();
    }
    getPayload() {
        const {pager, keyword, keywordType} = this.data.get('');
        let payload = {
            pageNo: pager.page,
            pageSize: pager.pageSize,
            keywordType,
            keyword: keyword || ''
        };
        return payload;
    }
    resetTable() {
        this.data.set('table.selection', {
            mode: 'multi',
            selectedIndex: []
        });
        this.data.set('table.selectedItems', []);
    }
    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }

    onPagerSizeChange(e) {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', e.value.pageSize);
        this.loadPage();
    }
    onRegionChange() {
        location.reload();
    }
    beforeEdit(row, type) {
        const keyMap = {
            name: 'name',
            desc: 'description'
        };
        this.data.set(`instance.${type}`, row[keyMap[type]]);
    }

    onEditInput(e, rowIndex, type) {
        let result = true;
        if (type === 'name') {
            const pattern = /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{0,64}$/;
            result = pattern.test(e.value);
        } else if (type === 'description') {
            result = e.value.length <= 200;
        }
        this.data.set(`instance.${type}`, e.value);
        this.ref(`${type}EditBtn${rowIndex}`).data.set('disabled', !result);
    }

    editConfirm(row, rowIndex, type) {
        const keyMap = {
            name: 'name',
            desc: 'description'
        };
        const payload = {
            [keyMap[type]]: this.data.get(`instance.${type}`)
        };
        this.ref(`${type}EditBtn${rowIndex}`).data.set('disabled', true);
        let req = this.data.get('addressType') !== 'group' ? 'updateSetInstance' : 'updateGroupInstance';
        if (this.data.get('addressType') !== 'group') {
            payload.ipSetUuid = row.ipSetUuid;
        } else {
            payload.ipGroupUuid = row.ipGroupUuid;
        }
        this.$http[req](payload).then(() => {
            this.loadPage();
            this.ref(`${type}EditBtn${rowIndex}`).data.set('disabled', false);
        });
    }

    editCancel(row, rowIndex, type) {
        this.ref(`${type}Edit${rowIndex}`).data.set('visible', false);
    }
    onDelete(row) {
        let confirm = new Confirm({
            data: {
                title: '删除前确认',
                content: '确认删除所选中的实例吗？'
            }
        });
        let payload = {};
        if (this.data.get('addressType') === 'set') {
            payload.ipSetUuids = [row.ipSetUuid];
        } else {
            payload.ipGroupUuids = [row.ipGroupUuid];
        }
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            let req = this.data.get('addressType') !== 'group' ? 'ipSetBatchDelete' : 'ipGroupBatchDelete';
            this.$http[req](payload).then(() => this.loadPage('quota'));
        });
    }
    onIpManage(row) {
        if (this.data.get('addressType') !== 'set') {
            location.hash = `#/vpc/group/address?ipGroupUuid=${row.ipGroupUuid}`;
        } else {
            location.hash = `#/vpc/param/address?ipSetUuid=${row.ipSetUuid}`;
        }
    }
    onAssociate(row) {
        if (this.data.get('addressType') !== 'set') {
            location.hash = `#/vpc/group/association?ipGroupUuid=${row.ipGroupUuid}`;
        } else {
            location.hash = `#/vpc/param/association?ipSetUuid=${row.ipSetUuid}`;
        }
    }
    tableSelected({value}) {
        this.data.set('table.selectedItems', value.selectedItems);
        let {batchDelete} = checker.check(rules, value.selectedItems, '');
        this.data.set('batchDelete', batchDelete);
    }
    getIpSet() {
        let payload = {
            pageNo: 1,
            pageSize: 10000
        };
        return this.$http.ipSetList(payload).then(res => {
            let array = [];
            res.result.forEach(item => {
                array.push({
                    label: item.name + '（' + item.ipSetId + '）',
                    value: item.ipSetUuid,
                    ipType: item.ethertype
                });
            });
            this.data.set('tableDatasource', array);
        });
    }
    getQuota() {
        this.$http.ipSetQuota().then(res => {
            let {createIpSet} = checker.check(rules, '', 'createIpSet', {quota: res});
            this.data.set('createBtn.disable', createIpSet.disable);
            this.data.set('createBtn.message', createIpSet.message);
        });
    }
    getGroupQuota() {
        this.$http.ipGroupQuota().then(res => {
            let {createIpGroup} = checker.check(rules, '', 'createIpGroup', {quota: res});
            this.data.set('createGroupBtn.disable', createIpGroup.disable);
            this.data.set('createGroupBtn.message', createIpGroup.message);
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(AddressList));
