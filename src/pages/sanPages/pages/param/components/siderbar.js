import {defineComponent} from 'san';
import {html} from '@baiducloud/runtime';

import '../style/detail.less';
/* eslint-disable */
const template = html`
<template>
<div class="ip-address-sidebar">
    <div class="menu-item-wrap">
        <div s-for="item in config" class="{{item | itemClass}}">
            <a href="{{item.disabled ? 'javascript:void(0);' : item.url}}"
                dataTrackId="{{dataTrackId}}" dataTrackName="{{item.name}}">
                {{item.name}}
            </a>
        </div>
    </div>
</div>
</template>
`;
/* eslint-enable */
export default defineComponent({
    template,
    initData() {
        return {
            current: '',
            config: [],
            dataTrackId: 'ipAddress_sub_sidebar'
        };
    },
    filters: {
        itemClass(item) {
            const klass = ['menu-item'];
            const current = this.data.get('current');
            current === item.id && klass.push('sidebar-current');
            item.disabled && klass.push('disabled');
            return klass;
        }
    },
    inited() {
        this.data.set('config', [
            {
                id: 'detail',
                name: '实例详情',
                url: '#/vpc/param/detail'
            },
            {
                id: 'ipAddress',
                name: 'IP地址',
                url: '#/vpc/param/address'
            },
            {
                id: 'associate',
                name: '关联实例',
                url: '#/vpc/param/association'
            }
        ]);
    }
});