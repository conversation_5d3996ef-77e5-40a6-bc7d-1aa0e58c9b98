import {ContextService} from '@/pages/sanPages/common';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';

export default {
    batchDelete: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            custom(data) {
                let associateList = data.map(item => item.associateNum);
                if (associateList.findIndex(item => item > 0) !== -1) {
                    return {
                        disable: true,
                        message: '当前实例中包含已关联的实例'
                    };
                }
            }
        }
    ],
    deleteAddress: [
        {required: true, message: '请先选择实例'},
        {
            custom(data, detail) {
                if (detail.associateNum && detail.ipSets.length === data.length) {
                    return {
                        disable: true,
                        message: '已关联实例的IP族，至少包含一个IP组'
                    };
                }
            }
        }
    ],
    deleteIpAddress: [
        {required: true, message: '请先选择实例'},
        {
            custom(data, detail) {
                if (detail.associateNum && detail.ipAddresses.length === data.length) {
                    return {
                        disable: true,
                        message: '已关联实例的IP组，至少包含一个IP'
                    };
                }
            }
        }
    ],
    createIpSet: [
        {
            required: false,
            message: ''
        },
        {
            custom(data, options) {
                if (options.quota && options.quota.free <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: 'IP地址组配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message:
                                `IP地址组配额不足，如需增加配额请提交<a href="${
                                    ContextService.Domains.ticket
                                }/#/ticket/create" target="_blank">工单</a>`
                        };
                    }
                }
            }
        }
    ],
    createIpGroup: [
        {
            required: false,
            message: ''
        },
        {
            custom(data, options) {
                if (options.quota && options.quota.free <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: 'IP地址族配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message:
                                `IP地址族配额不足，如需增加配额请提交<a href="${
                                    ContextService.Domains.ticket
                                }/#/ticket/create" target="_blank">工单</a>`
                        };
                    }
                }
            }
        }
    ]
};
