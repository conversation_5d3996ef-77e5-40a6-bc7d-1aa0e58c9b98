.vpc-path-create {
    background: #f7f7f9;
    width: 100%;
    min-height: 100%;
    padding-bottom: 20px;
    .content-box {
        text-align: left;
        .tip {
            font-size: 12px;
            margin-top: 10px;
            color: #999;
        }
        .tip-icon {
            color: #9e9898;
            border: 1px solid #9e9898;
            margin-left: 10px;
            box-sizing: content-box;
            &:hover {
                border-color: #108cee;
                color: #108cee;
            }
        }
        .s-form-item-with-help {
            margin-bottom: 24px;
        }
        .frequency-radio {
            float: left;
        }
        .frequency-unit {
            padding-left: 10px;
        }
        .destip-wrapper {
            display: table;
            .destip {
                display: table-cell;
            }
            .destip-port-wrapper {
                .destip {
                    float: left;
                }
            }
        }
    }
    .s-probe-radio {
        display: inline-block;
    }
    .ip-radio {
        .s-radio-group {
            .s-radio-checked {
                margin: 10px;
            }
        }
    }
    .destip {
        .s-row {
            margin-right: 20px;
            .s-form-item-control-wrapper {
                .s-form-item {
                    display: inline;
                    float: left;
                }
            }
        }
    }
    .form-part-wrap {
        border-radius: 6px;
        width: calc(~'100vw - 32px');
        background: #fff;
        margin: 16px;
        padding: 24px;
        h4 {
            margin: 0;
            display: block;
            zoom: 1;
            font-size: 16px;
            color: #151b26;
            font-weight: 500;
            margin: 8px 0;
            margin: 0;
        }
        .s-form-item-label {
            width: 106px;
            height: 30px;
        }
    }
    .buybucket {
        width: 100%;
        left: 0;
        height: 60px;
        .buybucket-container {
            width: auto;
            float: left;
            display: inline-flex;
            height: 48px;
            transform: translateY(12.5%);
            display: flex;
            align-items: center;
        }
    }
    .destip-wrapper {
        margin-top: 24px;
    }
    .label_class {
        .inline-tip {
            top: 3px;
            position: relative;
            .s-tip-warning {
                justify-content: center;
                .warning_class {
                    fill: #999;
                }
            }
            .s-tip:hover .s-icon path {
                fill: #2468f2 !important;
            }
        }
    }
    .destip-other {
        .destport {
            margin-top: 0 !important;
        }
    }
    .destip-dns {
        margin-top: 0;
    }

    .probe-route-wrap {
        .s-form-item {
            .s-form-item-label {
                .probe-route-type {
                    display: inline;
                    float: left;
                }
            }
        }
        .probe-route-select {
            margin-right: 20px !important;
            margin-top: 0 !important;
        }
    }
    .path-wrap {
        .s-form-item {
            display: inline-block;
            .s-form-item-control-wrapper {
                width: auto;
            }
        }
        .s-form-item-label > label {
            margin-right: 0;
        }
        .s-form-item-no-label {
            margin-left: 4px !important;
        }
    }
    .no-label-class {
        .s-form-item-label {
            display: none;
        }
    }
    .no-width-error-class {
    }
}
.app-create-page-footer {
    padding-left: 182px;
    display: inline-block;
}

.route-item {
    .probe-route-type {
        float: left;
        margin-right: 10px;
    }
}
.locale-en {
    .vpc-path-create .form-part-wrap .s-form-item-label {
        width: 138px;
    }
}
