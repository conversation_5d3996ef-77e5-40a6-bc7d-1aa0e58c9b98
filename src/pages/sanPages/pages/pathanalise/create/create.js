import u from 'lodash';
import {html, decorators, CreatePage, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {Form, Input, Select, Radio, Switch, Button, Tooltip, Icon} from '@baidu/sui';
import {OutlinedPlus} from '@baidu/sui-icon';
import {serviceTypeUrl, checkSts} from '@/pages/sanPages/utils/config';
import {pathTypeList, RouteStatus, VpnStatus, DcGatewayStatus} from '@/pages/sanPages/common/enum';

import {formValidator} from '../components/helper';
import {AppCreatePage, Tip} from '@baidu/sui-biz';
import {$flag as FLAG, getVpcAvaliableRegion} from '@/pages/sanPages/utils/helper';
import rule from '@/pages/sanPages/utils/rule';

import './style.less';
const {asPage, invokeSUI, invokeSUIBIZ, template} = decorators;
const kXhrOptions = {'X-silence': true};
const {IP} = rule;
const tpl = html`
    <div class="vpc-path-create">
        <s-bizpage backTo="{{pageNav.backUrl}}" backToLabel="{{pageNav.backLabel}}" pageTitle="{{pageNav.title}}">
            <div class="content-box form-part-wrap">
                <h4>配置信息</h4>
                <s-form s-ref="form" rules="{{validateRule}}" data="{=formData=}" label-align="left">
                    <s-form-item
                        label="路径名称："
                        prop="name"
                        help="大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65"
                    >
                        <s-input width="{{300}}" value="{=formData.name=}" />
                    </s-form-item>
                    <div class="path-wrap">
                        <s-form-item label="源：" prop="sourceType" class="no-width-error-class">
                            <s-select value="{=formData.sourceType=}" on-change="sourceTypeChange">
                                <s-select-option
                                    s-for="item in sourceTypeList"
                                    value="{{item.value}}"
                                    disabled="{{item.disabled}}"
                                    label="{{item.text}}"
                                >
                                    <s-tooltip
                                        trigger="{{item.disabled ? 'hover' : ''}}"
                                        placement="right"
                                        layerWidth="300"
                                    >
                                        <div slot="content">
                                            <!--bca-disable-next-line-->
                                            {{item.value | noOpenTip | raw}}
                                        </div>
                                        <div>{{item.text}}</div>
                                    </s-tooltip>
                                </s-select-option>
                            </s-select>
                        </s-form-item>
                        <s-form-item
                            label="源地域："
                            prop="sourceRegion"
                            class="no-label-class"
                            s-if="formData.sourceType !== 'PUBLIC_IP' && formData.sourceType !== 'DC'"
                        >
                            <s-select
                                width="{{140}}"
                                datasource="{{regionList}}"
                                value="{=formData.sourceRegion=}"
                                on-change="sourceRegionChange($event)"
                            >
                            </s-select>
                        </s-form-item>
                        <!--<s-form-item
                            s-if="formData.sourceType !== 'DC' && formData.sourceType !== 'PUBLIC_IP' && formData.sourceType !== 'EIP'"
                            label="源所在网络： "
                            prop="vpcId"
                            class="no-label-class no-width-error-class"
                        >
                            <s-select
                                width="{{300}}"
                                filterable
                                datasource="{{resVpcList}}"
                                placeholder="请选择源所在网络"
                                value="{=formData.vpcId=}"
                                on-change="resVpcChange($event)"
                            >
                            </s-select>
                        </s-form-item>-->
                        <s-form-item
                            prop="sourceId"
                            class="no-width-error-class"
                            s-if="formData.sourceType !== 'PUBLIC_IP' && formData.sourceType !== 'DC'"
                        >
                            <s-select
                                s-if="formData.sourceType !== 'BCC'"
                                width="{{300}}"
                                filterable
                                placeholder="请选择源实例"
                                datasource="{{sourceIdList}}"
                                on-change="sourceIdChange"
                                value="{=formData.sourceId=}"
                            >
                            </s-select>
                            <s-input
                                width="300"
                                s-if="formData.sourceType === 'BCC'"
                                value="{=formData.sourceId=}"
                                placeholder="请输入BCC实例ID，例如：i-Uu1CIcJH"
                            >
                            </s-input>
                        </s-form-item>
                        <s-form-item
                            s-if="formData.sourceType === 'DC' || formData.sourceType === 'VPN_CONN'"
                            prop="connId"
                            class="no-width-error-class"
                        >
                            <s-select
                                width="{{formData.sourceType === 'DC' ? 300 : 200}}"
                                filterable
                                placeholder="{{formData.sourceType === 'DC' ? '请选择通道' : '请选择VPN通道'}}"
                                datasource="{{connIdList}}"
                                value="{=formData.connId=}"
                            >
                            </s-select>
                        </s-form-item>
                        <s-form-item prop="sourceIp" s-if="formData.sourceType !== 'EIP'">
                            <s-select
                                width="{{240}}"
                                loading="{{loadIpLoading}}"
                                s-if="formData.sourceType === 'BCC' && sourceBccExit"
                                datasource="{{sourceIpDataSource}}"
                                value="{=formData.sourceIp=}"
                                placeholder="请选择BCC实例IP"
                            >
                            </s-select>
                            <s-input s-else width="{{160}}" placeholder="请输入源IP地址" value="{=formData.sourceIp=}">
                            </s-input>
                        </s-form-item>
                    </div>
                    <s-form-item label="协议：" prop="protocol">
                        <s-select width="{{300}}" datasource="{{protocolDatasource}}" value="{=formData.protocol=}">
                        </s-select>
                    </s-form-item>
                    <s-form-item label="源端口：" prop="sourcePort">
                        <s-input
                            width="300"
                            disabled="{{inputDis}}"
                            value="{=formData.sourcePort=}"
                            placeholder="1-65535之间的整数"
                        >
                        </s-input>
                    </s-form-item>
                    <div class="path-wrap">
                        <s-form-item label="目的：" prop="destType" class="no-width-error-class">
                            <s-select
                                datasource="{{destTypeDatasource}}"
                                value="{=formData.destType=}"
                                on-change="destTypeChange"
                            >
                            </s-select>
                        </s-form-item>
                        <s-form-item
                            label="目的地域："
                            s-if="formData.destType !== 'IDC' && formData.destType !== 'PUBLIC_IP'"
                            prop="desRegion"
                            class="no-label-class"
                        >
                            <s-select
                                width="{{140}}"
                                datasource="{{regionList}}"
                                value="{=formData.desRegion=}"
                                on-change="desRegionChange($event)"
                            >
                            </s-select>
                        </s-form-item>
                        <!--<s-form-item
                            label="目的所在网络： "
                            s-if="formData.destType !== 'IDC' && formData.destType !== 'PUBLIC_IP' && formData.destType !== 'EIP'"
                            prop="desVpcId"
                            class="no-label-class no-width-error-class"
                        >
                            <s-select
                                width="{{300}}"
                                datasource="{{desVpcList}}"
                                placeholder="请选择目的所在网络"
                                filterable
                                value="{=formData.desVpcId=}"
                                on-change="desVpcChange($event)"
                            >
                            </s-select>
                        </s-form-item>-->
                        <s-form-item
                            prop="destId"
                            s-if="formData.destType !== 'IDC' && formData.destType !== 'PUBLIC_IP'"
                            class="no-width-error-class"
                        >
                            <s-select
                                width="{{300}}"
                                s-if="formData.destType !== 'BCC'"
                                filterable
                                placeholder="请选择目的实例"
                                datasource="{{destIdDatasource}}"
                                value="{=formData.destId=}"
                            >
                            </s-select>
                            <s-input
                                width="300"
                                s-if="formData.destType === 'BCC'"
                                value="{=formData.destId=}"
                                placeholder="请输入BCC实例ID，例如：i-Uu1CIcJH"
                            >
                            </s-input>
                        </s-form-item>
                        <s-form-item prop="destIp" s-if="formData.destType !== 'EIP'">
                            <s-select
                                width="{{240}}"
                                loading="{{loadDestIpLoading}}"
                                s-if="formData.destType === 'BCC' || formData.destType === 'BBC'"
                                datasource="{{destIpDatasource}}"
                                placeholder="请选择BCC实例IP"
                                value="{=formData.destIp=}"
                            >
                            </s-select>
                            <s-input width="{{160}}" s-else placeholder="请输入目的IP地址" value="{=formData.destIp=}">
                            </s-input>
                        </s-form-item>
                    </div>
                    <s-form-item label="目的端口：" prop="destPort">
                        <s-input
                            width="300"
                            disabled="{{inputDis}}"
                            value="{=formData.destPort=}"
                            placeholder="1-65535之间的整数"
                        >
                        </s-input>
                    </s-form-item>
                    <s-form-item label="描述：" prop="description">
                        <s-input-text-area
                            width="{{300}}"
                            maxLength="{{200}}"
                            height="{{100}}"
                            multiline
                            value="{=formData.description=}"
                        />
                    </s-form-item>
                </s-form>
            </div>
            <div class="buybucket" slot="pageFooter">
                <div class="buybucket-container">
                    <s-tooltip placement="top" trigger="{{pathDisable.disable ? 'hover' : ''}}">
                        <div slot="content">{{pathDisable.message}}</div>
                        <s-button
                            skin="primary"
                            disabled="{{pathDisable.disable || disableSub}}"
                            on-click="doSubmit"
                            size="large"
                            >确定</s-button
                        >
                    </s-tooltip>
                    <s-button on-click="onClose" size="large">取消</s-button>
                </div>
            </div>
        </s-bizpage>
    </div>
`;
@template(tpl)
@invokeSUI
@invokeSUIBIZ
class ProbeCreate extends CreatePage {
    static components = {
        's-bizpage': AppCreatePage,
        'outlined-plus': OutlinedPlus,
        's-button': Button,
        's-tooltip': Tooltip,
        's-select': Select,
        's-tip': Tip,
        's-form': Form,
        's-form-item': Form.Item,
        's-input': Input,
        's-radio-radio-group': Radio.RadioGroup,
        's-switch': Switch,
        's-icon': Icon
    };
    static filters = {
        noOpenTip(value) {
            const isSubUser = window.$context.isSubUser();
            let ResourceType = this.data.get('sourceTypeList') || [];
            let serviceText = ResourceType.find(item => item.value === value)?.text;
            let url = serviceTypeUrl[value];
            let str = `您的账号当前未开通${serviceText}服务，请前往${serviceText}控制台开通。<a href="${url}">去开通</a>`;
            if (isSubUser) {
                str = `该功能需要开通${serviceText}服务，当前登录的子账号没有开通产品服务的权限，请联系主账号授予开通服务权限，或联系主账户开通服务后授权使用。`;
            }
            return str;
        }
    };
    initData() {
        return {
            pageNav: {
                title: '创建路径分析',
                backUrl: '/network/#/vpc/pathanalise/list',
                backLabel: '返回路径分析'
            },
            flag: FLAG,
            vpcList: [],
            formData: {
                name: '',
                sourceType: '',
                sourceRegion: '',
                sourceVpcId: '',
                sourceIp: '',
                sourcePort: '',
                sourceId: '',
                destType: '',
                destRegion: '',
                destVpcId: '',
                destIp: '',
                destPort: '',
                destId: '',
                protocol: '',
                description: ''
            },
            protocolDatasource: [
                // {
                //     text: '全部协议(默认)',
                //     value: 'ALL'
                // },
                {
                    text: 'TCP',
                    value: 'tcp'
                },
                {
                    text: 'UDP',
                    value: 'udp'
                },
                {
                    text: 'ICMP',
                    value: 'icmp'
                }
            ],
            validateRule: formValidator(this),
            vpcMap: {},
            pathDisable: {},
            sourceTypeList: pathTypeList.toArray().concat({alias: 'PUBLIC_IP', text: '公网IP', value: 'PUBLIC_IP'}),
            destTypeDatasource: pathTypeList
                .toArray()
                .slice(0, 2)
                .concat({text: '混合云网关', value: 'IDC'}, {alias: 'PUBLIC_IP', text: '公网IP', value: 'PUBLIC_IP'}),
            sourceRegionList: [{text: '华北-北京', value: 'bj'}],
            sourceIdList: [],
            resVpcList: [],
            desVpcList: [],
            sourceBccExit: true,
            destBccExit: true
        };
    }
    inited() {
        let routeTypeList = u.cloneDeep(this.data.get('sourceTypeList'));
        let routeTypeDestList = u.cloneDeep(this.data.get('destTypeDatasource'));
        routeTypeList = checkSts(routeTypeList);
        routeTypeDestList = checkSts(routeTypeDestList);
        this.data.set('sourceTypeList', routeTypeList);
        this.data.set('destTypeDatasource', routeTypeDestList);

        this.getPathList();
        const regionArray = getVpcAvaliableRegion();
        this.data.set('regionList', regionArray?.regionList);
        this.data.set('formData.sourceRegion', regionArray?.regionList[0].value);
        this.data.set('formData.desRegion', regionArray?.regionList[0].value);
        this.getSourceVpc(regionArray?.regionList[0].value);
        this.getDesVpcList(regionArray?.regionList[0].value);
        this.watch('formData.protocol', value => {
            if (value === 'ALL' || value === 'icmp') {
                this.data.set('inputDis', true);
                this.data.set('formData.destPort', '');
                this.data.set('formData.sourcePort', '');
            } else {
                this.data.set('inputDis', false);
            }
        });
        this.watch('formData.destId', value => {
            this.data.set('formData.destIp', '');
            if (value) {
                this.setDestIpList();
            }
        });
        this.watch('formData.sourceId', value => {
            this.data.set('formData.sourceIp', '');
            if (value) {
                this.setSourceIpList();
            }
        });
    }

    async doSubmit() {
        const form = this.ref('form');
        return form.validateFields().then(() => {
            this.data.set('disableSub', true);
            let formData = this.data.get('formData');
            let payload = {
                name: formData.name,
                description: formData.description,
                sourceType: formData.sourceType,
                sourceRegion: formData.sourceRegion,
                sourceIp: formData.sourceIp,
                sourceVpcId: formData.vpcId,
                sourcePort: formData.sourcePort ? Number(formData.sourcePort) : formData.sourcePort,
                sourceId: formData.sourceId,
                destType: formData.destType,
                destRegion: formData.desRegion,
                destVpcId: formData.desVpcId,
                destPort: formData.destPort ? Number(formData.destPort) : formData.destPort,
                destIp: formData.destIp,
                destId: formData.destId,
                protocol: formData.protocol
            };
            if (payload.protocol === 'ALL') {
                delete payload.protocol;
            }
            if (payload.sourceType === 'VPN_CONN' || formData.sourceType === 'DC') {
                payload.sourceId = formData.connId;
            }
            if (payload.destType === 'IDC') {
                delete payload.destId;
                delete payload.destVpcId;
                delete payload.destRegion;
            }
            if (formData.sourceType === 'DC') {
                delete payload.sourceVpcId;
            }
            if (payload.sourceType === 'PUBLIC_IP') {
                payload.sourceId = '';
                payload.sourceVpcId = '';
                payload.sourceRegion = '';
            }
            if (payload.destType === 'PUBLIC_IP') {
                payload.destId = '';
                payload.destVpcId = '';
                payload.destRegion = '';
            }
            if (payload.sourceType === 'EIP' || payload.sourceType === 'PUBLIC_IP') {
                payload.sourceType = 'PUBLIC_IP';
            }
            if (payload.destType === 'EIP') {
                let destIdDatasource = this.data.get('destIdDatasource') || [];
                if (destIdDatasource.find(item => item.eip === payload.destId)) {
                    payload.destIp = formData.destId;
                    payload.destId = destIdDatasource.find(item => item.value === formData.destId)?.destOriginId;
                } else {
                    payload.destIp = destIdDatasource.find(item => item.value === payload.destId)?.eip;
                }
            }
            if (payload.destType === 'EIP' || payload.destType === 'PUBLIC_IP') {
                payload.destType = 'PUBLIC_IP';
            }
            this.$http
                .createPathanalise(payload)
                .then(res => {
                    this.data.set('disableSub', false);
                    location.hash = '#/vpc/pathanalise/list';
                })
                .catch(() => {
                    this.data.set('disableSub', false);
                });
        });
    }
    loadResVpnList(region, type) {
        if (type) {
            this.data.set('destTypeDatasource', []);
            this.data.set('destIdDatasource', []);
            this.data.set('formData.destType', '');
            this.nextTick(() => {
                this.data.set('destTypeDatasource', [
                    {text: '云服务器 BCC', value: 'BCC'},
                    // {text: '弹性裸金属服务器 BBC', value: 'BBC'},
                    {text: '子网', value: 'SUBNET'},
                    {text: '混合云网关', value: 'IDC'}
                ]);
            });
        }
        let query = {
            pageNo: 1,
            pageSize: 10000
        };
        let vpcId = this.data.get('formData.vpcId');
        let vpcInfo = u.filter(this.data.get('resVpcs'), item => item.vpcInfo.shortId === vpcId)[0]?.vpcInfo;
        return this.$http
            .getVpnList(query, {
                headers: {region: this.data.get('formData.sourceRegion') || window.$context.getCurrentRegionId()}
            })
            .then(data => {
                let result = [];
                u.each(data.result, item => {
                    if (!vpcId) {
                        if (u.indexOf([VpnStatus.ACTIVE], item.status) > -1) {
                            if (item.vpnType !== 'SSL') {
                                result.push({
                                    value: item.vpnId,
                                    text: `${item.vpnName}/${item.vpnId}`,
                                    vpnType: item.vpnType
                                });
                            }
                        }
                    } else {
                        if (u.indexOf([VpnStatus.ACTIVE], item.status) > -1) {
                            if (item.vpcId === vpcInfo?.vpcId) {
                                if (item.vpnType !== 'SSL') {
                                    result.push({
                                        value: item.vpnId,
                                        text: `${item.vpnName}/${item.vpnId}`,
                                        vpnType: item.vpnType
                                    });
                                }
                            }
                        }
                    }
                });
                this.data.set('sourceIdList', result);
            });
    }
    getInstanceListBySubnets(region, type) {
        if (type) {
            this.data.set('destTypeDatasource', []);
            this.data.set('destIdDatasource', []);
            this.data.set('formData.destType', '');
            this.nextTick(() => {
                this.data.set(
                    'destTypeDatasource',
                    pathTypeList
                        .toArray()
                        .slice(0, 2)
                        .concat(
                            {text: '混合云网关', value: 'IDC'},
                            {alias: 'PUBLIC_IP', text: '公网IP', value: 'PUBLIC_IP'}
                        )
                );
                this.data.set('formData.destType', '');
            });
        }
        let vpcId = this.data.get('formData.vpcId');
        let vpcInfo = u.filter(this.data.get('resVpcs'), item => item.vpcInfo.shortId === vpcId)[0]?.vpcInfo;
        let payload = {
            pageNo: 1,
            pageSize: 1000,
            serverType: 'BCC',
            enableBid: true,
            isAdvancedSearch: false
        };
        return this.$http.getBccList(payload, {headers: {region}}).then(data => {
            let result = [];
            u.each(data.result, item => {
                if (!vpcId) {
                    if (!u.isEmpty(RouteStatus.fromValue(item.status)) && !item.specificType) {
                        let text = '';
                        text = item.name + (item.internalIp ? `（${item.internalIp}）` : '');
                        result.push({
                            value: item.instanceId,
                            text,
                            id: item.id
                        });
                    }
                } else {
                    if (!u.isEmpty(RouteStatus.fromValue(item.status)) && !item.specificType) {
                        if (item.vpcId === vpcInfo?.vpcId) {
                            let text = '';
                            text = item.name + (item.internalIp ? `（${item.internalIp}）` : '');
                            result.push({
                                value: item.instanceId,
                                text,
                                id: item.id
                            });
                        }
                    }
                }
            });
            this.data.set('sourceIdList', result);
        });
    }
    loadResDcList(value) {
        return this.$http
            .getChannelList({
                type: 'available'
            })
            .then(res => {
                let dcphyList = res.infos.reduce((arr, next) => {
                    let obj = {};
                    obj[next.dcphyId]
                        ? ''
                        : (obj[next.dcphyId] =
                              true &&
                              arr.push({
                                  text: next.id,
                                  value: next.id,
                                  dcphyId: next.dcphyId,
                                  status: next.status
                              }));
                    return arr;
                }, []);
                dcphyList = dcphyList.filter(item => item.dcphyId === value && item.status === 'established');
                this.data.set('connIdList', dcphyList);
            });
    }
    onRegionChange() {
        location.hash = '#/vpc/pathanalise/list';
    }
    onClose() {
        this.dispose();
        location.hash = '#/vpc/pathanalise/list';
    }

    sourceRegionChange({value}) {
        this.data.set('resVpcList', []);
        this.data.set('sourceIdList', []);
        this.data.set('formData.vpcId', '');
        this.data.set('formData.sourceId', '');
        this.data.set('sourceIpDataSource', []);
        this.data.set('formData.sourceIp', '');
        this.getSourceVpc(value);
        if (this.data.get('formData.sourceType') === 'BCC') {
            this.getInstanceListBySubnets(value);
        } else if (this.data.get('formData.sourceType') === 'BBC') {
            this.loadBbcList(value);
        } else if (this.data.get('formData.sourceType') === 'SUBNET') {
            this.loadResSubnets(value);
        } else if (this.data.get('formData.sourceType') === 'VPN_CONN') {
            this.data.set('connIdList', []);
            this.data.set('formData.connId', '');
            this.loadResVpnList(value);
        } else if (this.data.get('formData.sourceType') === 'DC') {
            this.data.set('connIdList', []);
            this.data.set('formData.connId', '');
            this.loadResDcGwList(value);
        } else if (this.data.get('formData.sourceType') === 'EIP') {
            this.loadResEipList(value);
        }
    }
    sourceTypeChange({value}) {
        this.data.set('formData.sourceId', '');
        this.data.set('formData.sourceIp', '');
        this.data.set('sourceIdList', []);
        this.data.set('sourceIpDataSource', []);
        if (value === 'BCC') {
            this.getInstanceListBySubnets(this.data.get('formData.sourceRegion'), value);
        } else if (value === 'BBC') {
            this.loadBbcList(this.data.get('formData.sourceRegion'), value);
        } else if (value === 'SUBNET') {
            this.loadResSubnets(this.data.get('formData.sourceRegion'), value);
        } else if (value === 'VPN_CONN') {
            this.data.set('connIdList', []);
            this.data.set('formData.connId', '');
            this.loadResVpnList(this.data.get('formData.sourceRegion'), value);
        } else if (value === 'DC') {
            this.data.set('connIdList', []);
            this.data.set('formData.connId', '');
            this.loadResDcGwList(this.data.get('formData.sourceRegion'), value);
        } else if (value === 'EIP' || value === 'PUBLIC_IP') {
            this.data.set('formData.destType', '');
            this.nextTick(() => {
                this.data.set('formData.destType', 'EIP');
                this.data.set('destTypeDatasource', [{alias: 'EIP', text: '弹性公网IP', value: 'EIP'}]);
                this.loadDesEipList(this.data.get('formData.desRegion'));
            });
            value === 'EIP' && this.loadResEipList(this.data.get('formData.sourceRegion'), value);
        }
    }
    sourceIdChange({value}) {
        if (this.data.get('formData.sourceType') === 'VPN_CONN') {
            this.data.set('connIdList', []);
            this.data.set('formData.connId', '');
            let sourceIdList = this.data.get('sourceIdList');
            let type = sourceIdList.find(item => item.value === value)?.vpnType;
            this.getVpnConnList(value, type.toLowerCase());
        } else if (this.data.get('formData.sourceType') === 'DC') {
            this.data.set('connIdList', []);
            this.data.set('formData.connId', '');
            this.loadResDcList(value);
        }
    }
    bccIdChange({value}) {
        this.data.set('sourceBccExit', true);
        if (!value) {
            return;
        }
        if (value.length !== 10 || !value.startsWith('i-')) {
            return;
        }
        let str = value.slice(2);
        if (!/[a-zA-Z0-9]{8}$/.test(str)) {
            return;
        }
        let payload = {
            pageNo: 1,
            pageSize: 1000,
            serverType: 'BCC',
            enableBid: true,
            isAdvancedSearch: false,
            filters: [
                {
                    keyword: value,
                    keywordType: 'instanceId',
                    subKeywordType: ''
                }
            ]
        };
        this.data.set('loadExitBccLoading', true);
        this.$http
            .getBccList(payload, {headers: {region: this.data.get('formData.sourceRegion')}})
            .then(async res => {
                if (res.result && res.result[0]?.id) {
                    this.data.set('sourceBccExit', true);
                    this.getResBccEnics(res.result[0]?.id);
                } else {
                    this.data.set('sourceBccExit', false);
                    await this.ref('form')?.validateFields(['sourceId']);
                }
                this.data.set('loadExitBccLoading', false);
            })
            .catch(e => this.data.set('loadExitBccLoading', false));
    }
    bccDestIdChange({value}) {
        this.data.set('destBccExit', true);
        if (!value) {
            return;
        }
        if (value.length !== 10 || !value.startsWith('i-')) {
            return;
        }
        let str = value.slice(2);
        if (!/[a-zA-Z0-9]{8}$/.test(str)) {
            return;
        }
        let payload = {
            pageNo: 1,
            pageSize: 1000,
            serverType: 'BCC',
            enableBid: true,
            isAdvancedSearch: false,
            filters: [
                {
                    keyword: value,
                    keywordType: 'instanceId',
                    subKeywordType: ''
                }
            ]
        };
        this.data.set('loadExitDestBccLoading', true);
        this.$http
            .getBccList(payload, {headers: {region: this.data.get('formData.desRegion')}})
            .then(async res => {
                if (res.result && res.result[0]?.id) {
                    this.data.set('destBccExit', true);
                    this.getDesBccEnics(res.result[0]?.id);
                } else {
                    this.data.set('destBccExit', false);
                    await this.ref('form')?.validateFields(['destId']);
                }
                this.data.set('loadExitDestBccLoading', false);
            })
            .catch(e => this.data.set('loadExitDestBccLoading', false));
    }
    desRegionChange({value}) {
        this.data.set('desVpcList', []);
        this.data.set('destIdDatasource', []);
        this.data.set('destIpDatasource', []);
        this.data.set('formData.destId', '');
        this.data.set('formData.desVpcId', '');
        this.data.set('formData.destIp', '');
        this.getDesVpcList(value);
        if (this.data.get('formData.sourceType') === 'BCC') {
            this.loadDesBccList(value);
        } else if (this.data.get('formData.sourceType') === 'BBC') {
            this.loadDesBbcList(value);
        } else if (this.data.get('formData.sourceType') === 'SUBNET') {
            this.loadDesSubnets(value);
        } else if (this.data.get('formData.sourceType') === 'EIP') {
            this.loadDesEipList(value);
        }
    }

    destTypeChange({value}) {
        this.data.set('destIdDatasource', []);
        this.data.set('destIpDatasource', []);
        this.data.set('formData.destId', '');
        this.data.set('formData.destIp', '');
        if (value === 'BCC') {
            this.loadDesBccList(this.data.get('formData.desRegion'));
        } else if (value === 'BBC') {
            this.loadDesBbcList(this.data.get('formData.desRegion'));
        } else if (value === 'SUBNET') {
            this.loadDesSubnets(this.data.get('formData.desRegion'));
        } else if (value === 'EIP') {
            this.loadDesEipList(this.data.get('formData.desRegion'));
        }
    }

    getSourceVpc(region) {
        this.getResVpclist(region).then(res => {
            let vpcList = this.data.get('resVpcList');
            this.getResVpcs(vpcList);
            let vpcs = this.data.get('resVpcs');
            this.data.set(
                'resVpcList',
                vpcs.map(item => ({
                    text: `${item.vpcInfo.name}（${item.vpcInfo.cidr}）`,
                    value: item.vpcInfo.shortId
                }))
            );
        });
    }
    getResVpclist(region) {
        return this.$http.vpcList({}, {headers: {region}}).then(data => {
            let vpcs = u.map(data, item => ({text: `${item.name}（${item.cidr}）`, value: item.vpcId, vpcInfo: item}));
            if (!this.data.get('notSupportAllVpc')) {
                vpcs.unshift({
                    text: '所在网络：全部私有网络',
                    value: ''
                });
            }
            this.data.set('resVpcList', vpcs);
        });
    }
    getResVpcs(data) {
        let vpcs = data
            .filter(item => item.value)
            .map(item => {
                if (item.vpcInfo.ipv6Cidr) {
                    return {
                        ...item,
                        text: `${item.text}(${item.vpcInfo.ipv6Cidr})`
                    };
                } else return item;
            });
        this.data.set('resVpcs', vpcs);
    }
    getDesVpcList(region) {
        this.getDesVpcArray(region).then(res => {
            let vpcList = this.data.get('desVpcList');
            this.getDesVpcs(vpcList);
            let vpcs = this.data.get('desVpcs');
            this.data.set(
                'desVpcList',
                vpcs.map(item => ({
                    text: `${item.vpcInfo.name}（${item.vpcInfo.cidr}）`,
                    value: item.vpcInfo.shortId
                }))
            );
        });
    }
    getDesVpcArray(region) {
        return this.$http.vpcList({}, {headers: {region}}).then(data => {
            let vpcs = u.map(data, item => ({text: `${item.name}（${item.cidr}）`, value: item.vpcId, vpcInfo: item}));
            if (!this.data.get('notSupportAllVpc')) {
                vpcs.unshift({
                    text: '所在网络：全部私有网络',
                    value: ''
                });
            }
            this.data.set('desVpcList', vpcs);
        });
    }
    getDesVpcs(data) {
        let vpcs = data
            .filter(item => item.value)
            .map(item => {
                if (item.vpcInfo.ipv6Cidr) {
                    return {
                        ...item,
                        text: `${item.text}(${item.vpcInfo.ipv6Cidr})`
                    };
                } else return item;
            });
        this.data.set('desVpcs', vpcs);
    }

    desVpcChange({value}) {
        this.data.set('formData.desVpcId', value);
        this.data.set('destIdDatasource', []);
        this.data.set('destIpDatasource', []);
        this.data.set('formData.destId', '');
        this.data.set('formData.destIp', '');
        if (this.data.get('formData.destType') === 'BCC') {
            this.loadDesBccList(this.data.get('formData.desRegion'));
        } else if (this.data.get('formData.destType') === 'BBC') {
            this.loadDesBbcList(this.data.get('formData.desRegion'));
        } else if (this.data.get('formData.destType') === 'SUBNET') {
            this.loadDesSubnets(this.data.get('formData.desRegion'));
        }
    }
    resVpcChange({value}) {
        this.data.set('formData.vpcId', value);
        this.data.set('sourceIdList', []);
        this.data.set('formData.sourceId', '');
        this.data.set('sourceIpDataSource', []);
        this.data.set('formData.sourceIp', '');
        if (this.data.get('formData.sourceType') === 'BCC') {
            this.getInstanceListBySubnets(this.data.get('formData.sourceRegion'));
        } else if (this.data.get('formData.sourceType') === 'BBC') {
            this.loadBbcList(this.data.get('formData.sourceRegion'));
        } else if (this.data.get('formData.sourceType') === 'SUBNET') {
            this.loadResSubnets(this.data.get('formData.sourceRegion'));
        } else if (this.data.get('formData.sourceType') === 'DC') {
            this.data.set('connIdList', []);
            this.data.set('formData.connId', '');
            this.loadResDcGwList(this.data.get('formData.sourceRegion'));
        } else if (this.data.get('formData.sourceType') === 'VPN_CONN') {
            this.data.set('connIdList', []);
            this.data.set('formData.connId', '');
            this.loadResVpnList(this.data.get('formData.sourceRegion'));
        }
    }
    loadResSubnets(region, type) {
        if (type) {
            this.data.set('destTypeDatasource', []);
            this.data.set('destIdDatasource', []);
            this.data.set('formData.destType', '');
            this.nextTick(() => {
                this.data.set(
                    'destTypeDatasource',
                    pathTypeList.toArray().slice(0, 2).concat({text: '混合云网关', value: 'IDC'})
                );
                this.data.set('formData.destType', '');
            });
        }
        let vpcId = this.data.get('formData.vpcId');
        let vpcInfo = u.filter(this.data.get('resVpcs'), item => item.vpcInfo.shortId === vpcId)[0]?.vpcInfo;
        let payload = {vpcId: vpcInfo?.vpcId, attachVm: false};
        this.$http.getSubnetList(payload, {headers: {region}}).then(data => {
            let datasource = [];
            u.each(data, item => {
                // 暂时不支持ipv6先注释掉
                // let text = '';
                // if (item.ipv6Cidr) {
                //   text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}(${item.ipv6Cidr})`;
                // }
                // else {
                //   text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}`;
                // }
                let text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}`;
                datasource.push({
                    value: item.shortId,
                    subnetId: item.subnetId,
                    text: text,
                    cidr: item.cidr,
                    ipv6Cidr: item.ipv6Cidr
                });
            });
            this.data.set('sourceIdList', datasource);
        });
    }
    loadDesSubnets(region) {
        let vpcId = this.data.get('formData.desVpcId');
        let vpcInfo = u.filter(this.data.get('desVpcs'), item => item.vpcInfo.shortId === vpcId)[0]?.vpcInfo;
        let payload = {vpcId: vpcInfo?.vpcId, attachVm: false};
        this.$http.getSubnetList(payload, {headers: {region}}).then(data => {
            let datasource = [];
            u.each(data, item => {
                // 暂时不支持ipv6先注释掉
                // let text = '';
                // if (item.ipv6Cidr) {
                //   text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}(${item.ipv6Cidr})`;
                // }
                // else {
                //   text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}`;
                // }
                let text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}`;
                datasource.push({
                    value: item.shortId,
                    subnetId: item.subnetId,
                    text: text,
                    cidr: item.cidr,
                    ipv6Cidr: item.ipv6Cidr
                });
            });
            this.data.set('destIdDatasource', datasource);
        });
    }
    loadDesBccList(region) {
        let vpcId = this.data.get('formData.desVpcId');
        let vpcInfo = u.filter(this.data.get('desVpcs'), item => item.vpcInfo.shortId === vpcId)[0]?.vpcInfo;
        let payload = {
            pageNo: 1,
            pageSize: 1000,
            serverType: 'BCC',
            enableBid: true,
            isAdvancedSearch: false
        };
        return this.$http.getBccList(payload, {headers: {region}}).then(data => {
            let result = [];
            u.each(data.result, item => {
                if (!vpcId) {
                    if (!u.isEmpty(RouteStatus.fromValue(item.status)) && !item.specificType) {
                        let text = '';
                        text = item.name + (item.internalIp ? `（${item.internalIp}）` : '');
                        result.push({
                            value: item.instanceId,
                            text,
                            id: item.id
                        });
                    }
                } else {
                    if (!u.isEmpty(RouteStatus.fromValue(item.status)) && !item.specificType) {
                        if (item.vpcId === vpcInfo?.vpcId) {
                            let text = '';
                            text = item.name + (item.internalIp ? `（${item.internalIp}）` : '');
                            result.push({
                                value: item.instanceId,
                                text,
                                id: item.id
                            });
                        }
                    }
                }
            });
            this.data.set('destIdDatasource', result);
        });
    }
    loadDesBbcList(region) {
        let vpcId = this.data.get('formData.desVpcId');
        let vpcInfo = u.filter(this.data.get('desVpcs'), item => item.vpcInfo.shortId === vpcId)[0]?.vpcInfo;
        let payload = {
            pageNo: 1,
            pageSize: 10000
        };
        this.$http.getBbcList(payload, {headers: {region}}).then(data => {
            let datasource = [];
            u.each(data.result, item => {
                if (!vpcId) {
                    if (!u.isEmpty(RouteStatus.fromValue(item.status))) {
                        let text = '';
                        text = item.name + (item.internalIp ? `（${item.internalIp}）` : '');
                        datasource.push({
                            value: item.instanceId,
                            text,
                            id: item.id
                        });
                    }
                } else {
                    if (item.vpcId === vpcInfo?.vpcId) {
                        if (!u.isEmpty(RouteStatus.fromValue(item.status))) {
                            let text = '';
                            text = item.name + (item.internalIp ? `（${item.internalIp}）` : '');
                            datasource.push({
                                value: item.instanceId,
                                text,
                                id: item.id
                            });
                        }
                    }
                }
            });
            this.data.set('destIdDatasource', datasource);
        });
    }
    loadBbcList(region, type) {
        if (type) {
            this.data.set('destTypeDatasource', []);
            this.data.set('destIdDatasource', []);
            this.data.set('formData.destType', '');
            this.nextTick(() => {
                this.data.set(
                    'destTypeDatasource',
                    pathTypeList
                        .toArray()
                        .slice(0, 2)
                        .concat(
                            {text: '混合云网关', value: 'IDC'},
                            {alias: 'PUBLIC_IP', text: '公网IP', value: 'PUBLIC_IP'}
                        )
                );
                this.data.set('formData.destType', '');
            });
        }
        let vpcId = this.data.get('formData.vpcId');
        let vpcInfo = u.filter(this.data.get('resVpcs'), item => item.vpcInfo.shortId === vpcId)[0]?.vpcInfo;
        let payload = {
            pageNo: 1,
            pageSize: 10000
        };
        this.$http.getBbcList(payload, {headers: {region}}).then(data => {
            let datasource = [];
            u.each(data.result, item => {
                if (!vpcId) {
                    if (!u.isEmpty(RouteStatus.fromValue(item.status))) {
                        let text = '';
                        text = item.name + (item.internalIp ? `（${item.internalIp}）` : '');
                        datasource.push({
                            value: item.instanceId,
                            text,
                            id: item.id
                        });
                    }
                } else {
                    if (item.vpcId === vpcInfo?.vpcId) {
                        if (!u.isEmpty(RouteStatus.fromValue(item.status))) {
                            let text = '';
                            text = item.name + (item.internalIp ? `（${item.internalIp}）` : '');
                            datasource.push({
                                value: item.instanceId,
                                text,
                                id: item.id
                            });
                        }
                    }
                }
            });
            this.data.set('sourceIdList', datasource);
        });
    }
    getPathList() {
        let payload = {
            pageNo: 1,
            pageSize: 10000
        };
        this.$http.getPathanaliseList(payload).then(data => {
            this.data.set('pathList', data.result);
        });
    }
    setDestIpList() {
        let destIdDatasource = this.data.get('destIdDatasource') || [];
        let destId = this.data.get('formData.destId');
        if (this.data.get('formData.destType') === 'BCC') {
            // let id = destIdDatasource.find(item => item.value === destId).id || '';
            this.bccDestIdChange({value: destId});
        }
        if (this.data.get('formData.destType') === 'SUBNET') {
            let desSubnet = destIdDatasource.find(item => item.value === destId).cidr || '';
            this.data.set('desSubnet', desSubnet);
        }
        if (this.data.get('formData.destType') === 'BBC') {
            let id = destIdDatasource.find(item => item.value === destId).id || '';
            this.getDesEnics(id);
        }
    }

    setSourceIpList() {
        let sourceIdList = this.data.get('sourceIdList') || [];
        let sourceId = this.data.get('formData.sourceId');
        if (this.data.get('formData.sourceType') === 'BCC') {
            this.bccIdChange({value: sourceId});
        }
        if (this.data.get('formData.sourceType') === 'SUBNET') {
            let sourceSubnet = sourceIdList.find(item => item.value === sourceId).cidr || '';
            this.data.set('sourceSubnet', sourceSubnet);
        }
        if (this.data.get('formData.sourceType') === 'BBC') {
            let id = sourceIdList.find(item => item.value === sourceId).id || '';
            this.getResEnics(id);
        }
    }
    getResEnics(instanceId) {
        let payload = {
            instanceId
        };
        this.$http
            .getBbcEnics(payload, this.data.get('formData.sourceRegion') || window.$context.getCurrentRegionId())
            .then(res => {
                let result =
                    res[0]?.privateIpSet.map(item => {
                        return {
                            text: item.privateIpAddress,
                            value: item.privateIpAddress
                        };
                    }) || [];
                this.data.set('sourceIpDataSource', result);
            });
    }
    getDesEnics(instanceId) {
        let payload = {
            instanceId
        };
        this.$http
            .getBbcEnics(payload, this.data.get('formData.desRegion') || window.$context.getCurrentRegionId())
            .then(res => {
                let result =
                    res[0]?.privateIpSet.map(item => {
                        return {
                            text: item.privateIpAddress,
                            value: item.privateIpAddress
                        };
                    }) || [];
                this.data.set('destIpDatasource', result);
            });
    }

    getResBccEnics(instanceId) {
        let payload = {
            instanceId
        };
        this.data.set('loadIpLoading', true);
        this.$http
            .getBccEnics(payload, this.data.get('formData.sourceRegion') || window.$context.getCurrentRegionId())
            .then(res => {
                let result = [];
                res.enis.map(item => {
                    item.ips?.map(i => {
                        if (IP.test(i.privateIp)) {
                            result.push({
                                text: i.privateIp,
                                value: i.privateIp
                            });
                        }
                    });
                });
                this.data.set('sourceIpDataSource', result);
            })
            .finally(() => this.data.set('loadIpLoading', false));
    }
    getDesBccEnics(instanceId) {
        let payload = {
            instanceId
        };
        this.data.set('loadDestIpLoading', true);
        this.$http
            .getBccEnics(payload, this.data.get('formData.desRegion') || window.$context.getCurrentRegionId())
            .then(res => {
                let result = [];
                res.enis.map(item => {
                    item.ips?.map(i => {
                        if (IP.test(i.privateIp)) {
                            result.push({
                                text: i.privateIp,
                                value: i.privateIp
                            });
                        }
                    });
                });
                this.data.set('destIpDatasource', result);
            })
            .finally(() => this.data.set('loadDestIpLoading', false));
    }
    getVpnConnList(vpnId, vpnType) {
        let payload = {
            vpnId,
            vpnType
        };
        this.$http.vpnConnList(payload, this.data.get('formData.sourceRegion')).then(res => {
            let array = [];
            u.each(res, item => {
                array.push({
                    value: item.vpnConnId,
                    text: `${item.vpnConnName}/${item.vpnConnId}`
                });
            });
            this.data.set('connIdList', array);
        });
    }
    loadResDcGwList(region, type) {
        if (type) {
            this.data.set('destTypeDatasource', []);
            this.data.set('destIdDatasource', []);
            this.data.set('formData.destType', '');
            this.nextTick(() => {
                this.data.set('destTypeDatasource', [
                    {text: '云服务器 BCC', value: 'BCC'},
                    // {text: '弹性裸金属服务器 BBC', value: 'BBC'},
                    {text: '子网', value: 'SUBNET'},
                    {text: '混合云网关', value: 'IDC'}
                ]);
            });
        }
        let vpcId = this.data.get('formData.vpcId');
        let vpcInfo = u.filter(this.data.get('resVpcs'), item => item.vpcInfo.shortId === vpcId)[0]?.vpcInfo;
        let payload = {
            pageNo: 1,
            pageSize: 10000
        };
        return this.$http
            .getChannelList({
                type: 'available'
            })
            .then(res => {
                let dcphyList = res.infos.reduce((arr, next) => {
                    let obj = {};
                    obj[next.dcphyId]
                        ? ''
                        : (obj[next.dcphyId] =
                              true &&
                              arr.push({
                                  text: next.id,
                                  value: next.id,
                                  dcphyId: next.dcphyId,
                                  status: next.status
                              }));
                    return arr;
                }, []);
                dcphyList = dcphyList.filter(item => item.status === 'established');
                this.data.set('connIdList', dcphyList);
            });
        return this.$http.getDcList(payload).then(res => {
            let result = [];
            u.each(res.result, item => {
                if (item.status === 'established' && result.findIndex(i => i.value === item.id) === -1) {
                    result.push({
                        text: `${item.name}/${item.id}`,
                        value: item.id
                    });
                }
            });
            this.data.set('sourceIdList', result);
        });
        // return this.$http.dcgwList(payload, {headers: {region}})
        //     .then((res) => {
        //       let result = [];
        //       if (!vpcId) {
        //         u.each(res.result, item => {
        //           if (u.indexOf([DcGatewayStatus.RUNNING], item.status) > -1) {
        //             let name = '物理专线Id：' + '/' + item.dcphyId;
        //             result.push({
        //               value: item.id,
        //               text: name
        //             });
        //           }
        //         });
        //       } else {
        //         u.each(res.result, item => {
        //           if (u.indexOf([DcGatewayStatus.RUNNING], item.status) > -1) {
        //             if (item.vpcId === vpcInfo?.vpcId) {
        //               let name = '物理专线Id：' + '/' + item.dcphyId;
        //               result.push({
        //                 value: item.id,
        //                 text: name
        //               });
        //             }
        //           }
        //         });
        //       }
        //       this.data.set('sourceIdList', result);
        //   });
    }
    loadResEipList() {
        let payload = {
            pageNo: 1,
            pageSize: 10000
        };
        Promise.all([
            this.$http.getEipList(payload, {
                headers: {region: this.data.get('formData.sourceRegion') || window.$context.getCurrentRegionId()}
            }),
            this.$http.hecgEipList(payload, {
                headers: {region: this.data.get('formData.sourceRegion') || window.$context.getCurrentRegionId()}
            })
        ]).then(res => {
            let datasource = [];
            res[0]?.result?.forEach(item => {
                datasource.push({
                    text: item.name + '/' + item.shortId + `（${item.eip}）`,
                    value: item.shortId,
                    eip: item.eip
                });
            });
            res[1]?.result?.forEach(item => {
                if (item.eips?.length > 0) {
                    item.eips.forEach(i => {
                        if (!i.eip.includes('/')) {
                            datasource.push({
                                text: item.name + '/' + item.id + `（${i.eip}）`,
                                value: i.eip,
                                eip: i.eip,
                                destOriginId: item.id
                            });
                        }
                    });
                }
            });

            this.data.set('sourceIdList', datasource);
        });
    }
    loadDesEipList() {
        let payload = {
            pageNo: 1,
            pageSize: 10000
        };
        Promise.all([
            this.$http.getEipList(payload, {
                headers: {region: this.data.get('formData.desRegion') || window.$context.getCurrentRegionId()}
            }),
            this.$http.hecgEipList(payload, {
                headers: {region: this.data.get('formData.desRegion') || window.$context.getCurrentRegionId()}
            })
        ]).then(res => {
            let datasource = [];
            res[0]?.result?.forEach(item => {
                datasource.push({
                    text: item.name + '/' + item.shortId + `（${item.eip}）`,
                    value: item.shortId,
                    eip: item.eip
                });
            });
            res[1]?.result?.forEach(item => {
                if (item.eips?.length > 0) {
                    item.eips.forEach(i => {
                        if (!i.eip.includes('/')) {
                            datasource.push({
                                text: item.name + '/' + item.id + `（${i.eip}）`,
                                value: i.eip,
                                eip: i.eip,
                                destOriginId: item.id
                            });
                        }
                    });
                }
            });
            this.data.set('destIdDatasource', datasource);
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(ProbeCreate));
