import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {Notification} from '@baidu/sui';
import {OutlinedPlus, OutlinedEditingSquare, OutlinedRefresh} from '@baidu/sui-icon';
import {checker} from '@baiducloud/bce-opt-checker';

import {PathSearchType} from '@/pages/sanPages/common/enum';
import Confirm from '@/pages/sanPages/components/confirm';
import DrawerPathamalise from '../components/drawPathanalise';
import rules from '../rules';
import {utcToTime, getVpcAvaliableRegion, $flag as FLAG} from '@/pages/sanPages/utils/helper';
import {columns} from './tableFiled';
import {ContextService, DocService} from '@/pages/sanPages/common';
import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import DOCUMENT_ICON from '@/img/document_normal.svg?url';
import DOCUMENT_ICON_COLOR from '@/img/document_color.svg?url';

import './list.less';

const AllRegion = window.$context.getEnum('AllRegion');
const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, invokeComp} = decorators;

const tpl = html`
    <div>
        <s-app-list-page class="path-list-wrap">
            <div slot="pageTitle">
                <div class="vpc-path-header">
                    <div class="header-left">
                        <span class="title">路径分析</span>
                    </div>
                    <div class="header-right">
                        <a
                            s-ref="introduce"
                            href="javascript:void(0)"
                            class="help-file function-introduce"
                            on-click="handleShowCard"
                            on-mouseenter="handleMouseEnter('introduce')"
                            on-mouseleave="handleMouseLeave('introduce')"
                            s-if="{{!FLAG.NetworkSupportXS}}"
                        >
                            <img class="s-icon" src="{{introduceIcon}}" />功能简介
                        </a>
                        <a
                            href="{{DocService.pathanalise_helpFile}}"
                            target="_blank"
                            class="help-file help-file-docs"
                            s-if="{{!FLAG.NetworkSupportXS}}"
                        >
                            <s-icon name="warning-new" />帮助文档
                        </a>
                    </div>
                </div>
                <introduce-panel
                    isShow="{{show}}"
                    klass="{{'IPv6-vpn-wrapper'}}"
                    title="{{introduceTitle}}"
                    markerDesc="{{markerDesc}}"
                    introduceOptions="{{introduceOptions}}"
                    on-toggle="handleToggle($event)"
                ></introduce-panel>
            </div>
            <div slot="bulk">
                <s-tip-button
                    skin="primary"
                    isDisabledVisibile="{{true}}"
                    disabled="{{createPath || disableCreate}}"
                    on-click="onCreate"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{createPathMessage || disableCreateTip | raw}}
                    </div>
                    <outlined-plus />
                    创建路径分析
                </s-tip-button>
                <s-tooltip class="left_class" trigger="{{release.disable ? 'hover' : ''}}" placement="top">
                    <!--bca-disable-next-line-->
                    <div slot="content">{{release.message | raw}}</div>
                    <s-button on-click="onDelete" disabled="{{release.disable}}"> 删除</s-button>
                </s-tooltip>
            </div>
            <div slot="filter">
                <div class="filter-buttons-wrap">
                    <s-search
                        width="{{230}}"
                        class="search-warp"
                        value="{=payload.keyword=}"
                        placeholder="{{searchholder}}"
                        on-search="onSearch($event)"
                    >
                        <s-select
                            slot="options"
                            width="120"
                            datasource="{{searchType}}"
                            value="{=payload.keywordType=}"
                            on-change="searchTypeChange($event)"
                        >
                        </s-select>
                    </s-search>
                    <s-button class="button-item s-icon-button" on-click="refresh" track-name="刷新"
                        ><outlined-refresh class="icon-class"
                    /></s-button>
                    <custom-column
                        class="left_class"
                        columnList="{{customColumn.datasource}}"
                        initValue="{{customColumn.value}}"
                        type="pathanalise"
                        on-init="initColumns"
                        on-change="onCustomColumns"
                    >
                    </custom-column>
                </div>
            </div>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                on-selected-change="tableSelected($event)"
                selection="{=table.selection=}"
            >
                <div slot="empty">
                    <s-empty on-click="onCreate"> </s-empty>
                </div>
                <div slot="c-pathId" class="path-id-widget">
                    <a href="#/vpc/pathanalise/detail?id={{row.shortId}}" class="truncated" title="{{row.name}}"
                        >{{ row.name }}</a
                    >
                    <s-popover
                        s-ref="popover-name-{{rowIndex}}"
                        placement="top"
                        trigger="click"
                        class="edit-popover-class"
                    >
                        <div class="edit-wrap" slot="content">
                            <s-input
                                value="{=edit.name.value=}"
                                width="320"
                                placeholder="请输入名称"
                                on-input="onEditInput($event, rowIndex, 'name')"
                            />
                            <div class="edit-tip">
                                大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65
                            </div>
                            <s-button
                                skin="primary"
                                s-ref="editBtn-name-{{rowIndex}}"
                                disabled="{{true}}"
                                on-click="editConfirm(row, rowIndex, 'name')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel(rowIndex, 'name')">取消</s-button>
                        </div>
                        <outlined-editing-square class="name-icon" on-click="onInstantEdit(row, rowIndex, 'name')" />
                    </s-popover>
                    <br />
                    <span class="truncated vpn-id" title="{{row.shortId}}">{{ row.shortId }}</span>
                    <s-clip-board class="name-icon" text="{{row.shortId}}" />
                </div>
                <div slot="c-sourceType">
                    <span>{{row.sourceType | getType}}</span>
                </div>
                <div slot="c-sourceId">
                    <span title="{{row.sourceId ? row.sourceId + '/' + row.sourceIp : row.sourceIp}}" class="truncated">
                        {{row.sourceId ? row.sourceId + '/' + row.sourceIp : row.sourceIp}}
                    </span>
                </div>
                <div slot="c-sourceVpcId">
                    <span>{{row.sourceVpcId || '-'}}</span>
                </div>
                <div slot="c-destVpcId">
                    <span>{{row.destVpcId || '-'}}</span>
                </div>
                <div slot="c-destId">
                    <span title="{{row.destId ? row.destId + '/' + row.destIp : row.destIp}}" class="truncated">
                        {{row.destId ? row.destId + '/' + row.destIp : row.destIp}}
                    </span>
                </div>
                <div slot="c-destType">
                    <span>{{row.destType | getType}}</span>
                </div>
                <div slot="c-protocol">
                    <span>{{row.protocol | getUpCase}}</span>
                </div>
                <div slot="c-sourceStatus">
                    <span class="{{row.status | statusClass}}">{{row.status | getStatus}}</span>
                </div>
                <div slot="c-sourceRegion">
                    <span>{{row.sourceRegion | getRegion}}</span>
                </div>
                <div slot="c-destRegion">
                    <span>{{row.destRegion | getRegion}}</span>
                </div>
                <div slot="c-lastAnaliseResult">
                    <span class="{{row.lastAnaliseResult | statusLastClass}}">
                        {{row.lastAnaliseResult | getLastStatus}}
                    </span>
                </div>
                <div slot="c-analyzeTime">
                    <span>{{row.analyzeTime | getTime}}</span>
                </div>
                <div slot="c-description">
                    <span class="truncated">{{row.description || '-'}}</span>
                    <s-popover
                        s-ref="popover-description-{{rowIndex}}"
                        placement="top"
                        trigger="click"
                        class="edit-popover-class"
                    >
                        <div class="edit-wrap" slot="content">
                            <s-input-text-area
                                placeholder="请输入描述"
                                value="{=edit.description.value=}"
                                width="160"
                                height="60"
                                maxLength="{{200}}"
                                on-input="onEditInput($event, rowIndex, 'description')"
                            />
                            <div class="edit-tip">描述不能超过200个字符</div>
                            <s-button
                                skin="primary"
                                s-ref="editBtn-description-{{rowIndex}}"
                                disabled="{{true}}"
                                on-click="editConfirm(row, rowIndex, 'description')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel(rowIndex, 'description')">取消</s-button>
                        </div>
                        <outlined-editing-square
                            class="name-icon"
                            on-click="onInstantEdit(row, rowIndex, 'description')"
                        />
                    </s-popover>
                </div>
                <div slot="c-opt" class="operations">
                    <s-button skin="stringfy" disabled="{{row.status === 'ANALISING'}}" on-click="startPathAnalise(row)"
                        >分析</s-button
                    >
                    <s-tooltip
                        placement="top"
                        trigger="{{row.status !== 'ANALISED' ? 'hover' : ''}}"
                        content="{{!row.status ? '请先进行分析' : '当前状态不支持查看分析路径，请重新分析再进行查看'}}"
                    >
                        <s-button
                            skin="stringfy"
                            on-click="onPathanaliseDrawerShow(row)"
                            disabled="{{row.status !== 'ANALISED'}}"
                            >查看分析路径</s-button
                        >
                    </s-tooltip>
                    <br />
                    <s-button skin="stringfy" on-click="deletePathanalise(row)">删除</s-button>
                </div>
            </s-table>
            <s-pagination
                s-if="{{pager.total}}"
                slot="pager"
                layout="{{'total, pageSize, pager, go'}}"
                pageSize="{{pager.pageSize}}"
                total="{{pager.total}}"
                page="{{pager.page}}"
                resetPageWhenSizeChange="{{true}}"
                on-pagerChange="onPagerChange"
            />
        </s-app-list-page>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@invokeComp('@custom-column', '@search-res', '@vpc-select', '@introduce-panel')
class PathAnaliseList extends Component {
    static components = {
        'outlined-plus': OutlinedPlus,
        'outlined-editing-square': OutlinedEditingSquare,
        'outlined-refresh': OutlinedRefresh
    };
    initData() {
        const allColumns = columns.slice();
        const customColumnDb = allColumns.map(item => ({
            text: item.label,
            value: item.name,
            disabled: item.name === 'pathId' || item.name === 'opt'
        }));
        return {
            customColumn: {
                value: [
                    'pathId',
                    'sourceStatus',
                    'lastAnaliseResult',
                    'sourceId',
                    'destId',
                    'protocol',
                    'analyzeTime',
                    'description',
                    'opt'
                ],
                datasource: customColumnDb
            },
            searchType: PathSearchType.toArray(),
            searchholder: '请输入路径分析名称进行搜索',
            payload: {
                keyword: '',
                keywordType: 'name'
            },
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                allColumns,
                datasource: [],
                selectedItems: []
            },
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            release: {},
            DocService,
            FLAG,
            introduceTitle: '路径分析',
            markerDesc: [
                '路径分析用于检查用户源实例和目的实例之间路径的连通性，为用户提供可视化、故障诊断/定位以及网络架构调优的能力。',
                '路径分析的核心字段为源、协议、源端口、目的、目的端口等。'
            ],
            introduceIcon: INTRODUCE_ICON_COLOR,
            documentIcon: DOCUMENT_ICON,
            introduceEle: null
        };
    }
    static filters = {
        getLastStatus(value) {
            if (value === 'SUCCESS') {
                return '可访问';
            } else if (value === 'FAILED') {
                return '不可访问';
            } else if (value === 'UN_ANALISE') {
                return '未分析';
            }
            return '-';
        },
        statusClass(value) {
            if (value === 'ANALISED') {
                return 'status normal';
            } else if (value === 'ANALISING') {
                return 'status warning';
            } else if (value === 'ANALISE_FAILED') {
                return 'status error';
            }
            return '';
        },
        statusLastClass(value) {
            if (value === 'SUCCESS') {
                return 'status normal';
            } else if (value === 'FAILED') {
                return 'status error';
            } else if (value === 'UN_ANALISE') {
                return 'status unavailable';
            }
            return '';
        },
        getStatus(value) {
            if (value === 'ANALISED') {
                return '分析完成';
            } else if (value === 'ANALISING') {
                return '分析中';
            } else if (value === 'ANALISE_FAILED') {
                return '分析失败';
            }
            return '-';
        },
        getType(value) {
            let typeList = {
                BCC: '云服务器',
                BBC: '弹性裸金属服务器',
                SUBNET: '子网',
                DC: '专线通道',
                VPN_CONN: 'VPN通道',
                IDC: '混合云网关',
                PUBLICIP: '公网IP',
                PUBLIC_IP: '公网IP',
                OUT_OF_CLOUD: '云外IP'
            };
            return value ? typeList[value] : '-';
        },
        getUpCase(value) {
            if (value === 'all' || value === 'ALL') {
                return '全部协议';
            }
            return value ? value.toUpperCase() : '全部协议';
        },
        getRegion(value) {
            return value ? AllRegion.getTextFromValue(value) : '-';
        },
        getTime(value) {
            return value ? utcToTime(value) : '-';
        }
    };
    static computed = {
        disableCreate() {
            const quota = this.data.get('quota');
            return !quota || quota?.free <= 0;
        },
        disableCreateTip() {
            const quota = this.data.get('quota');
            return !quota || quota?.free <= 0 ? '路径分析配额不足。如需提升配额，请提交工单' : '';
        }
    };
    inited() {
        const AllRegion = ContextService.getEnum('AllRegion');
        if (window.$context.getCurrentRegionId() === AllRegion.SIN) {
            this.data.set('createPath', true);
            this.data.set('createPathMessage', '新加坡地域资源售罄，请您切换到其他地域创建');
        }
    }
    attached() {
        // this.checkIamStsRole();
        window.$storage.get('showPathIntroduce') === false && this.data.set('show', false);
        this.loadPage();
        let {release} = checker.check(rules, []);
        this.data.set('release', release);
        this.data.set('introduceEle', this.ref('introduce'));
    }
    getQuota() {
        this.$http.pathanaliseQuota().then(res => {
            this.data.set('quota', res);
        });
    }

    // 改变页数
    onPagerChange({value}) {
        this.data.set('pager.page', value.page);
        this.data.set('pager.pageSize', value.pageSize);
        this.loadPage();
    }

    onInstantEdit(row, rowIndex, type) {
        this.data.set(`edit.${type}.value`, row[`${type}`]);
        this.data.set(`edit.${type}.error`, false);
        const editWrap = this.ref(`popover-${type}-${rowIndex}`);
        editWrap.data.set('visible', !editWrap.data.get('visible'));
    }

    // 编辑弹框-输入名称/描述
    onEditInput(e, rowIndex, type) {
        let result =
            type === 'name'
                ? e.value === '' || !/^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/.test(e.value)
                : e.value.length > 200;
        this.data.set(`edit.${type}.error`, result);
        this.data.set(`edit.${type}.value`, e.value);
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', result);
    }
    async editConfirm(row, rowIndex, type) {
        let edit = this.data.get(`edit.${type}`);
        if (edit.error) {
            return;
        }
        this.$http
            .editPathanalise(row.shortId, {
                [type]: edit.value
            })
            .then(() => {
                this.editCancel(rowIndex, type);
                Notification.success('修改成功');
                this.loadPage();
            });
    }

    editCancel(rowIndex, type) {
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', true);
        this.ref(`popover-${type}-${rowIndex}`).data.set('visible', false);
    }

    onCreate() {
        location.hash = '#/vpc/pathanalise/create';
    }

    onDelete() {
        let confirm = new Confirm({
            data: {
                open: true,
                content: '确认删除选中的路径分析？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            let shortId = this.data.get('table.selectedItems')[0].shortId;
            this.$http.deletePathanalise(shortId).then(() => {
                this.data.set('pager.page', 1);
                this.loadPage();
                Notification.success('释放成功', {placement: 'topRight'});
            });
        });
    }

    onPathanaliseDrawerShow(row) {
        const bcm = new DrawerPathamalise({
            data: {
                name: row.name,
                payload: row,
                resourceId: row.shortId,
                protocol: row.protocol
            }
        });
        bcm.attach(document.body);
    }

    getPayload() {
        const searchParam = this.data.get('payload');
        const {pager} = this.data.get('');
        const vpcId = window.$storage.get('vpcInfo')?.shortId || '';
        let payload = {
            pageNo: pager.page,
            pageSize: pager.pageSize
        };
        return {...payload, vpcId, ...searchParam};
    }

    searchTypeChange({value}) {
        this.data.set('payload.keyword', '');
        let tip = `请输入${PathSearchType.getTextFromValue(value)}进行搜索`;
        this.data.set('searchholder', tip);
    }

    handleInputChange({value}) {
        this.data.set('payload.keyword', value);
    }

    onSearch(e) {
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    // 设置表格列
    setTableColumns(customColumnNames) {
        customColumnNames = customColumnNames ? customColumnNames : this.data.get('customColumn.value');
        const allColumns = this.data.get('table.allColumns');
        let columns = [];
        allColumns.forEach(item => customColumnNames.indexOf(item.name) > -1 && columns.push(item));
        this.data.set('table.columns', columns);
    }
    initColumns(value) {
        this.setTableColumns(value);
    }
    // 自定义表格列
    onCustomColumns(value) {
        this.data.set('customColumn.value', value);
        this.setTableColumns(value);
    }
    tableSelected(e) {
        this.data.set('table.selectedItems', e.value.selectedItems);
        let {release} = checker.check(rules, e.value.selectedItems);
        this.data.set('release', release);
    }
    resetTable() {
        this.data.set('table.selection', {
            mode: 'multi',
            selectedIndex: []
        });
        this.data.set('table.selectedItems', []);
    }

    loadPage() {
        this.data.set('table.loading', true);
        this.getQuota();
        let payload = this.getPayload();
        if (payload.keywordType === 'protocol') {
            payload.keyword = payload?.keyword?.trim()?.toLowerCase();
            if (('全部协议'.includes(payload.keyword) || payload.keyword === 'all') && payload.keyword) {
                payload.keyword = 'all';
            }
        }
        if (
            (payload.keywordType === 'sourceRegion' || payload.keywordType === 'destRegion') &&
            payload?.keyword?.trim()
        ) {
            let key = payload.keyword.trim();
            const regionArray = getVpcAvaliableRegion() || {};
            let type = regionArray?.regionList.map(item => item.value);
            let typeObj = regionArray?.regionList.map(item => item.text);
            let index = type.findIndex(item => item.includes(key));
            let indexObj = typeObj.findIndex(item => item.includes(key));
            if (index > -1 || indexObj > -1) {
                payload.keyword = type[index] || type[indexObj];
                payload.keyword = payload.keyword;
            }
        }
        if ((payload.keywordType === 'sourceType' || payload.keywordType === 'destType') && payload?.keyword?.trim()) {
            let key = payload.keyword.trim();
            let type = ['BCC', 'BBC', 'SUBNET', 'VPN_CONN', 'DC', 'IDC'];
            let typeObj = ['云服务器', '弹性裸金属服务器', '子网', 'VPN通道', '专线通道', '混合云网关'];
            let index = type.findIndex(item => item.includes(key));
            let indexObj = typeObj.findIndex(item => item.includes(key));
            if (index > -1 || indexObj > -1) {
                payload.keyword = type[index] || type[indexObj];
                payload.keyword = payload.keyword;
            }
        }
        this.resetTable();
        return this.$http.getPathanaliseList(payload).then(data => {
            this.data.set('table.datasource', data.result);
            this.data.set('pager.total', data.totalCount);
            this.data.set('table.loading', false);
        });
    }

    onRegionChange() {
        location.reload();
    }

    vpcChange() {
        this.data.set('pager.page', '1');
        this.loadPage();
    }
    deletePathanalise(row) {
        let confirm = new Confirm({
            data: {
                open: true,
                content: '确认删除选中的路径分析？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            let shortId = row.shortId;
            let count = this.data.get('pager.total');
            this.$http.deletePathanalise(shortId).then(() => {
                this.loadPage().then(() => {
                    // 防止在第二页删除数据后没数据页码对不上的问题
                    this.data.set('pager.total', count);
                });
                Notification.success('释放成功', {placement: 'topRight'});
            });
        });
    }
    startPathAnalise(row) {
        let count = this.data.get('pager.total');
        this.$http.startPathanalise(row.shortId).then(res => {
            this.loadPage().then(() => {
                let dataSource = this.data.get('table.datasource');
                dataSource.forEach(item => {
                    if (item.shortId === row.shortId) {
                        item.pathAnaId = res.id;
                    }
                });
                this.data.set('table.datasource', dataSource);
                // 防止在第二页删除数据后没数据页码对不上的问题
                this.data.set('pager.total', count);
            });
            Notification.success('执行分析成功', {placement: 'topRight'});
        });
    }
    /**
     * @description 介绍卡片变化时回调
     * @param {boolean} flag
     */
    handleToggle(flag) {
        this.data.set('show', flag);
        window.$storage.set('showPathIntroduce', flag);
        this.data.set('introduceIcon', INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#151B26';
    }
    handleShowCard() {
        if (!this.data.get('show')) {
            this.data.set('show', true);
            window.$storage.set('showPathIntroduce', true);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type) {
        if (type === 'introduce') {
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
            const ele = this.data.get('introduceEle');
            ele.style.color = '#2468F2';
        } else {
            this.data.set('documentIcon', DOCUMENT_ICON_COLOR);
        }
    }
    handleMouseLeave(type) {
        const isShow = this.data.get('show');
        if (type === 'introduce') {
            this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
            const ele = this.data.get('introduceEle');
            !isShow && (ele.style.color = '#151B26');
        } else {
            this.data.set('documentIcon', DOCUMENT_ICON);
        }
    }
    refresh() {
        this.loadPage();
    }
}
export default San2React(Processor.autowireUnCheckCmpt(PathAnaliseList));
