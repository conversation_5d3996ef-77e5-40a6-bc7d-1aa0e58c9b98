.s-tooltip-arrow {
    display: none;
}

.path_ana_drawer {
    .s-drawer-header-title {
        font-size: 18px !important;
    }
    .pathErrShow {
        color: #f33e3e !important;
    }
    .flex_wrap {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 20px;
    }
    .select_wrap {
        width: 270px;
        height: 50px;
        border-radius: 50px;
        border: 1px solid #84868c;
        line-height: 50px;
        margin-left: 50px;
        display: flex;
        align-items: center;
        .s-icon-button-able {
            position: relative;
        }
        a {
            margin-left: 14px;
        }
    }
    .s-drawer-wrapper {
        width: 1200px !important;
    }
    .select_wrap_content {
        width: 270px;
        height: auto;
        padding-bottom: 8px;
        border: 1px solid #84868c;
        text-align: left;
        padding-left: 16px;
        padding-top: 8px;
        margin-left: 50px;
        border-top: none;
        a {
            margin-left: 14px;
        }
    }
    .wrap_line {
        width: 270px;
        height: 50px;
        border-left: 1px solid #84868c;
        margin-left: 188px;
    }
    .no_line {
        border-left: none;
    }
    .item_text {
        display: inline-block;
        width: 60px;
    }
    .item_wrap {
        display: inline-block;
        margin: 0 16px;
        max-width: 150px;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: bottom;
    }
    .item-circle {
        width: 40px;
        height: 40px;
        margin-left: 8px;
        margin-top: 4px;
        border-radius: 40px;
        display: inline-block;
    }
    .bcc_item {
        background: url('../img/BCC.svg?url') no-repeat;
        background-size: 40px 40px;
        border-radius: 40px;
    }
    .subnet_item {
        background: url('../img/子网.svg?url') no-repeat;
        background-size: 40px 40px;
        border-radius: 40px;
    }
    .bbc_item {
        background: url('../img/BBC.svg?url') no-repeat;
        background-size: 40px 40px;
        border-radius: 40px;
    }
    .sg_item {
        background: url('../img/安全组.svg?url') no-repeat;
        background-size: 40px 40px;
        border-radius: 40px;
    }
    .acl_item {
        background: url('../img/安全访问控制列表.svg?url') no-repeat;
        background-size: 40px 40px;
        border-radius: 40px;
    }
    .route_item {
        background: url('../img/路由表.svg?url') no-repeat;
        background-size: 40px 40px;
        border-radius: 40px;
    }
    .pc_item {
        background: url('../img/对等链接.svg?url') no-repeat;
        background-size: 40px 40px;
        border-radius: 40px;
    }
    .csn_item {
        background: url('../img/云智能网csn.svg?url') no-repeat;
        background-size: 40px 40px;
        border-radius: 40px;
    }
    .dc_item {
        background: url('../img/专线通道.svg?url') no-repeat;
        background-size: 40px 40px;
        border-radius: 40px;
    }
    .dg_item {
        background: url('../img/专线网关.svg?url') no-repeat;
        background-size: 40px 40px;
        border-radius: 40px;
    }
    .vpn_item {
        background: url('../img/vpn网关.svg?url') no-repeat;
        background-size: 40px 40px;
        border-radius: 40px;
    }
    .vpn_conn_item {
        background: url('../img/VPN通道.svg?url') no-repeat;
        background-size: 40px 40px;
        border-radius: 40px;
    }
    .ip_dc_item {
        background: url('../img/IP图标.svg?url') no-repeat;
        background-size: 40px 40px;
        border-radius: 40px;
    }
    .nat_item {
        background: url('../img/NAT网关.svg?url') no-repeat;
        background-size: 40px 40px;
        border-radius: 40px;
    }
    .item_top_wrap {
        margin: 8px 8px 8px 16px;
        width: 172px;
    }
    .loading_wrap {
        position: relative;
        top: 50%;
        left: 50%;
    }
    .item_subnodes_line {
        border-left: 1px solid black;
        width: 50px;
        height: 100px;
        height: 50px;
        margin-left: 100px;
    }
    .item_subnodes_node {
        border: 1px solid black;
        width: 50px;
        height: 140px;
        text-align: center;
        margin-left: 75px;
        border-radius: 50px;
    }
    .item_subNodes_3 {
        .item_subnodes_line_1 {
            margin-left: 40px;
        }
        .item_subnodes_node_1 {
            margin-left: 15px;
        }
        .item_subnodes_line_2 {
            margin-left: 30px;
        }
        .item_subnodes_node_2 {
            margin-left: 10px;
        }
    }
    .item_subNodes_4 {
        .item_subnodes_line_0 {
            margin-left: 75px;
        }
        .item_subnodes_node_0 {
            margin-left: 50px;
        }
        .item_subnodes_line_1 {
            margin-left: 20px;
        }
        .item_subnodes_node_1 {
            margin-left: -5px;
        }
        .item_subnodes_line_2 {
            margin-left: 20px;
        }
        .item_subnodes_node_2 {
            margin-left: -5px;
        }
        .item_subnodes_line_3 {
            margin-left: 20px;
        }
        .item_subnodes_node_3 {
            margin-left: -5px;
        }
    }
    .flex_inline {
        line-height: initial;
    }
    .item_subnodes_fath {
        display: inline-block;
    }
    .path_subNodes {
        display: flex;
    }
    .node_error {
        border-color: red;
    }
    .node_success {
        border-color: #30bf13;
    }
    .node_unknown {
        border-color: #f4b329;
    }
    .alarm-info-status {
        height: 100%;
        display: flex;
        align-items: center;
        margin-left: 10px;
        .alarm-state {
            margin-right: 8px;
            .status {
                margin-right: -8px;
            }
        }
    }
    .line_error {
        border-left: 2px dashed red;
    }
    .line_success {
        border-left: 2px solid #30bf13;
    }
    .line_unknown {
        border-left: 2px dashed #f4b329;
    }
    .item_sg_class {
        width: 84px;
    }
    .pc_text {
        margin: 0 12px;
    }
    .dc_text {
        margin: 0 6px;
    }
    .item_pc_vcp {
        width: 76px;
    }
    .item_pc_vpc {
        margin-left: 0px;
    }
    .select_wrap_content_pc {
        height: 118px;
    }
    .item_sg_content {
        margin-left: -8px;
    }
    .item-shu-circle {
        width: 30px;
        height: 30px;
        background-size: 30px 30px;
        border-radius: 30px;
        margin-left: 0px;
    }
    .item-shu-div {
        writing-mode: tb-rl;
        padding-left: 14px;
        padding-top: 14px;
    }
    .request {
        .checkbox_no_lable {
            .s-checkbox-input {
                display: none;
            }
            .s-radio-text {
                margin-left: 0px;
                font-size: 16px;
                font-weight: 500;
            }
        }
    }
    .response {
        .checkbox_no_lable {
            .s-checkbox-input {
                display: none;
            }
            .s-radio-text {
                margin-left: 0px;
                font-size: 16px;
                font-weight: 500;
            }
        }
    }
    .item_route_vpc_status {
        width: 76px;
    }
    #wrapper {
        background: radial-gradient(ellipse at top left, rgba(255, 255, 255, 1) 40%, rgba(229, 229, 229, 0.9) 100%);
        height: 100vh;
        padding: 60px 80px;
        width: 100vw;
    }
    .state-item {
        width: 80px;
        height: 40px;
        color: #606266;
        background: #f6f6f6;
        border: 2px solid rgba(0, 0, 0, 0.05);
        text-align: center;
        line-height: 40px;
        font-family: sans-serif;
        border-radius: 4px;
        margin-right: 60px;
    }
    .line-wrap {
        display: flex;
        margin-bottom: 40px;
    }
    .line_nextLine_style {
        border-left: 2px dashed red;
    }
    .line_nextLine_style_success {
        border-left: 2px solid #30bf13;
    }
    .line_nextLine_style_unknown {
        border-left: 2px dashed #f4b329;
    }
    .displayLine {
        display: none;
    }
    .item_subNodes_last_2 {
        border-top: 2px solid #30bf13;
    }
    .item_subNodes_last_2_3 {
        border-right: 2px solid #30bf13;
        border-top: 2px solid #30bf13;
        border-left: none;
        margin-left: -13px;
    }
    .item_subNodes_last_2 {
        border-top: 2px solid #30bf13;
    }
    .item_subNodes_last_2_3 {
        border-right: 2px solid #30bf13;
        border-top: 2px solid #30bf13;
        border-left: none;
        margin-left: -14px !important;
    }

    .item_subNodes_last_2_0 {
        border-top: 2px solid #30bf13;
    }
    .item_subNodes_last_2_1 {
        border-right: 2px solid #30bf13;
        border-top: 2px solid #30bf13;
        border-left: none;
        margin-left: -8px !important;
    }
    .item_subNodes_last_2_2 {
        margin-left: 10px !important;
    }
    .item_subNodes_last_2_m_2 {
        margin-left: -10px !important;
    }

    .item_subNodes_reverse_last_2 {
        border-bottom: 2px solid #30bf13;
    }
    .item_subNodes_reverse_last_2_3 {
        border-right: 2px solid #30bf13;
        border-bottom: 2px solid #30bf13;
        border-left: none;
        margin-left: -8px !important;
    }
    .item_subNodes_reverse_last_2_1 {
        border-right: 2px solid #30bf13;
        border-bottom: 2px solid #30bf13;
        border-left: none;
        margin-left: -8px !important;
    }
    .item_subNodes_reverse_last_2_0 {
        border-bottom: 2px solid #30bf13;
    }
    .item_subNodes_reverse_last_2_2 {
        margin-left: 10px !important;
    }
    .item_subNodes_reverse_m_last_2_2 {
        margin-left: -14px !important;
    }
    .item_reach_node_double_1 {
        border-top: 2px solid #30bf13;
        width: 80px;
    }
    .item_reach_node_double_1_0 {
        border-top: 2px solid #30bf13;
        border-right: 2px solid #30bf13;
        border-left: none;
        margin-left: -8px !important;
        width: 80px;
    }
    .item_reach_node_double_1_1 {
        margin-left: 48px !important;
    }
    .item_reach_node_double_1_2 {
        margin-left: 70px !important;
    }

    .item_top_wrap_dc {
        width: 174px;
        margin-right: 6px;
    }

    .item_reach_node_3_no_middle {
        margin-left: 20px !important;
    }
    .item_reach_node_3_no_one_1 {
        margin-left: 90px !important;
    }
    .item_reach_node_3_no_one_2 {
        margin-left: -70px !important;
    }

    .item_reach_node_reverse_3_0 {
        border-top: 2px solid #30bf13;
        width: 60px;
    }
    .item_reach_node_reverse_3_1_0 {
        border-top: 2px solid #30bf13;
        margin-left: -24px !important;
        border-right: 2px solid #30bf13;
        border-left: 0;
    }
    .item_reach_node_reverse_3_1_1 {
        margin-left: 0px !important;
    }
    .item_reach_node_reverse_3_2_0 {
        border-right: 2px solid #30bf13;
        border-left: 0;
        margin-left: -24px !important;
        width: 86px;
        border-top: 2px solid #30bf13;
    }
    .item_reach_node_reverse_3_2_1 {
        margin-left: 36px !important;
    }
    .item_reach_node_reverse_3_2_2 {
        margin-left: 60px !important;
    }
    .item_reach_node_3_no_one {
        margin-left: 40px !important;
        .item_subnodes_line {
            margin-left: 0 !important;
        }
        .item_subnodes_node {
            margin-left: -24px !important;
        }
    }
    .s-loading {
        position: relative;
        left: 50%;
        top: 50%;
    }
    .path-error-show {
        color: #f33e3e;
        font-weight: 400;
        font-size: 16px;
    }
    .no-loading-wrap {
        display: none;
    }
    .status.unavailable:before {
        color: #dcdcdc;
    }
    .warn {
        color: #f4b329;
    }
    .satisfaction {
        width: 100%;
        height: auto;
        margin: 8px 0px 12px;
        display: flex;
        align-items: flex-start;
        background: #eef3fe;
        border-radius: 4px;
        padding: 8px 0;
    }
    .suggest-widget-auto {
        padding: 0;
        background: none;
    }
    .suggest-title {
        display: none;
    }
    .satisfaction-text {
        font-size: 12px;
        color: #151b26;
        font-weight: 400;
        margin-left: 12px;
        margin-right: 12px;
        height: 22px;
        line-height: 22px;
        display: inline-block;
    }
    .suggest-widget-inner .like-dislike-widget {
        margin-top: 0px;
        display: flex;
        .common {
            background: none;
            border: none;
            width: auto;
            height: 22px;
            line-height: 22px;
        }
    }
    .suggest-widget-inner .suggest-no-satisfied .suggest-tag-item-selected {
        background: #ffffff !important;
        border: 1px solid #2468f2 !important;
    }
    .suggest-tag-item {
        background-color: #ffffff !important;
    }
    .like-dislike-widget .common:hover {
        background-color: inherit !important;
    }
    .suggest-textarea {
        width: 396px;
    }
    .suggest-widget-inner .suggest-no-satisfied {
        margin-top: 0px;
    }
    .suggest-widget-inner .suggest-submit {
        margin-top: 10px;
    }
}

// .path-dobule-width {
//   .s-drawer-wrapper {
//     width: 100vw !important;
//   }
// }

// .path_ana_drawer {
//   .s-drawer-wrapper {
//     width: 100vw !important;
//   }
// }

.close-drawer-class {
    .s-drawer-wrapper {
        width: 0 !important;
    }
    .s-drawer-mask {
        width: 0 !important;
    }
}
