export let linkHref = {
    BCC: '/bcc/#/bcc/instance/detail',
    BBC: '/bbc/#/bbc/instance/detail/detail',
    SUBNET: '/network/#/vpc/subnet/detail',
    SG: '/network/#/vpc/security/detail',
    ACL: '/network/#/vpc/acl/manage',
    ROUTE: '/network/#/vpc/route/detail',
    PC: '/network/#/vpc/peerconn/detail',
    CSN: '/csn/#/csn/detail',
    DC: '/network/#/dc/channel/detail',
    DG: '/network/#/vpc/dcgw/detail',
    VPN: '/network/#/vpc/vpn/detail',
    NAT: '/network/#/vpc/nat/detail',
    ESG: '/network/#/vpc/enterpriseSecurity/detail',
    IDC: 'javascript:;',
    VPN_CONN: '/network/#/vpc/vpn/list',
    CFW: '/cfw/#/cfw/strategy/detail',
    ENI: '/network/#/vpc/eni/detail',
    EIP: '/eip/#/eip/instance/detail'
};

export let linkStr = value => {
    let str = '';
    if (value?.nodeType === 'BCC') {
        str = `?id=${value.id}&instanceId=${value.uuid}`; //
        if (value.id && value.uuid) {
            return linkHref[value?.nodeType] + str;
        }
        return '';
    } else if (value?.nodeType === 'BBC') {
        str = `?instanceId=${value.uuid}`; //
        if (value.uuid) {
            return linkHref[value?.nodeType] + str;
        }
        return '';
    } else if (value?.nodeType === 'SUBNET') {
        str = `?subnetId=${value.id}`; //
        if (value.id) {
            return linkHref[value?.nodeType] + str;
        }
        return '';
    } else if (value?.nodeType === 'SG') {
        str = `?id=${value?.showContextObj?.securityGroupUuid}&vpcId=${value.vpcUuid}`; //
        if (value?.showContextObj?.securityGroupUuid && value.vpcUuid) {
            return linkHref[value?.nodeType] + str;
        }
        return '';
    } else if (value?.nodeType === 'ACL') {
        str = `?vpcId=${value.vpcUuid}&id=${value?.showContextObj?.aclId}`; //
        if (value?.showContextObj?.aclId && value.vpcUuid) {
            return linkHref[value?.nodeType] + str;
        }
        return '';
    } else if (value?.nodeType === 'ROUTE') {
        str = `?vpcId=${value.vpcUuid}`; //
        if (value.vpcUuid) {
            return linkHref[value?.nodeType] + str;
        }
        return '';
    } else if (value?.nodeType === 'PC') {
        str = `?vpcId=${value.vpcUuid}&localIfId=${value?.localIfId || value?.showContextObj?.localIfId}`; //
        if (value.vpcUuid && (value?.localIfId || value?.showContextObj?.localIfId)) {
            return linkHref[value?.nodeType] + str;
        }
        return '';
    } else if (value?.nodeType === 'CSN') {
        str = `?csnId=${value.id}&current=detail`; //
        if (value.id) {
            return linkHref[value?.nodeType] + str;
        }
        return '';
    } else if (value?.nodeType === 'DC') {
        str = `?instanceId=${value?.showContextObj?.dcphy_id}&channelId=${value.id || value?.showContextObj?.id}`; //
        if (value?.showContextObj?.authorizationStatus === 'other_available') {
            str += '&creator=other';
        } else {
            str += '&creator=oneself';
        }
        if (value?.showContextObj?.dcphy_id && (value.id || value?.showContextObj?.id)) {
            return linkHref[value?.nodeType] + str;
        }
        return '';
    } else if (value?.nodeType === 'DG') {
        str = `?vpcId=${value.vpcUuid}&dcgwId=${value.id}`; //
        if (value.vpcUuid && value.id) {
            return linkHref[value?.nodeType] + str;
        }
        return '';
    } else if (value?.nodeType === 'VPN') {
        str = `?vpcId=${value.vpcUuid}&vpnId=${value.id}&vpnType=${value.vpnType || value?.showContextObj?.vpnType}`;
        if (value.vpcUuid && value.id && (value.vpnType || value?.showContextObj?.vpnType)) {
            return linkHref[value?.nodeType] + str;
        }
        return '';
    } else if (value?.nodeType === 'NAT') {
        str = `?vpcId=${value.vpcUuid || value.showContextObj?.vpcID}&id=${value.id}&natType=public`; //
        if ((value.vpcUuid || value.showContextObj?.vpcID) && value.id) {
            return linkHref[value?.nodeType] + str;
        }
        return '';
    } else if (value?.nodeType === 'ESG') {
        str = `?id=${value?.showContextObj?.esgUuid}`; //
        if (value?.showContextObj?.esgUuid) {
            return linkHref[value?.nodeType] + str;
        }
        return '';
    } else if (value?.nodeType === 'IDC') {
        str = ''; //
        return '';
    } else if (value?.nodeType === 'VPN_CONN') {
        str = ''; //
        return '';
    } else if (value?.nodeType === 'CFW') {
        str = `?cfwId=${value?.id || value?.showContextObj?.cfwId}&type=detail`; //
        if (value?.id || value?.showContextObj?.cfwId) {
            return linkHref[value?.nodeType] + str;
        }
        return '';
    } else if (value?.nodeType === 'ENI') {
        str = `?eniId=${value?.showContextObj?.eniUuid}
            &shortEniId=${value?.id || value?.showContextObj?.eniId}&vpcId=${value?.showContextObj?.vpcId}`; //
        if (
            value?.showContextObj?.eniUuid &&
            (value?.id || value?.showContextObj?.eniId) &&
            value?.showContextObj?.vpcId
        ) {
            return linkHref[value?.nodeType] + str;
        }
        return '';
    } else if (value?.nodeType === 'EIP') {
        str = `?eip=${value?.showContextObj?.ip}&ipVersion=eipv4`; //
        if (value?.showContextObj?.ip) {
            return linkHref[value?.nodeType] + str;
        }
        return '';
    }
    return '';
};
