/*
 * @Description: 路径分析的抽屉
 */

import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, HttpClient, Processor} from '@baiducloud/runtime';
import {Drawer, Button, Checkbox, Loading, Tooltip, Notification} from '@baidu/sui';
import {SuggestSDKProcessor} from '@baidu/bce-vpc-sdk-react';
import {SuggestSDKProcessor as CollectionSDKProcessor, SuggestSDK} from '@baidu/bce-suggest-collection-sdk';
import {linkHref, linkStr} from './href';
import {pathReasonList} from '@/pages/sanPages/utils/config';
import {showContextPath} from './helper';

import './drawPathanalise.less';
import '@baidu/bce-suggest-collection-sdk/lib/style.css';
import '@baidu/bce-vpc-sdk-react/lib/style.css';

const AllRegion = window.$context.getEnum('AllRegion');
const processor = new SuggestSDKProcessor();
const processorCollection = new CollectionSDKProcessor();
const isOnline = location.hostname === 'console.bce.baidu.com' || location.hostname === 'console.vcp.baidu.com';
const {asComponent, invokeSUI, invokeSUIBIZ, template} = decorators;

const tpl = html`
    <template>
        <s-drawer
            open="{=open=}"
            id="pathanaliseId"
            direction="right"
            otherClose="{{false}}"
            on-close="closeDrawer"
            class="path_ana_drawer path-dobule-width {{closeDrawerClass}}"
        >
            <div slot="title" class="bcm-header">实例分析路径：{{name}}</div>
            <!--<span s-if="pathErrShow" class="pathErrShow">请先进行分析，再查看分析路径</span>-->
            <s-loading loading="{{loading}}" class="{{!loading ? 'no-loading-wrap' : ''}}"></s-loading>
            <div s-if="contentShow && !loading" class="path-error-show">
                当前路径分析的源或者目的已经被删除，请重新创建后查看。
            </div>
            <div class="satisfaction" s-if="!loading && !contentShow && showSatisfactionSuggest">
                <span class="satisfaction-text">问题是否解决：</span>
                <div id="satisfactionCollection"></div>
            </div>
            <div class="request" s-if="!loading && !contentShow" style="height:{{heightBorder}}px;width:100%;">
                <div s-if="!pathErrShow" class="flex_wrap">
                    <s-tooltip
                        trigger="{{checkedDis === 'success' ? 'hover' : ''}}"
                        placement="top"
                        s-if="!content"
                        content="当前路径状态不支持查看反向路径"
                    >
                        <s-checkbox
                            class="checkbox_no_lable"
                            label="请求方向路径"
                            checked="{=pathRevert=}"
                            disabled="{{checkedDis === 'success'}}"
                        />
                    </s-tooltip>
                    <s-tooltip
                        trigger="{{checkedDis === 'success' ? 'hover' : ''}}"
                        placement="top"
                        s-if="content"
                        content="当前路径状态不支持查看反向路径"
                    >
                        <s-checkbox
                            class="checkbox_no_lable"
                            label="请求方向路径"
                            checked="{{pathRevertDetail}}"
                            on-change="checkedChange"
                            disabled="{{checkedDis === 'success'}}"
                        />
                    </s-tooltip>
                    <div class="alarm-info-status">
                        <span class="alarm-state">
                            <span class="status normal"></span>
                            节点可访问
                        </span>
                        <span class="alarm-state">
                            <span class="status error"></span>
                            节点不可访问
                        </span>
                        <span class="alarm-state">
                            <span class="status unavailable"></span>
                            节点未抵达
                        </span>
                        <span class="alarm-state">
                            <span class="status warn"></span>
                            节点状态未知
                        </span>
                    </div>
                </div>
                <div style="height:{{heightBorder}}px;width:100%;" id="satisfaction" s-if="showSatisfaction"></div>
            </div>
            <div class="response" s-if="checkedDis === 'SUCCESS'" style="height:{{heightReverseBorder}}px;width:100%;">
                <div s-if="!pathErrShow" class="flex_wrap">
                    <s-tooltip
                        trigger="{{checkedDis === 'success' ? 'hover' : ''}}"
                        placement="top"
                        s-if="!content"
                        content="当前路径状态不支持查看反向路径"
                    >
                        <s-checkbox
                            class="checkbox_no_lable"
                            label="响应方向路径"
                            checked="{=pathRevert=}"
                            disabled="{{checkedDis === 'success'}}"
                        />
                    </s-tooltip>
                    <s-tooltip
                        trigger="{{checkedDis === 'success' ? 'hover' : ''}}"
                        placement="top"
                        s-if="content"
                        content="当前路径状态不支持查看反向路径"
                    >
                        <s-checkbox
                            class="checkbox_no_lable"
                            label="响应方向路径"
                            checked="{{pathRevertDetail}}"
                            on-change="checkedChange"
                            disabled="{{checkedDis === 'success'}}"
                        />
                    </s-tooltip>
                </div>
                <div
                    style="height:{{heightReverseBorder}}px;width:100%;"
                    id="satisfactionReverse"
                    s-if="showSatisfactionReverse"
                ></div>
            </div>
        </s-drawer>
    </template>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
class BcmDetail extends Component {
    components = {
        's-drawer': Drawer,
        's-button': Button,
        's-checkbox': Checkbox,
        's-loading': Loading,
        's-tooltip': Tooltip
    };
    initData() {
        return {
            open: true,
            chartConfig: [],
            pathRevert: false,
            pathRevertDetail: false,
            pathConfig: [],
            pathRevertConfig: [],
            showSatisfaction: true,
            showSatisfactionReverse: true,
            loading: true,
            heightBorder: 400,
            heightReverseBorder: 400,
            contentShow: false,
            showSatisfactionSuggest: true
        };
    }

    attached() {
        if (this.data.get('content')) {
            this.initContent();
            return;
        }
        // if (!this.data.get('payload').pathAnaId) {
        //   this.data.set('pathErrShow', true);
        //   return;
        // }
        let payload = {
            pageNo: 1,
            pageSize: 10000,
            pathId: this.data.get('payload').shortId,
            orderBy: 'createTime',
            order: 'asc'
        };
        this.$http.pathanaliseHistory(payload).then(res => {
            let result = res.result[0];
            this.data.set('historyInstance', result);
            let originResult = u.cloneDeep(result);
            if (result.status === 'ANALISING') {
                this.data.set('pathPending', true);
                return;
            } else {
                this.data.set('pathPending', false);
            }
            if (!result.content) {
                this.data.set('contentShow', true);
                this.data.set('loading', false);
                return;
            }
            let path = [];
            let pathMax = 4;
            let reversePath = [];
            let reversePathMax = 4;
            let pathIndex = 0;
            let pathReverseIndex = 0;
            !result.content.path && (result.content.path = []);
            !result.content.reversePath && (result.content.reversePath = []);
            const maxAxis = Math.max(
                ...result.content.path
                    .filter(item => item.subNodes && item.subNodes.length)
                    .map(item => item.subNodes.length)
            );
            const maxReverseAxis = Math.max(
                ...result.content.reversePath
                    .filter(item => item.subNodes && item.subNodes.length)
                    .map(item => item.subNodes.length)
            );
            if (result.content) {
                result.content.path.forEach((item, i) => {
                    if (item.context) {
                        item.showContextObj = JSON.parse(item.context);
                        item.showContext = showContextPath(item);
                    } else {
                        item.showContextObj = {};
                        item.showContext = showContextPath(item);
                    }
                    if (item.nodeType === 'EIP_BIND_INSTANCE') {
                        item.nodeTypeShow = item.showContextObj?.bindServiceType
                            ? window.$context.SERVICE_TYPE[item.showContextObj?.bindServiceType]
                                ? window.$context.SERVICE_TYPE[item.showContextObj?.bindServiceType]?.externalName
                                : item.showContextObj?.bindServiceType
                            : 'EIP_BIND_INSTANCE';
                    }
                    if (!item.showContext['节点类型']) {
                        item.showContext['节点类型'] = item.nodeTypeShow;
                    }
                    if (item?.id?.startsWith('esg')) {
                        item.nodeType = 'ESG';
                        item.esgId = item.id;
                    }
                    if (item.nodeType === 'ACL') {
                        item.id = item.showContextObj.aclId || '-';
                    }
                    if (item.nodeType === 'ROUTE') {
                        item.id = item.showContextObj.routeTableId || '-';
                    }
                    if (item.nodeType === 'SG') {
                        item.id = item.showContextObj.securityGroupId || item.id || '-';
                    }
                    if (item.nodeType === 'ESG') {
                        item.id = item.showContextObj.securityGroupId || item.esgId || '-';
                    }
                    if (!item.subNodes) {
                        item.reason = result?.reason || result?.content?.reason;
                        if (item.reason) {
                            if (item.nodeStatus === 'FAILED') {
                                item.showContext['失败原因'] =
                                    pathReasonList.find(j => j.value === item.reason)?.text || item.reason;
                            }
                        }
                        if (i === 0 || i === result.content.path.length - 1) {
                            i === 0 && (item.showContext['源IP'] = item.sourceIp);
                            i !== 0 && (item.showContext['目的IP'] = item.destIp);
                            if (i === 0 && !item.showContext['源IP']) {
                                item.showContext['源IP'] = item.showContextObj.ip;
                            }
                            if (i !== 0 && !item.showContext['目的IP']) {
                                item.showContext['目的IP'] = item.showContextObj.ip;
                            }
                        }
                        item.linkStr = linkStr(item);
                        if (i === result.content.path.length - 1 && result.content.pathStatus !== 'SUCCESS') {
                            item.nodeStatus = result.content.pathStatus;
                        }
                        pathIndex++;
                        path.push(item);
                    } else {
                        if (item.subNodes?.length > 1) {
                            // let subNodes = u.cloneDeep(item.subNodes);
                            // let array = [];
                            // if (item.subNodes.length > 3) {
                            //     if (
                            //         subNodes[0].group === subNodes[2].group &&
                            //         subNodes[0].group === subNodes[1].group &&
                            //         subNodes[0].group !== subNodes[3].group
                            //     ) {
                            //         array.push(subNodes[0], subNodes[1], subNodes[3]);
                            //     } else {
                            //         array = item.subNodes.slice(0, 3);
                            //     }
                            // } else {
                            //     array = item.subNodes.slice(0, 3);
                            // }
                            // item.subNodes = array;
                            // path[i - 1].nextSubNodes = true;
                            // if (path[i - 1]?.subNodes) {
                            //     item.lastNodeStatus = path[i - 1].nodeStatus;
                            //     if (path[i - 1].subNodes.length === 2 && item.subNodes.length === 3) {
                            //         if (
                            //             item.subNodes[2].group === path[i - 1].subNodes[1].group &&
                            //             item.subNodes[1].group === path[i - 1].subNodes[1].group &&
                            //             item.subNodes[0].group === path[i - 1].subNodes[0].group &&
                            //             path[i - 1].subNodes[0].group !== path[i - 1].subNodes[1].group
                            //         ) {
                            //             item.hasThreeLine = true;
                            //         }
                            //         if (
                            //             item.subNodes[2].group === path[i - 1].subNodes[1].group &&
                            //             item.subNodes[1].group === path[i - 1].subNodes[0].group &&
                            //             item.subNodes[0].group === path[i - 1].subNodes[0].group &&
                            //             path[i - 1].subNodes[0].group !== path[i - 1].subNodes[1].group
                            //         ) {
                            //             item.hasTwoLine = true;
                            //         }
                            //     }
                            //     if (path[i - 1].subNodes.length === 3 && item.subNodes.length === 2) {
                            //         if (
                            //             item.subNodes[1].group === path[i - 1].subNodes[2].group &&
                            //             item.subNodes[0].group === path[i - 1].subNodes[1].group &&
                            //             item.subNodes[0].group !== item.subNodes[1].group
                            //         ) {
                            //             item.hasThreeNoOne = true;
                            //         }
                            //         if (
                            //             path[i - 1].subNodes[0].group === 0 &&
                            //             path[i - 1].subNodes[1].group === 1 &&
                            //             path[i - 1].subNodes[2].group === 2 &&
                            //             item.subNodes[0].group === 0 &&
                            //             item.subNodes[1].group === 2
                            //         ) {
                            //             item.hasThreeNoTwo = true;
                            //         }
                            //     }
                            //     if (path[i - 1]?.hasThreeNoOne) {
                            //         item.hasThreeNoOne = true;
                            //     }
                            //     if (path[i - 1]?.hasThreeNoTwo) {
                            //         item.hasThreeNoTwo = true;
                            //     }
                            // } else {
                            //     item.lastNodeStatus = item.nodeStatus;
                            // }
                            item.subNodes.forEach((iSubNodes, index) => {
                                // if (iSubNodes.status === 'down') {
                                //     iSubNodes.subNodeStatus = 'FAILED';
                                // } else {
                                //     iSubNodes.subNodeStatus = item.nodeStatus;
                                // }
                                // if (path[i - 1]?.subNodes) {
                                //     iSubNodes.lastNodeStatus = path[i - 1].subNodes[index]?.subNodeStatus || path[i - 1].subNodes[index - 1]?.subNodeStatus;
                                // } else {
                                //     iSubNodes.lastNodeStatus = item.nodeStatus;
                                // }
                                iSubNodes.region = item.region;
                                iSubNodes.nodeType = item.nodeType;
                                iSubNodes.reason = result?.reason || result?.content?.reason;
                                if (iSubNodes.context) {
                                    iSubNodes.showContextObj = JSON.parse(iSubNodes.context);
                                    iSubNodes.showContext = showContextPath(iSubNodes);
                                } else {
                                    iSubNodes.showContextObj = {};
                                    iSubNodes.showContext = showContextPath(iSubNodes);
                                }
                                if (item.id && !iSubNodes.id) {
                                    if (iSubNodes.nodeType === 'ROUTE') {
                                        iSubNodes.id = iSubNodes.showContextObj?.routeTableId;
                                    } else {
                                        iSubNodes.id = item.id;
                                    }
                                }
                                iSubNodes.linkStr = linkStr(iSubNodes);
                                if (i === 0) {
                                }
                            });
                            pathIndex++;
                            path.push(item);
                        } else {
                            item.id = item.subNodes[0].id;
                            item.group = item.subNodes[0].group;
                            item.status = item.subNodes[0].status;
                            if (item.subNodes[0]?.context) {
                                item.showContextObj = JSON.parse(item.subNodes[0].context);
                                item.showContext = showContextPath(item);
                            } else {
                                item.showContextObj = {};
                                item.showContext = showContextPath(item);
                            }
                            if (i === 0 || i === result.content.path.length - 1) {
                                i === 0 && (item.showContext['源IP'] = item.sourceIp);
                                i !== 0 && (item.showContext['目的IP'] = item.destIp);
                                if (i === 0 && !item.showContext['源IP']) {
                                    item.showContext['源IP'] = item.showContextObj.ip;
                                }
                                if (i !== 0 && !item.showContext['目的IP']) {
                                    item.showContext['目的IP'] = item.showContextObj.ip;
                                }
                            }
                            if (!item.showContext['实例ID'] && i !== 0 && i !== result.content.path.length - 1) {
                                item.showContext['实例ID'] = item.id;
                            }
                            item.linkStr = linkStr(item);
                            pathIndex++;
                            delete item.subNodes;
                            path.push(item);
                        }
                    }
                });
                // 确保最小并且一样
                // let indexList = [];
                // let newArray = [];
                // if (originResult.content) {
                //     originResult.content.path.forEach((item, index) => {
                //         if (item.subNodes) {
                //             let obj = u.cloneDeep(newArray[index - 1]);
                //             if (obj.subNodes) {
                //                 let newType = item.subNodes.map(item => item.group);
                //                 obj.subNodes.forEach(i => {
                //                     if (!newType.includes(i.group)) {
                //                         indexList.push({
                //                             group: i.group,
                //                             index: index - 1
                //                         });
                //                     }
                //                 });
                //             }
                //         }
                //         newArray.push(item);
                //     });
                // }
                // let hasIdc = path.map(item => item.nodeType).includes('IDC');
                // path.forEach((item, index) => {
                //     if (item.subNodes) {
                //         if (!hasIdc) {
                //             item.subNodes = item.subNodes.slice(0, pathMax);
                //         } else {
                //             item.subNodes = item.subNodes.slice(0, 3);
                //         }
                //     }
                //     if (indexList.length && item.subNodes) {
                //         let i = indexList.findIndex(item => item.index === index);
                //         let subNodes = u.cloneDeep(item.subNodes);
                //         if (i > -1) {
                //             let group = subNodes.findIndex(item => item.group === indexList[i].group);
                //             subNodes[group].nextLineHas = true;
                //             item.subNodes = subNodes;
                //         }
                //     }
                // });
                result.content.reversePath.forEach((item, i) => {
                    if (item.context) {
                        item.showContextObj = JSON.parse(item.context);
                        item.showContext = showContextPath(item);
                    } else {
                        item.showContextObj = {};
                        item.showContext = showContextPath(item);
                    }
                    if (item.nodeType === 'EIP_BIND_INSTANCE') {
                        item.nodeTypeShow = item.showContextObj?.bindServiceType
                            ? window.$context.SERVICE_TYPE[item.showContextObj?.bindServiceType]
                                ? window.$context.SERVICE_TYPE[item.showContextObj?.bindServiceType]?.externalName
                                : item.showContextObj?.bindServiceType
                            : '';
                    }
                    if (!item.showContext['节点类型']) {
                        item.showContext['节点类型'] = item.nodeTypeShow;
                    }
                    if (item?.id?.startsWith('esg')) {
                        item.nodeType = 'ESG';
                        item.esgId = item.id;
                    }
                    if (item.nodeType === 'ACL') {
                        item.id = item.showContextObj.aclId || '-';
                    }
                    if (item.nodeType === 'ROUTE') {
                        item.id = item.showContextObj.routeTableId || '-';
                    }
                    if (item.nodeType === 'SG') {
                        item.id = item.showContextObj.securityGroupId || item.id || '-';
                    }
                    if (item.nodeType === 'ESG') {
                        item.id = item.showContextObj.securityGroupId || item.esgId || '-';
                    }
                    if (!item.subNodes) {
                        item.reason = result?.reason || result?.content?.reason;
                        if (item.reason) {
                            if (item.nodeStatus === 'FAILED') {
                                item.showContext['失败原因'] =
                                    pathReasonList.find(j => j.value === item.reason)?.text || item.reason;
                            }
                        }
                        if (i === 0 || i === result.content.path.length - 1) {
                            i === 0 && (item.showContext['源IP'] = item.destIp);
                            i !== 0 && (item.showContext['目的IP'] = item.sourceIp);
                            if (item.nodeType === 'PUBLIC_IP') {
                                i === 0 && (item.showContext['源IP'] = item.destIp);
                                i !== 0 && (item.showContext['目的IP'] = item.sourceIp);
                                if (i === 0 && !item.showContext['源IP']) {
                                    item.showContext['源IP'] = item.showContextObj.ip;
                                }
                                if (i !== 0 && !item.showContext['目的IP']) {
                                    item.showContext['目的IP'] = item.showContextObj.ip;
                                }
                            }
                        }
                        item.linkStr = linkStr(item);
                        if (i === result.content.reversePath - 1 && result.content.reversePathStatus !== 'SUCCESS') {
                            item.nodeStatus = result.content.reversePathStatus;
                        }
                        pathReverseIndex++;
                        reversePath.push(item);
                    } else {
                        if (item.subNodes?.length > 1) {
                            let subNodes = u.cloneDeep(item.subNodes);
                            // let array = [];
                            // if (item.subNodes.length > 3) {
                            //     if (
                            //         subNodes[0].group === subNodes[2].group &&
                            //         subNodes[0].group === subNodes[1].group &&
                            //         subNodes[0].group !== subNodes[3].group
                            //     ) {
                            //         array.push(subNodes[0], subNodes[1], subNodes[3]);
                            //     } else {
                            //         array = item.subNodes.slice(0, 3);
                            //     }
                            // } else {
                            //     array = item.subNodes.slice(0, 3);
                            // }
                            // item.subNodes = array;
                            // reversePath[i - 1].nextSubNodes = true;
                            // if (reversePath[i - 1]?.subNodes) {
                            //     if (reversePath[i - 1].subNodes.length === 3 && item.subNodes.length === 2) {
                            //         if (
                            //             item.subNodes[1].group === reversePath[i - 1].subNodes[2].group &&
                            //             item.subNodes[1].group === reversePath[i - 1].subNodes[1].group &&
                            //             item.subNodes[0].group === reversePath[i - 1].subNodes[0].group &&
                            //             item.subNodes[0].group !== item.subNodes[1].group
                            //         ) {
                            //             reversePath[i - 1].reverseHasThreeLine = true;
                            //         }
                            //         if (
                            //             item.subNodes[0].group === reversePath[i - 1].subNodes[1].group &&
                            //             item.subNodes[0].group === reversePath[i - 1].subNodes[0].group &&
                            //             item.subNodes[1].group === reversePath[i - 1].subNodes[2].group &&
                            //             item.subNodes[0].group !== item.subNodes[1].group
                            //         ) {
                            //             reversePath[i - 1].reverseHasTwoLine = true;
                            //         }
                            //         if (
                            //             reversePath[i - 1].subNodes[0].group === 0 &&
                            //             reversePath[i - 1].subNodes[1].group === 1 &&
                            //             reversePath[i - 1].subNodes[2].group === 2 &&
                            //             item.subNodes[0].group === 0 &&
                            //             item.subNodes[1].group === 2
                            //         ) {
                            //             item.hasThreeNoTwo = true;
                            //         }
                            //         if (
                            //             reversePath[i - 1].subNodes[0].group === 0 &&
                            //             reversePath[i - 1].subNodes[1].group === 1 &&
                            //             reversePath[i - 1].subNodes[2].group === 2 &&
                            //             item.subNodes[0].group === 0 &&
                            //             item.subNodes[1].group === 1
                            //         ) {
                            //             item.hasThreeNoTwoOne = true;
                            //         }
                            //     }
                            //     if (reversePath[i - 1].canReachAllNextNode && reversePath[i - 1].subNodes.length === 2 && item.subNodes.length === 2) {
                            //         item.hasReverseReachNext = true;
                            //     }
                            //     if (reversePath[i - 1].canReachAllNextNode && reversePath[i - 1].subNodes.length === 2 && item.subNodes.length === 3) {
                            //         item.hasReverseReachThreeNext = true;
                            //     }
                            //     item.lastNodeStatus = reversePath[i - 1].nodeStatus;
                            // } else {
                            //     item.lastNodeStatus = item.nodeStatus;
                            // }
                            item.subNodes.forEach((iSubNodes, index) => {
                                // if (iSubNodes.status === 'down') {
                                //     iSubNodes.subNodeStatus = 'FAILED';
                                // } else {
                                //     iSubNodes.subNodeStatus = item.nodeStatus;
                                // }
                                // if (reversePath[i - 1]?.subNodes) {
                                //     iSubNodes.lastNodeStatus =
                                //         reversePath[i - 1].subNodes[index]?.subNodeStatus || reversePath[i - 1].subNodes[index - 1]?.subNodeStatus;
                                // } else {
                                //     iSubNodes.lastNodeStatus = item.nodeStatus;
                                // }
                                iSubNodes.region = item.region;
                                iSubNodes.nodeType = item.nodeType;

                                iSubNodes.reason = result?.reason || result?.content?.reason;
                                if (iSubNodes.context) {
                                    iSubNodes.showContextObj = JSON.parse(iSubNodes.context);
                                    iSubNodes.showContext = showContextPath(iSubNodes);
                                } else {
                                    iSubNodes.showContextObj = {};
                                    iSubNodes.showContext = showContextPath(iSubNodes);
                                }
                                if (item.id && !iSubNodes.id) {
                                    if (iSubNodes.nodeType === 'ROUTE') {
                                        iSubNodes.id = iSubNodes.showContextObj?.routeTableId;
                                    } else {
                                        iSubNodes.id = item.id;
                                    }
                                }
                                iSubNodes.linkStr = linkStr(iSubNodes);
                                if (i === 0) {
                                }
                            });
                            pathReverseIndex++;
                            reversePath.push(item);
                        } else {
                            item.id = item.subNodes[0].id;
                            item.group = item.subNodes[0].group;
                            item.status = item.subNodes[0].status;
                            if (item.subNodes[0]?.context) {
                                item.showContextObj = JSON.parse(item.subNodes[0].context);
                                item.showContext = showContextPath(item);
                            } else {
                                item.showContextObj = {};
                                item.showContext = showContextPath(item);
                            }
                            if (i === 0 || i === result.content.path.length - 1) {
                                i === 0 && (item.showContext['源IP'] = item.destIp);
                                i !== 0 && (item.showContext['目的IP'] = item.sourceIp);
                                if (item.nodeType === 'PUBLIC_IP') {
                                    i === 0 && (item.showContext['源IP'] = item.destIp);
                                    i !== 0 && (item.showContext['目的IP'] = item.sourceIp);
                                }
                                if (i === 0 && !item.showContext['源IP']) {
                                    item.showContext['源IP'] = item.showContextObj.ip;
                                }
                                if (i !== 0 && !item.showContext['目的IP']) {
                                    item.showContext['目的IP'] = item.showContextObj.ip;
                                }
                            }
                            if (!item.showContext['实例ID'] && i !== 0 && i !== result.content.path.length - 1) {
                                item.showContext['实例ID'] = item.id;
                            }
                            item.linkStr = linkStr(item);
                            pathReverseIndex++;
                            delete item.subNodes;
                            reversePath.push(item);
                        }
                    }
                });
                // 确保最小并且一样
                // let revertIndexList = [];
                // let revertNewArray = [];
                // if (originResult.content) {
                //     originResult.content.reversePath.forEach((item, index) => {
                //         if (item.subNodes) {
                //             let obj = u.cloneDeep(revertNewArray[index - 1]);
                //             if (obj.subNodes) {
                //                 let newType = item.subNodes.map(item => item.group);
                //                 obj.subNodes.forEach(i => {
                //                     if (!newType.includes(i.group)) {
                //                         revertIndexList.push({
                //                             group: i.group,
                //                             index: index - 1
                //                         });
                //                     }
                //                 });
                //             }
                //         }
                //         revertNewArray.push(item);
                //     });
                // }
                // let reverseHasIdc = path.map(item => item.nodeType).includes('IDC');
                // reversePath.forEach((item, index) => {
                //     if (item.subNodes) {
                //         if (!reverseHasIdc) {
                //             item.subNodes = item.subNodes.slice(0, reversePathMax);
                //         } else {
                //             item.subNodes = item.subNodes.slice(0, 3);
                //         }
                //     }
                //     if (revertIndexList.length && item.subNodes) {
                //         let i = revertIndexList.findIndex(item => item.index === index);
                //         let subNodes = u.cloneDeep(item.subNodes);
                //         if (i > -1) {
                //             let group = subNodes.findIndex(item => item.group === revertIndexList[i].group);
                //             subNodes[group].nextLineHas = true;
                //             item.subNodes = subNodes;
                //         }
                //     }
                // });
                // result.content.path = path;
                // if (result.content.pathStatus !== 'SUCCESS') {
                //     result.content.path[result.content.path.length - 2].specStatus = result.content.pathStatus;
                //     if (result.content.path[result.content.path.length - 2]?.subNodes) {
                //         result.content.path[result.content.path.length - 2]?.subNodes.forEach(item => {
                //             item.specStatus = result.content.pathStatus;
                //         });
                //     }
                // }
                // result.content.reversePath = reversePath;
                // if (result.content.reversePathStatus !== 'SUCCESS') {
                //     result.content.reversePath[result.content.reversePath.length - 2].specReverseStatus = result.content.reversePathStatus;
                //     if (result.content.reversePath[result.content.reversePath.length - 2]?.subNodes) {
                //         result.content.reversePath[result.content.reversePath.length - 2]?.subNodes.forEach(item => {
                //             item.specReverseStatus = result.content.reversePathStatus;
                //         });
                //     }
                // }
                this.data.set('pathConfig', result?.content?.path || []);
                this.data.set('checkedDis', result?.content?.pathStatus);
                this.data.set('pathRevertConfig', result?.content?.reversePath || []);
                this.initChart();
                let analysisGraph = {
                    pathStatus: result?.content?.pathStatus,
                    reversePathStatus: result?.content?.reversePathStatus,
                    path: path,
                    reversePath: result?.content?.reversePath
                };
                let yAxisLength = 20;
                let yAxisReverseLength = 20;

                let height = 280;
                let reverseHeight = 280;
                if (maxAxis <= 2) {
                    yAxisLength = 30;
                } else {
                    height += maxAxis > 2 ? (maxAxis - 2) * 100 : 0;
                }
                if (maxReverseAxis <= 2) {
                    yAxisReverseLength = 30;
                } else {
                    reverseHeight += maxReverseAxis > 2 ? (maxReverseAxis - 2) * 200 : 0;
                }
                this.data.set('heightBorder', height);
                this.data.set('heightReverseBorder', reverseHeight);
                if (pathIndex === path.length) {
                    this.checkElementExistence('#satisfaction', 500, 10000)
                        .then(() => {
                            processor.applyComponent(
                                'NetworkAnalysisGraph',
                                {
                                    type: 'userView',
                                    isOnline,
                                    height,
                                    echartData: analysisGraph,
                                    yAxisLength,
                                    title: '请求',
                                    onFinished: () => {},
                                    labelsDisNodes: 8
                                },
                                '#satisfaction'
                            );
                        })
                        .catch(error => {
                            console.error(error.message);
                        });
                    this.checkElementExistence('#satisfactionCollection', 500, 10000)
                        .then(() => {
                            this.loadSatisfaction();
                        })
                        .catch(error => {
                            console.error(error.message);
                        });
                }

                if (result?.content?.pathStatus === 'SUCCESS') {
                    this.checkElementExistence('#satisfactionReverse', 500, 10000)
                        .then(() => {
                            processor.applyComponent(
                                'NetworkAnalysisGraph',
                                {
                                    type: 'userView',
                                    isOnline,
                                    height: reverseHeight,
                                    pathKey: 'reversePath',
                                    yAxisLength: yAxisReverseLength,
                                    echartData: analysisGraph,
                                    title: '响应',
                                    onFinished: () => {},
                                    labelsDisNodes: 8
                                },
                                '#satisfactionReverse'
                            );
                        })
                        .catch(error => {
                            console.error(error.message);
                        });
                }
                this.data.set('loading', false);
            }
        });
        // this.watch('pathRevert', value => {
        //   this.initChart();
        // });
        // this.watch('pathRevertDetail', value => {
        //   this.initChart();
        // });
    }
    initChart() {
        this.data.set('pathPending', true);
        let pathRevert = this.data.get('pathRevert');
        if (this.data.get('content')) {
            pathRevert = this.data.get('pathRevertDetail');
        }
        let pathConfig = this.data.get('pathConfig');
        let pathRevertConfig = this.data.get('pathRevertConfig');
        this.data.set('chartConfig', pathConfig);
        this.data.set('pathRevertConfig', pathRevertConfig);
        this.data.set('pathPending', false);
    }
    refresh() {
        if (this.data.get('content')) {
            this.initChart();
            return;
        }
        let payload = {
            pageNo: 1,
            pageSize: 10000,
            pathId: this.data.get('payload').shortId,
            orderBy: 'createTime',
            order: 'asc'
        };
        this.$http.pathanaliseHistory(payload).then(res => {
            let result = res.result[0];
            if (result.status === 'ANALISING') {
                this.data.set('pathPending', true);
                return;
            } else {
                this.data.set('pathPending', false);
            }
            this.data.set('pathConfig', result?.content?.path || []);
            this.data.set('checkedDis', result?.content?.pathStatus);
            this.data.set('pathRevertConfig', result?.content?.reversePath || []);
            this.initChart();
        });
    }
    revertIcon(index, type) {
        let chartConfig = u.cloneDeep(this.data.get('chartConfig'));
        chartConfig.forEach((item, i) => {
            if (index === i) {
                if (type === 'up') {
                    item.showIcon = true;
                } else {
                    item.showIcon = false;
                }
            }
        });
        this.data.set('chartConfig', chartConfig);
    }
    revertPathIcon(index, type) {
        let chartConfig = u.cloneDeep(this.data.get('pathRevertConfig'));
        chartConfig.forEach((item, i) => {
            if (index === i) {
                if (type === 'up') {
                    item.showIcon = true;
                } else {
                    item.showIcon = false;
                }
            }
        });
        this.data.set('pathRevertConfig', chartConfig);
    }
    checkedChange({value}) {
        this.data.set('pathRevertDetail', value);
        this.initChart();
    }
    closeDrawer() {
        this.data.set('showSatisfaction', false);
        this.data.set('showSatisfactionSuggest', false);
        this.data.set('showSatisfactionReverse', false);
        this.data.set('checkedDis', 'failed');
        this.data.set('closeDrawerClass', 'close-drawer-class');
    }
    clickHref(item) {
        const {region} = item;
        if (region) {
            window.$context.setRegion(region);
        }
        let url = linkHref[item.nodeType] + linkStr(item);
        if (item?.nodeType === 'IDC' || item?.nodeType === 'NAT') {
            return;
        }
        if (item?.nodeType === 'ACL' && value?.showContext?.aclType === 'systemAcl') {
            return;
        }
        this.data.set('closeDrawerClass', 'close-drawer-class');
        redirect(url);
    }
    // 定义一个定时器函数，用于检查元素是否存在
    checkElementExistence(selector, interval = 500, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            const timer = setInterval(() => {
                const elapsedTime = Date.now() - startTime;

                if (document.querySelector(selector)) {
                    clearInterval(timer);
                    resolve(true);
                } else if (elapsedTime >= timeout) {
                    clearInterval(timer);
                    reject(new Error('Element not found within the specified timeout.'));
                }
            }, interval);
        });
    }
    initContent() {
        let result = this.data.get('content');
        let originResult = u.cloneDeep(result);
        if (!result.content) {
            this.data.set('contentShow', true);
            this.data.set('loading', false);
            return;
        }
        let path = [];
        let pathMax = 4;
        let reversePath = [];
        let reversePathMax = 4;
        let pathIndex = 0;
        let pathReverseIndex = 0;
        !result.content.path && (result.content.path = []);
        !result.content.reversePath && (result.content.reversePath = []);
        const maxAxis = Math.max(
            ...result.content.path
                .filter(item => item.subNodes && item.subNodes.length)
                .map(item => item.subNodes.length)
        );
        const maxReverseAxis = Math.max(
            ...result.content.reversePath
                .filter(item => item.subNodes && item.subNodes.length)
                .map(item => item.subNodes.length)
        );
        if (result.content) {
            result.content.path.forEach((item, i) => {
                if (item.context) {
                    item.showContextObj = JSON.parse(item.context);
                    item.showContext = showContextPath(item);
                } else {
                    item.showContextObj = {};
                    item.showContext = showContextPath(item);
                }
                if (item.nodeType === 'EIP_BIND_INSTANCE') {
                    item.nodeTypeShow = item.showContextObj?.bindServiceType
                        ? window.$context.SERVICE_TYPE[item.showContextObj?.bindServiceType]
                            ? window.$context.SERVICE_TYPE[item.showContextObj?.bindServiceType]?.externalName
                            : item.showContextObj?.bindServiceType
                        : '';
                }
                if (!item.showContext['节点类型']) {
                    item.showContext['节点类型'] = item.nodeTypeShow;
                }
                if (item?.id?.startsWith('esg')) {
                    item.nodeType = 'ESG';
                    item.esgId = item.id;
                }
                if (item.nodeType === 'ACL') {
                    item.id = item.showContextObj.aclId || '-';
                }
                if (item.nodeType === 'ROUTE') {
                    item.id = item.showContextObj.routeTableId || '-';
                }
                if (item.nodeType === 'SG') {
                    item.id = item.showContextObj.securityGroupId || item.id || '-';
                }
                if (item.nodeType === 'ESG') {
                    item.id = item.showContextObj.securityGroupId || item.esgId || '-';
                }
                if (!item.subNodes) {
                    item.reason = result?.reason || result?.content?.reason;
                    if (item.reason) {
                        if (item.nodeStatus === 'FAILED') {
                            item.showContext['失败原因'] =
                                pathReasonList.find(j => j.value === item.reason)?.text || item.reason;
                        }
                    }
                    if (i === 0 || i === result.content.path.length - 1) {
                        i === 0 && (item.showContext['源IP'] = item.sourceIp);
                        i !== 0 && (item.showContext['目的IP'] = item.destIp);
                        if (i === 0 && !item.showContext['源IP']) {
                            item.showContext['源IP'] = item.showContextObj.ip;
                        }
                        if (i !== 0 && !item.showContext['目的IP']) {
                            item.showContext['目的IP'] = item.showContextObj.ip;
                        }
                    }
                    item.linkStr = linkStr(item);
                    if (i === result.content.path.length - 1 && result.content.pathStatus !== 'SUCCESS') {
                        item.nodeStatus = result.content.pathStatus;
                    }
                    pathIndex++;
                    path.push(item);
                } else {
                    if (item.subNodes?.length > 1) {
                        // let subNodes = u.cloneDeep(item.subNodes);
                        // let array = [];
                        // if (item.subNodes.length > 3) {
                        //     if (
                        //         subNodes[0].group === subNodes[2].group &&
                        //         subNodes[0].group === subNodes[1].group &&
                        //         subNodes[0].group !== subNodes[3].group
                        //     ) {
                        //         array.push(subNodes[0], subNodes[1], subNodes[3]);
                        //     } else {
                        //         array = item.subNodes.slice(0, 3);
                        //     }
                        // } else {
                        //     array = item.subNodes.slice(0, 3);
                        // }
                        // item.subNodes = array;
                        // path[i - 1].nextSubNodes = true;
                        // if (path[i - 1]?.subNodes) {
                        //     item.lastNodeStatus = path[i - 1].nodeStatus;
                        //     if (path[i - 1].subNodes.length === 2 && item.subNodes.length === 3) {
                        //         if (
                        //             item.subNodes[2].group === path[i - 1].subNodes[1].group &&
                        //             item.subNodes[1].group === path[i - 1].subNodes[1].group &&
                        //             item.subNodes[0].group === path[i - 1].subNodes[0].group &&
                        //             path[i - 1].subNodes[0].group !== path[i - 1].subNodes[1].group
                        //         ) {
                        //             item.hasThreeLine = true;
                        //         }
                        //         if (
                        //             item.subNodes[2].group === path[i - 1].subNodes[1].group &&
                        //             item.subNodes[1].group === path[i - 1].subNodes[0].group &&
                        //             item.subNodes[0].group === path[i - 1].subNodes[0].group &&
                        //             path[i - 1].subNodes[0].group !== path[i - 1].subNodes[1].group
                        //         ) {
                        //             item.hasTwoLine = true;
                        //         }
                        //     }
                        //     if (path[i - 1].subNodes.length === 3 && item.subNodes.length === 2) {
                        //         if (
                        //             item.subNodes[1].group === path[i - 1].subNodes[2].group &&
                        //             item.subNodes[0].group === path[i - 1].subNodes[1].group &&
                        //             item.subNodes[0].group !== item.subNodes[1].group
                        //         ) {
                        //             item.hasThreeNoOne = true;
                        //         }
                        //         if (
                        //             path[i - 1].subNodes[0].group === 0 &&
                        //             path[i - 1].subNodes[1].group === 1 &&
                        //             path[i - 1].subNodes[2].group === 2 &&
                        //             item.subNodes[0].group === 0 &&
                        //             item.subNodes[1].group === 2
                        //         ) {
                        //             item.hasThreeNoTwo = true;
                        //         }
                        //     }
                        //     if (path[i - 1]?.hasThreeNoOne) {
                        //         item.hasThreeNoOne = true;
                        //     }
                        //     if (path[i - 1]?.hasThreeNoTwo) {
                        //         item.hasThreeNoTwo = true;
                        //     }
                        // } else {
                        //     item.lastNodeStatus = item.nodeStatus;
                        // }
                        item.subNodes.forEach((iSubNodes, index) => {
                            // if (iSubNodes.status === 'down') {
                            //     iSubNodes.subNodeStatus = 'FAILED';
                            // } else {
                            //     iSubNodes.subNodeStatus = item.nodeStatus;
                            // }
                            // if (path[i - 1]?.subNodes) {
                            //     iSubNodes.lastNodeStatus = path[i - 1].subNodes[index]?.subNodeStatus || path[i - 1].subNodes[index - 1]?.subNodeStatus;
                            // } else {
                            //     iSubNodes.lastNodeStatus = item.nodeStatus;
                            // }
                            iSubNodes.region = item.region;
                            iSubNodes.nodeType = item.nodeType;
                            iSubNodes.reason = result?.reason || result?.content?.reason;
                            if (iSubNodes.context) {
                                iSubNodes.showContextObj = JSON.parse(iSubNodes.context);
                                iSubNodes.showContext = showContextPath(iSubNodes);
                            } else {
                                iSubNodes.showContextObj = {};
                                iSubNodes.showContext = showContextPath(iSubNodes);
                            }
                            if (item.id && !iSubNodes.id) {
                                if (iSubNodes.nodeType === 'ROUTE') {
                                    iSubNodes.id = iSubNodes.showContextObj?.routeTableId;
                                } else {
                                    iSubNodes.id = item.id;
                                }
                            }
                            iSubNodes.linkStr = linkStr(iSubNodes);
                            if (i === 0) {
                            }
                        });
                        pathIndex++;
                        path.push(item);
                    } else {
                        item.id = item.subNodes[0].id;
                        item.group = item.subNodes[0].group;
                        item.status = item.subNodes[0].status;
                        if (item.subNodes[0]?.context) {
                            item.showContextObj = JSON.parse(item.subNodes[0].context);
                            item.showContext = showContextPath(item);
                        } else {
                            item.showContextObj = {};
                            item.showContext = showContextPath(item);
                        }
                        if (i === 0 || i === result.content.path.length - 1) {
                            i === 0 && (item.showContext['源IP'] = item.sourceIp);
                            i !== 0 && (item.showContext['目的IP'] = item.destIp);
                            if (i === 0 && !item.showContext['源IP']) {
                                item.showContext['源IP'] = item.showContextObj.ip;
                            }
                            if (i !== 0 && !item.showContext['目的IP']) {
                                item.showContext['目的IP'] = item.showContextObj.ip;
                            }
                        }
                        if (!item.showContext['实例ID'] && i !== 0 && i !== result.content.path.length - 1) {
                            item.showContext['实例ID'] = item.id;
                        }
                        item.linkStr = linkStr(item);
                        pathIndex++;
                        delete item.subNodes;
                        path.push(item);
                    }
                }
            });
            // 确保最小并且一样
            // let indexList = [];
            // let newArray = [];
            // if (originResult.content) {
            //     originResult.content.path.forEach((item, index) => {
            //         if (item.subNodes) {
            //             let obj = u.cloneDeep(newArray[index - 1]);
            //             if (obj.subNodes) {
            //                 let newType = item.subNodes.map(item => item.group);
            //                 obj.subNodes.forEach(i => {
            //                     if (!newType.includes(i.group)) {
            //                         indexList.push({
            //                             group: i.group,
            //                             index: index - 1
            //                         });
            //                     }
            //                 });
            //             }
            //         }
            //         newArray.push(item);
            //     });
            // }
            // let hasIdc = path.map(item => item.nodeType).includes('IDC');
            // path.forEach((item, index) => {
            //     if (item.subNodes) {
            //         if (!hasIdc) {
            //             item.subNodes = item.subNodes.slice(0, pathMax);
            //         } else {
            //             item.subNodes = item.subNodes.slice(0, 3);
            //         }
            //     }
            //     if (indexList.length && item.subNodes) {
            //         let i = indexList.findIndex(item => item.index === index);
            //         let subNodes = u.cloneDeep(item.subNodes);
            //         if (i > -1) {
            //             let group = subNodes.findIndex(item => item.group === indexList[i].group);
            //             subNodes[group].nextLineHas = true;
            //             item.subNodes = subNodes;
            //         }
            //     }
            // });
            result.content.reversePath.forEach((item, i) => {
                if (item.context) {
                    item.showContextObj = JSON.parse(item.context);
                    item.showContext = showContextPath(item);
                } else {
                    item.showContextObj = {};
                    item.showContext = showContextPath(item);
                }
                if (item.nodeType === 'EIP_BIND_INSTANCE') {
                    item.nodeTypeShow = item.showContextObj?.bindServiceType
                        ? window.$context.SERVICE_TYPE[item.showContextObj?.bindServiceType]
                            ? window.$context.SERVICE_TYPE[item.showContextObj?.bindServiceType]?.externalName
                            : item.showContextObj?.bindServiceType
                        : '';
                }
                if (!item.showContext['节点类型']) {
                    item.showContext['节点类型'] = item.nodeTypeShow;
                }
                if (item?.id?.startsWith('esg')) {
                    item.nodeType = 'ESG';
                    item.esgId = item.id;
                }
                if (item.nodeType === 'ACL') {
                    item.id = item.showContextObj.aclId || '-';
                }
                if (item.nodeType === 'ROUTE') {
                    item.id = item.showContextObj.routeTableId || '-';
                }
                if (item.nodeType === 'SG') {
                    item.id = item.showContextObj.securityGroupId || item.id || '-';
                }
                if (item.nodeType === 'ESG') {
                    item.id = item.showContextObj.securityGroupId || item.esgId || '-';
                }
                if (!item.subNodes) {
                    item.reason = result?.reason || result?.content?.reason;
                    if (item.reason) {
                        if (item.nodeStatus === 'FAILED') {
                            item.showContext['失败原因'] =
                                pathReasonList.find(j => j.value === item.reason)?.text || item.reason;
                        }
                    }
                    if (i === 0 || i === result.content.path.length - 1) {
                        i === 0 && (item.showContext['源IP'] = item.destIp);
                        i !== 0 && (item.showContext['目的IP'] = item.sourceIp);
                        if (item.nodeType === 'PUBLIC_IP') {
                            i === 0 && (item.showContext['源IP'] = item.destIp);
                            i !== 0 && (item.showContext['目的IP'] = item.sourceIp);
                        }
                        if (i === 0 && !item.showContext['源IP']) {
                            item.showContext['源IP'] = item.showContextObj.ip;
                        }
                        if (i !== 0 && !item.showContext['目的IP']) {
                            item.showContext['目的IP'] = item.showContextObj.ip;
                        }
                    }
                    item.linkStr = linkStr(item);
                    if (i === result.content.reversePath - 1 && result.content.reversePathStatus !== 'SUCCESS') {
                        item.nodeStatus = result.content.reversePathStatus;
                    }
                    pathReverseIndex++;
                    reversePath.push(item);
                } else {
                    if (item.subNodes?.length > 1) {
                        let subNodes = u.cloneDeep(item.subNodes);
                        // let array = [];
                        // if (item.subNodes.length > 3) {
                        //     if (
                        //         subNodes[0].group === subNodes[2].group &&
                        //         subNodes[0].group === subNodes[1].group &&
                        //         subNodes[0].group !== subNodes[3].group
                        //     ) {
                        //         array.push(subNodes[0], subNodes[1], subNodes[3]);
                        //     } else {
                        //         array = item.subNodes.slice(0, 3);
                        //     }
                        // } else {
                        //     array = item.subNodes.slice(0, 3);
                        // }
                        // item.subNodes = array;
                        // reversePath[i - 1].nextSubNodes = true;
                        // if (reversePath[i - 1]?.subNodes) {
                        //     if (reversePath[i - 1].subNodes.length === 3 && item.subNodes.length === 2) {
                        //         if (
                        //             item.subNodes[1].group === reversePath[i - 1].subNodes[2].group &&
                        //             item.subNodes[1].group === reversePath[i - 1].subNodes[1].group &&
                        //             item.subNodes[0].group === reversePath[i - 1].subNodes[0].group &&
                        //             item.subNodes[0].group !== item.subNodes[1].group
                        //         ) {
                        //             reversePath[i - 1].reverseHasThreeLine = true;
                        //         }
                        //         if (
                        //             item.subNodes[0].group === reversePath[i - 1].subNodes[1].group &&
                        //             item.subNodes[0].group === reversePath[i - 1].subNodes[0].group &&
                        //             item.subNodes[1].group === reversePath[i - 1].subNodes[2].group &&
                        //             item.subNodes[0].group !== item.subNodes[1].group
                        //         ) {
                        //             reversePath[i - 1].reverseHasTwoLine = true;
                        //         }
                        //         if (
                        //             reversePath[i - 1].subNodes[0].group === 0 &&
                        //             reversePath[i - 1].subNodes[1].group === 1 &&
                        //             reversePath[i - 1].subNodes[2].group === 2 &&
                        //             item.subNodes[0].group === 0 &&
                        //             item.subNodes[1].group === 2
                        //         ) {
                        //             item.hasThreeNoTwo = true;
                        //         }
                        //         if (
                        //             reversePath[i - 1].subNodes[0].group === 0 &&
                        //             reversePath[i - 1].subNodes[1].group === 1 &&
                        //             reversePath[i - 1].subNodes[2].group === 2 &&
                        //             item.subNodes[0].group === 0 &&
                        //             item.subNodes[1].group === 1
                        //         ) {
                        //             item.hasThreeNoTwoOne = true;
                        //         }
                        //     }
                        //     if (reversePath[i - 1].canReachAllNextNode && reversePath[i - 1].subNodes.length === 2 && item.subNodes.length === 2) {
                        //         item.hasReverseReachNext = true;
                        //     }
                        //     if (reversePath[i - 1].canReachAllNextNode && reversePath[i - 1].subNodes.length === 2 && item.subNodes.length === 3) {
                        //         item.hasReverseReachThreeNext = true;
                        //     }
                        //     item.lastNodeStatus = reversePath[i - 1].nodeStatus;
                        // } else {
                        //     item.lastNodeStatus = item.nodeStatus;
                        // }
                        item.subNodes.forEach((iSubNodes, index) => {
                            // if (iSubNodes.status === 'down') {
                            //     iSubNodes.subNodeStatus = 'FAILED';
                            // } else {
                            //     iSubNodes.subNodeStatus = item.nodeStatus;
                            // }
                            // if (reversePath[i - 1]?.subNodes) {
                            //     iSubNodes.lastNodeStatus =
                            //         reversePath[i - 1].subNodes[index]?.subNodeStatus || reversePath[i - 1].subNodes[index - 1]?.subNodeStatus;
                            // } else {
                            //     iSubNodes.lastNodeStatus = item.nodeStatus;
                            // }
                            iSubNodes.region = item.region;
                            iSubNodes.nodeType = item.nodeType;

                            iSubNodes.reason = result?.reason || result?.content?.reason;
                            if (iSubNodes.context) {
                                iSubNodes.showContextObj = JSON.parse(iSubNodes.context);
                                iSubNodes.showContext = showContextPath(iSubNodes);
                            } else {
                                iSubNodes.showContextObj = {};
                                iSubNodes.showContext = showContextPath(iSubNodes);
                            }
                            if (item.id && !iSubNodes.id) {
                                if (iSubNodes.nodeType === 'ROUTE') {
                                    iSubNodes.id = iSubNodes.showContextObj?.routeTableId;
                                } else {
                                    iSubNodes.id = item.id;
                                }
                            }
                            iSubNodes.linkStr = linkStr(iSubNodes);
                            if (i === 0) {
                            }
                        });
                        pathReverseIndex++;
                        reversePath.push(item);
                    } else {
                        item.id = item.subNodes[0].id;
                        item.group = item.subNodes[0].group;
                        item.status = item.subNodes[0].status;
                        if (item.subNodes[0]?.context) {
                            item.showContextObj = JSON.parse(item.subNodes[0].context);
                            item.showContext = showContextPath(item);
                        } else {
                            item.showContextObj = {};
                            item.showContext = showContextPath(item);
                        }
                        if (i === 0 || i === result.content.path.length - 1) {
                            i === 0 && (item.showContext['源IP'] = item.destIp);
                            i !== 0 && (item.showContext['目的IP'] = item.sourceIp);
                            if (item.nodeType === 'PUBLIC_IP') {
                                i === 0 && (item.showContext['源IP'] = item.destIp);
                                i !== 0 && (item.showContext['目的IP'] = item.sourceIp);
                            }
                            if (i === 0 && !item.showContext['源IP']) {
                                item.showContext['源IP'] = item.showContextObj.ip;
                            }
                            if (i !== 0 && !item.showContext['目的IP']) {
                                item.showContext['目的IP'] = item.showContextObj.ip;
                            }
                        }
                        if (!item.showContext['实例ID'] && i !== 0 && i !== result.content.path.length - 1) {
                            item.showContext['实例ID'] = item.id;
                        }
                        item.linkStr = linkStr(item);
                        pathReverseIndex++;
                        delete item.subNodes;
                        reversePath.push(item);
                    }
                }
            });
            // 确保最小并且一样
            // let revertIndexList = [];
            // let revertNewArray = [];
            // if (originResult.content) {
            //     originResult.content.reversePath.forEach((item, index) => {
            //         if (item.subNodes) {
            //             let obj = u.cloneDeep(revertNewArray[index - 1]);
            //             if (obj.subNodes) {
            //                 let newType = item.subNodes.map(item => item.group);
            //                 obj.subNodes.forEach(i => {
            //                     if (!newType.includes(i.group)) {
            //                         revertIndexList.push({
            //                             group: i.group,
            //                             index: index - 1
            //                         });
            //                     }
            //                 });
            //             }
            //         }
            //         revertNewArray.push(item);
            //     });
            // }
            // let reverseHasIdc = path.map(item => item.nodeType).includes('IDC');
            // reversePath.forEach((item, index) => {
            //     if (item.subNodes) {
            //         if (!reverseHasIdc) {
            //             item.subNodes = item.subNodes.slice(0, reversePathMax);
            //         } else {
            //             item.subNodes = item.subNodes.slice(0, 3);
            //         }
            //     }
            //     if (revertIndexList.length && item.subNodes) {
            //         let i = revertIndexList.findIndex(item => item.index === index);
            //         let subNodes = u.cloneDeep(item.subNodes);
            //         if (i > -1) {
            //             let group = subNodes.findIndex(item => item.group === revertIndexList[i].group);
            //             subNodes[group].nextLineHas = true;
            //             item.subNodes = subNodes;
            //         }
            //     }
            // });
            // result.content.path = path;
            // if (result.content.pathStatus !== 'SUCCESS') {
            //     result.content.path[result.content.path.length - 2].specStatus = result.content.pathStatus;
            //     if (result.content.path[result.content.path.length - 2]?.subNodes) {
            //         result.content.path[result.content.path.length - 2]?.subNodes.forEach(item => {
            //             item.specStatus = result.content.pathStatus;
            //         });
            //     }
            // }
            // result.content.reversePath = reversePath;
            // if (result.content.reversePathStatus !== 'SUCCESS') {
            //     result.content.reversePath[result.content.reversePath.length - 2].specReverseStatus = result.content.reversePathStatus;
            //     if (result.content.reversePath[result.content.reversePath.length - 2]?.subNodes) {
            //         result.content.reversePath[result.content.reversePath.length - 2]?.subNodes.forEach(item => {
            //             item.specReverseStatus = result.content.reversePathStatus;
            //         });
            //     }
            // }
            this.data.set('pathConfig', result?.content?.path || []);
            this.data.set('checkedDis', result?.content?.pathStatus);
            this.data.set('pathRevertConfig', result?.content?.reversePath || []);
            this.initChart();
            let analysisGraph = {
                pathStatus: result?.content?.pathStatus,
                reversePathStatus: result?.content?.reversePathStatus,
                path: path,
                reversePath: result?.content?.reversePath
            };
            let yAxisLength = 20;
            let yAxisReverseLength = 20;

            let height = 280;
            let reverseHeight = 280;
            if (maxAxis <= 2) {
                yAxisLength = 30;
            } else {
                height += maxAxis > 2 ? (maxAxis - 2) * 100 : 0;
            }
            if (maxReverseAxis <= 2) {
                yAxisReverseLength = 30;
            } else {
                reverseHeight += maxReverseAxis > 2 ? (maxReverseAxis - 2) * 200 : 0;
            }
            this.data.set('heightBorder', height);
            this.data.set('heightReverseBorder', reverseHeight);
            if (pathIndex === path.length) {
                this.checkElementExistence('#satisfaction', 500, 10000)
                    .then(() => {
                        processor.applyComponent(
                            'NetworkAnalysisGraph',
                            {
                                type: 'userView',
                                height: height,
                                isOnline,
                                yAxisLength,
                                echartData: analysisGraph,
                                title: '请求',
                                onFinished: () => {},
                                labelsDisNodes: 8
                            },
                            '#satisfaction'
                        );
                    })
                    .catch(error => {
                        console.error(error.message);
                    });
                this.checkElementExistence('#satisfactionCollection', 500, 10000)
                    .then(() => {
                        this.loadSatisfaction();
                    })
                    .catch(error => {
                        console.error(error.message);
                    });
            }
            if (result?.content?.pathStatus === 'SUCCESS') {
                this.checkElementExistence('#satisfactionReverse', 500, 10000)
                    .then(() => {
                        processor.applyComponent(
                            'NetworkAnalysisGraph',
                            {
                                type: 'userView',
                                isOnline,
                                height: reverseHeight,
                                pathKey: 'reversePath',
                                echartData: analysisGraph,
                                yAxisLength: yAxisReverseLength,
                                title: '响应',
                                onFinished: () => {},
                                labelsDisNodes: 8
                            },
                            '#satisfactionReverse'
                        );
                    })
                    .catch(error => {
                        console.error(error.message);
                    });
            }
            this.data.set('loading', false);
        }
    }
    loadSatisfaction() {
        processorCollection.applyComponent(
            'Entry',
            {
                mode: 'like',
                suggestSDK: new SuggestSDK({client: new HttpClient(), context: window.$context}),
                scoreTip: '路径分析',
                simpleUnlikeText: '未解决',
                simpleLikeText: '已解决',
                resourceId: this.data.get('resourceId'),
                placeholder: '请填写未解决原因，最大限制200字',
                dislikeTagData: ['分析有误', '分析麻烦', '操作麻烦', '问题依旧', '其他'],
                onClose: () => {
                    let payload = {
                        pathId: this.data.get('resourceId'),
                        unsolved: this.data.get('unsolved')
                    };
                    if (this.data.get('unsolved') > 0) {
                        payload.unsolvedReason = this.data.get('unsolvedReason');
                    }
                    this.data.get('userOpinion') && (payload.userOpinion = this.data.get('userOpinion'));
                    // this.$http.feedBackReport(payload).then(res => {
                    Notification.success('我们将认真评估每一条建议，为您提供更好的服务！', {title: '感谢您的反馈'});
                    // });
                    this.data.set('showSatisfactionSuggest', false);
                },
                onTextareaChange: value => {
                    this.data.set('userOpinion', value);
                },
                onLikeDislikeChange: value => {
                    this.data.set('unsolved', value > 1 ? 0 : 1);
                },
                onTagSelectChange: (value, allValue) => {
                    let map = {
                        诊断有误: 1,
                        修复麻烦: 2,
                        操作麻烦: 3,
                        问题依旧: 4,
                        其他: 5
                    };
                    let unsolvedReason = [];
                    value.forEach(item => {
                        unsolvedReason.push(map[item]);
                    });
                    this.data.set('unsolvedReason', unsolvedReason);
                }
            },
            '#satisfactionCollection'
        );
    }
}
export default Processor.autowireUnCheckCmpt(BcmDetail);
