import u from 'lodash';
import {Enum} from '@baiducloud/runtime';
import rule from '@/pages/sanPages/utils/rule';
import {pathReasonList} from '@/pages/sanPages/utils/config';
import {checkIsInSubnet} from '@/pages/sanPages/utils/common';
import {RouteType, PathType, IpVersion, ProtocolType} from '@/pages/sanPages/common/enum';
const {IP} = rule;
const AllRegion = window.$context.getEnum('AllRegion');
let kAllPorts = ['0', '1-65535', '0-65535'];
let kAllIp = '0.0.0.0/0';
let kAllIpv6 = '::/0';

let ActionType = new Enum(
    {
        alias: 'ALLOW',
        text: '允许',
        value: 'allow'
    },
    {
        alias: 'DENY',
        text: '拒绝',
        value: 'deny'
    },
    {
        alias: 'OBSERVER',
        text: '观察',
        value: 'observe'
    }
);

export const formValidator = (self, formName) => ({
    name: [
        {
            required: true,
            message: '请填写路径名称'
        },
        {
            pattern: /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{0,64}$/,
            message: '格式不符合要求'
        }
    ],
    vpcId: [
        {
            required: true,
            message: '请选择所在网络'
        }
    ],
    sourceRegion: [
        {
            required: true,
            message: '请选择源地域'
        }
    ],
    desRegion: [
        {
            required: true,
            message: '请选择目的地域'
        }
    ],
    sourceType: [
        {
            required: true,
            message: '请选择源实例类型'
        }
    ],
    sourceVpcId: [
        {
            required: true,
            message: '请选择源实例所在网络'
        }
    ],
    sourceIp: [
        {
            required: true,
            message: '请输入IP地址'
        },
        {
            validator: (rule, value, callback) => {
                let sourceType = formName
                    ? self.data.get(`${formName}.sourceType`)
                    : self.data.get('formData.sourceType');
                let sourceSubnet = self.data.get('sourceSubnet');
                if (sourceType === 'SUBNET') {
                    if (!IP.test(value)) {
                        return callback('IP地址格式不正确');
                    }
                    if (sourceSubnet && !checkIsInSubnet(value + '/32', sourceSubnet)) {
                        return callback(`IP地址不属于所在子网${sourceSubnet.split('/')[0]}`);
                    }
                    return callback();
                }
                if (
                    sourceType === 'DC' ||
                    sourceType === 'VPN_CONN' ||
                    sourceType === 'PUBLICIP' ||
                    sourceType === 'PUBLIC_IP' ||
                    sourceType === 'BCC' ||
                    !sourceType
                ) {
                    if (!IP.test(value)) {
                        return callback('IP地址格式不正确');
                    }
                    return callback();
                }
                return callback();
            }
        }
    ],
    sourcePort: [
        // {required: true, message: '请输入源端口'},
        {
            validator: (rule, value, callback) => {
                if (!/^[1-9]\d*$/.test(value)) {
                    if (!value) {
                        return callback();
                    }
                    return callback('源端口格式不正确');
                }
                if (value < 0 || value > 65535) {
                    return callback('源端口格式不正确');
                }
                callback();
            }
        }
    ],
    sourceId: [
        {
            validator: (rule, value, callback) => {
                let sourceType = formName
                    ? self.data.get(`${formName}.sourceType`)
                    : self.data.get('formData.sourceType');
                let sourceBccExit = self.data.get('sourceBccExit');
                if (sourceType === 'BCC') {
                    if (!value) {
                        return callback('请输入BCC实例ID');
                    }
                    if (value.length !== 10 || !value.startsWith('i-')) {
                        return callback('BCC实例ID格式不正确');
                    }
                    let str = value.slice(2);
                    if (!/[a-zA-Z0-9]{8}$/.test(str)) {
                        return callback('BCC实例ID格式不正确');
                    }
                    if (!sourceBccExit) {
                        return callback('当前BCC实例ID不存在');
                    }
                    return callback();
                } else {
                    if (!value) {
                        return callback('请选择源实例');
                    }
                    return callback();
                }
            }
        }
    ],
    destType: [
        {
            required: true,
            message: '请选择目的实例类型'
        }
    ],
    desVpcId: [
        {
            required: true,
            message: '请选择目的实例所在网络'
        }
    ],
    destIp: [
        {
            required: true,
            message: '请输入IP地址'
        },
        {
            validator: (rule, value, callback) => {
                let destType = formName ? self.data.get(`${formName}.destType`) : self.data.get('formData.destType');
                let desSubnet = self.data.get('desSubnet');
                if (destType === 'SUBNET') {
                    if (!IP.test(value)) {
                        return callback('IP地址格式不正确');
                    }
                    if (desSubnet && !checkIsInSubnet(value + '/32', desSubnet)) {
                        return callback(`IP地址不属于所在子网${desSubnet.split('/')[0]}`);
                    }
                    return callback();
                }
                if (destType === 'IDC' || destType === 'PUBLICIP' || destType === 'PUBLIC_IP' || !destType) {
                    if (!IP.test(value)) {
                        return callback('IP地址格式不正确');
                    }
                    return callback();
                }
                callback();
            }
        }
    ],
    destPort: [
        // {required: true, message: '请输入目的端口'},
        {
            validator: (rule, value, callback) => {
                if (!/^[1-9]\d*$/.test(value)) {
                    if (!value) {
                        return callback();
                    }
                    return callback('目的格式不正确');
                }
                if (value < 0 || value > 65535) {
                    return callback('目的格式不正确');
                }
                callback();
            }
        }
    ],
    destId: [
        {
            validator: (rule, value, callback) => {
                let destType = formName ? self.data.get(`${formName}.destType`) : self.data.get('formData.destType');
                let destBccExit = self.data.get('destBccExit');
                if (destType === 'BCC') {
                    if (!value) {
                        return callback('请输入BCC实例ID');
                    }
                    if (value.length !== 10 || !value.startsWith('i-')) {
                        return callback('BCC实例ID格式不正确');
                    }
                    let str = value.slice(2);
                    if (!/[a-zA-Z0-9]{8}$/.test(str)) {
                        return callback('BCC实例ID格式不正确');
                    }
                    if (!destBccExit) {
                        return callback('当前BCC实例ID不存在');
                    }
                    return callback();
                } else {
                    if (!value) {
                        return callback('请选择目的实例');
                    }
                    return callback();
                }
            }
        }
    ],
    protocol: [
        {
            required: true,
            message: '请选择协议类型'
        }
    ],
    description: [
        {
            min: 0,
            max: 200,
            message: '长度0到200个字符'
        }
    ],
    connId: [
        {
            required: true,
            message: '请选择通道'
        }
    ]
});

const getNodeId = node => {
    if (node) {
        let context = node?.context ? JSON.parse(node?.context) : {};
        return context?.routeTableId || node?.id;
    }
    return '';
};
const getSource = item => {
    if (item?.showContextObj?.routeRuleContext) {
        let sourceName = u.escape(item.showContextObj.routeRuleContext.sourceName);
        let sourceAddress = u.escape(item.showContextObj.routeRuleContext.sourceAddress);
        let source = sourceName ? sourceName + '(' + sourceAddress + ')' : sourceAddress;
        return source;
    }
    return '-';
};
const getDesResource = item => {
    if (item?.showContextObj?.routeRuleContext) {
        return u.escape(item.showContextObj.routeRuleContext.destinationAddress);
    }
    return '-';
};
const getRouteType = item => {
    let pathType = '';
    if (item?.showContextObj?.routeRuleContext) {
        if (item.showContextObj.routeRuleContext.nexthopType === RouteType.GW) {
            if (item.showContextObj.routeRuleContext.pathType === PathType.ECMP) {
                pathType = '专线-负载均衡';
            } else if (item.showContextObj.routeRuleContext.pathType === PathType.NORMAL) {
                pathType = '单线';
            } else if (
                item.showContextObj.routeRuleContext.pathType === PathType.ACTIVE ||
                item.showContextObj.routeRuleContext.pathType === PathType.STANDBY
            ) {
                pathType = '专线-主备';
                if (
                    item.showContextObj.routeRuleContext.multiRouteRuleVos &&
                    item.showContextObj.routeRuleContext.multiRouteRuleVos[0]?.nexthopType === 'vpn'
                ) {
                    pathType = '专线-主，IPsec VPN备';
                }
            }
        }
        if (item.showContextObj.routeRuleContext.nexthopType === RouteType.VPN) {
            if (item.showContextObj.routeRuleContext.pathType === PathType.STANDBY) {
                return '专线网关（专线-主，IPsec VPN备）';
            }
        }
        return (
            RouteType.getTextFromValue(item.showContextObj.routeRuleContext.nexthopType) +
            (pathType ? '（' + pathType + '）' : '')
        );
    }
    return '-';
};
const getNextHop = item => {
    if (item?.showContextObj?.routeRuleContext) {
        let nexthopExtra = '';
        if (item.showContextObj.routeRuleContext.nexthopType !== 'CSN') {
            if (typeof item.showContextObj.routeRuleContext.nexthop === 'string') {
                if (item.showContextObj.routeRuleContext.nexthopType === 'vpc2tgw') {
                    item.showContextObj.routeRuleContext.nexthop = item.showContextObj.routeRuleContext.nexthopId;
                }
                nexthopExtra = u.escape(item.showContextObj.routeRuleContext.nexthop);
                if (nexthopExtra === 'local') {
                    nexthopExtra = '本地';
                }
                if (nexthopExtra === 'default gateway') {
                    nexthopExtra = '默认网关';
                }
            } else {
                nexthopExtra = item.showContextObj.routeRuleContext.nexthop.join('<br>');
            }
            return nexthopExtra;
        }
        return '-';
    }
    return '-';
};
const getEthertype = item => {
    if (item?.showContextObj?.sgRuleContext) {
        let result = item.showContextObj.sgRuleContext;
        return u.escape(result.ethertype);
    }
    return '-';
};
const getProtocol = item => {
    if (item?.showContextObj?.sgRuleContext) {
        let result = item.showContextObj.sgRuleContext;
        if (result.protocol === 'all' || result.protocol === '') {
            return '全部协议';
        }
        return u.escape(result.protocol);
    }
    if (item?.showContextObj?.aclRule) {
        let result = item.showContextObj.aclRule;
        if (result.protocol === 'all') {
            return '全部协议';
        }
        return u.escape(result.protocol.toUpperCase());
    }
    return '-';
};
const getPort = item => {
    if (item?.showContextObj?.sgRuleContext) {
        let result = item.showContextObj.sgRuleContext;
        return u.escape(result.portRange || '1-65535');
    }
    if (item?.showContextObj?.aclRule) {
        let result = item.showContextObj.aclRule;
        if (result.protocol === ProtocolType.ICMP) {
            return 'N/A';
        }
        if (u.contains(kAllPorts, result.sourcePort)) {
            return 'all';
        }
        return u.escape(result.sourcePort);
    }
    return '-';
};
const getDesPort = item => {
    if (item?.showContextObj?.aclRule) {
        let result = item.showContextObj.aclRule;
        if (result.protocol === ProtocolType.ICMP) {
            return 'N/A';
        }
        if (u.contains(kAllPorts, result.destinationPort)) {
            return 'all';
        }
        return u.escape(result.destinationPort);
    }
    return '-';
};
const getSourceSec = item => {
    if (item?.showContextObj?.sgRuleContext) {
        let result = item.showContextObj.sgRuleContext;
        if (item.nodeType === 'ESG') {
            if (result.ipCollectionType) {
                if (result.ipCollectionType === 1) {
                    return 'IP地址组：' + result.ipCollectionId;
                }
                return 'IP地址族：' + result.ipCollectionId;
            }
        }
        var id = result.remoteGroupShortId || result.remoteGroupId;
        if (result.remoteGroupName) {
            return '安全组：' + u.escape(result.remoteGroupName) + (id ? '（' + u.escape(id) + '）' : '');
        }
        return (
            (result.ingress === 'in' || result.direction === 'ingress' ? '源IP：' : '目的IP：') +
            (result.remoteIpPrefix === 'all' || result.remoteIpPrefix === ''
                ? result.ethertype === 'IPv4'
                    ? '0.0.0.0/0'
                    : '::/0'
                : u.escape(result.remoteIpPrefix))
        );
    }
    if (item?.showContextObj?.aclRule) {
        let result = item.showContextObj.aclRule;
        if (result.sourceIpAddress === 'all') {
            return result.ipVersion === IpVersion.IPV6 ? kAllIpv6 : kAllIp;
        }
        let sourceIpAddress = u.escape(result.sourceIpAddress);
        return `${sourceIpAddress}`;
    }
    return '-';
};
const getDesc = item => {
    if (item?.showContextObj?.sgRuleContext) {
        let result = item.showContextObj.sgRuleContext;
        if (result.remark) {
            return result.remark;
        }
        return u.escape(result.name) || '-';
    }
    if (item?.showContextObj?.aclRule) {
        let result = item.showContextObj.aclRule;
        let description = u.escape(result.description);
        if (!result.id && description === 'default') {
            description = '默认';
        }
        return `${description}`;
    }
    return '-';
};
const getAction = item => {
    if (item?.showContextObj?.sgRuleContext) {
        let result = item.showContextObj.sgRuleContext;
        return result.action === 'allow' ? '允许' : '拒绝';
    }
    if (item?.showContextObj?.aclRule) {
        let result = item.showContextObj.aclRule;
        return ActionType.getTextFromValue(result.action);
    }
    return '-';
};
const getPriority = item => {
    if (item?.showContextObj?.sgRuleContext) {
        let result = item.showContextObj.sgRuleContext;
        if (!result.priority) {
            return u.escape(result.priority);
        }
        return result.priority;
    }
    if (item?.showContextObj?.aclRule) {
        let result = item.showContextObj.aclRule;
        return u.escape(result.position);
    }
    return '-';
};
const getDesResSec = item => {
    if (item?.showContextObj?.aclRule) {
        let result = item.showContextObj.aclRule;
        if (result.destinationIpAddress === 'all') {
            return result.ipVersion === IpVersion.IPV6 ? kAllIpv6 : kAllIp;
        }
        let destinationIpAddress = u.escape(result.destinationIpAddress);
        return `${destinationIpAddress}`;
    }
    return '-';
};

const getTextFormRegion = value => {
    return AllRegion.getTextFromValue(value) || '-';
};

const nodeTypeMap = {
    BCC: '云服务器',
    BBC: '物理服务器',
    SUBNET: '子网',
    SG: '安全组',
    ACL: 'ACL',
    ROUTE: '路由表',
    PC: '对等连接',
    CSN: '云智能网',
    DC: '专线通道',
    DG: '专线网关',
    VPN: 'VPN网关',
    NAT: 'NAT网关',
    ESG: '企业安全组',
    IDC: 'IP',
    VPN_CONN: 'VPN通道',
    CFW: '云防火墙',
    PUBLICIP: '公网IP',
    PUBLIC_IP: '公网IP',
    EIP: '弹性公网IP',
    PRIVATE_IP: '内网IP',
    BLB: '负载均衡',
    ENI: '弹性网卡'
};

export const showContextPath = item => {
    let obj = {};

    obj['节点类型'] = nodeTypeMap[item.nodeType];

    if (
        item.nodeType !== 'ACL' &&
        item.nodeType !== 'SG' &&
        item.nodeType !== 'ESG' &&
        item.nodeType !== 'ROUTE' &&
        item.id
    ) {
        obj['实例ID'] = item.id || '-';
    }

    if (item.nodeType === 'ROUTE' || item.nodeType === 'ACL' || item.nodeType === 'SG' || item.nodeType === 'ESG') {
        if (item.nodeType === 'ROUTE') {
            obj['实例ID'] = item.showContextObj.routeTableId || '-';
        }
        if (item.nodeType === 'ACL') {
            obj['实例ID'] = item.showContextObj.aclId || '-';
        }
        if (item.nodeType === 'SG' || item.nodeType === 'ESG') {
            obj['实例ID'] =
                item.nodeType === 'SG'
                    ? item.showContextObj.securityGroupId || item.id || '-'
                    : item.showContextObj.securityGroupId || item.esgId || '-';
        }
    }

    if (item.showContextObj.sgRuleContext) {
        if (item.nodeType === 'SG' || item.nodeType === 'ESG') {
            obj['类型'] = getEthertype(item);
        }
        if (item.nodeType === 'ESG') {
            obj['优先级'] = getPriority(item);
        }
        if (item.nodeType === 'SG' || item.nodeType === 'ESG') {
            obj['协议'] = getProtocol(item);
        }
        if (item.nodeType === 'SG' || item.nodeType === 'ESG') {
            obj['端口'] = getPort(item);
        }
        if (
            item.showContextObj.sgRuleContext.ingress === 'in' ||
            item.showContextObj.sgRuleContext.direction === 'ingress'
        ) {
            obj['类型'] = getSourceSec(item);
        }
        if (
            item.showContextObj.sgRuleContext.ingress !== 'in' &&
            item.showContextObj.sgRuleContext.direction !== 'ingress'
        ) {
            obj['目的'] = getSourceSec(item);
        }
        if (item.nodeType === 'ESG') {
            obj['策略'] = getAction(item);
        }
        if (item.nodeType === 'SG' || item.nodeType === 'ESG') {
            obj['备注'] = getDesc(item);
        }
    }

    if (item.showContextObj.routeRuleContext) {
        if (item.nodeType === 'ROUTE') {
            obj['源网段'] = getSource(item);
        }
        if (item.nodeType === 'ROUTE') {
            obj['目标网段'] = getDesResource(item);
        }
        if (item.nodeType === 'ROUTE') {
            obj['下一跳'] = getNextHop(item);
        }
        if (item.nodeType === 'ROUTE') {
            obj['路由类型'] = getRouteType(item);
        }
    }

    if (item.showContextObj.aclRule) {
        if (item.nodeType === 'ACL') {
            obj['优先级'] = getPriority(item);
        }
        if (item.nodeType === 'ACL') {
            obj['协议'] = getProtocol(item);
        }
        if (item.nodeType === 'ACL') {
            obj['源IP'] = getSourceSec(item);
        }
        if (item.nodeType === 'ACL') {
            obj['源端口'] = getPort(item);
        }
        if (item.nodeType === 'ACL') {
            obj['目的IP'] = getDesResSec(item);
        }
        if (item.nodeType === 'ACL') {
            obj['目的端口'] = getDesPort(item);
        }
        if (item.nodeType === 'ACL') {
            obj['策略'] = getAction(item);
        }
        if (item.nodeType === 'ACL') {
            obj['备注'] = getDesc(item);
        }
    }
    if (item.nodeType === 'CFW') {
        obj['优先级'] = item.showContextObj.priority;
        obj['协议'] =
            item.showContextObj.protocol === 'ALL' ||
            item.showContextObj.protocol === 'all' ||
            !item.showContextObj.protocol
                ? '全部协议'
                : item.showContextObj.protocol.toUpperCase();
        obj['源IP'] =
            item.showContextObj.sourceAddress === 'ALL' ||
            item.showContextObj.sourceAddress === 'all' ||
            item.showContextObj.sourceAddress === '0.0.0.0/0' ||
            item.showContextObj.sourceAddress === '::/0' ||
            !item.showContextObj.sourceAddress
                ? item.showContextObj.ipVersion === 4
                    ? kAllIp
                    : kAllIpv6
                : item.showContextObj.sourceAddress;

        let sourcePort = '';
        if (item.showContextObj.protocol?.toLowerCase() === ProtocolType.ICMP) {
            sourcePort = 'N/A';
        }
        if (u.contains(kAllPorts, item.showContextObj.sourcePort)) {
            sourcePort = '0-65535';
        }
        sourcePort = u.escape(item.showContextObj.sourcePort);

        obj['目的端口'] = sourcePort;

        obj['目的IP'] =
            item.showContextObj.destAddress === 'ALL' ||
            item.showContextObj.destAddress === 'all' ||
            item.showContextObj.destAddress === '0.0.0.0/0' ||
            item.showContextObj.destAddress === '::/0' ||
            !item.showContextObj.destAddress
                ? item.showContextObj.ipVersion === 4
                    ? kAllIp
                    : kAllIpv6
                : item.showContextObj.destAddress;

        let destPort = '';
        if (item.showContextObj.protocol?.toLowerCase() === ProtocolType.ICMP) {
            destPort = 'N/A';
        }
        if (u.contains(kAllPorts, item.showContextObj.destPort)) {
            destPort = '0-65535';
        }
        destPort = u.escape(item.showContextObj.destPort);

        obj['目的端口'] = destPort;

        let applications = '';
        if (
            (item.showContextObj.applications && item.showContextObj.applications[0] === 'any') ||
            !item.showContextObj.applications
        ) {
            applications = '-';
        } else {
            applications = item.showContextObj.applications;
        }
        obj['应用'] = applications;
        obj['策略'] = ActionType.getTextFromValue(item.showContextObj.action);
        let description = u.escape(item.showContextObj.description);
        if (!item.showContextObj.id && description === 'default') {
            description = '默认';
        }
        obj['备注'] = description;
    }

    if (item.nodeType === 'PC') {
        obj['本端VPC ID'] = item.showContextObj.localVpcId;
        obj['对端VPC ID'] = item.showContextObj.peerVpcId;
        obj['本端地域'] = getTextFormRegion(item.showContextObj.localVpcRegion);
        obj['对端地域'] = getTextFormRegion(item.showContextObj.peerVpcRegion);
    }

    if (item.nodeType !== 'PC' && item.nodeType !== 'IDC' && item.nodeType !== 'CFW') {
        obj['区域'] = getTextFormRegion(item.region);
    }
    if (item.nodeType === 'EIP') {
        obj['区域'] = getTextFormRegion(item.showContextObj.region);
    }
    let reason = item?.reason;
    if (reason) {
        obj['失败原因'] = pathReasonList.find(item => item.value === reason)?.text || '分析异常';
    }
    if (item.nodeType === 'PUBLICIP') {
        delete obj['区域'];
    }
    return obj;
};
