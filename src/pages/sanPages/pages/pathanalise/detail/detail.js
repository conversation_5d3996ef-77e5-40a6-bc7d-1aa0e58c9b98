import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {OutlinedEditingSquare, OutlinedLeft, OutlinedRefresh} from '@baidu/sui-icon';

import {ProbeStatus, RouteType, AnalyzeStatus} from '@/pages/sanPages/common/enum';
import Confirm from '@/pages/sanPages/components/confirm';
import {checkRule} from '../../instance/page/helper';
import {Notification} from '@baidu/sui';
import BcmDetail from '../components/drawPathanalise';
import {utcToTime} from '@/pages/sanPages/utils/helper';
import {pathReasonList} from '@/pages/sanPages/utils/config';

import './detail.less';

const AllRegion = window.$context.getEnum('AllRegion');
const {asPage, invokeSUI, invokeSUIBIZ, template, withSidebar, invokeAppComp} = decorators;

const tpl = html`
<div>
    <s-app-detail-page class="{{klass}}">
        <div slot="pageTitle" class="title_class">
            <div class="tltle_left">
                <span class="page-title-nav" on-click="back"><icon-left/>返回</span>
                <span class="vpc-name">{{instance.name}}</span>
                <span class="status normal"></span>
            </div>
            <!--<s-popover trigger="{{deleteVpc.disable ? 'hover' : ''}}" class="float_right">
                <div slot="content">
                    {{deleteVpc.message}}
                </div>
                <s-button on-click="onDelete" disabled= "{{deleteVpc.disable}}">删除</s-button>
            </s-popover>-->
        </div>
        <div class="content">
            <h4>基本信息</h4>
            <div class="cell detail-part-item">
                <div class="cell-title">路径名称：</div>
                <div class="cell-content">{{instance.name || '-'}}</div>
                <s-popover s-ref="popover-name" placement="right" trigger="click"
                    class="edit-popover-class">
                    <div class="edit-wrap" slot="content">
                        <s-input value="{=edit.name.value=}"
                            width="320"
                            placeholder="{{'请输入名称'}}"
                            on-input="onEditInput($event, 'name')"/>
                        <div class="edit-tip">大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65</div>
                        <s-button skin="primary" s-ref="editBtn-name" disabled="{{true}}"
                            on-click="editConfirm(instance, 'name')">确定</s-button>
                        <s-button on-click="editCancel('name')">取消</s-button>
                    </div>
                    <outlined-editing-square
                        class="name-icon" on-click="onInstantEdit(instance, 'name')" />
                </s-popover>
            </div>
            <div class="cell detail-part-item">
                <div class="cell-title">路径ID：</div>
                <div class="cell-content">
                    {{instance.shortId || '-'}}
                </div>
                <s-clip-board class="name-icon" text="{{instance.shortId}}" successMessage="已复制到剪贴板" />
            </div>
            <div class="cell detail-part-item">
                <div class="cell-title">协议：</div>
                <div class="cell-content inline_class">
                    <span class="">{{instance.protocol | getUpCase}}</span>
                </div>
            </div>
            <div class="cell detail-part-item">
                <div class="cell-title">源地域：</div>
                <div class="cell-content inline_class">
                    <span class="">{{instance.sourceRegion | getRegion}}</span>
                </div>
            </div>
            <div class="cell detail-part-item">
                <div class="cell-title">源实例类型：</div>
                <div class="cell-content inline_class">
                    <span class="">{{instance.sourceType | getType}}</span>
                </div>
            </div>
            <div class="cell detail-part-item">
                <div class="cell-title">源实例ID：</div>
                <div class="cell-content">
                    <p>{{instance.sourceId || '-'}}</p>
                </div>
            </div>
            <div class="cell detail-part-item">
                <div class="cell-title">探测源 IP：</div>
                <div class="cell-content">
                    <p>{{instance.sourceIp || '-'}}</p>
                </div>
            </div>
            <div class="cell detail-part-item">
                <div class="cell-title">源端口：</div>
                <div class="cell-content">
                    {{instance.sourcePort || '-'}}
                </div>
            </div>
            <div class="cell detail-part-item">
                <div class="cell-title">目标地域：</div>
                <div class="cell-content inline_class">
                    <span class="">{{instance.destRegion | getRegion}}</span>
                </div>
            </div>
            <div class="cell detail-part-item">
                <div class="cell-title">目的实例类型：</div>
                <div class="cell-content inline_class">
                    <span class="">{{instance.destType | getType}}</span>
                </div>
            </div>
            <div class="cell detail-part-item">
                <div class="cell-title">目的实例ID：</div>
                <div class="cell-content inline_class">
                    <span class="">{{instance.destId || '-'}}</span>
                </div>
            </div>
            <div class="cell detail-part-item">
                <div class="cell-title">探测目的 IP：</div>
                <div class="cell-content">
                    {{instance.destIp || '-'}}
                </div>
            </div>
            <div class="cell detail-part-item">
                <div class="cell-title">目的端口：</div>
                <div class="cell-content">
                    {{instance.destPort || '-'}}
                </div>
            </div>
            <div class="cell detail-part-item">
                <div class="cell-title">描述：</div>
                <div class="cell-content">
                    {{instance.description || '-'}}
                </div>
                <s-popover s-ref="popover-description" placement="right" trigger="click"
                    class="edit-popover-class">
                    <div class="edit-wrap" slot="content">
                        <s-input-text-area
                            placeholder="{{'请输入描述'}}"
                            value="{=edit.description.value=}"
                            width="200"
                            height="60"
                            maxLength="200"
                            on-input="onEditInput($event, 'description')"
                        />
                        <div class="edit-tip">描述不能超过200个字符</div>
                        <s-button skin="primary" s-ref="editBtn-description" disabled="{{true}}"
                            on-click="editConfirm(instance, 'description')">确定</s-button>
                        <s-button on-click="editCancel('description')">取消</s-button>
                    </div>
                    <outlined-editing-square
                        class="name-icon" on-click="onInstantEdit(instance, 'description')" />
                </s-popover>
            </div>
        </div>
        <div class="content content_bottom">
            <div class="content-title">
                <div class="content-title-left">
                    <h4>分析记录</h4>
                    <s-button on-click="startPathAnalise">分析</s-button>
                </div>
                <s-button
                    class="button-item s-icon-button"
                    on-click="refresh"
                    track-name="刷新"
                    ><outlined-refresh class="icon-class"
                /></s-button>
            </div>
            <s-table
              loading="{{table.loading}}"
              datasource="{{table.datasource}}"
              columns="{{table.columns}}">
              <div slot="empty">
                <s-empty>
                    <div slot="action">
                    </div>
                </s-empty>
              </div>
              <div slot="c-status">
                <span class="{{row.status | analyzeStatusStyle}}">{{row.status | getStatus}}</span>
              </div>
              <div slot="c-result">
                <span>{{row.result | getResult}}
                  <s-tooltip s-if="row.result === 'FAILED'" content="{{row | getReason}}">
                    <s-icon name="warning"/>
                  </s-tooltip>
                </span>
              </div>
              <div slot="c-createTime">
                <span>{{row.createTime | timeFormat}}</span>
              </div>
              <div slot="c-operation">
                <s-tooltip placement="top" trigger="{{row.status !== 'ANALISED' ? 'hover' : ''}}" content="当前状态不支持查看分析路径，请重新分析再进行查看">
                    <s-button skin="stringfy" on-click="editPath(row)" disabled="{{row.status !== 'ANALISED'}}">查看分析路径</s-button>
                </s-tooltip>
                <s-button skin="stringfy" on-click="deletePath(row)">删除</s-button>
              </div>
            </s-table>
            <s-pagination
              class="pagin_wrap"
              s-if="{{pager.total}}"
              slot="pager"
              layout="{{'total, pageSize, pager, go'}}"
              pageSize="{{pager.pageSize}}"
              total="{{pager.total}}"
              page="{{pager.page}}"
              resetPageWhenSizeChange="{{true}}"
              on-pagerChange="onPagerChange"/>
        </div>
    </s-app-detail-page>
</template>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class PathDetail extends Component {
    static components = {
        'outlined-editing-square': OutlinedEditingSquare,
        'icon-left': OutlinedLeft,
        'outlined-refresh': OutlinedRefresh
    };
    initData() {
        return {
            klass: 'vpc-path-detail',
            instance: {},
            table: {
                datasource: [],
                columns: [
                    {name: 'shortId', label: '分析ID'},
                    {name: 'status', label: '分析状态'},
                    {name: 'result', label: '分析结果'},
                    {name: 'createTime', label: '分析时间'},
                    {name: 'operation', label: '操作'}
                ]
            },
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            urlQuery: getQueryParams()
        };
    }

    static filters = {
        statusStyle(value) {
            return ProbeStatus.fromValue(value).styleClass || '';
        },
        getStatus(value) {
            if (value === 'ANALISED') {
                return '分析完成';
            } else if (value === 'ANALISING') {
                return '分析中';
            } else if (value === 'ANALISE_FAILED') {
                return '分析失败';
            }
        },
        analyzeStatusStyle(value) {
            return AnalyzeStatus.fromValue(value).styleClass || '';
        },
        getResult(value) {
            if (value === 'SUCCESS') {
                return '可访问';
            } else if (value === 'FAILED') {
                return '不可访问';
            } else if (value === 'UN_ANALISE') {
                return '未分析';
            }
            return '-';
        },
        getType(value) {
            let typeList = {
                BCC: '云服务器',
                BBC: '弹性裸金属服务器',
                SUBNET: '子网',
                DC: '专线通道',
                VPN_CONN: 'VPN通道',
                IDC: '混合云网关',
                PUBLICIP: '公网IP',
                PUBLIC_IP: '公网IP',
                OUT_OF_CLOUD: '云外IP'
            };
            return value ? typeList[value] : '-';
        },
        routeText(type) {
            return RouteType.getTextFromValue(type);
        },
        timeFormat(time) {
            return utcToTime(time) || '-';
        },
        getUpCase(value) {
            if (value === 'all' || value === 'ALL') {
                return '全部协议';
            }
            return value ? value.toUpperCase() : '全部协议';
        },
        getRegion(value) {
            return value ? AllRegion.getTextFromValue(value) : '-';
        },
        getReason(value) {
            let reason = value?.reason || value?.content?.reason;
            return pathReasonList.find(item => item.value === reason)?.text || '分析异常';
        }
    };

    attached() {
        this.loadDetail();
        this.loadPathList();
    }

    loadDetail() {
        return this.$http
            .pathanaliseDetail(this.data.get('urlQuery.id'), {'x-silent-codes': ['NoSuchObject']})
            .then(data => {
                this.data.set('instance', data);
            });
    }

    loadVpcDetail() {
        const vpcId = this.data.get('urlQuery.vpcId');
        this.$http
            .getVpcDetail({
                vpcIds: [vpcId]
            })
            .then(data => {
                const result = data[vpcId] || {};
                this.data.set('vpcInfo', result);
            });
    }

    beforeEdit(type) {
        const TYPE_MAP = {
            name: 'name',
            description: 'description'
        };
        const instance = this.data.get('instance');
        this.data.set(TYPE_MAP[type], instance[TYPE_MAP[type]]);
    }

    onRegionChange() {
        location.hash = '#/vpc/probe/list';
    }

    onInput(e, type) {
        let result;
        switch (type) {
            case 'name':
                const pattern = /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{0,64}$/;
                result = e.value.length <= 64 && pattern.test(e.value);
                break;
            case 'description':
                result = e.value.length <= 200;
                break;
        }
        this.ref(`${type}EditBtn`).data.set('disabled', !result);
    }

    onEdit(type) {
        const TYPE_MAP = {
            name: 'name',
            description: 'description'
        };
        let instance = this.data.get('instance');
        let value = this.data.get(TYPE_MAP[type]);
        this.$http
            .editProbe(instance.probeId, {
                [type]: value
            })
            .then(() => {
                this.editCancel(type);
                this.loadDetail();
            })
            .catch(() => {
                this.editCancel(type);
                this.loadDetail();
            });
    }

    // 编辑弹框-取消
    editCancel(type) {
        const extraType = ['domainName', 'dnsServer'];
        const disableBtn = extraType.indexOf(type) === -1;
        this.ref(`editBtn-${type}`).data.set('disabled', disableBtn);
        this.ref(`popover-${type}`).data.set('visible', false);
    }
    onRegionChange() {
        location.hash = '#/vpc/probe/list';
    }
    loadPathList() {
        this.data.set('table.loading', true);
        let payload = this.getPayload();
        return this.$http.pathanaliseHistory(payload).then(data => {
            this.data.set('table.datasource', data.result);
            this.data.set('pager.total', data.totalCount);
            this.data.set('table.loading', false);
        });
    }
    getPayload() {
        const {pager} = this.data.get('');
        let payload = {
            pageNo: pager.page,
            pageSize: pager.pageSize,
            pathId: this.data.get('urlQuery.id'),
            orderBy: 'createTime',
            order: 'asc'
        };
        return {...payload};
    }
    // 改变页数
    onPagerChange({value}) {
        this.data.set('pager.page', value.page);
        this.data.set('pager.pageSize', value.pageSize);
        this.loadPathList();
    }
    // 点击修改icon
    onInstantEdit(row, type) {
        this.data.set(`edit.${type}.value`, row[`${type}`]);
        this.data.set(`edit.${type}.error`, false);
        const typePop = this.ref(`popover-${type}`);
        typePop.data.set('visible', !typePop.data.get('visible'));
    }
    // 编辑弹框-输入名称/描述
    onEditInput({value}, type) {
        let result = checkRule(type, value);
        this.data.set(`edit.${type}.error`, !result);
        this.data.set(`edit.${type}.value`, value);
        this.ref(`editBtn-${type}`).data.set('disabled', !result);
    }
    // 编辑弹框-提交
    editConfirm(instance, type) {
        let edit = this.data.get(`edit.${type}`);
        if (edit.error) {
            return;
        }
        const key = type === 'name' ? 'description' : 'name';
        this.$http
            .editPathanalise(this.data.get('urlQuery.id'), {
                [type]: edit.value,
                [key]: instance[`${key}`]
            })
            .then(() => {
                this.editCancel(type);
                Notification.success('修改成功');
                this.loadDetail();
            });
    }
    deletePath(row) {
        let confirm = new Confirm({
            data: {
                open: true,
                content: '确认删除选中的分析记录？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http.pathanaliseDelete(row.shortId).then(() => {
                this.data.set('pager.page', 1);
                this.loadPathList();
                Notification.success('释放成功', {placement: 'topRight'});
            });
        });
    }
    editPath(row) {
        const bcm = new BcmDetail({
            data: {
                content: row,
                name: this.data.get('instance.name'),
                resourceId: this.data.get('instance')?.shortId
            }
        });
        bcm.attach(document.body);
    }
    back() {
        window.location.hash = '#/vpc/pathanalise/list';
    }
    startPathAnalise() {
        this.$http.startPathanalise(this.data.get('urlQuery.id')).then(res => {
            this.loadPathList();
            Notification.success('执行分析成功', {placement: 'topRight'});
        });
    }
    refresh() {
        this.loadPathList();
    }
}
export default San2React(Processor.autowireUnCheckCmpt(PathDetail));
