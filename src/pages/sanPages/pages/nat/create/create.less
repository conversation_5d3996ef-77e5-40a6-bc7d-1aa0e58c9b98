.create-nat-wrap {
    .config-content-not-border {
        width: 1180px;
        background: #fff;
        margin: 0 130px;
        padding: 24px;
        .resource-group-panel {
            padding: 0;
            margin: 0;
            border: none;
            h4 {
                padding: 0;
            }
        }
    }
    .h4-not-bg h4 {
        &::before {
            background-color: transparent;
        }
    }
    .body-part-content {
        width: 980px;
        margin: 10px auto 0 auto;
        padding: 20px;
        border-radius: 6px;
        .project-name {
            width: 100px;
            display: inline-block;
        }
        .s-form-item {
            &-label {
                .inline-tip {
                    position: absolute;
                    .s-tip {
                        position: relative;
                        top: 3px;
                        &:hover {
                            background: #fff;
                            path {
                                fill: #2468f2;
                            }
                        }
                    }
                }
            }
            &:last-child {
                margin-bottom: 0;
            }
            &-with-help {
                margin-bottom: 20px;
            }
        }
        &-region,
        &-flavor {
            .s-form-item-control {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                .nat-mode-wrap {
                    margin-bottom: 16px;
                }
            }
        }
        .slider-info {
            color: #5e626a;
            margin-top: 20px;
        }
        .dragger-input {
            display: inline-block;
            position: relative;
            left: 10px;
            top: -10px;
        }
    }
    .normal-flavor-wrapper {
        display: flex;
        align-items: center;
        .inline-tip {
            margin-left: 20px;
        }
        .enhance-inline-tip {
            margin: 0 0 15px 8px;
            .s-tip {
                position: relative;
                top: 2px;
                .s-icon {
                    fill: #84868c;
                    left: -1px;
                }
                &:hover {
                    background: #fff;
                    path {
                        fill: #2468f2;
                    }
                }
            }
        }
    }
    .normal-flavor-tip {
        color: #84868c;
        .inline-tip {
            margin-top: 8px;
        }
    }

    .tip-wrap {
        margin-top: 10px;
        color: #fd2828;
    }
    .tip-icon {
        color: #9e9898;
        border: 1px solid #9e9898;
        margin-left: 10px;
        box-sizing: content-box;
        &:hover {
            border-color: #2468f2;
            color: #2468f2;
        }
    }
    .tip-icon-wrap {
        margin-left: 5px;
        font-size: 14px;
        border: 1px solid #999;
        color: #999;
        &:hover {
            color: #fff;
            background-color: #f18823;
            border-color: #f18823;
        }
    }
    .nat-purchase-wrap {
        .wrapper {
            display: flex;
        }
        .s-radio-checked {
            .s-radio-text {
                border-right: 1px solid #2468f2;
            }
        }
        label {
            width: 45px;
            .s-radio-text {
                border-radius: 0 !important;
            }
        }
        .s-radio-text {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .radius-left {
            .s-radio-text {
                border-radius: 4px 0 0 4px !important;
            }
        }
        .radius-right {
            .s-radio-text {
                border-radius: 0 4px 4px 0 !important;
            }
        }
    }
    .order-confirm {
        margin-top: 20px;
    }
    .form-part-wrap {
        width: 1180px;
        background: #fff;
        margin: 0 130px;
        padding: 24px;
        h4 {
            margin: 0;
            display: block;
            zoom: 1;
            font-size: 16px;
            color: #151b26;
            font-weight: 500;
            margin: 8px 0;
            margin: 0;
        }
        .s-form-item-label {
            width: 130px;
            height: 30px;
        }
        .label_class {
            .inline-tip {
                top: 3px;
                position: relative;
            }
        }
        .vpc-nat-eip-opt {
            margin-top: 0px;
            .s-radio-text {
                width: 95px;
            }
            .s-select {
                margin-bottom: 16px;
                .s-select-option-list {
                    .s-checkbox {
                        width: 22px;
                    }
                }
            }
            .s-table {
                width: 700px;
                .s-radio-text {
                    display: none;
                }
                .s-table-body {
                    max-height: 200px;
                }
            }
            .s-pagination {
                float: right;
                margin-top: 10px;
            }
            .nat-shareGroup-tip {
                margin-top: -8px;
                margin-bottom: 8px;
                color: #84868c;
                font-size: 12px;
            }
        }
    }
    .buybucket {
        width: 100%;
        left: 0;
        height: 80px !important;
        .buybucket-container {
            width: 1180px;
            display: inline-flex;
            height: 80px;
            transform: translateY(0%);
            display: flex;
            align-items: center;
            margin: 0 auto;
            .price-item {
                padding: 0 40px !important;
            }
            .detail-wrapper {
                padding-left: 0px;
            }
            .confirm-btn {
                margin-left: 0 !important;
            }
            .shopping-cart {
                flex: none;
            }
            .billing-sdk-total-price-wrapper {
                margin-left: 16px;
            }
            .nat-price-detail {
                margin-left: 16px;
                .price_label {
                    color: #2468f2;
                }
            }
            .billing-sdk-protocol-wrapper .buy-agreement {
                margin-bottom: 8px !important;
            }
        }
    }
    .billing-sdk-order-confirm-wrapper-default {
        padding: 0px;
    }
    // 售罄的匹配样式
    .nat_create_sellOut {
        .s-badge {
            &::after {
                display: block;
                content: '';
                position: absolute;
                top: 0px;
                left: -2px;
                width: 24px;
                height: 24px;
                background: url('https://bce.bdstatic.com/p3m/common-service/uploads/soldout_d7e337c.png') no-repeat;
                background-size: contain;
            }
        }
        .s-radio-text {
            width: 72px;
        }
        .s-radio-checked:not(.state-disabled) .s-radio-text:after {
            width: 1px !important;
        }
    }
    .s-step-block {
        width: 300px !important;
        margin-bottom: 25px !important;
    }
    .productType-wrap {
        display: flex;
        .productType-item {
            &:hover {
                border-color: #2468f2;
            }
            display: flex;
            position: relative;
            width: 260px;
            height: 72px;
            align-items: center;
            margin-right: 16px;
            border: 1px solid #e8e9eb;
            border-radius: 6px;
            padding: 14px 12px;
            box-sizing: border-box;
            .productType-img {
                width: 40px;
                height: 40px;
                margin-right: 21px;
            }
            .productType-content-wrap {
                display: flex;
                flex-direction: column;
                .productType-text {
                    font-size: 14px;
                    color: #151b26;
                    font-weight: 500;
                    margin-bottom: 8px;
                }
                .productType-content {
                    font-size: 12px;
                    color: #5e626a;
                }
            }
            &:hover {
                cursor: pointer;
            }
        }
        .actice-mode {
            background: #eef3fe;
            border: 1px solid #2468f2;
            .productType-text {
                color: #2468f2 !important;
            }
        }
    }
    .button-mode {
        .s-radio-checked {
            .s-radio-text {
                background: #eef3fe;
            }
        }
    }
    .form-body-wrapper {
        margin: 40px 0 0 0;
        padding: 0;
    }
    .form-part-title {
        margin-top: 40px !important;
    }
    .network-subnet-widget {
        .s-form-item-control {
            display: flex;
            .subnetId-wrapper {
                display: inline-block;
                margin-left: 8px;
                position: relative;
                .common-tip {
                    position: absolute;
                    top: 40px;
                    white-space: nowrap;
                    font-family: PingFangSC-Regular;
                    font-size: 12px;
                    line-height: 20px;
                    font-weight: 400;
                }
                .zone-tip {
                    color: #84868c;
                }
                .none-ip-tip {
                    color: #f33e3e;
                }
                .s-form-item {
                    margin-top: 0px !important;
                    .s-form-item-control-wrapper {
                        margin-left: 0px;
                    }
                }
            }
        }
    }
    .resource-group-panel {
        dt {
            margin-bottom: 24px;
        }
        h4 {
            padding: 0;
        }
    }
    .autoRenew-wrapper {
        display: flex;
    }
    .purchase-btn {
        .s-radio-button:first-child .s-radio-text {
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
            border-left-width: 1px;
        }
        .s-radio-button:last-child .s-radio-text {
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
        }
        .s-radio-button-group label:not(.state-disabled) input[type='radio']:checked ~ .s-radio-text {
            background-color: #eef3fe;
        }
    }
    .resouce-group-select .footer .tip {
        color: #84868c;
    }
    .s-radio-button-group .s-radio-button .s-radio-text:before {
        width: 0;
    }
    .inline-tip {
        .s-tip:hover {
            background: #fff;
            path {
                fill: #2468f2;
            }
        }
        .warning-class {
            position: relative;
            left: -1px;
            .s-icon {
                path {
                    fill: #83868c;
                }
            }
        }
        &:hover {
            .warning-class {
                .s-icon {
                    path {
                        fill: #2468f2;
                    }
                }
            }
        }
    }
    .resouce-group-select {
        .resouce-group-select-main {
            .wrapper {
                margin-left: 7px;
            }
        }
    }
    .s-create-page-footer {
        padding-left: 0;
    }
    .s-radio-button-group .s-radio-text,
    .s-radio-button .s-radio-text {
        border-color: #d4d6d9;
    }
    .s-radio-button-group .s-radio-text,
    .s-radio-button .s-radio-text {
        border-left-width: 1px;
        margin-left: -1px;
        transform: translateX(1px);
    }
    .s-radio-button-group label:not(.state-disabled) input[type='radio']:checked ~ .s-radio-text {
        border-left: 1px solid #2468f2;
    }
    .autorenew-panel {
        label {
            margin-right: 19px;
        }
        .renew-expire-time {
            color: #84868c;
            margin-top: 8px;
        }
    }
    .nat-eip-wrap {
        .s-radio-button-group .s-wrapper {
            height: 32px;
        }
        .nat-eip-loading {
            margin-top: 16px;
        }
    }
    .s-radio-button .s-radio-text:hover {
        z-index: 1;
    }
    .region-radio-group .s-radio-button-group .s-radio-text:hover {
        &:hover {
            border-left: 1px solid #2468f2;
            left: -1px;
            z-index: 11;
        }
        &:active {
            left: -1px;
            z-index: 111;
        }
    }
    .s-radio-button-group label:not(.state-disabled) input[type='radio']:checked ~ .s-radio-text {
        left: -1px;
        z-index: 999;
    }
    .tag-edit-panel {
        .s-form-item {
            margin: 0 8px 12px 0 !important;
        }
        .s-form-item-control-wrapper {
            margin-left: 0px !important;
        }
    }
    .tag-v2-panel-container {
        width: 800px !important;
    }
    .advance-icon {
        margin-left: 10px;
        transition: all 0.3s ease;
        transform: rotate(0);
    }
    .advance-icon.actived {
        transform: rotate(180deg);
    }
    .legend-wrap {
        margin: 10px 0;
    }
    .legend-wrap > label.s-legend-highlight:before {
        content: none;
    }
    .resouce-group-select .resouce-group-select-main > label {
        width: 123px;
    }
    .resouce-group-select .footer {
        margin-left: 130px;
    }
}

.nat-order-wrap {
    margin: 0 130px;
    width: 1180px !important;
    .order-legend {
        border-radius: 6px;
        .legend-header {
            line-height: 24px;
            padding: 24px 0 16px 0;
        }
        .content {
            padding: 16px 0 0 0;
        }
    }
    .coupon-wrapper {
        margin-top: 16px;
        .content {
            padding: 12px 0;
        }
        .legend-header {
            padding-top: 12px;
            padding-bottom: 12px;
        }
    }
    .price-summary {
        border-radius: 0 0 6px 6px;
        .buy-agreement {
            label {
                display: inline-table;
            }
            input {
                border-radius: 2px;
                border-color: #e8e9eb;
                margin-right: 8px;
            }
        }
    }
}

.vpn-purchase-wrap {
    .wrapper {
        display: flex;
    }
    label {
        width: 45px;
    }
    .s-radio-text {
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.s-badge-content {
    z-index: 99;
}

.locale-en {
    .create-nat-wrap .form-part-wrap .s-form-item-label {
        width: 186px;
    }
    .create-nat-wrap .nat-purchase-wrap label {
        width: auto;
    }
    .create-nat-wrap .resouce-group-select .resouce-group-select-main > label {
        width: 186px;
    }
    .create-nat-wrap .resouce-group-select .footer {
        margin-left: 186px;
    }
}
.nat-create-component {
    width: 100%;
}
.nat-price-detail {
    .s-table-qw {
        max-width: 500px !important;
    }
}
