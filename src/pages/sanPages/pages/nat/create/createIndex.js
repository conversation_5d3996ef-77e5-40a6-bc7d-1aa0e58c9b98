import _ from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import {San2React} from '@baidu/bce-react-toolkit';
import './create';
const AllRegion = window.$context.getEnum('AllRegion');

const {template, invokeSUI, invokeAppComp, invokeComp} = decorators;

const tpl = html`
    <template>
        <div s-if="!indexLoading" style="width:100%" class="index-loading-class">
            <s-loading loading style="width:100%">
                <nat-create
                    route="{{urlQuery}}"
                    requestNeed="{{requestNeed}}"
                    vpcId="{{vpcId}}"
                    class="nat-create-component"
                />
            </s-loading>
        </div>
        <nat-create
            route="{{urlQuery}}"
            s-if="indexLoading"
            s-ref="vpcNat"
            vpcId="{{vpcId}}"
            class="nat-create-component"
        />
    </template>
`;

@invokeComp('@nat-create')
@template(tpl)
@invokeSUI
@invokeAppComp
class CreateIndex extends Component {
    initData() {
        return {
            indexLoading: true,
            requestNeed: true,
            vpcId: '',
            urlQuery: getQueryParams()
        };
    }
    inited() {
        this.data.set('vpcId', this.data.get('urlQuery.vpcId'));
        window.$framework.events.on(window.$framework.EVENTS.AFTER_REGION_CHANGED, () => {
            const isPrivate = this.data.get('urlQuery').natType === 'private';
            const currentRegion = window.$context.getCurrentRegionId();
            if (isPrivate) {
                if (
                    [AllRegion.BJ, AllRegion.NJ, AllRegion.SU, AllRegion.FWH, AllRegion.BD, AllRegion.GZ].includes(
                        window.$context.getCurrentRegionId()
                    )
                ) {
                    this.handleRefresh({id: currentRegion});
                } else {
                    window.location.hash = '#/vpc/nat/list';
                }
            } else {
                this.handleRefresh({id: currentRegion});
            }
        });
    }
    handleRefresh(e) {
        let vpcNat = this.ref('vpcNat')?.data;
        if (e.id === vpcNat?.get('formData')?.region && !vpcNat?.get('loadNeed')) {
            return;
        }
        this.data.set('vpcId', '');
        this.data.set('indexLoading', false);
        this.nextTick(() => {
            // 等一会再执行
            setTimeout(() => {
                this.data.set('indexLoading', true);
            }, 100);
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(CreateIndex));
