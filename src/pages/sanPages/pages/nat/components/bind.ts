/**
 * @file network/nat/pages/Create.js
 * <AUTHOR>
 */

import {Component} from 'san';
import {Dialog, Button, Form, Radio, Tooltip, Notification} from '@baidu/sui';
import {html, decorators} from '@baiducloud/runtime';

import {NatType} from '@/pages/sanPages/common/enum';
import Eip from './eip';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';

const {service} = decorators;
import './style.less';
import _ from 'lodash';

interface SubmitParams {
    natId?: string;
    natGatewayId?: string;
    eips?: Array<string>;
    dnatEips?: Array<string>;
    bindEips?: Array<string>;
}

/* eslint-disable */
const template = html`
    <template>
        <s-dialog open="{{open}}" title="{{title}}">
            <div class="vpc-nat-eip-bind">
                <div class="kind-tip">
                    {{'温馨提示：普通型NAT网关的SNAT和DNAT的公网不能是同一个EIP，增强型NAT网关的SNAT和DNAT的公网可以是同一个EIP。'}}
                </div>
                <s-form s-ref="form" data="{=formData=}" rules="{{rules}}">
                    <s-form-item s-if="!selectedItem.clusterMode" prop="type" label="NAT类型：">
                        <s-radio-group
                            enhanced
                            class="s-doc-radio"
                            datasource="{{datasource.type}}"
                            value="{=formData.type=}"
                            track-id="ti_vpc_nat_create"
                            track-name="付费方式"
                            radioType="button"
                        >
                        </s-radio-group>
                    </s-form-item>
                    <s-form-item prop="nat" label="NAT公网IP：" class="center_class">
                        <nat-eip
                            eips="{=formData.eips=}"
                            flavor="{=selectedItem.flavor=}"
                            clusterMode="{=selectedItem.clusterMode=}"
                            cuNum="{=selectedItem.cuNum=}"
                            selected-item="{{selectedItem}}"
                            nat-type="{{formData.type}}"
                            quota="{=formData.quota=}"
                            is-share-group="{{isShareGroup}}"
                            is-bind="{{isBind}}"
                            hideNormalType="{{hideNormalType}}"
                        />
                    </s-form-item>
                </s-form>
            </div>
            <div slot="footer">
                <s-button on-click="onClose">取消</s-button>
                <s-tooltip placement="topRight" trigger="{{disableTip ? 'hover' : ''}}">
                    <!--bca-disable-next-line-->
                    <div slot="content">{{disableTip | raw}}</div>
                    <s-button disabled="{{disableNat}}" skin="primary" on-click="onConfirm">确定</s-button>
                </s-tooltip>
            </div>
        </s-dialog>
    </template>
`;
/* eslint-enable */
export default class extends Component {
    static template = template;
    static components = {
        's-form': Form,
        's-form-item': Form.Item,
        's-dialog': Dialog,
        's-button': Button,
        's-radio-group': (Radio as any).RadioGroup,
        'nat-eip': Eip,
        's-tooltip': Tooltip
    };
    initData() {
        return {
            FLAG,
            formData: {
                type: NatType.SNAT,
                eips: [],
                quota: null
            },
            datasource: {
                type: []
            },
            isShareGroup: '',
            rules: {
                nat: [
                    {
                        validator(rule, value, callback, source) {
                            if (value instanceof Array) {
                                if (source.isBind && (!source.quota || value.length > (source.quota.free || 0))) {
                                    return callback(source.quota.errorMessage);
                                }
                            }
                            callback();
                        }
                    }
                ]
            },
            disableNat: true,
            disableTip: '',
            isBind: false,
            open: true
        };
    }
    inited() {
        this.data.set('formData.isBind', this.data.get('isBind'));
        this.data.set('datasource.type', this.data.get('natType') || NatType.toArray());
        if (!FLAG.NetworkNatOpt) {
            this.data.set('hideNormalType', true);
        }
    }
    attached() {
        this.watch('formData.eips', e => {
            this.checkDisable();
        });
        this.watch('formData.type', e => {
            if (this.data.get('disabledFlag')) {
                this.data.set('disabledFlag', false);
            }
        });
    }
    checkDisable() {
        let eips = this.data.get('formData.eips');
        let disable = eips ? eips.length === 0 : true;
        const natType = this.data.get('formData.type');
        let selectedItem = this.data.get('selectedItem');
        let selectedEips = [];
        let natTypeMapEips = [];
        const snatEips = this.data.get('snatEips');
        const dnatEips = this.data.get('dnatEips');
        // 增强型NAT不区分SNAT、DNAT
        if (selectedItem.clusterMode) {
            selectedEips = selectedItem.combineEipsAll;
            natTypeMapEips = [...(snatEips || []), ...(dnatEips || [])];
        } else if (natType && selectedItem) {
            selectedEips = natType === NatType.SNAT ? selectedItem.eips || [] : selectedItem.dnatEips || [];
            natTypeMapEips = natType === NatType.SNAT ? snatEips : dnatEips;
        }
        let isShareGroup = '';
        if (selectedItem) {
            let shareGroupIdItem = _.find(selectedEips, item => item.shareGroupId);
            if (shareGroupIdItem) {
                isShareGroup = shareGroupIdItem.shareGroupId;
            }
        }
        let usedEip = [];
        // 初始状态下的为[]
        if (!this.data.get('disabledFlag')) {
            if (!this.data.get('isBind') && eips && eips.length === 0 && !isShareGroup) {
                this.data.set('disabledFlag', true);
                const selectedEip = _.map(selectedEips, item => item.eip);
                usedEip = _.intersection(natTypeMapEips, selectedEip);
                disable = selectedEips.length === 0 ? true : Boolean(usedEip.length);
            } else if (isShareGroup) {
                usedEip = _.intersection(natTypeMapEips, eips);
                disable = eips.length === 0 ? true : Boolean(usedEip.length);
            }
        } else {
            if (!this.data.get('isBind') && eips) {
                usedEip = _.intersection(natTypeMapEips, eips);
                disable = eips.length === 0 ? true : Boolean(usedEip.length);
            }
        }
        this.data.set('disableNat', disable);
        this.data.set(
            'disableTip',
            `${
                usedEip?.length
                    ? usedEip.join('，') +
                      (selectedItem.clusterMode
                          ? '被SNAT或DNAT条目占用，请先删除对应的条目'
                          : `被${natType.toUpperCase()}条目占用，请先删除对应的${natType.toUpperCase()}条目。`)
                    : ''
            }`
        );
    }
    doSubmit() {
        const form = this.ref('form');
        return (this.ref('form') as any).validateFields().then(() => {
            this.data.set('disableNat', true);
            let formData = this.data.get('formData');
            const natType = this.data.get('formData.type');
            const selectedItem = this.data.get('selectedItem');
            let selectedEips = [];
            if (selectedItem.clusterMode) {
                selectedEips = selectedItem.combineEipsAll;
            } else if (natType && selectedItem) {
                selectedEips = natType === NatType.SNAT ? selectedItem.eips || [] : selectedItem.dnatEips || [];
            }
            let isShareGroup = '';
            if (selectedItem) {
                let shareGroupIdItem = _.find(selectedEips, item => item.shareGroupId);
                if (shareGroupIdItem) {
                    isShareGroup = shareGroupIdItem.shareGroupId;
                }
            }
            // 初始状态下的为[]
            if (!this.data.get('isBind') && formData.eips && formData.eips.length === 0 && !isShareGroup) {
                formData.eips = selectedEips.map(item => item.eip);
            }
            const isDnat = formData.type === NatType.DNAT;

            let payload: SubmitParams = {natGatewayId: selectedItem.id};

            if (selectedItem.clusterMode) {
                payload.bindEips = formData.eips;
            } else if (!isDnat) {
                payload.eips = formData.eips;
            } else {
                payload.dnatEips = formData.eips;
            }
            let quota = formData.quota?.free;
            // 绑定的情况下判断
            if (this.data.get('isBind') && quota && quota < formData.eips?.length) {
                Notification.error(formData.quota?.errorMessage || `此类型NAT网关最多可绑定个${quota}EIP`);
                return Promise.reject();
            }
            const {clusterMode, ipVersion} = selectedItem;
            if (this.data.get('isBind')) {
                let bindReqUrl = 'natEipBind';
                // 增强型NAT IPv6绑定eip
                if (clusterMode && ipVersion === 'v6') {
                    bindReqUrl = 'bindEipNatIPv6';
                    payload.natId = selectedItem.id;
                    delete payload.natGatewayId;
                }
                return (this as any).$http[bindReqUrl](payload);
            }
            let unbindReqUrl = 'natEipUnbind';
            // 增强型NAT IPv6 解绑eip
            if (clusterMode && ipVersion === 'v6') {
                unbindReqUrl = 'unbindEipNatIPv6';
                payload.natId = selectedItem.id;
                delete payload.natGatewayId;
            }
            return (this as any).$http[unbindReqUrl](payload);
        });
    }
    onConfirm() {
        this.doSubmit()
            .then(() => {
                this.fire('confirm', '');
                this.data.set('disableNat', false);
                this.onClose();
            })
            .catch(() => {
                this.data.set('disableNat', false);
            });
    }

    onClose() {
        this.dispose && this.dispose();
    }
}
