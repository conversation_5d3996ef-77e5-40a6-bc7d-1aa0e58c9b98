.vpc-nat-eip-bind {
    .kind-tip {
        color: #f33e3e;
    }
    .s-form {
        .s-form-item {
            margin: 10px 0;
        }
        .s-form-item-label {
            width: 100px;
            label {
                float: left;
            }
        }
        .center_class {
            .s-row {
                .s-form-item-control-wrapper {
                    line-height: 30px;
                }
            }
        }
    }
    .vpc-nat-eip-opt {
        margin-top: 0;
        .s-table {
            width: 500px;
        }
        .s-select {
            .s-select-option-list {
                .s-checkbox {
                    width: 22px;
                }
            }
        }
        .s-pagination {
            margin-top: 10px;
            float: right;
        }
    }
    .nat-eip-textellipsis {
        display: inline-block;
        align-items: center;
        line-height: 1;
        text-overflow: ellipsis;
        width: 100px !important;
        white-space: nowrap;
        overflow: hidden;
    }
}

.drawer-open-class {
    display: none !important;
}
.nat-diagnose-disable {
    color: #84868c;
}
