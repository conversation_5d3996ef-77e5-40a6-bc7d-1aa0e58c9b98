import {defineComponent} from 'san';
import u from 'lodash';
import {Select, Popover, Button, Tooltip, Dropdown, Menu} from '@baidu/sui';
import {OutlinedDown} from '@baidu/sui-icon';
import {html} from '@baiducloud/runtime';
import {checker} from '@baiducloud/bce-opt-checker';
import rules from '../rules';

import './style.less';

export default defineComponent({
    template: html`<template>
        <s-dropdown
            s-if="isSupDiagnose"
            class="more-opt {{isOpenDrawer ? 'drawer-open-class' : ''}}"
            trigger="hover"
            visible="{{visible}}"
        >
            <s-menu slot="overlay">
                <s-menu-item disabled="{{item.disabled}}" s-for="item in datasource" key="{{index}}">
                    <s-tooltip placement="top" trigger="{{item.disabled ? 'hover' : ''}}">
                        <!--bca-disable-next-line-->
                        <div slot="content">{{item.tip | raw}}</div>
                        <s-button skin="stringfy" on-click="buttonClick(item.value)" disabled="{{item.disabled}}">
                            {{item.text}}
                        </s-button>
                    </s-tooltip>
                </s-menu-item>
            </s-menu>
            <s-button skin="stringfy">诊断</s-button>
        </s-dropdown>
        <s-tooltip s-else content="{{'增强型IPv6类型实例暂不支持诊断'}}"
            ><span class="nat-diagnose-disable">诊断</span></s-tooltip
        >
    </template>`,
    components: {
        's-dropdown': Dropdown,
        's-select': Select,
        's-popover': Popover,
        's-button': Button,
        's-menu': Menu,
        's-menu-item': Menu.Item,
        's-tooltip': Tooltip,
        's-icon-down': OutlinedDown
    },
    initData() {
        return {
            isOpenDrawer: false
        };
    },
    computed: {
        datasource() {
            let operation = [
                {
                    text: '实例诊断',
                    value: 'diagnose'
                },
                {
                    text: '路径分析',
                    value: 'pathAnalysis'
                }
            ];
            let item = this.data.get('item');
            u.each(operation, (command, i) => {
                let result = checker.check(rules, item, command.value)[command.value];
                operation[i].disabled = result.disable ? result.disable : '';
                operation[i].tip = result.message ? result.message : '';
            });
            return operation;
        },
        isSupDiagnose() {
            const item = this.data.get('item');
            let flag = true;
            if (item?.ipVersion === 'v6') {
                flag = false;
            }
            return flag;
        }
    },
    buttonClick(type) {
        let row = this.data.get('item');
        switch (type) {
            case 'diagnose': {
                this.fire('diagnose', row);
                break;
            }
            case 'pathAnalysis': {
                this.fire('pathAnalysis', row);
                break;
            }
        }
    }
});
