/**
 * @file eip.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import {Radio, Select, Checkbox, Loading, Table, Pagination, Tooltip} from '@baidu/sui';
import {utcToTime} from '@/pages/sanPages/utils/helper';

import {EipType, NatType, NatFlavor} from '@/pages/sanPages/common/enum';
import './style.less';

const {asComponent} = decorators;

@asComponent('@nat-eip')
export default class Eip extends Component {
    static template = html`
        <template>
            <div class="nat-eip-wrap">
                <span s-if="!isBind">{{eipsType}}</span>
                <s-radio-group
                    s-else
                    class="type-wrap"
                    value="{=formData.eipType=}"
                    datasource="{{datasource.eipType}}"
                    disabled="{{(isBind && selectedEips.length > 0) || loading}}"
                    radioType="button"
                    enhanced
                />
                <s-loading loading="{{loading}}" class="nat-eip-loading"></s-loading>
                <div s-if="{{!loading}}" class="vpc-nat-eip-opt">
                    <div s-if="!isBind && !isShareGroup">
                        <span s-if="!selectedEips.length">-</span>
                        <span s-if="{{selectedEips.length === 1}}">{{selectedEips[0].eip}}</span>
                        <s-checkbox-group
                            s-if="{{selectedEips.length > 1}}"
                            class="group-wrap"
                            datasource="{{selectedEipArray}}"
                            value="{=selectedEip=}"
                        />
                    </div>
                    <div s-else>
                        <s-select
                            multiple
                            s-if="{{formData.eipType === 'normal'}}"
                            width="{{320}}"
                            datasource="{{eipDatasource}}"
                            value="{=formData.eipArray=}"
                            disabled="{{quota.free <= 0}}"
                            default-label="请选择弹性公网IP"
                        />
                        <s-select
                            s-else
                            width="{{320}}"
                            datasource="{{eipDatasource}}"
                            value="{=formData.eip=}"
                            disabled="{{(isBind && selectedEips.length > 0) || quota.free <= 0 || eipDatasource.length === 0}}"
                            default-label="请选择弹性公网IP"
                        />
                        <p class="nat-shareGroup-tip" s-if="!isShowShareGroupNoneTip">{{noneEipTip}}</p>
                        <div s-if="datasource.eipGroupDetail.length && isGroup && isShowShareGroupNoneTip">
                            <s-table
                                columns="{{columns}}"
                                loading="{{table.loading}}"
                                datasource="{{datasource.eipGroupDetail}}"
                                selection="{{table.selection}}"
                                on-selected-change="tableSelected($event)"
                            >
                                <template slot="c-text">
                                    <s-tooltip content="{{row.text}}" trigger="hover"
                                        ><span class="nat-eip-textellipsis">{{row.text}}</span></s-tooltip
                                    >
                                </template>
                            </s-table>
                            <s-pagination
                                total="{{pager.count}}"
                                layout="{{'total, pageSize, pager, go'}}"
                                pageSizes="{{pageSizes}}"
                                pageSize="{=pager.size=}"
                                page="{=pager.page=}"
                                on-pagerChange="onPagerChange"
                                on-pagerSizeChange="onPagerSizeChange"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </template>
    `;

    static components = {
        's-checkbox': Checkbox,
        's-checkbox-group': Checkbox.CheckboxGroup,
        's-radio-group': Radio.RadioGroup,
        's-select': Select,
        's-loading': Loading,
        's-table': Table,
        's-pagination': Pagination,
        's-tooltip': Tooltip
    };

    static computed = {
        selectedEips() {
            const natType = this.data.get('natType');
            const selectedItem = this.data.get('selectedItem');
            // 增强型NAT不区分SNAT、DNAT； combineEips即 eips,dnatEips,bindEips去重并集
            if (selectedItem?.clusterMode) {
                return selectedItem.combineEipsAll || [];
            }
            if (natType && selectedItem) {
                return natType === NatType.SNAT ? selectedItem.eips || [] : selectedItem.dnatEips || [];
            }
            return [];
        },
        isShareGroup() {
            const selectedItem = this.data.get('selectedItem');
            if (selectedItem) {
                let shareGroupIdItem = _.find(this.data.get('selectedEips'), item => item.shareGroupId);
                if (shareGroupIdItem) {
                    return shareGroupIdItem.shareGroupId;
                }
                return '';
            }
            return '';
        },
        eipsType() {
            return this.data.get('isShareGroup') ? '共享带宽' : '弹性公网IP';
        },
        selectedEipArray() {
            const natType = this.data.get('natType');
            const selectedItem = this.data.get('selectedItem');
            if (selectedItem?.clusterMode) {
                return selectedItem.combineEipsAll.map(item => ({text: item.eip, value: item.eip})) || [];
            }
            if (natType && selectedItem) {
                return natType === NatType.SNAT
                    ? selectedItem.eips.map(item => {
                          return {
                              text: item.eip,
                              value: item.eip
                          };
                      }) || []
                    : selectedItem.dnatEips.map(item => {
                          return {
                              text: item.eip,
                              value: item.eip
                          };
                      }) || [];
            }
            return [];
        },
        selectedEip() {
            return this.data.get('selectedEipArray').map(item => item.value);
        },
        eipDatasource() {
            const eipList = this.data.get('datasource.eipList');
            return eipList;
        },
        isShowShareGroupNoneTip() {
            const eipDatasource = this.data.get('eipDatasource');
            const eipType = this.data.get('formData.eipType');
            let flag = true;
            const isExistAvailableEip = _.some(eipDatasource, item => !!item.value);
            if ((eipDatasource.length === 0 || !isExistAvailableEip) && eipType === EipType.GROUP) {
                flag = false;
            }
            return flag;
        },
        noneEipTip() {
            const selectedItem = this.data.get('selectedItem');
            const clusterMode = this.data.get('clusterMode');
            const {ipVersion} = selectedItem || {};
            const finalIpVersion = ipVersion ? ipVersion : clusterMode;
            return `带宽组中不存在可绑定的${finalIpVersion === 'v6' ? 'IPv6' : 'IPv4'} IP`;
        }
    };

    initData() {
        return {
            formData: {
                eipType: null,
                eip: null,
                eips: [],
                eipArray: []
            },
            datasource: {
                eipType: EipType.toArray(),
                eipList: [],
                eipGroupDetail: []
            },
            quota: null,
            loading: true,
            eipGroupDetailDisabled: false,
            selectedEip: [],
            table: {
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                loading: false
            },
            columns: [
                {name: 'text', label: '公网IP'},
                {name: 'bandWidth', label: '带宽'},
                {
                    name: 'createTime',
                    label: '创建时间',
                    render(item) {
                        return item.createTime ? utcToTime(item.createTime) : '-';
                    },
                    sortable: true
                },
                {
                    name: 'expireTime',
                    label: '到期时间',
                    render(item) {
                        return item.expireTime ? utcToTime(item.expireTime) : '-';
                    },
                    sortable: true
                }
            ],
            pager: {
                size: 10,
                page: 1,
                count: 0
            },
            pageSizes: [10, 20, 50, 100, 200, 300, 400, 500]
        };
    }

    inited() {
        this.setWatcher();
    }

    setWatcher() {
        // 共享带宽有值默认选中第一个
        this.watch('eipDatasource', value => {
            const isExistAvailable = _.some(value, item => item.value);
            if (!!value?.length && isExistAvailable) {
                const eip = this.data.get('formData.eip');
                if (!eip) {
                    this.data.set('formData.eip', value?.[0]?.value || null);
                }
            } else {
                this.data.set('formData.eip', null);
            }
        });
    }

    attached() {
        const eips = this.data.get('eips') || [];
        this.getEipQuota();
        this.watch('formData.eipType', type => {
            this.data.set('loading', true);
            this.data.set('formData.eip', null);
            this.data.set('formData.eipArray', null);
            this.data.set('datasource.eipList', []);
            this.loadData(type);
        });
        this.watch('formData.eip', eip => {
            const isGroup = this.data.get('formData.eipType') === EipType.GROUP;
            this.data.set('table.selection.selectedIndex', []);
            this.data.set('isGroup', isGroup);
            if (!eip) {
                this.data.set('formData.eips', []);
                this.data.set('datasource.eipGroupDetail', []);
            } else {
                if (!isGroup && !this.data.get('selectedEips').length) {
                    this.data.set('eips', [eip]);
                } else {
                    if (this.data.get('isBind')) {
                        this.data.set('formData.eips', []);
                    }
                    let payload = {
                        shareGroupId: eip,
                        pageNo: this.data.get('pager.page'),
                        pageSize: this.data.get('pager.size')
                    };
                    this.loadEip(payload).then(data => {
                        this.data.set('datasource.eipGroupDetail', data.datasource);
                        this.data.set('pager.count', data.total);
                    });
                }
            }
            this.fire('change', eip);
        });
        this.watch('formData.eipArray', eip => {
            this.data.set('formData.eips', eip);
        });
        this.watch('formData.eips', value => {
            this.data.set('eips', value);
        });
        this.watch('natType', value => {
            this.updateData();
            const eipType = this.data.get('formData.eipType');
            this.loadData(eipType);
            // 切换NAT类型时 清空当前已选择的公网IP
            const eipArray = this.data.get('formData.eipArray');
            if (eipArray?.length) {
                this.data.set('formData.eipArray', []);
            }
        });
        this.watch('selectedEip', value => {
            this.data.set('formData.eips', value);
        });
        this.watch('flavor', value => this.getEipQuota());
        this.watch('clusterMode', value => {
            // 加载配额
            this.getEipQuota();
            // 重新加载eip
            const eipType = this.data.get('formData.eipType');
            this.loadData(eipType);
        });
        this.watch('cuNum', value => this.getEipQuota());
        this.updateData();

        const hideNormalType = this.data.get('hideNormalType');
        if (hideNormalType) {
            this.data.set('datasource.eipType', EipType.toArray('NORMAL'));
        }

        const eipType = this.data.get('formData.eipType');
        const eipArray = eipType === EipType.NORMAL ? eips : [];
        this.data.set('formData.eipArray', eipArray);
    }

    updateData() {
        const isShareGroup = this.data.get('isShareGroup');
        const selectedEips = this.data.get('selectedEips');
        this.data.set('formData.eipType', isShareGroup ? EipType.GROUP : EipType.NORMAL);
        // 增加共享带宽判断 解决解绑共享带宽时未选中也能解绑的bug
        this.data.set('formData.eips', this.data.get('isBind') ? [] : isShareGroup ? [] : _.map(selectedEips, 'eip'));
        const eip = this.data.get('eip');
        if (!_.isUndefined(eip)) {
            this.data.set('formData.eip', eip);
        } else {
            this.data.set('formData.eip', isShareGroup || (selectedEips.length ? selectedEips[0].eip : null) || null);
        }
    }

    // nat创建恢复默认配置进行初始化
    initLoadData() {
        this.data.set('formData.eipType', 'normal');
        this.data.set('formData.eipArray', null);
    }

    loadData(type: 'normal' | 'group') {
        if (type === EipType.NORMAL) {
            this.data.set('datasource.eipGroupDetail', []);
            this.loadEip().then(datasource => {
                const eip = this.data.get('formData.eip');
                if (
                    this.data.get('isBind') &&
                    this.data.get('selectedEips').length &&
                    !this.data.get('isShareGroup') &&
                    eip
                ) {
                    this.data.set('datasource.eipList', datasource.datasource);
                } else {
                    if (eip === null) {
                        this.data.set(
                            'formData.eip',
                            datasource.datasource.length > 0 ? datasource?.datasource?.[0].value : null
                        );
                    }
                    this.data.set('datasource.eipList', datasource.datasource);
                }
                if (this.data.get('formData.eip')) {
                    this.data.set('formData.eip', null);
                }
                this.data.set('loading', false);
            });
        } else {
            this.loadEipGroupList()
                .then(datasource => {
                    const eip = this.data.get('formData.eip');
                    if (eip === null && datasource.length > 0) {
                        this.data.set('formData.eip', datasource[0].value);
                    }
                    this.data.set('datasource.eipList', datasource);
                    this.data.set('loading', false);
                })
                .then(() => {
                    const eip = this.data.get('formData.eip');
                    if (eip) {
                        let payload = {
                            shareGroupId: eip,
                            pageNo: this.data.get('pager.page'),
                            pageSize: this.data.get('pager.size')
                        };
                        return this.loadEip(payload);
                    } else {
                        return Promise.reject();
                    }
                })
                .then(data => {
                    this.data.set('datasource.eipGroupDetail', data.datasource);
                    this.data.set('pager.count', data.total);
                    this.data.set('loading', false);
                })
                .catch(() => {
                    this.data.set('loading', false);
                });
        }
    }

    loadEip(payload: Record<string, any> = {}) {
        const isBind = this.data.get('isBind');
        this.data.set('eipGroupDetailDisabled', true);
        this.data.set('table.loading', true);
        const selectedItem = this.data.get('selectedItem');
        const clusterMode = this.data.get('clusterMode');
        const {ipVersion} = selectedItem || {};
        const finalIpVersion = ipVersion ? ipVersion : clusterMode;
        const isEnhancedIPv6 = finalIpVersion === 'v6';
        const reqParams = _.extend(
            {
                pageNo: payload.pageNo || 1,
                pageSize: payload.pageSize || 1000,
                status: isBind ? 'available' : 'binded'
            },
            payload
        );
        return this.$http.getEipList(reqParams, isEnhancedIPv6 ? 'ipVersion=ipv6' : '').then(data => {
            this.data.set('table.loading', false);
            this.data.set('eipGroupDetailDisabled', false);
            let datasource = [];
            let total = data.totalCount;
            _.each(data.result, (item, idx) => {
                if ((!payload.shareGroupId && !item.shareGroupId) || payload.shareGroupId) {
                    let options: any = {
                        value: item.eip,
                        text:
                            item.name +
                            ' / ' +
                            item.eip +
                            (!payload.shareGroupId ? '（' + item.bandWidth + 'Mbps）' : ''),
                        bandWidth: item.bandWidth + 'Mbps',
                        createTime: item.createTime,
                        expireTime: item.expireTime
                    };
                    if (item.shareGroupId) {
                        options.shareGroupId = item.shareGroupId;
                    }
                    if (!isBind) {
                        if (_.indexOf(_.map(this.data.get('selectedEips'), 'eip'), item.eip) > -1) {
                            datasource.push(options);
                        }
                    } else {
                        datasource.push(options);
                    }
                }
            });
            let result = {datasource, total};
            return result;
        });
    }

    loadEipGroupList() {
        const isShareGroup = this.data.get('isShareGroup');
        return this.$http
            .getEipGroupList({
                pageSize: 1000
            })
            .then(data => {
                let datasource = [];
                const selectedItem = this.data.get('selectedItem');
                const clusterMode = this.data.get('clusterMode');
                const {ipVersion} = selectedItem || {};
                const finalIpVersion = ipVersion ? ipVersion : clusterMode;
                const isEnhancedIPv6 = finalIpVersion === 'v6';
                _.each(data.result, item => {
                    let availableEips = null;
                    if (!this.data.get('isBind')) {
                        availableEips = this.data.get('selectedEips');
                    } else {
                        availableEips = _.find(
                            isEnhancedIPv6 ? item.eipv6s : item.eips,
                            eip => eip.status === 'available'
                        );
                    }
                    if (availableEips && (!isShareGroup || (isShareGroup && item.id === isShareGroup))) {
                        const {id, eipv6Num, name, bandwidthInMbps} = item;
                        // 如果是增强型IPv6NAT 需过滤出支持v6的共享带宽
                        if (isEnhancedIPv6) {
                            if (!!eipv6Num) {
                                datasource.push({
                                    value: id,
                                    text: name + '（' + bandwidthInMbps + 'Mbps）'
                                });
                            }
                        } else {
                            datasource.push({
                                value: id,
                                text: name + '（' + bandwidthInMbps + 'Mbps）'
                            });
                        }
                    }
                });
                if (!this.data.get('selectedItem')) {
                    datasource.push({
                        value: '',
                        text: '暂不选择'
                    });
                } else if (datasource.length === 0) {
                    datasource.push({
                        value: '',
                        text: '暂无数据'
                    });
                }
                return datasource;
            });
    }

    getEipQuota() {
        const item = this.data.get('selectedItem');
        const id = item ? item.id : 'VPC_NAT_DEFAUL_GATEWAYID';
        let type = '';
        if (this.data.get('clusterMode')) {
            // 切换增强型时可能cuNum还没值此时不请求
            if (!this.data.get('cuNum')) {
                return;
            }
            type = `cuNum:${this.data.get('cuNum')}`;
            return this.$http
                .natEipQuota({
                    natGatewayId: id,
                    type
                })
                .then(data =>
                    this.data.set(
                        'quota',
                        _.extend(
                            {
                                errorMessage: html`此类型NAT网关最多可绑定${data.total}个EIP`,
                                maxQuota:
                                    data.total - (this.data.get('isBind') ? this.data.get('selectedEips').length : 0)
                            },
                            data
                        )
                    )
                );
        } else {
            let flavor = this.data.get('flavor');
            type = flavor || (item ? item.flavor : NatFlavor.L) || NatFlavor.L;
            if (type === 'enhanced_12c6q') {
                type = NatFlavor.G;
            }
            return this.$http
                .natEipQuota({
                    natGatewayId: id,
                    type
                })
                .then(data =>
                    this.data.set(
                        'quota',
                        _.extend(
                            {
                                errorMessage: html`此类型NAT网关最多可绑定${data.total}个EIP`,
                                maxQuota:
                                    data.total - (this.data.get('isBind') ? this.data.get('selectedEips').length : 0)
                            },
                            data
                        )
                    )
                );
        }
    }

    tableSelected(e: any) {
        let selectedDatasource = e.value.selectedItems.map(item => {
            return item.value;
        });
        this.data.set('formData.eips', selectedDatasource);
    }

    onPagerChange(e: any) {
        this.data.set('pager.page', e.value.page);
        this.resetTable();
        this.loadPage();
    }

    onPagerSizeChange(e: any) {
        this.data.set('pager.page', 1);
        this.data.set('pager.size', e.value.pageSize);
        this.loadPage();
    }

    loadPage() {
        let payload = {
            shareGroupId: this.data.get('formData.eip'),
            pageNo: this.data.get('pager.page'),
            pageSize: this.data.get('pager.size')
        };
        this.loadEip(payload).then(data => {
            this.data.set('datasource.eipGroupDetail', data.datasource);
            this.data.set('pager.count', data.total);
            this.data.set('table.loading', false);
        });
    }
    resetTable() {
        this.data.set('table.selection.selectedIndex', []);
    }
}
