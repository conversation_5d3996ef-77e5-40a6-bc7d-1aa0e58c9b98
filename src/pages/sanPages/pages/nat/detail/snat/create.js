/**
 * @file network/eni/pages/Create.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {Dialog, Form, Input, Button, Select, Tooltip} from '@baidu/sui';
import {html, decorators} from '@baiducloud/runtime';

import Rule from '@/pages/sanPages/utils/rule';
import {uniqBy, $flag as FLAG} from '@/pages/sanPages/utils/helper';
import {isOverlapping} from '@/pages/sanPages/utils/common';
import './create.less';
const formValidator = self => ({
    name: [
        {required: true, message: '请输入名称'},
        {pattern: Rule.NAT.NAME.pattern, message: '格式不正确'}
    ],
    customCidr: [
        {
            validator(rule, value, cb) {
                let source = self.data.get('formData');
                // 编辑的时候不与自己校验重复
                let selfCidr = self.data.get('editCidr');
                const isEnhancedIPv6 = self.data.get('isEnhancedIPv6');
                let reg = isEnhancedIPv6 ? new RegExp(Rule.IPV6_SEG) : new RegExp(Rule.IP_CIDR);
                if (source.cidr === 'CUSTOM') {
                    if (value === '') {
                        return cb('请输入网段或IP地址');
                    } else if (!reg.test(value)) {
                        return cb('IP格式不正确');
                    } else if (
                        !selfCidr &&
                        _.find(
                            source.ipList,
                            item => item === value + (/\//g.test(value) ? '' : isEnhancedIPv6 ? '/128' : '/32')
                        )
                    ) {
                        return cb('该条目的源网段与已有源网段重叠');
                    }
                }
                return cb();
            }
        }
    ],
    cidr: [
        {required: true, message: '请选择源网段'},
        {
            validator(rule, value, cb) {
                let source = self.data.get('formData');
                // 编辑的时候不与自己校验重复
                let selfCidr = self.data.get('editCidr');
                if (value === 'CUSTOM') {
                    return cb();
                } else if (!selfCidr && _.find(source.ipList, item => item === value)) {
                    return cb('该条目的源网段与已有源网段重复');
                } else if (selfCidr && value !== selfCidr && _.find(source.ipList, item => item === value)) {
                    return cb('该条目的源网段与已有源网段重复');
                }
                return cb();
            }
        }
    ],
    eips: [
        {
            validator(rule, value, cb) {
                let source = self.data.get('formData');
                let eips = self.data.get('eips');
                if (!eips.length) {
                    return cb('暂无可选的公网IP，请先给NAT网关绑定公网IP');
                } else {
                    if (!value.length) {
                        return cb('请选择公网IP');
                    }
                    if (value.length > source.eipQuota) {
                        return cb('超过可绑定的IP配额');
                    }
                    return cb();
                }
            }
        }
    ],
    natIps: [
        {
            validator(rule, value, cb) {
                let source = self.data.get('formData');
                let natIps = self.data.get('natIps');
                if (!natIps.length) {
                    return cb('暂无可选的NAT IP，请先给NAT网关绑定NAT IP');
                } else {
                    if (!value.length) {
                        return cb('请选择NAT IP');
                    }
                    if (value.length > source.natipQuota) {
                        return cb('超过可绑定的IP配额');
                    }
                    return cb();
                }
            }
        }
    ]
});

const {service} = decorators;
const template = html`
<div class="vpc-nat-snat-create">
    <s-dialog open="{{open}}" title="{{title}}" class="vpc-nat-snat-create-dialog">
        <s-form
            s-ref="form"
            rules="{{rules}}"
            data="{=formData=}"
            label-width="{{80}}"
            label-align="left">
            <s-form-item
                label="{{isPrivate ? '名称：' : '条目名称：'}}"
                help="大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65"
                prop="name">
                <s-input
                    value="{=formData.name=}"
                    width="{{220}}"
                    track-id="ti_vpc_nat_snat_create"
                    track-name="条目名称" />
            </s-form-item>
            <s-form-item label="源网段"
                prop="cidr" class="cidr_form_class">
                <s-select
                    width="{{220}}"
                    placeholder="请选择网段"
                    value="{=formData.cidr=}"
                    track-id="ti_vpc_nat_snat_create"
                    track-name="公网IP地址" >
                    <s-select-option
                        s-for="item in sourceAddressDatasource"
                        value="{{item.value}}"
                        label="{{item.text}}"
                    >
                        <s-tooltip visibleArrow="{{false}}" placement="right">
                            <div slot="content">
                                <!--bca-disable-next-line-->
                                {{item.text | raw}}
                            </div>
                            <div>{{item.text}}</div>
                        </s-tooltip>
                    </s-select-option>
                </s-select>
                <div class="tip-class" s-if="formData.cidr!=='CUSTOM' && cidrTip">{{cidrTip}}</div>
            </s-form-item>
            <s-form-item
                style="padding-left:100px"
                s-if="formData.cidr==='CUSTOM'"
                prop="customCidr">
                <s-input
                    width="{{220}}"
                    placeholder="请输入网段或IP地址"
                    value="{=formData.customCidr=}"
                    track-id="ti_vpc_nat_snat_create"
                    track-name="源网段" />
                </s-input>
                <div class="tip-class" s-if="formData.cidr==='CUSTOM' && cidrTip">{{cidrTip}}</div>
            </s-form-item>
            <s-form-item label="公网IP地址："
                class="eips-form-wrap"
                prop="eips" s-if="!isPrivate">
                <s-select
                    filterable
                    filterPlaceholder="输入IP查询"
                    width="{{220}}"
                    placeholder="请选择公网IP地址"
                    value="{=formData.eips=}"
                    multiple
                    check-all="{{eips.length > 0}}"
                    datasource="{{eips}}"
                    track-id="ti_vpc_nat_snat_create"
                    track-name="公网IP地址" />
            </s-form-item>
            <s-form-item label="NAT IP："
                class="eips-form-wrap"
                prop="natIps"
                s-if="isPrivate">
                <s-select
                    width="{{220}}"
                    placeholder="请选择"
                    value="{=formData.natIps=}"
                    multiple
                    check-all="{{natIps.length > 0}}"
                    datasource="{{natIps}}"
                    track-id="ti_vpc_nat_snat_create"
                    track-name="NAT IP" />
            </s-form-item>
            <s-form-item label=" ">
                <div class="none-eip-tip">
                    {{isPrivate ? noneNatipsTip : noneEipsTip}}
                </div>
            </s-form-item>
        </s-form>
        <div slot="footer">
            <s-button on-click="onClose">取消</s-button>
            <s-button skin="primary" disabled="{{snatCreateDisaled}}" on-click="onConfirm">确定</s-button>
        </div>
    </s-dialog>
</div>
`;

export default class extends Component {
    static template = template;
    static components = {
        's-form': Form,
        's-button': Button,
        's-dialog': Dialog,
        's-form-item': Form.Item,
        's-select': Select,
        's-select-option': Select.Option,
        's-tooltip': Tooltip,
        's-input': Input
    };
    static computed = {
        sourceAddressDatasource() {
            let result = [];
            const isEnhancedIPv6 = this.data.get('isEnhancedIPv6');
            _.each(this.data.get('subnetList'), item => {
                const {cidr, ipv6Cidr, name} = item;
                const finalCidr = isEnhancedIPv6 ? ipv6Cidr : cidr;
                result.push({
                    value: finalCidr,
                    text: name + (finalCidr ? '（' + finalCidr + '）' : '')
                });
            });
            result.push({
                value: 'CUSTOM',
                text: '自定义配置'
            });
            return result;
        },
        isEnhancedIPv6() {
            const natInfo = this.data.get('natInfo');
            const {ipVersion} = natInfo || {};
            return ipVersion === 'v6';
        }
    };
    initData() {
        return {
            rules: formValidator(this),
            subnetList: [],
            formData: {
                name: '',
                eips: '',
                cidr: '',
                eipQuota: 0,
                ipList: [],
                customCidr: '',
                natIps: '',
                natipQuota: 0
            },
            formErrors: null,
            snatCreateDisaled: false,
            noneEipsTip: '',
            noneNatipsTip: '',
            cidrTip: '',
            natIps: [],
            FLAG
        };
    }
    attached() {
        const isPrivate = this.data.get('isPrivate');
        const isEnhancedIPv6 = this.data.get('isEnhancedIPv6');
        this.loadSubnetList().then(data => {
            if (!_.isNull(data)) {
                let finalData = data;
                if (isEnhancedIPv6) {
                    finalData = data.filter(item => !!item.ipv6Cidr);
                }
                this.data.set('subnetList', finalData);
                let cidr = this.data.get('formData.cidr');
                if (cidr && !data.find(item => (isEnhancedIPv6 ? item.ipv6Cidr : item.cidr) === cidr)) {
                    this.nextTick(() => {
                        this.data.set('formData.cidr', 'CUSTOM');
                        this.data.set('formData.customCidr', cidr.replace('/32', ''));
                    });
                }
            }
        });
        isPrivate ? this.getNatIpQuota() : this.getEipQuota();
        this.getIpList();
        isPrivate ? this.loadPrivateNatips() : isEnhancedIPv6 ? this.setEnhancedIPv6Eips() : this.loadNatInfo();
        this.watch('formData.cidr', value => {
            let source = this.data.get('formData');
            let overlapIp = '';
            source.ipList.forEach(ip => {
                if (
                    value !== 'CUSTOM' &&
                    !_.find(source.ipList, item => item === value) &&
                    ip !== value + (/\//g.test(value) ? '' : '/32') &&
                    isOverlapping(ip, value + (/\//g.test(value) ? '' : '/32'))
                ) {
                    overlapIp = ip;
                }
            });
            this.data.set(
                'cidrTip',
                overlapIp ? `该条目的源网段与已有源网段${overlapIp}重叠，系统会根据最长掩码匹配规则确定优先级。` : ''
            );
        });
        this.watch('formData.customCidr', value => {
            let source = this.data.get('formData');
            let reg = new RegExp(Rule.IP_CIDR);
            let overlapIp = '';
            source.ipList.forEach(ip => {
                if (
                    value &&
                    reg.test(value) &&
                    !_.find(source.ipList, item => item === value) &&
                    ip !== value + (/\//g.test(value) ? '' : '/32') &&
                    isOverlapping(ip, value + (/\//g.test(value) ? '' : '/32'))
                ) {
                    overlapIp = ip;
                }
            });
            this.data.set(
                'cidrTip',
                overlapIp ? `该条目的源网段与已有源网段${overlapIp}重叠，系统会根据最长掩码匹配规则确定优先级。` : ''
            );
        });
    }

    setEnhancedIPv6Eips() {
        const natInfo = this.data.get('natInfo');
        if (Object.keys(natInfo).length) {
            const {clusterMode, eips, bindEips} = natInfo;
            const finalEips = clusterMode ? uniqBy([...eips, ...(bindEips || [])], 'eip') : item.eips;
            const formatedData = _.map(finalEips, item => {
                return {
                    value: item.eip,
                    text: item.eip
                };
            });
            this.data.set('eips', formatedData);
        }
    }
    loadNatInfo() {
        this.$http
            .getNatList({
                natGatewayId: this.data.get('id')
            })
            .then(data => {
                const item = FLAG.NetworkNatOpt ? data.result?.[0] : data.natgateways?.[0];
                // 增强型不区分SNAT、DNAT, 普通型区分
                const finalEips = item.clusterMode
                    ? uniqBy([...item.eips, ...(item.bindEips || [])], 'eip')
                    : item.eips;
                const formatedData = _.map(finalEips, item => {
                    return {
                        value: item.eip,
                        text: item.eip
                    };
                });
                this.data.set('eips', formatedData);
            });
    }

    // 查询私网NAT IP
    loadPrivateNatips() {
        this.$http
            .getNatIpList({
                natId: this.data.get('id')
            })
            .then(res => {
                const data = res.result;
                const formatedData = _.map(data, item => {
                    return {
                        value: item.natIp,
                        text: item.natIp
                    };
                });
                if (!formatedData.length) {
                    this.data.set('noneNatipsTip', '暂无可选的NAT IP，请先给NAT网关绑定NAT IP');
                }
                this.data.set('natIps', formatedData);
            });
    }
    getEipQuota() {
        this.$http
            .snatEipQuota({
                natId: this.data.get('id')
            })
            .then(data => {
                this.data.set('formData.eipQuota', data.free);
            });
    }
    // 查询snat规则可关联natIp配额
    getNatIpQuota() {
        this.$http.snatNatIpQuota().then(data => {
            this.data.set('formData.natipQuota', data.total);
        });
    }
    getIpList() {
        const isPrivate = this.data.get('isPrivate');
        const isEnhancedIPv6 = this.data.get('isEnhancedIPv6');
        const natId = this.data.get('id');
        (isPrivate
            ? this.$http.privateSnatRuleList({
                  natId
              })
            : isEnhancedIPv6
              ? this.$http.queryIPv6SnatRuleList({natId})
              : this.$http.snatRuleList({
                    natGatewayId: natId
                })
        ).then(data => {
            let ipList = [];
            _.each(data.result, item => {
                ipList.push(item.cidr);
            });
            this.data.set('formData.ipList', ipList);
        });
    }
    doSubmit() {
        let form = this.ref('form');
        let payload = {
            natId: this.data.get('id'),
            ...this.data.get('formData')
        };
        if (payload.cidr === 'CUSTOM') {
            payload.cidr = payload.customCidr;
            if (payload.cidr.indexOf('/') === -1) {
                payload.cidr += '/32';
            }
            delete payload.customCidr;
        }
        delete payload.eipQuota;
        delete payload.ipList;
        return form.validateFields().then(() => {
            this.data.set('snatCreateDisaled', true);
            if (payload.ruleId) {
                return this.$http.editSnatRule(payload);
            }
            return this.$http.snatCreate(payload);
        });
    }
    loadSubnetList() {
        const vpcId = this.data.get('vpcId');
        return this.$http.getSubnetList({vpcId, attachVm: false}).then(data => {
            return data;
        });
    }
    onConfirm() {
        this.data.set('snatCreateDisaled', true);
        const isPrivate = this.data.get('isPrivate');
        const isEnhancedIPv6 = this.data.get('isEnhancedIPv6');
        (isPrivate ? this.onPrivateSubmit() : isEnhancedIPv6 ? this.handleCreateIPv6Snat() : this.doSubmit())
            .then(() => {
                this.fire('confirm');
                this.onClose();
                this.data.set('snatCreateDisaled', false);
            })
            .catch(() => {
                this.data.set('snatCreateDisaled', false);
            });
    }
    onClose() {
        this.data.set('snatCreateDisaled', false);
        this.data.set('open', false);
        this.dispose && this.dispose();
    }

    // 创建/编辑私网SNAT规则
    onPrivateSubmit() {
        let {name, cidr, natIps, ruleId, customCidr} = this.data.get('formData');
        customCidr?.indexOf('/') === -1 && (customCidr += '/32');
        let payload = {
            natId: this.data.get('id'),
            natIps,
            name,
            cidr: cidr === 'CUSTOM' ? customCidr : cidr,
            ...(ruleId ? {ruleId} : {})
        };
        const form = this.ref('form');
        return form.validateFields().then(() => {
            return ruleId ? this.$http.updatePrivateSnat(payload) : this.$http.createPrivateSnat(payload);
        });
    }
    // 创建/编辑 IPV6 SNAT规则
    handleCreateIPv6Snat() {
        let {name, cidr, natIps, ruleId, customCidr} = this.data.get('formData');
        customCidr?.indexOf('/') === -1 && (customCidr += '/128');
        let payload = {
            natId: this.data.get('id'),
            eips: this.data.get('formData.eips'),
            name,
            cidr: cidr === 'CUSTOM' ? customCidr : cidr,
            ...(ruleId ? {ruleId} : {})
        };
        const form = this.ref('form');
        return form.validateFields().then(() => {
            return ruleId ? this.$http.updateIPv6SnatRule(payload) : this.$http.createIPv6SnatRule(payload);
        });
    }
}
