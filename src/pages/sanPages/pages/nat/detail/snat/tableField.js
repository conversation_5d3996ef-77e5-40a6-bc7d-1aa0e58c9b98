import _ from 'lodash';

export const columns = isPrivate =>
    _.compact([
        {
            name: 'name',
            label: isPrivate ? 'SNAT规则名称/ID' : 'SNAT条目名称/ID',
            width: 160
        },
        {
            name: 'status',
            label: '状态',
            width: 80
        },
        {
            name: 'cidr',
            label: '源网段',
            sortable: true
        },
        !isPrivate && {
            name: 'eips',
            label: '公网IP地址'
        },
        isPrivate && {
            name: 'natIps',
            label: 'NAT IP'
        },
        {
            name: 'opt',
            label: '操作'
        }
    ]);
