/* eslint-disable @typescript-eslint/member-ordering */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {Notification} from '@baidu/sui';
import {OutlinedPlus, OutlinedRefresh, OutlinedEditingSquare} from '@baidu/sui-icon';

import rules from '../../rules';
import Create from './create';
import {columns} from './tableField';
import Confirm from '@/pages/sanPages/components/confirm';
import {SnatStatus, NatStatus} from '@/pages/sanPages/common/enum';
import testID from '@/testId';
import './style.less';

const {invokeSUI, invokeSUIBIZ, asComponent, invokeAppComp, template} = decorators;

const AllRegion = window.$context.getEnum('AllRegion');

const tpl = html`
    <div
        class="nat-list-detail-wrap {{isPrivate ? 'private-nat-list-detail-wrap' : ''}}"
        data-testid="${testID.nat.detailSnat}"
    >
        <h4 s-if="!isPrivate">SNAT列表</h4>
        <s-alert skin="warning" s-if="!isPrivate">
            温馨提示：在配置SNAT条目前，请确保NAT网关所在的VPC中已经添加了NAT路由。
            普通型NAT网关和增强型NAT网关均需要配置SNAT条目才能访问互联网。若配置SNAT条目，则轮询SNAT条目指定的公网IP访问互联网。
        </s-alert>
        <s-app-list-page class="nat-snat-list-wrap {{isPrivate ? 'private-nat-snat-list-wrap' : ''}}">
            <div slot="pageTitle"></div>
            <div slot="bulk" s-if="isPrivate">
                <s-tip-button
                    disabled="{{unEditTip || addPrivateSnat.disable}}"
                    isDisabledVisibile="{{true}}"
                    skin="primary"
                    on-click="onCreatePrivate"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{unEditTip || addPrivateSnat.message | raw}}
                    </div>
                    <outlined-plus />
                    创建SNAT规则
                </s-tip-button>
            </div>
            <div slot="bulk" s-else>
                <s-tip-button
                    disabled="{{addSnat.disable || addIPv6Snat.disable || isCanAdd}}"
                    skin="primary"
                    isDisabledVisibile="{{true}}"
                    on-click="onCreate"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{(addSnat.message || addIPv6Snat.message || '资源加载中...') | raw}}
                    </div>
                    <outlined-plus />
                    添加SNAT条目
                </s-tip-button>
                <s-tip-button
                    class="left_class"
                    disabled="{{deleteSnat.disable}}"
                    isDisabledVisibile="{{true}}"
                    on-click="onDelete"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{deleteSnat.message | raw}}
                    </div>
                    删除
                </s-tip-button>
            </div>
            <div slot="filter">
                <s-button class="s-icon-button" on-click="refresh" track-name="刷新"
                    ><outlined-refresh class="icon-class"
                /></s-button>
            </div>
            <s-table
                columns="{{table.columns | filterColumns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                on-selected-change="tableSelected($event)"
                on-filter="onFilter"
                on-sort="onSort"
                selection="{=table.selection=}"
                track-id="ti_vpc_instance_table"
                track-name="列表操作"
            >
                <div slot="error">
                    啊呀，出错了？
                    <a href="javascript:void(0)" on-click="refresh">{{'重新加载'}}</a>
                </div>
                <div slot="empty">
                    <s-empty
                        actionText="{{(addPrivateSnat.disable || unEditTip || addSnat.disable || addIPv6Snat.disable) ? '' : '马上创建'}}"
                        vertical="{{addPrivateSnat.disable || unEditTip || addSnat.disable || addIPv6Snat.disable}}"
                        on-click="onCreate"
                    >
                    </s-empty>
                </div>
                <div slot="c-name">
                    <span>{{row.name}}</span>
                    <s-popover
                        s-ref="popover-name-{{rowIndex}}"
                        placement="top"
                        trigger="click"
                        class="edit-popover-class"
                    >
                        <div class="edit-wrap" slot="content">
                            <s-input
                                value="{=edit.name.value=}"
                                width="320"
                                placeholder="请输入名称"
                                on-input="onEditInput($event, rowIndex, 'name')"
                            />
                            <div class="edit-tip">大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65</div>
                            <s-button
                                skin="primary"
                                s-ref="editBtn-name-{{rowIndex}}"
                                disabled="{{true}}"
                                on-click="editConfirm(row, rowIndex, 'name')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel(rowIndex, 'name')">取消</s-button>
                        </div>
                        <outlined-editing-square class="name-icon" on-click="onInstantEdit(row, rowIndex, 'name')" />
                    </s-popover>
                    <br />
                    <span class="truncated">{{row.ruleId}}</span>
                    <s-clip-board class="name-icon" text="{{row.ruleId}}" />
                </div>
                <div slot="c-status">
                    <span class="{{row.status | statusClass}}">{{row.status | statusText}}</span>
                </div>
                <div slot="c-eips">
                    <!--bca-disable-next-line-->
                    {{row | getEips | raw}}
                </div>
                <div slot="c-natIps">
                    <!--bca-disable-next-line-->
                    {{row | getNatips | raw}}
                </div>
                <div slot="c-opt" class="operations">
                    <s-tooltip trigger="{{unEditTip ? 'hover' : ''}}">
                        <span slot="content">{{unEditTip}}</span>
                        <s-button disabled="{{!isCanEdit}}" skin="stringfy" on-click="onEdit(row)"
                            >编辑</s-button
                        ></s-tooltip
                    >
                    <s-button s-if="isPrivate && !unEditTip" skin="stringfy" on-click="onDeletePrivate(row)">
                        删除
                    </s-button>
                </div>
            </s-table>
            <s-pagination
                s-if="{{pager.total}}"
                slot="pager"
                layout="{{'total, pageSize, pager, go'}}"
                pageSize="{{pager.pageSize}}"
                total="{{pager.total}}"
                page="{{pager.page}}"
                on-pagerChange="onPagerChange"
                on-pagerSizeChange="onPagerSizeChange"
            />
        </s-app-list-page>
    </div>
`;
@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@asComponent('@snat-list')
class NatSnatList extends Component {
    static components = {
        'outlined-plus': OutlinedPlus,
        'outlined-refresh': OutlinedRefresh,
        'outlined-editing-square': OutlinedEditingSquare
    };
    initData() {
        return {
            table: {
                loading: false,
                datasource: [],
                columns,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                }
            },
            pager: {
                page: 1,
                total: 10,
                pageSize: 10
            }
        };
    }

    static filters = {
        statusClass(value: string) {
            return SnatStatus.fromValue(value).styleClass || '';
        },
        statusText(value: string) {
            return value ? SnatStatus.getTextFromValue(value) : '-';
        },
        getEips(item: any) {
            return item.eips.join('<br>');
        },
        filterColumns() {
            const isPrivate = this.data.get('context').isPrivate;
            return columns(isPrivate);
        },
        getNatips(row: any) {
            return row.natIps.join('<br>');
        }
    };
    static computed = {
        unEditTip() {
            const natInfo = this.data.get('context').natInfo;
            const isPrivate = this.data.get('context').isPrivate;
            let tip = '';
            if (isPrivate && !u.contains([NatStatus.ACTIVE, NatStatus.DOWN], natInfo.status)) {
                tip = `当前实例${NatStatus.getTextFromValue(natInfo.status)}，请稍后再试。`;
            }
            return tip;
        },
        isCanEdit() {
            const isPrivate = this.data.get('context').isPrivate;
            const unEditTip = this.data.get('unEditTip');
            let flag = true;
            if (isPrivate && unEditTip) {
                flag = false;
            }
            return flag;
        },
        isCanAdd() {
            const natInfo = this.data.get('context').natInfo;
            let flag = true;
            if (!!Object.keys(natInfo).length) {
                flag = false;
            }
            return flag;
        }
    };

    inited() {
        this.data.set('isPrivate', this.data.get('context').isPrivate);
    }

    async attached() {
        this.loadPage({'x-silent-codes': ['NoSuchNat']});
    }

    getSnatQuota() {
        let natId = this.data.get('context').id;
        this.$http.snatRuleQuota({natId}, {'x-silent-codes': ['Exception']}).then((data: any) => {
            let {addSnat} = checker.check(rules, '', 'addSnat', {
                free: data.free
            });
            this.data.set('addSnat', addSnat);
        });
    }

    getPayload() {
        const {id, vpcId} = this.data.get('context');
        const {pager, order} = this.data.get('');
        let payload = {
            natGatewayId: id,
            vpcId,
            pageNo: pager.page,
            pageSize: pager.pageSize
        };
        return {...payload, ...order};
    }

    loadPage(option = {}) {
        let payload = this.getPayload();
        const isPrivate = this.data.get('context').isPrivate;
        const natInfo = this.data.get('context').natInfo;
        const {ipVersion} = natInfo;
        const isIPv6 = ipVersion === 'v6';
        if (!payload.natGatewayId) {
            return;
        }
        this.resetTable();
        this.data.set('table.loading', true);
        return isPrivate
            ? this.privateSnatRuleList()
            : isIPv6
              ? this.queryIPv6SnatRuleList()
              : this.$http.snatRuleList(payload, option).then((res: any) => {
                    this.getSnatQuota();
                    this.data.set('table.datasource', res.result);
                    this.data.set('pager.total', res.totalCount);
                    this.data.set('table.loading', false);
                });
    }

    resetTable() {
        this.data.set('table.selection.selectedIndex', []);
    }

    tableSelected(e: Event) {
        this.data.set('table.selectedItems', e.value.selectedItems);
        let {deleteSnat} = checker.check(rules, e.value.selectedItems, '');
        this.data.set('deleteSnat', deleteSnat);
    }

    onSort(e: Event) {
        this.data.set('order', e.value);
        this.loadPage();
    }

    // 改变页数
    onPagerChange(e: Event) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }

    // 改变每页显示个数
    onPagerSizeChange(e: Event) {
        this.data.set('pager.pageSize', e.value.pageSize);
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    onCreate() {
        const isPrivate = this.data.get('context').isPrivate;
        if (isPrivate) {
            const unEditTip = this.data.get('unEditTip');
            if (!unEditTip) {
                this.onCreatePrivate();
            }
            return;
        }
        const {vpcId, id} = this.data.get('context');
        const dialog = new Create({
            data: {
                open: true,
                title: '添加SNAT条目',
                vpcId,
                id,
                natInfo: this.data.get('context').natInfo
            }
        });
        dialog.on('confirm', e => this.loadPage());
        dialog.attach(document.body);
    }

    onEdit(row: any) {
        const {vpcId, id} = this.data.get('context');
        const isPrivate = this.data.get('context').isPrivate;
        const dialog = new Create({
            data: {
                open: true,
                title: isPrivate ? '编辑SNAT规则' : '编辑SNAT条目',
                formData: row,
                vpcId,
                id,
                editCidr: row.cidr,
                isPrivate,
                natInfo: this.data.get('context').natInfo
            }
        });
        dialog.on('confirm', e => this.loadPage());
        dialog.attach(document.body);
    }

    onDelete() {
        let selectedItems = this.data.get('table.selectedItems');
        const nameWithId = u.map(selectedItems, item => {
            const {name, ruleId} = item;
            return `${name}/${ruleId}`;
        });
        const dialog = new Confirm({
            data: {
                title: '删除前确认',
                content: `确认删除当前SNAT条目： ${nameWithId.join('、')}?`
            }
        });
        dialog.on('confirm', () => {
            let payload = {
                natId: this.data.get('context').id,
                ruleIds: u.pluck(selectedItems, 'ruleId')
            };
            const natInfo = this.data.get('context').natInfo;
            const {ipVersion} = natInfo;
            const isIPv6 = ipVersion === 'v6';
            let reqUrl = 'deleteSnatRule';
            if (isIPv6) {
                reqUrl = 'deleteIPv6SnatRule';
            }
            this.$http[reqUrl](payload).then(() => this.loadPage());
        });
        dialog.attach(document.body);
    }

    refresh() {
        this.loadPage();
    }

    // 点击修改icon
    onInstantEdit(row: any, rowIndex: number, type: string) {
        this.data.set(`edit.${type}.value`, row[`${type}`]);
        this.data.set(`edit.${type}.error`, false);
        const popWrap = this.ref(`popover-${type}-${rowIndex}`);
        popWrap.data.set('visible', !popWrap.data.get('visible'));
    }

    // 编辑弹框-输入名称/描述
    onEditInput(e: Event, rowIndex: number, type: string) {
        let result = e.value === '' || !/^[a-zA-Z][\w\-\_\/\.]{0,64}$/.test(e.value);
        this.data.set(`edit.${type}.error`, result);
        this.data.set(`edit.${type}.value`, e.value);
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', result);
    }

    // 编辑弹框-提交
    editConfirm(row: any, rowIndex: number, type: string) {
        let edit = this.data.get(`edit.${type}`);
        const isPrivate = this.data.get('context').isPrivate;
        const natInfo = this.data.get('context').natInfo;
        const {ipVersion} = natInfo;
        const isIPv6 = ipVersion === 'v6';
        if (edit.error) {
            return;
        }
        const natId = this.data.get('context').id;
        const {status, name, ...payload} = row;
        (isPrivate
            ? this.$http.updatePrivateSnat({
                  ...payload,
                  natId,
                  [type]: edit.value
              })
            : isIPv6
              ? this.$http.updateIPv6SnatRule({...row, natId, [type]: edit.value})
              : this.$http.editSnatRule({
                    ...row,
                    natId,
                    [type]: edit.value
                })
        ).then(() => {
            this.editCancel(rowIndex, type);
            Notification.success('修改成功');
            this.loadPage();
        });
    }

    // 编辑弹框-取消
    editCancel(rowIndex: number, type: string) {
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', true);
        this.ref(`popover-${type}-${rowIndex}`).data.set('visible', false);
    }

    // 新建私网SNAT规则
    onCreatePrivate() {
        const {vpcId, id} = this.data.get('context');
        const dialog = new Create({
            data: {
                open: true,
                title: '新建SNAT规则',
                vpcId,
                id,
                isPrivate: this.data.get('context').isPrivate
            }
        });
        dialog.on('confirm', e => this.loadPage());
        dialog.attach(document.body);
    }

    onSearch() {
        this.loadPage();
    }

    // 删除私网NAT规则
    onDeletePrivate(row: any) {
        const dialog = new Confirm({
            data: {
                title: '删除前确认',
                content: `确认删除当前SNAT规则?`
            }
        });
        dialog.on('confirm', () => {
            let payload = {
                natId: this.data.get('context.id'),
                ruleIds: [row.ruleId]
            };
            this.$http.deletePrivateSnatRule(payload).then(() => this.loadPage());
        });
        dialog.attach(document.body);
    }

    // 查询私网SNAT列表
    privateSnatRuleList() {
        const {pager, order} = this.data.get('');
        const id = this.data.get('context.id');
        let payload = {
            natId: id,
            pageNo: pager.page,
            pageSize: pager.pageSize,
            ...order
        };
        this.$http
            .privateSnatRuleList(payload, {'x-silent-codes': ['IntranetNat.IntranetNatResourceNotExist']})
            .then(res => {
                this.privateSnatRuleQuota();
                this.data.set('table.datasource', res.result);
                this.data.set('pager.total', res.totalCount);
                this.data.set('table.loading', false);
            })
            .catch(() => {
                this.data.set('table.loading', false);
            });
    }

    // 查询私网nat snat规则配额
    privateSnatRuleQuota() {
        const natId = this.data.get('context.id');
        this.$http.privateSnatRuleQuota(natId, {'x-silent-codes': ['Exception']}).then((data: any) => {
            let {addPrivateSnat} = checker.check(rules, '', 'addPrivateSnat', {
                free: data.free
            });

            this.data.set('addPrivateSnat', addPrivateSnat);
        });
    }

    // 查询IPv6 NAT snat列表
    queryIPv6SnatRuleList() {
        const {pager, order} = this.data.get('');
        const id = this.data.get('context.id');
        let payload = {
            natId: id,
            pageNo: pager.page,
            pageSize: pager.pageSize,
            ...order
        };
        this.$http
            .queryIPv6SnatRuleList(payload)
            .then(res => {
                this.queryIPv6SnatRuleQuota();
                this.data.set('table.datasource', res.result);
                this.data.set('pager.total', res.totalCount);
                this.data.set('table.loading', false);
            })
            .catch(() => {
                this.data.set('table.loading', false);
            });
    }

    // 查询私网nat snat规则配额
    queryIPv6SnatRuleQuota() {
        const natId = this.data.get('context.id');
        this.$http.queryIPv6SnatRuleQuota(natId, {'x-silent-codes': ['Exception']}).then((data: any) => {
            let {addIPv6Snat} = checker.check(rules, '', 'addIPv6Snat', {
                free: data.free
            });

            this.data.set('addIPv6Snat', addIPv6Snat);
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(NatSnatList));
