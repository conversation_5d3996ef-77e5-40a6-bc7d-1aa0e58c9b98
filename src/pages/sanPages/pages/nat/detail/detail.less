/**
 * @file vpc/nat/detail/style.less
 * <AUTHOR>
 */
.inline-block {
    display: inline-block;
}
.page-sub-title() {
    display: block;
    color: #151b26;
    line-height: 24px;
    font-weight: 500;
    margin-bottom: 16px;
    font-size: 16px;
    // border-left: solid 4px #2468f2;
    // padding-left: 14px;
}
.vpc-nat-detail {
    width: 100%;
    padding: 24px;
    .content-item {
        &:not(:last-child) {
            border-bottom: 1px solid #ebebeb;
        }

        h4 {
            .page-sub-title();
        }

        .item-col {
            width: 30%;
            margin-bottom: 16px;
            vertical-align: text-top;
            .inline-block();

            .item-label {
                display: inline-block;
                vertical-align: top;
                color: #5e626a;
                margin-right: 16px;
                width: fit-content;
            }

            .item-content {
                color: #151a26;
                max-width: 80%;
                word-break: break-all;
                position: relative;
                .inline-tip {
                    position: relative;
                    top: 3px;
                    margin-left: 12px;
                    .s-tip-warning:hover path {
                        fill: #ff9326;
                    }
                }
                .change-config {
                    cursor: pointer;
                    font-family: PingFangSC-Regular;
                    font-size: 12px;
                    color: #2468f2;
                    line-height: 20px;
                    font-weight: 400;
                    margin-left: 8px;
                }
                .edit-detail-pop {
                    margin-left: 8px;
                }
            }
            .ip-content {
                word-break: break-word;
            }
        }
        .enhance-nat-wrapper {
            margin-top: 10px;
        }
    }
    .icon-copy {
        font-size: 12px;
        color: #2468f2;
    }
}
.locale-en {
    .vpc-nat-detail {
        .content-item .item-col .item-label {
            width: 150px;
        }
    }
}

.nat-detail-main-warp {
    width: 100%;
    .app-tab-page {
        padding: 0;
        background: #f7f7f7 !important;
        .bui-tab-header {
            border-right: 1px solid #ebebeb;
            border-bottom: none;
        }
        .skin-accordion {
            min-height: 540px !important;
        }
    }
    .instance-not-found-class {
        height: 100%;
    }
    .s-detail-page-content {
        margin: 0;
        .app-tab-page {
            padding: 16px;
            border-radius: 6px;
            background: #fff;
            .bui-tab {
                border-radius: 6px;
                border: none;
            }
        }
    }
    .space-header {
        height: 50px;
        display: flex;
        align-items: center;
        .status {
            margin-left: 10px;
        }
    }
    .backbox {
        margin-right: 5px;
        font-size: 16px;
    }
    .instance-name {
        font-size: 16px;
        font-weight: 500;
        color: #151b26;
        padding-right: 12px;
        padding-left: 16px;
    }
    .title_class {
        display: flex;
        align-items: center;
        .s-icon {
            font-size: 14px;
            color: #84868c;
        }
    }
}
