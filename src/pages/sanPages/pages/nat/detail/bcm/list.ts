/**
 * @file vpc/src/nat/bcm/List.js
 * <AUTHOR>
 */
import _ from 'lodash';
import moment from 'moment';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {BcmSDK} from '@baidu/new-bcm-sdk';
import {BcmChartPanel} from '@baidu/new-bcm-sdk-san';
import {Button, Select, DatePicker, Checkbox, Tooltip} from '@baidu/sui';
import {Tip} from '@baidu/sui-biz';
import {OutlinedRefresh} from '@baidu/sui-icon';

import {NatStatus} from '@/pages/sanPages/common/enum';
import monitorConfig from '@/pages/sanPages/utils/monitorConfig';
import './style.less';
import {kXhrOptions} from '@/pages/sanPages/utils/helper';
import testID from '@/testId';

const {natMetrics, serverMetrics, shortcutItems, natClusterMetrics} = monitorConfig;

const {asComponent} = decorators;

/* eslint-disable */
const template = html`
    <div data-test-id="${testID.nat.detailMonitor}">
        <div class="{{klass}}">
            <div class="content-item">
                <dl>
                    <dt><h4>NAT网关监控信息</h4></dt>
                    <dd>
                        <div class="alarm-info">
                            报警信息：
                            <span class="alarm-state">
                                <span class="status normal"></span>
                                {{natAlarm.okStateCount}}项状态正常
                            </span>
                            <span class="alarm-state">
                                <span class="status error"></span>
                                {{natAlarm.alarmStateCount}}项状态异常
                            </span>
                            <span class="alarm-state">
                                <span class="status warning"></span>
                                {{natAlarm.insufficientStateCount}}项数据不足
                            </span>
                            <s-button
                                class="alarm-detail"
                                track-id="ti_vpc_nat_monitor_detail"
                                tack-name="NAT网关/报警详情"
                                on-click="alarmDetail()"
                            >
                                报警详情
                            </s-button>
                        </div>
                    </dd>
                    <dd>
                        时间：
                        <s-date-range-picker
                            value="{=natTime.timeRange=}"
                            width="{{310}}"
                            shortcut="{{shortcutItems}}"
                            range="{{range}}"
                            mode="second"
                            on-change="onTimeChange('natTime', $event)"
                        />
                        <s-button
                            class="s-icon-button"
                            on-click="onTimeRefresh('nat')"
                            track-id="ti_vpc_nat_bcm_refresh"
                            track-name="NAT网关监控信息/刷新"
                        >
                            <outlined-refresh class="icon-class" />
                        </s-button>
                    </dd>
                    <div class="nat-monitor-trends">
                        <div class="monitor-trend-box" s-for="item,index in natChart">
                            <bcm-chart-panel
                                s-ref="nat-alarm-chart-{{index}}"
                                withFilter="{{false}}"
                                scope="{{item.scope}}"
                                dimensions="{{item.dimensions}}"
                                statistics="{{item.statistics}}"
                                title="{{item.title}}"
                                api-type="metricName"
                                options="{{options}}"
                                startTime="{=natTime.startTime=}"
                                endTime="{=natTime.endTime=}"
                                period="{{natTime.monitorDefaultPeriod}}"
                                metrics="{{item.metrics}}"
                                unit="{{item.unit}}"
                                bitUnit="{{item.bitUnit}}"
                                width="{{'auto'}}"
                                height="{{230}}"
                                sdk="{{bcmSdk}}"
                            >
                            </bcm-chart-panel>
                        </div>
                    </div>
                </dl>
                <dl>
                    <dt><h4>后端实例监控信息</h4></dt>
                    <dd class="vpc-nat-bcm-server">
                        <div>
                            时间范围：
                            <s-tooltip
                                trigger="{{!natServer.datasource.length ? 'hover' : ''}}"
                                content="当前无数据，系统仅保留近30天数据。"
                            >
                                <s-date-range-picker
                                    class="picker_class"
                                    disabled="{{!natServer.datasource.length}}"
                                    value="{=serverTime.timeRange=}"
                                    width="{{310}}"
                                    shortcut="{{shortcutItems}}"
                                    range="{{range}}"
                                    on-change="onTimeChange('serverTime', $event)"
                                    mode="second"
                                />
                            </s-tooltip>
                        </div>
                        <div>
                            统计方式：
                            <s-select
                                datasource="{{statisticsDatasource}}"
                                value="{=statistics=}"
                                track-id="ti_vpc_nat_bcm_server"
                                track-name="后端实例监控统计方式"
                                disabled="{{!natServer.datasource.length}}"
                            />
                        </div>
                        <div class="server-compute">
                            后端实例：
                            <s-select
                                datasource="{{natServer.datasource}}"
                                value="{=natServer.value=}"
                                disabled="{{!natServer.datasource.length}}"
                                width="200"
                                multiple
                                filterable
                                checkAll="{{!(!displayTopN && natServer.datasource.length > 10)}}"
                                track-id="ti_vpc_nat_bcm_server"
                                track-name="后端实例"
                            />
                            <s-tip
                                skin="question"
                                content="{{natServerTip}}"
                                class="warn_class"
                                layer-width="300"
                                track-id="ti_vpc_nat_bcm_server_tip"
                                track-name="后端实例监控信息/提示"
                            />
                        </div>
                        <template>
                            <div class="nat-display-top">
                                <s-checkbox checked="{=displayTopN=}" label="{{'显示TOPN' | i18n}}" />
                            </div>
                            <div s-if="{{displayTopN}}">
                                监控项：
                                <s-select
                                    datasource="{{natServerMetrics}}"
                                    value="{=natServerMetric=}"
                                    width="100"
                                    track-id="ti_vpc_nat_bcm_server"
                                    track-name="后端实例指标项"
                                />
                            </div>
                        </template>
                        <div>
                            <s-button
                                class="s-icon-button"
                                disabled="{{!natServer.datasource.length}}"
                                on-click="onTimeRefresh('server')"
                                track-id="ti_vpc_nat_bcm_refresh"
                                track-name="NAT网关监控信息/刷新"
                            >
                                <outlined-refresh />
                            </s-button>
                        </div>
                    </dd>
                    <div class="nat-monitor-trends nat-topn-show">
                        <div class="monitor-trend-box" s-for="item,index in serverChart">
                            <bcm-chart-panel
                                s-ref="server-alarm-chart-{{index}}"
                                withFilter="{{false}}"
                                scope="{{item.scope}}"
                                dimensions="{{item.dimensions}}"
                                statistics="{{item.statistics}}"
                                title="{{item.title}}"
                                api-type="dimensions"
                                options="{{options}}"
                                startTime="{=serverTime.startTime=}"
                                endTime="{=serverTime.endTime=}"
                                period="{{serverTime.monitorDefaultPeriod}}"
                                metrics="{{item.metrics}}"
                                unit="{{item.unit}}"
                                bitUnit="{{item.bitUnit}}"
                                width="{{'auto'}}"
                                height="{{230}}"
                                tooltipSeriesNameWidth="{{200}}"
                                sdk="{{bcmSdk}}"
                                proccessor="{{proccessor}}"
                            >
                            </bcm-chart-panel>
                        </div>
                    </div>
                </dl>
            </div>
        </div>
    </div>
`;
/* eslint-enable */
@asComponent('@nat-monitor')
class NatBcm extends Component {
    static template = template;
    static components = {
        's-button': Button,
        's-select': Select,
        's-tip': Tip,
        's-date-range-picker': DatePicker.DateRangePicker,
        'bcm-chart-panel': BcmChartPanel,
        's-checkbox': Checkbox,
        'outlined-refresh': OutlinedRefresh,
        's-tooltip': Tooltip
    };
    initData() {
        return {
            klass: 'vpc-nat-bcm-list',
            withSidebar: true,
            instance: {},
            natAlarm: {},
            config: [],
            current: 'monitor',
            options: {
                color: [
                    '#2468f2',
                    '#5FB333',
                    '#096',
                    '#FFDE33',
                    '#FF9933',
                    '#CC0033',
                    '#660099',
                    '#7E0023',
                    '#2F4554',
                    '#61A0A8'
                ],
                legend: {
                    x: 'right',
                    y: 'top'
                },
                dataZoom: {start: 0}
            },
            natServer: {
                value: [],
                datasource: []
            },
            range: {
                begin: new Date(moment().subtract(40, 'day').valueOf()),
                end: new Date(moment().valueOf())
            },
            natTime: {
                timeRange: {
                    begin: new Date(moment().subtract(1, 'hour').valueOf()),
                    end: new Date(moment().valueOf())
                },
                startTime: moment().subtract(1, 'hour').utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
                endTime: moment().utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
                monitorDefaultPeriod: 60
            },
            serverTime: {
                timeRange: {
                    begin: new Date(moment().subtract(30, 'day').valueOf()),
                    end: new Date(moment().valueOf())
                },
                startTime: moment().subtract(30, 'day').utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
                endTime: moment().utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
                monitorDefaultPeriod: 60
            },
            endOriginTime: moment().valueOf(),
            bcmSdk: new BcmSDK({client: window.$http, context: window.$context}),
            shortcutItems,
            statistics: 'average',
            statisticsDatasource: [
                {
                    text: '平均值',
                    value: 'average'
                },
                {
                    text: '最大值',
                    value: 'maximum'
                },
                {
                    text: '最小值',
                    value: 'minimum'
                },
                {
                    text: '和值',
                    value: 'sum'
                }
            ],
            displayTopN: false,
            natServerMetrics: [
                {
                    text: '入流量',
                    value: 'InBytes'
                },
                {
                    text: '出流量',
                    value: 'OutBytes'
                },
                {
                    text: '入带宽',
                    value: 'InBandwith'
                },
                {
                    text: '出带宽',
                    value: 'OutBandwith'
                },
                {
                    text: '入包速率',
                    value: 'InPps'
                },
                {
                    text: '出包速率',
                    value: 'OutPps'
                },
                {
                    text: '连接数',
                    value: 'ConnNumber'
                }
            ],
            natServerMetric: 'OutBytes',
            proccessor: this.proccessor.bind(this)
        };
    }

    static computed = {
        natServerTip() {
            if (this.data.get('displayTopN')) {
                return '最多选择10个监控对象';
            }
            // eslint-disable-next-line
            return '后端实例数量少于10个时，可以展示所有后端实例监控信息。超过10个时，用户可自定义选择需要展示的后端实例监控信息，最多可选10个。';
        },
        isPrivate() {
            const params = getQueryParams(location?.href);
            return params?.natType === 'private';
        }
    };

    inited() {
        ['natTime', 'serverTime'].forEach(it => {
            this.watch(`${it}.timeRange`, timeRange => {
                this.onTimeChange(it, {value: timeRange});
            });
        });
    }
    attached() {
        this.loadNatDetail().then(() => {
            this.loadNatAlarmInfo();
            this.loadNatMetrics();
        });
        this.watch('natServer.value', value => {
            if (!this.data.get('displayTopN')) {
                this.updateNatserverDisable(value);
            }
            this.loadNatServerMetrics();
        });
        this.loadNatServerList();
        this.watch('statistics', () => {
            if (this.data.get('displayTopN')) {
                this.getServerNatWhite();
            } else {
                this.loadNatServerMetrics();
            }
        });
        this.watch('displayTopN', val => {
            if (val) {
                this.getServerNatWhite();
            } else {
                const natServers = this.data.get('natServers');
                this.data.set('natServer.datasource', natServers);
                let natServerDatasource = [];
                if (natServers.length > 0) {
                    natServerDatasource = [natServers[0].value];
                }
                this.data.set('natServer.value', natServerDatasource);
            }
        });
        this.watch('natServerMetric', () => {
            if (this.data.get('displayTopN')) {
                this.getServerNatWhite();
            }
        });
    }
    proccessor(data) {
        const statistics = this.data.get('statistics');
        if (data && data.series && data.series.length > 0) {
            data.series.forEach(series => {
                let ipData = series.name.split(',')[1];
                let simpleItem = this.data.get('simpleServers').find(item => item.internalIp === ipData);
                let eniItem;
                // 展示弹性网卡的id和ip
                if (this.data.get('displayTopN') && this.data.get('topnEniIpList')) {
                    eniItem = this.data.get('topnEniIpList').find(item => item.ip === ipData);
                }
                if (simpleItem) {
                    series.name = ipData + ',' + simpleItem.id;
                } else if (eniItem) {
                    series.name = eniItem.id ? `${eniItem.ip},${eniItem.id}` : eniItem.ip;
                } else {
                    series.name = series.name;
                }
                series.data = series.data.map(item => {
                    if (!item || Object.keys(item).length === 0) {
                        let obj = {};
                        obj[statistics] = 0;
                        return obj;
                    }
                    return item;
                });
            });
        }
        return data;
    }
    updateNatserverDisable(value) {
        if (value.length < 10) {
            this.data.set('natServer.datasource', this.data.get('natServers'));
            return;
        }
        const datasource = _.cloneDeep(this.data.get('natServer.datasource'));
        for (let i = 0; i < value.length; i++) {
            for (let j = 0; j < datasource.length; j++) {
                if (datasource[j].value === value[i]) {
                    datasource[j].flag = true;
                }
            }
        }
        for (let i = 0; i < datasource.length; i++) {
            const item = {...datasource[i]};
            const flag = item.flag || false;
            delete item.flag;
            if (!flag) {
                item.disabled = true;
            }
            datasource[i] = {...item};
        }
        this.data.set('natServer.datasource', [...datasource]);
    }

    onTimeRefresh(type: 'nat' | 'server') {
        const typeMapMonitor = {
            nat: 'natTime',
            server: 'serverTime'
        };
        if (this.data.get(`${typeMapMonitor[type]}.timeRange.end`).valueOf() >= this.data.get('endOriginTime')) {
            this.data.set(`${typeMapMonitor[type]}.timeRange.end`, new Date(moment().valueOf()));
        } else {
            this.onRefresh(type);
        }
    }
    onTimeChange(type, {value}) {
        const key = {
            natTime: 'nat',
            serverTime: 'server'
        };
        let startTime = moment(value.begin).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        let endTime = moment(value.end).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        let ms = moment(value.end) - moment(value.begin);
        let monitorDefaultPeriod = Math.round(ms / 3600000) * 60 || 60;
        this.data.set(`${type}.startTime`, startTime);
        this.data.set(`${type}.endTime`, endTime);
        this.data.set(`${type}.monitorDefaultPeriod`, monitorDefaultPeriod);
        if (type === 'serverTime' && this.data.get('displayTopN')) {
            this.getServerNatWhite();
        } else {
            this.nextTick(() => {
                this.onRefresh(key[type]);
            });
        }
    }
    alarmDetail() {
        const region = window.$context.getCurrentRegion().id;
        redirect(
            `/bcm/#/bcm/alarm/rule/list~scope=BCE_NAT&region=${region}&dimensions=NatId:${this.data.get('context').id};FixIp:0.0.0.0`
        );
    }
    loadNatDetail() {
        const isPrivate = this.data.get('isPrivate');
        const {ipVersion} = this.data.get('context')?.natInfo;
        const isEnhancedIPv6 = ipVersion === 'v6';
        const paramKey = isPrivate || isEnhancedIPv6 ? 'natId' : 'natGatewayId';
        const reqUrl = isPrivate ? 'getPrivateNatDetail' : isEnhancedIPv6 ? 'getIPv6NatDetail' : 'getNatList';
        const payload: any = {[paramKey]: this.data.get('context').id};
        return this.$http[reqUrl](payload).then(data => {
            let result = isPrivate || isEnhancedIPv6 ? data : data.result ? data.result[0] : {};
            this.data.set('detail.name', result.name);
            const config = NatStatus.fromValue(result.status);
            this.data.set('detail.styleClass', config.styleClass);
            this.data.set('detail.statusText', config.text);
            this.data.set('instance', result);
        });
    }
    onRefresh(type) {
        let chartConfig = type === 'nat' ? this.data.get('natChart') : this.data.get('serverChart');
        _.map(chartConfig, (item, i) => {
            this.ref(`${type}-alarm-chart-${i}`).loadMetrics();
        });
    }
    loadNatMetrics() {
        let chartConfig = [];
        let instance = this.data.get('instance');
        const isPrivate = this.data.get('isPrivate');
        const statistics = 'maximum';
        let natMetricsConfig = _.cloneDeep(natMetrics);
        if (instance.clusterMode || isPrivate) {
            natMetricsConfig.conn.title = '并发连接数';
            natMetricsConfig.conn.metrics = `ConnNumber(${'并发连接数'})`;
            Object.assign(natMetricsConfig, natClusterMetrics);
        } else {
            natMetricsConfig = natMetrics;
        }
        _.each(natMetricsConfig, item => {
            let config = {
                scope: 'BCE_NAT',
                period: 60,
                statistics,
                title: item.title,
                unit: item.unit,
                bitUnit: item.bitUnit,
                metrics: item.metrics,
                dimensions: 'NatId:' + this.data.get('context').id + ';FixIp:0.0.0.0'
            };
            chartConfig.push(config);
        });
        this.data.set('natChart', chartConfig);
    }
    loadNatServerMetrics() {
        let chartConfig = [];
        let dimensions = [];
        const statistics = this.data.get('statistics');
        _.each(this.data.get('natServer.value'), value => {
            if (dimensions.length < 10) {
                dimensions.push('NatId:' + this.data.get('context').id + ';FixIp:' + value);
            }
        });
        _.each(serverMetrics, item => {
            let config = {
                scope: 'BCE_NAT',
                period: 60,
                statistics,
                title: item.title,
                unit: item.unit,
                bitUnit: item.bitUnit,
                metrics: item.metrics,
                dimensions
            };
            chartConfig.push(config);
        });
        this.data.set('serverChart', chartConfig);
        this.nextTick(() => {
            this.onRefresh('server');
        });
    }
    loadNatAlarmInfo() {
        this.$http
            .getAlarmSummary({
                dimensions: 'NatId:' + this.data.get('context').id + ';FixIp:0.0.0.0',
                scope: 'BCE_NAT'
            })
            .then(data => this.data.set('natAlarm', data));
    }

    getDimensionsV3FixIp() {
        return this.$http.getBcmDimensionsV3({
            userId: window.$context.getUserId(),
            metricName: 'InBytes',
            labels: ['FixIp'],
            region: window.$context.getCurrentRegionId(),
            scope: 'BCE_NAT',
            dimensions: {NatId: [`${this.data.get('context').id}`]},
            startTime: this.data.get('serverTime.timeRange.begin'),
            endTime: this.data.get('serverTime.timeRange.end')
        });
    }
    loadNatServerList() {
        return this.getDimensionsV3FixIp().then(data => {
            let result = [];
            _.each(data?.result?.FixIp || [], item => {
                const ip = item;
                result.push({
                    value: ip,
                    text: item.id ? `${ip}(${item.id})` : `${ip}`
                });
            });
            this.data.set('simpleServers', data?.simpleServers || []);
            this.data.set('natServers', result);
            this.data.set('natServer.datasource', result);
            if (result.length > 0) {
                this.data.set('natServer.value', [result[0].value]);
            } else {
                this.data.set('natServer.value', []);
            }
        });
    }
    async getServerNatWhite() {
        const {
            statistics,
            serverTime: {startTime, endTime},
            natServerMetric
        } = this.data.get();
        const id = this.data.get('context').id;
        const dimension = `NatId:${id}`;
        const region = this.$context.getCurrentRegion().id;
        const params = {
            statistics,
            startTime,
            endTime,
            topNum: 10,
            dimension,
            service: 'BCE_NAT',
            region,
            userId: this.$context.getUserId(),
            metricName: natServerMetric
        };
        const [{topDatas} = {topDatas: []}] = await this.$http.getServerNatWhite(params);
        const topNServer = topDatas.map(item => item.dimensions.filter(dimens => dimens.name === 'FixIp')[0].value);
        let eniIpList = [];
        if (topNServer.length) {
            // 根据topN接口的ip去查那些是弹性网卡的并展示
            const res = await this.$http.getNatIpInstanceList({
                ips: topNServer,
                vpcUuid: this.data.get('context').vpcId
            });
            if (res.instances.length > 0) {
                eniIpList = res.instances.filter(item => item.type === 'enic');
                this.data.set('topnEniIpList', eniIpList);
            } else {
                this.data.set('topnEniIpList', []);
            }
        }
        let top = [];
        let natServers = this.data.get('natServers');
        topNServer.forEach(item => {
            let serverItem = natServers.find(server => server.value === item);
            serverItem && top.push(serverItem);
        });
        // 将查到的弹性网卡合并
        if (topNServer.length && eniIpList.length) {
            eniIpList.forEach(item => {
                top.push({
                    value: item.ip,
                    text: item.id ? `${item.ip}(${item.id})` : `${item.ip}`
                });
            });
        }
        this.data.set('natServer.datasource', top);
        let natServerDatasource = [];
        if (top.length > 0) {
            natServerDatasource = top.map(item => item.value);
        }
        this.data.set('natServer.value', natServerDatasource);
    }
}
export default San2React(Processor.autowireUnCheckCmpt(NatBcm));
