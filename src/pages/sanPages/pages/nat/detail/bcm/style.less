.inline-block {
    display: inline-block;
}
.vpc-nat-bcm-list {
    background-color: #ffffff;
    padding: 24px;
    position: relative;
    .content-item {
        &:not(:last-child) {
            border-bottom: 1px solid #ebebeb;
        }

        dl {
            dt {
                h4 {
                    display: block;
                    color: #151b26;
                    line-height: 24px;
                    font-weight: 500;
                    margin-bottom: 16px;
                    font-size: 16px;
                }
            }

            &:last-child {
                margin-top: 20px;
            }
        }
    }

    .alarm-info {
        padding: 0;
        .alarm-state {
            margin-left: 4px;
            margin-right: 6px;
            span {
                margin: 0;
            }
        }
        .inline-block();
        vertical-align: middle;

        .alarm-detail {
            float: none;
            margin-left: 10px;
        }
    }

    .refresh-button {
        margin-left: 10px;
    }

    .monitor-trends {
        margin: 0;
    }

    .ui-bcmchart-x {
        display: block;
        border-radius: 0;
        margin-bottom: 0;
    }

    dd {
        display: table;
        table-layout: fixed;
        width: 100%;
        margin: 10px 0 0 0;
        line-height: 24px;
    }

    .bui-tip {
        margin-left: 5px;
        color: #999 !important;
    }
    .nat-monitor-trends {
        display: flex;
        flex-wrap: wrap;
        // max-height: calc(~'100vh - 400px');
        .monitor-trend-box {
            width: 49%;
            margin-top: 10px;
            .ui-bcmchart {
                width: 100%;
            }
        }
    }
    .picker_class {
        .s-input-area {
            display: inline-flex;
        }
    }
    .nat-topn-show {
        .monitor-trend-box {
            .box {
                .chart {
                    .bui-chart {
                        div:nth-child(2) {
                            width: 310px;
                        }
                    }
                }
            }
        }
    }
    .server-compute {
        display: flex;
        align-items: center;
        .s-tip {
            margin-left: 10px;
        }
    }
    .warn_class {
        .s-tip-warning {
            position: relative;
            top: 3px;
        }
    }

    .s-picker-foot {
        padding: 8px;
    }
    .s-daterangepicker-disabled {
        input {
            &:hover {
                cursor: not-allowed;
            }
        }
    }
    .s-daterangepicker-popup {
        width: 382px;
    }
    .nat-display-top {
        .s-checkbox {
            display: flex;
            align-items: center;
        }
    }
}

dd.vpc-nat-bcm-server {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    line-height: 40px;
    & > div:nth-child(n + 2) {
        margin-left: 20px;
    }
}
