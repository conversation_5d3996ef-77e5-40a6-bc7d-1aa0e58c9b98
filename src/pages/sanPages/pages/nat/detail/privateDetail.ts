/* eslint-disable @typescript-eslint/member-ordering */
/**
 * @file vpc/nat/detail/Detail.js
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component, defineComponent} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {Icon} from '@baidu/sui';
import {ClipBoard} from '@baidu/sui-biz';

import {toTime, $flag as FLAG} from '@/pages/sanPages/utils/helper';
import {NatStatus} from '@/pages/sanPages/common/enum';
import Rule from '@/pages/sanPages/utils/rule';
import zone from '@/pages/sanPages/utils/zone';
import testID from '@/testId';
import './detail.less';

const {service, asComponent, invokeComp, template} = decorators;
/* eslint-disable */
const tpl = html` <div>
    <div class="{{klass}}">
        <div class="content-item">
            <dl>
                <dt><h4>基本信息</h4></dt>
                <span class="item-col">
                    <label class="item-label">实例名称：</label>
                    <span class="item-content">
                        {{instance.name}}
                        <edit-popover
                            s-if="isCanUpdate"
                            value="{=instance.name=}"
                            rule="{{Rule.NAME}}"
                            on-edit="updateName"
                        >
                            <a href="javascript:void(0)" data-testid="${testID.nat.detailName}">变更</a>
                        </edit-popover>
                    </span>
                </span>
                <span class="item-col">
                    <label class="item-label">ID：</label>
                    <span class="item-content">
                        <span>{{instance.natId}}</span>
                        <s-clip-board text="{{instance.natId}}">
                            <s-icon name="copy" />
                        </s-clip-board>
                    </span>
                </span>
                <span class="item-col">
                    <label class="item-label">运行状态：</label>
                    <span class="item-content">
                        <span class="{{instance.status | statusStyle}}"> {{instance.status | statusText}} </span>
                    </span>
                </span>
                <span class="item-col">
                    <label class="item-label">可用区：</label>
                    <span class="item-content"> {{instance.az | getZoneLabel}} </span>
                </span>
                <span class="item-col">
                    <label class="item-label">性能容量：</label>
                    <span class="item-content ip-content">
                        {{instance.cuNum | getCuNumText}}
                        <span
                            s-if="isCanChangeCu"
                            on-click="handleChangeConfig"
                            class="change-config"
                            data-testid="${testID.nat.detailPriChangeCu}"
                            >变配</span
                        >
                    </span>
                </span>
                <span class="item-col">
                    <label class="item-label">子网：</label>
                    <span class="item-content">
                        <span>{{instance.subnetId}}</span>
                        <s-clip-board text="{{instance.subnetId}}">
                            <s-icon name="copy" />
                        </s-clip-board>
                    </span>
                </span>
                <span class="item-col">
                    <label class="item-label">所在网络：</label>
                    <span class="item-content">
                        {{vpcInfo.name}}<span s-f="vpcInfo.cidr">（{{vpcInfo.cidr}}）</span>
                    </span>
                </span>
                <span class="item-col">
                    <label class="item-label">描述：</label>
                    <span class="item-content">
                        {{instance.description || '-'}}
                        <edit-popover
                            s-if="isCanUpdate"
                            value="{=instance.description=}"
                            rule="{{Rule.DESC}}"
                            on-edit="updateDesc"
                        >
                            <a href="javascript:void(0)">修改</a>
                        </edit-popover>
                    </span>
                </span>
            </dl>
            <dl class="enhance-nat-wrapper">
                <dt><h4>网关性能</h4></dt>
                <span class="item-col">
                    <label class="item-label">新建连接数：</label>
                    <span class="item-content">
                        <span>{{instance.cuNum ? instance.cuNum * 1000 + '个/秒' : '-'}}</span>
                    </span>
                </span>
                <span class="item-col">
                    <label class="item-label">并发连接数：</label>
                    <span class="item-content">
                        <span>{{instance.cuNum ? instance.cuNum * 10000 + '个' : '-'}}</span>
                    </span>
                </span>
                <span class="item-col">
                    <label class="item-label">处理流量：</label>
                    <span class="item-content">
                        <span>{{instance.cuNum ? instance.cuNum + 'Gbps' : '-'}}</span>
                    </span>
                </span>
            </dl>
        </div>
    </div>
</div>`;
/* eslint-enable */
@template(tpl)
@asComponent('@private-nat-detail')
@invokeComp('@edit-popover')
class NatDetail extends Component {
    static components = {
        's-icon': Icon,
        's-clip-board': ClipBoard
    };
    static filters = {
        statusStyle(status) {
            let config = NatStatus.fromValue(status);
            return config ? config.styleClass : '';
        },
        statusText(status) {
            let config = NatStatus.fromValue(status);
            return config ? config.text : '';
        },
        getCuNumText(cuNum) {
            return cuNum ? cuNum + 'CU' : '-';
        },
        getZoneLabel(az) {
            return zone.getLabel(az);
        }
    };
    static computed = {
        isCanUpdate() {
            const instance = this.data.get('context').instance || {};
            let flag = true;
            if (_.contains([NatStatus.BUILDING, NatStatus.DELETING, NatStatus.CONFIGURING], instance?.status)) {
                flag = false;
            }
            return flag;
        },
        isCanChangeCu() {
            const instance = this.data.get('context').instance || {};
            let flag = true;
            if (!_.contains([NatStatus.ACTIVE], instance?.status)) {
                flag = false;
            }
            return flag;
        }
    };
    initData() {
        return {
            klass: 'vpc-nat-detail',
            withSidebar: true,
            config: [],
            current: 'detail',
            flag: FLAG,
            Rule: Rule.DETAIL_EDIT
        };
    }
    inited() {
        this.children = [];
        this.data.set('instance', this.data.get('context').instance || {});
    }
    attached() {
        this.loadVpcDetail();
    }
    loadVpcDetail() {
        const vpcId = this.data.get('context').vpcId;
        return this.$http
            .getVpcDetail({
                vpcIds: [vpcId]
            })
            .then(data => {
                const result = data[vpcId] || {};
                this.data.set('vpcInfo', result);
            });
    }
    updateName(value) {
        this.$http
            .updatePrivateNat({
                natId: this.data.get('instance.natId'),
                name: value
            })
            .then(() => {
                this.fire('updateName', value);
            });
    }
    updateDesc(value) {
        this.$http
            .updatePrivateNat({
                natId: this.data.get('instance.natId'),
                description: value
            })
            .then(() => {
                this.data.set('instance.description', value);
            });
    }
    handleChangeConfig() {
        const {vpcId} = this.data.get('vpcInfo');
        const {natId} = this.data.get('instance');
        location.hash = `#/vpc/nat/upgrade?vpcId=${vpcId}&id=${natId}&from=private`;
    }
}
export default San2React(Processor.autowireUnCheckCmpt(NatDetail));
