/**
 * @file NAT操作禁用配置
 * <AUTHOR>
 */
import u from 'lodash';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';

export default {
    createCidr: [
        {
            required: false
        },
        {
            custom(data, options) {
                if (options.noneCidrQuota) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: '当前nat网关下地址段配额不足'
                        };
                    }
                    return {
                        disable: true,
                        message:
                            `当前nat网关下地址段配额不足，请<a href="/quota_center/#/quota/apply/create?serviceType=NAT&region=${window?.$context?.getCurrentRegionId()}&cloudCenterQuotaName=IntranetNatSegmentQuota" target="_blank">申请配额</a>`
                    };
                }
            }
        }
    ],
    createNatIp: [
        {
            required: false
        },
        {
            custom(data, options) {
                if (options.noneNatIpQuota) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: '当前地址段下NAT IP配额不足'
                        };
                    }
                    return {
                        disable: true,
                        message:
                            `当前地址段下NAT IP配额不足，请<a href="/quota_center/#/quota/apply/create?serviceType=NAT&region=${window?.$context?.getCurrentRegionId()}&cloudCenterQuotaName=IntranetNatIpQuota" target="_blank">申请配额</a>`
                    };
                }
            }
        }
    ],
    deleteNatIp: [
        {
            required: true,
            message: '请先选中NAT IP'
        },
        {
            custom(data) {
                if (u.some(data, item => item.default)) {
                    return {
                        disable: true,
                        message: '默认NAT IP不支持删除'
                    };
                }
            }
        }
    ]
};
