/* eslint-disable @typescript-eslint/member-ordering */
import _ from 'lodash';
import {Component} from 'san';
import {Notification} from '@baidu/sui';
import {OutlinedRefresh, OutlinedEditingSquare} from '@baidu/sui-icon';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {Plus1, More, Search} from '@baidu/xicon-san';
import RULES from './rules';
import CreateCidr from './createCidr';
import CreateNatIp from './create';
import {columns} from './tableField';
import {confirmValidate, $flag as FLAG} from '@/pages/sanPages/utils/helper';
import {NatStatus} from '@/pages/sanPages/common/enum';
import testID from '@/testId';

import './style.less';

const {template, asComponent, invokeComp, invokeSUI, invokeSUIBIZ, invokeAppComp} = decorators;
const tpl = html`
    <div class="nat-ip-widget" data-testid="${testID.nat.detailNatIP}">
        <s-loading loading="{{cidrLoading}}">
            <div class="ip-left-nav">
                <div class="title">
                    <span class="title-text">地址段列表</span>
                    <s-popover class="nat-ip-cidr">
                        <!--bca-disable-next-line-->
                        <span slot="content">{{(unEditTip || noneCidrQuotaTip) | raw}}</span>
                        <s-plus1
                            on-click="handleAddCidr"
                            class="nat-ip-plus"
                            theme="line"
                            size="{{16}}"
                            color="{{addCidrIconColor}}"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        />
                    </s-popover>
                </div>
                <div class="input-widget">
                    <s-input
                        on-change="handleSearchChange($event)"
                        on-enter="handleSearch($event)"
                        placeholder="请输入名称"
                    >
                        <s-search-icon
                            on-click="handleSearch"
                            slot="suffix"
                            theme="line"
                            size="{{16}}"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        />;
                    </s-input>
                </div>
                <div class="cidr-widget">
                    <div
                        s-for="item in cidrDataSource"
                        class="cidr-item {{item.segmentId === segmentId ? 'cidr-selected' : ''}}"
                        key="{{item.cidr}}"
                        on-click="handleSelectCidr(item)"
                    >
                        <div class="cidr-item-wrapper">
                            <div class="cidr-value common">
                                <span class="cidr-span">{{item.cidr}}</span>
                                <span s-if="item.type === 'default'" class="default-mark">默认</span>
                            </div>
                            <div class="cidr-name common">{{item.name}}</div>
                        </div>
                        <s-popover s-if="item.type !== 'default'" class="popover-wrapper" placement="bottom">
                            <div slot="content">
                                <div class="cidr-opt">
                                    <s-button skin="stringfy" on-click="handleAction(item, 'edit')">编辑</s-button>
                                </div>
                                <div class="cidr-opt">
                                    <s-tip-button
                                        disabled="{{disableDeleteCidr}}"
                                        skin="stringfy"
                                        isDisabledVisibile
                                        on-click="handleAction(item, 'delete')"
                                    >
                                        <span s-if="disableDeleteCidr" slot="content">请先删除该地址段下的NAT IP</span>
                                        删除
                                    </s-tip-button>
                                </div>
                            </div>
                            <s-more
                                s-if="item.segmentId === segmentId && !unEditTip"
                                class="nat-ip-more"
                                theme="line"
                                color="#151B26"
                                size="{{16}}"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                            />
                        </s-popover>
                    </div>
                </div>
            </div>
        </s-loading>
        <div class="ip-right-content">
            <s-app-list-page class="{{klass}}">
                <div slot="bulk" class="nat-list-table">
                    <s-tip-button
                        skin="primary"
                        isDisabledVisibile
                        on-click="handleCreateNatIp"
                        track-id="vpc_nat_create"
                        disabled="{{unEditTip || noneNatIpQuota.disable || cidrLoading || table.loading}}"
                    >
                        <!--bca-disable-next-line-->
                        <div slot="content">{{(unEditTip || noneNatIpQuota.message) | raw}}</div>
                        <outlined-plus />
                        创建NAT IP
                    </s-tip-button>
                    <s-tooltip
                        trigger="{{unEditTip || deleteNatIp.disable ? 'hover' : ''}}"
                        placement="top"
                        class="left_class"
                    >
                        <!--bca-disable-next-line-->
                        <div slot="content">{{unEditTip || deleteNatIp.message | raw}}</div>
                        <s-button
                            on-click="handleDeleteNatIp"
                            disabled="{{unEditTip || deleteNatIp.disable || deleting}}"
                            track-id="ti_vpc_instance_delete"
                            track-name="删除"
                            >删除</s-button
                        >
                    </s-tooltip>
                </div>
                <div slot="filter">
                    <div class="nat-buttons-wrap">
                        <s-search
                            width="{{230}}"
                            class="search-warp"
                            value="{=searchbox.keyword=}"
                            placeholder="{{searchbox.placeholder}}"
                            on-search="onSearch"
                        >
                            <s-select
                                slot="options"
                                width="120"
                                datasource="{{searchbox.keywordTypes}}"
                                value="{=searchbox.keywordType=}"
                                on-change="onSearchboxChange($event)"
                            >
                            </s-select>
                        </s-search>
                        <s-button on-click="handleRefresh" class="s-icon-button left_class" track-name="刷新"
                            ><outlined-refresh class="icon-class"
                        /></s-button>
                    </div>
                </div>
                <s-table
                    columns="{{table.columns}}"
                    loading="{{table.loading}}"
                    error="{{table.error}}"
                    datasource="{{table.datasource}}"
                    on-selected-change="tableSelected($event)"
                    selection="{=table.selection=}"
                    on-filter="onFilter"
                    on-sort="onSort"
                    track-id="ti_vpc_instance_table"
                    track-name="列表操作"
                >
                    <div slot="empty">
                        <table-empty
                            desc="{{'暂无NAT IP。'}}"
                            actionAuth="{{{disable: unEditTip}}}"
                            actionText="立即创建"
                            on-click="handleCreateNatIp"
                        />
                    </div>
                    <div slot="c-name" class="nat-ip-name">
                        <span> {{row.name || '-'}} </span>
                        <span s-if="row.default" class="default-mark">默认</span>
                        <s-popover
                            s-ref="popover-name-{{rowIndex}}"
                            placement="top"
                            trigger="click"
                            class="edit-popover-class"
                        >
                            <div class="edit-wrap" slot="content">
                                <s-input
                                    value="{=edit.name.value=}"
                                    width="320"
                                    placeholder="请输入名称"
                                    on-input="onEditInput($event, rowIndex, 'name')"
                                />
                                <div class="edit-tip">大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65</div>
                                <s-button
                                    skin="primary"
                                    s-ref="editBtn-name-{{rowIndex}}"
                                    disabled="{{true}}"
                                    on-click="editConfirm(row, rowIndex, 'name')"
                                    >确定</s-button
                                >
                                <s-button on-click="editCancel(rowIndex, 'name')">取消</s-button>
                            </div>
                            <outlined-editing-square
                                class="name-icon"
                                on-click="onInstantEdit(row, rowIndex, 'name')"
                            />
                        </s-popover>
                    </div>
                    <div slot="c-opt">
                        <s-tooltip trigger="{{unEditTip || row.default || row.disableFlag ? 'hover' : ''}}">
                            <span slot="content">{{row | disableDeleteTip}}</span>
                            <s-button
                                disabled="{{row | disableDeleteTip}}"
                                skin="stringfy"
                                on-click="handleDeleteNatIp(row)"
                                >删除</s-button
                            >
                        </s-tooltip>
                    </div>
                </s-table>
                <s-pagination
                    s-if="{{pager.total}}"
                    slot="pager"
                    layout="{{'total, pageSize, pager, go'}}"
                    pageSize="{{pager.pageSize}}"
                    total="{{pager.total}}"
                    page="{{pager.page}}"
                    on-pagerChange="onPagerChange"
                    on-pagerSizeChange="onPagerSizeChange"
                />
            </s-app-list-page>
        </div>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@asComponent('@nat-ip-list')
@invokeComp('@table-empty', '@create-cidr')
class NatIpList extends Component {
    static components = {
        'outlined-refresh': OutlinedRefresh,
        's-plus1': Plus1,
        's-more': More,
        's-search-icon': Search,
        'outlined-editing-square': OutlinedEditingSquare
    };
    initData() {
        return {
            klass: 'nat-ip-list',
            pager: {
                size: 10,
                page: 1,
                total: 0
            },
            table: {
                columns,
                loading: false,
                datasource: [],
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                }
            },
            cidrLoading: false,
            cidrDataSource: [],
            cidrDataSourceCopy: [],
            existedSubnet: [],
            segmentId: '',
            noneCidrQuota: {},
            noneNatIpQuota: {},
            FLAG,
            selectedItems: [],
            deleting: false,
            edit: {
                name: {
                    value: '',
                    error: true,
                    visible: false
                }
            },
            deleteNatIp: {},
            searchVal: '',
            remainNatIpQuota: 0,
            searchbox: {
                keyword: '',
                placeholder: '请输入NAT IP进行搜索',
                keywordType: 'natIp',
                keywordTypes: [
                    {
                        value: 'natIp',
                        text: 'NAT IP'
                    }
                ]
            }
        };
    }

    static computed = {
        noneCidrQuotaTip() {
            const noneCidrQuota = this.data.get('noneCidrQuota');
            return noneCidrQuota.disable ? noneCidrQuota.message : '新建地址段';
        },
        disableDeleteCidr() {
            const natIpDatasource = this.data.get('table.datasource');
            return !!natIpDatasource.length;
        },
        addCidrIconColor() {
            const noneCidrQuota = this.data.get('noneCidrQuota');
            const unEditTip = this.data.get('unEditTip');
            return unEditTip || noneCidrQuota.disable ? '#B4B6BA' : '#2468F2';
        },
        unEditTip() {
            const instance = this.data.get('context').natInfo || {};
            let tip = '';
            if (!_.contains([NatStatus.ACTIVE, NatStatus.DOWN], instance.status)) {
                tip = `当前实例${NatStatus.getTextFromValue(instance.status)}，请稍后再试。`;
            }
            return tip;
        }
    };

    static filters = {
        disableDeleteTip(row: any) {
            let deleteTip = '';
            const unEditTip = this.data.get('unEditTip');
            if (row.default) {
                deleteTip = '默认NAT IP不支持删除';
            } else if (row.disableFlag) {
                deleteTip = '当前NAT IP已被SNAT或DNAT规则关联，请先取消关联后再删除';
            }
            return unEditTip || deleteTip;
        }
    };

    async inited() {
        this.initProps();
        this.getNatIpCidr();
        this.watchOptions();
        this.initTableSelectedOpt();
        this.getCidrQuotaPerPrivateNat();
    }

    initProps() {
        const segmentId = this.data.get('context').segmentId;
        const natIp = this.data.get('context').natIp;
        if (segmentId) {
            this.data.set('segmentId', segmentId);
            this.data.set('searchbox.keyword', natIp);
            // 初始化时watch不执行，需要手动调用一次
            this.queryNatIpListAndQuota();
        }
    }
    queryNatIpListAndQuota() {
        this.getNatIpQuota();
        this.queryNatIpList();
    }

    watchOptions() {
        this.watch('segmentId', () => {
            this.queryNatIpListAndQuota();
        });
    }

    initTableSelectedOpt() {
        const {deleteNatIp} = checker.check(RULES, []);
        this.data.set('deleteNatIp', deleteNatIp);
    }

    getNatIpCidr() {
        this.data.set('cidrLoading', true);
        const natId = this.data.get('context').id;
        this.$http
            .getNatIpCidr(
                {
                    natId
                },
                {'x-silent-codes': ['IntranetNat.IntranetNatResourceNotExist']}
            )
            .then(res => {
                const segments = res.segments || [];
                const existedSubnet = _.map(segments, item => item.subnetUuid);
                let currSegmentId = this.data.get('segmentId');
                const isExistCurrSegmentId = _.find(segments, item => item.segmentId === currSegmentId);
                if (!currSegmentId || !isExistCurrSegmentId) {
                    const defaultCidr = _.find(segments, item => item.type === 'default');
                    currSegmentId = defaultCidr?.segmentId;
                }

                this.data.set('segmentId', currSegmentId);
                this.data.set('cidrDataSource', segments);
                this.data.set('cidrDataSourceCopy', segments);
                this.data.set('existedSubnet', existedSubnet);
                const searchVal = this.data.get('searchVal');
                if (searchVal) {
                    this.handleSearch();
                }
            })
            .finally(() => this.data.set('cidrLoading', false));
    }

    getNatIpQuota() {
        const natId = this.data.get('context').id;
        const segmentId = this.data.get('segmentId');
        this.$http
            .getNatIpQuota(
                {
                    natId,
                    segmentId
                },
                {'x-silent-codes': ['NoSuchObject']}
            )
            .then(res => {
                const {free} = res;
                const {createNatIp} = checker.check(RULES, [], '', {
                    noneNatIpQuota: free <= 0
                });
                this.data.set('remainNatIpQuota', free);
                this.data.set('noneNatIpQuota', createNatIp);
            });
    }

    getCidrQuotaPerPrivateNat() {
        const natId = this.data.get('context').id;
        this.$http.getCidrQuotaPerPrivateNat({natId}, {'x-silent-codes': ['NoSuchObject']}).then(res => {
            const {free} = res;
            const {createCidr} = checker.check(RULES, [], '', {
                noneCidrQuota: free <= 0
            });
            this.data.set('noneCidrQuota', createCidr);
        });
    }

    handleSearchChange(e: any) {
        const {value} = e;
        this.data.set('searchVal', value);
    }

    handleSearch(e?: any) {
        const searchVal = this.data.get('searchVal');
        const search = e?.value || searchVal;
        if (search) {
            const cidrDataSource = this.data.get('cidrDataSource');
            const result = [];
            _.each(cidrDataSource, item => {
                if (item.name.includes(search)) {
                    result.push(item);
                }
            });
            this.data.set('cidrDataSource', result);
        } else {
            const cidrDataSourceCopy = this.data.get('cidrDataSourceCopy');
            this.data.set('cidrDataSource', cidrDataSourceCopy);
        }
    }

    getNatIpPayload() {
        const {
            segmentId,
            pager: {page, size}
        } = this.data.get('');
        const id = this.data.get('context').id;
        const {keyword, keywordType} = this.data.get('searchbox');
        return {
            natId: id,
            segmentId,
            pageNo: page,
            pageSize: size,
            keyword,
            keywordType
        };
    }

    resetTable() {
        this.data.set('selectedItems', []);
        this.data.set('table.selection', {
            mode: 'multi',
            selectedIndex: []
        });
    }

    onSearch() {
        this.queryNatIpList();
    }

    getPrivateDnat(natPayload: Record<string, any>) {
        return this.$http
            .privateDnatRuleList(natPayload, {'x-silent-codes': ['IntranetNat.IntranetNatResourceNotExist']})
            .then(res => {
                const {result} = res;
                const natIps = [];
                if (result.length) {
                    _.forEach(result, item => {
                        const {natIp} = item;
                        natIps.push(natIp);
                    });
                }
                return Array.from(new Set(natIps));
            });
    }

    getPrivateSnat(natPayload: Record<string, any>) {
        return this.$http
            .privateSnatRuleList(natPayload, {'x-silent-codes': ['IntranetNat.IntranetNatResourceNotExist']})
            .then(res => {
                const {result} = res;
                const natIpsRes = [];
                if (result.length) {
                    _.forEach(result, item => {
                        const {natIps} = item;
                        natIpsRes.push(...natIps);
                    });
                }
                return Array.from(new Set(natIpsRes));
            });
    }

    getDnatSnatNatIp(payload: Record<string, any>) {
        return Promise.all([this.getPrivateSnat(payload), this.getPrivateDnat(payload)]);
    }

    async queryNatIpList() {
        const payload = this.getNatIpPayload();
        const natId = this.data.get('context').id;
        const natPayload = {
            natId,
            pageNo: 1,
            pageSize: 10000
        };
        this.data.set('table.loading', true);
        try {
            const natIps = await this.getDnatSnatNatIp(natPayload);
            const allAssociateNatIps = Array.from(new Set([...natIps[0], ...natIps[1]]));
            const res = await this.$http.getNatIpList(payload);
            const {result, totalCount, pageNo, pageSize} = res;
            const formatedRes = _.map(result, item => ({
                ...item,
                disableFlag: allAssociateNatIps.includes(item.natIp)
            }));
            this.data.set('pager.total', totalCount);
            this.data.set('pager.page', pageNo);
            this.data.set('pager.size', pageSize);
            this.data.set('table.datasource', formatedRes);
            this.resetTable();
        } catch (error) {
        } finally {
            this.data.set('table.loading', false);
        }
    }

    handleRefresh() {
        this.queryNatIpList();
    }

    tableSelected(e) {
        this.data.set('selectedItems', e.value.selectedItems);
        const {deleteNatIp} = checker.check(RULES, e.value.selectedItems);
        this.data.set('deleteNatIp', deleteNatIp);
    }

    handleCreateNatIp() {
        const natId = this.data.get('context').id;
        const {segmentId, cidrDataSource, remainNatIpQuota} = this.data.get('');
        const createDialog = new CreateNatIp({
            data: {
                natId,
                segmentId,
                remainNatIpQuota,
                cidrDataSource
            }
        });
        createDialog.attach(document.body);
        createDialog.on('confirm', () => {
            this.queryNatIpList();
            this.getNatIpQuota();
        });
    }

    getCidrPayload() {
        const natId = this.data.get('context').id;
        const vpcId = this.data.get('context').vpcId;
        const existedSubnet = this.data.get('existedSubnet');
        return {
            natId,
            vpcId,
            existedSubnet
        };
    }

    renderDialogInstance(type: 'create' | 'edit', cidrItem: Record<string, any> = {}) {
        const {natId, vpcId, existedSubnet} = this.getCidrPayload();
        const editInstance = new CreateCidr({
            data: {
                type,
                natId,
                vpcId,
                cidrItem,
                existedSubnet
            }
        });
        editInstance.attach(document.body);
        editInstance.on('confirm', (res: any) => {
            if (type === 'create') {
                const {id} = res;
                this.data.set('segmentId', id);
                this.getCidrQuotaPerPrivateNat();
            }
            this.getNatIpCidr();
        });
    }

    handleAddCidr() {
        const {disable} = this.data.get('noneCidrQuota');
        const unEditTip = this.data.get('unEditTip');
        if (!disable && !unEditTip) {
            this.renderDialogInstance('create');
        }
    }

    handleSelectCidr(item: Record<string, any>) {
        const {segmentId} = item;
        this.data.set('searchbox.keyword', '');
        this.data.set('segmentId', segmentId);
    }

    handleAction(item: any, type: 'edit' | 'delete') {
        if (type === 'edit') {
            this.renderDialogInstance(type, item);
        } else {
            const confirmTip = {
                title: '提示',
                content: `确认删除 ${item.name} 地址段吗？`
            };
            const confirmFn = async () => {
                try {
                    const natId = this.data.get('context').id;
                    this.$http
                        .deleteNatIpCidr({
                            natId,
                            segmentId: item.segmentId
                        })
                        .then(res => {
                            Notification.success(`已删除地址段 ${item.name}`);
                            this.getNatIpCidr();
                            this.getCidrQuotaPerPrivateNat();
                        });
                } catch (err) {}
            };
            confirmValidate(confirmTip, confirmFn);
        }
    }

    async handleDeleteNatIp(row?: any) {
        const {natIp} = row;
        const natIps = natIp ? [natIp] : [];
        const selectedItems = this.data.get('selectedItems');
        const selectedIps = _.map(selectedItems, item => item.natIp);
        const natId = this.data.get('context').id;
        const data = {
            title: '提示',
            content: '确认删除吗？'
        };
        const confirmFn = async () => {
            try {
                this.data.set('deleting', true);
                const res = await this.$http.deleteNatIp({
                    natId,
                    natIps: Array.from(new Set([...natIps, ...selectedIps]))
                });
                this.getNatIpQuota();
                this.data.set('selectedItems', []);
                Notification.success('删除成功！');
                this.queryNatIpList();
            } catch (err) {
            } finally {
                this.data.set('deleting', false);
            }
        };
        confirmValidate(data, confirmFn);
    }

    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.queryNatIpList();
    }

    onPagerSizeChange(e) {
        this.data.set('pager.size', e.value.pageSize);
        this.data.set('pager.page', 1);
        this.queryNatIpList();
    }
    onRegionChange() {
        location.reload();
    }

    onEditInput(e, rowIndex, type) {
        let result =
            type === 'name' ? e.value === '' || !/^[a-zA-Z][\w\-\_\/\.]{0,64}$/.test(e.value) : e.value.length > 200;
        this.data.set(`edit.${type}.error`, result);
        this.data.set(`edit.${type}.value`, e.value);
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', result);
    }
    editCancel(rowIndex, type) {
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', true);
        this.ref(`popover-${type}-${rowIndex}`).data.set('visible', false);
    }
    editConfirm(row, rowIndex, type) {
        const {natIp} = row;
        const edit = this.data.get(`edit.${type}`);
        const natId = this.data.get('context').id;
        if (edit.error) {
            return;
        }

        this.$http
            .updateNatIp({
                natId,
                natIp,
                name: edit.value
            })
            .then(() => {
                this.editCancel(rowIndex, type);
                Notification.success('修改成功');
                this.queryNatIpList();
            });
    }
    onInstantEdit(row, rowIndex, type) {
        this.data.set(`edit.${type}.value`, row[`${type}`]);
        this.data.set(`edit.${type}.error`, false);
        const popWrap: any = this.ref(`popover-${type}-${rowIndex}`);
        popWrap.data.set('visible', !popWrap.data.get('visible'));
    }
}
// eslint-disable-next-line babel/new-cap
export default San2React(Processor.autowireUnCheckCmpt(NatIpList));
