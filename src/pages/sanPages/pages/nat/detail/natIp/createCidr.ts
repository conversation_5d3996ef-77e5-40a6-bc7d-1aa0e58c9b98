import {Component} from 'san';
import _ from 'lodash';
import {Notification} from '@baidu/sui';
import {html, decorators, Processor} from '@baiducloud/runtime';

import './style.less';

const {template, asComponent, invokeSUI} = decorators;

const tpl = html`<template>
    <s-dialog width="600" class="nat-ip-create" open="{=open=}" title="{{dialogTitle}}">
        <s-form s-ref="form" data="{=formData=}" rules="{{rules}}">
            <s-form-item
                prop="name"
                label="名称："
                help="大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65"
            >
                <s-input width="400" value="{=formData.name=}" />
            </s-form-item>
            <s-form-item prop="subnetUuid" label="地址段：">
                <s-select
                    disabled="{{loading || disable}}"
                    width="394"
                    value="{=formData.subnetUuid=}"
                    datasource="{{subnetDataSource}}"
                >
                <div class="cidr-empty" slot="empty">暂无可用地址段，去 <a href="#/vpc/subnet/list" target="_blank">私有网络-子网</a> 新建</div>
                </select>
            </s-form-item>
        </s-form>
        <div slot="footer">
            <s-button on-click="handleCancel">取消</s-button>
            <s-button loading="{{submitLoading}}" skin="primary" on-click="handleConfirm">确定</s-button>
        </div>
    </s-dialog>
</template>`;

@asComponent('@create-cidr')
@template(tpl)
@invokeSUI
class CreateCidr extends Component {
    static computed = {
        dialogTitle() {
            const type = this.data.get('type');
            return `${type === 'edit' ? '编辑' : '新建'}地址段`;
        }
    };
    initData() {
        return {
            open: true,
            formData: {
                name: '',
                subnetUuid: ''
            },
            rules: {
                name: [
                    {required: true, message: '请输入名称'},
                    {
                        pattern: /^[a-zA-Z][\w\-\_\/\.]{0,64}$/,
                        message: '支持大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65'
                    }
                ],
                subnetUuid: [{required: true, message: '请选择地址段'}]
            },
            subnetDataSource: [],
            loading: true,
            disable: false,
            submitLoading: false
        };
    }
    inited() {
        const {type, cidrItem, vpcId, existedSubnet} = this.data.get('');
        if (type === 'edit') {
            this.data.set('disable', true);
        }
        if (cidrItem) {
            this.initFormData(cidrItem);
        }
        this.getSubnetsList(vpcId, existedSubnet);
    }
    initFormData(cidrData: any) {
        const {name, subnetUuid} = cidrData;
        this.data.set('formData', {
            name,
            subnetUuid
        });
    }
    getSubnetsList(vpcId: string, existedSubnet: string[]) {
        const {type, cidrItem} = this.data.get('');
        this.$http
            .vpcSubnetList({attachVm: false, vpcId})
            .then(res => {
                const result = _.map(res || [], item => {
                    const {name, cidr, subnetUuid} = item;
                    return {label: `${name}（${cidr}）`, value: subnetUuid, cidr};
                });
                const differenceBySubnetUuid = result.filter(item => !existedSubnet?.includes(item.value));
                if (type === 'edit') {
                    const editItem = result.find(item => item.value === cidrItem.subnetUuid);
                    differenceBySubnetUuid.push(editItem);
                }
                this.data.set('subnetDataSource', differenceBySubnetUuid);
            })
            .finally(() => this.data.set('loading', false));
    }
    async handleConfirm() {
        await this.ref('form').validateFields();
        this.data.set('submitLoading', true);
        const {type, natId, formData, cidrItem} = this.data.get('');
        const params: Record<string, any> = {
            natId
        };
        let reqUrl = '';
        if (type === 'edit') {
            reqUrl = 'updateNatIpCidr';
            params.segmentId = cidrItem.segmentId;
            params.name = formData.name;
        } else {
            reqUrl = 'createNatIpCidr';
            params.segment = {
                type: 'subnet',
                ...formData
            };
        }
        this.$http[reqUrl](params)
            .then(res => {
                this.data.set('open', false);
                Notification.success(`${type === 'edit' ? '编辑' : '新建'}地址段成功!`);
                this.fire('confirm', res);
            })
            .finally(() => this.data.set('submitLoading', false));
    }
    handleCancel() {
        this.data.set('open', false);
    }
}
export default Processor.autowireUnCheckCmpt(CreateCidr);
