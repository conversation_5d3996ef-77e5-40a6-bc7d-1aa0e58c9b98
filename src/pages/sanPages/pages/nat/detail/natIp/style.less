.nat-ip-widget {
    width: 100%;
    display: flex;
    .ip-left-nav {
        width: 180px;
        border-right: 1px solid #e8e9eb;
        .title {
            display: flex;
            justify-content: space-between;
            margin: 16px 24px;
            .title-text {
                font-family: PingFangSC-Medium;
                font-size: 16px;
                color: #151b26;
                line-height: 22px;
                font-weight: 500;
            }
            .nat-ip-plus {
                cursor: pointer;
                line {
                    color: #2468f2;
                }
            }
        }
        .input-widget {
            text-align: center;
            margin-bottom: 8px;
            .s-input-suffix-container {
                width: 134px;
                .s-input-area {
                    width: 100px;
                    input {
                        padding: 0 50px 0 12px;
                    }
                }
                .s-input-suffix {
                    cursor: pointer;
                    z-index: 99999;
                }
                .input-search {
                    cursor: pointer;
                }
            }
        }
        .cidr-widget {
            max-height: 480px;
            overflow: auto;
            .cidr-item {
                padding: 0 24px;
                padding-right: 32px;
                height: 60px;
                cursor: pointer;
                display: flex;
                align-items: center;
                .cidr-item-wrapper {
                    width: 100%;
                    flex: 1;
                    .cidr-value {
                        display: flex;
                        align-items: center;
                        .cidr-span {
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            display: inline-block;
                            margin-right: 4px;
                            flex: 1;
                        }
                        .default-mark {
                            display: inline-block;
                            font-family: PingFangSC-Regular;
                            font-size: 12px;
                            color: #2468f2;
                            line-height: 20px;
                            font-weight: 400;
                            width: 40px;
                            height: 20px;
                            border: 1px solid rgba(36, 104, 242, 1);
                            border-radius: 2px;
                            text-align: center;
                        }
                    }
                    .cidr-name {
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }
                .more-option {
                    display: '';
                    margin-left: 4px;
                }
                .common {
                    font-family: PingFangSC-Regular;
                    font-size: 12px;
                    color: #151b26;
                    text-align: left;
                    line-height: 20px;
                    font-weight: 400;
                }
                &:hover {
                    background-color: #e6f0ff;
                    .common {
                        color: #2468f2;
                    }
                    .more-option {
                        display: block !important;
                        &:hover {
                            color: red;
                        }
                    }
                }
            }
            .cidr-selected {
                position: relative;
                background-color: #e6f0ff;
                &::before {
                    width: 4px;
                    display: inline-block;
                    height: 100%;
                    z-index: 9999;
                    content: '';
                    position: absolute;
                    left: 0;
                    background-color: #2468f2;
                }
            }
        }
    }
    .ip-right-content {
        flex: 1;
        .nat-ip-list {
            width: 100%;
            // flex: 1;
            .table-full-wrap {
                margin: 0;
            }
        }
        .nat-buttons-wrap {
            display: flex;
            align-items: center;
            .s-cascader {
                margin-right: 5px;
                .s-cascader-panel {
                    display: flex;
                    height: auto;
                    background: none;
                    box-shadow: none;
                    .s-cascader-column {
                        background: #fff;
                        box-shadow: 0 1px 6px #ccc;
                        max-height: 150px;
                    }
                }
            }
            .s-cascader-value {
                vertical-align: middle;
                font-size: 12px;
                padding-top: 0;
                padding-bottom: 0;
                line-height: 30px;
            }
            .s-auto-compelete {
                .s-select {
                    input {
                        width: 170px !important;
                    }
                }
            }
            .refresh-button {
                margin-right: 5px;
            }
            .search-content {
                display: flex;
                align-items: center;
                position: relative;
                margin-right: 5px;
            }
            .s-icon.search {
                position: absolute;
                right: 5px;
                color: #615a5a;
            }
            .icon-fresh {
                margin-right: 5px;
            }
        }
        .s-table {
            .s-table-row:hover {
                .name-icon {
                    display: inline;
                }
            }
            .name-icon {
                display: none;
                font-size: 12px;
                fill: #2468f2;
            }
            .nat-ip-name {
                display: flex;
                align-items: center;
                .default-mark {
                    display: inline-block;
                    font-family: PingFangSC-Regular;
                    font-size: 12px;
                    color: #2468f2;
                    line-height: 20px;
                    font-weight: 400;
                    width: 40px;
                    height: 20px;
                    border: 1px solid rgba(36, 104, 242, 1);
                    border-radius: 2px;
                    text-align: center;
                    margin: 0 4px 0 8px;
                }
            }
            .s-button-skin-stringfy {
                padding: 0;
            }
        }
    }
}

.nat-ip-create {
    .s-form {
        .s-form-item-label {
            width: 72px;
            text-align: left;
        }
        .s-form-item-control-wrapper {
            line-height: 30px;
            margin-left: 12px;
            flex: 1;
            // .s-form-item-help {
            //     width: 0px;
            // }
        }
        .cidr-empty {
            text-align: center;
            padding: 8px;
            a {
                cursor: pointer;
            }
        }
        .s-inputnumber-handler-minus-icon {
            border-top: none;
        }
    }
}
.popover-wrapper {
    .nat-ip-more {
        &:hover {
            path {
                fill: #2468f2;
            }
        }
    }
    .s-popover-body {
        width: 90px;
        overflow: hidden;
        .s-popover-content {
            width: 90px !important;
            padding: 0 !important;
            ul > li {
                cursor: pointer;
                width: 100px;
                line-height: 32px;
                padding: 4px 12px;
                &:hover {
                    background-color: #e6f0ff;
                }
            }
            .cidr-opt {
                cursor: pointer;
                width: 100px;
                line-height: 32px;
                padding: 0 12px;
                .s-button-skin-stringfy {
                    padding: 0;
                    color: #151b26;
                    width: 100%;
                    display: inline-block;
                    text-align: left;
                }
                &:hover {
                    background-color: #e6f0ff;
                }
            }
        }
    }
}
.nat-ip-cidr {
    .s-popover-body {
        .s-popover-content {
            padding: 12px !important;
        }
    }
}
