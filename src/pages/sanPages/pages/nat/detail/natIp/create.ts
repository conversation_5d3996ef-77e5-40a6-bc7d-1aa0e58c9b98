import _ from 'lodash';
import {Component} from 'san';
import {Input, Notification} from '@baidu/sui';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {checkIsInSubnet} from '@/pages/sanPages/utils/common';
import RULE from '@/pages/sanPages/utils/rule';

import './style.less';

const {template, asComponent, invokeSUI} = decorators;

const formValidator = self => {
    return {
        name: [
            {required: true, message: '请输入名称'},
            {
                pattern: /^[a-zA-Z][\w\-\_\/\.]{0,64}$/,
                message: '支持大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65'
            }
        ],
        segmentId: [{required: true, message: '请选择地址段'}],
        natIps: [
            {required: true, message: '请输入IP地址'},
            {
                validator(rule, value, callback) {
                    const splitValue = self.splitBySpace(value);
                    const remainQuota = self.data.get('remainQuota');
                    if (splitValue.length > remainQuota) {
                        return callback(`最多可输入${remainQuota}个IP`);
                    }
                    const illegalIps = [];
                    const errIps = [];
                    const segmentId = self.data.get('formData.segmentId');
                    const cidrDataSource = self.data.get('cidrDataSource');
                    const cidr = cidrDataSource.find(item => item.value === segmentId)?.cidr;
                    splitValue.forEach(item => {
                        if (!RULE.IP.test(item)) {
                            errIps.push(item);
                        }
                        if (!checkIsInSubnet(item + '/32', cidr)) {
                            illegalIps.push(item);
                        }
                    });

                    if (errIps.length) {
                        return callback(`IP地址${errIps.toString()}格式错误，请检查`);
                    }
                    if (illegalIps.length) {
                        return callback(`IP地址${illegalIps.toString()}不在所选地址段内，请检查`);
                    }
                    return callback();
                }
            }
        ]
    };
};

const tpl = html`<template>
    <s-dialog width="{{600}}" class="nat-ip-create" open="{=open=}" title="新建NAT IP">
        <s-form layout="horizontal" s-ref="form" data="{=formData=}" rules="{{rules}}">
            <s-form-item prop="segmentId" label="地址段：">
                <s-select width="394" value="{=formData.segmentId=}" datasource="{{cidrDataSource}}" />
            </s-form-item>
            <s-form-item prop="allocation" label="分配方式：">
                <s-radio-radio-group
                    datasource="{{allocationOptions}}"
                    disabled="{{radioGroup.disabled}}"
                    value="{=formData.allocation=}"
                />
            </s-form-item>
            <s-form-item s-if="!isManual" label="NAT IP个数：" prop="allocNum">
                <s-input-number
                    displayMode="enhanced"
                    width="120"
                    max="{{remainQuota}}"
                    min="{{1}}"
                    precision="{{0}}"
                    stepStrictly
                    value="{=formData.allocNum=}"
                />
            </s-form-item>
            <s-form-item s-if="isManual" prop="natIps" label="IP地址：" help="{{natIpHelp}}">
                <s-textarea width="{{400}}" height="88" value="{=formData.natIps=}"></s-textarea>
            </s-form-item>
        </s-form>
        <div slot="footer">
            <s-button on-click="handleCancel">取消</s-button>
            <s-button loading="{{createLoading}}" skin="primary" on-click="handleConfirm">确定</s-button>
        </div>
    </s-dialog>
</template>`;

@template(tpl)
@invokeSUI
class CreateNatIp extends Component {
    static components = {
        's-textarea': Input.TextArea
    };
    static computed = {
        isManual() {
            const allocation = this.data.get('formData.allocation');
            return allocation === 'manual';
        },
        remainQuota() {
            const remain = this.data.get('remainNatIpQuota');
            return remain >= 10 ? 10 : remain;
        },
        natIpHelp() {
            const remainQuota = this.data.get('remainQuota');
            return `可输入IP的个数最大${remainQuota}个，每行仅支持输入一个IP，如需输入多个IP，请换行输入。`;
        }
    };
    initData() {
        return {
            open: true,
            formData: {
                name: '',
                segmentId: '',
                allocation: 'random',
                natIps: '',
                allocNum: 1
            },
            allocationOptions: [
                {label: '随机分配', value: 'random'},
                {label: '手动分配', value: 'manual'}
            ],
            cidrDataSource: [],
            createLoading: false,
            rules: formValidator(this),
            nameHelp: '支持大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65 '
        };
    }
    inited() {
        this.initFormData();
        this.initCidrDataSource();
    }

    initFormData() {
        const segmentId = this.data.get('segmentId');
        this.data.set('formData.segmentId', segmentId);
    }

    initCidrDataSource() {
        const {cidrDataSource} = this.data.get('');
        if (cidrDataSource?.length) {
            const formatData = _.map(cidrDataSource, item => {
                return {label: `${item.name} (${item.cidr})`, value: item.segmentId, cidr: item.cidr};
            });
            this.data.set('cidrDataSource', formatData);
        }
    }

    splitBySpace(str: string) {
        if (!str) return;
        return _.trim(str).split(/\r\n|\n/);
    }

    async handleConfirm() {
        await this.ref('form').validateFields();
        const formData = this.data.get('formData');
        const {natId} = this.data.get('');
        const payload: Record<string, any> = {
            natId,
            ...formData
        };
        delete payload.allocation;
        if (formData.allocation === 'random') {
            delete payload.natIps;
        } else {
            const ips = this.splitBySpace(payload.natIps);
            payload.natIps = ips;
            delete payload.allocNum;
        }
        this.data.set('createLoading', true);
        try {
            const res = await this.$http.createNatIp(payload);
            Notification.success('创建NAT IP成功');
            this.data.set('open', false);
            this.fire('confirm', '');
        } catch (error) {
        } finally {
            this.data.set('createLoading', false);
        }
    }

    handleCancel() {
        this.data.set('open', false);
    }
}
export default Processor.autowireUnCheckCmpt(CreateNatIp);
