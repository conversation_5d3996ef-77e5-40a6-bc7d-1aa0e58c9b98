/**
 * @file network/eni/pages/Create.js
 * <AUTHOR>
 */
import _ from 'lodash';
import {defineComponent} from 'san';
import {Dialog, Form, Input, Button, Select} from '@baidu/sui';

import {html} from '@baiducloud/runtime';
import Rule from '@/pages/sanPages/utils/rule';
import {DnatProtocol} from '@/pages/sanPages/common/enum';
import {uniqBy} from '@/pages/sanPages/utils/helper';
import './create.less';

const DefaultPort = 65535;
// 公网、内网端口号自校验、联合校验检查
const checkPort = (type, self, value, callback) => {
    const rangeReg = /^([1-9][0-9]*)-([1-9][0-9]*)$/;
    let source = self.data.get('formData');
    const netType = type === 'public' ? '公网' : type === 'internal' ? '内网' : 'NAT IP';
    const oppoNetType = type === 'public' ? '公网' : type === 'internal' ? '内网' : 'NAT IP';
    if (!_.trim(value + '')) {
        return callback(`请填写${netType}端口`);
    }
    if (source.protocol !== DnatProtocol.ALL) {
        // 增强型NAT支持端口区间
        const {clusterMode} = self.data.get('natInfo');
        if (clusterMode) {
            const oppositePort = type === 'public' ? source.internalPort : source.publicPort;
            if (value.includes('-')) {
                const valueSplit = value.split('-');
                if (
                    rangeReg.test(value) &&
                    +valueSplit[0] >= 1 &&
                    +valueSplit[1] <= 65535 &&
                    +valueSplit[0] <= +valueSplit[1]
                ) {
                    // 联合校验内网端口
                    if (oppositePort && oppositePort.includes('-')) {
                        const oppositePortSplit = oppositePort.split('-');
                        if (valueSplit[1] - valueSplit[0] !== oppositePortSplit[1] - oppositePortSplit[0]) {
                            return callback(`${netType}端口段端口个数应当与${oppoNetType}端口段端口个数相同，请检查！`);
                        }
                    } else if (oppositePort) {
                        return callback(`${netType}端口与${oppoNetType}端口应当同为1-65535之间的整数或区间`);
                    }
                } else {
                    return callback(`${netType}端口范围是1-65535之间的区间，如80-90`);
                }
            } else if (oppositePort.includes('-')) {
                return callback(`${netType}端口与${oppoNetType}端口应当同为1-65535之间的整数或区间`);
            } else if (value !== '0' && !/^[1-9][0-9]*$/.test(value)) {
                return callback('请填写整数');
            }
        } else if (value !== '0' && !/^[1-9][0-9]*$/.test(value)) {
            return callback('请填写整数');
        }
    }
    if (value < 1 || value > 65535) {
        return callback(`${netType}端口范围是1-65535之间的整数`);
    }
    callback();
};
const formValidator = self => ({
    ruleName: [
        {required: true, message: '请输入名称'},
        {pattern: Rule.NAT.NAME.pattern, message: '格式不正确'}
    ],
    eip: [
        {
            validator(rule, value, callback) {
                if (!self.data.get('datasource.dnatEips').length) {
                    return callback('暂无可选的公网IP，请先给NAT网关绑定公网IP');
                } else {
                    if (!value) {
                        return callback('请选择公网IP地址');
                    }
                    callback();
                }
            }
        }
    ],
    internalIp: {
        validator(rule, value, callback) {
            const isEnhancedIPv6 = self.data.get('isEnhancedIPv6');
            //
            const regExp = isEnhancedIPv6 ? new RegExp(Rule.IPV6) : new RegExp(Rule.DNAT_INTERNALIP);
            if (!_.trim(value)) {
                return callback('请选择内网IP地址');
            }
            if (!regExp.test(value)) {
                return callback('IP地址格式不正确');
            }
            callback();
        }
    },
    protocol: [{required: true, message: '请选择协议'}],
    publicPort: [
        {required: true, message: '请填写公网端口'},
        {
            validator(rule, value, callback) {
                checkPort('public', self, value, callback);
            }
        }
    ],
    internalPort: [
        {required: true, message: '请填写内网端口'},
        {
            validator(rule, value, callback) {
                checkPort('internal', self, value, callback);
            }
        }
    ],
    natipPort: [
        {required: true, message: '请填写NAT IP端口'},
        {
            validator(rule, value, callback) {
                checkPort('private', self, value, callback);
            }
        }
    ],
    natIp: [{required: true, message: '请选择NAT IP地址'}]
});

const template = html`
    <div>
        <s-dialog open="{{open}}" title="{{title}}">
            <div class="vpc-nat-dnat-create">
                <s-form s-ref="form" rules="{{rules}}" data="{=formData=}">
                    <s-form-item
                        label="{{isPrivate ? '名称：' : '条目名称：'}}"
                        help="大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65"
                        prop="ruleName"
                    >
                        <s-input
                            width="{{220}}"
                            value="{=formData.ruleName=}"
                            track-id="ti_vpc_nat_dnat_create"
                            track-name="{{isPrivate ? '名称：' : '条目名称：'}}"
                        />
                    </s-form-item>
                    <s-form-item label="公网IP地址：" class="eip-form-wrap" prop="eip" s-if="!isPrivate">
                        <s-select
                            width="{{220}}"
                            placeholder="请选择公网IP地址"
                            value="{=formData.eip=}"
                            datasource="{{datasource.dnatEips}}"
                            track-id="ti_vpc_nat_dnat_create"
                            track-name="公网IP地址"
                        />
                    </s-form-item>
                    <s-form-item label=" " s-if="!isPrivate && noneEipsTip">
                        <div class="none-eip-natip-tip">{{noneEipsTip}}</div>
                    </s-form-item>
                    <s-form-item label="内网IP地址：" class="required_class" prop="internalIp">
                        <s-input
                            width="{{220}}"
                            value="{=formData.internalIp=}"
                            disabled="{{loading}}"
                            track-id="ti_vpc_nat_dnat_create"
                            track-name="内网IP地址"
                        />
                    </s-form-item>
                    <s-form-item label="NAT IP：" class="required_class" prop="natIp" s-if="isPrivate">
                        <s-select
                            width="{{220}}"
                            placeholder="请选择"
                            value="{=formData.natIp=}"
                            datasource="{{datasource.natips}}"
                            track-id="ti_vpc_nat_dnat_create"
                            track-name="NAT IP地址"
                        />
                    </s-form-item>
                    <s-form-item s-if="noneNatipsTip" label=" ">
                        <div class="none-eip-natip-tip">{{noneNatipsTip}}</div>
                    </s-form-item>
                    <s-form-item label="协议：" prop="protocol">
                        <s-select
                            width="{{220}}"
                            placeholder="请选择协议"
                            value="{=formData.protocol=}"
                            datasource="{{datasource.protocol}}"
                            track-id="ti_vpc_nat_dnat_create"
                            track-name="公网IP地址"
                        />
                    </s-form-item>
                    <s-form-item label="公网端口：" class="required_class" prop="publicPort" s-if="!isPrivate">
                        <div slot="help">
                            {{'源端口，取值范围1-65535之间的整数'}}
                            <span s-if="isShowPortHelp" class="tip">{{'或区间，比如：80，80-90。'}}</span>
                        </div>
                        <s-input
                            width="{{220}}"
                            value="{=formData.publicPort=}"
                            disabled="{{formData.protocol === '${DnatProtocol.ALL}'}}"
                            on-blur="handlePortBlur('public', $event)"
                            track-id="ti_vpc_nat_dnat_create"
                            track-name="公网端口"
                        />
                    </s-form-item>
                    <s-form-item label="内网端口：" class="required_class" prop="internalPort">
                        <div slot="help">
                            {{isPrivate ? '内网': '目标'}}端口，取值范围1-65535之间的整数
                            <span s-if="isShowPortHelp" class="tip">{{'或区间，比如：80，80-90。'}}</span>
                        </div>
                        <s-input
                            width="{{220}}"
                            value="{=formData.internalPort=}"
                            disabled="{{formData.protocol === '${DnatProtocol.ALL}'}}"
                            on-blur="handlePortBlur('internal', $event)"
                            track-id="ti_vpc_nat_dnat_create"
                            track-name="内网端口"
                        />
                    </s-form-item>
                    <s-form-item label="NAT IP端口：" class="required_class" prop="natipPort" s-if="isPrivate">
                        <div slot="help">
                            {{'NAT IP端口，取值范围1-65535之间的整数'}}
                            <span s-if="isShowPortHelp" class="tip">{{'或区间，比如：80，80-90。'}}</span>
                        </div>
                        <s-input
                            width="{{220}}"
                            value="{=formData.natipPort=}"
                            disabled="{{formData.protocol === '${DnatProtocol.ALL}'}}"
                            on-blur="handlePortBlur('private', $event)"
                            track-id="ti_vpc_nat_dnat_create"
                            track-name="NAT IP端口"
                        />
                    </s-form-item>
                </s-form>
            </div>
            <div slot="footer">
                <s-button on-click="onClose">取消</s-button>
                <s-button skin="primary" on-click="onConfirm">确定</s-button>
            </div>
        </s-dialog>
    </div>
`;

export default defineComponent({
    template,
    components: {
        's-form': Form,
        's-button': Button,
        's-dialog': Dialog,
        's-form-item': Form.Item,
        's-select': Select,
        's-input': Input
    },
    computed: {
        isShowPortHelp() {
            const {clusterMode} = this.data.get('natInfo');
            const {protocol} = this.data.get('formData');
            return clusterMode && [DnatProtocol.TCP, DnatProtocol.UDP].includes(protocol);
        },
        isEnhancedIPv6() {
            const natInfo = this.data.get('natInfo');
            const {ipVersion} = natInfo || {};
            return ipVersion === 'v6';
        }
    },
    initData() {
        return {
            open: true,
            rules: formValidator(this),
            formData: {
                ruleName: '',
                eip: '',
                internalIp: '',
                protocol: DnatProtocol.ALL,
                publicPort: DnatProtocol.ALL,
                internalPort: DnatProtocol.ALL,
                natipPort: DnatProtocol.ALL,
                natIp: ''
            },
            formErrors: {},
            datasource: {
                dnatEips: [],
                protocol: DnatProtocol.toArray(),
                natips: []
            },
            noneEipsTip: '',
            noneNatipsTip: ''
        };
    },
    inited() {
        const natInfo = this.data.get('natInfo');
        const isPrivate = this.data.get('isPrivate');
        if (isPrivate) {
            this.$http
                .getNatIpList({
                    natId: this.data.get('id')
                })
                .then(res => {
                    const data = res.result;
                    const formatedData = _.map(data, item => {
                        return {
                            value: item.natIp,
                            text: item.natIp
                        };
                    });
                    if (!formatedData.length) {
                        this.data.set('noneNatipsTip', '暂无可选的NAT IP，请先给NAT网关绑定NAT IP');
                    }
                    this.data.set('datasource.natips', formatedData);
                });
        }
        if (natInfo?.id) {
            const {clusterMode, dnatEips, bindEips} = natInfo;
            // 增强型不区分SNAT、DNAT, 普通型区分
            const finalEips = clusterMode ? uniqBy([...dnatEips, ...(bindEips || [])], 'eip') : dnatEips;
            const formatedEips = _.map(finalEips, item => {
                return {
                    value: item.eip,
                    text: item.eip
                };
            });
            this.data.set('datasource.dnatEips', formatedEips);
        }
        // 设置初始值
        let formData = this.data.get('formData');
        if (formData) {
            if (formData.protocol === DnatProtocol.ALL) {
                this.data.set('formData.publicPort', DnatProtocol.ALL);
                this.data.set('formData.internalPort', DnatProtocol.ALL);
                this.data.set('formData.natipPort', DnatProtocol.ALL);
            } else if ([DnatProtocol.TCP, DnatProtocol.UDP].includes(formData.protocol.toUpperCase())) {
                this.data.set(
                    'formData.publicPort',
                    natInfo.clusterMode ? formData.publicPortRange : formData.publicPort
                );
                this.data.set(
                    'formData.internalPort',
                    natInfo.clusterMode ? formData.internalPortRange : formData.internalPort
                );
                isPrivate &&
                    this.data.set('formData.internalPort', formData.internalIpPortRange || formData.internalPort);
                isPrivate && this.data.set('formData.natipPort', formData.natIpPortRange || formData.natipPort);
                const {ipVersion} = natInfo;
                if (ipVersion === 'v6') {
                    this.data.set('formData.internalPort', formData.internalIpPortRange || formData.internalPort);
                    this.data.set('formData.publicPort', formData.natIpPortRange || formData.publicPort);
                }
            }
        }
    },
    attached() {
        this.watch('formData.protocol', value => {
            const port = value === DnatProtocol.ALL ? DnatProtocol.ALL : '';
            this.data.set('formData.publicPort', port);
            this.data.set('formData.internalPort', port);
            this.data.set('formData.natipPort', port);
        });
    },
    doSubmit() {
        let formData = this.data.get('formData');

        if (formData.publicPort === DnatProtocol.ALL) {
            formData.publicPort = DefaultPort;
        }

        if (formData.internalPort === DnatProtocol.ALL) {
            formData.internalPort = DefaultPort;
        }

        let payload = {natId: this.data.get('id')};

        const form = this.ref('form');
        return form.validateFields().then(() => {
            const {clusterMode} = this.data.get('natInfo');
            // 增强型NAT网关支持端口区间
            if (clusterMode && [DnatProtocol.TCP, DnatProtocol.UDP].includes(formData.protocol)) {
                if (formData.publicPort?.includes('-')) {
                    formData.publicPortRange = formData.publicPort;
                    delete formData.publicPort;
                } else {
                    delete formData.publicPortRange;
                }
                if (formData.internalPort?.includes('-')) {
                    formData.internalPortRange = formData.internalPort;
                    delete formData.internalPort;
                } else {
                    delete formData.internalPortRange;
                }
            }
            // 普通型nat删除冗余字段
            if (!clusterMode) {
                formData.publicPort = formData.publicPort || formData.publicPortRange;
                formData.internalPort = formData.internalPort || formData.internalPortRange;
                delete formData.publicPortRange;
                delete formData.internalPortRange;
            }
            const data = _.extend(payload, formData);

            if (formData.ruleId) {
                return this.$http.dnatUpdate(data);
            }

            return this.$http.dnatCreate(data);
        });
    },
    onConfirm() {
        const isPrivate = this.data.get('isPrivate');
        const natInfo = this.data.get('natInfo');
        const {ipVersion, clusterMode} = natInfo;
        const isIPv6 = clusterMode && ipVersion === 'v6';
        (isPrivate ? this.onPrivateSubmit() : isIPv6 ? this.handleIPv6DnatSubmit() : this.doSubmit()).then(() => {
            this.fire('confirm');
            this.onClose();
        });
    },
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    },
    handlePortBlur(type) {
        const isPrivate = this.data.get('isPrivate');
        this.ref('form') &&
            this.ref('form').validateFields([
                ['public', 'private'].includes(type) ? 'internalPort' : isPrivate ? 'natipPort' : 'publicPort'
            ]);
    },

    // 创建/编辑DNAT规则
    onPrivateSubmit() {
        const {ruleName, natIp, internalIp, protocol, internalPort, natipPort, ruleId} = this.data.get('formData');
        let payload = {
            natId: this.data.get('id'),
            name: ruleName,
            natIp,
            internalIp,
            protocol: protocol.toLowerCase(),
            natIpPortRange: natipPort === DnatProtocol.ALL ? DefaultPort : natipPort,
            internalIpPortRange: internalPort === DnatProtocol.ALL ? DefaultPort : internalPort,
            ...(ruleId ? {ruleId} : {})
        };
        const form = this.ref('form');
        return form.validateFields().then(() => {
            return ruleId ? this.$http.updatePrivateDnat(payload) : this.$http.createPrivateDnat(payload);
        });
    },

    // 创建/编辑DNAT规则
    handleIPv6DnatSubmit() {
        const {ruleName, internalIp, protocol, internalPort, publicPort, ruleId} = this.data.get('formData');
        let payload = {
            natId: this.data.get('id'),
            name: ruleName,
            eip: this.data.get('formData.eip'),
            internalIp,
            protocol: protocol.toLowerCase(),
            natIpPortRange: publicPort === DnatProtocol.ALL ? DefaultPort : publicPort,
            internalIpPortRange: internalPort === DnatProtocol.ALL ? DefaultPort : internalPort,
            ...(ruleId ? {ruleId} : {})
        };
        const form = this.ref('form');
        return form.validateFields().then(() => {
            return ruleId ? this.$http.updateIPv6DnatRule(payload) : this.$http.createIPv6DnatRule(payload);
        });
    }
});
