import _ from 'lodash';

export const columns = isPrivate =>
    _.compact([
        {
            name: 'id',
            label: isPrivate ? 'DNAT规则名称/ID' : 'DNAT条目名称/ID',
            width: 160
        },
        {
            name: 'status',
            label: '状态',
            width: 80
        },
        !isPrivate && {
            name: 'eip',
            label: '公网IP地址'
        },
        !isPrivate && {
            name: 'publicPort',
            label: '公网端口',
            sortable: true
        },
        isPrivate && {
            name: 'natIp',
            label: 'NAT IP'
        },
        isPrivate && {
            name: 'natIpPortRange',
            label: 'NAT IP端口',
            sortable: true
        },
        {
            name: 'protocol',
            label: isPrivate ? '协议' : '协议类型',
            sortable: true
        },
        {
            name: 'internalIp',
            label: '内网IP地址'
        },
        {
            name: isPrivate ? 'internalIpPortRange' : 'internalPort',
            label: '内网端口',
            sortable: true
        },
        {
            name: 'opt',
            label: '操作',
            width: 110
        }
    ]);
