.vpc-nat-dnat-create {
    .s-form {
        width: 600px;
        .s-form-item {
            .s-form-item-label {
                width: 100px;
                label {
                    float: left;
                }
            }
            .s-form-item-control-wrapper {
                flex: 1;
            }
        }
    }
    .required_class {
        .s-form-item-label > label:before {
            left: -7px;
            position: absolute;
            content: '*';
            color: #e8684a;
            margin-right: 4px;
        }
        .tip {
            color: #f33e3e;
        }
    }
    .eip-form-wrap {
        .s-form-item-label label::before {
            left: -7px;
            position: absolute;
            content: '*';
            color: #e8684a;
            margin-right: 4px;
        }
    }
    .none-eip-natip-tip {
        color: #f33e3e;
        margin-top: -22px;
    }
}
