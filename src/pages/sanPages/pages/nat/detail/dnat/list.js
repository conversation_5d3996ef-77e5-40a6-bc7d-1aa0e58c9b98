import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {Notification} from '@baidu/sui';
import {OutlinedPlus, OutlinedRefresh, OutlinedEditingSquare} from '@baidu/sui-icon';
import {checker} from '@baiducloud/bce-opt-checker';
import Confirm from '@/pages/sanPages/components/confirm';
import Create from './create';
import rules from '../../rules';
import {DnatStatus, DnatProtocol, NatStatus} from '@/pages/sanPages/common/enum';
import {columns} from './tableField';
import testID from '@/testId';
import './style.less';

const {invokeSUI, invokeSUIBIZ, asComponent, invokeAppComp, template} = decorators;

const tpl = html`
    <div
        class="nat-list-detail-wrap {{isPrivate ? 'private-nat-list-detail-wrap' : ''}}"
        data-testid="${testID.nat.detailDnat}"
    >
        <h4 s-if="!isPrivate">DNAT列表</h4>
        <s-app-list-page class="nat-dnat-list-wrap {{isPrivate ? 'private-nat-dnat-list-wrap' : ''}}">
            <div slot="pageTitle"></div>
            <div slot="bulk" s-if="isPrivate">
                <s-tip-button
                    disabled="{{unEditTip || addPrivateDnat.disable}}"
                    isDisabledVisibile="{{true}}"
                    skin="primary"
                    on-click="onCreatePrivate"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{(unEditTip || addPrivateDnat.message) | raw}}
                    </div>
                    <outlined-plus />
                    创建DNAT规则
                </s-tip-button>
            </div>
            <div slot="bulk" s-else>
                <s-tip-button
                    disabled="{{addDnat.disable || addIPv6Dnat.disable || isCanAdd}}"
                    skin="primary"
                    isDisabledVisibile="{{true}}"
                    on-click="onCreate"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{(addDnat.message || addIPv6Dnat.message || '资源加载中...') | raw}}
                    </div>
                    <outlined-plus />
                    添加DNAT条目
                </s-tip-button>
                <s-tip-button
                    disabled="{{deleteDnat.disable}}"
                    isDisabledVisibile="{{true}}"
                    on-click="onDelete"
                    class="left_class"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{deleteDnat.message | raw}}
                    </div>
                    删除
                </s-tip-button>
            </div>
            <div slot="filter">
                <s-button class="s-icon-button" on-click="refresh" track-name="刷新"
                    ><outlined-refresh class="icon-class"
                /></s-button>
            </div>
            <s-table
                columns="{{table.columns | filterColumns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                on-selected-change="tableSelected($event)"
                on-filter="onFilter"
                on-sort="onSort"
                selection="{=table.selection=}"
                track-id="ti_vpc_instance_table"
                track-name="列表操作"
            >
                <div slot="error">啊呀，出错了？ <a href="javascript:void(0)" on-click="refresh">重新加载</a></div>
                <div slot="empty">
                    <s-empty
                        actionText="{{(addPrivateDnat.disable || addDnat.disable || unEditTip || addIPv6Dnat.disable) ? '' : '马上创建'}}"
                        vertical="{{addPrivateDnat.disable || addDnat.disable || unEditTip || addIPv6Dnat.disable}}"
                        on-click="onCreate"
                    >
                    </s-empty>
                </div>
                <div slot="c-id">
                    <span>{{isPrivate ? row.name : row.ruleName}}</span>
                    <s-popover
                        s-ref="popover-ruleName-{{rowIndex}}"
                        placement="top"
                        trigger="click"
                        class="edit-popover-class"
                    >
                        <div class="edit-wrap" slot="content">
                            <s-input
                                value="{=edit.ruleName.value=}"
                                width="320"
                                placeholder="请输入名称"
                                on-input="onEditInput($event, rowIndex, 'ruleName')"
                            />
                            <div class="edit-tip">大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65</div>
                            <s-button
                                skin="primary"
                                s-ref="editBtn-ruleName-{{rowIndex}}"
                                disabled="{{true}}"
                                on-click="editConfirm(row, rowIndex, 'ruleName')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel(rowIndex, 'ruleName')">取消</s-button>
                        </div>
                        <outlined-editing-square
                            class="name-icon"
                            on-click="onInstantEdit(row, rowIndex, 'ruleName')"
                        />
                    </s-popover>
                    <br />
                    <span class="truncated">{{row.ruleId}}</span>
                    <s-clip-board class="name-icon" text="{{row.ruleId}}" />
                </div>
                <div slot="c-status">
                    <span class="{{row.status | statusClass}}">{{row.status | statusText}}</span>
                </div>
                <div slot="c-publicPort">{{row | getPublicPort}}</div>
                <div slot="c-protocol">{{row | getProtoCol}}</div>
                <div slot="c-internalPort">{{row | getInternalPort}}</div>
                <div slot="c-natIpPortRange">{{row | getNatIpPortRange}}</div>
                <div slot="c-internalIpPortRange">{{row | getInternalIpPortRange}}</div>
                <div slot="c-opt" class="operations">
                    <s-tooltip trigger="{{isPrivate && unEditTip ? 'hover' : ''}}">
                        <span slot="content">{{unEditTip}}</span>
                        <s-button disabled="{{!isCanEdit}}" skin="stringfy" on-click="onEdit(row)"
                            >编辑</s-button
                        ></s-tooltip
                    >
                    <s-button s-if="isPrivate && !unEditTip" skin="stringfy" on-click="onDeletePrivate(row)">
                        删除
                    </s-button>
                </div>
            </s-table>
            <s-pagination
                s-if="{{pager.total}}"
                slot="pager"
                layout="{{'total, pageSize, pager, go'}}"
                pageSize="{{pager.pageSize}}"
                total="{{pager.total}}"
                page="{{pager.page}}"
                on-pagerChange="onPagerChange"
                on-pagerSizeChange="onPagerSizeChange"
            />
        </s-app-list-page>
    </div>
`;
@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@asComponent('@dnat-list')
class NatDnatList extends Component {
    static components = {
        'outlined-plus': OutlinedPlus,
        'outlined-refresh': OutlinedRefresh,
        'outlined-editing-square': OutlinedEditingSquare
    };
    initData() {
        return {
            table: {
                loading: false,
                datasource: [],
                columns,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                }
            },
            pager: {
                page: 1,
                total: 10,
                pageSize: 10
            }
        };
    }

    static filters = {
        statusClass(value) {
            return DnatStatus.fromValue(value).styleClass || '';
        },
        statusText(value) {
            return value ? DnatStatus.getTextFromValue(value) : '-';
        },
        getPublicPort(item) {
            if (item.protocol === DnatProtocol.ALL) {
                return DnatProtocol.ALL;
            }
            return u.escape(item.publicPortRange || item.publicPort || item.natIpPortRange);
        },
        getProtoCol(item) {
            return DnatProtocol.getTextFromValue(
                item.protocol === DnatProtocol.ALL ? item.protocol : item.protocol.toUpperCase()
            );
        },
        getInternalPort(item) {
            if (item.protocol === DnatProtocol.ALL) {
                return DnatProtocol.ALL;
            }
            return u.escape(item.internalPortRange || item.internalPort || item.internalIpPortRange);
        },
        filterColumns(columns) {
            const isPrivate = this.data.get('context').isPrivate;
            return columns(isPrivate);
        },
        getInternalIpPortRange(item) {
            if (item.protocol === DnatProtocol.ALL) {
                return DnatProtocol.ALL;
            }

            return u.escape(item.internalIpPortRange);
        },
        getNatIpPortRange(item) {
            if (item.protocol === DnatProtocol.ALL) {
                return DnatProtocol.ALL;
            }

            return u.escape(item.natIpPortRange);
        }
    };

    static computed = {
        unEditTip() {
            const natInfo = this.data.get('context').natInfo;
            const isPrivate = this.data.get('isPrivate');
            let tip = '';
            if (isPrivate && !u.contains([NatStatus.ACTIVE, NatStatus.DOWN], natInfo.status)) {
                tip = `当前实例${NatStatus.getTextFromValue(natInfo.status)}，请稍后再试。`;
            }
            return tip;
        },
        isCanEdit() {
            const isPrivate = this.data.get('context').isPrivate;
            const unEditTip = this.data.get('unEditTip');
            let flag = true;
            if (isPrivate && unEditTip) {
                flag = false;
            }
            return flag;
        },
        isCanAdd() {
            const natInfo = this.data.get('context').natInfo;
            let flag = true;
            if (!!Object.keys(natInfo).length) {
                flag = false;
            }
            return flag;
        }
    };

    inited() {
        this.data.set('isPrivate', this.data.get('context').isPrivate);
    }

    attached() {
        this.loadPage({'x-silent-codes': ['NoSuchNat']});
    }

    getDnatQuota() {
        let id = this.data.get('context').id;
        let natInfo = this.data.get('context').natInfo;
        this.$http.dnatRuleQuota({natGatewayId: id}, {'x-silent-codes': ['Exception']}).then(data => {
            let {addDnat} = checker.check(rules, '', 'addDnat', {free: data.free, natInfo});
            this.data.set('addDnat', addDnat);
        });
    }

    getPayload() {
        const {pager, order} = this.data.get('');
        const {id, vpcId} = this.data.get('context');
        let payload = {
            natGatewayId: id,
            vpcId,
            pageNo: pager.page,
            pageSize: pager.pageSize
        };
        return {...payload, ...order};
    }

    loadPage(option = {}) {
        let payload = this.getPayload();
        const isPrivate = this.data.get('context').isPrivate;
        const natInfo = this.data.get('context').natInfo;
        const {ipVersion} = natInfo;
        const isIPv6 = ipVersion === 'v6';
        if (!payload.natGatewayId) {
            return;
        }
        this.resetTable();
        this.data.set('table.loading', true);
        return isPrivate
            ? this.privateDnatRuleList()
            : isIPv6
              ? this.queryIPv6DnatRuleList()
              : this.$http.dnatRuleList(payload, option).then(res => {
                    this.getDnatQuota();
                    this.data.set('table.datasource', res.result);
                    this.data.set('pager.total', res.totalCount);
                    this.data.set('table.loading', false);
                });
    }

    resetTable() {
        this.data.set('table.selection.selectedIndex', []);
    }

    tableSelected(e) {
        this.data.set('table.selectedItems', e.value.selectedItems);
        let {deleteDnat} = checker.check(rules, e.value.selectedItems, '');
        this.data.set('deleteDnat', deleteDnat);
    }

    onSort(e) {
        this.data.set('order', e.value);
        this.loadPage();
    }

    // 改变页数
    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }

    // 改变每页显示个数
    onPagerSizeChange(e) {
        this.data.set('pager.pageSize', e.value.pageSize);
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    onCreate() {
        const unEditTip = this.data.get('unEditTip');
        const isPrivate = this.data.get('isPrivate');
        if (isPrivate && unEditTip) {
            return;
        }
        if (isPrivate) {
            this.onCreatePrivate();
            return;
        }
        const {vpcId, id, natInfo} = this.data.get('context');
        const dialog = new Create({
            data: {
                open: true,
                title: '添加DNAT条目',
                vpcId,
                id,
                natInfo
            }
        });
        dialog.on('confirm', e => this.loadPage());
        dialog.attach(document.body);
    }

    onDelete() {
        let selectedItems = this.data.get('table.selectedItems');
        const dialog = new Confirm({
            data: {
                title: '删除前确认',
                content: `确认删除当前DNAT条目${selectedItems[0].ruleName}?`
            }
        });
        dialog.on('confirm', () => {
            let payload = {
                natId: this.data.get('context').id,
                dnatRules: u.pluck(selectedItems, 'ruleId')
            };
            const natInfo = this.data.get('context').natInfo;
            const {ipVersion} = natInfo;
            const isIPv6 = ipVersion === 'v6';
            let reqUrl = 'deleteDnatRule';
            if (isIPv6) {
                reqUrl = 'deleteIPv6DnatRule';
                payload.ruleIds = u.pluck(selectedItems, 'ruleId');
                delete payload.dnatRules;
            }
            this.$http[reqUrl](payload).then(() => this.loadPage());
        });
        dialog.attach(document.body);
    }

    refresh() {
        this.loadPage();
    }

    // 点击修改icon
    onInstantEdit(row, rowIndex, type) {
        const isPrivate = this.data.get('isPrivate');
        this.data.set(`edit.${type}.value`, isPrivate ? row.name : row[`${type}`]);
        this.data.set(`edit.${type}.error`, false);
        const popWrap = this.ref(`popover-${type}-${rowIndex}`);
        popWrap.data.set('visible', !popWrap.data.get('visible'));
    }

    // 编辑弹框-输入名称/描述
    onEditInput(e, rowIndex, type) {
        let result = e.value === '' || !/^[a-zA-Z][\w\-\_\/\.]{0,64}$/.test(e.value);
        this.data.set(`edit.${type}.error`, result);
        this.data.set(`edit.${type}.value`, e.value);
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', result);
    }

    // 编辑弹框-提交
    editConfirm(row, rowIndex, type) {
        let edit = this.data.get(`edit.${type}`);
        if (edit.error) {
            return;
        }
        const natId = this.data.get('context').id;
        const isPrivate = this.data.get('isPrivate');
        const instanceInfo = u.cloneDeep(row);
        const natInfo = this.data.get('context').natInfo;
        const {ipVersion, clusterMode} = natInfo;
        const isIPv6 = ipVersion === 'v6';
        // 增强型只用publicPortRange、internalPortRange字段 普通型仍用publicPort、internalPort字段
        if (clusterMode) {
            delete instanceInfo.publicPort;
            delete instanceInfo.internalPort;
        } else {
            instanceInfo.internalPort = instanceInfo.internalPort || instanceInfo.internalPortRange;
            instanceInfo.publicPort = instanceInfo.publicPort || instanceInfo.publicPortRange;
            delete instanceInfo.publicPortRange;
            delete instanceInfo.internalPortRange;
        }
        const {status, name, ...payload} = instanceInfo;
        (isPrivate
            ? this.$http.updatePrivateDnat({
                  ...payload,
                  natId,
                  name: edit.value
              })
            : isIPv6
              ? this.$http.updateIPv6DnatRule({...instanceInfo, natId, [type]: edit.value})
              : this.$http.dnatUpdate({
                    ...instanceInfo,
                    natId,
                    [type]: edit.value
                })
        ).then(() => {
            this.editCancel(rowIndex, type);
            Notification.success('修改成功');
            this.loadPage();
        });
    }

    // 编辑弹框-取消
    editCancel(rowIndex, type) {
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', true);
        this.ref(`popover-${type}-${rowIndex}`).data.set('visible', false);
    }

    onEdit(row) {
        const {vpcId, id, natInfo} = this.data.get('context');
        const isPrivate = this.data.get('isPrivate');
        const {ipVersion} = natInfo;
        const isEnhancedIPv6 = ipVersion === 'v6';
        // 格式兼容
        isPrivate &&
            (row.ruleName = row.name) &&
            (row.protocol = row.protocol === 'all' ? 'all' : row.protocol.toUpperCase());
        isEnhancedIPv6 && (row.protocol = row.protocol === 'all' ? 'all' : row.protocol.toUpperCase());
        const dialog = new Create({
            data: {
                open: true,
                title: isPrivate ? '编辑DNAT规则' : '编辑DNAT条目',
                formData: row,
                vpcId,
                id,
                natInfo,
                isPrivate
            }
        });
        dialog.on('confirm', e => this.loadPage());
        dialog.attach(document.body);
    }

    // 私网NAT规则删除
    onDeletePrivate(row) {
        const dialog = new Confirm({
            data: {
                title: '删除前确认',
                content: `确认删除当前DNAT规则${row.name}?`
            }
        });
        dialog.on('confirm', () => {
            let payload = {
                natId: this.data.get('context').id,
                ruleIds: [row.ruleId]
            };
            this.$http.deletePrivateDnatRule(payload).then(() => this.loadPage());
        });
        dialog.attach(document.body);
    }

    // 新建私网DNAT规则
    onCreatePrivate() {
        const {vpcId, id, natInfo} = this.data.get('context');
        const dialog = new Create({
            data: {
                open: true,
                title: '新建DNAT规则',
                vpcId,
                id,
                natInfo,
                isPrivate: this.data.get('isPrivate')
            }
        });
        dialog.on('confirm', e => this.loadPage());
        dialog.attach(document.body);
    }

    // 私网DNAT规则列表
    privateDnatRuleList() {
        const {pager, order} = this.data.get('');
        const id = this.data.get('context').id;
        let payload = {
            natId: id,
            pageNo: pager.page,
            pageSize: pager.pageSize,
            ...order
        };
        this.$http
            .privateDnatRuleList(payload, {'x-silent-codes': ['IntranetNat.IntranetNatResourceNotExist']})
            .then(res => {
                this.privateDnatRuleQuota();
                this.data.set('table.datasource', res.result);
                this.data.set('pager.total', res.totalCount);
                this.data.set('table.loading', false);
            })
            .catch(() => {
                this.data.set('table.loading', false);
            });
    }

    privateDnatRuleQuota() {
        const natId = this.data.get('context').id;
        this.$http.privateDnatRuleQuota(natId, {'x-silent-codes': ['Exception']}).then(data => {
            let {addPrivateDnat} = checker.check(rules, '', 'addPrivateDnat', {
                free: data.free
            });
            this.data.set('addPrivateDnat', addPrivateDnat);
        });
    }

    // IPv6 DNAT规则列表
    queryIPv6DnatRuleList() {
        const {pager, order} = this.data.get('');
        const id = this.data.get('context').id;
        let payload = {
            natId: id,
            pageNo: pager.page,
            pageSize: pager.pageSize,
            ...order
        };
        this.$http
            .queryIPv6DnatRuleList(payload)
            .then(res => {
                this.queryIPv6DnatRuleQuota();
                this.data.set('table.datasource', res.result);
                this.data.set('pager.total', res.totalCount);
                this.data.set('table.loading', false);
            })
            .catch(() => {
                this.data.set('table.loading', false);
            });
    }

    queryIPv6DnatRuleQuota() {
        const natId = this.data.get('context').id;
        this.$http.queryIPv6DnatRuleQuota(natId, {'x-silent-codes': ['Exception']}).then(data => {
            let {addIPv6Dnat} = checker.check(rules, '', 'addIPv6Dnat', {
                free: data.free
            });
            this.data.set('addIPv6Dnat', addIPv6Dnat);
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(NatDnatList));
