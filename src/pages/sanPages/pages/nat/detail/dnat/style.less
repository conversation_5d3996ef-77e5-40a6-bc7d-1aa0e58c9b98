.nat-list-detail-wrap {
    padding: 24px;
    &.private-nat-list-detail-wrap {
        padding-top: 0;
    }
    h4 {
        display: block;
        color: #151b26;
        line-height: 24px;
        font-weight: 500;
        margin: 0 0 16px;
        font-size: 16px;
    }
}
.nat-dnat-list-wrap {
    padding: 0;
    background: white;
    .s-list-content {
        .table-full-wrap {
            padding: 24px 0 !important;
            margin: 0 0 !important;
        }
    }
    &.private-nat-dnat-list-wrap .s-list-content .table-full-wrap {
        margin-top: 0px !important;
    }
    .s-table {
        .s-table-row:hover {
            .name-icon {
                display: inline;
            }
        }
        .name-icon {
            display: none;
            font-size: 12px;
            fill: #2468f2;
        }
        .s-table-body {
            max-height: calc(~'100vh - 368px');
        }
    }
    .tip-icon-wrap {
        font-size: 14px;
        border: 1px solid #f18823;
        color: #f18823;
        &:hover {
            color: #fff;
            background-color: #f18823;
        }
    }
    .foot-pager {
        margin-top: 16px;
    }
    .operations {
        .s-button {
            padding: 0;
            margin-right: 16px;
        }
    }
}

.locale-en {
    .vpc-nat-dnat-create,
    .vpc-nat-snat-create {
        .bui-form-item-label {
            width: 180px !important;
        }
        .s-form {
            .s-form-item {
                .s-form-item-label {
                    width: 180px !important;
                }
            }
        }
    }
}
