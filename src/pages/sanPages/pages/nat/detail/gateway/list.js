import {Component} from 'san';
import _ from 'lodash';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {GATEWAY_TABLE} from './tableField';
import {Notification} from '@baidu/sui';
import {OutlinedPlus} from '@baidu/sui-icon';

import delDules from '../../rules';
import {NatTypeSessionBps} from '@/pages/sanPages/common/enum';
import {convertCidrToBinary, checkIsInSubnet} from '@/pages/sanPages/utils/common';
import rule from '@/pages/sanPages/utils/rule';
import Confirm from '@/pages/sanPages/components/confirm';
import './style.less';

const {asComponent, invokeSUI, invokeSUIBIZ, template} = decorators;

const tpl = html`
    <div class="gateway-control-container">
        <div class="gateway-control-group">
            <div class="gateway-detail-table">
                <h4>入站策略</h4>
                <div class="inline_class">
                    <s-button skin="primary" disabled="{{addDisable}}" on-click="addRules('ingress')">
                        <outlined-plus />
                        添加策略
                    </s-button>
                    <s-tooltip class="left_class" trigger="{{ingressDelRule.disable ? 'hover' : ''}}" placement="right">
                        <!--bca-disable-next-line-->
                        <div slot="content">{{ingressDelRule.message | raw}}</div>
                        <s-button on-click="onDelete" disabled="{{ingressDelRule.disable}}"> 删除</s-button>
                    </s-tooltip>
                </div>
                <div class="list-section">
                    <s-table
                        loading="{{tableLoading}}"
                        datasource="{{ingressTable.datasource}}"
                        columns="{{ingressTable.schema}}"
                        on-selected-change="tableSelected($event, 'ingress')"
                        selection="{=ingressTable.selection=}"
                    >
                        <!--<div slot="empty">
                            <s-empty on-click="addRules('ingress')"> </s-empty>
                        </div>-->
                        <div slot="c-bps">{{row.bps}}Mbps</div>
                        <div slot="c-opt">
                            <s-button skin="stringfy" on-click="onEdit(row)">编辑</s-button>
                        </div>
                    </s-table>
                </div>
            </div>
            <div class="gateway-detail-table">
                <h4>出站策略</h4>
                <div class="inline_class">
                    <s-button skin="primary" disabled="{{addDisable}}" on-click="addRules('egress')">
                        <outlined-plus />
                        添加策略
                    </s-button>
                    <s-tooltip class="left_class" trigger="{{egressDelRule.disable ? 'hover' : ''}}" placement="right">
                        <!--bca-disable-next-line-->
                        <div slot="content">{{egressDelRule.message | raw}}</div>
                        <s-button on-click="onDelete" disabled="{{egressDelRule.disable}}"> 删除</s-button>
                    </s-tooltip>
                </div>
                <div class="list-section">
                    <s-table
                        loading="{{tableLoading}}"
                        datasource="{{egressTable.datasource}}"
                        columns="{{egressTable.schema}}"
                        on-selected-change="tableSelected($event, 'egress')"
                        selection="{=egressTable.selection=}"
                    >
                        <!--<div slot="empty">
                            <s-empty on-click="addRules('egress')"> </s-empty>
                        </div>-->
                        <div slot="c-bps">{{row.bps}}Mbps</div>
                        <div slot="c-opt">
                            <s-button skin="stringfy" on-click="onEdit(row)">编辑</s-button>
                        </div>
                    </s-table>
                </div>
            </div>
        </div>
        <s-dialog title="{{dialogTitle}}" on-close="close" open="{=dialogDisplay=}">
            <div class="gateway-control-form-container">
                <s-form s-ref="rules-ref" data="{=formData=}" rules="{{rules}}" label-align="left">
                    <s-form-item label="CIDR：" prop="internalAddr">
                        <s-input value="{=formData.internalAddr=}" placeholder="请输入IPv4网段，如：10.0.0.0/24" />
                    </s-form-item>
                    <s-form-item label="带宽：" prop="bps">
                        <s-input value="{=formData.bps=}" placeholder="请输入带宽" />
                        <span style="margin-left: 8px">Mbps</span>
                    </s-form-item>
                    <s-form-item label="并发连接数：" prop="maxSession">
                        <s-input value="{=formData.maxSession=}" placeholder="请输入并发连接数" />
                    </s-form-item>
                </s-form>
            </div>
            <div slot="footer">
                <s-button on-click="close">取消</s-button>
                <s-button skin="primary" disabled="{{confirmDisabled}}" on-click="dialogConfirm">确定</s-button>
            </div>
        </s-dialog>
    </div>
`;
@template(tpl)
@invokeSUI
@invokeSUIBIZ
@asComponent('@gateway-control')
class GatewayControl extends Component {
    static components = {
        'outlined-plus': OutlinedPlus
    };
    initData() {
        return {
            ingressTable: {
                schema: GATEWAY_TABLE,
                datasource: [],
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                }
            },
            egressTable: {
                schema: GATEWAY_TABLE,
                datasource: [],
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                }
            },
            // 用于检验cidr是否重叠
            checkCidrList: [],
            tableLoading: false,
            dialogDisplay: false,
            rules: {
                internalAddr: [this.validateWhiteCidr()],
                bps: [this.validateBps()],
                maxSession: [this.validateMaxSession()]
            },
            formData: {},
            isEdit: false,
            selectedItems: [],
            ingressDelRule: {},
            egressDelRule: {},
            confirmDisabled: false
        };
    }

    static computed = {
        addDisable() {
            const natInfo = this.data.get('context').natInfo;
            return natInfo ? natInfo.status !== 'active' : true;
        },
        dialogTitle() {
            if (this.data.get('direction') === 'ingress') {
                return this.data.get('isEdit') ? '编辑入站规则' : '添加入站规则';
            } else {
                return this.data.get('isEdit') ? '编辑出站规则' : '添加出站规则';
            }
        }
    };

    inited() {
        this.data.set('natId', this.data.get('context').id);
        this.loadVpcDetail();
    }

    attached() {
        this.getLimitRules();
        let {deleteRules} = checker.check(delDules, []);
        this.data.set('ingressDelRule', deleteRules);
        this.data.set('egressDelRule', deleteRules);
    }

    loadVpcDetail() {
        const vpcId = this.data.get('context').vpcId;
        return this.$http
            .getVpcDetail({
                vpcIds: [vpcId]
            })
            .then(data => {
                const result = data[vpcId] || {};
                this.data.set('vpcInfo', result);
            });
    }

    tableSelected(e, direction) {
        this.data.set('selectedItems', e.value.selectedItems);
        let {deleteRules} = checker.check(delDules, e.value.selectedItems);
        if (direction === 'ingress') {
            this.data.set('ingressDelRule', deleteRules);
        } else {
            this.data.set('egressDelRule', deleteRules);
        }
    }

    convertCidrArr(rules) {
        return _.map(rules, item => item.internalAddr);
    }

    getLimitRules() {
        this.data.set('tableLoading', true);
        return this.$http.getNatLimitRule(this.data.get('context').id, {'x-silent-codes': ['NoSuchNat']}).then(res => {
            this.resetTable();
            this.data.set('tableLoading', false);
            const {ingress, egress} = res;
            this.data.set('ingressTable.datasource', ingress);
            this.data.set('egressTable.datasource', egress);
            let ingressCidr = this.convertCidrArr(ingress);
            let egressCidr = this.convertCidrArr(egress);
            this.data.set('cidrList', ingressCidr.concat(egressCidr));
        });
    }

    validateWhiteCidr() {
        return {
            validator: (rules, value, callback) => {
                value = value && (value + '').trim();
                if (!value) {
                    return callback('请输入IPv4 CIDR');
                }
                if (!new RegExp(rule.IP_CIDR).test(value)) {
                    return callback('CIDR不合法');
                }
                let valueString = convertCidrToBinary(value);
                let valueMask = value.split('/')[1];
                if (valueString.substring(+valueMask, valueString.length).indexOf('1') > -1) {
                    return callback('CIDR不合法');
                }
                // 限制0.0.0.0或0.0.0.0/0网段
                if (value === '0.0.0.0' || value === '0.0.0.0/0') {
                    return callback('CIDR不合法');
                }
                for (const i of this.data.get('checkCidrList')) {
                    if (checkIsInSubnet(i, value) || checkIsInSubnet(value, i)) {
                        return callback('CIDR重叠');
                    }
                }
                callback();
            }
        };
    }

    validateBps() {
        return {
            validator: (rules, value, callback) => {
                value = value && (value + '').trim();
                let {flavor} = this.data.get('context').natInfo;
                const max = NatTypeSessionBps.fromValue(flavor).bps;
                if (!value) {
                    return callback(`请输入带宽`);
                }
                if (!/^[1-9]\d*$/.test(value)) {
                    return callback('请填写整数');
                }
                if (value < 1 || value > max) {
                    return callback(`带宽取值范围[1, ${max}]`);
                }
                callback();
            }
        };
    }

    validateMaxSession() {
        return {
            validator: (rules, value, callback) => {
                value = value && (value + '').trim();
                let {flavor} = this.data.get('context').natInfo;
                const max = NatTypeSessionBps.fromValue(flavor).maxSession;
                if (!value) {
                    return callback(`请输入连接数`);
                }
                if (!/^[1-9]\d*$/.test(value)) {
                    return callback('请填写整数');
                }
                if (value < 1 || value > max) {
                    return callback(`连接数取值范围[1, ${max}]`);
                }
                callback();
            }
        };
    }

    getConfig() {
        return this.data.get('formData');
    }

    addRules(direction) {
        this.data.set('isEdit', false);
        this.data.set('dialogDisplay', true);
        this.data.set('direction', direction);
        if (direction === 'ingress') {
            let ingressCidr = this.convertCidrArr(this.data.get('ingressTable.datasource'));
            this.data.set('checkCidrList', ingressCidr);
        } else {
            let egressCidr = this.convertCidrArr(this.data.get('egressTable.datasource'));
            this.data.set('checkCidrList', egressCidr);
        }
    }

    async dialogConfirm() {
        let param = {
            natId: this.data.get('natId'),
            ...this.getConfig()
        };
        await this.ref('rules-ref').validateFields();
        this.data.set('confirmDisabled', true);
        if (this.data.get('isEdit')) {
            this.$http
                .updateNatLimitRule(param)
                .then(res => {
                    this.close();
                    this.data.set('confirmDisabled', false);
                    this.getLimitRules();
                })
                .catch(() => {
                    this.data.set('confirmDisabled', false);
                });
        } else {
            this.$http
                .addNatLimitRule({
                    ...param,
                    direction: this.data.get('direction')
                })
                .then(res => {
                    this.close();
                    this.data.set('confirmDisabled', false);
                    this.getLimitRules();
                })
                .catch(() => {
                    this.data.set('confirmDisabled', false);
                });
        }
    }

    onEdit(row) {
        this.data.set('isEdit', true);
        const {direction, ruleId, internalAddr, bps, maxSession} = row;
        this.data.set('direction', direction);
        this.data.set('formData', {
            ruleId,
            internalAddr,
            bps,
            maxSession
        });
        let cidrList =
            direction === 'ingress'
                ? this.convertCidrArr(this.data.get('ingressTable.datasource'))
                : this.convertCidrArr(this.data.get('egressTable.datasource'));
        // 编辑时过滤当前编辑项cidr的校验
        this.data.set(
            'checkCidrList',
            _.filter(cidrList, item => item !== row.internalAddr)
        );
        this.data.set('dialogDisplay', true);
    }

    onDelete() {
        let confirm = new Confirm({
            data: {
                open: true,
                content: '确定要删除策略？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            let ruleIds = _.map(this.data.get('selectedItems'), item => item.ruleId);
            this.$http
                .delNatLimitRule({
                    natId: this.data.get('natId'),
                    ruleIds: ruleIds
                })
                .then(() => {
                    this.getLimitRules();
                    Notification.success('删除成功');
                });
        });
    }

    resetTable() {
        this.data.set('ingressTable.selection.selectedIndex', []);
        this.data.set('egressTable.selection.selectedIndex', []);
    }

    close() {
        this.data.set('dialogDisplay', false);
        this.data.set('formData', {});
    }
}
export default San2React(Processor.autowireUnCheckCmpt(GatewayControl));
