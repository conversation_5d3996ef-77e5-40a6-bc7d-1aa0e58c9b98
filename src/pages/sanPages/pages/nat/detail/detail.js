/**
 * @file vpc/nat/detail/Detail.js
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {OutlinedEditingSquare} from '@baidu/sui-icon';
import {San2React} from '@baidu/bce-react-toolkit';
import {Button, Icon, Notification, InputNumber, Popover, Switch, Tooltip} from '@baidu/sui';
import {ClipBoard, Tip} from '@baidu/sui-biz';

import {NatStatus} from '@/pages/sanPages/common/enum';
import {toTime, $flag as FLAG} from '@/pages/sanPages/utils/helper';
import Rule from '@/pages/sanPages/utils/rule';
import testID from '@/testId';
import './detail.less';

const {service, asComponent, invokeComp, template, invokeSUI} = decorators;
/* eslint-disable */
const tpl = html` <div>
    <div class="{{klass}}">
        <div class="content-item">
            <dl>
                <dt><h4>基本信息</h4></dt>
                <span class="item-col">
                    <label class="item-label">ID：</label>
                    <span class="item-content">
                        <span>{{instance.id}}</span>
                        <s-clip-board text="{{instance.id}}">
                            <s-icon name="copy" />
                        </s-clip-board>
                    </span>
                </span>
                <span class="item-col">
                    <label class="item-label">SNAT公网IP：</label>
                    <s-tooltip trigger="hover">
                        <div slot="content">
                            {{isEnhanced ? instance.bindEips : instance.eips | snatEipContent('multiple')}}
                        </div>
                        <span class="item-content ip-content">
                            {{isEnhanced ? instance.bindEips : instance.eips | snatEipContent}}
                        </span>
                    </s-tooltip>
                </span>
                <span class="item-col">
                    <label class="item-label">DNAT公网IP：</label>
                    <s-tooltip trigger="hover">
                        <div slot="content">
                            {{isEnhanced ? instance.bindEips : instance.dnatEips | dnatEipContent('multiple')}}
                        </div>
                        <span class="item-content ip-content">
                            {{isEnhanced ? instance.bindEips : instance.dnatEips | dnatEipContent}}
                        </span>
                    </s-tooltip>
                </span>
                <span class="item-col">
                    <label class="item-label">名称：</label>
                    <span class="item-content">
                        {{instance.name}}
                        <edit-popover value="{=instance.name=}" rule="{{Rule.NAME}}" on-edit="updateName">
                            <a href="javascript:void(0)" data-testid="${testID.nat.detailName}">变更</a>
                        </edit-popover>
                    </span>
                </span>
                <span class="item-col">
                    <label class="item-label">所在网络：</label>
                    <span class="item-content">
                        {{vpcInfo.name}}<span s-if="vpcInfo.cidr">（{{vpcInfo.cidr}}）</span
                        ><span s-if="vpcInfo.ipv6Cidr">（{{vpcInfo.ipv6Cidr}}）</span>
                    </span>
                </span>
                <span class="item-col">
                    <label class="item-label">描述：</label>
                    <span class="item-content">
                        {{instance.description || '-'}}
                        <edit-popover value="{=instance.description=}" rule="{{Rule.DESC}}" on-edit="updateDesc">
                            <a href="javascript:void(0)">变更</a>
                        </edit-popover>
                    </span>
                </span>
                <span class="item-col" s-if="FLAG.NetworkNatOpt">
                    <label class="item-label">到期时间：</label>
                    <span class="item-content"> {{instance.expiredTime | timeFormat}} </span>
                </span>
                <span class="item-col">
                    <label class="item-label">运行状态：</label>
                    <span class="item-content">
                        <span class="{{instance.status | statusStyle}}"> {{instance.status | statusText}} </span>
                    </span>
                </span>
                <span class="item-col">
                    <label class="item-label">释放保护：</label>
                    <span class="item-content">
                        <s-switch
                            disabled="{{deleteProtectStatus}}"
                            on-change="updateDeleteProtect"
                            checked="{=instance.deleteProtect=}"
                        />
                        <!--<s-tip
                            class="inline-tip"
                            skin="warning"
                            placement="topRight"
                            content="请确认您已解除相关的关联设备"
                        />-->
                    </span>
                </span>
                <span class="item-col" s-if="instance.clusterMode">
                    <label class="item-label">TCP连接超时时间：</label>
                    <span class="item-content">
                        {{instance.sessionConfig ? instance.sessionConfig.tcpTimeOut / 1000 : '-'}}
                    </span>
                    <s-popover s-ref="popover-tcp-edit" placement="top" trigger="click" class="edit-popover-class">
                        <div class="edit-wrap" slot="content">
                            <s-input-number
                                value="{=edit.tcp.value=}"
                                placeholder="请输入TCP连接超时时间"
                                on-change="onEditInput($event, 'tcp')"
                                max="4000"
                                width="320"
                                min="10"
                                step="1"
                            />
                            <div class="edit-tip">输入范围：【10，4000】整数</div>
                            <s-button skin="primary" s-ref="editBtn-tcp-edit" on-click="editConfirm(instance, 'tcp')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel('tcp')">取消</s-button>
                        </div>
                        <outlined-editing-square class="name-icon" on-click="onInstantEdit(instance, 'tcp')" />
                    </s-popover>
                </span>
                <span class="item-col" s-if="instance.clusterMode">
                    <label class="item-label">UDP连接超时时间：</label>
                    <span class="item-content">
                        {{instance.sessionConfig ? instance.sessionConfig.udpTimeOut / 1000 : '-'}}
                    </span>
                    <s-popover s-ref="popover-udp-edit" placement="top" trigger="click" class="edit-popover-class">
                        <div class="edit-wrap" slot="content">
                            <s-input-number
                                value="{=edit.udp.value=}"
                                max="4000"
                                placeholder="请输入UDP连接超时时间"
                                width="320"
                                on-change="onEditInput($event, 'udp')"
                                min="5"
                                step="1"
                            />
                            <div class="edit-tip">输入范围：【5，4000】整数</div>
                            <s-button skin="primary" s-ref="editBtn-udp-edit" on-click="editConfirm(instance, 'udp')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel('udp')">取消</s-button>
                        </div>
                        <outlined-editing-square class="name-icon" on-click="onInstantEdit(instance, 'udp')" />
                    </s-popover>
                </span>
                <span class="item-col" s-if="instance.clusterMode">
                    <label class="item-label">ICMP连接超时时间：</label>
                    <span class="item-content">
                        {{instance.sessionConfig ? instance.sessionConfig.icmpTimeOut / 1000 : '-'}}
                    </span>
                    <s-popover s-ref="popover-icmp-edit" placement="top" trigger="click" class="edit-popover-class">
                        <div class="edit-wrap" slot="content">
                            <s-input-number
                                value="{=edit.icmp.value=}"
                                on-change="onEditInput($event, 'tcp')"
                                placeholder="请输入ICMP连接超时时间"
                                max="4000"
                                width="320"
                                min="5"
                                step="1"
                            />
                            <div class="edit-tip">输入范围：【5，4000】整数</div>
                            <s-button skin="primary" s-ref="editBtn-icmp-edit" on-click="editConfirm(instance, 'icmp')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel('icmp')">取消</s-button>
                        </div>
                        <outlined-editing-square class="name-icon" on-click="onInstantEdit(instance, 'icmp')" />
                    </s-popover>
                </span>
                <span class="item-col" s-if="FLAG.NetworkNatSupOrganization">
                    <label class="item-label">项目信息：</label>
                    <span class="item-content">
                        <span s-for="item,index in instance.resourceGroups"> {{item.resourceGroupName}} </span>
                    </span>
                </span>
            </dl>
            <dl s-if="{{instance.clusterMode}}" class="enhance-nat-wrapper">
                <dt><h4>网关性能</h4></dt>
                <span class="item-col">
                    <label class="item-label">新建连接数：</label>
                    <span class="item-content">
                        <span>{{instance.cuNum ? instance.cuNum * 1000 + '个/秒' : '-'}}</span>
                    </span>
                </span>
                <span class="item-col">
                    <label class="item-label">并发连接数：</label>
                    <span class="item-content">
                        <span>{{instance.cuNum ? instance.cuNum * 10000 + '个' : '-'}}</span>
                    </span>
                </span>
                <span class="item-col">
                    <label class="item-label">处理流量：</label>
                    <span class="item-content">
                        <span>{{instance.cuNum ? instance.cuNum + 'Gbps' : '-'}}</span>
                    </span>
                </span>
            </dl>
        </div>
    </div>
</div>`;
/* eslint-enable */
@template(tpl)
@invokeSUI
@asComponent('@nat-detail')
@invokeComp('@edit-popover')
class NatDetail extends Component {
    static components = {
        's-icon': Icon,
        's-clip-board': ClipBoard,
        's-switch': Switch,
        's-input-number': InputNumber,
        's-button': Button,
        's-tip': Tip,
        's-tooltip': Tooltip,
        's-popover': Popover,
        'outlined-editing-square': OutlinedEditingSquare
    };
    static filters = {
        timeFormat(time) {
            if (!time) {
                return '-';
            }
            return toTime(time);
        },
        dnatEipContent(eips, showType) {
            return showType === 'multiple' ? _.pluck(eips, 'eip').join('，') || '-' : _.pluck(eips, 'eip')[0] || '-';
        },
        snatEipContent(eips, showType) {
            return showType === 'multiple' ? _.pluck(eips, 'eip').join('，') || '-' : _.pluck(eips, 'eip')[0] || '-';
        },
        statusStyle(status) {
            let config = NatStatus.fromValue(status);
            return config ? config.styleClass : '';
        },
        statusText(status) {
            let config = NatStatus.fromValue(status);
            return config ? config.text : '';
        }
    };
    static computed = {
        isEnhancedIPv6() {
            const instance = this.data.get('instance');
            return instance?.ipVersion === 'v6';
        },
        isEnhanced() {
            const instance = this.data.get('instance');
            return instance.clusterMode;
        }
    };
    initData() {
        return {
            FLAG,
            klass: 'vpc-nat-detail',
            withSidebar: true,
            config: [],
            current: 'detail',
            Rule: Rule.DETAIL_EDIT,
            instance: {},
            deleteProtectStatus: false
        };
    }
    inited() {
        this.children = [];
        this.data.set('instance', this.data.get('context').instance);
    }
    attached() {
        this.loadVpcDetail();
    }
    loadVpcDetail() {
        const vpcId = this.data.get('context').vpcId;
        return this.$http
            .getVpcDetail({
                vpcIds: [vpcId]
            })
            .then(data => {
                const result = data[vpcId] || {};
                this.data.set('vpcInfo', result);
            });
    }
    updateName(value) {
        const instance = this.data.get('instance');
        const {id, ipVersion} = instance;
        let reqUrl = 'updateNat';
        const payload = {
            natGatewayId: id,
            name: value
        };
        const isEnhancedIPv6 = ipVersion === 'v6';
        if (isEnhancedIPv6) {
            reqUrl = 'updateNatIPv6';
            payload.natId = id;
            delete payload.natGatewayId;
        }
        this.$http[reqUrl](payload).then(() => {
            this.fire('updateName', value);
            this.data.get('context')?.updateName();
        });
    }
    updateDesc(value) {
        const instance = this.data.get('instance');
        const {id, ipVersion} = instance;
        let reqUrl = 'updateNat';
        const payload = {
            natGatewayId: id,
            description: value
        };
        const isEnhancedIPv6 = ipVersion === 'v6';
        if (isEnhancedIPv6) {
            reqUrl = 'updateNatIPv6';
            payload.natId = id;
            delete payload.natGatewayId;
        }
        this.$http[reqUrl](payload).then(() => {
            this.data.set('instance.description', value);
        });
    }
    // 更新释放保护状态
    updateDeleteProtect({value}) {
        this.data.set('deleteProtectStatus', true);
        this.$http
            .natUpdateDeleteProject({
                natGatewayId: this.data.get('instance.id'),
                deleteProtect: value
            })
            .then(() => {
                this.data.set('deleteProtectStatus', false);
                this.data.set('vpcInfo.deleteProtect', value);
            })
            .catch(() => {
                this.data.set('deleteProtectStatus', false);
                this.data.set('vpcInfo.deleteProtect', !value);
            });
    }

    // 点击修改icon
    onInstantEdit(instance, type) {
        let value = type === 'tcp' ? 900 : type === 'udp' ? 30 : 30;
        this.data.set(
            `edit.${type}.value`,
            instance.sessionConfig ? instance.sessionConfig[type + 'TimeOut'] / 1000 : value
        );
        this.data.set(`edit.${type}.error`, false);
        const editPop = this.ref(`popover-${type}-edit`);
        editPop.data.set('visible', !editPop.data.get('visible'));
    }
    // 编辑弹框-提交
    editConfirm(instance, type) {
        let edit = this.data.get(`edit.${type}`);
        if (edit.error) {
            return;
        }
        let icmpTimeOut = instance.sessionConfig?.icmpTimeOut
            ? instance.sessionConfig?.icmpTimeOut > 4000000
                ? 4000000
                : instance.sessionConfig?.icmpTimeOut
            : 30;
        let udpTimeOut = instance.sessionConfig?.udpTimeOut
            ? instance.sessionConfig?.udpTimeOut > 4000000
                ? 4000000
                : instance.sessionConfig?.udpTimeOut
            : 30;
        let tcpTimeOut = instance.sessionConfig?.tcpTimeOut
            ? instance.sessionConfig?.tcpTimeOut > 4000000
                ? 4000000
                : instance.sessionConfig?.tcpTimeOut
            : 30;

        let payload = {
            icmpTimeOut,
            udpTimeOut,
            tcpTimeOut
        };
        const {id, name, ipVersion} = instance;
        let reqUrl = 'updateNat';
        const updatePayload = {
            natGatewayId: id,
            name,
            sessionConfig: {
                ...payload,
                [type + 'TimeOut']: edit.value > 4000 ? 4000000 : edit.value * 1000
            }
        };
        const isEnhancedIPv6 = ipVersion === 'v6';
        if (isEnhancedIPv6) {
            reqUrl = 'updateNatIPv6';
            updatePayload.natId = id;
            updatePayload.icmpTimeout = icmpTimeOut;
            updatePayload.udpTimeout = udpTimeOut;
            updatePayload.tcpTimeout = tcpTimeOut;
            updatePayload[type + 'Timeout'] = edit.value > 4000 ? 4000000 : edit.value * 1000;
            delete updatePayload.natGatewayId;
            delete updatePayload.sessionConfig;
        }

        this.$http[reqUrl](updatePayload).then(() => {
            this.editCancel(type);
            Notification.success('修改成功');
            this.data.get('context')?.updateName();
            this.data.set(
                `instance.sessionConfig.${type + 'TimeOut'}`,
                edit.value > 4000 ? 4000000 : edit.value * 1000
            );
        });
    }
    // 编辑弹框-取消
    editCancel(type) {
        this.ref(`popover-${type}-edit`).data.set('visible', false);
    }
}
export default San2React(Processor.autowireUnCheckCmpt(NatDetail));
