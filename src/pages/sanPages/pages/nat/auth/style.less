.nat-auth-dialog {
    .img-preview {
        margin: auto;
        text-align: center;
        position: relative;
        height: 100%;
        img {
            width: 100%;
            position: relative;
            top: 50%;
            transform: translateY(-50%);
        }
    }
}
.nat-auth {
    .bui-biz-order .bui-biz-order-container {
        .icon-0 {
            background: url('https://bce.bdstatic.com/network-frontend/nat-create.png') no-repeat;
        }
        .icon-1 {
            background: url('https://bce.bdstatic.com/network-frontend/nat-set.png') no-repeat;
        }
        .icon-2 {
            background: url('https://bce.bdstatic.com/network-frontend/nat-set.png') no-repeat;
        }
        .icon-3 {
            background: url('https://bce.bdstatic.com/network-frontend/monitor.png') no-repeat;
        }
    }
    .s-new-app-order-page-panel-title {
        padding-left: 0px !important;
        margin-bottom: 16px !important;
    }
    @media screen and (max-width: 1280px) {
        .s-new-app-order-page-info {
            background:
                url('https://bce.bdstatic.com/network-frontend/common-intro-bg-1280px.png') no-repeat right,
                linear-gradient(140deg, #f3f7ff 0%, #cddbf3 100%) !important;
            background-size: auto 360px !important;
            border-radius: 6px;
        }
    }
    @media screen and (max-width: 1440px) {
        .s-new-app-order-page-info {
            background:
                url('https://bce.bdstatic.com/network-frontend/common-intro-bg-1440px.png') no-repeat right,
                linear-gradient(140deg, #f3f7ff 0%, #cddbf3 100%) !important;
            background-size: auto 360px !important;
            border-radius: 6px;
        }
    }
    @media screen and (max-width: 1680px) {
        .s-new-app-order-page-info {
            background:
                url('https://bce.bdstatic.com/network-frontend/common-intro-bg-1680px.png') no-repeat right,
                linear-gradient(140deg, #f3f7ff 0%, #cddbf3 100%) !important;
            background-size: auto 360px !important;
            border-radius: 6px;
        }
    }
    @media screen and (min-width: 1680px) {
        .s-new-app-order-page-info {
            background:
                url('https://bce.bdstatic.com/network-frontend/common-intro-bg-1920px.png') no-repeat right,
                linear-gradient(140deg, #f3f7ff 0%, #cddbf3 100%) !important;
            background-size: auto 360px !important;
            border-radius: 6px;
        }
    }
    .s-new-app-order-page-feature-item {
        height: auto;
        padding: 12px 16px;
    }
    .s-new-app-order-page-advantage-item {
        width: 259px;
        flex: none;
    }
    .s-new-app-order-page-info-protocal {
        margin-bottom: 0px;
    }
    .s-new-app-order-page-info-desc {
        margin-bottom: 110px;
    }
    .s-new-app-order-page-info-create {
        display: flex;
    }
    .s-new-app-order-page-scene-item {
        height: auto;
        padding-top: 12px;
        padding-bottom: 0;
    }
    .nat-open-extra {
        display: flex;
        border: 1px solid #ffb6b3;
        border-radius: 6px;
        padding: 0 16px;
        height: 40px;
        line-height: 40px;
        align-items: center;
        cursor: pointer;
        .open-extra-desc {
            font-size: 12px;
            color: #f33e3e;
            line-height: 20px;
            font-weight: 400;
            height: 20px;
        }
        .open-extra-title {
            background-image: linear-gradient(90deg, #f3413f 22%, #f86454 100%);
            color: #fff;
            line-height: 20px;
            font-weight: 400;
            font-size: 12px;
            border-radius: 1.6px;
            width: 60px;
            text-align: center;
            margin-right: 8px;
        }
    }
    .scene-content {
        .img-title-wrapper {
            display: flex;
            margin-bottom: 8px;
            align-items: center;
            .item-img {
                display: inline-block;
                vertical-align: sub;
                width: 32px;
                height: 32px;
                margin-right: 12px;
            }
            .item-title {
                font-size: 14px;
                color: #151b26;
                line-height: 24px;
                font-weight: 500;
            }
        }
        .content-item {
            margin-bottom: 16px;
        }
        .subTitle-class {
            font-size: 12px;
            color: #151b26;
            line-height: 22px;
            font-weight: 500;
        }
        .subDesc-class {
            font-size: 12px;
            color: #5c5f66;
            line-height: 22px;
            font-weight: 400;
        }
        .scene-btn {
            padding-left: 0;
            margin-top: 8px;
        }
    }
    .s-new-app-order-page-feature-item-desc {
        height: auto;
    }
    .s-new-app-order-page-advantage {
        height: 176px;
    }
    .s-new-app-order-page-feature-item-icon {
        width: 32px;
        height: 32px;
    }
    .s-new-app-order-page-feature-item-title {
        height: 32px;
    }
    .s-new-app-order-page {
        height: 93%;
        overflow-y: auto;
    }
    .s-new-app-order-page-advantage-item-desc {
        margin-top: 8px;
    }
}
