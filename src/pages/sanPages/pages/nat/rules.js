/**
 * @file pages/nat/rules.js
 * <AUTHOR>
 */

import _ from 'lodash';

import {NatStatus, NatFlavor, PayType} from '@/pages/sanPages/common/enum';
import {ContextService} from '@/pages/sanPages/common';
const AllRegion = ContextService.getEnum('AllRegion');
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import moment from 'moment';

const currentModule = 'NAT';
const getBindedGroupEip = (item = {}) => {
    if (item.eips && item.eips.length > 0 && item.eips[0].shareGroupId) {
        return item.eips[0].shareGroupId;
    }
    if (item.dnatEips && item.dnatEips.length > 0 && item.dnatEips[0].shareGroupId) {
        return item.dnatEips[0].shareGroupId;
    }
    return '';
};

export default {
    addDnat: [
        {
            required: false,
            message: ''
        },
        {
            custom(data, options = {}) {
                if (options.free <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: 'DNAT条目配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `DNAT条目配额不足，<a href="${
                                '/quota_center/#/quota/apply/create?serviceType='
                            }${currentModule}&region=${
                                window?.$context?.getCurrentRegionId()
                            }&cloudCenterQuotaName=dnatRuleQuota" target="_blank">去申请配额</a>`
                        };
                    }
                }
            }
        }
    ],
    addSnat: [
        {
            required: false,
            message: ''
        },
        {
            custom(data, options = {}) {
                if (options.free <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: 'SNAT条目配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `SNAT条目配额不足，<a href="${
                                '/quota_center/#/quota/apply/create?serviceType='
                            }${currentModule}&region=${
                                window?.$context?.getCurrentRegionId()
                            }&cloudCenterQuotaName=snatRuleQuota" target="_blank">去申请配额</a>`
                        };
                    }
                }
            }
        }
    ],
    deleteDnat: [
        {
            required: true,
            message: '请先选择实例对象'
        }
    ],
    deleteSnat: [
        {
            required: true,
            message: '请先选择实例对象'
        }
    ],
    recharge: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            status: [
                NatStatus.ACTIVE,
                NatStatus.UPDATING,
                NatStatus.UNCONFIGURED,
                NatStatus.DOWN,
                NatStatus.STARTING,
                NatStatus.CONFIGURING,
                NatStatus.REBOOTING
            ],
            message(data) {
                if (data.length > 1) {
                    return '部分网关当前状态不支持续费。如需操作，请稍后重试';
                }
                return '当前状态下不支持续费。如需操作，请稍后重试';
            }
        },
        {
            productType: [PayType.PREPAY],
            field: 'productType',
            message: '后付费实例无需进行续费操作'
        },
        {
            custom(data) {
                console.log(data, 'data');
                if (_.some(data, item => item.orderProductPayType)) {
                    return {
                        disable: true,
                        message: '计费变更中的实例不可续费，请稍后重试'
                    };
                }
                if (_.find(data, item => item.taskStatus === 'BUSY' || item.taskStatus === 'ORDER_BUSY')) {
                    return {
                        disable: true,
                        message: `当前账号有未完成订单，请前往
                            ${
                                FLAG.NetworkSupportXS
                                    ? '订单管理'
                                    : '<a href="/billing/#/order/list" target="_blank">财务中心-订单管理</a>'
                            }页面处理`
                    };
                }
                // 增强型IPv6暂不支持续费
                if (_.find(data, item => item.ipVersion === 'v6')) {
                    return {
                        disable: true,
                        message: '增强型IPv6类型的实例无法进行该操作'
                    };
                }
            }
        }
    ],
    editTag: [
        {
            required: true,
            message: '请先选择实例对象'
        }
    ],
    ALTER_PRODUCTTYPE: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            status: [NatStatus.ACTIVE, NatStatus.UNCONFIGURED, NatStatus.UPDATING],
            message(data) {
                if (data.length > 1) {
                    return '部分实例当前状态无法计费变更';
                }
                return '该实例当前状态无法计费变更';
            }
        },
        {
            custom(data) {
                let productTypes = [];
                for (let i = 0; i < data.length; i++) {
                    if (data[i].orderProductPayType) {
                        return {
                            disable: true,
                            message: '计费变更中的网关不支持再次计费变更。如需操作，请稍后重试'
                        };
                    }
                    if (data[i].task === 'auto_renew') {
                        return {
                            disable: true,
                            message: `自动续费的网关不支持计费变更。如需操作，请先关闭计费变更，关闭计费请点击<a href="${
                                'https://console.bce.baidu.com/billing/#/renew/list'
                            }" target="_blank">续费</a>`
                        };
                    }
                    productTypes.push(data[i].productType);
                }
                if (_.chain(productTypes).uniq().value().length > 1) {
                    return {
                        disable: true,
                        message:
                            '计费变更已经支持预付费转后付费和后付费转预付费两种模式。<br>请您将预付费资源和后付费资源分开操作，谢谢！'
                    };
                }
                if (_.find(data, item => item.taskStatus === 'BUSY' || item.taskStatus === 'ORDER_BUSY')) {
                    return {
                        disable: true,
                        message: `当前账号有未完成订单，请前往
                            ${
                                FLAG.NetworkSupportXS
                                    ? '订单管理'
                                    : '<a href="/billing/#/order/list" target="_blank">财务中心-订单管理</a>'
                            }页面处理`
                    };
                }
                // 增强型IPv6暂不支持计费变更
                if (_.find(data, item => item.ipVersion === 'v6')) {
                    return {
                        disable: true,
                        message: '增强型IPv6类型的实例无法进行该操作'
                    };
                }
            }
        }
    ],
    CANCEL_ALTER_PRODUCTTYPE: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            status: [NatStatus.ACTIVE, NatStatus.UNCONFIGURED, NatStatus.UPDATING],
            message(data) {
                if (data.length > 1) {
                    return '部分实例当前状态无法取消计费变更';
                }
                return '该实例当前状态无法取消计费变更';
            }
        },
        {
            custom(data) {
                if (_.some(data, item => !item.orderProductPayType)) {
                    return {
                        disable: true,
                        message: '未进行计费变更无法进行该操作'
                    };
                }
                // 增强型IPv6暂不支持取消计费变更
                if (_.find(data, item => item.ipVersion === 'v6')) {
                    return {
                        disable: true,
                        message: '增强型IPv6类型的实例无法进行该操作'
                    };
                }
            }
        }
    ],
    BIND: [
        {
            status: [NatStatus.ACTIVE, NatStatus.UNCONFIGURED],
            message() {
                return '该实例当前状态无法绑定公网ip';
            }
        },
        {
            custom(data, {item = {}}) {
                if (
                    getBindedGroupEip(item) ||
                    (!getBindedGroupEip(item) &&
                        (!item.eips || item.eips.length === 0 || !item.dnatEips || item.dnatEips.length === 0))
                ) {
                    return {
                        disable: false
                    };
                }
                if (!getBindedGroupEip(item)) {
                    return {
                        disable: false
                    };
                }
                return {
                    disable: true
                };
            }
        }
    ],
    UNBIND: [
        {
            status: [NatStatus.ACTIVE, NatStatus.UNCONFIGURED, NatStatus.DOWN],
            message() {
                return '该实例当前状态无法解绑';
            }
        },
        {
            custom(data, {item = {}}) {
                !item.eips && (item.eips = []);
                !item.dnatEips && (item.dnatEips = []);
                !item.bindEips && (item.bindEips = []);
                if (
                    (!item.eips || item.eips.length === 0) &&
                    (!item.dnatEips || item.dnatEips.length === 0) &&
                    (!item.bindEips || item.bindEips.length === 0)
                ) {
                    return {
                        disable: true,
                        message: ''
                    };
                }
            }
        }
    ],
    RELEASE: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            isSingle: true,
            message: '不支持批量操作，请依次进行操作'
        },
        {
            custom(data, {item = {}, mirrorList = []}) {
                let isPrepay = item.productType === PayType.PREPAY;
                let activeStatus = [NatStatus.ACTIVE, NatStatus.UNCONFIGURED, NatStatus.DOWN];
                let isMirrorBindList = mirrorList.filter(data => data.sourceId === item.id);
                if (isPrepay && item.status !== NatStatus.DOWN) {
                    return {
                        disable: true,
                        message: '预付费NAT网关在到期前不支持释放，到期后可以释放'
                    };
                }
                if (!isPrepay && activeStatus.indexOf(item.status) === -1) {
                    return {
                        disable: true,
                        message: '当前状态不可释放'
                    };
                }
                if (isMirrorBindList.length > 0) {
                    return {
                        disable: true,
                        message: '当前网关已绑定流量镜像不可释放，如需释放，请先删除镜像会话'
                    };
                }
                if (item.taskStatus === 'BUSY' || item.taskStatus === 'ORDER_BUSY') {
                    return {
                        disable: true,
                        message: `当前账号有未完成订单，请前往
                            ${
                                FLAG.NetworkSupportXS
                                    ? '订单管理'
                                    : '<a href="/billing/#/order/list" target="_blank">财务中心-订单管理</a>'
                            }页面处理`
                    };
                }
                if (item.deleteProtect) {
                    return {
                        disable: true,
                        message: '该实例开启了释放保护功能，请在实例详情页面中关闭释放保护后再点击释放'
                    };
                }
            }
        }
    ],
    NATUPGRADE: [
        {
            status: [NatStatus.ACTIVE, NatStatus.UNCONFIGURED],
            message: '仅运行中和未配置状态的网关可进行网关升级'
        },
        {
            custom(data, {item = {}}) {
                let isPrepay = item.productType === PayType.PREPAY;
                if (item.orderProductPayType) {
                    return {
                        disable: true,
                        message: '计费变更中的网关不可进行网关升级。如需操作，请稍后重试'
                    };
                }
                if (isPrepay) {
                    const oneDayTimeStamp = 24 * 60 * 60 * 1000;
                    const isLastOneDay = moment(item.expiredTime).valueOf() - moment().valueOf() < oneDayTimeStamp;
                    if (isLastOneDay) {
                        return {
                            disable: true,
                            message: '到期前1天内无法操作，如需操作，请续费后操作。'
                        };
                    }
                }
                if (item.taskStatus === 'BUSY' || item.taskStatus === 'ORDER_BUSY') {
                    return {
                        disable: true,
                        message: `当前账号有未完成订单，请前往
                            ${
                                FLAG.NetworkSupportXS
                                    ? '订单管理'
                                    : '<a href="/billing/#/order/list" target="_blank">财务中心-订单管理</a>'
                            }页面处理`
                    };
                }
            }
        }
    ],
    EDIT_RES: [
        {
            required: true,
            message: '请先选择实例对象'
        }
    ],
    changeResource: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            isSingle: true,
            message: '不支持批量变更'
        }
    ],
    deleteRules: [
        {
            required: true,
            message: '请先选择策略'
        }
    ],
    natSinDisable: [
        {
            required: false,
            message: ''
        },
        {
            custom(data) {
                if (window.$context.getCurrentRegionId() === AllRegion.SIN) {
                    return {
                        disable: true,
                        message: '新加坡地域资源售罄，请您切换到其他地域创建'
                    };
                }
            }
        }
    ],
    addPrivateSnat: [
        {
            required: false,
            message: ''
        },
        {
            custom(data, options = {}) {
                if (options.free <= 0) {
                    return {
                        disable: true,
                        message: 'SNAT规则配额不足'
                    };
                }
            }
        }
    ],
    addPrivateDnat: [
        {
            required: false,
            message: ''
        },
        {
            custom(data, options = {}) {
                if (options.free <= 0) {
                    return {
                        disable: true,
                        message: 'DNAT规则配额不足'
                    };
                }
            }
        }
    ],
    addIPv6Dnat: [
        {
            required: false,
            message: ''
        },
        {
            custom(data, options = {}) {
                if (options.free <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: 'DNAT条目配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `DNAT条目配额不足，<a href="${
                                '/quota_center/#/quota/apply/create?serviceType='
                            }${currentModule}&region=${
                                window?.$context?.getCurrentRegionId()
                            }&cloudCenterQuotaName=dnatRuleQuota" target="_blank">去申请配额</a>`
                        };
                    }
                }
            }
        }
    ],
    addIPv6Snat: [
        {
            required: false,
            message: ''
        },
        {
            custom(data, options = {}) {
                if (options.free <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: 'SNAT条目配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `SNAT条目配额不足，<a href="${
                                '/quota_center/#/quota/apply/create?serviceType='
                            }${currentModule}&region=${
                                window?.$context?.getCurrentRegionId()
                            }&cloudCenterQuotaName=snatRuleQuota" target="_blank">去申请配额</a>`
                        };
                    }
                }
            }
        }
    ],
    diagnose: [
        {
            required: true,
            message: '请先选中实例'
        },
        {
            custom(data, {item = {}}) {
                if (data && data[0] && data[0].status === 'building') {
                    return {
                        disable: true,
                        message: '当前状态不支持实例诊断'
                    };
                }
            }
        }
    ],
    pathAnalysis: [
        {
            required: true,
            message: '请先选中实例'
        }
    ]
};
