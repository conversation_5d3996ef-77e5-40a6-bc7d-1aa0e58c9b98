/**
 * @file VPC - NAT - SubSidebar
 *
 * <AUTHOR>
 */
import _ from 'lodash';

import Client from '@/pages/sanPages/common/client';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';

const http = new Client();
export function subSidebarConfig(vpcId, natId) {
    const defaultSidebar = [
        {
            id: 'detail',
            name: '详情',
            url: `#/vpc/nat/detail?vpcId=${vpcId}&id=${natId}`
        },
        {
            id: 'snat',
            name: 'SNAT列表',
            url: `#/vpc/nat/snat/list?vpcId=${vpcId}&id=${natId}`
        },
        {
            id: 'dnat',
            name: 'DNAT列表',
            url: `#/vpc/nat/dnat/list?vpcId=${vpcId}&id=${natId}`
        },
        {
            id: 'monitor',
            name: '监控',
            url: `#/vpc/nat/monitor?vpcId=${vpcId}&id=${natId}`
        }
    ];
    if (!FLAG.NetworkNatOpt) {
        defaultSidebar.pop();
    }
    return http.natBlackList()
        .then(data => {
            if (data) {
                return _.filter(defaultSidebar, item => item.id !== 'dnat');
            }
            return defaultSidebar;
        })
        .catch(() => _.filter(defaultSidebar, item => item.id !== 'dnat'));
}
