import {PayType, NatStatus} from '@/pages/sanPages/common/enum';

export const columns = [
    {
        name: 'id',
        label: 'NAT网关名称/ID',
        width: 160,
        fixed: 'left'
    },
    {
        name: 'status',
        label: '状态',
        width: 90,
        filter: {
            options: [
                {
                    text: '全部',
                    value: ''
                },
                ...NatStatus.toArray()
            ],
            value: ''
        }
    },
    {
        name: 'diagnose',
        label: '诊断',
        width: 160
    },
    {
        name: 'vpcId',
        label: '所在网络',
        width: 180
    },
    {
        name: 'flavor',
        label: '类型',
        width: 120
    },
    {
        name: 'cuNum',
        label: '性能容量',
        width: 80
    },
    {
        name: 'productType',
        label: '支付方式',
        width: 120,
        filter: {
            options: [
                {
                    text: '全部',
                    value: ''
                },
                {
                    text: PayType.getTextFromAlias('PREPAY'),
                    value: PayType.PREPAY
                },
                {
                    text: PayType.getTextFromAlias('POSTPAY'),
                    value: PayType.POSTPAY
                }
            ],
            value: ''
        }
    },
    {
        name: 'snat',
        label: '公网IP',
        width: 230
    },
    {
        name: 'expiredTime',
        label: '到期时间',
        sortable: true,
        width: 160
    },
    {
        name: 'tag',
        label: '标签',
        sortable: true,
        width: 100
    },
    {
        name: 'description',
        label: '描述',
        width: 160
    },
    {
        name: 'groups',
        label: '资源分组',
        width: 100
    },
    {
        name: 'opt',
        label: '操作',
        width: 110,
        fixed: 'right'
    }
];

export const PRIVATE_COLUMNS = [
    {
        name: 'id',
        label: 'NAT网关名称/ID',
        width: 200,
        fixed: 'left'
    },
    {
        name: 'status',
        label: '状态',
        width: 100,
        filter: {
            options: [
                {
                    text: '全部',
                    value: ''
                },
                ...NatStatus.toArray().filter(
                    item => !['unconfigured', 'updating', 'starting', 'rebooting', 'stopping'].includes(item.value)
                )
            ],
            value: ''
        }
    },
    {
        name: 'diagnose',
        label: '诊断',
        width: 160
    },
    {
        name: 'vpcId',
        label: '所在网络',
        width: 140
    },
    {
        name: 'natIp',
        label: 'NAT IP',
        width: 100
    },
    {
        name: 'cuNum',
        label: '性能容量',
        width: 100
    },
    {
        name: 'productType',
        label: '支付方式',
        width: 200,
        filter: {
            options: [
                {
                    text: '全部',
                    value: ''
                },
                {
                    text: PayType.getTextFromAlias('PREPAY'),
                    value: PayType.PREPAY
                },
                {
                    text: PayType.getTextFromAlias('POSTPAY'),
                    value: PayType.POSTPAY
                }
            ],
            value: ''
        }
    },
    {
        name: 'tag',
        label: '标签',
        sortable: true,
        width: 80
    },
    {
        name: 'description',
        label: '描述',
        width: 200
    },
    {
        name: 'groups',
        label: '资源分组',
        width: 120
    },
    {
        name: 'opt',
        label: '操作',
        width: 244,
        fixed: 'right'
    }
];

export const MORE_OPTIONS = [
    {label: '性能容量变配', value: 'CHANGECU'},
    {label: '释放', value: 'RELEASE'}
];
