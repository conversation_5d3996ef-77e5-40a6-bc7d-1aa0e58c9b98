/* eslint-disable @typescript-eslint/member-ordering */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams, request} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {Notification, Input} from '@baidu/sui';
import {
    OutlinedRefresh,
    OutlinedPlus,
    OutlinedLink,
    OutlinedEditingSquare,
    OutlinedDownload,
    OutlinedDocument,
    OutlinedDown
} from '@baidu/sui-icon';
import {ResourceGroupDialog} from '@baidu/bce-resourcegroup-sdk-san';
import {DiagnoseSDKProcessor, AnalysisSDKProcessor} from '@baidu/bce-vpc-sdk-react';
import {ResourceGroupSDK} from '@baidu/bce-resourcegroup-sdk';

import {Label} from '@baidu/xicon-san';
import Bind from '../components/bind';
import rules from '../rules';
import MoreOperation from '../components/MoreOperation';
import {columns, PRIVATE_COLUMNS, MORE_OPTIONS} from './tableField';
import Monitor from './bcmDetail';
import {$flag as FLAG, uniqBy, recharge, alterProductType, utcToTime} from '@/pages/sanPages/utils/helper';
import Confirm from '@/pages/sanPages/components/confirm';
import DiagnoseConfirm from '@/pages/sanPages/components/diagnoseConfirm/confirm';
import {NatStatus, PayType, NatFlavor, NatType, DocService} from '@/pages/sanPages/common';
import NatListOperation from '@/pages/sanPages/pages/nat/list/moreOperation';
import testID from '@/testId';
import {NAT_ENHANCED_MAPPING} from '@/pages/sanPages/common/enum';
import './style.less';
import '@baidu/bce-vpc-sdk-react/lib/style.css';

const processor = new DiagnoseSDKProcessor();
const processorPath = new AnalysisSDKProcessor();
const {asComponent, invokeSUI, invokeSUIBIZ, invokeAppComp, template, invokeComp} = decorators;
const AllRegion = window.$context.getEnum('AllRegion');

const tpl = html`
<div class="nat-list-widget">
    <div class="nat-header-wrapper">
        <span class="title">NAT 网关</span>
        <vpc-select
            class="vpc-select"
            on-change="vpcChange"
            on-int="vpcInt" />
        <div class="header-button-wrap">
            <s-button
                skin="stringfy"
                on-click="showIntro"
                class="intro-btn {{!introShow && 'intro-hide'}}"
            >
                <outlined-doc class="outlined-doc {{!introShow && 'intro-hide'}}"/>功能简介
            </s-button>
            <s-button skin="stringfy" on-click="showExpireData" s-if="!showMode && !isPrivate">
                7天即将到期
            </s-button>
            <s-button s-else-if="!isPrivate" on-click="showExpireData" class="butotn-shortcut">
                7天即将到期
                <s-icon name="close"/>
            </s-button>
            <div s-if="{{!FLAG.NetworkSupportXS}}" class="link-wrap {{isPrivate && 'no-margin-left'}}">
                <div class="service-best-practice"> </div>
                <a class="nat-tip" target="_BLANK" href="/billing/#/renew/list~serviceType=NAT" style="margin-right:16px">
                    自动续费
                </a>
                <div class="service-best-practice"> </div>
                <a class="nat-tip" target="_BLANK" href="{{docLink}}">
                    NAT 网关最佳实践
                </a>
            </div>
        </div>
    </div>
    <s-app-list-page class="{{klass}}">
        <div class="nat-list-header {{introShow ? 'intro-show-margin' : ''}}" slot="pageTitle">
            <div class="nat-intro-wrapper {{introShow ? '' : 'intro-show'}}">
              <div class="nat-intro-header">
                <span class="nat-intro-title">{{introduceTitle}}</span>
                <s-button class="close-btn" skin="light-stringfy" on-click="closeIntro">隐藏</s-button>
              </div>
              <div class="nat-intro-steps">
                <div s-for="item, index in natIntro" class="nat-intro-step">
                  <img class="step-img" src="{{item.img}}"/>
                  <div class="step-title-wrap">
                    <div class="step-title-index">{{index + 1}}</div>
                    <div class="step-title">{{item.title}}</div>
                  </div>
                  <div class="step-content-wrap">
                    <span class="step-content">{{item.content}}</span>
                    <a s-if="{{item.btn}}" skin="stringfy" class="step-link" href="{{item.href}}">{{item.btn}}</a>
                  </div>
                </div>
              </div>
            </div>
        </div>
        <div slot="bulk" class="nat-list-table">
            <s-tip-button
                skin="primary"
                isDisabledVisibile="{{true}}"
                on-click="onCreate"
                track-id="vpc_nat_create"
                disabled="{{accountState.disabled || natSinDisable.disable || iamPass.disable || (isPrivate && quotaLimitTip)}}"
            >
                <div slot="content">
                    <!--bca-disable-next-line-->
                    {{(accountState.message || natSinDisable.message || iamPass.message || quotaLimitTip) | raw}}
                </div>
                <outlined-plus/>
                创建NAT网关
            </s-tip-button>
            <s-tip-button
                s-if="!isPrivate"
                disabled="{{natSinDisable.disable || recharge.disable || accountState.disabled}}"
                isDisabledVisibile="{{true}}"
                on-click="onRecharge"
                class="left_class"
            >
                <div slot="content">
                    <!--bca-disable-next-line-->
                    {{natSinDisable.message || recharge.message || accountState.message | raw}}
                </div>
                续费
            </s-tip-button>
            <s-tip-button
                s-if="!isPrivate"
                disabled="{{releaseInfo.disabled}}"
                isDisabledVisibile="{{true}}"
                class="left_class"
                on-click="onNatRelease"
            >
                <div slot="content">
                    {{releaseInfo.message}}
                </div>
                释放
            </s-tip-button>
            <edit-tag
                selectedItems="{{table.selectedItems}}"
                on-success="refresh"
                class="left_class"
                type="{{isPrivate ? 'PRIVATE_NAT' : 'NAT'}}"
            ></edit-tag>
            <div class="intro-warp left_class"
                data-intro="这里是批量操作区"
                s-if="{{!inBjksRegion && !isPrivate}}"
            >
                <s-tooltip content="请先选择实例对象"
                    trigger="{{operationDisabled ? 'hover' : ''}}" placement="top">
                    <s-select placeholder="批量操作" value="{=operation=}"
                        class="{{!operationDisabled ? 'placeholder-style' : ''}}"
                        disabled="{{operationDisabled}}"
                        on-change="onOperationChange">
                        <s-select-option class="operation-select"
                            s-for="item in OperationType"
                            value="{{item.value}}"
                            label="{{item.label}}"
                            disabled="{{item.disabled}}"
                        >
                            <s-tooltip placement="right"
                                trigger="{{item.message ? 'hover' : ''}}" width="200">
                                <div slot="content">
                                    <!--bca-disable-next-line-->
                                    {{item.message | raw}}
                                </div>
                                <div>{{item.label}}</div>
                            </s-tooltip>
                        </s-select-option>
                    </s-select>
                </s-tooltip>
            </div>
        </div>
        <div slot="filter">
            <div class="nat-buttons-wrap">
                <search-tag
                    s-ref="search"
                    serviceType="{{isPrivate ? 'PRIVATE_NAT' : 'NAT'}}"
                    searchbox="{=searchbox=}"
                    on-search="onSearch"
                ></search-tag>
                <s-button
                    on-click="refresh" class="s-icon-button left_class" track-name="刷新"
                    ><outlined-refresh class="icon-class"/></s-button>
                <s-button s-if="!isPrivate" on-click="onDownload" class="s-icon-button" track-id="ti_vpc_nat_download" track-name="下载"><outlined-download class="icon-class"/></s-button>
                <custom-column
                    class="left_class"
                    columnList="{{customColumn.datasource}}"
                    initValue="{{customColumn.value}}"
                    type="{{isPrivate ? 'privateNat' : 'nat'}}"
                    on-init="initColumns"
                    on-change="onCustomColumns">
                </custom-column>
            </div>
        </div>
        <s-table
            columns="{{table.columns}}"
            loading="{{table.loading}}"
            error="{{table.error}}"
            datasource="{{table.datasource}}"
            on-selected-change="tableSelected($event)"
            on-filter="onFilter"
            on-sort="onSort"
            selection="{=table.selection=}"
            track-id="ti_vpc_instance_table"
            track-name="列表操作"
            data-test-id="{{listTableTestId}}"
        >
            <div slot="empty">
                <s-empty
                    on-click="onCreate"
                    track-id="vpc_nat_create"
                    class="{{iamPass.disable ? 'create-disable' : ''}}"
                >
                </s-empty>
            </div>
            <div slot="c-id">
                    <span class="truncated">
                        <s-tooltip content="{{row.name}}">
                            <a
                                href="#/vpc/nat/detail?vpcId={{row | getNatVpcId}}&id={{row | getNatId}}&natType={{natType}}&ipVersion={{row.ipVersion}}"
                                track-id="vpc_nat_enter_detail"
                                data-testid="{{listNameTestId}}{{rowIndex}}"
                            >
                                {{row.name}}
                            </a>
                        </s-tooltip>
                    </span>
                    <s-popover
                        style="display: {{isPrivate && (row.status === NatStatus.BUILDING || row.status === NatStatus.DELETING || row.status === NatStatus.CONFIGURING) ? 'none' : ''}}"
                        s-ref="popover-name-{{rowIndex}}"
                        placement="top"
                        trigger="click"
                        class="edit-popover-class"
                    >
                        <div class="edit-wrap" slot="content">
                            <s-input value="{=edit.name.value=}"
                                width="320"
                                placeholder="请输入名称"
                                on-input="onEditInput($event, rowIndex, 'name')"/>
                            <div class="edit-tip">大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65</div>
                            <s-button skin="primary" s-ref="editBtn-name-{{rowIndex}}" disabled="{{true}}"
                                on-click="editConfirm(row, rowIndex, 'name')">确定</s-button>
                            <s-button on-click="editCancel(rowIndex, 'name')">取消</s-button>
                        </div>
                        <outlined-editing-square class="name-icon" on-click="onInstantEdit(row, rowIndex, 'name')"/>
                    </s-popover>
                    <br>
                    <span class="truncated">{{row.id || row.natId}}</span>
                    <s-clip-board class="name-icon" text="{{row.id || row.natId}}"/>
            </div>
            <div slot="c-vpcId">
                <div class="truncated">
                    <s-tooltip content="{{row.vpcName || '-'}}">
                        <a data-testid="${testID.nat.listVpc}" href="#/vpc/instance/detail?vpcId={{row.vpcId}}"
                        class="list-link">{{row.vpcName || '-'}}</a>
                    </s-tooltip>
                </div>
                <br>
                <span class="truncated">
                    <s-tooltip content="{{row.vpcShortId || row.vpcId || '-'}}">
                        {{row.vpcShortId || row.vpcId || '-'}}
                    </s-tooltip>
                </span>
            </div>
            <div slot="c-natIp">
                <s-popover placement="top" class="nat-ip-dropdown">
                    <div slot="content">
                        <div on-click="handleNatIpLink(row, item)" class="nat-ip-item" s-for="item, index in row.natIps " key="{{item.value}}">
                            {{item.natIp}}
                        </div>
                    </div>
                    <span class="natIp-wrapper">{{row.natIps.length || 0}}</span>
                </s-popover>
            </div>
            <div slot="c-productType">
                <span>{{row | getProductType}}</span>
                <span s-if="{{row.task === 'auto_renew' && row.productType === 'prepay'}}"
                    class="icon-auto-renew"
                    name="auto-renew" title="已开通自动续费">
                </span>
                <s-popover s-if="row.enableProduct" placement="top">
                    <div slot="content">
                        该实例已开通计费变更-预付费转后付费，将会在到期后转为后付费资源，请关注！如需进行续费、升级等操作，请先取消计费变更，谢谢！
                    </div>
                    <s-icon class="tip-icon-wrap" name="warning-mark"/>
                </s-popover>
                </span>
            </div>
            <div slot="c-snat">
                <div s-if="!row.clusterMode" class="nat-eip-wrapper">
                    <div class="nat-eip">
                        <span class="label">{{'SNAT：'}}</span>
                        <div class="eip-wrapper">
                            <div
                                s-for="item, index in row.eips"
                                key="{{index}}"
                                class="value"
                                on-click="handleToEip(item, row)"
                            >
                                {{item.eip}}
                            </div>
                            <div s-if="row.eipsLength > 3">...</div>
                            <div s-if="row.eipsLength === 0">-</div>
                        </div>
                        <s-popover s-if="{{row.snatEipsResourceStatus === 'STOPPED'}}" placement="top">
                            <div slot="content">
                                {{row.snatEipProductType === 'prepay' ? prepayIP : postpayIP}}
                            </div>
                            <s-icon class="tip-icon-wrap" name="warning-mark"/>
                        </s-popover>
                    </div>
                    <div class="nat-eip">
                        <span class="label">{{'DNAT：'}}</span>
                        <div class="eip-wrapper">
                            <div
                                s-for="item, index in row.dnatEips"
                                key="{{index}}"
                                class="value"
                                on-click="handleToEip(item, row)"
                            >
                                {{item.eip}}
                            </div>
                            <div s-if="row.dnatEipsLength > 3">...</div>
                            <div s-if="row.dnatEipsLength === 0">-</div>
                        </div>
                        <s-popover s-if="{{row.dnatEipsResourceStatus === 'STOPPED'}}" placement="top">
                            <div slot="content">
                                {{row.dnatEipProductType === 'prepay' ? prepayIP : postpayIP}}
                            </div>
                            <s-icon class="tip-icon-wrap" name="warning-mark"/>
                        </s-popover>
                    </div>
                </div>
                <div s-else class="nat-eip-wrapper">
                    <div
                        s-for="item, index in row.combineEips"
                        key="{{index}}"
                        class="clusterMode-nat"
                        on-click="handleToEip(item, row)"
                    >
                        {{item.eip}}
                    </div>
                    <div s-if="row.combineEipsLength > 3">...</div>
                    <div s-if="row.combineEipsLength === 0">-</div>
                    <s-popover s-if="{{row.dnatEipsResourceStatus === 'STOPPED' || row.snatEipsResourceStatus === 'STOPPED'}}" placement="top">
                        <div slot="content">
                            {{(row.dnatEipProductType === 'prepay' || row.snatEipProductType === 'prepay') ? prepayIP : postpayIP}}
                        </div>
                        <s-icon class="tip-icon-wrap" name="warning-mark"/>
                    </s-popover>
                </div>
            </div>
            <div slot="c-flavor">
                {{row | getFlavor}}
            </div>
            <div slot="c-cuNum">
                {{row.cuNum | getCuNumDisplay}}
            </div>
            <div slot="c-status">
                <span class="{{row.status | statusClass}}">{{row.status | statusText}}</span>
            </div>
            <div slot="c-tag">
                <s-popover class="nat-ip-tag" s-if="isPrivate">
                    <div slot="content">
                        <div s-if="!row.tags || !row.tags.length" class="nat-no-tag">
                            未设置标签
                            <edit-tag
                                selectedItems="{{[row]}}"
                                isShowTooltip="{{false}}"
                                skin="stringfy"
                                on-success="refresh"
                                actionText="添加标签"
                                type="{{isPrivate ? 'PRIVATE_NAT' : 'NAT'}}"
                            ></edit-tag>
                        </div>
                        <div class="nat-tag-widget" s-else>
                            <div class="nat-tag-item" s-for="item,index in row.tags">{{item.tagKey + '：' + item.tagValue}}</div>
                            <edit-tag
                                selectedItems="{{[row]}}"
                                isShowTooltip="{{false}}"
                                skin="stringfy"
                                on-success="refresh"
                                class="left_class"
                                type="{{isPrivate ? 'PRIVATE_NAT' : 'NAT'}}"
                            ></edit-tag>
                        </div>
                    </div>
                    <div class="tag-wrapper">
                        <s-label class="tag-icon" theme="line" size="{{16}}" strokeLinecap="round" strokeLinejoin="round" />
                        <span>{{row.tags.length || 0}}</span>
                    </div>
                </s-popover>
                <div s-else>
                    <span s-if="!row.tags || row.tags.length < 1">
                        -
                    </span>
                    <div s-else s-for="item,index in row.tags">
                        <span s-if="index <= 1">
                            {{item.tagKey + ':' + item.tagValue}}
                        </span>
                        <div s-if="row.tags.length > 2 && index === 1">...</div>
                    </div>
                </div>
            </div>
            <div slot="c-expiredTime">{{row | getTime}}</div>
            <template slot="c-groups">
                <p s-for="item in row.resourceGroups">{{item.name}}</p>
            </template>
            <div slot="c-description">
                <span class="truncated" title="{{row.description}}">{{row.description || '-'}}</span>
                <s-popover
                    style="display: {{isPrivate && (row.status === NatStatus.BUILDING || row.status === NatStatus.DELETING || row.status === NatStatus.CONFIGURING) ? 'none' : ''}}"
                    s-ref="popover-description-{{rowIndex}}"
                    placement="top"
                    trigger="click"
                    class="edit-popover-class"
                >
                    <div class="edit-wrap" slot="content">
                        <s-textarea
                            value="{=edit.description.value=}"
                            width="200"
                            height="48"
                            on-input="onEditInput($event, rowIndex, 'description')"/>
                        <div class="edit-tip">描述不能超过200个字符</div>
                        <s-button skin="primary" s-ref="editBtn-description-{{rowIndex}}" disabled="{{true}}"
                            on-click="editConfirm(row, rowIndex, 'description')">确定</s-button>
                        <s-button on-click="editCancel(rowIndex, 'description')">取消</s-button>
                    </div>
                    <outlined-editing-square class="name-icon" on-click="onInstantEdit(row, rowIndex, 'description')"/>
                </s-popover>
            </div>
            <div slot="h-diagnose">
                <span
                    >诊断
                    <div class="new-tag new-instance-tag">new</div></span
                >
            </div>
            <template slot="c-diagnose">
                <x-moreoperation
                    item="{{row}}"
                    on-diagnose="diagnoseNat(row)"
                    on-pathAnalysis="pathAnalysisNat(row)"
                    isOpenDrawer="{{openDrawer}}"
                />
            </template>
            <div slot="c-opt">
                <div class="private-opts" s-if="isPrivate">
                    <s-button skin="stringfy" on-click="redirectNat(row, 'snat')" data-test-id="{{listSetSnatTestId}}{{rowIndex}}">SNAT规则</s-button>
                    <s-button skin="stringfy" on-click="redirectNat(row, 'dnat')" data-test-id="{{listSetDnatTestId}}{{rowIndex}}">DNAT规则</s-button>
                    <s-button skin="stringfy" on-click="showMonitor(row)" data-test-id="{{listMonitorTestId}}{{rowIndex}}">监控</s-button>
                    <s-popover class="private-more-opts" trigger="hover">
                        <div slot="content">
                            <div
                                on-click="handleMoreAction(row, item.value)"
                                class="opts-item"
                                s-for="item,index in MORE_OPTIONS" key="{{item.value}}">
                                <s-button disabled="{{row | releaseDisableFlag(item.value)}}" skin="stringfy">
                                    {{item.label}}
                                </s-button>
                            </div>
                        </div>
                        <s-button skin="stringfy" data-test-id="${testID.nat.listPriMore}">更多 <s-icon-down /></s-button>
                    </s-popover>
                </div>
                <div s-else class="opt-wrap">
                    <s-button skin="stringfy" on-click="showMonitor(row)" data-test-id="{{listMonitorTestId}}{{rowIndex}}">监控</s-button>
                    <s-button skin="stringfy" on-click="redirectNat(row, 'snat')" data-test-id="{{listSetSnatTestId}}{{rowIndex}}">设置SNAT</s-button>
                    <s-button s-if="!inNatBlack" skin="stringfy" on-click="redirectNat(row, 'dnat')" data-test-id="{{listSetDnatTestId}}{{rowIndex}}">设置DNAT</s-button>
                    <nat-list-operation
                        class="nat_multiple_class"
                        item="{{row}}"
                        on-command="changeOpt"
                        mirrorList="{{flowMirrorList}}"
                        row-index="{{rowIndex}}"/>
                </div>
            </div>
        </s-table>
        <s-pagination
            s-if="{{pager.total}}"
            slot="pager"
            layout="{{'total, pageSize, pager, go'}}"
            pageSize="{{pager.pageSize}}"
            total="{{pager.total}}"
            page="{{pager.page}}"
            on-pagerChange="onPagerChange"
            on-pagerSizeChange="onPagerSizeChange"
        />
        <resource-group-dialog
            s-if="{{showResource}}"
            sdk="{{resourceSDK}}"
            resource="{{resource}}"
            on-success="onCommit"
            on-cancel="onCancel"
        />
    </s-app-list-page>
    <div id="satisfactionNew" s-if="drawerVisible"></div>
</div>
`;

@template(tpl)
@asComponent('@nat-list')
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@invokeComp('@edit-tag', '@search-tag', '@vpc-select', '@custom-column')
class NatList extends Component {
    static components = {
        'outlined-refresh': OutlinedRefresh,
        'outlined-plus': OutlinedPlus,
        'outlined-link': OutlinedLink,
        'outlined-editing-square': OutlinedEditingSquare,
        'outlined-download': OutlinedDownload,
        'outlined-doc': OutlinedDocument,
        'resource-group-dialog': ResourceGroupDialog,
        'nat-list-operation': NatListOperation,
        's-icon-down': OutlinedDown,
        's-label': Label,
        's-textarea': Input.TextArea,
        'x-moreoperation': MoreOperation
    };

    initData() {
        const allColumns = columns.slice();
        const customColumnDb = allColumns.map(item => ({
            text: item.label,
            value: item.name,
            disabled: item.name === 'id' || item.name === 'opt'
        }));
        return {
            FLAG,
            DocService,
            klass: 'nat-list-wrap',
            introduceTitle: '公网NAT 网关使用流程',
            searchbox: {
                keyword: '',
                placeholder: '请输入实例名称进行搜索',
                keywordType: ['name'],
                keywordTypes: [
                    {
                        value: 'name',
                        text: '实例名称'
                    },
                    {
                        value: 'natId',
                        text: '实例ID'
                    },
                    {
                        value: 'ip',
                        text: 'SNAT公网IP'
                    },
                    {
                        value: 'dnatEip',
                        text: 'DNAT公网IP'
                    },
                    {
                        value: 'tag',
                        text: '标签'
                    },
                    {
                        value: 'resGroupId',
                        text: '资源分组'
                    }
                ],
                multi: true
            },
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                allColumns,
                datasource: [],
                selectedItems: []
            },
            OperationType: [
                {
                    label: '计费变更',
                    value: 'ALTER_PRODUCTTYPE'
                },
                {
                    label: '取消计费变更',
                    value: 'CANCEL_ALTER_PRODUCTTYPE'
                }
            ],
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            customColumn: {
                value: [
                    'id',
                    'status',
                    'diagnose',
                    'vpcId',
                    'flavor',
                    'cuNum',
                    'productType',
                    'snat',
                    'expiredTime',
                    'description',
                    'groups',
                    'tag',
                    'opt'
                ],
                datasource: customColumnDb
            },
            order: {},
            edit: {
                name: {
                    value: '',
                    error: true,
                    visible: false
                },
                description: {
                    value: '',
                    error: true,
                    visible: false
                }
            },
            inBjksRegion: false,
            showMode: '',
            prepayIP: '您的公网 IP 已到期，续费后才能使用。',
            postpayIP: '您的公网 IP 已欠费，充值后才能使用。',
            vpcId: '',
            natSinDisable: {},
            flowMirrorList: [],
            iamPass: {},
            natIntro: [
                {
                    title: '创建NAT网关',
                    content: '包年包月，按量计费两种购买方式，填写配置信息',
                    img: 'https://bce.bdstatic.com/network-frontend/create-nat-icon.png',
                    btn: '立即创建',
                    href: `#/vpc/nat/create?vpcId=${window.$storage.get('vpcId')}`
                },
                {
                    title: '配置NAT路由',
                    content: '配置子网所关联的路由表，将指Internat的流量转发到NAT网关',
                    img: 'https://bce.bdstatic.com/network-frontend/set-nat-route.png',
                    btn: '查看详情',
                    href: DocService.nat_index
                },
                {
                    title: '配置NAT网关',
                    content: 'NAT 网关绑定EIP，配置SNAT、DNAT表',
                    img: 'https://bce.bdstatic.com/network-frontend/set-nat-gateway.png',
                    btn: '查看详情',
                    href: DocService.nat_index
                },
                {
                    title: '查看监控',
                    content: '查看带宽，流量，包速率等监控信息，并配置报警策略',
                    img: 'https://bce.bdstatic.com/network-frontend/nat-monitor.png'
                }
            ],
            privateNatIntro: [
                {
                    title: '创建NAT网关',
                    content: '按量计费的购买方式，填写配置信息',
                    img: 'https://bce.bdstatic.com/network-frontend/create-nat-icon.png',
                    btn: '立即创建',
                    href: `#/vpc/nat/create?vpcId=${window.$storage.get('vpcId')}&natType=private`
                },
                {
                    title: '配置私网NAT网关',
                    content: '新增地址段，设置NAT IP，创建SNAT规则、DNAT规则',
                    img: 'https://bce.bdstatic.com/network-frontend/set-nat-gateway.png'
                    // btn: '查看详情',
                    // href: DocService.nat_index
                },
                {
                    title: '配置VPC路由',
                    content: '配置VPC路由表，指向NAT网关',
                    img: 'https://bce.bdstatic.com/network-frontend/set-nat-route.png'
                    // btn: '查看详情',
                    // href: DocService.nat_index
                },
                {
                    title: '查看监控',
                    content: '查看带宽，流量，包速率等监控信息，并配置报警策略',
                    img: 'https://bce.bdstatic.com/network-frontend/nat-monitor.png'
                }
            ],
            releaseInfo: {},
            resourceSDK: new ResourceGroupSDK(window.$http, window.$context),
            natType: '',
            PRIVATE_COLUMNS,
            MORE_OPTIONS,
            currentNatRow: {},
            quotaLimitTip: '',
            NatStatus,
            docLink: DocService.nat_index,
            accountState: {
                disabled: false,
                message: ''
            },
            listCreateTestId: testID.nat.listCreateBtn,
            listTableTestId: testID.nat.listTable,
            listNameTestId: testID.nat.listName,
            listSetDnatTestId: testID.nat.listSetDnat,
            listSetSnatTestId: testID.nat.listSetSnat,
            listMonitorTestId: testID.nat.listMonitor,
            urlQuery: getQueryParams(),
            drawerVisible: true,
            visibleDraw: true,
            openDrawer: false
        };
    }
    static computed = {
        operationDisabled() {
            const selectedItems = this.data.get('table.selectedItems');
            return selectedItems.length === 0;
        },
        isPrivate() {
            const natType = this.data.get('context').natType;
            return natType === 'private';
        }
    };
    static filters = {
        statusClass(value) {
            return NatStatus.fromValue(value).styleClass || '';
        },
        statusText(value) {
            return value ? NatStatus.getTextFromValue(value) : '-';
        },
        getFlavor(row) {
            if (row) {
                const {flavor, clusterMode, ipVersion} = row;
                if (clusterMode) {
                    let formIpVersion = ipVersion === 'v6' ? 'v6' : 'v4';
                    return NAT_ENHANCED_MAPPING.getTextFromValue(formIpVersion);
                }
                const config = NatFlavor.fromValue(flavor);
                return config.text ? `普通型（${config.text}）` : '普通型（超大）';
            }
            return '';
        },
        getProductType(item) {
            return PayType.getTextFromValue(item.productType);
        },
        getTime(item) {
            return item.expiredTime ? utcToTime(item.expiredTime) : '-';
        },
        ipText(ips = []) {
            let result = [];
            u.each(ips, (data, index) => {
                if (index <= 2) {
                    result.push(data.eip || '-');
                }
            });

            if (ips && ips.length > 3) {
                result.push('...');
            }

            return result.length ? result.join('<br>') : '-';
        },
        getCuNumDisplay(cuNum) {
            return cuNum ? cuNum + 'CU' : '-';
        },
        getNatVpcId(row: any) {
            const isPrivate = this.data.get('isPrivate');
            return isPrivate ? row.vpcUuid : row.vpcId;
        },
        getNatId(row: any) {
            const isPrivate = this.data.get('isPrivate');
            return isPrivate ? row.natId : row.id || row.natId;
        },
        releaseDisableFlag(row: any, action: 'CHANGECU' | 'RELEASE' | 'DIAGNOSE') {
            const {status} = row;
            const releaseFlag =
                action === 'RELEASE' && !u.contains([NatStatus.ACTIVE, NatStatus.DOWN, NatStatus.UPDATING], status);
            const changeCuFlag = action === 'CHANGECU' && !u.contains([NatStatus.ACTIVE], status);
            const diagnoseFlag = action === 'DIAGNOSE' && u.contains([NatStatus.BUILDING], status);
            return releaseFlag || changeCuFlag || diagnoseFlag;
        }
    };

    inited() {
        const natType = this.data.get('context').natType;
        this.data.set('natType', natType);
        if (natType === 'private') {
            this.data.set('introduceTitle', '私网NAT 网关使用流程');
        }
        let vpcId = this.data.get('urlQuery.vpcId');
        if (vpcId) {
            window.$storage.set('vpcId', vpcId);
        }
        this.initPrivateConfig();
        this.getIamQuery();
        this.checkNatBlackList();
        this.setOperationMessage();
        this.getMirrorList();
        this.handleJumpFromMirror();
        natType === 'private' && this.checkUserQuota();
        let {natSinDisable} = checker.check(rules, []);
        this.data.set('natSinDisable', natSinDisable);
        const accountState = window.$storage.get('accountState');
        this.data.set('accountState', accountState);
    }
    attached() {
        this.data.set('introShow', window.$storage.get('natIntroShow'));
        let region = window.$context.getCurrentRegionId();
        this.data.set('inBjksRegion', region === AllRegion.BJKS);
        this.watch('operation', value => {
            if (value !== '') {
                this.data.set('operation', '');
            }
        });
    }

    handleJumpFromMirror() {
        const natId = this.data.get('urlQuery.natId');
        if (natId) {
            this.data.set('searchbox.keywordType', ['id']);
            this.data.set('searchbox.keyword', natId);
        }
    }

    checkNatBlackList() {
        this.$http.natBlackList().then(data => {
            this.data.set('inNatBlack', data);
        });
    }
    getColumnIndexByName(datasource, name) {
        return datasource.findIndex(item => item.value === name || item.name === name);
    }

    vpcInt() {
        this.loadPage();
    }

    vpcChange() {
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    getPayload() {
        const searchParam = this.ref('search') ? this.ref('search').getSearchCriteria() : {};
        const {pager, order, filters, showMode} = this.data.get('');
        const isPrivate = this.data.get('isPrivate');
        let payload: Record<string, any> = {
            pageNo: pager.page,
            pageSize: pager.pageSize
        };
        if (!isPrivate) {
            payload.vpcId = window.$storage.get('vpcId');
        } else {
            payload.vpcUuid = window.$storage.get('vpcId');
        }
        if (!payload.vpcId) {
            delete payload.vpcId;
        }
        if (!payload.vpcUuid) {
            delete payload.vpcUuid;
        }
        if (showMode && !isPrivate) {
            payload.showMode = showMode;
        }
        return {
            ...payload,
            ...order,
            ...filters,
            ...searchParam
        };
    }

    loadPage() {
        this.data.set('table.loading', true);
        const isPrivate = this.data.get('isPrivate');
        let payload = this.getPayload();

        if (isPrivate && payload.keywordType === 'id') {
            payload.keywordType === 'natId';
        }
        if (isPrivate && payload.keywordType === 'tag') {
            payload.keywordType = 'TAG';
        }
        if (payload.keywordType === 'resGroupId') {
            payload.keywordType = 'resourceGroupName';
        }
        if (payload.keywordType === 'resGroupId') {
            payload.keywordType = 'resourceGroupName';
        }
        this.resetTable();
        const reqUrl = isPrivate ? 'getPrivateNatList' : 'getNatListV2';
        this.$http[reqUrl](payload).then(res => {
            let dataList = [];
            if (isPrivate) {
                dataList = res.result;
            } else {
                dataList = this.checkBindAble(res.result);
                let cuMap = new Map([
                    ['little', 1],
                    ['medium', 3],
                    ['large', 7],
                    ['enhanced_12c6q', 14]
                ]);
                // 存量普通型的性能容量做下转换,小型1CU,中型3CU，大兴7CU,超大型14CU
                dataList = dataList.map(item => {
                    if (!item.clusterMode) {
                        return {
                            ...item,
                            cuNum: cuMap.get(item.flavor),
                            eipsLength: item?.eips?.length,
                            dnatEipsLength: item?.dnatEips?.length,
                            eips: item?.eips?.slice(0, 3),
                            dnatEips: item?.dnatEips?.slice(0, 3)
                        };
                    }
                    // 合并eips、dnatEips、bindEips并去重
                    const combineEips = uniqBy([...item.eips, ...item.dnatEips, ...(item.bindEips || [])], 'eip');
                    return {
                        ...item,
                        combineEipsLength: combineEips?.length,
                        combineEips: combineEips.slice(0, 3),
                        combineEipsAll: combineEips
                    };
                });
            }
            this.data.set('table.datasource', dataList);
            this.data.set('pager.total', res.totalCount);
            this.data.set('table.loading', false);
        });
    }

    checkBindAble(data) {
        return u.map(data, item => {
            return {
                ...item,
                enableProduct: this.checkEnableProduct(item)
            };
        });
    }

    checkEnableProduct(item) {
        return (
            u.indexOf([NatStatus.ACTIVE, NatStatus.UNCONFIGURED, NatStatus.UPDATING], item.status) > -1 &&
            item.productType === PayType.PREPAY &&
            item.orderProductPayType === 'to_postpay'
        );
    }

    onRecharge() {
        let selectedItems = this.data.get('table.selectedItems');
        let ids = u.pluck(selectedItems, 'id');
        recharge('NAT', ids, null, '/api/nat/order/confirm/new');
    }

    resetTable() {
        this.data.set('table.selection', {
            mode: 'multi',
            selectedIndex: []
        });
        this.data.set('table.selectedItems', []);
    }

    refresh() {
        this.loadPage();
    }

    initPrivateConfig() {
        const isPrivate = this.data.get('isPrivate');
        const PRIVATE_COLUMNS = this.data.get('PRIVATE_COLUMNS');
        if (isPrivate) {
            this.data.set('listCreateTestId', testID.nat.listPriCreateBtn);
            this.data.set('listTableTestId', testID.nat.listPriTable);
            this.data.set('listNameTestId', testID.nat.listPriName);
            this.data.set('listSetDnatTestId', testID.nat.listPriSetDnat);
            this.data.set('listSetSnatTestId', testID.nat.listPriSetSnat);
            this.data.set('listMonitorTestId', testID.nat.listPriMonitor);
            this.data.set('table.allColumns', PRIVATE_COLUMNS);
            this.data.set('docLink', DocService.private_nat_doc);
            this.data.set('customColumn.value', [
                'id',
                'status',
                'diagnose',
                'vpcId',
                'natIp',
                'cuNum',
                'productType',
                'description',
                'groups',
                'tag',
                'opt'
            ]);
            const formatCustomDatasource = PRIVATE_COLUMNS.map(item => ({
                text: item.label,
                value: item.name,
                disabled: item.name === 'id' || item.name === 'opt'
            }));
            this.data.set('customColumn.datasource', formatCustomDatasource);
            this.data.set('searchbox', {
                keyword: '',
                placeholder: '请输入实例名称进行搜索',
                keywordType: ['name'],
                keywordTypes: [
                    {
                        value: 'name',
                        text: '实例名称'
                    },
                    {
                        value: 'natId',
                        text: '实例ID'
                    },
                    {
                        value: 'tag',
                        text: '标签'
                    },
                    {
                        value: 'resGroupId',
                        text: '资源分组'
                    }
                ],
                multi: true
            });
            this.data.set('natIntro', this.data.get('privateNatIntro'));
        }
    }

    // 设置表格列
    setTableColumns(customColumnNames) {
        customColumnNames = customColumnNames ? customColumnNames : this.data.get('customColumn.value');
        const allColumns = this.data.get('table.allColumns');
        let columns = [];
        allColumns.forEach(item => customColumnNames.indexOf(item.name) > -1 && columns.push(item));
        this.data.set('table.columns', columns);
    }

    initColumns(value) {
        this.setTableColumns(value);
    }
    // 自定义表格列
    onCustomColumns(value) {
        this.data.set('customColumn.value', value);
        this.setTableColumns(value);
    }

    tableSelected(e) {
        this.data.set('table.selectedItems', e.value.selectedItems);
        let {recharge} = checker.check(rules, e.value.selectedItems, '');
        this.data.set('recharge', recharge);
        this.setOperationMessage();
    }

    setOperationMessage() {
        let selectedItem = this.data.get('table.selectedItems');
        let checkResult = checker.check(rules, selectedItem, '');
        let OperationType = u.cloneDeep(this.data.get('OperationType'));
        const accountState = window.$storage.get('accountState');
        const {disabled, message} = accountState;
        OperationType.forEach(item => {
            if (checkResult[item.value]) {
                item.disabled = checkResult[item.value].disable;
                item.message = checkResult[item.value].message;
            }
            // 欠费校验
            if (item.value === 'ALTER_PRODUCTTYPE' && !item.disabled && disabled) {
                item.disabled = true;
                item.message = message;
            }
        });
        this.data.set('OperationType', OperationType);
        const mirrorList = this.data.get('flowMirrorList');
        const item = selectedItem.length === 1 ? selectedItem[0] : selectedItem;
        const result = checker.check(rules, item, 'RELEASE', {
            item,
            mirrorList
        }).RELEASE;
        let releaseInfo = {};
        releaseInfo.disabled = result.disable ? result.disable : '';
        releaseInfo.message = result.message ? result.message : '';
        this.data.set('releaseInfo', releaseInfo);
    }

    onOperationChange(e) {
        const methodMap = {
            ALTER_PRODUCTTYPE: this.alterProduct,
            CANCEL_ALTER_PRODUCTTYPE: this.cancelAlterProduct
        };
        let requester = methodMap[e.value].bind(this);
        requester();
    }

    changeOpt(e) {
        let {type, payload, rowIndex} = e;
        const methodMap = {
            RELEASE: this.onRelease,
            BIND: this.bindEip,
            UNBIND: this.unbindEip,
            NATUPGRADE: this.onUpgrade,
            EDIT_RES: this.changeResourceGroup
        };
        let requester = methodMap[type].bind(this);
        requester(payload);
    }

    bindEip(item) {
        const inNatBlack = this.data.get('inNatBlack');
        let dialog = new Bind({
            data: {
                selectedItem: item,
                title: '绑定公网IP',
                isBind: true,
                natType: inNatBlack ? NatType.toArray('SNAT') : NatType.toArray()
            }
        });
        dialog.attach(document.body);
        dialog.on('confirm', () => this.loadPage());
    }
    // SNAT eips
    getSNATEips(payload) {
        return this.$http.snatRuleList(payload).then(res => {
            return u.flatten(u.map(res.result || [], item => item.eips));
        });
    }
    // DNAT eips
    getDNATEips(payload) {
        return this.$http.dnatRuleList(payload).then(res => {
            return u.map(res.result || [], item => item.eip);
        });
    }
    // 获取SNAT、DNAT关联的eips
    async getSnatDnatEips(natGatewayId, vpcId) {
        const res = await Promise.all([
            this.getSNATEips({
                natGatewayId,
                vpcId,
                pageNo: 1,
                pageSize: 10000
            }),
            this.getDNATEips({
                natGatewayId,
                vpcId,
                pageNo: 1,
                pageSize: 10000
            })
        ]);
        return {
            snatEips: res[0],
            dnatEips: res[1]
        };
    }

    async unbindEip(item) {
        const {id, vpcId} = item;
        const {snatEips, dnatEips} = await this.getSnatDnatEips(id, vpcId);
        const inNatBlack = this.data.get('inNatBlack');
        let dialog = new Bind({
            data: {
                selectedItem: item,
                title: '解绑公网IP',
                isBind: false,
                natType: inNatBlack ? NatType.toArray('SNAT') : NatType.toArray(),
                snatEips,
                dnatEips
            }
        });
        dialog.attach(document.body);
        dialog.on('confirm', () => this.loadPage());
    }

    onUpgrade(item) {
        const {vpcId, id, ipVersion} = item;
        location.hash = `#/vpc/nat/upgrade?vpcId=${vpcId}&id=${id}&ipVersion=${ipVersion}`;
    }

    onRelease(item) {
        const {id, natId, ipVersion} = item;
        const isPrivate = this.data.get('isPrivate');
        const isEnhancedIPv6 = ipVersion === 'v6';
        const netDesc = isPrivate ? '私网' : 'Internet';
        let confirm = new Confirm({
            data: {
                title: '释放前确认',
                content: `释放时会关联删除该NAT网关的路由，${netDesc}转发请求将立即中断，请确定释放此NAT网关？`
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            const reqUrl = isPrivate ? 'releasePrivateNat' : isEnhancedIPv6 ? 'releaseNatIPv6' : 'natRelease';

            const params: Record<string, any> = {};
            if (isPrivate || isEnhancedIPv6) {
                params.natId = natId;
            } else {
                params.ids = [id];
            }
            this.$http[reqUrl](params).then(() => {
                Notification.success('释放成功');
                this.loadPage();
            });
        });
    }

    alterProduct() {
        let selectedItems = this.data.get('table.selectedItems');
        if (selectedItems.length < 1) {
            return;
        }
        let instanceIds = u.pluck(selectedItems, 'id');
        let isPrepay = selectedItems[0].productType === PayType.PREPAY;
        let type = isPrepay ? 'TO_POSTPAY' : 'TO_PREPAY';
        let url = isPrepay ? '/api/nat/order/confirm/topostpay' : '/api/nat/order/confirm/toprepay';

        alterProductType('NAT', instanceIds, type, null, url);
    }

    cancelAlterProduct(e) {
        let selectedItems = this.data.get('table.selectedItems');
        let ids = u.pluck(selectedItems, 'id');

        let confirm = new Confirm({
            data: {
                title: '取消计费变更',
                content:
                    '确认取消计费变更？<br>您已开通计费变更-预付费转后付费功能。<br>实例将会在到期后自动转换为后付费的计费方式。'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http.natCancelAlterProductType({ids}).then(() => {
                Notification.success('取消计费变更成功');
                this.loadPage();
            });
        });
    }

    // 搜索事件
    onSearch() {
        this.data.set('pager.page', 1);
        this.loadPage();
    }
    // 改变页数
    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }
    // 改变每页显示数量
    onPagerSizeChange(e) {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', e.value.pageSize);
        this.loadPage();
    }

    // 点击修改icon
    onInstantEdit(row, rowIndex, type) {
        this.data.set(`edit.${type}.value`, row[`${type}`]);
        this.data.set(`edit.${type}.error`, false);
        const popWrap = this.ref(`popover-${type}-${rowIndex}`);
        popWrap.data.set('visible', !popWrap.data.get('visible'));
    }

    // 编辑弹框-输入名称/描述
    onEditInput(e, rowIndex, type) {
        let result =
            type === 'name' ? e.value === '' || !/^[a-zA-Z][\w\-\_\/\.]{0,64}$/.test(e.value) : e.value.length > 200;
        this.data.set(`edit.${type}.error`, result);
        this.data.set(`edit.${type}.value`, e.value);
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', result);
    }

    // 编辑弹框-提交
    editConfirm(row, rowIndex, type) {
        const {id, natId, ipVersion} = row;
        let edit = this.data.get(`edit.${type}`);
        if (edit.error) {
            return;
        }
        const isEnhancedIPv6 = ipVersion === 'v6';
        const isPrivate = this.data.get('isPrivate');
        const reqUrl = isPrivate ? 'updatePrivateNat' : isEnhancedIPv6 ? 'updateNatIPv6' : 'updateNat';
        const payload: any = {
            [type]: edit.value
        };
        if (isPrivate || isEnhancedIPv6) {
            payload.natId = natId;
        } else {
            payload.natGatewayId = id;
        }
        this.$http[reqUrl](payload).then(() => {
            this.editCancel(rowIndex, type);
            Notification.success('修改成功');
            this.loadPage();
        });
    }

    // 编辑弹框-取消
    editCancel(rowIndex, type) {
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', true);
        this.ref(`popover-${type}-${rowIndex}`).data.set('visible', false);
    }

    // 排序

    onSort(e) {
        this.data.set('order', e.value);
        this.loadPage();
    }

    // 筛选
    onFilter(e) {
        let name = e.field.name;
        let value = e[name] ? e[name] : '';
        this.data.set('filters.' + name, value);
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    onCreate() {
        let vpcId = window.$storage.get('vpcId');
        const natType = this.data.get('context').natType;
        location.hash = `#/vpc/nat/create?vpcId=${vpcId}&natType=${natType}`;
    }
    handleMoreAction(row: any, action: 'CHANGECU' | 'RELEASE' | 'DIAGNOSE') {
        const {vpcUuid, natId, status} = row;
        const releaseFlag =
            action === 'RELEASE' && !u.contains([NatStatus.ACTIVE, NatStatus.DOWN, NatStatus.UPDATING], status);
        const changeCuFlag = action === 'CHANGECU' && !u.contains([NatStatus.ACTIVE], status);
        const diagnoseFlag = action === 'DIAGNOSE' && u.contains([NatStatus.BUILDING], status);
        if (releaseFlag || changeCuFlag || diagnoseFlag) {
            return;
        }
        if (action === 'CHANGECU') {
            location.hash = `#/vpc/nat/upgrade?vpcId=${vpcUuid}&id=${natId}&from=private`;
        } else if (action === 'DIAGNOSE') {
            location.hash = `#/vpc/instance/diagnosis?natId=${natId}`;
        } else {
            this.onRelease(row);
        }
    }

    showMonitor(item) {
        this.nextTick(() => {
            let dialog = new Monitor({
                data: {
                    instance: item,
                    isPrivate: this.data.get('isPrivate')
                }
            });
            dialog.attach(document.body);
        });
    }

    showExpireData() {
        let showMode = this.data.get('showMode');
        showMode = showMode ? '' : 'WILLEXPIRED';
        this.data.set('showMode', showMode);
        this.loadPage();
    }

    redirectNat(row, type) {
        const natType = this.data.get('context').natType;
        const {vpcId, id, natId, ipVersion} = row;
        location.hash = `#/vpc/nat/${type}?vpcId=${vpcId}&id=${id || natId}&natType=${natType}&ipVersion=${ipVersion}`;
    }

    onRegionChange() {
        location.reload();
    }

    // 绑定了流量镜像的实例不可释放
    async getMirrorList() {
        await this.$http
            .getMirrorSessionList({
                keyword: 'NAT',
                keywordType: 'source_type',
                pageNo: 1,
                pageSize: 10000
            })
            .then(res => {
                let mirrorList = res.result.filter(data => data.sourceType === 'nat');
                this.data.set('flowMirrorList', mirrorList);
            });
    }
    getIamQuery() {
        this.$http
            .getInterfaceIam({
                interfaceName: 'createNat'
            })
            .then(res => {
                let message = '';
                !res.interfacePermission && (message += '创建NAT网关权限');
                !res.createOrderPermission && (message += message ? '、创建订单权限' : '创建订单权限');
                !res.payOrderPermission && (message += message ? '、支付订单权限' : '');
                if (!res.requestId && !res.masterAccount) {
                    if (message) {
                        this.data.set('iamPass', {
                            disable: true,
                            message: `您没有${message}，请联系主用户添加`
                        });
                    } else {
                        this.data.set('iamPass', {
                            disable: false,
                            message: ''
                        });
                    }
                } else {
                    this.data.set('iamPass', {
                        disable: false,
                        message: ''
                    });
                }
            });
    }
    // 下载
    onDownload() {
        let ids = [];
        ids = this.data.get('table.selectedItems').map(item => {
            return item.id;
        });
        let filter = 'ids=' + ids.join(',');
        window.open(`/api/nat/download?` + filter);
    }

    closeIntro() {
        window.$storage.set('natIntroShow', false);
        this.data.set('introShow', false);
    }

    showIntro() {
        window.$storage.set('natIntroShow', true);
        this.data.set('introShow', true);
    }
    onNatRelease() {
        const selectedItem = this.data.get('table.selectedItems');
        selectedItem[0] && this.onRelease(selectedItem[0]);
    }
    // 编辑资源分组
    changeResourceGroup(row) {
        let resource = {
            resourceId: row.id,
            serviceType: 'NAT'
        };
        this.data.set('resource', resource);
        this.data.set('showResource', true);
    }
    // 编辑资源分组确定后
    onCommit() {
        this.data.set('showResource', false);
        this.loadPage();
    }
    // 编辑资源分组取消后
    onCancel() {
        this.data.set('showResource', false);
    }
    handleSubmitTag(tagData: any[]) {
        const row = this.data.get('currentNatRow');
    }
    handleNatIpLink(row: Record<string, any>, item: Record<string, any>) {
        const {natId, vpcUuid} = row;
        const {segmentId, natIp} = item;
        location.hash = `#/vpc/nat/natIp?vpcId=${vpcUuid}&id=${natId}&segmentId=${segmentId}&natIp=${natIp}&natType=private`;
    }
    checkUserQuota() {
        this.$http.getUserPrivateNatQuota().then(res => {
            const {free} = res;
            if (free <= 0) {
                if (FLAG.NetworkSupportXS) {
                    this.data.set('quotaLimitTip', '当前用户nat网关配额不足');
                } else {
                    const isPrivate = this.data.get('isPrivate');
                    const quotaCenterName = isPrivate ? 'IntranetNatUserQuota' : 'natQuota';
                    this.data.set(
                        'quotaLimitTip',
                        `当前用户nat网关配额不足，请<a href="/quota_center/#/quota/apply/create?serviceType=NAT&region=${window?.$context?.getCurrentRegionId()}&cloudCenterQuotaName=${quotaCenterName}" target="_blank">申请配额</a>`
                    ); // eslint-disable-line
                }
            }
        });
    }
    handleToEip(row: Record<string, any>, tableRow: any) {
        const {eip} = row;
        const {ipVersion} = tableRow;
        let url = `/eip/#/eip/instance/list?queryParam=${eip}`;
        if (ipVersion === 'v6') {
            url = `/eip/#/eip/ipv6/list?queryParam=${eip}`;
        }
        window.open(url);
    }
    diagnoseNat(e) {
        this.data.set('openDrawer', true);
        const confirm = new DiagnoseConfirm({
            data: {
                title: '发起诊断',
                instanceId: e.id || e.natId,
                instanceType: 'nat',
                region: e.region || window.$context.getCurrentRegionId()
            }
        });
        confirm.on('confirm', () => {
            this.loadNatDiagnose(e);
        });
        confirm.on('close', () => {
            this.nextTick(() => {
                this.data.set('openDrawer', false);
                this.data.set('drawerVisible', false);
                this.data.set('visibleDraw', false);
            });
        });
        confirm.attach(document.body);
    }
    loadNatDiagnose(e) {
        this.nextTick(() => {
            this.data.set('openDrawer', true);
            this.data.set('drawerVisible', true);
            this.data.set('visibleDraw', true);
            if (document.getElementById('satisfactionNew')) {
                this.loadSatisfaction(e);
            } else {
                this.loadNatDiagnose(e);
            }
        });
    }
    loadSatisfaction(row) {
        let extraData = {
            natType: this.data.get('natType')
        };
        this.nextTick(() => {
            processor.applyComponent(
                'DiagnoseDrawer',
                {
                    diagnoseData: {
                        instanceType: 'nat',
                        instanceId: row.id || row.natId,
                        region: row.region || window.$context.getCurrentRegionId(),
                        vpcId: row.id ? row.vpcId : row.vpcUuid,
                        natId: row.id || row.natId,
                        natType: this.data.get('natType')
                    },
                    visible: this.data.get('visibleDraw'),
                    instanceType: 'nat',
                    extraData: JSON.stringify(extraData),
                    onCloseDrawer: () => {
                        this.data.set('openDrawer', false);
                        this.data.set('drawerVisible', false);
                        this.data.set('visibleDraw', false);
                    },
                    http: request
                },
                '#satisfactionNew'
            );
        });
    }
    pathAnalysisNat(e) {
        this.data.set('openDrawer', true);
        this.nextTick(() => {
            this.data.set('drawerVisible', true);
            this.data.set('visibleDraw', true);
            if (document.getElementById('satisfactionNew')) {
                this.loadSatisfactionPath(e);
            } else {
                this.pathAnalysisNat(e);
            }
        });
    }
    loadSatisfactionPath() {
        this.nextTick(() => {
            processorPath.applyComponent(
                'AnalysisGraphDrawer',
                {
                    visible: this.data.get('visibleDraw'),
                    scoreTipTitle: 'NAT网关',
                    onCloseDrawer: () => {
                        this.data.set('openDrawer', false);
                        this.data.set('drawerVisible', false);
                        this.data.set('visibleDraw', false);
                    },
                    http: request
                },
                '#satisfactionNew'
            );
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(NatList));
