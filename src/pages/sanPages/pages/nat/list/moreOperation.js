/**
 * @file pages/nat/page/list/more-operation.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect} from '@baiducloud/runtime';
import {checker} from '@baiducloud/bce-opt-checker';
import {Select} from '@baiducloud/bce-ui/san';

import {OptType} from '@/pages/sanPages/common/enum';
import rules from '../rules';

const {asComponent, template} = decorators;

const tpl = html` <div>
    <ui-select class="more-opt" datasource="{{datasource}}" value="{=value=}" default-label="更多"> </ui-select>
</div>`;

@template(tpl)
export default class NatListOperation extends Component {
    static components = {
        'ui-select': Select
    };

    static computed = {
        datasource() {
            let operation = OptType.toArray('BIND', 'UNBIND', 'RELEASE', 'NATUPGRADE', 'EDIT_RES');
            let item = this.data.get('item');
            let mirrorList = this.data.get('mirrorList');
            const accountState = window.$storage.get('accountState');
            const {disabled, message} = accountState;
            _.each(operation, (command, i) => {
                let result = checker.check(rules, item, command.value, {item, mirrorList})[command.value];
                operation[i].disabled = result.disable ? result.disable : '';
                operation[i].tip = result.message ? result.message : '';
                // 检查账户是否欠费
                if (command.value === 'NATUPGRADE' && !command.disabled && disabled) {
                    command.disabled = true;
                    command.tip = message;
                }
            });
            return operation;
        }
    };

    attached() {
        this.watch('value', value => {
            if (value) {
                this.fire('command', {
                    type: value,
                    payload: this.data.get('item'),
                    rowIndex: this.data.get('rowIndex')
                });
            }
            this.data.set('value', '');
        });
    }
}
