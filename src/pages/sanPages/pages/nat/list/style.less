.nat-list-widget {
    width: 100%;
    flex: 1;
    .nat-header-wrapper {
        position: absolute;
        z-index: 9999;
        top: 0px;
        color: #151b26;
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
        padding-top: 2px;
        padding-bottom: 4px;
        background-color: #fff;
        width: 100%;
        display: flex;
        justify-content: space-between;
        .title {
            display: inline-block;
            margin: 0;
            color: #151b26;
            height: 47px;
            line-height: 47px;
            font-weight: 500;
            font-size: 16px;
            margin: 0 12px 0 16px;
        }
        .vpc-select {
            line-height: 47px;
        }
        .header-button-wrap {
            margin-right: 16px;
            .s-button {
                padding: 0;
            }
            margin-left: auto;
            display: flex;
            align-items: center;
            .link-wrap {
                margin-left: 16px;
                display: flex;
                .service-best-practice {
                    height: 16px;
                    width: 16px;
                    margin-right: 8px;
                    position: relative;
                    top: 4px;
                    background: url('../../../../../img/natbestpractice.svg?url') center center no-repeat;
                }
                .service-auto-renewal {
                    height: 16px;
                    width: 16px;
                    margin-right: 8px;
                    position: relative;
                    top: 4px;
                    background: url('../../../../../img/autorenewal.svg?url') center center no-repeat;
                }
                .nat-tip {
                    font-size: 12px;
                    font-weight: 400;
                }
            }
            .no-margin-left {
                margin-left: 0;
            }
            .intro-btn {
                margin-right: 16px;
            }
            .intro-hide {
                color: #151b26 !important;
                svg {
                    fill: #151b26 !important;
                }
            }
        }
    }
    .nat-list-wrap {
        width: 100%;
        flex: 1;
        .s-table {
            .s-table-row:hover {
                .name-icon {
                    display: inline;
                }
            }
            .name-icon {
                display: none;
                font-size: 12px;
                fill: #2468f2;
            }
        }
        .icon-renewmanage {
            font-size: 16px;
            color: #0786e9;
        }

        .button-shortcut {
            background-color: #f5f5f5;
            border-color: #ebebeb;
        }
        .nat-tip {
            background: #fcf7f1;
            padding: 5px;
            margin-left: 10px;
            color: #f38900;
        }
        .title {
            font-size: 16px;
            margin-right: 10px;
        }
        .intro-warp {
            display: inline-block;
            .placeholder-style {
                input::-webkit-input-placeholder {
                    /*WebKit browsers*/
                    color: #000;
                }
                input::-moz-input-placeholder {
                    /*Mozilla Firefox*/
                    color: #000;
                }

                input::-ms-input-placeholder {
                    /*Internet Explorer*/
                    color: #000;
                }
            }
        }
        .nat-list-header {
            align-items: center;
            .title {
                display: inline-block;
                margin: 0;
                color: #151b26;
                margin-right: 12px;
                height: 47px;
                line-height: 47px;
                font-weight: 500;
                font-size: 16px;
                margin-left: 16px;
            }
        }
        .tip-icon-wrap {
            font-size: 14px;
            border: 1px solid #f18823;
            color: #f18823;
            &:hover {
                color: #fff;
                background-color: #f18823;
            }
        }
        .nat-buttons-wrap {
            display: flex;
            align-items: center;
            .s-cascader {
                margin-right: 5px;
                .s-cascader-panel {
                    display: flex;
                    height: auto;
                    background: none;
                    box-shadow: none;
                    .s-cascader-column {
                        background: #fff;
                        box-shadow: 0 1px 6px #ccc;
                        max-height: 150px;
                    }
                }
            }
            .s-cascader-value {
                vertical-align: middle;
                font-size: 12px;
                padding-top: 0;
                padding-bottom: 0;
                line-height: 30px;
            }
            .s-auto-compelete {
                .s-select {
                    input {
                        width: 170px !important;
                    }
                }
            }
            .refresh-button {
                margin-right: 5px;
            }
            .search-content {
                display: flex;
                align-items: center;
                position: relative;
                margin-right: 5px;
            }
            .s-icon.search {
                position: absolute;
                right: 5px;
                color: #615a5a;
            }
            .icon-fresh {
                margin-right: 5px;
            }
        }
        .more-opt {
            color: #2468f2 !important;
            border: none !important;
            min-width: 80px !important;
            background: transparent;
            padding-left: 0 !important;
            .s-input-suffix-container {
                border: none !important;
                margin-left: 2px !important;
            }
            .s-trigger-container {
                .s-popup {
                    .s-popup-content-box {
                        .s-select-option-list {
                            padding-bottom: 0px !important;
                        }
                        .s-selectdropdown {
                            width: 100px !important;
                            margin-left: 12px !important;
                            .s-option {
                                height: 25px !important;
                                line-height: 23px !important;
                            }
                        }
                    }
                }
            }
            &:after {
                color: #2468f2 !important;
            }

            &:hover,
            &:active {
                background: transparent;
            }
            &::after {
                content: none !important;
            }
        }
        .opt-wrap {
            .s-button {
                display: block;
                padding: 0;
                margin-right: 12px;
            }
            .nat_multiple_class {
                position: relative;
                top: -4px;
            }
            .placeholder {
                color: #2468f2;
            }
        }
        .s-table {
            .s-table-body {
                max-height: calc(~'100vh - 334px');
                .s-table-cell-text {
                    .tag-wrapper {
                        &:hover {
                            color: #2468f2;
                            cursor: pointer;
                            .tag-icon {
                                .x-icon {
                                    polygon {
                                        color: #2468f2;
                                    }
                                }
                            }
                        }
                    }
                    .truncated {
                        max-width: 75%;
                    }
                    .private-opts {
                        .s-button {
                            padding: 0;
                            margin-right: 12px;
                        }
                    }
                    .natIp-wrapper {
                        font-family: PingFangSC-Regular;
                        font-size: 12px;
                        color: #2468f2;
                        line-height: 20px;
                        font-weight: 400;
                        cursor: pointer;
                    }
                    .more-options {
                        font-family: PingFangSC-Regular;
                        font-size: 12px;
                        color: #151b26;
                        line-height: 20px;
                        font-weight: 400;
                    }
                    .nat-eip-wrapper {
                        .nat-eip {
                            display: flex;
                            .label {
                                display: inline-block;
                                width: 46px;
                            }
                            .eip-wrapper {
                                flex: 1;
                                .value {
                                    flex: 1;
                                    cursor: pointer;
                                    &:hover {
                                        color: #2468f2;
                                    }
                                }
                            }
                        }
                        .clusterMode-nat {
                            cursor: pointer;
                            &:hover {
                                color: #2468f2;
                            }
                        }
                    }
                }
            }
        }

        .nat-intro-wrapper {
            padding: 24px;
            margin: 16px;
            border-radius: 6px;
            @media screen and (max-width: 1280px) {
                background:
                    url('https://bce.bdstatic.com/network-frontend/eip-bg-1280.png') no-repeat,
                    linear-gradient(180deg, rgba(230, 240, 255, 0) 0%, #ffffff 63%);
                background-size: 100% 100%;
            }
            @media screen and (max-width: 1440px) {
                background:
                    url('https://bce.bdstatic.com/network-frontend/eip-bg-1440.png') no-repeat,
                    linear-gradient(180deg, rgba(230, 240, 255, 0) 0%, #ffffff 63%);
                background-size: 100% 100%;
            }
            .nat-intro-title {
                color: #151b26;
                font-weight: 500;
                font-size: 16px;
            }
            @media screen and (max-width: 1680px) {
                .nat-intro-steps {
                    justify-content: space-between;
                    .nat-intro-step {
                        margin: 0 32px;
                        flex: 1;
                    }
                }
            }
            @media screen and (min-width: 1440px) {
                background:
                    url('https://bce.bdstatic.com/network-frontend/eip-bg-1920.png') no-repeat,
                    linear-gradient(180deg, rgba(230, 240, 255, 0) 0%, #ffffff 63%);
                background-size: 100% 100%;
                .nat-intro-steps {
                    justify-content: center;
                    .step-content-wrap {
                        margin: 0 32px;
                    }
                }
            }
            .nat-intro-steps {
                display: flex;
                margin-top: 32px;
                .nat-intro-step {
                    text-align: center;
                    .step-img {
                        width: auto;
                        height: 67px;
                        margin-bottom: 28px;
                    }
                    .step-title-wrap {
                        display: flex;
                        height: 24px;
                        justify-content: center;
                        align-items: center;
                        .step-title-index {
                            font-size: 20px;
                            color: #b8babf;
                            font-weight: 400;
                            height: 28px;
                            line-height: 28px;
                        }
                        .step-title {
                            font-size: 12px;
                            color: #151b26;
                            line-height: 20px;
                            font-weight: 400;
                            margin-left: 4px;
                        }
                    }
                    .step-content {
                        font-size: 12px;
                        color: #84868c;
                        line-height: 20px;
                        font-weight: 400;
                    }
                    .step-content-wrap {
                        text-align: center;
                    }
                }
            }
        }
        .intro-show {
            display: none;
        }
        .intro-show-margin {
            // margin-bottom: 212px;
        }
        .nat-intro-header {
            height: 20px;
            line-height: 20px;
            .close-btn {
                padding: 0;
                float: right;
                color: #2468f2;
            }
        }
        .private-introduce {
            margin: 16px 0 24px;
            font-family: PingFangSC-Regular;
            font-size: 12px;
            color: #5c5f66;
            line-height: 24px;
            font-weight: 400;
        }
        .table-full-wrap {
            height: 100%;
            @media screen and (max-width: 1280px) {
                overflow-x: scroll;
            }
        }
    }
    .new-tag {
        display: inline-block;
        background-color: #f72e32;
        border-radius: 16px;
        line-height: 16px;
        min-width: 40px;
        text-align: center;
        font-size: 12px;
        color: #fff;
    }
    .new-instance-tag {
        background-color: #f33e3e;
        border-radius: 2px;
        line-height: 17px;
    }
}

.drawer-open-class {
    display: none !important;
}

.nat-edit-wrap {
    width: 200px;
}
.vpc-nat-eip-opt {
    margin-top: 10px;

    .bui-boxgroup-group {
        margin-top: 10px;
        label {
            min-width: 95px;
        }
    }
}

.nat-eip-wrap {
    .group-wrap {
        margin-top: 10px;
        max-width: 250px;
        .s-checkbox {
            margin-right: 5px;
        }
    }
    .type-wrap {
        height: 32px;
    }
}

.bui-select-item:hover {
    background-color: #e6f0ff;
    color: #2468f2;
}

.bui-select-item-disabled:hover {
    background-color: #f5f5f5;
    color: #ccc;
}

.s-checkbox-group .s-checkbox-input:checked,
.s-checkbox .s-checkbox-input:checked {
    background-color: #2468f2;
}
.app-vpn-tab {
    .s-tabs {
        padding-top: 54px;
        background-color: #fff;
    }
}
.nat-ip-dropdown {
    .s-popover-body {
        .s-popover-content {
            max-width: 264px;
            max-height: 132px;
            padding: 12px !important;
            overflow: auto;
        }
    }
    .nat-ip-item {
        line-height: 20px;
        margin-top: 8px;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #151b26;
        line-height: 20px;
        font-weight: 400;
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        &:first-child {
            margin-top: 0;
        }
        &:hover {
            color: #2468f2;
        }
    }
}
.nat-ip-tag {
    .s-popover-body {
        .s-popover-content {
            max-width: 264px;
            max-height: 104px;
            padding: 12px !important;
            overflow: auto;
            .nat-no-tag {
                display: flex;
                align-items: center;
                .s-button {
                    padding: 0;
                    margin: -1px 0 0 4px;
                }
            }
            .nat-tag-widget {
                .nat-tag-item {
                    font-family: PingFangSC-Regular;
                    font-size: 12px;
                    color: #151b26;
                    line-height: 20px;
                    font-weight: 400;
                    line-height: 20px;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    overflow: hidden;
                    margin-bottom: 8px;
                }
            }
            .nat-tag-edit {
                padding: 0;
                margin-top: 4px;
            }
        }
    }
}
.private-more-opts {
    .s-popover-body {
        width: 100px;
        overflow: hidden;
        .s-popover-content {
            padding: 0 !important;
        }
    }
    .opts-item {
        width: 100px;
        line-height: 32px;
        &:hover {
            background-color: #e6f0ff;
        }
        .s-button-skin-stringfy {
            padding: 0 12px;
            width: 100px;
            line-height: 32px;
            color: #151b26;
            width: 100%;
            display: inline-block;
            text-align: left;
        }
    }
}

.tag-edit-panel .s-alert {
    height: auto;
}
