import u from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {BcmSDK} from '@baidu/new-bcm-sdk';
import {BcmChartPanel} from '@baidu/new-bcm-sdk-san';
import {Drawer} from '@baidu/sui';

import monitorConfig from '@/pages/sanPages/utils/monitorConfig';

const {natMetrics, natClusterMetrics} = monitorConfig;
const tpl = html`
    <div>
        <s-drawer open="{{true}}" direction="right" class="vpn-list-drawer" size="{{500}}" otherClose="{{false}}">
            <div slot="title">
                NAT网关名称：{{instance.name}}
                <a
                    href="#/vpc/nat/monitor?vpcId={{vpcId}}&id={{natId}}&natType={{natType}}&ipVersion={{ipVersion}}"
                    target="_BLANK"
                >
                    查看更多
                </a>
            </div>
            <div class="monitor-wrap">
                <div s-for="item,index in chartConfig">
                    <bcm-chart-panel
                        withFilter="{{false}}"
                        scope="{{item.scope}}"
                        dimensions="{{item.dimensions}}"
                        statistics="{{item.statistics}}"
                        title="{{item.title}}"
                        time="{{item.time}}"
                        height="{{230}}"
                        width="{{400}}"
                        options="{{item.options}}"
                        api-type="metricName"
                        period="{{item.period}}"
                        metrics="{{item.metrics}}"
                        unit="{{item.unit}}"
                        bitUnit="{{item.bitUnit}}"
                        sdk="{{bcmSdk}}"
                    >
                    </bcm-chart-panel>
                </div>
            </div>
        </s-drawer>
    </div>
`;

export default class NatListMonitor extends Component {
    static template = tpl;
    static components = {
        'bcm-chart-panel': BcmChartPanel,
        's-drawer': Drawer
    };

    static computed = {
        natId() {
            const instance = this.data.get('instance');
            const isPrivate = this.data.get('isPrivate');
            return isPrivate ? instance.natId : instance.id;
        },
        vpcId() {
            const instance = this.data.get('instance');
            const isPrivate = this.data.get('isPrivate');
            return isPrivate ? instance.vpcUuid : instance.vpcId;
        },
        natType() {
            return this.data.get('isPrivate') ? 'private' : 'public';
        },
        ipVersion() {
            const instance = this.data.get('instance');
            return instance.ipVersion || '';
        }
    };

    initData() {
        return {
            chartConfig: [],
            bcmSdk: new BcmSDK({client: window.$http, context: window.$context})
        };
    }

    inited() {
        this.initChart();
    }

    initChart() {
        let instance = this.data.get('instance');
        const isPrivate = this.data.get('isPrivate');
        let chartConfig = [];
        let options = {
            color: ['#2468f2', '#5FB333'],
            legend: {
                x: 'right',
                y: 'top'
            },
            dataZoom: {start: 0}
        };
        let natMetricsConfig = u.cloneDeep(natMetrics);
        if (instance.clusterMode || isPrivate) {
            natMetricsConfig.conn.title = '并发连接数';
            natMetricsConfig.conn.metrics = `ConnNumber(${'并发连接数'})`;
            Object.assign(natMetricsConfig, natClusterMetrics);
        } else {
            natMetricsConfig = natMetrics;
        }
        u.each(natMetricsConfig, item => {
            let config = {
                scope: 'BCE_NAT',
                time: '1h',
                period: 60,
                statistics: item.statistics || 'maximum',
                title: item.title,
                unit: item.unit,
                bitUnit: item.bitUnit,
                metrics: item.metrics,
                dimensions: `NatId:${isPrivate ? instance.natId : instance.id};FixIp:0.0.0.0`,
                options
            };
            chartConfig.push(config);
        });
        this.data.set('chartConfig', chartConfig);
    }
}
