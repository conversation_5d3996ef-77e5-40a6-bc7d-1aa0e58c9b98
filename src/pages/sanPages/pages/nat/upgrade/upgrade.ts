/**
 * @file network/nat/pages/Upgrade.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {Button, Form, Radio, Tooltip, Slider, InputNumber} from '@baidu/sui';
import {AppCreatePage} from '@baidu/sui-biz';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {BillingSdk, OrderItem, UPDATE_STATUS} from '@baiducloud/bce-billing-sdk';
import {OrderConfirm, ShoppingCart, TotalPrice, Protocol} from '@baiducloud/bce-billing-sdk-san';
import Client from '@baiducloud/httpclient';

import {PayType, NatFlavor, NAT_ENHANCED_MAPPING} from '@/pages/sanPages/common/enum';
import {toTime, contextPipe, $flag as FLAG} from '@/pages/sanPages/utils/helper';
import testID from '@/testId';
import './style.less';

const {asPage, service} = decorators;
const toClusterCuNumMin = {
    little: 1,
    medium: 3,
    large: 7,
    enhanced_12c6q: 14
};

/* eslint-disable */
const template = html`
    <template>
        <s-page
            class="{{klass}}"
            backTo="{{pageNav.url}}"
            backToLabel="{{pageNav.backLabel}}"
            pageTitle="{{pageNav.title}}"
            style="display: {{confirming ? 'none' : 'block'}}"
        >
            <s-form data="{=formData=}" rules="{{rules}}" s-ref="form">
                <div class="body-part-content base-info form-part-wrap">
                    <h4>基本信息</h4>
                    <div>
                        <span class="item-col">
                            <label class="item-label">NAT网关名称：</label>
                            <span class="item-content">
                                {{instance.name}}
                            </span>
                        </span>
                        <span s-if="isPrivate" class="item-col">
                            <label class="item-label">ID：</label>
                            <span class="item-content">
                                <span>{{instance.id || instance.natId}}</span>
                            </span>
                        </span>
                        <span s-if="isPrivate" class="item-col">
                            <label class="item-label">地域：</label>
                            <span class="item-content">
                                {{currentRegion}}
                            </span>
                        </span>
                        <span s-if="isPrivate" class="item-col">
                            <label class="item-label">类型：</label>
                            <span class="item-content">
                                {{(instance.cuNum || '') + ' CU'}}
                            </span>
                        </span>
                        <span s-if="!isPrivate" class="item-col">
                            <label class="item-label">NAT公网IP：</label>
                            <span class="item-content">
                                {{instance.eips | eipContent}}
                            </span>
                        </span>
                        <span class="item-col">
                            <label class="item-label">计费方式：</label>
                            <span class="item-content">
                                <span>{{instance.productType | productTypeText}}</span>
                            </span>
                        </span>
                        <span s-if="!isPrivate" class="item-col">
                            <label class="item-label">ID：</label>
                            <span class="item-content">
                                <span>{{instance.id}}</span>
                            </span>
                        </span>
                        <span s-if="!isPrivate" class="item-col">
                            <label class="item-label">类型：</label>
                            <span class="item-content">
                                {{instance | flavorText}}
                            </span>
                        </span>
                        <span class="item-col" s-if="flag.NetworkNatOpt && !isPrivate">
                            <label class="item-label">到期时间：</label>
                            <span class="item-content">
                                {{instance.expiredTime | timeFormat}}
                            </span>
                        </span>
                        <span s-if="!isPrivate" class="item-col">
                            <label class="item-label">地域：</label>
                            <span class="item-content">
                                {{currentRegion}}
                            </span>
                        </span>
                        <span class="item-col" s-if="flag.NetworkNatSupOrganization && !isPrivate">
                            <label class="item-label">项目信息：</label>
                            <span class="item-content">
                                <span s-for="item,index in instance.resourceGroups" >
                                    {{item.resourceGroupName}}
                                </span>
                            </span>
                        </span>
                    </div>
                </div>
                <div class="body-part-content form-part-wrap">
                    <h4>变更配置</h4>
                    <s-form-item label="类型：" prop="flavor">
                        <div class="slider-container" s-if="{{instance.clusterMode || isPrivate}}">
                            <div class="slider-wrapper">
                                <s-slider
                                    ref="drag"
                                    parts="{{2}}"
                                    marks="{{marks}}"
                                    max="{{cuNum.max}}"
                                    min="{{cuNum.min}}"
                                    step="{{cuNum.step}}"
                                    value="{=formData.cuNum=}"
                                />
                                <div class="dragger-input">
                                    <s-input-number
                                        value="{=formData.cuNum=}"
                                        max="{{cuNum.max}}"
                                        min="{{cuNum.min}}"
                                    />
                                    CU
                                </div>
                            </div>
                            <p class="slider-info">
                                NAT网关性能容量参数：新建连接数{{formData.cuNum * 1000}}个/秒、
                                并发连接数{{formData.cuNum * 10000}}个、 转发能力{{formData.cuNum}}Gbps
                            </p>
                        </div>
                        <s-radio-group
                            s-else
                            value="{=toClusterMode=}"
                            radioType="button"
                            datasource="{{ToClusterModeList}}"
                            class="nat-mode-wrap"
                            enhanced
                        >
                    </s-form-item>
                    <s-form-item s-if="{{!instance.clusterMode && !toClusterMode && !isPrivate}}">
                        <s-radio-group
                            class="flavor-radio"
                            datasource="{{flavor.datasource}}"
                            disabled="{{flavor.disabled}}"
                            value="{=formData.flavor=}"
                            on-change="onFlavorChange"
                            radioType="button"
                            track-id="ti_vpc_nat_create"
                            track-name="NAT网关类型">
                        </s-radio-group>
                    </s-form-item>
                    <s-form-item label="性能容量：" prop="cuNum" s-if="{{!instance.clusterMode && toClusterMode && !isPrivate}}">
                        <div class="slider-wrapper">
                            <s-slider
                                ref="toCluster-drag"
                                parts="{{2}}"
                                marks="{{toClustermarks}}"
                                max="{{toClustercuNum.max}}"
                                min="{{toClustercuNum.min}}"
                                step="{{toClustercuNum.step}}"
                                value="{=toClustercuNum.value=}"/>
                            <div class="dragger-input">
                                <s-input-number
                                value="{=toClustercuNum.value=}"
                                max="{{toClustercuNum.max}}"
                                min="{{toClustercuNum.min}}"
                                    /> CU
                            </div>
                        </div>
                        <p class="slider-info">
                            NAT网关性能容量参数：新建连接数{{toClustercuNum.value * 1000}}个/秒、
                            并发连接数{{toClustercuNum.value * 10000}}个、
                            转发能力{{toClustercuNum.value}}Gbps
                        </p>
                    </s-form-item>
                </div>
            </s-form>
            <div slot="pageFooter" class="buybucket">
                <div class="buybucket-container">
                    <s-tooltip placement="top" trigger="{{noUpgrade ? 'hover' : ''}}">
                        <!--bca-disable-next-line-->
                        <div slot="content">{{noUpgradeText | raw}}</div>
                        <s-button
                            skin="primary"
                            size="large"
                            class="no-mg-bt"
                            on-click="onConfirm"
                            disabled="{{noUpgrade || priceLoading}}"
                            data-test-id="${testID.nat.upgradeConfirm}"
                            >{{'确认订单'}}</s-button
                        >
                    </s-tooltip>
                    <s-button size="large" on-click="cancel">取消</s-button>
                    <shopping-cart
                        sdk="{{newBillingSdk}}"
                        on-reset="onReset"
                        addItemToCartAvailable="{{addItemToCartAvailable}}"
                        addItemToCartDisable="{=priceLoading=}"
                        on-change="onShoppingCartChange"
                        theme="default"
                    ></shopping-cart>
                </div>
            </div>
        </s-page>
        <s-page
            class="{{klass}}"
            backToLabel="{{confirmPageNav.backLabel}}"
            pageTitle="{{confirmPageNav.title}}"
            backTo="{{confirmPageNav.url}}"
            content-in-center
            s-if="confirming"
        >
            <order-confirm
                s-ref="orderConfirm"
                sdk="{{newBillingSdk}}"
                use-coupon="{{instance.productType === 'prepay' && priceHas && FLAG.NetworkSupportXS}}"
                theme="default"
                showAgreementCheckbox
            />
            <div slot="pageFooter" class="buybucket">
                <div class="buybucket-container">
                    <div class="buybucket-container-protocol">
                        <billing-protocol s-if="!FLAG.NetworkSupportXS" s-ref="billingProtocol" />
                        <s-button size="large" on-click="onBack" class="no-mg-bt" data-test-id="${testID.nat.createBackPre}">{{'上一步'}}</s-button>
                        <s-button size="large" on-click="cancel">取消</s-button>
                        <s-button skin="primary" size="large" disabled="{{disableSub}}" on-click="onPay" data-test-id="${testID.nat.createSubmitOrder}">
                            {{'提交订单'}}
                        </s-button>
                    </div>
                    <total-price sdk="{{newBillingSdk}}" />
                </div>
            </div>
        </s-page>
    </template>
`;
/* eslint-enable */
class NatUpgrade extends Component {
    static template = template;
    static components = {
        's-page': AppCreatePage,
        's-form': Form,
        's-form-item': Form.Item,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        'shopping-cart': ShoppingCart,
        'order-confirm': OrderConfirm,
        's-button': Button,
        's-tooltip': Tooltip,
        's-slider': Slider,
        's-input-number': InputNumber,
        'total-price': TotalPrice,
        'billing-protocol': Protocol
    };
    static filters = {
        timeFormat(time) {
            if (!time) {
                return '-';
            }
            return toTime(time);
        },
        eipContent(eips) {
            return _.pluck(eips, 'eip').join('、');
        },
        productTypeText(productType) {
            return PayType.getTextFromValue(productType);
        },
        flavorText(instance) {
            if (instance) {
                const {flavor, clusterMode, ipVersion} = instance;
                if (clusterMode) {
                    return NAT_ENHANCED_MAPPING.getTextFromValue(ipVersion) || '增强型-IPv4';
                }
                const config = NatFlavor.fromValue(flavor);
                return config.text ? `普通型（${config.text}）` : '普通型（超大）';
            }
            return '';
        }
    };
    static computed = {
        flavorText() {
            const {flavor, cuNum} = this.data.get('formData');
            const clusterMode = this.data.get('instance.clusterMode');
            let toClusterMode = this.data.get('toClusterMode');
            if (clusterMode) {
                return `${cuNum}CU`;
            } else if (!toClusterMode) {
                const config = NatFlavor.fromValue(flavor);
                return config ? config.text : '';
            }
            return `增强型-IPv4 ` + this.data.get('toClustercuNum.value') + 'CU';
        },
        marks() {
            let marks = {};
            let min = this.data.get('cuNum.min');
            let max = this.data.get('cuNum.max');
            let middle = _.round(max / 2);
            marks[min] = min + 'CU';
            marks[max] = max + 'CU';
            marks[middle] = middle + 'CU';
            return marks;
        },
        noUpgrade() {
            let instance = this.data.get('instance') || {};
            let formData = this.data.get('formData') || {};
            if (instance.flavor && !this.data.get('toClusterMode')) {
                if (instance.flavor === formData.flavor) {
                    return true;
                }
                return false;
            } else if (instance.flavor && this.data.get('toClusterMode')) {
                return false;
            }
            if (instance.cuNum === formData.cuNum) {
                return true;
            }
            return false;
        },
        noUpgradeText() {
            let noUpgrade = this.data.get('noUpgrade');
            if (noUpgrade) {
                return '请先变更配置';
            }
            return '';
        },
        toClustermarks() {
            let marks = {};
            let min = this.data.get('toClustercuNum.min');
            let max = this.data.get('toClustercuNum.max');
            let middle = _.round(max / 2);
            marks[min] = min + 'CU';
            marks[max] = max + 'CU';
            marks[middle] = middle + 'CU';
            return marks;
        },
        toClustercuNum() {
            let flavor = this.data.get('instance.flavor');
            let min = toClusterCuNumMin[flavor] ? toClusterCuNumMin[flavor] : 14;
            return {
                min: 1,
                max: 100,
                mid: 50,
                step: 1,
                unit: 'CU',
                value: min
            };
        },
        isPrivate() {
            const natType = this.data.get('urlQuery.from');
            return natType === 'private';
        },
        isEnhancedIPv6() {
            const ipVersion = this.data.get('instance.ipVersion');
            return ipVersion === 'v6';
        }
    };
    initData() {
        return {
            FLAG,
            flag: FLAG,
            confirming: false,
            klass: ['vpc-nat-upgrade'],
            pageNav: {
                title: '升级NAT网关配置',
                backLabel: '返回',
                url: '/network/#/vpc/nat/list'
            },
            confirmPageNav: {
                title: '升级NAT网关配置',
                backLabel: '返回',
                url: '/network/#/vpc/nat/list'
            },
            currentRegion: window.$context.getCurrentRegion().label,
            rules: {},
            formErrors: null,
            formData: {},
            flavor: {
                datasource: NatFlavor.toArray(),
                disabled: true
            },
            cuNum: {
                min: 1,
                max: 100,
                mid: 50,
                step: 1,
                unit: 'CU',
                value: 1
            },
            toClustercuNum: {
                min: 1,
                max: 100,
                mid: 50,
                step: 1,
                unit: 'CU',
                value: 1
            },
            bucket: {
                price: [
                    {
                        title: '配置费用：',
                        count: '0',
                        extra: ''
                    }
                ],
                datasource: [],
                confirmText: '确认变更',
                disabled: true
            },
            disableSub: false,
            confirm: {
                previous: true
            },
            priceLoading: true,
            confirmedPay: false,
            addItemToCartAvailable: false,
            toClusterMode: false,
            ToClusterModeList: [
                {text: '普通型', value: false},
                {text: '增强型-IPv4', value: 'IPv4'}
            ],
            urlQuery: getQueryParams()
        };
    }
    inited() {
        const {vpcId, id} = this.data.get('urlQuery');
        this.data.set('vpcId', vpcId);
        this.data.set('id', id);
        this.initPrivateConfig();
        const client = new Client({}, {}.$context);
        // 创建实例
        const sdk = new BillingSdk({
            client,
            AllRegion: window.$context.getEnum('AllRegion'),
            context: contextPipe(this)
        });
        this.data.set('newBillingSdk', sdk);
    }
    attached() {
        this.watch(
            'formData.cuNum',
            _.debounce(value => {
                if (this.data.get('instance')) {
                    this.loadPrice();
                }
            }, 300)
        );
        this.watch('toClusterMode', value => {
            this.loadPrice();
        });
        this.watch(
            'toClustercuNum',
            _.debounce(value => {
                if (this.data.get('instance')) {
                    this.loadPrice();
                }
            }, 300)
        );
        this.loadDetail()
            .then(() => this.updateFlavor())
            .then(() => {
                // 防止增强型重复请求
                if (!this.data.get('instance.clusterMode')) {
                    this.loadPrice();
                }
            });
    }

    initPrivateConfig() {
        const {from} = this.data.get('urlQuery');
        if (from === 'private') {
            this.data.set('pageNav.url', '/network/#/vpc/privateNat/list');
            this.data.set('confirmPageNav.url', '/network/#/vpc/privateNat/list');
            this.data.set('pageNav.title', '性能容量变配');
            this.data.set('confirmPageNav.title', '性能容量变配');
        }
    }

    cancel() {
        const isPrivate = this.data.get('urlQuery.from') === 'private';
        const backUrl = isPrivate ? '#/vpc/privateNat/list' : '#/vpc/nat/list';
        location.hash = backUrl;
    }

    loadDetail() {
        const urlQuery = this.data.get('urlQuery');
        const isPrivate = urlQuery.from === 'private';
        const isEnhancedIPv6 = urlQuery.ipVersion === 'v6';
        const reqUrl = isPrivate ? 'getPrivateNatDetail' : isEnhancedIPv6 ? 'getIPv6NatDetail' : 'getNatList';
        const paramKey = isPrivate || isEnhancedIPv6 ? 'natId' : 'natGatewayId';
        const payload: any = {[paramKey]: this.data.get('urlQuery.id')};
        return this.$http[reqUrl](payload).then(data => {
            if (!isPrivate && !isEnhancedIPv6) {
                let result = FLAG.NetworkNatOpt ? data.result[0] : data.natgateways[0];
                result.cuNum = result.cuNum || toClusterCuNumMin[result.flavor];
                this.data.set('instance', result);
                this.data.set('formData.cuNum', result.cuNum + 1);
            } else {
                this.data.set('instance', data);
                this.data.set('formData.cuNum', data?.cuNum);
            }
        });
    }
    updateFlavor() {
        const {flavor, productType} = this.data.get('instance');
        let flavorDatasource = NatFlavor.toArray();
        const flavorIndex = _.findIndex(flavorDatasource, item => item.value === flavor);
        if (flavor === 'enhanced_12c6q') {
            this.data.set('toClusterMode', true);
            this.data.set('ToClusterModeList', [
                {text: '普通型', value: false, disabled: true},
                {text: '增强型-IPv4', value: 'IPv4'}
            ]);
        }
        if (flavorIndex > -1) {
            this.data.set('flavor', {
                datasource: flavorDatasource,
                disabled: false
            });
            const newFlavorItem = _.find(flavorDatasource, item => !item.disabled);
            if (newFlavorItem) {
                this.data.set('formData.flavor', newFlavorItem.value);
                this.data.set('initFlavor', newFlavorItem.value);
            }
        }
    }
    onFlavorChange(e) {
        this.data.set('formData.flavor', e.value);
        this.loadPrice();
    }
    getConfig() {
        const {formData} = this.data.get('');
        const id = this.data.get('urlQuery.id');
        let payload: any = {
            clusterMode: this.data.get('instance.clusterMode'),
            natGatewayId: id
        };
        if (this.data.get('instance.clusterMode')) {
            payload.cuNum = formData.cuNum;
        } else if (this.data.get('toClusterMode')) {
            payload.cuNum = this.data.get('toClustercuNum.value');
            payload.toClusterMode = this.data.get('toClusterMode');
        } else {
            payload.flavor = formData.flavor;
        }
        // 私网nat
        const isPrivate = this.data.get('isPrivate');
        if (isPrivate) {
            payload.cuNum = formData.cuNum;
            payload.natId = id;
            delete payload.clusterMode;
            delete payload.natGatewayId;
        }
        // 增强型IPv6
        const isEnhancedIPv6 = this.data.get('isEnhancedIPv6');
        if (isEnhancedIPv6) {
            payload.cuNum = formData.cuNum;
            payload.natId = id;
            payload.clusterMode;
            payload.natGatewayId;
        }
        return payload;
    }
    loadPrice() {
        const {newBillingSdk} = this.data.get('');
        newBillingSdk.clearItems();
        const payload = this.initPriceFlavor();
        const orderItem = new OrderItem(payload);
        this.data.set('bucketItems', [orderItem]);
        newBillingSdk.addItems([orderItem]);
    }
    onConfirm() {
        const form = this.ref('form');
        return form.validateFields().then(() => {
            const {instance, flavorText} = this.data.get('');
            const isPrivate = this.data.get('isPrivate');
            const cuNum = this.data.get('formData.cuNum');
            let configDetail = [
                {
                    label: '地域',
                    value: this.$context.getCurrentRegion().label
                },
                {
                    label: '计费方式',
                    value: instance.productType === PayType.PREPAY ? '包年包月' : '按时长'
                },
                {
                    label: 'NAT网关名称',
                    value: instance.name || '-'
                },
                {
                    label: '配置',
                    value: isPrivate ? `${cuNum}CU` : flavorText
                }
            ];

            const orderItem = this.data.get('bucketItems');
            orderItem[0].updateConfigDetail(configDetail);
            this.data.set('confirming', true);
        });
    }
    onBack() {
        this.data.set('confirming', false);
        this.setConfigDetail();
    }
    async onPay() {
        !FLAG.NetworkSupportXS && (await this.ref('billingProtocol').validateAgreement());
        this.data.set('disableSub', true);
        let {newBillingSdk, bucketItems} = this.data.get('');
        let params = {
            items: [
                {
                    config: this.getConfig(),
                    paymentMethod: bucketItems[0].couponId ? [{type: 'coupon', values: [bucketItems[0].couponId]}] : []
                }
            ]
        };
        try {
            const isPrivate = this.data.get('isPrivate');
            const isEnhancedIPv6 = this.data.get('isEnhancedIPv6');
            let confirmUrl = '/api/nat/order/confirm/resize?orderType=RESIZE';
            if (isPrivate) {
                confirmUrl = '/api/intranet/nat/order/confirm/resize?orderType=RESIZE';
            } else if (isEnhancedIPv6) {
                confirmUrl = '/api/nat/ipv6/order/confirm/resize';
            }
            const data = await this.$http.newConfirmOrder(confirmUrl, params);
            let url = '';
            try {
                const info = await newBillingSdk.checkPayInfo(data);
                url = info.url + '&fromService=NAT';
                info.url && (location.href = url);
            } catch (info) {
                // 跳转到相应页面
                url = info.url + '&fromService=NAT';
                info.url && (location.href = url);
            }
        } catch (err) {}
        this.data.set('disableSub', false);
    }
    onReset() {
        const initFlavor = this.data.get('initFlavor');
        const initCuNum = this.data.get('instance.cuNum');
        this.data.set('formData', {flavor: initFlavor, cuNum: initCuNum});
        this.loadPrice();
    }
    onRegionChange() {
        location.hash = '#/vpc/nat/list';
    }

    initPriceFlavor() {
        const payload = this.getConfig();
        const instance = this.data.get('instance');
        let toClusterMode = this.data.get('toClusterMode');
        let toClusterModeCu = this.data.get('toClustercuNum.value');
        let datasource = this.data.get('flavor.datasource');
        let index = _.findIndex(datasource, item => item.value === this.data.get('formData.flavor'));
        let originIndex = _.findIndex(datasource, item => item.value === this.data.get('instance.flavor'));
        let orderType = 'DILATATION';
        if (instance.flavor) {
            if (index < originIndex) {
                orderType = 'SHRINKAGE';
            }
        }
        if (toClusterMode && instance.cuNum < toClusterModeCu) {
            orderType = 'DILATATION';
        }
        let priceParams = {
            serviceType: 'NAT',
            configName: '配置费用',
            serviceName: 'NAT网关',
            chargeItem: instance.productType === PayType.PREPAY ? 'Cpt2' : 'RunningTimeMinutes',
            productType: instance.productType,
            region: window.$context.getCurrentRegionId(),
            type: instance.productType === PayType.PREPAY ? orderType : 'RESIZE',
            timeUnit: instance.productType === PayType.PREPAY ? 'MONTH' : 'MINUTE',
            count: 1,
            instanceId: payload.natGatewayId,
            flavor: this.getFlavorData(payload)
        };
        // 新增shoppingCart详情展示页面
        let configuration = this.getConfigDetail();
        priceParams.configDetail = configuration;
        return priceParams;
    }
    getFlavorData(payload) {
        let flavorList = [{name: 'nat_gateway_scale', value: payload.flavor, scale: 1}];
        if (payload.clusterMode || payload.toClusterMode) {
            flavorList = [
                {name: 'subServiceType', value: 'enhanced', scale: 1},
                {name: 'Instance', value: 'default', scale: 1},
                {name: 'CU', value: payload.cuNum, scale: 1}
            ];
        }
        const isPrivate = this.data.get('isPrivate');
        const privateFlavor = [
            {
                name: 'Instance',
                value: 'default',
                scale: 1
            },
            {
                name: 'CU',
                value: payload.cuNum,
                scale: 1
            },
            {
                name: 'subServiceType',
                value: 'default',
                scale: 1
            }
        ];
        return isPrivate ? privateFlavor : flavorList;
    }
    // 新增onShoppingCartChange
    onShoppingCartChange(e) {
        this.data.set('priceLoading', UPDATE_STATUS[e] !== UPDATE_STATUS.DONE);
        if (UPDATE_STATUS[e] === UPDATE_STATUS.DONE) {
            const bucketItems = this.data.get('bucketItems');
            let unitPrice = bucketItems[0]?.unitPrice;
            // 用来判断是否需要二次询价
            if (+unitPrice < 0) {
                this.data.set('priceHas', false);
            } else {
                this.data.set('priceHas', true);
            }
        }
    }

    getConfigDetail() {
        const {flavorText} = this.data.get('');
        const isPrivate = this.data.get('isPrivate');
        const cuNum = this.data.get('formData.cuNum');
        let datasource = [{label: '配置', value: isPrivate ? `${cuNum}CU` : flavorText}];
        return datasource;
    }

    setConfigDetail() {
        let configuration = this.getConfigDetail();
        const orderItem = this.data.get('bucketItems');
        if (!orderItem[0]) {
            return;
        }
        orderItem[0].updateConfigDetail(configuration);
    }
}
export default San2React(Processor.autowireUnCheckCmpt(NatUpgrade));
