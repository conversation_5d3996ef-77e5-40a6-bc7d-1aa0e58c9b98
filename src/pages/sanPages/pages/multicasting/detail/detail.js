import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {Notification} from '@baidu/sui';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';

import {getVpcName} from '@/pages/sanPages/utils/common';
import {utcToTime} from '@/pages/sanPages/utils/helper';
import Rule from '@/pages/sanPages/utils/rule';
import './style.less';

const {invokeComp, invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent} = decorators;
const tpl = html`
    <div>
        <div class="{{klass}}">
            <div class="multicasting-content-wrap">
                <div class="content-box">
                    <div class="content-header">
                        <h4 class="multicasting-common-label">{{'基本信息：'}}</h4>
                    </div>
                    <div class="content-item-box">
                        <div class="content-item">
                            <div class="content-item-key">ID：</div>
                            <div class="content-item-value">
                                <span class="text-hidden">{{instance.multicastGroupId}}</span>
                                <s-clip-board
                                    class="blue-icon"
                                    text="{{instance.multicastGroupId}}"
                                    successMessage="{{'已复制到剪贴板'}}"
                                >
                                    <s-icon name="copy" />
                                </s-clip-board>
                            </div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'名称：'}}</div>
                            <div class="content-item-value" s-ref="edit-name">
                                <span class="text-hidden"> {{instance.name || '-'}} </span>
                                <edit-popover
                                    value="{=instance.name=}"
                                    rule="{{Rule.NAME}}"
                                    width="320"
                                    on-edit="updateName"
                                >
                                    <a href="javascript:void(0)" style="margin-left: 8px">变更</a>
                                </edit-popover>
                            </div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'所在网络：'}}</div>
                            <div class="content-item-value">
                                <a href="#/vpc/instance/detail?vpcId={{urlQuery.vpcId}}">
                                    {{instance.vpcName || '-'}}{{vpcInfo.cidr | filterCidr}}
                                </a>
                            </div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'创建时间：'}}</div>
                            <div class="content-item-value">{{instance.createdTime | filterTime}}</div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'描述：'}}</div>
                            <div class="content-item-value" s-ref="edit-desc">
                                <span class="text-hidden"> {{instance.description || '-'}} </span>
                                <edit-popover
                                    value="{=instance.description=}"
                                    rule="{{Rule.DESC}}"
                                    on-edit="updateDesc"
                                >
                                    <a href="javascript:void(0)" style="margin-left: 8px">变更</a>
                                </edit-popover>
                            </div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'组播IP：'}}</div>
                            <div class="content-item-value">{{instance.ipAddress}}</div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'组播源：'}}</div>
                            <div class="content-item-value">{{instance | getSourceLength}}</div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'组播成员：'}}</div>
                            <div class="content-item-value">{{instance | getGroupLength}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@invokeComp('@edit-popover')
@asComponent('@multicasting-detail')
class MulticastingDetail extends Component {
    static filters = {
        filterTime(value) {
            return value ? utcToTime(value) : '-';
        },
        filterCidr(value) {
            return value ? `(${value})` : '';
        },
        getSourceLength(value) {
            return value ? value.multicastSources?.length : 0;
        },
        getGroupLength(value) {
            return value ? value.multicastMembers?.length : 0;
        }
    };
    initData() {
        return {
            klass: ['main-wrap multicasting-detail-wrap multicasting-common-page'],
            instance: {},
            unset: '未配置',
            Rule: {
                NAME: {
                    placeholder: '大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-50',
                    required: true,
                    requiredErrorMessage: '名称必填',
                    /* eslint-disable */
                    pattern: /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\_\/\.]{0,49}$/,
                    /* eslint-enable */
                    patternErrorMessage: '支持大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-50'
                },
                DESC: Rule.DETAIL_EDIT.DESC
            },
            urlQuery: getQueryParams()
        };
    }

    attached() {
        this.loadPage();
    }

    loadPage() {
        this.getDetail().then(() => {
            this.getVpcInfo();
        });
    }

    updateName(value) {
        const updatePayload = {
            name: value
        };
        this.$http
            .multicastingUpdate(u.extend({multicastGroupId: this.data.get('context').id}, updatePayload))
            .then(() => {
                Notification.success('修改成功');
                this.data.set('instance.name', value);
                this.data.get('context').getDetail();
            });
    }

    updateDesc(value) {
        const updatePayload = {
            description: value
        };
        this.$http
            .multicastingUpdate(u.extend({multicastGroupId: this.data.get('context').id}, updatePayload))
            .then(() => {
                Notification.success('修改成功');
                this.data.set('instance.description', value);
            });
    }

    getDetail() {
        return this.$http
            .getMultiCastingList({
                vpcUuid: this.data.get('context').vpcId,
                multicastGroupLongIds: [this.data.get('context').id]
            })
            .then(data => {
                this.data.set('instance', data?.result[0] || {});
            });
    }
    getVpcInfo() {
        let vpcId = this.data.get('context').vpcId || '';
        this.$http.getVpcDetail({vpcIds: [vpcId]}).then(data => {
            let vpc = data[vpcId] || {};
            vpc.name = getVpcName(vpc.name);
            this.data.set('vpcInfo', vpc);
        });
    }
}

export default San2React(Processor.autowireUnCheckCmpt(MulticastingDetail));
