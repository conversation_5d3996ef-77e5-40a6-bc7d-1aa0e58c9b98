import {Component} from 'san';
import _ from 'lodash';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {GATEWAY_TABLE} from './tableField';
import {Message, Notification} from '@baidu/sui';
import {OutlinedPlus} from '@baidu/sui-icon';
import delDules from '../../rules';
import RULE from '@/pages/sanPages/utils/rule';
import {checkIsInSubnet} from '@/pages/sanPages/utils/common';
import rules from '../../rules';
import BindToInstance from './bind/BindToInstance';
import Confirm from '@/pages/sanPages/components/confirm';
import './style.less';

const validateRules = self => {
    return {
        ip: [
            {required: true, message: '请填写IP地址'},
            {
                validator: (rule, value, callback) => {
                    let vpcCidr = self.data.get('vpcCidr');
                    value = value || self.data.get('formData.ip');
                    if (!RULE.IP.test(value)) {
                        return callback('IP地址格式有误');
                    }
                    if (vpcCidr && !checkIsInSubnet(value + '/32', vpcCidr)) {
                        return callback(`当前IP地址不在VPC网段（${vpcCidr}）内`);
                    }
                    return callback();
                }
            }
        ]
    };
};
const {asComponent, invokeSUI, invokeSUIBIZ, template} = decorators;

const tpl = html`
    <div class="gateway-control-container">
        <div class="gateway-control-group">
            <div class="gateway-detail-table">
                <h4>组播源</h4>
                <div class="inline_class">
                    <s-tooltip class="left_class" trigger="{{addSourceDisable.disable ? 'hover' : ''}}" placement="top">
                        <div slot="content">
                            <!--bca-disable-next-line-->
                            {{addSourceDisable.message | raw}}
                        </div>
                        <s-button
                            skin="primary"
                            disabled="{{addSourceDisable.disable || loadInstance || tableSourceLoading || loadQuotaSourceDis || allDataLoading}}"
                            on-click="onBindInstance('source')"
                        >
                            <outlined-plus />
                            组播源
                        </s-button>
                    </s-tooltip>
                    <s-tooltip class="left_class" trigger="{{ingressDelRule.disable ? 'hover' : ''}}" placement="top">
                        <!--bca-disable-next-line-->
                        <div slot="content">{{ingressDelRule.message | raw}}</div>
                        <s-button on-click="onDelete('source')" disabled="{{ingressDelRule.disable}}"> 删除</s-button>
                    </s-tooltip>
                </div>
                <div class="list-section">
                    <s-table
                        loading="{{tableSourceLoading}}"
                        datasource="{{ingressTable.datasource}}"
                        columns="{{ingressTable.schema}}"
                        on-selected-change="tableSelected($event, 'source')"
                        selection="{=ingressTable.selection=}"
                    >
                        <div slot="empty">
                            <s-empty on-click="onBindInstance('source')"> </s-empty>
                        </div>
                        <div slot="c-bps">{{row.bps}}Mbps</div>
                        <div slot="c-opt">
                            <s-button skin="stringfy" on-click="onEdit(row, 'source')">删除</s-button>
                        </div>
                    </s-table>
                    <s-pagination
                        s-if="{{sourcePager.total}}"
                        class="pagination-class-fl-tp"
                        slot="pager"
                        layout="{{'total, pageSize, pager, go'}}"
                        pageSize="{{sourcePager.pageSize}}"
                        total="{{sourcePager.total}}"
                        page="{{sourcePager.page}}"
                        on-pagerChange="onSourcePagerChange"
                        on-pagerSizeChange="onSourceSizeChange"
                    />
                </div>
            </div>
            <div class="gateway-detail-table">
                <h4>组播成员</h4>
                <div class="inline_class">
                    <s-tooltip class="left_class" trigger="{{addMemberDisable.disable ? 'hover' : ''}}" placement="top">
                        <div slot="content">
                            <!--bca-disable-next-line-->
                            {{addMemberDisable.message | raw}}
                        </div>
                        <s-button
                            skin="primary"
                            disabled="{{addMemberDisable.disable || loadInstance || tableMemberLoading || loadQuotaMemberDis || allDataLoading}}"
                            on-click="onBindInstance('member')"
                        >
                            <outlined-plus />
                            组播成员
                        </s-button>
                    </s-tooltip>
                    <s-tooltip class="left_class" trigger="{{egressDelRule.disable ? 'hover' : ''}}" placement="top">
                        <!--bca-disable-next-line-->
                        <div slot="content">{{egressDelRule.message | raw}}</div>
                        <s-button on-click="onDelete('member')" disabled="{{egressDelRule.disable}}"> 删除</s-button>
                    </s-tooltip>
                </div>

                <div class="list-section">
                    <s-table
                        loading="{{tableMemberLoading}}"
                        datasource="{{egressTable.datasource}}"
                        columns="{{egressTable.schema}}"
                        on-selected-change="tableSelected($event, 'member')"
                        selection="{=egressTable.selection=}"
                    >
                        <div slot="empty">
                            <s-empty on-click="onBindInstance('member')"> </s-empty>
                        </div>
                        <div slot="c-opt">
                            <s-button skin="stringfy" on-click="onEdit(row, 'member')">删除</s-button>
                        </div>
                        <div slot="c-sourceIp">{{row.memberIp}}</div>
                    </s-table>
                    <s-pagination
                        s-if="{{memberPager.total}}"
                        class="pagination-class-fl-tp"
                        slot="pager"
                        layout="{{'total, pageSize, pager, go'}}"
                        pageSize="{{memberPager.pageSize}}"
                        total="{{memberPager.total}}"
                        page="{{memberPager.page}}"
                        on-pagerChange="onMemberPagerChange"
                        on-pagerSizeChange="onMemberSizeChange"
                    />
                </div>
            </div>
        </div>
        <s-dialog title="{{dialogTitle}}" on-close="close" width="360" open="{=dialogDisplay=}">
            <div class="gateway-control-form-container">
                <s-alert
                    >{{direction === 'source' ? '组播源' : '组播成员'}}不支持一代BBC，请不要填写一代BBC的IP。</s-alert
                >
                <s-form s-ref="rules-ref" data="{=formData=}" rules="{{rules}}" label-align="left">
                    <s-form-item label="IP地址：" prop="ip">
                        <s-input value="{=formData.ip=}" placeholder="请输入IP，如：************" width="320" />
                    </s-form-item>
                </s-form>
            </div>
            <div slot="footer">
                <s-button on-click="close">取消</s-button>
                <s-button skin="primary" disabled="{{confirmDisabled}}" on-click="dialogConfirm">确定</s-button>
            </div>
        </s-dialog>
    </div>
`;
@template(tpl)
@invokeSUI
@invokeSUIBIZ
@asComponent('@multicasting-ip-list')
class MulticastingIpList extends Component {
    static components = {
        'outlined-plus': OutlinedPlus
    };
    initData() {
        return {
            ingressTable: {
                schema: GATEWAY_TABLE,
                datasource: [],
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                }
            },
            egressTable: {
                schema: GATEWAY_TABLE,
                datasource: [],
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                }
            },
            // 用于检验cidr是否重叠
            checkCidrList: [],
            tableMemberLoading: false,
            tableSourceLoading: false,
            dialogDisplay: false,
            rules: validateRules(this),
            formData: {},
            isEdit: false,
            selectedItems: [],
            ingressDelRule: {},
            egressDelRule: {},
            confirmDisabled: false,
            memberPager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            sourcePager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            instance: {},
            direction: 'source',
            ingressTableDataSource: [],
            egressTableDataSource: []
        };
    }

    static computed = {
        dialogTitle() {
            if (this.data.get('direction') === 'source') {
                return this.data.get('isEdit') ? '编辑组播源' : '添加组播源';
            } else {
                return this.data.get('isEdit') ? '编辑组播成员' : '添加组播成员';
            }
        }
    };

    inited() {
        this.loadAllData();
        this.data.set('loadInstance', true);
        const vpcId = this.data.get('context').vpcId;
        this.$http
            .getVpcDetail({
                vpcIds: [vpcId]
            })
            .then(data => {
                const result = data[vpcId] || {};
                this.data.set('vpcCidr', result.cidr);
                this.data.set('vpcInfo', result);
            })
            .finally(e => this.data.set('loadInstance', false));
    }

    async attached() {
        this.loadSourcePage();
        this.loadMemberPage();
        let {deleteRules} = checker.check(delDules, []);
        this.data.set('ingressDelRule', deleteRules);
        this.data.set('egressDelRule', deleteRules);
    }

    loadVpcDetail() {
        const vpcId = this.data.get('context').vpcId;
        return this.$http
            .getVpcDetail({
                vpcIds: [vpcId]
            })
            .then(data => {
                const result = data[vpcId] || {};
                this.data.set('vpcInfo', result);
            });
    }

    tableSelected(e, direction) {
        this.data.set('selectedItems', e.value.selectedItems);
        let {deleteRules} = checker.check(delDules, e.value.selectedItems);
        if (direction === 'source') {
            this.data.set('ingressDelRule', deleteRules);
        } else {
            this.data.set('egressDelRule', deleteRules);
        }
    }

    async dialogConfirm() {
        let form = this.ref('rules-ref');
        form.validateFields().then(res => {
            let requester =
                this.data.get('direction') === 'source' ? 'multicastingSourceCreate' : 'multicastingMemberCreate';
            let config = {
                multicastGroupId: this.data.get('context').id,
                ipVersion: 4
            };
            if (this.data.get('direction') !== 'source') {
                config.memberIp = this.data.get('formData.ip');
                config.memberType = 'bcc';
            } else {
                config.sourceIp = this.data.get('formData.ip');
                config.sourceType = 'bcc';
            }
            let newPayload = {multicastSources: [config]};
            this.data.get('direction') !== 'source' && (newPayload = {multicastMembers: [config]});
            this.data.set('confirmDisabled', true);
            return this.$http[requester](newPayload)
                .then(result => {
                    this.close();
                    Message.success({
                        content: '添加成功'
                    });
                    this.loadAllData();
                    if (this.data.get('direction') === 'source') {
                        this.loadSourcePage();
                        return;
                    }
                    this.loadMemberPage();
                })
                .finally(e => this.data.set('confirmDisabled', false));
        });
    }

    onEdit(row, type) {
        let requester = type === 'source' ? 'multicastingSourceDelete' : 'multicastingMemberDelete';
        let confirm = new Confirm({
            data: {
                open: true,
                content: `确定要删除${type === 'source' ? '组播源' : '组播成员'}？`
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            let ruleIds = type === 'source' ? [row.multicastSourceId] : [row.multicastMemberLongId];
            let payload = type === 'source' ? {multicastSourceIds: ruleIds} : {multicastMemberIds: ruleIds};
            this.$http[requester](payload).then(() => {
                Notification.success('删除成功');
                this.loadAllData();
                if (type === 'source') {
                    this.loadSourcePage();
                    return;
                }
                this.loadMemberPage();
            });
        });
    }

    onDelete(type) {
        let requester = type === 'source' ? 'multicastingSourceDelete' : 'multicastingMemberDelete';
        let confirm = new Confirm({
            data: {
                open: true,
                content: `确定要删除${type === 'source' ? '组播源' : '组播成员'}？`
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            let ruleIds =
                type === 'source'
                    ? _.map(this.data.get('selectedItems'), item => item.multicastSourceId)
                    : _.map(this.data.get('selectedItems'), item => item.multicastMemberLongId);
            let payload = type === 'source' ? {multicastSourceIds: ruleIds} : {multicastMemberIds: ruleIds};
            this.$http[requester](payload).then(() => {
                Notification.success('删除成功');
                this.loadAllData();
                if (type === 'source') {
                    this.loadSourcePage();
                    return;
                }
                this.loadMemberPage();
            });
        });
    }

    resetTable() {
        this.data.set('ingressTable.selection.selectedIndex', []);
        this.data.set('egressTable.selection.selectedIndex', []);
    }

    close() {
        this.data.set('dialogDisplay', false);
        this.data.set('formData', {});
    }
    onMemberPagerChange(e) {
        this.data.set('memberPager.page', e.value.page);
        this.loadMemberPage();
    }
    onMemberSizeChange(e) {
        this.data.set('memberPager.page', 1);
        this.data.set('memberPager.pageSize', e.value.pageSize);
        this.loadMemberPage();
    }
    onSourcePagerChange(e) {
        this.data.set('sourcePager.page', e.value.page);
        this.loadSourcePage();
    }
    onSourceSizeChange(e) {
        this.data.set('sourcePager.page', 1);
        this.data.set('sourcePager.pageSize', e.value.pageSize);
        this.loadSourcePage();
    }
    loadSourcePage() {
        this.data.set('tableSourceLoading', true);
        let payload = this.getSourcePayload();
        this.resetTable();
        this.$http.multicastingSourceList(payload).then(res => {
            this.loadSourceQuota(res.totalCount);
            this.data.set('ingressTable.datasource', res.result);
            this.data.set('sourcePager.total', res.totalCount);
            this.data.set('tableSourceLoading', false);
        });
    }
    loadSourceQuota(totalCount = 0) {
        this.data.set('loadQuotaSourceDis', true);
        this.$http
            .getMultiCastingSourceQuota({multicastGroupId: this.data.get('context').multicastGroupId})
            .then(res => {
                this.data.set('loadQuotaSourceDis', false);
                let {createSource} = checker.check(rules, '', '', {createdIpv6Gateway: res.free});
                this.data.set('addSourceDisable', createSource);
            })
            .catch(e => {
                let {createSource} = checker.check(rules, '', '', {createdIpv6Gateway: 50 - totalCount});
                this.data.set('addSourceDisable', createSource);
                this.data.set('loadQuotaSourceDis', false);
            });
    }

    loadMemberPage() {
        this.data.set('tableMemberLoading', true);
        let payload = this.getMemberPayload();
        this.resetTable();
        this.$http.multicastingMemberList(payload).then(res => {
            this.loadMemberQuota(res.totalCount);
            this.data.set('egressTable.datasource', res.result);
            this.data.set('memberPager.total', res.totalCount);
            this.data.set('tableMemberLoading', false);
        });
    }
    loadMemberQuota(totalCount = 0) {
        this.data.set('loadQuotaMemberDis', true);
        this.$http
            .getMultiCastingMemberQuota({multicastGroupId: this.data.get('context').multicastGroupId})
            .then(res => {
                this.data.set('loadQuotaMemberDis', false);
                let {createMember} = checker.check(rules, '', '', {createdIpv6Gateway: res.free});
                this.data.set('addMemberDisable', createMember);
            })
            .catch(e => {
                let {createMember} = checker.check(rules, '', '', {createdIpv6Gateway: 50 - totalCount});
                this.data.set('addMemberDisable', createMember);
                this.data.set('loadQuotaMemberDis', false);
            });
    }

    getSourcePayload() {
        const {sourcePager} = this.data.get('');
        let payload = {
            pageNo: sourcePager.page,
            pageSize: sourcePager.pageSize,
            multicastGroupIds: [this.data.get('context').multicastGroupId]
        };

        return {...payload};
    }

    getMemberPayload() {
        const {memberPager} = this.data.get('');
        let payload = {
            pageNo: memberPager.page,
            pageSize: memberPager.pageSize,
            multicastGroupIds: [this.data.get('context').multicastGroupId]
        };
        return {...payload};
    }
    // 绑定服务
    onBindInstance(type) {
        this.data.set('dialogDisplay', true);
        this.data.set('direction', type);

        // let bind = new BindToInstance({
        //     data: {
        //         open: true,
        //         title: type === 'source' ? '绑定组播源' : '绑定组播成员',
        //         type,
        //         vpcShortId: this.data.get('instance').vpcShortId,
        //         multicastGroupId: this.data.get('instance').multicastGroupLongId,
        //     }
        // });
        // bind.attach(document.body);
        // bind.on('success', () => {
        //     Message.success({
        //         content: '添加成功'
        //     });
        //     if (type === 'source') {
        //         this.loadSourcePage();
        //         return;
        //     }
        //     this.loadMemberPage();
        // });
    }
    loadAllData() {
        this.data.set('allDataLoading', true);
        this.$http
            .getMultiCastingList({
                vpcUuid: this.data.get('context').vpcId,
                multicastGroupLongIds: [this.data.get('context').id]
            })
            .then(data => {
                this.data.set('ingressTableDataSource', data?.result[0]?.multicastSources);
                this.data.set('egressTableDataSource', data?.result[0]?.multicastMembers);
            })
            .finally(e => this.data.set('allDataLoading', false));
    }
}

export default San2React(Processor.autowireUnCheckCmpt(MulticastingIpList));
