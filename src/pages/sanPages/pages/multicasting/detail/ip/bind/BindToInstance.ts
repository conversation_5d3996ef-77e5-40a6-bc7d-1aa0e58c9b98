import _ from 'lodash';
import {Component} from 'san';
import {Tooltip, Icon, Select, Pagination, Table, Dialog, Button, Search} from '@baidu/sui';
import {Empty} from '@baidu/sui-biz';
import {html} from '@baiducloud/runtime';
import {OutlinedRefresh} from '@baidu/sui-icon';
import {serverInstanceStatus, Payment} from '@/pages/sanPages/common/enum';
import {utcToTime} from '@/pages/sanPages/utils/helper';
import './style.less';

const schemaColumn = [
    {
        name: 'id',
        label: '实例名称/ID',
        render(item) {
            let name = _.escape(item.name);
            if (!name) {
                return `<div>
                    <span class="instance-id" title="${item.instanceId}">${item.instanceId}</span>
                </div>`;
            }
            name = html`<a
                href="/bcc/#/bcc/instance/detail?instanceId=${item.id}&id=${item.instanceId}"
                title="${name}"
                class="instance-name"
            >
                ${name}
            </a>`;
            return `${name}
                <div>
                    <span class="instance-id" title="${item.instanceId}">${item.instanceId}</span>
                </div>`;
        }
    },
    {
        name: 'status',
        label: '服务器状态',
        render(item) {
            return `
                <span class="status ${serverInstanceStatus.fromValue(item.status).klass}">
                    ${serverInstanceStatus.getTextFromValue(item.status)}
                </span>
            `;
        }
    },
    {
        name: 'payment',
        label: '支付方式',
        render(item) {
            let expire = item.expireDate;
            if (item.payment === 'postpay') {
                expire = item.status === 'EXPIRED' ? '已欠费 ' : '';
                if (item.releaseTime) {
                    expire += '<span class="red">' + '预计于' + utcToTime(item.releaseTime) + '释放' + '</span>';
                } else if (item.createTime && item.status !== 'BUILD') {
                    expire += '创建于' + utcToTime(item.createTime);
                }
            } else {
                expire = expire === 0 ? '今天到期' : expire > 0 ? `${expire}天后到期` : `已过期${-expire}天`;
            }
            return Payment.getTextFromValue(item.payment) + '<br>' + expire;
        }
    },
    {
        name: 'internalIp',
        label: '内网IP地址'
    }
];

const tpl = html`
    <template>
        <s-dialog open="{=open=}" title="{{title}}" width="700" height="520" class="multicasting-dialog">
            <div class="multicasting-bind-to-instance">
                <div class="multicasting-bind-operation-wrap">
                    <div>
                        <label>实例类型：</label>
                        <s-select value="{=select.value=}" width="{{120}}" on-change="onChange">
                            <s-option
                                class="operation-select"
                                s-for="item in select.datasource"
                                value="{{item.value}}"
                                label="{{item.text}}"
                            >
                            </s-option>
                        </s-select>
                    </div>
                    <div>
                        <!--
                <s-search
                    width="150"
                    value="{=searchbox.keyword=}"
                    placeholder="{{searchbox.placeholder}}"
                    on-search="onSearch">
                    <s-select slot="options" width="100"
                        class="bind-select"
                        datasource="{{searchbox.keywordTypes}}"
                        value="{=searchbox.keywordType=}" on-change="onSearchBoxChange">
                    </s-select>
                </s-search>-->
                        <s-button on-click="refresh" class="s-icon-button"
                            ><outlined-refresh class="icon-class"
                        /></s-button>
                    </div>
                </div>
                <div class="multicasting-bind-table-wrap">
                    <s-table
                        columns="{{table.schema}}"
                        datasource="{{table.datasource}}"
                        loading="{{table.loading}}"
                        selection="{{table.select}}"
                        on-selected-change="onTableRowSelected($event)"
                    >
                        <div slot="empty">
                            <s-empty vertical>
                                <div slot="action"></div>
                            </s-empty>
                        </div>
                    </s-table>
                </div>
                <div s-if="pager.total" class="multicasting-bind-pager-wrap">
                    <s-pagination
                        s-if="pager.total"
                        layout="{{'total, pageSize, pager, go'}}"
                        pageSize="{{pager.size}}"
                        page="{{pager.page}}"
                        total="{{pager.total}}"
                        pageSizes="{{pageSizes}}"
                        on-pagerChange="onPagerChange($event)"
                        on-pagerSizeChange="onPagerSizeChange"
                    />
                </div>
            </div>
            <div slot="footer">
                <s-button on-click="close">取消</s-button>
                <s-button skin="primary" disabled="{{!selectedItems.length || disableSub}}" on-click="doSubmit">
                    确定
                </s-button>
            </div>
        </s-dialog>
    </template>
`;
export default class EipBind extends Component {
    static template = tpl;
    static components = {
        's-table': Table,
        's-pagination': Pagination,
        's-tooltip': Tooltip,
        's-icon': Icon,
        's-select': Select,
        's-option': Select.Option,
        's-button': Button,
        's-dialog': Dialog,
        's-search': Search,
        's-empty': Empty,
        'outlined-refresh': OutlinedRefresh
    };
    initData() {
        return {
            value: null,
            enableBindTo: false,
            open: false,
            disableSub: false,
            table: {
                loading: true,
                datasource: [],
                schema: schemaColumn,
                select: {
                    mode: 'multi',
                    selectedIndex: []
                }
            },
            pager: {
                size: 5,
                total: 0,
                page: 1
            },
            select: {
                value: 'BCC',
                datasource: [{value: 'BCC', text: '服务器'}]
            },
            pageSizes: [5, 10, 20, 50],
            searchbox: {
                placeholder: '请输入实例名称进行搜索',
                keyword: '',
                keywordType: 'name',
                keywordTypes: [
                    {text: '实例名称', value: 'name', placeholder: '请输入实例名称进行搜索'},
                    {text: '实例ID', value: 'instanceId', placeholder: '请输入实例ID进行搜索'}
                ]
            },
            title: '添加组播源',
            selectedItems: []
        };
    }
    inited() {
        this.loadData();
    }
    loadData() {
        this.data.set('table.select', {
            mode: 'multi',
            selectedIndex: []
        });
        this.data.set('table.datasource', []);
        this.data.set('table.loading', true);
        let {pager, searchbox} = this.data.get('');
        let payload = {
            filters: [
                {
                    keyword: this.data.get('vpcShortId'),
                    keywordType: 'fuzzySearch'
                },
                {
                    keyword: searchbox.keywordType,
                    keywordType: searchbox.keyword
                }
            ],
            order: 'desc',
            orderBy: 'createTime',
            needAlarmStatus: true,
            isAdvancedSearch: false,
            enableBid: true,
            serverType: 'BCC',
            pageNo: pager.page,
            pageSize: pager.size,
            keyword: this.data.get('vpcShortId'),
            keywordType: 'fuzzySearch'
        };
        this.$http
            .getBccList(payload)
            .then((res: any) => {
                this.data.set('table.datasource', res.result);
                this.data.set('pager.total', res.totalCount);
            })
            .finally(e => this.data.set('table.loading', false));
    }
    onSearchBoxChange({value}) {
        this.data.set('searchbox.keyword', '');
    }
    onSearch() {
        this.data.set('pager.page', 1);
        this.loadData();
    }
    onPagerChange(e: Event) {
        this.data.set('pager.page', e.value.page);
        this.loadData();
    }
    onPagerSizeChange(e: Event) {
        this.data.set('pager.page', 1);
        this.data.set('pager.size', e.value.pageSize);
        this.loadData();
    }
    onTableRowSelected(e: Event) {
        const selectedItems = e.value.selectedItems;
        this.data.set('selectedItems', selectedItems);
    }
    async onChange(e: Event) {
        this.data.set('pager.page', 1);
        this.data.set('pager.size', 5);
        this.data.set('pager.total', 0);
    }
    doSubmit() {
        let payload = [];
        this.data.get('selectedItems').forEach(item => {
            let config = {
                multicastGroupId: this.data.get('multicastGroupId'),
                ipVersion: 4
            };
            if (this.data.get('type') !== 'source') {
                config.memberIp = item.internalIp;
                config.memberType = 'bcc';
            } else {
                config.sourceIp = item.internalIp;
                config.sourceType = 'bcc';
            }
            payload.push(config);
        });
        let requester = this.data.get('type') === 'source' ? 'multicastingSourceCreate' : 'multicastingMemberCreate';
        let newPayload = {multicastSources: payload};
        this.data.get('type') !== 'source' && (newPayload = {multicastMembers: payload});
        this.data.set('disableSub', true);
        return this.$http[requester](newPayload)
            .then(result => {
                this.data.set('open', false);
                this.fire('success');
            })
            .finally(e => this.data.set('disableSub', false));
    }
    close() {
        this.data.set('open', false);
    }
    refresh() {
        this.data.set('pager.page', 1);
        this.loadData();
    }
}
