.multicasting-bind-to-instance {
    .multicasting-bind-operation-wrap {
        margin-bottom: 16px;
        display: flex;
        justify-content: space-between;
        .s-search {
            margin-left: 6px;
        }
    }
}
.multicasting-bind-table-wrap {
    .s-table-body {
        max-height: 310px;
        overflow: auto;
    }
}
.multicasting-bind-pager-wrap {
    .s-pagination-wrapper {
        margin-top: 10px;
        position: absolute;
        right: 24px;
        bottom: 64px;
    }
}
.bind-tip {
    margin-left: 8px;
    .s-popup-content-box {
        z-index: 999999 !important;
    }
}
.locale-en {
    .multicasting-bind-to-instance {
        .bind-select {
            .s-input-area {
                input {
                    width: 86px !important;
                }
            }
        }
    }
}

.multicasting-dialog {
    .s-dialog-content {
        padding-top: 12px !important;
    }
}
