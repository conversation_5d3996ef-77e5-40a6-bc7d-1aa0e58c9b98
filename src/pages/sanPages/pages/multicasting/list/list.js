import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {TagEditDialog} from '@baidu/bce-tag-sdk-san';
import {Notification} from '@baidu/sui';
import {OutlinedPlus, OutlinedEditingSquare, OutlinedDownload, OutlinedRefresh} from '@baidu/sui-icon';

import {Ipv6Payment, Ipv6Status, Ipv6SubProductType} from '@/pages/sanPages/common';
import {DocService} from '@/pages/sanPages/common';
import rules from '../rules';
import {columns} from './tableFields';
import Confirm from '../../../components/confirm';
import {alterProductType, toTime, $flag as FLAG} from '@/pages/sanPages/utils/helper';
import {ResourceGroupDialog} from '@baidu/bce-resourcegroup-sdk-san';
import {ResourceGroupSDK} from '@baidu/bce-resourcegroup-sdk';
import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import DOCUMENT_ICON from '@/img/document_normal.svg?url';
import DOCUMENT_ICON_COLOR from '@/img/document_color.svg?url';
import './style.less';

const {asPage, invokeAppComp, invokeSUI, invokeSUIBIZ, template, withSidebar, invokeComp} = decorators;
const tpl = html`
    <template>
        <s-app-list-page class="{{klass}}">
            <div slot="pageTitle">
                <div class="vpc-ipv6-header">
                    <div class="header-left">
                        <span class="title">组播网关</span>
                        <vpc-select class="vpc-select" on-change="vpcChange" on-int="vpcInt" />
                    </div>
                    <div class="header-right">
                        <a
                            href="javascript:void(0)"
                            class="help-file"
                            s-ref="introduce"
                            on-click="handleShowCard"
                            on-mouseenter="handleMouseEnter('introduce')"
                            on-mouseleave="handleMouseLeave('introduce')"
                            s-if="{{!flag.NetworkSupportXS}}"
                        >
                            <img class="s-icon" src="{{introduceIcon}}" />功能简介
                        </a>
                        <!--<s-icon name="warning-new" class="warning-class" />
                        <a
                            href="{{DocService.ipv6_helpFile}}"
                            target="_blank"
                            class="help-file"
                            s-if="{{!FLAG.NetworkSupportXS}}"
                        >
                            帮助文档
                        </a>-->
                    </div>
                </div>
                <introduce-panel
                    isShow="{{show}}"
                    klass="{{'IPv6-vpn-wrapper'}}"
                    title="{{introduceTitle}}"
                    description="{{description}}"
                    introduceOptions="{{introduceOptions}}"
                    on-toggle="handleToggle($event)"
                ></introduce-panel>
            </div>
            <div slot="bulk">
                <s-tip-button
                    disabled="{{loadQuotaDis || createIpv6.disable || iamPass.disable}}"
                    skin="primary"
                    isDisabledVisibile="{{true}}"
                    on-click="onCreate"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{createIpv6.message || iamPass.message | raw}}
                    </div>
                    <outlined-plus />
                    创建组播网关
                </s-tip-button>
                <s-tooltip class="left_class" trigger="{{release.disable ? 'hover' : ''}}" placement="top">
                    <!--bca-disable-next-line-->
                    <div slot="content">{{release.message | raw}}</div>
                    <s-button on-click="onRelease" disabled="{{release.disable}}"> 释放</s-button>
                </s-tooltip>
            </div>
            <div slot="filter" class="inline_class">
                <s-button on-click="onRefresh" class="s-icon-button"><outlined-refresh class="icon-class" /></s-button>
                <!--<s-button on-click="onDownload" class="s-icon-button" track-id="ti_vpc_security_download" track-name="下载"><outlined-download class="icon-class"/></s-button>-->
            </div>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                on-selected-change="tableSelected($event)"
                on-filter="onFilter"
                on-sort="onSort"
                selection="{=table.selection=}"
            >
                <div slot="empty">
                    <!--bca-disable-->
                    <s-empty
                        vertical
                        class="empty-wrapper {{iamPass.disable ? 'create-disable' : ''}}"
                        on-click="onCreate"
                    >
                        <p class="empty-desc common" slot="desc">{{"暂无组播网关。"}}</p>
                        <s-tooltip s-if="{{createIpv6.disable}}" slot="action">
                            <!--bca-disable-next-line-->
                            <div slot="content">{{createIpv6.message | raw}}</div>
                            <span class="common">立即创建</span>
                        </s-tooltip>
                        <s-button
                            s-else
                            class="empty-action common"
                            slot="action"
                            skin="stringfy"
                            slot="action"
                            on-click="onCreate"
                            >{{"立即创建"}}</s-button
                        >
                    </s-empty>
                    <!--bca-disable-->
                </div>
                <div slot="c-vpcId">
                    <span class="truncated" title="{{row.vpcName}}">
                        <a href="#/vpc/instance/detail?vpcId={{row.vpcId}}" class="text-hidden">{{row.vpcName}}</a>
                    </span>
                    <br />
                    <span class="truncated" title="{{row.vpcShortId}}">{{row.vpcShortId}}</span>
                </div>
                <div slot="c-createTime">{{row.createTime | getTime}}</div>
                <div slot="c-status">
                    <span class="{{row.status | statusClass}}">{{row.status | statusText}}</span>
                </div>
                <div slot="c-name" class="multicast-name-widget">
                    <span class="truncated">
                        <s-tooltip content="{{row.name}}">
                            <a
                                href="#/vpc/multicasting/detail?vpcId={{row.vpcId}}&id={{row.multicastGroupLongId}}&multicastGroupId={{row.multicastGroupId}}"
                                >{{row.name}}</a
                            >
                        </s-tooltip>
                    </span>
                    <s-popover
                        s-ref="popover-name-{{rowIndex}}"
                        placement="top"
                        trigger="click"
                        class="edit-popover-class"
                    >
                        <div class="edit-wrap" slot="content">
                            <s-input
                                value="{=edit.name.value=}"
                                width="320"
                                placeholder="请输入名称"
                                on-input="onEditInput($event, rowIndex, 'name')"
                            />
                            <div class="edit-tip">
                                大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-50
                            </div>
                            <s-button
                                skin="primary"
                                s-ref="editBtn-name-{{rowIndex}}"
                                disabled="{{true}}"
                                on-click="editConfirm(row, rowIndex, 'name')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel(rowIndex, 'name')">取消</s-button>
                        </div>
                        <outlined-editing-square class="name-icon" on-click="onInstantEdit(row, rowIndex, 'name')" />
                    </s-popover>
                    <br />
                    <span class="truncated">{{row.multicastGroupId}}</span>
                    <s-clip-board class="name-icon" text="{{row.multicastGroupId}}" />
                </div>
                <div slot="c-tag">
                    <span s-if="!row.tags || row.tags.length < 1"> - </span>
                    <div s-else s-for="item,index in row.tags">
                        <span s-if="index <= 1"> {{item.tagKey + ':' + item.tagValue}} </span>
                        <div s-if="row.tags.length > 2 && index === 1">...</div>
                    </div>
                </div>
                <div slot="c-productType">
                    {{row | productType}}
                    <s-popover s-if="row.enableProduct" placement="top">
                        <div slot="content">{{row | tipContent}}</div>
                        <s-icon class="tip-icon-wrap" name="warning-mark" />
                    </s-popover>
                </div>
                <div slot="c-resourceGroups">
                    <p s-for="item in row.resourceGroups">{{item.name}}</p>
                </div>
                <div slot="c-operation" class="operations">
                    <span class="oprations">
                        <s-tooltip trigger="{{row.notDisRelease ? 'hover' : ''}}" placement="top">
                            <div slot="content">请先删除实例的组播成员和组播源</div>
                            <s-button skin="stringfy" on-click="onRelease(row)" disabled="{{row.notDisRelease}}"
                                >释放</s-button
                            >
                        </s-tooltip>
                        <br />
                        <s-button skin="stringfy" on-click="toAlarmDetail(row)">管理组播组</s-button>
                    </span>
                </div>
                <div slot="c-ipSource">
                    <a
                        href="#/vpc/multicasting/ip?vpcId={{row.vpcId}}&id={{row.multicastGroupLongId}}&multicastGroupId={{row.multicastGroupId}}"
                        >{{row | getSourceLength}}</a
                    >
                </div>
                <div slot="c-ipGroup">
                    <a
                        href="#/vpc/multicasting/ip?vpcId={{row.vpcId}}&id={{row.multicastGroupLongId}}&multicastGroupId={{row.multicastGroupId}}"
                        >{{row | getGroupLength}}</a
                    >
                </div>
                <div slot="c-description">
                    <s-tooltip content="{{row.description || '-'}}" class="tooltip-width-class">
                        <span class="text-hidden">{{row.description || '-'}}</span>
                    </s-tooltip>
                    <s-popover
                        s-ref="popover-description-{{rowIndex}}"
                        placement="top"
                        trigger="click"
                        class="edit-popover-class"
                    >
                        <div class="edit-wrap" slot="content">
                            <s-input
                                value="{=edit.description.value=}"
                                width="160"
                                placeholder="请输入名称"
                                on-input="onEditInput($event, rowIndex, 'description')"
                            />
                            <div class="edit-tip">描述不能超过200个字符</div>
                            <s-button
                                skin="primary"
                                s-ref="editBtn-description-{{rowIndex}}"
                                disabled="{{true}}"
                                on-click="editConfirm(row, rowIndex, 'description')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel(rowIndex, 'description')">取消</s-button>
                        </div>
                        <outlined-editing-square
                            class="name-icon"
                            on-click="onInstantEdit(row, rowIndex, 'description')"
                        />
                    </s-popover>
                </div>
            </s-table>
            <s-pagination
                s-if="{{pager.total}}"
                slot="pager"
                layout="{{'total, pageSize, pager, go'}}"
                pageSize="{{pager.pageSize}}"
                total="{{pager.total}}"
                page="{{pager.page}}"
                on-pagerChange="onPagerChange"
                on-pagerSizeChange="onPagerSizeChange"
            />
        </s-app-list-page>
    </template>
`;

@template(tpl)
@invokeComp('@introduce-panel', '@vpc-select')
@invokeAppComp
@invokeSUI
@invokeSUIBIZ
class MulticastingList extends Component {
    static components = {
        'resource-group-dialog': ResourceGroupDialog,
        'outlined-plus': OutlinedPlus,
        'outlined-editing-square': OutlinedEditingSquare,
        'outlined-download': OutlinedDownload,
        'outlined-refresh': OutlinedRefresh
    };
    static filters = {
        statusClass(value) {
            return Ipv6Status.fromValue(value).styleClass || '';
        },
        statusText(value) {
            return value ? Ipv6Status.getTextFromValue(value) : '-';
        },
        productType(item) {
            return '后付费' + `-${Ipv6SubProductType.getTextFromValue(item.subProductType)}`;
        },
        tipContent(item) {
            let type = '';
            if (item.subProductType === Ipv6SubProductType.BANDWIDTH) {
                type = '后付费-按带宽转后付费-按流量';
            } else {
                type = '后付费-按流量转后付费-按带宽';
            }
            let tipContent =
                `该实例已开通计费变更${type}，将会在下个整点生效，` +
                '请关注！如需进行升级等操作，请先取消计费变更，谢谢！';
            return tipContent;
        },
        getTime(value) {
            return value ? toTime(value) : '-';
        },
        getSourceLength(value) {
            return value ? value.multicastSources?.length : 0;
        },
        getGroupLength(value) {
            return value ? value.multicastMembers?.length : 0;
        }
    };

    static computed = {
        operationDisabled() {
            const selectedItems = this.data.get('table.selectedItems');
            return selectedItems.length === 0;
        }
    };

    initData() {
        return {
            klass: 'multicasting-list-wrap',
            editTag: {},
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                columns,
                datasource: [],
                selectedItems: []
            },
            OperationType: [
                {label: '编辑标签', value: 'editTag'},
                {label: '计费变更', value: 'ALTER_PRODUCTTYPE'},
                {label: '取消计费变更', value: 'CANCEL_ALTER_PRODUCTTYPE'}
            ],
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            resourceSDK: new ResourceGroupSDK(window.$http, window.$context),
            iamPass: {},
            DocService,
            FLAG,
            show: true,
            introduceTitle: '组播网关',
            description: `支持创建多个组播组，在组播组内的多个组播成员可以接收到组播源发送的组播流量。可以通过点击"管理组播组"来配置和增加组播组的组播源和组播成员。`,
            introduceIcon: INTRODUCE_ICON,
            documentIcon: DOCUMENT_ICON,
            vpcId: ''
        };
    }

    inited() {
        let selectedItem = this.data.get('table.selectedItems');
        let {deleteRules} = checker.check(rules, selectedItem);
        this.data.set('release', deleteRules);
    }
    attached() {
        if (window.$storage.get('showMulticastIntroduce') === false) {
            this.data.set('show', false);
            this.ref('introduce').style.color = '#151b26';
        }
        this.data.set('introduceEle', this.ref('introduce'));
    }

    loadVpcList() {
        this.$http.ipv6gwVpcList().then(data => {
            let vpcs = u.map(data.ipv6GatewayVpcVoList, item => ({
                text: `${item.vpcName}（${item.cidr}）`,
                value: item.vpcId
            }));
            vpcs.unshift({
                text: '所在网络：全部私有网络',
                value: ''
            });
            this.data.set('vpcList', vpcs);
            this.data.set('vpcInfoList', data.ipv6GatewayVpcVoList);
            this.data.set('vpcId', vpcs[0].value);
            this.data.set('vpcInfo', data.ipv6GatewayVpcVoList[0]);
            this.loadPage();
        });
    }
    vpcInt() {
        this.loadPage();
    }

    vpcChange() {
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    checkIpv6Create(count) {
        let {createIpv6} = checker.check(rules, '', '', {createdIpv6Gateway: count});
        this.data.set('createIpv6', createIpv6);
    }

    getPayload() {
        const {pager, order, filters} = this.data.get('');
        let payload = {
            pageNo: pager.page,
            pageSize: pager.pageSize,
            vpcUuid: window.$storage.get('vpcId')
        };
        if (!payload.vpcUuid) {
            delete payload.vpcUuid;
        }
        return {...payload, ...order, ...filters};
    }

    loadPage() {
        this.data.set('table.loading', true);
        let payload = this.getPayload();
        this.resetTable();
        this.resetMoreOperation();
        this.$http.getMultiCastingList(payload).then(res => {
            this.loadQuota(res.totalCount);
            u.each(res.result, item => {
                item.notDisRelease = item.multicastMembers?.length || item.multicastSources?.length;
            });
            this.data.set('table.datasource', res.result);
            this.data.set('pager.total', res.totalCount);
            this.data.set('table.loading', false);
        });
    }
    loadQuota(totalCount) {
        this.data.set('loadQuotaDis', true);
        this.$http
            .getMultiCastingQuota()
            .then(res => {
                this.checkIpv6Create(res.free);
                this.data.set('loadQuotaDis', false);
            })
            .catch(e => {
                this.checkIpv6Create(10 - totalCount);
                this.data.set('loadQuotaDis', false);
            });
    }

    checkBindAble(data) {
        return u.map(data, item => {
            return {
                ...item,
                canNotUpgrade: !!item.orderStatus,
                enableProduct: this.checkEnableProduct(item)
            };
        });
    }

    checkEnableProduct(item) {
        return item.status === Ipv6Status.AVAILABLE && item.orderStatus === 'shift_charge';
    }

    tableSelected(e) {
        this.data.set('table.selectedItems', e.value.selectedItems);
        let {deleteRules} = checker.check(rules, e.value.selectedItems);
        this.data.set('release', deleteRules);
    }

    setOperationMessage() {
        let selectedItem = this.data.get('table.selectedItems');
        let checkResult = checker.check(rules, selectedItem, '');
        let OperationType = u.cloneDeep(this.data.get('OperationType'));
        OperationType.forEach(item => {
            if (checkResult[item.value]) {
                item.disabled = checkResult[item.value].disable;
                item.message = checkResult[item.value].message;
            }
        });
        this.data.set('OperationType', OperationType);
    }

    resetMoreOperation() {
        this.data.set('operation', '');
    }

    onOperationChange(e) {
        const methodMap = {
            editTag: this.editTag,
            ALTER_PRODUCTTYPE: this.alterProduct,
            CANCEL_ALTER_PRODUCTTYPE: this.cancelAlterProduct
        };
        let requester = methodMap[e.value].bind(this);
        requester();
    }

    editTag() {
        const instances = this.data.get('table.selectedItems');
        const actionOptions = {
            serviceType: 'IPVSIXGW',
            instances,
            options: () => {
                return this.$http
                    .getSearchTagList({
                        serviceType: ['IPVSIXGW'],
                        region: [window.$context.getCurrentRegionId()]
                    })
                    .then(result => {
                        return result;
                    });
            },
            parentAction: this,
            submitHandler: tags => {
                const param = {
                    insertTags: tags,
                    resources: u.map(instances, item => ({
                        id: item.gatewayId,
                        resourceId: item.gatewayId,
                        serviceType: 'IPVSIXGW',
                        tags: instances.length > 1 && item.tags ? tags.concat(item.tags) : tags
                    }))
                };
                return this.$http.vpcTagAssign(param).then(data => data);
            },
            helpDocUrl: DocService.vpcTag
        };
        const dialog = new TagEditDialog({
            data: actionOptions
        });
        dialog.attach(document.body);
        dialog.on('success', e => {
            this.loadPage();
            dialog.dispose && dialog.dispose();
        });
        dialog.on('cancel', e => {
            dialog.dispose && dialog.dispose();
            this.resetMoreOperation();
        });
        return dialog;
    }

    alterProduct() {
        // 目前计费变更不支持修改带宽，所以暂时不需要弹窗逻辑
        let datas = this.data.get('table.selectedItems');
        let ids = u.map(datas, item => item.gatewayId);
        let url = '/api/ipv6gw/order/confirm/to_postpay';
        let subProductType = '';
        if (datas[0].subProductType === Ipv6Payment.BANDWIDTH) {
            subProductType = Ipv6Payment.NETRAFFIC;
        } else {
            subProductType = Ipv6Payment.BANDWIDTH;
        }
        let param = {
            productType: 'postpay',
            subProductType: subProductType,
            data: datas,
            productTypeBefore: 'postpay',
            subProductTypeBefore: datas[0].subProductType
        };
        let type = 'TO_POSTPAY';
        sessionStorage.setItem('IPVSIXGW_ALTER', JSON.stringify(param));
        alterProductType('IPVSIXGW', ids, type, null, url);
    }

    cancelAlterProduct(e) {
        let selectedItems = this.data.get('table.selectedItems');
        let instanceIds = u.pluck(selectedItems, 'gatewayId');

        let confirm = new Confirm({
            data: {
                title: '取消计费变更',
                content: '确认取消计费变更？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http
                .ipv6gwCancelAlterProductType({
                    instanceIds,
                    serviceType: 'IPVSIXGW'
                })
                .then(() => {
                    Notification.success('取消计费变更成功');
                    this.loadPage();
                });
        });
    }

    resetTable() {
        this.data.set('table.selection', {
            mode: 'multi',
            selectedIndex: []
        });
        this.data.set('table.selectedItems', []);
    }

    // 点击修改icon
    onInstantEdit(row, rowIndex, type) {
        this.data.set(`edit.${type}.value`, row[`${type}`]);
        this.data.set(`edit.${type}.error`, false);
        const popWrap = this.ref(`popover-${type}-${rowIndex}`);
        popWrap.data.set('visible', !popWrap.data.get('visible'));
    }

    // 编辑弹框-输入名称/描述
    onEditInput(e, rowIndex, type) {
        let result =
            type === 'name'
                ? e.value === '' || !/^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\_\/\.]{0,49}$/.test(e.value)
                : e.value.length > 200;
        this.data.set(`edit.${type}.error`, result);
        this.data.set(`edit.${type}.value`, e.value);
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', result);
    }

    // 编辑弹框-提交
    editConfirm(row, rowIndex, type) {
        let edit = this.data.get(`edit.${type}`);
        if (edit.error) {
            return;
        }
        const key = type === 'name' ? 'description' : 'name';
        this.$http
            .multicastingUpdate({
                [type]: edit.value,
                multicastGroupId: row.multicastGroupLongId
            })
            .then(() => {
                this.editCancel(rowIndex, type);
                Notification.success('修改成功');
                this.loadPage();
            });
    }

    // 编辑弹框-取消
    editCancel(rowIndex, type) {
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', true);
        this.ref(`popover-${type}-${rowIndex}`).data.set('visible', false);
    }

    // 改变页数
    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }
    // 改变每页显示数量
    onPagerSizeChange(e) {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', e.value.pageSize);
        this.loadPage();
    }

    onSort(e) {
        this.data.set('order', e.value);
        this.loadPage();
    }

    // 筛选
    onFilter(e) {
        let name = e.field.name;
        let value = e[name] ? e[name] : '';
        this.data.set('filters.' + name, value);
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    onCreate() {
        let vpcId = this.data.get('vpcId') || '';
        location.hash = `#/vpc/multicasting/create?vpcId=${vpcId}`;
    }

    showMonitor(item) {
        let dialog = new Monitor({
            data: {
                ipv6Id: item.gatewayId
            }
        });
        dialog.attach(document.body);
    }

    onRelease(row) {
        let array = this.data.get('table.selectedItems').map(item => item.multicastGroupLongId);
        let confirm = new Confirm({
            data: {
                open: true,
                content: '确定要删除组播网关？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http
                .multicastingDelete({
                    multicastGroupIds: row.multicastGroupLongId ? [row.multicastGroupLongId] : array
                })
                .then(() => {
                    Notification.success('删除成功');
                    this.loadPage();
                })
                .catch(error => {
                    if (error) {
                        if (error.global) {
                            Notification.error(error.global, {duration: -1});
                        } else if (!error.field) {
                            Notification.error('未知错误', {duration: -1});
                        }
                    }
                });
        });
    }

    onUpgrade(row) {
        let url = `#/vpc/ipv6gw/upgrade?vpcId=${row.vpcId}&gatewayId=${row.gatewayId}&multicastGroupId=${multicastGroupId}`;
        location.hash = url;
    }

    toAlarmDetail(row) {
        location.hash = `#/vpc/multicasting/ip?id=${row.multicastGroupLongId}&vpcId=${row.vpcId}&multicastGroupId=${row.multicastGroupId}`;
    }

    onRegionChange() {
        location.reload();
    }
    // 编辑资源分组确定后
    oncommit() {
        this.data.set('showResource', false);
        this.loadPage();
    }
    // 编辑资源分组取消后
    onCancel() {
        this.data.set('showResource', false);
    }
    // 编辑资源分组
    changeResourceGroup(row) {
        let resource = {
            resourceId: row.gatewayId,
            serviceType: 'IPVSIXGW'
        };
        this.data.set('resource', resource);
        this.data.set('showResource', true);
    }
    // 下载
    onDownload() {
        let ids = [];
        ids = this.data.get('table.selectedItems').map(item => {
            return item.gatewayId;
        });
        let filter = 'ids=' + ids.join(',');
        // window.open(`/api/ipv6gw/download?` + filter);
    }
    /**
     * @description 介绍卡片变化时回调
     * @param {boolean} flag
     */
    handleToggle(flag) {
        this.data.set('show', flag);
        window.$storage.set('showMulticastIntroduce', false);
        this.data.set('introduceIcon', INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#151B26';
    }
    handleShowCard() {
        if (!this.data.get('show')) {
            this.data.set('show', true);
            window.$storage.set('showMulticastIntroduce', false);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type) {
        this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#2468F2';
    }
    handleMouseLeave(type) {
        const isShow = this.data.get('show');
        this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        !isShow && (ele.style.color = '#151B26');
    }
    onRefresh() {
        this.data.set('pager.page', 1);
        this.resetTable();
        this.loadPage();
    }
}

export default San2React(Processor.autowireUnCheckCmpt(MulticastingList));
