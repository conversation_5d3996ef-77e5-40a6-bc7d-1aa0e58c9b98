.multicasting-list-wrap {
    width: 100%;
    flex: 1;
    background: #f7f7f9 !important;
    .remote-text-wrap {
        display: flex;
    }
    .s-table {
        .s-table-row:hover {
            .name-icon {
                display: inline;
            }
        }
        .name-icon {
            display: none;
            font-size: 12px;
            fill: #2468f2;
        }
        .s-table-empty {
            border-bottom: none;
        }
        .empty-wrapper {
            display: flex;
            .common {
                font-family: PingFangSC-Regular;
                font-size: 12px;
                line-height: 20px;
                font-weight: 400;
                color: #84868c;
            }
            .empty-desc {
                color: #84868c;
            }
            .empty-action {
                color: #2468f2;
                cursor: pointer;
            }
        }
        .multicast-name-widget {
            white-space: nowrap;
        }
    }
    .icon-bind,
    .icon-unbind {
        font-size: 16px;
        color: #0786e9;
    }
    .header-button-wrap {
        margin-left: auto;
        display: flex;
        align-items: center;
        .link-wrap {
            margin-left: 10px;
        }
    }
    .text-hidden {
        display: inline-block;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        max-width: 120px;
        vertical-align: middle;
    }
    .button-shortcut {
        background-color: #f5f5f5;
        border-color: #ebebeb;
    }
    .vpn-tip {
        background: #fcf7f1;
        padding: 5px;
        margin-left: 10px;
        color: #f38900;
    }
    .title {
        font-size: 16px;
        margin-right: 10px;
    }
    .intro-warp {
        display: inline-block;
        margin-left: 8px;
        .placeholder-style {
            input::-webkit-input-placeholder {
                /*WebKit browsers*/
                color: #000;
            }
            input::-moz-input-placeholder {
                /*Mozilla Firefox*/
                color: #000;
            }

            input::-ms-input-placeholder {
                /*Internet Explorer*/
                color: #000;
            }
        }
    }
    .vpc-ipv6-header {
        display: flex;
        align-items: center;
        background-color: #ffffff;
        justify-content: space-between;
        .header-left {
            display: flex;
            align-items: center;
            .title {
                display: inline-block;
                margin: 0;
                color: #151b26;
                margin-right: 12px;
                height: 47px;
                line-height: 47px;
                font-weight: 500;
                font-size: 16px;
                margin-left: 16px;
            }
        }
        .header-right {
            display: flex;
            align-items: center;
            .help-file {
                margin-right: 16px;
                font-size: 12px;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                line-height: 20px;
                .s-icon {
                    position: relative;
                    top: -1px;
                    right: 4px;
                    margin-right: 0;
                    color: #2468f2;
                    font-size: 14px;
                }
            }
            .warning-class {
                position: relative;
                right: 4px;
                font-size: 14px;
                margin-right: 0;
                color: #2468f2;
            }
        }
    }

    .tip-icon-wrap {
        font-size: 14px;
        border: 1px solid #f18823;
        color: #f18823;
        &:hover {
            color: #fff;
            background-color: #f18823;
        }
    }
    .s-table {
        .s-table-body {
            max-height: calc(~'100vh - 334px');
        }
    }
    .operations {
        .s-button {
            padding: 0;
            margin-right: 16px;
        }
        .block_class {
            display: block;
        }
    }
}
.ipv6-edit-wrap {
    width: 200px;
}

.tooltip-width-class {
    .s-tooltip-body .s-tooltip-content {
        max-width: 300px;
    }
}
