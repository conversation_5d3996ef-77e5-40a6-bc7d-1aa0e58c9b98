/*
 * @description: 创建筛选条件
 * @file: network/flowmirror/filterRulegroup/create.js
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@baidu.com
 */
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import './create.less';
import {ContextService} from '@/pages/sanPages/common';
import FilterRuleList from './components/ruleList';

const {asPage, invokeSUI, invokeSUIBIZ, template, invokeComp} = decorators;

/* eslint-disable */
const tpl = html`
    <template>
        <s-app-create-page
            class="{{klass}}"
            pageTitle="{{pageNav.title}}"
            backTo="{{pageNav.backUrl}}"
            backToLabel="{{pageNav.backLabel}}"
        >
            <div class="content">
                <h4>{{'基础信息'}}</h4>
                <s-form
                    s-ref="form"
                    data="{=formData=}"
                    rules="{{rules}}"
                    wrapper-col="{{wrapperCol}}"
                    label-align="left"
                >
                    <s-form-item
                        prop="name"
                        label="{{'筛选条件名称：'}}"
                        help="{{'大小写字母、数字，中文以及-_/.特殊字符，必须以字母、中文开头，长度1-65'}}"
                    >
                        <s-input value="{=formData.name=}" placeholder="{{'请输入名称'}}"></s-input>
                    </s-form-item>
                    <s-form-item label="{{'描述：'}}">
                        <s-input-text-area
                            maxLength="200"
                            width="220"
                            height="60"
                            value="{=formData.description=}"
                            placeholder="{{'最多200个字符'}}"
                        ></s-input-text-area>
                    </s-form-item>
                </s-form>
            </div>
            <div class="content">
                <h4>{{'规则配置'}}</h4>
                <filter-rule-list s-ref="ruleList" totalQuota="{{totalQuota}}"> </filter-rule-list>
            </div>
            <div slot="pageFooter" class="buybucket">
                <div class="buybucket-container">
                    <s-tooltip placement="top" trigger="{{filterSinDisable.disable ? 'hover' : ''}}">
                        <div slot="content">{{filterSinDisable.message}}</div>
                        <s-button
                            size="large"
                            skin="primary"
                            disabled="{{filterSinDisable.disable || !formData.name}}"
                            on-click="onCreate"
                        >
                            {{'确定'}}
                        </s-button>
                    </s-tooltip>
                    <s-button size="large" on-click="cancel">{{'取消'}}</s-button>
                </div>
            </div>
        </s-app-create-page>
    </template>
`;
/* eslint-enable */
@template(tpl)
@invokeSUI
@invokeSUIBIZ
class VpcInstanceIndex extends Component {
    static components = {
        'filter-rule-list': FilterRuleList
    };

    initData() {
        return {
            klass: ['filter-rulegroup-create'],
            pageNav: {
                title: '创建筛选条件',
                backUrl: '/network/#/vpc/filterRuleGroup/list',
                backLabel: '返回'
            },
            labelCol: {span: 3},
            wrapperCol: {span: 19},
            formData: {
                name: '',
                description: ''
            },
            rules: {
                name: [
                    {required: true, message: '名称必填'},
                    {
                        pattern: /^[a-zA-Z\u4e00-\u9fa5][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/,
                        message: '大小写字母、数字，中文以及-_/.特殊字符，必须以字母、中文开头，长度1-65'
                    }
                ]
            },
            totalQuota: 10,
            filterSinDisable: {}
        };
    }

    inited() {
        const AllRegion = ContextService.getEnum('AllRegion');
        if (window.$context.getCurrentRegionId() === AllRegion.SIN) {
            this.data.set('filterSinDisable.disable', true);
            this.data.set('filterSinDisable.message', '新加坡地域资源售罄，请您切换到其他地域创建');
        }
        this.$http
            .getMirrorQuota({
                quotaTypes: 'ruleNumberPerGroupQuota'
            })
            .then(res => {
                this.data.set('totalQuota', res?.quotaType2quota?.ruleNumberPerGroupQuota || 10);
            })
            .catch(() => this.data.set('totalQuota', 10));
    }

    async onCreate() {
        await this.ref('form').validateFields();
        this.data.set('requesting', true);
        let rules = this.ref('ruleList').getRules();
        let formData = this.data.get('formData');
        let payload = {
            description: formData.description,
            name: formData.name,
            rules
        };
        this.$http.creatFilterRule(payload).then(() => {
            this.data.set('requesting', false);
            location.hash = '#/vpc/filterRuleGroup/list';
        });
    }
    cancel() {
        location.hash = '#/vpc/filterRuleGroup/list';
    }
    onRegionChange() {
        location.hash = '#/vpc/filterRuleGroup/list';
    }
}
export default San2React(Processor.autowireUnCheckCmpt(VpcInstanceIndex));
