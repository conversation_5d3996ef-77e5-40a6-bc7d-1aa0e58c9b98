.filter-rulegroup-create {
    min-height: 100%;
    width: 100%;
    .s-form {
        .s-form-item-label {
            width: 96px;
        }
    }
    .content {
        border-radius: 6px;
        width: calc(~'100vw - 32px');
        background: #fff;
        margin: 16px 16px 0;
        padding: 24px;
        h4 {
            display: block;
            color: #333;
            font-size: 16px;
            margin-bottom: 20px;
        }
    }
    .buybucket {
        width: 100%;
        left: 0;
        height: 60px;
        .buybucket-container {
            width: auto;
            float: left;
            height: 48px;
            transform: translateY(12.5%);
            display: flex;
            align-items: center;
        }
    }
    .s-create-page-footer {
        width: 100vw;
    }
}
