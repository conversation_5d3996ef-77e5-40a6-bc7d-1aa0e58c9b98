/*
 * @description: 添加acl规则
 * @file: acl/pages/create.js
 * @author: pian<PERSON><EMAIL>
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {OutlinedQuestion, OutlinedPlus} from '@baidu/sui-icon';
import {filterProtocolType, filterActionType} from '@/pages/sanPages/common/enum';
import {checkIpv6Cidr, convertCidrToBinary} from '@/pages/sanPages/utils/common';
import Rule from '@/pages/sanPages/utils/rule';

import './editRule.less';
function portRange(value) {
    if (value.toLowerCase() === 'all' || +value === 0) {
        return '';
    }
    var isPort = function (v) {
        return /(^[1-9][0-9]{0,4}$)/.test(v) && v <= 65535;
    };
    if (value.indexOf('-') === -1) {
        if (!isPort(value)) {
            return '端口范围不符合规则';
        }
        return '';
    }
    var array = value.split('-');
    if (!(isPort(array[0]) && isPort(array[1]) && +array[0] < +array[1] && (array[1] ? +array[0] !== 0 : false))) {
        return '端口范围不符合规则';
    }
    return '';
}
function remoteIp(value, ipType, ipVersion) {
    value = u.trim(value);
    var ipText = ipType ? (ipType === 'destination' ? '目的IP' : '源IP') : 'IP';
    if (value.toLowerCase() === 'all') {
        return false;
    }
    if (ipVersion === 'IPv6') {
        if (!checkIpv6Cidr(value)) {
            return ipText + '格式不正确';
        }
    } else {
        var valueString = convertCidrToBinary(value);
        var valueMask = value.split('/')[1] || 32;
        if (valueString.substring(+valueMask, valueString.length).indexOf('1') > -1) {
            return true;
        }
        var reg = new RegExp(Rule.IP_CIDR);
        if (!reg.test(value)) {
            return ipText + '格式不正确';
        }
    }
    return false;
}

const {asComponent, invokeSUI, invokeSUIBIZ, template} = decorators;
const tpl = html`
    <template>
        <s-dialog class="filter-rule-create" open="{=open=}" width="1120" title="{{title}}">
            <div class="ruleIpTypeLine">
                <span>规则类型：</span>
                <s-radio-radio-group
                    enhanced
                    class="ruletype"
                    datasource="{{ruleIpTypeList}}"
                    value="{=ruleIpType=}"
                    radioType="button"
                >
                </s-radio-radio-group>
            </div>
            <s-table
                s-if="ruleIpType === 'IPv4'"
                style="overflow: visible"
                columns="{{table.columns}}"
                maxHeight="{{270}}"
                datasource="{{table.datasource}}"
            >
                <div slot="empty">还未添加规则</div>
                <div slot="h-position">
                    {{col.label}}
                    <s-tooltip class="bind-tip" placement="right" content="{{tips.position}}">
                        <outlined-question />
                    </s-tooltip>
                </div>
                <div slot="h-sourceIpAddress">
                    {{col.label}}
                    <s-tooltip class="bind-tip" content="{{tips.sourceIpAddress}}">
                        <outlined-question />
                    </s-tooltip>
                </div>
                <div slot="h-sourcePort">
                    {{col.label}}
                    <s-tooltip class="bind-tip" content="{{tips.port}}">
                        <outlined-question />
                    </s-tooltip>
                </div>
                <div slot="h-destinationIpAddress">
                    {{col.label}}
                    <s-tooltip class="bind-tip" content="{{tips.destinationIpAddress}}">
                        <outlined-question />
                    </s-tooltip>
                </div>
                <div slot="h-destinationPort">
                    {{col.label}}
                    <s-tooltip class="bind-tip" content="{{tips.port}}">
                        <outlined-question />
                    </s-tooltip>
                </div>
                <div slot="c-position">
                    <s-input
                        width="60"
                        value="{=position[rowIndex]=}"
                        on-input="dataInput('position', rowIndex, $event)"
                    />
                    <p style="color: #EB5252" s-if="positionErr[rowIndex]">{{positionErr[rowIndex]}}</p>
                </div>
                <div slot="c-protocol">
                    <s-select
                        width="120"
                        on-change="dataChange('protocol', rowIndex, $event)"
                        datasource="{{protocolList}}"
                        value="{=protocol[rowIndex]=}"
                    >
                    </s-select>
                </div>
                <div slot="c-sourceIpAddress">
                    <s-input
                        width="100"
                        value="{=sourceIpAddress[rowIndex]=}"
                        on-input="dataInput('sourceIpAddress', rowIndex, $event)"
                    />
                    <p style="color: #EB5252" s-if="sourceIpAddressErr[rowIndex]">{{sourceIpAddressErr[rowIndex]}}</p>
                </div>
                <div slot="c-sourcePort">
                    <s-input
                        width="80"
                        disabled="{{sourcePortDisable[rowIndex]}}"
                        value="{=sourcePort[rowIndex]=}"
                        on-input="dataInput('sourcePort', rowIndex, $event)"
                        on-blur="dataBlur('sourcePort', rowIndex, $event)"
                    />
                    <p style="color: #EB5252" s-if="sourcePortErr[rowIndex]">{{sourcePortErr[rowIndex]}}</p>
                </div>
                <div slot="c-destinationIpAddress">
                    <s-input
                        width="100"
                        value="{=destinationIpAddress[rowIndex]=}"
                        on-input="dataInput('destinationIpAddress', rowIndex, $event)"
                    />
                    <p style="color: #EB5252" s-if="destinationIpAddressErr[rowIndex]">
                        {{destinationIpAddressErr[rowIndex]}}
                    </p>
                </div>
                <div slot="c-destinationPort">
                    <s-input
                        width="80"
                        disabled="{{destinationPortDisable[rowIndex]}}"
                        value="{=destinationPort[rowIndex]=}"
                        on-blur="dataBlur('destinationPort', rowIndex, $event)"
                        on-input="dataInput('destinationPort', rowIndex, $event)"
                    />
                    <p style="color: #EB5252" s-if="destinationPortErr[rowIndex]">{{destinationPortErr[rowIndex]}}</p>
                </div>
                <div slot="c-action">
                    <s-select
                        width="80"
                        on-change="dataChange('action', rowIndex, $event)"
                        datasource="{{actionList}}"
                        value="{=action[rowIndex]=}"
                    >
                    </s-select>
                </div>
                <div slot="c-description">
                    <s-input
                        width="80"
                        value="{=description[rowIndex]=}"
                        on-input="dataInput('description', rowIndex, $event)"
                    />
                    <p style="color: #EB5252" s-if="descriptionErr[rowIndex]">{{descriptionErr[rowIndex]}}</p>
                </div>
                <div slot="c-opt">
                    <a href="javascript:void(0)" on-click="deleteRule(rowIndex)">删除</a>
                </div>
            </s-table>
            <s-table
                s-if="ruleIpType === 'IPv6'"
                style="overflow: visible"
                maxHeight="{{270}}"
                columns="{{table.columns}}"
                datasource="{{table.ipv6Datasource}}"
            >
                <div slot="empty">还未添加规则</div>
                <div slot="h-position">
                    {{col.label}}
                    <s-tooltip class="bind-tip" placement="right" content="{{tips.position}}">
                        <outlined-question />
                    </s-tooltip>
                </div>
                <div slot="h-sourceIpAddress">
                    {{col.label}}
                    <s-tooltip class="bind-tip" content="{{tips.sourceIpAddressIpv6}}">
                        <outlined-question />
                    </s-tooltip>
                </div>
                <div slot="h-sourcePort">
                    {{col.label}}
                    <s-tooltip class="bind-tip" content="{{tips.port}}">
                        <outlined-question />
                    </s-tooltip>
                </div>
                <div slot="h-destinationIpAddress">
                    {{col.label}}
                    <s-tooltip class="bind-tip" content="{{tips.destinationIpAddressIpv6}}">
                        <outlined-question />
                    </s-tooltip>
                </div>
                <div slot="h-destinationPort">
                    {{col.label}}
                    <s-tooltip class="bind-tip" content="{{tips.port}}">
                        <outlined-question />
                    </s-tooltip>
                </div>
                <div slot="c-position">
                    <s-input
                        width="60"
                        value="{=positionIpv6[rowIndex]=}"
                        on-input="dataInput('position', rowIndex, $event)"
                    />
                    <p style="color: #EB5252" s-if="positionErrIpv6[rowIndex]">{{positionErrIpv6[rowIndex]}}</p>
                </div>
                <div slot="c-protocol">
                    <s-select
                        width="120"
                        on-change="dataChange('protocol', rowIndex, $event)"
                        datasource="{{protocolList}}"
                        value="{=protocolIpv6[rowIndex]=}"
                    >
                    </s-select>
                </div>
                <div slot="c-sourceIpAddress">
                    <s-input
                        width="100"
                        value="{=sourceIpAddressIpv6[rowIndex]=}"
                        on-input="dataInput('sourceIpAddress', rowIndex, $event)"
                    />
                    <p style="color: #EB5252" s-if="sourceIpAddressErrIpv6[rowIndex]">
                        {{sourceIpAddressErrIpv6[rowIndex]}}
                    </p>
                </div>
                <div slot="c-sourcePort">
                    <s-input
                        width="80"
                        disabled="{{sourcePortIpv6Disable[rowIndex]}}"
                        value="{=sourcePortIpv6[rowIndex]=}"
                        on-input="dataInput('sourcePort', rowIndex, $event)"
                        on-blur="dataBlur('sourcePort', rowIndex, $event)"
                    />
                    <p style="color: #EB5252" s-if="sourcePortErrIpv6[rowIndex]">{{sourcePortErrIpv6[rowIndex]}}</p>
                </div>
                <div slot="c-destinationIpAddress">
                    <s-input
                        width="100"
                        value="{=destinationIpAddressIpv6[rowIndex]=}"
                        on-input="dataInput('destinationIpAddress', rowIndex, $event)"
                    />
                    <p style="color: #EB5252" s-if="destinationIpAddressErrIpv6[rowIndex]">
                        {{destinationIpAddressErrIpv6[rowIndex]}}
                    </p>
                </div>
                <div slot="c-destinationPort">
                    <s-input
                        width="80"
                        disabled="{{destinationPortIpv6Disable[rowIndex]}}"
                        value="{=destinationPortIpv6[rowIndex]=}"
                        on-blur="dataBlur('destinationPort', rowIndex, $event)"
                        on-input="dataInput('destinationPort', rowIndex, $event)"
                    />
                    <p style="color: #EB5252" s-if="destinationPortErrIpv6[rowIndex]">
                        {{destinationPortErrIpv6[rowIndex]}}
                    </p>
                </div>
                <div slot="c-action">
                    <s-select
                        width="80"
                        on-change="dataChange('action', rowIndex, $event)"
                        datasource="{{actionList}}"
                        value="{=actionIpv6[rowIndex]=}"
                    >
                    </s-select>
                </div>
                <div slot="c-description">
                    <s-input
                        width="80"
                        value="{=descriptionIpv6[rowIndex]=}"
                        on-input="dataInput('description', rowIndex, $event)"
                    />
                    <p style="color: #EB5252" s-if="descriptionErrIpv6[rowIndex]">{{descriptionErrIpv6[rowIndex]}}</p>
                </div>
                <div slot="c-opt">
                    <a href="javascript:void(0)" on-click="deleteRule(rowIndex)">删除</a>
                </div>
            </s-table>
            <div style="padding:10px 0 0 10px">
                <s-tip-button
                    disabled="{{allItemsLength >= freeQuota}}"
                    skin="primary"
                    content="添加规则数量超过限制，如需提升配额，请提交工单"
                    isDisabledVisibile="{{true}}"
                    on-click="addRule"
                >
                    <outlined-plus />新增规则
                </s-tip-button>
            </div>
            <div slot="footer">
                <s-button on-click="close">取消</s-button>
                <s-tip-button
                    skin="primary"
                    isDisabledVisibile="true"
                    content="{{disableSub ? '规则格式校验失败' : '添加规则数量超过限制，如需提升配额，请提交工单'}}"
                    disabled="{{disableSub || allItemsLength >= freeQuota}}"
                    on-click="create"
                    >确定</s-tip-button
                >
            </div>
        </s-dialog>
    </template>
`;

@asComponent('@rule-create')
@template(tpl)
@invokeSUI
@invokeSUIBIZ
class ruleCreate extends Component {
    static components = {
        'outlined-question': OutlinedQuestion,
        'outlined-plus': OutlinedPlus
    };
    static computed = {
        disableSub() {
            let checkItems = [
                'position',
                'sourceIpAddress',
                'sourcePort',
                'destinationIpAddress',
                'destinationPort',
                'description'
            ]; //eslint-disable-line
            let disableSub = false;
            this.data.get('table.datasource').forEach((item, index) => {
                checkItems.forEach(checkItem => {
                    if (this.data.get(`${checkItem}Err[${index}]`)) {
                        disableSub = true;
                    }
                });
            });
            this.data.get('table.ipv6Datasource').forEach((item, index) => {
                checkItems.forEach(checkItem => {
                    if (this.data.get(`${checkItem}ErrIpv6[${index}]`)) {
                        disableSub = true;
                    }
                });
            });
            return disableSub;
        },
        allItemsLength() {
            let ipv4Length = this.data.get('table.datasource.length');
            let ipv6Length = this.data.get('table.ipv6Datasource.length');
            return ipv4Length + ipv6Length;
        }
    };

    initData() {
        return {
            title: '添加入站规则',
            tips: {
                position:
                    '取值范围1-1000，数值越小，优先级越高，规则匹配顺序按优先级从高到低匹配。若两条规则其他都相同只有策略不同，则拒绝生效，允许不生效',
                sourceIpAddress: '可填网段或IP，例如：**********/24，或**********，或all（0.0.0.0/0）',
                sourceIpAddressIpv6: '例如：1::/64，或1::1，或all（::/0）',
                port: '0-65535之间的整数，或区间，例如：8080，1-65535，或all（所有端口）',
                destinationIpAddress: '可填网段或IP，例如：**********/24，或**********，或all（0.0.0.0/0）',
                destinationIpAddressIpv6: '例如：1::/64，或1::1，或all（::/0）'
            },
            ruleIpTypeList: [
                {
                    text: 'IPv4',
                    value: 'IPv4'
                }
            ],
            ruleIpType: 'IPv4',
            protocolList: filterProtocolType.toArray(),
            actionList: filterActionType.toArray(),
            table: {
                columns: [
                    {
                        name: 'position',
                        label: '优先级'
                    },
                    {
                        name: 'protocol',
                        label: '协议',
                        width: 140
                    },
                    {
                        name: 'sourceIpAddress',
                        label: '源IP',
                        width: 140
                    },
                    {
                        name: 'sourcePort',
                        label: '源端口'
                    },
                    {
                        name: 'destinationIpAddress',
                        label: '目的IP',
                        width: 140
                    },
                    {
                        name: 'destinationPort',
                        label: '目的端口'
                    },
                    {
                        name: 'action',
                        label: '策略'
                    },
                    {
                        name: 'description',
                        label: '备注'
                    },
                    {
                        name: 'opt',
                        label: '操作'
                    }
                ],
                datasource: [],
                ipv6Datasource: []
            },
            position: [],
            positionIpv6: [],
            positionErr: [],
            positionErrIpv6: [],
            protocol: [filterProtocolType.toArray()[0].value],
            protocolIpv6: [filterProtocolType.toArray()[0].value],
            protocolErr: [],
            protocolErrIpv6: [],
            sourceIpAddress: [],
            sourceIpAddressIpv6: [],
            sourceIpAddressErr: [],
            sourceIpAddressErrIpv6: [],
            sourcePort: [],
            sourcePortIpv6: [],
            sourcePortErr: [],
            sourcePortErrIpv6: [],
            destinationIpAddress: [],
            destinationIpAddressIpv6: [],
            destinationIpAddressErr: [],
            destinationIpAddressErrIpv6: [],
            destinationPort: [],
            destinationPortIpv6: [],
            destinationPortErr: [],
            destinationPortErrIpv6: [],
            action: [filterActionType.toArray()[0].value],
            actionIpv6: [filterActionType.toArray()[0].value],
            actionErr: [],
            actionErrIpv6: [],
            description: [],
            descriptionIpv6: [],
            descriptionErr: [],
            descriptionErrIpv6: [],
            destinationPort: [],
            destinationPortIpv6: [],
            destinationPortErr: [],
            destinationPortErrIpv6: [],
            disableSub: false,
            isEdit: false
        };
    }
    addRule() {
        let ruleIpType = this.data.get('ruleIpType');
        this.data.push('sourcePort', 'all');
        this.data.push('sourcePortIpv6', 'all');
        if (ruleIpType === 'IPv4') {
            this.data.push('table.datasource', {});
            this.data.push('sourcePort', 'all');
            this.data.push('destinationPort', 'all');
            this.data.push('protocol', filterProtocolType.toArray()[0].value);
            this.data.push('action', filterActionType.toArray()[0].value);
            let index = this.data.get('table.datasource.length') - 1;
            this.data.set(`sourcePortDisable[${index}]`, true);
            this.data.set(`destinationPortDisable[${index}]`, true);
        } else {
            this.data.push('table.ipv6Datasource', {});
            this.data.push('sourcePortIpv6', 'all');
            this.data.push('destinationPortIpv6', 'all');
            this.data.push('protocolIpv6', filterProtocolType.toArray()[0].value);
            this.data.push('actionIpv6', filterActionType.toArray()[0].value);
            let index = this.data.get('table.ipv6Datasource.length') - 1;
            this.data.set(`sourcePortIpv6Disable[${index}]`, true);
            this.data.set(`destinationPortIpv6Disable[${index}]`, true);
        }
    }
    deleteRule(index) {
        let ruleIpType = this.data.get('ruleIpType');
        let editItem = [
            'position',
            'protocol',
            'sourceIpAddress',
            'sourcePort',
            'destinationIpAddress',
            'destinationPort',
            'action',
            'description'
        ]; //eslint-disable-line
        if (ruleIpType === 'IPv4') {
            this.data.splice('table.datasource', [index, 1]);
            editItem.forEach(item => {
                this.data.splice(`${item}`, [index, 1]);
                this.data.splice(`${item}Err`, [index, 1]);
            });
        } else {
            this.data.splice('table.ipv6Datasource', [index, 1]);
            editItem.forEach(item => {
                this.data.splice(`${item}Ipv6`, [index, 1]);
                this.data.splice(`${item}ErrIpv6`, [index, 1]);
            });
        }
    }
    dataBlur(type, index, e) {
        if (e.value === '0' || e.value === '0-65535' || e.value === '1-65535') {
            this.data.set(`${type}[${index}]`, 'all');
        }
    }
    dataInput(type, index, e) {
        let positions = [].concat(this.data.get('positions'), this.data.get('position'), this.data.get('positionIpv6'));
        let ruleIpType = this.data.get('ruleIpType');
        if (type === 'position') {
            let errType = ruleIpType === 'IPv4' ? 'positionErr' : 'positionErrIpv6';
            if (!e.value) {
                this.data.set(`${errType}[${index}]`, '请填写优先级');
            } else if (!/^[1-9][0-9]*$/.test(e.value)) {
                this.data.set(`${errType}[${index}]`, '请填写正整数');
            } else if (e.value > 1000) {
                this.data.set(`${errType}[${index}]`, '不能大于1000');
            } else {
                this.data.set(`${errType}[${index}]`, '');
            }
        }
        if (type === 'sourceIpAddress') {
            let errType = ruleIpType === 'IPv4' ? 'sourceIpAddressErr' : 'sourceIpAddressErrIpv6';
            let result = remoteIp(e.value, 'source', ruleIpType);

            if (!e.value) {
                this.data.set(`${errType}[${index}]`, 'IP范围必填');
            } else if (result) {
                if (typeof result === 'string') {
                    this.data.set(`${errType}[${index}]`, result);
                } else if (result === true) {
                    this.data.set(`${errType}[${index}]`, 'IP范围不符合规则');
                }
            } else {
                this.data.set(`${errType}[${index}]`, '');
            }
        }
        if (type === 'sourcePort') {
            let errType = ruleIpType === 'IPv4' ? 'sourcePortErr' : 'sourcePortErrIpv6';
            if (!e.value) {
                this.data.set(`${errType}[${index}]`, '请填写源端口');
            } else {
                this.data.set(`${errType}[${index}]`, portRange(e.value));
            }
        }
        if (type === 'destinationIpAddress') {
            let errType = ruleIpType === 'IPv4' ? 'destinationIpAddressErr' : 'destinationIpAddressErrIpv6';
            let result = remoteIp(e.value, 'destination', ruleIpType);
            if (!e.value) {
                this.data.set(`${errType}[${index}]`, 'IP范围必填');
            } else if (result) {
                if (typeof result === 'string') {
                    this.data.set(`${errType}[${index}]`, result);
                } else {
                    this.data.set(`${errType}[${index}]`, 'IP范围不符合规则');
                }
            } else {
                this.data.set(`${errType}[${index}]`, '');
            }
        }
        if (type === 'destinationPort') {
            let errType = ruleIpType === 'IPv4' ? 'destinationPortErr' : 'destinationPortErrIpv6';
            if (!e.value) {
                this.data.set(`${errType}[${index}]`, '请填写目的端口');
            } else {
                this.data.set(`${errType}[${index}]`, portRange(e.value));
            }
        }
        if (type === 'description') {
            let errType = ruleIpType === 'IPv4' ? 'descriptionErr' : 'descriptionErrIpv6';
            if (e.value && e.value.length > 200) {
                this.data.set(`${errType}[${index}]`, '最大长度200');
            } else {
                this.data.set(`${errType}[${index}]`, '');
            }
        }
        if (ruleIpType === 'IPv4') {
            this.data.set(`${type}[${index}]`, e.value);
        } else {
            this.data.set(`${type}Ipv6[${index}]`, e.value);
        }
    }
    dataChange(type, index, e) {
        let icmpText = 'N/A';
        let ruleIpType = this.data.get('ruleIpType');
        let isIpv4 = ruleIpType === 'IPv4';
        if (type === 'protocol') {
            let item = filterProtocolType.fromValue(e.value);
            let port = item.rules && item.rules.length > 0 ? item.rules[0].port : '';
            let protocol = item.rules && item.rules.length > 0 ? item.rules[0].protocol : '';
            let sourcePort = isIpv4 ? 'sourcePort' : 'sourcePortIpv6';
            let destinationPort = isIpv4 ? 'destinationPort' : 'destinationPortIpv6';
            let errType = isIpv4 ? 'sourcePortErr' : 'sourcePortErrIpv6';
            let destErrType = ruleIpType === 'IPv4' ? 'destinationPortErr' : 'destinationPortErrIpv6';

            if (protocol === filterProtocolType.ICMP || e.value === filterProtocolType.ALL) {
                this.data.set(`${sourcePort}[${index}]`, protocol === filterProtocolType.ICMP ? icmpText : port);
                this.data.set(`${destinationPort}[${index}]`, protocol === filterProtocolType.ICMP ? icmpText : port);
                this.data.set(`${sourcePort}Disable[${index}]`, true);
                this.data.set(`${destinationPort}Disable[${index}]`, true);
                this.data.set(`${errType}[${index}]`, '');
                this.data.set(`${destErrType}[${index}]`, '');
            } else {
                this.data.set(`${sourcePort}Disable[${index}]`, false);
                this.data.set(`${sourcePort}[${index}]`, filterProtocolType.fromValue('all').rules[0].port);
                this.data.set(`${destinationPort}Disable[${index}]`, false);
                this.data.set(`${destinationPort}[${index}]`, port);
            }
            if (isIpv4) {
                this.data.set(`protocol[${index}]`, protocol);
            } else {
                this.data.set(`protocolIpv6[${index}]`, protocol);
            }
        } else {
            if (isIpv4) {
                this.data.set(`${type}[${index}]`, e.value);
            } else {
                this.data.set(`${type}Ipv6[${index}]`, e.value);
            }
        }
    }
    check() {
        let checkItems = ['position', 'sourceIpAddress', 'sourcePort', 'destinationIpAddress', 'destinationPort'];
        let errTip = {
            position: '请填写优先级',
            sourceIpAddress: 'IP范围必填',
            sourcePort: '请填写源端口',
            destinationIpAddress: 'IP范围必填',
            destinationPort: '请填写目的端口'
        };
        let checkResult = true;
        this.data.get('table.datasource').forEach((item, index) => {
            checkItems.forEach(checkItem => {
                if (!this.data.get(`${checkItem}[${index}]`)) {
                    checkResult = false;
                    this.data.set(`${checkItem}Err[${index}]`, errTip[checkItem]);
                } else {
                    this.data.set(`${checkItem}Err[${index}]`, '');
                }
            });
        });
        this.data.get('table.ipv6Datasource').forEach((item, index) => {
            checkItems.forEach(checkItem => {
                if (!this.data.get(`${checkItem}Ipv6[${index}]`)) {
                    checkResult = false;
                    this.data.set(`${checkItem}ErrIpv6[${index}]`, errTip[checkItem]);
                } else {
                    this.data.set(`${checkItem}ErrIpv6[${index}]`, '');
                }
            });
        });
        return checkResult;
    }
    close() {
        this.data.set('open', false);
    }
    create() {
        if (!this.check()) {
            return;
        }
        let rules = [];
        this.data.get('table.datasource').forEach((item, index) => {
            let rule = {
                strategy: this.data.get(`action[${index}]`),
                description: this.data.get(`description[${index}]`) || '',
                destIp: this.data.get(`destinationIpAddress[${index}]`),
                destinationPort: this.data.get(`destinationPort[${index}]`),
                direction: this.data.get('ruleType'),
                ruleIpType: 'IPv4',
                ipVersion: 4,
                priority: this.data.get(`position[${index}]`),
                protocol: this.data.get(`protocol[${index}]`),
                srcIp: this.data.get(`sourceIpAddress[${index}]`),
                sourcePort: this.data.get(`sourcePort[${index}]`)
            };
            rules.push(rule);
        });
        this.data.get('table.ipv6Datasource').forEach((item, index) => {
            let rule = {
                ruleIpType: 'IPv6',
                strategy: this.data.get(`actionIpv6[${index}]`),
                description: this.data.get(`descriptionIpv6[${index}]`) || '',
                destIp: this.data.get(`destinationIpAddressIpv6[${index}]`),
                destinationPort: this.data.get(`destinationPortIpv6[${index}]`),
                direction: this.data.get('ruleType'),
                ipVersion: 6,
                priority: this.data.get(`positionIpv6[${index}]`),
                protocol: this.data.get(`protocolIpv6[${index}]`),
                srcIp: this.data.get(`sourceIpAddressIpv6[${index}]`),
                sourcePort: this.data.get(`sourcePortIpv6[${index}]`)
            };
            rules.push(rule);
        });
        this.fire('create', rules);
        this.close();
    }
}
export default Processor.autowireUnCheckCmpt(ruleCreate);
