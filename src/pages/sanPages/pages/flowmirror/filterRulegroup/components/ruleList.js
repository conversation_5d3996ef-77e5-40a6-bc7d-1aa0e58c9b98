/*
 * @description: 安全组规则列表
 * @file: network/security/pages/ruleList.js
 * @author: p<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {Message, Dialog} from '@baidu/sui';
import {OutlinedPlus} from '@baidu/sui-icon';
import ruleCreate from './editRule';
import {SecurityIpVersion as IpVersion, filterActionType, filterProtocolType} from '@/pages/sanPages/common/enum';
import {checkIpv6Cidr, convertCidrToBinary} from '@/pages/sanPages/utils/common';
import './ruleList.less';

const {asComponent, invokeSUI, template, invokeComp, invokeSUIBIZ} = decorators;

function portRange(value) {
    if (!value) {
        return '端口范围必填';
    }
    value = u.trim(value);
    if (value.toLowerCase() === 'all' || +value === 0) {
        return '';
    }
    var isPort = function (v) {
        return /(^[1-9][0-9]{0,4}$)/.test(v) && v <= 65535;
    };
    if (value.indexOf('-') === -1) {
        if (!isPort(value)) {
            return '端口范围不符合规则';
        }
        return '';
    }
    var array = value.split('-');
    if (!(isPort(array[0]) && isPort(array[1]) && +array[0] < +array[1] && (array[1] ? +array[0] !== 0 : false))) {
        return '端口范围不符合规则';
    }
    return '';
}

function remoteIp(value, ipType, ipVersion) {
    value = u.trim(value);
    var ipText = ipType ? (ipType === 'destination' ? '目的IP' : '源IP') : 'IP';
    if (value.toLowerCase() === 'all') {
        return false;
    }
    if (ipVersion === 'ipv6') {
        if (!checkIpv6Cidr(value)) {
            return ipText + '格式不正确';
        }
    } else {
        var valueString = convertCidrToBinary(value);
        var valueMask = value.split('/')[1] || 32;
        if (valueString.substring(+valueMask, valueString.length).indexOf('1') > -1) {
            return true;
        }
        var reg = new RegExp('^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}');
        if (!reg.test(value)) {
            return ipText + '格式不正确';
        }
    }
    return false;
}
/* eslint-disable */
const tpl = html`
    <div class="filter-rule-list">
        <div class="rule-list-button">
            <s-radio-radio-group
                enhanced
                datasource="{{ruleTypeList}}"
                on-change="ruleTypeChange($event)"
                value="{=ruleType=}"
                radioType="button"
            >
            </s-radio-radio-group>
            <s-tip-button
                disabled="{{rules.length>totalQuota || rules.length===totalQuota}}"
                skin="primary"
                content="添加规则数量超过限制，如需提升配额，请提交工单"
                isDisabledVisibile="{{true}}"
                on-click="onCreate"
            >
                <outlined-plus />添加规则
            </s-tip-button>
            <s-button on-click="deleteRule" disabled="{{selectedRule.length===0}}"> {{'删除'}} </s-button>
        </div>
        <s-select class="ruleiptype" width="100" datasource="{{ruleIpTypeList}}" value="{=ruleIpType=}" />
        <s-table
            s-ref="table"
            columns="{{table.columns}}"
            loading="{{table.loading}}"
            error="{{table.error}}"
            maxHeight="{{280}}"
            on-selected-change="tableSelected($event)"
            selection="{=table.selection=}"
            datasource="{{filterDatasource}}"
        >
            <div slot="error">
                {{'啊呀，出错了？'}}
                <a href="javascript:void(0);" on-click="refresh">{{'重新加载'}}</a>
            </div>
            <div slot="empty">{{'没有数据'}}</div>
            <div slot="c-priority">
                <s-input-number
                    min="{{1}}"
                    max="{{1000}}"
                    value="{=prioritys[rowIndex]=}"
                    s-if="editRow[rowIndex]"
                    width="60"
                    on-change="itemChange('prioritys',rowIndex,$event,row)"
                />
                <span s-else>{{row | priority}}</span>
                <p style="color: #EB5252" s-if="editRow[rowIndex] && prioritysErr[rowIndex]">
                    {{prioritysErr[rowIndex]}}
                </p>
            </div>
            <div slot="c-protocol">
                <s-select
                    s-if="editRow[rowIndex]"
                    width="70"
                    on-change="itemChange('protocols',rowIndex,$event)"
                    datasource="{{protocolList}}"
                    value="{=protocols[rowIndex]=}"
                />
                <span s-else>{{row | protocol}}</span>
            </div>
            <div slot="c-srcIp">
                <s-input
                    s-if="editRow[rowIndex]"
                    width="80"
                    value="{=srcIps[rowIndex]=}"
                    on-input="itemChange('srcIps', rowIndex, $event, row)"
                ></s-input>
                <span s-else>{{row.srcIp}}</span>
                <p style="color: #EB5252" s-if="editRow[rowIndex] && srcIpsErr[rowIndex]">{{srcIpsErr[rowIndex]}}</p>
            </div>
            <div slot="c-sourcePort">
                <s-input
                    s-if="editRow[rowIndex]"
                    width="80"
                    disabled="{{portRangeDisable[rowIndex]}}"
                    value="{=sourcePorts[rowIndex]=}"
                    on-input="itemChange('sourcePorts', rowIndex, $event)"
                    on-blur="dataBlur('sourcePorts', rowIndex, $event)"
                />
                <span s-else>{{row | sourcePort}}</span>
                <p style="color: #EB5252" s-if="editRow[rowIndex] && sourcePortsErr[rowIndex]">
                    {{sourcePortsErr[rowIndex]}}
                </p>
            </div>
            <div slot="c-destIp">
                <s-input
                    s-if="editRow[rowIndex]"
                    width="80"
                    value="{=destIps[rowIndex]=}"
                    on-input="itemChange('destIps', rowIndex, $event, row)"
                />
                <span s-else>{{row.destIp}}</span>
                <p style="color: #EB5252" s-if="editRow[rowIndex] && destIpsErr[rowIndex]">{{destIpsErr[rowIndex]}}</p>
            </div>
            <div slot="c-destinationPort">
                <s-input
                    s-if="editRow[rowIndex]"
                    width="80"
                    disabled="{{portRangeDisable[rowIndex]}}"
                    value="{=destinationPorts[rowIndex]=}"
                    on-input="itemChange('destinationPorts', rowIndex, $event)"
                    on-blur="dataBlur('destinationPorts', rowIndex, $event)"
                />
                <span s-else>{{row | destinationPort}}</span>
                <p style="color: #EB5252" s-if="editRow[rowIndex] && destinationPortErr[rowIndex]">
                    {{destinationPortErr[rowIndex]}}
                </p>
            </div>
            <div slot="c-strategy">
                <s-select
                    width="{{80}}"
                    datasource="{{actionList}}"
                    on-change="itemChange('strategys',rowIndex,$event)"
                    s-if="editRow[rowIndex]"
                    value="{=strategys[rowIndex]=}"
                />
                <span s-else>{{row | strategy}}</span>
            </div>
            <div slot="c-description">
                <s-input
                    width="80"
                    on-input="itemChange('descriptions',rowIndex,$event)"
                    s-if="editRow[rowIndex]"
                    value="{=descriptions[rowIndex]=}"
                >
                </s-input>
                <span s-else>{{row | remark}}</span>
            </div>
            <div slot="c-opt">
                <div>
                    <div>
                        <a s-if="!editRow[rowIndex]" href="javascript:void(0)" on-click="edit(row,rowIndex)"
                            >{{'编辑'}}</a
                        >
                    </div>
                    <a
                        disabled="{{portRangeErr[rowIndex]||remoteIPErr[rowIndex]}}"
                        href="javascript:void(0)"
                        s-if="editRow[rowIndex]"
                        on-click="editConfirm(rowIndex, row)"
                        >{{'确定'}}
                    </a>
                    <a href="javascript:void(0)" s-if="editRow[rowIndex]" on-click="editCancel(row, rowIndex)"
                        >{{'取消'}}</a
                    >
                </div>
            </div>
        </s-table>
    </div>
`;
/* eslint-enable */

@invokeComp('@rule-create')
@template(tpl)
@invokeSUI
@invokeSUIBIZ
class RuleList extends Component {
    static components = {
        'outlined-plus': OutlinedPlus
    };
    static computed = {
        datasource() {
            let rules = this.data.get('rules');
            return rules.filter(item => item.direction === this.data.get('ruleType'));
        },
        filterDatasource() {
            let datasource = this.data.get('datasource');
            if (this.data.get('ruleIpType') === 'all') {
                return datasource;
            } else if (this.data.get('ruleIpType') === 4) {
                return datasource.filter(item => item.ruleIpType.toLowerCase() === 'ipv4');
            } else {
                return datasource.filter(item => item.ruleIpType.toLowerCase() === 'ipv6');
            }
        }
    };
    static filters = {
        priority(row) {
            if (!row.priority) {
                return u.escape(row.priority);
            }
            return row.priority;
        },
        protocol(row) {
            if (row.protocol === 'all') {
                return '全部协议';
            }
            return u.escape(row.protocol);
        },
        strategy(row) {
            return row.strategy === 'allow' ? '采集' : '不采集';
        },
        remark(row) {
            return row.description || '-';
        },
        sourcePort(item) {
            if (!this.data.get('isDetail')) {
                return item.sourcePort;
            }
            if (item.protocol === 'icmp') {
                return 'N/A';
            } else {
                let portRang = item.srcPortRange;
                return portRang.all
                    ? 'all'
                    : portRang.begin === portRang.end
                      ? portRang.begin + ''
                      : portRang.begin + '-' + portRang.end;
            }
        },
        destinationPort(item) {
            if (!this.data.get('isDetail')) {
                return item.destinationPort;
            }
            if (item.protocol === 'icmp') {
                return 'N/A';
            } else {
                let portRang = item.destPortRange;
                return portRang.all
                    ? 'all'
                    : portRang.begin === portRang.end
                      ? portRang.begin + ''
                      : portRang.begin + '-' + portRang.end;
            }
        }
    };
    initData() {
        return {
            totalQuota: 10,
            ruleTypeList: [
                {
                    text: '入站',
                    value: 'in'
                },
                {
                    text: '出站',
                    value: 'out'
                }
            ],
            ruleType: 'in',
            selectedRule: [],
            ruleIpTypeList: [
                {
                    text: '全部规则',
                    value: 'all'
                },
                {
                    text: 'IPv4',
                    value: 4
                }
            ],
            ruleIpType: 'all',
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                columns: [
                    {
                        name: 'priority',
                        label: '优先级',
                        width: 90
                    },
                    {
                        name: 'protocol',
                        label: '协议',
                        width: 90
                    },
                    {
                        name: 'srcIp',
                        label: '源IP',
                        width: 90
                    },
                    {
                        name: 'sourcePort',
                        label: '源端口',
                        width: 90
                    },
                    {
                        name: 'destIp',
                        label: '目的IP',
                        width: 90
                    },
                    {
                        name: 'destinationPort',
                        label: '目的端口',
                        width: 90
                    },
                    {
                        name: 'strategy',
                        label: '策略',
                        width: 90
                    },
                    {
                        name: 'description',
                        label: '备注',
                        width: 90
                    },
                    {
                        name: 'opt',
                        label: '操作',
                        width: 90
                    }
                ]
            },
            rules: [],
            protocolList: [
                {text: '全部协议', value: 'all'},
                {text: 'tcp', value: 'tcp'},
                {text: 'udp', value: 'udp'},
                {text: 'icmp', value: 'icmp'}
            ],
            prioritys: [],
            protocols: [],
            srcIps: [],
            srcIpsErr: [],
            sourcePorts: [],
            sourcePortsErr: [],
            destIps: [],
            destIpsErr: [],
            destinationPorts: [],
            destinationPortErr: [],
            strategys: [],
            remarks: [],
            actionList: filterActionType.toArray(),
            isDetail: false
        };
    }
    inited() {
        this.watch('datasource', datas => {
            this.reset();
            this.initEditDatasource(datas);
        });
        let type = this.data.get('type');
        type && this.data.set('ruleType', type);
        this.initEditDatasource(this.data.get('datasource'));
    }

    initEditDatasource(datas) {
        let editItems = [
            'priority',
            'protocol',
            'srcIp',
            'sourcePort',
            'destIp',
            'destinationPort',
            'strategy',
            'remark',
            'description'
        ];
        datas.forEach((item, index) => {
            this.data.set(`portRangeDisable[${index}]`, false);
            editItems.forEach(editItem => {
                if (editItem === 'protocol' && (item[editItem] === 'icmp' || item[editItem] === 'all')) {
                    this.data.set(`portRangeDisable[${index}]`, true);
                }
                if (editItem === 'sourcePort' || editItem === 'destinationPort') {
                    this.data.set(`${editItem}s[${index}]`, this.dealPort(editItem, item));
                } else {
                    this.data.set(`${editItem}s[${index}]`, item[editItem]);
                }
            });
        });
    }

    dealPort(type, item) {
        if (!this.data.get('isDetail')) {
            return item[type];
        }
        let portRang = type === 'sourcePort' ? item.srcPortRange : item.destPortRange;
        if (item.protocol === 'icmp') {
            return 'N/A';
        }
        return portRang.all
            ? 'all'
            : portRang.begin === portRang.end
              ? portRang.begin + ''
              : portRang.begin + '-' + portRang.end;
    }

    ruleTypeChange(e) {
        this.data.set('ruleType', e.value);
        this.resetTable();
    }

    tableSelected(e) {
        this.data.set('selectedRule', e.value.selectedItems);
    }

    deleteRule() {
        let selectItems = this.ref('table').getSelectedItems();
        if (this.data.get('isDetail')) {
            let ruleIds = selectItems.map(ele => ele.ruleId);
            Dialog.warning({
                title: '删除提示',
                content: '确认要删除该规则？',
                onOk: () => {
                    this.$http.deleteGroupRule(this.data.get('instance.ruleGroupId'), {ruleIds}).then(() => {
                        Message.success('删除成功');
                        this.upDataDetail();
                    });
                }
            });
            return;
        }
        let rules = this.data.get('rules');
        let newRules = rules.filter(item => {
            return (
                selectItems.findIndex(ele => {
                    return item.priority === ele.priority && ele.direction === this.data.get('ruleType');
                }) === -1
            );
        });
        this.data.set('rules', newRules);
        this.resetTable();
    }

    editConfirm(index, row) {
        let errList = ['priority', 'srcIp', 'destIp', 'sourcePort', 'destinationPort'];
        if (
            errList.findIndex(item => {
                return !!this.data.get(`${item}Err[${index}]`);
            }) !== -1
        ) {
            return;
        }
        let editItems = [
            'priority',
            'protocol',
            'srcIp',
            'sourcePort',
            'destIp',
            'destinationPort',
            'strategy',
            'remark',
            'description'
        ];

        editItems.forEach(editItem => {
            row[editItem] = this.data.get(`${editItem}s[${index}]`);
        });
        if (this.data.get('isDetail')) {
            let rules = this.getRules([row]);
            this.$http.changeGroupRule(this.data.get('instance.ruleGroupId'), {rules}).then(() => {
                Message.success('编辑成功');
                this.upDataDetail();
            });
        }
        this.data.set(`editRow[${index}]`, false);
    }

    dataBlur(type, index, e) {
        if (e.value === '0' || e.value === '0-65535' || e.value === '1-65535') {
            this.data.set(`${type}[${index}]`, 'all');
        }
    }

    editCancel(row, index) {
        let editItems = [
            'priority',
            'protocol',
            'srcIp',
            'sourcePort',
            'destIp',
            'destinationPort',
            'strategy',
            'remark',
            'description'
        ];
        editItems.forEach(editItem => {
            if (editItem === 'protocol' && row[editItem] !== 'icmp' && row[editItem] !== 'all') {
                this.data.set(`portRangeDisable[${index}]`, false);
            }
            if (editItem === 'sourcePort' || editItem === 'destinationPort') {
                this.data.set(`${editItem}s[${index}]`, this.dealPort(editItem, row));
            } else {
                this.data.set(`${editItem}s[${index}]`, row[editItem]);
            }
        });
        this.data.set(`editRow[${index}]`, false);
    }

    itemChange(type, index, e, row) {
        let positions = this.data.get('datasource').map(item => item.priority);
        if (type === 'protocols') {
            if (e.value === 'all') {
                this.data.set(`sourcePorts[${index}]`, 'all');
                this.data.set(`destinationPorts[${index}]`, 'all');
                this.data.set(`portRangeDisable[${index}]`, true);
            } else if (e.value === 'icmp') {
                this.data.set(`sourcePorts[${index}]`, 'N/A');
                this.data.set(`destinationPorts[${index}]`, 'N/A');
                this.data.set(`portRangeDisable[${index}]`, true);
            } else {
                this.data.set(`portRangeDisable[${index}]`, false);
                this.data.set(`sourcePorts[${index}]`, '');
                this.data.set(`destinationPorts[${index}]`, '');
            }
        }
        if (type === 'prioritys') {
            if (!e.value) {
                this.data.set(`prioritysErr[${index}]`, '请填写优先级');
            } else if (!/^[1-9][0-9]*$/.test(e.value)) {
                this.data.set(`prioritysErr[${index}]`, '请填写正整数');
            } else if (e.value > 1000) {
                this.data.set(`prioritysErr[${index}]`, '不能大于1000');
            } else {
                this.data.set(`prioritysErr[${index}]`, '');
            }
        }
        if (type === 'srcIps') {
            let result = remoteIp(e.value, 'src', row.ruleIpType);
            if (!e.value) {
                this.data.set(`srcIpsErr[${index}]`, 'IP范围必填');
            } else if (result) {
                if (typeof result === 'string') {
                    this.data.set(`srcIpsErr[${index}]`, result);
                } else if (result === true) {
                    this.data.set(`srcIpsErr[${index}]`, 'IP范围不符合规则');
                }
            } else {
                this.data.set(`srcIpsErr[${index}]`, '');
            }
        }
        if (type === 'sourcePorts') {
            if (!e.value) {
                this.data.set(`sourcePortsErr[${index}]`, '请填写源端口');
            } else {
                this.data.set(`sourcePortsErr[${index}]`, portRange(e.value));
            }
        }
        if (type === 'destIps') {
            let result = remoteIp(e.value, 'destination', row.ruleIpType);
            if (!e.value) {
                this.data.set(`destIpsErr[${index}]`, 'IP范围必填');
            } else if (result) {
                if (typeof result === 'string') {
                    this.data.set(`destIpsErr[${index}]`, result);
                } else {
                    this.data.set(`destIpsErr[${index}]`, 'IP范围不符合规则');
                }
            } else {
                this.data.set(`destIpsErr[${index}]`, '');
            }
        }
        if (type === 'destinationPorts') {
            if (!e.value) {
                this.data.set(`destinationPortsErr[${index}]`, '请填写目的端口');
            } else {
                this.data.set(`destinationPortsErr[${index}]`, portRange(e.value));
            }
        }
        if (type === 'descriptions') {
            if (e.value && e.value.length > 200) {
                this.data.set(`descriptionsErr[${index}]`, '最大长度200');
            } else {
                this.data.set(`descriptionsErr[${index}]`, '');
            }
        }
        this.data.set(`${type}[${index}]`, e.value);
    }

    resetTable() {
        this.data.set('table.selection.selectedIndex', []);
        this.data.set('selectedRule', []);
    }

    edit(row, rowIndex) {
        this.data.set(`editRow[${rowIndex}]`, true);
    }
    // 创建规则
    onCreate() {
        let create = new ruleCreate({
            data: {
                open: true,
                ruleType: this.data.get('ruleType'),
                title: this.data.get('ruleType') === 'in' ? '添加入站规则' : '添加出站规则',
                positions: this.data.get('datasource').map(item => item.priority),
                freeQuota: this.data.get('totalQuota') - this.data.get('rules.length')
            }
        });
        create.on('create', newRules => {
            if (this.data.get('isDetail')) {
                this.creatRule(newRules);
                return;
            }
            let rules = this.data.get('rules');
            this.data.set('rules', rules.concat(newRules));
        });
        create.attach(document.body);
    }

    creatRule(newRules) {
        let rules = this.getRules(newRules);
        this.$http.putGroupRule(this.data.get('instance.ruleGroupId'), {rules}).then(() => {
            Message.success('添加成功');
            this.upDataDetail();
        });
    }

    getRules(rules) {
        let result = u.cloneDeep(rules ? rules : this.data.get('rules'));
        result.forEach(item => {
            item.srcPortRange = {all: true};
            item.destPortRange = {all: true};
            if (item.protocol !== filterProtocolType.ICMP) {
                if (item.sourcePort.indexOf('-') !== -1) {
                    var array = item.sourcePort.split('-');
                    item.srcPortRange = {
                        begin: array[0],
                        end: array[1],
                        all: false
                    };
                } else if (item.sourcePort !== 'all') {
                    item.srcPortRange = {
                        begin: item.sourcePort,
                        end: item.sourcePort,
                        all: false
                    };
                }
                if (item.destinationPort.indexOf('-') !== -1) {
                    var array = item.destinationPort.split('-');
                    item.destPortRange = {
                        begin: array[0],
                        end: array[1],
                        all: false
                    };
                } else if (item.destinationPort !== 'all') {
                    item.destPortRange = {
                        begin: item.destinationPort,
                        end: item.destinationPort,
                        all: false
                    };
                }
            }
            item.srcIp === 'all' && (item.srcIp = item.ruleIpType === 'IPv4' ? '0.0.0.0/0' : '::/0');
            item.destIp === 'all' && (item.destIp = item.ruleIpType === 'IPv4' ? '0.0.0.0/0' : '::/0');
            item.ruleType = item.ruleIpType;
            item.strategy = item.strategy.toUpperCase();
            delete item.ruleIpType;
            delete item.sourcePort;
            delete item.destinationPort;
        });
        return result;
    }

    reset() {
        this.data.set('prioritys', []);
        this.data.set('protocols', []);
        this.data.set('srcIps', []);
        this.data.set('sourcePorts', []);
        this.data.set('destIps', []);
        this.data.set('destinationPorts', []);
        this.data.set('strategys', []);
        this.data.set('remarks', []);
        this.data.set('descriptions', []);
    }

    upDataDetail() {
        this.dispatch('upDataRule');
    }
}
export default Processor.autowireUnCheckCmpt(RuleList);
