export const SCHEMA = [
    {
        name: 'name',
        label: '实例名称/ID',
        minWidth: 160
    },
    {
        name: 'ingressRules',
        label: '入方向规则',
        minWidth: 120,
        render(item) {
            return `<a href="#/vpc/filterRuleGroup/detail?id=${item.ruleGroupId}&type=in">
                    ${item?.ingressRules?.length || 0}</a>`;
        }
    },
    {
        name: 'egressRules',
        label: '出方向规则',
        minWidth: 120,
        render(item) {
            return `<a href="#/vpc/filterRuleGroup/detail?id=${item.ruleGroupId}&type=out">
                    ${item?.egressRules?.length || 0}</a>`;
        }
    },
    {
        name: 'mirrorSessions',
        label: '已关联镜像会话',
        minWidth: 120,
        render(item) {
            return `<a href="#/vpc/filterRuleGroup/detail/mirror?id=${item.ruleGroupId}">
                    ${item?.mirrorSessions?.length || 0}</a>`;
        }
    },
    {
        name: 'desc',
        label: '描述',
        minWidth: 120,
        render(item) {
            return item.description || '-';
        }
    },
    {name: 'opt', label: '操作', minWidth: 120}
];