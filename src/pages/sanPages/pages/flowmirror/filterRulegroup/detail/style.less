.filter-rule-detail {
    min-height: 100%;
    width: 100%;
    position: relative;
    padding: 20px;
    .content-box {
        padding: 20px 20px 0 0;
        .cell {
            min-width: 30%;
            display: inline-block;
            margin: 10px;
            .cell-title {
                color: #999999;
                display: inline-block;
            }
            .cell-content {
                display: inline-block;
            }
            .icon-edit,
            .icon-copy {
                font-size: 12px;
                margin-left: 10px;
            }
            .name-icon {
                fill: #2468f2;
            }
        }
        .app-legend {
            margin-bottom: 10px;
        }
        .s-legend-highlight {
            &::before {
                display: none;
            }
        }
    }
}

.rule-detail-main-wrap {
    .s-detail-page-content {
        margin: 0;
    }

    .instance-not-found-class {
        height: 100%;
    }
    .app-tab-page {
        .skin-accordion-tab {
            border: none;
            margin: 20px;
            border-radius: 6px;
        }
    }
    .title_class {
        display: flex;
        align-items: center;
        .s-icon {
            font-size: 14px;
            color: #84868c;
        }
        .instance-name {
            font-size: 16px;
            font-weight: 500;
            color: #151b26;
            padding: 0px 12px 0px 16px;
        }
    }
}
