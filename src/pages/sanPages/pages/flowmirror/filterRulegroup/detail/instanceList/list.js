/*
 * @description: 安全组已绑定资源列表页
 * @file: security/pages/detail/list.js
 * @author: p<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */

import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {OutlinedEditingSquare} from '@baidu/sui-icon';
const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent} = decorators;

import {columns} from './columns';
import {mirrorStatus, mirrorSourceType, mirrorDestType} from '@/pages/sanPages/common/enum';
import './style.less';

const tpl = html` <div>
    <s-biz-page class="mirror-instance-list">
        <s-app-legend label="关联镜像会话" />
        <s-table columns="{{table.columns}}" datasource="{{instance.mirrorSessions}}">
            <div slot="c-id">
                <span class="text-hidden">{{ row.name ? row.name : '-' }}</span>
                <br />
                {{ row.sessionId ? row.sessionId : '-' }}
            </div>
            <div slot="c-status">
                <span class="{{row.status | statusClass}}">{{row.status | statusText}}</span>
            </div>
            <div slot="c-sourceType">
                <span>{{row.sourceType | sourceType}}</span>
                <br />
                <span>{{row.sourceId}}</span>
            </div>
            <div slot="c-destType">
                <span>{{row.destType | destType}}</span>
                <br />
                <span>{{row.destId}}</span>
            </div>
            <div slot="c-opt">
                <span class="operations">
                    <s-button
                        s-if="row.status==='running'"
                        skin="stringfy"
                        on-click="changeStatus(row, 'stop')"
                        disabled="{{submiting}}"
                    >
                        暂停</s-button
                    >
                    <s-button
                        s-if="row.status==='paused'"
                        skin="stringfy"
                        on-click="changeStatus(row, 'start')"
                        disabled="{{submiting}}"
                        >启用</s-button
                    >
                </span>
            </div>
        </s-table>
    </s-biz-page>
</div>`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@asComponent('@bind-mirror-instance')
class SecurityInstanceList extends Component {
    static components = {
        'outlined-editing-square': OutlinedEditingSquare
    };
    static filters = {
        statusClass(value) {
            return mirrorStatus.fromValue(value).styleClass || '';
        },
        statusText(value) {
            return value ? mirrorStatus.getTextFromValue(value) : '-';
        },
        sourceType(value) {
            return mirrorSourceType.getTextFromValue(value.toUpperCase()) || '';
        },
        destType(value) {
            return mirrorDestType.getTextFromValue(value.toUpperCase()) || '';
        }
    };
    initData() {
        return {
            table: {
                columns: columns
            },
            instance: {},
            submiting: false
        };
    }

    inited() {
        this.data.set('instance', this.data.get('context').instance);
    }

    getDetail() {
        return this.$http
            .getFilterRuleDetail(this.data.get('context').id, {'x-silent-codes': ['InstanceNotExist']})
            .then(result => {
                result.rules = result.ingressRules.concat(result.egressRules);
                result.rules.forEach(item => {
                    item.strategy = item.strategy.toLowerCase();
                    item.ruleIpType = item.ruleType;
                    item.sourcePort = this.dealPort(item.srcPortRange);
                    item.destinationPort = this.dealPort(item.destPortRange);
                });
                this.data.set('instance', result);
            });
    }

    changeStatus(row, status) {
        this.data.set('submiting', true);
        return this.$http
            .putSessionAction(row.sessionId, status)
            .then(() => {
                this.getDetail();
            })
            .catch(() => {})
            .finally(() => {
                this.data.set('submiting', false);
            });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(SecurityInstanceList));
