/**
 * @file security/pages/detail/detail.js
 * <AUTHOR>
 */
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {Notification} from '@baidu/sui';
import {OutlinedEditingSquare} from '@baidu/sui-icon';

import Rule from '@/pages/sanPages/utils/rule';
import FilterRuleList from '@/pages/sanPages/pages/flowmirror/filterRulegroup/components/ruleList';
import {utcToTime} from '@/pages/sanPages/utils/helper';

const {asComponent, invokeSUI, invokeSUIBIZ, invokeComp, template} = decorators;
/* eslint-disable */
const tpl = html` <div>
    <div class="{{klass}}">
        <div class="content-box">
            <s-app-legend label="基本信息" />
            <div class="cell">
                <div class="cell-title">{{'实例名称：'}}</div>
                <div class="cell-content" ref="name">
                    {{instance.name}}
                    <edit-popover
                        value="{=instance.name=}"
                        rule="{{Rule.NAME_SUPPORT_CHINESE}}"
                        tip="支持大小写字母，数字，中文和-_/.以字母或者中文开头，不超过65个字"
                        on-edit="update($event, 'name')"
                        ><outlined-editing-square color="#2468f2" />
                    </edit-popover>
                </div>
            </div>
            <div class="cell">
                <div class="cell-title">{{'实例ID：'}}</div>
                <div class="cell-content">{{instance.ruleGroupId}}</div>
                <s-clip-board class="name-icon" text="{{instance.ruleGroupId}}"></s-clip-board>
            </div>
            <div class="cell">
                <div class="cell-title">{{'创建时间：'}}</div>
                <div class="cell-content">{{instance | createdTime}}</div>
            </div>
            <div class="cell">
                <div class="cell-title">{{'规则数量：'}}</div>
                <div class="cell-content">{{instance.ingressRules.length + instance.egressRules.length}}</div>
            </div>
            <div class="cell">
                <div class="cell-title">{{'关联镜像会话：'}}</div>
                <div class="cell-content">{{instance.mirrorSessions.length}}</div>
            </div>
            <div class="cell">
                <div class="cell-title">{{'描述：'}}</div>
                <div class="cell-content">
                    {{instance.description}}
                    <edit-popover
                        value="{=instance.description=}"
                        rule="{{Rule.DETAIL_EDIT.DESC}}"
                        on-edit="update($event, 'description')"
                        ><outlined-editing-square color="#2468f2" />
                    </edit-popover>
                </div>
            </div>
        </div>
        <div class="content-box">
            <s-app-legend label="配置信息" />
            <filter-rule-list
                instance="{{instance}}"
                totalQuota="{{totalQuota}}"
                rules="{{instance.rules}}"
                isDetail="true"
                type="{{type}}"
            ></filter-rule-list>
        </div>
    </div>
</div>`;

/* eslint-enable */
@template(tpl)
@invokeComp('@edit-popover')
@invokeSUI
@invokeSUIBIZ
@asComponent('@rule-filter-detail')
class SecurityDetail extends Component {
    static components = {
        'outlined-editing-square': OutlinedEditingSquare,
        'filter-rule-list': FilterRuleList
    };
    static filters = {
        createdTime(item) {
            return item.createTime ? utcToTime(item.createTime) : '-';
        }
    };

    static messages = {
        upDataRule() {
            this.getDetail();
        }
    };

    initData() {
        return {
            klass: ['filter-rule-detail'],
            instance: {},
            rules: [],
            Rule,
            totalQuota: 10
        };
    }

    inited() {
        this.$http
            .getMirrorQuota({
                quotaTypes: 'ruleNumberPerGroupQuota'
            })
            .then(res => {
                this.data.set('totalQuota', res?.quotaType2quota?.ruleNumberPerGroupQuota || 10);
            })
            .catch(() => this.data.set('totalQuota', 10));
        this.getDetail();
    }

    getDetail() {
        return this.$http
            .getFilterRuleDetail(this.data.get('ruleGroupId'), {'x-silent-codes': ['InstanceNotExist']})
            .then(result => {
                result.rules = result.ingressRules.concat(result.egressRules);
                result.rules.forEach(item => {
                    item.strategy = item.strategy.toLowerCase();
                    item.ruleIpType = item.ruleType;
                    item.sourcePort = this.dealPort(item.srcPortRange);
                    item.destinationPort = this.dealPort(item.destPortRange);
                });
                this.data.set('instance', result);
            });
    }

    dealPort(port) {
        if (port.begin === port.end) {
            return port.begin;
        } else {
            return `${port.begin}-${port.end}`;
        }
    }

    update(value, field) {
        let instance = this.data.get('instance');
        this.$http
            .putFilterRule(instance.ruleGroupId, {
                [field]: value
            })
            .then(() => {
                Notification.success('修改成功');
                this.data.set(`instance.${field}`, value);
                this.dispatch('upDataDetail');
            });
    }
}
export default Processor.autowireUnCheckCmpt(SecurityDetail);
