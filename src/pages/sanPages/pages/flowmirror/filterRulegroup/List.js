/*
 * @description: 筛选条件列表页
 * @file:
 * @author:
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {OutlinedPlus, OutlinedRefresh, OutlinedEditingSquare} from '@baidu/sui-icon';
import {Message, Dialog} from '@baidu/sui';

import {SCHEMA} from './schema';
import './list.less';
import {ContextService} from '@/pages/sanPages/common';

const {asComponent, invokeSUI, invokeSUIBIZ, invokeAppComp, template} = decorators;

const tpl = html` <div>
    <s-app-list-page class="{{klass}}">
        <div slot="pageTitle"></div>
        <div slot="bulk">
            <s-tip-button
                disabled="{{createFilter || pager.total>totalQuota || pager.total===totalQuota}}"
                placement="bottom"
                skin="primary"
                content="{{createFilterMessage || '筛选条件数量超过限制，如需提升配额，请提交工单'}}"
                isDisabledVisibile="{{true}}"
                on-click="onCreate"
            >
                <outlined-plus />{{'创建筛选条件'}}
            </s-tip-button>
        </div>
        <div slot="filter">
            <s-search
                width="150"
                value="{=searchbox.keyword=}"
                placeholder="{{searchbox.placeholder}}"
                on-search="onSearch"
            >
                <s-select
                    slot="options"
                    width="100"
                    datasource="{{searchbox.keywordTypes}}"
                    value="{=searchbox.keywordType=}"
                    on-change="onSearchboxChange"
                >
                </s-select>
            </s-search>
            <s-button class="button-margin-left" on-click="refresh"><outlined-refresh /></s-button>
        </div>
        <s-table
            s-ref="table"
            columns="{{table.columns}}"
            loading="{{table.loading}}"
            error="{{table.error}}"
            datasource="{{table.datasource}}"
        >
            <div slot="error">
                啊呀，出错了？
                <a href="javascript:;" on-click="refresh">重新加载</a>
            </div>
            <template slot="c-name">
                <a class="instance-name" href="#/vpc/filterRuleGroup/detail?id={{row.ruleGroupId}}" title="{{row.name}}"
                    >{{row.name}}</a
                >
                <s-popover s-ref="{{'nameEdit' + rowIndex}}" placement="top" trigger="click">
                    <div class="edit-name-warp" slot="content">
                        <s-input
                            value="{=instanceName.name=}"
                            width="160"
                            placeholder="请输入名称"
                            on-input="onNameInput($event, rowIndex, 'name')"
                        />
                        <div class="edit-name-tip">
                            支持大小写字母，数字，中文和"-_/.”以字母或者中文开头，不超过65个字
                        </div>
                        <s-button
                            skin="primary"
                            s-ref="{{'nameBtn' + rowIndex}}"
                            disabled="{{true}}"
                            on-click="editConfirm(row, rowIndex, 'name')"
                            >确定</s-button
                        >
                        <s-button on-click="editCancel(rowIndex, 'name')">取消</s-button>
                    </div>
                    <outlined-editing-square class="name-icon" on-click="editName(row, 'name')" />
                </s-popover>
                <br />
                {{row.ruleGroupId}}
                <s-clip-board class="name-icon" text="{{row.ruleGroupId}}" successMessage="已复制到剪贴板" />
            </template>
            <template slot="c-desc">
                {{row.description || '-'}}
                <s-popover s-ref="{{'descriptionEdit' + rowIndex}}" placement="top" trigger="click">
                    <div class="edit-name-warp" slot="content">
                        <s-input-text-area
                            maxLength="200"
                            width="220"
                            height="60"
                            value="{=instanceName.description=}"
                            on-input="onNameInput($event, rowIndex, 'description')"
                            placeholder="{{'最多200个字符'}}"
                        ></s-input-text-area>
                        <s-button
                            skin="primary"
                            s-ref="{{'descriptionBtn' + rowIndex}}"
                            disabled="{{true}}"
                            on-click="editConfirm(row, rowIndex, 'description')"
                            >确定</s-button
                        >
                        <s-button on-click="editCancel(rowIndex, 'description')">取消</s-button>
                    </div>
                    <outlined-editing-square class="name-icon" on-click="editName(row, 'description')" />
                </s-popover>
            </template>
            <div slot="c-opt">
                <span class="operations">
                    <s-tip-button
                        disabled="{{row.mirrorSessions.length}}"
                        placement="bottom"
                        skin="stringfy"
                        content="已关联镜像会话，该筛选条件不支持删除"
                        isDisabledVisibile="{{true}}"
                        on-click="onDelete(row)"
                    >
                        {{'删除'}}
                    </s-tip-button>
                </span>
            </div>
        </s-table>
        <s-pagination
            slot="pager"
            s-if="pager.total"
            layout="{{'total, pageSize, pager'}}"
            pageSize="{{pager.size}}"
            total="{{pager.total}}"
            page="{{pager.page}}"
            on-pagerChange="onPageChange"
            on-pagerSizeChange="onPageSizeChange"
        />
    </s-app-list-page>
</div>`;

@asComponent('@filter-rule-group-list')
@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class VpcFlowLogList extends Component {
    static components = {
        'outlined-plus': OutlinedPlus,
        'outlined-refresh': OutlinedRefresh,
        'outlined-editing-square': OutlinedEditingSquare
    };
    initData() {
        return {
            klass: ['main-wrap-new filter-rule-wrapper'],
            searchbox: {
                keywordTypes: [
                    {text: '实例名称', value: 'name', placeholder: '请输入实例名称进行搜索'},
                    {text: '实例ID', value: 'rule_group_id', placeholder: '请输入实例ID进行搜索'}
                ],
                placeholder: '请输入实例名称进行搜索',
                keywordType: 'name',
                keyword: ''
            },
            table: {
                loading: false,
                columns: SCHEMA,
                datasource: []
            },
            instanceName: {
                name: '',
                description: ''
            },
            pager: {
                size: 10,
                page: 1,
                total: 0
            },
            totalQuota: 50,
            createFilter: false,
            createFilterMessage: ''
        };
    }
    inited() {
        const AllRegion = ContextService.getEnum('AllRegion');
        if (window.$context.getCurrentRegionId() === AllRegion.SIN) {
            this.data.set('createFilter', true);
            this.data.set('createFilterMessage', '新加坡地域资源售罄，请您切换到其他地域创建');
        }
        this.$http
            .getMirrorQuota({
                quotaTypes: 'ruleGroupNumberQuota'
            })
            .then(res => {
                this.data.set('totalQuota', res?.quotaType2quota?.ruleGroupNumberQuota || 50);
            })
            .catch(() => this.data.set('totalQuota', 50));
    }
    attached() {
        this.loadPage();
    }

    loadPage() {
        this.data.set('table.loading', true);
        let payload = this.getSearchCriteria();
        this.$http.getFilterRuleList(payload).then(data => {
            this.data.set('table.datasource', data.result);
            this.data.set('pager.total', data.totalCount);
            this.data.set('table.loading', false);
        });
    }

    onCreate() {
        location.hash = '#/vpc/filterRuleGroup/create';
    }

    onDelete(item) {
        Dialog.warning({
            title: '删除提示',
            content: '确认要删除该筛选条件？',
            onOk: () => {
                this.$http.deleteFilterRule(item.ruleGroupId).then(() => {
                    Message.success('删除成功');
                    this.loadPage();
                });
            }
        });
    }

    // 点击修改名称icon
    editName(row, field) {
        this.data.set(`instanceName.${field}`, row[field]);
    }
    // 修改名称确认
    editConfirm(row, rowIndex, field) {
        let instanceName = this.data.get('instanceName');
        this.$http
            .putFilterRule(row.ruleGroupId, {
                [field]: instanceName[field]
            })
            .then(() => {
                this.editCancel(rowIndex, field);
                Message.success({
                    content: '修改成功'
                });
                this.loadPage();
            })
            .catch(() => {
                this.editCancel(rowIndex, field);
                Message.success({
                    content: '修改失败'
                });
                this.loadPage();
            });
    }
    // 输入名称
    onNameInput(e, rowIndex, field) {
        let result = false;
        if (
            field === 'name' &&
            (e.value === '' || !/^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{0,64}$/.test(e.value))
        ) {
            result = true;
        } else {
            result = false;
        }
        this.data.set(`instanceName.${field}`, e.value);
        this.ref(field + 'Btn' + rowIndex).data.set('disabled', result);
    }
    // 修改名称取消
    editCancel(rowIndex, field) {
        this.ref(field + 'Btn' + rowIndex).data.set('disabled', true);
        this.ref(field + 'Edit' + rowIndex).data.set('visible', false);
    }

    onSearchboxChange(e) {
        this.data.set('searchbox.keywordType', e.value);
        this.data.set('searchbox.keyword', '');
        let data = u.find(this.data.get('searchbox.keywordTypes'), item => item.value === e.value);
        if (data.placeholder) {
            this.data.set('searchbox.placeholder', data.placeholder);
        }
    }

    onSearch() {
        this.data.set('pager.page', 1);
        return this.loadPage();
    }

    refresh() {
        return this.loadPage();
    }

    getSearchCriteria() {
        const pager = this.data.get('pager');
        let searchParam = {
            keyword: this.data.get('searchbox.keyword') || '',
            keywordType: this.data.get('searchbox.keywordType')
        };
        return u.extend({}, searchParam, {pageNo: pager.page, pageSize: pager.size});
    }

    onPageChange({value}) {
        this.data.set('pager.page', value.page);
        this.loadPage();
    }

    onPageSizeChange({value}) {
        this.data.set('pager.size', value.pageSize);
        this.data.set('pager.page', 1);
        this.loadPage();
    }
}
export default Processor.autowireUnCheckCmpt(VpcFlowLogList);

// export default San2React(Processor.autowireUnCheckCmpt(VpcFlowLogList));
