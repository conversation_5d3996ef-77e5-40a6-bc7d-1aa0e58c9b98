/*
 * @Description: 镜像会话列表s
 * @Author: <EMAIL>
 * @Date: 2022-06-01 17:18:55
 */

import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {OutlinedPlus, OutlinedRefresh, OutlinedEditingSquare} from '@baidu/sui-icon';
import {Message, Dialog} from '@baidu/sui';
import Confirm from '@/pages/sanPages/components/confirm';
import {SCHEMA} from './schema';
import {utcToTime} from '@/pages/sanPages/utils/helper';
import './list.less';
import {mirrorStatus, mirrorSourceType, mirrorDestType} from '@/pages/sanPages/common/enum';
import {ContextService} from '@/pages/sanPages/common';
import testID from '@/testId';

const {asComponent, invokeSUI, invokeSUIBIZ, invokeAppComp, template} = decorators;

const tpl = html`
    <div>
        <slot name="introducePanel" />
        <s-app-list-page class="{{klass}}" data-test-id="test">
            <div slot="bulk">
                <s-tip-button
                    disabled="{{createMirrow || disableCreate}}"
                    skin="primary"
                    isDisabledVisibile="{{true}}"
                    on-click="onCreate"
                >
                    <outlined-plus />创建镜像会话
                    <div slot="content">{{createMirrowMessage || disableCreateTip}}</div>
                </s-tip-button>
                <s-tip-button
                    class="button-margin-left"
                    content="请先选中实例"
                    isDisabledVisibile="{{true}}"
                    disabled="{{!enableDelete}}"
                    on-click="onDelete"
                    >删除</s-tip-button
                >
            </div>
            <div slot="filter">
                <s-search
                    width="150"
                    value="{=searchbox.keyword=}"
                    placeholder="{{searchbox.placeholder}}"
                    on-search="onSearch"
                >
                    <s-select
                        slot="options"
                        width="100"
                        datasource="{{searchbox.keywordTypes}}"
                        value="{=searchbox.keywordType=}"
                        on-change="onSearchboxChange"
                    >
                    </s-select>
                </s-search>
                <s-button class="button-margin-left" on-click="refresh"><outlined-refresh /></s-button>
            </div>
            <s-table
                s-ref="table"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                datasource="{{table.datasource}}"
                selection="{=table.selection=}"
                data-test-id="${testID.flowMirror.listTable}"
                class="mirror-session-table"
            >
                <div slot="error">
                    啊呀，出错了？
                    <a href="javascript:;" on-click="refresh">重新加载</a>
                </div>
                <div slot="empty">
                    <s-empty actionText="" data-test-id="${testID.flowMirror.listEmpty}{{rowIndex}}" />
                </div>
                <template slot="c-name">
                    <span class="text-hidden">{{ row.name || '-' }}</span>
                    <s-popover s-ref="{{'instanceNameEdit' + rowIndex}}" placement="top" trigger="click">
                        <div class="edit-name-warp" slot="content">
                            <s-input
                                value="{=instance.name=}"
                                width="160"
                                placeholder="请输入名称"
                                on-input="onNameInput($event,row, rowIndex, 'Name')"
                            />
                            <div class="edit-name-tip">
                                支持大小写字母，数字，中文和"-_/.”以字母或者中文开头，不超过65个字
                            </div>
                            <s-button
                                skin="primary"
                                s-ref="{{'editBtnName' + rowIndex}}"
                                disabled="{{true}}"
                                on-click="onEdit(row, 'Name')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel(row,rowIndex, 'Name')">取消</s-button>
                        </div>
                        <outlined-editing-square class="name-icon" on-click="beforeEdit(row)" />
                    </s-popover>
                    <br />
                    {{ row.sessionId }}
                    <s-clip-board class="name-icon" text="{{row.sessionId}}" successMessage="已复制到剪贴板" />
                </template>
                <div slot="c-status">
                    <span class="{{row.status | statusClass}}">{{row.status | statusText}}</span>
                </div>
                <div slot="c-sourceType">
                    <a href="javascript: void(0);" on-click="handleLinkTo(row, 'source')">
                        <span>{{row.sourceType | sourceType}}</span>
                        <br />
                        <span s-if="row.sourceType === 'csn'">{{row.showSourceId}}:</span>
                        <span s-if="row.sourceType === 'peerconn'">{{row.showSourceId}}</span>
                        <span s-if="row.sourceType !== 'peerconn'">{{row.sourceId}}</span>
                    </a>
                </div>
                <div slot="c-ruleGroupId">
                    <span class="truncated">
                        <a href="#/vpc/filterRuleGroup/detail?id={{row.ruleGroupId}}">{{ row.ruleGroupName || '-' }}</a>
                    </span>
                    <br />
                    <span class="truncated">{{ row.ruleGroupId || '-' }}</span>
                </div>
                <div slot="c-destType">
                    <a href="javascript: void(0);" on-click="handleLinkTo(row, 'dest')">
                        <span>{{row.destType | destType}}</span>
                        <br />
                        <span>{{row.destId}}</span>
                    </a>
                </div>
                <div slot="c-packetLength">{{row.packetLength | packetLengthTxt}}</div>
                <div slot="c-desc">
                    <span class="text-hidden flow-desc">{{ row.description || '-' }}</span>
                    <s-popover
                        s-ref="{{'instanceDescEdit' + rowIndex}}"
                        placement="top"
                        class="edit-popover-class edit-mirror-session-popover"
                        trigger="click"
                    >
                        <div class="edit-name-warp" slot="content">
                            <s-input-text-area
                                maxLength="200"
                                width="220"
                                height="60"
                                value="{=instance.desc=}"
                                on-input="onNameInput($event,row, rowIndex, 'Desc')"
                                placeholder="请输入描述，最多200个字符"
                            ></s-input-text-area>
                            <s-button
                                skin="primary"
                                s-ref="{{'editBtnDesc' + rowIndex}}"
                                disabled="{{true}}"
                                on-click="onEdit(row, 'Desc')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel(row,rowIndex, 'Desc')">取消</s-button>
                        </div>
                        <outlined-editing-square class="name-icon" on-click="beforeEdit(row)" />
                    </s-popover>
                </div>
                <div slot="c-createTime">{{row | createdTime}}</div>
                <div slot="c-opt">
                    <span class="operations">
                        <s-button
                            skin="stringfy"
                            on-click="changeStatus(row, row.status === 'running' ? 'stop' : 'start')"
                            disabled="{{submiting}}"
                        >
                            {{row.status === 'running' ? '暂停' : '启用'}}</s-button
                        >
                        <s-button
                            data-test-id="${testID.flowMirror.instanceEdit}{{rowIndex}}"
                            skin="stringfy"
                            on-click="goEdit(row)"
                            >编辑</s-button
                        >
                    </span>
                </div>
            </s-table>
            <s-pagination
                slot="pager"
                s-if="pager.total"
                layout="{{'total, pageSize, pager'}}"
                pageSize="{{pager.size}}"
                total="{{pager.total}}"
                page="{{pager.page}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </s-app-list-page>
    </div>
`;

@asComponent('@vpc-mirror-list')
@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class VpcMirrorList extends Component {
    static components = {
        'outlined-plus': OutlinedPlus,
        'outlined-refresh': OutlinedRefresh,
        'outlined-editing-square': OutlinedEditingSquare
    };
    initData() {
        return {
            klass: ['main-wrap-new mirror-list-wrapper'],
            searchbox: {
                keywordTypes: [
                    {text: '实例名称', value: 'name', placeholder: '请输入实例名称进行搜索'},
                    {text: '实例ID', value: 'session_id', placeholder: '请输入实例ID进行搜索'},
                    {text: '镜像源', value: 'source_id', placeholder: '请输入镜像源进行搜索'},
                    {text: '镜像目的', value: 'dest_id', placeholder: '请输入镜像目的进行搜索'}
                ],
                placeholder: '请输入实例名称进行搜索',
                keywordType: 'name',
                keyword: ''
            },
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                columns: SCHEMA,
                datasource: []
            },
            instanceName: {
                name: '',
                desc: ''
            },
            pager: {
                size: 10,
                page: 1,
                total: 0
            },
            createMirrow: false,
            createMirrowMessage: '',
            submiting: false
        };
    }

    static computed = {
        disableCreate() {
            let total = this.data.get('pager.total');
            let quota = this.data.get('quota');
            return quota && total >= quota?.sessionNumberQuota;
        },
        disableCreateTip() {
            let total = this.data.get('pager.total');
            let quota = this.data.get('quota');
            return quota && total >= quota?.sessionNumberQuota ? '镜像会话配额不足。如需提升配额，请提交工单' : '';
        },
        enableDelete() {
            const table = this.data.get('table');
            return table.selection && table.selection.selectedIndex && table.selection.selectedIndex.length === 1;
        }
    };

    static filters = {
        createdTime(item) {
            if (!item.createdTime) {
                return '-';
            }
            return utcToTime(item.createdTime);
        },
        statusClass(value) {
            return mirrorStatus.fromValue(value).styleClass || '';
        },
        statusText(value) {
            return mirrorStatus.getTextFromValue(value) || '';
        },
        sourceType(value) {
            return mirrorSourceType.getTextFromValue(value.toUpperCase()) || '';
        },
        destType(value) {
            return mirrorDestType.getTextFromValue(value.toUpperCase()) || '';
        },
        packetLengthTxt(length) {
            if (!length) {
                return '全部字节';
            }
            return `${length}字节`;
        }
    };
    inited() {
        const AllRegion = ContextService.getEnum('AllRegion');
        if (window.$context.getCurrentRegionId() === AllRegion.SIN) {
            this.data.set('createMirrow', true);
            this.data.set('createMirrowMessage', '新加坡地域资源售罄，请您切换到其他地域创建');
        }
    }

    attached() {
        this.loadPage();
    }

    onCreate() {
        location.hash = '#/vpc/mirror/create';
    }

    resetTable() {
        this.data.set('table.selection.selectedIndex', []);
    }
    handleLinkTo(row: Record<string, any>, mirrorType: 'dest' | 'source') {
        const {sourceId, sourceType, destType, destId, showSourceId} = row;
        const type = mirrorType === 'dest' ? destType : sourceType;
        const id = mirrorType === 'dest' ? destId : ['csn', 'peerconn'].includes(sourceType) ? showSourceId : sourceId;
        const sourceTypeMapLink: Record<string, string> = {
            eip: `/eip/#/eip/instance/list?queryParam=${id}`,
            bcc: `/bcc/#/bcc/instance/detail?instanceId=${id}&id=${id}`,
            eni: `#/vpc/eni/list?id=${id}`,
            nat: `#/vpc/nat/list?natId=${id}`,
            peerconn: `#/vpc/peerconn/list?id=${id}`,
            csn: `/csn/#/csn/instance/list?id=${id}`,
            dcgw: `#/vpc/dcgw/list?dcgwId=${id}`,
            blb: `/blb/#/blb/list?id=${id}`
        };
        window.open(sourceTypeMapLink[type]);
    }

    onDelete() {
        let selectItemIndexs = this.ref('table').getSelectedItems();
        let ids = selectItemIndexs.map(ele => ele.sessionId);
        Dialog.warning({
            title: '删除提示',
            content: '确认要删除该镜像会话？',
            onOk: () => {
                this.$http.deleteMirrorSession({sessionIds: ids}).then(() => {
                    Message.success('删除成功');
                    this.loadPage();
                });
            }
        });
    }

    goEdit(row) {
        location.hash = `#/vpc/mirror/create?sessionId=${row.sessionId}`;
    }

    getSearchCriteria() {
        const pager = this.data.get('pager');
        let searchParam = {
            keyword: this.data.get('searchbox.keyword'),
            keywordType: this.data.get('searchbox.keywordType')
        };
        return u.extend({}, searchParam, {pageNo: pager.page, pageSize: pager.size});
    }

    loadPage() {
        this.getQuota();
        this.data.set('table.loading', true);
        this.resetTable();
        let payload = this.getSearchCriteria();
        this.$http.getMirrorSessionList(payload).then(data => {
            this.data.set('table.datasource', data.result);
            this.data.set('pager.total', data.totalCount);
            this.data.set('table.loading', false);
        });
    }

    getQuota() {
        this.$http
            .getMirrorQuota({
                quotaTypes: 'sessionNumberQuota'
            })
            .then(res => {
                this.data.set('quota', res.quotaType2quota);
            });
    }

    onSearchboxChange(e) {
        this.data.set('searchbox.keywordType', e.value);
        this.data.set('searchbox.keyword', '');
        let data = u.find(this.data.get('searchbox.keywordTypes'), item => item.value === e.value);
        if (data.placeholder) {
            this.data.set('searchbox.placeholder', data.placeholder);
        }
    }

    onSearch() {
        this.data.set('pager.page', 1);
        return this.loadPage();
    }

    refresh() {
        return this.loadPage();
    }

    beforeEdit(row) {
        this.data.set('instance.name', row.name);
        this.data.set('instance.desc', row.description);
    }

    editCancel(row, rowIndex, type) {
        this.ref(`instance${type}Edit${rowIndex}`).data.set('visible', false);
    }

    onNameInput(e, row, rowIndex, type) {
        let result;
        let editType = type.toLocaleLowerCase();
        if (type === 'Name') {
            result = e.value.length <= 128 && /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{0,64}$/.test(e.value);
        } else {
            result = e.value.length <= 200;
        }
        this.data.set(`instance.${editType}`, e.value);
        this.ref(`editBtn${type}${rowIndex}`).data.set('disabled', !result);
    }

    // 改变镜像会话状态
    changeStatus(row, status) {
        const statusText = row.status === 'running' ? '暂停' : '启用';
        const confirm = new Confirm({
            data: {
                open: true,
                title: `${statusText}流量镜像会话`,
                content: `您确定要${statusText}流量镜像会话：${row.name}/${row.sessionId} 吗？`
            }
        });
        confirm.on('confirm', () => {
            this.data.set('submiting', true);
            return this.$http
                .putSessionAction(row.sessionId, status)
                .then(() => {
                    this.loadPage();
                })
                .catch(() => {})
                .finally(() => {
                    this.data.set('submiting', false);
                });
        });
        confirm.attach(document.body);
    }

    onEdit(item, type) {
        let instance = this.data.get('instance');
        let payload;
        if (type === 'Name') {
            payload = {
                name: instance.name,
                description: item.description,
                ruleGroupId: item.ruleGroupId,
                vni: item.vni,
                packetLength: item.packetLength ? item.packetLength : ''
            };
        } else {
            payload = {
                description: instance.desc,
                name: item.name,
                ruleGroupId: item.ruleGroupId,
                vni: item.vni,
                packetLength: item.packetLength ? item.packetLength : ''
            };
        }
        return this.$http.updateMirrorSession(item.sessionId, payload).then(() => this.loadPage());
    }

    onPageChange({value}) {
        this.data.set('pager.page', value.page);
        this.loadPage();
    }

    onPageSizeChange({value}) {
        this.data.set('pager.size', value.pageSize);
        this.data.set('pager.page', 1);
        this.loadPage();
    }
}
export default Processor.autowireUnCheckCmpt(VpcMirrorList);
