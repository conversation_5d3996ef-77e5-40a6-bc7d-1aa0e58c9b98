export const SCHEMA = [
  {
    name: 'name',
    label: '实例名称/ID',
    minWidth: 160,
    fixed: 'left'
  },
  {
    name: 'status',
    label: '状态',
    minWidth: 80,
  },
  {
    name: 'sourceType',
    label: '镜像源',
    minWidth: 120,
  },
  {
    name: 'ruleGroupId',
    label: '筛选条件',
    minWidth: 120,
  },
  {
    name: 'destType',
    label: '镜像目的',
    minWidth: 120,
  },
  {
    name: 'vni',
    label: '指定VNI',
    minWidth: 120,
  },
  {
    name: 'packetLength',
    label: '数据包长度',
    minWidth: 120,
  },
  {
    name: 'desc',
    label: '描述',
    minWidth: 180,
  },
  {
    name: 'createTime',
    label: '创建时间',
    minWidth: 180,
  },
  {
    name: 'opt',
    label: '操作',
    fixed: 'right',
    minWidth: 120
  }
];