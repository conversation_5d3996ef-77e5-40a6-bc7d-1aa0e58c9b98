.vpc-mirror-create {
    width: 100%;
    min-height: 100%;
    padding-bottom: 20px;
    .label_class {
        .inline-tip {
            top: 3px;
            left: -5px;
            position: relative;
            .s-tip-warning {
                justify-content: center;
                .warning_class {
                    fill: #999;
                }
            }
            .s-tip:hover .s-icon path {
                fill: #2468f2 !important;
            }
        }
    }
    .s-legend-highlight {
        &::before {
            display: none;
        }
    }
    .content-box {
        border-radius: 6px;
        width: calc(~'100vw - 32px');
        background: #fff;
        margin: 16px 16px 0;
        padding: 24px;
        .s-form-item {
            .s-form-item-label {
                min-width: 120px;
                .inline-tip {
                    top: 3px;
                    left: -5px;
                    position: relative;
                    .s-tip-warning {
                        justify-content: center;
                        .warning_class {
                            fill: #999;
                        }
                    }
                    .s-tip:hover .s-icon path {
                        fill: #2468f2 !important;
                    }
                }
            }
        }
        .device-port-widget {
            .s-form-item-label {
                label {
                    display: none;
                }
            }
        }
        .filter-rule-item {
            .s-row-flex {
                flex-flow: inherit;
            }
            .filter-rule-list {
                .tool-tip {
                    display: flex;
                    justify-content: space-between;
                }
                .s-table {
                    margin: 20px 0;
                }
                .s-pagination {
                    float: right;
                }
            }
        }
        .share-wrapper {
            .s-form-item-control {
                .mirror-dest-tip {
                    line-height: 20px;
                    margin-top: 4px;
                    .value {
                        color: #ff9326;
                    }
                }
                .text-wrapper {
                    display: flex;
                    height: 30px;
                    line-height: 30px;
                }
            }
        }
    }
    .source-id-wrapper {
        .s-form-item {
            margin: 0;
            display: inline-block;
            .s-form-item-control-wrapper {
                width: auto;
            }
        }
        .s-select {
            margin-right: 10px;
        }
    }
    .tip-icon-wrap {
        font-size: 14px;
        border: 1px solid #f18823;
        color: #f18823;
        margin-left: 8px;
        &:hover {
            color: #fff;
            background-color: #f18823;
        }
    }
    .buybucket {
        width: 100%;
        left: 0;
        height: 60px;
        .buybucket-container {
            width: auto;
            float: left;
            height: 48px;
            transform: translateY(12.5%);
            display: flex;
            align-items: center;
        }
    }
    .s-biz-page-fixed-footer {
        width: 100vw !important;
        .fixed-footer-content {
            .s-button {
                margin-right: 20px;
            }
        }
    }
}
.rule-filter {
    .s-drawer-content {
        padding: 0 !important;
    }
    .filter-rule-detail {
        padding-top: 0;
    }
}
