export const formValidator = self => ({
    name: [
        {required: true, message: '请填写名称'},
        {pattern: /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{0,64}$/, message: '格式不符合要求'}
    ],
    sourceId: [{required: true, message: '请选择'}],
    networkCardId: [
        {required: true, message: '请选择'},
        {
            validator: (rule, value, callback) => {
                const destNetworkCardId = self.data.get('formData.destNetworkCardId');
                if (destNetworkCardId === value) {
                    return callback('镜像源和镜像目的不能为同一个实例');
                }
                return callback();
            }
        }
    ],
    destNetworkCardId: [
        {requires: true, message: '请选择'},
        {
            validator: (rule, value, callback) => {
                const networkCardId = self.data.get('formData.networkCardId');
                if (networkCardId === value) {
                    return callback('镜像目的和镜像源不能为同一个实例');
                }
                return callback();
            }
        }
    ],
    ruleGroupId: [
        {
            validator: (rule, value, callback) => {
                if (!self.data.get('formData.ruleGroupId')) {
                    return callback('请选择筛选条件');
                }
                callback();
            }
        }
    ],
    destId: [
        {required: true, message: '请选择镜像目的'},
        {
            validator: (rule, value, callback) => {
                if (self.data.get('formData.destType') === 'BLB') {
                    let blbQuota = self.data.get('blbQuota');
                    let blbNum = self.data.get('blbNum');
                    if (blbQuota && blbNum !== undefined) {
                        if (blbNum >= blbQuota) {
                            return callback('负载均衡镜像目的配额不足');
                        } else {
                            return callback();
                        }
                    }
                }
                callback();
            }
        }
    ],
    sourceVpc: [{required: true, message: '请选择VPC'}],
    destVpc: [{required: true, message: '请选择VPC'}],
    vni: [
        {
            validator: (rule, value, callback) => {
                value = value && (value + '').trim();
                if (value === '') {
                    return callback();
                }
                if (!/^[1-9]\d*$/.test(value)) {
                    return callback('请输入0-16777215之间的整数');
                }
                if (value < 0 || value > 16777215) {
                    return callback('请输入0-16777215之间的整数');
                }
                callback();
            }
        }
    ],
    packetLength: [
        {
            validator: (rule, value, callback) => {
                value = value && (value + '').trim();
                if (value === '') {
                    return callback();
                }
                if (!/^[1-9]\d*$/.test(value)) {
                    return callback('请输入100-1500之间的整数');
                }
                if (value < 100 || value > 1500) {
                    return callback('请输入100-1500之间的整数');
                }
                callback();
            }
        }
    ],
    desc: [{min: 0, max: 200, message: '长度0到200个字符'}]
});
