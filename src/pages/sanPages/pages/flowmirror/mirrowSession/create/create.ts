/*
 * @Description: 镜像会话创建页
 * @Author: <EMAIL>
 * @Date: 2022-06-02 17:20:29
 */

import {html, decorators, redirect, CreatePage, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {OutlinedRefresh, OutlinedPlus} from '@baidu/sui-icon';
import {Radio, Input} from '@baidu/sui';
import u from 'lodash';

import {formValidator} from './rules';
import {
    PayType,
    mirrorSourceType,
    mirrorDestType,
    DcGatewayStatus,
    PeerConnStatus,
    EniStatus
} from '@/pages/sanPages/common/enum';
import {$flag as FLAG, urlSerialize, getVPCSupportRegion, kXhrOptions} from '@/pages/sanPages/utils/helper';
import {serviceTypeUrl, checkSts} from '@/pages/sanPages/utils/config';
import {ContextService} from '@/pages/sanPages/common';
import RuleFilterDetail from '@/pages/sanPages/pages/flowmirror/filterRulegroup/detail/sanDetail';
import {parseQuery} from '@/utils';
import './create.less';
import testID from '@/testId';

const {invokeSUI, invokeSUIBIZ, invokeAppComp, invokeComp, template, asComponent} = decorators;
const AllRegion = ContextService.getEnum('AllRegion');

const tpl = html`
    <template>
        <s-app-create-page
            class="vpc-mirror-create"
            backTo="{{pageNav.backUrl}}"
            backToLabel="{{pageNav.backLabel}}"
            pageTitle="{{pageNav.title}}"
        >
            <s-form
                s-ref="form"
                data="{=formData=}"
                rules="{{rules}}"
                label-col="{{labelCol}}"
                wrapper-col="{{wrapperCol}}"
                label-align="left"
            >
                <div class="content-box">
                    <s-app-legend label="{{'地域'}}" />
                    <!--<s-form-item label="{{'付费方式：'}}" prop="payType">
                        <s-radio-radio-group
                            datasource="{{payTypeSource}}"
                            value="{=payType=}"
                            radioType="button"
                        />
                    </s-form-item>-->
                    <s-form-item label="{{'当前地域：'}}" prop="region" class="create-region s-form-item-region">
                        <template slot="label" class="label_class">
                            {{'当前地域：'}}
                            <s-tip
                                class="inline-tip"
                                content="如需修改购买其他区域产品，请{{!FLAG.NetworkSupportXS ? '前往主导航进行切换': '在顶栏重新选择区域'}}"
                                skin="question"
                            />
                        </template>
                        <s-radio-radio-group
                            enhanced
                            datasource="{{regionSource}}"
                            value="{=region=}"
                            radioType="button"
                            disabled="{{sessionId}}"
                            on-change="onRegionChange"
                        />
                    </s-form-item>
                </div>
                <div class="content-box">
                    <s-app-legend label="{{'配置信息'}}" />
                    <s-form-item
                        label="{{'名称：'}}"
                        prop="name"
                        help="{{'大小写字母、数字，中文以及-_/.特殊字符，必须以字母、中文开头，长度1-65'}}"
                    >
                        <s-input placeholder="请输入镜像会话名称" width="{{300}}" value="{=formData.name=}" />
                    </s-form-item>
                    <s-form-item label="{{'源类型：'}}" prop="sourceType" class="share-wrapper">
                    <s-radio-radio-group
                        enhanced
                        value="{=formData.sourceType=}"
                        radioType="button"
                        class="no-res-wrap"
                        on-change="sourceChange"
                    >
                        <span s-for="item, index in sourceTypeSource">
                          <s-popover trigger="{{item.disabled ? 'hover' : ''}}">
                              <div slot="content">
                                  <!--bca-disable-next-line-->
                                  {{item.value | noOpenTip | raw}}
                              </div>
                              <s-radio
                                  value="{{item.value}}"
                                  disabled="{{item.disabled || sessionId}}"
                                  class="{{(index === 0 && 'radius-left') || (index === sourceTypeSource.length - 1 && 'radius-right')}}"
                                  label="{{item.text}}">
                              </s-radio>
                          </s-popover>
                      </span>
                  <s-radio-radio-group/>
                  <span class="text-wrapper" s-else>{{formData.sourceType | getOriginTypeDisplay}}</span>
                    </s-form-item>
                    <div s-if="{{formData.sourceType === 'EIP'}}" class="source-id-wrapper">
                        <s-form-item label="镜像源：" prop="sourceId" class="share-wrapper">
                            <s-select
                                width="{{300}}"
                                s-if="{{!sessionId}}"
                                placeholder="请选择镜像源"
                                disabled="{{sourceSwitch || sessionId}}"
                                datasource="{{mirrorSource}}"
                                filterable
                                value="{=formData.sourceId=}"
                            />
                            <span s-else class="text-wrapper">{{mirrorOriginDisplay}}</span>
                        </s-form-item>
                    </div>
                    <div s-else class="source-id-wrapper">
            <div s-if="{{!sessionId}}">
                <s-form-item
                  label="镜像源："
                  prop="sourceVpc">
                  <s-select
                    width="{{300}}"
                    placeholder="{{formData.sourceType === 'CSN' ? '请选择云智能网' : '请选择镜像源所在VPC'}}"
                    disabled="{{sessionId}}"
                    datasource="{{vpcs}}"
                    filterable
                    value="{=formData.sourceVpc=}"
                    on-change="sourceVpcChange"
                  />
                </s-form-item>
                <s-form-item
                  prop="sourceId"
                >
                  <s-select
                    width="{{300}}"
                    placeholder="{{sourcePlaceholder}}"
                    filterable
                    disabled="{{sourceSwitch || sessionId}}"
                    value="{=formData.sourceId=}"
                    on-change="sourceIdChange"
                    >
                    <s-select-option
                        s-for="item in mirrorSource"
                        value="{{item.value}}"
                        text="{{item.text}}"
                        key="{{item.value}}"
                        disabled="{{item.disabled}}"
                    >
                        <s-tooltip>
                            <div slot="content">
                                <span s-if="{{item.disabled}}">
                                    {{'暂不支持超过10G带宽的网络实例作为镜像源类型，请调整源类型带宽或选择其他实例ID。'}}
                                </span>
                            </div>
                            <span>{{item.text}}</span>
                        </s-tooltip>
                    </s-select-option>
                  </s-select>
                </s-form-item>
            </div>
            <s-form-item
                s-else
                label="镜像源："
                class="share-wrapper"
            >
                <span class="text-wrapper">{{mirrorOriginDisplay}}</span>
            </s-form-item>
        </div>
        <s-form-item class="device-port-widget" s-if="showNetCard && !sessionId" prop="networkCardId" label=" ">
            <s-select
                width="{{300}}"
                filterable
                placeholder="{{'请选择' + networkCardType}}"
                disabled="{{networkCardLoading || urlQuery.sessionId}}"
                datasource="{{networkCardDatasource}}"
                value="{=formData.networkCardId=}"
            >
            </s-select>
            <s-popover s-if="formData.sourceType === 'BCC'" placement="top">
                <div slot="content">
                    {{'选择的云服务器镜像源仅包含云服务器的主网卡，不包含云服务器挂载的弹性网卡。'}}
                </div>
                <s-icon class="tip-icon-wrap" name="warning-mark"/>
            </s-popover>
        </s-form-item>
                    <s-form-item class="filter-rule-item" prop="ruleGroupId" label="筛选条件：">
                        <div class="filter-rule-list">
                            <div class="tool-tip">
                                <s-button skin="primary" on-click="addRule">
                                    <outlined-plus />添加筛选条件
                                </s-button>
                                <div class="search-wrap">
                                    <s-search
                                        width="150"
                                        value="{=searchbox.keyword=}"
                                        placeholder="{{searchbox.placeholder}}"
                                        on-search="onSearch"
                                    >
                                        <s-select
                                            slot="options"
                                            width="100"
                                            datasource="{{searchbox.keywordTypes}}"
                                            value="{=searchbox.keywordType=}"
                                            on-change="onSearchboxChange"
                                        >
                                        </s-select>
                                    </s-search>
                                    <s-button class="button-margin-left" on-click="refresh"
                                        ><outlined-refresh
                                    /></s-button>
                                </div>
                            </div>
                            <s-table
                                s-ref="table"
                                columns="{{table.columns}}"
                                loading="{{table.loading}}"
                                error="{{table.error}}"
                                datasource="{{table.datasource}}"
                                selection="{=table.selection=}"
                                on-selected-change="tableSelected($event)"
                            >
                                <template slot="c-name">
                                    <s-button on-click="openDrawer(row)" skin="stringfy">{{row.name}}</s-button>
                                </template>
                            </s-table>
                            <s-pagination
                                s-if="pager.total"
                                layout="{{'pageSize, pager'}}"
                                pageSize="{{pager.size}}"
                                total="{{pager.total}}"
                                page="{{pager.page}}"
                                resetPageWhenSizeChange="{{true}}"
                                on-pagerChange="onPagerChange"
                            />
                        </div>
                    </s-form-item>
                    <s-form-item label="{{'目的类型：'}}" prop="destType" class="share-wrapper">
                    <s-radio-radio-group
                        enhanced
                        s-if="{{!sessionId}}"
                        datasource="{{destTypeSource}}"
                        disabled="{{sessionId}}"
                        value="{=formData.destType=}"
                        radioType="button"
                        on-change="destChange"
                    />
                    <p class="mirror-dest-tip" s-if="{{!sessionId}}">
                        <span class="value">{{'确保镜像源到选择的镜像目的UDP协议4789端口的网络联通性'}}</span>
                    </p>
                  <span class="text-wrapper" s-else>{{formData.destType | getDestTypeDisplay}}</span>
                    </s-form-item>
                    <!--<div s-if="{{formData.destType === 'BLB'}}" class="source-id-wrapper">
                        <s-form-item
                            label="镜像目的："
                            prop="destId">
                            <s-select
                            width="{{300}}"
                            placeholder="请选择负载均衡"
                            filterable
                            datasource="{{mirrorDest}}"
                            value="{=formData.destId=}"
                            />
                        </s-form-item>
                    </div>-->
                    <div class="source-id-wrapper">
                        <s-form-item s-if="{{!sessionId}}" label="镜像目的：" prop="destVpc">
                            <s-select
                                width="{{300}}"
                                filterable
                                placeholder="请选择镜像目的所在VPC"
                                datasource="{{originVpcs}}"
                                value="{=formData.destVpc=}"
                                on-change="destVpcChange"
                            />
                        </s-form-item>
                        <s-form-item label="{{sessionId ? '镜像目的：' : ''}}" prop="destId" class="share-wrapper">
                        <s-select
                        s-if="{{!sessionId}}"
                        width="{{300}}"
                        placeholder="{{destPlaceholder}}"
                        filterable
                        disabled="{{destSwitch || sessionId}}"
                        datasource="{{mirrorDest}}"
                        value="{=formData.destId=}"
                        on-change="destIdChange"
                      />
                      <span s-else class="text-wrapper">{{formData.destId}}</span>
                        </s-form-item>
                    </div>
                    <s-form-item s-if="showDestNetCard && !sessionId" prop="destNetworkCardId" label=" ">
            <s-select
                width="{{300}}"
                filterable
                disabled="{{networkCardLoading}}"
                placeholder="{{'请选择' + networkCardType}}"
                datasource="{{destNetworkCardDatasource}}"
                value="{=formData.destNetworkCardId=}"
            >
            </s-select>
            <s-popover s-if="formData.destType === 'BCC'" placement="top">
                <div slot="content">
                    {{'选择的云服务器镜像源仅包含云服务器的主网卡，不包含云服务器挂载的弹性网卡。'}}
                </div>
                <s-icon class="tip-icon-wrap" name="warning-mark"/>
            </s-popover>
        </s-form-item>
                    <s-form-item
                        label="{{'指定VNI：'}}"
                        help="通过VNI来区分不同的镜像流量。如果未指定，将随机分配VNI"
                        prop="vni"
                    >
                        <s-input placeholder="请输入0-16777215之间的整数" width="{{300}}" value="{=formData.vni=}" />
                    </s-form-item>
                    <s-form-item
                        label="{{'数据包长度：'}}"
                        help="镜像的每个数据包中的字节数，如果未指定，将镜像整个数据包"
                        prop="packetLength"
                    >
                        <s-input
                            placeholder="请输入100-1500之间的整数"
                            width="{{300}}"
                            value="{=formData.packetLength=}"
                        />
                        <span>字节</span>
                    </s-form-item>
                    <s-form-item label="{{'描述：'}}">
                        <s-input-text-area
                            width="{{300}}"
                            maxLength="{{200}}"
                            height="{{100}}"
                            value="{=formData.description=}"
                        />
                    </s-form-item>
                </div>
            </s-form>
            <div slot="pageFooter" class="buybucket">
                <div class="buybucket-container">
                    <s-tooltip placement="top" trigger="{{mirrowSinDisable.disable ? 'hover' : ''}}">
                        <div slot="content">{{mirrowSinDisable.message}}</div>
                        <s-button
                            width="{{90}}"
                            height="{{40}}"
                            disabled="{{mirrowSinDisable.disable || confirmed}}"
                            skin="primary"
                            on-click="onCreate"
                            data-test-id="${testID.flowMirror.submitButton}"
                        >
                            确定
                        </s-button>
                    </s-tooltip>
                    <s-button width="{{90}}" height="{{40}}" on-click="cancel"> 取消 </s-button>
                </div>
            </div>
            <s-drawer
                class="rule-filter"
                open="{=open=}"
                title="筛选条件详情"
                direction="right"
                size="{{800}}"
                on-close="drawClose"
                otherClose="{{false}}"
            >
                <rule-filter-detail s-if="{{ruleGroupId}}" ruleGroupId="{{ruleGroupId}}" />
            </s-drawer>
        </s-app-create-page>
    </template>
`;

@template(tpl)
@asComponent('@mirror-create')
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@invokeComp('@rule-filter-detail')
class MirrorCreate extends CreatePage {
    static components = {
        's-textarea': Input.TextArea,
        's-radio-group': Radio.RadioGroup,
        'outlined-refresh': OutlinedRefresh,
        'rule-filter-detail': RuleFilterDetail,
        'outlined-plus': OutlinedPlus
    };

    static messages = {
        upDataDetail() {
            this.loadRuleList();
        }
    };
    static filters = {
        getOriginTypeDisplay(sourceType) {
            return mirrorSourceType.getTextFromValue(sourceType.toUpperCase()) || '';
        },

        getDestTypeDisplay(destType) {
            return mirrorDestType.getTextFromValue(destType.toUpperCase()) || '';
        },
        noOpenTip(value: string) {
            const isSubUser = window.$context.isSubUser();
            let serviceText = this.data.get('sourceTypeSource').find((item: any) => item.value === value)?.text;
            let url = serviceTypeUrl[value];
            let str = `您的账号当前未开通${serviceText}服务，请前往${serviceText}控制台开通。<a href="${url}">去开通</a>`;
            if (isSubUser) {
                str = `该功能需要开通${serviceText}服务，当前登录的子账号没有开通产品服务的权限，请联系主账号授予开通服务权限，或联系主账户开通服务后授权使用。`;
            }
            return str;
        }
    };

    static computed = {
        showNetCard() {
            return ['BCC', 'ENI'].includes(this.data.get('formData.sourceType'));
        },
        showDestNetCard() {
            return ['BCC', 'ENI'].includes(this.data.get('formData.destType'));
        },
        mirrorOriginDisplay() {
            const sourceType = this.data.get('formData.sourceType');
            const formData = this.data.get('formData');
            let text = '';
            if (sourceType === 'CSN') {
                text = formData.sourceVpc + '：' + formData.sourceId;
            } else {
                text = formData.sourceId;
            }
            return text;
        }
    };

    initData() {
        return {
            FLAG,
            pageNav: {
                title: '创建镜像会话',
                backUrl: '/network/#/vpc/mirror/list',
                backLabel: '返回'
            },
            rules: formValidator(this),
            region: ContextService.getCurrentRegionId(),
            payType: 'postpay',
            formData: {
                name: '',
                sourceType: 'EIP',
                destType: 'BLB',
                ruleGroupId: '',
                sourceVpc: '',
                destVpc: '',
                sourceId: '',
                description: '',
                vni: '',
                packetLength: '',
                networkCardId: ''
            },
            searchbox: {
                keywordTypes: [
                    {text: '实例名称', value: 'name', placeholder: '请输入实例名称进行搜索'},
                    {text: '实例ID', value: 'rule_group_id', placeholder: '请输入实例ID进行搜索'}
                ],
                placeholder: '请输入实例名称进行搜索',
                keywordType: 'name',
                keyword: ''
            },
            table: {
                loading: false,
                selection: {
                    mode: 'single',
                    selectedIndex: []
                },
                columns: [
                    {
                        name: 'name',
                        label: '实例名称/ID',
                        minWidth: 120
                    },
                    {
                        name: 'description',
                        label: '描述',
                        minWidth: 160
                    }
                ],
                datasource: []
            },
            pager: {
                size: 10,
                page: 1,
                total: 0
            },
            vpcs: [],
            mirrorSource: [],
            mirrorDest: [],
            payTypeSource: [
                {
                    label: PayType.getTextFromValue('postpay'),
                    value: PayType.POSTPAY
                }
            ],
            sourceTypeSource: [],
            destTypeSource: [],
            regionSource: [],
            open: false,
            mirrowSinDisable: {},
            sourcePlaceholder: '请选择镜像源',
            destPlaceholder: '请选择镜像目的',
            loadNeed: false,
            networkCardType: '云服务器',
            networkCardLoading: false,
            networkCardDatasource: [],
            destNetworkCardDatasource: [],
            urlQuery: parseQuery(location.hash)
        };
    }

    attached() {
        if (this.data.get('requestNeed')) {
            return;
        }
    }

    inited() {
        this.setSourceDestTypeSource();
        if (window.$context.getCurrentRegionId() === AllRegion.SIN) {
            this.data.set('mirrowSinDisable.disable', true);
            this.data.set('mirrowSinDisable.message', '新加坡地域资源售罄，请您切换到其他地域创建');
        }
        if (this.data.get('requestNeed')) {
            return;
        }
        this.setRegionSource();
        this.data.set('sessionId', this.data.get('urlQuery').sessionId);
        if (this.data.get('sessionId')) {
            this.data.set('pageNav.title', '编辑镜像会话');
            this.getSessionInfo();
        } else {
            this.getVpcList();
            this.loadRuleList();
            this.initMirrorList();
        }
        this.getBlbQuota();
    }

    setSourceDestTypeSource() {
        const whiteList: Record<string, boolean> = (window as any).$storage.get('commonWhite');
        let originSourceDatasource: Array<Record<string, string>> = mirrorSourceType.toArray();
        let originDestDatasource: Array<Record<string, string>> = mirrorDestType.toArray();
        if (!whiteList?.mirrorWhiteList) {
            const mirrorBlackKeyMap = ['BCC', 'ENI'];
            originSourceDatasource = originSourceDatasource.filter(it => !mirrorBlackKeyMap.includes(it.value));
            // 只控制源类型，不控制目的 https://console.cloud.baidu-int.com/devops/icafe/issue/BCE-JC-31019/show?source=issue-title
            // originDestDatasource = originDestDatasource.filter(it => !mirrorBlackKeyMap.includes(it.value));
        }
        originSourceDatasource = checkSts(originSourceDatasource);
        (this as any).data.set('sourceTypeSource', originSourceDatasource);
        (this as any).data.set('destTypeSource', originDestDatasource);
    }

    getBlbQuota() {
        this.$http
            .getMirrorQuota({
                quotaTypes: 'sessionNumberPerBlbQuota'
            })
            .then(res => {
                this.data.set('blbQuota', res.quotaType2quota?.sessionNumberPerBlbQuota);
            });
    }

    addRule() {
        location.hash = '#/vpc/filterRuleGroup/create';
    }

    initMirrorList() {
        if (this.data.get('sourceTypeSource')[0].disabled) {
            return;
        }
        this.loadMirrorList('EIP', 'source').then(() => {
            this.mirrorSourceFilter('EIP');
        });
        this.loadMirrorList('BLB', 'dest');
    }

    // 编辑时默认搜索已有筛选条件id
    setRuleSearch(info) {
        this.onSearch().then(res => {
            if (!!res?.length) {
                const selectedIdx = res.findIndex(item => item.ruleGroupId === info.ruleGroupId);
                this.data.set('table.selection.selectedIndex', [selectedIdx]);
            }
        });
    }

    getSessionInfo() {
        let sessionId = this.data.get('sessionId');
        this.$http.getMirrorSessionDetail(sessionId).then(res => {
            if (res.sourceType === 'csn') {
                this.getCsnDetail(res.showSourceId);
            }
            let formData = {
                name: res.name,
                description: res.description,
                sourceType: res.sourceType.toUpperCase(),
                sourceId: res.sourceId,
                destType: res.destType.toUpperCase(),
                destId: res.destId,
                ruleGroupId: res.ruleGroupId,
                vni: res.vni,
                packetLength: res.packetLength || ''
            };
            // todo 优化 直接赋值会是引用地址改变导致校验不通过
            this.data.set('formData.name', formData.name);
            this.data.set('formData.description', formData.description);
            this.data.set('formData.sourceType', formData.sourceType);
            this.data.set('formData.sourceId', formData.sourceId);
            this.data.set('formData.destType', formData.destType);
            this.data.set('formData.destId', formData.destId);
            this.data.set('formData.ruleGroupId', formData.ruleGroupId);
            this.data.set('formData.vni', formData.vni);
            this.data.set('formData.packetLength', formData.packetLength);
            this.setRuleSearch(res);
        });
    }

    async getVpcList() {
        const data = await this.$http.vpcList();
        const vpcs = u.map(data, item => ({
            text: `${item.name}（${item.cidr}）`,
            value: item.vpcId,
            shortId: item.shortId
        }));
        this.data.set('vpcs', vpcs);
        this.data.set('originVpcs', vpcs);
        this.setInitData(vpcs);
    }

    dealPort(port) {
        if (port.begin === port.end) {
            return port.begin;
        }
        return `${port.begin}-${port.end}`;
    }

    openDrawer(row) {
        this.data.set('open', true);
        this.data.set('ruleGroupId', row.ruleGroupId);
    }

    drawClose() {
        this.data.set('ruleGroupId', '');
    }

    setInitData(vpcs) {
        const vpcId = this.data.get('urlQuery.vpcId');
        if (u.find(vpcs, {value: vpcId})) {
            this.data.set('formData.vpcId', vpcId);
        } else if (vpcs.length) {
            this.data.set('formData.vpcId', vpcs[0].value);
        }
    }

    tableSelected({value}) {
        let {selectedItems} = value;
        if (selectedItems.length) {
            this.data.set('formData.ruleGroupId', selectedItems[0].ruleGroupId);
        }
    }

    // 每个eip、nat只能创建一个会话，过滤掉已经创建过的eip
    mirrorSourceFilter(keyword) {
        this.$http
            .getMirrorSessionList({
                keyword: keyword,
                keywordType: 'source_type',
                pageNo: 1,
                pageSize: 10000
            })
            .then(res => {
                let allSourceList = this.data.get('mirrorSource');
                let createdSourceList = res.result;
                this.data.set(
                    'mirrorSource',
                    u.filter(allSourceList, item => {
                        return !u.find(createdSourceList, list => list.sourceId === item.value);
                    })
                );
            });
    }
    async sourceChange(e: any) {
        this.data.set('formData.sourceType', e.value);
        this.data.set('formData.sourceVpc', '');
        this.data.set('formData.sourceId', '');
        this.data.set('formData.networkCardId', '');
        if (e.value === 'EIP') {
            this.loadMirrorList('EIP', 'source').then(() => {
                this.mirrorSourceFilter('EIP');
            });
        }
        if (e.value === 'NAT') {
            this.data.set('mirrorSource', []);
            this.data.set('sourceSwitch', true);
            this.data.set('sourcePlaceholder', '请选择增强型NAT网关');
            this.data.set('vpcs', this.data.get('originVpcs'));
        } else if (e.value === 'CSN') {
            this.data.set('sourcePlaceholder', '请选择私有网络');
            const csnList = await this.getCsnList();
            this.data.set('vpcs', csnList);
        } else {
            if (['BCC', 'ENI'].includes(e.value)) {
                this.data.set('sourcePlaceholder', '请选择所在子网');
                this.data.set('mirrorSource', []);
                this.data.set('networkCardDatasource', []);
                const sourceTypeMapping = {
                    BCC: '云服务器',
                    ENI: '弹性网卡'
                };
                this.data.set('networkCardType', sourceTypeMapping[e.value]);
            } else {
                this.data.set('sourcePlaceholder', '请选择镜像源');
            }
            this.data.set('vpcs', this.data.get('originVpcs'));
        }
    }
    loadSubnets(mirrorType: 'source' | 'dest') {
        const vpcId = mirrorType === 'source' ? this.data.get('formData.sourceVpc') : this.data.get('formData.destVpc');
        const payload = {
            vpcId,
            pageNo: 1,
            pageSize: 1000
        };
        return this.$http.vpcSubnetPageList(payload).then((res: any) => {
            let datasource = [];
            u.each(res.result || [], item =>
                datasource.push({
                    value: item.subnetId,
                    text: item.name + (item.cidr ? '（' + item.cidr + '）' : ''),
                    az: item.az
                })
            );
            return Promise.resolve(datasource);
        });
    }

    destChange(e) {
        this.data.set('formData.destType', e.value);
        this.data.set('formData.destVpc', '');
        this.data.set('formData.destId', '');
        this.data.set('formData.destNetworkCardId', '');
        this.data.set('destNetworkCardDatasource', []);
        if (['BCC', 'ENI'].includes(e.value)) {
            this.data.set('destPlaceholder', '请选择所在子网');
            this.data.set('mirrorDest', []);
            this.data.set('destId', '');
            const sourceTypeMapping = {
                BCC: '云服务器',
                ENI: '弹性网卡'
            };
            this.data.set('networkCardType', sourceTypeMapping[e.value]);
        }
    }

    // 当前所选lb创建了多少条
    getBlbNum(val) {
        this.$http
            .getMirrorSessionList({
                keyword: val,
                keywordType: 'dest_id',
                pageNo: 1,
                pageSize: 10000
            })
            .then(res => {
                this.data.set('blbNum', res.totalCount);
            });
    }

    destIdChange(e) {
        const type = this.data.get('formData.destType');
        this.data.set('formData.destId', e.value);
        this.data.set('formData.destNetworkCardId', '');
        // 拉取云服务器、弹性网卡列表
        if (['BCC', 'ENI'].includes(type)) {
            if (type === 'BCC') {
                this.loadEnableResource(e.value, 'dest');
            } else {
                this.getEniList('dest');
            }
        } else {
            // 配额判断
            this.getBlbNum(e.value);
        }
    }

    sourceVpcChange(e) {
        this.data.set('formData.sourceId', '');
        this.data.set('formData.sourceVpc', e.value);
        let type = this.data.get('formData.sourceType');
        if (['BCC', 'ENI'].includes(type)) {
            this.data.set('formData.networkCardId', '');
        }
        if (['NAT', 'CSN', 'PEERCONN', 'DCGW'].includes(type)) {
            this.loadMirrorList(type, 'source').then(() => {
                this.mirrorSourceFilter(type);
            });
        } else {
            this.loadMirrorList(type, 'source');
        }
        if (type === 'CSN') {
            this.data.set('showSourceId', e.value);
        }
    }
    loadEnableResource(id: string, mirrorType: 'source' | 'dest') {
        this.data.set('networkCardLoading', true);
        this.$http
            .getBccListBySubnet({subnetUuids: id})
            .then(res => {
                const formatedRes = u.map(res, item => {
                    return {
                        text: `${item.name}(${item.instanceId})`,
                        value: item.instanceId
                    };
                });
                if (mirrorType === 'source') {
                    this.data.set('networkCardDatasource', formatedRes || []);
                } else {
                    this.data.set('destNetworkCardDatasource', formatedRes || []);
                }
            })
            .catch(e => {
                if (mirrorType === 'source') {
                    this.data.set('networkCardDatasource', []);
                } else {
                    this.data.set('destNetworkCardDatasource', []);
                }
            })
            .finally(() => {
                this.data.set('networkCardLoading', false);
            });
    }

    sourceIdChange(e) {
        let type = this.data.get('formData.sourceType');
        let pcList = this.data.get('pcList');
        if (type === 'PEERCONN') {
            let obj = pcList.find(item => item.value === e.value);
            let showSourceId = obj.id;
            this.data.set('showSourceId', showSourceId);
        }
        this.data.set('formData.networkCardId', '');
        // 拉取云服务器、弹性网卡列表
        if (['BCC', 'ENI'].includes(type)) {
            if (type === 'BCC') {
                this.loadEnableResource(e.value, 'source');
            } else {
                this.getEniList('source');
            }
        }
    }

    destVpcChange(e) {
        this.data.set('formData.destVpc', e.value);
        let type = this.data.get('formData.destType');
        if (['BCC', 'ENI'].includes(type)) {
            this.data.set('formData.destId', '');
            this.data.set('formData.destNetworkCardId', '');
        }
        this.loadMirrorList(type, 'dest');
    }

    getEipList() {
        let payload = {
            pageNo: 1,
            pageSize: 1000
        };
        return this.$http.getEipList(payload).then(data => {
            let result = [];
            u.each(data.result, item => {
                if (item.status === 'available' || item.status === 'binded' || item.status === 'bound') {
                    result.push({
                        text: `${item.name}（${item.eip}）`,
                        value: item.eip
                    });
                }
            });
            return Promise.resolve(result);
        });
    }

    getGwList(mirrorType) {
        let query = {
            vpcId: mirrorType === 'source' ? this.data.get('formData.sourceVpc') : this.data.get('formData.destVpc'),
            pageNo: 1,
            pageSize: 10000
        };
        return this.$http.dcgwList(query).then(res => {
            let result = [];
            u.each(res.result, item => {
                if (u.indexOf([DcGatewayStatus.RUNNING], item.status) > -1) {
                    let name = item.name + '/' + item.id;
                    result.push({
                        value: item.id,
                        text: name,
                        disabled: item?.speed / 1000 > 10
                    });
                }
            });
            return Promise.resolve(result);
        });
    }

    getPcList(mirrorType) {
        let vpcs = this.data.get('originVpcs');
        let shortId = vpcs.find(item => item.value === this.data.get('formData.sourceVpc'))?.shortId;
        let query = {
            vpcId: mirrorType === 'source' ? this.data.get('formData.sourceVpc') : this.data.get('formData.destVpc'),
            pageNo: 1,
            pageSize: 10000,
            localVpcShortId: shortId
        };
        return this.$http.peerconnList(query).then(res => {
            let result = [];
            u.each(res.result, item => {
                if (u.indexOf([PeerConnStatus.ACTIVE], item.status) > -1 && item.peerRegion !== item.localRegion) {
                    let name = item.localIfName + '/' + item.localIfId;
                    result.push({
                        value: item.localIfId,
                        text: name,
                        id: item.peerConnId,
                        disabled: item?.bandwidth / 1000 > 10
                    });
                }
            });
            this.data.set('pcList', result);
            return Promise.resolve(result);
        });
    }

    getCsnList() {
        let payload = {
            pageNo: 1,
            pageSize: 10000
        };
        return this.$http
            .csnList(urlSerialize(payload), kXhrOptions.customSilent)
            .then(data => {
                let result = [];
                if (data) {
                    u.each(data.result, item => {
                        result.push({
                            value: item.csnId,
                            text: item.name
                        });
                    });
                }
                return result;
            })
            .catch(e => {
                return Promise.resolve([]);
            });
    }

    getEniList(mirrorType) {
        let query = {
            vpcId: mirrorType === 'source' ? this.data.get('formData.sourceVpc') : this.data.get('formData.destVpc'),
            pageNo: 1,
            pageSize: 10000
        };
        return this.$http.getEniList(query).then(res => {
            let result = [];
            u.each(res.result, item => {
                // if (u.indexOf([EniStatus.AVAILABLE], item.status) > -1) {
                result.push({
                    text: `${item.name}（${item.eniId}）`,
                    value: item.eniId
                });
                // }
            });
            if (mirrorType === 'source') {
                this.data.set('networkCardDatasource', result);
            } else {
                this.data.set('destNetworkCardDatasource', result);
            }
        });
    }

    getNatList(mirrorType) {
        let query = {
            vpcId: mirrorType === 'source' ? this.data.get('formData.sourceVpc') : this.data.get('formData.destVpc'),
            pageNo: 1,
            pageSize: 10000
        };
        return this.$http.getNatList(query).then(res => {
            let result = [];
            u.each(res.result, item => {
                if (item.clusterMode) {
                    result.push({
                        text: `${item.name}（${item.id}）`,
                        value: item.id
                    });
                }
            });
            return Promise.resolve(result);
        });
    }

    getCsnVpcs(mirrorType) {
        let query = {
            csnId: mirrorType === 'source' ? this.data.get('formData.sourceVpc') : this.data.get('formData.destVpc'),
            pageNo: 1,
            pageSize: 10000,
            instanceType: 'vpc'
        };
        if (!query.csnId || !window.$storage.get('csnSts')) {
            return;
        }
        return this.$http.csnInstanceList(query).then(res => {
            let result = [];
            // 筛选出当前region下的vpc
            const region = window.$context.getCurrentRegionId();
            const inCurrentRegionVpcs = u.filter(res.result || [], item => item.region === region);
            u.each(inCurrentRegionVpcs, item => {
                result.push({
                    text: `${item.instanceName}（${item.instanceShortId}）`,
                    value: item.instanceShortId,
                    disabled: item?.bandwidth / 1000 > 10
                });
            });
            return Promise.resolve(result);
        });
    }

    loadMirrorList(value, mirrorType) {
        let type = value;
        const requesetMap = {
            EIP: this.getEipList,
            // ENI: this.getEniList,
            DCGW: this.getGwList,
            PEERCONN: this.getPcList,
            BLB: this.getBlbList,
            NAT: this.getNatList,
            CSN: this.getCsnVpcs,
            BCC: this.loadSubnets,
            ENI: this.loadSubnets
        };
        if (!requesetMap[type]) {
            return;
        }
        if (mirrorType === 'source') {
            this.data.set('sourceSwitch', true);
        } else {
            this.data.set('destSwitch', true);
        }
        return requesetMap[type].call(this, mirrorType).then(res => {
            if (mirrorType === 'source') {
                this.data.set('mirrorSource', res);
                this.data.set('sourceSwitch', false);
            } else {
                this.data.set('mirrorDest', res);
                this.data.set('destSwitch', false);
            }
            return Promise.resolve();
        });
    }

    getBlbList(mirrorType) {
        // 不支持应用类型的负载均衡
        this.data.set('formData.lbPort', '');
        let param = {
            vpcId: mirrorType === 'source' ? this.data.get('formData.sourceVpc') : this.data.get('formData.destVpc')
        };
        return this.$http.getFlowlogBlbList(param).then(res => {
            let result = [];
            u.each(res.allBlbVOList, item => {
                if (
                    item.status === 'available' &&
                    item.blbType !== 'application' &&
                    item.blbType !== 'ipv6Application'
                ) {
                    result.push({
                        text: `${item.name}（${item.blbShortId}）`,
                        value: item.blbShortId
                    });
                }
            });
            return Promise.resolve(result);
        });
    }

    setRegionSource() {
        const region = ContextService.getCurrentRegionId();
        this.data.set('regionSource', getVPCSupportRegion(window));
        this.data.set('region', region);
    }

    resetTable() {
        this.data.set('table.selection.selectedIndex', []);
    }

    getSearchCriteria() {
        const pager = this.data.get('pager');
        let searchParam = {
            keyword: this.data.get('searchbox.keyword'),
            keywordType: this.data.get('searchbox.keywordType')
        };
        return u.extend({}, searchParam, {pageNo: pager.page, pageSize: pager.size});
    }

    loadRuleList() {
        this.data.set('table.loading', true);
        this.resetTable();
        let payload = this.getSearchCriteria();
        return this.$http.getFilterRuleList(payload).then(data => {
            this.data.set('table.datasource', data.result);
            this.data.set('pager.total', data.totalCount);
            this.data.set('table.loading', false);
            return data.result;
        });
    }

    onPagerChange({value}) {
        this.data.set('pager.page', value.page);
        this.data.set('pager.pageSize', value.pageSize);
        this.loadRuleList();
    }

    onSearchboxChange(e) {
        this.data.set('searchbox.keywordType', e.value);
        this.data.set('searchbox.keyword', '');
        let data = u.find(this.data.get('searchbox.keywordTypes'), item => item.value === e.value);
        if (data.placeholder) {
            this.data.set('searchbox.placeholder', data.placeholder);
        }
    }

    onSearch() {
        this.data.set('pager.page', 1);
        return this.loadRuleList();
    }

    refresh() {
        return this.loadRuleList();
    }

    async onCreate() {
        let sessionId = this.data.get('sessionId');
        await this.ref('form').validateFields();
        this.data.set('confirmed', true);
        let formData = this.data.get('formData');
        if (sessionId) {
            let payload = {
                name: formData.name,
                description: formData.description,
                ruleGroupId: formData.ruleGroupId,
                vni: formData.vni,
                packetLength: formData.packetLength
            };
            this.$http
                .updateMirrorSession(sessionId, payload)
                .then(() => {
                    this.data.set('confirmed', false);
                    location.hash = '#/vpc/mirror/list';
                })
                .catch(() => {
                    this.data.set('confirmed', false);
                });
        } else {
            const sourceId = ['BCC', 'ENI'].includes(formData.sourceType) ? formData.networkCardId : formData.sourceId;
            const destId = ['BCC', 'ENI'].includes(formData.destType) ? formData.destNetworkCardId : formData.destId;
            let payload = {
                name: formData.name,
                description: formData.description,
                sourceType: formData.sourceType,
                sourceId,
                destType: formData.destType,
                destId,
                ruleGroupId: formData.ruleGroupId,
                vni: formData.vni,
                packetLength: formData.packetLength
            };
            if (payload.sourceType === 'CSN' || payload.sourceType === 'PEERCONN') {
                payload.showSourceId = this.data.get('showSourceId');
            }
            this.$http
                .createMirrorSession(payload)
                .then(() => {
                    this.data.set('confirmed', false);
                    location.hash = '#/vpc/mirror/list';
                })
                .catch(() => {
                    this.data.set('confirmed', false);
                });
        }
    }

    cancel() {
        location.hash = '#/vpc/mirror/list';
    }

    getNatDetail(sourceId) {
        this.$http.getNatList({natGatewayId: sourceId}).then(res => {
            this.data.set('formData.sourceVpc', res.result[0].vpcShortId);
        });
    }
    getDcgwDetail(sourceId) {
        this.$http.dcgwDetail({dcgwId: sourceId}).then(res => {
            this.data.set('formData.sourceVpc', res.vpcShortId);
        });
    }
    getPcDetail(sourceId) {
        this.$http.getPeerDetail({localIfId: sourceId}).then(res => {
            this.data.set('formData.sourceVpc', res.localVpcShortId);
        });
    }
    getCsnDetail(sourceId) {
        this.data.set('formData.sourceVpc', sourceId);
    }
    // 切换地域
    onRegionChange(e) {
        let value = e.value || e.id;
        if (!value || value === window.$context.getCurrentRegionId()) {
            return;
        }
        this.data.set('region', value);
        window.$context.setRegion(value);
        this.data.set('loadNeed', true);
    }
}
export default San2React(Processor.autowireUnCheckCmpt(MirrorCreate));
