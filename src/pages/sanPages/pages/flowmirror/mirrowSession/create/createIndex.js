import _ from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import './create';
const {asPage, template, invokeSUI, invokeAppComp, invokeComp} = decorators;

const tpl = html`
    <template>
        <div s-if="!indexLoading" style="width:100%" class="index-loading-class">
            <s-loading loading style="width:100%">
                <mirror-create
                    requestNeed="{{requestNeed}}"
                    sessionId="{{sessionId}}"
                    class="mirror-create-component"
                />
            </s-loading>
        </div>
        <mirror-create
            s-if="indexLoading"
            s-ref="vpcFlowmirror"
            sessionId="{{sessionId}}"
            class="mirror-create-component"
        />
    </template>
`;

@invokeComp('@mirror-create')
@template(tpl)
@invokeSUI
@invokeAppComp
class CreateIndex extends Component {
    initData() {
        return {
            indexLoading: true,
            requestNeed: true,
            sessionId: '',
            urlQuery: getQueryParams()
        };
    }

    inited() {
        this.data.get('urlQuery.sessionId') && this.data.set('sessionId', this.data.get('urlQuery.sessionId'));
        window.$framework.events.on(window.$framework.EVENTS.AFTER_REGION_CHANGED, () => {
            let vpcFlowmirror = this.ref('vpcFlowmirror')?.data;
            const currentRegion = window.$context.getCurrentRegionId();
            if (currentRegion === vpcFlowmirror?.get('region') && !vpcFlowmirror?.get('loadNeed')) {
                return;
            }
            this.data.set('indexLoading', false);
            this.nextTick(() => {
                // 等一会再执行
                setTimeout(() => {
                    this.data.set('indexLoading', true);
                }, 100);
            });
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(CreateIndex));
