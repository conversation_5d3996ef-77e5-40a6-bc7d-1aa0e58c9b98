.vpc-probe-detail {
    .s-detail-page-title {
        display: none;
    }
    .s-detail-page-content {
        margin: 0px;
        padding: 24px;
        h2 {
            font-size: 16px;
            color: #151b26;
            line-height: 24px;
            font-weight: 500;
            margin-bottom: 16px;
        }
    }
    .s-icon {
        font-size: 12px;
        color: #2468f2;
    }
    li.content-item {
        margin-bottom: 16px;
        width: 33%;
        display: inline-block;
        vertical-align: top;
        label {
            display: inline-block;
            margin-right: 16px;
            width: 72px;
            color: #666;
        }
        .probe-domain-desc {
            width: 200px;
            word-break: break-all;
            display: block;
            float: left;
            margin-left: 5px;
        }
        .probe-description {
            float: left;
            word-break: normal;
            display: block;
            white-space: pre-wrap;
            overflow: auto;
        }
        .edit-detail-pop {
            display: inline;
            margin-left: 5px;
        }
    }
    .instance-not-found-class {
        height: 100%;
    }
    .vpn-detail-header {
        display: flex;
        align-items: center;
    }
    .instance-name {
        font-weight: 500;
        color: #151b26;
        padding-right: 16px;
        font-size: 16px;
    }
}
.content-label {
    float: left;
}

.instance-no-found-content {
    .s-detail-page-title {
        display: none;
    }
    .s-detail-page-content {
        margin: 0 !important;
    }
}

.loading-no-found-content {
    .s-detail-page-content {
        margin: 0 !important;
    }
}
