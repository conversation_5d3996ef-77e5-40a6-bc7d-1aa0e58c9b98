/*
 * @Description: 网络探测详情页
 * @Author: wang<PERSON><EMAIL>
 * @Date: 2022-03-04 15:40:09
 */

import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {OutlinedEditingSquare} from '@baidu/sui-icon';

import {ProbeStatus, RouteType} from '@/pages/sanPages/common/enum';
import './detail.less';
import '../list/list.less';
import {kXhrOptions} from '@/pages/sanPages/utils/helper';

const {asPage, invokeSUI, invokeSUIBIZ, template, withSidebar, invokeAppComp, invokeComp} = decorators;

const tpl = html`
    <div>
        <s-app-detail-page class="{{klass}}">
            <h2>基本信息</h2>
            <ul class="probe-info-container">
                <li class="content-item">
                    <label>名称：</label>
                    <span>
                        {{ instance.name }}
                        <s-popover class="edit-popover-class" s-ref="{{'nameEdit'}}" placement="top" trigger="click">
                            <div class="edit-wrap" slot="content">
                                <s-input
                                    value="{=name=}"
                                    width="320"
                                    placeholder="请输入"
                                    on-input="onInput($event, 'name')"
                                />
                                <div class="edit-tip">
                                    大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65
                                </div>
                                <s-button
                                    skin="primary"
                                    s-ref="{{'nameEditBtn'}}"
                                    disabled="{{true}}"
                                    on-click="onEdit('name')"
                                    >确定</s-button
                                >
                                <s-button on-click="editCancel('name')">取消</s-button>
                            </div>
                            <outlined-editing-square on-click="beforeEdit('name')" color="#2468f2" />
                        </s-popover>
                    </span>
                </li>
                <li class="content-item">
                    <label>ID：</label>
                    <span>{{ instance.probeId }}</span>
                    <s-clip-board text="{{instance.probeId}}" successMessage="已复制到剪贴板" />
                </li>
                <li class="content-item">
                    <label>所在网络：</label>
                    <span>
                        <a href="#/vpc/instance/detail?vpcId={{vpcInfo.vpcId}}">
                            {{ vpcInfo.name }}
                            <span s-if="vpcInfo.cidr">
                                （{{ vpcInfo.cidr }}）{{ instance.vpcIpv6Cidr ? ('(' + instance.vpcIpv6Cidr + ')') : ''
                                }}
                            </span>
                        </a>
                    </span>
                </li>
                <li class="content-item">
                    <label>所在子网：</label>
                    <span>
                        <a href="#/vpc/subnet/detail?subnetId={{instance.subnetUuid}}">
                            {{ instance.subnetName }}
                            <span s-if="instance.subnetCidr">
                                （{{ instance.subnetCidr }}）{{ instance.subnetIpv6Cidr ? ('(' + instance.subnetIpv6Cidr
                                + ')') : '' }}
                            </span>
                        </a>
                    </span>
                </li>
                <li class="content-item">
                    <label>探测方式：</label>
                    <span>{{instance.protocol}}</span>
                </li>
                <li class="content-item" s-if="{{instance.protocol === 'UDP' || instance.protocol === 'DNS'}}">
                    <label class="content-label" s-if="{{instance.protocol==='UDP'}}">探测字符串：</label>
                    <label class="content-label" s-else-if="{{instance.protocol==='DNS'}}">探测域名：</label>
                    <div class="probe-domain-desc truncated">{{instance.payload}}</div>
                    <s-clip-board text="{{instance.payload}}" successMessage="已复制到剪贴板">
                        <s-icon name="copy" />
                    </s-clip-board>
                </li>
                <li class="content-item">
                    <label>探测频率：</label>
                    <span>{{instance.frequency}}次/分钟</span>
                </li>
                <li class="content-item" s-if="{{instance.protocol === 'ICMP'}}">
                    <label>探测包大小：</label>
                    <span>{{instance.packetLen}}字节</span>
                </li>
                <li class="content-item">
                    <label>探测源IP：</label>
                    <span>{{instance.sourceIps}}</span>
                </li>
                <li class="content-item">
                    <label>下一跳类型：</label>
                    <span>{{instance.nextHopType | routeText}}</span>
                </li>
                <li class="content-item">
                    <label>下一跳实例：</label>
                    <span>{{instance.nextHop || '-'}}</span>
                </li>
                <li class="content-item">
                    <label>探测目的IP：</label>
                    <span s-if="{{instance.protocol !== 'ICMP'}}">{{instance.destIp}} : {{instance.destPort }}</span>
                    <span s-if="{{instance.protocol === 'ICMP'}}">{{instance.destIp}}</span>
                </li>
                <li class="content-item">
                    <label class="content-label">描述：</label>
                    <span class="probe-description">{{ instance.description || '-' }}</span>
                    <s-popover class="edit-popover-class" s-ref="{{'descriptionEdit'}}" placement="top" trigger="click">
                        <div class="edit-wrap" slot="content">
                            <s-input
                                value="{=description=}"
                                width="160"
                                placeholder="请输入"
                                on-input="onInput($event, 'description')"
                            />
                            <div class="edit-tip">描述不能超过200个字符</div>
                            <s-button
                                skin="primary"
                                s-ref="{{'descriptionEditBtn'}}"
                                disabled="{{true}}"
                                on-click="onEdit('description')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel('description')">取消</s-button>
                        </div>
                        <outlined-editing-square color="#2468f2" on-click="beforeEdit('description')" />
                    </s-popover>
                </li>
            </ul>
        </s-app-detail-page>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class ProbeDetail extends Component {
    static components = {
        'outlined-editing-square': OutlinedEditingSquare
    };
    initData() {
        return {
            klass: 'vpc-probe-detail',
            instance: {},
            urlQuery: getQueryParams()
        };
    }

    static filters = {
        statusStyle(value) {
            return ProbeStatus.fromValue(value).styleClass || '';
        },
        statusText(value) {
            return value ? ProbeStatus.getTextFromValue(value) : '-';
        },
        routeText(type) {
            return RouteType.getTextFromValue(type) || '-';
        }
    };

    attached() {
        this.loadDetail({'x-silent-codes': ['ProbeIdInvalidException']});
        this.loadVpcDetail();
    }

    loadDetail(option = {}) {
        return this.$http.getProbeDetail(this.data.get('urlQuery.probeId'), option).then(data => {
            this.data.set('instance', data);
        });
    }

    loadVpcDetail() {
        const vpcId = this.data.get('urlQuery.vpcId');
        this.$http
            .getVpcDetail({
                vpcIds: [vpcId]
            })
            .then(data => {
                const result = data[vpcId] || {};
                this.data.set('vpcInfo', result);
            });
    }

    beforeEdit(type) {
        const TYPE_MAP = {
            name: 'name',
            description: 'description'
        };
        const instance = this.data.get('instance');
        this.data.set(TYPE_MAP[type], instance[TYPE_MAP[type]]);
    }

    onRegionChange() {
        location.hash = '#/vpc/probe/list';
    }

    onInput(e, type) {
        let result;
        switch (type) {
            case 'name':
                const pattern = /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{0,64}$/;
                result = e.value.length <= 64 && pattern.test(e.value);
                break;
            case 'description':
                result = e.value.length <= 200;
                break;
        }
        this.ref(`${type}EditBtn`).data.set('disabled', !result);
    }

    onEdit(type) {
        const TYPE_MAP = {
            name: 'name',
            description: 'description'
        };
        let instance = this.data.get('instance');
        let value = this.data.get(TYPE_MAP[type]);
        this.$http
            .editProbe(instance.probeId, {
                [type]: value
            })
            .then(() => {
                this.editCancel(type);
                this.loadDetail();
                this.data.get('context')?.updateName();
            })
            .catch(() => {
                this.editCancel(type);
                this.loadDetail();
            });
    }

    editCancel(type) {
        this.ref(`${type}Edit`).data.set('visible', false);
    }
    onRegionChange() {
        location.hash = '#/vpc/probe/list';
    }
}
export default San2React(Processor.autowireUnCheckCmpt(ProbeDetail));
