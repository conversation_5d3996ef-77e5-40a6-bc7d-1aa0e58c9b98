.probe-list-wrap {
    flex: 1;
    .probe-search-input {
        .s-input-addon-before {
            padding: 0 !important;
            .s-input-suffix-container {
                border: none;
            }
        }
        .probe-search-select {
            position: relative;
            z-index: 100;
            .s-input-area {
                height: 100%;
                input {
                    width: 75px !important;
                }
            }
        }
        .s-icon {
            font-size: 14px;
        }
    }
    .s-table {
        .s-table-row:hover {
            .name-icon {
                display: inline;
            }
        }
        .s-table-body {
            max-height: calc(~'100vh - 334px');
        }
        .operations {
            .s-button {
                padding: 0;
                margin-right: 12px;
            }
        }
        .name-icon {
            display: none;
            font-size: 12px;
            fill: #2468f2;
        }
        .s-table-cell-description {
            .s-table-cell-text {
                word-break: break-all;
            }
        }
        .probe-id-widget {
            white-space: nowrap;
        }
    }
    .vpc-probe-header {
        display: flex;
        align-items: center;
        background-color: #ffffff;
        justify-content: space-between;
        .header-left {
            display: flex;
            align-items: center;
            .title {
                display: inline-block;
                margin: 0;
                color: #151b26;
                margin-right: 12px;
                height: 47px;
                line-height: 47px;
                font-weight: 500;
                font-size: 16px;
                margin-left: 16px;
            }
        }
        .header-right {
            display: flex;
            align-items: center;
            .help-file {
                margin-right: 16px;
                font-size: 12px;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                color: #151b26;
                line-height: 20px;
                &:hover {
                    color: #2468f2;
                }
                .s-icon {
                    position: relative;
                    top: -1px;
                    right: 4px;
                    margin-right: 0;
                    color: #2468f2;
                    font-size: 14px;
                }
            }
            .help-file-docs {
                color: #2468f2;
            }
            .function-introduce {
                color: #2468f2;
            }
        }
    }
}

.edit-wrap {
    .s-textarea .s-textarea-limit {
        background-color: #fff;
    }
}
