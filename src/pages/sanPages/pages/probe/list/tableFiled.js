export const columns = [
  {
    name: 'probeId',
    label: '探测名称/ID',
    width: 160,
    fixed: 'left'
  },
  {
    name: 'status',
    label: '状态',
    width: 90
  },
  {
    name: 'vpcId',
    label: '所在网络',
    width: 120,
  },
  {
    name: 'subnetId',
    label: '所在子网',
    width: 120,
  },
  {
    name: 'protocol',
    label: '探测方式',
    width: 120,
  },
  {
    name: 'frequency',
    label: '探测频率',
    width: 90,
  },
  {
    name: 'sourceIps',
    label: '探测源IP',
    width: 120,
  },
  {
    name: 'nextHopType',
    label: '下一跳类型',
    width: 90,
  },
  {
    name: 'nextHop',
    label: '下一跳实例',
    width: 120,
  },
  {
    name: 'destIp',
    label: '探测目的IP',
    width: 120,
  },
  {
    name: 'description',
    label: '描述',
    width: 140,
  },
  {
    name: 'opt',
    label: '操作',
    width: 140,
    fixed: 'right'
  },
];