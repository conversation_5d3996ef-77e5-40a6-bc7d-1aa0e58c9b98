/*
 * @Description: 网络探测列表页
 * @Author: <EMAIL>
 * @Date: 2022-02-28 11:14:01
 */

import {Component} from 'san';
import u from 'lodash';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {Notification} from '@baidu/sui';
import {OutlinedPlus, OutlinedEditingSquare} from '@baidu/sui-icon';
import {checker} from '@baiducloud/bce-opt-checker';

import {ProbeStatus, RouteType, ProbeSearchType, ProbeProtocol} from '@/pages/sanPages/common/enum';
import Confirm from '@/pages/sanPages/components/confirm';
import BcmDetail from '../components/bcmDetail';
import rules from '../rules';
import {StsConfig} from '@/pages/sanPages/utils/config';
import {isOnline} from '@/pages/sanPages/utils/common';
import {columns} from './tableFiled';
import './list.less';
import {ContextService, DocService} from '@/pages/sanPages/common';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';

import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import DOCUMENT_ICON from '@/img/document_normal.svg?url';
import DOCUMENT_ICON_COLOR from '@/img/document_color.svg?url';

const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, invokeComp} = decorators;

const tpl = html`
    <div>
        <s-app-list-page class="probe-list-wrap">
            <div slot="pageTitle">
                <div class="vpc-probe-header">
                    <div class="header-left">
                        <span class="title">网络探测</span>
                        <vpc-select class="vpc-select" on-int="loadPage" on-change="vpcChange" />
                    </div>
                    <div class="header-right">
                        <a
                            s-ref="introduce"
                            href="javascript:void(0)"
                            class="help-file function-introduce"
                            on-click="handleShowCard"
                            on-mouseenter="handleMouseEnter('introduce')"
                            on-mouseleave="handleMouseLeave('introduce')"
                            s-if="{{!FLAG.NetworkSupportXS}}"
                        >
                            <img class="s-icon" src="{{introduceIcon}}" />功能简介
                        </a>
                        <a
                            href="{{DocService.probe_helpFile}}"
                            target="_blank"
                            class="help-file help-file-docs"
                            s-if="{{!FLAG.NetworkSupportXS}}"
                        >
                            <s-icon name="warning-new" />帮助文档
                        </a>
                    </div>
                </div>
                <introduce-panel
                    isShow="{{show}}"
                    klass="{{'IPv6-vpn-wrapper'}}"
                    title="{{introduceTitle}}"
                    markerDesc="{{markerDesc}}"
                    introduceOptions="{{introduceOptions}}"
                    on-toggle="handleToggle($event)"
                ></introduce-panel>
            </div>
            <div slot="bulk">
                <s-tip-button
                    skin="primary"
                    isDisabledVisibile="{{true}}"
                    disabled="{{createProbe || disableCreate}}"
                    on-click="onCreate"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{createProbeMessage || disableCreateTip | raw}}
                    </div>
                    <outlined-plus />
                    创建网络探测
                </s-tip-button>
                <s-tooltip class="left_class" trigger="{{release.disable ? 'hover' : ''}}" placement="top">
                    <!--bca-disable-next-line-->
                    <div slot="content">{{release.message | raw}}</div>
                    <s-button on-click="onDelete" disabled="{{release.disable}}"> 删除</s-button>
                </s-tooltip>
            </div>
            <div slot="filter">
                <div class="filter-buttons-wrap">
                    <s-search
                        width="{{230}}"
                        class="search-warp"
                        value="{=payload.keyword=}"
                        placeholder="{{searchholder}}"
                        on-search="onSearch($event)"
                    >
                        <s-select
                            slot="options"
                            width="120"
                            datasource="{{searchType}}"
                            value="{=payload.keywordType=}"
                            on-change="searchTypeChange($event)"
                        >
                        </s-select>
                    </s-search>
                    <custom-column
                        class="left_class"
                        columnList="{{customColumn.datasource}}"
                        initValue="{{customColumn.value}}"
                        type="probe"
                        on-init="initColumns"
                        on-change="onCustomColumns"
                    >
                    </custom-column>
                </div>
            </div>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                on-selected-change="tableSelected($event)"
                selection="{=table.selection=}"
            >
                <div slot="empty">
                    <s-empty on-click="onCreate"> </s-empty>
                </div>
                <div slot="c-probeId" class="probe-id-widget">
                    <a
                        href="#/vpc/probe/detail?vpcId={{row.vpcUuid}}&probeId={{row.probeId}}"
                        class="truncated"
                        title="{{row.name}}"
                        >{{ row.name }}</a
                    >
                    <s-popover
                        s-ref="popover-name-{{rowIndex}}"
                        placement="top"
                        trigger="click"
                        class="edit-popover-class"
                    >
                        <div class="edit-wrap" slot="content">
                            <s-input
                                value="{=edit.name.value=}"
                                width="320"
                                placeholder="请输入名称"
                                on-input="onEditInput($event, rowIndex, 'name')"
                            />
                            <div class="edit-tip">
                                大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65
                            </div>
                            <s-button
                                skin="primary"
                                s-ref="editBtn-name-{{rowIndex}}"
                                disabled="{{true}}"
                                on-click="editConfirm(row, rowIndex, 'name')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel(rowIndex, 'name')">取消</s-button>
                        </div>
                        <outlined-editing-square class="name-icon" on-click="onInstantEdit(row, rowIndex, 'name')" />
                    </s-popover>
                    <br />
                    <span class="truncated vpn-id" title="{{row.probeId}}">{{ row.probeId }}</span>
                    <s-clip-board class="name-icon" text="{{row.probeId}}" />
                </div>
                <div slot="c-status">
                    <span class="{{row.status | statusClass}}">{{row.status | statusText}}</span>
                </div>
                <div slot="c-vpcId">
                    <div class="truncated">
                        <a class="list-link" href="#/vpc/instance/detail?vpcId={{row.vpcUuid}}" target="_blank"
                            >{{row.vpcName || '-'}}</a
                        >
                    </div>
                    <br />
                    <span class="truncated">{{row.vpcId || '-'}}</span>
                </div>
                <div slot="c-subnetId">
                    <div class="truncated">
                        <a
                            href="#/vpc/subnet/detail?subnetId={{row.subnetUuid}}"
                            class="list-link"
                            target="_blank"
                            title="{{row.subnetName}}（{{row.subnetCidr}}）"
                            >{{row.subnetName || '-'}}</a
                        >
                    </div>
                    <br />
                    <span class="truncated">{{row.subnetCidr || '-'}}</span>
                </div>
                <div slot="c-protocol">
                    <span>{{row.protocol || '-'}}</span>
                </div>
                <div slot="c-frequency">
                    <span>{{row.frequency}}次/分钟</span>
                </div>
                <div slot="c-sourceIps">
                    <p s-if="row.sourceIps && row.sourceIps.length > 0" s-for="item in row.sourceIps">{{ item }}</p>
                    <span s-else>-</span>
                </div>
                <div slot="c-nextHop">
                    <span>{{row.nextHop || '-'}}</span>
                </div>
                <div slot="c-nextHopType">
                    <span>{{row.nextHopType | routeText}}</span>
                </div>
                <div slot="c-destIp">
                    <span>{{row | destIp}}</span>
                </div>
                <div slot="c-description">
                    <span class="truncated">{{row.description || '-'}}</span>
                    <s-popover
                        s-ref="popover-description-{{rowIndex}}"
                        placement="top"
                        trigger="click"
                        class="edit-popover-class"
                    >
                        <div class="edit-wrap" slot="content">
                            <s-input-text-area
                                value="{=edit.description.value=}"
                                width="160"
                                height="60"
                                maxLength="{{200}}"
                                placeholder="请输入描述"
                                on-input="onEditInput($event, rowIndex, 'description')"
                            />
                            <div class="edit-tip">描述不能超过200个字符</div>
                            <s-button
                                skin="primary"
                                s-ref="editBtn-description-{{rowIndex}}"
                                disabled="{{true}}"
                                on-click="editConfirm(row, rowIndex, 'description')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel(rowIndex, 'description')">取消</s-button>
                        </div>
                        <outlined-editing-square
                            class="name-icon"
                            on-click="onInstantEdit(row, rowIndex, 'description')"
                        />
                    </s-popover>
                </div>
                <div slot="c-opt" class="operations">
                    <s-button skin="stringfy" on-click="editProbe(row)">编辑</s-button>
                    <s-button skin="stringfy" on-click="onMonitor(row)">监控</s-button>
                    <s-button skin="stringfy" on-click="onDelete(row)">删除</s-button>
                </div>
            </s-table>
            <s-pagination
                s-if="{{pager.total}}"
                slot="pager"
                layout="{{'pageSize, pager'}}"
                pageSize="{{pager.pageSize}}"
                total="{{pager.total}}"
                page="{{pager.page}}"
                resetPageWhenSizeChange="{{true}}"
                on-pagerChange="onPagerChange"
            />
        </s-app-list-page>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@invokeComp('@custom-column', '@search-res', '@vpc-select', '@introduce-panel')
class VpcProbeList extends Component {
    static components = {
        'outlined-plus': OutlinedPlus,
        'outlined-editing-square': OutlinedEditingSquare
    };
    initData() {
        const allColumns = columns.slice();
        const customColumnDb = allColumns.map(item => ({
            text: item.label,
            value: item.name,
            disabled: item.name === 'probeId' || item.name === 'opt'
        }));
        return {
            customColumn: {
                value: [
                    'probeId',
                    'status',
                    'vpcId',
                    'subnetId',
                    'protocol',
                    'frequency',
                    'sourceIps',
                    'nextHopType',
                    'nextHop',
                    'destIp',
                    'description',
                    'opt'
                ],
                datasource: customColumnDb
            },
            searchType: ProbeSearchType.toArray(),
            searchholder: '请输入网络探测名称进行搜索',
            payload: {
                keyword: '',
                keywordType: 'name'
            },
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                allColumns,
                datasource: [],
                selectedItems: []
            },
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            release: {},
            DocService,
            FLAG,
            introduceTitle: '网络探测',
            markerDesc: [
                '网络探测监控VPC网络连接质量的服务，支持云服务器、NAT网关、VPN网关、对等连接、专线网关、云智能网的Ping探测，监控网络连接的时延、丢包率等关键指标，可实时感知连接质量并对连接故障实时告警。',
                '网络探测的核心字段为探测方式、探测频率、探测源IP、探测目的IP等。'
            ],
            introduceIcon: INTRODUCE_ICON_COLOR,
            documentIcon: DOCUMENT_ICON,
            introduceEle: null
        };
    }
    static filters = {
        statusClass(value) {
            return ProbeStatus.fromValue(value).styleClass || '';
        },
        statusText(value) {
            return value ? ProbeStatus.getTextFromValue(value) : '-';
        },
        routeText(type) {
            return RouteType.getTextFromValue(type) || '-';
        },
        destIp(row) {
            if (row.protocol === ProbeProtocol.ICMP) {
                return row.destIp;
            } else {
                return `${row.destIp}:${row.destPort || '-'}`;
            }
        }
    };
    static computed = {
        disableCreate() {
            const quota = this.data.get('quota');
            return !quota || quota?.free <= 0;
        },
        disableCreateTip() {
            const quota = this.data.get('quota');
            return !quota || quota?.free <= 0 ? '网络探测配额不足。如需提升配额，请提交工单' : '';
        }
    };
    inited() {
        const AllRegion = ContextService.getEnum('AllRegion');
        if (window.$context.getCurrentRegionId() === AllRegion.SIN) {
            this.data.set('createProbe', true);
            this.data.set('createProbeMessage', '新加坡地域资源售罄，请您切换到其他地域创建');
        }
    }
    attached() {
        this.checkIamStsRole();
        window.$storage.get('showProbeIntroduce') === false && this.data.set('show', false);
        this.getQuota();
        let {release} = checker.check(rules, []);
        this.data.set('release', release);
        this.data.set('introduceEle', this.ref('introduce'));
    }
    getQuota() {
        this.$http.getProbeQuota().then(res => {
            this.data.set('quota', res);
        });
    }

    // 改变页数
    onPagerChange({value}) {
        this.data.set('pager.page', value.page);
        this.data.set('pager.pageSize', value.pageSize);
        this.loadPage();
    }

    onInstantEdit(row, rowIndex, type) {
        this.data.set(`edit.${type}.value`, row[`${type}`]);
        this.data.set(`edit.${type}.error`, false);
        const editWrap = this.ref(`popover-${type}-${rowIndex}`);
        editWrap.data.set('visible', !editWrap.data.get('visible'));
    }

    // 编辑弹框-输入名称/描述
    onEditInput(e, rowIndex, type) {
        let result =
            type === 'name'
                ? e.value === '' || !/^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/.test(e.value)
                : e.value.length > 200;
        this.data.set(`edit.${type}.error`, result);
        this.data.set(`edit.${type}.value`, e.value);
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', result);
    }
    async editConfirm(row, rowIndex, type) {
        let edit = this.data.get(`edit.${type}`);
        if (edit.error) {
            return;
        }
        this.$http
            .editProbe(row.probeId, {
                [type]: edit.value
            })
            .then(() => {
                this.editCancel(rowIndex, type);
                Notification.success('修改成功');
                this.loadPage();
            });
    }

    editCancel(rowIndex, type) {
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', true);
        this.ref(`popover-${type}-${rowIndex}`).data.set('visible', false);
    }

    onCreate() {
        location.hash = '#/vpc/probe/create';
    }

    editProbe(row) {
        location.hash = '#/vpc/probe/edit?probeId=' + row.probeId;
    }

    onDelete(row) {
        let confirm = new Confirm({
            data: {
                open: true,
                title: '删除网络探测',
                content: '确认删除选中的网络探测？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            let probeId = row?.probeId || this.data.get('table.selectedItems')[0].probeId;
            let count = this.data.get('pager.total');
            this.$http.deleteProbe(probeId).then(() => {
                this.loadPage().then(() => {
                    // 防止在第二页删除数据后没数据页码对不上的问题
                    this.data.set('pager.total', count);
                });
                Notification.success('释放成功', {placement: 'topRight'});
            });
        });
    }

    onMonitor(row) {
        const bcm = new BcmDetail({
            data: {
                name: row.name,
                payload: row,
                protocol: row.protocol
            }
        });
        bcm.attach(document.body);
    }

    getPayload() {
        const searchParam = this.data.get('payload');
        const {pager} = this.data.get('');
        const vpcId = window.$storage.get('vpcInfo')?.shortId || '';
        let payload = {
            pageNo: pager.page,
            pageSize: pager.pageSize
        };
        return {...payload, vpcId, ...searchParam};
    }

    searchTypeChange({value}) {
        let tip = `请输入${ProbeSearchType.getTextFromValue(value)}进行搜索`;
        this.data.set('searchholder', tip);
    }

    handleInputChange({value}) {
        this.data.set('payload.keyword', value);
    }

    onSearch(e) {
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    // 设置表格列
    setTableColumns(customColumnNames) {
        customColumnNames = customColumnNames ? customColumnNames : this.data.get('customColumn.value');
        const allColumns = this.data.get('table.allColumns');
        let columns = [];
        allColumns.forEach(item => customColumnNames.indexOf(item.name) > -1 && columns.push(item));
        this.data.set('table.columns', columns);
    }
    initColumns(value) {
        this.setTableColumns(value);
    }
    // 自定义表格列
    onCustomColumns(value) {
        this.data.set('customColumn.value', value);
        this.setTableColumns(value);
    }
    tableSelected(e) {
        this.data.set('table.selectedItems', e.value.selectedItems);
        let {release} = checker.check(rules, e.value.selectedItems);
        this.data.set('release', release);
    }
    resetTable() {
        this.data.set('table.selection', {
            mode: 'multi',
            selectedIndex: []
        });
        this.data.set('table.selectedItems', []);
    }

    loadPage() {
        this.data.set('table.loading', true);
        this.getQuota();
        let payload = this.getPayload();
        this.resetTable();
        return this.$http.getProbeList(payload).then(data => {
            this.data.set('table.datasource', data.result);
            this.data.set('pager.total', data.totalCount);
            this.data.set('table.loading', false);
        });
    }

    onRegionChange() {
        location.reload();
    }

    // csn服务授权
    checkIamStsRole() {
        const AllRegion = this.$context.getEnum('AllRegion');
        const roleName = StsConfig.CSN.roleName;
        const isSubUser = window.$context.isSubUser();
        if (!window.$storage.get('csnSts') && !isSubUser) {
            // 主用户才可以激活授权
            this.$http.iamStsRoleActivate(
                u.extend(
                    {
                        roleName,
                        accountId: window.$context.getUserId()
                    },
                    isOnline() ? StsConfig.CSN.online : StsConfig.CSN.sandbox
                ),
                {region: AllRegion.BJ}
            );
        }
    }

    vpcChange() {
        this.data.set('pager.page', '1');
        this.loadPage();
    }
    /**
     * @description 介绍卡片变化时回调
     * @param {boolean} flag
     */
    handleToggle(flag) {
        this.data.set('show', flag);
        window.$storage.set('showProbeIntroduce', flag);
        this.data.set('introduceIcon', INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#151B26';
    }
    handleShowCard() {
        if (!this.data.get('show')) {
            this.data.set('show', true);
            window.$storage.set('showProbeIntroduce', true);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type) {
        if (type === 'introduce') {
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
            const ele = this.data.get('introduceEle');
            ele.style.color = '#2468F2';
        } else {
            this.data.set('documentIcon', DOCUMENT_ICON_COLOR);
        }
    }
    handleMouseLeave(type) {
        const isShow = this.data.get('show');
        if (type === 'introduce') {
            this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
            const ele = this.data.get('introduceEle');
            !isShow && (ele.style.color = '#151B26');
        } else {
            this.data.set('documentIcon', DOCUMENT_ICON);
        }
    }
}
export default San2React(Processor.autowireUnCheckCmpt(VpcProbeList));
