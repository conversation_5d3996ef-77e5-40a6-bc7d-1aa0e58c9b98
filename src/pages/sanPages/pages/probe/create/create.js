import u from 'lodash';
import {html, decorators, CreatePage, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {Form, Input, Select, Radio, Switch, Button, Tooltip, Icon} from '@baidu/sui';
import {OutlinedPlus} from '@baidu/sui-icon';

import {
    ProbeProtocol,
    RouteType,
    DcGatewayStatus,
    NatStatus,
    VpnStatus,
    RouteStatus,
    PeerConnStatus
} from '@/pages/sanPages/common/enum';
import {disable_vpn_region} from '@/pages/sanPages/common/flag';
import rules from '../rules';
import {formValidator} from '../components/helper';
import {AppCreatePage, Tip} from '@baidu/sui-biz';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import {checkIsInSubnet} from '@/pages/sanPages/utils/common';
import {serviceTypeUrl, checkSts} from '@/pages/sanPages/utils/config';
import RULE from '@/pages/sanPages/utils/rule';
import Assist from '@/utils/assist';
import {ContextService} from '@/pages/sanPages/common';
import './style.less';

const {asPage, invokeSUI, invokeSUIBIZ, template} = decorators;
const AllRegion = window.$context.getEnum('AllRegion');
const kXhrOptions = {'X-silence': true};
const tpl = html`
    <div class="vpc-probe-create">
        <s-bizpage backTo="{{pageNav.backUrl}}" backToLabel="{{pageNav.backLabel}}" pageTitle="{{pageNav.title}}">
            <div class="content-box form-part-wrap">
                <h4>配置信息</h4>
                <s-form s-ref="form" rules="{{validateRule}}" data="{=formData=}" label-align="left">
                    <s-form-item
                        label="探测名称："
                        prop="name"
                        help="大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65"
                    >
                        <s-input width="{{300}}" value="{=formData.name=}" />
                    </s-form-item>
                    <s-form-item label="所在网络： " prop="vpcId">
                        <s-select
                            width="{{300}}"
                            datasource="{{vpcList}}"
                            value="{=formData.vpcId=}"
                            on-change="vpcChange($event)"
                            disabled="{{probeInfo}}"
                        >
                        </s-select>
                    </s-form-item>
                    <s-form-item label="所在子网：" prop="subnetId">
                        <s-select
                            width="{{300}}"
                            datasource="{{subnetDatasource}}"
                            value="{=formData.subnetId=}"
                            on-change="subnetChange"
                            disabled="{{loading || probeInfo}}"
                        >
                        </s-select>
                    </s-form-item>
                    <s-form-item class="probe-way-item" label="探测方式：" prop="protocol">
                        <s-radio-radio-group
                            datasource="{{protocolList}}"
                            radioType="button"
                            value="{=formData.protocol=}"
                            disabled="{{probeInfo}}"
                            on-change="protocolChange($event)"
                        />
                        <!--<p class="assist-tip-form">
                            不同的探测方式有哪些区别？
                            <a class="assist-tip" href="javascript:void(0)" on-click="showAssist('protocol')">了解详情</a>
                        </p>-->
                    </s-form-item>
                    <s-form-item s-if="{{formData.protocol === 'UDP'}}" prop="payload">
                        <div slot="label" class="label_class">
                            {{'探测字符串：'}}
                            <s-tip
                                class="inline-tip"
                                content="用户侧的UDP server根据探测字符串进行回包，若回包则表示网络连通。"
                                skin="question"
                            />
                        </div>
                        <s-input width="{{300}}" value="{=formData.payload=}" />
                    </s-form-item>
                    <s-form-item s-if="{{formData.protocol === 'DNS'}}" prop="payload">
                        <div slot="label" class="label_class">
                            {{'探测域名：'}}
                            <s-tip
                                class="inline-tip"
                                content="DNS服务器对探测域名进行解析并回包，解析成功则表示DNS服务正常。"
                                skin="question"
                            />
                        </div>
                        <s-input width="{{300}}" value="{=formData.payload=}" />
                    </s-form-item>
                    <s-form-item class="probe-frequency-item" label="探测频率：" prop="frequency">
                        <s-radio-radio-group
                            class="frequency-radio"
                            datasource="{{frequencyDatasource}}"
                            radioType="button"
                            value="{=formData.frequency=}"
                        >
                        </s-radio-radio-group>
                        <!--<p class="assist-tip-form">
                            探测频率对网络探测的影响是什么？
                            <a class="assist-tip" href="javascript:void(0)" on-click="showAssist('frequency')">了解详情</a>
                        </p>-->
                        <span class="frequency-unit">次/分钟</span>
                    </s-form-item>
                    <s-form-item label="探测源IP：" prop="sourceIps" class="ip-item">
                        <s-table datasource="{{table.datasource}}" columns="{{table.columns}}">
                            <div slot="empty">暂无数据</div>
                            <div slot="c-ip">
                                <s-radio-radio-group
                                    class="ip-radio"
                                    datasource="{{ipDatasource}}"
                                    value="{{row.isCustom}}"
                                    disabled="{{probeInfo}}"
                                    on-change="typeChange($event, rowIndex)"
                                />
                                <div class="probe-custom-ip-wrapper">
                                    <s-input
                                        s-if="row.isCustom"
                                        placeholder="请输入IP地址"
                                        value="{=row.sourceIp=}"
                                        disabled="{{probeInfo}}"
                                        on-input="inputChange($event, rowIndex)"
                                    />
                                    <label s-if="row.isCustom && row.error" class="probe-custom-ip-err"
                                        >{{ row.error }}</label
                                    >
                                </div>
                            </div>
                            <div slot="c-operation">
                                <s-button
                                    skin="stringfy"
                                    s-if="!row.primary"
                                    disabled="{{probeInfo}}"
                                    on-click="onRemove(rowIndex)"
                                    >删除</s-button
                                >
                            </div>
                        </s-table>
                        <s-tooltip trigger="{{disableAddIp.message ? 'hover' : ''}}" placement="right">
                            <div slot="content">{{ disableAddIp.message }}</div>
                            <s-button
                                skin="stringfy"
                                on-click="addIp()"
                                disabled="{{disableAddIp.disable || probeInfo}}"
                                ><outlined-plus /> 添加源IP</s-button
                            >
                        </s-tooltip>
                    </s-form-item>
                    <div class="destip-wrapper destip-dns" s-if="{{formData.protocol === 'DNS'}}">
                        <div class="destip-port-wrapper">
                            <s-form-item label="探测目的IP：" class="destip-dns-required">
                                <div>
                                    <s-radio-radio-group
                                        class="ip-radio"
                                        disabled="{{probeInfo}}"
                                        datasource="{{dnsDatasource}}"
                                        value="{{dnsRadio}}"
                                        on-change="switch($event)"
                                    />
                                </div>
                                <s-form-item prop="destIp" class="destip">
                                    <s-input
                                        class="destip-input"
                                        width="{{200}}"
                                        value="{=formData.destIp=}"
                                        placeholder="{{dnsRadio ? '请输入探测目的IP' : ''}}"
                                        disabled="{{!dnsRadio}}"
                                    />
                                </s-form-item>
                                <s-form-item prop="destPort" class="destport">
                                    <s-input
                                        class="destip-input"
                                        width="{{200}}"
                                        value="{=formData.destPort=}"
                                        placeholder="{{dnsRadio ? '请输入探测目的端口' : ''}}"
                                        disabled="{{!dnsRadio}}"
                                    />
                                </s-form-item>
                            </s-form-item>
                        </div>
                    </div>
                    <div class="destip-wrapper destip-other" s-else>
                        <s-form-item label="探测目的IP：" prop="destIp" class="destip">
                            <s-input
                                class="destip-input"
                                width="{{200}}"
                                value="{=formData.destIp=}"
                                placeholder="请输入探测目的IP"
                            />
                        </s-form-item>
                        <s-form-item prop="destPort" class="destport" s-if="{{formData.protocol !== 'ICMP'}}">
                            <s-input
                                s-if="{{formData.protocol !== 'ICMP'}}"
                                class="destip-input"
                                width="{{200}}"
                                value="{=formData.destPort=}"
                                placeholder="请输入探测目的端口"
                            />
                        </s-form-item>
                    </div>
                    <s-form-item
                        class="probe-auto-item"
                        s-if="formData.protocol !== 'DNS' || dnsRadio"
                        label="自动生成路由："
                        prop="autoGenerateRouteRule"
                    >
                        <s-switch
                            disabled="{{probeInfo}}"
                            checked="{=formData.autoGenerateRouteRule=}"
                            on-change="autoGenerateChange"
                        />
                    </s-form-item>
                    <div
                        s-if="formData.autoGenerateRouteRule && (formData.protocol !== 'DNS' || dnsRadio)"
                        class="probe-route-wrap"
                    >
                        <s-form-item label="源端下一跳路由：" prop="nextHopType" class="route-item">
                            <s-form-item class="probe-route-type probe-route-select">
                                <s-select
                                    width="200"
                                    placeholder="请选择路由类型"
                                    value="{=formData.nextHopType=}"
                                    on-change="nextHopTypeChange"
                                >
                                    <s-select-option
                                        s-for="item in routeTypeList"
                                        value="{{item.value}}"
                                        disabled="{{item.disabled}}"
                                        label="{{item.text}}"
                                    >
                                        <s-tooltip
                                            trigger="{{item.disabled ? 'hover' : ''}}"
                                            placement="right"
                                            layerWidth="300"
                                        >
                                            <div slot="content">
                                                <!--bca-disable-next-line-->
                                                {{item.value | noOpenTip | raw}}
                                            </div>
                                            <div>{{item.text}}</div>
                                        </s-tooltip>
                                    </s-select-option>
                                </s-select>
                            </s-form-item>
                            <s-form-item prop="nextHop" class="probe-route-select">
                                <s-select
                                    width="200"
                                    filterable
                                    datasource="{{routeList}}"
                                    value="{=formData.nextHop=}"
                                />
                            </s-form-item>
                        </s-form-item>
                    </div>
                    <s-form-item label="描述：" prop="description">
                        <s-input-text-area
                            width="{{300}}"
                            maxLength="{{200}}"
                            height="{{100}}"
                            multiline
                            value="{=formData.description=}"
                        />
                    </s-form-item>
                </s-form>
            </div>
            <div class="buybucket" slot="pageFooter">
                <div class="buybucket-container">
                    <s-tooltip placement="top" trigger="{{probeSinDisable.disable ? 'hover' : ''}}">
                        <div slot="content">{{probeSinDisable.message}}</div>
                        <s-button
                            skin="primary"
                            disabled="{{probeSinDisable.disable || disableSub}}"
                            on-click="doSubmit"
                            size="large"
                            >确定</s-button
                        >
                    </s-tooltip>
                    <s-button on-click="onClose" size="large">取消</s-button>
                </div>
            </div>
        </s-bizpage>
    </div>
`;
@template(tpl)
@invokeSUI
@invokeSUIBIZ
class ProbeCreate extends CreatePage {
    static components = {
        's-bizpage': AppCreatePage,
        'outlined-plus': OutlinedPlus,
        's-button': Button,
        's-tooltip': Tooltip,
        's-select': Select,
        's-tip': Tip,
        's-form': Form,
        's-form-item': Form.Item,
        's-input': Input,
        's-radio-radio-group': Radio.RadioGroup,
        's-switch': Switch,
        's-icon': Icon
    };
    initData() {
        return {
            pageNav: {
                title: '创建网络探测',
                backUrl: '/network/#/vpc/probe/list',
                backLabel: '返回网络探测'
            },
            flag: FLAG,
            vpcList: [],
            subnetDatasource: [],
            initSubnetDatasource: [],
            subnetIpv6Cidr: '',
            dnsRadio: false,
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            formData: {
                name: '',
                vpcId: '',
                subnetId: '',
                frequency: 20,
                protocol: 'ICMP',
                packetLen: 64,
                payload: '',
                destIp: '',
                destPort: '',
                autoGenerateRouteRule: false,
                nextHopType: '',
                nextHop: '',
                description: ''
            },
            quotaError: false,
            frequencyDatasource: [
                {text: 10, value: 10},
                {text: 20, value: 20},
                {text: 30, value: 30}
            ],
            protocolList: ProbeProtocol.toArray(),
            table: {
                datasource: [
                    {
                        isCustom: false,
                        primary: true,
                        sourceIp: ''
                    }
                ],
                columns: [
                    {name: 'ip', label: 'IP地址'},
                    {name: 'operation', label: '操作', width: 90}
                ]
            },
            ipDatasource: [
                {
                    text: '自动分配',
                    value: false
                },
                {
                    text: '指定',
                    value: true
                }
            ],
            dnsDatasource: [
                {
                    text: '百度智能云DNS服务器',
                    value: false
                },
                {
                    text: '用户自建DNS服务器',
                    value: true
                }
            ],
            routeTypeList: [],
            routeList: [],
            validateRule: formValidator(this),
            vpcMap: {},
            probeSinDisable: {},
            urlQuery: getQueryParams()
        };
    }
    static computed = {
        disableAddIp() {
            let num = this.data.get('table.datasource').length;
            let subnetId = this.data.get('formData.subnetId');
            let {addIp} = checker.check(rules, '', 'addIp', {num, subnetId});
            return addIp;
        }
    };
    static filters = {
        noOpenTip(value) {
            const isSubUser = window.$context.isSubUser();
            let ResourceType = this.data.get('routeTypeList') || [];
            let serviceText = ResourceType.find(item => item.value === value)?.text;
            let url = serviceTypeUrl[value];
            let str = `您的账号当前未开通${serviceText}服务，请前往${serviceText}控制台开通。<a href="${url}">去开通</a>`;
            if (isSubUser) {
                str = `该功能需要开通${serviceText}服务，当前登录的子账号没有开通产品服务的权限，请联系主账号授予开通服务权限，或联系主账户开通服务后授权使用。`;
            }
            return str;
        }
    };
    inited() {
        const AllRegion = ContextService.getEnum('AllRegion');
        if (window.$context.getCurrentRegionId() === AllRegion.SIN) {
            this.data.set('probeSinDisable.disable', true);
            this.data.set('probeSinDisable.message', '新加坡地域资源售罄，请您切换到其他地域创建');
        }
        if (FLAG.NetworkSupportXS) {
            this.data.set('dnsDatasource[0].text', '智能云DNS服务器');
        }
        this.getWhiteList();
        let probeId = this.data.get('urlQuery.probeId');
        this.isInstanceEdit(probeId);
        let vpcList = window.$storage.get('vpcList');
        this.getVpcs(vpcList);
        let vpcs = this.data.get('vpcs');
        let vpcMap = {};
        u.each(vpcs, item => {
            vpcMap[item.value] = item.text;
        });
        this.data.set(
            'vpcList',
            vpcs.map(item => ({
                text: `${item.vpcInfo.name}（${item.vpcInfo.cidr}）`,
                value: item.vpcInfo.shortId
            }))
        );
        this.data.set('formData.vpcMap', vpcMap);
    }

    getTgwList() {
        let vpcId = this.data.get('formData.vpcId');
        let vpcInfo = u.filter(this.data.get('vpcs'), item => item.vpcInfo.shortId === vpcId)[0]?.vpcInfo;
        if (!vpcInfo.csnId || !window.$storage.get('csnSts')) {
            return;
        }
        let region = window.$context.getCurrentRegionId();
        return this.$http.getTgwInstanceList(vpcInfo.csnId, {region}).then(res => {
            let tgwInstanceList = res.tgws.map(item => {
                return {text: item.tgwId, value: item.tgwId};
            });
            this.data.set('tgwInstanceList', tgwInstanceList);
            return Promise.resolve(tgwInstanceList);
        });
    }

    enableVpnRegion() {
        let region = window.$context.getCurrentRegionId();
        if (region === AllRegion.HK02) {
            return true;
        }
        return u.indexOf(disable_vpn_region, region) === -1;
    }

    natWhiteList() {
        return this.data.get('flag.NetworkSupportNat');
    }

    peerconnWhiteList() {
        const serversType = Object.keys(window.$context.SERVICE_TYPE);
        return serversType.indexOf('PEERCONN') > -1 && window.$context.getCurrentRegionId() !== AllRegion.BJKS;
    }

    // 不在白名单的路由类型不展示
    getWhiteList() {
        // 先隐藏掉实例，nat，vpn
        let all = [this.enableVpnRegion(), this.natWhiteList(), this.peerconnWhiteList()];
        Promise.all(all).then(white => {
            let [VPN, NAT, PEERCONN] = white;
            const whiteList = [];
            const whiteMap = {
                CUSTOM: true,
                NAT: NAT,
                VPN: VPN,
                PEERCONN: PEERCONN,
                GW: true,
                TGW: true
            };
            u.each(whiteMap, (item, key) => {
                item && whiteList.push(key);
            });
            let routeTypeList = RouteType.toArray(...whiteList);
            routeTypeList = checkSts(routeTypeList);
            this.data.set('routeTypeList', routeTypeList);
        });
    }

    getProbeQuota() {
        let vpcId = this.data.get('formData.vpcId');
        let vpcInfo = u.filter(this.data.get('vpcs'), item => item.vpcInfo.shortId === vpcId)[0]?.vpcInfo;
        return this.$http.getProbeQuota({vpcId: vpcInfo.vpcId}).then(data => this.data.set('quotaFree', data.free));
    }

    vpcChange(e) {
        this.data.set('formData.vpcId', e.value);
        this.data.set('formData.nextHopType', '');
        this.data.set('formData.nextHop', '');
        this.data.set('formData.routeList', []);
        this.data.set('formData.subnetId', '');
        this.initSourceIp();
        this.getProbeQuota();
        this.loadSubnets();
    }

    subnetChange() {
        this.initSourceIp();
    }

    initSourceIp() {
        this.data.set('table.datasource', [
            {
                isCustom: false,
                primary: true,
                sourceIp: ''
            }
        ]);
    }

    loadSubnets() {
        let vpcId = this.data.get('formData.vpcId');
        let vpcInfo = u.filter(this.data.get('vpcs'), item => item.vpcInfo.shortId === vpcId)[0]?.vpcInfo;
        let payload = {vpcId: vpcInfo.vpcId};
        this.data.set('loading', true);
        this.$http
            .getSubnetList(payload)
            .then(data => {
                let datasource = [];
                u.each(data, item => {
                    // 暂时不支持ipv6先注释掉
                    // let text = '';
                    // if (item.ipv6Cidr) {
                    //   text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}(${item.ipv6Cidr})`;
                    // }
                    // else {
                    //   text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}`;
                    // }
                    let text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}`;
                    datasource.push({
                        value: item.shortId,
                        subnetId: item.subnetId,
                        text: text,
                        cidr: item.cidr,
                        ipv6Cidr: item.ipv6Cidr
                    });
                });
                this.data.set('subnetDatasource', datasource);
                this.data.set('initSubnetDatasource', datasource);
            })
            .finally(() => this.data.set('loading', false));
    }

    inputChange(e, index) {
        this.data.set(`table.datasource[${index}].sourceIp`, e.value);
        this.sourceIpCheck(index, e.value);
    }

    addIp() {
        this.data.push('table.datasource', {
            isCustom: false,
            primary: false,
            sourceIp: ''
        });
    }

    autoGenerateChange() {
        this.data.set('formData.nextHopType', '');
        this.data.set('formData.nextHop', '');
    }

    typeChange(e, index) {
        this.data.set(`table.datasource[${index}].error`, '');
        this.data.set(`table.datasource[${index}].sourceIp`, '');
        this.data.set(`table.datasource[${index}].isCustom`, e.value);
    }

    getSubnetIdsByValue() {
        return u.pluck(this.data.get('subnetDatasource'), 'subnetId');
    }

    getInstanceListBySubnets(name) {
        let ids = this.getSubnetIdsByValue();
        return this.$http.bccInstanceSubnetsList({ids, name: name || ''}, kXhrOptions).then(data => {
            let result = [];
            u.each(data.servers, item => {
                if (!u.isEmpty(RouteStatus.fromValue(item.status))) {
                    let text = item.name + (item.internalIp ? `（${item.internalIp}）` : '');
                    result.push({
                        value: item.instanceId,
                        text
                    });
                }
            });
            return Promise.resolve(result);
        });
    }

    getNatList(name) {
        let vpcId = this.data.get('formData.vpcId');
        let vpcInfo = u.filter(this.data.get('vpcs'), item => item.vpcInfo.shortId === vpcId)[0]?.vpcInfo;
        let query = {
            vpcId: vpcInfo.vpcId,
            pageNo: 1,
            pageSize: 10000
        };
        if (name) {
            query.name = name;
        }
        return this.$http.getNatList(query, kXhrOptions).then(res => {
            let result = [];
            let title = '';
            if (res) {
                u.each(res.result, item => {
                    // 只显示active状态的nat
                    if (u.indexOf([NatStatus.ACTIVE], item.status) > -1) {
                        title = item.name;
                        result.push({
                            value: item.id,
                            primaryId: item.primaryId,
                            status: item.status,
                            text: title
                        });
                    }
                });
            }
            return Promise.resolve(result);
        });
    }

    getVpnList() {
        let vpcId = this.data.get('formData.vpcId');
        let vpcInfo = u.filter(this.data.get('vpcs'), item => item.vpcInfo.shortId === vpcId)[0]?.vpcInfo;
        let query = {vpcId: vpcInfo.vpcId};
        return this.$http.getVpnList(query, kXhrOptions).then(data => {
            let result = [];
            u.each(data.result, item => {
                if (u.indexOf([VpnStatus.ACTIVE], item.status) > -1) {
                    result.push({
                        value: item.vpnId,
                        text: `${item.vpnName}/${item.vpnId}`
                    });
                }
            });
            return Promise.resolve(result);
        });
    }

    getPeerConnList(localIfName) {
        let query = {
            localVpcShortId: this.data.get('formData.vpcId'),
            pageNo: 1,
            pageSize: 10000
        };
        if (localIfName) {
            query.localIfName = localIfName;
        }
        return this.$http.peerconnList(query, kXhrOptions).then(res => {
            let result = [];
            u.each(res.result, item => {
                if (u.indexOf([PeerConnStatus.ACTIVE], item.status) > -1) {
                    let name = item.localIfName + '（' + item.localIfId + '）';
                    result.push({
                        value: item.localIfId,
                        text: name
                    });
                }
            });
            return Promise.resolve(result);
        });
    }

    getGwList(name) {
        let query = {
            vpcId: this.data.get('formData.vpcId'),
            pageNo: 1,
            pageSize: 10000
        };
        if (name) {
            query.keywordType = 'name';
            query.keyword = name;
        }
        return this.$http.dcgwList(query, kXhrOptions).then(res => {
            let result = [];
            u.each(res.result, item => {
                if (u.indexOf([DcGatewayStatus.RUNNING], item.status) > -1) {
                    let name = item.name + '/' + item.id;
                    result.push({
                        value: item.id,
                        text: name
                    });
                }
            });
            return Promise.resolve(result);
        });
    }

    nextHopTypeChange({value}) {
        this.data.set('routeList', []);
        this.data.set('formData.nextHop', '');
        this.loadSource(value);
    }

    loadSource(value) {
        let type = value || this.data.get('formData.nextHopType');
        const requesetMap = {
            custom: this.getInstanceListBySubnets,
            nat: this.getNatList,
            vpn: this.getVpnList,
            peerConn: this.getPeerConnList,
            dcGateway: this.getGwList,
            vpc2tgw: this.getTgwList
        };
        if (!requesetMap[type]) {
            return;
        }
        return requesetMap[type].apply(this).then(res => {
            this.data.set('routeList', res);
        });
    }

    hasRepeat(arr) {
        const nums = [];
        for (let i = 0; i < arr.length; i++) {
            if (nums.includes(arr[i])) {
                return true;
            } else if (arr[i] !== '') {
                nums.push(arr[i]);
            }
        }
        return false;
    }

    sourceIpCheck(index, val) {
        let customIpList = this.data.get('table.datasource').map(item => item.sourceIp);
        let formData = this.data.get('formData');
        if (!formData.subnetId) {
            this.data.set(`table.datasource[${index}].error`, '请选择所在子网');
            return;
        }
        if (!val) {
            this.data.set(`table.datasource[${index}].error`, '请填写IP地址');
            return;
        }
        if (!RULE.IP.test(val)) {
            this.data.set(`table.datasource[${index}].error`, 'IP地址格式有误');
            return;
        }
        if (this.hasRepeat(customIpList)) {
            this.data.set(`table.datasource[${index}].error`, '源IP地址不能重复');
            return;
        }
        const subnetItem = u.find(this.data.get('initSubnetDatasource'), item => item.value === formData.subnetId);
        if (subnetItem && !checkIsInSubnet(val + '/32', subnetItem.cidr)) {
            this.data.set(`table.datasource[${index}].error`, '不属于所在子网');
            return;
        }
        this.data.set(`table.datasource[${index}].error`, '');
    }

    onRemove(rowIndex) {
        this.data.removeAt('table.datasource', rowIndex);
    }

    // 完整form校验，包含自定义IP的校验
    async completeCheck() {
        return new Promise((resolve, reject) => {
            this.ref('form')
                .validateFields()
                .then(() => {
                    let ipList = this.data.get('table.datasource');
                    for (let i = 0; i < ipList.length; i++) {
                        if (ipList[i].isCustom) {
                            this.sourceIpCheck(i, ipList[i].sourceIp);
                        }
                        let err = this.data.get(`table.datasource[${i}]`).error;
                        if (err) {
                            reject();
                            return;
                        }
                    }
                    resolve();
                })
                .catch(e => {
                    reject(e);
                });
        });
    }

    async doSubmit() {
        await this.completeCheck();
        let formData = this.data.get('formData');
        delete formData.vpcMap;
        let sourceIpList = this.data.get('table.datasource');
        let sourceIpNum = 0;
        let sourceIps = [];
        u.forEach(sourceIpList, item => {
            if (item.isCustom) {
                sourceIps.push(item.sourceIp);
            } else {
                sourceIpNum++;
            }
        });

        let payload = {
            ...formData,
            sourceIps,
            sourceIpNum
        };
        if (sourceIpNum < 1) {
            delete payload.sourceIpNum;
        }
        this.data.set('disableSub', true);
        if (this.data.get('probeInfo')) {
            let {destPort, protocol} = payload;
            let strPayload = payload.payload;
            payload = u.pick(payload, ['frequency', 'name', 'description', 'destIp', 'nextHop', 'nextHopType']);
            if (protocol !== 'ICMP') {
                payload = {
                    ...payload,
                    destPort
                };
            }
            if (protocol === 'UDP' || protocol === 'DNS') {
                payload = {
                    ...payload,
                    payload: strPayload
                };
            }
            this.$http
                .editProbe(formData.probeId, payload)
                .then(res => {
                    this.data.set('disableSub', false);
                    location.hash = '#/vpc/probe/list';
                })
                .catch(() => {
                    this.data.set('disableSub', false);
                });
        } else {
            this.$http
                .createProbe(payload)
                .then(res => {
                    this.data.set('disableSub', false);
                    location.hash = '#/vpc/probe/list';
                })
                .catch(() => {
                    this.data.set('disableSub', false);
                });
        }
    }

    onClose() {
        this.dispose();
        location.hash = '#/vpc/probe/list';
    }
    getPayload() {
        const searchParam = this.data.get('payload');
        const {pager} = this.data.get('');
        const vpcId = window.$storage.get('vpcInfo')?.shortId || '';
        let payload = {
            pageNo: pager.page,
            pageSize: pager.pageSize
        };
        return {...payload, vpcId, ...searchParam};
    }

    loadProbeInfo(option = {}) {
        return this.$http.getProbeDetail(this.data.get('urlQuery.probeId'), option).then(data => {
            this.data.set('probeInfo', data);
        });
    }

    setEditDetail() {
        let probeInfo = this.data.get('probeInfo');
        // 编辑状态
        if (probeInfo) {
            Object.keys(probeInfo).forEach(item => {
                this.data.set(`formData.${item}`, probeInfo[item]);
            });
            if (probeInfo.protocol === 'DNS') {
                let editDestIp = probeInfo.destIp;
                let editDestPort = probeInfo.destPort;
                if (editDestIp !== '***************' && editDestPort !== '53') {
                    this.data.set('dnsRadio', true);
                }
            }
            let sourceIps = probeInfo.sourceIps;
            if (sourceIps?.length) {
                this.data.set(
                    'table.datasource',
                    sourceIps.map((item, index) => ({
                        isCustom: true,
                        primary: index === 0 ? true : false,
                        sourceIp: item
                    }))
                );
            }
            if (probeInfo.sourceIpNum > 0) {
                for (let i = 0; i < probeInfo.sourceIpNum; i++) {
                    this.data.push('table.datasource', {
                        isCustom: false,
                        primary: false,
                        sourceIp: ''
                    });
                }
            }
            this.data.set('formData.autoGenerateRouteRule', !!probeInfo.nextHop);
        } else {
            this.data.set('formData.vpcId', vpcs[0]?.vpcInfo.shortId);
        }
    }
    isInstanceEdit(data) {
        if (data) {
            this.data.set('pageNav.title', '编辑网络探测');
            this.loadProbeInfo()
                .then(() => {
                    this.setEditDetail();
                    this.loadSubnets();
                })
                .catch(err => {
                    return err;
                });
        }
    }
    getVpcs(data) {
        let vpcs = data
            .filter(item => item.value)
            .map(item => {
                if (item.vpcInfo.ipv6Cidr) {
                    return {
                        ...item,
                        text: `${item.text}(${item.vpcInfo.ipv6Cidr})`
                    };
                } else return item;
            });
        this.data.set('vpcs', vpcs);
    }
    switch(e) {
        this.data.set(`dnsRadio`, e.value);
        this.protocolChange(e);
    }

    protocolChange(e) {
        let dnsRadio = this.data.get('dnsRadio');
        let editDestIp = this.data.get('probeInfo.destIp');
        let editDestPort = this.data.get('probeInfo.destPort');
        if ((e.value === 'DNS' || !e.value) && !dnsRadio) {
            this.data.set('formData.destIp', '***************');
            this.data.set('formData.destPort', '53');
        } else if (editDestIp && editDestPort && e.value) {
            this.data.set('formData.destIp', editDestIp);
            this.data.set('formData.destPort', editDestPort);
        } else {
            this.data.set('formData.destIp', '');
            this.data.set('formData.destPort', '');
        }
    }
    onRegionChange() {
        location.hash = '#/vpc/probe/list';
    }
    howAssist(type) {
        Assist.sendMessageToAssist({
            sceneLabel: 'probe_create',
            message: type === 'protocol' ? '不同的探测方式有哪些区别？' : '探测频率对网络探测的影响是什么？'
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(ProbeCreate));
