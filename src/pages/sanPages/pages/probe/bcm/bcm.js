/*
 * @Description: 网络探测监控页面
 * @Author: <EMAIL>
 * @Date: 2022-03-04 17:09:34
 */

import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {BcmSDK} from '@baidu/new-bcm-sdk';
import {BcmChartPanel} from '@baidu/new-bcm-sdk-san';
import {Notification} from '@baidu/sui';
import {OutlinedRefresh} from '@baidu/sui-icon';
import moment from 'moment';

import {ProbeStatus} from '@/pages/sanPages/common/enum';
import monitorConfig from '@/pages/sanPages/utils/monitorConfig';
import './bcm.less';
import {kXhrOptions} from '@/pages/sanPages/utils/helper';

const {asPage, template, withSidebar, invokeSUI, invokeSUIBIZ, invokeAppComp, invokeComp} = decorators;
const {ProbeMetrics, shortcutItems} = monitorConfig;

const tpl = html`
    <template>
        <s-app-detail-page class="{{klass}}">
            <div class="monitor-wrap">
                <div class="monitor-item-box">
                    <h2>监控信息</h2>
                    <div class="button-wrap">
                        <span class="alarm-state">
                            <span>时间：</span>
                            <s-date-picker-date-range-picker
                                value="{=timeRange=}"
                                width="{{310}}"
                                mode="second"
                                range="{{range}}"
                                on-change="onTimeChange"
                                shortcut="{{shortcutItems}}"
                            />
                        </span>
                        <s-button class="s-icon-button" on-click="onTimeRefresh">
                            <outlined-refresh class="icon-class" />
                        </s-button>
                        <s-button class="alarm-detail left_class" on-click="alarmDetail"> 报警详情 </s-button>
                    </div>
                    <div class="probe-monitor-trends">
                        <div class="monitor-trend-box" s-for="item,index in chart">
                            <bcm-chart-panel
                                s-ref="alarm-chart-{{index}}"
                                withFilter="{{false}}"
                                scope="{{item.scope}}"
                                dimensions="{{item.dimensions}}"
                                statistics="{{item.statistics}}"
                                title="{{item.title}}"
                                options="{{options}}"
                                api-type="dimensions"
                                startTime="{=startTime=}"
                                endTime="{=endTime=}"
                                period="{{monitorDefaultPeriod}}"
                                metrics="{{item.metrics}}"
                                unit="{{item.unit}}"
                                bitUnit="{{item.bitUnit}}"
                                width="{{'auto'}}"
                                height="{{230}}"
                                sdk="{{bcmSdk}}"
                                proccessor="{{proccessor}}"
                            >
                            </bcm-chart-panel>
                        </div>
                    </div>
                </div>
            </div>
        </s-app-detail-page>
    </template>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class ProbeMonitor extends Component {
    static components = {
        'bcm-chart-panel': BcmChartPanel,
        'outlined-refresh': OutlinedRefresh
    };

    initData() {
        return {
            klass: 'vpc-probe-monitor',
            instance: {},
            chart: [],
            options: {
                color: ['#2468f2', '#5FB333'],
                legend: {
                    x: 'right',
                    y: 'top'
                },
                dataZoom: {start: 0}
            },
            range: {
                begin: new Date(moment().subtract(40, 'day').valueOf()),
                end: new Date(moment().valueOf())
            },
            timeRange: {
                begin: new Date(moment().subtract(1, 'hour').valueOf()),
                end: new Date(moment().valueOf())
            },
            startTime: moment().subtract(1, 'hour').utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
            endTime: moment().utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
            endOriginTime: moment().valueOf(),
            bcmSdk: new BcmSDK({client: window.$http, context: window.$context}),
            shortcutItems,
            proccessor: this.proccessor.bind(this)
        };
    }

    static filters = {
        statusStyle(value) {
            return ProbeStatus.fromValue(value).styleClass || '';
        },
        statusText(value) {
            return value ? ProbeStatus.getTextFromValue(value) : '-';
        }
    };

    async attached() {
        this.loadDetail();
        this.data.set('loading', false);
        this.watch('timeRange', timeRange => {
            this.onTimeChange({value: timeRange});
        });
    }

    onTimeChange({value}) {
        let startTime = moment(value.begin).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        let endTime = moment(value.end).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        let ms = moment(value.end) - moment(value.begin);
        let hourTime = Math.round(ms / 86400000);
        switch (true) {
            case hourTime <= 1:
                this.data.set('monitorDefaultPeriod', 60);
                break;
            case hourTime <= 3:
                this.data.set('monitorDefaultPeriod', 300);
                break;
            case hourTime <= 7:
                this.data.set('monitorDefaultPeriod', 600);
                break;
            case hourTime <= 14:
                this.data.set('monitorDefaultPeriod', 1800);
                break;
            case hourTime <= 40:
                this.data.set('monitorDefaultPeriod', 3600);
                break;
            default:
                break;
        }
        this.data.set('startTime', startTime);
        this.data.set('endTime', endTime);
        this.onRefresh();
    }

    onRefresh() {
        let chartConfig = this.data.get('chart');
        u.map(chartConfig, (item, i) => {
            this.ref(`alarm-chart-${i}`).loadMetrics();
        });
    }
    onTimeRefresh() {
        if (this.data.get('timeRange.end').valueOf() >= this.data.get('endOriginTime')) {
            this.data.set('timeRange.end', new Date(moment().valueOf()));
        } else {
            this.onRefresh();
        }
    }
    onRegionChange() {
        location.hash = '#/vpc/probe/list';
    }

    loadDetail() {
        return this.$http
            .getProbeDetail(this.data.get('context').probeId, {'x-silent-codes': ['ProbeIdInvalidException']})
            .then(data => {
                this.data.set('instance', data);
                this.initMonitor();
            });
    }

    initMonitor() {
        const payload = this.data.get('instance');
        const protocol = this.data.get('instance.protocol');
        let chartConfig = [];
        let dimensions = [];
        u.each(payload.sourceIps, value => {
            dimensions.push(`InstanceId:${payload.probeId};SourceIp:${value}`);
        });
        u.each(ProbeMetrics, item => {
            let config = {
                scope: 'BCE_NP',
                period: 60,
                statistics: 'average',
                title: item.title,
                unit: item.unit,
                bitUnit: item.bitUnit,
                metrics: item.metrics,
                dimensions
            };
            if (item.type !== 'DNS' || protocol === 'DNS') {
                chartConfig.push(config);
            }
        });
        this.data.set('chart', chartConfig);
    }

    alarmDetail() {
        let probeId = this.data.get('instance.probeId');
        let region = window.$context.getCurrentRegionId();
        let protocol = this.data.get('instance.protocol');
        window.open(
            `/bcm/#/bcm/alarm/rule/list~scope=BCE_NP&dimensions=InstanceId:${probeId}&typeName=${protocol}&region=${region}`,
            '_blank'
        );
    }

    proccessor(data) {
        const statistics = this.data.get('metrics.statisticsMethods');
        if (data && data.series && data.series.length > 0) {
            data.series.forEach(series => {
                series.name = series.name.split(',')[1] || series.name;
                series.data = series.data.map(item => {
                    if (!item || Object.keys(item).length === 0) {
                        let obj = {};
                        obj[statistics] = 0;
                        return obj;
                    }
                    return item;
                });
            });
        }
        return data;
    }
    onRegionChange() {
        location.hash = '#/vpc/probe/list';
    }
}
export default San2React(Processor.autowireUnCheckCmpt(ProbeMonitor));
