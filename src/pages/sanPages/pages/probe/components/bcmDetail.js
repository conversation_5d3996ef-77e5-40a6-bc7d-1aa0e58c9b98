/*
 * @Description: 网络探测监控侧边栏
 * @Author: <EMAIL>
 * @Date: 2022-03-04 18:20:57
 */

import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import u from 'lodash';
import {BcmSDK} from '@baidu/new-bcm-sdk';
import {BcmChartPanel} from '@baidu/new-bcm-sdk-san';
import {Drawer} from '@baidu/sui';
import './create.less';

import monitorConfig from '@/pages/sanPages/utils/monitorConfig';

const {ProbeMetrics} = monitorConfig;
const {asComponent, invokeSUI, invokeSUIBIZ, template} = decorators;

const tpl = html`
    <template>
        <s-drawer open="{=open=}" direction="right" otherClose="{{false}}">
            <div slot="title" class="bcm-header">
                网络探测名称：{{name}}
                <a href="{{bcm}}" target="_blank" class="bcm-detail-link">查看更多&gt; </a>
            </div>
            <div class="monitor-wrap">
                <div s-for="item,index in chartConfig">
                    <bcm-chart-panel
                        withFilter="{{false}}"
                        scope="{{item.scope}}"
                        dimensions="{{item.dimensions}}"
                        statistics="{{item.statistics}}"
                        title="{{item.title}}"
                        time="{{item.time}}"
                        height="{{230}}"
                        width="{{350}}"
                        options="{{item.options}}"
                        api-type="dimensions"
                        period="{{item.period}}"
                        metrics="{{item.metrics}}"
                        unit="{{item.unit}}"
                        bitUnit="{{item.bitUnit}}"
                        sdk="{{bcmSdk}}"
                        proccessor="{{proccessor}}"
                    >
                    </bcm-chart-panel>
                </div>
            </div>
        </s-drawer>
    </template>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
class BcmDetail extends Component {
    components = {
        'bcm-chart-panel': BcmChartPanel,
        's-drawer': Drawer
    };

    initData() {
        return {
            open: true,
            chartConfig: [],
            bcmSdk: new BcmSDK({client: window.$http, context: window.$context}),
            proccessor: this.proccessor.bind(this)
        };
    }

    inited() {
        const payload = this.data.get('payload');
        const protocol = this.data.get('protocol');
        this.data.set('bcm', '#/vpc/probe/monitor?vpcId=' + payload.vpcId + '&probeId=' + payload.probeId);
        this.initChart();
    }

    initChart() {
        let payload = this.data.get('payload');
        let protocol = this.data.get('protocol');
        let chartConfig = [];
        let options = {
            color: ['#2468f2', '#5FB333'],
            legend: {
                x: 'right',
                y: 'top'
            },
            dataZoom: {start: 0}
        };
        let dimensions = [];
        u.each(payload.sourceIps, value => {
            dimensions.push(`InstanceId:${payload.probeId};SourceIp:${value}`);
        });
        u.each(ProbeMetrics, item => {
            let config = {
                scope: 'BCE_NP',
                period: 60,
                statistics: 'average',
                title: item.title,
                unit: item.unit,
                bitUnit: item.bitUnit,
                metrics: item.metrics,
                dimensions,
                options
            };
            if (item.type !== 'DNS' || protocol === 'DNS') {
                chartConfig.push(config);
            }
        });
        this.data.set('chartConfig', chartConfig);
    }

    proccessor(data) {
        const statistics = this.data.get('metrics.statisticsMethods');
        if (data && data.series && data.series.length > 0) {
            data.series.forEach(series => {
                series.name = series.name.split(',')[1] || series.name;
                series.data = series.data.map(item => {
                    if (!item || Object.keys(item).length === 0) {
                        let obj = {};
                        obj[statistics] = 0;
                        return obj;
                    }
                    return item;
                });
            });
        }
        return data;
    }
}

export default Processor.autowireUnCheckCmpt(BcmDetail);
