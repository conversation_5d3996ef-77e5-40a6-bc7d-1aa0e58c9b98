import rule from '@/pages/sanPages/utils/rule';
const {IP} = rule;

export const formValidator = (self, formName) => ({
  name: [
    {required: true, message: '请填写名称'},
    {pattern: /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{0,64}$/, message: '格式不符合要求'}
  ],
  vpcId: [
    {required: true, message: '请选择所在网络'}
  ],
  subnetId: [
    {required: true, message: '请选择所在子网'}
  ],
  frequency: [
    {required: true, message: '请选择探测频率'}
  ],
  destIp: [
    {required: true, message: '请输入探测目的IP'},
    {pattern: IP, message: '格式不正确'}
  ],
  destPort: [
    {required: true, message: '请输入探测目的IP端口'},
    {
      validator: (rule, value, callback) => {
        if (!/^[1-9]\d*$/.test(value)) {
          return callback('格式不正确');
        }
        if (value < 0 || value > 65535) {
          return callback('格式不正确');
        }
        callback();
      }
    }
  ],
  payload: [
    {required: true, message: () => {
      let formData = formName ? self.data.get(formName) : self.data.get('formData');
      let protocol = formData.protocol;
      const title = protocol === 'UDP' ? '请输入探测字符串' : '请输入探测域名';
      return title;
    }},
    {
      validator: (rule, value, callback) => {
        let formData = formName ? self.data.get(formName) : self.data.get('formData');
        let protocol = formData.protocol;
        value = value || formData.payload;
        if (protocol === 'UDP' && !/^[^\u4e00-\u9fa5]{0,512}$/.test(value)) {
          return callback('字符串长度1-512，不支持中文。');
        }
        if (protocol === 'DNS'
        && (!/^(?:http(s)?:\/\/)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:/?#[\]@!\$&'\*\+,;=.]$/.test(value)
        || value.length > 512)) {
          return callback('域名格式不正确且域名长度不超过512。');
        }
        callback();
      }
    }
  ],
  nextHopType: [
    {
      validator: (rule, value, callback) => {
        let formData = formName ? self.data.get(formName) : self.data.get('formData');
        let isAuto = formData.autoGenerateRouteRule;
        value = value || formData.nextHopType;
        if (isAuto && !value) {
          return callback('请选择路由类型');
        }
        callback();
      }
    }
  ],
  nextHop: [
    {
      validator: (rule, value, callback) => {
        let formData = formName ? self.data.get(formName) : self.data.get('formData');
        let isAuto = formData.autoGenerateRouteRule;
        value = value || formData.nextHop;
        if (isAuto && !value) {
          return callback('请选择下一跳实例');
        }
        callback();
      }
    }
  ],
  description: [{min: 0, max: 200, message: '长度0到200个字符'}]
});
