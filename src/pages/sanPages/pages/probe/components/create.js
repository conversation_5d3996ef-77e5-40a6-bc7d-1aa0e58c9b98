/*
 * @Description: 网络探测创建
 * @Author: <EMAIL>
 * @Date: 2022-02-28 11:11:08
 */

import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {checker} from '@baiducloud/bce-opt-checker';
import {OutlinedPlus} from '@baidu/sui-icon';

import {
    RouteType,
    RouteStatus,
    NatStatus,
    VpnStatus,
    PeerConnStatus,
    DcGatewayStatus,
    ProbeProtocol
} from '@/pages/sanPages/common/enum';
import {disable_vpn_region} from '@/pages/sanPages/common/flag';
import rules from '../rules';
import {formValidator} from './helper';
import {checkIsInSubnet} from '@/pages/sanPages/utils/common';
import RULE from '@/pages/sanPages/utils/rule';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import './create.less';

const AllRegion = window.$context.getEnum('AllRegion');
const {asComponent, invokeSUI, invokeSUIBIZ, template} = decorators;
const kXhrOptions = {'X-silence': true};
const tpl = html`
<div>
  <s-dialog
    class="vpc-probe-create"
    open="{{true}}"
    title="{{probeInfo ? '编辑网络探测' : '创建网络探测'}}"
  >
    <s-form
      s-ref="form"
      rules="{{validateRule}}"
      data="{=formData=}"
      label-align="left"
    >
      <s-form-item label="探测名称："
        prop="name"
        help="大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65"
      >
        <s-input width="{{300}}" value="{=formData.name=}" />
      </s-form-item>
      <s-form-item
        label="所在网络"
        prop="vpcId"
      >
        <s-select width="{{300}}"
          datasource="{{vpcList}}"
          value="{=formData.vpcId=}"
          on-change="vpcChange($event)"
          class="{{quotaError ? 'error_select' : ''}}"
          disabled="{{probeInfo}}"
        ></s-select>
      </s-form-item>
      <!--bca-disable-next-line-->
      <div class='error_tip' s-if="{{quotaError}}" s-html="quotaErrorMessageUnified"></div>
      <s-form-item
        label="所在子网："
        prop="subnetId"
      >
        <s-select width="{{300}}"
          datasource="{{subnetDatasource}}"
          value="{=formData.subnetId=}"
          on-change="subnetChange"
          disabled="{{loading || probeInfo}}"
        ></s-select>
      </s-form-item>
      <s-form-item
        class="probe-way-item"
        label="探测方式："
        prop="protocol"
      >
        <s-radio-radio-group
          datasource="{{protocolList}}"
          radioType="button"
          value="{=formData.protocol=}"
          disabled="{{probeInfo}}"
          on-change="onChange"
        >
      </s-form-item>
      <s-form-item
        s-if="{{formData.protocol === 'UDP'}}"
        label="探测字符串："
        prop="payload"
      >
        <s-input width="{{300}}" value="{=formData.payload=}" />
        <s-tooltip
          class="bind-tip"
          content="用户侧的UDP server根据探测字符串进行回包，若回包则表示网络连通。">
          <s-icon name="question-mark"/>
        </s-tooltip>
      </s-form-item>
      <s-form-item
        s-if="{{formData.protocol === 'DNS'}}"
        label="探测域名："
        prop="payload"
      >
        <s-input width="{{300}}" value="{=formData.payload=}" />
        <s-tooltip
          class="bind-tip"
          content="DNS服务器对探测域名进行解析并回包，解析成功则表示DNS服务正常。">
          <s-icon name="question-mark"/>
        </s-tooltip>
      </s-form-item>
      <s-form-item
        class="probe-frequency-item"
        label="探测频率："
        prop="frequency"
      >
        <s-radio-radio-group
          datasource="{{frequencyDatasource}}"
          radioType="button"
          value="{=formData.frequency=}"
        >
        </s-radio-radio-group>
        <span class="frequency-unit">次/分钟</span>
      </s-form-item>
      <s-form-item
        label="探测源IP："
        prop="sourceIps"
        class="ip-item"
      >
        <s-table
          datasource="{{table.datasource}}"
          columns="{{table.columns}}"
        >
          <div slot="empty">
            <s-empty>
                <div slot="action">
                </div>
            </s-empty>
          </div>
          <div slot="c-ip">
            <s-radio-radio-group
              class="ip-radio"
              datasource="{{ipDatasource}}"
              value="{{row.isCustom}}"
              disabled="{{probeInfo}}"
              on-change="typeChange($event, rowIndex)" />
            <div class="probe-custom-ip-wrapper">
              <s-input
                s-if="row.isCustom"
                placeholder="请输入IP地址"
                value="{=row.sourceIp=}"
                disabled="{{probeInfo}}"
                on-input="inputChange($event, rowIndex)" />
              <label s-if="row.isCustom && row.error" class="probe-custom-ip-err">{{ row.error }}</label>
            </div>
          </div>
          <div slot="c-operation">
            <s-button skin="stringfy"
              s-if="!row.primary" disabled="{{probeInfo}}" on-click="onRemove(rowIndex)">删除</s-button>
          </div>
        </s-table>
        <s-tooltip
          trigger="{{disableAddIp.message ? 'hover' : ''}}" placement="right">
          <div slot="content">{{ disableAddIp.message }}</div>
          <s-button
            skin="stringfy"
            on-click="addIp()"
            disabled="{{disableAddIp.disable || probeInfo}}"
          ><outlined-plus/>添加源IP</s-button>
        </s-tooltip>
      </s-form-item>
      <div class="destip-wrapper">
        <s-form-item
          s-if="{{formData.protocol === 'DNS'}}"
          label="探测目的IP："
          prop="destIp"
          class="destip"
        >
          <s-radio-radio-group
            class="ip-radio"
            datasource="{{dnsDatasource}}"
            value="{{dnsRadio}}"
            on-change="switch($event)"/>
            <s-input
              class="destip-input"
              width="{{200}}"
              value="{=formData.destIp=}"
              placeholder="{{dnsRadio ? '请输入探测目的IP' : ''}}"
              disabled="{{!dnsRadio}}"
             />
            <s-input
              class="destip-input"
              width="{{200}}"
              value="{=formData.destPort=}"
              placeholder="{{dnsRadio ? '请输入探测目的端口' : ''}}"
              disabled="{{!dnsRadio}}" />
        </s-form-item>
      </div>
      <div class="destip-wrapper">
        <s-form-item
          s-if="{{formData.protocol !== 'DNS'}}"
          label="探测目的IP："
          prop="destIp"
          class="destip"
        >
          <s-input
            class="destip-input"
            width="{{200}}"
            value="{=formData.destIp=}"
            placeholder="请输入探测目的IP" />
          <s-input
            s-if="{{formData.protocol !== 'ICMP'}}"
            class="destip-input"
            width="{{200}}"
            value="{=formData.destPort=}"
            placeholder="请输入探测目的端口" />
        </s-form-item>
      </div>
      <s-form-item
        s-if="formData.protocol !== 'DNS' || dnsRadio"
        class="probe-auto-item"
        label="自动生成路由："
        prop="autoGenerateRouteRule"
      >
        <s-switch disabled="{{probeInfo}}" checked="{=formData.autoGenerateRouteRule=}" on-change="autoGenerateChange"/>
      </s-form-item>
      <div s-if="formData.autoGenerateRouteRule && (formData.protocol !== 'DNS' || dnsRadio)" class="probe-route-wrap">
        <s-form-item
          label="源端下一跳路由："
          prop="nextHopType"
          class="route-item"
        >
          <s-select
            class="probe-route-type"
            width="200"
            placeholder="请选择路由类型"
            datasource="{{routeTypeList}}"
            value="{=formData.nextHopType=}"
            on-change="nextHopTypeChange" />
        </s-form-item>
        <s-form-item
          prop="nextHop"
        >
          <s-select
            width="200"
            filterable
            disabled="{{switchLoading}}"
            datasource="{{routeList}}"
            value="{=formData.nextHop=}" />
        </s-form-item>
      </div>
      <s-form-item label="描述：" prop="description">
        <s-input-text-area width="{{300}}"
          maxLength="{{200}}"
          height="{{100}}"
          multiline
          value="{=formData.description=}" />
      </s-form-item>
    </s-form>
    <div slot="footer">
      <s-button on-click="onClose">取消</s-button>
      <s-button skin="primary" disabled="{{disableSub}}" on-click="doSubmit">确定</s-button>
    </div>
  </s-dialog>
</div>
`;

@asComponent('@create-probe')
@template(tpl)
@invokeSUI
class CreateProbe extends Component {
    static components = {
        'outlined-plus': OutlinedPlus
    };
    initData() {
        return {
            flag: FLAG,
            vpcList: [],
            subnetDatasource: [],
            initSubnetDatasource: [],
            subnetIpv6Cidr: '',
            dnsRadio: false,
            formData: {
                name: '',
                vpcId: '',
                subnetId: '',
                frequency: 20,
                protocol: 'ICMP',
                packetLen: 64,
                payload: '',
                destIp: '',
                destPort: '',
                autoGenerateRouteRule: false,
                nextHopType: '',
                nextHop: '',
                description: ''
            },
            quotaError: false,
            frequencyDatasource: [
                {text: 10, value: 10},
                {text: 20, value: 20},
                {text: 30, value: 30}
            ],
            protocolList: ProbeProtocol.toArray(),
            table: {
                datasource: [
                    {
                        isCustom: false,
                        primary: true,
                        sourceIp: ''
                    }
                ],
                columns: [
                    {name: 'ip', label: 'IP地址'},
                    {name: 'operation', label: '操作', width: 90}
                ]
            },
            ipDatasource: [
                {
                    text: '自动分配',
                    value: false
                },
                {
                    text: '指定',
                    value: true
                }
            ],
            dnsDatasource: [
                {
                    text: '百度智能云DNS服务器',
                    value: false
                },
                {
                    text: '用户自建DNS服务器',
                    value: true
                }
            ],
            routeTypeList: [],
            routeList: [],
            validateRule: formValidator(this),
            vpcMap: {}
        };
    }

    static computed = {
        disableAddIp() {
            let num = this.data.get('table.datasource').length;
            let subnetId = this.data.get('formData.subnetId');
            let {addIp} = checker.check(rules, '', 'addIp', {num, subnetId});
            return addIp;
        },
        quotaErrorMessage() {
            const vpcName = this.data.get('formData.vpcMap[formData.vpcId]');
            const ticketUrl = this.data.get('ContextService.Domains.ticket');
            return `您的私有网络${vpcName}下弹性网卡数量已经达到配额，如需更多弹性网卡，可以通过<a href="${ticketUrl}/#/ticket/create" target="_blank">工单</a>申请`;
        },
        quotaErrorMessageXS() {
            const vpcName = this.data.get('formData.vpcMap[formData.vpcId]');
            return `您的私有网络${vpcName}下弹性网卡数量已经达到配额`;
        },
        quotaErrorMessageUnified() {
            const vpcName = this.data.get('formData.vpcMap[formData.vpcId]');
            const isXS = this.data.get('flag.NetworkSupportXS');
            const ticketUrl = this.data.get('ContextService.Domains.ticket');

            if (!isXS) {
                return `您的私有网络${vpcName}下弹性网卡数量已经达到配额，如需更多弹性网卡，可以通过<a href="${ticketUrl}/#/ticket/create" target="_blank">工单</a>申请`;
            } else {
                return `您的私有网络${vpcName}下弹性网卡数量已经达到配额`;
            }
        }
    };

    inited() {
        if (FLAG.NetworkSupportXS) {
            this.data.set('dnsDatasource[0].text', '智能云DNS服务器');
        }
        let vpcs = this.data.get('vpcs');
        let probeInfo = this.data.get('probeInfo');
        let vpcMap = {};
        u.each(vpcs, item => {
            vpcMap[item.value] = item.text;
        });
        this.data.set(
            'vpcList',
            vpcs.map(item => ({
                text: `${item.vpcInfo.name}（${item.vpcInfo.cidr}）`,
                value: item.vpcInfo.shortId
            }))
        );
        this.data.set('formData.vpcMap', vpcMap);

        // 编辑状态
        if (probeInfo) {
            this.data.set('formData.vpcId', probeInfo.vpcId);
            this.data.set('formData', probeInfo);
            let sourceIps = probeInfo.sourceIps;
            if (sourceIps?.length) {
                this.data.set(
                    'table.datasource',
                    sourceIps.map((item, index) => ({
                        isCustom: true,
                        primary: index === 0 ? true : false,
                        sourceIp: item
                    }))
                );
            }
            if (probeInfo.sourceIpNum > 0) {
                for (let i = 0; i < probeInfo.sourceIpNum; i++) {
                    this.data.push('table.datasource', {
                        isCustom: false,
                        primary: false,
                        sourceIp: ''
                    });
                }
            }
            this.data.set('formData.autoGenerateRouteRule', !!probeInfo.nextHop);
        } else {
            this.data.set('formData.vpcId', vpcs[0].vpcInfo.shortId);
        }
    }

    attached() {
        this.getWhiteList();
        this.loadSubnets();
    }

    getTgwList() {
        let vpcId = this.data.get('formData.vpcId');
        let vpcInfo = u.filter(this.data.get('vpcs'), item => item.vpcInfo.shortId === vpcId)[0].vpcInfo;
        if (!vpcInfo.csnId || !window.$storage.get('csnSts')) {
            return;
        }
        let region = window.$context.getCurrentRegionId();
        return this.$http.getTgwInstanceList(vpcInfo.csnId, {region}).then(res => {
            let tgwInstanceList = res.tgws.map(item => {
                return {text: item.tgwId, value: item.tgwId};
            });
            this.data.set('tgwInstanceList', tgwInstanceList);
            return Promise.resolve(tgwInstanceList);
        });
    }

    enableVpnRegion() {
        let region = window.$context.getCurrentRegionId();
        if (region === AllRegion.HK02) {
            return true;
        }
        return u.indexOf(disable_vpn_region, region) === -1;
    }

    natWhiteList() {
        return this.data.get('flag.NetworkSupportNat');
    }

    peerconnWhiteList() {
        const serversType = Object.keys(window.$context.SERVICE_TYPE);
        return serversType.indexOf('PEERCONN') > -1 && window.$context.getCurrentRegionId() !== AllRegion.BJKS;
    }

    // 不在白名单的路由类型不展示
    getWhiteList() {
        // 先隐藏掉实例，nat，vpn
        let all = [
            // this.enableVpnRegion(),
            // this.natWhiteList(),
            this.peerconnWhiteList()
        ];
        Promise.all(all).then(white => {
            // let [VPN, NAT, PEERCONN] = white;
            let [PEERCONN] = white;
            const whiteList = [];
            const whiteMap = {
                // 'CUSTOM': true,
                // 'NAT': NAT,
                // 'VPN': VPN,
                PEERCONN: PEERCONN,
                GW: true,
                TGW: true
            };
            u.each(whiteMap, (item, key) => {
                item && whiteList.push(key);
            });
            let routeTypeList = RouteType.toArray(...whiteList);
            this.data.set('routeTypeList', routeTypeList);
        });
    }

    getProbeQuota() {
        let vpcId = this.data.get('formData.vpcId');
        let vpcInfo = u.filter(this.data.get('vpcs'), item => item.vpcInfo.shortId === vpcId)[0].vpcInfo;
        return this.$http.getProbeQuota({vpcId: vpcInfo.vpcId}).then(data => this.data.set('quotaFree', data.free));
    }

    vpcChange(e) {
        this.data.set('formData.vpcId', e.value);
        this.data.set('formData.nextHopType', '');
        this.data.set('formData.nextHop', '');
        this.data.set('formData.routeList', []);
        this.data.set('formData.subnetId', '');
        this.initSourceIp();
        this.getProbeQuota();
        this.loadSubnets();
    }

    subnetChange() {
        this.initSourceIp();
    }

    initSourceIp() {
        this.data.set('table.datasource', [
            {
                isCustom: false,
                primary: true,
                sourceIp: ''
            }
        ]);
    }

    loadSubnets() {
        let vpcId = this.data.get('formData.vpcId');
        let vpcInfo = u.filter(this.data.get('vpcs'), item => item.vpcInfo.shortId === vpcId)[0].vpcInfo;
        let payload = {vpcId: vpcInfo.vpcId};
        this.data.set('loading', true);
        this.$http
            .getSubnetList(payload)
            .then(data => {
                let datasource = [];
                u.each(data, item => {
                    // 暂时不支持ipv6先注释掉
                    // let text = '';
                    // if (item.ipv6Cidr) {
                    //   text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}(${item.ipv6Cidr})`;
                    // }
                    // else {
                    //   text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}`;
                    // }
                    let text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}`;
                    datasource.push({
                        value: item.shortId,
                        subnetId: item.subnetId,
                        text: text,
                        cidr: item.cidr,
                        ipv6Cidr: item.ipv6Cidr
                    });
                });
                this.data.set('subnetDatasource', datasource);
                this.data.set('initSubnetDatasource', datasource);
            })
            .finally(() => this.data.set('loading', false));
    }

    inputChange(e, index) {
        this.data.set(`table.datasource[${index}].sourceIp`, e.value);
        this.sourceIpCheck(index, e.value);
    }

    addIp() {
        this.data.push('table.datasource', {
            isCustom: false,
            primary: false,
            sourceIp: ''
        });
    }

    autoGenerateChange() {
        this.data.set('formData.nextHopType', '');
        this.data.set('formData.nextHop', '');
    }

    typeChange(e, index) {
        this.data.set(`table.datasource[${index}].error`, '');
        this.data.set(`table.datasource[${index}].sourceIp`, '');
        this.data.set(`table.datasource[${index}].isCustom`, e.value);
    }

    getSubnetIdsByValue() {
        return u.pluck(this.data.get('subnetDatasource'), 'subnetId');
    }

    getInstanceListBySubnets(name) {
        let ids = this.getSubnetIdsByValue();
        return this.$http.bccInstanceSubnetsList({ids, name: name || ''}, kXhrOptions).then(data => {
            let result = [];
            u.each(data.servers, item => {
                if (!u.isEmpty(RouteStatus.fromValue(item.status))) {
                    let text = item.name + (item.internalIp ? `（${item.internalIp}）` : '');
                    result.push({
                        value: item.instanceId,
                        text
                    });
                }
            });
            return Promise.resolve(result);
        });
    }

    getNatList(name) {
        let vpcId = this.data.get('formData.vpcId');
        let vpcInfo = u.filter(this.data.get('vpcs'), item => item.vpcInfo.shortId === vpcId)[0].vpcInfo;
        let query = {
            vpcId: vpcInfo.vpcId,
            pageNo: 1,
            pageSize: 10000
        };
        if (name) {
            query.name = name;
        }
        return this.$http.getNatList(query, kXhrOptions).then(res => {
            let result = [];
            let title = '';
            if (res) {
                u.each(res.result, item => {
                    // 只显示active状态的nat
                    if (u.indexOf([NatStatus.ACTIVE], item.status) > -1) {
                        title = item.name;
                        result.push({
                            value: item.id,
                            primaryId: item.primaryId,
                            status: item.status,
                            text: title
                        });
                    }
                });
            }
            return Promise.resolve(result);
        });
    }

    getVpnList() {
        let vpcId = this.data.get('formData.vpcId');
        let vpcInfo = u.filter(this.data.get('vpcs'), item => item.vpcInfo.shortId === vpcId)[0].vpcInfo;
        let query = {vpcId: vpcInfo.vpcId};
        return this.$http.getVpnList(query, kXhrOptions).then(data => {
            let result = [];
            u.each(data.result, item => {
                if (u.indexOf([VpnStatus.ACTIVE], item.status) > -1) {
                    result.push({
                        value: item.vpnId,
                        text: `${item.vpnName}/${item.vpnId}`
                    });
                }
            });
            return Promise.resolve(result);
        });
    }

    getPeerConnList(localIfName) {
        let query = {
            localVpcShortId: this.data.get('formData.vpcId'),
            pageNo: 1,
            pageSize: 10000
        };
        if (localIfName) {
            query.localIfName = localIfName;
        }
        return this.$http.peerconnList(query, kXhrOptions).then(res => {
            let result = [];
            u.each(res.result, item => {
                if (u.indexOf([PeerConnStatus.ACTIVE], item.status) > -1) {
                    let name = item.localIfName + '（' + item.localIfId + '）';
                    result.push({
                        value: item.localIfId,
                        text: name
                    });
                }
            });
            return Promise.resolve(result);
        });
    }

    getGwList(name) {
        let query = {
            vpcId: this.data.get('formData.vpcId'),
            pageNo: 1,
            pageSize: 10000
        };
        if (name) {
            query.keywordType = 'name';
            query.keyword = name;
        }
        return this.$http.dcgwList(query, kXhrOptions).then(res => {
            let result = [];
            u.each(res.result, item => {
                if (u.indexOf([DcGatewayStatus.RUNNING], item.status) > -1) {
                    let name = item.name + '/' + item.id;
                    result.push({
                        value: item.id,
                        text: name
                    });
                }
            });
            return Promise.resolve(result);
        });
    }

    async nextHopTypeChange({value}) {
        this.data.set('routeList', []);
        this.data.set('formData.nextHop', '');
        this.data.set('switchLoading', true);
        try {
            await this.loadSource(value);
            this.data.set('switchLoading', false);
        } catch (e) {
            this.data.set('switchLoading', false);
        }
    }

    loadSource(value) {
        let type = value || this.data.get('formData.nextHopType');
        const requesetMap = {
            custom: this.getInstanceListBySubnets,
            nat: this.getNatList,
            vpn: this.getVpnList,
            peerConn: this.getPeerConnList,
            dcGateway: this.getGwList,
            vpc2tgw: this.getTgwList
        };
        if (!requesetMap[type]) {
            return;
        }
        return requesetMap[type].apply(this).then(res => {
            this.data.set('routeList', res);
            return Promise.resolve();
        });
    }

    hasRepeat(arr) {
        const nums = [];
        for (let i = 0; i < arr.length; i++) {
            if (nums.includes(arr[i])) {
                return true;
            } else if (arr[i] !== '') {
                nums.push(arr[i]);
            }
        }
        return false;
    }

    sourceIpCheck(index, val) {
        let customIpList = this.data.get('table.datasource').map(item => item.sourceIp);
        let formData = this.data.get('formData');
        if (!formData.subnetId) {
            this.data.set(`table.datasource[${index}].error`, '请选择所在子网');
            return;
        }
        if (!val) {
            this.data.set(`table.datasource[${index}].error`, '请填写IP地址');
            return;
        }
        if (!RULE.IP.test(val)) {
            this.data.set(`table.datasource[${index}].error`, 'IP地址格式有误');
            return;
        }
        if (this.hasRepeat(customIpList)) {
            this.data.set(`table.datasource[${index}].error`, '源IP地址不能重复');
            return;
        }
        const subnetItem = u.find(this.data.get('initSubnetDatasource'), item => item.value === formData.subnetId);
        if (subnetItem && !checkIsInSubnet(val + '/32', subnetItem.cidr)) {
            this.data.set(`table.datasource[${index}].error`, '不属于所在子网');
            return;
        }
        this.data.set(`table.datasource[${index}].error`, '');
    }

    onRemove(rowIndex) {
        this.data.removeAt('table.datasource', rowIndex);
    }

    // 完整form校验，包含自定义IP的校验
    async completeCheck() {
        return new Promise((resolve, reject) => {
            this.ref('form')
                .validateFields()
                .then(() => {
                    let ipList = this.data.get('table.datasource');
                    for (let i = 0; i < ipList.length; i++) {
                        if (ipList[i].isCustom) {
                            this.sourceIpCheck(i, ipList[i].sourceIp);
                        }
                        let err = this.data.get(`table.datasource[${i}]`).error;
                        if (err) {
                            reject();
                            return;
                        }
                    }
                    resolve();
                })
                .catch(() => {
                    reject();
                });
        });
    }

    async doSubmit() {
        await this.completeCheck();
        let formData = this.data.get('formData');
        delete formData.vpcMap;
        let sourceIpList = this.data.get('table.datasource');
        let sourceIpNum = 0;
        let sourceIps = [];
        u.forEach(sourceIpList, item => {
            if (item.isCustom) {
                sourceIps.push(item.sourceIp);
            } else {
                sourceIpNum++;
            }
        });

        let payload = {
            ...formData,
            sourceIps,
            sourceIpNum
        };
        if (sourceIpNum < 1) {
            delete payload.sourceIpNum;
        }
        this.data.set('disableSub', true);
        if (this.data.get('probeInfo')) {
            let {destPort, protocol} = payload;
            let strPayload = payload.payload;
            payload = u.pick(payload, ['frequency', 'name', 'description', 'destIp', 'nextHop', 'nextHopType']);
            if (protocol !== 'ICMP') {
                payload = {
                    ...payload,
                    destPort
                };
            }
            if (protocol === 'UDP') {
                payload = {
                    ...payload,
                    payload: strPayload
                };
            }
            this.$http
                .editProbe(formData.probeId, payload)
                .then(res => {
                    this.fire('success');
                    this.data.set('disableSub', false);
                    this.onClose();
                })
                .catch(() => {
                    this.data.set('disableSub', false);
                });
        } else {
            this.$http
                .createProbe(payload)
                .then(res => {
                    this.fire('success');
                    this.data.set('disableSub', false);
                    this.onClose();
                })
                .catch(() => {
                    this.data.set('disableSub', false);
                });
        }
    }

    onClose() {
        this.dispose();
    }
    switch(e) {
        this.data.set(`dnsRadio`, e.value);
        this.onChange();
    }

    onChange() {
        let dnsRadio = this.data.get('dnsRadio');
        this.nextTick(() => {
            let protocol = this.data.get('formData.protocol');
            if (protocol === 'DNS' && !dnsRadio) {
                this.data.set('formData.destIp', '***************');
                this.data.set('formData.destPort', '53');
            } else {
                this.data.set('formData.destIp', '');
                this.data.set('formData.destPort', '');
            }
        });
    }
}
export default Processor.autowireUnCheckCmpt(CreateProbe);
