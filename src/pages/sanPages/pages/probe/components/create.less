.vpc-probe-create {
  .s-form-item-label {
    width: 120px;
    text-align: left;
  }
  .probe-way-item {
    .s-row {
      align-items: center;
    }
  }
  .destip-wrapper {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .destip-input {
      margin-right: 8px;
    }
    .s-radio {
      margin: 10px;
    }
    .destip {
      .s-row {
        .s-form-item-control-wrapper {
          .s-form-item {
            display: inline;
            float: left;
          }
        }
      }
    }
  }
  .bind-tip {
    margin-left: 8px;
    .s-icon {
      font-size: 12px;
      border: 1px solid;
      color: #9e9898;
      border: 1px solid #9e9898;
      &:hover {
        border-color: #108cee;
        color: #108cee;
      }
    }
  }
  .probe-frequency-item {
    .s-row {
      .s-form-item-control {
        display: flex;
        align-items: center;
        .frequency-unit {
          margin-left: 12px;
        }
      }
    }
  }
  .probe-auto-item {
    .s-row {
      display: flex;
      align-items: center;
    }
  }
  .ip-item {
    .s-form-item-control-wrapper {
      max-width: 510px;
      .ip-radio {
        display: inline-block;
        .s-radio {
            margin: 10px;
        }
      }
    }
    .s-trigger-container {
      margin-top: 5px;
    }
    .s-table-container {
      .probe-custom-ip-wrapper {
        display: inline-flex;
        flex-direction: column;
        .probe-custom-ip-err {
          margin-top: 5px;
          color: #f33e3e;;
        }
      }
    }
  }
  .probe-route-wrap {
    .s-form-item {
      display: inline-block;
      .s-form-item-control-wrapper {
        width: auto;
        .probe-route-type {
          margin-right: 10px;
        }
      }
    }
  }
}

.s-tooltip-arrow {
  display: none;
}