.vpc-havip-create {
    font-size: 12px;
    .project-name {
        width: 97px;
        display: inline-block;
    }
    .s-form-item-label {
        width: 100px;
        text-align: left;
    }
    .s-form-item {
        margin-top: 20px;
        margin-bottom: 0;
        &:first-child {
            margin: 0;
        }
    }
    .s-form-item-content {
        margin-left: 80px;
    }
    .ip-radio {
        display: inline-block;
        .s-radio {
            margin: 10px;
        }
    }
    .ip-item {
        .s-row {
            .s-form-item-control-wrapper {
                .s-form-item-control {
                    padding-top: 7px;
                }
            }
        }
    }
    .internalIp_item {
        .s-row {
            .s-form-item-control-wrapper {
                line-height: 30px;
            }
        }
    }
    .name-havip-item {
        .s-form-item-help {
            width: 300px;
        }
    }
    .text-item {
        line-height: 30px;
    }
    .invalid-label {
        color: #eb5252;
    }
    .error_select {
        .s-input-suffix-container {
            border-color: #d0021b;
        }
    }
    .error_tip {
        color: #d0021b;
        margin-top: 5px;
        width: 308px;
    }
    .s-form-item-invalid-div {
        padding: 3px;
        color: #f33e3e;
        font-size: 12px;
    }
}
