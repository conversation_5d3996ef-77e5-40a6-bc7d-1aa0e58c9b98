/**
 * @file network/havip/pages/styles/detail.less
 */
.clearfix() {
    &:before,
    &:after {
        display: table;
        content: '';
    }
    &:after {
        clear: both;
    }
}
.vpc-main-wrap {
    width: 100%;
    height: 100%;
    overflow-y: scroll;
    .s-biz-page-header {
        font-size: 16px;
        padding: 15px 0;
    }
    .s-detail-page-title {
        display: none;
    }
    .s-detail-page-content {
        margin: 0;
        padding: 24px;
    }
    li.content-item {
        margin-bottom: 16px;
        width: 33%;
        label {
            color: #666;
        }
    }
}

.vpc-havip-detail {
    .s-icon {
        font-size: 12px;
        color: #2468f2;
    }
    .instance-not-found-class {
        height: 100%;
    }
    .instance-name {
        font-size: 16px;
        font-weight: 500;
        color: #151b26;
        padding-right: 12px;
        padding-left: 0px;
    }
    h4 {
        display: block;
        color: #151b26;
        line-height: 24px;
        font-weight: 500;
        margin-bottom: 16px;
        font-size: 16px;
        zoom: 1;
    }
    .content-item {
        width: 33%;
        margin-bottom: 16px;
        display: inline-block;
        float: none !important;
    }
    .cell-title {
        display: inline-block;
        vertical-align: top;
        color: #5e626a;
        margin-right: 16px;
        width: 64px;
    }
    .cell-content {
        display: inline-block;
        color: #151a26;
        max-width: 70%;
        word-break: break-all;
        position: relative;
    }
}

.vpc-havip-server {
    .havip-add-server {
        margin-bottom: 16px;
    }
    .s-pagination-wrapper {
        float: right;
    }
    .instance-name {
        font-size: 16px;
        font-weight: 500;
        color: #151b26;
        padding-right: 12px;
    }
    .instance-not-found-class {
        height: 100%;
    }
}

.havip-server-create {
    padding: 24px;
    .s-biz-page-header {
        border-bottom: none !important;
        margin: 0 !important;
        display: inline !important;
    }
    .s-biz-page-title {
        h2 {
            display: block !important;
            height: 24px !important;
            color: #151b26 !important;
            line-height: 24px !important;
            font-weight: 500 !important;
            margin-bottom: 16px !important;
            font-size: 16px !important;
        }
    }
    .s-biz-page-content {
        margin: 0 !important;
    }
    .s-biz-page-tb-left,
    .s-biz-page-tb-right {
        .inline_class {
            display: inline-flex !important;
        }
    }
    .inline_class {
        display: inline-flex !important;
    }
    .toolbar-line {
        margin-bottom: 10px;
        overflow: auto;
        .selectTip {
            color: #999;
            display: inline-block;
            padding-top: 8px;
        }
        .opts {
            display: inline-block;
            float: right;
            .s-search {
                margin-left: 8px;
            }
        }
    }
    .s-pagination {
        display: inline-block;
        margin-top: 16px;
        float: right;
    }
    .s-biz-page-footer {
        zoom: 1;
        padding-bottom: 16px !important;
        margin-top: 0px !important;
    }
    .s-table {
        .s-table-body {
            max-height: calc(~'100vh - 368px');
            overflow: auto;
        }
    }
    .error_class {
        color: #f33e3e;
        margin-right: 8px;
    }
}

.ipv6-group {
    display: flex;
    align-items: center;
    .title,
    .s-radio {
        margin-right: 10px;
    }
}
