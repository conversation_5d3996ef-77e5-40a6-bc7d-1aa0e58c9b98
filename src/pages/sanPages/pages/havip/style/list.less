.vpc-havip-list {
    // width: 0;
    flex: 1;
    .vpc-havip-header {
        .havip-widget {
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .widget-left {
                .title {
                    display: inline-block;
                    margin: 0;
                    color: #151b26;
                    margin-right: 12px;
                    height: 47px;
                    line-height: 47px;
                    font-weight: 500;
                    font-size: 16px;
                    margin-left: 16px;
                }
            }
            .widget-right {
                .help-file {
                    margin-right: 16px;
                    font-size: 12px;
                    font-weight: 400;
                    .s-icon {
                        position: relative;
                        top: -1px;
                        right: 4px;
                        font-size: 14px;
                        margin-right: 0;
                        color: #2468f2;
                    }
                }
            }
        }
    }
    .s-biz-page-toolbar {
        overflow: hidden;
    }
    .s-biz-page-tb-left {
        .icon-plus {
            color: #fff;
        }
    }
    .s-table {
        .s-table-row:hover {
            .icon-edit,
            .icon-copy {
                display: block;
            }
            .name-icon {
                display: block;
            }
        }
        .icon-edit,
        .icon-copy {
            display: none;
            font-size: 12px;
            color: #108cee;
        }
        .opt_class {
            .s-button {
                padding: 0;
                margin-right: 12px;
            }
        }
        .s-icon {
            font-size: 12px;
            color: #108cee;
        }
        .truncated {
            overflow: hidden;
            text-overflow: ellipsis;
            -o-text-overflow: ellipsis;
            white-space: nowrap;
            zoom: 1;
            display: inline-block;
            max-width: 80%;
            vertical-align: middle;
        }
    }
    .filter-buttons-wrap {
        display: flex;
        align-items: center;

        .s-cascader {
            margin-right: 5px;
        }
        .s-cascader-value {
            font-size: 12px;
            padding-top: 0;
            padding-bottom: 0;
            line-height: 30px;
        }
        .s-auto-compelete {
            .s-select {
                input {
                    width: 170px !important;
                }
            }
        }
        .button-item {
            margin-right: 5px;
        }
        .search-content {
            display: flex;
            align-items: center;
            position: relative;
            margin-right: 5px;
        }
        .s-icon.search {
            position: absolute;
            right: 5px;
            color: #615a5a;
        }
        .fresh_class {
            margin-right: 8px;
        }
    }
    .s-list-content {
        .foot-pager {
            margin: 12px 0;
        }
    }
    .left_class {
        .icon-column {
            height: 30px;
        }
        margin-left: 8px;
    }
    .add_havip {
        .s-button {
            height: 30px;
        }
    }
    .name-icon {
        display: none;
        font-size: 12px;
        fill: #108cee;
    }
    .truncated_haId {
        max-width: 75% !important;
    }
}
