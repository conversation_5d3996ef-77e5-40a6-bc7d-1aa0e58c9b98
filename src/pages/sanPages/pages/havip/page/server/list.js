import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {Notification} from '@baidu/sui';
import {OutlinedPlus} from '@baidu/sui-icon';

import '../../style/detail.less';
import {HaVipStatus, ServiceType, PayType, serverInstanceStatus} from '@/pages/sanPages/common/enum';
import rules from '../list/rules';
import Confirm from '@/pages/sanPages/components/confirm';
import CreateServer from './create';
import {$flag as FLAG, kXhrOptions} from '@/pages/sanPages/utils/helper';

const {asPage, invokeSUI, invokeSUIBIZ, template, withSidebar, invokeAppComp, invokeComp} = decorators;
const tpl = html`
    <div>
        <s-app-detail-page class="{{klass}}">
            <div slot="pageTitle" class="vpn-detail-header vpn-common-header">
                <span class="instance-name">{{instance.name}}</span>
                <span class="{{instance.status | statusStyle}}">{{instance.status | havStatusText}}</span>
            </div>
            <div class="havip-add-server">
                <s-tip-button skin="primary" disabled="{{disabled}}" s-if="tip">
                    <span slot="content">
                        <!--bca-disable-next-line-->
                        {{tip | raw}}
                    </span>
                    <outlined-plus />
                    添加后端服务器
                </s-tip-button>
                <s-button skin="primary" on-click="onCreate" s-else>
                    <outlined-plus />
                    添加后端服务器
                </s-button>
                <s-tip-button
                    disabled="{{releaseServer.disable}}"
                    isDisabledVisibile="{{true}}"
                    placement="top"
                    on-click="onRelease"
                    class="left_class"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{releaseServer.message | raw}}
                    </div>
                    释放
                </s-tip-button>
            </div>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                selection="{{table.selection}}"
                datasource="{{table.datasource}}"
                on-selected-change="tableSelected($event)"
            >
                <div slot="empty">
                    <s-empty on-click="onCreate"> </s-empty>
                </div>
                <div slot="error">
                    啊呀，出错了
                    <s-button skin="stringfy" on-click="refreshTable">重新加载</s-button>
                </div>
                <div slot="c-name">
                    <span class="truncated" title="{{row.name || '-'}}">
                        <a href="{{row | linkHref}}"> {{row.name || '-'}} </a>
                    </span>
                    <br />
                    <span>{{row.instanceId}}</span>
                </div>
                <div slot="c-type">
                    <span>{{row.instanceType | getType}}</span>
                </div>
                <div slot="c-internalIp">
                    <span s-if="{{row.instanceType === 'ENI'}}">
                        <p s-for="item in row.ips" slot="content">
                            <span>{{item.privateIp || '-'}}</span>
                            <span s-if="{{FLAG.NetworkSupportEip}}">/{{item.eip || '-'}}</span>
                        </p>
                    </span>
                    <span s-if="{{row.instanceType !== 'ENI'}}">{{row.internalIp || '-'}}</span>
                    <span s-if="{{row.instanceType !== 'ENI' && FLAG.NetworkSupportEip}}"
                        >/{{row.publicIp || '-'}}</span
                    >
                </div>
                <div slot="c-payment">
                    <span>{{row.payment | getPaymant}}</span>
                </div>
                <div slot="c-master">
                    <span>{{row.master === true ? '主' : '备'}}</span>
                </div>
                <div slot="c-status">
                    <span s-if="{{row.instanceType === 'ENI'}}">-</span>
                    <span class="status {{row | statusClass}}" s-else>{{row | statusText}}</span>
                </div>
            </s-table>
            <s-pagination
                s-if="{{pager.total}}"
                layout="{{'total, pageSize, pager, go'}}"
                pageSize="{{pager.pageSize}}"
                total="{{pager.total}}"
                page="{{pager.page}}"
                on-pagerChange="onPagerChange($event)"
                on-pagerSizeChange="onPagerSizeChange($event)"
            />
        </s-app-detail-page>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class HaVipServer extends Component {
    static components = {
        'outlined-plus': OutlinedPlus
    };
    static filters = {
        getPaymant(value) {
            return PayType.getTextFromValue(value) || '-';
        },
        statusClass(row) {
            let type = row?.instanceType;
            if (type === 'ENI') {
                return 'normal';
            }
            return serverInstanceStatus.fromValue(row?.status).klass || '';
        },
        statusText(row) {
            let type = row?.instanceType;
            if (type === 'ENI') {
                return '可用';
            }
            return row?.status ? serverInstanceStatus.getTextFromValue(row?.status) : '-';
        },
        getType(value) {
            if (value === 'ENI') {
                return '弹性网卡';
            }
            return '云服务器';
        },
        statusStyle(value) {
            return HaVipStatus.fromValue(value).styleClass || '';
        },
        havStatusText(value) {
            return value ? HaVipStatus.getTextFromValue(value) : '-';
        },
        linkHref(row) {
            let type = row?.instanceType;
            let vpcId = this.data.get('context').vpcId;
            if (type === 'ENI') {
                return `/network/#/vpc/eni/detail?vpcId=${vpcId}&eniId=${row.instanceUuid}`;
            }
            return `/bcc/#/bcc/instance/detail?instanceId=${row.instanceUuid}&id=${row.instanceId}`;
        }
    };
    initData() {
        return {
            klass: 'vpc-main-wrap vpc-havip-server',
            table: {
                loading: false,
                error: null,
                datasource: [],
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                columns: [
                    {
                        name: 'name',
                        label: '云端服务器名称'
                    },
                    {
                        name: 'type',
                        label: '类型'
                    },
                    {
                        name: 'status',
                        label: '服务器状态'
                    },
                    {
                        name: 'payment',
                        label: '支付方式'
                    },
                    {
                        name: 'internalIp',
                        label: FLAG.NetworkSupportEip ? '内网/公网地址' : '内网地址'
                    },
                    {
                        name: 'master',
                        label: '主备'
                    }
                ]
            },
            disabled: true,
            tip: '',
            pager: {
                size: 10,
                page: 1,
                count: 0,
                datasource: [
                    {text: '10', value: 10},
                    {text: '20', value: 20},
                    {text: '50', value: 50},
                    {text: '100', value: 100}
                ]
            },
            FLAG
        };
    }

    static computed = {
        natSubnet() {
            const subnetType = this.data.get('table.subnetType');
            return subnetType === ServiceType.NAT || subnetType === ServiceType.BBC_NAT;
        },
        HaVipStatus() {
            const status = this.data.get('table.status');
            return u.indexOf([HaVipStatus.ATTACHING, HaVipStatus.DETACHING], status) === -1;
        }
    };

    inited() {
        let {releaseServer} = checker.check(rules, []);
        this.data.set('releaseServer', releaseServer);
    }
    attached() {
        this.loadDetail();
        this.getServerList();
    }

    getServerList() {
        this.data.set('table.loading', true);
        let payload = {
            haVipUuid: this.data.get('context').haVipId,
            pageNo: this.data.get('pager.page'),
            pageSize: this.data.get('pager.size')
        };
        if (this.data.get('keyword')) {
            payload.keywordType = this.data.get('keywordType');
            payload.keyword = this.data.get('keyword');
        }
        this.$http
            .haVipBindList(payload, {'x-silent-codes': ['HaVip.HaVipResourceNotExist']})
            .then(data => {
                this.data.set('table.datasource', data.result);
                this.data.set('pager.page', data.pageNo);
                if (data.totalCount > 5) {
                    this.data.set('tip', '每个havip最多绑定5个实例');
                }
                this.data.set('pager.total', data.totalCount);
                this.data.set('pager.pageSize', data.pageSize);
                this.data.set('table.loading', false);
            })
            .catch(e => {
                this.data.set('table.loading', false);
            });
    }

    loadDetail() {
        return this.$http
            .haVipDetail(
                {haVipUuid: this.data.get('context').haVipId},
                {'x-silent-codes': ['HaVip.HaVipResourceNotExist']}
            )
            .then(data => {
                this.data.set('instance', data);
            });
    }

    onCreate() {
        const dialog = new CreateServer({
            data: {
                instance: this.data.get('instance'),
                total: this.data.get('pager.total'),
                open: true,
                haVipUuid: this.data.get('context').haVipId
            }
        });
        dialog.on('bind', () => {
            this.refreshTable();
        });
        dialog.attach(document.body);
    }

    onDelete(e, row) {
        e.target && e.target.blur();
        const confirm = new Confirm({
            data: {
                content: ''
            }
        });
        confirm.on('confirm', () => {});
        confirm.attach(document.body);
    }

    refreshTable() {
        this.resetTable();
        this.getServerList();
    }

    onRegionChange() {
        location.hash = '#/vpc/havip/list';
    }

    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.data.set('table.selection.selectedIndex', []);
        this.resetTable();
        this.getServerList();
    }

    // 重置列表
    resetTable() {
        this.data.set('table.selection', {
            mode: 'multi',
            selectedIndex: []
        });
        this.data.set('selectedItems', []);
    }

    onPagerSizeChange(e) {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', e.value.pageSize);
        this.resetTable();
        this.getServerList();
    }
    tableSelected({value}) {
        const {releaseServer} = checker.check(rules, value.selectedItems, '');
        this.data.set('releaseServer', releaseServer);
        this.data.set('selectedItems', value.selectedItems);
    }
    onRelease() {
        let confirm = new Confirm({
            data: {
                open: true,
                content: '确认解绑实例？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            let ids = this.data.get('selectedItems').map(item => item.instanceUuid);
            let instanceType = this.data.get('selectedItems')[0]?.instanceType === 'ENI' ? 'ENI' : 'SERVER';
            this.$http
                .haVipUnbind({
                    instanceUuids: ids,
                    instanceType: instanceType,
                    haVipUuid: this.data.get('context').haVipId
                })
                .then(() => {
                    this.refreshTable();
                    Notification.success('删除成功', {placement: 'topRight'});
                });
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(HaVipServer));
