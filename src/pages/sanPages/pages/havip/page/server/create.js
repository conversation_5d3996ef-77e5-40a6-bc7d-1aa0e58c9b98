import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';

import {OutlinedRefresh} from '@baidu/sui-icon';
import {PayType, serverInstanceStatus} from '@/pages/sanPages/common/enum';

const {asComponent, invokeSUI, invokeSUIBIZ, template} = decorators;

/* eslint-disable */
const tpl = html`
    <template>
        <s-dialog
            width="600"
            class="{{klass}}"
            closeAfterMaskClick="{{false}}"
            open="{{open}}"
            title="{{'添加后端服务器'}}"
        >
            <div>
                <div class="toolbar-line">
                    <span>{{'绑定类型：'}}</span>
                    <s-radio-radio-group
                        class="inline_class"
                        datasource="{{typeList}}"
                        value="{=type=}"
                        radioType="button"
                    />
                    <div class="opts inline_class">
                        <s-search
                            width="{{184}}"
                            class="search-warp"
                            value="{=keyword=}"
                            placeholder="{{placeholder}}"
                            on-search="onSearch"
                        >
                            <s-select
                                slot="options"
                                width="120"
                                datasource="{{keywordTypeList}}"
                                value="{=keywordType=}"
                                on-change="keywordTypeChange($event)"
                            >
                            </s-select>
                        </s-search>
                        <s-button class="s-icon-button" on-click="refresh"
                            ><outlined-refresh class="icon-class"
                        /></s-button>
                    </div>
                </div>
                <s-table
                    columns="{{table.columns}}"
                    loading="{{table.loading}}"
                    error="{{table.error}}"
                    datasource="{{table.datasource}}"
                    on-selected-change="tableSelected($event)"
                    selection="{=table.selection=}"
                >
                    <div slot="error">
                        加载失败
                        <a href="javascript:;" on-click="refresh">{{'重新加载'}}</a>
                    </div>
                    <div slot="c-name">
                        <span>{{row.name || '-'}}</span><br />
                        <span>{{row.instanceId}}</span>
                    </div>
                    <div slot="c-internalIp">
                        <span s-if="{{type !== 'ENI'}}">{{row.internalIp || '-'}}</span>
                        <div s-else>
                            <p s-for="item in row.ips">
                                <span>{{item.privateIp || '-'}}</span>
                            </p>
                        </div>
                    </div>
                    <div slot="c-payment">
                        <span s-if="{{type === 'ENI'}}">-</span>
                        <span s-else>{{row.payment | getPaymant}}</span>
                    </div>
                    <div slot="c-status">
                        <span s-if="{{type === 'ENI'}}">-</span>
                        <span s-else class="status {{row.status | statusClass}}">{{row.status | statusText}}</span>
                    </div>
                    <div slot="empty">
                        <s-empty>
                            <div slot="action"></div>
                        </s-empty>
                    </div>
                </s-table>
                <s-pagination
                    s-if="{{pager.total}}"
                    class="pagination"
                    layout="{{'total, pageSize, pager, go'}}"
                    pageSize="{{pager.size}}"
                    total="{{pager.total}}"
                    page="{{pager.page}}"
                    pageSizes="{{pager.pageSizes}}"
                    on-pagerChange="onPageChange"
                    on-pagerSizeChange="onPageSizeChange"
                />
            </div>
            <div slot="footer">
                <div style="padding-left: 300px; padding-top:20px; display: inline-block;">
                    <span s-if="{{(selectedItems.length + total) > 5}}" class="error_class"
                        >每个havip最多绑定5个实例</span
                    >
                    <s-button size="larger" on-click="cancel">{{'取消'}}</s-button>
                    <s-button
                        disabled="{{!selectedItems.length || (selectedItems.length + total > 5) || isConfirm}}"
                        skin="primary"
                        on-click="onBind"
                        >{{'确定'}}</s-button
                    >
                </div>
            </div>
        </s-dialog>
    </template>
`;
/* eslint-enable */
@template(tpl)
@invokeSUI
@invokeSUIBIZ
class CreateHaVipServer extends Component {
    static components = {
        'outlined-refresh': OutlinedRefresh
    };
    static filters = {
        getPaymant(value) {
            return PayType.getTextFromValue(value) || '-';
        },
        statusClass(value) {
            let type = this.data.get('type');
            if (type === 'ENI') {
                return 'normal';
            }
            return serverInstanceStatus.fromValue(value).klass || '';
        },
        statusText(value) {
            let type = this.data.get('type');
            if (type === 'ENI') {
                return '可用';
            }
            return value ? serverInstanceStatus.getTextFromValue(value) : '-';
        }
    };
    initData() {
        return {
            klass: ['havip-server-create'],
            vpcId: '',
            selectedItems: [],
            title: '',
            emptyText: '暂无数据',
            keywordTypeList: [
                {text: '实例名称', value: 'name'},
                {text: '实例内网IP', value: 'internalIp'}
            ],
            typeList: [
                {label: '云服务器', value: 'SERVER'},
                {label: '弹性网卡', value: 'ENI'}
            ],
            type: 'SERVER',
            placeholder: '请输入实例名称进行搜索',
            keywordType: 'name',
            keyword: '',
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                columns: [
                    {
                        name: 'name',
                        label: '实例名称/ID'
                    },
                    {
                        name: 'status',
                        label: '状态'
                    },
                    {
                        name: 'payment',
                        label: '支付方式'
                    },
                    {
                        name: 'internalIp',
                        label: '内网IP地址'
                    }
                ],
                datasource: []
            },
            pager: {
                size: 10,
                page: 1,
                total: 0,
                pageSizes: [10, 20, 50, 100]
            }
        };
    }

    keywordTypeChange(e) {
        switch (e.value) {
            case 'name':
                this.data.set('placeholder', '请输入实例名称进行搜索');
                break;
            case 'internalIp':
                this.data.set('placeholder', '请输入实例内网IP进行搜索');
                break;
            default:
                break;
        }
    }
    onPageChange(e) {
        this.data.set('pager.page', e.value.page);
        this.data.set('table.selection.selectedIndex', []);
        this.resetTable();
        this.loadPage();
    }
    onPageSizeChange(e) {
        this.data.set('pager.size', e.value.pageSize);
        this.data.set('pager.page', 1);
        this.resetTable();
        this.loadPage();
    }
    tableSelected({value}) {
        this.data.set('selectedItems', value.selectedItems);
    }
    // 重置列表
    resetTable() {
        this.data.set('table.selection', {
            mode: 'multi',
            selectedIndex: []
        });
        this.data.set('selectedItems', []);
    }
    async onSearch() {
        this.data.set('pager.page', 1);
        this.resetTable();
        this.loadPage();
    }
    getSearchCriteria() {
        let pager = this.data.get('pager');
        let payload = {pageNo: pager.page, pageSize: pager.size, haVipUuid: this.data.get('haVipUuid')};
        let keyword = this.data.get('keyword');
        if (keyword) {
            payload.keywordType = this.data.get('keywordType');
            payload.keyword = keyword;
        }
        return payload;
    }
    async loadPage() {
        this.data.set('table.loading', true);
        let payload = this.getSearchCriteria();
        let type = this.data.get('type');
        payload.instanceType = type;
        let result = null;
        try {
            result = await this.$http.haVipUnbindList(payload);
            this.data.set('table.datasource', result.result);
            this.data.set('pager.total', result.totalCount);
            this.data.set('table.loading', false);
        } catch (err) {
            this.data.set('table.datasource', []);
            this.data.set('table.loading', false);
        }
    }
    async onBind() {
        const type = this.data.get('type');
        let payload = {
            instanceUuids: this.data.get('selectedItems').map(item => item.instanceUuid),
            haVipUuid: this.data.get('haVipUuid'),
            instanceType: type.toUpperCase()
        };
        this.data.set('isConfirm', true);
        try {
            await this.$http.haVipBind(payload);
            this.data.set('open', false);
            this.fire('bind');
        } catch (err) {}
        this.data.set('isConfirm', false);
    }
    cancel() {
        this.data.set('open', false);
    }
    attached() {
        this.loadPage();
        this.watch('type', value => {
            this.data.set('pager.page', 1);
            this.resetTable();
            this.loadPage();
        });
    }
    refresh() {
        this.loadPage();
    }
}
export default Processor.autowireUnCheckCmpt(CreateHaVipServer);
