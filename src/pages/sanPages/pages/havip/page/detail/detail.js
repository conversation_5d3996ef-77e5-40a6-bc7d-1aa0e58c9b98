import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {Notification} from '@baidu/sui';
import {OutlinedEditingSquare} from '@baidu/sui-icon';

import '../../style/detail.less';
import {HaVipStatus} from '@/pages/sanPages/common/enum';
import {$flag as FLAG, toTime, kXhrOptions} from '@/pages/sanPages/utils/helper';

const {invokeSUI, invokeSUIBIZ, template, withSidebar, invokeAppComp} = decorators;
const tpl = html`
    <div>
        <s-app-detail-page class="{{klass}}">
            <h4>基本信息</h4>
            <ul>
                <li class="content-item">
                    <label class="cell-title">名称：</label>
                    <span class="cell-content">
                        {{instance.name}}
                        <s-popover class="edit-popover-class" s-ref="{{'nameEdit'}}" placement="top" trigger="click">
                            <div class="edit-wrap" slot="content">
                                <s-input
                                    value="{=name=}"
                                    width="320"
                                    placeholder="请输入"
                                    on-input="onInput($event, 'name')"
                                />
                                <div class="edit-tip">
                                    大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65
                                </div>
                                <s-button
                                    skin="primary"
                                    s-ref="{{'nameEditBtn'}}"
                                    disabled="{{true}}"
                                    on-click="onEdit('name')"
                                    >确定</s-button
                                >
                                <s-button on-click="editCancel('name')">取消</s-button>
                            </div>
                            <outlined-editing-square color="#2468f2" on-click="beforeEdit('name')" />
                        </s-popover>
                    </span>
                </li>
                <li class="content-item">
                    <label class="cell-title">ID：</label>
                    <span class="cell-content">{{instance.haVipId}}</span>
                    <s-clip-board text="{{instance.haVipId}}" successMessage="已复制到剪贴板" />
                </li>
                <li class="content-item">
                    <label class="cell-title">所在网络：</label>
                    <span class="cell-content">
                        <a
                            href="#/vpc/instance/detail?vpcId={{vpcInfo.vpcId}}"
                            data-track-id="ti_vpc_havip_detail"
                            data-track-name="实例详情/所在网络"
                        >
                            {{vpcInfo.name}}
                            <span s-if="vpcInfo.cidr">
                                （{{vpcInfo.cidr}}）{{instance.vpcIpv6Cidr ? ('(' + instance.vpcIpv6Cidr +')') : ''}}
                            </span>
                        </a>
                    </span>
                </li>
                <li class="content-item">
                    <label class="cell-title">所在子网：</label>
                    <span class="cell-content">
                        <a
                            href="#/vpc/subnet/detail?subnetId={{instance.subnetUuid}}"
                            data-track-id="ti_vpc_havip_detail"
                            data-track-name="实例详情/所在子网"
                        >
                            {{instance.subnetName}}
                            <span s-f="instance.subnetCidr">
                                {{instance.subnetCidr ? ('(' + instance.subnetCidr +')') : ''}}{{instance.subnetIpv6Cidr
                                ? ('(' + instance.subnetIpv6Cidr +')') : ''}}
                            </span>
                        </a>
                    </span>
                </li>
                <li class="content-item">
                    <label class="cell-title">公网IP</label>
                    <span>{{instance | getIp}}</span>
                </li>
                <li class="content-item">
                    <label class="cell-title">内网IP</label>
                    <span>{{instance.internalIp || '-'}}</span>
                </li>
                <li class="content-item">
                    <label class="cell-title">创建时间：</label>
                    <span class="cell-content"> {{instance.createTime | timeFormat}} </span>
                </li>
                <li class="content-item">
                    <label class="cell-title">描述：</label>
                    <span class="cell-content"> {{instance.desc || '-'}} </span>
                    <s-popover class="edit-popover-class" s-ref="{{'descEdit'}}" placement="top" trigger="click">
                        <div class="edit-wrap" slot="content">
                            <s-input
                                value="{=desc=}"
                                width="160"
                                placeholder="请输入"
                                on-input="onInput($event, 'desc')"
                            />
                            <div class="edit-tip">描述不能超过200个字符</div>
                            <s-button
                                skin="primary"
                                s-ref="{{'descEditBtn'}}"
                                disabled="{{true}}"
                                on-click="onEdit('desc')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel('desc')">取消</s-button>
                        </div>
                        <outlined-editing-square color="#2468f2" on-click="beforeEdit('desc')" />
                    </s-popover>
                </li>
                <li class="content-item">
                    <label class="cell-title">运行状态：</label>
                    <span class="{{instance.status | statusStyle}} cell-content">{{instance.status | statusText}}</span>
                </li>
            </ul>
        </s-app-detail-page>
    </div>
`;

@template(tpl)
@withSidebar({active: 'vpc-havip-detail'})
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class HaVipDetail extends Component {
    static components = {
        'outlined-editing-square': OutlinedEditingSquare
    };
    initData() {
        return {
            FLAG,
            klass: 'vpc-main-wrap vpc-havip-detail',
            instance: {},
            setIpv6Show: false,
            ipv6Set: 'auto',
            ipv6Datasource: [
                {
                    text: '自动分配',
                    value: 'auto'
                },
                {
                    text: '指定',
                    value: 'custom'
                }
            ]
        };
    }

    filters = {
        timeFormat(time) {
            return toTime(time);
        },
        statusStyle(value) {
            return HaVipStatus.fromValue(value).styleClass || '';
        },
        statusText(value) {
            return value ? HaVipStatus.getTextFromValue(value) : '-';
        },
        getIp(value) {
            return value.eip ? value.eip + '/' + value.eipBandWidth + 'Mbps' : '-';
        }
    };

    attached() {
        this.loadDetail();
        this.loadVpcDetail();
    }

    loadVpcDetail() {
        const vpcId = this.data.get('context').vpcId;
        this.$http
            .getVpcDetail({
                vpcIds: [vpcId]
            })
            .then(data => {
                const result = data[vpcId] || {};
                this.data.set('vpcInfo', result);
            });
    }

    loadDetail() {
        return this.$http
            .haVipDetail(
                {haVipUuid: this.data.get('context').haVipId},
                {'x-silent-codes': ['HaVip.HaVipResourceNotExist']}
            )
            .then(data => {
                this.data.set('instance', data);
            });
    }

    beforeEdit(type) {
        const TYPE_MAP = {
            name: 'name',
            desc: 'desc'
        };
        const instance = this.data.get('instance');
        this.data.set(TYPE_MAP[type], instance[TYPE_MAP[type]]);
    }

    onInput(e, type) {
        let result;
        switch (type) {
            case 'name':
                const pattern = /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{0,64}$/;
                result = e.value.length <= 64 && pattern.test(e.value);
                break;
            case 'desc':
                result = e.value.length <= 200;
                break;
        }
        this.ref(`${type}EditBtn`).data.set('disabled', !result);
    }

    onEdit(type) {
        const TYPE_MAP = {
            name: 'name',
            desc: 'desc'
        };
        const payload = {
            [TYPE_MAP[type]]: this.data.get(TYPE_MAP[type])
        };
        payload.haVipUuid = this.data.get('context').haVipId;
        this.$http
            .updateHaVip(payload)
            .then(() => {
                this.editCancel(type);
                this.loadDetail();
                this.data.get('context')?.updateName();
            })
            .catch(() => {
                this.editCancel(type);
                this.loadDetail();
            });
    }

    editCancel(type) {
        this.ref(`${type}Edit`).data.set('visible', false);
    }

    onRegionChange() {
        location.hash = '#/vpc/havip/list';
    }
}
export default San2React(Processor.autowireUnCheckCmpt(HaVipDetail));
