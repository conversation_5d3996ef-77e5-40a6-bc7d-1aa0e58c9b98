import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';

import RULE from '@/pages/sanPages/utils/rule';
import {NatIp} from '@/pages/sanPages/common/enum';
import {ContextService} from '@/pages/sanPages/common';
import {checkIsInSubnet} from '@/pages/sanPages/utils/common';
import '../../style/create.less';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';

const formValidator = self => ({
    name: [
        {required: true, message: '请填写名称'},
        {pattern: /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{0,64}$/, message: '格式不符合要求'}
    ],
    vpcId: [
        {required: true, message: '请选择所在网络'},
        {
            validator(rule, value, callback) {
                if (value === '') {
                    return callback('请选择所在网络');
                }
                return callback();
            }
        }
    ],
    subnetId: [{required: true, message: '请选择所在子网'}],
    description: [{maxLength: 200}],
    internalIp: [
        {pattern: RULE.IP, message: '格式不符合要求'},
        {
            validator: (rule, value, callback) => {
                const source = self.data.get('formData');
                const subnetList = self.data.get('subnetDatasource');
                if (source.type !== NatIp.CUSTOM) {
                    callback();
                    return;
                }
                if (!value) {
                    return callback('请填写指定IP地址');
                }
                if (!u.trim(value)) {
                    return callback('请填写指定IP地址');
                }
                const subnet = u.find(subnetList, item => item.value === source.subnetId);
                if (subnet && !checkIsInSubnet(source.internalIp + '/32', subnet.cidr)) {
                    return callback('IP地址不在所选子网内');
                }
                callback();
            }
        }
    ]
});
const {asComponent, invokeSUI, invokeSUIBIZ, template} = decorators;
const tpl = html`
    <div>
        <s-dialog class="vpc-havip-create" open="{{true}}" title="创建虚拟IP">
            <s-form s-ref="form" data="{=formData=}" rules="{{rules}}" label-align="left">
                <s-form-item
                    label="虚拟IP名称："
                    prop="name"
                    help="大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65"
                    class="name-havip-item"
                >
                    <s-input width="{{300}}" value="{=formData.name=}" />
                </s-form-item>
                <s-form-item label="所在网络：" prop="vpcId">
                    <s-select
                        width="{{300}}"
                        datasource="{{vpcs}}"
                        value="{=formData.vpcId=}"
                        on-change="vpcChange($event)"
                        class="{{quotaError ? 'error_select' : ''}}"
                    ></s-select>
                    <div class="error_tip" s-if="{{quotaError}}">
                        <!--bca-disable-next-line-->
                        {{quotaTip | raw}}
                    </div>
                </s-form-item>
                <s-form-item label="所在子网：" prop="subnetId">
                    <s-select
                        width="{{300}}"
                        datasource="{{subnetDatasource}}"
                        value="{=formData.subnetId=}"
                        disabled="{{loading}}"
                        on-change="subnetChange($event)"
                    ></s-select>
                </s-form-item>
                <s-form-item prop="internalIp" label="IP地址：" class="internalIp_item">
                    <s-radio-radio-group
                        datasource="{{ipDatasource}}"
                        value="{=formData.type=}"
                        on-change="ipTypeChange($event)"
                    />
                    <s-input
                        s-if="{{formData.type === 'custom'}}"
                        width="{{240}}"
                        placeholder="请输入该子网内可用IP"
                        value="{=formData.internalIp=}"
                    />
                    <div s-if="messageShow" class="s-form-item-invalid-div">{{errorMessage}}</div>
                </s-form-item>
                <s-form-item label="描述：" prop="description">
                    <s-input-text-area
                        width="{{300}}"
                        maxLength="200"
                        height="{{100}}"
                        placeholder="{{'最多200个字符'}}"
                        value="{=formData.description=}"
                    />
                </s-form-item>
            </s-form>
            <div slot="footer">
                <s-button on-click="onClose">取消</s-button>
                <s-button skin="primary" disabled="{{disableSub}}" on-click="doSubmit">确定</s-button>
            </div>
        </s-dialog>
    </div>
`;

@asComponent('@create-havip')
@template(tpl)
@invokeSUI
@invokeSUIBIZ
class CreateHaVip extends Component {
    initData() {
        return {
            FLAG,
            rules: formValidator(this),
            formData: {
                subnetId: '',
                type: 'auto',
                vpcId: '',
                desc: '',
                internalIp: ''
            },
            subnetDatasource: [],
            initSubnetDatasource: [],
            loading: false,
            ContextService,
            quotaError: false,
            ipDatasource: [
                {
                    text: '自动分配',
                    value: 'auto'
                },
                {
                    text: '指定',
                    value: 'custom'
                }
            ]
        };
    }

    static computed = {
        quotaTip() {
            let formData = this.data.get('formData');
            if (!formData?.vpcMap) {
                return '';
            }
            let ticketUrl = `${ContextService.Domains.ticket}/#/ticket/create`;
            return (
                `您的私有网络${formData.vpcMap[formData.vpcId]}下高可用虚拟IP数量已经达到配额，如需更多高可用虚拟IP，可以通过工单<a href="${ticketUrl}" target="_blank">工单</a>申请`
            );
        }
    };

    inited() {
        let vpcs = this.data.get('vpcs');
        let vpcMap = {};
        u.each(vpcs, item => {
            vpcMap[item.value] = item.text;
        });
        this.data.set('formData.vpcMap', vpcMap);
        this.data.set('allVpc', vpcs);
    }

    attached() {
        this.watch('formData.vpcId', value => {
            this.data.set('formData.subnetId', '');
            this.getHaVipQuota(value);
            this.loadSubnets();
        });

        // 默认选择列表页选中的vpc实例，赋值操作需要放在watch后面，才能够触发watch
        let vpcId = this.data.get('vpcId');
        let vpcs = this.data.get('vpcs');
        if (!vpcId && vpcs.length) {
            this.data.set('formData.vpcId', vpcs[0].value);
        }
        vpcId && this.data.set('formData.vpcId', vpcId);
    }

    loadSubnets() {
        let payload = {vpcId: this.data.get('formData.vpcId')};
        this.data.set('loading', true);
        this.$http
            .getSubnetList(payload)
            .then(data => {
                let datasource = [];
                u.each(data, item => {
                    let text = '';
                    if (item.ipv6Cidr) {
                        text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}(${item.ipv6Cidr})`;
                    } else {
                        text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}`;
                    }
                    datasource.push({
                        value: item.subnetId,
                        text: text,
                        cidr: item.cidr,
                        ipv6Cidr: item.ipv6Cidr
                    });
                });
                this.data.set('subnetDatasource', datasource);
                this.data.set('initSubnetDatasource', datasource);
            })
            .finally(() => this.data.set('loading', false));
    }

    doSubmit() {
        let formData = this.data.get('formData');
        let vpcId = this.data.get('formData.vpcId');
        const form = this.ref('form');
        return form.validateFields().then(async () => {
            if (formData.type === 'custom') {
                let params = {
                    vpcUuid: formData.vpcId,
                    internalIp: formData.internalIp
                };
                await this.$http.haVipCheckPrivateIp(params).then(res => {
                    if (res.vpcPrivateIpAddresses && res.vpcPrivateIpAddresses.length) {
                        this.data.set('messageShow', true);
                        let ipValidMessage = res.vpcPrivateIpAddresses[0].privateIpAddressType;
                        this.data.set('errorMessage', `IP被${ipValidMessage}占用`);
                    } else {
                        this.data.set('messageShow', false);
                    }
                });
            }
            if (this.data.get('messageShow')) {
                return;
            }
            this.data.set('disableSub', true);
            let payload = {
                desc: formData.description,
                name: formData.name,
                vpcUuid: formData.vpcId,
                subnetUuid: formData.subnetId
            };
            if (formData.type === NatIp.CUSTOM) {
                payload.internalIp = formData.internalIp;
            }
            return this.$http
                .createHaVip(payload)
                .then(() => {
                    this.data.set('disableSub', false);
                    this.fire('success');
                    this.onClose();
                })
                .catch(() => {
                    this.data.set('disableSub', false);
                });
        });
    }

    onClose() {
        this.dispose();
    }
    getHaVipQuota(value) {
        return this.$http.getHaVipQuota({vpcUuid: value}).then(data => {
            if (data.free < 1) {
                this.data.set('quotaError', true);
            } else {
                this.data.set('quotaError', false);
            }
        });
    }
    ipTypeChange({value}) {
        if (value === 'custom') {
            this.data.set('messageShow', true);
        } else {
            this.data.set('messageShow', false);
        }
    }
}
export default Processor.autowireUnCheckCmpt(CreateHaVip);
