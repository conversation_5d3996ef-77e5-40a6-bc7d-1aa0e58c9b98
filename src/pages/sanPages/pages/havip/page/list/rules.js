import {ContextService} from '@/pages/sanPages/common';
import {isSameArray} from '@/pages/sanPages/utils/helper';


export default {
    createHaVip: [
        {
            required: false,
            message: ''
        },
        {
            custom(data, options) {
                if (options.quota && options.quota.free <= 0) {
                    return {
                        disable: true,
                        message: `高可用虚拟IP配额不足，如需增加配额请提交<a href="${
                            ContextService.Domains.ticket
                        }/#/ticket/create" target="_blank">工单</a>`
                    };
                }
            }
        }
    ],
    release: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            custom(data) {
                let list = data.map(item => item.associateNum);
                if (list.find(item => item > 0)) {
                    return {
                        disable: true,
                        message: '当前部分实例已绑定服务器，请先进行解绑再进行释放'
                    };
                }
            }
        }
    ],
    releaseServer: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            custom(data) {
                let instanceTypeList = data.map(item => item.instanceType);
                if (instanceTypeList.includes('ENI')) {
                    let check = isSameArray(instanceTypeList);
                    if (!check) {
                        return {
                            disable: true,
                            message: '请选择相同的类型的实例进行解绑'
                        };
                    }
                }
            }
        }
    ],
};
