import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {Notification} from '@baidu/sui';
import {OutlinedRefresh, OutlinedPlus, OutlinedEditingSquare} from '@baidu/sui-icon';

import {HaVipStatus, DocService} from '@/pages/sanPages/common';
import '../../style/list.less';
import {$flag as FLAG, utcToTime, eipBlackTip} from '@/pages/sanPages/utils/helper';
import {SCHEMA} from './schema';
import {EipBindDialog} from '@baidu/bce-eip-sdk-san';
import {EipSDK} from '@baidu/bce-eip-sdk';
import Confirm from '@/pages/sanPages/components/confirm';
import CreateHaVip from './create';
import rules from './rules';
import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import IntroducePanel from '@/pages/sanPages/components/introducePanel/introducePanel';

const {asPage, invokeSUI, invokeSUIBIZ, template, withSidebar, invokeComp, invokeAppComp} = decorators;
const tpl = html` <template>
    <s-app-list-page class="{{klass}}">
        <div class="vpc-havip-header" slot="pageTitle">
            <div class="havip-widget">
                <div class="widget-left">
                    <span class="title">{{title}}</span>
                    <vpc-select class="vpc-select" on-change="vpcChange" on-int="vpcInt" />
                </div>
                <div class="widget-right">
                    <a
                        s-ref="introduce"
                        href="javascript:void(0)"
                        class="help-file function-introduce"
                        on-click="handleShowCard"
                        on-mouseenter="handleMouseEnter('introduce')"
                        on-mouseleave="handleMouseLeave('introduce')"
                        s-if="{{!FLAG.NetworkSupportXS}}"
                    >
                        <img class="s-icon" src="{{introduceIcon}}" />功能简介
                    </a>
                    <a
                        href="{{DocService.havip_helpFile}}"
                        target="_blank"
                        class="help-file"
                        s-if="{{!FLAG.NetworkSupportXS}}"
                    >
                        <s-icon name="warning-new" />帮助文档
                    </a>
                </div>
            </div>
            <introduce-panel
                isShow="{{show}}"
                klass="{{'endpoint-peerconn-wrapper'}}"
                title="{{introduceTitle}}"
                description="{{description}}"
                introduceOptions="{{introduceOptions}}"
                on-toggle="handleToggle($event)"
            ></introduce-panel>
        </div>
        <div class="list-page-tb-left-toolbar" slot="bulk">
            <s-tooltip
                trigger="{{iamPass.disable || createBtn.disable ? 'hover' : ''}}"
                placement="right"
                class="add_havip"
            >
                <!--bca-disable-next-line-->
                <div slot="content">{{iamPass.message || createBtn.tip | raw}}</div>
                <s-button skin="primary" on-click="onCreate" disabled="{{iamPass.disable || createBtn.disable}}"
                    ><outlined-plus />
                    创建高可用虚拟IP
                </s-button>
            </s-tooltip>
            <s-tip-button
                disabled="{{release.disable}}"
                isDisabledVisibile="{{true}}"
                placement="top"
                on-click="onRelease"
                class="left_class"
            >
                <div slot="content">
                    <!--bca-disable-next-line-->
                    {{release.message | raw}}
                </div>
                释放
            </s-tip-button>
        </div>
        <div slot="filter">
            <div class="filter-buttons-wrap">
                <s-search
                    width="{{230}}"
                    class="search-warp"
                    value="{=searchbox.keyword=}"
                    placeholder="{{searchbox.placeholder}}"
                    on-search="onSearch"
                >
                    <s-select
                        slot="options"
                        width="120"
                        datasource="{{searchbox.keywordTypes}}"
                        value="{=searchbox.keywordType=}"
                        on-change="onSearchboxChange($event)"
                    >
                    </s-select>
                </s-search>
                <s-button class="s-icon-button fresh_class left_class" on-click="refresh"
                    ><outlined-refresh
                /></s-button>
                <custom-column
                    columnList="{{customColumn.datasource}}"
                    initValue="{{customColumn.value}}"
                    type="haVip"
                    on-init="initColumns"
                    on-change="onCustomColumns"
                >
                </custom-column>
            </div>
        </div>
        <s-table
            columns="{{table.columns}}"
            loading="{{table.loading}}"
            error="{{table.error}}"
            selection="{=table.selection=}"
            datasource="{{table.datasource}}"
            on-selected-change="tableSelected($event)"
            on-filter="onFilter"
            on-sort="onSort"
        >
            <div slot="empty">
                <s-empty on-click="onCreate" class="{{iamPass.disable ? 'create-disable' : ''}}"> </s-empty>
            </div>
            <div slot="error">
                啊呀，出错了
                <a href="javascript:;" on-click="refresh">重新加载</a>
            </div>
            <div slot="c-id">
                <span class="truncated" title="{{row.name}}">
                    <a href="#/vpc/havip/detail?vpcId={{row.vpcUuid}}&haVipId={{row.haVipUuid}}"> {{row.name}} </a>
                </span>
                <s-popover s-ref="{{'nameEdit'+rowIndex}}" placement="top" trigger="click" class="edit-popover-class">
                    <div class="edit-wrap" slot="content">
                        <s-input
                            value="{=instance.name=}"
                            width="320"
                            placeholder="请输入名称"
                            on-input="onEditInput($event, rowIndex, 'name')"
                        />
                        <div class="edit-tip">支持大小写字母，数字，中文和"-_/.”以字母或者中文开头，不超过65个字</div>
                        <s-button
                            skin="primary"
                            s-ref="{{'nameEditBtn' + rowIndex}}"
                            disabled="{{true}}"
                            on-click="editConfirm(row, rowIndex, 'name')"
                        >
                            确定</s-button
                        >
                        <s-button on-click="editCancel(row, rowIndex, 'name')">取消</s-button>
                    </div>
                    <outlined-editing-square class="name-icon" s-if="row.name" on-click="beforeEdit(row, 'name')" />
                </s-popover>
                <br />
                <span class="truncated truncated_haId">{{row.haVipId}}</span>
                <s-clip-board text="{{row.haVipId}}">
                    <s-icon s-if="row.haVipId" name="copy" />
                </s-clip-board>
            </div>
            <div slot="c-status">
                <span class="{{row.status | statusClass}}">{{row.status | statusText}}</span>
            </div>
            <div slot="c-vpcId">
                <span class="truncated">
                    <a href="#/vpc/instance/detail?vpcId={{row.vpcId}}">{{row.vpcName || '-'}}</a>
                </span>
                <br />
                <span class="truncated">{{row.vpcId || '-'}}</span>
            </div>
            <div slot="c-subnet">
                <a
                    class="truncated"
                    href="#/vpc/subnet/detail?subnetId={{row.subnetUuid}}"
                    title="{{row.subnetName}}（{{row.subnetCidr}}）"
                >
                    {{row.subnetName}}
                </a>
                <br />
                <span s-f="row.subnetCidr">
                    {{row.subnetCidr ? ('(' + row.subnetCidr +')') : ''}} {{row.subnetIpv6Cidr ? ('(' +
                    row.subnetIpv6Cidr +')') : ''}}
                </span>
            </div>
            <div slot="c-server">
                <a href="#/vpc/havip/server?vpcId={{row.vpcUuid}}&haVipId={{row.haVipUuid}}">{{row.associateNum}}</a>
            </div>
            <div slot="c-publicIp">
                <span s-if="row.eip">{{row.eip}}/{{row.eipBandWidth}}Mbps</span>
                <span s-else>-</span>
            </div>
            <div slot="c-createTime">{{row | createdTime}}</div>
            <div slot="c-opt" class="opt_class">
                <s-tooltip trigger="{{eipBlackList ? 'hover' : ''}}" placement="top">
                    <!--bca-disable-next-line-->
                    <div slot="content" s-html="{{eipBlackTip}}"></div>
                    <s-button skin="stringfy" s-if="!row.eip" on-click="bindEip(row)" disabled="{{eipBlackList}}"
                        >绑定EIP</s-button
                    >
                </s-tooltip>
                <s-button skin="stringfy" s-if="row.eip" on-click="unbindEip(row)">解绑EIP</s-button>
                <br />
                <s-tooltip trigger="{{row.associateNum ? 'hover' : ''}}" placement="top">
                    <div slot="content">请您先解绑后端服务器后再释放</div>
                    <s-button skin="stringfy" on-click="onDelete(row)" disabled="{{row.associateNum}}">释放</s-button>
                </s-tooltip>
            </div>
            <div slot="c-description">
                <span class="truncated" title="{{row.description}}">{{row.description}}</span>
                <s-popover
                    s-ref="popover-description-{{rowIndex}}"
                    placement="top"
                    trigger="click"
                    class="edit-popover-class"
                >
                    <div class="edit-wrap" slot="content">
                        <s-input
                            value="{=edit.description.value=}"
                            width="160"
                            on-input="onEditInput($event, rowIndex, 'description')"
                        />
                        <div class="edit-tip">描述不能超过200个字符</div>
                        <s-button
                            skin="primary"
                            s-ref="editBtn-description-{{rowIndex}}"
                            disabled="{{true}}"
                            on-click="editConfirm(row, rowIndex, 'description')"
                            >确定</s-button
                        >
                        <s-button on-click="editCancel(rowIndex, 'description')">取消</s-button>
                    </div>
                    <outlined-editing-square on-click="onInstantEdit(row, rowIndex, 'description')" class="name-icon" />
                </s-popover>
            </div>
        </s-table>
        <s-pagination
            slot="pager"
            layout="{{'total, pageSize, pager, go'}}"
            pageSize="{{pager.size}}"
            total="{{pager.count}}"
            page="{{pager.page}}"
            on-pagerChange="onPagerChange"
            on-pagerSizeChange="onPagerSizeChange"
        />
    </s-app-list-page>
</template>`;

@template(tpl)
@withSidebar({active: 'vpc-havip-list'})
@invokeComp('@create-havip', '@vpc-select', '@custom-column')
@invokeAppComp
@invokeSUI
@invokeSUIBIZ
class HaVipList extends Component {
    static components = {
        'outlined-refresh': OutlinedRefresh,
        'outlined-plus': OutlinedPlus,
        'outlined-editing-square': OutlinedEditingSquare,
        'introduce-panel': IntroducePanel
    };
    initData() {
        let allColumns = SCHEMA;
        if (!FLAG.NetworkSupportEip) {
            allColumns = allColumns.filter(item => item.name !== 'publicIp');
        }
        const customColumnDb = allColumns.map(item => ({
            text: item.label,
            value: item.name,
            disabled: item.name === 'id' || item.name === 'opt'
        }));
        return {
            FLAG,
            title: '高可用虚拟IP',
            klass: 'vpc-havip-list',
            vpcId: '',
            table: {
                loading: false,
                error: null,
                datasource: [],
                selection: {
                    mode: 'multi',
                    selectedIndex: [],
                    selectedItems: []
                },
                allColumns
            },
            searchbox: {
                keyword: '',
                placeholder: '请输入实例名称进行搜索',
                keywordType: 'HAVIP_NAME',
                keywordTypes: [
                    {value: 'HAVIP_NAME', text: '实例名称'},
                    {value: 'HAVIP_ID', text: '实例ID'},
                    {value: 'SUBNET_NAME', text: '所在子网'},
                    {value: 'INTERNAL_IP', text: '实例内网IP'},
                    {value: 'PUBLIC_IP', text: '实例公网IP'}
                ]
            },
            pager: {
                size: 10,
                page: 1,
                count: 0,
                datasource: [
                    {text: '10', value: 10},
                    {text: '20', value: 20},
                    {text: '50', value: 50},
                    {text: '100', value: 100}
                ]
            },
            customColumn: {
                value: ['id', 'status', 'vpcId', 'subnetId', 'ip', 'publicIp', 'server', 'createTime', 'opt'],
                datasource: customColumnDb
            },
            eipBlackTip,
            iamPass: {},
            DocService,
            show: true,
            introduceTitle: '高可用虚拟IP简介',
            description:
                '高可用虚拟IP（High-Availability Virtual IP Address，HAVIP）是VPC内的私网IP资源，通过与高可用软件（如Keepalived等）配合使用，搭建高可用主备集群，实现业务高可用场景。',
            introduceIcon: INTRODUCE_ICON_COLOR,
            introduceEle: null
        };
    }

    filters = {
        statusClass(value) {
            return HaVipStatus.fromValue(value).styleClass || 'status error';
        },
        statusText(value) {
            return value ? HaVipStatus.getTextFromValue(value) : '异常';
        },
        createdTime(item) {
            return utcToTime(item.createTime);
        }
    };

    inited() {
        this.getIamQuery();
        this.eipBlackList();
        let {release} = checker.check(rules, []);
        this.data.set('release', release);
        let searchBoxData = this.data.get('searchbox.keywordTypes');
        !FLAG.NetworkSupportEip &&
            this.data.set(
                'searchbox.keywordTypes',
                searchBoxData.filter(item => item.value !== 'PUBLIC_IP')
            );
        this.loadPage();
    }
    attached() {
        this.data.set('introduceEle', this.ref('introduce'));
    }
    getSearchCriteria() {
        const {pager, searchbox} = this.data.get('');
        let payload = {
            pageNo: pager.page,
            pageSize: pager.size,
            keyword: searchbox.keyword,
            keywordType: searchbox.keywordType,
            vpcId: window.$storage.get('vpcId')
        };
        if (!payload.vpcId) {
            delete payload.vpcId;
        }
        return payload;
    }

    loadPage() {
        const payload = this.getSearchCriteria();
        this.resetTable();
        this.data.set('table.loading', true);
        this.$http
            .getHaVipList(payload)
            .then(data => {
                this.data.set('pager.count', data.totalCount);
                this.data.set('table.datasource', data.result);
            })
            .finally(() => this.data.set('table.loading', false));
    }

    resetTable() {
        this.data.set('selectedItems', []);
        this.data.set('table.selection.selectedIndex', []);
        this.data.set('table.datasource', []);
    }

    loadVpcInfo() {
        let vpcId = window.$storage.get('vpcId');
        return this.$http
            .getVpcDetail({
                vpcIds: vpcId ? [vpcId] : []
            })
            .then(data => {
                this.data.set('vpcInfo', !vpcId ? u.find(data, item => !u.isEmpty(item)) || {} : data[vpcId] || {});
            });
    }

    getHaVipQuota() {
        let vpcUuid = window.$storage.get('vpcId');
        if (!vpcUuid) {
            return;
        }
        let vpcInfo = this.data.get('vpcInfo');
        return this.$http.getHaVipQuota({vpcUuid}).then(data => {
            let createHaVip = this.getPermission('', 'createHaVip', {quota: data, vpcInfo});
            this.data.set('createBtn.disabled', createHaVip.disable);
            this.data.set('createBtn.tip', createHaVip.message);
            this.data.set('quota', data.total);
        });
    }

    getPermission(data, oprName, options) {
        const result = checker.check(rules, data, oprName, options);
        return result[oprName];
    }

    vpcChange() {
        this.data.set('pager.page', 1);
        this.loadPage();
        this.getHaVipQuota();
    }

    // 设置表格列
    setTableColumns(customColumnNames) {
        customColumnNames = customColumnNames ? customColumnNames : this.data.get('customColumn.value');
        const allColumns = this.data.get('table.allColumns');
        let columns = [];
        allColumns.forEach(item => customColumnNames.indexOf(item.name) > -1 && columns.push(item));
        this.data.set('table.columns', columns);
    }

    initColumns(value) {
        this.setTableColumns(value);
    }
    // 自定义表格列
    onCustomColumns(value) {
        this.data.set('customColumn.value', value);
        this.setTableColumns(value);
    }

    vpcInt() {
        this.loadVpcInfo().then(() => this.getHaVipQuota());
    }

    onCreate() {
        const dialog = new CreateHaVip({
            data: {
                vpcs: window.$storage
                    .get('vpcList')
                    .filter(item => item.value)
                    .map(item => {
                        if (item.vpcInfo.ipv6Cidr) {
                            return {
                                ...item,
                                text: `${item.text}(${item.vpcInfo.ipv6Cidr})`
                            };
                        } else return item;
                    }),
                vpcId: window.$storage.get('vpcId')
            }
        });
        dialog.on('success', () => {
            this.loadPage();
        });
        dialog.attach(document.body);
    }

    bindEip(row) {
        const dialog = new EipBindDialog({
            data: {
                instanceType: 'HAVIP',
                instanceId: row.haVipUuid,
                EipSDK: new EipSDK({client: window.$http, context: window.$context}),
                listRequester: query => this.$http.getEipBindList(query),
                submitRequester: query =>
                    this.$http.haVipBindEip({
                        eip: query.eip,
                        haVipUuid: row.haVipUuid
                    })
            }
        });
        dialog.on('cancel', () => {
            dialog.dispose();
        });
        dialog.on('success', () => {
            Notification.success('绑定成功');
            dialog.dispose();
            this.refresh();
        });
        dialog.on('create', ({module, path, query}) => {
            redirect({module, path, params: query});
        });
        dialog.attach(document.body);
    }

    unbindEip(row) {
        const confirm = new Confirm({
            data: {
                content: `您确认解绑${row.eip}吗？`
            }
        });
        confirm.on('confirm', () => {
            this.$http.haVipUnBindEip({haVipUuid: row.haVipUuid}).then(() => this.refresh());
        });
        confirm.attach(document.body);
    }

    onDelete(row) {
        const confirm = new Confirm({
            data: {
                title: '请确认',
                content: '确认释放该可用虚拟IP资源？'
            }
        });
        confirm.on('confirm', () => {
            this.$http.deleteHaVip({haVipUuids: [row.haVipUuid]}).then(() => this.loadPage());
        });
        confirm.attach(document.body);
    }

    beforeEdit(row, type) {
        const keyMap = {
            name: 'name',
            desc: 'description'
        };
        this.data.set(`instance.${type}`, row[keyMap[type]]);
    }

    onEditInput(e, rowIndex, type) {
        let result = true;
        if (type === 'name') {
            const pattern = /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{0,64}$/;
            result = pattern.test(e.value);
        } else if (type === 'description') {
            result = e.value.length <= 200;
        }
        this.data.set(`instance.${type}`, e.value);
        this.ref(`${type}EditBtn${rowIndex}`).data.set('disabled', !result);
    }

    editConfirm(row, rowIndex, type) {
        const keyMap = {
            name: 'name',
            desc: 'description'
        };
        const payload = {
            [keyMap[type]]: this.data.get(`instance.${type}`)
        };
        this.ref(`${type}EditBtn${rowIndex}`).data.set('disabled', true);
        payload.haVipUuid = row.haVipUuid;
        this.$http.updateHaVip(payload).then(() => {
            this.loadPage();
            this.ref(`${type}EditBtn${rowIndex}`).data.set('disabled', false);
        });
    }

    editCancel(row, rowIndex, type) {
        this.ref(`${type}Edit${rowIndex}`).data.set('visible', false);
    }

    onPagerSizeChange({value}) {
        this.data.set('pager.page', 1);
        this.data.set('pager.size', value.pageSize);
        this.loadPage();
    }

    // 搜索事件
    onSearch() {
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    onPagerChange({value}) {
        this.data.set('pager.page', value.page);
        this.loadPage();
    }

    refresh() {
        this.loadPage();
    }
    onRegionChange() {
        window.$storage.set('vpcId', '');
        location.reload();
    }
    onSearchboxChange({value}) {
        const {keywordTypes} = this.data.get('searchbox');
        let index = keywordTypes.findIndex(item => item.value === value);
        if (index > -1) {
            this.data.set('searchbox.keyword', '');
            this.data.set('searchbox.placeholder', `请输入${keywordTypes[index].text}进行搜索`);
        }
    }
    tableSelected({value}) {
        const {release} = checker.check(rules, value.selectedItems, '');
        this.data.set('release', release);
        this.data.set('selectedItems', value.selectedItems);
    }
    onRelease() {
        let confirm = new Confirm({
            data: {
                open: true,
                content: '确认释放可用虚拟IP资源？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            let ids = this.data.get('selectedItems').map(item => item.haVipUuid);
            this.$http.deleteHaVip({haVipUuids: ids}).then(() => {
                this.refresh();
                Notification.success('删除成功', {placement: 'topRight'});
            });
        });
    }
    eipBlackList() {
        const whiteList = window.$storage.get('commonWhite');
        this.data.set('eipBlackList', whiteList?.eipBlackList);
    }
    getIamQuery() {
        this.$http.getInterfaceIam({interfaceName: 'createHaVip'}).then(res => {
            if (!res.requestId && !res.masterAccount) {
                if (!res.interfacePermission) {
                    this.data.set('iamPass', {
                        disable: true,
                        message: '您没有创建高可用虚拟IP权限，请联系主用户添加'
                    });
                } else {
                    this.data.set('iamPass', {
                        disable: false,
                        message: ''
                    });
                }
            } else {
                this.data.set('iamPass', {
                    disable: false,
                    message: ''
                });
            }
        });
    }
    handleToggle(flag) {
        this.data.set('show', flag);
        this.data.set('introduceIcon', INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#151B26';
    }
    handleShowCard() {
        if (!this.data.get('show')) {
            this.data.set('show', true);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type) {
        this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#2468F2';
    }
    handleMouseLeave(type) {
        const isShow = this.data.get('show');
        this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        !isShow && (ele.style.color = '#151B26');
    }
}
export default San2React(Processor.autowireUnCheckCmpt(HaVipList));
