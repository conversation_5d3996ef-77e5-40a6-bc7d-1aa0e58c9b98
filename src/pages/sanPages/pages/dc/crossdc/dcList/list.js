/**
 * @file blb/appblb/list/List.es6
 * <AUTHOR>
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {
    ListPage,
    confirm,
    Button,
    SearchBox,
    Table,
    Icon,
    Tip,
    TableColumnToggle,
    Pager,
    Select
} from '@baiducloud/bce-ui/san';

import {utcToTime} from '@/pages/sanPages/utils/helper';
import iconImg from './iconImg';
import './style.less';
const {template} = decorators;
const AllRegion = window.$context.getEnum('AllRegion');

const SCHEMA = [
    {
        name: 'accountId',
        label: '用户ID',
        width: '15%'
    },
    {
        name: 'enterpriseName',
        label: '公司名称',
        width: '12%'
    },
    {
        name: 'peerConnId',
        label: '实例ID',
        width: '15%'
    },
    {
        name: 'localRegion',
        label: '本端地域',
        width: '9%'
    },
    {
        name: 'peerRegion',
        label: '对端地域',
        width: '9%'
    },
    {
        name: 'bandwidth',
        label: '带宽',
        width: '9%'
    },
    {
        name: 'createTime',
        label: '申请时间',
        width: '15%'
    },
    {
        name: 'rejectTime',
        label: '拒绝时间',
        width: '15%'
    },
    {
        name: 'allowTime',
        label: '开通时间',
        width: '15%'
    },
    {
        name: 'pauseTime',
        label: '关闭时间',
        width: '15%'
    },
    {
        name: 'operation',
        label: '操作',
        width: '12%'
    }
];

const NO_AUDITING = ['rejectTime', 'allowTime', 'pauseTime'];
const NO_AUDIT_FAILED = ['allowTime', 'pauseTime', 'operation'];
const NO_AUDIT_ACTIVE = ['rejectTime', 'pauseTime'];
const NO_AUDIT_PAUSE = ['rejectTime'];
const DEFALUT_PAGER = {
    size: 10,
    page: 1,
    count: 1,
    datasource: [
        {text: 10, value: 10},
        {text: 20, value: 20},
        {text: 50, value: 50},
        {text: 100, value: 100}
    ]
};
/* eslint-disable */

const tpl = html`
    <div>
        <x-page class="{{klass}}" title="{{title}}" with-sidebar="{{withSidebar}}">
            <div slot="helps">
                <ui-button
                    disabled="{{loading}}"
                    class="help-item"
                    icon="cdnrefresh"
                    on-click="refreshTable"
                    track-id="ti_blb_appblb_refresh"
                    track-name="刷新"
                />
            </div>
            <div slot="toolbar-left" s-if="type==='notopened'">
                <div
                    class="audit_btn {{currentStatus==='auditing'?'active':''}}"
                    on-click="setCurrentStatus('auditing')"
                >
                    待处理
                </div>
                <div
                    class="audit_btn {{currentStatus==='audit_failed'?'active':''}}"
                    on-click="setCurrentStatus('audit_failed')"
                >
                    已拒绝
                </div>
            </div>
            <div slot="toolbar-left" s-if="type==='opened'">
                <div class="audit_btn {{currentStatus==='active'?'active':''}}" on-click="setCurrentStatus('active')">
                    已开通
                </div>
                <div
                    class="audit_btn {{currentStatus==='auditor_pause'?'active':''}}"
                    on-click="setCurrentStatus('auditor_pause')"
                >
                    已关闭
                </div>
            </div>
            <div slot="toolbar-right">
                <ui-searchbox
                    value="{=$extraPayload.keyword=}"
                    keyword-type="{=$extraPayload.keywordType=}"
                    placeholder="{{filter.$searchbox.placeholder}}"
                    datasource="{{filter.$searchbox.datasource}}"
                    on-search="doSearch"
                    width="{{200}}"
                    multi="{{filter.$searchbox.multi}}"
                    loader="{{filter.$searchbox.loader}}"
                    skin="{{filter.$searchbox.skin}}"
                    track-id="ti_blb_appblb_search"
                    track-name="搜索"
                >
                </ui-searchbox>
                <ui-button
                    on-click="onDownloadList"
                    class="toolbarOpts-item"
                    title="{{'下载当前列表展现的实例信息，编码格式为utf-8'}}"
                    track-id="ti_blb_appblb_instance_download"
                    track-name="下载"
                    >{{'下载全部'}}
                </ui-button>
                <xui-table-column-toggle
                    class="toolbarOpts-item toolbarOpts-item-fr"
                    on-change="onToggleTableColumns"
                    layer-align="right"
                    value="{=tct.value=}"
                    label="{{'自定义列'}}"
                    datasource="{{tct.datasource}}"
                    track-id="ti_blb_appblb_instance_custom_fields"
                    track-name="自定义列"
                />
            </div>
            <xui-table
                selected-index="{=table.selectedIndex=}"
                schema="{{table.schema}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                on-row-enter="onEnterRow"
                on-row-leave="onLeaveRow"
                on-selected-change="onTableRowSelected($event)"
                on-sort="sortByTag($event)"
                on-command="onTableCommand($event)"
                track-id="ti_blb_appblb_table"
                track-name="应用型列表操作"
            >
                <template slot="empty"> {{'暂无数据'}} </template>
                <template slot="c-local_account_id">
                    <span class="instance-id" title="{{row.local_account_id}}"> {{row.local_account_id}} </span>
                </template>
                <template slot="c-enterpriseName">
                    <span>{{row.enterpriseName}}</span>
                </template>
                <template slot="c-peer_connection_id">
                    <span>{{row.peer_connection_id}}</span>
                </template>
                <template slot="c-localRegion">
                    <span>{{row.localRegion | regionToText}}</span>
                </template>
                <template slot="c-peerRegion">
                    <span>{{row.peerRegion | regionToText}}</span>
                </template>
                <template slot="c-bandwidth">
                    <span>{{row.bandwidth}}Mbps</span>
                </template>
                <template slot="c-createTime">
                    <span>{{row.createTime | timeFormat}}</span>
                </template>
                <template slot="c-rejectTime">
                    <span>{{row.rejectTime | timeFormat}}</span>
                </template>
                <template slot="c-allowTime">
                    <span>{{row.allowTime | timeFormat}}</span>
                </template>
                <template slot="c-pauseTime">
                    <span>{{row.pauseTime | timeFormat}}</span>
                </template>
                <template slot="c-operation">
                    <a data-command="agree" s-if="currentStatus==='auditing'" href="javascript:void(0)">同意</a>
                    &nbsp;&nbsp;
                    <a
                        data-command="reject"
                        style="color:#E02020"
                        s-if="currentStatus==='auditing'"
                        href="javascript:void(0)"
                        >拒绝</a
                    >
                    <a data-command="open" s-if="currentStatus==='auditor_pause'" href="javascript:void(0)">开通</a>
                    <a
                        data-command="close"
                        style="color:#E02020"
                        s-if="currentStatus==='active'"
                        href="javascript:void(0)"
                        >关闭</a
                    >
                </template>
                <div class="bui-table-error" slot="error">
                    {{'啊呀，出错了？'}}<a href="javascript:void(0)" on-click="onRefresh">{{'重新加载'}}</a>
                </div>
            </xui-table>
            <div slot="pager">
                <xui-pager
                    size="{{pager.size}}"
                    page="{{pager.page}}"
                    count="{{pager.count}}"
                    on-change="onPagerChange($event)"
                    on-pagesizechange="onPagerSizeChange($event)"
                    track-id="ti_appblb_pager"
                    track-name="翻页"
                >
                    <template slot="size-select">
                        <span>{{'每页显示'}}</span>
                        <xui-select
                            datasource="{{pager.datasource}}"
                            value="{=pager.size=}"
                            on-change="onPagerSizeChange($event)"
                            data-track-id="ti_appblb_paper_pagesize"
                            data-track-name="每页显示"
                        />
                    </template>
                </xui-pager>
            </div>
        </x-page>
    </div>
`;

@template(tpl)
class CrossDcList extends Component {
    static components = {
        'x-page': ListPage,
        'ui-button': Button,
        'ui-searchbox': SearchBox,
        'xui-table': Table,
        'xui-icon': Icon,
        'xui-pager': Pager,
        'xui-select': Select,
        'xui-table-column-toggle': TableColumnToggle
    };
    initData() {
        // 根据hash判断是展示未开通还是已开通，notopened：未开通；opened：已开通
        let type = 'notopened';
        // 未开通列表默认展示待处理tab
        let currentStatus = 'auditing';
        let defaultSchema = u.filter(SCHEMA, item => !u.contains(NO_AUDITING, item.name));
        let title = '待开通专线';
        if (location.hash.indexOf('notopened') === -1) {
            title = '已开通专线';
            type = 'opened';
            // 已开通列表默认展示已开通tab
            currentStatus = 'active';
            defaultSchema = u.filter(SCHEMA, item => !u.contains(NO_AUDIT_ACTIVE, item.name));
        }
        let TCT_VAL = defaultSchema.map(item => item.name);
        let TCT_DS = defaultSchema.map(item => {
            if (u.contains(['accountId', 'operation'], item.name)) {
                return {
                    text: item.label,
                    value: item.name,
                    disabled: true
                };
            }
            return {
                text: item.label,
                value: item.name
            };
        });
        return {
            klass: 'crossdc-list',
            title: title,
            filter: {
                $searchbox: {
                    keywordType: ['accountId'],
                    datasource: [
                        {value: 'accountId', text: '用户ID', expandable: false},
                        {value: 'enterpriseName', text: '公司名称', expandable: false}
                    ],
                    multi: true,
                    skin: 'blb-search-tag'
                }
            },
            $extraPayload: {
                keyword: ''
            },
            withSidebar: false,
            orderPayload: {
                orderBy: 'tag',
                order: 'desc'
            },
            table: {
                schema: defaultSchema,
                selectedIndex: [],
                selectedItems: [],
                datasource: [],
                filters: []
            },
            pager: DEFALUT_PAGER,
            tct: {
                value: TCT_VAL, // 获取缓存
                datasource: TCT_DS
            },
            currentStatus,
            type
        };
    }
    static filters = {
        timeFormat(time) {
            return utcToTime(time);
        },
        regionToText(region) {
            return AllRegion.getTextFromValue(region);
        }
    };

    static computed = {
        pageMax() {
            const pager = this.data.get('pager');
            return pager.count <= pager.size ? 1 : Math.ceil(pager.count / pager.size);
        },
        createTip() {
            let disableCreate = this.data.get('disableCreate');
            return disableCreate ? '售罄！请您移步其他地域购买资源。' : '';
        }
    };

    inited() {
        this.attachIconImg();
        const keywordType = this.data.get('filter.$searchbox.keywordType');
        if (keywordType) {
            this.data.set('$extraPayload.keywordType', keywordType);
        }
        const keyword = this.data.get('filter.$searchbox.value');
        if (keyword) {
            this.data.set('$extraPayload.keyword', keyword);
        }
        this.$childs = [];
    }

    attachIconImg() {
        const dialog = new iconImg();
        dialog.attach(document.querySelector('#header'));
    }

    doSearch() {
        this.disposeInternalChilds();
        this.data.set('pager.page', 1);
        const payload = this.getSearchCriteria();
        return this.loadPage(payload);
    }

    attached() {
        const payload = this.getSearchCriteria();
        this.loadPage(payload);
        const whiteList = window.$storage.get('commonWhite');
        if (!whiteList?.ChinaUnicomWhiteList) {
            location.hash = '#/dc/instance/list';
        }
        this.watch('currentStatus', item => {
            let newSchema = [];
            if (item === 'auditing') {
                newSchema = u.filter(SCHEMA, item => {
                    return !u.contains(NO_AUDITING, item.name);
                });
            } else if (item === 'audit_failed') {
                newSchema = u.filter(SCHEMA, item => {
                    return !u.contains(NO_AUDIT_FAILED, item.name);
                });
            } else if (item === 'active') {
                newSchema = u.filter(SCHEMA, item => {
                    return !u.contains(NO_AUDIT_ACTIVE, item.name);
                });
            } else if (item === 'auditor_pause') {
                newSchema = u.filter(SCHEMA, item => {
                    return !u.contains(NO_AUDIT_PAUSE, item.name);
                });
            }
            this.data.set('tct', {
                value: u.map(newSchema, item => item.name),
                datasource: u.map(newSchema, item => {
                    if (u.contains(['accountId', 'operation'], item.name)) {
                        return {
                            text: item.label,
                            value: item.name,
                            disabled: true
                        };
                    }
                    return {
                        text: item.label,
                        value: item.name
                    };
                })
            });
            this.data.set('table.schema', newSchema);
        });
    }

    setCurrentStatus(status) {
        this.data.set('currentStatus', status);
        const payload = this.getSearchCriteria();
        this.loadPage(payload);
    }

    onTableCommand({type, payload}) {
        switch (type) {
            case 'agree':
                this.showConfirm('确认同意开通该专线？', 'allow', payload);
                break;
            case 'reject':
                this.showConfirm('确定拒绝开通该专线？', 'reject', payload);
                break;
            case 'open':
                this.showConfirm('确认开通该专线？', 'auditor_start', payload);
                break;
            case 'close':
                this.showConfirm('确认关闭该专线？', 'auditor_stop', payload);
                break;
            default:
                break;
        }
    }

    showConfirm(text = '', status = '', payload = {}) {
        confirm({
            message: text
        }).then(() =>
            this.$http
                .auditCrossDc(payload.peerConnId, {
                    action: status
                })
                .then(() => this.refreshTable())
        );
    }

    getSearchCriteria() {
        const payload = {
            pageNo: this.data.get('pager.page'),
            pageSize: this.data.get('pager.size')
        };
        const extraPayload = this.data.get('$extraPayload');
        const keywordName = this.data.get('filter.$searchbox.name');
        let keyword = extraPayload.keyword;
        const keywordType = u.cloneDeep(extraPayload.keywordType);
        if (u.isArray(keywordType)) {
            extraPayload.keywordType = keywordType[0];
            if (keywordType.length > 1) {
                extraPayload.subKeywordType = keywordType[1];
                keyword === '空值' && (keyword = '');
                keyword === '所有值' && (keyword = '@@@');
            }
        }
        if (keywordName && keyword) {
            return u.extend(payload, u.omit(extraPayload, 'keyword'), {[keywordName]: keyword});
        }
        return u.extend(payload, extraPayload);
    }

    disposeInternalChilds() {
        u.each(this.$childs, component => {
            const dialog = component.ref('dialog');
            if (dialog) {
                dialog.dispose();
            }
            component.dispose();
        });
        this.$childs = [];
    }

    loadPage(payload = {}) {
        this.data.set('table.loading', true);
        u.extend(payload, {status: this.data.get('currentStatus')});
        this.$http
            .getCrossPeerconns(payload)
            .then(data => {
                this.data.set('table.datasource', data.result);
                this.data.set('pager.count', data.totalCount);
                this.data.set('table.loading', false);
            })
            .catch(() => this.data.set('table.loading', false));
    }

    // 重置列表
    resetTable() {
        this.data.set('table.loading', true);
        this.data.set('table.selectedIndex', []);
    }

    refreshTable() {
        this.disposeInternalChilds();
        this.resetTable();
        const payload = this.getSearchCriteria();
        return this.loadPage(payload);
    }

    sortByTag(e) {
        const {order, orderBy} = e;
        this.data.set('orderPayload', {order, orderBy});
        this.disposeInternalChilds();
        const payload = this.getSearchCriteria();
        return this.loadPage(
            u.extend(
                {
                    order,
                    orderBy
                },
                payload
            )
        );
    }

    onEnterRow(e) {
        this.data.set(`table.datasource[${e.rowIndex}].optActive`, true);
    }

    onLeaveRow(e) {
        this.data.set(`table.datasource[${e.rowIndex}].optActive`, false);
    }

    // 分页相关方法
    onPagerChange(e) {
        this.data.set('pager.page', e.pageNo);
        this.refreshTable();
    }

    onPagerSizeChange(e) {
        this.nextTick(() => this.refreshTable());
    }

    onTableRowSelected(e) {
        this.data.set('table.selectedItems', e.selectedItems);
    }

    // 下载功能
    onDownloadList() {
        window.open('/api/peerconn/crossBorder/peerconns/download?status=' + this.data.get('currentStatus'));
    }

    onToggleTableColumns() {
        const columnNames = this.data.get('tct.value');
        const schema = this.data.get('table.schema');
        u.each(schema, (item, index) =>
            this.data.set(`table.schema[${index}].xui__hidden`, !u.contains(columnNames, item.name))
        );
    }
}
export default San2React(Processor.autowireUnCheckCmpt(CrossDcList));
