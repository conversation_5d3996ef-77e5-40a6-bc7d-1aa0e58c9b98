/**
 * @file blb/appblb/list/List.es6
 * <AUTHOR>
 */

import {Component} from 'san';
import {Button, Drawer} from '@baidu/sui';
import {html, decorators, redirect} from '@baiducloud/runtime';
import {handleDownloadBinaryFile} from '@/utils/helper';
import './style.less';

let certificateTypeMap = {
    ID_CARD: '身份证',
    PASSPORT: '护照',
    MILITARY_ID_CARD: '军人身份证',
    POLICE_ID_CARD: '警察身份证',
    MACAO_PERMIT: '港澳居民往来内地通行证',
    TAIWAN_PERMIT: '台湾居民往来大陆通行证',
    MILITARY_OFFICER_CARD: '军官证',
    POLICE_OFFICER_CARD: '警官证',
    HMT_RESIDENCE_CARD: '港澳台居民居住证'
};

class ManageInfo extends Component {
    static template = html`
        <div>
            <ui-drawer
                open="{{open}}"
                otherClose="{{false}}"
                maskClose="{{false}}"
                title="{{title}}"
                size="{{size}}"
                direction="right"
            >
                <div class="drawer">
                    <div class="content-item">
                        <label class="content-item-label">姓名：</label>
                        <label class="content-item-value">{{payload.managerName}}</label>
                    </div>
                    <div class="content-item">
                        <label class="content-item-label">证件类别：</label>
                        <label class="content-item-value">{{payload.certificateType | certificateTypeMap}}</label>
                    </div>
                    <div class="content-item">
                        <label class="content-item-label">证件号：</label>
                        <label class="content-item-value">{{payload.certificateNum}}</label>
                    </div>
                    <div class="content-item">
                        <label class="content-item-label">证件地址：</label>
                        <label class="content-item-value">{{payload.certificateAddress}}</label>
                    </div>
                    <div class="content-item">
                        <label class="content-item-label">电话：</label>
                        <label class="content-item-value">{{payload.managerPhoneNum}}</label>
                    </div>
                    <div class="content-item">
                        <label class="content-item-label">营业执照复印件：</label>
                        <a
                            class="cross-download"
                            on-click="handleDownload($event, payload.certificateId, payload.agentIdCard ?
                                '营业执照复印件.pdf'
                                    : '营业执照复印件.png')"
                            >下载</a
                        >
                    </div>
                    <div s-if="payload.agentIdCard" class="content-item">
                        <label class="content-item-label">经办人身份证复印件：</label>
                        <a
                            class="cross-download"
                            on-click="handleDownload($event, payload.agentIdCard,'经办人身份证复印件.pdf')"
                            >下载</a
                        >
                    </div>
                    <div s-if="payload.introductionLetter" class="content-item">
                        <label class="content-item-label">介绍信：</label>
                        <a
                            class="cross-download"
                            on-click="handleDownload($event, payload.introductionLetter,'介绍信.pdf')"
                            >下载</a
                        >
                    </div>
                    <div s-if="payload.securityCommitmentLetter" class="content-item">
                        <label class="content-item-label">信息安全承诺书：</label>
                        <a
                            class="cross-download"
                            on-click="handleDownload($event,payload.securityCommitmentLetter,'信息安全承诺书.pdf')"
                            >下载</a
                        >
                    </div>
                    <!--<div class="footer" s-if="payload.showFooter">
                        <ui-button skin="primary" on-click="doAudit('agree', payload)">通过</ui-button>
                        <ui-button on-click="doAudit('reject', payload)">拒绝</ui-button>
                    </div>-->
                </div>
            </ui-drawer>
        </div>
    `;

    static components = {
        'ui-button': Button,
        'ui-drawer': Drawer
    };

    initData() {
        return {
            certificate: '',
            open: false
        };
    }

    doAudit(type, payload) {
        this.fire('audit', {type, payload});
    }
    attached() {
        this.data.set(
            'certificate',
            location.origin + '/api/peerconn/crossBorder/audit/certificate/' + this.data.get('payload.certificateId')
        );
    }
    handleDownload(e, certificateId, fileName) {
        e?.preventDefault();
        const isImage = fileName.endsWith('.png');
        const mimeType = isImage ? 'image/*' : 'application/pdf';
        handleDownloadBinaryFile(`/api/peerconn/crossBorder/audit/certificate/${certificateId}`, mimeType, fileName);
    }
}

ManageInfo.filters = {
    certificateTypeMap(key) {
        return certificateTypeMap[key];
    }
};
export default ManageInfo;
