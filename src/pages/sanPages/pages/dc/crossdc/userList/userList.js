/**
 * @file blb/appblb/list/List.es6
 * <AUTHOR>
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {
    ListPage,
    confirm,
    Button,
    SearchBox,
    FrozenColumnTable,
    Icon,
    Tip,
    TableColumnToggle,
    Pager,
    Select,
    InstantEditor,
    TextBox
} from '@baiducloud/bce-ui/san';
import {Input} from '@baidu/sui';

import iconImg from '../dcList/iconImg';
import ManageInfo from './manegerInfo';
import {TableTennisRacket} from '@baidu/xicon-san';
const {withSidebar, template, invokeSUI, invokeSUIBIZ} = decorators;
const AllRegion = window.$context.getEnum('AllRegion');
import './style.less';
import '../dcList/style.less';

const SCHEMA = [
    {
        name: 'accountId',
        label: '用户ID',
        width: 160,
        fixed: 'left'
    },
    {
        name: 'enterpriseName',
        label: '企业名称',
        width: 130,
        fixed: 'left'
    },
    {
        name: 'issuingAuthority',
        label: '发证机关',
        width: 120
    },
    {
        name: 'corporation',
        label: '法人经营者',
        width: 120
    },
    {
        name: 'registrationNum',
        label: '注册号',
        width: 160
    },
    {
        name: 'address',
        label: '住所/经营场所',
        width: 120
    },
    {
        name: 'mailingAddress',
        label: '客户通讯地址',
        width: 160
    },
    {
        name: 'postcode',
        label: '邮编',
        width: 120
    },
    {
        name: 'name',
        label: '联系人姓名',
        width: 120
    },
    {
        name: 'phoneNum',
        label: '联系人电话',
        width: 120
    },
    {
        name: 'mail',
        label: '联系人邮箱',
        width: 160
    },
    {
        name: 'enterpriseType',
        label: '总部行业分类',
        width: 120
    },
    {
        name: 'managerName',
        label: '客户经办人信息及企业证件扫描件',
        width: 220
    },
    {
        name: 'operation',
        label: '操作',
        width: 130,
        fixed: 'right'
    }
];

const DEFALUT_PAGER = {
    size: 10,
    page: 1,
    count: 1,
    datasource: [
        {text: 10, value: 10},
        {text: 20, value: 20},
        {text: 50, value: 50},
        {text: 100, value: 100}
    ]
};
const TCT_VAL = SCHEMA.map(item => item.name);
const TCT_DS = SCHEMA.map(item => {
    if (u.contains(['accountId', 'enterpriseName', 'operation'], item.name)) {
        return {
            text: item.label,
            value: item.name,
            disabled: true
        };
    }
    return {
        text: item.label,
        value: item.name
    };
});
/* eslint-disable */
const tpl = html`
    <template>
        <div>
            <x-page class="{{klass}}" title="{{title}}" breadcrumbs="{{breadcrumbs}}" with-sidebar="{{withSidebar}}">
                <div slot="helps">
                    <ui-button
                        disabled="{{loading}}"
                        class="help-item"
                        icon="cdnrefresh"
                        on-click="refreshTable"
                        track-id="ti_blb_appblb_refresh"
                        track-name="刷新"
                    />
                </div>
                <div slot="toolbar-left">
                    <div
                        class="audit_btn {{currentStatus==='AUDITING'?'active':''}}"
                        on-click="setCurrentStatus('AUDITING')"
                    >
                        待审核
                    </div>
                    <div
                        class="audit_btn {{currentStatus==='AUDIT_SUCCESS'?'active':''}}"
                        on-click="setCurrentStatus('AUDIT_SUCCESS')"
                    >
                        审核通过
                    </div>
                    <div
                        class="audit_btn {{currentStatus==='AUDIT_FAILED'?'active':''}}"
                        on-click="setCurrentStatus('AUDIT_FAILED')"
                    >
                        审核拒绝
                    </div>
                </div>
                <div slot="toolbar-right">
                    <ui-searchbox
                        value="{=$extraPayload.keyword=}"
                        keyword-type="{=$extraPayload.keywordType=}"
                        placeholder="{{filter.$searchbox.placeholder}}"
                        datasource="{{filter.$searchbox.datasource}}"
                        on-search="doSearch"
                        width="{{200}}"
                        multi="{{filter.$searchbox.multi}}"
                        loader="{{filter.$searchbox.loader}}"
                        skin="{{filter.$searchbox.skin}}"
                        track-id="ti_blb_appblb_search"
                        track-name="搜索"
                    >
                    </ui-searchbox>
                    <ui-button
                        on-click="onDownloadList"
                        class="toolbarOpts-item"
                        title="{{'下载当前列表展现的实例信息，编码格式为utf-8'}}"
                        track-id="ti_blb_appblb_instance_download"
                        track-name="下载"
                        >{{'下载全部'}}
                    </ui-button>
                    <xui-table-column-toggle
                        class="toolbarOpts-item toolbarOpts-item-fr"
                        on-change="onToggleTableColumns"
                        layer-align="right"
                        value="{=tct.value=}"
                        label="{{'自定义列'}}"
                        datasource="{{tct.datasource}}"
                        track-id="ti_blb_appblb_instance_custom_fields"
                        track-name="自定义列"
                    />
                </div>
                <s-table
                    columns="{{table.schema}}"
                    loading="{{table.loading}}"
                    error="{{table.error}}"
                    datasource="{{table.datasource}}"
                    on-row-enter="onEnterRow"
                    on-row-leave="onLeaveRow"
                    on-selected-change="onTableRowSelected($event)"
                    on-sort="sortByTag($event)"
                    track-id="ti_blb_appblb_table"
                    track-name="用户列表"
                >
                    <div class="bui-table-error" slot="error">
                        {{'啊呀，出错了？'}}<a href="javascript:void(0)" on-click="onRefresh">{{'重新加载'}}</a>
                    </div>
                    <div slot="c-managerName">
                        <a href="javascript:void(0)" on-click="showManagerInfo(row)">查看</a>
                    </div>
                    <div slot="c-operation">
                        <a on-click="showConfirm('AUDIT_SUCCESS', row)" href="javascript:void(0)">通过</a>&nbsp;&nbsp;
                        <a on-click="showConfirm('AUDIT_FAILED', row)" href="javascript:void(0)">拒绝</a>
                    </div>
                </s-table>
                <div slot="pager">
                    <xui-pager
                        size="{{pager.size}}"
                        page="{{pager.page}}"
                        count="{{pager.count}}"
                        on-change="onPagerChange($event)"
                        on-pagesizechange="onPagerSizeChange($event)"
                        track-id="ti_appblb_pager"
                        track-name="翻页"
                    >
                        <template slot="size-select">
                            <span>{{'每页显示'}}</span>
                            <xui-select
                                datasource="{{pager.datasource}}"
                                value="{=pager.size=}"
                                on-change="onPagerSizeChange($event)"
                                data-track-id="ti_appblb_paper_pagesize"
                                data-track-name="每页显示"
                            />
                        </template>
                    </xui-pager>
                </div>
            </x-page>
        </div>
        <s-dialog
            type="warning"
            showClose="{{false}}"
            class="crossdc-audit-action-dialog"
            closeAfterMaskClick="{{false}}"
            open="{=confirmOpen=}"
            title="{{confirmText}}"
        >
            <s-textarea
                maxLength="100"
                value="{=rejectReason=}"
                s-if="isShowDesc"
                style="width:100%;"
                on-input="handleInput($event)"
            ></s-textarea>
            <p class="crossdc-audit-refuse-tip" s-if="isShowDesc && rejectReasonError">{{rejectReasonError}}</p>
            <div slot="footer">
                <s-button on-click="onClose">取消</s-button>
                <s-button skin="primary" disabled="{{isShowDesc ? !rejectReason : false}}" on-click="dialogConfirm"
                    >确定</s-button
                >
            </div>
        </s-dialog>
    </template>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
class CrossDcUserList extends Component {
    static components = {
        'x-page': ListPage,
        'ui-button': Button,
        'ui-searchbox': SearchBox,
        'xui-table': FrozenColumnTable,
        'xui-icon': Icon,
        'xui-pager': Pager,
        'xui-select': Select,
        'xui-tip': Tip,
        'xui-table-column-toggle': TableColumnToggle,
        'xui-textbox': TextBox,
        's-textarea': Input.TextArea
    };

    static messages = {
        audit(arg) {
            this.onTableCommand(arg.value);
        }
    };

    initData() {
        return {
            klass: 'crossdc-user-list',
            title: '用户列表',
            confirmText: '',
            confirmOpen: false,
            filter: {
                $searchbox: {
                    keywordType: ['accountId'],
                    datasource: [
                        {value: 'accountId', text: '用户ID', expandable: false},
                        {value: 'enterpriseName', text: '公司名称', expandable: false}
                    ],
                    multi: true,
                    skin: 'blb-search-tag'
                }
            },
            $extraPayload: {
                keyword: ''
            },
            withSidebar: false,
            orderPayload: {
                orderBy: 'tag',
                order: 'desc'
            },
            table: {
                schema: SCHEMA,
                selectedIndex: [],
                selectedItems: [],
                datasource: [],
                filters: []
            },
            pager: DEFALUT_PAGER,
            tct: {
                value: TCT_VAL,
                datasource: TCT_DS
            },
            currentStatus: 'AUDITING',
            expand: false,
            expandTo: 'right',
            managerInfo: {},
            userInfo: {},
            isShowDesc: false,
            rejectReason: '',
            rejectReasonError: '请输入拒绝原因'
        };
    }

    static computed = {
        pageMax() {
            const pager = this.data.get('pager');
            return pager.count <= pager.size ? 1 : Math.ceil(pager.count / pager.size);
        },
        createTip() {
            let disableCreate = this.data.get('disableCreate');
            return disableCreate ? '售罄！请您移步其他地域购买资源。' : '';
        }
    };

    inited() {
        const keywordType = this.data.get('filter.$searchbox.keywordType');
        if (keywordType) {
            this.data.set('$extraPayload.keywordType', keywordType);
        }
        const keyword = this.data.get('filter.$searchbox.value');
        if (keyword) {
            this.data.set('$extraPayload.keyword', keyword);
        }
        this.$childs = [];
    }

    attachIconImg() {
        const dialog = new iconImg();
        dialog.attach(document.querySelector('#header'));
    }

    showManagerInfo(data) {
        const payload = {
            ...data,
            showFooter: this.data.get('currentStatus') === 'AUDITING'
        };
        const dialog = new ManageInfo({
            data: {
                open: true,
                title: '客户经办人/授权人信息及企业扫描件',
                size: 400,
                payload
            }
        });
        this.nextTick(() => {
            dialog.attach(document.body);
            dialog.on('audit', value => {
                this.onTableCommand(value);
            });
        });
    }

    handleInput(e) {
        const value = e.value;
        if (value) {
            this.data.set('rejectReasonError', '');
        } else {
            this.data.set('rejectReasonError', '请输入拒绝原因');
        }
    }

    doSearch() {
        this.disposeInternalChilds();
        this.data.set('pager.page', 1);
        const payload = this.getSearchCriteria();
        return this.loadPage(payload);
    }

    attached() {
        const payload = this.getSearchCriteria();
        this.loadPage(payload);
        this.$http.crossPeerconnWhiteList().then(data => {
            if (!data.inWhiteList) {
                location.hash = '#/dc/instance/list';
            }
        });
        this.watch('currentStatus', item => {
            let newSchema = [];
            if (item !== 'AUDITING') {
                newSchema = u.filter(SCHEMA, item => {
                    return item.name !== 'operation';
                });
                this.data.set('tct', {
                    value: u.map(newSchema, item => item.name),
                    datasource: u.map(newSchema, item => {
                        if (u.contains(['accountId', 'enterpriseName', 'operation'], item.name)) {
                            return {
                                text: item.label,
                                value: item.name,
                                disabled: true
                            };
                        }
                        return {
                            text: item.label,
                            value: item.name
                        };
                    })
                });
                this.data.set('table.schema', newSchema);
            } else {
                this.data.set('tct', {
                    value: TCT_VAL,
                    datasource: TCT_DS
                });
                this.data.set('table.schema', SCHEMA);
            }
        });
    }

    setCurrentStatus(status) {
        this.data.set('currentStatus', status);
        let payload = this.getSearchCriteria();
        this.loadPage(payload);
    }

    handleReset() {
        this.data.set('rejectReason', '');
        this.data.set('rejectReasonError', '请输入拒绝原因');
        this.data.set('confirmOpen', false);
    }

    onClose() {
        this.handleReset();
    }

    dialogConfirm() {
        const actionStatus = this.data.get('actionStatus');
        const rejectReason = this.data.get('rejectReason');
        if (actionStatus === 'AUDIT_FAILED' && !rejectReason) {
            this.data.set('rejectReasonError', '请输入拒绝原因');
            return;
        }
        const userInfo = this.data.get('userInfo');
        this.$http
            .auditUser(userInfo.accountId, {
                auditStatus: actionStatus,
                rejectReason
            })
            .then(() => {
                this.data.set('rejectReason', '');
                this.data.set('confirmOpen', false);
                this.refreshTable();
            });
    }

    showConfirm(status = '', payload = {}) {
        let text = '';
        status === 'AUDIT_SUCCESS' ? (text = '确认通过该用户审核？') : (text = '确认拒绝该用户审核？');
        this.data.set('confirmOpen', true);
        this.data.set('confirmText', text);
        this.data.set('userInfo', payload);
        this.data.set('actionStatus', status);
        if (status === 'AUDIT_FAILED') {
            this.data.set('isShowDesc', true);
        } else {
            this.data.set('isShowDesc', false);
        }
    }

    getSearchCriteria() {
        const payload = {
            pageNo: this.data.get('pager.page'),
            pageSize: this.data.get('pager.size')
        };
        const extraPayload = this.data.get('$extraPayload');
        const keywordName = this.data.get('filter.$searchbox.name');
        let keyword = extraPayload.keyword;
        const keywordType = u.cloneDeep(extraPayload.keywordType);
        if (u.isArray(keywordType)) {
            extraPayload.keywordType = keywordType[0];
            if (keywordType.length > 1) {
                extraPayload.subKeywordType = keywordType[1];
                keyword === '空值' && (keyword = '');
                keyword === '所有值' && (keyword = '@@@');
            }
        }
        if (keywordName && keyword) {
            return u.extend(payload, u.omit(extraPayload, 'keyword'), {[keywordName]: keyword});
        }
        return u.extend(payload, extraPayload);
    }

    disposeInternalChilds() {
        u.each(this.$childs, component => {
            const dialog = component.ref('dialog');
            if (dialog) {
                dialog.dispose();
            }
            component.dispose();
        });
        this.$childs = [];
    }

    loadPage(payload = {}) {
        this.data.set('table.loading', true);
        u.extend(payload, {status: this.data.get('currentStatus')});
        this.$http
            .getCrossUsers(payload)
            .then(data => {
                this.data.set('table.datasource', data.result);
                this.data.set('pager.count', data.totalCount);
                this.data.set('table.loading', false);
            })
            .catch(() => this.data.set('table.loading', false));
    }

    // 重置列表
    resetTable() {
        this.data.set('table.loading', true);
        this.data.set('table.selectedIndex', []);
    }

    refreshTable() {
        this.disposeInternalChilds();
        this.resetTable();
        const payload = this.getSearchCriteria();
        return this.loadPage(payload);
    }

    sortByTag(e) {
        const {order, orderBy} = e;
        this.data.set('orderPayload', {order, orderBy});
        this.disposeInternalChilds();
        const payload = this.getSearchCriteria();
        return this.loadPage(
            u.extend(
                {
                    order,
                    orderBy
                },
                payload
            )
        );
    }

    onEnterRow(e) {
        this.data.set(`table.datasource[${e.rowIndex}].optActive`, true);
    }

    onLeaveRow(e) {
        this.data.set(`table.datasource[${e.rowIndex}].optActive`, false);
    }

    // 分页相关方法
    onPagerChange(e) {
        this.data.set('pager.page', e.pageNo);
        this.refreshTable();
    }

    onPagerSizeChange(e) {
        this.nextTick(() => this.refreshTable());
    }

    onTableRowSelected(e) {
        this.data.set('table.selectedItems', e.selectedItems);
    }

    // 下载功能
    onDownloadList() {
        window.open('/api/peerconn/crossBorder/users/download?status=' + this.data.get('currentStatus'));
    }

    onToggleTableColumns() {
        const columnNames = this.data.get('tct.value');
        const schema = this.data.get('table.schema');
        u.each(schema, (item, index) =>
            this.data.set(`table.schema[${index}].xui__hidden`, !u.contains(columnNames, item.name))
        );
    }
}
export default San2React(Processor.autowireUnCheckCmpt(CrossDcUserList));
