/**
 * @file 专线信息列表
 */

import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {Notification, Dialog} from '@baidu/sui';
import {OutlinedRefresh, OutlinedPlus} from '@baidu/sui-icon';
import {decodeXssOutput} from '@/pages/sanPages/utils/xssTranslation';

import Create from './create';
import './style.less';

const {invokeSUI, invokeAppComp, template, invokeSUIBIZ} = decorators;
const tpl = html`
    <div>
        <s-app-list-page class="{{klass}}">
            <div class="dc-info-list-header" slot="pageTitle">
                <span class="title">运营商线路管理</span>
            </div>
            <div slot="bulk">
                <s-button skin="primary" on-click="onAdd" class="s-icon-button"><outlined-plus />新增</s-button>
            </div>
            <div slot="filter">
                <s-button class="s-icon-button" on-click="refresh"><outlined-refresh class="icon-class" /></s-button>
            </div>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                track-id="ti_vpc_instance_table"
                track-name="列表操作"
            >
                <div slot="empty">
                    <s-empty on-click="onAdd"> </s-empty>
                </div>
                <div slot="c-opt" class="operations">
                    <s-button skin="stringfy" on-click="onEdit(row)">修改</s-button>
                    <s-button skin="stringfy" on-click="onDelete(row)">删除</s-button>
                </div>
            </s-table>
            <s-pagination
                s-if="{{pager.total}}"
                slot="pager"
                layout="{{'total, pageSize, pager, go'}}"
                pageSize="{{pager.pageSize}}"
                total="{{pager.total}}"
                page="{{pager.page}}"
                on-pagerChange="onPagerChange"
                on-pagerSizeChange="onPagerSizeChange"
            />
        </s-app-list-page>

        <create-dialog s-if="showCreateDialog" edit-data="{{editData}}" on-close="onClose" on-cancel="onCancel" />
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class EtInfo extends Component {
    static components = {
        'create-dialog': Create,
        'outlined-refresh': OutlinedRefresh,
        'outlined-plus': OutlinedPlus
    };

    initData() {
        return {
            klass: 'dc-info-list-wrap',
            table: {
                loading: false,
                datasource: [],
                columns: [
                    {
                        name: 'dcphyId',
                        label: '公有云物理专线ID',
                        width: '150',
                        fixed: 'left'
                    },
                    {
                        name: 'ispLineNumber',
                        label: '运营商线路号',
                        width: '100'
                    },
                    {
                        name: 'ispName',
                        label: '运营商名称',
                        width: '100'
                    },
                    {
                        name: 'orgName',
                        label: '机构名称',
                        width: '80'
                    },
                    {
                        name: 'monitorIp',
                        label: '监控IP',
                        width: '60'
                    },
                    {
                        name: 'switchId',
                        label: '交换机设备号',
                        width: '100'
                    },
                    {
                        name: 'switchPort',
                        label: '交换机端口',
                        width: '100'
                    },
                    {
                        name: 'localDcphyIp',
                        label: '本端专线IP',
                        width: '100'
                    },
                    {
                        name: 'lineStruct',
                        label: '线路结构',
                        width: '100'
                    },
                    {
                        name: 'contact',
                        label: '机构网络联系人姓名及方式',
                        render(item) {
                            const result = item.contactPerson;
                            if (result && result !== '') {
                                return `${result}-${item.contactWay}`;
                            }
                            return item.contactWay || '';
                        },
                        width: '150'
                    },
                    {
                        name: 'remark',
                        label: '备注',
                        width: '150'
                    },
                    {
                        name: 'opt',
                        label: '操作',
                        width: '140',
                        fixed: 'right'
                    }
                ]
            },
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            showCreateDialog: false
        };
    }

    attached() {
        this.loadPage();
    }

    async loadPage() {
        this.data.set('table.loading', true);
        const {page, pageSize} = this.data.get('pager');
        try {
            const {result, totalCount} = await this.$http.getDcInfoList({pageNo: page, pageSize});
            this.data.set('table.datasource', result);
            this.data.set('pager.total', totalCount);
        } catch (e) {}
        this.data.set('table.loading', false);
    }

    // 改变页数
    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }

    // 改变每页显示数量
    onPagerSizeChange(e) {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', e.value.pageSize);
        this.loadPage();
    }

    refresh() {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', 10);
        this.loadPage();
    }

    onEdit(row) {
        const decodedRow = {};
        u.each(Object.keys(row), item => {
            decodedRow[item] = row[item] ? decodeXssOutput(row[item]) : '';
        });
        this.data.set('editData', decodedRow);
        this.data.set('showCreateDialog', true);
    }

    async onDelete(row) {
        Dialog.warning({
            content: '确认删除该条运营商线路？',
            onOk: async () => {
                await this.$http.deleteDcInfo(row.id);
                this.refresh();
                Notification.success('删除成功');
            }
        });
    }

    onAdd() {
        this.data.set('showCreateDialog', true);
    }

    onClose() {
        this.onCancel();
        this.refresh();
    }

    onCancel() {
        this.data.set('showCreateDialog', false);
        this.data.set('editData', null);
    }
}
export default San2React(Processor.autowireUnCheckCmpt(EtInfo));
