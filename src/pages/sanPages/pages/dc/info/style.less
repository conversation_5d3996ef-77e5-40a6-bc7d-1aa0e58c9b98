.dc-info-list-wrap {
    height: 100%;
    overflow: inherit !important;

    .dc-info-list-header {
        height: 50px;
        display: flex;
        align-items: center;
        background-color: #fff;
        border-bottom: 1px solid #ebebeb;
        .title {
            display: inline-block;
            margin: 0;
            color: #151a26;
            margin-right: 10px;
            height: 47px;
            line-height: 47px;
            font-weight: 500;
            font-size: 16px;
            margin-left: 16px;
        }
    }
    .s-table-cell-text {
        white-space: nowrap;
        overflow-x: hidden;
        text-overflow: ellipsis;
        text-align: center;
    }
    .s-table .s-table-hcell .s-table-hcell-text {
        line-height: 1.6;
    }
    .s-table-body {
        max-height: calc(~'100vh - 374px');
    }
    .buttons-wrap {
        .s-button {
            margin-left: 0;
        }
    }
    .operations {
        .s-button {
            padding: 0%;
            margin-right: 12px;
        }
    }
}

.dc-info-create {
    &-form {
        .s-form-item-label {
            width: 120px;
        }
        .s-form-item-control-wrapper {
            width: calc(~'100% - 120px');

            .s-input {
                width: 100%;

                &-area {
                    width: 100%;

                    input {
                        width: calc(~'100% - 20px') !important;
                    }
                }
            }
        }
    }
}