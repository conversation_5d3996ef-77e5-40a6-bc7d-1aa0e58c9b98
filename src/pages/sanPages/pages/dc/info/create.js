/**
 *  @file 创建专线信息维护弹窗
 *  <AUTHOR>
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {Notification} from '@baidu/sui';
import {encodeXssInput} from '@/pages/sanPages/utils/xssTranslation';

const {asComponent, invokeSUI, invokeAppComp, template} = decorators;
const SHORT_FILED = {type: 'string', max: 64, message: '请输入64个字符以内的字符串'};
const LONG_FILED = {type: 'string', max: 200, message: '请输入200个字符以内的字符串'};
const tpl = html` <template>
    <s-dialog class="dc-info-create" open="{{true}}" title="{{title}}" on-close="onClose">
        <s-form s-ref="form" data="{=formData=}" rules="{{rules}}" label-align="left" class="dc-info-create-form">
            <s-form-item prop="dcphyId" label="公有云物理专线ID：">
                <s-input value="{=formData.dcphyId=}" placeholder="请输入公有云物理专线ID'" />
            </s-form-item>
            <s-form-item prop="ispLineNumber" label="运营商线路号：">
                <s-input value="{=formData.ispLineNumber=}" placeholder="请输入运营商线路号" />
            </s-form-item>
            <s-form-item prop="ispName" label="运营商名称：">
                <s-input value="{=formData.ispName=}" placeholder="请输入运营商名称" />
            </s-form-item>
            <s-form-item prop="orgName" label="机构名称：">
                <s-input value="{=formData.orgName=}" placeholder="请输入机构名称" />
            </s-form-item>
            <s-form-item prop="monitorIp" label="监控IP：">
                <s-input value="{=formData.monitorIp=}" placeholder="请输入监控IP" />
            </s-form-item>
            <s-form-item prop="switchId" label="交换机设备号：">
                <s-input value="{=formData.switchId=}" placeholder="请输入交换机设备号" />
            </s-form-item>
            <s-form-item prop="switchPort" label="交换机端口：">
                <s-input value="{=formData.switchPort=}" placeholder="请输入交换机端口" />
            </s-form-item>
            <s-form-item prop="localDcphyIp" label="本端专线IP：">
                <s-input value="{=formData.localDcphyIp=}" placeholder="请输入本端专线IP" />
            </s-form-item>
            <s-form-item prop="lineStruct" label="线路结构：">
                <s-input value="{=formData.lineStruct=}" placeholder="请输入线路结构" />
            </s-form-item>
            <s-form-item prop="contactPerson" label="机构网络联系人姓名：">
                <s-input value="{=formData.contactPerson=}" placeholder="请输入机构网络联系人姓名" />
            </s-form-item>
            <s-form-item prop="contactWay" label="机构网络联系人方式：">
                <s-input value="{=formData.contactWay=}" placeholder="请输入机构网络联系人方式" />
            </s-form-item>
            <s-form-item prop="remark" label="备注：">
                <s-input value="{=formData.remark=}" placeholder="请输入备注" />
            </s-form-item>
        </s-form>

        <div slot="footer">
            <s-button on-click="onClose">取消</s-button>
            <s-button on-click="onConfirm" disabled="{{submiting}}" skin="primary"> 确定 </s-button>
        </div>
    </s-dialog>
</template>`;

@template(tpl)
@invokeSUI
@invokeAppComp
@asComponent('@et-info-create')
class EtInfoCreate extends Component {
    initData() {
        return {
            title: '新增运营商线路',
            rules: {
                dcphyId: [SHORT_FILED, {required: true, message: '公有云物理专线ID不能为空'}],
                ispLineNumber: [SHORT_FILED],
                ispName: [SHORT_FILED],
                orgName: [SHORT_FILED],
                monitorIp: [SHORT_FILED],
                switchId: [SHORT_FILED],
                switchPort: [SHORT_FILED],
                localDcphyIp: [SHORT_FILED],
                lineStruct: [SHORT_FILED],
                contactPerson: [SHORT_FILED],
                contactWay: [SHORT_FILED],
                remark: [LONG_FILED]
            },
            type: 'new'
        };
    }

    inited() {
        const data = this.data.get('editData');
        if (data) {
            this.data.set('title', '编辑运营商线路');
            this.data.set('type', 'edit');
            this.data.set('id', data.id);
            delete data.id;
            this.data.set('formData', data);
        }
    }

    xssInputTranslation(formData) {
        const encodedFormData = {};
        u.each(Object.keys(formData), item => {
            encodedFormData[item] = encodeXssInput(formData[item]);
        });
        return encodedFormData;
    }

    async createDcInfo() {
        const formData = this.data.get('formData');
        const encodedFormData = this.xssInputTranslation(formData);
        try {
            await this.$http.createDcInfo(encodedFormData);
            this.fire('close');
            Notification.success('添加成功');
        } catch (err) {
            Notification.error('添加失败，请重试');
        }
        this.data.set('submiting', false);
    }

    async editDcInfo() {
        const {id, formData} = this.data.get();
        const encodedFormData = this.xssInputTranslation(formData);
        try {
            await this.$http.updateDcInfo(id, encodedFormData);
            this.fire('close');
            Notification.success('编辑成功');
        } catch (err) {
            Notification.error('编辑失败，请重试');
        }
        this.data.set('submiting', false);
    }

    async onConfirm() {
        await this.ref('form').validateFields();
        this.data.set('submiting', true);
        if (this.data.get('type') === 'new') {
            this.createDcInfo();
        } else {
            this.editDcInfo();
        }
    }

    onClose() {
        this.fire('cancel');
    }
}
export default Processor.autowireUnCheckCmpt(EtInfoCreate);
