/*
 * @Description:
 * @Author: <EMAIL>
 * @Date: 2022-04-12 11:04:44
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor as RuntimeProcessor} from '@baiducloud/runtime';
import {Notification} from '@baidu/sui';
import {
    OutlinedEditingSquare,
    OutlinedRefresh,
    OutlinedPlus,
    OutlinedDownload,
    OutlinedQuestion
} from '@baidu/sui-icon';
import {DiagnoseSDKProcessor, AnalysisSDKProcessor} from '@baidu/bce-vpc-sdk-react';
import {request} from '@/apis';

import {getQueryParams} from '@baidu/bce-react-toolkit';
import ResetKey from '../create/resetBgpKey';
import Monitor from '../monitor/monitor';
import EditDialog from '../list/editDialog';
import rules from '../../instance/rules';
import EditUsers from './editUsers';
import {columns} from './tableField';
import ChannelListOperation from './moreOpt';
import {ObjectToQuery} from '@/utils';
import MoreOperation from './MoreOperation';
import {InstanceStatus, ChannelRouteBgpStatus} from '@/pages/sanPages/common/enum';
import {getEtAvaliableRegion, confirmValidate, $flag as FLAG} from '@/pages/sanPages/utils/helper';
import Confirm from '@/pages/sanPages/components/confirm';
import DiagnoseConfirm from '@/pages/sanPages/components/diagnoseConfirm/confirm';
import EtChannelCreate from '@/pages/sanPages/pages/dc/channel/create/create';
import './moreOpt';
import '@baidu/bce-vpc-sdk-react/lib/style.css';

import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import './style.less';

const {asComponent, invokeSUI, invokeAppComp, invokeSUIBIZ, template, invokeComp} = decorators;
const processor = new DiagnoseSDKProcessor();
const processorPath = new AnalysisSDKProcessor();

const tpl = html`
<div>
    <div class="title_channel">
        <span>专线通道</span>
        <a
            s-ref="introduce"
            href="javascript:void(0)"
            class="help-file function-introduce"
            on-click="handleShowCard"
            on-mouseenter="handleMouseEnter('introduce')"
            on-mouseleave="handleMouseLeave('introduce')"
            >
            <img class="s-icon" src="{{introduceIcon}}" />功能简介
        </a>
    </div>
    <s-biz-page class="{{klass}}">
        <div class="channel-instance-header" slot="header">
            <div style="background:#fff">
                <s-select filterable
                    width="{{240}}"
                    on-change="dcOnChange"
                    datasource="{{dcList}}"
                    value="{=dcphyId=}"
                ></s-select>
            </div>
            <introduce-panel
                isShow="{{show}}"
                klass="{{'endpoint-peerconn-wrapper'}}"
                title="{{introduceTitle}}"
                description="{{description}}"
                introduceOptions="{{introduceOptions}}"
                on-toggle="handleToggle($event)"
            ></introduce-panel>
        </div>
        <div class="list-page-tb-left-toolbar" slot="tb-left">
            <s-tip-button
                disabled="{{createDc.disable}}"
                skin="primary"
                isDisabledVisibile="{{true}}"
                on-click="onCreate">
                <div slot="content">
                    {{createDc.message}}
                </div>
                <outlined-plus/>{{'创建专线通道'}}
            </s-tip-button>
            <edit-tag
                selectedItems="{{table.selectedItems}}"
                on-success="refresh"
                class="left_class"
                type="ET_CHANNEL"
                editTag="{{editTag}}"
                ></edit-tag>
        </div>
        <div slot="tb-right">
            <div class="dc-buttons-wrap">
                <search-res
                    s-ref="search"
                    serviceType="ET_CHANNEL"
                    searchbox="{=searchbox=}"
                    on-search="onSearch"
                    class="search-et"
                ></search-res>
                <s-button class="s-icon-button button-margin-left" on-click="refresh" track-name="刷新">
                    <outlined-refresh class="icon-class"/></s-button>
                </s-button>
                <s-button
                    on-click="handleDownload"
                    class="s-icon-button"
                    track-id="ti_vpc_nat_download"
                    track-name="下载"
                    ><outlined-download class="icon-class"
                /></s-button>
                <custom-column
                    class="left_class"
                    columnList="{{customColumn.datasource}}"
                    initValue="{{customColumn.value}}"
                    type="channel"
                    on-init="initColumns"
                    on-change="onCustomColumns">
                </custom-column>
            </div>
        </div>
        <s-table
            columns="{{table.columns}}"
            loading="{{table.loading}}"
            error="{{table.error}}"
            datasource="{{table.datasource}}"
            on-selected-change="tableSelected($event)"
            on-filter="onFilter"
            on-sort="onSort"
            selection="{=table.selection=}"
            track-id="ti_vpc_channel_table"
            track-name="列表操作">
            <div slot="empty">
                <s-empty on-click="onCreate">
                </s-empty>
            </div>
            <div slot="c-dcphyId">
                <a
                    href="#/dc/instance/list"
                    class="truncated">
                    {{row.dcphyId}}
                </a>
                <s-clip-board text="{{row.dcphyId}}"><s-icon name="copy"/></s-clip-board>
            </div>
            <div slot="c-id">
                <a
                    href="#/dc/channel/detail?instanceId={{row.dcphyId}}&channelId={{row.id}}&creator={{creator}}"
                    class="truncated">
                    {{row.id}}
                </a>
                <s-clip-board text="{{row.id}}"><s-icon name="copy"/></s-clip-board>
            </div>
            <div slot="c-name">
                {{row.name}}
                <s-popover class="edit-popover-class"
                    s-ref="{{'nameEdit' + rowIndex}}"
                    placement="top"
                    trigger="click"
                    s-if="row.manager && row.status !== 'reject' && row.status !== 'ack-wait'"
                >
                    <div class="edit-wrap" slot="content">
                        <s-input
                            value="{=instance.name=}"
                            width="320"
                            placeholder="请输入名称"
                            on-input="onEditInput($event,row, rowIndex, 'name')"/>
                        <div class="edit-tip">大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65</div>
                        <s-button skin="primary" s-ref="{{'nameBtn' + rowIndex}}" disabled="{{true}}"
                            on-click="onEdit(row, 'name')">{{'确定'}}</s-button>
                        <s-button on-click="editCancel(row,rowIndex,'name')">{{'取消'}}</s-button>
                    </div>
                    <outlined-editing-square class="name-icon" on-click="beforeEdit(row)"/>
                </s-popover>
            </div>
            <div slot="c-region">
                <span>{{row.region | regionText}}</span>
            </div>
            <div slot="c-status">
                <span class="{{row.status | statusClass('channel')}}">{{row.status | statusText('channel')}}</span>
            </div>
            <div slot="c-bgpStatus">
                <span class="{{row.bgpStatus | statusClass('bgp')}}">{{row.bgpStatus | statusText('bgp')}}</span>
            </div>
            <div slot="c-ipv6BgpStatus">
                <span class="{{row.ipv6BgpStatus | statusClass('bgp')}}">{{row.ipv6BgpStatus | statusText('bgp')}}</span>
            </div>
            <div slot="c-authorizedUsers">
                <p>
                    <span s-if="row.onlySelfAuthorized">{{'本账户'}}</span>
                    <span s-else>{{row.authorizedUsers[0]}}</span>
                    <outlined-editing-square
                        class="name-icon"
                        s-if="row.onlySelfAuthorized && (row.status !== 'reject' && row.status !== 'ack-wait' && row.status !== 'established')"
                        on-click="editUsers(row)"/>
                </p>
            </div>
            <div slot="c-manager">
                <span class="text-overflow">
                    {{(creator === 'oneself' && row.onlySelfAuthorized) ? '-' : (row.assignManager ? '管理权' : (row.assignManager === false ? '使用权' : '-')) }}
                </span>
            </div>
            <div slot="c-ips">
                <p>{{row.localIp}}{{'（云端网络）'}}</p>
                <p>{{row.remoteIp}}{{'（IDC端）'}}</p>
            </div>
            <div slot="c-routeType">
                <p>{{(row.routeType ? (row.routeType === 'bgp' ? 'BGP' : '静态') : '-')}}</p>
            </div>
            <div slot="c-bgpKey">
                <span class="text-overflow">{{row.bgpKey ? row.bgpKey : '-'}}</span>
            </div>
            <div slot="c-bgpAsn">
                <span class="text-overflow">{{row.bgpAsn ? row.bgpAsn : '-'}}</span>
            </div>
            <div slot="c-fakeAsn">
                <span class="text-overflow">{{row.fakeAsn ? row.fakeAsn : '-'}}</span>
            </div>
            <div slot="c-localIpv6">
                <p>{{row.localIpv6 || '-'}}{{'（云端网络）'}}</p>
                <p>{{row.remoteIpv6 || '-'}}{{'（IDC端）'}}</p>
            </div>
            <div slot="c-bgpRouteLimit">
                <p>
                    {{row.routeType === 'bgp' ? row.bgpRouteLimit : '-'}}
                    <s-popover class="edit-pop"
                        s-ref="{{'bgpRouteLimitEdit' + rowIndex}}"
                        placement="top"
                        trigger="click"
                    >
                        <div class="edit-content" slot="content">
                            <s-input-number
                                min="{{row.bgpRouteLimit}}"
                                max="{{quota}}"
                                value="{=instance.bgpRouteLimit=}"
                                width="160"
                                placeholder="{{'请输入'}}"
                                on-change="bgpRouteChange($event,row, rowIndex, 'bgpRouteLimit')"
                                on-input="onEditInput($event,row, rowIndex, 'bgpRouteLimit')"/>
                            <div class="edit-tip {{bgpLimitClass}}">{{bgpLimitTip}}</div>
                            <s-button skin="primary" s-ref="{{'bgpRouteLimitBtn' + rowIndex}}" disabled="{{true}}"
                                on-click="onEdit(row, 'bgpRouteLimit')">{{'确定'}}</s-button>
                            <s-button on-click="editCancel(row,rowIndex,'bgpRouteLimit')">{{'取消'}}</s-button>
                            <p class="edit-tip"></p>
                        </div>
                        <s-icon class="edit-icon" on-click="beforeEdit(row)" name="edit" s-if="{{quota > row.bgpRouteLimit && row.routeType === 'bgp' && row.manager}}"/>
                    </s-popover>
                </p>
            </div>
            <div slot="h-bgpRouteLimit">
                <span>BGP路由条目上限</span>
                <s-tip
                    class="inline-tip"
                >
                    <s-question class="question-class warning-class"></s-question>
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{quotaApplyHref | raw}}
                    </div>
                </s-tip>
            </div>
            <div slot="c-description">
                <p class="desc-cell">
                    <span class="truncated">{{row.description || '-'}}</span>
                    <s-popover class="edit-popover-class"
                        s-ref="{{'descEdit' + rowIndex}}"
                        placement="top"
                        trigger="click"
                        s-if="row.manager && row.status !== 'reject' && row.status !== 'ack-wait'"
                    >
                        <div class="edit-wrap" slot="content">
                            <s-input
                                value="{=instance.desc=}"
                                width="160"
                                placeholder="{{'请输入'}}"
                                on-input="onEditInput($event,row, rowIndex, 'desc')"/>
                            <div class="edit-tip">描述不能超过200个字符</div>
                            <s-button skin="primary" s-ref="{{'descBtn' + rowIndex}}" disabled="{{true}}"
                                on-click="onEdit(row, 'desc')">{{'确定'}}</s-button>
                            <s-button on-click="editCancel(row,rowIndex,'desc')">{{'取消'}}</s-button>
                        </div>
                        <outlined-editing-square class="name-icon" on-click="beforeEdit(row)"/>
                    </s-popover>
                </p>
            </div>
            <div slot="c-tag">
                <!--bca-disable-next-line-->
                {{row.tags | getTag | raw}}
            </div>
            <div slot="h-diagnose">
                <span
                    >诊断
                    <div class="new-tag new-instance-tag">new</div></span
                >
            </div>
            <div slot="c-diagnose">
                <x-moreoperation
                    item="{{row}}"
                    on-diagnose="diagnoseChannel(row)"
                    on-pathAnalysis="pathAnalysisEip(row)"
                    isOpenDrawer="{{openDrawer}}"
                />
            </div>
            <div slot="c-opt" class="operations">
                <s-button skin="stringfy" on-click="onMonitor(row)">{{'监控'}}</s-button>
                <s-button skin="stringfy" on-click="onAlarm(row)">{{'报警详情'}}</s-button>
                <s-tip-button
                    disabled="{{row | deleteDisable}}"
                    skin="stringfy"
                    isDisabledVisibile="{{true}}"
                    on-click="onDelete(row)">
                    <div slot="content">
                        {{row | deleteDisable}}
                    </div>
                    删除
                </s-tip-button>
                <s-tip-button
                    s-if="{{ipv6Show && row.manager}}"
                    disabled="{{row.status !== 'established' || !row.manager}}"
                    skin="stringfy"
                    isDisabledVisibile="{{true}}"
                    on-click="onEditIPV6(row)">
                    <div slot="content">
                        {{row | getIpv6DisabledText}}
                    </div>
                    IPv6编辑
                </s-tip-button>
                <s-button s-if="isShowRecreate(row)" skin="stringfy" on-click="onReCreate(row)">
                    {{'重新提交'}}
                </s-button>
                <s-button skin="stringfy" on-click="onResetKey(row)" s-if="{{row.manager && row.routeType === 'bgp'}}">
                    {{'重置密钥'}}
                </s-button>
                <s-button
                    s-if="isShowAcceptReject(row)"
                    skin="stringfy"
                    on-click="handleCrossAction(row, 'accept')"
                >{{"接受"}}</s-button>
                <s-button
                    s-if="isShowAcceptReject(row)"
                    skin="stringfy"
                    on-click="handleCrossAction(row, 'reject')"
                >{{"拒绝"}}</s-button>
                <channel-list-operation
                    class="nat_multiple_class"
                    creator="{{creator}}"
                    item="{{row}}"
                    on-command="changeOpt"
                    row-index="{{rowIndex}}"/>
            </div>
        </s-table>
        <s-pagination
            s-if="{{pager.total}}"
            slot="footer"
            layout="{{'pageSize, pager'}}"
            pageSize="{{pager.pageSize}}"
            total="{{pager.total}}"
            page="{{pager.page}}"
            resetPageWhenSizeChange="{{true}}"
            on-pagerChange="onPagerChange"/>
    </s-biz-page>
    <div id="satisfactionNew" s-if="drawerVisible"></div>
</div>`;
@asComponent('@channel-instance-list')
@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@invokeComp(
    '@custom-column',
    '@channel-list-operation',
    '@channel-ipv6Asn-edit',
    '@search-res',
    '@edit-tag',
    '@introduce-panel'
)
class ChannelList extends Component {
    static components = {
        'outlined-refresh': OutlinedRefresh,
        'outlined-editing-square': OutlinedEditingSquare,
        'outlined-plus': OutlinedPlus,
        'channel-list-operation': ChannelListOperation,
        'outlined-download': OutlinedDownload,
        's-question': OutlinedQuestion,
        'x-moreoperation': MoreOperation
    };
    static filters = {
        statusClass(value: string, type: 'channel' | 'bgp') {
            if (type === 'channel') {
                const currStatus = InstanceStatus.getChannelStatus().find((item: any) => item.value === value);
                return currStatus?.kclass || '';
            }
            return ChannelRouteBgpStatus.fromValue(value).kclass || '';
        },
        statusText(value: string, type: 'channel' | 'bgp') {
            if (type === 'channel') {
                const currStatus = InstanceStatus.getChannelStatus().find((item: any) => item.value === value);
                return currStatus ? currStatus.text : '-';
            }
            return value && ChannelRouteBgpStatus.getTextFromValue(value)
                ? ChannelRouteBgpStatus.getTextFromValue(value)
                : '-';
        },
        getContent(value: any) {
            if (!value.dcgwId) {
                return '请先绑定专线网关后再关联其他专线通道';
            }
            return '';
        },
        getGray(value: any) {
            if (!value.dcgwId) {
                return 'gray';
            }
            return '';
        },
        // 没有绑定专线网关，没有关联关系，没有配置bfd，没有在未完成的故障演练任务中，并且是以下几种状态才可删除
        deleteDisable(row: any) {
            const {manager, healthCheckMode, dcgwId, inDrillTask} = row;
            const creator = this.data.get('creator');
            if (healthCheckMode !== 'close') {
                return '请先关闭专线通道的可靠性检测，再删除专线通道';
            } else if (dcgwId) {
                return '请先解除专线网关绑定，再删除专线通道';
            }
            const notDeletedStatus = u.contains([InstanceStatus.APPLYING, InstanceStatus.BUILDING], row.status);
            // 不可删状态
            if (notDeletedStatus) {
                return '当前状态不可删除';
            }
            // 可删除状态，判断是否在未完成的故障演练任务中
            if (!!inDrillTask) {
                return '当前通道在未完成的故障演练任务中，不可删除';
            }
            const deletedStatus = u.contains([InstanceStatus.ACTIVE, 'stopped'], row.status);
            // 可删需判断管理权
            if (deletedStatus) {
                if (manager) {
                    return '';
                }
                return '无通道管理权用户不可删除，如需删除请联系通道管理者删除';
            }
            // 其他状态

            if (creator === 'oneself') {
                return '';
            }

            return '只有专线通道创建者才能删除';
        },
        getIpv6DisabledText(row: any) {
            if (row.status !== 'established') {
                return '当前状态不允许编辑IPV6';
            }
            if (!row.manager) {
                return '当前专线通道分配给其他账户，不可进行编辑';
            }
        },
        regionText(value: string) {
            let regionList = this.data.get('regionList');
            let text = regionList.find((item: any) => item.value === value)?.text || '-';
            return text;
        },
        getTag(value: any) {
            if (!value || value.length < 1) {
                return '-';
            }
            let tagHtml = '';
            let tags = '';
            u.each(value, (item, index) => {
                let tagKey = u.escape(item.tagKey);
                let tagValue = u.escape(item.tagValue);
                tags += '{' + tagKey + ' : ' + tagValue + '} ';
                if (index < 2) {
                    tagHtml += tagKey + ' : ' + tagValue + '<br>';
                }
            });
            value.length > 2 && (tagHtml += '...');
            return '<div title="' + tags + '">' + tagHtml + '</div>';
        }
    };

    initData() {
        const allColumns = columns.slice();
        const allCustomColumnDb = allColumns.map(item => ({
            text: item.label,
            value: item.name,
            disabled: item.name === 'id' || item.name === 'opt'
        }));
        // 默认不显示IPV6互联IP
        let customColumnDb = allCustomColumnDb.filter(item => {
            if (['ipv6BgpStatus', 'localIpv6'].includes(item.value)) {
                return false;
            }
            return true;
        });
        return {
            FLAG,
            klass: 'channel-instance-list',
            allCustomColumnDb,
            dcInstance: {},
            table: {
                loading: false,
                datasource: [],
                allColumns,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                }
            },
            customColumn: {
                value: [
                    'id',
                    'name',
                    'status',
                    'diagnose',
                    'bgpStatus',
                    'authorizedUsers',
                    'manager',
                    'dcphyId',
                    'vlanId',
                    'ips',
                    'routeType',
                    'bgpKey',
                    'bgpAsn',
                    'fakeAsn',
                    'bgpRouteLimit',
                    'description',
                    'tag',
                    'opt'
                ],
                datasource: customColumnDb
            },
            searchbox: {
                placeholder: '请输入通道名称进行搜索',
                keyword: '',
                keywordType: ['name'],
                keywordTypes: [
                    {value: 'name', text: '通道名称'},
                    {value: 'id', text: '通道ID'},
                    {value: 'vlanId', text: 'VLAN ID'},
                    {value: 'localIp', text: '云端网络IP'},
                    {value: 'localIpv6', text: '云端网络IPv6'},
                    {value: 'remoteIp', text: 'IDC端IP'},
                    {value: 'remoteIpv6', text: 'IDC端IPv6'},
                    {value: 'description', text: '描述'}
                ]
            },
            instance: {},
            sortParams: {},
            outNumber: false,
            createDc: {
                disable: false,
                message: ''
            },
            dcList: [],
            dcphyId: '',
            region: '',
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            regionList: [],
            bgpLimitTip: '',
            bgpLimitClass: '',
            editTag: {
                disable: true,
                message: '请选择实例'
            },
            show: true,
            introduceTitle: '专线通道简介',
            description: '专线通道是在物理专线上划分出来的虚拟链路，用于连接用户百度智能云上虚拟网络和用户数据中心。',
            introduceIcon: INTRODUCE_ICON_COLOR,
            introduceEle: null,
            quotaApplyHref: `若用户端发送BGP路由超过默认100条时，请先到<a target="_blank" href="/quota_center/#/quota/apply/create?serviceType=ET&region=global&name=channelBgpRouteLimit">配额申请页</a>调整配额后再创建专线通道，并在专线通道列表页修改"BGP路由条目上限"生效。`,
            drawerVisible: true,
            visibleDraw: true,
            openDrawer: false,
            urlQuery: getQueryParams()
        };
    }

    inited() {
        this.checkAndSetFilters();
    }

    async attached() {
        this.data.set('introduceEle', this.ref('introduce'));
        if (window.$storage.get('showChannelIntroduce')) {
            this.handleToggle(false);
        }
        this.getIpv6White();
        await this.getDcList();
        await this.getRegionList();
        this.loadPage();
        this.loadQuota();
    }
    /** 检查链接里是否带参数，如果有则设置过滤条件 */
    checkAndSetFilters() {
        const urlQuery = this.data.get('urlQuery');
        if (!urlQuery?.id) return;
        this.data.set('searchbox.keyword', urlQuery.id);
        this.data.set('searchbox.keywordType', ['id']);
    }
    getDcList() {
        return this.$http.getDcList({pageNo: 1, pageSize: 10000}).then((res: any) => {
            let dcList = res.result.map(item => {
                let datalist = {
                    text: `${item.name}：${item.id}`,
                    value: item.id,
                    isCreateDisabled: item.status !== InstanceStatus.ACTIVE,
                    errorTip:
                        item.status !== InstanceStatus.ACTIVE
                            ? '该物理专线' + InstanceStatus.fromValue(item.status).errorStatusTip
                            : ''
                };
                return datalist;
            });
            this.data.set('dcphyList', u.cloneDeep(dcList));
            dcList.unshift({
                text: '全部物理专线',
                value: ''
            });
            this.data.set('dcList', dcList);
            this.data.set('dcphyId', dcList[0]?.value);
        });
    }
    getIpv6White() {
        const whiteList = window.$storage.get('commonWhite');
        this.data.set('ipv6Show', whiteList?.DedicatedConnIpv6);
        if (whiteList?.DedicatedConnIpv6) {
            this.data.set('customColumn.datasource', this.data.get('allCustomColumnDb'));
            let customValue = this.data.get('customColumn.value');
            this.data.set('customColumn.value', [...new Set([...customValue, 'localIpv6', 'ipv6BgpStatus'])]);
            this.setTableColumns();
        }
    }
    channelQuotaFree() {
        return this.$http
            .dcChannelQuota({
                dcphyId: this.data.get('dcphyId')
            })
            .then((data: any) => data.free > 0);
    }

    setTableColumns(customColumnNames: any) {
        customColumnNames = customColumnNames ? customColumnNames : this.data.get('customColumn.value');
        const allColumns = this.data.get('table.allColumns');
        let columns = [];
        allColumns.forEach((item: any) => customColumnNames.indexOf(item.name) > -1 && columns.push(item));
        let regionList = this.data.get('regionList');
        let regionCols = [{text: '全部', value: ''}, ...regionList];
        let region = columns.find(item => item.name === 'region');
        region && (region.filter = {options: regionCols});
        this.data.set('table.columns', columns);
    }

    initColumns(value: any) {
        this.setTableColumns(value);
    }

    onCustomColumns(value: any) {
        this.data.set('customColumn.value', value);
        this.setTableColumns(value);
    }

    loadPage() {
        this.data.set('table.loading', true);
        const payload = this.getPayload();
        return this.$http
            .channelList(payload)
            .then((res: any) => {
                this.data.set('table.datasource', res.result);
                this.data.set('pager.total', res.totalCount);
                this.data.set('table.loading', false);
                let hasNotVLAN0 = false;
                if (res.result.find(item => item.vlanId === 0) && this.data.get('dcphyId')) {
                    hasNotVLAN0 = true;
                }
                this.data.set('hasNotVLAN0', hasNotVLAN0);
                if (hasNotVLAN0) {
                    this.data.set('createDc', {
                        disable: true,
                        message: 'vlan0模式下，只能创建1条专线通道'
                    });
                } else {
                    this.data.set('createDc', {
                        disable: false,
                        message: ''
                    });
                }
            })
            .catch(() => {
                this.data.set('table.loading', false);
            });
    }

    getDcgwList() {
        return this.$http.dcgwList({pageNo: 1, pageSize: 10000}).then(({result}) => result);
    }

    onPagerChange({value}) {
        this.data.set('pager.page', value.page);
        this.data.set('pager.pageSize', value.pageSize);
        this.loadPage();
    }

    onCreate() {
        const dcphyId = this.data.get('dcphyId');
        location.hash = `#/dc/channel/create?dcphyId=${dcphyId}`;
    }

    editUsers(row: any) {
        const confirm = new EditUsers({
            data: {
                title: '添加新分配对象',
                dcphyId: this.data.get('dcphyId'),
                channelId: row.id,
                row: row
            }
        });
        confirm.on('confirmed', () => {
            this.loadPage();
        });
        confirm.attach(document.body);
    }

    isShowRecreate(row: Record<string, any>) {
        const creator = this.data.get('creator');
        return (
            u.contains([InstanceStatus.APPLYING, InstanceStatus.REJECTED, 'vlan-conflict'], row.status) &&
            creator === 'oneself'
        );
    }

    isShowAcceptReject(row: Record<string, any>) {
        const creator = this.data.get('creator');
        return creator === 'other' && row.status === 'pending';
    }

    onMonitor(row: Record<string, any>) {
        const dialog = new Monitor({
            data: {
                id: row.id || '',
                scope: 'BCE_DEDICATEDCONN_CHANNEL',
                currentRegion: row.region
            }
        });
        dialog.attach(document.body);
    }

    onAlarm(row) {
        redirect(
            `/bcm/#/bcm/alarm/rule/list~scope=BCE_DEDICATEDCONN_CHANNEL&dimensions=InstanceId:${row.id}&region=${row.region}`
        );
    }

    // 删除校验，需要调用详情接口判断有没有主通道和子通道
    deleteCheck(channelId: string) {
        return new Promise((resolve, reject) => {
            if (this.data.get('creator') === 'other') {
                this.$http
                    .channelDetail(channelId, {
                        type: 'available'
                    })
                    .then((data: any) => {
                        if (data.subChannel?.length || data.mainChannel) {
                            reject();
                        } else {
                            resolve();
                        }
                    });
            } else {
                this.$http.channelDetail(channelId).then((data: any) => {
                    if (data.subChannel?.length || data.mainChannel) {
                        reject();
                    } else {
                        resolve();
                    }
                });
            }
        });
    }

    onDelete(row: any) {
        const confirm = new Confirm({
            data: {
                title: '删除提示',
                content: '请确认是否要操作？'
            }
        });
        confirm.on('confirm', () => {
            const options = {
                headers: {
                    region: row.region || window.$context.getCurrentRegionId()
                }
            };
            this.deleteCheck(row.id)
                .then(() => {
                    this.$http
                        .channelDetele(
                            {
                                dcphyId: row.dcphyId,
                                channelId: row.id
                            },
                            options
                        )
                        .then(() => {
                            this.refresh();
                            Notification.success('删除成功');
                        });
                })
                .catch(() => {
                    Notification.warning('请先解除专线通道关联，再删除专线通道');
                });
        });
        confirm.attach(document.body);
    }

    onReCreate(row) {
        const {dcphyId, id, status} = row;
        const creator = this.data.get('creator');
        const params = {
            dcphyId,
            creator,
            status,
            channelInstance: id
        };
        sessionStorage.setItem('EDIT_CHANNEL_PARAMS', JSON.stringify(params));
        location.hash = `#/dc/channel/create`;
    }

    onResetKey(row: any) {
        const confirm = new ResetKey({
            data: {
                info: row
            }
        });
        confirm.on('bgpKeyReset', () => {
            this.loadPage();
        });
        confirm.attach(document.body);
    }

    beforeEdit(row: any) {
        this.data.set('instance.channelId', row.id);
        this.data.set('instance.name', row.name);
        this.data.set('instance.desc', row.description);
        this.data.set('instance.bgpRouteLimit', row.bgpRouteLimit);
        this.data.set('bgpLimitClass', '');
        this.data.set('bgpLimitTip', '输入路由条目上限值不大于' + this.data.get('quota'));
    }

    onEditInput(e: Event, row: any, rowIndex: number, type: string) {
        if (type === 'bgpRouteLimit') {
            this.bgpRouteChange(e, row, rowIndex, type);
        } else {
            let result =
                type === 'name'
                    ? e.value === '' || !/^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/.test(e.value)
                    : e.value.length > 200;
            this.data.set(`instance.${type}`, e.value);
            this.ref(`${type}Btn${rowIndex}`).data.set('disabled', result);
        }
    }

    onEdit(row: any, type: string) {
        const instance = this.data.get('instance');
        const TYPE_MPA = {
            name: 'name',
            desc: 'description',
            bgpRouteLimit: 'bgpRouteLimit'
        };
        const payload = {
            dcphyId: row.dcphyId,
            channelId: instance.channelId,
            [TYPE_MPA[type]]: instance[type]
        };
        return this.$http.channelUpdate(payload).then(() => this.loadPage());
    }

    editCancel(row: any, rowIndex: number, type: string) {
        this.ref(`${type}Btn${rowIndex}`).data.set('disabled', true);
        this.ref(`${type}Edit${rowIndex}`).data.set('visible', false);
    }

    refresh() {
        this.data.set('searchbox.keyword', '');
        this.data.set('searchbox.keywordType', ['name']);
        this.loadPage();
    }

    getPayload() {
        const {order, orderBy} = this.data.get('sortParams');
        const searchParam = this.ref('search') ? this.ref('search').getSearchCriteria() : {};
        const {filter} = this.data.get('');
        const params = {
            dcphyId: this.data.get('dcphyId'),
            keywordType: searchParam.keywordType,
            keyword: searchParam.keyword,
            region: this.data.get('region'),
            pageNo: this.data.get('pager.page'),
            pageSize: this.data.get('pager.pageSize'),
            creator: this.data.get('creator')
        };
        if (order && orderBy) {
            params.order = order;
            params.orderBy = orderBy;
        }
        if (searchParam.keywordType === 'tag') {
            params.subKeywordType = searchParam.subKeywordType;
        }
        return {...params, ...filter};
    }

    // 搜索事件
    onSearch(e: Event) {
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    onSort({value: {order, orderBy}}) {
        this.data.set('sortParams', {order, orderBy});
        this.loadPage();
    }
    // 编辑IPV6参数
    onEditIPV6(row: any) {
        const confirm = new EditDialog({
            data: {
                title: '编辑IPV6参数',
                isShow: true,
                row: row,
                type: 'editIpv6'
            }
        });
        confirm.on('created', () => {
            this.loadPage();
        });
        confirm.attach(document.body);
    }
    dcOnChange({value}) {
        this.data.set('pager.page', 1);
        this.data.set('dcphyId', value);
        this.loadPage();
    }
    getRegionList() {
        let region = 'all';
        return this.$http.getApAddrList(region).then(res => {
            const enableEtRegion = getEtAvaliableRegion(window.$context, res);
            this.data.set('regionList', enableEtRegion.regionList);
        });
    }

    onFilter(e: Event) {
        let name = e.field.name;
        let value = e[name] ? e[name] : '';
        this.data.set('region', e.region || '');
        this.data.set('filter.' + name, value);
        this.loadPage();
    }
    loadQuota() {
        this.$http.commonQuota({serviceType: 'ET', quotaType: 'channelBgpRouteLimit'}).then(res => {
            this.data.set('quota', res);
        });
    }
    bgpRouteChange(e: Event, row: any, rowIndex: number, type: string) {
        if (!/^[1-9]+[0-9]*$/.test(e.value)) {
            this.ref(`${type}Btn${rowIndex}`).data.set('disabled', true);
        } else if (this.data.get('quota') < e.value) {
            this.data.set('bgpLimitTip', '输入路由条目上限值不大于' + this.data.get('quota'));
            this.data.set('bgpLimitClass', 'errorClass');
            this.ref(`${type}Btn${rowIndex}`).data.set('disabled', true);
        } else if (e.value <= row.bgpRouteLimit) {
            this.data.set('bgpLimitTip', '输入配额需大于当前BGP路由条目上限。');
            this.data.set('bgpLimitClass', 'errorClass');
            this.ref(`${type}Btn${rowIndex}`).data.set('disabled', true);
        } else {
            this.data.set('bgpLimitTip', '');
            this.data.set(`instance.${type}`, e.value);
            this.ref(`${type}Btn${rowIndex}`).data.set('disabled', false);
        }
    }
    changeOpt(e: Event) {
        let {type, payload} = e;
        const methodMap = {
            FAKEASN: this.onEditFakeASN,
            DIAGNOSE: this.diagnoseNat
        };
        let requester = methodMap[type].bind(this);
        requester(payload);
    }
    diagnoseNat(row: any) {
        const {id} = row;
        location.hash = `#/vpc/instance/diagnosis?channelId=${id}`;
    }
    onEditFakeASN(item: any) {
        const confirm = new EditDialog({
            data: {
                title: '编辑Fake ASN',
                isShow: true,
                row: item,
                fakeAsOn: item.fakeAsn.length > 0,
                type: 'editFakeASN'
            }
        });
        confirm.on('created', () => {
            this.loadPage();
        });
        confirm.attach(document.body);
    }
    // 跨账号支付接受、拒绝
    handleCrossAction(row: Record<string, any>, action: 'accept' | 'reject') {
        if (action === 'accept') {
            const passedParams = {isAccept: true, row};
            sessionStorage.setItem('ACCEPT_PARAMS', JSON.stringify(passedParams));
            location.hash = '#/dc/channel/create';
        } else {
            const data = {
                title: '提示',
                content: '确定拒绝吗？'
            };
            const confirmFn = async () => {
                try {
                    const res = await this.$http.userRejectChannel({
                        dcphyId: row.dcphyId,
                        channelId: row.id
                    });
                    if (res) {
                        Notification.success('已拒绝');
                        this.loadPage();
                    }
                } catch (err) {}
            };
            confirmValidate(data, confirmFn);
        }
    }
    tableSelected(e: Event) {
        this.data.set('table.selectedItems', e.value.selectedItems);
        let {EDITTAG} = checker.check(rules, e.value.selectedItems, '');
        this.data.set('editTag', EDITTAG);
    }
    /**
     * @param {boolean} flag
     */
    handleToggle(flag) {
        window.$storage.set('showChannelIntroduce', true);
        this.data.set('show', flag);
        this.data.set('introduceIcon', INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#151B26';
    }
    handleShowCard() {
        window.$storage.set('showChannelIntroduce', false);
        if (!this.data.get('show')) {
            this.data.set('show', true);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type) {
        this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#2468F2';
    }
    handleMouseLeave(type) {
        const isShow = this.data.get('show');
        this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        !isShow && (ele.style.color = '#151B26');
    }
    handleDownload() {
        const payload = this.getPayload();
        const queryStr = ObjectToQuery(payload);
        window.open(`/api/network/v1/dc/channel/list/download?${queryStr}`);
    }
    diagnoseChannel(e) {
        this.data.set('openDrawer', true);
        const confirm = new DiagnoseConfirm({
            data: {
                title: '发起诊断',
                instanceId: e.id,
                instanceType: 'etChannel',
                region: e.region
            }
        });
        confirm.on('confirm', () => {
            this.loadChannelDiagnose(e);
        });
        confirm.on('close', () => {
            this.nextTick(() => {
                this.data.set('openDrawer', false);
                this.data.set('drawerVisible', false);
                this.data.set('visibleDraw', false);
            });
        });
        confirm.attach(document.body);
    }
    loadChannelDiagnose(e) {
        this.data.set('openDrawer', true);
        this.nextTick(() => {
            this.data.set('drawerVisible', true);
            this.data.set('visibleDraw', true);
            if (document.getElementById('satisfactionNew')) {
                this.loadSatisfaction(e);
            } else {
                this.loadChannelDiagnose(e);
            }
        });
    }
    loadSatisfaction(row) {
        this.nextTick(() => {
            processor.applyComponent(
                'DiagnoseDrawer',
                {
                    diagnoseData: {
                        instanceType: 'etChannel',
                        dcphyId: row.dcphyId,
                        channelId: row.id,
                        region: row.region,
                        instanceId: row.id,
                        oneself: this.data.get('creator')
                    },
                    http: request,
                    visible: this.data.get('visibleDraw'),
                    instanceType: 'etChannel',
                    extraData: '{}',
                    onCloseDrawer: () => {
                        this.data.set('openDrawer', false);
                        this.data.set('drawerVisible', false);
                        this.data.set('visibleDraw', false);
                    }
                },
                '#satisfactionNew'
            );
        });
    }
    pathAnalysisEip(e) {
        this.data.set('openDrawer', true);
        this.nextTick(() => {
            this.data.set('drawerVisible', true);
            this.data.set('visibleDraw', true);
            if (document.getElementById('satisfactionNew')) {
                this.loadSatisfactionPath(e);
            } else {
                this.pathAnalysisEip(e);
            }
        });
    }
    loadSatisfactionPath() {
        this.nextTick(() => {
            processorPath.applyComponent(
                'AnalysisGraphDrawer',
                {
                    visible: this.data.get('visibleDraw'),
                    scoreTipTitle: '专线通道',
                    onCloseDrawer: () => {
                        this.data.set('openDrawer', false);
                        this.data.set('drawerVisible', false);
                        this.data.set('visibleDraw', false);
                    },
                    http: request
                },
                '#satisfactionNew'
            );
        });
    }
}

export default RuntimeProcessor.autowireUnCheckCmpt(ChannelList);
