.channel-group-header {
    height: 100%;
    display: flex;
    flex-flow: column;
    background: #fff;
    flex: auto;
    overflow-y: auto;
    .app-tab-page {
        display: block;
        overflow: unset;
        padding: 0;
    }
}

.channel-instance-list {
    min-height: 100%;
    background: #f7f7f9 !important;
    .s-biz-page-header {
        margin: 0px !important;
        background-color: #f7f7f7 !important;
        height: auto !important;
        border: none !important;
    }
    .s-biz-page-content {
        background-color: #fff;
        border-radius: 6px !important;
        margin: 16px !important;
        padding: 24px !important;
        .s-biz-page-footer {
            padding-bottom: 0;
            margin-top: 16px;
        }
    }
    .s-biz-page-toolbar {
        margin: 0 0 16px !important;
    }
    .dc-buttons-wrap {
        display: inline-flex;
        .search-res {
            margin-right: 0;
        }
    }
    .s-table {
        display: table;
        table-layout: fixed;
        width: 100%;
        .s-table-cell-description {
            .desc-cell {
                word-break: break-all;
            }
        }
    }
    .channel-instance-header {
        line-height: 50px;
        .title {
            display: inline-block;
            margin: 0;
            color: #333;
            font-size: 14px;
            font-weight: 400;
            margin-right: 10px;
        }
        .s-select {
            margin-left: 16px;
        }
    }
    .operations {
        .s-button {
            padding: 0;
            margin-right: 12px;
        }
    }
    .icon-copy,
    .icon-edit {
        font-size: 12px;
        color: #108cee;
    }
    .icon-edit {
        cursor: pointer;
    }
    .gray {
        color: #ccc !important;
    }
    .icon-edit,
    .icon-copy {
        font-size: 12px;
        color: #2468f2;
    }
    .s-table-body {
        max-height: calc(~'100vh-414px');
        overflow: auto;
    }
    .s-table .s-table-row:hover .name-icon {
        display: inline;
    }
    .name-icon {
        position: relative;
        top: -1px;
        fill: #2468f2;
        color: #2468f2;
        display: none;
    }
    .selectTip {
        margin-left: 5px;
        color: #999;
    }
    .edit-popover-class {
        .s-input {
            display: block;
        }
    }
    .more-opt {
        color: #2468f2 !important;
        border: none !important;
        min-width: 80px !important;
        background: transparent;
        padding-left: 0 !important;
        .bui-select-text {
            color: #2468f2;
        }
        .s-input-suffix-container {
            border: none !important;
            margin-left: 2px !important;
        }
        .s-trigger-container {
            .s-popup {
                .s-popup-content-box {
                    .s-select-option-list {
                        padding-bottom: 0px !important;
                    }
                    .s-selectdropdown {
                        width: 150px !important;
                        margin-left: 12px !important;
                        .s-option {
                            height: 25px !important;
                            line-height: 23px !important;
                        }
                    }
                }
            }
        }
        &:after {
            color: #2468f2 !important;
        }

        &:hover,
        &:active {
            background: transparent;
        }
        &::after {
            content: none !important;
        }
        .search-et {
            .search-res {
                margin: 0px;
                display: inline-flex;
                .s-cascader {
                    margin: 0 8px 0 0;
                }
                .s-cascader-value {
                    width: 130px;
                }
                .s-cascader-value-arrow {
                    top: 30%;
                }
            }
        }
    }
    .new-tag {
        display: inline-block;
        background-color: #f72e32;
        border-radius: 16px;
        line-height: 16px;
        min-width: 40px;
        text-align: center;
        font-size: 12px;
        color: #fff;
    }
    .new-instance-tag {
        background-color: #f33e3e;
        border-radius: 2px;
        line-height: 17px;
    }
}

.dc-channel-editroute {
    .route-tip {
        color: #999;
        margin: 0 0 5px 0;
    }
    .error_text {
        color: #f33e3e;
        padding: 3px 0;
    }
    .networks-wrap {
        .subChannel-add {
            margin-bottom: 10px;
        }
        .s-row {
            width: 678px;
        }
    }
    .other-networks-wrap {
        .subChannel-add {
            margin-bottom: 10px;
        }
    }
    .channel-id-input {
        margin-bottom: 4px;
    }
    .tip-text {
        margin-bottom: 4px;
        color: #f33e3e;
    }
    .delete-check {
        .s-radio-text {
            font-size: 12px;
        }
    }
}

.app-channel-tab {
    position: relative;
    .s-tabs {
        padding-top: 40px;
    }
}

.title_channel {
    position: absolute;
    z-index: 999999;
    top: 0px;
    color: #151b26;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    padding-top: 12px;
    width: 100%;
    padding-bottom: 4px;
    background-color: #fff;
    text-indent: 16px;
    display: flex;
    justify-content: space-between;
    .help-file {
        font-size: 12px;
        font-weight: 400;
        font-family: PingFangSC-Regular;
        color: #151b26;
        line-height: 20px;
        position: absolute;
        right: 16px;
        &:hover {
            color: #2468f2;
        }
        .s-icon {
            position: relative;
            top: -1px;
            right: 4px;
            margin-right: 0;
            color: #2468f2;
            font-size: 14px;
        }
    }
    .function-introduce {
        color: #2468f2;
    }
}

.edit-popover-desc {
    .s-input {
        display: block;
    }
}
.tip-icon {
    font-size: 12px;
    // border: 1px solid #9e9898;
    color: #9e9898;
    &:hover {
        border-color: #108cee;
        color: #108cee;
    }
}
.inline-tip {
    top: 3px;
    position: relative;
    .s-tip {
        justify-content: center;
        .warning_class {
            fill: #999;
        }
    }
    .s-tip:hover .s-icon path {
        fill: #2468f2 !important;
    }
}
.errorClass {
    color: #d0021b;
}

.channel-edit-wrap {
    .form-content {
        line-height: 30px;
    }
    .s-form-item-label {
        width: 155px;
        text-align: left;
    }
    .s-alert-skin-warning {
        height: 95px;
        align-items: flex-start;
        margin-top: 10px;
    }
    .channel_localIpv6 {
        .s-form-item-label {
            label:before {
                content: '*';
                color: #f33e3e;
                position: relative;
                margin-right: -4px;
                right: 6px;
            }
        }
        .s-form-item-help,
        .s-form-item-error {
            width: 350px;
        }
    }
    .channel-edit-wrap-custom .s-dialog-content {
        min-width: 630px !important;
    }
    .fakeAs-tip {
        color: #84868c;
        margin-top: 4px;
    }
}

.drawer-open-class {
    display: none !important;
}
