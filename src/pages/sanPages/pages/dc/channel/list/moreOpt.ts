import _ from 'lodash';
import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import {Select} from '@baiducloud/bce-ui/san';
import {checker} from '@baiducloud/bce-opt-checker';

import {ChannelOptType} from '@/pages/sanPages/common/enum';
import rules from '../../instance/rules';

const {asComponent, template} = decorators;

const tpl = html` <div>
    <ui-select class="more-opt" datasource="{{datasource}}" value="{=value=}" tip-position="rt" default-label="更多">
    </ui-select>
</div>`;

@template(tpl)
@asComponent('@channel-list-operation')
export default class DcListOperation extends Component {
    static components = {
        'ui-select': Select
    };

    static computed = {
        datasource() {
            let operation = ChannelOptType.toArray('FAKEASN');
            let item = this.data.get('item');
            _.each(operation, (command, i) => {
                let result = checker.check(rules, item, command.value)[command.value];
                operation[i].disabled = result.disable ? result.disable : '';
                operation[i].tip = result.message ? result.message : '';
            });
            return operation;
        }
    };

    attached() {
        this.watch('value', value => {
            if (value) {
                this.fire('command', {
                    type: value,
                    payload: this.data.get('item'),
                    rowIndex: this.data.get('rowIndex')
                });
            }
            this.data.set('value', '');
        });
    }
}
