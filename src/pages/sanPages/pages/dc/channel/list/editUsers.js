import {defineComponent} from 'san';
import u from 'lodash';
import {html} from '@baiducloud/runtime';
import {Input, Notification, Dialog, Form, Radio, Button} from '@baidu/sui'; // eslint-disable-line

import {AuthorizedUsersType, RouteParamsType} from '@/pages/sanPages/common/enum';

const tpl = html`
<div>
    <s-dialog
        title="{{title}}"
        open="{{true}}"
    >
        <s-form s-ref="form" data="{=formData=}" rules="{{rules}}" class="dc-channel-create">
            <s-form-form-item prop="onlySelfAuthorized" label="{{'分配对象：'}}">
                <s-radio-radio-group value="{=formData.onlySelfAuthorized=}">
                    <s-radio s-for="item in authorizedUsersRadio"
                        label="{{item.text}}"
                        value="{{item.value}}"
                    ></s-radio>
                </s-radio-radio-group>
            </s-form-form-item>
            <s-form-form-item s-if="formData.onlySelfAuthorized === 'others'"
                prop="authorizedUsers" label="{{'填写账户ID：'}}">
                <s-input
                    value="{=formData.authorizedUsers=}"
                    on-blur="checkAccount"
                />
                <span class="s-form-item-error">{{checkAccountErr}}</span>
            </s-form-form-item>
        </s-form>
        <div slot="footer">
            <s-button on-click="onConfirm" disabled="{{disabled}}" skin="primary">{{'确定'}}</s-button>
            <s-button on-click="closeDialog">{{'取消'}}</s-button>
        </div>
    </s-dialog>
</div>
`;
export default defineComponent({
    template: tpl,

    components: {
        's-input': Input,
        's-dialog': Dialog,
        's-form': Form,
        's-form-form-item': Form.Item,
        's-radio-radio-group': Radio.RadioGroup,
        's-radio': Radio,
        's-button': Button
    },

    initData() {
        return {
            klass: 'dc-channel-wrap',
            open: true,
            disabled: false,
            formData: {
                onlySelfAuthorized: 'self'
            },
            rules: {
                authorizedUsers: [
                    {required: true, message: '账户ID必填'}
                ]
            },
            authorizedUsersRadio: AuthorizedUsersType.toArray()
        };
    },

    attached() {
        this.setInitData();
    },

    setInitData() {
        const row = this.data.get('row');
        this.data.set('formData.onlySelfAuthorized', row.onlySelfAuthorized ? 'self' : 'others');
    },

    checkAccount() {
        const accountIds = this.data.get('formData.authorizedUsers');
        if (!accountIds) {
            return;
        }
        this.$http.checkAccount({
            accountIds: [accountIds]
        })
        .then((result) => {
            this.data.set('checkAccountErr', result.value ? '' : '账户ID不合法');
        });
    },

    async onConfirm() {
        const form = this.ref('form');
        await form.validateFields();
        const formData = u.cloneDeep(this.data.get('formData'));
        const payload = {
            authorizedUsers: [],
            dcphyId: this.data.get('dcphyId'),
            channelId: this.data.get('channelId')
        };
        if (formData.onlySelfAuthorized === 'others') {
            if (this.data.get('checkAccountErr') !== '') {
                return;
            }
            payload.authorizedUsers.push(formData.authorizedUsers);
        }
        this.data.set('disabled', true);
        this.$http.updateChannelUsers(payload).then(() => {
            Notification.success('修改成功');
            this.fire('confirmed');
            this.closeDialog();
        }).catch(() => {
            this.data.set('disabled', false);
        });
    },
    closeDialog() {
        this.dispose();
    }
});
