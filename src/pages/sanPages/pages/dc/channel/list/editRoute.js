import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';

import {checkIsInSubnet} from '@/pages/sanPages/utils/common';
import rule from '@/pages/sanPages/utils/rule';

const {invokeSUI, invokeSUIBIZ, asComponent, invokeAppComp, template} = decorators;
const tpl = html`
<template>
    <s-dialog
        class="dc-channel-editroute"
        title="{{title}}"
        open="{{true}}"
    >
        <s-form>
            <s-form-item prop="networks" label="{{'路由参数：'}}" class="networks-wrap" s-if="{{type === 'ipv4'}}">
                <div s-for="item,index in networks">
                    <s-input
                        class="{{networksInfo[index] ? 'input-error' : ''}}"
                        value="{=networks[index]=}"
                        placeholder="{{'示例：***********/16'}}"
                        on-input="networksInput($event, index)"
                    />
                    <s-icon s-if="index > 0" class="iconfont icon-close" on-click="deleteItem(index)" />
                    <span class="s-form-item-error">{{networksInfo[index]}}</span>
                </div>
                <div class="route-tip">*************/24</div>
                <s-button skin="stringfy" on-click="addItem">
                    {{'+添加更多路由参数'}}
                </s-button>
            </s-form-item>
            <s-form-item prop="ipv6Networks" label="{{'IPV6路由参数：'}}" class="networks-wrap" s-if="{{type === 'ipv6'}}">
                <div s-for="item,index in ipv6Networks">
                    <s-input
                        class="{{ipv6NetworksInfo[index] ? 'input-error' : ''}}"
                        value="{=ipv6Networks[index]=}"
                        on-input="ipv6NetworksInput($event, index)"
                    />
                    <s-icon s-if="index > 0" class="iconfont icon-close" on-click="deleteIpv6Item(index)" />
                    <span class="s-form-item-error">{{ipv6NetworksInfo[index]}}</span>
                </div>
                <s-button skin="stringfy" on-click="addIpv6Item">
                    {{'+添加更多路由参数'}}
                </s-button>
            </s-form-item>
        </s-form>
        <div slot="footer">
            <s-button on-click="submit" disabled="{{disabled}}" skin="primary">{{'确定'}}</s-button>
            <s-button on-click="closeDialog">{{'取消'}}</s-button>
        </div>
    </s-dialog>
</div>
`;

@template(tpl)
@asComponent('@et-route-edit')
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class EtChannelCreate extends Component {
    initData() {
        return {
            networks: [''],
            ipv6Networks: [''],
            ipv6NetworksInfo: [],
            errorMessage: '',
            errorIndex: null
        };
    }

    addItem(e) {
        this.data.push('networks', '');
    }

    deleteItem(index) {
        this.data.removeAt('networks', index);
        this.data.splice('networksInfo', [index, 1]);
    }

    submit(e) {
        let networks = this.data.get('networks');
        let ipv6Networks = this.data.get('ipv6Networks');
        let message = '';
        let index = null;

        this.errorHandler();

        for (let i = 0; i < networks.length; i++) {
            let item = networks[i];
            if (!item) {
                message = rule.CHANNEL.NETWORK.requiredErrorMessage;
                index = i;
                break;
            } else if (!rule.CHANNEL.NETWORK.pattern.test(item)) {
                message = rule.CHANNEL.NETWORK.patternErrorMessage;
                index = i;
                break;
            } else if (!rule.CHANNEL.NETWORK.custom(item)) {
                message = rule.CHANNEL.NETWORK.customErrorMessage;
                index = i;
                break;
            }

            for (let j = i + 1; j < networks.length; j++) {
                if (checkIsInSubnet(item, networks[j]) || checkIsInSubnet(networks[j], item)) {
                    message = '路由参数不能重叠';
                    index = j;
                    break;
                }
            }

            if (message) {
                break;
            }
        }

        if (message) {
            this.data.set(`networksInfo[${index}]`, message);
            return;
        }
        if (this.data.get('type') === 'ipv6') {
            ipv6Networks.map((value, index) => {
                this.ipv6NetworksInput({value}, index);
            });
            if (this.data.get('ipv6NetworksInfo').join('') !== '') {
                return;
            }
        }
        this.data.set('disabled', true);
        let payload = {
            dcphyId: this.data.get('dcphyId'),
            channelId: this.data.get('channelId'),
            routeType: this.data.get('routeType')
        };
        if (this.data.get('type') === 'ipv4') {
            payload.networks = networks;
        } else {
            payload.ipv6Networks = ipv6Networks;
        }
        return this.$http
            .editRoutes(payload)
            .then(() => {
                this.closeDialog();
                this.fire('created');
            })
            .catch(() => {
                this.closeDialog();
                this.fire('created');
            });
    }

    errorHandler() {
        this.data.set('networksInfo', []);
    }
    addIpv6Item(e) {
        this.data.push('ipv6Networks', '');
    }

    deleteIpv6Item(index) {
        this.data.removeAt('ipv6Networks', index);
        this.data.splice('ipv6NetworksInfo', [index, 1]);
    }
    ipv6NetworksInput({value}, index) {
        if (!value) {
            this.data.set(`ipv6NetworksInfo[${index}]`, '请输入IPV6参数');
        } else if (!rule.IPV6_SEG.test(value)) {
            this.data.set(`ipv6NetworksInfo[${index}]`, '参数格式不合法');
        } else {
            this.data.set(`ipv6NetworksInfo[${index}]`, '');
        }
    }
    closeDialog() {
        this.dispose();
    }
}
export default Processor.autowireUnCheckCmpt(EtChannelCreate);
