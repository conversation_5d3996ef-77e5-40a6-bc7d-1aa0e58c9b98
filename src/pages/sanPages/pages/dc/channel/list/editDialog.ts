import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';

import {validateRules} from '../create/helper';
import {AuthorizedUsersType} from '@/pages/sanPages/common/enum';
import './style.less';

const {invokeSUI, invokeSUIBIZ, asComponent, invokeAppComp, template} = decorators;
const tpl = html`
<template>
    <s-dialog
        class="channel-edit-wrap"
        title="{{title}}"
        open="{{isShow}}"
    >
        <s-form s-ref="form" data="{=formData=}" rules="{{rules}}">
            <s-form-item label="{{'物理专线ID：'}}">
                <div class="form-content">{{row.dcphyId}}</div>
            </s-form-item>
            <s-form-item label="{{'通道名称：'}}">
                <div class="form-content">{{row.name}}</div>
            </s-form-item>
            <s-form-item label="{{'分配对象：'}}">
                <div class="form-content">{{row.onlySelfAuthorized | getOnlySelfAuthorizedConent}}</div>
            </s-form-item>
            <s-form-item label="{{'账户ID：'}}" s-if="{{!row.onlySelfAuthorized}}">
                <div class="form-content">{{row.authorizedUsers[0]}}</div>
            </s-form-item>
            <div s-if="{{type === 'editIpv6'}}">
                <s-form-item label="{{'IPv6功能:'}}" class="channel_ipv6">
                <s-tooltip content="专线通道只有在未绑定、未关联的状态下可以关闭 IPv6 开关" trigger="{{formData.enableIpv6 && disabledIPv6?'hover':''}}">
                    <s-switch disabled="{{formData.enableIpv6 && disabledIPv6}}" checked="{=formData.enableIpv6=}"/>
                </s-tooltip>
                </s-form-item>
                <s-form-item prop="localIpv6" label="{{'云端网络侧IPv6互联地址:'}}" s-if="{{formData.enableIpv6}}"
                    class="channel_localIpv6">
                    <div class="form-content" s-if="{{row.enableIpv6}}">{{formData.localIpv6}}</div>
                    <s-input value="{=formData.localIpv6=}" s-else width="220"/>
                </s-form-item>
                <s-form-item prop="remoteIpv6" label="{{'IDC侧IPv6互联地址:'}}" s-if="{{formData.enableIpv6}}"
                    class="channel_localIpv6">
                    <div class="form-content" s-if="{{row.enableIpv6}}">{{formData.remoteIpv6}}</div>
                    <s-input value="{=formData.remoteIpv6=}" s-else width="220"/>
                </s-form-item>
                <s-alert s-if="{{formData.enableIpv6}}" skin="warning">
                    <p>IPv6互联地址的2个IP必须在同一个网段内且掩码输入范围为88-127</p>
                    <p>示例：</p>
                    <p>云端网络侧IPV6互联地址:2400:DA00:E003:0000:016A:0400:0000:100/127，</p>
                    <p>IDC侧IPV6互联地址:2400:DA00:E003:0000:016A:0400:0000:101/127</p>
                </s-alert>
            </div>
            <div s-if="{{type === 'editFakeASN'}}">
                <s-form-item label="{{'路由协议：'}}">
                    <div class="form-content">{{'动态路由'}}</div>
                </s-form-item>
                <s-form-item label="BGP ASN：">
                    <div class="form-content">{{formData.bgpAsn}}</div>
                </s-form-item>
                <s-form-item prop="fakeAs" label="{{'Fake ASN功能：'}}">
                    <s-switch checked="{=fakeAsOn=}"></s-switch>
                    <div
                        s-if="{{row.fakeAsn.length > 0 && !fakeAsOn}}"
                        class="fakeAs-tip">该操作可能会造成当前业务断流，请确认。</div>
                </s-form-item>
                <s-form-item
                    prop="fakeAsn"
                    label="Fake ASN："
                    s-if="{{fakeAsOn}}"
                    help="有效范围：1 - 4294967295，不允许输入45085，且不能与BGP ASN相同。">
                    <s-input
                        value="{=formData.fakeAsn=}"
                        placeholder="{{'请输入Fake ASN'}}"/>
                </s-form-item>
            </div>
        </s-form>
        <div slot="footer">
            <s-button on-click="submit" disabled="{{disabledSubmit}}" skin="primary">{{'确定'}}</s-button>
            <s-button on-click="closeDialog">{{'取消'}}</s-button>
        </div>
    </s-dialog>
</div>
`;

@template(tpl)
@asComponent('@channel-ipv6Asn-edit')
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
export default class EtChannelEdit extends Component {
    static filters = {
        getOnlySelfAuthorizedConent(value: string) {
            return AuthorizedUsersType.getTextFromValue(value ? 'self' : 'others');
        }
    };
    static computed = {
        disabledSubmit() {
            let row = this.data.get('row');
            let formData = this.data.get('formData');
            let type = this.data.get('type');
            let fakeAsOn = this.data.get('fakeAsOn');
            if (type === 'editIpv6') {
                return (row.enableIpv6 && formData.enableIpv6) || (!row.enableIpv6 && !formData.enableIpv6);
            }
            return (
                (row.fakeAsn?.length > 0 && fakeAsOn && row.fakeAsn === formData.fakeAsn) ||
                (row.fakeAsn?.length <= 0 && !fakeAsOn)
            );
        }
    };
    initData() {
        return {
            isShow: true,
            rules: validateRules(this),
            formData: {},
            disabledSubmit: false,
            fakeAsOn: false,
            disabledIPv6: true
        };
    }
    inited() {
        this.data.set('formData', this.data.get('row'));
        this.getChannerDetail();
    }

    async submit() {
        let type = this.data.get('type');
        const form = this.ref('form');
        await form.validateFields();
        this.data.set('disabledSubmit', true);
        let formData = this.data.get('formData');
        const row = this.data.get('row');
        if (type === 'editIpv6') {
            if (formData.enableIpv6) {
                let data = {
                    dcphyId: row.dcphyId,
                    channelId: row.id,
                    localIpv6: formData.localIpv6,
                    remoteIpv6: formData.remoteIpv6
                };
                return this.$http
                    .channelAddIpv6(data)
                    .then(() => {
                        this.confirm();
                    })
                    .catch(() => this.data.set('disabledSubmit', false));
            }
            let data = {
                dcphyId: row.dcphyId,
                channelId: row.id
            };
            return this.$http
                .channelDeleteIpv6(data)
                .then(() => {
                    this.confirm();
                })
                .catch(() => this.data.set('disabledSubmit', false));
        }
        if (type === 'editFakeASN') {
            let data = {
                dcphyId: row.dcphyId,
                channelId: row.id,
                fakeAsn: this.data.get('fakeAsOn') ? formData.fakeAsn : ''
            };
            return this.$http
                .channelUpdate(data)
                .then(() => {
                    this.confirm();
                })
                .catch(() => this.data.set('disabledSubmit', false));
        }
    }
    confirm() {
        this.fire('created');
        this.closeDialog();
        this.data.set('disabledSubmit', false);
    }
    closeDialog() {
        this.data.set('isShow', false);
        this.dispose();
    }
    getChannerDetail() {
        let isOther = this.data.get('creator') === 'other';
        this.$http
            .channelDetail(
                this.data.get('row.id'),
                isOther && {
                    type: 'available'
                }
            )
            .then(res => {
                const {dcgwId, mainChannel, subChannel} = res;
                this.data.set('disabledIPv6', !!(dcgwId || mainChannel || subChannel?.length));
            })
            .catch(() => {
                this.data.set('disabledIPv6', false);
            });
    }
}
