import u from 'lodash';

export const columns = [
    {
        name: 'name', label: '安全组名称'
    },
    {name: 'resourceGroups', label: '项目',
        render(item) {
            let str = [];
            item.resourceGroups && item.resourceGroups.forEach(resourceGroup => {
                str.push(u.escape(resourceGroup.organizationName) + '：' + u.escape(resourceGroup.resourceGroupName)); //eslint-disable-line
            });
            return str.join('<br>');
        }
    },
    {
        name: 'associateNum',
        label: '关联实例数量',
        render(item) {
            var count = item.associateNum;
            if (u.isNull(count) || u.isUndefined(count) || count < 0) {
                return '-';
            }
            return count;
        }
    },
    {
        name: 'vpcName',
        label: '所在网络',
        render(item) {
            return (item.vpcName || '-') + '<br>' + (item.vpcShortId || '-');
        }
    },
    {
        name: 'desc', label: '描述',
        render(item) {
            return u.escape(item.desc) || '-';
        }
    },
    {
        name: 'tag', label: '标签',
        render(item) {
            if (!item.tags || item.tags.length < 1) {
                return '-';
            }
            var tagHtml = '';
            var tags = '';
            u.each(item.tags, function (item, index) {
                var tagKey = u.escape(item.tagKey);
                var tagValue = u.escape(item.tagValue);
                tags += '{' + tagKey + ' : ' + tagValue + '} ';
                if (index < 2) {
                    tagHtml += tagKey + ' : ' + tagValue + '<br>';
                }
            });
            item.tags.length > 2 && (tagHtml += '...');
            return '<div title="' + tags + '">' + tagHtml + '</div>';
        }
    },
    {
        name: 'opt', label: '操作'
    }
];