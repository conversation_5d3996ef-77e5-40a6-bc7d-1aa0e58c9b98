import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {Notification} from '@baidu/sui';

import './style.less';
import {DocService} from '@/pages/sanPages/common';

const {invokeSUI, invokeSUIBIZ, asComponent, invokeAppComp, template} = decorators;
const tpl = html`
<template>
    <s-dialog
        class="dc-channel-editroute"
        title="{{title}}"
        open="{{isShow}}"
    >
        <s-alert skin="warning" class="dc-channel-warning">
          请谨慎使用专线通道关联功能，具体可参考<a href="{{DocService.dc_channel}}">使用约束</a>。
        </s-alert>
        <s-form>
            <s-form-item prop="subChannel" label="{{'专线通道ID：'}}" class="{{creator === 'other' ? 'other-networks-wrap' : 'networks-wrap'}}">
                <div class="subChannel-add"  s-for="item,index in subChannel">
                    <div class="channel-id-input">
                        <s-input
                            class="{{subChannelsInfo[index] ? 'input-error' : ''}}"
                            value="{=subChannel[index]=}"
                            placeholder="{{'请输入关联的专线通道ID'}}"
                            on-input="subChannelInput($event, index)"
                            disabled="{{inputDisabled[index]}}"
                        />
                        <s-icon s-if="index >= 0" class="iconfont icon-close" on-click="deleteSubChannel(index)"
                            disabled="{{deleteDisabled[index]}}"/>
                            <p class="error_text" s-if="errorShow[index]">该专线通道已开启链路探测，请先关闭链路探测后再进行删除。</p>
                    </div>
                    <div class="delete-check-wrap" s-if="showDeleteCheck[index] && !avaliableDisabled">
                        <p class="tip-text">解绑专线通道ID：{{subChannel[index]}}的同时，会同时进行以下操作，请根据个人使用情况选择。</p>
                        <s-checkbox-group
                            class="delete-check">
                            <s-checkbox checked disabled>解关联专线通道</s-checkbox>
                            <s-checkbox checked disabled>删除专线网关链路探测</s-checkbox>
                            <s-checkbox checked="{=cascadeDelBfd=}">删除专线通道可靠性监测</s-checkbox>
                        </s-checkbox-group>
                    </div>
                </div>
                <s-button
                    s-if="subChannel.length <= (subChannelNum - 1)"
                    skin="stringfy"
                    on-click="addItem"
                    disabled="{{avaliableDisabled}}">
                    {{'+新增一行'}}
                </s-button>
            </s-form-item>
        </s-form>
        <div slot="footer">
            <s-button on-click="submit" disabled="{{disabledSubmit}}" skin="primary">{{'确定'}}</s-button>
            <s-button on-click="closeDialog">{{'取消'}}</s-button>
        </div>
    </s-dialog>
</div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class EtChannelCreate extends Component {
    initData() {
        return {
            avaliableDisabled: false,
            action: 'associate',
            inputDisabled: [],
            deleteDisabled: [],
            extraChannelId: '',
            disabledSubmit: false,
            DocService,
            showDeleteCheck: [],
            cascadeDelBfd: false
        };
    }
    inited() {
        //传进来的值为null时，进行转换
        if (!this.data.get('subChannel')) {
            this.data.set('subChannel', ['']);
            this.data.set('avaliableDisabled', true);
            this.data.set('originalValue', []);
        } else {
            this.data.set('originalValue', this.data.get('subChannel'));
        }
        if (this.data.get('originalValue').length === 0) {
            this.data.set('deleteDisabled', [true]);
        }
        let now = this.data.get('subChannel');
        let inputDisabled = [];
        if (now[0] !== '') {
            for (let i = 0; i < now.length; i++) {
                inputDisabled.push(true);
            }
        }
        this.data.set('inputDisabled', inputDisabled);
        this.watch('subChannel', value => {
            this.checkDisabled(this.data.get('subChannel').length - 1);
            this.checkAction();
        });
    }

    /**
     * 通道关联时的校验
     * 父通道不能是别人的子通道，父通道必须已经绑定专线网关
     * 子通道必须是是可用状态，子通道没有绑定专线网关，子通道底下没有子通道，也不是其他通道父通道
     * @param {string} extraChannelId
     */
    relateChannelCheck(extraChannelId: string) {
        return new Promise((resolve, reject) => {
            // 解关联不需要校验
            if (this.data.get('action') === 'disassociate') {
                resolve();
            } else {
                // 父通道检查
                if (!this.data.get('dcgwId')) {
                    reject('当前专线通道不可用');
                } else if (this.data.get('mainChannel')) {
                    reject('当前专线通道已被关联，请检查您的操作');
                }
                // 子通道检查
                else {
                    let isOther = this.data.get('creator') === 'other';
                    this.$http
                        .channelDetail(
                            extraChannelId,
                            isOther && {
                                type: 'available'
                            }
                        )
                        .then(res => {
                            const {status, dcgwId, mainChannel, subChannel, enableIpv6} = res;
                            if (status !== 'established') {
                                reject('当前专线通道不可用');
                            } else if (mainChannel) {
                                reject('当前专线通道已被关联，请检查您的操作');
                            } else if (subChannel?.length) {
                                reject('该通道已经是主通道，无法关联到其他通道');
                            } else if (dcgwId) {
                                reject('该通道已经绑定到专线网关，无法关联到其他通道');
                            } else if (this.data.get('enableIpv6') !== enableIpv6) {
                                reject('该通道与当前通道IPv6功能状态不一致，无法关联');
                            } else {
                                resolve();
                            }
                        });
                }
            }
        });
    }

    checkDisabled(deleteIndex: number) {
        let now = this.data.get('subChannel');
        let primary = this.data.get('originalValue');
        this.data.set('deleteDisabled', []);
        this.data.set('inputDisabled', []);
        this.data.set('extraChannelId', '');
        for (let i = 0; i < now.length; i++) {
            this.data.push('deleteDisabled', false);
            this.data.push('inputDisabled', true);
        }
        if (now.length === primary.length && deleteIndex >= primary.length) {
            this.data.set('avaliableDisabled', false);
        } else {
            now.forEach((item: any, index: number) => {
                if (primary.includes(item) && deleteIndex >= primary.length) {
                    this.data.splice('inputDisabled', [index, 1, true]);
                    this.data.splice('deleteDisabled', [index, 1, true]);
                } else if (!primary.includes(item)) {
                    this.data.splice('inputDisabled', [index, 1, false]);
                    this.data.splice('deleteDisabled', [index, 1, false]);
                }
            });
            if (now.length > primary.length) {
                this.data.splice('inputDisabled', [now.length - 1, 1, false]);
                this.data.splice('deleteDisabled', [now.length - 1, 1, false]);
                this.data.set('extraChannelId', now[now.length - 1]);
            } else {
                this.data.set('extraChannelId', primary[deleteIndex]);
            }
            if (primary.length === 0) {
                this.data.set('deleteDisabled', [true]);
            }
        }
    }

    //判断此时的状态是绑定还是解绑
    checkAction() {
        let now = this.data.get('subChannel');
        let primary = this.data.get('originalValue');
        if (now.length > primary.length) {
            this.data.set('action', 'associate');
            this.data.set('avaliableDisabled', true);
        } else {
            this.data.set('action', 'disassociate');
            this.data.set('avaliableDisabled', this.data.get('creator') === 'other');
        }
    }
    addItem(e: Event) {
        this.data.push('subChannel', '');
        this.clearShowDeleteCheck();
        this.checkDisabled(this.data.get('subChannel').length - 1);
    }
    submit(e: Event) {
        let primary = this.data.get('originalValue');
        if (!this.data.get('extraChannelId')) {
            return this.closeDialog();
        }
        this.relateChannelCheck(this.data.get('extraChannelId'))
            .then(() => {
                let payload = {
                    dcphyId: this.data.get('dcphyId'),
                    action: this.data.get('action'),
                    extraChannelId: this.data.get('extraChannelId')
                };
                this.data.set('disabledSubmit', true);
                let channelId = this.data.get('channelId');
                let isOther = this.data.get('creator') === 'other';
                let requester = 'channelAssociate';
                if (isOther) {
                    requester = 'crossChannelAssociate';
                }
                if (!isOther && this.data.get('action') === 'disassociate') {
                    payload.cascadeDelBfd = this.data.get('cascadeDelBfd');
                }
                this.$http[requester](channelId, payload)
                    .then(() => {
                        this.data.set('disabledSubmit', false);
                        this.closeDialog();
                        this.fire('created');
                    })
                    .catch(() => {
                        this.data.set('disabledSubmit', false);
                    });
            })
            .catch(msg => {
                Notification.warning(msg);
            });
    }
    closeDialog() {
        this.data.set('isShow', false);
        this.dispose();
    }
    deleteSubChannel(index: number) {
        let isOther = this.data.get('creator');
        if (isOther === 'other') {
            let value = this.data.get(`subChannel[${index}]`);
            if (this.data.get('inputDisabled')[index]) {
                this.$http.checkChannelHc(value).then((res: any) => {
                    if (!res.healthCheckId) {
                        this.data.splice('subChannel', [index, 1]);
                        this.checkDisabled(index);
                        this.checkAction();
                    } else {
                        this.data.set(`errorShow[${index}]`, true);
                    }
                });
            } else {
                this.data.splice('subChannel', [index, 1]);
                this.checkDisabled(index);
            }
        } else {
            this.clearShowDeleteCheck();
            if (this.data.get('inputDisabled')[index]) {
                this.checkDisabled(index);
                this.checkAction();
                this.data.set(`showDeleteCheck[${index}]`, true);
            } else {
                this.data.splice('subChannel', [index, 1]);
                this.checkDisabled(index);
            }
        }
    }
    clearShowDeleteCheck() {
        this.data.get('subChannel').forEach((element: string, index: number) => {
            this.data.set(`showDeleteCheck[${index}]`, false);
        });
    }
}
export default Processor.autowireUnCheckCmpt(EtChannelCreate);
