import {InstanceStatus, ChannelRouteBgpStatus} from '@/pages/sanPages/common/enum';

export const columns = [
    {name: 'id', label: '通道ID', width: '240', fixed: 'left'},
    {name: 'name', label: '通道名称', width: '120'},
    {name: 'region', label: '地域', width: '100'},
    {
        name: 'status',
        label: '状态',
        width: '100',
        filter: {
            options: InstanceStatus.getChannelStatus(),
            value: ''
        }
    },
    {
        name: 'diagnose',
        label: '诊断',
        width: 160
    },
    {name: 'authorizedUsers', label: '分配对象', width: '240'},
    {name: 'manager', label: '分配权限', width: '120'},
    {name: 'dcphyId', label: '专线ID', width: '200'},
    {name: 'vlanId', label: 'VLAN ID', width: '100', sortable: true},
    {name: 'ips', label: 'IPV4互联IP', width: '240'},
    {name: 'localIpv6', label: 'IPV6互联IP', width: '240'},
    {name: 'routeType', label: '路由协议', width: '100'},
    {name: 'bgpKey', label: 'BGP密钥', width: '100'},
    {name: 'bgpAsn', label: 'BGP ASN', width: '100'},
    {name: 'fakeAsn', label: 'Fake ASN', width: '100'},
    {
        name: 'bgpStatus',
        label: 'IPv4 BGP状态',
        width: '140',
        filter: {
            options: [{text: '全部状态', value: ''}, ...ChannelRouteBgpStatus.toArray()],
            value: ''
        }
    },
    {
        name: 'ipv6BgpStatus',
        label: 'IPv6 BGP状态',
        width: '140',
        filter: {
            options: [{text: '全部状态', value: ''}, ...ChannelRouteBgpStatus.toArray()],
            value: ''
        }
    },
    {name: 'bgpRouteLimit', label: 'BGP路由条目上限', width: '160'},
    {name: 'description', label: '描述', width: '100'},
    {name: 'tag', label: '标签', width: '120'},
    {name: 'opt', label: '操作', width: '130', fixed: 'right'}
];
