/*
 * @Description:
 * @Author: <EMAIL>
 * @Date: 2022-04-12 11:34:02
 */

import _ from 'lodash';
import {Component} from 'san';
import {San2React} from '@baidu/bce-react-toolkit';
import {html, decorators, Processor} from '@baiducloud/runtime';
import './list';

const {invokeComp, withSidebar, invokeAppComp, template, invokeSUIBIZ} = decorators;

import './style.less';

const tpl = html`
    <template class="s-tab-list-page">
        <s-app-tab-page s-ref="tab" class="app-channel-tab">
            <s-app-tab-page-tab-panel url="#/dc/channel/list" label="本帐号专线通道">
                <channel-instance-list creator="oneself"></channel-instance-list>
            </s-app-tab-page-tab-panel>
            <s-app-tab-page-tab-panel url="#/dc/channel/list" label="跨账号专线通道">
                <channel-instance-list creator="other"></channel-instance-list>
            </s-app-tab-page-tab-panel>
        </s-app-tab-page>
    </template>
`;
@template(tpl)
@invokeSUIBIZ
@invokeComp('@channel-instance-list')
@invokeAppComp
class channelTab extends Component {}

export default San2React(Processor.autowireUnCheckCmpt(channelTab));
