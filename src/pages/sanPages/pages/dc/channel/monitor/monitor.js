/**
 * @file src/dc/channel/monitor.js
 * <AUTHOR>
 */

import {Component} from 'san';
import {Dialog, Select, DatePicker, Button} from '@baidu/sui';
import {html, decorators, redirect} from '@baiducloud/runtime';
import moment from 'moment';
import {BcmSDK} from '@baidu/new-bcm-sdk';
import {BcmChartPanel} from '@baidu/new-bcm-sdk-san';
import {OutlinedRefresh} from '@baidu/sui-icon';

import monitorConfig from '@/pages/sanPages/utils/monitorConfig';
import './style.less';

const {dcgwMonitorMetric, shortcutItems} = monitorConfig;
const {template} = decorators;

const tpl = html`
    <template>
        <s-dialog open="{{true}}" title="{{title}}" width="{{initWidth}}">
            <div class="dc-channel-monitor">
                <div class="content-box">
                    <div class="m-g-b">
                        <span>
                            <span>{{'监控指标：'}}</span>
                            <s-select width="120" placeholder="{{'请选择'}}" datasource="{{typeList}}" value="{=type=}">
                            </s-select>
                        </span>
                        <span class="statistics">
                            <span>{{'统计项：'}}</span>
                            <s-select
                                width="120"
                                placeholder="{{'请选择'}}"
                                datasource="{{statisticsList}}"
                                value="{=statistics=}"
                            >
                            </s-select>
                        </span>
                        <span class="alarm-state">
                            <span>{{'时间：' }}</span>
                            <s-date-range-picker
                                value="{=timeRange=}"
                                width="240"
                                shortcut="{{shortcutItems}}"
                                range="{{range}}"
                                mode="second"
                                on-change="onTimeChange"
                            />
                        </span>
                        <s-button class="s-icon-button button-margin-left" on-click="refresh"
                            ><outlined-refresh class="icon-class"
                        /></s-button>
                    </div>
                    <div class="monitor-trends">
                        <div class="monitor-trend-box">
                            <bcm-chart-panel
                                s-ref="metric"
                                upperCaseUnit="{{true}}"
                                api-type="metricName"
                                showbigable="{{false}}"
                                scope="{{scope}}"
                                height="{{300}}"
                                options="{{options}}"
                                statistics="{{statistics}}"
                                dimensions="InstanceId:{{dcgwId || id}}"
                                metrics="{{metricsData.metrics}}"
                                startTime="{=startTime=}"
                                endTime="{=endTime=}"
                                period="{{monitorDefaultPeriod}}"
                                unit="{{metricsData.unit}}"
                                bitUnit="{{metricsData.bitUnit}}"
                                sdk="{{bcmSdk}}"
                                connect-nulls="{{true}}"
                                no-data="{{noData}}"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div slot="footer"></div>
        </s-dialog>
    </template>
`;

@template(tpl)
export default class EtChannelMonitor extends Component {
    static components = {
        'bcm-chart-panel': BcmChartPanel,
        's-date-range-picker': DatePicker.DateRangePicker,
        's-dialog': Dialog,
        'outlined-refresh': OutlinedRefresh,
        's-select': Select,
        's-button': Button
    };

    static computed = {
        metricsData() {
            const type = this.data.get('type');
            return dcgwMonitorMetric[type];
        }
    };

    initData() {
        return {
            typeList: [
                {
                    text: '带宽',
                    value: 'bandwidth'
                },
                {
                    text: '流量',
                    value: 'flow'
                },
                {
                    text: '包速率',
                    value: 'package'
                },
                {
                    text: '限速丢包带宽',
                    value: 'limitSpeedBandwidth'
                },
                {
                    text: '限速丢包速率',
                    value: 'limitSpeedRate'
                }
            ],
            type: 'bandwidth',
            statisticsList: [
                {
                    text: '平均值',
                    value: 'average'
                },
                {
                    text: '最大值',
                    value: 'maximum'
                },
                {
                    text: '最小值',
                    value: 'minimum'
                },
                {
                    text: '和值',
                    value: 'sum'
                },
                {
                    text: '样本数',
                    value: 'sampleCount'
                }
            ],
            statistics: 'average',
            scope: 'BCE_DEDICATEDCONN_GATEWAY',
            monitorDefaultPeriod: 60,
            timeRange: {
                begin: new Date(moment().subtract(1, 'hour').valueOf()),
                end: new Date(moment().valueOf())
            },
            startTime: moment().subtract(1, 'hour').utc().format('YYYY-MM-DDTHH:mm:00') + 'Z',
            endTime: moment().utc().format('YYYY-MM-DDTHH:mm:00') + 'Z',
            shortcutItems,
            range: {
                begin: new Date(moment().subtract(40, 'day').valueOf()),
                end: new Date(moment().valueOf())
            },
            noData: '暂无数据，请检查专线通道是否绑定专线网关',
            bcmSdk: new BcmSDK({client: window.$http, context: window.$context}),
            options: {dataZoom: {start: 0}},
            initWidth: 900,
            title: '通道监控'
        };
    }
    inited() {
        if (this.data.get('scope') === 'BCE_DEDICATEDCONN_CHANNEL') {
            this.data.push('typeList', {
                text: '限速丢包率',
                value: 'iplr'
            });
        }
        this.initBcmSdk();
        const {initWidth, title} = this.data.get('');
        if (initWidth) {
            this.data.set('initWidth', initWidth);
        }
        if (title) {
            this.data.set('title', title);
        }
    }
    initBcmSdk() {
        let context = window.$context;
        context.getCurrentRegion = () => {
            return this.data.get('currentRegion');
        };
        let bcmSdk = new BcmSDK({client: window.$http, context});
        this.data.set('bcmSdk', bcmSdk);
    }
    onTimeChange({value}) {
        this.data.set('startTime', moment(value.begin).utc().format('YYYY-MM-DDTHH:mm:00') + 'Z');
        this.data.set('endTime', moment(value.end).utc().format('YYYY-MM-DDTHH:mm:00') + 'Z');
        let ms = moment(value.end) - moment(value.begin);
        let hourTime = Math.round(ms / 86400000);
        switch (true) {
            case hourTime <= 1:
                this.data.set('monitorDefaultPeriod', 60);
                break;
            case hourTime <= 3:
                this.data.set('monitorDefaultPeriod', 300);
                break;
            case hourTime <= 7:
                this.data.set('monitorDefaultPeriod', 600);
                break;
            case hourTime <= 14:
                this.data.set('monitorDefaultPeriod', 1800);
                break;
            case hourTime <= 40:
                this.data.set('monitorDefaultPeriod', 3600);
                break;
            default:
                break;
        }
    }
    attached() {
        this.watch('timeRange', () => this.refresh());
        this.watch('type', () => this.refresh());
        this.watch('statistics', () => this.refresh());
    }
    refresh() {
        this.ref('metric').loadMetrics();
    }
}
