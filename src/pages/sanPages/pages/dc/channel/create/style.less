.dc-channel-new {
    .s-step-block {
        width: 316px !important;
    }
    .form-widget {
        margin-top: 16px;
        min-width: 1020px;
        .body-part-content {
            width: calc(~'100vw - 260px');
            padding: 24px;
        }
        .form-part-wrap {
            border-radius: 6px;
            width: calc(~'100vw - 260px');
            background: #fff;
            &:first-child {
                padding: 24px;
            }
            padding: 16px 24px 24px;
            min-width: 1020px;
            .s-form-item-label {
                width: 155px;
                text-align: left;
                .label_class {
                    display: flex;
                    align-items: center;
                    .inline-tip {
                        top: 3px;
                        position: relative;
                        .s-tip-warning {
                            justify-content: center;
                            .warning_class {
                                fill: #999;
                            }
                        }
                        .s-tip:hover .s-icon path {
                            fill: #2468f2 !important;
                        }
                    }
                }
                .inline-tip {
                    top: 3px;
                    position: relative;
                    .s-tip-warning {
                        justify-content: center;
                        .warning_class {
                            fill: #999;
                        }
                    }
                    .s-tip:hover .s-icon path {
                        fill: #2468f2 !important;
                    }
                }
            }

            .s-alert-skin-warning {
                height: 95px;
                align-items: flex-start;
                margin-top: 10px;
            }

            .s-radio-group {
                line-height: 30px;
                .s-radio {
                    margin-right: 50px;
                }
            }

            .ips-wrap {
                .s-input input {
                    text-align: center;
                }

                .s-select .s-input-area {
                    width: 40px;
                    input {
                        text-align: left;
                    }
                }

                .s-form-item-error {
                    padding-bottom: 0;
                }
            }

            .networks-wrap {
                .s-form-item-control > div {
                    margin-bottom: 10px;
                }

                .icon-close {
                    font-size: 14px;
                }
            }

            .ips-wrap,
            .networks-wrap {
                .s-form-item-label > label {
                    &::before {
                        content: '*';
                        color: #f33e3e;
                        margin-right: 4px;
                        position: absolute;
                        left: -7px;
                    }
                }
            }

            .localIp-wrap {
                display: flex;
                span {
                    width: 10px;
                    text-align: center;
                    line-height: 26px;
                }
                .s-input-suffix-container {
                    width: 80px;
                }
                .s-selectdropdown {
                    width: 80px !important;
                    text-align: left;
                }
                .margin-hack {
                    margin-left: 36px;
                }
            }

            .input-error {
                input {
                    border-color: #f33e3e !important;
                    &:hover {
                        border-color: #f33e3e;
                    }
                }
                .s-input-true {
                    border-color: #f33e3e !important;
                }
            }

            .s-input input {
                box-sizing: content-box;
            }
            .channel_ipv6 {
                .s-row {
                    align-items: center;
                }
            }
            .channel_localIpv6 {
                .s-form-item-label {
                    label:before {
                        content: '*';
                        color: #f33e3e;
                        position: relative;
                        margin-right: -4px;
                        right: 6px;
                    }
                }
                .s-form-item-help,
                .s-form-item-error {
                    width: 350px;
                }
            }
            .channel_idc_ipv6 {
                margin-top: 0px !important;
            }
            .dcphyId-select {
                .s-selectdropdown > div:first-child {
                    overflow: hidden;
                }
            }
        }
        .s-input {
            border-color: #e8e9eb !important;
            .s-input-area {
                input {
                    box-sizing: border-box;
                }
            }
        }
        .channel-protoco-tip {
            color: #ff9326;
            a {
                cursor: pointer;
            }
        }
    }
    .order-confirm-panel {
        width: 100%;
        margin-top: 16px;
        width: calc(~'100vw - 240px');
        min-width: 1020px;
        .charge-wrapper {
            display: none;
        }
    }
    .order-confirm {
        margin-top: 16px;
        width: calc(~'100vw - 260px');
        min-width: 1020px;
    }
    .buybucket {
        width: 100%;
        left: 0;
        height: 80px;
        .buybucket-container {
            float: left;
            height: 80px;
            transform: translateY(4%);
            display: flex;
            align-items: center;
            .billing-sdk-protocol-wrapper .buy-agreement {
                margin-bottom: 8px !important;
            }
        }
        .dc-port-tooltip {
            margin-left: 16px;
            .price_label {
                color: #2468f2;
            }
        }
    }
    .bucket-footer-wrap {
        width: 100%;
        padding: 0 105px;
        .billing-sdk-protocol-wrapper .buy-agreement .agreement-wrap {
            margin-top: 5px;
            margin-left: 16px;
            margin-bottom: 5px;
        }
        .confirm-wrapper {
            display: block;
        }
        .confirm-footer-wrap {
            display: flex;
            .total-price-wrap {
                margin-left: 16px;
            }
        }
    }
    .dcphyId-select {
        .s-selectdropdown > div:first-child {
            overflow: hidden;
        }
    }
    .fakeAs-tip {
        color: #84868c;
        margin-top: 4px;
    }
    .s-create-page-content {
        padding-bottom: 96px;
    }
    .wrapper-title {
        display: inline-flex;
        font-size: 16px;
        line-height: 24px;
        color: #151b26;
        font-weight: 500;
    }
}

.dc-channel-create {
    .s-form-item-label {
        width: 155px;
        text-align: left;
        .label_class {
            display: flex;
            align-items: center;
            .inline-tip {
                top: 3px;
                position: relative;
                .s-tip-warning {
                    justify-content: center;
                    .warning_class {
                        fill: #999;
                    }
                }
                .s-tip:hover .s-icon path {
                    fill: #2468f2 !important;
                }
            }
        }
        .inline-tip {
            top: 3px;
            position: relative;
            .s-tip-warning {
                justify-content: center;
                .warning_class {
                    fill: #999;
                }
            }
            .s-tip:hover .s-icon path {
                fill: #2468f2 !important;
            }
        }
    }

    .s-alert-skin-warning {
        height: 95px;
        align-items: flex-start;
        margin-top: 10px;
    }

    .s-radio-group {
        line-height: 30px;
        .s-radio {
            margin-right: 10px;
        }
    }

    .ips-wrap {
        .s-input input {
            text-align: center;
        }

        .s-select .s-input-area {
            width: 40px;
            input {
                text-align: left;
            }
        }

        .s-form-item-error {
            padding-bottom: 0;
        }
    }

    .networks-wrap {
        .s-form-item-control > div {
            margin-bottom: 10px;
        }

        .icon-close {
            font-size: 14px;
        }
    }

    .ips-wrap,
    .networks-wrap {
        .s-form-item-label > label {
            &::before {
                content: '*';
                color: #f33e3e;
                margin-right: 4px;
                position: absolute;
                left: -7px;
            }
        }
    }

    .localIp-wrap {
        display: flex;
        span {
            width: 10px;
            text-align: center;
            line-height: 26px;
        }
        .s-input-suffix-container {
            width: 80px;
        }
        .s-selectdropdown {
            width: 80px !important;
            text-align: left;
        }
    }

    .input-error {
        input {
            border-color: #f33e3e !important;
            &:hover {
                border-color: #f33e3e;
            }
        }
        .s-input-true {
            border-color: #f33e3e !important;
        }
    }

    .s-input input {
        box-sizing: content-box;
    }
    .channel_ipv6 {
        .s-row {
            align-items: center;
        }
    }
    .channel_localIpv6 {
        .s-form-item-label {
            label:before {
                content: '*';
                color: #f33e3e;
                position: relative;
                margin-right: -4px;
                right: 6px;
            }
        }
        .s-form-item-help,
        .s-form-item-error {
            width: 350px;
        }
    }
    .dcphyId-select {
        .s-selectdropdown > div:first-child {
            overflow: hidden;
        }
    }
}
.dc-bgp-key-reset {
    .s-form-item {
        margin: 20px 0;
        .s-row-flex {
            align-items: center;
        }
        .s-form-item-label {
            width: 70px;
            text-align: left;
        }
    }
    .security-tip {
        background: #fcf7f1;
        color: #333333;
        padding: 9px 20px 9px 40px;
        position: relative;
        line-height: 20px;
        .icon-warning {
            color: #f39000;
            margin-right: 5px;
            position: absolute;
            left: 20px;
            font-size: 12px;
        }
    }
}
.error-message {
    color: #d0021b;
}

.dc-channel-create-dialog {
    .s-dialog-wrapper {
        overflow: unset !important;
    }
    .s-alert {
        .s-alert-icon {
            margin-right: 0;
        }
    }
    .alert_tip {
        .alert-time {
            color: #f33e3e !important;
        }
        .alert-link {
            font-size: 12px;
            &:hover {
                text-decoration: underline;
            }
        }
    }
}

.locale-en {
    .dc-channel-create .s-form-item-label {
        width: 210px;
    }
}
.s-popup {
    .s-tooltip-body {
        .config-item {
            label {
                white-space: nowrap;
            }
            .item-values {
                width: 200px;
                word-wrap: break-word;
            }
        }
    }
}
