import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';

import Confirm from '@/pages/sanPages/components/confirm';
import './style.less';

const {invokeSUI, asComponent, template} = decorators;

const validateRules = self => {
    return {
        new: [
            {
                pattern: /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[!@#$%^*()_.])[\da-zA-Z!@#$%^*()_.]{8,17}$/,
                message: '8～17位字符，英文、数字和符号必须同时存在，符号仅限!@#$%^*()_.'
            }
        ],
        confirm: [
            {
                pattern: /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[!@#$%^*()_.])[\da-zA-Z!@#$%^*()_.]{8,17}$/,
                message: '8～17位字符，英文、数字和符号必须同时存在，符号仅限!@#$%^*()_.'
            },
            {
                validator(rule, value, callback) {
                    let newPass = self.data.get('formData.new');
                    if (newPass !== value) {
                        return callback('两次密码不一致');
                    }
                    return callback();
                }
            }
        ]
    };
};

const tpl = html`
    <div>
        <s-dialog title="重置密钥" open="{{open}}">
            <s-form s-ref="form" data="{=formData=}" rules="{{rules}}" class="dc-bgp-key-reset">
                <p class="security-tip">
                    <s-icon name="warning"></s-icon>
                    重置密钥会导致BGP专线通道中断，为保证你的服务不受影响，请在割接窗口期操作，并同时注意专线两端的BGP密钥配置一致，否则会导致专线通道的BGP会话无法建立
                </p>
                <s-form-item label="{{'通道ID：'}}">
                    <span>{{info.id}}</span>
                </s-form-item>
                <s-form-item label="{{'通道名称：'}}">
                    <span>{{info.name}}</span>
                </s-form-item>
                <s-form-item prop="new" label="{{'新密码：'}}">
                    <s-input width="300" placeholder="请输入新密钥" value="{=formData.new=}"></s-input>
                </s-form-item>
                <s-form-item prop="confirm" label="{{'确认密码：'}}">
                    <s-input width="300" placeholder="请输入新密钥" value="{=formData.confirm=}"></s-input>
                </s-form-item>
            </s-form>
            <div slot="footer">
                <s-button on-click="onConfirm" disabled="{{disabled}}" skin="primary">{{'确定'}}</s-button>
                <s-button on-click="closeDialog">{{'取消'}}</s-button>
            </div>
        </s-dialog>
    </div>
`;

@template(tpl)
@invokeSUI
class ChannelBgpKeyReset extends Component {
    initData() {
        return {
            open: true,
            info: {},
            formData: {
                new: '',
                confirm: ''
            },
            rules: validateRules(this),
            disabled: false
        };
    }

    async onConfirm() {
        const form = this.ref('form');
        await form.validateFields();
        this.data.set('disabled', true);
        let info = this.data.get('info');
        let payload = {
            dcphyId: info.dcphyId,
            channelId: info.id,
            bgpKey: this.data.get('formData.new')
        };
        if (this.data.get('formData.new') === '') {
            let confirm = new Confirm({
                data: {
                    open: true,
                    content: '是否密钥重置为空？'
                }
            });
            confirm.attach(document.body);
            confirm.on('confirm', () => {
                this.$http
                    .channelUpdate(payload)
                    .then(() => {
                        this.fire('bgpKeyReset');
                        this.closeDialog();
                        this.data.set('disabled', false);
                    })
                    .catch(() => this.data.set('disabled', false));
            });
        } else {
            this.$http
                .channelUpdate(payload)
                .then(() => {
                    this.fire('bgpKeyReset');
                    this.closeDialog();
                    this.data.set('disabled', false);
                })
                .catch(() => this.data.set('disabled', false));
        }
    }

    closeDialog() {
        this.dispose();
    }
}
export default Processor.autowireUnCheckCmpt(ChannelBgpKeyReset);
