import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import rule from '@/pages/sanPages/utils/rule';
import {
    AuthorizedUsersType,
    AuthDistributionType,
    RouteParamsType,
    InstanceStatus,
    ET_CHANNEL_PAYER
} from '@/pages/sanPages/common/enum';
import {ContextService, DocService} from '@/pages/sanPages/common';
import {convertCidrToBinary} from '@/pages/sanPages/utils/common';

import Client from '@baiducloud/httpclient';
import {TagEditPanel} from '@baidu/bce-tag-sdk-san';
import {BillingSdk, OrderItem, UPDATE_STATUS} from '@baiducloud/bce-billing-sdk';
import {ShoppingCart, OrderConfirm, Protocol, TotalPrice} from '@baiducloud/bce-billing-sdk-san';
import {$flag as FLAG, contextPipe} from '@/pages/sanPages/utils/helper';

import {getUserId} from '@/pages/sanPages/utils/helper';
import {parseQuery} from '@/utils';
import {validateRules} from './helper';
import './style.less';

const {invokeSUI, invokeSUIBIZ, asComponent, invokeAppComp, template} = decorators;
const MASK_RANGE = 31;
const kXhrOptions = {'X-silence': true};
const tpl = html`
    <template>
        <s-app-create-page
            class="{{klass}}"
            backTo="{{pageTitle.backTo}}"
            backToLabel="{{pageTitle.label}}"
            pageTitle="{{pageHeaderTitle}}"
        >
            <div class="s-step-block" s-if="!isOtherPay && !isReCreate && !isAccept">
                <s-steps current="{{stepIndex + 1}}">
                    <s-steps-step s-for="i in steps" title="{{i.title}}" />
                </s-steps>
            </div>
            <!--<s-alert skin="warning">
            <span class="alert_tip">
                <span class="alert-time">专线接入ET产品预计2024年4月1日起新增专线通道出方向流量费，</span>
                <span>具体详情请参见：</span>
                <a class="alert-link" target="_blank" href="{{DocService.et_helpFile}}">
                    专线接入产品价格。
                </a>
            </span>
        </s-alert>-->
            <div class="form-widget" s-if="stepIndex === 0">
                <s-form s-ref="form" data="{=formData=}" rules="{{rules}}">
                    <div class="body-part-content form-part-wrap">
                        <h4 class="wrapper-title">配置信息</h4>
                        <s-form-item prop="dcphyId" label="{{'物理专线ID：'}}">
                            <s-select
                                width="320"
                                value="{=formData.dcphyId=}"
                                disabled="{{isReCreate}}"
                                on-change="dcphyIdChange($event)"
                                filterable
                                class="dcphyId-select"
                            >
                                <s-tooltip
                                    s-for="item in dcphyList"
                                    trigger="hover"
                                    placement="right"
                                    content="{{item.errorTip ? item.errorTip : item.text}}"
                                >
                                    <s-select-option
                                        value="{{item.value}}"
                                        label="{{item.text}}"
                                        disabled="{{item.isCreateDisabled}}"
                                    ></s-select-option>
                                </s-tooltip>
                            </s-select>
                        </s-form-item>
                        <s-form-item prop="name" label="{{'通道名称：'}}">
                            <s-input value="{=formData.name=}" disabled="{{type === 'editIpv6'}}" width="318"></s-input>
                        </s-form-item>
                        <s-form-item prop="onlySelfAuthorized" label="{{'分配对象：'}}">
                            <s-radio-radio-group value="{=formData.onlySelfAuthorized=}">
                                <s-radio
                                    s-for="item in authorizedUsersRadio"
                                    disabled="{{type === 'editIpv6' || disableFlag}}"
                                    label="{{item.text}}"
                                    value="{{item.value}}"
                                ></s-radio>
                            </s-radio-radio-group>
                        </s-form-item>
                        <s-form-item
                            s-if="formData.onlySelfAuthorized === 'others'"
                            prop="authorizedUsers"
                            label="{{'填写账户ID：'}}"
                        >
                            <s-input
                                width="320"
                                value="{=formData.authorizedUsers=}"
                                on-blur="checkAccount"
                                disabled="{{type === 'editIpv6' || disableFlag}}"
                            />
                            <span class="s-form-item-error">{{checkAccountErr}}</span>
                        </s-form-item>
                        <s-form-item s-if="formData.onlySelfAuthorized === 'others'" prop="onlySelfAuthorized">
                            <template slot="label" class="label_class">
                                {{'分配权限：'}}
                                <s-tip
                                    class="inline-tip"
                                    content="管理权包含配置及使用权，使用权只可使用专线通道，不能配置。专线通道的管理权包含专线通道的路由管理、可靠性检测配置的配置权限。"
                                    skin="question"
                                />
                            </template>
                            <s-radio-radio-group value="{=formData.managingUser=}">
                                <s-radio
                                    disabled="{{isReCreate}}"
                                    s-for="item in authDistributionRadio"
                                    label="{{item.text}}"
                                    value="{{item.value}}"
                                ></s-radio>
                            </s-radio-radio-group>
                        </s-form-item>
                        <s-form-item prop="payer" s-if="showPayer">
                            <template slot="label" class="label_class">
                                {{'费用支付方：'}}
                                <s-tip
                                    class="inline-tip"
                                    content="创建者指创建专线通道的人，即物理专线所有人。接受者指最终使用用户。"
                                    skin="question"
                                />
                            </template>
                            <s-radio-radio-group value="{=formData.payer=}">
                                <s-radio
                                    s-for="item in channelPayer"
                                    disabled="{{type === 'editIpv6' || isReCreate}}"
                                    label="{{item.text}}"
                                    value="{{item.value}}"
                                ></s-radio>
                            </s-radio-radio-group>
                        </s-form-item>
                        <s-form-item prop="vlanId" label="VLAN ID：" help="{{'例如：0，2-4009'}}">
                            <s-input-number
                                value="{=formData.vlanId=}"
                                min="{{0}}"
                                max="{{4009}}"
                                disabled="{{type === 'editIpv6'}}"
                            />
                        </s-form-item>
                        <s-form-item
                            prop="localIp"
                            class="ips-wrap"
                            label="{{'云端网络互联IP：'}}"
                            help="{{'例如：**********（按tab键切换到下一输入框）'}}"
                        >
                            <div class="localIp-wrap">
                                <div class="{{localIpErrInfo[0] ? 'input-error' : ''}}">
                                    <s-input
                                        s-ref="ipInput0"
                                        disabled="{{type === 'editIpv6'}}"
                                        value="{=formData.localIp[0]=}"
                                        placeholder=""
                                        width="{{44}}"
                                        maxLength="3"
                                        on-input="ipInput($event, 0, 'local')"
                                    />
                                </div>
                                <span>.</span>
                                <div class="{{localIpErrInfo[1] ? 'input-error' : ''}}">
                                    <s-input
                                        s-ref="ipInput1"
                                        disabled="{{type === 'editIpv6'}}"
                                        value="{=formData.localIp[1]=}"
                                        placeholder=""
                                        width="{{44}}"
                                        maxLength="3"
                                        on-input="ipInput($event, 1, 'local')"
                                    />
                                </div>
                                <span>.</span>
                                <div class="{{localIpErrInfo[2] ? 'input-error' : ''}}">
                                    <s-input
                                        s-ref="ipInput2"
                                        disabled="{{type === 'editIpv6'}}"
                                        value="{=formData.localIp[2]=}"
                                        placeholder=""
                                        width="{{44}}"
                                        maxLength="3"
                                        on-input="ipInput($event, 2, 'local')"
                                    />
                                </div>
                                <span>.</span>
                                <div class="{{localIpErrInfo[3] ? 'input-error' : ''}}">
                                    <s-input
                                        s-ref="ipInput3"
                                        disabled="{{type === 'editIpv6'}}"
                                        value="{=formData.localIp[3]=}"
                                        placeholder=""
                                        width="{{44}}"
                                        maxLength="3"
                                        on-input="ipInput($event, 3, 'local')"
                                    />
                                </div>
                                <span>/</span>
                                <s-select
                                    on-change="maskChange('local')"
                                    value="{=formData.maskCode=}"
                                    disabled="{{type === 'editIpv6'}}"
                                >
                                    <s-select-option
                                        s-for="item in maskDatasource"
                                        value="{{item.value}}"
                                        label="{{item.label}}"
                                    ></s-select-option>
                                </s-select>
                            </div>
                            <p s-if="{{localIpErr || localIpFirstErr}}" class="s-form-item-error">
                                {{localIpErr || localIpFirstErr}}
                            </p>
                        </s-form-item>
                        <s-form-item prop="remoteIp" label="{{'IDC互联IP：'}}" class="ips-wrap">
                            <div class="localIp-wrap">
                                <s-input
                                    value="{{formData.remoteIp[0] || '-'}}"
                                    placeholder=""
                                    width="{{44}}"
                                    disabled="{{true}}"
                                />
                                <span>.</span>
                                <s-input
                                    value="{{formData.remoteIp[1] || '-'}}"
                                    placeholder=""
                                    width="{{44}}"
                                    disabled="{{true}}"
                                />
                                <span>.</span>
                                <s-input
                                    value="{{formData.remoteIp[2] || '-'}}"
                                    placeholder=""
                                    width="{{44}}"
                                    disabled="{{true}}"
                                />
                                <span>.</span>
                                <s-tooltip trigger="{{localIpEnable ? 'hover' : ''}}" placement="bottom">
                                    <div slot="content">
                                        <!--bca-disable-next-line-->
                                        {{'可输入范围' + remoteRange | raw}}
                                    </div>
                                    <div class="{{localIpErrInfo[4] ? 'input-error' : ''}}">
                                        <s-input
                                            s-ref="ipInput4"
                                            disabled="{{type === 'editIpv6'}}"
                                            value="{=formData.remoteIp[3]=}"
                                            placeholder=""
                                            width="{{44}}"
                                            maxLength="3"
                                            on-input="ipInput($event, 4, 'remote')"
                                        />
                                    </div>
                                </s-tooltip>
                                <span class="margin-hack">/</span>
                                <s-select
                                    on-change="maskChange('remote')"
                                    value="{=formData.maskCode=}"
                                    disabled="{{type === 'editIpv6'}}"
                                >
                                    <s-select-option
                                        s-for="item in maskDatasource"
                                        value="{{item.value}}"
                                        label="{{item.label}}"
                                    ></s-select-option>
                                </s-select>
                            </div>
                            <p s-if="{{localIpErrInfo[4] || remoteIpFirstErr}}" class="s-form-item-error">
                                {{localIpErrInfo[4] || remoteIpFirstErr || ''}}
                            </p>
                        </s-form-item>
                        <s-form-item label="{{'IPv6功能:'}}" class="channel_ipv6" s-if="{{ipv6Show}}">
                            <s-switch checked="{=formData.enableIpv6=}" />
                        </s-form-item>
                        <s-form-item
                            prop="localIpv6"
                            label="{{'云端网络侧IPv6互联地址:'}}"
                            s-if="{{ipv6Show && formData.enableIpv6}}"
                            class="channel_localIpv6"
                        >
                            <s-input
                                width="318"
                                value="{=formData.localIpv6=}"
                                disabled="{{type === 'editIpv6' && editedRow.enableIpv6}}"
                            />
                        </s-form-item>
                        <s-form-item
                            prop="remoteIpv6"
                            label="{{'IDC侧IPv6互联地址:'}}"
                            s-if="{{ipv6Show && formData.enableIpv6}}"
                            class="channel_localIpv6"
                        >
                            <s-input
                                width="318"
                                value="{=formData.remoteIpv6=}"
                                disabled="{{type === 'editIpv6' && editedRow.enableIpv6}}"
                            />
                        </s-form-item>
                        <s-form-item label=" " class="channel_idc_ipv6">
                            <s-alert s-if="{{ipv6Show && formData.enableIpv6}}" skin="warning">
                                <p>IPv6互联地址的2个IP必须在同一个网段内且掩码输入范围为88-127</p>
                                <p>示例：</p>
                                <p>云端网络侧IPV6互联地址:2400:DA00:E003:0000:016A:0400:0000:100/127，</p>
                                <p>IDC侧IPV6互联地址:2400:DA00:E003:0000:016A:0400:0000:101/127</p>
                            </s-alert>
                        </s-form-item>
                        <s-form-item prop="routePortocol" label="{{'路由协议：'}}">
                            <s-radio-radio-group
                                value="{=formData.routePortocol=}"
                                on-change="handleRouteProtocolChange"
                            >
                                <s-radio
                                    s-for="item in routePortocol"
                                    label="{{item.text}}"
                                    value="{{item.value}}"
                                    disabled="{{isReCreate || type === 'editIpv6'}}"
                                ></s-radio>
                            </s-radio-radio-group>
                            <!--bca-disable-next-line-->
                            <p class="channel-protoco-tip" s-if="isShowRouteTip" s-html="bgpRouteTipText"></p>
                        </s-form-item>
                        <template s-if="formData.routePortocol === 'bgp'">
                            <s-form-item
                                prop="bgpAsn"
                                label="BGP ASN："
                                help="有效范围：1 - 4294967295，{{projectName}}ASN:45085。"
                            >
                                <s-input
                                    value="{=formData.bgpAsn=}"
                                    placeholder="{{'请输入用户侧BGP ASN'}}"
                                    disabled="{{isReCreate || type === 'editIpv6'}}"
                                />
                            </s-form-item>
                            <s-form-item prop="fakeAsOn" label="{{'Fake ASN功能：'}}">
                                <s-switch checked="{=fakeAsOn=}"></s-switch>
                                <div s-if="{{row.fakeAsn.length > 0 && !fakeAsOn}}" class="fakeAs-tip">
                                    该操作可能会造成当前业务断流，请确认。
                                </div>
                            </s-form-item>
                            <s-form-item
                                prop="fakeAsn"
                                label="Fake ASN："
                                s-if="{{fakeAsOn}}"
                                help="有效范围：1 - 4294967295，不允许输入45085，且不能与BGP ASN相同。"
                            >
                                <s-input value="{=formData.fakeAsn=}" placeholder="{{'请输入Fake ASN'}}" />
                            </s-form-item>
                            <s-form-item
                                s-if="{{!isReCreate}}"
                                prop="bgpKey"
                                label="{{'BGP 密钥：'}}"
                                help="8～80位字符，英文、数字和符号必须同时存在，符号仅限!@#$%*()_."
                            >
                                <s-input width="318" value="{=formData.bgpKey=}" placeholder="{{'请输入密钥'}}" />
                            </s-form-item>
                        </template>
                        <template s-else> </template>
                        <s-form-item prop="description" label="{{'描述：'}}">
                            <s-input-text-area
                                disabled="{{type === 'editIpv6'}}"
                                value="{=formData.description=}"
                                width="{{320}}"
                                height="{{60}}"
                            />
                        </s-form-item>
                    </div>
                    <div class="body-part-content form-part-wrap tag-wrapper" s-if="!isOtherPay">
                        <h4 class="wrapper-title">标签</h4>
                        <s-form-item prop="tag" label="绑定标签：">
                            <tag-edit-panel
                                instances="{{defaultInstances}}"
                                options="{{tagListRequster}}"
                                s-ref="tagPanel"
                            />
                        </s-form-item>
                    </div>
                </s-form>
            </div>
            <div s-else class="order-confirm-panel">
                <order-confirm
                    s-ref="orderConfirm"
                    mergeBy="{{false}}"
                    couponMergeBy="{{false}}"
                    sdk="{{billingSDK}}"
                    theme="default"
                    useCoupon="{{false}}"
                    showAgreementCheckbox
                />
            </div>
            <div class="buybucket {{stepIndex === 1 ? 'bucket-footer-wrap' : 'bucket-footer-wrap'}}" slot="pageFooter">
                <div class="buybucket-container" s-if="stepIndex === 0">
                    <s-tooltip trigger="{{vlan0Message || accountBalanceCheck ? 'hover' : ''}}">
                        <div slot="content">
                            <!--bca-disable-next-line-->
                            {{vlan0Message || accountBalanceCheck | raw}}
                        </div>
                        <s-button
                            on-click="goToConfirm"
                            skin="primary"
                            size="large"
                            disabled="{{(type === 'editIpv6' || isReCreate) ? (disabled || confirmLoading) : accountBalanceCheck}}"
                        >
                            {{nextText}}
                        </s-button>
                    </s-tooltip>
                    <s-button size="large" on-click="backToList">取消</s-button>
                    <shopping-cart
                        s-if="!isReCreate"
                        sdk="{{billingSDK}}"
                        on-reset="onReset"
                        on-change="onShoppingCartChange"
                        addItemToCartAvailable="{{addItemToCartAvailable}}"
                        addItemToCartDisable="{=priceLoading=}"
                        couponMergeBy="{{false}}"
                        mergeBy="{{false}}"
                        theme="default"
                        class="basic-spc"
                    />
                </div>
                <div class="buybucket-container confirm-wrapper" s-else>
                    <billing-protocol s-if="!FLAG.NetworkSupportXS" s-ref="billingProtocol" />
                    <div class="confirm-footer-wrap">
                        <s-button s-if="!isAccept" on-click="backToOrder" size="large">{{'上一步'}}</s-button>
                        <s-button size="large" on-click="backToList">取消</s-button>
                        <s-button skin="primary" size="large" on-click="submitOrder" disabled="{{confirming}}"
                            >{{'提交订单'}}</s-button
                        >
                        <total-price class="total-price-wrap" sdk="{{billingSDK}}" />
                    </div>
                </div>
            </div>
        </s-app-create-page>
    </template>
`;

@template(tpl)
@asComponent('@et-channel-create')
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class EtChannelCreate extends Component {
    static components = {
        'total-price': TotalPrice,
        'billing-protocol': Protocol,
        'shopping-cart': ShoppingCart,
        'order-confirm': OrderConfirm,
        'tag-edit-panel': TagEditPanel
    };
    static computed = {
        localIpErr() {
            const localIpErrInfo = this.data.get('localIpErrInfo');
            for (let i = 0; i < localIpErrInfo.length; i++) {
                if (localIpErrInfo[i] && i < 4) {
                    return localIpErrInfo[i];
                }
            }
        },
        localIpEnable() {
            const localIp = this.data.get('formData.localIp');
            const localIpErrInfo = this.data.get('localIpErrInfo');
            if (
                localIp[0] &&
                localIp[1] &&
                localIp[2] &&
                localIp[3] &&
                !localIpErrInfo[0] &&
                !localIpErrInfo[1] &&
                !localIpErrInfo[2] &&
                (!localIpErrInfo[3] || localIpErrInfo[3] === rule.CHANNEL.IP_H.repeatMessage)
            ) {
                return true;
            } else {
                return false;
            }
        },
        showPayer() {
            return !(this.data.get('formData.onlySelfAuthorized') === 'self');
        },
        isOtherPay() {
            const {payer, onlySelfAuthorized} = this.data.get('formData');
            return onlySelfAuthorized === 'others' && payer === 'recipient';
        },
        type() {
            return this.data.get('passedParams')?.type;
        },
        pageHeaderTitle() {
            const passedParams = this.data.get('passedParams');
            const isIpv6Edit = !!passedParams?.type;
            const isReCreate = !!passedParams?.channelInstance;
            const stepIndex = this.data.get('stepIndex');
            const isAccept = this.data.get('isAccept');
            const pageStepTitle = this.data.get('pageStepTitle');
            return isAccept
                ? '确认订单'
                : isReCreate
                  ? isIpv6Edit
                      ? '编辑IPV6参数'
                      : '编辑专线通道'
                  : pageStepTitle[stepIndex];
        },
        nextText() {
            const isOtherPay = this.data.get('isOtherPay');
            const isReCreate = this.data.get('isReCreate');
            return isOtherPay || isReCreate ? '确定' : '下一步';
        },
        isReCreate() {
            return !!this.data.get('passedParams')?.channelInstance;
        },
        isReSubmit() {
            const passedParams = this.data.get('passedParams');
            return !!(passedParams?.channelInstance && passedParams?.status);
        },
        disableFlag() {
            const isReSubmit = this.data.get('isReSubmit');
            const isPayer = this.data.get('isPayer');
            if (isReSubmit) {
                if (isPayer) {
                    return false;
                } else {
                    return true;
                }
            }
            return false;
        },
        accountBalanceCheck() {
            const accountValidation = this.data.get('accountBalanceVali');
            if (accountValidation && !accountValidation.status) {
                return accountValidation.failReason
                    ? accountValidation.failReason +
                          '，请及时<a href="/finance/#/finance/account/recharge" target="_blank">充值</a>'
                    : '';
            }
            return '';
        },
        isShowRouteTip() {
            const routeProtocol = this.data.get('formData.routePortocol');
            return routeProtocol === 'bgp';
        },
        bgpRouteTipText() {
            return '若用户端发送BGP路由超过默认100条时，请先到<a target="_blank" href="/quota_center/#/quota/apply/create?serviceType=ET&region=global&name=channelBgpRouteLimit">配额申请页</a><br />调整配额后再创建专线通道，并在专线通道列表页修改"BGP路由条目上限"生效。';
        }
    };

    initData() {
        return {
            FLAG,
            projectName: ContextService.ProjectName,
            klass: 'dc-channel-new',
            pageTitle: {
                backTo: '/network/#/dc/channel/list',
                label: '返回专线通道列表页',
                title: '申请专线通道'
            },
            pageStepTitle: ['申请专线通道', '确认订单'],
            stepIndex: 0,
            steps: [
                {title: '通道配置', key: 'SELECT_CONFIG'},
                {title: '确认订单', key: 'ORDER'}
            ],
            billingSDK: {},
            buyBucketItems: [],
            passedParams: {},
            isAccept: false,
            acceptChannelRow: {},
            priceLoading: true,
            addItemToCartAvailable: false,
            editedRow: {},
            disabled: false,
            formData: {
                onlySelfAuthorized: 'self',
                payer: 'creator',
                routePortocol: 'static-route',
                vlanId: 2,
                localIp: [],
                remoteIp: [],
                maskCode: 24,
                bgpKey: '',
                bgpAsn: '',
                localIpv6: '',
                remoteIpv6: '',
                enableIpv6: false,
                managingUser: 'manage',
                fakeAsn: ''
            },
            rules: validateRules(this),
            authorizedUsersRadio: AuthorizedUsersType.toArray(),
            authDistributionRadio: AuthDistributionType.toArray(),
            channelPayer: ET_CHANNEL_PAYER.toArray(),
            routePortocol: RouteParamsType.toArray(),
            localIpSource: [],
            maskDatasource: [],
            localIpErrInfo: [],
            networksInfo: [],
            ipv6ListInfo: [],
            ipv6Show: false,
            checkAccountErr: '',
            vlan0Message: '',
            remoteIpErr: '',
            confirmLoading: false,
            DocService,
            fakeAsOn: false,
            isFakeAsOn: false,
            changeCount: 0,
            isPayer: false,
            dcphyRegion: '',
            defaultInstances: [
                {
                    tags: [
                        {
                            tagKey: '默认项目',
                            tagValue: ''
                        }
                    ]
                }
            ],
            tagListRequster: this.tagListRequster.bind(this),
            accountBalanceVali: {},
            urlQuery: parseQuery(location.hash)
        };
    }

    inited() {
        this.watch('fakeAsOn', value => {
            this.data.set('formData.fakeAsn', '');
        });
        const acceptParams = JSON.parse(sessionStorage.getItem('ACCEPT_PARAMS'));
        const isAccept = acceptParams?.isAccept || false;
        this.data.set('isAccept', isAccept);
        this.data.set('acceptChannelRow', acceptParams?.row);

        const passedParams = JSON.parse(sessionStorage.getItem('EDIT_CHANNEL_PARAMS')) || {};
        this.data.set('passedParams', passedParams);
        // 重新提交、IPv6编辑无需询价
        if (!passedParams?.channelInstance || isAccept) {
            const client = new Client({}, ({} as any).$context);
            const billingSDK = new BillingSdk({
                client,
                AllRegion: (window as any).$context.getEnum('AllRegion'),
                context: contextPipe(this)
            });
            this.data.set('billingSDK', billingSDK);
            // configDetail 动态展示详情
            this.watchConfigDetail();
            // 跨账号接受直接确认订单
            if (isAccept) {
                this.data.set('dcphyRegion', acceptParams?.row?.region);
                this.loadPrice();
                this.data.set('stepIndex', -1);
            } else {
                // 只有创建时才检查余额
                this.checkAccountBalance();
            }
        }
        this.data.set('formData', this.initFormData());
        !isAccept && this.getDcList();
        this.getIpv6White();
    }

    watchConfigDetail() {
        ['formData.name', 'formData.onlySelfAuthorized', 'formData.dcphyId', 'formData.authorizedUsers'].forEach(
            item => {
                this.watch(item, value => {
                    value && this.updateConfigDetail();
                });
            }
        );
    }

    handleRouteProtocolChange() {
        this.data.set('fakeAsOn', false);
        this.data.set('formData.fakeAsn', '');
    }

    disposed() {
        ['ACCEPT_PARAMS', 'EDIT_CHANNEL_PARAMS'].forEach(key => {
            sessionStorage.removeItem(key);
        });
    }
    // 询价
    loadPrice() {
        const {billingSDK} = this.data.get('');
        billingSDK.clearItems();
        const bucketItems = [];
        const billingConfig = this.getBillingConfig();
        const billingConfigItem = new OrderItem(billingConfig);
        bucketItems.push(billingConfigItem);

        this.data.set('buyBucketItems', bucketItems);
        billingSDK.addItems(bucketItems);
    }
    // 更新配置
    updateConfigDetail() {
        const bucketItems = this.data.get('buyBucketItems');
        const configDetail = this.getConfigDetail();
        bucketItems[0]?.updateConfigDetail(configDetail);
    }

    // 根据region id获取地域
    getRegionTextFromValue(region: string) {
        return (window as any).$context.getEnum('AllRegion')?.getTextFromValue(region);
    }

    // 获取配置
    getConfigDetail() {
        const {formData, isAccept} = this.data.get('');
        const isOthers = formData.onlySelfAuthorized === 'others';
        const dcphyRegion = this.data.get('dcphyRegion');
        const regionText: string = this.getRegionTextFromValue(dcphyRegion);
        let configDetail = [
            {label: '通道名称', value: formData.name},
            {label: '通道所在物理专线ID', value: formData.dcphyId},
            {label: '通道所属物理专线地域', value: regionText},
            {label: '分配对象', value: isOthers ? '其他账户' : '本账户'}
        ];
        if (isOthers) {
            configDetail.push({label: '分配账号', value: formData.authorizedUsers});
        }
        // 跨账号确认订单
        if (isAccept) {
            const acceptChannelRow = this.data.get('acceptChannelRow');
            const {id, name, dcphyId} = acceptChannelRow;
            configDetail = [
                {label: '专线通道ID', value: id},
                {label: '通道名称', value: name},
                {label: '通道所在物理专线ID', value: dcphyId},
                {label: '通道所属物理专线地域', value: regionText},
                {label: '分配对象', value: '跨账号'}
            ];
        }
        return configDetail;
    }

    getBillingConfig() {
        const currentRegionId = this.data.get('dcphyRegion');
        const isAbroad = ['hk', 'hkg', 'hk02'].includes(currentRegionId);
        const chargeItem = isAbroad ? 'et_outbound_nettraffic_nonmainland' : 'et_outbound_nettraffic_mainland';
        const config = {
            amount: 1073741824,
            serviceType: 'ET',
            serviceName: '出方向流量费',
            configName: '出方向流量费',
            productType: 'postpay',
            chargeItem,
            chargeItemName: chargeItem,
            subServiceType: 'default',
            region: currentRegionId,
            scene: 'NEW',
            flavor: [],
            usage: {
                usageAmount: '1Gi'
            },
            count: 1,
            timeUnit: 'MONTH',
            unitText: 'GB',
            billingQuery: false,
            processedFlag: false,
            configDetail: this.getConfigDetail()
        };
        return config;
    }
    getDcList() {
        this.data.set('confirmLoading', true);
        return this.$http
            .getDcList({
                pageNo: 1,
                pageSize: 10000
            })
            .then((res: any) => {
                const dcList =
                    res.result.map(item => {
                        // 可用但是在未完成演练任务中，需要禁止掉
                        const isInDrillTask = item?.status === InstanceStatus.ACTIVE && item?.inDrillTask;
                        let errorTip = '';
                        if (item.status !== InstanceStatus.ACTIVE) {
                            errorTip = '该物理专线' + InstanceStatus.fromValue(item.status).errorStatusTip;
                        } else if (isInDrillTask) {
                            errorTip = '该物理专线正在进行故障演练';
                        }
                        return {
                            text: `${item.name}：${item.id}`,
                            value: item.id,
                            region: item.region,
                            isCreateDisabled: item.status !== InstanceStatus.ACTIVE || isInDrillTask,
                            errorTip
                        };
                    }) || [];
                this.data.set('dcphyList', dcList);
                this.setInitData(dcList);
            });
    }
    getIpv6White() {
        const whiteList = window.$storage.get('commonWhite');
        this.data.set('ipv6Show', whiteList?.DedicatedConnIpv6);
    }
    checkAccountBalance() {
        this.$http
            .purchaseValidation({serviceType: 'ET', productType: 'postpay'}, kXhrOptions)
            .then(res => this.data.set('accountBalanceVali', res));
    }
    shouldLoadPrice(dcphyList: Array<any>, dcphyId: string) {
        const isAccept = this.data.get('isAccept');
        const passedParams = this.data.get('passedParams');
        if (!passedParams?.channelInstance || isAccept) {
            const dcItem = u.find(dcphyList, (item: any) => item.value === dcphyId);
            if (dcItem?.region) {
                this.data.set('dcphyRegion', dcItem.region);
                this.loadPrice();
            }
        }
    }
    async setInitData(dcList: Array<any>) {
        const passedParams = this.data.get('passedParams');
        const dcphyId = passedParams?.dcphyId || this.data.get('urlQuery').dcphyId;
        const channelId = passedParams?.channelInstance;
        if (dcphyId) {
            this.shouldLoadPrice(dcList, dcphyId);
            let item = dcList.find(item => item.value === dcphyId);
            if ((!item?.isCreateDisabled || channelId) && this.data.get('type') !== 'editIpv6') {
                this.data.set('formData.dcphyId', dcphyId);
                this.getDelicatedLineChannel(dcphyId);
            }
        }
        this.data.set('maskDatasource', this.maskDatasource());
        for (let i = 0; i < 4; i++) {
            this.data.push('formData.localIp', '');
            this.data.push('formData.remoteIp', '');
        }
        if (channelId) {
            const payload = {
                keyword: channelId,
                keywordType: 'id',
                creator: passedParams?.creator,
                pageNo: 1,
                pageSize: 10
            };
            const res = await this.$http.channelList(payload);
            const data = res?.result?.[0];
            const existFakeAsOn = data.fakeAsn && data.fakeAsn.length > 0;
            this.data.set('editedRow', data);
            this.data.set('fakeAsOn', existFakeAsOn);
            this.data.set('isFakeAsOn', existFakeAsOn);
            // 重新提交修改参数
            const formData = {
                dcphyId: data.dcphyId,
                authorizedUsers: data.authorizedUsers[0] || '',
                bgpAsn: data.bgpAsn,
                bgpKey: data.bgpKey,
                description: data.description,
                localIp: data.localIp.split('/')[0].split('.'),
                name: data.name,
                enableIpv6: data.enableIpv6 || false,
                localIpv6: data.localIpv6 || '',
                remoteIpv6: data.remoteIpv6 || '',
                onlySelfAuthorized: data.onlySelfAuthorized ? 'self' : 'others',
                remoteIp: data.remoteIp.split('/')[0].split('.'),
                routePortocol: data.routeType,
                status: data.status,
                vlanId: data.vlanId,
                maskCode: data.localIp.split('/')[1],
                managingUser: data.assignManager ? 'manage' : 'use',
                fakeAsn: data.fakeAsn,
                payer: data.payer ? 'creator' : 'recipient'
            };
            this.data.set('isPayer', data?.payer);
            // 直接设置formData不生效
            Object.keys(formData).forEach(item => {
                this.data.set(`formData.${item}`, formData[item]);
            });
        }
        this.data.set('confirmLoading', false);
    }
    // 下一步或确定
    async goToConfirm() {
        let form = this.ref('form');
        const isOtherPay = this.data.get('isOtherPay');
        await (form as any).validateFields();
        if (!isOtherPay) {
            try {
                await this.ref('tagPanel').validate(false);
            } catch (error) {
                return;
            }
        }
        if (!isOtherPay) {
            let tags = await this.ref('tagPanel').getTags();
            this.data.set('tagResource', tags);
        }
        this.validataEmpty();
        if (this.data.get('localIpErrInfo').join('') !== '') {
            return;
        }
        const {formData} = this.data.get('');
        if (formData.onlySelfAuthorized === 'others' && this.data.get('checkAccountErr') !== '') {
            return;
        }
        const isReCreate = this.data.get('isReCreate');

        if (isReCreate) {
            // 重新提交、IPv6编辑
            this.onConfirm();
        } else if (isOtherPay) {
            // 其他账户付费
            this.onConfirm(true);
        } else {
            // 创建者付费
            const bucketItems = this.data.get('buyBucketItems');
            const {billingSDK} = this.data.get('');
            billingSDK.clearItems();
            const configDetail = this.getConfigDetail();
            bucketItems[0].updateConfigDetail(configDetail);
            billingSDK.addItems(bucketItems);
            this.data.set('stepIndex', 1);
        }
    }

    // 出方向流量费提交订单
    async submitOrder() {
        !FLAG.NetworkSupportXS && (await this.ref('billingProtocol').validateAgreement());
        const bucketItems = this.data.get('buyBucketItems');
        const isReCreate = this.data.get('isReCreate');
        const {formData, billingSDK, isAccept, acceptChannelRow, isOtherPay} = this.data.get('');
        const payload: Record<string, any> = {
            dcphyId: formData.dcphyId,
            description: formData.description,
            localIp: formData.localIp.join('.') + '/' + formData.maskCode,
            name: formData.name,
            remoteIp: formData.remoteIp.join('.') + '/' + formData.maskCode,
            routePortocol: [formData.routePortocol],
            routeType: formData.routePortocol,
            vlanId: formData.vlanId
        };

        if (this.data.get('ipv6Show')) {
            payload.enableIpv6 = formData.enableIpv6 ? 1 : 0;
        }
        if (formData.enableIpv6) {
            payload.localIpv6 = formData.localIpv6;
            payload.remoteIpv6 = formData.remoteIpv6;
        }
        if (payload.routeType === 'bgp') {
            const isFakeAsOn = this.data.get('isFakeAsOn');
            payload.bgpAsn = formData.bgpAsn;
            !isReCreate && (payload.bgpKey = formData.bgpKey);
            // 关闭fake as时 之前没有开启过则传null，已开启则传""
            payload.fakeAsn = this.data.get('fakeAsOn')
                ? formData.fakeAsn
                : !isReCreate
                  ? null
                  : isFakeAsOn
                    ? ''
                    : null;
        } else {
            if (this.data.get('networksInfo').join('') !== '') {
                return;
            }
            if (formData.enableIpv6) {
                if (this.data.get('ipv6ListInfo').join('') !== '') {
                    return;
                }
            }
        }
        if (formData.onlySelfAuthorized === 'others') {
            payload.authorizedUsers = [formData.authorizedUsers];
            if (formData.managingUser === 'use') {
                payload.managingUser = getUserId();
            } else {
                payload.managingUser = '';
            }
        }
        if (!isOtherPay) {
            payload.tags = this.data.get('tagResource');
        }
        let params = {
            paymentMethod: [],
            items: [
                {
                    config: payload,
                    paymentMethod: bucketItems[0].couponId ? [{type: 'coupon', values: bucketItems[0].couponId}] : []
                }
            ]
        };

        if (isAccept) {
            params = {
                paymentMethod: [],
                items: [
                    {
                        config: {channelId: acceptChannelRow?.id},
                        paymentMethod: bucketItems[0].couponId
                            ? [{type: 'coupon', values: bucketItems[0].couponId}]
                            : []
                    }
                ]
            };
        }
        this.data.set('confirming', true);
        try {
            const dcphyRegion = this.data.get('dcphyRegion');
            const data = await this.$http.newConfirmOrder('/api/network/v1/dc/channel/order/confirm/new', params, {
                headers: {region: dcphyRegion}
            });
            let url = '';
            try {
                const info = await billingSDK.checkPayInfo(data);
                url = info.url + '&fromService=CHANNEL';
                info.url && (location.href = url);
            } catch (info) {
                // 跳转到相应页面
                url = info.url + '&fromService=CHANNEL';
                info.url && (location.href = url);
            }
        } catch (err) {}
        this.data.set('confirming', false);
    }

    // 上一步
    backToOrder() {
        this.data.set('stepIndex', 0);
        let tags = this.data.get('tagResource');
        this.data.set('defaultInstances', [{tags: tags}]);
    }

    // 返回列表
    backToList() {
        location.hash = '#/dc/channel/list';
    }

    maskDatasource() {
        let masks = [];
        for (let i = 23; i < MASK_RANGE; i++) {
            masks.push({label: i + 1, value: i + 1});
        }
        return masks;
    }

    checkAccount() {
        const accountIds = this.data.get('formData.authorizedUsers');
        if (!accountIds) {
            return;
        }
        this.$http
            .checkAccount({
                accountIds: [accountIds]
            })
            .then(result => {
                this.data.set('checkAccountErr', result.value ? '' : '账户ID不合法');
            });
    }

    /**
     * 计算网段内可用ip
     * @param {*} cidr
     */
    calAvaliableIp(cidr: string) {
        this.nextTick(() => {
            if (!this.data.get('localIpEnable')) {
                return;
            }
            let mask = cidr.split('/')[1];
            const binary = convertCidrToBinary(cidr);
            // 最后8位
            const last = binary.slice(24, 32);
            let num = 32 - mask;
            // 网络位取0
            let zero = '';
            for (let i = 0; i < num; i++) {
                zero = zero + '0';
            }
            // 广播位取1
            let broad = '';
            for (let i = 0; i < num; i++) {
                broad = broad + '1';
            }
            // 网络
            let net = last.slice(0, mask - 24) + zero;
            // 广播
            let host = last.slice(0, mask - 24) + broad;
            // 转10进制，掩码为31时特殊处理
            let start, end;
            if (mask === '31') {
                start = parseInt(net, 2);
                end = parseInt(host, 2);
            } else {
                start = parseInt(net, 2) + 1;
                end = parseInt(host, 2) - 1;
            }
            let range = end ? `${start} - ${end}` : start;
            this.data.set('remoteRange', range);
        });
    }

    maskChange(type: 'local' | 'remote') {
        this.nextTick(() => {
            let mask = this.data.get('formData.maskCode');
            // 掩码31时去掉输入0的报错提示
            if (
                mask === 31 &&
                this.data.get('formData.localIp[3]') === '0' &&
                this.data.get('formData.remoteIp[3]') !== '0'
            ) {
                this.data.set(`localIpErrInfo[${3}]`, '');
            }
            if (
                mask === 31 &&
                this.data.get('formData.localIp[3]') !== '0' &&
                this.data.get('formData.remoteIp[3]') === '0'
            ) {
                this.data.set(`localIpErrInfo[${4}]`, '');
            }
            let ip = this.data.get('formData.localIp');
            let cidr = `${ip[0]}.${ip[1]}.${ip[2]}.${ip[3]}/${mask}`;
            this.calAvaliableIp(cidr);
            this.checkIPv4FirstAdd(mask, type);
        });
    }

    ipInput({value}: {value: string}, key: number, type: string) {
        const CONFIG_MAP = {
            0: rule.CHANNEL.IP_H,
            1: rule.CHANNEL.IP_M,
            2: rule.CHANNEL.IP_M,
            3: rule.CHANNEL.IP_H,
            4: rule.CHANNEL.IP_H
        };
        let mask = this.data.get('formData.maskCode');
        if (value === '') {
            this.data.set(`localIpErrInfo[${key}]`, CONFIG_MAP[key].requiredErrorMessage);
            this.ref(`ipInput${key}`) && this.ref(`ipInput${key}`).focus();
            return;
        }
        if (!CONFIG_MAP[key].custom(value, mask)) {
            this.data.set(`localIpErrInfo[${key}]`, CONFIG_MAP[key].customErrorMessage);
            this.ref(`ipInput${key}`) && this.ref(`ipInput${key}`).focus();
            return;
        }
        if (key === 3) {
            // 校验互联ip是否在范围内和是否重复
            if (value === this.data.get('formData.remoteIp')[3]) {
                this.data.set(`localIpErrInfo[${key}]`, CONFIG_MAP[key].repeatMessage);
                this.ref(`ipInput${key}`) && this.ref(`ipInput${key}`).focus();
                return;
            } else {
                this.data.set(`localIpErrInfo[4]`, '');
            }
            this.nextTick(() => {
                let ip = this.data.get('formData.localIp');
                let cidr = `${ip[0]}.${ip[1]}.${ip[2]}.${ip[key]}/${mask}`;
                this.calAvaliableIp(cidr);
            });
        } else if (key === 4) {
            // 校验互联ip是否在范围内和是否重复
            if (value === this.data.get('formData.localIp')[3]) {
                this.data.set(`localIpErrInfo[${key}]`, CONFIG_MAP[key].repeatMessage);
                this.ref(`ipInput${key}`) && this.ref(`ipInput${key}`).focus();
                return;
            } else {
                this.data.set(`localIpErrInfo[3]`, '');
            }
            let range = this.data.get('remoteRange');
            // 检验是否在输入范围内
            if (range) {
                const rangeArr = range.split('-');
                if (rangeArr.length > 1) {
                    let min = Number(rangeArr[0]);
                    let max = Number(rangeArr[1]);
                    if (Number(value) < min || Number(value) > max) {
                        this.data.set(`localIpErrInfo[${key}]`, CONFIG_MAP[key].rangeMessage);
                        this.ref(`ipInput${key}`) && this.ref(`ipInput${key}`).focus();
                        return;
                    }
                } else {
                    let min = Number(rangeArr[0]);
                    if (Number(value) !== min) {
                        this.data.set(`localIpErrInfo[${key}]`, CONFIG_MAP[key].rangeMessage);
                        this.ref(`ipInput${key}`) && this.ref(`ipInput${key}`).focus();
                        return;
                    }
                }
            }
        } else if (value.length === 3) {
            this.ref(`ipInput${key + 1}`) && this.ref(`ipInput${key + 1}`).focus();
        }
        // 对于掩码长度小于 31 的 IPv4 地址,不能使用网段的第一个地址
        this.nextTick(() => {
            this.checkIPv4FirstAdd(mask, type);
        });
        this.data.set(`localIpErrInfo[${key}]`, '');
        key < 3 && this.setRemoteIp(key, value);
    }

    // 校验是否为ipv4的第一个地址
    checkIPv4FirstAdd(mask: number, type: 'local' | 'remote') {
        if (mask < 31) {
            let firstAdd = this.getLowAddr(this.data.get(`formData.${type}Ip`).join('.'), mask).split('.')[3];
            if (this.data.get(`formData.${type}Ip`)[3] === firstAdd) {
                this.data.set(`${type}IpFirstErr`, '不能使用网段的第一个地址');
            } else {
                this.data.set(`${type}IpFirstErr`, '');
            }
        } else {
            this.data.set(`${type}IpFirstErr`, '');
        }
    }

    setRemoteIp(index: number, value: string) {
        this.data.set(`formData.remoteIp[${index}]`, value);
    }

    networksInput({value}: {value: string}, index: number) {
        const result = this.inputValid(rule.CHANNEL.NETWORK, value);
        this.data.set(`networksInfo[${index}]`, result);
    }

    inputValid(r: any, value: string) {
        if (r === undefined) {
            return '';
        }

        let result = '';
        if (r.required) {
            value === '' && (result = r.requiredErrorMessage);
        }
        if (r.pattern) {
            !r.pattern.test(value) && (result = r.patternErrorMessage);
        }
        if (r.custom) {
            !r.custom(value) && (result = r.customErrorMessage);
        }
        return result;
    }
    ipv6ListInput({value}: {value: string}, index: number) {
        if (!value) {
            this.data.set(`ipv6ListInfo[${index}]`, '请输入IPV6参数');
        } else if (!rule.IPV6_SEG.test(value)) {
            this.data.set(`ipv6ListInfo[${index}]`, '参数格式不合法');
        } else {
            this.data.set(`ipv6ListInfo[${index}]`, '');
        }
    }
    validataEmpty() {
        const {localIp, remoteIp} = this.data.get('formData');
        localIp.map((value, index) => {
            this.ipInput({value}, index, 'local');
        });
        this.ipInput({value: remoteIp[3]}, 4, 'remote');
    }

    async onConfirm(isOther?: boolean) {
        const form = this.ref('form');
        await (form as any).validateFields();
        this.validataEmpty();
        if (this.data.get('localIpErrInfo').join('') !== '') {
            return;
        }
        const formData = u.cloneDeep(this.data.get('formData'));
        const isReCreate = this.data.get('isReCreate');
        const payload: Record<string, any> = {
            dcphyId: formData.dcphyId,
            description: formData.description,
            localIp: formData.localIp.join('.') + '/' + formData.maskCode,
            name: formData.name,
            remoteIp: formData.remoteIp.join('.') + '/' + formData.maskCode,
            routePortocol: [formData.routePortocol],
            routeType: formData.routePortocol,
            vlanId: formData.vlanId
        };
        if (isOther) {
            payload.paymentUser = formData.authorizedUsers;
        }
        if (this.data.get('ipv6Show')) {
            payload.enableIpv6 = formData.enableIpv6 ? 1 : 0;
        }
        if (formData.enableIpv6) {
            payload.localIpv6 = formData.localIpv6;
            payload.remoteIpv6 = formData.remoteIpv6;
        }
        if (payload.routeType === 'bgp') {
            const isFakeAsOn = this.data.get('isFakeAsOn');
            payload.bgpAsn = formData.bgpAsn;
            !isReCreate && (payload.bgpKey = formData.bgpKey);
            payload.fakeAsn = this.data.get('fakeAsOn')
                ? formData.fakeAsn
                : !isReCreate
                  ? null
                  : isFakeAsOn
                    ? ''
                    : null;
        } else {
            if (this.data.get('networksInfo').join('') !== '') {
                return;
            }
            if (formData.enableIpv6) {
                if (this.data.get('ipv6ListInfo').join('') !== '') {
                    return;
                }
            }
        }
        if (formData.onlySelfAuthorized === 'others') {
            if (this.data.get('checkAccountErr') !== '') {
                return;
            }
            payload.authorizedUsers = [formData.authorizedUsers];
            if (formData.managingUser === 'use') {
                payload.managingUser = getUserId();
            } else {
                payload.managingUser = '';
            }
        }
        this.data.set('disabled', true);
        let request;
        if (isReCreate) {
            payload.channelId = this.data.get('passedParams')?.channelInstance;
            request = this.$http.channelReCreate.bind(this.$http);
        } else {
            request = this.$http.channelCreate.bind(this.$http);
        }
        if (this.data.get('type') === 'editIpv6') {
            if (payload.enableIpv6) {
                let data = {
                    dcphyId: payload.dcphyId,
                    channelId: payload.channelId,
                    localIpv6: payload.localIpv6,
                    remoteIpv6: payload.remoteIpv6
                    // ipv6Networks: payload.ipv6Networks
                };
                if (this.data.get('editedRow').enableIpv6) {
                    this.backToList();
                    return;
                }
                return this.$http
                    .channelAddIpv6(data)
                    .then(() => {
                        this.backToList();
                        this.data.set('disabled', false);
                    })
                    .catch(() => this.data.set('disabled', false));
            } else {
                if (this.data.get('editedRow').enableIpv6) {
                    let data = {
                        dcphyId: payload.dcphyId,
                        channelId: payload.channelId
                    };
                    return this.$http
                        .channelDeleteIpv6(data)
                        .then(() => {
                            this.backToList();
                            this.data.set('disabled', false);
                        })
                        .catch(() => this.data.set('disabled', false));
                } else {
                    this.backToList();
                    return;
                }
            }
        }
        request(payload)
            .then(() => {
                this.backToList();
                this.data.set('disabled', false);
            })
            .catch(() => this.data.set('disabled', false));
    }

    dcphyIdChange(e: any) {
        const dcphyList = this.data.get('dcphyList');
        this.shouldLoadPrice(dcphyList, e.value);
        this.getDelicatedLineChannel(e.value);
    }
    getDelicatedLineChannel(dcphyId: string) {
        return this.$http
            .getDelicatedLineChannel(dcphyId)
            .then((res: any) => {
                if (res.infos.length) {
                    const passedParams = this.data.get('passedParams');
                    if (res.infos.some((item: any) => item.vlanId === 0)) {
                        if (res.infos.length > 1 || !passedParams?.channelInstance) {
                            // 重新提交时允许修改vlanId为0的通道
                            if (!this.data.get('isReSubmit')) {
                                this.data.set('disabled', true);
                                this.data.set(
                                    'vlan0Message',
                                    '如要创建其他专线通道，请先删除vlan0的专线通道才可操作。'
                                );
                                this.data.set('hasNotVLAN0', false);
                            }
                        }
                    } else {
                        if (res.infos.length === 1 && passedParams?.channelInstance) {
                            this.data.set('hasNotVLAN0', false);
                        } else {
                            this.data.set('hasNotVLAN0', true);
                        }
                        this.data.set('disabled', false);
                        this.data.set('vlan0Message', '');
                    }
                }
            })
            .catch(() => {
                this.data.set('table.loading', false);
            });
    }
    getLowAddr(ip: string, netMask: number) {
        let ipArray = ip.split('.');
        let ipBinary =
            ('00000000' + parseInt(ipArray[0], 10).toString(2)).slice(-8) +
            ('00000000' + parseInt(ipArray[1], 10).toString(2)).slice(-8) +
            ('00000000' + parseInt(ipArray[2], 10).toString(2)).slice(-8) +
            ('00000000' + parseInt(ipArray[3], 10).toString(2)).slice(-8);

        let subnetBinary = '1'.repeat(netMask).padEnd(32, '0');
        let networkBinary = '';
        for (let i = 0; i < 32; i++) {
            networkBinary += ipBinary[i] & subnetBinary[i];
        }
        let startAddressBinary = networkBinary.substr(0, 32 - (32 - netMask)) + '0'.repeat(32 - netMask);
        let startAddress =
            parseInt(startAddressBinary.substr(0, 8), 2) +
            '.' +
            parseInt(startAddressBinary.substr(8, 8), 2) +
            '.' +
            parseInt(startAddressBinary.substr(16, 8), 2) +
            '.' +
            parseInt(startAddressBinary.substr(24, 8), 2);
        return startAddress;
    }
    initFormData() {
        const defaultFormData = {
            onlySelfAuthorized: 'self',
            payer: 'creator',
            routePortocol: 'static-route',
            vlanId: 2,
            localIp: [],
            remoteIp: [],
            maskCode: 24,
            bgpKey: '',
            bgpAsn: '',
            localIpv6: '',
            remoteIpv6: '',
            name: '',
            dcphyId: '',
            enableIpv6: false,
            managingUser: 'manage',
            description: '',
            authorizedUsers: ''
        };
        return defaultFormData;
    }
    resetFormData() {
        const defaultFormData = this.initFormData();
        Object.keys(defaultFormData).forEach(item => {
            this.data.set(`formData.${item}`, defaultFormData[item]);
        });
    }
    onReset() {
        this.resetFormData();
        this.loadPrice();
    }
    onShoppingCartChange(e: Event) {
        this.data.set('priceLoading', UPDATE_STATUS[e] !== UPDATE_STATUS.DONE);
    }
    tagListRequster() {
        return this.$http.getSearchTagList({serviceType: [], region: ['global']});
    }
}

export default San2React(Processor.autowireUnCheckCmpt(EtChannelCreate));
