.channel-instance-detail-wrap {
    .s-detail-page-content {
        margin: 16px !important;
    }
    .page-header {
        display: flex;
        align-items: center;
        .instance-name {
            font-size: 16px;
            font-weight: 500;
            color: #151b26;
            padding: 0px 12px 0px 16px;
        }
    }
    .app-tab-page {
        padding: 0;
        border-radius: 6px;
        .s-tabnav {
            height: auto !important;
            align-self: stretch;
            min-height: 600px;
        }
        .skin-accordion-tab {
            border: none;
        }
        .s-tabpane-wrapper {
            flex: 1;
        }
    }
    .instance-not-found-class {
        height: 100%;
    }
    .bui-tab-content {
        padding: 8px 15px;
    }
}
