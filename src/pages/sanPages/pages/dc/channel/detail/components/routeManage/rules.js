/*
 * @Description:
 * @Author: <EMAIL>
 * @Date: 2022-04-15 17:37:31
 */

import {ContextService} from '@/pages/sanPages/common';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';


export default {
  createRoute: [
    {
      required: false,
      message: ''
    },
    {
      custom(data, options = {}) {
        if (options.mainChannel) {
          return {
            disable: true,
            message: '子通道不允许配置路由'
          };
        }
        else if (options.free <= 0) {
          if (FLAG.NetworkSupportXS) {
            return {
              disable: true,
              message: '路由条目已达上限'
            };
          }
          else {
            return {
              disable: true,
              message: `路由条目已达上限，如需更多配额，请提交
                <a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">工单</a>`,
            };
          }
        }
      }
    }
  ],
  deleteRoute: [
    {
      required: true,
      message: '请先选择路由条目'
    },
    {
      custom(data, options = {}) {
        let selectItem = data[0];
        if (selectItem) {
          if (selectItem.routeType === 'bgp' && options.routeType === 'bgp') {
            return {
              disable: true,
              message: 'bgp通道下bgp路由无法删除'
            };
          }
        }
      }
    },
    {
      isSingle: true,
      message: '不支持批量删除'
    }
  ]
};
