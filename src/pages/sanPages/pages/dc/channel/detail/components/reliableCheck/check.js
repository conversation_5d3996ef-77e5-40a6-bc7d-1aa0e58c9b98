/*
 * @Description:
 * @Author: <EMAIL>
 * @Date: 2021-09-23 15:27:24
 */
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {checker} from '@baiducloud/bce-opt-checker';
import {San2React} from '@baidu/bce-react-toolkit';
import {OutlinedPlus} from '@baidu/sui-icon';

import CreateBfd from './bfdDialog';
import rules from '../../../../instance/rules';
import Confirm from '@/pages/sanPages/components/confirm';
import {bfdStatus} from '@/pages/sanPages/common/enum';
import './style.less';

const {asComponent, invokeSUI, invokeSUIBIZ, invokeComp, template, service} = decorators;

const tpl = html`
    <div>
        <div class="bfd-container">
            <h4>可靠性检测</h4>
            <s-alert skin="warning">
                {{'对于已经承载流量的专线通道，在开启BFD功能前请检查两端配置，如BFD会话未建立会造成路由撤销，建议在割接窗口期操作。'}}
            </s-alert>
            <div class="tool-tip">
                <s-tooltip trigger="{{disableCreate ? 'hover' : ''}}" placement="top">
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{disableMessage | raw}}
                    </div>
                    <s-button disabled="{{disableCreate}}" skin="primary" on-click="onCreate">
                        <outlined-plus />{{'创建规则'}}
                    </s-button>
                </s-tooltip>
                <s-tooltip class="button-margin-left" trigger="{{deleteBfd.disable ? 'hover' : ''}}" placement="top">
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{deleteBfd.message | raw}}
                    </div>
                    <s-button on-click="onDelete" disabled="{{deleteBfd.disable}}"> 删除 </s-button>
                </s-tooltip>
            </div>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                datasource="{{table.datasource}}"
                selection="{=table.selection=}"
                on-selected-change="tableSelected($event)"
            >
                <div slot="empty">
                    <s-empty on-click="onCreate"> </s-empty>
                </div>
                <div slot="h-sendInterval">
                    <span>报文发送间隔</span>
                    <s-tip class="inline-tip" content="{{'输入范围为：200-1000间的整数，单位为ms'}}" skin="question" />
                </div>
                <div slot="h-receivInterval">
                    <span>报文接收间隔</span>
                    <s-tip class="inline-tip" content="{{'输入范围为：200-1000间的整数，单位为ms'}}" skin="question" />
                </div>
                <div slot="h-detectMultiplier">
                    <span>检测时间倍数</span>
                    <s-tip class="inline-tip" content="{{'输入范围为：3-10间的整数，单位为ms'}}" skin="question" />
                </div>
                <div slot="c-bfdStatus">
                    <span class="{{row.bfdStatus | statusClass}}">{{row.bfdStatus | statusText}}</span>
                </div>
                <div slot="c-ipv6BfdStatus">
                    <span class="{{row.ipv6BfdStatus | statusClass}}">{{row.ipv6BfdStatus | statusText}}</span>
                </div>
                <div slot="c-opt">
                    <a href="javascript:void(0)" on-click="onEdit(row)">{{'编辑'}}</a>
                </div>
            </s-table>
        </div>
    </div>
`;
@template(tpl)
@invokeComp('@bfd-dialog')
@invokeSUI
@invokeSUIBIZ
@asComponent('@dc-reliable-check')
class ReliableCheck extends Component {
    static components = {
        'outlined-plus': OutlinedPlus
    };
    initData() {
        return {
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                columns: [
                    {name: 'channelId', label: '专线通道ID', width: 350},
                    {name: 'bfdStatus', label: 'IPv4状态'},
                    {name: 'sendInterval', label: '报文发送间隔'},
                    {name: 'receivInterval', label: '报文接收间隔'},
                    {name: 'detectMultiplier', label: '检测时间倍数'},
                    {name: 'opt', label: '操作', fixed: 'right'}
                ],
                datasource: []
            },
            selectedItems: [],
            deleteBfd: {}
        };
    }
    static filters = {
        statusClass(value) {
            return bfdStatus.fromValue(value).kclass || '';
        },
        statusText(value) {
            return value ? bfdStatus.getTextFromValue(value) : '-';
        }
    };
    static computed = {
        disableCreate() {
            return this.data.get('table.datasource')?.length;
        },
        disableMessage() {
            if (this.data.get('table.datasource')?.length) {
                return '一条通道下只能创建一条bfd。';
            }
        }
    };

    inited() {
        this.getIpv6White();
    }

    attached() {
        this.loadPage();
        let {deleteBfd} = checker.check(rules, []);
        this.data.set('deleteBfd', deleteBfd);
    }

    getIpv6White() {
        const whiteList = window.$storage.get('commonWhite');
        this.data.set('ipv6Show', whiteList?.DedicatedConnIpv6);
        whiteList?.DedicatedConnIpv6 &&
            this.data.splice('table.columns', [2, 0, {name: 'ipv6BfdStatus', label: 'IPv6状态'}]);
    }

    loadPage() {
        if (this.data.get('context')?.route?.creator === 'other') {
            this.$http
                .channelDetail(this.data.get('context')?.route?.channelId, {
                    type: 'available'
                })
                .then(data => {
                    this.data.set('channelDetail', data);
                    this.getlist();
                });
        } else {
            this.$http.channelDetail(this.data.get('context')?.route?.channelId).then(data => {
                this.data.set('channelDetail', data);
                this.getlist();
            });
        }
    }

    getlist() {
        // 目前每个通道只能创建一个bfd
        this.data.set('table.selection.selectedIndex', []);
        let {bfdStatus, ipv6BfdStatus, id, detectMultiplier, receivInterval, sendInterval, healthCheckMode} =
            this.data.get('channelDetail');
        // healthCheckMode为close时不展示规则
        if (detectMultiplier > 0 && healthCheckMode !== 'close') {
            this.data.set('table.datasource', [
                {
                    bfdStatus,
                    ipv6BfdStatus,
                    detectMultiplier,
                    receivInterval,
                    sendInterval,
                    channelId: id
                }
            ]);
        } else {
            this.data.set('table.datasource', []);
        }
    }

    tableSelected(e) {
        const {deleteBfd} = checker.check(rules, e.value.selectedItems);
        this.data.set('deleteBfd', deleteBfd);
        this.data.set('selectedItems', e.value.selectedItems);
    }

    onCreate() {
        const dialog = new CreateBfd({
            data: {
                type: 'create',
                dcphyId: this.data.get('context').instanceId,
                channelId: this.data.get('channelDetail.id')
            }
        });
        dialog.on('success', () => {
            this.loadPage();
        });
        dialog.attach(document.body);
    }

    onEdit(row) {
        const dialog = new CreateBfd({
            data: {
                type: 'edit',
                dcphyId: this.data.get('context').instanceId,
                channelId: row.channelId,
                formData: {
                    sendInterval: row.sendInterval,
                    receivInterval: row.receivInterval,
                    detectMultiplier: row.detectMultiplier
                }
            }
        });
        dialog.on('success', () => {
            this.loadPage();
        });
        dialog.attach(document.body);
    }

    onDelete() {
        let confirm = new Confirm({
            data: {
                open: true,
                content: '确认删除选中的规则？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            let dcphyId = this.data.get('context').instanceId;
            let channelId = this.data.get('selectedItems')[0].channelId;
            this.$http.deleteBfd(dcphyId, channelId).then(() => {
                this.loadPage();
            });
        });
    }
}

export default San2React(Processor.autowireUnCheckCmpt(ReliableCheck));
