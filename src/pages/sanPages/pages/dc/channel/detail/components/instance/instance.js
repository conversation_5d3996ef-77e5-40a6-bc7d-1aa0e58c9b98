/*
 * @Description:
 * @Author: <EMAIL>
 * @Date: 2022-04-12 17:41:10
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {Notification} from '@baidu/sui';
import {San2React} from '@baidu/bce-react-toolkit';
import {OutlinedEditingSquare} from '@baidu/sui-icon';

import {InstanceStatus, ChannelRouteBgpStatus} from '@/pages/sanPages/common/enum';
import EditChannel from '../../../list/editChannel';
import EditUsers from '../../../list/editUsers';
import rule from '@/pages/sanPages/utils/rule';
import './style.less';

const {asComponent, invokeComp, invokeSUI, invokeSUIBIZ, invokeAppComp, template} = decorators;

const tpl = html`
<div>
  <div class="{{klass}}">
    <div class="list-content">
      <div class="detail-parts-table">
        <dl class="detail-part-1-col">
          <h4>专线通道详情</h4>
          <dd>
            <div class="detail-cell">
              <label>{{ '物理专线ID：'}}</label>
              <div>
                <span>{{ instance.dcphyId }}</span>
                <s-clip-board text="{{instance.dcphyId}}"><s-icon name="copy" /></s-clipboard>
              </div>
            </div>
            <div class="detail-cell">
              <label>{{ '通道ID：'}}</label>
              <div>
                <span>{{ instance.id }}</span>
                <s-clip-board text="{{instance.id}}"><s-icon name="copy" /></s-clipboard>
              </div>
            </div>
            <div class="detail-cell">
              <label>{{ '通道名称：'}}</label>
              <div>
                <span>{{ instance.name }}</span>
                <s-popover
                  s-if="instance.manager && instance.status !== 'reject' && instance.status !== 'ack-wait'"
                  class="edit-detail-pop"
                  s-ref="nameEdit" placement="top" trigger="click">
                  <div class="edit-wrap {{editTip.name ? 'edit-nonetip' : 'edit-hastip'}}" slot="content">
                    <s-input
                      value="{=editValue.name=}"
                      width="320"
                      placeholder="{{'请输入通道名称'}}"
                      on-input="onInput($event, 'name')" />
                    <div class="edit-tip">{{ editTip.name }}</div>
                    <s-button skin="primary" s-ref="nameEditBtn" disabled="{{true}}"
                      on-click="onEdit('name')">{{ '确定'}}</s-button>
                    <s-button on-click="editCancel('name')">{{ '取消'}}</s-button>
                  </div>
                  <outlined-editing-square class="name-icon" on-click="beforeEdit('name')"/>
                </s-popover>
              </div>
            </div>
          </dd>
          <dd>
            <div class="detail-cell">
              <label>{{ '状态：'}}</label>
              <div>
                <span class="{{instance.status | statusClass('channel')}}">{{instance.status | statusText('channel')}}</span>
              </div>
            </div>
            <div class="detail-cell">
              <label>{{ '分配对象：'}}</label>
              <div>
                <span s-if="instance.onlySelfAuthorized">{{ '本账户'}}</span>
                <span s-else>{{ instance.authorizedUsers[0] }}</span>
                <s-icon
                  s-if="instance.onlySelfAuthorized &&
                    instance.status !== 'reject' && instance.status !== 'ack-wait' && instance.status !== 'established'"
                  class="edit-icon" name="edit"
                  on-click="editUsers(instance)">
                </s-icon>
              </div>
            </div>
            <div class="detail-cell">
              <label>{{ 'VLANID：'}}</label>
              <div>
                <span>{{ instance.vlanId }}</span>
              </div>
            </div>
          </dd>
          <dd>
            <div class="detail-cell">
              <label>{{ '路由协议：'}}</label>
              <div>
                <p>{{(instance.routeType ? (instance.routeType === 'bgp' ? 'BGP' : '静态') : '-')}}</p>
              </div>
            </div>
            <div class="detail-cell">
              <label>{{ 'IPv4 BGP状态：'}}</label>
              <div>
                <span class="{{instance.bgpStatus | statusClass('bgp')}}">{{instance.bgpStatus | statusText('bgp')}}</span>
              </div>
            </div>
            <div s-if="ipv6Show" class="detail-cell">
              <label>{{ 'IPv6 BGP状态：'}}</label>
              <div>
                <span class="{{instance.ipv6BgpStatus | statusClass('bgp')}}">{{instance.ipv6BgpStatus | statusText('bgp')}}</span>
              </div>
            </div>
          </dd>
          <dd>
            <div class="detail-cell">
              <label>{{ 'BGP ASN：'}}</label>
              <div>
                <span class="text-overflow">{{ instance.bgpAsn || '-' }}</span>
              </div>
            </div>
            <div class="detail-cell">
              <label>{{ '描述：'}}</label>
              <div>
                <span>{{ instance.description || '-' }}</span>
                <s-popover
                  s-if="instance.manager && instance.status !== 'reject' && instance.status !== 'ack-wait'"
                  class="edit-detail-pop"
                  s-ref="descriptionEdit" placement="top" trigger="click">
                  <div class="edit-wrap {{editTip.description ? 'edit-nonetip' : 'edit-hastip'}}" slot="content">
                    <s-input
                      value="{=editValue.description=}"
                      width="160"
                      placeholder="请输入描述"
                      on-input="onInput($event, 'description')" />
                    <div class="edit-tip">{{ editTip.description }}</div>
                    <s-button skin="primary" s-ref="descriptionEditBtn" disabled="{{true}}"
                      on-click="onEdit('description')">{{ '确定'}}</s-button>
                    <s-button on-click="editCancel('description')">{{ '取消'}}</s-button>
                  </div>
                  <outlined-editing-square class="name-icon" on-click="beforeEdit('description')"/>
                </s-popover>
              </div>
            </div>
          </dd>
          <dd>
            <div class="detail-cell">
              <label>{{ 'IPV4互联IP：'}}</label>
              <div>
                <p>{{instance.localIp || '-'}}{{'（云端网络）'}}</p>
                <p>{{instance.remoteIp || '-'}}{{'（IDC端）'}}</p>
              </div>
            </div>
             <div class="detail-cell">
              <label>{{ 'IPV6互联IP：'}}</label>
              <div>
                <p>{{instance.localIpv6  || '-'}}{{'（云端网络）'}}</p>
                <p>{{instance.remoteIpv6 || '-'}}{{'（IDC端）'}}</p>
              </div>
            </div>
          </dd>
          <dd>
            <div class="detail-cell">
              <label>{{ '主通道：'}}</label>
              <div>
                <span>{{instance.mainChannel || '-'}}</span>
              </div>
            </div>
            <div class="detail-cell">
              <label>{{ '子通道：'}}</label>
              <div s-if="instance.subChannel">
                <p s-for="value in instance.subChannel">{{value}}</p>
              </div>
              <div s-else>
                <p>-</p>
              </div>
              <s-tooltip s-if="{{instance.manager}}">
                <div slot="content">
                    <!--bca-disable-next-line-->
                    {{(instance | getContent) | raw}}
                </div>
                <outlined-editing-square
                  class="name-icon {{instance | getGray}}"
                  on-click="editChannel(instance)"/>
              </s-tooltip>
            </div>
            <div class="detail-cell" s-if="{{instance.routeType === 'bgp'}}">
              <label>{{ 'Fake ASN：'}}</label>
              <div>
                <span>{{instance.fakeAsn || '-'}}</span>
              </div>
            </div>
          </dd>
        </dl>
      </div>
    </div>
  </div>
</div>
`;

@template(tpl)
@invokeAppComp
@invokeSUI
@invokeSUIBIZ
@asComponent('@dc-channel-detail')
class ChannelDetail extends Component {
    static components = {
        'outlined-editing-square': OutlinedEditingSquare
    };
    initData() {
        return {
            klass: 'dc-channel-wrap',
            instance: {},
            editValue: {},
            editTip: {},
            ipv6Show: false
        };
    }

    static filters = {
        statusClass(value, type) {
            if (type === 'channel') {
                return InstanceStatus.fromValue(value).kclass || '';
            } else {
                return ChannelRouteBgpStatus.fromValue(value).kclass || '';
            }
        },
        statusText(value, type) {
            if (type === 'channel') {
                return value && InstanceStatus.getTextFromValue(value) ? InstanceStatus.getTextFromValue(value) : '-';
            } else {
                return value && ChannelRouteBgpStatus.getTextFromValue(value)
                    ? ChannelRouteBgpStatus.getTextFromValue(value)
                    : '-';
            }
        },
        getContent(value) {
            if (!value.dcgwId) {
                return '请先绑定专线网关后再关联其他专线通道';
            }
            return '';
        },
        getGray(value) {
            if (!value.dcgwId) {
                return 'gray';
            }
            return '';
        }
    };

    inited() {
        this.data.set('instance', this.data.get('context').instance);
        this.subchannelQuota();
        this.getIpv6White();
    }

    getIpv6White() {
        const whiteList = window.$storage.get('commonWhite');
        this.data.set('ipv6Show', whiteList?.DedicatedConnIpv6);
    }

    beforeEdit(type) {
        let instance = this.data.get('context').instance;
        this.data.set('editTip', {});
        this.data.set(`editValue.${type}`, instance[type]);
    }

    inputValid(type, value) {
        let result = '';
        if (type === 'name') {
            const r = rule.DC.NAME;
            if (r === undefined) {
                return false;
            }

            if (r.required) {
                value === '' && (result = r.requiredErrorMessage);
            }
            if (r.pattern) {
                !r.pattern.test(value) && (result = r.patternErrorMessage);
            }
            if (r.custom) {
                !r.custom(value) && (result = r.customErrorMessage);
            }
            return result;
        } else {
            if (value.length >= 200) {
                return '描述不能超过200个字符';
            }
        }
    }

    onEdit(type) {
        const instance = this.data.get('context').instance;
        const value = this.data.get(`editValue.${type}`);
        const TYPE_MPA = {
            name: 'name',
            description: 'description'
        };
        const payload = {
            dcphyId: instance.dcphyId,
            channelId: instance.id,
            [TYPE_MPA[type]]: value
        };
        return this.$http
            .channelUpdate(payload)
            .then(() => {
                Notification.success('修改成功');
                this.editCancel(type);
                this.data.get('context').refresh();
            })
            .catch(() => {
                Notification.error('修改失败，请稍后再试');
                this.editCancel(type);
            });
    }

    onInput(e, type) {
        const result = this.inputValid(type, e.value);
        this.data.set(`editTip.${type}`, result ? result : '');
        this.ref(`${type}EditBtn`).data.set('disabled', !!result);
    }

    editCancel(type) {
        this.ref(`${type}Edit`).data.set('visible', false);
    }

    subchannelQuota() {
        this.$http.commonQuota({quotaType: 'associateChannelQuota', serviceType: 'ET'}).then(res => {
            this.data.set('subChannelNum', res);
        });
    }

    editChannel(row) {
        const edit = new EditChannel({
            data: {
                title: '修改子通道列表',
                dcphyId: row.dcphyId,
                channelId: row.id,
                routeType: row.routeType,
                subChannel: row.subChannel,
                isShow: true,
                subChannelNum: this.data.get('subChannelNum'),
                mainChannel: row.mainChannel,
                dcgwId: row.dcgwId,
                enableIpv6: row.enableIpv6,
                creator: this.data.get('context').creator
            }
        });
        edit.on('created', () => {
            this.data.get('context').refresh();
        });
        if (row.dcgwId) {
            edit.attach(document.body);
        }
    }

    editUsers(row) {
        const confirm = new EditUsers({
            data: {
                title: '添加新分配对象',
                dcphyId: this.data.get('context').dcphyId,
                channelId: row.id,
                row: row
            }
        });
        confirm.on('confirmed', () => {
            this.data.get('context').refresh();
        });
        confirm.attach(document.body);
    }
}

export default San2React(Processor.autowireUnCheckCmpt(ChannelDetail));
