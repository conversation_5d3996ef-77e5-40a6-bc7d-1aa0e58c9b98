import {Component} from 'san';
import _ from 'lodash';
import {html, decorators, Processor} from '@baiducloud/runtime';

import './style.less';

const {asComponent, invokeSUI, template, invokeSUIBIZ} = decorators;

const tpl = html`
    <div>
        <s-dialog
            class="dc-bfd-dialog"
            open="{{true}}"
            title="{{type === 'create' ? '创建可靠性检测' : '编辑可靠性检测'}}"
        >
            <s-form s-ref="form" data="{=formData=}" rules="{{rules}}">
                <s-form-item prop="sendInterval">
                    <template slot="label" class="label_class">
                        {{'报文发送间隔'}}
                        <s-tip
                            class="inline-tip"
                            content="{{'输入范围为：200-1000间的整数，单位为ms，推荐值为300'}}"
                            skin="question"
                        />
                    </template>
                    <s-input width="{{300}}" value="{=formData.sendInterval=}" />
                </s-form-item>
                <s-form-item prop="receivInterval">
                    <template slot="label" class="label_class">
                        {{'报文接收间隔'}}
                        <s-tip
                            class="inline-tip"
                            content="{{'输入范围为：200-1000间的整数，单位为ms，推荐值为300'}}"
                            skin="question"
                        />
                    </template>
                    <s-input width="{{300}}" value="{=formData.receivInterval=}" />
                </s-form-item>
                <s-form-item prop="detectMultiplier">
                    <template slot="label" class="label_class">
                        {{'检测时间倍数'}}
                        <s-tip class="inline-tip" content="{{'输入范围为：3-10间的整数，推荐值为4'}}" skin="question" />
                    </template>
                    <s-input width="{{300}}" value="{=formData.detectMultiplier=}" />
                </s-form-item>
            </s-form>
            <div slot="footer">
                <s-button on-click="onClose">取消</s-button>
                <s-button skin="primary" disabled="{{disableSub}}" on-click="doSubmit">确定</s-button>
            </div>
        </s-dialog>
    </div>
`;
@asComponent('@bfd-dialog')
@template(tpl)
@invokeSUI
@invokeSUIBIZ
class BfdDialog extends Component {
    initData() {
        return {
            type: 'create',
            rules: {
                sendInterval: [
                    {required: true, message: '报文发送间隔必填'},
                    {pattern: /^([2-9][0-9]{2}|1000)$/, message: '取值范围不符合规则'}
                ],
                receivInterval: [
                    {required: true, message: '报文接收间隔必填'},
                    {pattern: /^([2-9][0-9]{2}|1000)$/, message: '取值范围不符合规则'}
                ],
                detectMultiplier: [
                    {required: true, message: '检测时间倍数必填'},
                    {pattern: /^([3-9]|10)$/, message: '取值范围不符合规则'}
                ]
            },
            formData: {
                sendInterval: '300',
                receivInterval: '300',
                detectMultiplier: '4'
            },
            disableSub: false
        };
    }

    async doSubmit() {
        await this.ref('form').validateFields();
        this.data.set('disableSub', true);
        let dcphyId = this.data.get('dcphyId');
        let channelId = this.data.get('channelId');
        let cloneForm = _.cloneDeep(this.data.get('formData'));
        let actionType = this.data.get('type') === 'create' ? 'createBfd' : 'editBfd';
        try {
            await this.$http[actionType](dcphyId, channelId, cloneForm);
            this.fire('success');
            this.onClose();
        } catch {
            this.data.set('disableSub', false);
        }
    }

    onClose() {
        this.dispose();
    }
}
export default Processor.autowireUnCheckCmpt(BfdDialog);
