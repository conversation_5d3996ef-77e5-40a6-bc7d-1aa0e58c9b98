/*
 * @Description: 通道路由列表页
 * @Author: <EMAIL>
 * @Date: 2022-04-13 16:57:00
 */

import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {checker} from '@baiducloud/bce-opt-checker';
import {Notification} from '@baidu/sui';
import {San2React} from '@baidu/bce-react-toolkit';
import {OutlinedEditingSquare, OutlinedPlus} from '@baidu/sui-icon';

import {ChannelRouteType, ChannelRouteProtocol} from '@/pages/sanPages/common/enum';
import {utcToTime} from '@/pages/sanPages/utils/helper';
import Confirm from '@/pages/sanPages/components/confirm';
import CreateRoute from '../routeManage/create';
import rules from './rules';
import './style.less';

const {asComponent, invokeSUI, invokeAppComp, invokeSUIBIZ, template, invokeComp} = decorators;

const tpl = html`
    <div>
        <s-app-list-page class="{{klass}}">
            <div slot="bulk">
                <s-tip-button
                    disabled="{{createRoute.disable}}"
                    skin="primary"
                    isDisabledVisibile="{{true}}"
                    on-click="onCreate"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{ createRoute.message | raw }}
                    </div>
                    <outlined-plus />
                    {{ '新建路由条目'}}
                </s-tip-button>
                <s-tooltip class="left_class" trigger="{{deleteRoute.disable ? 'hover' : ''}}" placement="right">
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{deleteRoute.message | raw}}
                    </div>
                    <s-button on-click="onDelete" disabled="{{deleteRoute.disable}}"> 删除</s-button>
                </s-tooltip>
            </div>
            <div slot="filter">
                <div class="dc-buttons-wrap">
                    <search-type s-ref="search" searchbox="{=searchbox=}" on-search="onSearch"> </search-type>
                </div>
            </div>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                on-selected-change="tableSelected($event)"
                selection="{=table.selection=}"
            >
                <div slot="c-ipVersion">
                    <span>{{row.ipVersion | ipType}}</span>
                </div>
                <div slot="c-nexthopType">
                    <span>{{row.nexthopType | nextType}}</span>
                </div>
                <div slot="c-routeType">
                    <span>{{row.routeType || '-'}}</span>
                </div>
                <div slot="c-asPaths">
                    <span>{{row.asPaths || '-'}}</span>
                </div>
                <div slot="h-updatedTime">
                    {{col.label}}
                    <s-tip
                        class="inline-tip"
                        layer-width="200"
                        content="更新时间指百度智能云专线路由器学习用户IDC路由器通过BGP路由协议发送的动态路由条目的时间。"
                        skin="question"
                    />
                </div>
                <div slot="c-updatedTime">
                    <span>{{row | getUpdatedTime}}</span>
                </div>
                <div slot="c-description">
                    <p class="desc-cell">
                        {{ row.description || '-'}}
                        <s-popover
                            class="edit-popover-class edit-popover-desc"
                            s-ref="{{'descEdit' + rowIndex}}"
                            placement="top"
                            trigger="click"
                        >
                            <div class="edit-wrap" slot="content">
                                <s-input
                                    value="{=editInstance=}"
                                    width="160"
                                    placeholder="{{'请输入'}}"
                                    on-input="onEditInput($event,row, rowIndex, 'desc')"
                                />
                                <s-button
                                    skin="primary"
                                    s-ref="{{'descBtn' + rowIndex}}"
                                    disabled="{{true}}"
                                    on-click="onEdit(row)"
                                    >{{ '确定'}}</s-button
                                >
                                <s-button on-click="editCancel(row,rowIndex,'desc')">{{ '取消'}}</s-button>
                            </div>
                            <outlined-editing-square class="name-icon" on-click="beforeEdit(row)" />
                        </s-popover>
                    </p>
                </div>
            </s-table>
            <s-pagination
                s-if="{{pager.total}}"
                slot="pager"
                layout="{{'total, pageSize, pager'}}"
                pageSize="{{pager.pageSize}}"
                total="{{pager.total}}"
                page="{{pager.page}}"
                resetPageWhenSizeChange="{{true}}"
                on-pagerChange="onPagerChange"
            />
        </s-app-list-page>
    </div>
`;

@template(tpl)
@invokeComp('@search-type')
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@asComponent('@dc-channel-route-list')
class ChannelRouteList extends Component {
    static components = {
        'outlined-editing-square': OutlinedEditingSquare,
        'outlined-plus': OutlinedPlus
    };
    initData() {
        return {
            klass: 'channel-route-list',
            table: {
                loading: false,
                datasource: [],
                columns: [
                    {name: 'ipVersion', label: '规则类型', width: '100'},
                    {name: 'destCidr', label: '目标网段', width: '140'},
                    {name: 'nexthopType', label: '下一跳类型', width: '140'},
                    {name: 'nexthop', label: '下一跳实例ID', width: '140'},
                    {name: 'routeType', label: '路由协议', width: '100'},
                    {name: 'asPaths', label: 'As-Path', width: '140'},
                    {name: 'updatedTime', label: '更新时间', width: '120'},
                    {name: 'description', label: '描述', width: '160'}
                ],
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                }
            },
            selectedItems: [],
            searchbox: {
                placeholder: '请输入规则类型进行搜索',
                keyword: 4,
                keywordType: 'ipVersion',
                keywordTypes: [
                    {
                        value: 'ipVersion',
                        text: '规则类型',
                        selectDataSource: [
                            {
                                text: '全部条目',
                                value: ''
                            },
                            {
                                value: 4,
                                text: 'IPv4'
                            },
                            {
                                value: 6,
                                text: 'IPv6'
                            }
                        ]
                    },
                    {value: 'destCidr', text: '目标网段'},
                    {
                        value: 'routeType',
                        text: '路由协议',
                        selectDataSource: ChannelRouteProtocol.toArray().map(item => ({
                            value: item.value,
                            text: item.text
                        }))
                    },
                    {
                        value: 'nexthopType',
                        text: '下一跳类型',
                        selectDataSource: ChannelRouteType.toArray().map(item => ({
                            value: item.value,
                            text: item.text
                        }))
                    }
                ]
            },
            createRoute: {
                disable: true,
                message: ''
            },
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            editInstance: {}
        };
    }

    static filters = {
        ipType(value) {
            return value === 4 ? 'IPv4' : 'IPv6';
        },
        nextType(value) {
            return ChannelRouteType.getTextFromValue(value);
        },
        getUpdatedTime(item) {
            return item.putTime ? utcToTime(item.putTime) : '-';
        }
    };

    attached() {
        this.loadPage();
        const {deleteRoute} = checker.check(rules, [], 'deleteRoute', {
            routeType: this.data.get('context').instance.routeType
        });
        this.data.set('deleteRoute', deleteRoute);
    }

    getQuota() {
        this.$http
            .channelRouteQuota({
                dcphyId: this.data.get('context').instance.dcphyId,
                channelId: this.data.get('context').channelId
            })
            .then(data => {
                let {createRoute} = checker.check(rules, '', 'createRoute', {
                    onlySelfAuthorized: this.data.get('context').instance.onlySelfAuthorized,
                    free: data.free,
                    mainChannel: this.data.get('context').instance.mainChannel,
                    routeType: this.data.get('context').instance.routeType
                });
                this.data.set('createRoute', createRoute);
            });
    }

    resetTable() {
        this.data.set('table.selection', {
            mode: 'multi',
            selectedIndex: []
        });
        this.data.set('selectedItems', []);
    }

    getPayload() {
        const {pager} = this.data.get('');
        let payload = {
            pageNo: pager.page,
            pageSize: pager.pageSize,
            keywordType: this.data.get('searchbox.keywordType'),
            keyword: this.data.get('searchbox.keyword'),
            channelId: this.data.get('context').channelId
        };
        return payload;
    }

    loadPage() {
        this.getQuota();
        this.data.set('table.loading', true);
        let payload = this.getPayload();
        this.resetTable();
        return this.$http.getChannelRouteList(payload, this.data.get('context').instance.region).then(data => {
            this.data.set('table.datasource', data.result);
            this.data.set('pager.total', data.totalCount);
            this.data.set('table.loading', false);
        });
    }

    onSearch() {
        this.data.set('pager.page', 1);
        return this.loadPage();
    }

    onDelete() {
        const confirm = new Confirm({
            data: {
                open: true,
                content: '确认删除选中的路由条目？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            let id = this.data.get('selectedItems')[0].id;
            this.$http.deleteChannelRoute(id, this.data.get('context').instance.region).then(() => {
                this.loadPage();
                Notification.success('删除成功', {placement: 'topRight'});
            });
        });
    }

    tableSelected({value}) {
        const {deleteRoute} = checker.check(rules, value.selectedItems, 'deleteRoute', {
            routeType: this.data.get('context').instance.routeType
        });
        this.data.set('deleteRoute', deleteRoute);
        this.data.set('selectedItems', value.selectedItems);
    }

    beforeEdit(row) {
        this.data.set('editInstance', row.description);
    }

    onEditInput(e, row, rowIndex, type) {
        this.data.set('editInstance', e.value);
        this.ref(`${type}Btn${rowIndex}`).data.set('disabled', false);
    }

    onEdit(row) {
        const payload = {
            description: this.data.get('editInstance')
        };
        return this.$http
            .updateChannelRoute(row.id, payload, this.data.get('context').instance.region)
            .then(() => this.loadPage());
    }

    editCancel(row, rowIndex, type) {
        this.ref(`${type}Edit${rowIndex}`).data.set('visible', false);
    }

    onPagerChange({value}) {
        this.data.set('pager.page', value.page);
        this.data.set('pager.pageSize', value.pageSize);
        this.loadPage();
    }

    onCreate() {
        const dialog = new CreateRoute({
            data: {
                channel: this.data.get('context').instance
            }
        });
        dialog.on('success', () => {
            this.loadPage();
        });
        dialog.attach(document.body);
    }
}

export default San2React(Processor.autowireUnCheckCmpt(ChannelRouteList));
