.channel-route-list {
    display: table;
    table-layout: fixed;
    width: 100%;
    .s-list-content {
        background: #fff;
    }
    .table-full-wrap {
        margin: 0 !important;
    }
    .s-table-cell-description {
        .desc-cell {
            word-break: break-all;
        }
    }
    .icon-edit {
        color: #108cee;
        font-size: 12px;
        margin-left: 10px;
    }
    .name-icon {
        position: relative;
        top: -1px;
        fill: #2468f2;
        color: #2468f2;
    }
    .inline-tip {
        top: 3px;
        position: relative;
        .s-tip-warning {
            justify-content: center;
            .warning_class {
                fill: #999;
            }
        }
        .s-tip:hover .s-icon path {
            fill: #2468f2 !important;
        }
    }
}

.channel-route-create {
    .s-form-item-label {
        width: 118px;
    }
    .s-form-item-control > div {
        margin-bottom: 10px;
    }
    .s-radio-button-group {
        .s-radio-text {
            width: 72px;
        }
    }
    .nexthopType-label {
        label {
            max-width: 96px !important;
        }
    }
}
