.dc-channel-wrap {
  .list-content {
    padding: 24px;
    margin: 0;
    .detail-parts-table {
      padding: 0;
      .detail-part-1-col {
        h4 {
          display: block;
          font-size: 16px;
          color: #151b26;
          line-height: 24px;
          font-weight: 500;
          margin-bottom: 16px;
        }
        & > dd {
          display: table;
          table-layout: fixed;
          width: 100%;
          margin: 0 0 20px;
          line-height: 24px;

          .detail-cell {
            display: table-cell;
            float: left;
            width: 33%;
          }
          .detail-cell > label {
            float: left;
            vertical-align: top;
            color: #5e626a;
            margin-right: 16px;
            width: 74px;
            & + div {
              display: inline-block;
              color: #151a26;
              max-width: 80%;
              word-break: break-all;
              position: relative;
            }
          }
        }
      }
    }
  }
  .icon-edit, .icon-copy {
    font-size: 12px;
    color: #108cee;
    cursor: pointer;
  }
  .name-icon {
    position: relative;
    top: -1px;
    fill: #2468F2;
    color: #2468F2;
  }
  .gray {
    svg {
      fill: #ccc !important;
    }
  }
  .edit-popover-class {
    .edit-hastip {
      margin-bottom: 50px !important;
    }
  }
}