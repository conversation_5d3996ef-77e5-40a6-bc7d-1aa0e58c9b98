/*
 * @Description: 通道路由创建页
 * @Author: <EMAIL>
 * @Date: 2022-04-14 14:19:16
 */

import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';

import {validateRules} from './helper';
import {urlSerialize} from '@/pages/sanPages/utils/helper';
import rule from '@/pages/sanPages/utils/rule';
import './style.less';

const {asComponent, invokeSUI, invokeSUIBIZ, template} = decorators;

const tpl = html`
    <div>
        <s-dialog class="channel-route-create" open="{{true}}" title="{{title}}">
            <s-form s-ref="form" data="{=formData=}" rules="{{rules}}" label-align="left">
                <s-form-item label="规则类型：" prop="ipVersion">
                    <s-radio-radio-group radioType="button" value="{=formData.ipVersion=}" on-change="ipVersionChange">
                        <s-radio label="IPv4" value="{{4}}" />
                        <s-tooltip trigger="{{!channel.enableIpv6 ? 'hover' : ''}}" content="请先配置互联">
                            <s-radio label="IPv6" value="{{6}}" disabled="{{!channel.enableIpv6}}" />
                        </s-tooltip>
                    </s-radio-radio-group>
                </s-form-item>
                <s-form-item label="目标网段：" s-if="{{formData.ipVersion === 4}}" prop="destCidr">
                    <div s-for="item,index in formData.destCidr">
                        <s-input
                            class="{{networksInfo[index] ? 'input-error' : ''}}"
                            value="{=formData.destCidr[index]=}"
                            placeholder="{{'示例：***********/16'}}"
                            on-input="networksInput($event, index)"
                        />
                        <s-icon s-if="index > 0" class="iconfont icon-close" on-click="deleteNetwork(index)" />
                        <span class="s-form-item-error">{{ networksInfo[index]}}</span>
                    </div>
                    <!--暂不支持多个-->
                    <!--<s-button skin="stringfy" on-click="addNetWork" s-if="formData.destCidr.length <= 9"
          disabled="{{type === 'editIpv6'}}">
          {{ '新增一行'}}
        </s-button>-->
                </s-form-item>
                <s-form-item label="目标网段：" s-else prop="destCidr">
                    <div s-for="item,index in formData.destCidr">
                        <s-input
                            class="{{networksInfo[index] ? 'input-error' : ''}}"
                            value="{=formData.destCidr[index]=}"
                            on-input="ipv6ListInput($event, index)"
                        />
                        <s-icon s-if="index > 0" class="iconfont icon-close" on-click="deleteNetwork(index)" />
                        <span class="s-form-item-error">{{ networksInfo[index]}}</span>
                    </div>
                    <!--暂不支持多个-->
                    <!--<s-button skin="stringfy" on-click="addNetWork" s-if="formData.destCidr.length <= 9">
          {{ '新增一行'}}
        </s-button>-->
                </s-form-item>
                <s-form-item label="下一跳实例类型：" prop="nexthopType" class="nexthopType-label">
                    <s-select
                        class="probe-route-type"
                        width="200"
                        placeholder="请选择下一跳实例类型"
                        datasource="{{nexthopTypeList}}"
                        value="{=formData.nexthopType=}"
                        on-change="nexthopTypeChange"
                    />
                </s-form-item>
                <s-form-item label="下一跳实例ID：" prop="nexthop">
                    <s-select width="200" filterable datasource="{{nexthopList}}" value="{=formData.nexthop=}" />
                </s-form-item>
                <s-form-item prop="description" label="{{'描述：'}}" class="desc">
                    <s-input-text-area value="{=formData.description=}" width="{{210}}" height="{{60}}" />
                </s-form-item>
            </s-form>
            <div slot="footer">
                <s-button on-click="onClose">取消</s-button>
                <s-button skin="primary" disabled="{{disableSub}}" on-click="doSubmit">确定</s-button>
            </div>
        </s-dialog>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
class channelRouteCreate extends Component {
    initData() {
        return {
            title: '新建路由条目',
            formData: {
                ipVersion: 4,
                destCidr: [''],
                nexthopType: '',
                description: ''
            },
            disableSub: false,
            ipVersionList: [
                {
                    text: 'IPv4',
                    value: 4
                },
                {
                    text: 'IPv6',
                    value: 6
                }
            ],
            rules: validateRules,
            nexthopTypeList: [
                {text: '专线网关', value: 'vpc'},
                {text: '专线通道', value: 'channel'}
                // 暂不支持
                // {text: 'CSN-ID', value: 'csn'},
            ],
            nexthopList: [],
            networksInfo: [],
            allRouteList: []
        };
    }

    inited() {
        this.queryRouteList();
    }

    queryRouteList() {
        const {id: channelId} = this.data.get('channel');
        const payload = {
            channelId,
            pageNo: 1,
            pageSize: 1000
        };
        this.$http.getChannelRouteList(payload).then((res: any) => {
            const {result} = res;
            this.data.set('allRouteList', result);
        });
    }

    attached() {
        // bgp通道不能新建专线通道路由
        if (this.data.get('channel.routeType') === 'bgp') {
            let nexthopTypeList = this.data.get('nexthopTypeList');
            this.data.set(
                'nexthopTypeList',
                nexthopTypeList.map(item => {
                    if (item.value === 'channel') {
                        return {
                            ...item,
                            disabled: true
                        };
                    }
                    return item;
                })
            );
        }
    }

    addNetWork() {
        this.data.push('formData.destCidr', '');
    }

    deleteNetwork(index) {
        this.data.splice('formData.destCidr', [index, 1]);
        this.data.splice('networksInfo', [index, 1]);
    }

    inputValid(r, value) {
        if (r === undefined) {
            return '';
        }

        let result = '';
        if (r.required) {
            value === '' && (result = r.requiredErrorMessage);
        }
        if (r.pattern) {
            !r.pattern.test(value) && (result = '目标网段不符合规则');
        }
        return result;
    }

    networksInput({value}, index) {
        const result = this.inputValid(rule.CHANNEL.NETWORK, value);
        this.data.set(`networksInfo[${index}]`, result);
    }

    ipv6ListInput({value}, index) {
        if (!value) {
            this.data.set(`networksInfo[${index}]`, '请输入IPV6参数');
        } else if (!rule.IPV6_SEG.test(value)) {
            this.data.set(`networksInfo[${index}]`, '参数格式不合法');
        } else {
            this.data.set(`networksInfo[${index}]`, '');
        }
    }

    loadSource(value) {
        let type = value || this.data.get('formData.nexthopType');
        const requesetMap = {
            vpc: this.getGwList,
            channel: this.getChanneList,
            csn: this.getCsnList
        };
        if (!requesetMap[type]) {
            return;
        }
        return requesetMap[type].apply(this).then(res => {
            this.data.set('nexthopList', res);
            return Promise.resolve();
        });
    }

    getGwList() {
        let result = [];
        let domainId = this.data.get('channel.domainId');
        if (this.data.get('channel.dcgwId') && domainId.split('-')[0] !== 'csn') {
            result = [
                {
                    text: this.data.get('channel.dcgwId'),
                    value: this.data.get('channel.dcgwId')
                }
            ];
        }
        return Promise.resolve(result);
    }

    getCsnList() {
        let payload = {
            pageNo: 1,
            pageSize: 1000
        };
        this.$http.csnList(urlSerialize(payload)).then(data => {
            let result = [];
            if (data) {
                u.each(data, item => {
                    result.push({
                        value: item.id,
                        text: item.name
                    });
                });
            }
            return Promise.resolve(result);
        });
    }

    getChanneList() {
        let channel = this.data.get('channel');
        const subChannel = this.data.get('channel.subChannel') || [];
        let result = subChannel.map(item => ({
            text: item,
            value: item
        }));
        // 加上自己本身的路由
        result.push({
            text: channel.id,
            value: channel.id
        });
        return Promise.resolve(result);
    }

    ipVersionChange() {
        this.data.set('formData.destCidr', ['']);
    }

    nexthopTypeChange({value}) {
        this.data.set('nexthopList', []);
        this.data.set('formData.nexthop', '');
        this.loadSource(value);
    }

    onClose() {
        this.dispose();
    }

    cidrCheck() {
        const {ipVersion, destCidr} = this.data.get('formData');
        if (ipVersion === 4) {
            destCidr.map((value, index) => {
                this.networksInput({value}, index);
            });
        } else {
            destCidr.map((value, index) => {
                this.ipv6ListInput({value}, index);
            });
        }
    }

    cidrRepeatCheck() {
        const {formData, allRouteList} = this.data.get('');
        u.forEach(formData.destCidr, (item, index) => {
            if (!item) {
                this.data.set(`networksInfo[${index}]`, '请填写目标网段');
            } else {
                const mask = item.split('/')[1];
                if (!mask) {
                    item += '/32';
                }
                const isRepeat = allRouteList.some((route: any) => {
                    const {destCidr, nexthop} = route;
                    const existCombineKey = destCidr + nexthop;
                    const newCombineKey = item + formData.nexthop;
                    return existCombineKey === newCombineKey;
                });
                if (isRepeat) {
                    this.data.set(`networksInfo[${index}]`, '目的网段与目的实例id组合不能与已有路由重复');
                } else {
                    this.data.set(`networksInfo[${index}]`, '');
                }
            }
        });
    }

    async doSubmit() {
        const form = this.ref('form');
        await form.validateFields();
        this.cidrRepeatCheck();
        if (this.data.get('networksInfo').join('') !== '') {
            return;
        }
        this.cidrCheck();
        if (this.data.get('networksInfo').join('') !== '') {
            return;
        }
        this.data.set('disableSub', true);
        let payload = u.cloneDeep(this.data.get('formData'));
        // 暂不支持多网段
        payload.destCidr = payload.destCidr[0];
        if (!payload.destCidr.includes('/')) {
            // 掩码自动补全
            payload.destCidr = payload.destCidr + '/32';
        }
        this.$http
            .createChannelRoute(payload, this.data.get('channel.region'))
            .then(() => {
                this.fire('success');
                this.onClose();
            })
            .finally(() => {
                this.data.set('disableSub', false);
            });
    }
}
export default Processor.autowireUnCheckCmpt(channelRouteCreate);
