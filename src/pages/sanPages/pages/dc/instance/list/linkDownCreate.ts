import {Component} from 'san';
import {Di<PERSON>, <PERSON>ton, Alert, Form, Switch, Input} from '@baidu/sui';
import { html } from '@baiducloud/runtime';
import { validateRules } from '../../instance/create/helper';

import './style.less';

/* eslint-disable */
const template = html`
    <template>
        <s-dialog open="{{open}}" title="{{title}}">
            <div class="dc-linkDown-create">
                <s-alert skin="warning">{{alertContent}}</s-alert>
                <s-form label-align="left" s-ref="form" rules="{{rules}}" data="{=formData=}">
                    <s-form-item label="物理专线ID：" class="item-content">
                        <span>{{selectedItem.id}}</span>
                    </s-form-item>
                    <s-form-item label="端口延迟Down功能：" class="item-content">
                        <s-switch checked="{=linkDelayOn=}"></s-switch>
                    </s-form-item>
                    <s-form-item label="端口延迟Down时间：" class="item-content" s-if="{{linkDelayOn}}" prop="linkDelayTime">
                        <s-input
                            width="100"
                            value="{=formData.linkDelayTime=}"/> ms
                        <div class="tip-class">{{delayTimeTip}}</div>
                    </s-form-item>
                </s-form>
            </div>
            <div slot="footer">
                <s-button on-click="onClose">取消</s-button>
                <s-button
                    disabled="{{confirmDisable}}"
                    skin="primary"
                    on-click="onConfirm"
                    >确定</s-button
                >
            </div>
        </s-dialog>
    </template>
`;

/* eslint-enable */
export default class extends Component {
    static template = template;
    static components = {
        's-dialog': Dialog,
        's-button': Button,
        's-alert': Alert,
        's-form': Form,
        's-form-item': Form.Item,
        's-switch': Switch,
        's-input': Input
    };
    initData() {
        return {
            open: true,
            alertContent: '该配置对物理专线生效，建议用户在配置专线通道前创建，并且确保专线两端的时间一致。否则可能影响专线流量。',
            delayTimeTip: '取值范围：100-10000，且为100的整数倍数。默认2000',
            linkDelayOn: false,
            formData: {
              linkDelayTime: 2000,
            },
            confirmDisable: false,
            rules: validateRules(this)
        };
    }
    inited() {
        // 编辑态，把linkDelay回显
        if (!!this.data.get('selectedItem')?.linkDelay) {
            this.data.set('linkDelayOn', true);
            this.data.set('formData.linkDelayTime', this.data.get('selectedItem').linkDelay);
        }
        this.watch('linkDelayOn', value => {
            this.data.set('formData.linkDelayTime', !!this.data.get('selectedItem')?.linkDelay ? this.data.get('selectedItem').linkDelay : 2000);
        });
    }

    async onConfirm() {
        const form = this.ref('form');
        await form.validateFields();
        this.data.set('confirmDisable', true);
        let instance = this.data.get('selectedItem');
        this.$http.dcUpdate({
            dcphyId: instance.id,
            linkDelay: this.data.get('linkDelayOn') ? this.data.get('formData.linkDelayTime') : 0,
        }).then(() => {
          this.fire('confirm');
          this.data.set('confirmDisable', false);
          this.onClose();
          }).catch(() => {
            this.data.set('confirmDisable', false);
        });
    }

    onClose() {
        this.dispose && this.dispose();
    }

}
