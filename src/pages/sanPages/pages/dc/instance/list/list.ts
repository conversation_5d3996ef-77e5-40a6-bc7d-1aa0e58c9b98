import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {Notification} from '@baidu/sui';
import {OutlinedRefresh, OutlinedEditingSquare, OutlinedPlus, OutlinedDownload} from '@baidu/sui-icon';
import {ObjectToQuery} from '@/utils';
import rules from '../rules';
import {SCHEMA} from './tableField';
import MonitorDialog from './monitor/monitor';
import linkDownCreate from './linkDownCreate';
import {DocService, ContextService} from '@/pages/sanPages/common';
import Confirm from '@/pages/sanPages/components/confirm';
import {utcToTime, serializeQuery, $flag as FLAG} from '@/pages/sanPages/utils/helper';
import {InstanceStatus, PortType, ISP, routeStandardList} from '@/pages/sanPages/common/enum';
import MonitorConfig from '@/pages/sanPages/utils/monitorConfig';
import './style.less';
import DcListOperation from './moreOpt';
import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';

const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, invokeComp} = decorators;

const monitorMetric = _.pick(MonitorConfig.monitorMetric, ['bandwidth', 'porterr']);
const tpl = html`
    <div>
        <s-app-list-page class="{{klass}}">
            <div slot="pageTitle">
                <div class="vpc-dc-header">
                    <div class="header-left">
                        <span class="title">物理专线</span>
                    </div>
                    <div class="header-right">
                        <a
                            s-ref="introduce"
                            href="javascript:void(0)"
                            class="help-file function-introduce"
                            on-click="handleShowCard"
                            on-mouseenter="handleMouseEnter('introduce')"
                            on-mouseleave="handleMouseLeave('introduce')"
                            s-if="{{!FLAG.NetworkSupportXS}}"
                        >
                            <img class="s-icon" src="{{introduceIcon}}" />功能简介
                        </a>
                        <s-button skin="stringfy" on-click="showExpireData" s-if="!showMode"> 7天即将到期 </s-button>
                        <s-button s-else on-click="showExpireData" class="butotn-shortcut">
                            7天即将到期
                            <s-icon name="close" />
                        </s-button>
                        <a
                            s-if="{{!FLAG.NetworkSupportXS}}"
                            target="_BLANK"
                            href="/billing/#/renew/list~serviceType=ET"
                            style="margin-right:8px"
                        >
                            <s-icon name="link" />
                            自动续费
                        </a>
                        <a
                            s-if="{{!FLAG.NetworkSupportXS}}"
                            target="_BLANK"
                            href="{{ContextService.Domains.ticket}}"
                            style="margin-right:8px"
                        >
                            <s-icon name="gongdanfankui" />
                            工单咨询
                        </a>
                        <a
                            s-if="{{!FLAG.NetworkSupportXS}}"
                            target="_BLANK"
                            href="{{DocService.et_index}}"
                            class="dc_action"
                            style="margin-right:16px"
                        >
                            <s-icon name="information" />
                            专线操作指南
                        </a>
                    </div>
                </div>
                <introduce-panel
                    isShow="{{show}}"
                    klass="{{'endpoint-peerconn-wrapper'}}"
                    title="{{introduceTitle}}"
                    description="{{description}}"
                    introduceOptions="{{introduceOptions}}"
                    on-toggle="handleToggle($event)"
                ></introduce-panel>
            </div>
            <div slot="bulk">
                <s-tip-button
                    disabled="{{accountState.disabled || createDc.disable || iamPass.disable}}"
                    skin="primary"
                    isDisabledVisibile="{{true}}"
                    trigger="{{accountState.message || createDc.message || iamPass.message ? 'hover' : ''}}"
                    on-click="onCreate"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{accountState.message || createDc.message || iamPass.message | raw}}
                    </div>
                    <outlined-plus />申请物理专线
                </s-tip-button>
                <s-tip-button
                    disabled="{{recharge.disable}}"
                    isDisabledVisibile="{{true}}"
                    on-click="onRecharge"
                    class="left_class"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{recharge.message | raw}}
                    </div>
                    续费
                </s-tip-button>
                <s-tip-button
                    disabled="{{release.disable}}"
                    isDisabledVisibile="{{true}}"
                    on-click="onRelease"
                    class="left_class"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{release.message | raw}}
                    </div>
                    释放
                </s-tip-button>
                <edit-tag
                    s-if="!FLAG.NetworkVpcSupOrganization"
                    selectedItems="{{table.selectedItems}}"
                    on-success="refresh"
                    class="left_class"
                    type="ET"
                    editTag="{{editTag}}"
                ></edit-tag>
            </div>
            <div slot="filter">
                <div class="dc-buttons-wrap">
                    <search-res
                        s-ref="search"
                        serviceType="ET"
                        searchbox="{=searchbox=}"
                        on-search="onSearch"
                    ></search-res>
                    <s-button on-click="refresh" class="s-icon-button"
                        ><outlined-refresh class="icon-class"
                    /></s-button>
                    <s-button
                        on-click="handleDownload"
                        class="s-icon-button"
                        track-id="ti_vpc_nat_download"
                        track-name="下载"
                        ><outlined-download class="icon-class"
                    /></s-button>
                </div>
            </div>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                on-selected-change="tableSelected($event)"
                on-filter="onFilter"
                on-sort="onSort"
                selection="{=table.selection=}"
                track-id="ti_vpc_instance_table"
                track-name="列表操作"
            >
                <div slot="empty">
                    <s-empty
                        vertical
                        on-click="onCreate"
                        class="{{iamPass.disable || createDc.disable ? 'create-disable' : ''}}"
                    />
                </div>
                <div slot="h-interfaceStatus">
                    <div>专线端口状态 <s-tip class="inline-tip" content="{{portStatusTip}}" skin="question" /></div>
                </div>
                <div slot="h-etNeighborInfo">
                    <div>
                        专线设备邻居信息 <s-tip class="inline-tip" content="{{etNeighborInfoTip}}" skin="question" />
                    </div>
                </div>
                <div slot="c-etNeighborInfo">
                    <span>{{row.neighborDevice || '-'}}/{{row.neighborInterface || '-'}}</span>
                </div>
                <div slot="c-name" class="dc-id-widget">
                    <a href="#/dc/instance/detail?instanceId={{row.id}}" class="truncated">{{row.name}}</a>
                    <s-popover
                        s-ref="popover-name-{{rowIndex}}"
                        s-if="row.status !== 'ack-wait' && row.status !== 'reject'"
                        placement="top"
                        trigger="click"
                        class="edit-popover-class"
                    >
                        <div class="edit-wrap" slot="content">
                            <s-input
                                value="{=edit.name.value=}"
                                width="320"
                                placeholder="请输入名称"
                                on-input="onEditInput($event, rowIndex, 'name')"
                            />
                            <div class="edit-tip">
                                大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65
                            </div>
                            <s-button
                                skin="primary"
                                s-ref="editBtn-name-{{rowIndex}}"
                                disabled="{{true}}"
                                on-click="editConfirm(row, rowIndex, 'name')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel(rowIndex, 'name')">取消</s-button>
                        </div>
                        <outlined-editing-square class="name-icon" on-click="onInstantEdit(row, rowIndex, 'name')" />
                    </s-popover>
                    <br />
                    <span class="truncated">{{row.id}}</span>
                    <s-clip-board class="name-icon" text="{{row.id}}" successMessage="已复制到剪贴板" />
                </div>
                <div slot="c-status">
                    <span class="{{row.status | statusClass}}">{{row.status | statusText}}</span>
                </div>
                <div slot="c-apAddr">
                    <span>{{row.apAddrText}}</span>
                </div>
                <div slot="c-intfType">
                    <span>{{row.intfType | getIntfType}}</span>
                </div>
                <div slot="c-isp">
                    <span>{{row.isp | getIsp}}</span>
                </div>
                <div slot="c-linkDelay">
                    <span>{{row.linkDelay | getLinkDelay}}</span>
                </div>
                <div slot="c-expireTime">{{row.expireTime | getTime}}</div>
                <div slot="c-lineSpecification">{{row.lineSpecificationName}}</div>
                <div slot="c-description">{{row.description || '-'}}</div>
                <div slot="c-tag">
                    <!--bca-disable-next-line-->
                    {{row.tags | getTag | raw}}
                </div>
                <div slot="c-opt" class="operations">
                    <span s-if="{{row | showMonitor}}">
                        <s-button skin="stringfy" on-click="showMonitor(row)">监控</s-button>
                        <s-button skin="stringfy" on-click="showMonitorDetail(row)">报警详情</s-button>
                    </span>
                    <s-button s-if="row.status === 'pay-wait'" skin="stringfy" on-click="buyPort(row)"
                        >支付端口资源占用费</s-button
                    >
                    <s-button
                        s-if="row.status === 'established' && !row.checkAccept"
                        skin="stringfy"
                        on-click="checkAccept(row)"
                        >验收</s-button
                    >
                    <dc-list-operation
                        class="nat_multiple_class"
                        item="{{row}}"
                        on-command="changeOpt"
                        row-index="{{rowIndex}}"
                    />
                </div>
            </s-table>
            <s-pagination
                s-if="pager.total"
                slot="pager"
                layout="{{'total, pageSize, pager, go'}}"
                pageSize="{{pager.pageSize}}"
                total="{{pager.total}}"
                page="{{pager.page}}"
                on-pagerChange="onPagerChange"
                on-pagerSizeChange="onPagerSizeChange"
            />
        </s-app-list-page>
    </div>
`;

@template(tpl)
@invokeAppComp
@invokeSUI
@invokeSUIBIZ
@invokeComp('@search-res', '@edit-tag', '@introduce-panel')
class EtInstanceList extends Component {
    static components = {
        'outlined-refresh': OutlinedRefresh,
        'outlined-editing-square': OutlinedEditingSquare,
        'outlined-plus': OutlinedPlus,
        'dc-list-operation': DcListOperation,
        'outlined-download': OutlinedDownload
    };
    static filters = {
        statusClass(value: string) {
            return InstanceStatus.fromValue(value).kclass || '';
        },
        statusText(value: string) {
            return value ? InstanceStatus.getTextFromValue(value) : '-';
        },
        getIntfType(value: string) {
            return value ? PortType.getTextFromValue(value) : '-';
        },
        getIsp(value: string) {
            return value ? ISP.getTextFromValue(value) : '-';
        },
        getTime(value: any) {
            return value ? utcToTime(value) : '-';
        },
        showMonitor(row: any) {
            return row.status === InstanceStatus.ACTIVE && row.apType !== 'BAIDU';
        },
        filterLine(value: string) {
            if (value) {
                let index = routeStandardList.findIndex(item => item.value === value);
                if (index > -1) {
                    return routeStandardList[index].text;
                }
                return '-';
            }
            return '-';
        },
        getLinkDelay(value: number) {
            return value ? value + 'ms' : '-';
        },
        getTag(value: any) {
            if (!value || value.length < 1) {
                return '-';
            }
            let tagHtml = '';
            let tags = '';
            u.each(value, (item, index) => {
                let tagKey = u.escape(item.tagKey);
                let tagValue = u.escape(item.tagValue);
                tags += '{' + tagKey + ' : ' + tagValue + '} ';
                if (index < 2) {
                    tagHtml += tagKey + ' : ' + tagValue + '<br>';
                }
            });
            value.length > 2 && (tagHtml += '...');
            return '<div title="' + tags + '">' + tagHtml + '</div>';
        }
    };

    initData() {
        return {
            FLAG,
            DocService,
            ContextService,
            klass: 'dc-list-wrap',
            table: {
                loading: false,
                datasource: [],
                columns: SCHEMA,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                }
            },
            searchbox: {
                placeholder: '请输入专线名称搜索',
                keyword: '',
                keywordType: ['name'],
                keywordTypes: [
                    {text: '专线名称', value: 'name'},
                    {text: '专线ID', value: 'id'},
                    {text: '描述', value: 'description'}
                ]
            },
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            showTip: '',
            monitorOptions: {},
            monitorDialogOpen: false,
            createDc: {
                disable: true,
                message: ''
            },
            iamPass: {},
            editTag: {
                disable: true,
                message: '请选择实例'
            },
            show: true,
            introduceTitle: '物理专线简介',
            description:
                '专线是一种高性能、安全性极好的网络传输服务。专线服务避免了用户核心数据走公网线路带来的抖动、延时、丢包等网络质量问题，大大提升了用户业务的性能与安全性。',
            introduceIcon: INTRODUCE_ICON_COLOR,
            introduceEle: null,
            accountState: {
                disabled: false,
                message: ''
            },
            urlQuery: getQueryParams(),
            portStatusTip:
                '此状态为了方便用户查看专线的端口状态，需要注意的是当专线端口状态为UP时，用户仍需要走完物理专线建设流程，否则会影响用户专线通道的创建。',
            etNeighborInfoTip:
                'LLDP:邻居层发现协议 （Link Layer Discovery Protocol），百度智能云端默认开启，如需查看，请用户确保专线对端或其他云端网络设备开启LLDP，此时百度智能云端展示的是用户对端或其他云端对接的设备名称及物理端口号。'
        };
    }

    inited() {
        this.getIamQuery();
        this.checkDcIsEnterprise();
        this.getStandardWhiteList();
        const accountState = window.$storage.get('accountState');
        this.data.set('accountState', accountState);
        this.checkAndSetFilters();
    }

    attached() {
        this.loadPage();
        this.data.set('introduceEle', this.ref('introduce'));
        if (window.$storage.get('showDcIntroduce')) {
            this.handleToggle(false);
        }
    }

    /** 检查链接里是否带参数，如果有则设置过滤条件 */
    checkAndSetFilters() {
        const urlQuery = this.data.get('urlQuery');
        if (!urlQuery?.id) return;
        this.data.set('searchbox.keyword', urlQuery.id);
        this.data.set('searchbox.keywordType', ['id']);
    }

    loadPage() {
        this.data.set('table.loading', true);
        let payload = this.getPayload();
        this.resetTable();
        this.$http.getDcList(payload).then((res: any) => {
            this.data.set('table.datasource', res.result);
            this.data.set('pager.total', res.totalCount);
            this.data.set('table.loading', false);
        });
    }

    resetTable() {
        this.data.set('table.selection', {
            mode: 'multi',
            selectedIndex: []
        });
        this.data.set('table.selectedItems', []);
    }

    refresh() {
        this.data.set('searchbox.keyword', '');
        this.data.set('searchbox.keywordType', ['name']);
        this.loadPage();
    }

    getPayload() {
        const {pager, order, filters, showMode} = this.data.get('');
        const searchParam = this.ref('search') ? this.ref('search').getSearchCriteria() : {};
        let payload = {
            pageNo: pager.page,
            pageSize: pager.pageSize,
            keywordType: searchParam.keywordType,
            keyword: searchParam.keyword
        };
        if (showMode) {
            payload.showMode = showMode;
        }
        if (searchParam.keywordType === 'tag') {
            payload.subKeywordType = searchParam.subKeywordType;
        }
        return {...payload, ...order, ...filters};
    }

    onRecharge() {
        const selectedItems = this.data.get('table.selectedItems');
        const instanceIds = u.pluck(selectedItems, 'id');
        const region = selectedItems[0].region;
        let urlParam = {
            serviceType: 'ET',
            region: region,
            confirmV2Url: '/api/network/v1/dc/phy/order/confirm/renew',
            instanceIds: instanceIds
        };
        let rechargeUrl = `/billing/#/billing/recharge/list~${serializeQuery(urlParam)}`;
        if (selectedItems[0].lineSpecification) {
            // 目前续费不支持批量操作
            rechargeUrl = `${rechargeUrl},${instanceIds[0]}@line_build`;
        } else {
            redirect(rechargeUrl);
        }
        redirect(rechargeUrl);
    }

    onRelease() {
        let selectedItems = this.data.get('table.selectedItems');
        const payload = {
            dcphyId: selectedItems[0].id
        };
        const options = {
            region: selectedItems[0].region
        };
        let confirm = new Confirm({
            data: {
                title: '释放前确认',
                content: '确认释放所选中的物理专线？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http.releaseDc(payload, options).then(() => this.loadPage());
        });
    }

    tableSelected(e: Event) {
        this.data.set('table.selectedItems', e.value.selectedItems);
        let {recharge, release, DCEDITTAG} = checker.check(rules, e.value.selectedItems, '');
        this.data.set('recharge', recharge);
        this.data.set('release', release);
        this.data.set('editTag', DCEDITTAG);
    }

    checkDcIsEnterprise() {
        this.$http.dcIsEnterprise().then((res: any) => {
            let {createDc} = checker.check(rules, '', '', res);
            this.data.set('createDc', createDc);
        });
    }

    // 搜索事件
    onSearch(e: Event) {
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    // 改变页数
    onPagerChange(e: Event) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }

    // 改变每页显示数量
    onPagerSizeChange(e: Event) {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', e.value.pageSize);
        this.loadPage();
    }

    // 排序
    onSort(e: Event) {
        this.data.set('order', e.value);
        this.loadPage();
    }

    // 筛选
    onFilter(e: Event) {
        let name = e.field.name;
        let value = e[name] ? e[name] : '';
        this.data.set('filters.' + name, value);
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    // 点击修改icon
    onInstantEdit(row: any, rowIndex: number, type: string) {
        this.data.set(`edit.${type}.value`, row[`${type}`]);
        this.data.set(`edit.${type}.error`, false);
        const popWrap = this.ref(`popover-${type}-${rowIndex}`);
        popWrap.data.set('visible', !popWrap.data.get('visible'));
    }

    // 编辑弹框-输入名称/描述
    onEditInput(e: Event, rowIndex: number, type: string) {
        let result =
            type === 'name'
                ? e.value === '' || !/^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/.test(e.value)
                : e.value.length > 200;
        this.data.set(`edit.${type}.error`, result);
        this.data.set(`edit.${type}.value`, e.value);
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', result);
    }

    // 编辑弹框-提交
    editConfirm(row: any, rowIndex: number, type: string) {
        let edit = this.data.get(`edit.${type}`);
        if (edit.error) {
            return;
        }
        this.$http
            .dcUpdate({
                dcphyId: row.id,
                [type]: edit.value
            })
            .then(() => {
                this.editCancel(rowIndex, type);
                Notification.success('修改成功');
                this.loadPage();
            });
    }

    // 编辑弹框-取消
    editCancel(rowIndex: number, type: string) {
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', true);
        this.ref(`popover-${type}-${rowIndex}`).data.set('visible', false);
    }

    showMonitor(row: any) {
        let region = row.region;
        this.data.set('monitorDialogOpen', true);
        const dialog = new MonitorDialog({
            data: {
                options: {
                    scope: 'BCE_DEDICATEDCONN',
                    dimensions: 'DedicatedConnectionId:' + row.id,
                    statistics: 'average',
                    apiType: 'metricName',
                    option: {region},
                    metrics: monitorMetric,
                    metric: 'bandwidth'
                }
            }
        });
        dialog.attach(document.body);
    }

    showMonitorDetail() {
        redirect('/bcm/#/bcm/alarm/rule/list~scope=BCE_DEDICATEDCONN');
    }

    buyPort(row: any) {
        window.location.hash = `#/dc/instance/createPort?id=${row.id}`;
    }

    checkAccept(row: any) {
        let confirm = new Confirm({
            data: {
                title: '验收前确认',
                content: '确认验收所选中的物理专线？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            const options = {
                region: row.region
            };
            this.$http.dcCheckAccept(row.id, options).then(res => {
                this.loadPage();
            });
        });
    }

    onCreate() {
        window.location.hash = '#/dc/instance/create';
    }

    showExpireData() {
        let showMode = this.data.get('showMode');
        showMode = showMode ? '' : 'WILLEXPIRED';
        this.data.set('showMode', showMode);
        this.loadPage();
    }

    // 关闭监控弹窗
    closeMonitorDialog() {
        this.data.set('monitorDialogOpen', false);
    }
    onRegionChange() {
        location.reload();
    }
    getStandardWhiteList() {
        const whiteList = window.$storage.get('commonWhite');
        if (whiteList?.LineBuild) {
            this.data.splice('table.columns', [
                4,
                0,
                {
                    name: 'lineSpecification',
                    label: '线路规格',
                    width: 100
                }
            ]);
        }
    }
    getIamQuery() {
        this.$http.getInterfaceIam({interfaceName: 'createDcPhy'}).then((res: any) => {
            let message = '';
            !res.interfacePermission && (message += '创建物理专线权限');
            !res.createOrderPermission && (message += message ? '、创建订单权限' : '创建订单权限');
            !res.payOrderPermission && (message += message ? '、支付订单权限' : '');
            if (!res.requestId && !res.masterAccount) {
                if (message) {
                    this.data.set('iamPass', {
                        disable: true,
                        message: `您没有${message}，请联系主用户添加`
                    });
                } else {
                    this.data.set('iamPass', {
                        disable: false,
                        message: ''
                    });
                }
            } else {
                this.data.set('iamPass', {
                    disable: false,
                    message: ''
                });
            }
        });
    }

    changeOpt(e: Event) {
        let {type, payload} = e;
        const methodMap = {
            LINKDOWN: this.onLinkDown
        };
        let requester = methodMap[type].bind(this);
        requester(payload);
    }

    onLinkDown(item: any) {
        let dialog = new linkDownCreate({
            data: {
                selectedItem: item,
                title: item.linkDelay ? '编辑端口延迟Down' : '创建端口延迟Down'
            }
        });
        dialog.attach(document.body);
        dialog.on('confirm', () => this.loadPage());
    }
    /**
     * @param {boolean} flag
     */
    handleToggle(flag) {
        window.$storage.set('showDcIntroduce', true);
        this.data.set('show', flag);
        this.data.set('introduceIcon', INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#151B26';
    }
    handleShowCard() {
        window.$storage.set('showDcIntroduce', false);
        if (!this.data.get('show')) {
            this.data.set('show', true);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type) {
        this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#2468F2';
    }
    handleMouseLeave(type) {
        const isShow = this.data.get('show');
        this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        !isShow && (ele.style.color = '#151B26');
    }
    handleDownload() {
        const payload = this.getPayload();
        const queryStr = ObjectToQuery(payload);
        window.open(`/api/network/v1/dc/phy/list/download?${queryStr}`);
    }
}
export default San2React(Processor.autowireUnCheckCmpt(EtInstanceList));
