import {InstanceStatus} from '@/pages/sanPages/common/enum';

export const SCHEMA = [
    {
        name: 'name',
        label: '专线名称／ID',
        width: 160,
        fixed: 'left'
    },
    {
        name: 'status',
        label: '专线建设状态',
        width: 140,
        filter: {
            options: InstanceStatus.getFilterStatus(),
            value: ''
        }
    },
    {
        name: 'interfaceStatus',
        label: '专线端口状态',
        width: 120
    },
    {
        name: 'etNeighborInfo',
        label: '专线设备邻居信息',
        width: 140
    },
    {
        name: 'apAddr',
        label: '接入点',
        width: 160
    },
    {
        name: 'intfType',
        label: '物理端口规格',
        width: 110
    },
    {
        name: 'isp',
        label: '物理线路运营商',
        width: 120
    },
    {
        name: 'linkDelay',
        label: '延迟down时间',
        width: 110
    },
    {
        name: 'expireTime',
        label: '到期时间',
        width: 90
    },
    {
        name: 'description',
        label: '描述',
        width: 90
    },
    {
        name: 'tag',
        label: '标签',
        width: 120,
        sortable: true
    },
    {
        name: 'opt',
        label: '操作',
        width: 150,
        fixed: 'right'
    }
];
