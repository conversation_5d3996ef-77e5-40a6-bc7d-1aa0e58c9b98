/**
 * @file ET-监控弹窗
 * <AUTHOR>
 */
import {Component} from 'san';
import moment from 'moment';
import {Dialog, Select, Button, DatePicker} from '@baidu/sui';
import {BcmSDK} from '@baidu/new-bcm-sdk';
import {BcmChartPanel} from '@baidu/new-bcm-sdk-san';
import {html} from '@baiducloud/runtime';
import {
    OutlinedRefresh
} from '@baidu/sui-icon';
import MonitorConfig from '@/pages/sanPages/utils/monitorConfig';
const {shortcutItems} = MonitorConfig;

import './style.less';


const tpl = html`
<div>
<s-dialog
    open="{{true}}"
    title="{{'物理专线监控'}}"
    width="{{900}}"
    class="monitor-dialog"
    on-confirm="closeMonitorDialog"
    on-close="closeMonitorDialog"
    >
    <div class="search-wrap">
        <div class="search-item">
            <label>{{'监控指标：'}}</label>
            <s-select
                value="{=ds.option=}"
                datasource="{{ds.options}}"
                width="{{120}}"
            />
        </div>
        <div class="search-item">
            <label>{{'统计项：'}}</label>
            <s-select
                value="{=chartConfig.statistics=}"
                datasource="{{statisticsDatasource}}"
                width="{{120}}"
            />
        </div>
        <div class="search-item">
            <label>{{'时间：'}}</label>
            <s-date-range-picker
                value="{=timeRange=}"
                width="{{310}}"
                shortcut="{{shortcutItems}}"
                range="{{range}}"
                mode="second"
                on-change="onTimeChange($event)"/>
        </div>
        <div class="search-item refresh-item">
            <s-button class="s-icon-button" on-click="refresh"><outlined-refresh class="icon-class"/></s-button>
        </div>
    </div>
    <div class="chart-panel">
        <bcm-chart-panel
            s-ref="bcmChartPanel"
            withFilter="{{false}}"
            scope="{{chartConfig.scope}}"
            dimensions="{{chartConfig.dimensions}}"
            statistics="{{chartConfig.statistics}}"
            startTime="{{chartConfig.startTime}}"
            endTime="{{chartConfig.endTime}}"
            period="{{chartConfig.period}}"
            metrics="{{metrics.metrics}}"
            apiType="{{chartConfig.apiType}}"
            unit="{{metrics.unit}}"
            bitUnit="{{metrics.bitUnit}}"
            width="{{860}}"
            height="{{350}}"
            options="{{optionSet}}"
            sdk="{{bcmSdk}}"
        />
    </div>
</s-dialog>
</div>
`;

export default class EtInstanceMonitor extends Component {
    static template = tpl;
    static components = {
        's-dialog': Dialog,
        's-select': Select,
        's-button': Button,
        'bcm-chart-panel': BcmChartPanel,
        'outlined-refresh': OutlinedRefresh,
        's-date-range-picker': DatePicker.DateRangePicker
    }
    initData() {
        return {
            metrics: {
                // option: 'bandwidth',
                metrics: [],
                unit: 'bps',
                bitUnit: 1000
            },
            ds: {
                option: 'bandwidth',
                options: [
                    {text: '带宽', value: 'bandwidth'},
                    {text: '端口错包', value: 'porterr'},
                ]
            },
            metricOption: [
                {text: '带宽', value: 'bandwidth'},
                {text: '端口错包', value: 'porterr'},
            ],
            statisticsDatasource: [
                {
                    value: 'average',
                    text: '平均值'
                },
                {
                    value: 'sum',
                    text: '和值'
                },
                {
                    value: 'maximum',
                    text: '最大值'
                },
                {
                    value: 'minimum',
                    text: '最小值'
                },
                {
                    value: 'sampleCount',
                    text: '样本数'
                }
            ],
            period: {
                options: [
                    {text: '近1小时', value: 'oneHour', getTime: () => ({
                        startTime: moment().subtract(1, 'hour'),
                        endTime: moment()
                    })},
                    {text: '近6小时', value: 'threeHour', getTime: () => ({
                        startTime: moment().subtract(6, 'hour'),
                        endTime: moment()
                    })},
                    {text: '近1天', value: 'oneDay', getTime: () => ({
                        startTime: moment().subtract(1, 'day'),
                        endTime: moment()
                    })},
                    {text: '近7天', value: 'sevenDay', getTime: () => ({
                        startTime: moment().subtract(7, 'day'),
                        endTime: moment()
                    })},
                    {text: '近14天', value: 'fourteenDay', getTime: () => ({
                        startTime: moment().subtract(14, 'day'),
                        endTime: moment()
                    })},
                    {text: '近40天', value: 'fourtyDay', getTime: () => ({
                        startTime: moment().subtract(40, 'day'),
                        endTime: moment()
                    })}
                ],
                value: 'oneHour',
                timeRange: {}
            },
            bcmSdk: new BcmSDK({client: window.$http, context: window.$context}),
            chartConfig: {},
            shortcutItems,
            range: {
                begin: new Date(moment().subtract(40, 'day').valueOf()),
                end: new Date(moment().valueOf())
            },
            timeRange: {
                begin: new Date(moment().subtract(1, 'hour').valueOf()),
                end: new Date(moment().valueOf())
            },
            optionSet: {dataZoom: {start: 0}}
        };
    }

    inited() {
        this.initChartConfig();
        this.initBcmSdk();
    }

    attached() {
        this.watch('timeRange', () => this.refresh());
        this.watch('ds.option', () => this.refresh());
        this.watch('chartConfig.statistics', () => this.refresh());
    }

    initChartConfig() {
        const {
            scope,
            dimensions,
            statistics,
            apiType,
            metrics,
            option,
            metric
        } = this.data.get('options');
        this.data.set('chartConfig', {
            scope,
            dimensions,
            statistics,
            period: 60,
            apiType,
            region: option.region,
        });
        this.data.set('etMetric', metrics);
        this.data.set('metrics.metrics', metrics[metric].metrics);
        this.data.set('metrics.unit', metrics[metric].unit);
        this.data.set('metrics.bitUnit', metrics[metric].bitUnit);
    }
    initBcmSdk() {
        let context = window.$context;
        context.getCurrentRegion = () => {
            return this.data.get('chartConfig.region');
        };
        const bcmSdk = new BcmSDK({client: window.$http, context});
        this.data.set('bcmSdk', bcmSdk);
    }
    onTimeChange({value}) {
        this.data.set('chartConfig.startTime', moment(value.begin).utc().format('YYYY-MM-DDTHH:mm:00') + 'Z');
        this.data.set('chartConfig.endTime', moment(value.end).utc().format('YYYY-MM-DDTHH:mm:00') + 'Z');
        let ms = moment(value.end) - moment(value.begin);
        let hourTime = Math.round(ms / 86400000);
        switch (true) {
            case (hourTime <= 1):
                this.data.set('chartConfig.period', 60);
                break;
            case (hourTime <= 3):
                this.data.set('chartConfig.period', 300);
                break;
            case (hourTime <= 7):
                this.data.set('chartConfig.period', 600);
                break;
            case (hourTime <= 14):
                this.data.set('chartConfig.period', 1800);
                break;
            case (hourTime <= 40):
                this.data.set('chartConfig.period', 3600);
                break;
            default: break;
        };
    }
    loadMetrics() {
        this.nextTick(() => {
            let option = this.data.get('ds.option');
            let etMetric = this.data.get('etMetric');
            this.data.set('metrics.metrics', etMetric[option].metrics);
            this.data.set('metrics.unit', etMetric[option].unit);
            this.data.set('metrics.bitUnit', etMetric[option].bitUnit);
            this.ref('bcmChartPanel').loadMetrics();
        });
    }
    refresh() {
        this.loadMetrics();
    }
    closeMonitorDialog() {
        this.dispose();
    }
    getUtcTime(time) {
        return moment(time).utc().format('YYYY-MM-DDTHH:mm:00') + 'Z';
    }
}
