.dc-list-wrap {
    flex: 1;
    .vpc-dc-header {
        display: flex;
        background-color: #fff;
        align-items: center;
        justify-content: space-between;
        .header-left {
            display: flex;
            align-items: center;
            .title {
                display: inline-block;
                margin: 0;
                color: #151b26;
                margin-right: 12px;
                height: 47px;
                line-height: 47px;
                font-weight: 500;
                font-size: 16px;
                margin-left: 16px;
            }
        }
        .header-right {
            display: flex;
            align-items: center;
            .help-file {
                font-size: 12px;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                color: #151b26;
                line-height: 20px;
                &:hover {
                    color: #2468f2;
                }
                .s-icon {
                    position: relative;
                    top: -1px;
                    right: 4px;
                    margin-right: 0;
                    color: #2468f2;
                    font-size: 14px;
                }
            }
            .function-introduce {
                color: #2468f2;
            }
            .iconfont {
                font-size: 14px;
            }
        }
    }
    .s-table {
        .s-table-row:hover {
            .icon-edit,
            .icon-copy {
                display: block;
            }
        }

        .icon-edit,
        .icon-copy {
            display: none;
            font-size: 12px;
            color: #2468f2;
        }

        .operation {
            a {
                display: block;
            }
        }
        .s-table-body {
            max-height: calc(~'100vh - 356px');
        }
        .s-table-cell-description {
            .s-table-cell-text > div {
                word-break: break-all;
            }
        }
        .operations {
            .s-button {
                padding: 0;
                margin-right: 12px;
            }
        }
        .dc-id-widget {
            white-space: nowrap;
        }
    }
    .s-table .s-table-row:hover .name-icon {
        display: inline;
    }
    .name-icon {
        position: relative;
        top: -1px;
        fill: #2468f2;
        color: #2468f2;
        display: none;
    }

    .foot-pager {
        margin: 20px 0;
    }
    .dc-buttons-wrap {
        .s-search {
            border-color: #e8e9eb;
        }
        .search-res {
            margin-right: 0;
        }
    }
    .more-opt {
        color: #2468f2 !important;
        border: none !important;
        min-width: 80px !important;
        background: transparent;
        padding-left: 0 !important;
        .bui-select-text {
            color: #2468f2;
        }
        .s-input-suffix-container {
            border: none !important;
            margin-left: 2px !important;
        }
        .s-trigger-container {
            .s-popup {
                .s-popup-content-box {
                    .s-select-option-list {
                        padding-bottom: 0px !important;
                    }
                    .s-selectdropdown {
                        width: 150px !important;
                        margin-left: 12px !important;
                        .s-option {
                            height: 25px !important;
                            line-height: 23px !important;
                        }
                    }
                }
            }
        }
        &:after {
            color: #2468f2 !important;
        }

        &:hover,
        &:active {
            background: transparent;
        }
        &::after {
            content: none !important;
        }
    }
}
.more-opt-layer {
    .scroll-into-view-stop {
        width: 124px !important;
    }
}

.dc-linkDown-create {
    .s-alert {
        max-width: 500px;
        height: 56px;
    }
    .item-content {
        .s-form-item-control-wrapper {
            line-height: 30px;
        }
    }
    .s-form-item-label {
        width: 140px;
    }
    .tip-class {
        color: #84868c;
    }
}
