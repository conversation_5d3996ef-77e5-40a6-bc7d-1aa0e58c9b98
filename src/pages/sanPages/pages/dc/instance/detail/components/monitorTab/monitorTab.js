import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import MonitorComponents from '../monitor/monitor';
import MonitorConfig from '@/pages/sanPages/utils/monitorConfig';
import './monitorTab.less';
const {asComponent, invokeComp, invokeAppComp, template} = decorators;
const monitorMetric = _.pick(MonitorConfig.monitorMetric, ['bandwidth', 'porterr']);
const qosMonitorMetric = _.pick(MonitorConfig.monitorMetric, ['bandwidth', 'package', 'lossPkg']);
const tpl = html`
    <template>
        <div class="dc-monitor-tab">
            <app-tab-page>
                <app-tab-page-panel title="物理专线监控" url="{{linkUrl}}">
                    <dc-monitor s-if="{{instance}}" monitorType="et" instance="{{instance}}" options="{{options}}">
                    </dc-monitor>
                </app-tab-page-panel>
                <app-tab-page-panel s-if="{{QosWhiteList}}" title="专线QoS监控" url="{{linkUrl}}">
                    <dc-monitor s-if="{{instance}}" monitorType="qos" instance="{{instance}}" options="{{qosOption}}">
                    </dc-monitor>
                </app-tab-page-panel>
            </app-tab-page>
        </div>
    </template>
`;

@template(tpl)
@invokeAppComp
@asComponent('@dc-monitor-tab')
class dcMonitorTab extends Component {
    initData() {
        return {
            options: {},
            linkUrl: '#/dc/instance/monitor'
        };
    }
    static components = {
        'dc-monitor': MonitorComponents
    };

    inited() {
        const instanceId = this.data.get('context').instanceId || '';
        this.data.set('linkUrl', `#/dc/instance/monitor?instanceId=${instanceId}`);
        this.$http.getDcDetail({dcphyId: instanceId}, {'x-silent-codes': ['NoSuchObject']}).then(data => {
            this.data.set('instance', data);
            this.setOptions();
        });
        this.data.set('QosWhiteList', window.$storage.get('commonWhite').QosWhiteList);
    }

    setOptions() {
        let instance = this.data.get('instance');
        let region = instance.region;
        let commonOption = {
            scope: 'BCE_DEDICATEDCONN',
            dimensions: 'DedicatedConnectionId:' + this.data.get('context').instanceId,
            statistics: 'average',
            apiType: 'metricName',
            option: {region}
        };
        this.data.set('options', {
            ...commonOption,
            metrics: monitorMetric
        });
        this.data.set('qosOption', {
            ...commonOption,
            metrics: qosMonitorMetric
        });
    }
}

export default San2React(Processor.autowireUnCheckCmpt(dcMonitorTab));
