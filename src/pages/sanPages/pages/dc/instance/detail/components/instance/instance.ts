import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {Notification} from '@baidu/sui';
import {OutlinedEditingSquare} from '@baidu/sui-icon';

import {InstanceStatus, PortType, ISP, routeStandardList} from '@/pages/sanPages/common/enum';
import {utcToTime} from '@/pages/sanPages/utils/helper';
import rule from '@/pages/sanPages/utils/rule';
import linkDownCreate from '../../../list/linkDownCreate';
import './style.less';

const {asComponent, invokeSUI, invokeSUIBIZ, invokeAppComp, template, invokeComp} = decorators;
const kXhrOptions = {'X-silence': true};

const tpl = html`
    <div>
        <div class="{{klass}}">
            <div class="list-content">
                <div class="detail-parts-table">
                    <dl class="detail-part-1-col">
                        <dt><h4>{{'基本信息'}}</h4></dt>
                        <dd>
                            <div class="detail-cell">
                                <label>{{'物理专线名称：'}}</label>
                                <div>
                                    <span data-ui-type="Panel" data-ui-id="namePanel"> {{instance.name}} </span>
                                    <s-popover
                                        class="edit-popover-class"
                                        s-if="instance.status !== 'ack-wait' && instance.status !== 'reject'"
                                        s-ref="nameEdit"
                                        placement="top"
                                        trigger="click"
                                    >
                                        <div
                                            class="edit-wrap {{editTip.name ? 'edit-nonetip' : 'edit-hastip'}}"
                                            slot="content"
                                        >
                                            <s-input
                                                value="{=editValue.name=}"
                                                width="320"
                                                placeholder="{{'请输入物理专线名称'}}"
                                                on-input="onInput($event, 'name')"
                                            />
                                            <div class="edit-tip">{{editTip.name}}</div>
                                            <s-button
                                                skin="primary"
                                                s-ref="nameEditBtn"
                                                disabled="{{true}}"
                                                on-click="onEdit('name')"
                                                >{{'确定'}}</s-button
                                            >
                                            <s-button on-click="editCancel('name')">{{'取消'}}</s-button>
                                        </div>
                                        <outlined-editing-square class="name-icon" on-click="beforeEdit('name')" />
                                    </s-popover>
                                </div>
                            </div>
                            <div class="detail-cell">
                                <label>{{'物理专线ID：'}}</label>
                                <div>
                                    <span>{{instance.id}}</span>
                                    <s-clip-board text="{{instance.id}}" />
                                </div>
                            </div>
                            <div class="detail-cell">
                                <label>{{'状态：'}}</label>
                                <div>
                                    <span class="{{instance.status | statusClass}}"
                                        >{{instance.status | statusText}}</span
                                    >
                                </div>
                            </div>
                        </dd>
                        <dd>
                            <div class="detail-cell">
                                <label>{{'接入点：'}}</label>
                                <div>
                                    <span>{{instance.apAddrText}}</span>
                                </div>
                            </div>
                            <div class="detail-cell">
                                <label>{{'物理端口规格：'}}</label>
                                <div>
                                    <span>{{instance.intfType | getIntfType}}</span>
                                </div>
                            </div>
                            <div class="detail-cell">
                                <label>{{'物理线路运营商：'}}</label>
                                <div>
                                    <span>{{instance.isp | getIsp}}</span>
                                </div>
                            </div>
                        </dd>
                        <dd>
                            <div class="detail-cell">
                                <label>{{'对端地址：'}}</label>
                                <div>
                                    <span>{{instance.userIdc}}</span>
                                </div>
                            </div>
                            <div class="detail-cell">
                                <label>{{'创建时间：'}}</label>
                                <div>
                                    <span>{{instance.createTime | getTime}}</span>
                                </div>
                            </div>
                            <div class="detail-cell">
                                <label>{{'到期时间：'}}</label>
                                <div>
                                    <span>{{instance.expireTime | getTime}}</span>
                                </div>
                            </div>
                        </dd>
                        <dd>
                            <div class="detail-cell">
                                <label>{{'描述：'}}</label>
                                <div>
                                    <span>{{instance.description || '-'}}</span>
                                    <s-popover
                                        class="edit-popover-class"
                                        s-if="instance.status !== 'ack-wait' && instance.status !== 'reject'"
                                        s-ref="descriptionEdit"
                                        placement="top"
                                        trigger="click"
                                    >
                                        <div
                                            class="edit-wrap {{editTip.description ? 'edit-nonetip' : 'edit-hastip'}}"
                                            slot="content"
                                        >
                                            <s-input
                                                value="{=editValue.description=}"
                                                width="160"
                                                placeholder="请输入描述"
                                                on-input="onInput($event, 'description')"
                                            />
                                            <div class="edit-tip">{{editTip.description}}</div>
                                            <s-button
                                                skin="primary"
                                                s-ref="descriptionEditBtn"
                                                disabled="{{true}}"
                                                on-click="onEdit('description')"
                                                >{{'确定'}}</s-button
                                            >
                                            <s-button on-click="editCancel('description')">{{'取消'}}</s-button>
                                        </div>
                                        <outlined-editing-square
                                            class="name-icon"
                                            on-click="beforeEdit('description')"
                                        />
                                    </s-popover>
                                </div>
                            </div>
                            <div class="detail-cell" s-if="standardWhite">
                                <label class="line-label">{{'线路规格：'}}</label>
                                <div>
                                    <span>{{instance.lineSpecificationName}}</span>
                                </div>
                            </div>
                            <div class="detail-cell" s-if="{{instance.status === 'established'}}">
                                <label class="linkdelay-label">{{'端口延迟down时间：'}}</label>
                                <div>
                                    <span>{{instance.linkDelay | getLinkDelayContent}}</span>
                                </div>
                                <outlined-editing-square class="name-icon" on-click="changeLinkDelay(instance)" />
                            </div>
                            <!--补全元素防止样式变形-->
                            <span s-else class="detail-cell"></span>
                            <span s-if="!standardWhite" class="detail-cell"></span>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
`;

@template(tpl)
@invokeAppComp
@invokeSUI
@invokeSUIBIZ
@asComponent('@dc-instance-detail')
class EtChannelDetail extends Component {
    static components = {
        'outlined-editing-square': OutlinedEditingSquare
    };
    static filters = {
        statusClass(value: string) {
            return InstanceStatus.fromValue(value).kclass || '';
        },
        statusText(value: string) {
            return value ? InstanceStatus.getTextFromValue(value) : '-';
        },
        getTime(value: any) {
            return value ? utcToTime(value) : '-';
        },
        getIntfType(value: string) {
            return PortType.getTextFromValue(value) || '-';
        },
        getIsp(value: string) {
            return ISP.getTextFromValue(value) || '-';
        },
        filterLine(value: string) {
            if (value) {
                let index = routeStandardList.findIndex(item => item.value === value);
                return routeStandardList[index].text;
            }
            return '-';
        },
        getLinkDelayContent(value: number) {
            return !!value ? value + 'ms' : '-';
        }
    };

    initData() {
        return {
            klass: 'dc-detail-wrap',
            instance: {},
            editValue: {},
            editTip: {}
        };
    }

    async inited() {
        this.data.set('instance', this.data.get('context').instance);
        this.data.set('instanceId', this.data.get('context').instanceId);
        await this.getStandardWhiteList();
    }

    beforeEdit(type: string) {
        let instance = this.data.get('context').instance;
        this.data.set('editTip', {});
        this.data.set(`editValue.${type}`, instance[type]);
    }

    onInput(e: Event, type: string) {
        const result = this.inputValid(type, e.value);
        this.data.set(`editTip.${type}`, result ? result : '');
        this.ref(`${type}EditBtn`).data.set('disabled', !!result);
    }

    inputValid(type: string, value: string) {
        const RULE_MAP = {
            name: rule.DC.NAME,
            userName: rule.DC.USERNAME,
            userPhone: rule.DC.MOBILE,
            userEmail: rule.DC.EMAIL
        };
        const r = RULE_MAP[type];
        if (r === undefined) {
            return false;
        }

        let result = false;
        if (r.required) {
            value === '' && (result = r.requiredErrorMessage);
        }
        if (r.pattern) {
            !r.pattern.test(value) && (result = r.patternErrorMessage);
        }
        if (r.custom) {
            !r.custom(value) && (result = r.customErrorMessage);
        }
        return result;
    }

    onEdit(type: string) {
        const dcphyId = this.data.get('context').instanceId;
        const value = this.data.get(`editValue.${type}`);
        const param = u.extend(
            {dcphyId},
            {
                [type]: value
            }
        );
        this.$http
            .dcUpdate(param, kXhrOptions)
            .then(() => {
                Notification.success('修改成功');
                this.editCancel(type);
                this.data.get('context').refresh();
            })
            .catch(() => {
                Notification.error('修改失败，请稍后再试');
                this.editCancel(type);
            });
    }

    editCancel(type: string) {
        this.ref(`${type}Edit`).data.set('visible', false);
    }
    getStandardWhiteList() {
        const whiteList = window.$storage.get('commonWhite');
        if (whiteList?.LineBuild) {
            this.data.set('standardWhite', true);
        }
    }
    changeLinkDelay(instance: any) {
        let dialog = new linkDownCreate({
            data: {
                selectedItem: instance,
                title: !!instance.linkDelay ? '编辑端口延迟Down' : '创建端口延迟Down'
            }
        });
        dialog.attach(document.body);
        dialog.on('confirm', () => {
            this.data.get('context').refresh();
        });
    }
}

export default San2React(Processor.autowireUnCheckCmpt(EtChannelDetail));
