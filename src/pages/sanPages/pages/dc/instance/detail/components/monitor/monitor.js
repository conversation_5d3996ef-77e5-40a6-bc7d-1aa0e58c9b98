import {Component} from 'san';
import moment from 'moment';
import {BcmChartPanel} from '@baiducloud/bcm-sdk/san';
import {BcmSDK} from '@baiducloud/bcm-sdk';
import {html, decorators, Processor} from '@baiducloud/runtime';
import MonitorConfig from '@/pages/sanPages/utils/monitorConfig';
import {OutlinedRefresh} from '@baidu/sui-icon';
const {shortcutItems} = MonitorConfig;

import './monitor.less';
const {asComponent, invokeSUI, template} = decorators;

const contextPipe = {
    getCurrentRegion: () => {
        return window.$context.getCurrentRegionId();
    },
    getCsrfToken() {
        // 返回cookie中的信息
        return window.$cookie.get('bce-user-info');
    },
    SERVICE_TYPE: window.$context.SERVICE_TYPE
};

const qosMetricMap = new Map([
    [
        'gold',
        {
            bandwidth: [{value: 'Queue2Bps', name: '金牌队列带宽'}],
            package: [{value: 'Queue2Pps', name: '金牌队列包速率'}],
            lossPkg: [{value: 'Queue2LossPkts', name: '金牌队列丢包数'}]
        }
    ],
    [
        'silver',
        {
            bandwidth: [{value: 'Queue0Bps', name: '银牌队列带宽'}],
            package: [{value: 'Queue0Pps', name: '银牌队列包速率'}],
            lossPkg: [{value: 'Queue0LossPkts', name: '银牌队列丢包数'}]
        }
    ],
    [
        'copper',
        {
            bandwidth: [{value: 'Queue4Bps', name: '铜牌队列带宽'}],
            package: [{value: 'Queue4Pps', name: '铜牌队列包速率'}],
            lossPkg: [{value: 'Queue4LossPkts', name: '铜牌队列丢包数'}]
        }
    ]
]);

const tpl = html`
    <div class="monitor-container">
        <div class="search-wrap">
            <div s-if="{{monitorType === 'qos'}}" class="search-item">
                <label>{{'服务质量：'}}</label>
                <s-select
                    value="{=service.option=}"
                    datasource="{{service.options}}"
                    width="{{120}}"
                    on-change="loadMetrics"
                />
            </div>
            <div class="search-item">
                <label>{{'监控指标：'}}</label>
                <s-select value="{=ds.option=}" datasource="{{ds.options}}" width="{{120}}" on-change="loadMetrics" />
            </div>
            <div class="search-item">
                <label>{{'统计项：'}}</label>
                <s-select
                    value="{=chartConfig.statistics=}"
                    datasource="{{statisticsDatasource}}"
                    width="{{120}}"
                    on-change="loadMetrics"
                />
            </div>
            <div class="search-item">
                <label>{{'时间：'}}</label>
                <s-date-picker-date-range-picker
                    value="{=timeRange=}"
                    width="310"
                    shortcut="{{shortcutItems}}"
                    range="{{range}}"
                    mode="second"
                    on-change="onTimeChange($event)"
                />
            </div>
            <div class="search-item refresh-item">
                <s-button class="s-icon-button" on-click="refresh"><outlined-refresh class="icon-class" /></s-button>
            </div>
        </div>
        <div class="chart-panel">
            <bcm-chart-panel
                s-ref="bcmChartPanel"
                withFilter="{{false}}"
                scope="{{chartConfig.scope}}"
                dimensions="{{chartConfig.dimensions}}"
                statistics="{{chartConfig.statistics}}"
                startTime="{{chartConfig.startTime}}"
                endTime="{{chartConfig.endTime}}"
                period="{{chartConfig.period}}"
                metrics="{{metrics.metrics}}"
                apiType="{{chartConfig.apiType}}"
                unit="{{metrics.unit}}"
                bitUnit="{{metrics.bitUnit}}"
                height="{{500}}"
                sdk="{{bcmSdk}}"
            />
        </div>
    </div>
`;
@template(tpl)
@invokeSUI
class DcMonitor extends Component {
    static components = {
        'bcm-chart-panel': BcmChartPanel,
        'outlined-refresh': OutlinedRefresh
    };
    initData() {
        return {
            monitorType: 'et',
            metrics: {
                metrics: [],
                unit: 'bps',
                bitUnit: 1000
            },
            service: {
                option: 'gold',
                options: [
                    {text: '金牌', value: 'gold'},
                    {text: '银牌', value: 'silver'},
                    {text: '铜牌', value: 'copper'}
                ]
            },
            ds: {
                option: 'bandwidth',
                options: [
                    {text: '带宽', value: 'bandwidth'},
                    {text: '端口错包', value: 'porterr'}
                ]
            },
            metricOption: [
                {text: '带宽', value: 'bandwidth'},
                {text: '端口错包', value: 'porterr'}
            ],
            statisticsDatasource: [
                {
                    value: 'average',
                    text: '平均值'
                },
                {
                    value: 'sum',
                    text: '和值'
                },
                {
                    value: 'maximum',
                    text: '最大值'
                },
                {
                    value: 'minimum',
                    text: '最小值'
                },
                {
                    value: 'sampleCount',
                    text: '样本数'
                }
            ],
            period: {
                options: [
                    {
                        text: '近1小时',
                        value: 'oneHour',
                        getTime: () => ({
                            startTime: moment().subtract(1, 'hour'),
                            endTime: moment()
                        })
                    },
                    {
                        text: '近6小时',
                        value: 'threeHour',
                        getTime: () => ({
                            startTime: moment().subtract(6, 'hour'),
                            endTime: moment()
                        })
                    },
                    {
                        text: '近1天',
                        value: 'oneDay',
                        getTime: () => ({
                            startTime: moment().subtract(1, 'day'),
                            endTime: moment()
                        })
                    },
                    {
                        text: '近7天',
                        value: 'sevenDay',
                        getTime: () => ({
                            startTime: moment().subtract(7, 'day'),
                            endTime: moment()
                        })
                    },
                    {
                        text: '近14天',
                        value: 'fourteenDay',
                        getTime: () => ({
                            startTime: moment().subtract(14, 'day'),
                            endTime: moment()
                        })
                    },
                    {
                        text: '近40天',
                        value: 'fourtyDay',
                        getTime: () => ({
                            startTime: moment().subtract(40, 'day'),
                            endTime: moment()
                        })
                    }
                ],
                value: 'oneHour',
                timeRange: {}
            },
            chartConfig: {},
            shortcutItems,
            range: {
                begin: new Date(moment().subtract(40, 'day').valueOf()),
                end: new Date(moment().valueOf())
            },
            timeRange: {
                begin: new Date(moment().subtract(1, 'hour').valueOf()),
                end: new Date(moment().valueOf())
            }
        };
    }

    static computed = {
        bcmMetrics() {
            if (this.data.get('monitorType') === 'qos') {
                return qosMetricMap.get(this.data.get('service.option'));
            }
            return [];
        }
    };

    attached() {
        this.initChartConfig();
        this.initBcmSdk();
        this.loadMetrics();
    }

    initChartConfig() {
        if (this.data.get('monitorType') === 'qos') {
            this.data.set('ds', {
                option: 'bandwidth',
                options: [
                    {text: '带宽', value: 'bandwidth'},
                    {text: '包速率', value: 'package'},
                    {text: '丢包数', value: 'lossPkg'}
                ]
            });
        }
        const {scope, dimensions, statistics, apiType, metrics, option} = this.data.get('options');
        this.data.set('chartConfig', {
            scope,
            dimensions,
            statistics,
            period: 60,
            apiType,
            region: option.region
        });
        this.data.set('etMetric', metrics);
    }

    initBcmSdk() {
        contextPipe.getCurrentRegion = () => {
            return this.data.get('chartConfig.region') || 'bj';
        };
        const bcmSdk = new BcmSDK({}, contextPipe);
        this.data.set('bcmSdk', bcmSdk);
    }

    onTimeChange({value}) {
        this.data.set('chartConfig.startTime', moment(value.begin).utc().format('YYYY-MM-DDTHH:mm:00') + 'Z');
        this.data.set('chartConfig.endTime', moment(value.end).utc().format('YYYY-MM-DDTHH:mm:00') + 'Z');
        let ms = moment(value.end) - moment(value.begin);
        this.data.set('chartConfig.period', Math.round(ms / 3600000) * 60);
        this.loadMetrics();
    }

    loadMetrics() {
        this.nextTick(() => {
            let option = this.data.get('ds.option');
            let etMetric = this.data.get('etMetric');
            if (this.data.get('monitorType') === 'et') {
                this.data.set('metrics.metrics', etMetric[option].metrics);
            } else {
                this.data.set('metrics.metrics', this.data.get('bcmMetrics')[option]);
            }
            this.data.set('metrics.unit', etMetric[option].unit);
            this.data.set('metrics.bitUnit', etMetric[option].bitUnit);
            this.ref('bcmChartPanel').loadMetrics();
        });
    }

    refresh() {
        this.loadMetrics();
    }

    getUtcTime(time) {
        return moment(time).utc().format('YYYY-MM-DDTHH:mm:00') + 'Z';
    }
}
export default Processor.autowireUnCheckCmpt(DcMonitor);
