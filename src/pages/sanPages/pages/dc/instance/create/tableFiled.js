/*
 * @Description: 专线线路套餐表格schema
 * @Author: wang<PERSON><PERSON><EMAIL>
 * @Date: 2022-01-20 17:25:34
 */

export const SCHEMA = [
    {
        name: 'skuId',
        label: '商品编号',
        width: '20%'
    },
    {
        name: 'cityCN',
        label: '城市',
        width: '10%'
    },
    {
        name: 'ispCN',
        label: '运营商',
        width: '10%'
    },
    {
        name: 'lineTypeCN',
        label: '线路性质',
        width: '10%'
    },
    {
        name: 'bandwidth',
        label: '带宽',
        width: '10%'
    },
    {
        name: 'num',
        label: '专线套餐标识',
        width: '10%'
    }
];

export const HIGH_MODE_POINT_COLUMNS = [
    {
        name: 'order',
        label: '接入点序号',
        width: '8%'
    },
    {
        name: 'region',
        label: '地域',
        width: '16%'
    },
    {
        name: 'accessPoint',
        label: '接入点',
        width: '18%'
    },
    {
        name: 'isp',
        label: '物理线路运营商',
        width: '12%'
    },
    {
        name: 'action',
        label: '操作',
        width: '8%'
    }
];
