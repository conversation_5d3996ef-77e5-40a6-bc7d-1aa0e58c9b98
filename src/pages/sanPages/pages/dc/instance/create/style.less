.dc-create-wrap {
    height: 100%;

    .order-confirm-panel {
        width: 100%;
        margin-left: 24px;
        margin-right: 24px;
        .billing-sdk-order-confirm-wrapper-default {
            margin-top: 0px !important;
        }
        .list-wrapper {
            max-height: 350px;
            overflow: auto;
        }
    }
    .s-legend-highlight {
        &::before {
            display: none;
        }
    }

    .purchase-wrap {
        .s-radio-button {
            width: 72px;
        }
        .s-radio-text {
            padding: 0 10px;
            width: 72px;
        }
    }
    .isp-form-class {
        .s-radio-button {
            width: auto;
        }
        .s-radio-text {
            padding: 0 16px;
            width: auto;
        }
    }

    .prices-wrapper {
        justify-content: center;
    }
    .switch-wrap {
        .s-row {
            .s-form-item-control-wrapper {
                display: flex;
                align-items: center;
            }
        }
    }
    .buybucket {
        width: 100%;
        left: 0;
        height: 60px;
        .buybucket-container {
            width: auto;
            float: left;
            height: 48px;
            transform: translateY(12.5%);
            display: flex;
            align-items: center;
            .billing-sdk-protocol-wrapper .buy-agreement {
                margin-bottom: 8px !important;
            }
        }
        .dc-port-tooltip {
            margin-left: 16px;
            .price_label {
                color: #2468f2;
            }
        }
    }
    .tip-icon {
        color: #9e9898;
        border: 1px solid #9e9898;
        margin-left: 10px;

        &:hover {
            border-color: #2468f2;
        }
    }

    .row-line {
        display: flex;
        align-items: center;
    }

    .flavor-line span {
        display: flex;
        align-items: center;
        height: 30px;
    }

    .page-floating-nav {
        z-index: 99;
    }

    .s-form {
        .s-form-item-label {
            min-width: 120px;
        }
        .s-form-item-help {
            .dc-connect-device-tip {
                color: #151b26 !important;
            }
        }
    }

    .require-label {
        position: relative;
        &:before {
            content: '*';
            left: -10px;
            top: 8px;
            position: absolute;
            color: #f33e3e;
            margin-right: 4px;
        }
    }

    .s-input {
        box-sizing: border-box;
    }

    .legend-wrap {
        margin-top: 10px;
        margin-bottom: 20px;
    }

    .mode-wrap {
        display: flex;
        .mode-item {
            display: flex;
            position: relative;
            width: 252px;
            height: 108px;
            align-items: center;
            margin-right: 16px;
            border: 1px solid #e8e9eb;
            border-radius: 6px;
            padding: 14px 12px;
            box-sizing: border-box;
            .mode-img {
                width: 40px;
                height: 40px;
                margin-right: 12px;
            }
            .new-mode {
                position: absolute;
                top: 0;
                right: 0;
            }
            .mode-text {
                display: flex;
                flex-direction: column;
                .mode-name {
                    font-size: 14px;
                    color: #151b26;
                    font-weight: 500;
                    margin-bottom: 8px;
                }
                .mode-desc {
                    font-size: 12px;
                    color: #5e626a;
                }
            }
            &:hover {
                cursor: pointer;
            }
        }
        .actice-mode {
            background: #eef3fe;
            border: 1px solid #2468f2;
        }
    }

    .mode-wrapper {
        display: flex;
        .left-form-part {
            flex: 10;
            .dc-preview-topo {
                cursor: pointer;
            }
        }
        .right-map-part {
            flex: 9;
        }
        .left-form-part {
            margin-right: 30px;

            .map-mode-wrap {
                .map-mode-form-wrap {
                    .s-form-item-control-wrapper {
                        flex: 1;
                    }
                    .search {
                        position: relative;
                        right: 27px;
                    }
                    .search-container {
                        .s-input-area {
                            input {
                                cursor: initial;
                                padding-right: 25px;
                            }
                        }
                        &:hover {
                            .search {
                                .s-icon {
                                    fill: #108cee !important;
                                }
                            }
                        }
                        &:active {
                            .search {
                                .s-icon {
                                    fill: #0d77ca !important;
                                }
                            }
                        }
                    }
                }
            }

            .s-tabnav {
                .s-tabnav-scroll {
                    border-bottom: none;
                }
            }
            .filter-container {
                display: flex;
                .s-form-item {
                    margin-top: 0;
                    margin-right: 8px;
                }
            }
            .ap-container {
                height: 350px;
                overflow-y: auto;
                border: 1px solid #e8e9eb;
                border-radius: 4px;
                margin-top: 16px;
                .ap-item {
                    height: 70px;
                    padding: 12px 0;
                    margin: 0 16px;
                    display: flex;
                    justify-content: space-between;
                    border-bottom: 1px solid #e8e9eb;
                    .ap-info {
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                        .ap-name {
                            font-size: 14px;
                            color: #151b26;
                            font-weight: 500;
                            margin-right: 8px;
                        }
                        .nearest {
                            width: 64px;
                            height: 20px;
                            line-height: 20px;
                            text-align: center;
                            background-color: #2468f2;
                            color: #fff;
                            font-size: 12px;
                            border-radius: 2px;
                        }
                        .ap-title {
                            display: flex;
                        }
                        .ap-location {
                            display: flex;
                            color: #84868c;
                            font-size: 12px;
                            .ap-distance {
                                margin-right: 16px;
                            }
                        }
                    }
                }
            }
        }
        .right-map-part {
            margin-right: 15px;
            #map-container {
                width: 100%;
                height: 680px;
                margin-bottom: 30px;
            }
        }
        .inline-tip {
            top: 3px;
            position: relative;
            .s-tip-warning {
                justify-content: center;
                .warning_class {
                    fill: #999;
                }
            }
            .s-tip:hover .s-icon path {
                fill: #2468f2 !important;
            }
        }
    }
    .map-disable {
        pointer-events: none;
    }

    .content-wrap {
        border-radius: 6px;
        width: calc(~'100% - 8px');
        background-color: #fff !important;
        margin: 16px 0px;
        padding: 24px;
        // .line-menu {
        //     .s-form-item-control-wrapper {
        //         width: calc(~'100% - 100px');
        //     }
        // }
        .dc-line-table {
            // margin-top: 0px !important;
            .s-row {
                display: flex;
                .s-form-item-control-wrapper {
                    flex: 1;
                }
            }
            .s-table {
                overflow: visible;
                .s-table-container {
                    overflow: visible;
                }
            }

            .dc-clear-config {
                padding: 0px;
            }
            .dc-lineSpecification-name {
                font-size: 12px;
                color: #b8babf;
                line-height: 20px;
                font-weight: 400;
                margin-bottom: 20px;
                &:last-child {
                    margin-bottom: 0px;
                }
            }
            .dc-no-multiple {
                margin-bottom: 0px !important;
            }
            .dc-sku-selected {
                color: #151b26;
            }
            .dc-validate-error {
                .s-input-suffix-container {
                    border-color: #f33e3e;
                }
            }
            .dc-highMode-tip {
                height: 20px;
                font-size: 12px;
                color: #f33e3e;
                margin-top: 8px;
            }
            .dc-line-dynamic {
                margin: 5px 0px 8px 0px;
                color: #151b26;
                font-size: 12px;
                font-weight: 400;
            }
            .dc-lineno-fill {
                margin-left: 70px;
            }
        }
        .dc-refresh-portnum {
            cursor: pointer;
            &:hover {
                color: #2468f2;
            }
        }
    }

    .tip-icon-wrap {
        font-size: 14px;
        border: 1px solid #f18823;
        margin-left: 10px;
        color: #f18823;
        &:hover {
            color: #fff;
            background-color: #f18823;
        }
    }

    .s-badge-content {
        z-index: 99;
    }

    .row-line {
        display: flex;
        align-items: center;
    }

    .vpn-renew-wrap {
        margin-top: 10px;
        .renew-title {
            margin-right: 10px;
        }
        .renew-tip {
            margin-left: 10px;
        }
    }

    .renew-item-label {
        .s-form-item-label {
            line-height: inherit;
        }
        .s-switch {
            margin-right: 8px;
        }
    }

    .apaddr_wrap {
        .s-row {
            .s-form-item-control-wrapper {
                flex: 1;
                .s-radio-button-group {
                    .s-radio-button {
                        margin-bottom: 10px;
                    }
                }
            }
        }
    }
    .basic-spc {
        .detail-wrapper {
            display: none !important;
        }
    }
    .skuId-select {
        margin-bottom: 10px;
        &:last-child {
            margin-bottom: 0px;
        }
    }
    .dc-alert {
        width: 100%;
    }
    .order-item-container {
        .billing-sdk-order-legend {
            .item-detail {
                flex-basis: auto !important;
            }
        }
    }
    .s-create-page-content {
        margin: 0 16px 16px;
    }
    .bucket-footer-wrap {
        height: 96px;
        .billing-sdk-protocol-wrapper .buy-agreement .agreement-wrap {
            margin-top: 5px;
            margin-left: 16px;
            margin-bottom: 5px;
        }
        .confirm-wrapper {
            display: block;
        }
        .confirm-footer-wrap {
            display: flex;
            .total-price-wrap {
                margin-left: 16px;
            }
        }
    }
    .tag-wrapper {
        margin-top: 24px;
        .tag-title {
            display: inline-flex;
            font-size: 16px;
            line-height: 24px;
            color: #151b26;
            font-weight: 500;
        }
        .tag-edit-panel {
            .inline-form {
                display: flex;
                width: 616px;
                .s-form-item .s-form-item-label {
                    text-align: left;
                    min-width: 50px !important;
                }
            }
        }
    }
}

.dc-port-tooltip {
    .s-table-qw {
        max-width: 600px !important;
        .dc-price-highlight {
            color: #f33e3e;
            font-weight: 500;
        }
    }
}

.locale-en {
    .dc-create-wrap .s-form .s-form-item-label {
        width: 190px;
    }
}

.dc-topo-dialog-wrapper {
    img {
        width: 800px;
    }
}
