import {defineComponent} from 'san';
import {Dialog, Button} from '@baidu/sui';
import {html} from '@baiducloud/runtime';
import '../style.less';

const modeMappingTopoUrl = {
    STRONGEST_DISASTER_RECOVERY: 'https://bce.bdstatic.com/network-frontend/network/STRONGEST_DISASTER_RECOVERY.png',
    DISASTER_RECOVERY: 'https://bce.bdstatic.com/network-frontend/network/DISASTER_RECOVERY.png',
    DEVELOPMENT_AND_TESTING: 'https://bce.bdstatic.com/network-frontend/network/DEVELOPMENT_AND_TESTING.png',
    LARGE_BANDWIDTH_LB: 'https://bce.bdstatic.com/network-frontend/network/LARGE_BANDWIDTH_LB.png'
};

export default defineComponent({
    template: html`
        <template>
            <s-dialog open="{{true}}" title="{{title}}" class="dc-topo-dialog-wrapper">
                <img src="{{imgSrc}}" alt="" />
                <div slot="footer">
                    <s-button skin="primary" on-click="handleClose">关闭</s-button>
                </div>
            </s-dialog>
        </template>
    `,

    components: {
        's-dialog': Dialog,
        's-button': Button
    },
    initData: function () {
        return {
            imgSrc: ''
        };
    },
    inited: function () {
        const mode = this.data.get('mode');
        this.data.set('imgSrc', modeMappingTopoUrl[mode]);
    },
    handleClose: function () {
        this.dispose();
    }
});
