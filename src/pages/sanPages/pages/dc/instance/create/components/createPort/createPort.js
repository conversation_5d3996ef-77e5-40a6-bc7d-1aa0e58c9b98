/*
 * @Description: 端口资源占用
 * @Author: <EMAIL>
 * @Date: 2022-02-08 15:11:44
 */

import {Component} from 'san';
import u from 'lodash';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {OutlinedQuestion} from '@baidu/sui-icon';
import {PortType, TimeType, Year, Month} from '@/pages/sanPages/common/enum';
import {DocService} from '@/pages/sanPages/common';
import {getConfig, $flag as FLAG} from '@/pages/sanPages/utils/helper';
import Assist from '@/utils/assist';
import Big from 'big.js';
import './style.less';

const {asComponent, invokeSUI, invokeSUIBIZ, invokeAppComp, template} = decorators;
// const kXhrOptions = {'X-silence': true};
const AllRegion = window.$context.getEnum('AllRegion');

const tpl = html`
    <div class="dc-instance-port-wrap">
        <s-form s-ref="form" label-align="left" data="{=formData=}">
            <div class="content-wrap-box">
                <s-app-legend class="legend-wrap" label="{{'购买信息'}}" />
                <s-form-item label="{{'购买时长：'}}" prop="purchaseLength">
                    <s-tag-radio-group
                        radioType="button"
                        value="{=formData.purchaseLength=}"
                        datasource="{{purchaseLengthList}}"
                        class="purchase-btn"
                    >
                    </s-tag-radio-group>
                </s-form-item>
                <s-form-item
                    s-if="!FLAG.NetworkSupportXS"
                    label="{{'自动续费：'}}"
                    prop="purchaseLength"
                    class="renew-item-label"
                >
                    <template slot="label">
                        {{'自动续费：'}}
                        <s-tip class="inline-tip">
                            <s-question class="question-class warning-class"></s-question>
                            <span slot="content">
                                开通自动续费后，百度智能云将在实例到期前7/3/1/0天定时进行自动扣款续费，续费成功或失败都将向您发送短信和邮件提醒。
                                <a
                                    class="assist-tip"
                                    href="javascript:void(0)"
                                    on-click="showAssist()"
                                    s-if="FLAG.NetworkSupportAI"
                                    >了解详情</a
                                >
                            </span>
                        </s-tip>
                    </template>
                    <div class="autoRenew-wrapper">
                        <s-radio-radio-group
                            enhanced
                            value="{=formData.autoRenew=}"
                            radioType="button"
                            datasource="{{autoRenewList}}"
                            class="nat-mode-wrap button-mode"
                        >
                        </s-radio-radio-group>
                        <span class="open-renew-tip">推荐开启</span>
                    </div>
                    <div class="vpn-renew-wrap row-line" s-if="formData.autoRenew">
                        <span class="renew-title">{{ '选择续费周期'}}</span>
                        <s-select
                            width="100"
                            on-change="onRenewUnitChange"
                            datasource="{{renewUnitList}}"
                            value="{=formData.autoRenewTimeUnit=}"
                        >
                        </s-select>
                        <s-select
                            style="margin-left: 8px"
                            width="100"
                            on-change="onRenewTimeChange"
                            datasource="{{renewNumberList}}"
                            value="{=formData.autoRenewTime=}"
                        >
                        </s-select>
                        <span class="renew-tip">
                            {{ '系统将于到期前7天进行扣费，扣费时长为'}}{{ formData.autoRenewTime }}{{autoRenewUnit}}
                        </span>
                    </div>
                </s-form-item>
                <div class="content-wrap-box" s-if="lineChecked">
                    <s-app-legend class="legend-wrap" label="{{'线路铺设费配置信息'}}" />
                    <s-form-item label="{{'付费方式：'}}" prop="purchaseLength">
                        <s-radio-radio-group
                            enhanced
                            class="s-doc-radio"
                            datasource="{{productTypeList}}"
                            value="{{form.productType}}"
                            radioType="button"
                        >
                        </s-radio-radio-group>
                    </s-form-item>
                    <s-form-item s-if="!isHighMode" label="{{'线路规格：'}}" prop="purchaseLength">
                        <span class="lineprice">{{ configInfo.lineSpecificationName || '-'}}</span>
                    </s-form-item>
                    <s-form-item s-if="!isHighMode" label="{{'线路费用：'}}" prop="purchaseLength">
                        <span class="lineprice" s-if="linePrice">{{ linePrice }}元</span>
                        <span class="lineprice" s-else>-</span>
                    </s-form-item>
                    <div class="dc-line-widget" s-if="isHighMode">
                        <div class="dc-line-post" s-for="item, idx in highModeLineCost">
                            <span class="label">线路规格{{idx+1}}：{{item.specifications}}</span
                            ><span class="value">线路费用{{idx+1}}：{{item | lineCost}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </s-form>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class DcInsatanceCreatePort extends Component {
    static components = {
        's-question': OutlinedQuestion
    };
    initData() {
        return {
            FLAG,
            DocService,
            renewNumberList: Month.toArray(),
            renewUnitList: TimeType.toArray('MONTH', 'YEAR'),
            purchaseLengthList: [],
            productTypeList: [
                {
                    text: '预付费',
                    value: 'prepay'
                }
            ],
            form: {
                productType: 'prepay',
                routeStandard: '',
                region: ''
            },
            autoRenewList: [
                {text: '关闭', value: false},
                {text: '开启', value: true}
            ],
            highModeLineCost: []
        };
    }
    static filters = {
        getYear(value) {
            const lineChecked = this.data.get('lineChecked');
            if (lineChecked) {
                return `注：购买${value / 12}年8.3折（不适用于线路铺设费）`;
            } else {
                return `注：购买${value / 12}年8.3折`;
            }
        },
        getIntfType(value) {
            return value ? PortType.getTextFromValue(value) : '-';
        },
        regionFilter(value) {
            return value ? AllRegion.getTextFromValue(value) : '';
        },
        lineCost(item) {
            const {unitPrice, scale} = item;
            return `${new Big(unitPrice).div(new Big(scale))} x ${scale} = ¥${unitPrice}`;
        }
    };
    static computed = {
        autoRenewUnit() {
            const formData = this.data.get('formData');
            return formData?.autoRenewTimeUnit === 'month' ? '月' : '年';
        }
    };
    inited() {
        this.getPurchaseLength();
        this.initFormData();
        // 询价完毕
        this.watch('isPricing', value => {
            if (!value) {
                const buyBucketItems = this.data.get('buyBucketItems');
                if (_.every(buyBucketItems, item => item.unitPrice)) {
                    if (buyBucketItems?.length && buyBucketItems.length !== 1) {
                        // 筛选出线路铺设费并按配置分组
                        const sameLineSpecificationsPay = _.groupBy(
                            _.filter(buyBucketItems, item => item.options.specifications),
                            item => item.options.specifications
                        );
                        const sameLineKeys = Object.keys(sameLineSpecificationsPay);
                        const highModeLineCost = _.map(sameLineKeys, (item, idx) => {
                            const lineItem = sameLineSpecificationsPay[item];
                            const sameLineTotalPrice = lineItem.reduce((pre, cur) => {
                                const {unitPrice} = cur;
                                return new Big(pre).plus(new Big(unitPrice));
                            }, 0);
                            return {
                                specifications: item,
                                scale: `${lineItem.length}`,
                                unitPrice: sameLineTotalPrice
                            };
                        });
                        this.data.set('highModeLineCost', highModeLineCost);
                    }
                }
            }
        });
    }
    attached() {
        this.watch('formData', value => {
            this.fire('portConfigChange', value);
        });
    }
    initFormData() {
        const portInfo = this.data.get('portInfo');
        const basicData = {
            productType: 'prepay',
            purchaseLength: portInfo?.purchaseLength || 1,
            autoRenew: portInfo?.autoRenew || false,
            autoRenewTimeUnit: portInfo?.autoRenewTimeUnit || 'month',
            autoRenewTime: portInfo?.autoRenewTime || 1,
            region: this.data.get('configInfo').region
        };
        this.data.set('formData', {
            ...basicData,
            ...this.data.get('configInfo')
        });
        this.fire('portConfigChange', this.data.get('formData'));
    }
    // 自动续费按月按年
    onRenewUnitChange({value}) {
        this.data.set('formData.autoRenewTimeUnit', value);
        this.changeRenewTimeList(value);
    }
    changeRenewTimeList(value) {
        let timeList = value === 'year' ? Year.toArray() : Month.toArray();
        this.data.set('renewNumberList', timeList);
        this.data.set('formData.autoRenewTime', '');
        this.nextTick(() => {
            this.data.set('formData.autoRenewTime', timeList[0].value);
        });
    }
    getPurchaseLength() {
        getConfig('/bce/config.json').then(data => {
            let datasource = [
                {label: '1个月', value: 1},
                {label: '2个月', value: 2},
                {label: '3个月', value: 3},
                {label: '4个月', value: 4},
                {label: '5个月', value: 5},
                {label: '6个月', value: 6},
                {label: '7个月', value: 7},
                {label: '8个月', value: 8},
                {label: '9个月', value: 9},
                {label: '1年', value: 12, class: 'new-tip', mark: '8.3折'},
                {label: '2年', value: 24, class: 'new-tip', mark: '8.3折'},
                {label: '3年', value: 36, class: 'new-tip', mark: '8.3折'}
            ];

            u.each(datasource, (item, index) => {
                // 虚商暂不支持年付优惠
                if (!FLAG.NetworkSupportXS && index > 8) {
                    item.showBadge = true;
                }
            });
            this.data.set('purchaseLengthList', datasource);
        });
    }
    showAssist() {
        Assist.sendMessageToAssist({
            sceneLabel: 'dc_create',
            message: '自动续费'
        });
    }
}
export default Processor.autowireUnCheckCmpt(DcInsatanceCreatePort);
