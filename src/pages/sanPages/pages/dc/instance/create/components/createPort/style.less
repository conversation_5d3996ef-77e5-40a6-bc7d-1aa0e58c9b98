.dc-instance-port-wrap {
    .content-item {
        display: flex;
        width: 50%;
        margin-bottom: 20px;
        align-items: center;
        .item-key {
            display: inline-block;
            vertical-align: top;
            color: #5e626a;
            margin-right: 16px;
            width: 84px;
        }
        .item-value {
            display: inline-flex;
            color: #151a26;
            max-width: 80%;
            word-break: break-all;
            position: relative;
        }
    }
    .content-item-box {
        display: flex;
        flex-wrap: wrap;
    }
    .text-hidden {
        word-break: break-all;
    }
    .lineprice {
        line-height: 30px;
    }
    .purchase-item-wrap {
        .s-radio-text {
            width: 60px;
        }
    }
    .content-wrap-box {
        margin-top: 24px;
        .autoRenew-wrapper {
            display: flex;
            align-items: center;
        }
        .open-renew-tip {
            padding: 2px 4px;
            box-sizing: border-box;
            margin-left: 10px;
            border-radius: 5%;
            font-size: 10px;
            color: #fff;
            background-color: #f33e3e;
        }
        .purchase-btn {
            .s-radio-button:first-child .s-radio-text {
                border-top-left-radius: 4px;
                border-bottom-left-radius: 4px;
                border-left-width: 1px;
                &::before {
                    display: none;
                }
            }
            .s-radio-button:last-child .s-radio-text {
                border-top-right-radius: 4px;
                border-bottom-right-radius: 4px;
            }
            .s-radio-button-group label:not(.state-disabled) input[type='radio']:checked ~ .s-radio-text {
                background-color: #eef3fe;
            }
        }
        .inline-tip {
            top: 3px;
            position: relative;
            .s-tip-warning {
                justify-content: center;
                .warning_class {
                    fill: #999;
                }
            }
            .s-tip:hover .s-icon path {
                fill: #2468f2 !important;
            }
        }
        .dc-line-widget {
            .dc-line-post {
                padding-left: 7px;
                margin-top: 24px;
                .label {
                    color: #5c5f66;
                    margin-right: 40px;
                    display: inline-block;
                    width: 250px;
                }
                .value {
                    color: #5c5f66;
                }
            }
        }
    }
}
