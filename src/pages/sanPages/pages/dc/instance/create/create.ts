import {columns} from './../../../flowlog/tableFields';
import u, {get, iteratee} from 'lodash';
import {html, decorators, redirect, CreatePage, Processor} from '@baiducloud/runtime';
import Big from 'big.js';
import {ShoppingCart, OrderConfirm, Protocol, TotalPrice} from '@baiducloud/bce-billing-sdk-san';
import {BillingSdk, OrderItem, UPDATE_STATUS} from '@baiducloud/bce-billing-sdk';
import Client from '@baiducloud/httpclient';
import {San2React} from '@baidu/bce-react-toolkit';
import Assist from '@/utils/assist';
import {Tabs, AutoComplete, Select, Alert} from '@baidu/sui';
import dcInstanceCreatePort from './components/createPort/createPort';
import {OutlinedSearch} from '@baidu/sui-icon';
import {TagEditPanel} from '@baidu/bce-tag-sdk-san';

import {regions as REGIONS} from '../../config/regions';
import {loadBaiduMap} from '@/pages/sanPages/utils/baidumap';
import {PayType, PortType, ISP} from '@/pages/sanPages/common/enum';
import {ContextService, DocService} from '@/pages/sanPages/common';
import {getEtAvaliableRegion, $flag as FLAG, contextPipe, getUserId} from '@/pages/sanPages/utils/helper';
import {
    getConfirmConfig,
    getPortConfirmConfig,
    validateRules,
    AVAILABLE_DEVICE_NUM,
    getDeviceNumByPortNum
} from './helper';
import PreviewDialog from './components/previewTopo';
import {SCHEMA, HIGH_MODE_POINT_COLUMNS} from './tableFiled';
import HIGH_MODE_IMG from '@/img/high_mode.svg?url';
import {Add, NarrowTheSides, Refresh, TheLeftSideNarrowed} from '@baidu/xicon-san';
import {use} from 'echarts';

interface Address {
    province: string;
    city: string;
    district: string;
}

interface OrderItemConfig {
    config: Record<string, any>;
    paymentMethod: Array<{type: string; values: Array<number | string>}>;
}

interface OrderParams {
    paymentMethod: Array<any>;
    items: Array<OrderItemConfig>;
}

const {invokeSUI, invokeSUIBIZ, invokeComp, invokeAppComp, template} = decorators;
const kXhrOptions = {'X-silence': true};
const AllRegion = ContextService.getEnum('AllRegion');
const standardModeImg = 'https://bce.bdstatic.com/network-frontend/standard-mode.png';
const mapModeImg = 'https://bce.bdstatic.com/network-frontend/map-mode.png';
const positionImg = 'https://bce.bdstatic.com/network-frontend/position.png';
const buildingImg = 'https://bce.bdstatic.com/network-frontend/building.png';
const buildingClickImg = 'https://bce.bdstatic.com/network-frontend/building-click.png';
const newImg = 'https://bce.bdstatic.com/network-frontend/new.svg';
const tpl =
    html`
<template>
    <s-app-create-page class="{{klass}}" backTo="{{pageTitle.backTo}}"
        backToLabel="{{pageTitle.label}}" pageTitle="{{pageTopTitle}}">
        <s-steps class="s-step-block" current="{{steps.current}}">
            <s-steps-step s-for="i in steps.datasource" title="{{i.title}}"/>
        </s-steps>
        <s-alert skin="warning" class="dc-alert">
          温馨提示：请在<a href="{{ContextService.Domains.console}}/mc/#/mc/settings" target="_blank">消息中心</a>配置消息接收人，并注意更新联系方式。为做到消息通知传递的可靠性，建议添加多个联系人。
        </s-alert>
        <div class="content-wrap" s-if="{{steps.current === 1}}">
            <s-form s-ref="form"
                label-align="left"
                data="{=formData=}" rules="{{rules}}">
                <div class="mode-wrapper {{locateDisable ? 'map-disable' : ''}}">
                    <div class="content-wrap-box left-form-part">
                        <s-app-legend class="legend-wrap" label="{{'配置信息'}}"></s-app-legend>
                        <s-form-item s-if="isShowName" label="{{'专线名称：'}}" class="require-label" prop="name">
                            <s-input value="{=formData.name=}"
                            placeholder="{{'请输入'}}" width="200"
                            ></s-input>
                        </s-form-item>
                        <s-form-item label="{{'接入点模式：'}}" class="require-label" prop="pointMode">
                            <template slot="label" class="label_class">
                                {{'接入点模式：'}}
                                <s-tip
                                    class="inline-tip"
                                    content="高可靠模式：支持用户批量创建专线。标准模式：用户手动选择接入点方式。地图模式：用户需允许浏览器获取用户当前位置或基于用户手填位置进行百度智能云专线接入点推荐。"
                                    skin="question"
                                    />
                            </template>
                            <div class="mode-wrap">
                                <div on-click="changeMode(item.value)"  s-for="item in modeList" class="mode-item {{item.value === formData.pointMode && 'actice-mode'}}">
                                    <img
                                        class="mode-img"
                                        src="{{item.value === 'map'
                                                ? imgSrc.mapModeImg : item.value === 'highReliability'
                                                    ? imgSrc.HIGH_MODE_IMG:imgSrc.standardModeImg}}"
                                        alt=""
                                    >
                                    <img s-if="{{item.value === 'highReliability'}}" class="new-mode" src="{{imgSrc.newImg}}" alt="">
                                    <div class="mode-text">
                                        <span class="mode-name">{{item.text}}</span>
                                        <span class="mode-desc">{{item.desc}}</span>
                                    </div>
                                </div>
                            </div>
                        </s-form-item>
                        <!--高可靠模式组合模式-->
                        <s-form-item s-if="isHighMode" label="组合模式：" prop="mode">
                            <s-radio-radio-group
                                enhanced
                                value="{=formData.mode=}"
                                radioType="button"
                                datasource="{{combineModeList}}"
                            >
                            </s-radio-radio-group>
                            <div slot="help">
                                <div class="help-tip">
                                    <!--bca-disable-next-line-->
                                    <div>{{combineModeTip | raw}}</div>
                                </div>
                            </div>
                        </s-form-item>
                        <div s-if="isStandardMode" class="standard-mode-wrap">
                            <s-form-item label="{{'地域：'}}" prop="region">
                                <s-select
                                    datasource="{{regionList}}"
                                    value="{=formData.region=}"
                                    width="200"
                                >
                                </s-select>
                            </s-form-item>
                            <s-form-item label="{{'描述：'}}" prop="description">
                                <s-input value="{=formData.description=}"
                                placeholder="{{'描述不能超过200字符'}}" width="200"
                                maxLength="200"
                                ></s-input>
                            </s-form-item>
                            <s-form-item class="apaddr_wrap" label="{{'接入点：'}}" prop="apAddr">
                                <s-radio-radio-group
                                    enhanced
                                    value="{=formData.apAddr=}"
                                    radioType="button"
                                    datasource="{{apAddrList}}"
                                >
                                </s-radio-radio-group>
                                <a href="{{DocService.dc_apAddr}}">百度智能云专线接入点</a>
                            </s-form-item>
                        </div>
                        <div s-else-if="{{isMapMode}}" class="map-mode-wrap">
                            <s-form-item label="{{'接入点：'}}" class="map-mode-form-wrap" prop="apAddr">
                                <s-tabs on-change="changeTab" active="{{activeTab}}">
                                    <s-tabpane key="all" label="所有接入点">
                                        <div class="filter-container">
                                            <s-form-item prop="region">
                                                <s-select
                                                    placeholder="请选择地域"
                                                    datasource="{{regionList}}"
                                                    value="{=formData.region=}"
                                                    on-change="drawApOnMap"
                                                    width="426"
                                                >
                                                </s-select>
                                            </s-form-item>
                                        </div>
                                        <div class="ap-container">
                                            <div s-for="item in mapModeApList" class="ap-item">
                                                <div class="ap-info">
                                                    <div class="ap-title">
                                                        <span class="ap-name">{{item.apName}}</span>
                                                        <div s-if="{{item.nearest}}" class="nearest">距离最近</div>
                                                    </div>
                                                    <div class="ap-location">
                                                        <span s-if="currentLocation" class="ap-distance">{{item.distance}}公里</span>
                                                        <span class="ap-region">{{item.region | regionFilter}}</span>
                                                    </div>
                                                </div>
                                                <s-button skin="enhance" on-click="chooseAp(item)">{{activeAp === item.apId ? '取消选择' : '使用此接入点'}}</s-button>
                                            </div>
                                        </div>
                                    </s-tabpane>
                                    <s-tabpane key="near" label="附近接入点">
                                        <div class="search-container">
                                            <s-autocompelete
                                                on-search="searchAddress"
                                                on-change="setLocation($event)"
                                                width="400"
                                                placeholder="请输入用户端地址，会按照位置由近到远展示接入点"
                                            >
                                                <s-option
                                                    s-for="item in searchAddressList"
                                                    value="{{item.value}}"
                                                    label="{{item.label}}">
                                                </s-option>
                                            </s-autocompelete>
                                            <outlined-search on-click="onSearch" class="search"/>
                                        </div>

                                        <div class="ap-container">
                                            <div s-for="item in mapModeApList" class="ap-item">
                                                <div class="ap-info">
                                                    <div class="ap-title">
                                                        <span class="ap-name">{{item.apName}}</span>
                                                        <div s-if="{{item.nearest}}" class="nearest">距离最近</div>
                                                    </div>
                                                    <div class="ap-location">
                                                        <span s-if="currentLocation" class="ap-distance">{{item.distance}}公里</span>
                                                        <span class="ap-region">{{item.region | regionFilter}}</span>
                                                    </div>
                                                </div>
                                                <s-button skin="enhance" on-click="chooseAp(item)">{{activeAp === item.apId ? '取消选择' : '使用此接入点'}}</s-button>
                                            </div>
                                        </div>
                                    </s-tabpane>
                                </s-tabs>
                            </s-form-item>
                        </div>

                    </div>
                    <div s-if="isMapMode" class="right-map-part">
                        <div id="map-container"></div>
                    </div>
                </div>

                <div class="content-wrap-box">
                    <s-form-item s-if="!isHighMode" class="require-label isp-form-class" label="{{'物理线路运营商：'}}" prop="isp">
                        <s-radio-radio-group
                            enhanced
                            value="{=formData.isp=}"
                            radioType="button"
                            datasource="{{ispList}}"
                            class="purchase-wrap"
                        >
                        </s-radio-radio-group>
                        <div slot="help">
                            <div class="help-tip" s-if="{{formData.isp}}">
                                <div>1.{{getHelpTip}}</div>
                                <div>{{'2. 请务必根据国家相关法律法规，选择具有相应电信业务经营资质的服务商，以避免由此带来的线路中断风险'}}</div>
                            </div>
                        </div>
                    </s-form-item>
                    <s-form-item class="require-label" label="{{'物理端口规格：'}}" prop="intfType">
                        <s-radio-radio-group
                            enhanced
                            value="{=formData.intfType=}"
                            radioType="button"
                            datasource="{{isHighMode ? highModeIntTypeList : intfTypeList}}"
                            class="purchase-wrap"
                        >
                        </s-radio-radio-group>
                        <div slot="help">
                            <div class="help-tip">
                                <div>{{ispSizeTip}}</div>
                            </div>
                        </div>
                    </s-form-item>
                    <!--购买端口规数-->
                    <s-form-item s-if="isLargeBandwidth" class="require-label" label="{{'购买端口数：'}}" prop="portNum">
                        <s-input-number
                            displayMode="enhanced"
                            width="120"
                            max="{{largeBandwidthMaxLimit}}"
                            min="{{2}}"
                            precision="{{0}}"
                            stepStrictly
                            value="{=formData.portNum=}"
                            on-input="handlePortNumChange"
                            on-change="handlePortNumChange"
                        />
                        <x-refresh class="dc-refresh-portnum" on-click="handleRefreshLargeQuota"></x-refresh>
                        <div slot="help">输入范围为2~{{largeBandwidthMaxLimit}}，如不满足，请到&nbsp;<a href="/quota_center/#/quota/apply/create?serviceType=ET` +
    `&region=global&cloudCenterQuotaName=dcphyQuota" target="_blank">配额管理</a>&nbsp;页面申请</div>
                    </s-form-item>
                    <s-form-item class="require-label" s-if="isLargeBandwidth" label="接入设备：" prop="deviceNum">
                        <s-radio-radio-group
                            enhanced
                            value="{=formData.deviceNum=}"
                            radioType="button"
                        >
                            <s-radio
                                s-for="item, idx in availableConnectDevice"
                                label="{{item.label}}"
                                value="{{item.value}}"
                                disabled="{{item.disabled}}"
                                tip="{{item.disabled ? '设备数必须能被端口数整除' : ''}}"
                            >
                            </s-radio>
                        </s-radio-radio-group>
                        <!--<s-input-number
                            displayMode="enhanced"
                            width="120"
                            max="{{4}}"
                            min="{{1}}"
                            precision="{{0}}"
                            stepStrictly
                            value="{=formData.deviceNum=}"
                        />-->
                        <div slot="help">
                            端口会被接入设备数平均分配。
                        </div>
                    </s-form-item>
                    <s-form-item s-if="!isHighMode" class="" label="{{'对端地址：'}}" class="require-label" prop="address">
                        <div class="region-wrap">
                            <s-select value="{=address.province=}"
                                width="200"
                                datasource="{{provinceList}}"
                            ></s-select>
                            <s-select value="{=address.city=}"
                                width="200"
                                datasource="{{cityList}}"
                            ></s-select>
                            <s-select value="{=address.district=}"
                                width="200"
                                datasource="{{districtList}}"
                            ></s-select>
                        </div>
                        <div style="margin-top:10px">
                            <s-input value="{=formData.address=}"/>
                        </div>
                    </s-form-item>
                    <s-form-item label="{{'线路铺设：'}}" class="switch-wrap" s-if="{{standardWhite}}">
                        <s-switch checked="{=checked=}" on-change="onChange($event)"/>
                    </s-form-item>
                    <!--高可靠模式接入点信息-->
                    <s-form-item s-if="isHighMode" label="接入点信息：" class="require-label dc-line-table">
                        <div s-if="checked && isLargeBandwidth" class="dc-line-dynamic">{{largeBandwidthConfigTip}}</div>
                        <s-table columns="{{pointTable.columns}}" datasource="{{pointTable.datasource}}">
                            <template slot="h-lineSpecification">
                                <div>
                                    <span>线路套餐编号</span>
                                    <s-tooltip content="{{'默认填充第一个线路套餐编号，如需修改请手动进行修改。'}}">
                                        <s-button s-if="isLargeBandwidth" class="dc-lineno-fill" on-click="handleOneClickFill" skin="stringfy">一键填充</s-button>
                                    </s-tooltip>
                                </div>
                            </template>
                            <div slot="c-order">
                                {{rowIndex+1}}
                            </div>
                            <div slot="c-region" prop='highModeRegion'>
                                <s-select
                                    getPopupContainer="{{handleGetPopupContainer}}"
                                    placeholder="请选择地域"
                                    datasource="{{regionList}}"
                                    value="{=highModeRegion[rowIndex]=}"
                                    on-change="handleHighModeRegionChange(rowIndex, $event)"
                                    width="140"
                                    class="{{highValidateTip[rowIndex].highModeRegion ? 'dc-validate-error' : ''}}"
                                >
                                </s-select>
                                <div s-if="highValidateTip[rowIndex].highModeRegion" class="dc-highMode-tip">请选择地域</div>
                            </div>
                            <div slot="c-accessPoint" prop="{{highModePoint[rowIndex]}}">
                                <s-select
                                    getPopupContainer="{{handleGetPopupContainer}}"
                                    value="{=highModePoint[rowIndex]=}"
                                    datasource="{{highModePointList[rowIndex]}}"
                                    on-change="handleHighPointChange(rowIndex, $event)"
                                    width="160"
                                    class="{{highValidateTip[rowIndex].highModePoint ? 'dc-validate-error' : ''}}"
                                >
                                </s-select>
                                <div s-if="highValidateTip[rowIndex].highModePoint" class="dc-highMode-tip">请选择接入点</div>
                            </div>
                            <div slot="c-isp" prop="highModeIsp">
                                <s-select
                                    getPopupContainer="{{handleGetPopupContainer}}"
                                    value="{=highModeIsp[rowIndex]=}"
                                    datasource="{{highModeIspList[rowIndex]}}"
                                    on-change="handleHighIspChange(rowIndex, $event)"
                                    width="100"
                                    class="{{highValidateTip[rowIndex].highModeIsp ? 'dc-validate-error' : ''}}"
                                >
                                </s-select>
                                <div s-if="highValidateTip[rowIndex].highModeIsp" class="dc-highMode-tip">请选择线路运营商</div>
                            </div>
                            <div slot="c-lineSpecification" prop="highModeLineSpecification">
                                <div
                                    class="skuId-select {{isLineSpecificationNoMB ? 'dc-no-multiple' : ''}}"
                                    s-for="item, idx in lineSpecificationList[rowIndex]"
                                >
                                    <s-select
                                        getPopupContainer="{{handleGetPopupContainer}}"
                                        filterable
                                        value="{=highModeLineSpecification[rowIndex][idx]=}"
                                        on-change="handleLineSpecificationChange($event, rowIndex, idx)"
                                        width="240"
                                    >
                                        <s-select-option
                                            s-for="item, idx in highModeSkuIdDatasource"
                                            label="{{item.label}}"
                                            value="{{item.value}}"
                                        >
                                            <s-tooltip placement="top" trigger="hover">
                                                <div slot="content">{{item.label}}</div>
                                                <div>{{item.label}}</div>
                                            </s-tooltip>
                                        </s-select-option>
                                    </s-select>
                                    <div class="dc-highMode-tip" s-if="highValidateTip[rowIndex][idx].highModeLineSpecification">请选择线路套餐</div>
                                </div>
                            </div>
                            <div slot="c-action">
                                <s-button class="dc-clear-config" on-click="handleClearConfig(rowIndex)" skin="stringfy">清空配置</s-button>
                            </div>
                        </s-table>
                    </s-form-item>
                    <s-form-item s-if="isHighMode" class="" label="{{'对端地址：'}}" class="require-label" prop="address">
                        <div class="region-wrap">
                            <s-select value="{=address.province=}"
                                width="200"
                                datasource="{{provinceList}}"
                            ></s-select>
                            <s-select value="{=address.city=}"
                                width="200"
                                datasource="{{cityList}}"
                            ></s-select>
                            <s-select value="{=address.district=}"
                                width="200"
                                datasource="{{districtList}}"
                            ></s-select>
                        </div>
                        <div style="margin-top:10px">
                            <s-input value="{=formData.address=}"/>
                        </div>
                    </s-form-item>
                    <s-form-item
                        label="{{'线路套餐：'}}"
                        class="require-label line-menu"
                        prop="lineSpecification"
                        s-if="checked && !isHighMode"
                    >
                        <s-select
                            filterable
                            value="{=skuId=}"
                            datasource="{{skuIdDatasource}}"
                            on-change="skuIdChange($event)"
                            width="200"
                            class="skuId-select">
                        </s-select>
                    </s-form-item>
                    <s-form-item class="dc-line-table" label=" " s-if="checked && !isHighMode">
                        <s-table
                            class="menu-table"
                            columns="{{table.columns}}"
                            datasource="{{table.datasource}}"
                            selection="{{table.selection}}"
                            on-selected-change="onSelectChange"
                        >
                            <div slot="empty">
                                <s-empty>
                                    <div slot="action">
                                    </div>
                                </s-empty>
                            </div>
                            <div slot="c-skuId">{{row.skuId || '-'}}</div>
                            <span slot="h-ispCN-lable">
                                {{'运营商'}}
                                <s-tooltip content="包含联通、移动、电信、其他（大陆为广电、中信，海外第三方）">
                                    <s-icon class="question-icon" name="bcmquestion"/>
                                </s-tooltip>
                            </span>
                            <span slot="h-lineTypeCN-lable">
                                {{'线路性质'}}
                                <s-tooltip content="包含本地、长途、光纤">
                                    <s-icon class="question-icon" name="bcmquestion"/>
                                </s-tooltip>
                            </span>
                            <span slot="h-num-lable">
                                {{'专线套餐标识'}}
                                <s-tooltip content="用于区分相同专线不同价格，输入范围包含a-z">
                                    <s-icon class="question-icon" name="bcmquestion"/>
                                </s-tooltip>
                            </span>
                        </s-table>
                    </s-form-item>
                    <dc-instance-create-port
                        configInfo="{{formData}}"
                        portInfo="{{portFormData}}"
                        lineChecked="{{checked}}"
                        apAddrList="{{apAddrList}}"
                        linePrice="{{linePrice}}"
                        isHighMode="{{isHighMode}}"
                        buyBucketItems="{{buyBucketItems}}"
                        isPricing="{=isPricing=}"
                        on-portConfigChange="onPortChange"
                    />
                    <div class="tag-wrapper" s-if="!FLAG.NetworkVpcSupOrganization">
                        <h4 class="tag-title">标签</h4>
                        <s-form-item prop="tag" label="绑定标签：">
                            <tag-edit-panel
                                instances="{{defaultInstances}}"
                                options="{{tagListRequster}}"
                                s-ref="tagPanel"
                            />
                        </s-form-item>
                    </div>
            </s-form>
        </div>
        <div s-else class="order-confirm-panel">
            <order-confirm
                items="{{buyBucketItems}}"
                s-ref="orderConfirm"
                mergeBy="{{mergePriceDisplay}}"
                couponMergeBy="{{mergePriceDisplay}}"
                sdk="{{sdk}}"
                theme="default"
                useCoupon="{{useCoupon}}"
                showAgreementCheckbox
            />
        </div>
        <div class="buybucket {{steps.current === 2 ? 'bucket-footer-wrap' : ''}}" slot="pageFooter">
            <div class="buybucket-container" s-if="steps.current === 1">
                <s-tooltip trigger="{{(updating && disableTip) || (portUpdating && portDisableTip) || isDisableNext ? 'hover' : ''}}">
                    <div slot="content">
                    <!--bca-disable-next-line-->
                        {{disableTip || portDisableTip || isDisableNext | raw}}
                    </div>
                    <s-button on-click="goToConfirm" skin="primary" size="large"
                        disabled="{{updating || (portUpdating && portDisableTip) || isDisableNext}}">
                        {{'下一步'}}
                    </s-button>
                </s-tooltip>
                <s-button size="large" on-click="cancel">取消</s-button>
                <s-tooltip class="dc-port-tooltip">
                    <span class="price_label">(配置费用明细)</span>
                    <div slot="content">
                        <div s-if="!isHighMode">
                            <span class="region">地域：{{formData.region | regionFilter}}、{{apAddrName}}</span>
                        </div>
                        <s-table
                            loading="{{loading}}"
                            class="s-table-qw"
                            width="{{200}}"
                            columns="{{portColumns}}"
                            datasource="{{configDataSource}}">
                            <div slot="empty">
                                <s-empty>
                                    <div slot="action"></div>
                                </s-empty>
                            </div>
                            <div slot="c-config">
                                <!--bca-disable-next-line-->
                                {{row.config | getIntfType |raw}}
                            </div>
                            <div slot="c-price">
                                <span class="dc-price-highlight">{{row.price}}</span>
                            </div>
                        </s-table>
                    </div>
                </s-tooltip>
                <shopping-cart
                    sdk="{{sdk}}"
                    on-reset="onReset"
                    on-change="onShoppingCartChange"
                    addItemToCartAvailable="{{addItemToCartAvailable}}"
                    addItemToCartDisable="{=priceLoading=}"
                    couponMergeBy="{{false}}"
                    mergeBy="{{mergePriceDisplay}}"
                    theme="default"
                    class="basic-spc"/>
            </div>
            <div class="buybucket-container confirm-wrapper" s-else>
                <billing-protocol s-if="!FLAG.NetworkSupportXS" s-ref="billingProtocol" />
                <div class="confirm-footer-wrap">
                  <s-button  on-click="backToOrder(1)" size="large"
                      >{{'上一步'}}</s-button>
                  <s-button size="large" on-click="cancel">取消</s-button>
                  <s-button skin="primary" size="large"
                      on-click="onConfirm" disabled="{{confirming}}">{{'提交订单'}}</s-button>
                  <total-price class="total-price-wrap" sdk="{{sdk}}" />
                </div>
            </div>
        </div>
    </s-app-create-page>
</template>
`;

@template(tpl)
@invokeAppComp
@invokeSUI
@invokeSUIBIZ
class EtInstanceCreate extends CreatePage {
    REGION_CHANGE_LOCATION = '#/dc/instance/list';
    static components = {
        'order-confirm': OrderConfirm,
        'shopping-cart': ShoppingCart,
        's-tab': Tabs,
        's-tabpane': Tabs.TabPane,
        's-autocompelete': AutoComplete,
        'outlined-search': OutlinedSearch,
        's-option': Select.Option,
        's-alert': Alert,
        'billing-protocol': Protocol,
        'total-price': TotalPrice,
        'tag-edit-panel': TagEditPanel,
        'dc-instance-create-port': dcInstanceCreatePort,
        'x-refresh': Refresh
    };

    static computed = {
        updating() {
            let loadingPrice = this.data.get('loadingPrice');
            let isEnterprise = this.data.get('isEnterprise');
            let quota = this.data.get('quota');
            let isQuotaOver = quota && quota.free <= 0;
            return loadingPrice || !isEnterprise || isQuotaOver;
        },
        portUpdating() {
            let loadingPrice = this.data.get('loadingPrice');
            let isEnterprise = this.data.get('isEnterprise');
            let quota = this.data.get('quota');
            let isQuotaOver = quota && quota.free <= 0;
            return loadingPrice || !isEnterprise || isQuotaOver;
        },
        disableTip() {
            let loadingPrice = this.data.get('loadingPrice');
            let isEnterprise = this.data.get('isEnterprise');
            let quota = this.data.get('quota');
            const {region} = this.data.get('formData');
            let isQuotaOver = quota && quota.free <= 0;
            if (isQuotaOver) {
                if (FLAG.NetworkSupportXS) {
                    return '您暂时没有可申请的物理专线配额';
                } else {
                    return (
                        '您暂时没有可申请的物理专线配额，如需更多配额，可' +
                        `<a href="/quota_center/#/quota/apply/create?serviceType=ET` +
                        `&region=global&cloudCenterQuotaName=dcphyQuota" target="_blank">去申请配额</a>`
                    );
                }
            }
            if (!isEnterprise) {
                if (FLAG.NetworkSupportXS) {
                    return '您暂时没有可申请的物理专线配额';
                } else {
                    return (
                        '您暂时没有可申请的物理专线配额，申请物理专线需要通过企业认证' +
                        `<a href="${ContextService.Domains.console}/qualify/#/qualify/company" target="_blank">企业认证入口</a>`
                    );
                }
            }

            if (loadingPrice) {
                return '价格加载中';
            }
            return '';
        },
        pageTopTitle() {
            let current = this.data.get('steps.current');
            if (current > 1) {
                return '购买物理专线';
            } else {
                return '申请物理专线';
            }
        },
        portDisableTip() {
            let posPurchaseValid = this.data.get('posPurchaseValid');
            if (posPurchaseValid && !posPurchaseValid.status) {
                return posPurchaseValid.failReason
                    ? posPurchaseValid.failReason +
                          '，请及时<a href="/finance/#/finance/account/recharge" target="_blank">充值</a>'
                    : '';
            }
            let loadingPrice = this.data.get('loadingPrice');
            if (loadingPrice) {
                return '价格加载中';
            }
            return '';
        },

        getHelpTip() {
            const ispType = this.data.get('formData.isp');
            if (ispType === ISP.ISP_OTHER) {
                return '您选择的接入点机房运营商主体是第三方IDC，你可以购买任意一家同时具备A26及A14资质运营商专线。';
            }
            const typeText = ISP.getTextFromValue(ispType);
            return `您选择的接入点机房运营商${typeText}，请向${typeText}购买专线接入。`;
        },

        apAddrName() {
            let apAddr = this.data.get('formData.apAddr');
            return u.find(this.data.get('apAddrList'), {value: apAddr})?.text;
        },

        // 地图模式接入点list
        mapModeApList() {
            let pointMode = this.data.get('formData.pointMode');
            let allList = this.data.get('allApList') || [];
            let allDistanceList = [];
            let currentLocation = this.data.get('currentLocation');
            if (currentLocation) {
                let BMap = this.data.get('BMap');
                let map = this.data.get('map');
                allDistanceList = allList.map(item => {
                    item.nearest = false;
                    let apLocation = item.apLocation;
                    let distance = map.getDistance(
                        new BMap.Point(currentLocation.lng, currentLocation.lat),
                        new BMap.Point(apLocation[0], apLocation[1])
                    );
                    let kilometer = (distance / 1000).toFixed(2);
                    return {
                        ...item,
                        distance: kilometer
                    };
                });
            } else {
                allDistanceList = allList;
            }
            allDistanceList = allDistanceList.filter((item: any) => !['M2A', 'SHPBS', 'SHWGQ'].includes(item.apId));
            if (pointMode === 'map') {
                if (this.data.get('activeTab') === 'all') {
                    let {region} = this.data.get('formData');
                    let checkList = {region};
                    let filterList = allDistanceList.filter(item => {
                        let flag = true;
                        for (let key in checkList) {
                            let val = checkList[key];
                            if (!flag) {
                                return flag;
                            }
                            if (val) {
                                if (key === 'region') {
                                    flag = item.region === val;
                                } else {
                                    flag = item[key].includes(val);
                                }
                            }
                        }
                        return flag;
                    });
                    if (currentLocation) {
                        // 根据位置排序
                        filterList.sort((x, y) => x.distance - y.distance);
                        filterList[0].nearest = true;
                    }
                    return filterList;
                } else {
                    if (currentLocation) {
                        let filterList = allDistanceList.sort((x, y) => x.distance - y.distance);
                        filterList[0].nearest = true;
                        return filterList;
                    }
                    return allDistanceList;
                }
            } else {
                return [];
            }
        },
        isHighMode() {
            const pointMode = this.data.get('formData.pointMode');
            return pointMode === 'highReliability';
        },
        isStandardMode() {
            const pointMode = this.data.get('formData.pointMode');
            return pointMode === 'standard';
        },
        isMapMode() {
            const pointMode = this.data.get('formData.pointMode');
            return pointMode === 'map';
        },
        ispSizeTip() {
            const pointMode = this.data.get('formData.pointMode');
            let tip = '只支持光口';
            if (pointMode === 'highReliability') {
                tip = '只支持光口，选择的端口规格将应用于每个不同接入点申请的资源。';
            }
            return tip;
        },
        combineModeTip() {
            const mode = this.data.get('formData.mode');
            const modeMappingTip = {
                STRONGEST_DISASTER_RECOVERY:
                    '您将申请2个接入点内的资源，建立4个独立的专线连接，为关键工作负载提供最强容灾能力。',
                DISASTER_RECOVERY: '您将申请2个接入点内的资源，建立2个独立的专线连接，为关键工作负载提供最强容灾能力。',
                DEVELOPMENT_AND_TESTING:
                    '您将申请1个接入点内的资源，建立2个独立的专线连接，为非关键工作负载提供开发和测试恢复能力。',
                LARGE_BANDWIDTH_LB:
                    '您将申请1个接入点内的资源，默认支持同一台设备接入多个物理端口，可选支持1～4台设备接入，<br />适用于大带宽场景，如单个专线接入点带宽超200G，单个接入点最多可批量创建16条专线。'
            };
            return modeMappingTip[mode];
        },
        isMultipleLine() {
            const mode = this.data.get('formData.mode');
            return mode === 'STRONGEST_DISASTER_RECOVERY';
        },
        isLargeBandwidth() {
            const mode = this.data.get('formData.mode');
            return mode === 'LARGE_BANDWIDTH_LB';
        },
        largeBandwidthConfigTip() {
            const portNum = this.data.get('formData.portNum');
            return `当前购买${portNum}个端口，线路套餐编号及名称可配置${portNum}个。`;
        },
        isLineSpecificationNoMB() {
            const mode = this.data.get('formData.mode');
            return mode === 'DISASTER_RECOVERY';
        },
        // 高可靠模式配置费用明细
        configDataSource() {
            const isPricing = this.data.get('isPricing');
            if (!isPricing) {
                const checked = this.data.get('checked');
                const formData = this.data.get('formData');
                const {intfType} = formData;
                const buyBucketItems = this.data.get('buyBucketItems');
                // 询价完毕
                if (_.every(buyBucketItems, item => item.unitPrice || item.unitPrice === 0)) {
                    let portNum = 0;
                    const TotalPortPayCost = buyBucketItems?.reduce((pre, cur, index) => {
                        const flavor = cur?.options?.flavor;
                        const isPortPay = _.some(flavor, item => item.name.includes('Port'));
                        let num = pre;
                        if (isPortPay) {
                            portNum++;
                            num = new Big(num).plus(cur.unitPrice);
                        }
                        return num;
                    }, 0);
                    if (checked && buyBucketItems?.length && buyBucketItems.length !== 1) {
                        const sameLineSpecificationsPay = _.groupBy(
                            _.filter(buyBucketItems, item => item.options.specifications),
                            item => item.options.specifications
                        );
                        const sameLineKeys = Object.keys(sameLineSpecificationsPay);
                        const dataSource = _.map(sameLineKeys, (item, idx) => {
                            const lineItem = sameLineSpecificationsPay[item];
                            const sameLinePrice = lineItem.reduce((pre, cur) => {
                                const {unitPrice} = cur;
                                return new Big(pre).plus(new Big(unitPrice));
                            }, 0);
                            return {
                                name: `线路铺设费${sameLineKeys.length > 1 ? idx + 1 : ''}`,
                                config: `${item}`,
                                price: '¥' + sameLinePrice,
                                unitPrice: `¥${lineItem?.[0]?.unitPrice}`,
                                num: lineItem.length
                            };
                        });
                        const totalLinePrice = dataSource.reduce((pre, cur) => {
                            return new Big(pre).plus(new Big(+cur.price.slice(1)));
                        }, 0);

                        const totalPrice = new Big(totalLinePrice).plus(new Big(TotalPortPayCost));
                        return [
                            ...dataSource,
                            {
                                name: '端口资源占用费',
                                config: intfType,
                                price: '¥' + TotalPortPayCost,
                                unitPrice: `¥${new Big(TotalPortPayCost || 0).div(new Big(portNum || 1))}`,
                                num: portNum
                            },
                            {name: '总价', config: '', price: '¥' + totalPrice, unitPrice: '-', num: '-'}
                        ];
                    } else {
                        return [
                            {
                                name: '端口资源占用费',
                                config: intfType,
                                price: '¥' + TotalPortPayCost,
                                unitPrice: `¥${new Big(TotalPortPayCost || 0).div(new Big(portNum || 1))}`,
                                num: portNum
                            },
                            {name: '总价', config: '', price: '¥' + TotalPortPayCost, unitPrice: '-', num: '-'}
                        ];
                    }
                }
            }
        },
        isDisableNext() {
            const isHighMode = this.data.get('isHighMode');
            const mode = this.data.get('formData.mode');
            let flag = '';
            // 只检查高可靠模式
            if (isHighMode) {
                const checked = this.data.get('checked');
                // 先检查线路套餐是否填写
                const highValidateTip = this.data.get('highValidateTip');
                _.each(highValidateTip, item => {
                    const {highModeRegion, highModePoint, highModeIsp} = item;
                    // 选择了线路铺设 检查线路填写情况 根据线路计算配额
                    if (checked) {
                        // 选择了线路套餐 检查线路套餐
                        for (let key in item) {
                            const keyItem = item[key];
                            if (keyItem && typeof keyItem === 'object') {
                                const {highModeLineSpecification} = keyItem;
                                if (highModeLineSpecification) {
                                    flag = '请选择线路套餐';
                                    break;
                                }
                            }
                        }
                        // 线路套餐填写完后检查接入点
                        if (!flag) {
                            if (highModeRegion || highModePoint || highModeIsp) {
                                flag = '请完善接入点信息';
                            }
                        }
                    } else {
                        // 只检查接入点
                        if (highModeRegion || highModePoint || highModeIsp) {
                            flag = '请完善接入点信息';
                        }
                    }
                });
                // 再检查配额是否充足
                if (!flag) {
                    const highModeMappingPortNum = this.data.get('highModeMappingPortNum');
                    const mode = this.data.get('formData.mode');
                    const availableQuota = this.data.get('quota.free');
                    if (highModeMappingPortNum[mode] > availableQuota) {
                        flag =
                            '您暂时没有可申请的物理专线配额，如需更多配额，可' +
                            `<a href="/quota_center/#/quota/apply/create?serviceType=ET` +
                            `&region=global&cloudCenterQuotaName=dcphyQuota" target="_blank">去申请配额</a>`;
                    }
                }
                // 最强容灾、强大容灾接入点不能完全相同
                if (['STRONGEST_DISASTER_RECOVERY', 'DISASTER_RECOVERY'].includes(mode)) {
                    const highModePoint = this.data.get('highModePoint');
                    if (highModePoint[0] === highModePoint[1]) {
                        flag = '接入点不能相同';
                    }
                }
            }
            return flag;
        },
        isShowName() {
            const pointMode = this.data.get('formData.pointMode');
            return ['standard', 'map'].includes(pointMode);
        },
        largeBandwidthMaxLimit() {
            const quota = this.data.get('quota');
            const {free} = quota;
            return free < 2 ? 2 : free;
        }
    };

    static filters = {
        getIntfType(value) {
            return value ? value : '-';
        },
        regionFilter(value) {
            return value ? AllRegion.getTextFromValue(value) : '';
        }
    };

    initData() {
        return {
            FLAG,
            DocService,
            klass: 'dc-create-wrap',
            pageTitle: {
                backTo: '/network/#/dc/instance/list',
                label: '返回物理专线列表页',
                title: '基本配置'
            },
            steps: {
                datasource: [
                    {
                        title: '配置信息及购买'
                    },
                    {
                        title: '确认订单'
                    }
                ],
                current: 1
            },
            activeTab: 'all',
            modeList: [
                {
                    text: '高可靠模式',
                    value: 'highReliability',
                    desc: '支持一次性购买多个接入点和端口，构建高可靠组网'
                },
                {
                    text: '标准模式',
                    value: 'standard',
                    desc: '手动查找选择接入点'
                },
                {
                    text: '地图模式',
                    value: 'map',
                    desc: '基于用户位置或填写位置推荐接入点'
                }
            ],
            searchAddressList: [],
            formData: {},
            regionList: [],
            ispAndIntfTypeData: [],
            productTypeList: PayType.toArray('PREPAY'),
            ispList: [],
            intfTypeList: PortType.toArray(),
            highModeIntTypeList: PortType.toArray(),
            isEnterprise: false, // 企业认证
            bandMax: 10000,
            buyBucketItems: [],
            portBuyBucketItems: [],
            lineBucketItems: [],
            confirmBucketItems: [],
            quota: {
                total: 10,
                free: 10
            },
            sdk: {},
            address: {},
            price: null,
            rules: validateRules(this),
            checked: false,
            table: {
                loading: false,
                columns: SCHEMA,
                datasource: [],
                selection: {
                    mode: 'single',
                    selectedIndex: [0]
                }
            },
            pointTable: {
                loading: false,
                columns: HIGH_MODE_POINT_COLUMNS,
                datasource: [{}, {}]
            },
            selectedItem: null,
            portColumns: [
                {name: 'name', label: '计费项', width: 120},
                {name: 'config', label: '配置', width: 180},
                {name: 'unitPrice', label: '单价', width: 100},
                {name: 'num', label: '数量', width: 80},
                {name: 'price', label: '价格', width: 100}
            ],
            priceDatasource: [],
            imgSrc: {
                standardModeImg,
                mapModeImg,
                newImg,
                HIGH_MODE_IMG
            },
            // 获取到定位前禁用
            locateDisable: false,
            isBaiduUser: false,
            skuIdDatasource: [],
            highModeSkuIdDatasource: [],
            skuId: '',
            priceLoading: true,
            addItemToCartAvailable: false,
            defaultInstances: [
                {
                    tags: [
                        {
                            tagKey: '默认项目',
                            tagValue: ''
                        }
                    ]
                }
            ],
            tagListRequster: this.tagListRequster.bind(this),
            combineModeList: [
                {label: '最强容灾能力', value: 'STRONGEST_DISASTER_RECOVERY'},
                {label: '强大容灾能力', value: 'DISASTER_RECOVERY'},
                {label: '开发和测试', value: 'DEVELOPMENT_AND_TESTING'},
                {label: '大带宽负载均衡能力', value: 'LARGE_BANDWIDTH_LB'}
            ],
            handleGetPopupContainer: dom => dom,
            highModeRegion: [],
            highModePoint: [],
            highModeIsp: [],
            highValidateTip: [],
            highModeLineSpecification: [[], []],
            availableConnectDevice: [],
            lineSpecificationList: [
                [{}, {}],
                [{}, {}]
            ],
            highModePointList: [],
            highModeIspList: [],
            highModeMappingPortNum: {
                STRONGEST_DISASTER_RECOVERY: 4,
                DISASTER_RECOVERY: 2,
                DEVELOPMENT_AND_TESTING: 2,
                LARGE_BANDWIDTH_LB: 2
            },
            isPricing: true,
            mergePriceDisplay: () => {}
        };
    }

    async inited() {
        // 自定义价格展示合并规则
        this.data.set('mergePriceDisplay', this.mergePriceDisplay);
        try {
            const client = new Client({}, {}.$context);
            // 创建实例
            const sdk = new BillingSdk({
                client,
                AllRegion: window.$context.getEnum('AllRegion'),
                context: contextPipe(this)
            });
            this.data.set('sdk', sdk);
            await this.setAvaliableRegion();
            await this.checkIsBaiduWhiteList();
            await this.data.set('formData', this.initFormData('highReliability'));
            await this.getApAddr(this.data.get('formData.region'));
            this.checkUserBuy();
            this.getStandardWhiteList();
            this.loadPrice();
        } catch (e) {}
    }

    attached() {
        window.$framework.events.fire(window.$framework.EVENTS.ENTER_ACTION_COMPLETE, {
            region: {
                id: null,
                globalOnly: true
            }
        });
        this.setWatchQueue();
        this.setProvinceList();
        this.hideSelectedConfigDisplay(true);
        // 校验表单必填项
        setTimeout(() => {
            const form = this.ref('form');
            form?.validateFields();
        }, 500);
    }

    mergePriceDisplay(items) {
        const groupedByConfigname = _.groupBy(items, item => item.configName);
        const formatedItems = _.map(Object.keys(groupedByConfigname) || [], item => {
            return [...groupedByConfigname[item]];
        });
        return formatedItems;
    }

    hideSelectedConfigDisplay(flag: boolean) {
        const shoppingTitle = document.querySelector('.shopping-cart-detail-container > .shopping-cart-detail-title');
        const shoppintWrapper = document.querySelector(
            '.shopping-cart-detail-container > .shopping-cart-detail-wrapper'
        );
        if (flag) {
            if (shoppingTitle && shoppintWrapper) {
                shoppingTitle.style.display = 'none';
                shoppintWrapper.style.display = 'none';
            }
        } else {
            if (shoppingTitle && shoppintWrapper) {
                shoppingTitle.style.display = 'block';
                shoppintWrapper.style.display = 'block';
            }
        }
    }

    handleInitConfig(value) {
        // 重置线路铺设开关
        this.data.set('checked', false);
        // 重新获取接入点信息
        const regionList = this.data.get('regionList');
        this.getApAddr(regionList[0].value);

        // 初始化接入点：开发和大带宽模式只有一个接入点
        if (['DEVELOPMENT_AND_TESTING', 'LARGE_BANDWIDTH_LB'].includes(value)) {
            this.data.splice('pointTable.datasource', [1, 1]);
        } else {
            const pointTableDatasource = this.data.get('pointTable.datasource');
            if (pointTableDatasource.length < 2) {
                this.data.push('pointTable.datasource', {});
            }
        }

        // 初始化线路
        const lineSpecificationList = this.data.get('lineSpecificationList');
        // 清空上一个组合模式接入点信息相关校验
        _.each(lineSpecificationList, (item, index) => {
            this.data.set(`highValidateTip[${index}].highModeRegion`, '');
            this.data.set(`highValidateTip[${index}].highModePoint`, '');
            this.data.set(`highValidateTip[${index}].highModeIsp`, '');
            _.each(item, (it, idx) => {
                this.data.set(`highValidateTip[${index}][${idx}].highModeLineSpecification`, '');
                this.data.set(`lineSpecificationList[${index}][${idx}]`, {});
                this.data.set(`highModeLineSpecification[${index}][${idx}]`, undefined);
            });
        });
        if (value === 'DISASTER_RECOVERY') {
            this.data.set('lineSpecificationList', [[{}], [{}]]);
            this.data.set('highModeLineSpecification', [[], []]);
        } else if (value === 'DEVELOPMENT_AND_TESTING') {
            this.data.set('lineSpecificationList', [[{}, {}]]);
            this.data.set('highModeLineSpecification', [[]]);
        } else if (value === 'STRONGEST_DISASTER_RECOVERY') {
            this.data.set('lineSpecificationList', [
                [{}, {}],
                [{}, {}]
            ]);
            this.data.set('highModeLineSpecification', [[], []]);
        } else {
            this.data.set('lineSpecificationList', [[{}, {}]]);
            this.data.set('highModeLineSpecification', [[]]);
            // 大带宽模式初始化端口和设备数
            // 获取端口配额
            this.data.set('formData.portNum', 2);
            this.data.set('formData.deviceNum', 1);
            this.getAvailableDeviceNum(2);
        }
        // 线路套餐初始化报警提示
        const finalLineSpecificationList = this.data.get('lineSpecificationList');
        _.each(finalLineSpecificationList, (item, index) => {
            _.each(item, (it, idx) => {
                this.data.set(`highValidateTip[${index}][${idx}].highModeLineSpecification`, '请选择线路套餐');
            });
        });
    }

    handleShowTopo() {
        const pointMode = this.data.get('formData.pointMode');
        if (pointMode === 'highReliability') {
            this.handleShowTopoPreview();
        }
    }

    setWatchQueue() {
        this.watch('formData.apAddr', async (value: string) => {
            value && (await this.setIspPort(value));
        });

        this.watch('formData.region', (value: string) => {
            value && this.getApAddr(value);
            this.loadPrice();
        });

        this.watch('formData.intfType', value => {
            this.loadPrice();
            const isHighMode = this.data.get('isHighMode');
            // 高可靠模式设置接入点
            if (isHighMode) {
                const currRegion = this.data.get('regionList')?.[0]?.value;
                this.getApAddr(currRegion);
            }
        });

        this.watch('formData.isp', () => {
            this.loadPrice();
        });

        this.watch('address.province', (value: string) => {
            this.getCities(value);
        });

        this.watch('address.city', (value: string) => {
            this.getDistricts(value);
        });

        this.watch('table.datasource', (value: string) => {
            if (value.length) {
                this.data.set('selectedItem', value[0]);
                this.onSelectChange(value[0]);
            }
        });

        // 高可靠模式组合模式发生变化重置线路铺设
        this.watch('formData.mode', value => {
            this.handleInitConfig(value);
            const pointMode = this.data.get('formData.pointMode');
            if (pointMode === 'highReliability') {
                this.handleShowTopoPreview();
            }
        });

        // 接入点模式切换初始化
        this.watch('formData.pointMode', value => {
            const mode = this.data.get('formData.mode');
            this.handleInitConfig(mode);
            if (value === 'highReliability') {
                this.hideSelectedConfigDisplay(true);
            } else {
                this.hideSelectedConfigDisplay(false);
            }
        });

        // 高可靠模式展示线路套餐
        this.watch('checked', value => {
            if (value) {
                this.data.splice('pointTable.columns', [
                    4,
                    0,
                    {
                        name: 'lineSpecification',
                        label: '线路套餐编号',
                        width: '23%'
                    }
                ]);
            } else {
                this.data.splice('pointTable.columns', [4, 2]);
            }
        });

        // 大带宽端口数变化后询价
        this.watch(
            'formData.portNum',
            _.debounce(value => {
                this.loadPrice();
            }, 200)
        );

        // 组合模式切换重新询价
        this.watch('formData.pointMode', value => {
            this.loadPrice();
        });
    }

    searchAddress = u.debounce((e: any) => {
        let BMap = this.data.get('BMap');
        let map = this.data.get('map');
        const searchComplete = res => {
            let list = [];
            for (let i = 0; i < res.getCurrentNumPois(); i++) {
                list.push(res.getPoi(i));
            }
            list = list.length
                ? list.map(item => ({
                      label: item.title,
                      value: item.point
                  }))
                : [];
            this.data.set('searchAddressList', list);
        };
        const local = new BMap.LocalSearch(map, {
            renderOptions: {},
            onSearchComplete: searchComplete
        });
        local.search(e.value);
    }, 1000);

    setLocation(e: Event) {
        let map = this.data.get('map');
        let BMap = this.data.get('BMap');
        let location = e.originalEvent.value;
        let point = new BMap.Point(location.lng, location.lat);
        // 删除上次定位marker
        let allOverlay = map.getOverlays();
        for (let i = 0; i < allOverlay.length; i++) {
            if (allOverlay[i].id === 'currentMk') {
                map.removeOverlay(allOverlay[i]);
            }
        }
        let icon = new BMap.Icon(positionImg, new BMap.Size(54, 65));
        const mk = new BMap.Marker(point, {icon});
        mk.id = 'currentMk';
        this.data.set('currentLocation', point);
        map.addOverlay(mk);
        map.panTo(point);
    }

    initAllIspPort() {
        this.data.set('ispList', ISP.toArray());
        this.data.set('intfTypeList', PortType.toArray());
    }

    // 初始化地图
    async initMap() {
        try {
            this.data.set('locateDisable', true);
            this.data.set('activeTab', 'near');
            if (!this.BMap) {
                this.BMap = await loadBaiduMap();
                this.data.set('BMap', this.BMap);
            }
            let map = new this.BMap.Map('map-container');
            map.centerAndZoom(new this.BMap.Point(116.404, 39.915), 11);

            // 获取定位
            const geolocation = new this.BMap.Geolocation();
            const that = this;
            geolocation.getCurrentPosition(function (r) {
                if (this.getStatus() == BMAP_STATUS_SUCCESS) {
                    let icon = new that.BMap.Icon(positionImg, new that.BMap.Size(54, 65));
                    let mk = new that.BMap.Marker(r.point, {icon});
                    mk.id = 'currentMk';
                    that.data.set('currentLocation', r.point);
                    // that.data.set('activeTab', 'near'); 计算完位置再渲染接入点会有时延，先注释
                    map.addOverlay(mk);
                    map.panTo(r.point);
                }
                that.data.set('locateDisable', false);
            });

            // this.map.enableScrollWheelZoom(true); // 开启鼠标滚轮缩放
            map.addControl(
                new this.BMap.NavigationControl({
                    type: BMAP_NAVIGATION_CONTROL_ZOOM,
                    anchor: BMAP_ANCHOR_BOTTOM_RIGHT
                })
            );

            this.data.set('map', map);

            // 在地图上绘制接入点
            await this.drawApOnMap();
        } catch (error) {
            console.error('百度地图加载失败，请重试');
        }
    }

    setMarker(item: any, point: any, other: any) {
        let cancelClick = other || item.apId !== this.data.get('activeAp');
        let map = this.data.get('map');
        let BMap = this.data.get('BMap');
        let icon = new BMap.Icon(`${cancelClick ? buildingImg : buildingClickImg}`, new BMap.Size(54, 65));
        const mk = new BMap.Marker(point, {icon});
        mk.addEventListener('click', () => {
            this.setInfoWindow(item, point);
        });
        map.addOverlay(mk);
    }

    drawApOnMap() {
        return new Promise(resolve => {
            this.nextTick(() => {
                let map = this.data.get('map');
                let BMap = this.data.get('BMap');
                let currentLocation = this.data.get('currentLocation');
                map.clearOverlays();
                let apList = this.data.get('mapModeApList');
                this.data.set('activeAp', '');
                this.data.set('formData.apAddr', '');
                apList.forEach(item => {
                    let apLocation = item.apLocation;
                    let point = new BMap.Point(apLocation[0], apLocation[1]);
                    this.setMarker(item, point);
                });
                if (currentLocation) {
                    let icon = new BMap.Icon(positionImg, new BMap.Size(54, 65));
                    const mk = new BMap.Marker(currentLocation, {icon});
                    map.addOverlay(mk);
                }
                resolve();
            });
        });
    }

    cancel() {
        location.hash = '#/dc/instance/list';
    }

    setAvaliableRegion() {
        let region = 'all';
        return this.$http.getApAddrList(region).then(res => {
            const enableEtRegion = getEtAvaliableRegion(window.$context, res);
            this.data.set('allApList', res);
            this.data.set('enableEtRegion', enableEtRegion);
            const filterFshRegion = enableEtRegion.regionList.filter(item => {
                return item.value !== AllRegion.FSH;
            });
            this.data.set('regionList', filterFshRegion);
        });
    }

    initFormData(pointMode: string) {
        let regionList = this.data.get('regionList');
        let intfTypeList = PortType.toArray();
        let ispListData = ISP.toArray();
        let apType = this.data.get('isBaiduUser') ? 'BAIDU' : 'SINGLE';
        let basicData: any = {
            productType: 'prepay',
            apType,
            description: '',
            mode: 'STRONGEST_DISASTER_RECOVERY',
            pointMode
        };
        if (pointMode === 'map') {
            basicData.region = '';
            basicData.isp = '';
            basicData.intfType = '';
        } else if (pointMode === 'standard') {
            basicData.region = regionList?.[0]?.value;
            basicData.isp = ispListData?.[0].value;
            basicData.intfType = intfTypeList?.[0].value;
        } else {
            // 高可靠模式初始化
            const initRegion = regionList?.[0]?.value;
            basicData.region = initRegion;
            basicData.intfType = intfTypeList?.[0].value;
            this.data.set('highModeRegion', [initRegion, initRegion]);
        }
        return basicData;
    }

    resetFormData() {
        let formData = this.data.get('formData');
        let basicData = this.initFormData(this.data.get('formData.pointMode'));
        let newData = {...formData, ...basicData};
        for (let k in newData) {
            this.data.set(`formData.${k}`, newData[k]);
        }
    }

    changeMode(val: string) {
        this.data.set('table.datasource', []);
        if (val !== this.data.get('formData.pointMode')) {
            this.data.set('formData.pointMode', val);
            let formData = this.initFormData(val);
            // 直接赋值formData会导致引用丢失校验不生效
            for (let k in formData) {
                this.data.set(`formData.${k}`, formData[k]);
            }
            this.data.set('checked', false);
        }
        if (val === 'map') {
            this.nextTick(() => {
                this.initMap();
            });
            this.initAllIspPort();
        }
    }

    getContextPipe() {
        const formData = this.data.get('formData');
        return {
            getOrderSuccessUrl() {
                return window.$context.getOrderSuccessUrl() || {};
            },
            getCurrentRegion() {
                return formData.region;
            },
            getCsrfToken() {
                // 返回cookie中的信息
                return window.$cookie.get('bce-user-info');
            },
            SERVICE_TYPE: window.$context.SERVICE_TYPE
        };
    }

    setAp(apItem: any, point: any) {
        let apId = apItem.apId;
        let activeAp = this.data.get('activeAp');
        let BMap = this.data.get('BMap');
        if (!activeAp) {
            this.data.set('activeAp', apId);
            this.data.set('formData.apAddr', apId);
        } else {
            // 取消选择
            if (activeAp === apId) {
                this.data.set('activeAp', '');
                this.data.set('formData.apAddr', '');
                this.initAllIspPort();
                this.data.set('formData.isp', '');
                this.data.set('formData.intfType', '');
            }
            // 选择了其他接入点
            else {
                let oldActiveItem = this.data.get('allApList').find((item: any) => item.apId === activeAp);
                let apLocation = oldActiveItem.apLocation;
                let oldPoint = new BMap.Point(apLocation[0], apLocation[1]);
                this.setMarker(oldActiveItem, oldPoint, true);

                this.data.set('activeAp', apId);
                this.data.set('formData.apAddr', apId);
            }
        }
        this.setMarker(apItem, point);
    }

    setInfoWindow(item: any, point: any) {
        let activeAp = this.data.get('activeAp');
        let region = AllRegion.getTextFromValue(item.region);
        let isp = item.ispList.map(item => ISP.getTextFromValue(item)).join(' ');
        let port = item.interfaceTypeList.join(' ');

        let containerStyle = 'display: flex';
        let keyStyle = 'font-size: 12px; color: #5C5F66; width: 80px; margin-bottom: 4px';
        let valStyle = 'font-size: 12px; color: #151B26; margin-bottom: 4px; flex: 1';
        let btnStyle = `background: #FFFFFF; border: 1px solid #2468F2; border-radius: 2px; font-size: 12px;
            color: #2468F2; text-align: center; padding: 2px 8px; height: 24px; cursor: pointer`;

        let content = `
            <div style="${containerStyle}">
                <span style="${keyStyle}">接入点名称：</span>
                <span style="${valStyle}">${item.apName}</span>
            </div>
            <div style="${containerStyle}">
                <span style="${keyStyle}">接入点地址：</span>
                <span style="${valStyle}">${item.apAddress}</span>
            </div>
            <div style="${containerStyle}">
                <span style="${keyStyle}">地域：</span>
                <span style="${valStyle}">${region}</span>
            </div>
            <div style="${containerStyle}">
                <span style="${keyStyle}">可选运营商：</span>
                <span style="${valStyle}">${isp}</span>
            </div>
            <div style="${containerStyle}">
                <span style="${keyStyle}">可购端口：</span>
                <span style="${valStyle}">${port}</span>
            </div>
            <button id="info-window-clicker" style="${btnStyle}">${item.apId === activeAp ? '取消选择' : '使用此接入点'}</button>
        `;

        let map = this.data.get('map');
        let BMap = this.data.get('BMap');
        let infoWindow = new BMap.InfoWindow(content, {width: 300});
        map.openInfoWindow(infoWindow, point);

        // 判断窗口打开状态
        if (!infoWindow.isOpen()) {
            infoWindow.addEventListener('open', () => {
                document.getElementById('info-window-clicker').onclick = () => {
                    this.setAp(item, point);
                    this.setInfoWindow(item, point);
                    this.data.set('formData.region', item.region);
                };
            });
        } else {
            document.getElementById('info-window-clicker').onclick = () => {
                this.setAp(item, point);
                this.setInfoWindow(item, point);
                this.data.set('formData.region', item.region);
            };
        }
    }

    chooseAp(item: any) {
        let BMap = this.data.get('BMap');
        let apLocation = item.apLocation;
        let point = new BMap.Point(apLocation[0], apLocation[1]);
        this.data.get('map').panTo(point);
        this.setAp(item, point);
        this.setInfoWindow(item, point);
        this.data.set('formData.region', item.region);
    }

    // 检查是否厂内白名单
    checkIsBaiduWhiteList() {
        const whiteList = window.$storage.get('commonWhite');
        whiteList?.DedicatedConnBaidu && this.data.set('isBaiduUser', true);
    }

    getMenuList() {
        let skuId = this.data.get('skuId');
        let payload = {};
        if (skuId) {
            payload = {
                skuId
            };
        }
        return this.$http.getDcMenuList(payload).then((res: any) => {
            let data = res.dedicatedConnMenuVos.map((item: any) => {
                let menuItem = JSON.parse(item.extra);
                return {
                    ...item,
                    cityCN: menuItem.city,
                    ispCN: menuItem.isp,
                    lineTypeCN: menuItem.lineType,
                    num: menuItem.num,
                    apAddrName: menuItem.apAddr
                };
            });
            let skuIdList = [];
            if (!skuId) {
                res.dedicatedConnMenuVos.forEach(item => {
                    if (item.skuId) {
                        skuIdList.push({
                            value: item.skuId,
                            text: item.skuId
                        });
                    }
                });
                this.data.set('skuIdDatasource', skuIdList);
                // 初始化高可靠模式线路套餐
                const highModeSkuIdData = _.map(res.dedicatedConnMenuVos || [], item => {
                    const {skuId, menuName} = item;
                    if (skuId) {
                        return {label: `${menuName}（${skuId}）`, value: skuId, disabled: false};
                    }
                    return null;
                }).filter(Boolean);
                this.data.set('highModeSkuIdDatasource', highModeSkuIdData);
            }
            this.data.set('allMenuList', data);
        });
    }

    onSelectChange(e: Event) {
        if (e) {
            this.data.set('selectedItem', e);
            const {city, cityCN, isp, ispCN, lineType, lineTypeCN, bandwidth, extra} = e;
            const num = JSON.parse(extra).num;
            const lineSpecification = `${city}-${isp}-${lineType}-${bandwidth}-${num}`;
            const lineSpecificationName = `${cityCN}${ispCN}${lineTypeCN}${bandwidth}-${num}`;
            this.data.set('formData.lineSpecification', lineSpecification);
            this.data.set('formData.lineSpecificationName', lineSpecificationName);
        }
        this.loadPrice();
    }
    // 获取对端城市,即直辖市和香港特殊处理
    getCity() {
        let {province, city} = this.data.get('address');
        let cities = ['北京', '天津', '上海', '重庆', '香港'];
        for (let i = 0; i < cities.length; i++) {
            if (province.includes(cities[i])) {
                return cities[i];
            }
        }
        return city;
    }

    getPosPurchaseValidation() {
        return this.$http
            .purchaseValidation({serviceType: 'ET', productType: 'prepay'}, kXhrOptions)
            .then((result: any) => this.data.set('posPurchaseValid', result));
    }

    checkUserBuy() {
        let all = [this.checkDcIsEnterprise(), this.checkQuota()];
        Promise.all(all).then(([{enterprise}, quota]) => {
            quota && this.data.set('quota', quota);
            this.data.set('isEnterprise', enterprise);
        });
    }

    checkDcIsEnterprise() {
        return this.$http.dcIsEnterprise();
    }
    checkQuota() {
        return this.$http.getDcQuota();
    }

    // 设置对端地址区域
    setProvinceList() {
        let regionList = u.map(REGIONS, item => ({
            text: item.name,
            value: item.name
        }));
        this.data.set('provinceList', regionList);
        this.data.set('address.province', regionList[0].value);
    }

    // 设置运营商与端口速率
    setIspPort(apAddr: string, rowIndex?: number) {
        const ispAndIntfType = this.data.get('ispAndIntfTypeData');
        return new Promise(resolve => {
            ispAndIntfType.find((item: any) => {
                if (item.text === apAddr) {
                    const ispList = item.value.isp;
                    const intfTypeList = item.value.intfType;
                    const ispListData = ISP.toArray().filter(item => ispList.indexOf(item.value) > -1);
                    const intfListData = PortType.toArray().filter(item => intfTypeList.indexOf(item.value) > -1);
                    this.data.set('ispList', ispListData);
                    this.data.set('intfTypeList', intfListData);
                    this.nextTick(() => {
                        !isHighMode && this.data.set('formData.isp', ispListData[0].value);
                        !isHighMode && this.data.set('formData.intfType', intfListData[0].value);
                        resolve();
                        this.loadPrice();
                    });
                    const isHighMode = this.data.get('isHighMode');
                    if (isHighMode) {
                        const initIsp = ispListData?.[0]?.value;
                        if (rowIndex !== 0 && !rowIndex) {
                            this.data.set('highModeIsp', [initIsp, initIsp]);
                            this.data.set('highModeIspList', [ispListData, ispListData]);
                        } else {
                            this.data.set(`highModeIsp[${rowIndex}]`, initIsp);
                            this.data.set(`highModeIspList[${rowIndex}]`, ispListData);
                        }
                    } else {
                    }
                }
            });
        });
    }

    // 高可靠模式初始化地域和接入点
    handleInitRegionAndApAddr(region: string, rowIndex?: number, port?: string) {
        const apAddrList = this.data.get('apAddrList');
        const ispAndIntfType = this.data.get('ispAndIntfTypeData');
        const currRegion = region || this.data.get('regionList')?.[0]?.value;
        // 找出支持当前端口的接入点
        const supPortApAddrList = _.filter(ispAndIntfType, item => item.value.intfType.includes(port));
        const finalApAddrList = _.filter(apAddrList, item => _.find(supPortApAddrList, it => it.text === item.value));
        const firstApAddr = finalApAddrList?.[0]?.value;
        if (rowIndex !== 0 && !rowIndex) {
            // 设置地域
            this.data.set('highModeRegion', [currRegion, currRegion]);
            // 设置接入点
            this.data.set('highModePoint', [firstApAddr, firstApAddr]);
            this.data.set('highModePointList', [finalApAddrList, finalApAddrList]);
            // 重置物理线路运营商
            this.setIspPort(firstApAddr);
        } else {
            this.data.set(`highModePoint[${rowIndex}]`, firstApAddr);
            this.data.set(`highModePointList[${rowIndex}]`, finalApAddrList);
            // 重置物理线路运营商
            this.setIspPort(firstApAddr, rowIndex);
        }
    }

    // 获取接入点
    getApAddr(region: string, rowIndex?: number) {
        return this.$http.getApAddrList(region).then((res: any) => {
            if (res) {
                let a = res.map((item: any) => {
                    return {
                        value: item.apId,
                        text: item.apName
                    };
                });
                a = a.filter((item: any) => !['M2A'].includes(item.value));
                this.data.set('apAddrList', a);
                let apAddr = a?.[0]?.value;
                if (this.data.get('isMapMode')) {
                    apAddr = this.data.get('formData.apAddr');
                }
                this.data.set('formData.apAddr', apAddr);
                const ispAndIntfList = [];
                u.forEach(res, item => {
                    ispAndIntfList.push({
                        text: item.apId,
                        value: {
                            isp: item.ispList,
                            intfType: item.interfaceTypeList
                        }
                    });
                });
                this.data.set('ispAndIntfTypeData', ispAndIntfList);
                const isHighMode = this.data.get('isHighMode');
                // 高可靠模式初始化接入点
                if (isHighMode) {
                    const currPort = this.data.get('formData.intfType');
                    this.handleInitRegionAndApAddr(region, rowIndex, currPort);
                }
                this.setIspPort(apAddr);
            }
        });
    }

    getCities(province: string) {
        let element = u.find(REGIONS, item => item.name === province);
        let cities = element ? element.cities : [];
        let citys = u.map(cities, item => ({
            text: item.name,
            value: item.name
        }));
        this.data.set('cityList', citys);
        this.data.set('address.city', citys[0].value);
        // 直辖市互相切换城市不变导致区不更新，手动触发一下
        if (province === '北京' || province === '天津' || province === '上海' || province === '重庆') {
            this.getDistricts(citys[0].value);
        }
    }

    getDistricts(city: string) {
        let province = this.data.get('address.province');
        let element = u.find(REGIONS, item => item.name === province);

        let cities = element ? element.cities : [];
        let element2 = u.find(cities, item => item.name === city);

        let districts = element2 ? element2.districtList : [];
        let districtList = u.map(districts, item => ({
            text: item.name,
            value: item.name
        }));
        this.data.set('districtList', districtList);
        this.data.set('address.district', districtList[0].value);
    }

    async loadPrice() {
        const {sdk, formData} = this.data.get();
        sdk.clearItems();
        let bucketItems = [];
        // 端口费
        const portConfigs = this.getDcPortConfig();
        if (!formData.intfType || !formData.region) {
            return;
        }
        const isHighMode = this.data.get('isHighMode');
        if (this.data.get('standardWhite') && this.data.get('checked')) {
            if (isHighMode) {
                // 高可靠模式线路铺设费
                const highModeLineSpecification = this.data.get('highModeLineSpecification');
                let isShouldQueryPrice = false;
                _.some(highModeLineSpecification, item => {
                    _.some(item, it => {
                        if (it.length) {
                            isShouldQueryPrice = true;
                        }
                    });
                });
                if (isShouldQueryPrice) {
                    const highModeLineConfigs = this.handleResolveHighModePriceParams();
                    _.each(highModeLineConfigs, item => {
                        const highModeConfigItem = new OrderItem(item);
                        bucketItems.push(highModeConfigItem);
                    });
                }
            } else {
                if (this.data.get('table.datasource').length) {
                    // 非高可靠模式线路铺设费
                    let lineSpecification = this.data.get('formData.lineSpecification');
                    const lineConfigs = this.getRouteStandardConfig(lineSpecification);

                    let lineConfigItems = new OrderItem(lineConfigs);
                    bucketItems.push(lineConfigItems);
                }
            }
        }
        if (isHighMode) {
            _.each(portConfigs, item => {
                bucketItems.push(new OrderItem(item));
            });
        } else {
            let portConfigItems = new OrderItem(portConfigs);
            bucketItems.push(portConfigItems);
        }

        this.data.set('buyBucketItems', bucketItems);
        sdk.addItems(bucketItems);
        this.data.set('isPricing', true);
    }

    onPortChange(val: any) {
        this.data.set('portFormData', val);
        this.loadPrice();
    }

    onReset() {
        this.resetFormData();
        this.loadPrice();
    }

    async goToConfirm() {
        let form = this.ref('form');
        await form.validateFields();
        await this.getPosPurchaseValidation();
        if (!FLAG.NetworkVpcSupOrganization) {
            try {
                await this.ref('tagPanel').validate(false);
            } catch (error) {
                return;
            }
        }
        if (!FLAG.NetworkVpcSupOrganization) {
            let tags = await this.ref('tagPanel').getTags();
            this.data.set('tagResource', tags);
        }
        let {formData} = this.data.get();
        let bucketItems = this.data.get('buyBucketItems');
        this.data.set('steps.current', 2);
        let {sdk} = this.data.get();
        sdk.clearItems();
        sdk.addItems(bucketItems);
        this.data.set('useCoupon', formData.productType === 'prepay' && FLAG.NetworkSupportXS);
    }

    backToOrder(step: number) {
        this.data.set('steps.current', step);
        // 第二步修改了购买时长，返回时更新铺设费价格
        if (step === 1) {
            if (this.data.get('checked')) {
                this.loadPrice();
            }
            if (this.data.get('isMapMode')) {
                let activeAp = this.data.get('activeAp');
                this.nextTick(async () => {
                    await this.initMap();
                    let item = this.data.get('mapModeApList').filter(item => item.apId === activeAp)[0];
                    this.chooseAp(item);
                });
            }
            if (!FLAG.NetworkVpcSupOrganization) {
                let tags = this.data.get('tagResource');
                this.data.set('defaultInstances', [{tags: tags}]);
            }
            this.nextTick(() => {
                if (this.data.get('isHighMode')) {
                    this.hideSelectedConfigDisplay(true);
                }
            });
        }
    }

    changeTab(e: Event) {
        this.data.set('activeTab', e.value.key);
        this.drawApOnMap();
    }

    getPortConfig(formData: Record<string, any>, address: Address) {
        const {province, city, district} = address;
        const {name, description, apAddr, intfType, isp, apType} = formData;
        const userIdc = province + '|' + city + '|' + district + '|' + formData.address;
        const portConfigDetail = {
            name,
            description,
            apAddr,
            intfType,
            isp,
            apType,
            userIdc,
            bandwidth: '10'
        };
        if (!FLAG.NetworkVpcSupOrganization) {
            portConfigDetail.tags = this.data.get('tagResource');
        }
        return portConfigDetail;
    }

    // 获取线路详情
    getLineDetail(formData: Record<string, any>, address: Address) {
        const {province, city, district} = address;
        const userIdc = province + '|' + city + '|' + district + '|' + formData.address;
        const highModePoint = this.data.get('highModePoint');
        const highModeIsp = this.data.get('highModeIsp');
        const pointDatasource = this.data.get('pointTable.datasource');
        const allMenuList = this.data.get('allMenuList');
        const apType = this.data.get('isBaiduUser') ? 'BAIDU' : 'SINGLE';
        const tagResource = this.data.get('tagResource');
        const lineSpecification = [];
        _.each(pointDatasource, (item, index) => {
            const lineItem = {
                apAddr: highModePoint[index],
                intfType: formData.intfType,
                isp: highModeIsp[index],
                apType,
                userIdc,
                bandwidth: '10'
            };
            if (!FLAG.NetworkVpcSupOrganization) {
                lineItem.tags = tagResource;
            }
            lineSpecification.push(lineItem);
        });
        return lineSpecification;
    }

    // 确认订单的时候
    async onConfirm() {
        !FLAG.NetworkSupportXS && (await this.ref('billingProtocol').validateAgreement());
        this.data.set('confirming', true);
        let {formData, sdk} = this.data.get();
        let portFormData = this.data.get('portFormData');
        let address = this.data.get('address');
        let bucketItems = this.data.get('buyBucketItems');

        // 线路铺设费
        let lineConfig = {};
        const isExistLineConfig = this.data.get('checked');
        const isHighMode = this.data.get('isHighMode');
        let config = getConfirmConfig(formData, address);
        if (!isExistLineConfig) {
            delete config.lineSpecification;
            delete config.lineSpecificationName;
        } else {
            lineConfig = {
                lineSpecification: config.lineSpecification,
                lineSpecificationName: config.lineSpecificationName
            };
        }

        // 端口费配置
        const portConfig = getPortConfirmConfig(portFormData);
        const portDetail = this.getPortConfig(formData, address);

        let params: any = {
            items: [
                {
                    config: {...portConfig, ...portDetail},
                    paymentMethod: (isExistLineConfig ? bucketItems?.[0]?.couponId : bucketItems?.[1]?.couponId)
                        ? [
                              {
                                  type: 'coupon',
                                  values: [isExistLineConfig ? bucketItems[0].couponId : bucketItems[1].couponId]
                              }
                          ]
                        : []
                }
            ],
            paymentMethod: []
        };
        if (isExistLineConfig && !isHighMode) {
            params.items.push({
                config: lineConfig,
                paymentMethod: bucketItems?.[0]?.couponId ? [{type: 'coupon', values: [bucketItems[0].couponId]}] : []
            });
        }
        // 高可靠模式参数组装
        let highModeParams: any = {};
        if (isHighMode) {
            const mode = formData.mode;
            const lineDetail = this.getLineDetail(formData, address);
            const highModeMappingPortNum = this.data.get('highModeMappingPortNum');
            const isOnePoint = ['DEVELOPMENT_AND_TESTING', 'LARGE_BANDWIDTH_LB'].includes(mode);

            // 根据端口数创建订单
            const portNum = highModeMappingPortNum[mode];
            const orderList = _.map(Array.from({length: portNum}), (item, idx) => {
                const lineDetailIdx = isOnePoint
                    ? 0
                    : mode === 'STRONGEST_DISASTER_RECOVERY'
                      ? Math.floor(idx / 2)
                      : idx;
                return {
                    items: [
                        {
                            config: {...portConfig, ...lineDetail[lineDetailIdx]},
                            paymentMethod: bucketItems?.[idx]?.couponId
                                ? [
                                      {
                                          type: 'coupon',
                                          values: [bucketItems?.[idx]?.couponId]
                                      }
                                  ]
                                : []
                        }
                    ]
                };
            });
            highModeParams = {
                mode,
                orderList
            };
            if (mode === 'LARGE_BANDWIDTH_LB') {
                highModeParams.portNum = formData.portNum;
                highModeParams.deviceNum = formData.deviceNum;
            }
            if (isExistLineConfig) {
                _.each(highModeParams.orderList, (item, index) => {
                    const highModeLineSpecification = this.data.get('highModeLineSpecification');
                    const flattedLineSpecification = highModeLineSpecification?.flat();
                    const allMenuList = this.data.get('allMenuList');
                    const selectedLineSpecification = _.find(
                        allMenuList,
                        menu => menu.skuId === flattedLineSpecification[index]
                    );
                    const {apAddr, city, cityCN, isp, ispCN, lineType, lineTypeCN, bandwidth, num} =
                        selectedLineSpecification || {};
                    const lineSpecification = `${city}-${isp}-${lineType}-${bandwidth}-${num}`;
                    const lineSpecificationName = `${cityCN}${ispCN}${lineTypeCN}${bandwidth}-${num}`;
                    item.items.push({
                        config: {
                            lineSpecification,
                            lineSpecificationName
                        },
                        paymentMethod: bucketItems[index].couponId
                            ? [
                                  {
                                      type: 'coupon',
                                      values: [bucketItems?.[index]?.couponId]
                                  }
                              ]
                            : []
                    });
                });
            }
        }

        try {
            let reqUrl = '/api/network/v1/dc/phy/order/confirm/new';
            let finalParams = params;
            const highModeRegion = this.data.get('highModeRegion');
            let finalRegion = formData.region;
            if (isHighMode) {
                reqUrl = '/api/network/v1/dc/phy/batchOrder/confirm/new';
                finalParams = highModeParams;
                finalRegion = highModeRegion[0]; // 批量专线取第一个接入点的region
            }
            const data = await this.$http.newConfirmOrder(reqUrl, finalParams, {
                headers: {region: finalRegion}
            });
            // 处理多订单提交结果
            let orderInfo = data;
            if (Array.isArray(orderInfo)) {
                const multiOrderInfo = {
                    orderId: '',
                    status: ''
                };
                _.each(orderInfo, (item, index) => {
                    const {orderId, status} = item;
                    multiOrderInfo.orderId += orderId + (index === orderInfo.length - 1 ? '' : ',');
                    multiOrderInfo.status = status;
                });
                orderInfo = multiOrderInfo;
            }
            let url = '';
            try {
                const info = await sdk.checkPayInfo(orderInfo);
                url = info.url + '&fromService=ET';
                info.url && (location.href = url);
            } catch (info) {
                // 跳转到相应页面
                url = info.url + '&fromService=ET';
                info.url && (location.href = url);
            }
        } catch (err) {}
        this.data.set('confirming', false);
    }

    handleResolveHighModePriceParams() {
        const {formData, portFormData} = this.data.get('');
        const highModeLineSpecification = this.data.get('highModeLineSpecification');
        const highModeRegion = this.data.get('highModeRegion');
        const allMenuList = this.data.get('allMenuList');
        const lineFlavorValue = [];
        const mode = this.data.get('formData.mode');
        const isOnePoint = ['DEVELOPMENT_AND_TESTING', 'LARGE_BANDWIDTH_LB'].includes(mode);
        // 遍历生成每条线路的询价配置
        _.each(highModeLineSpecification, (item, index) => {
            _.each(item, (it, idx) => {
                const selectedLineSpecification = _.find(allMenuList, menu => menu.skuId === it);
                const {apAddr, city, cityCN, isp, ispCN, lineType, lineTypeCN, bandwidth, num} =
                    selectedLineSpecification || {};
                const lineConfig = `${city}-${isp}-${lineType}-${bandwidth}-${num}`;
                const lineSpecification = `${cityCN}${ispCN}${lineTypeCN}${bandwidth}-${num}`;
                const allApList = this.data.get('allApList');
                const menuInfo = _.find(allApList, item => {
                    return item.apId === apAddr;
                });
                const {region} = menuInfo || {};
                const pointIdx = isOnePoint ? 0 : index;
                const config = {
                    specifications: lineSpecification,
                    accountId: getUserId(),
                    serviceType: 'ET',
                    configName: `线路铺设费`,
                    serviceName: '线路费用',
                    chargeItem: 'Cpt2',
                    amount: 1,
                    region,
                    productType: formData.productType,
                    subProductType: 'Cpt2',
                    duration: portFormData.purchaseLength || 1,
                    scene: 'NEW',
                    flavor: [
                        {
                            name: 'subServiceType',
                            value: 'circuit_fee',
                            scale: 1
                        },
                        {
                            name: 'circuit_fee',
                            value: lineConfig,
                            scale: 1
                        }
                    ],
                    timeUnit: 'MONTH',
                    configDetail: [
                        {
                            label: '地域',
                            value: AllRegion.getTextFromValue(highModeRegion[pointIdx]),
                            showInConfirm: true
                        },
                        {label: '线路规格', value: lineSpecification},
                        {label: '购买时长', value: `1个*${portFormData.purchaseLength}月`}
                    ]
                };
                lineFlavorValue.push(config);
            });
        });
        return lineFlavorValue;
    }

    getRouteStandardConfig(flavor: any) {
        const {formData, portFormData} = this.data.get();
        let allApList = this.data.get('allApList');
        let menu = this.data.get('selectedItem');
        let menuInfo = allApList.filter(item => {
            return item.apId === menu.apAddr;
        });
        const {cityCN, ispCN, lineTypeCN, bandwidth, extra} = this.data.get('selectedItem');
        const num = JSON.parse(extra).num;
        const lineSpecification = `${cityCN}${ispCN}${lineTypeCN}${bandwidth}-${num}`;
        const configs = {
            specifications: lineSpecification,
            accountId: getUserId(),
            serviceType: 'ET',
            configName: '线路铺设费',
            serviceName: '线路费用',
            chargeItem: 'Cpt2',
            amount: 1,
            region: menuInfo[0].region,
            productType: formData.productType,
            subProductType: 'Cpt2',
            duration: portFormData.purchaseLength || 1,
            scene: 'NEW',
            flavor: [
                {
                    name: 'subServiceType',
                    value: 'circuit_fee',
                    scale: 1
                },
                {
                    name: 'circuit_fee',
                    value: flavor || lineSpecification,
                    scale: 1
                }
            ],
            timeUnit: 'MONTH',
            configDetail: [
                {label: '地域', value: AllRegion.getTextFromValue(formData.region), showInConfirm: true},
                {label: '线路规格', value: lineSpecification},
                {label: '购买时长', value: `1个*${portFormData.purchaseLength}月`}
            ]
        };
        return configs;
    }

    // 端口费配置
    getDcPortConfig() {
        const formData = this.data.get('formData');
        let portFormData = this.data.get('portFormData') || {};
        const {region, productType, intfType, mode} = formData;
        const isHighMode = this.data.get('isHighMode');
        // 高可靠不同组合模式对应的端口数
        const highModeMappingPortNum = this.data.get('highModeMappingPortNum');
        let highModePortConfig = [];
        if (isHighMode) {
            const highModeRegion = this.data.get('highModeRegion');
            const highModePoint = this.data.get('highModePoint');
            const highModePointList = this.data.get('highModePointList');
            const mode = this.data.get('formData.mode');
            const isOnePoint = ['DEVELOPMENT_AND_TESTING', 'LARGE_BANDWIDTH_LB'].includes(mode);
            highModePortConfig = _.map(Array.from({length: highModeMappingPortNum[mode]}), (item, index) => {
                const pointIdx = isOnePoint
                    ? 0
                    : mode === 'STRONGEST_DISASTER_RECOVERY'
                      ? Math.floor(index / 2)
                      : index;
                const currPoint = _.find(highModePointList[pointIdx], item => {
                    return item.value === highModePoint[pointIdx];
                });
                return {
                    accountId: getUserId(),
                    serviceType: 'ET',
                    chargeItem: 'Cpt2',
                    chargeItemName: 'Cpt2',
                    serviceName: '端口资源占用费',
                    region,
                    productType: productType,
                    duration: portFormData.purchaseLength || 1,
                    scene: 'NEW',
                    flavor: [
                        {
                            name: `Port_${intfType}`,
                            value: intfType,
                            scale: 1
                        }
                    ],
                    count: 1,
                    timeUnit: 'MONTH',
                    subProductType: 'Cpt2',
                    subServiceType: 'default',
                    amount: 1,
                    configName: '端口资源占用费',
                    configDetail: [
                        {
                            label: '地域',
                            value: AllRegion.getTextFromValue(highModeRegion[pointIdx]),
                            showInConfirm: true
                        },
                        {label: '端口规格', value: formData.intfType},
                        {label: '接入点', value: currPoint?.text},
                        {label: '购买配额', value: `1个 * ${portFormData.purchaseLength}月`}
                    ]
                };
            });
        }
        const configs = {
            accountId: getUserId(),
            serviceType: 'ET',
            chargeItem: 'Cpt2',
            chargeItemName: 'Cpt2',
            serviceName: '端口资源占用费',
            region,
            productType: productType,
            duration: portFormData.purchaseLength || 1,
            scene: 'NEW',
            flavor: [
                {
                    name: `Port_${intfType}`,
                    value: intfType,
                    scale: 1
                }
            ],
            count: 1,
            timeUnit: 'MONTH',
            subProductType: 'Cpt2',
            subServiceType: 'default',
            amount: 1,
            configName: '端口资源占用费',
            configDetail: [
                {label: '地域', value: AllRegion.getTextFromValue(formData.region), showInConfirm: true},
                {label: '端口规格', value: formData.intfType},
                {label: '接入点', value: this.data.get('apAddrName')},
                {label: '购买配额', value: `1个 * ${portFormData.purchaseLength}月`}
            ]
        };
        if (portFormData.autoRenew && !isHighMode) {
            configs.configDetail.push({
                label: '购买配置',
                value:
                    '开通自动续费' +
                    portFormData.autoRenewTime +
                    (portFormData.autoRenewTimeUnit === 'month' ? '月' : '年')
            });
        }
        return isHighMode ? highModePortConfig : configs;
    }

    getStandardWhiteList() {
        const whiteList = window.$storage.get('commonWhite');
        if (whiteList?.LineBuild) {
            this.data.set('standardWhite', true);
            this.getMenuList();
        }
    }
    onChange(e: Event) {
        this.data.set('buyBucketItems', this.data.get('cloneBucketItems'));
        this.data.set('skuId', '');
        if (!e.value === true) {
            this.data.set('table.datasource', []);
            this.data.set('selectedItem', null);
            this.data.set('formData.lineSpecification', '');
            this.data.set('formData.lineSpecificationName', '');
        }
        this.nextTick(() => {
            this.loadPrice();
        });
    }
    async skuIdChange({value}) {
        this.data.set('skuId', value);
        await this.getMenuList();
        // 选完之后重新校验
        this.nextTick(() => {
            this.ref('form').validateFields(['lineSpecification']);
        });
        this.data.set('table.datasource', this.data.get('allMenuList'));
        this.loadPrice();
    }
    onShoppingCartChange(e: Event) {
        this.data.set('priceLoading', UPDATE_STATUS[e] !== UPDATE_STATUS.DONE);
        if (UPDATE_STATUS[e] === UPDATE_STATUS.DONE) {
            this.data.set('isPricing', false);
            let formData = this.data.get('formData');
            let buyBucketItems = this.data.get('buyBucketItems');
            if (
                this.data.get('standardWhite') &&
                this.data.get('checked') &&
                this.data.get('table.datasource').length
            ) {
                const {cityCN, ispCN, lineTypeCN, bandwidth, extra} = this.data.get('selectedItem');
                const num = JSON.parse(extra).num;
                const lineConfig = `${cityCN}${ispCN}${lineTypeCN}${bandwidth}-${num}`;
                let linePrice = buyBucketItems[0]?.unitPrice || '-';
                let portPrice = buyBucketItems[1]?.unitPrice || '-';
                this.data.set('priceDatasource', [
                    {name: '端口资源占用费', config: formData.intfType, price: '¥' + portPrice},
                    {name: '线路铺设费', config: lineConfig, price: '¥' + linePrice},
                    {name: '总价', config: '', price: '¥' + new Big(portPrice).plus(linePrice)}
                ]);
                this.data.set('linePrice', linePrice);
            } else {
                let portPrice = buyBucketItems[0]?.unitPrice || '-';
                this.data.set('priceDatasource', [
                    {name: '端口资源占用费', config: formData.intfType, price: '¥' + portPrice},
                    {name: '总价', config: '', price: '¥' + portPrice}
                ]);
            }
        }
    }
    tagListRequster() {
        return this.$http.getSearchTagList({serviceType: [], region: ['global']});
    }
    showAssist(type: string) {
        Assist.sendMessageToAssist({
            sceneLabel: 'et_create',
            message: type === 'address' ? '百度云专线接入点有哪些？' : '端口规格有哪些类型？'
        });
    }

    // 预览拓扑图
    handleShowTopoPreview() {
        const mode = this.data.get('formData.mode');
        const combineModeList = this.data.get('combineModeList');
        const currModeLabel = _.find(combineModeList, item => item.value === mode);
        const dialog = new PreviewDialog({
            data: {
                title: `${currModeLabel?.label}拓扑图`,
                mode
            }
        });
        dialog.attach(document.body);
    }

    // 获取可用的设备数
    getAvailableDeviceNum(value: number) {
        const largeBandwidthQuota = this.data.get('quota.free');
        const availableDeviceNum = getDeviceNumByPortNum(+value, largeBandwidthQuota);
        const finalDeviceNum = _.map(AVAILABLE_DEVICE_NUM, (item: any) => {
            if (availableDeviceNum.includes(item.value)) {
                return item;
            }
            return {...item, disabled: true};
        });
        this.data.set('availableConnectDevice', finalDeviceNum);
    }

    // 端口个数变化
    handlePortNumChange({value}) {
        const portMaxLimit = this.data.get('quota.free');
        const numberVal = +value;
        if (!_.isNumber(numberVal) || numberVal > portMaxLimit || numberVal < 2) {
            return;
        }
        this.data.set('formData.deviceNum', 1);
        this.data.set('highModeMappingPortNum.LARGE_BANDWIDTH_LB', numberVal);
        this.getAvailableDeviceNum(numberVal);
        const highModeLineSpecification = this.data.get('highModeLineSpecification');
        const currlineSpecificationList = [Array.from({length: numberVal}).map((item, idx) => ({}))];
        this.data.set('lineSpecificationList', currlineSpecificationList);
        this.data.set(
            'highModeLineSpecification[0]',
            highModeLineSpecification[0].slice(0, currlineSpecificationList[0].length)
        );
        const allMenuList = this.data.get('allMenuList');

        _.each(currlineSpecificationList, (item, index) => {
            _.each(item, (it, idx) => {
                const lineNo = highModeLineSpecification[index][idx];
                const currLine = _.find(allMenuList, item => item.skuId === lineNo);
                if (!lineNo) {
                    this.data.set(`highValidateTip[${index}][${idx}].highModeLineSpecification`, '请选择线路套餐');
                } else {
                    this.data.set(`lineSpecificationList[${index}][${idx}]`, {name: currLine.menuName});
                }
            });
        });
    }

    // 刷新端口配额
    handleRefreshLargeQuota() {
        this.$http.getDcQuota().then(res => {
            if (res) {
                this.data.set('quota', res);
            }
        });
    }

    // 高可靠模式切换region
    async handleHighModeRegionChange(rowIndex: number, e: any) {
        this.data.set(`highModeRegion[${rowIndex}]`, e.value);
        this.data.set(`highValidateTip[${rowIndex}].highModeRegion`, '');
        await this.getApAddr(e.value, rowIndex);
        // 重新触发接入点和物理线路运营商的校验
        this.data.set(`highValidateTip[${rowIndex}].highModePoint`, '');
        this.data.set(`highValidateTip[${rowIndex}].highModeIsp`, '');
    }

    // 切换接入点
    handleHighPointChange(rowIndex: number, e: any) {
        const {value} = e;
        this.data.set(`highModePoint[${rowIndex}]`, value);
        this.data.set(`highValidateTip[${rowIndex}].highModePoint`, '');
        this.setIspPort(value, rowIndex);
    }

    // 切换物理线路运营商
    handleHighIspChange(rowIndex: number, e: any) {
        this.data.set(`highModeIsp[${rowIndex}]`, e.value);
        this.data.set(`highValidateTip[${rowIndex}].highModeIsp`, '');
    }

    handleSetLineDisable() {
        const highModeSkuIdDatasource = this.data.get('highModeSkuIdDatasource');
        const highModeLineSpecification = this.data.get('highModeLineSpecification');
        const updatedSkuIdDatasource = highModeSkuIdDatasource.map(item => {
            const isExistLineSelected = _.some(highModeLineSpecification, it => it.includes(item.value));
            if (isExistLineSelected) {
                item.disabled = true;
            } else {
                item.disabled = false;
            }
            return item;
        });
        this.data.set(`highModeSkuIdDatasource`, []);
        this.nextTick(() => {
            this.data.set('highModeSkuIdDatasource', updatedSkuIdDatasource);
        });
    }

    // 清空配置
    handleClearConfig(rowIndex: number) {
        this.data.set(`highModeRegion[${rowIndex}]`, undefined);
        this.data.set(`highModePoint[${rowIndex}]`, undefined);
        this.data.set(`highModeIsp[${rowIndex}]`, undefined);
        this.data.set(`highModeLineSpecification[${rowIndex}]`, []);
        const mode = this.data.get('formData.mode');
        if (mode === 'STRONGEST_DISASTER_RECOVERY') {
            this.data.set(`lineSpecificationList[${rowIndex}]`, [{}, {}]);
        }

        // 提示必选项
        this.data.set(`highValidateTip[${rowIndex}].highModeRegion`, '请选择地域');
        this.data.set(`highValidateTip[${rowIndex}].highModePoint`, '请选择接入点');
        this.data.set(`highValidateTip[${rowIndex}].highModeIsp`, '请选择线路运营商');
        const lineSpecificationList = this.data.get(`lineSpecificationList`);
        _.each(lineSpecificationList[rowIndex], (item, idx) => {
            this.data.set(`lineSpecificationList[${rowIndex}][${idx}]`, {});
            this.data.set(`highValidateTip[${rowIndex}][${idx}].highModeLineSpecification`, '请选择线路套餐');
        });

        // 重置线路套餐禁用态
        // this.handleSetLineDisable();
    }

    // 线路套餐编号切换
    handleLineSpecificationChange(e: any, rowIndex, idx: number) {
        const {value} = e;
        this.data.set(`highModeLineSpecification[${rowIndex}][${idx}]`, value);
        this.data.set(`highValidateTip[${rowIndex}][${idx}].highModeLineSpecification`, '');
        // const allMenuList = this.data.get('allMenuList');
        // const currLine = _.find(allMenuList, item => item.skuId === value);
        // if (currLine) {
        //     this.data.set(`lineSpecificationList[${rowIndex}][${idx}]`, {name: currLine.menuName});
        // }
        this.loadPrice();

        // 重置线路套餐禁用态
        // this.handleSetLineDisable();
    }

    // 一键填充
    handleOneClickFill() {
        const highModeSkuIdDatasource = this.data.get('highModeSkuIdDatasource');
        const lineSpecificationList = this.data.get('lineSpecificationList');
        const defaultLine = highModeSkuIdDatasource?.[0]?.value;
        const allMenuList = this.data.get('allMenuList');
        const currLine = _.find(allMenuList, item => item.skuId === defaultLine);

        _.each(lineSpecificationList, (item, index) => {
            _.each(item, (it, idx) => {
                this.data.set(`highModeLineSpecification[${index}][${idx}]`, defaultLine);
                this.data.set(`highValidateTip[${index}][${idx}].highModeLineSpecification`, '');
                this.data.set(`lineSpecificationList[${index}][${idx}]`, {name: currLine.menuName});
            });
        });
        this.loadPrice();
    }
}

export default San2React(Processor.autowireUnCheckCmpt(EtInstanceCreate));
