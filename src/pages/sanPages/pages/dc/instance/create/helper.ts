import {PayType} from '@/pages/sanPages/common/enum';
import {showMoney, convertPrice} from '@/pages/sanPages/utils/helper';

const AllRegion = window.$context.getEnum('AllRegion');

export const validateRules = self => {
    return {
        name: [
            {
                validator: (rule, value, callback) => {
                    value = value || self.data.get('formData.name');
                    if (!value) {
                        return callback('名称必填');
                    }
                    let pattern = /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/;
                    if (!pattern.test(value)) {
                        return callback(
                            '长度限制为1-65个字符，支持大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头'
                        );
                    }
                    callback();
                }
            }
        ],
        address: [
            {
                validator: (rule, value, callback) => {
                    value = value || self.data.get('formData.address');
                    if (!value) {
                        return callback('请填写详细地址');
                    }
                    callback();
                }
            }
        ],
        lineSpecification: [
            {
                validator: (rule, value, callback) => {
                    value = value || self.data.get('formData.lineSpecification');
                    if (!value && self.data.get('checked')) {
                        return callback('线路规格必填');
                    }
                    callback();
                }
            }
        ],
        apAddr: [{required: true, message: '请选择接入点'}],
        linkDelayTime: [
            {
                validator: (rule, value, callback) => {
                    value = value || self.data.get('formData.linkDelayTime');
                    let pattern = /^[1-9]\d*00$/;
                    if (!pattern.test(value)) {
                        return callback('端口延迟Down时间的取值范围：100-10000，且为100的整数倍数。');
                    }
                    if (value < 100 || value > 10000) {
                        return callback('取值范围应为100-10000。');
                    }
                    callback();
                }
            }
        ]
    };
};

export const getOrderConfig = (formData = {}, price = {price: 0.0}, showFlavor = false) => {
    const unitPrice = convertPrice(+price.price).getPriceOfMinute();
    let order = {
        managePrice: false,
        type: 'NEW',
        serviceName: '初装费',
        serviceType: 'ET',
        productType: formData.productType,
        region: formData.region,
        count: 1,
        price: price.price,
        unitPrice,
        configuration: ['地域：' + AllRegion.getTextFromValue(formData.region), '专线名称：' + formData.name],
        configDetail: [
            {label: '地域', value: AllRegion.getTextFromValue(formData.region), showInConfirm: true},
            {label: '物理端口规格', value: formData.intfType},
            {label: '物理线路运营商', value: ''},
            {label: '接入点', value: ''},
            {label: '计费类型', value: '一次性收取初装费'}
        ],
        config: {}
    };
    return order;
};

export const setInstancePrice = (instance, formData = {}, price) => {
    const isPrepay = formData.productType === PayType.PREPAY;
    const priceSubText = isPrepay
        ? ''
        : `
        （预计¥ ${showMoney(+price.price * 60 * 24, 2)}/天
        ¥${showMoney(+price.price * 60 * 24 * 30, 2)}/月）
    `;
    const unit = isPrepay ? '' : '分钟';
    const unitPrice = convertPrice(price.price).getPriceOfMinute(unit);
    const unitPriceText = `¥${unitPrice}`;
    instance.set('priceSubText', priceSubText);
    instance.set('unitPriceText', unitPriceText);
    instance.set('productType', formData.productType);
    instance.set('price', price.price);
    instance.set('unitPrice', price.price);
    instance.set('unit', unit);
    instance.set('priceError', '');
};

export const getConfirmConfig = (formData, address) => {
    let {province, city, district} = address;
    let detailAddress = province + '|' + city + '|' + district + '|' + formData.address;
    return {
        ...formData,
        userIdc: detailAddress,
        flavor: 'installation fee'
    };
};

/**
 * 获取端口资源配置费下单参数
 *
 * @param {*} formData
 */
export const getPortConfirmConfig = formData => {
    return {
        billing: {
            billingMethod: formData.productType,
            reservation: {
                reservationLength: formData.purchaseLength,
                reservationTimeUnit: 'month'
            }
        },
        renewReservation: {
            reservationTimeUnit: formData.autoRenewTimeUnit,
            reservationLength: formData.autoRenewTime
        }
    };
};

// 线路铺设配置信息
export const getLineConfirmConfig = formData => {
    return {
        lineSpecification: formData.lineSpecification,
        lineSpecificationName: formData.lineSpecificationName
    };
};

// 大带宽模式下可用的设备数
export const AVAILABLE_DEVICE_NUM: Array<{label: string; value: number}> = [
    {
        label: '1台',
        value: 1
    },
    {
        label: '2台',
        value: 2
    },
    {
        label: '3台',
        value: 3
    },
    {
        label: '4台',
        value: 4
    }
];

/**
 *
 * @param num 当前端口数
 * @param max 最大端口数
 * @returns
 */
export const getDeviceNumByPortNum = (num: number, max: number): Array<Number> => {
    if (!Number.isInteger(num)) {
        return [1];
    } else if (num < 2) {
        return [1, 2];
    } else if (num > max) {
        return [1, 2, 4];
    }

    const divisors = [1];
    if (num === 1) return divisors;

    // 特殊处理4的情况
    if (num % 4 === 0 && num >= 4) divisors.push(4);
    if (num % 3 === 0 && num >= 3) divisors.push(3);
    if (num % 2 === 0 && num >= 2) divisors.push(2);

    return divisors.sort((a, b) => a - b);
};
