import {InstanceStatus} from '@/pages/sanPages/common/enum';
import {ContextService} from '@/pages/sanPages/common';

import u from 'lodash';

export default {
    createDc: [
        {
            required: false
        },
        {
            custom(data, options = {}) {
                if (!options.enterprise) {
                    return {
                        disable: true,
                        message: '请您先通过企业认证再申请'
                    };
                }
            }
        }
    ],
    release: [
        {
            required: true,
            message: '请先选择实例'
        },
        {
            isSingle: true,
            message: '不支持批量操作，请依次进行操作'
        },
        {
            custom(data) {
                const selectItem = data[0];
                const canReleaseStatus = [InstanceStatus.STOPPED, InstanceStatus.DELETED, InstanceStatus.REJECTED];
                if (canReleaseStatus.indexOf(selectItem.status) !== -1) {
                    return {
                        disable: false,
                        message: ''
                    };
                }
                return {
                    disable: true,
                    message:
                        '物理专线仅支持“审核已拒绝、已到期、到期删除中“状态删除。如需其它状态删除，请提交' +
                        `<a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">` +
                        '工单' +
                        '</a>'
                };
            }
        }
    ],
    recharge: [
        {
            required: true,
            message: '请先选择实例'
        },
        {
            isSingle: true,
            message: '不支持批量操作，请依次进行操作'
        },
        {
            custom(data) {
                const selectItem = data[0];
                const canRechargeStatus = [InstanceStatus.ACTIVE, InstanceStatus.STOPPED];
                // 增量有expireTime字段的状态、stopped、 active、 deleted
                // 加expireTime是为了判断是否是增量
                if (canRechargeStatus.indexOf(selectItem.status) !== -1 && selectItem.expireTime) {
                    return {
                        disable: false,
                        message: ''
                    };
                }
                return {
                    disable: true,
                    message: '该状态下的专线不支持续费。如需操作，请稍后重试'
                };
            }
        }
    ],
    deleteBfd: [
        {
            required: true,
            message: '请先选择专线通道'
        },
        {
            isSingle: true,
            message: '不支持批量删除'
        }
    ],
    LINKDOWN: [
        {
            status: [InstanceStatus.ACTIVE],
            message() {
                return '物理专线状态为可用时，可创建端口延迟Down';
            }
        }
    ],
    FAKEASN: [
        {
            custom(data) {
                const selectItem = data[0];
                if (selectItem.routeType !== 'bgp') {
                    return {
                        disable: true,
                        message: '专线通道状态可用且路由类型必须为动态路由时，可配置Fake ASN'
                    };
                }
                return {
                    disable: false,
                    message: ''
                };
            }
        }
    ],
    diagnose: [
        {
            custom(data) {
                const selectItem = data[0];
                if (selectItem.status !== 'established') {
                    return {
                        disable: true,
                        message: '当前状态不支持诊断'
                    };
                }
                if (!selectItem.onlySelfAuthorized) {
                    return {
                        disable: true,
                        message: '跨账号使用专线通道，暂不支持实例诊断'
                    };
                }
            }
        }
    ],
    EDITTAG: [
        {
            required: true,
            message: '请先选择专线通道'
        },
        {
            custom(data) {
                if (u.find(data, item => !item.onlySelfAuthorized && !item.payer)) {
                    return {
                        disable: true,
                        message: '部分实例不支持编辑标签'
                    };
                }
                if (u.find(data, item => item.status === 'ack-wait')) {
                    return {
                        disable: true,
                        message: '部分实例不支持编辑标签'
                    };
                }
            }
        }
    ],
    DCEDITTAG: [
        {
            required: true,
            message: '请先选择物理专线'
        },
        {
            custom(data) {
                if (u.find(data, item => item.status === 'ack-wait')) {
                    return {
                        disable: true,
                        message: '部分实例不支持编辑标签'
                    };
                }
            }
        }
    ],
    pathAnalysis: [
        {
            required: true,
            message: '请先选择实例'
        }
    ]
};
