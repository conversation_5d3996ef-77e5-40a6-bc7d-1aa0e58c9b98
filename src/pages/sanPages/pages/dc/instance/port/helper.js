import RULE from '@/pages/sanPages/utils/rule';
import {PayType} from '@/pages/sanPages/common/enum';
import {convertPrice, showMoney} from '@/pages/sanPages/utils/helper';


const AllRegion = window.$context.getEnum('AllRegion');

export const validateRules = (self) => {
    return {
        name: [{
            validator: (rule, value, callback) => {
                value = value || self.data.get('formData.name');
                if (!value) {
                    return callback('名称必填');
                }
                let pattern =  /^[a-zA-Z][\w\-\/\.]{0,64}$/;
                if (!pattern.test(value)) {
                    return callback('长度限制为1-65个字符，以字母开头，只允许包含字母、数字及 - _ . /');
                }
                callback();
            }
        }],
        address: [{
            validator: (rule, value, callback) => {
                value = value || self.data.get('formData.address');
                if (!value) {
                    return callback('请填写详细地址');
                }
                callback();
            }
        }],
        userName: [{
            validator: (rule, value, callback) => {
                value = value || self.data.get('formData.userName');
                if (!value) {
                    return callback('姓名必填');
                }
                callback();
            }
        }],
        userEmail: [{
            validator: (rule, value, callback) => {
                value = value || self.data.get('formData.userEmail');
                const pattern = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
                if (!value) {
                    return callback('邮箱必填');
                }
                if (!pattern.test(value)) {
                    return callback('邮箱格式不正确');
                }
                callback();
            }
        }],
        userPhone: [{
            validator: (rule, value, callback) => {
                value = value || self.data.get('formData.userPhone');
                if (!value) {
                    return callback('电话必填');
                }
                if (!RULE.DC.MOBILE.pattern.test(value)) {
                    return callback('电话格式不正确');
                }
                callback();
            }
        }]
    };
};

export const getOrderConfig = (formData = {}, price = {price: 0.00}, showFlavor = false) => {
    const unitPrice = convertPrice(+price.price).getPriceOfMinute();
    let order = {
        managePrice: false,
        type: 'NEW',
        serviceName: '端口资源占用费',
        serviceType: 'ET',
        productType: formData.productType,
        region: formData.region,
        count: 1,
        time: formData.purchaseLength,
        price: price.price,
        unitPrice,
        configuration: [
            '地域：' + AllRegion.getTextFromValue(formData.region),
            '专线名称：' + formData.name
        ],
        configDetail: [
            {label: '物理专线ID', value: formData.id},
            {label: '物理端口规格', value: formData.intfType},
            {label: '接入点', value: formData.apAddrText}
        ],
        config: {}
    };
    return order;
};

export const getStandardConfig = (formData = {}, price = {price: 0.00}, showFlavor = false) => {
    const unitPrice = convertPrice(+price.price).getPriceOfMinute();
    let order = {
        managePrice: false,
        type: 'NEW',
        serviceName: '线路铺设费',
        region: formData.region,
        serviceType: 'ET',
        productType: formData.productType,
        count: 1,
        price: price.price,
        unitPrice,
        configuration: [
            '地域：' + AllRegion.getTextFromValue(formData.region),
        ],
        configDetail: [],
        config: {}
    };
    return order;
};

export const setLinePrice = (instance, formData = {}, price) => {
    const unit = '月';
    instance.set('configName', '线路费用');
    const unitPrice = price?.price + '/月';
    const unitPriceText = `¥${unitPrice}`;
    instance.set('unitPriceText', unitPriceText);
    instance.set('productType', formData.productType);
    instance.set('price', price?.price);
    instance.set('unitPrice', price?.price);
    instance.set('unit', unit);
    instance.set('priceError', '');
};

export const setInstancePrice = (instance, formData = {}, price) => {
    const isPrepay = formData.productType === PayType.PREPAY;
    const priceSubText = isPrepay ? '' : `
        （预计¥${showMoney(+price.price * 60 * 24, 2)}/天
        ¥${showMoney(+price.price * 60 * 24 * 30, 2)}/月）
    `;
    const unit = '/个';
    const unitPrice = convertPrice(price.price).getPriceOfMinute(unit);
    const unitPriceText = `¥${unitPrice}`;
    instance.set('configName', '端口资源占用费');
    instance.set('priceSubText', priceSubText);
    instance.set('unitPriceText', unitPriceText);
    instance.set('productType', formData.productType);
    instance.set('price', price.price);
    instance.set('unitPrice', price.price);
    instance.set('unit', unit);
    instance.set('priceError', '');
};

export const getConfirmConfig = (formData) => {
    let {autoRenew, autoRenewTimeUnit, autoRenewTime} = formData;
    let renewPayload = {};
    if (autoRenew && formData.productType === 'prepay') {
        renewPayload.renewReservation = {
            reservationTimeUnit: autoRenewTimeUnit,
            reservationLength: autoRenewTime
        };
    }
    let billingPayload = {
        billing: {
            billingMethod: formData.productType,
            reservation: {
                reservationLength: formData.productType === 'prepay' ? formData.purchaseLength : '',
                reservationTimeUnit: 'month'
            }
        },
    };
    return {
        flavor: formData.intfType,
        dcphyId: formData.id,
        name: formData.name,
        description: formData.description,
        apAddr: formData.apAddr,
        intfType: formData.intfType,
        isp: formData.isp,
        bandwidth: formData.bandwidth,
        userName: formData.userName,
        userPhone: formData.userPhone,
        userEmail: formData.userEmail,
        apType: formData.apType,
        userIdc: formData.userIdc,
        lineSpecification: formData.lineSpecification || '',
        ...billingPayload,
        ...renewPayload
    };
};
