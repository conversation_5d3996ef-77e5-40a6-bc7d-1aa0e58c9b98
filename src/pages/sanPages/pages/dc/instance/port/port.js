import {html, decorators, redirect, CreatePage, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {BillingSDK, OrderItem} from '@baiducloud/billing-sdk';
import {OrderConfirm, ShoppingCart} from '@baiducloud/billing-sdk/san';

import {PortType, ISP, TimeType, Year, Month, routeStandardList} from '@/pages/sanPages/common/enum';
import {DocService} from '@/pages/sanPages/common';
import {convertPrice} from '@/pages/sanPages/utils/helper';
import {getOrderConfig, setInstancePrice, getConfirmConfig} from './helper';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import './style.less';

const {invokeSUI, invokeSUIBIZ, invokeAppComp, template} = decorators;
const kXhrOptions = {'X-silence': true};
const domains = window.$context.getDomains();
const AllRegion = window.$context.getEnum('AllRegion');

const tpl = html`
<div>
    <s-app-create-page class="{{klass}}" backTo="{{pageTitle.backTo}}"
        backToLabel="{{pageTitle.label}}" pageTitle="{{pageTitle.title}}">
        <s-steps class="s-step-block" current="{{steps.current}}">
            <s-steps-step s-for="i in steps.datasource" title="{{i.title}}"/>
        </s-steps>
        <div class="content-wrap" s-if="{{steps.current === 1}}">
            <s-form s-ref="form"
                label-align="left"
                data="{=formData=}" rules="{{rules}}">
                <div class="content-wrap-box">
                    <s-app-legend label="{{'物理端口资源占用费-配置信息'}}"/>
                    <div class="content-item-box">
                        <div class="content-item">
                            <div class="item-key">{{'物理专线名称：'}}</div>
                            <div class="item-value text-hidden">{{formData.name}}</div>
                        </div>
                        <div class="content-item">
                            <div class="item-key">{{'物理专线ID：'}}</div>
                            <div class="item-value">{{formData.id}}</div>
                        </div>
                        <div class="content-item">
                            <div class="item-key">{{'物理端口规格：'}}</div>
                            <div class="item-value">{{formData.intfType | getIntfType}}</div>
                        </div>
                        <div class="content-item">
                            <div class="item-key">{{'接入点：'}}</div>
                            <div class="item-value">{{formData.apAddrText}}</div>
                        </div>
                        <div class="content-item">
                            <div class="item-key">{{'付费方式：'}}</div>
                            <div class="item-value">{{'预付费'}}</div>
                        </div>
                    </div>
                </div>
                <div class="content-wrap-box" s-if="{{formData.productType === 'prepay'}}">
                    <s-app-legend class="legend-wrap" label="{{'购买信息'}}"/>
                    <s-form-item label="{{'购买时长：'}}" prop="purchaseLength">
                        <s-tag-radio-group
                            radioType="button"
                            value="{=formData.purchaseLength=}"
                            datasource="{{purchaseLengthList}}">
                        </s-tag-radio-group>
                    </s-form-item>
                    <s-form-item s-if="!FLAG.NetworkSupportXS" label="{{'自动续费：'}}" prop="purchaseLength" class="renew-item-label">
                        <div s-if="formData.autoRenew" slot="help">
                            <span >
                                {{'系统将于到期前7天进行扣费，扣费时长为'}}
                                {{formData.autoRenewTime}}{{formData.autoRenewTimeUnit==='month'?'月':'年'}}
                            </span>
                        </div>
                        <s-switch checked="{=formData.autoRenew=}"></s-switch>
                        <a s-if="{{!FLAG.NetworkSupportXS}}" href="{{DocService.autorenew}}"
                            class="left_class"
                            target="_blank"
                            data-track-id="ti_vpc_vpn_create_auto_renew_doc"
                            data-track-name="自动续费文档">{{'什么是自动续费？'}}
                        </a>
                        <div class="vpn-renew-wrap row-line" s-if="formData.autoRenew">
                            <span class="renew-title">{{'选择续费周期'}}</span>
                            <s-select
                                width="100"
                                on-change="onRenewUnitChange"
                                datasource="{{renewUnitList}}"
                                value="{=formData.autoRenewTimeUnit=}"
                            >
                            </s-select>
                            <s-select
                                class="button-margin-left"
                                width="100"
                                on-change="onRenewTimeChange"
                                datasource="{{renewNumberList}}"
                                value="{=formData.autoRenewTime=}"
                            >
                            </s-select>
                        </div>
                    </s-form-item>
                </div>
            </s-form>
        </div>
        <div s-else class="order-confirm-panel">
            <order-confirm
                s-ref="orderConfirm"
                items="{{buyBucketItems}}"
                sdk="{{sdk}}"
                theme="default"
                useCoupon="{{useCoupon}}"
                showAgreementCheckbox/>
        </div>
        <div class="buybucket" slot="pageFooter">
            <div class="buybucket-container" s-if="steps.current === 1">
                <s-popover trigger="{{updating ? 'hover' : ''}}">
                    <div slot="content">
                    <!--bca-disable-next-line-->
                        {{disableTip | raw}}
                    </div>
                    <s-button on-click="goToConfirm" skin="primary" size="large" disabled="{{updating}}">
                        {{'下一步'}}
                    </s-button>
                </s-popover>
                <s-button size="large" on-click="cancel">取消</s-button>
                <s-tooltip class="tooltip">
                    <span class="price_label">(配置费用明细)</span>
                    <div slot="content">
                        <div>
                            <span class="region">地域：{{formData.region | regionFilter}}、{{formData.apAddrText}}</span>
                        </div>
                        <s-table
                            loading="{{loading}}"
                            class="s-table-qw"
                            width="{{200}}"
                            columns="{{columns}}"
                            datasource="{{priceDatasource}}">
                            <div slot="empty">
                                <s-empty>
                                    <div slot="action"></div>
                                </s-empty>
                            </div>
                            <div slot="c-config">
                                {{row.config | getIntfType}}
                            </div>
                        </s-table>
                    </div>
                </s-tooltip>
                <shopping-cart items="{{buyBucketItems}}" on-reset="onReset"/>
            </div>
            <div class="buybucket-container" s-else>
                <s-button on-click="backToOrder" size="large">{{'上一步'}}</s-button>
                <s-button size="large" on-click="cancel">取消</s-button>
                <s-button skin="primary" size="large" on-click="onConfirm" disabled="{{confirming}}">{{'提交订单'}}</s-button>
            </div>
        </div>
    </s-app-create-page>
</template>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class EtPortCreate extends CreatePage {
    REGION_CHANGE_LOCATION = '#/dc/instance/list';
    static components = {
        'order-confirm': OrderConfirm,
        'shopping-cart': ShoppingCart
    };

    initData() {
        return {
            FLAG,
            DocService,
            klass: 'dc-create-wrap dc-port-wrap',
            pageTitle: {
                backTo: '/network/#/dc/instance/list',
                label: '返回',
                title: '申请物理专线-端口资源占用费'
            },
            steps: {
                datasource: [
                    {
                        title: '配置信息'
                    },
                    {
                        title: '确认订单'
                    }
                ],
                current: 1
            },
            renewNumberList: Month.toArray(),
            renewUnitList: TimeType.toArray('MONTH', 'YEAR'),
            purchaseLengthList: [],
            buyBucketItems: [],
            sdk: {},
            price: null,
            productTypeList: [
                {
                    text: '后付费',
                    value: 'postpay'
                }
            ],
            form: {
                productType: 'postpay',
                routeStandard: '',
                region: ''
            },
            routeStandardList: routeStandardList,
            unitPrice: '',
            columns: [
                {name: 'name', label: '计费项'},
                {name: 'config', label: '配置'},
                {name: 'price', label: '价格'}
            ],
            priceDatasource: [],
            AllRegion: AllRegion,
            loading: true,
            urlQuery: getQueryParams()
        };
    }

    static computed = {
        updating() {
            let price = this.data.get('price');
            let loadingPrice = this.data.get('loadingPrice');
            let posPurchaseValid = this.data.get('posPurchaseValid');
            if (posPurchaseValid && !posPurchaseValid.status) {
                return true;
            }
            return !price || loadingPrice;
        },
        disableTip() {
            let posPurchaseValid = this.data.get('posPurchaseValid');
            if (posPurchaseValid && !posPurchaseValid.status) {
                return posPurchaseValid.failReason
                    ? posPurchaseValid.failReason +
                          '，请及时<a href="/finance/#/finance/account/recharge" target="_blank">充值</a>'
                    : '';
            }
            let price = this.data.get('price');
            let loadingPrice = this.data.get('loadingPrice');
            if (!price) {
                return '价格获取失败，请重新加载。';
            }
            if (loadingPrice) {
                return '价格加载中';
            }
            return '';
        }
    };

    static filters = {
        getYear(value) {
            return `注：购买${value / 12}年8.3折`;
        },
        getIntfType(value) {
            return value ? PortType.getTextFromValue(value) : '-';
        },
        getIsp(value) {
            return value ? ISP.getTextFromValue(value) : '-';
        },
        regionFilter(value) {
            return value ? AllRegion.getTextFromValue(value) : '';
        }
    };

    async inited() {
        await this.getPosPurchaseValidation();
        this.getPurchaseLength();
        await this.getDetail();
    }

    attached() {
        this.watch('formData.autoRenew', value => {
            this.updateOrderItems();
        });

        this.watch('formData.purchaseLength', value => {
            this.loadPrice();
        });
    }

    getDetail() {
        const dcphyId = this.data.get('urlQuery.id');
        return this.$http.getDcDetail({dcphyId}).then(res => {
            this.data.set('instance', res);
            const basicData = this.initFormData();
            this.data.set('form.region', res.region);
            this.data.set('formData', {...basicData, ...res});
            this.initOrderItems();
        });
    }

    getPurchaseLength() {
        let datasource = [
            {label: '1个月', value: 1},
            {label: '2', value: 2},
            {label: '3', value: 3},
            {label: '4', value: 4},
            {label: '5', value: 5},
            {label: '6', value: 6},
            {label: '7', value: 7},
            {label: '8', value: 8},
            {label: '9', value: 9},
            {label: '1年', value: 12, mark: '8.3折'},
            {label: '2年', value: 24, mark: '8.3折'},
            {label: '3年', value: 36, mark: '8.3折'}
        ];
        if (FLAG.NetworkSupportXS) {
            datasource = datasource.map(item => {
                if (item.mark) {
                    return {
                        label: item.label,
                        value: item.value
                    };
                } else return item;
            });
        }
        this.data.set('purchaseLengthList', datasource);
    }

    initFormData() {
        const instance = this.data.get('instance');
        let basicData = {
            productType: 'prepay',
            purchaseLength: 1,
            autoRenewTimeUnit: 'month',
            autoRenewTime: 1,
            region: instance.region
        };
        return basicData;
    }

    cancel() {
        location.hash = '/network/#/dc/instance/list';
    }

    initOrderItems() {
        this.loadPrice().then(price => {
            const formData = this.data.get('formData');
            const form = this.data.get('form');
            const contextPipe = this.getContextPipe();
            let orderConfig = getOrderConfig(formData, price);

            let order = new OrderItem(orderConfig, contextPipe);
            this.data.set('buyBucketItems', [order]);
            //更新订单项
            this.updateOrderItems();
        });
    }

    getContextPipe() {
        const formData = this.data.get('formData');
        return {
            getOrderSuccessUrl() {
                return window.$context.getOrderSuccessUrl() || {};
            },
            getCurrentRegion() {
                return formData.region;
            },
            getCsrfToken() {
                // 返回cookie中的信息
                return window.$cookie.get('bce-user-info');
            },
            SERVICE_TYPE: window.$context.SERVICE_TYPE
        };
    }

    getDcPortConfig() {
        const formData = this.data.get('formData');
        const {region, productType, intfType, purchaseLength} = formData;
        const configs = {
            serviceType: 'ET',
            chargeItem: 'Cpt2',
            region: region,
            productType: productType,
            duration: purchaseLength || 1,
            scene: 'NEW',
            flavor: [
                {
                    name: `Port_${intfType}`,
                    value: intfType,
                    scale: 1
                }
            ]
        };
        return configs;
    }

    loadPrice() {
        const configs = this.getDcPortConfig();
        const formData = this.data.get('formData');
        this.data.set('loadingPrice', true);
        this.data.set('loading', true);
        return this.$http
            .priceV3({configs: [configs]})
            .then(res => {
                this.data.set('price', res[0]);
                this.data.set('priceDatasource', [
                    {
                        name: '端口资源占用费',
                        config: formData.intfType,
                        price: '¥' + convertPrice(+res[0].price).getPriceOfMinute()
                    },
                    {name: '总价', config: '', price: '¥' + convertPrice(+res[0].price).getPriceOfMinute()}
                ]);
                this.data.set('loading', false);
                this.data.set('loadingPrice', false);
                this.updateOrderItems();
                return res[0];
            })
            .catch(() => {
                this.data.set('loadingPrice', false);
                this.data.set('loading', false);
            });
    }

    updateOrderItems() {
        let formData = this.data.get('formData');
        let instance = this.data.get('buyBucketItems[0]');

        if (!instance) {
            return;
        }

        let price = this.data.get('price');
        const configDetail = [
            {label: '物理专线ID', value: formData.id},
            {label: '端口规格', value: formData.intfType},
            {label: '接入点', value: formData.apAddrText},
            {label: '购买配额', value: `1个 * ${formData.purchaseLength}月`}
        ];
        if (formData.autoRenew) {
            configDetail.push({
                label: '购买配置',
                value: '开通自动续费' + formData.autoRenewTime + (formData.autoRenewTimeUnit === 'month' ? '月' : '年')
            });
        }

        setInstancePrice(instance, formData, price);
        instance.set('configDetail', configDetail);
        this.data.set('buyBucketItems', [instance]);
    }

    onReset() {
        let formData = this.data.get('formData');
        let basicData = this.initFormData();
        this.data.set('formData', {...formData, ...basicData});
        this.loadPrice();
    }

    async goToConfirm() {
        let formData = this.data.get('formData');
        let form = this.ref('form');
        let instance = this.data.get('buyBucketItems[0]');
        await form.validateFields();
        this.data.set('steps.current', 2);
        const sdkOptions = {
            type: 'NEW',
            serviceType: 'ET',
            serviceName: '物理专线端口资源占用费',
            productType: formData.productType,
            region: formData.region,
            items: [instance]
        };
        const contextPipe = this.getContextPipe();
        const sdk = new BillingSDK(sdkOptions, contextPipe);
        this.data.set('sdk', sdk);
        this.data.set('useCoupon', formData.productType === 'prepay' && FLAG.NetworkSupportXS);
    }

    backToOrder() {
        this.data.set('steps.current', 1);
    }

    // 确认订单的时候 询价
    async onConfirm() {
        await this.ref('orderConfirm').validateAgreement();
        this.data.set('confirming', true);
        this.loadPrice().then(() => {
            let formData = this.data.get('formData');
            let instance = this.data.get('buyBucketItems[0]');
            let config = getConfirmConfig(formData);
            instance.set('config', config);
            let sdk = this.data.get('sdk');
            const sdkPayload = {
                url: '/api/network/v1/dc/phy/order/confirm/new',
                type: 'NEW',
                instances: [instance]
            };
            sdk.confirmOrder(sdkPayload)
                .then(result => {
                    window.location.href = result.url;
                })
                .catch(result => {
                    result.url && (window.location.href = result.url);
                    this.data.set('confirming', false);
                });
        });
    }

    // 自动续费按月按年
    onRenewUnitChange({value}) {
        this.data.set('formData.autoRenewTimeUnit', value);
        this.changeRenewTimeList(value);
        this.loadPrice();
    }

    onRenewTimeChange({value}) {
        this.data.set('formData.autoRenewTime', value);
        this.loadPrice();
    }

    changeRenewTimeList(value) {
        let timeList = value === 'year' ? Year.toArray() : Month.toArray();
        this.data.set('renewNumberList', timeList);
        this.data.set('formData.autoRenewTime', '');
        this.nextTick(() => {
            this.data.set('formData.autoRenewTime', timeList[0].value);
        });
    }
    getPosPurchaseValidation() {
        return this.$http
            .purchaseValidation({serviceType: 'ET', productType: 'postpay'}, kXhrOptions)
            .then(result => this.data.set('posPurchaseValid', result));
    }
}
export default San2React(Processor.autowireUnCheckCmpt(EtPortCreate));
