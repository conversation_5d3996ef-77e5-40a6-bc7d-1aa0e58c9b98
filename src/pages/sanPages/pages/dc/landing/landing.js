/**
 * @file 专线开通页
 * <AUTHOR>
 */

import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {Notification} from '@baidu/sui';
import {StsConfig} from '@/pages/sanPages/utils/config';
import {getUserId, $flag as FLAG} from '@/pages/sanPages/utils/helper';
import {isOnline} from '@/pages/sanPages/utils/common';
import {EventBus, EventName, activeServiceType} from '@/utils';
import './landing.less';

const tpl = html`
<template>
<div class="dc-auth-wrap">
    <div class="auth dc-auth">
        <div class="auth-title">物理专线</div>
        <s-app-order-page
            title="{{title}}"
            tips="{{tips}}"
            desc="{{desc}}"
            openBtnText="{{openBtnText}}"
            process="{{process}}"
            useNewVersion="{{true}}"
            guide="{{FLAG.NetworkSupportXS ? '' : guide}}"
            protocal="{{protocal}}"
            agreed="{=agreed=}"
            openBtnDisabled="{{openBtnDisabled}}"
            openBtnDisabledTip="{{openBtnDisabledTip}}"
            on-click="applyDC">
        </s-app-order-page>
    <div>
</div>
</template>
`;

const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, withSidebar} = decorators;
const kXhrOptions = {'X-silence': true};

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class EtLanding extends Component {
    initData() {
        return {
            FLAG,
            title: '专线接入ET',
            desc: '专线是一种高性能、安全性极好的网络传输服务。专线服务避免了用户核心数据走公网线路带来的抖动、延时、丢包等网络质量问题，大大提升了用户业务的性能与安全性。',
            openBtnText: '立即开通',
            agreed: false,
            protocal: {
                content: '百度云协议',
                link: 'https://cloud.baidu.com/doc/ET/s/2jwvyvom6'
            },
            process: {
                title: '流程引导',
                content: [
                    {
                        title: '申请并购买物理专线',
                        desc: `根据用户的专线带宽需求就近选择接入${FLAG.NetworkSupportXS ? '' : '百度智能云'}专线接入点，支持预付费方式购买。`
                    },
                    {
                        title: '新增专线通道',
                        desc: `配置专线通道，用于打通用户IDC与${FLAG.NetworkSupportXS ? '' : '百度智能云'}专线路由器通信。`
                    },
                    {
                        title: '创建专线网关',
                        desc: `配置专线网关，用于打通${FLAG.NetworkSupportXS ? '' : '百度智能云'}专线路由器与用户VPC通信。`
                    },
                    {
                        title: '配置路由表',
                        desc: `根据专线通道路由协议的不同，完成配置${FLAG.NetworkSupportXS ? '' : '百度智能云'}的VPC路由表、专线通道路由管理（可选）。`
                    }
                ]
            },
            openBtnDisabled: false,
            openBtnDisabledTip: '',
            disabled: false
        };
    }

    inited() {
        const isSubUser = window.$context.isSubUser();
        if (isSubUser) {
            this.data.set('openBtnDisabled', true);
            this.data.set('openBtnDisabledTip', '当前登录的子账户没有开通服务的权限，请联系主账户开通服务后使用。');
        }
    }

    applyDC() {
        if (!this.data.get('agreed')) {
            Notification.warning('请先勾选开通协议');
            return;
        }
        const AllRegion = window.$context.getEnum('AllRegion');
        const roleName = StsConfig.DCGW.roleName;
        this.$http
            .iamStsRoleActivate(
                u.extend(
                    {
                        roleName,
                        accountId: getUserId()
                    },
                    isOnline() ? StsConfig.DCGW.online : StsConfig.DCGW.sandbox
                ),
                {region: AllRegion.BJ}
            )
            .then(() => {
                EventBus.fire(EventName.productActive, activeServiceType.dc);
                window.$storage.set('etSts', true);
                // redirect('#/dc/instance/create?from=landing');
            });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(EtLanding));
