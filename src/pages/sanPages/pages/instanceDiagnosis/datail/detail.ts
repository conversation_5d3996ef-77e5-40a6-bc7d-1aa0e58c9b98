import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams, request} from '@baidu/bce-react-toolkit';
import {OutlinedEditingSquare, OutlinedLeft} from '@baidu/sui-icon';

import {Notification} from '@baidu/sui';
import {checkRule} from '../../instance/page/helper';
import {diagnosisStatus, AnalyzeStatus} from '@/pages/sanPages/common/enum';
import {utcToTime} from '@/pages/sanPages/utils/helper';
import {DiagnoseSDKProcessor} from '@baidu/bce-vpc-sdk-react';
import Confirm from '@/pages/sanPages/components/confirm';
import testID from '@/testId';

import './detail.less';
import '@baidu/bce-vpc-sdk-react/lib/style.css';

const processor = new DiagnoseSDKProcessor();
const {invokeSUI, invokeSUIBIZ, template, invokeAppComp} = decorators;

const tpl = html`
    <div>
        <s-app-detail-page class="{{klass}}">
            <div slot="pageTitle" class="title_class">
                <div class="tltle_left">
                    <s-app-link
                        to="/network/#/vpc/instance/diagnosis"
                        class="page-title-nav"
                        data-test-id="${testID.diagnosis.detailBack}"
                        ><icon-left />返回</s-app-link
                    >
                    <span class="vpc-name"> {{instance.instanceId || urlQuery.instanceId}}</span>
                    <!--<span class="status normal"></span>-->
                </div>
            </div>
            <div class="content">
                <h4>基本信息</h4>
                <div class="cell detail-part-item">
                    <div class="cell-title">实例类型：</div>
                    <div class="cell-content">{{instance.instanceType | getInstanceType}}</div>
                </div>
                <div class="cell detail-part-item">
                    <div class="cell-title" s-if="instance.instanceType === 'csn'">云智能网ID：</div>
                    <div class="cell-title" s-else>实例ID：</div>
                    <div class="cell-content">
                        <s-tooltip
                            content="实例可能已删除"
                            trigger="{{instance.instanceDeleted ? 'hover' : ''}}"
                            placement="top"
                        >
                            <a
                                href="{{instance | getHref}}"
                                style="{{instance.instanceDeleted ? 'color:#b4b6ba;' : ''}}"
                                >{{instance.instanceId || urlQuery.instanceId}}</a
                            >
                        </s-tooltip>
                    </div>
                    <s-clip-board
                        class="name-icon"
                        text="{{instance.instanceId || urlQuery.instanceId}}"
                        successMessage="已复制到剪贴板"
                    />
                </div>
                <div class="cell detail-part-item">
                    <div class="cell-title">诊断时间：</div>
                    <div class="cell-content">{{instance.lastDiagnoseTime | timeFormat}}</div>
                </div>
                <div class="cell detail-part-item">
                    <div class="cell-title">描述：</div>
                    <div class="cell-content">{{instance.description || '-'}}</div>
                    <s-popover s-ref="popover-description" placement="right" trigger="click" class="edit-popover-class">
                        <div class="edit-wrap" slot="content">
                            <s-input
                                value="{=edit.description.value=}"
                                width="160"
                                on-input="onEditInput($event, 'description')"
                            />
                            <div class="edit-tip">描述不能超过200个字符</div>
                            <s-button
                                skin="primary"
                                s-ref="editBtn-description"
                                disabled="{{true}}"
                                on-click="editConfirm(instance, 'description')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel('description')">取消</s-button>
                        </div>
                        <span
                            class="popover-change"
                            name="edit"
                            data-testid="${testID.diagnosis.detailDesc}"
                            on-click="onInstantEdit(instance, 'description')"
                            >变更</span
                        >
                    </s-popover>
                </div>
            </div>
            <div class="content content_bottom">
                <h4>诊断记录</h4>
                <s-table loading="{{table.loading}}" datasource="{{table.datasource}}" columns="{{table.columns}}">
                    <div slot="empty">
                        <s-empty>
                            <div slot="action"></div>
                        </s-empty>
                    </div>
                    <div slot="c-diagnoseStatus">
                        <span>{{row.diagnoseStatus | getStatus}}</span>
                    </div>
                    <div slot="c-diagnoseResult">
                        <span class="{{row.diagnoseResult | diagnoseStyle}}">{{row.diagnoseResult | getResult}}</span>
                    </div>
                    <div slot="c-diagnoseTime">
                        <span>{{row.diagnoseTime | timeFormat}}</span>
                    </div>
                    <div slot="c-operation">
                        <s-button
                            skin="stringfy"
                            on-click="openDiagnosis(row)"
                            disabled="{{row.diagnoseStatus !== 'diagnosed'}}"
                            >查看诊断报告</s-button
                        >
                        <s-tooltip
                            content="最少保留一次诊断记录 "
                            trigger="{{pager.total === 1 ? 'hover' : ''}}"
                            placement="top"
                        >
                            <s-button skin="stringfy" on-click="deleteDiagnosis(row)" disabled="{{pager.total === 1}}"
                                >删除</s-button
                            >
                        </s-tooltip>
                    </div>
                </s-table>
                <s-pagination
                    class="pagin_wrap"
                    s-if="{{pager.total}}"
                    slot="pager"
                    layout="{{'total, pageSize, pager, go'}}"
                    pageSize="{{pager.pageSize}}"
                    total="{{pager.total}}"
                    page="{{pager.page}}"
                    resetPageWhenSizeChange="{{true}}"
                    on-pagerChange="onPagerChange"
                />
            </div>
        </s-app-detail-page>
        <div id="satisfactionNew" s-if="drawerVisible"></div>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class DiagnosisDetail extends Component {
    static components = {
        'outlined-editing-square': OutlinedEditingSquare,
        'icon-left': OutlinedLeft
    };
    initData() {
        return {
            klass: 'vpc-diagnosis-detail',
            instance: {},
            table: {
                datasource: [],
                columns: [
                    {name: 'diagnoseId', label: '诊断ID'},
                    {name: 'diagnoseStatus', label: '诊断状态'},
                    {name: 'diagnoseResult', label: '诊断结果'},
                    {name: 'diagnoseTime', label: '诊断时间'},
                    {name: 'operation', label: '操作'}
                ]
            },
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            urlQuery: getQueryParams()
        };
    }

    static filters = {
        statusStyle(value) {
            return diagnosisStatus.fromValue(value).styleClass || '';
        },
        getStatus(value) {
            if (value === 'diagnosing') {
                return '诊断中';
            } else if (value === 'diagnosed') {
                return '诊断完成';
            } else if (value === 'diagnose_fail') {
                return '诊断失败';
            }
            return value || '-';
        },
        diagnoseStyle(value) {
            return value ? value + '-class-tag' : '';
        },
        getResult(value) {
            if (value === 'normal') {
                return '正常';
            } else if (value === 'critical') {
                return '严重';
            } else if (value === 'warning') {
                return '警告';
            }
            return '-';
        },
        getInstanceType(value) {
            let typeList = {
                csn: '云智能网',
                eip: '弹性公网IP',
                etChannel: '专线通道',
                nat: 'NAT网关',
                vpn: 'VPN网关'
            };
            return value ? typeList[value] : '-';
        },
        timeFormat(time) {
            return utcToTime(time) || '-';
        },
        getHref(row: any) {
            let obj = {
                eip: 'javascript:;',
                csn: `/csn/#/csn/detail?csnId=${row?.instanceId}&current=detail`
            };
            if (row?.extra) {
                let extraObj = JSON.parse(row.extra);
                if (row?.instanceType === 'eip') {
                    return `/eip/#/eip/instance/detail?eip=${extraObj.eip}&eipType=${extraObj.eipType}&ipVersion=${extraObj.ipVersion}`;
                } else if (row?.instanceType === 'nat') {
                    return `#/vpc/nat/detail?vpcId=${extraObj.vpcId}&id=${extraObj.id}&natType=${extraObj.natType}`;
                } else if (row?.instanceType === 'etChannel') {
                    return `#/dc/channel/detail?instanceId=${extraObj.instanceId}&channelId=${extraObj.channelId}&creator=${extraObj.oneself}`;
                } else if (row?.instanceType === 'vpn') {
                    return `#/vpc/vpn/detail?vpnId=${extraObj.vpnId}&vpcId=${extraObj.vpcId}&showRoute=${extraObj.showRoute}&vpnType=${extraObj.vpnType}`;
                }
            }
            let instanceType: string = row?.instanceType || 'eip';
            return row?.instanceDeleted ? 'javascript:;' : obj[instanceType];
        }
    };

    inited() {
        this.loadDetail();
    }

    attached() {
        // 私有云没有这个事件  这里try catch一下
        this.loadDiagnosisList();
    }

    loadDetail() {
        const {instanceType, instanceId} = this.data.get('urlQuery');
        let payload = {
            pageNo: 1,
            pageSize: 10,
            keywordType: 'instanceId',
            instanceType,
            keyword: instanceId
        };
        this.$http.getDiagnoseList(payload).then(data => {
            this.data.set('instance', data?.result[0] ? data.result[0] : {});
        });
    }

    beforeEdit(type) {
        const TYPE_MAP = {
            name: 'name',
            description: 'description'
        };
        const instance = this.data.get('instance');
        this.data.set(TYPE_MAP[type], instance[TYPE_MAP[type]]);
    }

    onInput(e, type) {
        let result;
        switch (type) {
            case 'name':
                const pattern = /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{0,64}$/;
                result = e.value.length <= 64 && pattern.test(e.value);
                break;
            case 'description':
                result = e.value.length <= 200;
                break;
        }
        this.ref(`${type}EditBtn`).data.set('disabled', !result);
    }

    onEdit(type) {
        const TYPE_MAP = {
            name: 'name',
            description: 'description'
        };
        let instance = this.data.get('instance');
        let value = this.data.get(TYPE_MAP[type]);
        this.$http
            .editProbe(instance.probeId, {
                [type]: value
            })
            .then(() => {
                this.editCancel(type);
                this.loadDetail();
            })
            .catch(() => {
                this.editCancel(type);
                this.loadDetail();
            });
    }

    // 编辑弹框-取消
    editCancel(type) {
        const extraType = ['domainName', 'dnsServer'];
        const disableBtn = !extraType.includes(type);
        this.ref(`editBtn-${type}`).data.set('disabled', disableBtn);
        this.ref(`popover-${type}`).data.set('visible', false);
    }
    loadDiagnosisList() {
        this.data.set('table.loading', true);
        let payload = this.getPayload();
        return this.$http.getDiagnoseDetail(payload).then(data => {
            this.data.set('table.datasource', data.result);
            this.data.set('pager.total', data.totalCount);
            this.data.set('table.loading', false);
        });
    }
    getPayload() {
        const {pager} = this.data.get('');
        const {instanceType, region, instanceId} = this.data.get('urlQuery');
        let payload = {
            instanceType: instanceType?.toUpperCase(),
            region: instanceType == 'csn' ? 'global' : region,
            instanceId: instanceId,
            pageNo: pager.page,
            pageSize: pager.pageSize
        };
        return {...payload};
    }
    // 改变页数
    onPagerChange({value}) {
        this.data.set('pager.page', value.page);
        this.data.set('pager.pageSize', value.pageSize);
        this.loadDiagnosisList();
    }
    // 点击修改icon
    onInstantEdit(row, type) {
        this.data.set(`edit.${type}.value`, row[`${type}`]);
        this.data.set(`edit.${type}.error`, false);
        const typePop = this.ref(`popover-${type}`);
        typePop.data.set('visible', !typePop.data.get('visible'));
    }
    // 编辑弹框-输入名称/描述
    onEditInput({value}, type) {
        let result = checkRule(type, value);
        this.data.set(`edit.${type}.error`, !result);
        this.data.set(`edit.${type}.value`, value);
        this.ref(`editBtn-${type}`).data.set('disabled', !result);
    }
    // 编辑弹框-提交
    editConfirm(instance, type) {
        let edit = this.data.get(`edit.${type}`);
        if (edit.error) {
            return;
        }
        const {instanceType, region, instanceId} = this.data.get('urlQuery');
        this.$http
            .updateDiagnosisInstance({
                [type]: edit.value,
                instanceType: instanceType?.toUpperCase(),
                region: instanceType == 'csn' ? 'global' : region,
                instanceId: instanceId
            })
            .then(() => {
                this.editCancel(type);
                Notification.success('修改成功');
                this.loadDetail();
            });
    }
    deleteDiagnosis(row) {
        let confirm = new Confirm({
            data: {
                open: true,
                content: '确认删除选中的诊断记录？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http.deleteDiagnosisRecord(row.diagnoseId).then(() => {
                this.data.set('pager.page', 1);
                this.loadDiagnosisList();
                Notification.success('释放成功', {placement: 'topRight'});
            });
        });
    }
    openDiagnosis(row) {
        // const {instanceType, region, instanceId} = this.data.get('urlQuery');
        // const bcm = new DiagnosisRecord({
        //     data: {
        //         instanceType: instanceType,
        //         region: region,
        //         instanceId: instanceId,
        //         diagnoseId: row.diagnoseId,
        //         diagnoseTime: row.diagnoseTime ? utcToTime(row.diagnoseTime) : '-'
        //     }
        // });
        // bcm.attach(document.body);
        this.nextTick(() => {
            this.data.set('drawerVisible', true);
            this.data.set('visibaleDraw', true);
            if (document.getElementById('satisfactionNew')) {
                this.loadSatisfaction(row);
            } else {
                this.openDiagnosis(row);
            }
        });
    }
    loadSatisfaction(row) {
        let {instanceType, instanceId, extra} = this.data.get('instance');
        this.nextTick(() => {
            processor.applyComponent(
                'DiagnoseDrawer',
                {
                    diagnoseData: {
                        diagnoseId: row.diagnoseId,
                        instanceId
                    },
                    visible: this.data.get('visibaleDraw'),
                    becSts: window.$storage.get('becSts'),
                    csnSts: window.$storage.get('csnSts'),
                    instanceType,
                    extraData: extra || '{}',
                    onCloseDrawer: () => {
                        this.data.set('drawerVisible', false);
                        this.data.set('visibaleDraw', false);
                    },
                    http: request,
                    showJumpUrl: false
                },
                '#satisfactionNew'
            );
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(DiagnosisDetail));
