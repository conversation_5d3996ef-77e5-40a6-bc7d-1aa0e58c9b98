import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';

import {Dialog} from '@baidu/sui';
import {$flag as FLAG, urlSerialize} from '@/pages/sanPages/utils/helper';
import {diagnosisInstanceType, VpnStatus} from '@/pages/sanPages/common/enum';
import {ContextService} from '@/pages/sanPages/common';

import './style.less';

const kXhrOptions = {'X-silence': true};
const AllRegion = window.$context.getEnum('AllRegion');
const formValidator = self => ({
    instanceType: [{required: true, message: '请选择实例类型'}],
    region: [{required: true, message: '请选择所在地域'}],
    instanceId: [{required: true, message: '请选择实例'}],
    description: [{maxLength: 200}]
});
const {asComponent, invokeSUI, invokeSUIBIZ, template} = decorators;
const tpl = html`
    <div>
        <s-dialog class="vpc-diagnosis-create" open="{{true}}" title="创建实例诊断" width="{{660}}">
            <s-form s-ref="form" data="{=formData=}" rules="{{rules}}" label-align="left">
                <s-form-item label="实例类型：" prop="instanceType">
                    <s-select
                        width="{{388}}"
                        datasource="{{instanceTypeList}}"
                        value="{=formData.instanceType=}"
                        on-change="instanceTypeChange($event)"
                    ></s-select>
                </s-form-item>
                <s-form-item
                    label="地域："
                    prop="region"
                    s-if="formData.instanceType !== 'csn' && formData.instanceType !== 'etChannel'"
                >
                    <s-select
                        width="{{180}}"
                        datasource="{{regionList}}"
                        value="{=formData.region=}"
                        on-change="regionChange($event)"
                    ></s-select>
                </s-form-item>
                <s-form-item label="实例ID：" prop="instanceId">
                    <s-select
                        width="{{388}}"
                        filterable
                        datasource="{{instanceIdList}}"
                        value="{=formData.instanceId=}"
                        on-change="instanceIdChange{value}($event)"
                    ></s-select>
                    <s-tip
                        s-if="formData.instanceType === 'etChannel'"
                        class="inline-tip"
                        content="只展示本账号及对方分配给本账号的专线通道。"
                        skin="question"
                    />
                </s-form-item>
                <s-form-item label="描述：" prop="description">
                    <s-input-text-area
                        width="{{392}}"
                        maxLength="200"
                        height="{{72}}"
                        placeholder="{{'最多200个字符'}}"
                        value="{=formData.description=}"
                    />
                </s-form-item>
            </s-form>
            <div slot="footer">
                <s-button on-click="onClose">取消</s-button>
                <s-button skin="primary" disabled="{{disableSub || allDataLoading}}" on-click="doSubmit">确定</s-button>
            </div>
        </s-dialog>
    </div>
`;

@asComponent('@create-diagnosis')
@template(tpl)
@invokeSUI
@invokeSUIBIZ
class CreateDiagnosis extends Component {
    initData() {
        return {
            FLAG: FLAG,
            rules: formValidator(this),
            formData: {
                region: '',
                instanceType: ''
            },
            instanceTypeList: diagnosisInstanceType.toArray(),
            instanceIdList: [],
            regionList: [],
            channelRegionList: [],
            loading: false,
            ContextService
        };
    }

    static computed = {};

    inited() {
        this.loadAllDiagnoseList();
        let regionArray = window.$context.getEnum('AllRegion').toArray();
        let vpcRegion = Object.keys(window.$context.SERVICE_TYPE['EIP'].region);
        regionArray = regionArray.filter(item => vpcRegion.includes(item.value));
        this.data.set('regionList', regionArray);
        if (this.data.get('instanceType')) {
            if (this.data.get('instanceType')) {
                this.data.set('formData.instanceType', this.data.get('instanceType'));
                if (this.data.get('instanceType') === 'eip' || this.data.get('instanceType') === 'nat') {
                    this.data.set('formData.region', window.$context.getCurrentRegionId());
                }
                this.instanceTypeChange({
                    value: this.data.get('instanceType')
                });
            }
        }
    }

    doSubmit() {
        let formData = this.data.get('formData');
        let allDiagnoseList = this.data.get('allDiagnoseList');
        const form = this.ref('form');
        return form.validateFields().then(async () => {
            if (allDiagnoseList.find(item => item.instanceId === formData.instanceId)) {
                Dialog.confirm({
                    showIcon: false,
                    title: '确定提示',
                    content: '当前实例诊断已存在，你确定重新触发诊断？',
                    onOk: () => {
                        this.onClose();
                        let payload = {
                            instanceType: formData.instanceType,
                            region: formData.region,
                            instanceId: formData.instanceId
                        };
                        (payload.instanceType === 'csn' || payload.instanceType === 'etChannel') &&
                            (payload.region = 'global');
                        if (payload.instanceType === 'etChannel' && this.data.get('dcRegion')) {
                            payload.region = this.data.get('dcRegion');
                        }
                        this.$http.executeDiagnosis(payload).then(() => {
                            this.fire('success');
                        });
                    }
                });
            } else {
                this.data.set('disableSub', true);
                let payload = {
                    instanceType: formData.instanceType,
                    region: formData.region,
                    instanceId: formData.instanceId,
                    description: formData.description
                };
                (payload.instanceType === 'csn' || payload.instanceType === 'etChannel') && (payload.region = 'global');
                if (payload.instanceType === 'etChannel' && this.data.get('dcRegion')) {
                    payload.region = this.data.get('dcRegion');
                }
                if (this.data.get('extra')) {
                    payload.extra = this.data.get('extra');
                }
                return this.$http
                    .createDiagnoseInstance(payload)
                    .then(() => {
                        this.data.set('disableSub', false);
                        this.fire('success');
                        this.onClose();
                    })
                    .catch(() => {
                        this.data.set('disableSub', false);
                    });
            }
        });
    }

    getCsnInstance() {
        let payload = {
            pageNo: 1,
            pageSize: 10000
        };
        return this.$http.csnList(urlSerialize(payload)).then(data => {
            let result = [];
            if (data.result) {
                u.each(data.result, item => {
                    result.push({
                        value: item.csnId,
                        text: item.name + '/' + item.csnId
                    });
                });
            }
            this.data.set('extraData', data.result);
            return Promise.resolve({result, type: 'csn'});
        });
    }

    getVpnInstance() {
        let query = {
            pageNo: 1,
            pageSize: 100000
        };
        return this.$http
            .getVpnList(query, {
                'X-silence': true,
                'headers': {
                    region: this.data.get('formData.region') || window.$context.getCurrentRegionId()
                }
            })
            .then(data => {
                let result = [];
                u.each(data.result, item => {
                    // if (u.indexOf([VpnStatus.ACTIVE], item.status) > -1) {
                    result.push({
                        value: item.vpnId,
                        text: `${item.vpnName}/${item.vpnId}`
                    });
                    // }
                });
                this.data.set('extraData', data.result);
                return Promise.resolve({result, type: 'vpn'});
            });
    }

    getEipInstance() {
        let payload = {
            pageNo: 1,
            pageSize: 10000
        };
        return this.$http
            .getEipList(payload, '', {
                headers: {
                    region: this.data.get('formData.region') || window.$context.getCurrentRegionId()
                }
            })
            .then(data => {
                let result = [];
                if (data.result) {
                    u.each(data.result, item => {
                        result.push({
                            value: item.shortId,
                            text: item.name + '/' + item.shortId
                        });
                    });
                }
                this.data.set('extraData', data.result);
                return Promise.resolve({result, type: 'eip'});
            });
    }

    getEtInstance() {
        return Promise.all([
            this.$http.channelList({
                pageNo: 1,
                pageSize: 10000,
                creator: 'oneself',
                status: 'established'
            }),
            this.$http.getChannelList({
                type: 'available'
            })
        ]).then((res: any) => {
            const dcList = [];
            res[1]?.infos.forEach((item: any) => {
                if (item.status === 'established') {
                    dcList.push({
                        text: `${item.name}/${item.id}`,
                        value: item.id
                    });
                }
                if (res[0]?.result?.find(i => i.id === item.id)) {
                    item.creator = 'oneself';
                } else {
                    item.creator = 'other';
                }
            });
            this.data.set('extraData', res[1].infos);
            return Promise.resolve({result: dcList, type: 'etChannel'});
        });
    }

    // 公网NAT
    getNatList() {
        let query = {
            pageNo: 1,
            pageSize: 10000
        };
        const isSupportPrivate = u.contains(
            [AllRegion.BJ, AllRegion.NJ, AllRegion.SU],
            window.$context.getCurrentRegionId()
        );
        let extraData: any = [];
        let array = [
            this.$http.getNatList(query, {
                'X-silence': true,
                'headers': {
                    region: this.data.get('formData.region') || window.$context.getCurrentRegionId()
                }
            })
        ];
        if (isSupportPrivate) {
            array.push(
                this.$http.getPrivateNatList(query, {
                    'X-silence': true,
                    'headers': {
                        region: this.data.get('formData.region') || window.$context.getCurrentRegionId()
                    }
                })
            );
        }
        return Promise.all(array).then((res: any) => {
            let result: any = [];
            // let eips = '';
            // let text = '';
            let title = '';
            if (res[0]) {
                u.each(res[0].result, item => {
                    // 只显示active状态的nat
                    if (item.status !== 'building') {
                        // eips = u.pluck(item.eips, 'eip').join('、');
                        // text = item.name + (eips ? '（'
                        //     + (eips.length > 14 ? (eips.substring(0, 14) + '...') : eips) + '）' : '');
                        // title = item.name + (eips ? '（' + eips + '）' : '');
                        title = `${item.name}(${item.id})`;
                        result.push({
                            value: item.id,
                            status: item.status,
                            text: title
                        });
                    }
                    extraData.push(item);
                });
            }
            if (res[1]) {
                u.each(res[1].result, item => {
                    // 只显示active状态的nat
                    if (item.status !== 'building') {
                        // eips = u.pluck(item.eips, 'eip').join('、');
                        // text = item.name + (eips ? '（'
                        //     + (eips.length > 14 ? (eips.substring(0, 14) + '...') : eips) + '）' : '');
                        // title = item.name + (eips ? '（' + eips + '）' : '');
                        title = `${item.name}(${item.natId})`;
                        result.push({
                            value: item.natId,
                            status: item.status,
                            text: title
                        });
                    }
                    extraData.push(item);
                });
            }
            this.data.set('extraData', extraData);
            return Promise.resolve({result, type: 'etChannel'});
        });
    }

    instanceTypeChange({value}) {
        this.data.set('formData.instanceId', '');
        this.data.set('instanceIdList', []);
        this.data.set('extraData', []);
        // 保留value切换的逻辑
        const requesetMap: any = {
            csn: this.getCsnInstance,
            eip: this.getEipInstance,
            etChannel: this.getEtInstance,
            nat: this.getNatList,
            vpn: this.getVpnInstance
        };
        if (!requesetMap[value]) {
            return;
        }
        return requesetMap[value].apply(this).then(res => {
            if (res.type !== this.data.get('formData.instanceType')) {
                return;
            }
            this.data.set('instanceIdList', res?.result);
            if (res?.result?.find(item => item.value === this.data.get('instanceId')) && this.data.get('instanceId')) {
                this.instanceIdChange({value: this.data.get('instanceId')});
            }
        });
    }

    regionChange({value}) {
        this.data.set('formData.region', value);
        if (!this.data.get('formData.instanceType')) {
            return;
        }
        this.instanceTypeChange({
            value: this.data.get('formData.instanceType')
        });
    }

    regionChange({value}) {
        this.data.set('formData.region', value);
        if (!this.data.get('formData.instanceType')) {
            return;
        }
        this.instanceTypeChange({
            value: this.data.get('formData.instanceType')
        });
    }

    onClose() {
        this.dispose();
    }
    loadAllDiagnoseList() {
        this.data.set('allDataLoading', true);
        let payload = {
            pageNo: 1,
            pageSize: 10000
        };
        this.$http
            .getDiagnoseList(payload)
            .then(res => {
                this.data.set('allDiagnoseList', res.result);
            })
            .finally(e => {
                this.data.set('allDataLoading', false);
            });
    }
    instanceIdChange({value}) {
        this.data.set('extra', '');
        this.data.set('formData.instanceId', value);
        let instanceType = this.data.get('formData.instanceType');
        let extraData = this.data.get('extraData') || [];
        let extra: any = {};
        if (instanceType === 'eip') {
            let obj = extraData.find((item: any) => item.shortId === value);
            if (obj) {
                extra.eip = obj.eip;
                extra.eipType = obj.eipType;
                extra.ipVersion = 'eipv4';
            }
        } else if (instanceType === 'nat') {
            let obj = extraData.find((item: any) => item.id === value || item.natId === value);
            if (obj) {
                extra.vpcId = obj.id ? obj.vpcId : obj.vpcUuid;
                extra.id = obj.id || obj.natId;
                extra.natType = obj.id ? 'public' : 'private';
            }
        } else if (instanceType === 'etChannel') {
            let obj = extraData.find((item: any) => item.id === value);
            if (obj) {
                extra.instanceId = obj.dcphyId;
                extra.channelId = obj.id;
                extra.oneself = obj.creator;
                this.data.set('dcRegion', obj.region);
            }
        } else if (instanceType === 'vpn') {
            let obj = extraData.find((item: any) => item.vpnId === value);
            if (obj) {
                const isShow = obj.vpnConns?.some(it => it.healthStatus === 'reachable');
                extra.vpnType = obj.vpnType.toLowerCase();
                extra.vpnId = obj.vpnId;
                extra.vpcId = obj.vpcId;
                extra.showRoute = isShow && ['ipsec', 'gre'].includes(extra.vpnType);
            }
        }
        if (extra.id || extra.eip || extra.instanceId || extra.vpnId) {
            this.data.set('extra', JSON.stringify(extra));
        }
    }
}
export default Processor.autowireUnCheckCmpt(CreateDiagnosis);
