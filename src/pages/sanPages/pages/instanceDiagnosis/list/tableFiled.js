import {diagnosisStatus, diagnosisInstanceType} from '../../../common/enum';
export const columns = [
    {
        name: 'instanceId',
        label: '实例ID',
        width: 180,
        fixed: 'left'
    },
    {
        name: 'lastDiagnoseStatus',
        label: '状态',
        width: 90,
        filter: {
            options: [
                {
                    text: '全部',
                    value: ''
                },
                ...diagnosisStatus.toArray()
            ],
            value: ''
        }
    },
    {
        name: 'instanceType',
        label: '实例类型',
        width: 90,
        filter: {
            options: [
                {
                    text: '全部',
                    value: ''
                },
                ...diagnosisInstanceType.toArray()
            ],
            value: ''
        }
    },
    {
        name: 'diagnoseCount',
        label: '诊断次数',
        width: 60
    },
    {
        name: 'lastDiagnoseTime',
        label: '最近诊断时间',
        sortable: true,
        width: 140
    },
    {
        name: 'lastDiagnoseResult',
        label: '诊断状态',
        width: 120
    },
    {
        name: 'description',
        label: '描述',
        width: 160
    },
    // {
    //     name: 'tag',
    //     label: '标签',
    //     width: 120
    // },
    // {
    //     name: 'resource',
    //     label: '资源分组',
    //     width: 90
    // },
    {
        name: 'opt',
        label: '操作',
        width: 110,
        fixed: 'right'
    }
];
