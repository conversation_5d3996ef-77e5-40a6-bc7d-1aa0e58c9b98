.diagnosis-list-wrap {
    .remote-text-wrap {
        display: flex;
    }
    .s-table {
        .s-table-row:hover {
            .name-icon {
                display: inline;
            }
        }
        .name-icon {
            display: none;
            font-size: 12px;
            fill: #2468f2;
        }
        .s-table-body {
            max-height: calc(~'100vh - 334px');
            .operations {
                .s-button {
                    padding: 0;
                    margin-right: 16px;
                }
                .block_class {
                    display: block;
                }
            }
        }
        .s-table-empty {
            border-bottom: none;
        }
        .instance-id-widget {
            white-space: nowrap;
        }
    }
    .icon-bind,
    .icon-unbind {
        font-size: 16px;
    }
    .icon-renewmanage {
        color: #2468f2;
    }
    .header-button-wrap {
        margin-left: auto;
        display: flex;
        align-items: center;
        .s-button {
            padding: 0;
        }
        .link-wrap {
            margin-left: 8px;
            margin-right: 16px;
        }
        .outlined-link {
            font-size: 14px;
            margin-right: 4px;
        }
    }
    .button-shortcut {
        background-color: #f5f5f5;
        border-color: #ebebeb;
    }
    .peerconn-tip {
        background: #fcf7f1;
        padding: 5px;
        margin-left: 10px;
        color: #f38900;
    }
    .intro-warp {
        display: inline-block;
        margin-left: 8px;
        .placeholder-style {
            input::-webkit-input-placeholder {
                /*WebKit browsers*/
                color: #000;
            }
            input::-moz-input-placeholder {
                /*Mozilla Firefox*/
                color: #000;
            }

            input::-ms-input-placeholder {
                /*Internet Explorer*/
                color: #000;
            }
        }
    }
    .list-header-wrap {
        height: 48px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #fff;
        .header-left {
            .title {
                display: inline-block;
                margin: 0;
                color: #151b26;
                margin-right: 12px;
                height: 47px;
                line-height: 47px;
                font-weight: 500;
                font-size: 16px;
                margin-left: 16px;
            }
        }
        .header-right {
            display: flex;
            align-items: center;
            .help-file {
                margin-right: 16px;
                font-size: 12px;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                color: #151b26;
                line-height: 20px;
                &:hover {
                    color: #2468f2;
                }
                .s-icon {
                    position: relative;
                    top: -1px;
                    right: 4px;
                    margin-right: 0;
                    color: #2468f2;
                    font-size: 14px;
                }
            }
            .function-introduce {
                color: #2468f2;
            }
        }
    }
    .icon-edit,
    .icon-copy {
        font-size: 12px;
        color: #0786e9;
    }
    .tip-icon-wrap {
        font-size: 14px;
        border: 1px solid #f18823;
        color: #f18823;
        &:hover {
            color: #fff;
            background-color: #f18823;
        }
    }
    .help-icon-wrap {
        color: #9e9898;
        border: 1px solid #9e9898;
        margin-left: 10px;
        font-size: 12px;
        &:hover {
            border-color: #2468f2;
        }
    }
    .foot-pager {
        margin-top: 16px;
    }
    .filter-buttons-wrap {
        display: flex;
        align-items: center;
        .s-cascader {
            margin-right: 5px;
        }
        .s-cascader-value {
            vertical-align: middle;
            font-size: 12px;
            padding-top: 0;
            padding-bottom: 0;
            line-height: 30px;
        }
        .s-auto-compelete {
            .s-select {
                input {
                    width: 170px !important;
                }
            }
        }
        .button-item {
            margin-left: 8px;
        }
        .download-item {
            margin-right: 8px;
        }
        .search-content {
            display: flex;
            align-items: center;
            position: relative;
            margin-right: 5px;
        }
        .s-icon.search {
            position: absolute;
            right: 5px;
            color: #615a5a;
        }
        .icon-fresh {
            margin-right: 5px;
        }
        .search-warp {
            .s-select-with-virtual {
                .s-input-suffix-container {
                    padding-left: 12px;
                }
            }
        }
    }
    .resource-group-search .s-cascader .s-cascader-value {
        min-width: 112px !important;
    }
    .success-icon-mark {
        width: 16px;
        height: 16px;
        display: inline-block;
        background-image: url('../../../../../img/对号@2x.png');
        background-size: cover;
        margin-right: 4px;
        position: relative;
        top: 4px;
    }
    .use-tip {
        width: 20px;
        height: 20px;
        font-size: 12px;
        color: #84868c;
        line-height: 20px;
        font-weight: 400;
        margin-right: 16px;
    }
    .instance-icon-tip {
        font-size: 12px;
        color: #151b26;
        line-height: 20px;
        font-weight: 400;
        margin-right: 24px;
    }
    .ellipsis-icon {
        width: 16px;
        height: 16px;
        margin-right: 4px;
        display: inline-block;
        background-image: url('../../../../../img/期待@2x.png');
        background-size: cover;
        position: relative;
        top: 4px;
    }
    .normal-class-tag {
        color: #30bf13;
        padding: 0 8px;
        background: #d1f2c7;
        border-radius: 2px;
        width: 40px;
        height: 20px;
    }
    .critical-class-tag {
        color: #f33e3e;
        padding: 0 8px;
        background: #ffdbd9;
        border-radius: 2px;
        width: 40px;
        height: 20px;
    }
    .warning-class-tag {
        color: #ffecd4;
        padding: 0 8px;
        background: #ff9326;
        border-radius: 2px;
        width: 40px;
        height: 20px;
    }
}

.vpc-diagnosis-create {
    font-size: 12px;
    .project-name {
        width: 97px;
        display: inline-block;
    }
    .s-form-item-label {
        width: 100px;
        text-align: left;
    }
    .s-form-item {
        margin-top: 20px;
        margin-bottom: 0;
        &:first-child {
            margin: 0;
        }
    }
    .s-form-item-content {
        margin-left: 80px;
    }
    .ip-radio {
        display: inline-block;
        .s-radio {
            margin: 10px;
        }
    }
    .ip-item {
        .s-row {
            .s-form-item-control-wrapper {
                .s-form-item-control {
                    padding-top: 7px;
                }
            }
        }
    }
    .internalIp_item {
        .s-row {
            .s-form-item-control-wrapper {
                line-height: 30px;
            }
        }
    }
    .name-havip-item {
        .s-form-item-help {
            width: 308px;
        }
    }
    .text-item {
        line-height: 30px;
    }
    .invalid-label {
        color: #eb5252;
    }
    .error_select {
        .s-input-suffix-container {
            border-color: #d0021b;
        }
    }
    .error_tip {
        color: #d0021b;
        margin-top: 5px;
        width: 308px;
    }
    .s-form-item-invalid-div {
        padding: 3px;
        color: #f33e3e;
        font-size: 12px;
    }
    .inline-tip {
        position: relative;
        top: 2px;
        left: 4px;
        &:hover {
            .s-tip {
                .s-icon path {
                    fill: #84868c !important;
                }
            }
        }
    }
}

.ant-drawer-content-wrapper {
    .suggest-widget-inner .like-dislike-widget .common {
        background: none;
        border: none;
        height: 22px;
        line-height: 22px;
        width: auto;
    }
    .suggest-widget-inner .like-dislike-widget {
        margin-top: 0;
    }
    .like-dislike-widget {
        margin-top: 0px;
        display: flex;
        align-items: center;
        .common {
            background: none;
            border: none;
            width: auto;
            height: 22px;
            line-height: 22px;
            img {
                position: relative;
            }
        }
        .dislike {
            margin-left: 8px;
        }
    }
    .suggest-widget-inner .suggest-no-satisfied {
        margin-top: 0px;
    }
}
