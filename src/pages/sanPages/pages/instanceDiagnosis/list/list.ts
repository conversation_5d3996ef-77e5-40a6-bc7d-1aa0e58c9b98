import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams, request} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {Notification} from '@baidu/sui';
import {OutlinedRefresh, OutlinedPlus, OutlinedLink, OutlinedEditingSquare} from '@baidu/sui-icon';

import {diagnosisStatus, diagnosisInstanceType} from '@/pages/sanPages/common/enum';
import {DocService} from '@/pages/sanPages/common';
import rules from '../rules';
import CreateDiagnosis from './create';
import {columns} from './tableFiled';
import Confirm from '@/pages/sanPages/components/confirm';
import {utcToTime, $flag as FLAG} from '@/pages/sanPages/utils/helper';
import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import DOCUMENT_ICON from '@/img/document_normal.svg?url';
import DOCUMENT_ICON_COLOR from '@/img/document_color.svg?url';
import {DiagnoseSDKProcessor} from '@baidu/bce-vpc-sdk-react';
import testID from '@/testId';

import './style.less';
import '@baidu/bce-vpc-sdk-react/lib/style.css';

const processor = new DiagnoseSDKProcessor();
const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, invokeComp} = decorators;

const tpl = html`
    <div>
        <s-app-list-page class="diagnosis-list-wrap">
            <div slot="pageTitle">
                <div class="list-header-wrap">
                    <div class="header-left">
                        <span class="title">实例诊断</span>
                    </div>
                    <div class="header-right">
                        <a
                            s-ref="introduce"
                            href="javascript:void(0)"
                            class="help-file function-introduce"
                            on-click="handleShowCard"
                            on-mouseenter="handleMouseEnter('introduce')"
                            on-mouseleave="handleMouseLeave('introduce')"
                            s-if="{{!FLAG.NetworkSupportXS}}"
                        >
                            <img class="s-icon" src="{{introduceIcon}}" />功能简介
                        </a>
                        <a
                            href="{{DocService.diagnosis_index}}"
                            target="_blank"
                            class="help-file"
                            on-mouseenter="handleMouseEnter('document')"
                            on-mouseleave="handleMouseLeave('document')"
                            s-if="{{!FLAG.NetworkSupportXS}}"
                        >
                            <img class="s-icon" src="{{documentIcon}}" />帮助文档
                        </a>
                    </div>
                </div>
                <introduce-panel
                    isShow="{{show}}"
                    klass="{{'endpoint-peerconn-wrapper'}}"
                    title="{{introduceTitle}}"
                    description="{{description}}"
                    on-toggle="handleToggle($event)"
                >
                    <!--<div slot="iconShowIntroduce">
                        <span class="use-tip">使用须知：</span>
                        <span class="success-icon-mark"></span>
                        <span class="use-tip instance-icon-tip">云智能网 CSN 实例</span>-->
                    <!--<span class="success-icon-mark"></span>
                        <span class="use-tip instance-icon-tip">NAT 网关实例</span>
                        <span class="ellipsis-icon"></span>
                        <span class="use-tip instance-icon-tip">负载均衡，VPC等实例尽请期待</span>-->
                    <!--</div>-->
                </introduce-panel>
            </div>
            <div slot="bulk">
                <s-tip-button
                    disabled="{{createDiagnosis.disable}}"
                    skin="primary"
                    placement="top"
                    isDisabledVisibile="{{true}}"
                    data-test-id="{{listCreateTestId}}"
                    on-click="onCreate({})"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{createDiagnosis.message raw}}
                    </div>
                    <outlined-plus />
                    创建实例诊断
                </s-tip-button>
                <s-tooltip
                    content="{{deleteDiagnosis.message}}"
                    trigger="{{deleteDiagnosis.disable ? 'hover' : ''}}"
                    placement="top"
                    class="left_class"
                >
                    <s-button
                        on-click="onRelease"
                        disabled="{{deleteDiagnosis.disable}}"
                        track-id="ti_diagnosis_instance_delete"
                        track-name="删除"
                        >删除</s-button
                    >
                </s-tooltip>
            </div>
            <div slot="filter">
                <div class="filter-buttons-wrap">
                    <s-search
                        width="{{200}}"
                        class="search-warp"
                        value="{=keyword=}"
                        placeholder="{{placeholder}}"
                        on-search="onSearch"
                    >
                        <s-select
                            slot="options"
                            width="120"
                            datasource="{{keywordTypeList}}"
                            value="{=keywordType=}"
                            on-change="keywordTypeChange($event)"
                        >
                        </s-select>
                    </s-search>
                    <s-button
                        class="button-item s-icon-button"
                        on-click="refresh"
                        track-name="刷新"
                        style="margin-right: 8px"
                        ><outlined-refresh class="icon-class"
                    /></s-button>
                    <custom-column
                        columnList="{{customColumn.datasource}}"
                        initValue="{{customColumn.value}}"
                        type="diagnosis"
                        on-init="initColumns"
                        on-change="onCustomColumns"
                    >
                    </custom-column>
                </div>
            </div>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                on-selected-change="tableSelected($event)"
                on-filter="onFilter"
                on-sort="onSort"
                selection="{=table.selection=}"
                data-test-id="{{listTableTestId}}"
            >
                <div slot="empty">
                    <table-empty
                        actionAuth="{{iamPass}}"
                        desc="暂无实例诊断"
                        actionText="立即创建"
                        on-click="onCreate({})"
                    />
                </div>
                <div slot="c-lastDiagnoseTime">{{row.lastDiagnoseTime | getTime}}</div>
                <div slot="c-lastDiagnoseStatus">
                    <span class="{{row.lastDiagnoseStatus | statusClass}}"
                        >{{row.lastDiagnoseStatus | statusText}}</span
                    >
                </div>
                <div slot="c-instanceId" class="instance-id-widget">
                    <s-tooltip
                        content="实例可能已删除"
                        trigger="{{row.instanceDeleted ? 'hover' : ''}}"
                        placement="top"
                    >
                        <a href="{{row | getHref}}" style="{{row.instanceDeleted ? 'color:#b4b6ba;' : ''}}"
                            >{{row.instanceId}}</a
                        >
                    </s-tooltip>
                    <s-clip-board class="name-icon" text="{{row.instanceId}}" />
                </div>
                <div slot="c-instanceType">{{row.instanceType | getInstanceType}}</div>
                <div slot="c-diagnoseCount">
                    <a
                        href="#/vpc/diagnosis/detail?instanceType={{row.instanceType}}&region={{row.region}}&instanceId={{row.instanceId}}"
                        data-testid="{{listNameTestId}}{{rowIndex}}"
                        >{{row.diagnoseCount}}</a
                    >
                </div>
                <!--bca-disable-next-line-->
                <div slot="c-tag">{{row.tags | getTag | raw}}</div>
                <div slot="c-resource">
                    <div s-if="row.resourceGroups && row.resourceGroups.length">
                        <p s-for="item in row.resourceGroups">{{item.name}}</p>
                    </div>
                    <span s-else>-</span>
                </div>
                <div slot="c-lastDiagnoseResult">
                    <span class="{{row.lastDiagnoseResult | diagnoseStyle}}"
                        >{{row.lastDiagnoseResult | getResult}}</span
                    >
                </div>
                <div slot="c-description">
                    <span class="truncated" title="{{row.description}}">{{row.description}}</span>
                    <s-popover
                        s-ref="popover-description-{{rowIndex}}"
                        placement="top"
                        trigger="click"
                        class="edit-popover-class"
                    >
                        <div class="edit-wrap" slot="content">
                            <s-input
                                value="{=edit.description.value=}"
                                width="160"
                                on-input="onEditInput($event, rowIndex, 'description')"
                            />
                            <div class="edit-tip">描述不能超过200个字符</div>
                            <s-button
                                skin="primary"
                                s-ref="editBtn-description-{{rowIndex}}"
                                disabled="{{true}}"
                                on-click="editConfirm(row, rowIndex, 'description')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel(rowIndex, 'description')">取消</s-button>
                        </div>
                        <outlined-editing-square
                            s-if="!iamPass.disable"
                            on-click="onInstantEdit(row, rowIndex, 'description')"
                            class="name-icon"
                        />
                    </s-popover>
                </div>
                <div slot="c-opt" class="operations">
                    <s-tooltip
                        content="实例可能已删除"
                        trigger="{{row.instanceDeleted ? 'hover' : ''}}"
                        placement="top"
                    >
                        <s-button
                            skin="stringfy"
                            on-click="repeatDiagnosis(row)"
                            data-test-id="{{listSetSnatTestId}}{{rowIndex}}"
                            disabled="{{row.lastDiagnoseStatus === 'diagnosing' || row.instanceDeleted}}"
                            >重新诊断</s-button
                        >
                    </s-tooltip>
                    <s-button
                        class="block_class"
                        skin="stringfy"
                        disabled="{{row.lastDiagnoseStatus !== 'diagnosed'}}"
                        data-test-id="{{listSetSnatTestId}}{{rowIndex}}"
                        on-click="diagnoseDetail(row)"
                        >查看诊断结果</s-button
                    >
                    <s-button
                        skin="stringfy"
                        disabled="{{row.lastDiagnoseStatus === 'diagnosing'}}"
                        data-test-id="{{listSetSnatTestId}}{{rowIndex}}"
                        on-click="deleteDiagnosis(row)"
                        >删除
                    </s-button>
                </div>
            </s-table>
            <s-pagination
                s-if="{{pager.total}}"
                slot="pager"
                layout="{{'total, pageSize, pager, go'}}"
                pageSize="{{pager.pageSize}}"
                total="{{pager.total}}"
                page="{{pager.page}}"
                on-pagerChange="onPagerChange"
                on-pagerSizeChange="onPagerSizeChange"
            />
        </s-app-list-page>
        <div id="satisfactionNew" s-if="drawerVisible"></div>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@invokeComp('@custom-column', '@introduce-panel', '@table-empty', '@create-diagnosis')
class DiagnosisInstanceList extends Component {
    static components = {
        'outlined-refresh': OutlinedRefresh,
        'outlined-plus': OutlinedPlus,
        'outlined-editing-square': OutlinedEditingSquare,
        'outlined-link': OutlinedLink
    };
    static filters = {
        statusClass(value: string) {
            return diagnosisStatus.fromValue(value).styleClass || '';
        },
        statusText(value: string) {
            return value ? diagnosisStatus.getTextFromValue(value) : '-';
        },
        getTime(value: string) {
            return value ? utcToTime(value) : '-';
        },
        getInstanceType(value: string) {
            return value ? diagnosisInstanceType.getTextFromValue(value) : '-';
        },
        getHref(row: any) {
            let obj = {
                eip: 'javascript:;',
                csn: `/csn/#/csn/detail?csnId=${row?.instanceId}&current=detail`
            };
            if (row.extra) {
                let extraObj = JSON.parse(row.extra);
                if (row?.instanceType === 'eip') {
                    return `/eip/#/eip/instance/detail?eip=${extraObj.eip}&eipType=${extraObj.eipType}&ipVersion=${extraObj.ipVersion}`;
                } else if (row?.instanceType === 'nat') {
                    return `#/vpc/nat/detail?vpcId=${extraObj.vpcId}&id=${extraObj.id}&natType=${extraObj.natType}`;
                } else if (row?.instanceType === 'etChannel') {
                    return `#/dc/channel/detail?instanceId=${extraObj.instanceId}&channelId=${extraObj.channelId}&creator=${extraObj.oneself}`;
                } else if (row?.instanceType === 'vpn') {
                    return `#/vpc/vpn/detail?vpnId=${extraObj.vpnId}&vpcId=${extraObj.vpcId}&showRoute=${extraObj.showRoute}&vpnType=${extraObj.vpnType}`;
                }
            }
            let instanceType: string = row?.instanceType || 'eip';
            return row?.instanceDeleted ? 'javascript:;' : obj[instanceType];
        },
        getResult(value) {
            if (value === 'normal') {
                return '正常';
            } else if (value === 'critical') {
                return '严重';
            } else if (value === 'warning') {
                return '警告';
            }
            return '-';
        },
        diagnoseStyle(value) {
            return value ? value + '-class-tag' : '';
        }
    };

    static computed = {};

    initData() {
        const allColumns = columns.slice();
        const customColumnDb = allColumns.map(item => ({
            text: item.label,
            value: item.name,
            disabled: item.name === 'instanceId' || item.name === 'opt'
        }));
        return {
            FLAG,
            DocService,
            keywordTypeList: [
                // {text: '实例名称', value: 'instanceName'},
                {text: '实例ID', value: 'instanceId'}
            ],
            deleteDiagnosis: {
                disable: true,
                message: '数据加载中'
            },
            placeholder: '请输入实例名称进行搜索',
            keywordType: 'instanceId',
            keyword: '',
            customColumn: {
                value: [
                    'instanceId',
                    'lastDiagnoseStatus',
                    'instanceType',
                    'diagnoseCount',
                    'lastDiagnoseTime',
                    'description',
                    // 'tag',
                    // 'resource',
                    'opt'
                ],
                datasource: customColumnDb
            },
            table: {
                loading: false,
                selection: {
                    mode: 'single',
                    selectedIndex: []
                },
                allColumns,
                datasource: [],
                selectedItems: []
            },
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            order: {},
            showMode: '',
            applyListNum: 0,
            iamPass: {},
            introduceTitle: '实例诊断简介',
            description:
                '实例诊断功能帮助您自助诊断实例的配置和运行状态，并提供诊断结果以及异常修复方案。保证业务在云上正常运行。',
            introduceIcon: INTRODUCE_ICON_COLOR,
            documentIcon: DOCUMENT_ICON,
            urlQuery: getQueryParams(),
            listCreateTestId: testID.diagnosis.listCreateBtn,
            listTableTestId: testID.diagnosis.listTable,
            listNameTestId: testID.diagnosis.listName,
            drawerVisible: true,
            visibaleDraw: true
        };
    }

    inited() {
        this.checkDiagnosisCreate();
    }

    attached() {
        this.data.set('introduceEle', this.ref('introduce'));
        window.$storage.get('showDiagnoseIntroduce') === false && this.data.set('show', false);
        this.loadPage();
        if (this.data.get('urlQuery').csnId) {
            this.onCreate({value: this.data.get('urlQuery').csnId});
        } else if (this.data.get('urlQuery').eipId) {
            this.onCreate({value: this.data.get('urlQuery').eipId});
        } else if (this.data.get('urlQuery').natId) {
            this.onCreate({value: this.data.get('urlQuery').natId});
        } else if (this.data.get('urlQuery').channelId) {
            this.onCreate({value: this.data.get('urlQuery').channelId});
        }
    }

    checkDiagnosisCreate() {
        let {createDiagnosis} = checker.check(rules, '', 'createDiagnosis', '');
        this.data.set('createDiagnosis', createDiagnosis);
    }

    getPayload() {
        const {pager, order, filters, keywordType, keyword} = this.data.get('');
        let payload = {
            pageNo: pager.page,
            pageSize: pager.pageSize,
            keywordType,
            keyword
        };
        return {...payload, ...order, ...filters};
    }

    loadPage() {
        this.data.set('table.loading', true);
        let payload = this.getPayload();
        if (!payload.lastDiagnoseStatus) {
            delete payload.lastDiagnoseStatus;
        }
        if (!payload.instanceType) {
            delete payload.instanceType;
        }
        if (!payload.order) {
            payload.order = 'desc';
            payload.orderBy = 'lastDiagnoseTime';
        }
        this.resetTable();
        this.$http.getDiagnoseList(payload).then(res => {
            this.data.set('table.datasource', res.result);
            this.data.set('pager.total', res.totalCount);
            this.data.set('table.loading', false);
        });
    }

    resetTable() {
        this.data.set('table.selection', {
            mode: 'single',
            selectedIndex: []
        });
        this.data.set('table.selectedItems', []);
    }

    refresh() {
        this.loadPage();
    }

    // 设置表格列
    setTableColumns(customColumnNames: any) {
        customColumnNames = customColumnNames ? customColumnNames : this.data.get('customColumn.value');
        const allColumns = this.data.get('table.allColumns');
        let columns = [];
        allColumns.forEach(item => customColumnNames.indexOf(item.name) > -1 && columns.push(item));
        this.data.set('table.columns', columns);
    }
    initColumns(value: any) {
        this.setTableColumns(value);
    }
    // 自定义表格列
    onCustomColumns(value: any) {
        this.data.set('customColumn.value', value);
        this.setTableColumns(value);
    }

    tableSelected(e: Event) {
        this.data.set('table.selectedItems', e.value.selectedItems);
        let {deleteDiagnosis} = checker.check(rules, e.value.selectedItems);
        this.data.set('deleteDiagnosis', deleteDiagnosis);
    }

    // 搜索事件
    onSearch() {
        this.data.set('pager.page', 1);
        this.loadPage();
    }
    // 改变页数
    onPagerChange(e: Event) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }

    // 改变每页显示数量
    onPagerSizeChange(e: Event) {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', e.value.pageSize);
        this.loadPage();
    }

    // 排序
    onSort(e: Event) {
        this.data.set('order', e.value);
        this.loadPage();
    }

    // 筛选
    onFilter(e: Event) {
        let name = e.field.name;
        let value = e[name] ? e[name] : '';
        this.data.set('filters.' + name, value);
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    // 点击修改icon
    onInstantEdit(row: any, rowIndex: number, type: string) {
        this.data.set(`edit.${type}.value`, row[`${type}`]);
        this.data.set(`edit.${type}.error`, false);
        const popWrap = this.ref(`popover-${type}-${rowIndex}`);
        popWrap.data.set('visible', !popWrap.data.get('visible'));
    }
    // 编辑弹框-输入名称/描述
    onEditInput(e: Event, rowIndex: number, type: string) {
        let result =
            type === 'localIfName'
                ? e.value === '' || !/^[a-zA-Z][\w\-\_\/\.]{0,64}$/.test(e.value)
                : e.value.length > 200;
        this.data.set(`edit.${type}.error`, result);
        this.data.set(`edit.${type}.value`, e.value);
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', result);
    }

    // 编辑弹框-提交
    editConfirm(row: any, rowIndex: number, type: string) {
        let edit = this.data.get(`edit.${type}`);
        if (edit.error) {
            return;
        }
        this.$http
            .updateDiagnosisInstance({
                [type]: edit.value,
                instanceType: row.instanceType?.toUpperCase(),
                region: row.instanceType == 'csn' ? 'global' : row.region,
                instanceId: row.instanceId
            })
            .then(() => {
                this.editCancel(rowIndex, type);
                Notification.success('修改成功');
                this.loadPage();
            });
    }

    // 编辑弹框-取消
    editCancel(rowIndex: number, type: string) {
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', true);
        this.ref(`popover-${type}-${rowIndex}`).data.set('visible', false);
    }

    onCreate({value}) {
        const dialog = new CreateDiagnosis({
            data: {
                instanceId: value,
                instanceType: value
                    ? this.data.get('urlQuery').csnId
                        ? 'csn'
                        : this.data.get('urlQuery').natId
                          ? 'nat'
                          : this.data.get('urlQuery').channelId
                            ? 'etChannel'
                            : 'eip'
                    : ''
            }
        });
        dialog.on('success', () => {
            this.loadPage();
        });
        dialog.attach(document.body);
    }

    onRelease() {
        let selectedItems = this.data.get('table.selectedItems');
        let payload = {
            instanceType: selectedItems[0].instanceType?.toUpperCase(),
            region: selectedItems[0].instanceType == 'csn' ? 'global' : selectedItems[0].region,
            instanceId: selectedItems[0].instanceId
        };
        const confirm = new Confirm({
            data: {
                title: '删除提示',
                content: '实例诊断删除后不能恢复，您确定删除吗？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http.deleteDiagnoseInstance(payload).then(() => {
                Notification.success('释放成功');
                this.loadPage();
            });
        });
    }

    // 编辑资源分组
    deleteDiagnosis(row: any) {
        let payload = {
            instanceType: row.instanceType?.toUpperCase(),
            region: row.instanceType == 'csn' ? 'global' : row.region,
            instanceId: row.instanceId
        };
        const confirm = new Confirm({
            data: {
                title: '删除提示',
                content: '实例诊断删除后不能恢复，您确定删除吗？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http.deleteDiagnoseInstance(payload).then(() => {
                Notification.success('释放成功');
                this.loadPage();
            });
        });
    }

    /**
     * @description 介绍卡片变化时回调
     * @param {boolean} flag
     */
    handleToggle(flag: boolean) {
        this.data.set('show', flag);
        window.$storage.set('showDiagnoseIntroduce', flag);
        this.data.set('introduceIcon', INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#151B26';
    }
    handleShowCard() {
        if (!this.data.get('show')) {
            this.data.set('show', true);
            window.$storage.set('showDiagnoseIntroduce', true);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type: string) {
        if (type === 'introduce') {
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
            const ele = this.data.get('introduceEle');
            ele.style.color = '#2468F2';
        } else {
            this.data.set('documentIcon', DOCUMENT_ICON_COLOR);
        }
    }
    handleMouseLeave(type: string) {
        const isShow = this.data.get('show');
        if (type === 'introduce') {
            this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
            const ele = this.data.get('introduceEle');
            !isShow && (ele.style.color = '#151B26');
        } else {
            this.data.set('documentIcon', DOCUMENT_ICON);
        }
    }
    keywordTypeChange(e) {
        switch (e.value) {
            case 'instanceName':
                this.data.set('placeholder', '请输入实例名称进行搜索');
                break;
            case 'instanceId':
                this.data.set('placeholder', '请输入实例ID进行搜索');
                break;
        }
    }
    repeatDiagnosis(row) {
        let payload = {
            instanceType: row.instanceType?.toUpperCase(),
            region: row.instanceType == 'csn' ? 'global' : row.region,
            instanceId: row.instanceId
        };
        this.$http.executeDiagnosis(payload).then(() => {
            this.loadPage();
        });
    }
    diagnoseDetail(row) {
        this.nextTick(() => {
            this.data.set('drawerVisible', true);
            this.data.set('visibaleDraw', true);
            if (document.getElementById('satisfactionNew')) {
                this.loadSatisfaction(row);
            } else {
                this.diagnoseDetail(row);
            }
        });
    }
    loadSatisfaction(row) {
        this.nextTick(() => {
            processor.applyComponent(
                'DiagnoseDrawer',
                {
                    diagnoseData: {
                        diagnoseId: row.lastDiagnoseId,
                        instanceId: row.instanceId
                    },
                    visible: this.data.get('visibaleDraw'),
                    becSts: window.$storage.get('becSts'),
                    csnSts: window.$storage.get('csnSts'),
                    instanceType: row.instanceType,
                    extraData: row.extra || '{}',
                    onCloseDrawer: () => {
                        this.data.set('drawerVisible', false);
                        this.data.set('visibaleDraw', false);
                    },
                    http: request,
                    showJumpUrl: false
                },
                '#satisfactionNew'
            );
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(DiagnosisInstanceList));
