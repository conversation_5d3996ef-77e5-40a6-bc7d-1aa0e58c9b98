export default {
    createDiagnosis: [
        {
            required: false,
            message: ''
        }
    ],
    editTag: [
        {
            required: true,
            message: '请先选择实例对象'
        }
    ],
    deleteDiagnosis: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            isSingle: true,
            message: '不支持批量删除'
        },
        {
            custom(data, options = {}) {
                let selectItem = data[0];
                if (selectItem && selectItem?.lastDiagnoseStatus === 'diagnosing') {
                    return {
                        disable: true,
                        message: '当前状态不允许删除'
                    };
                }
            }
        }
    ]
};
