import u from 'lodash';
import {Component} from 'san';
import {html, decorators, HttpClient, Processor} from '@baiducloud/runtime';
import {Drawer, Collapse, Checkbox, Tag, Loading, Notification, Table} from '@baidu/sui';
import {ClipBoard, Tip} from '@baidu/sui-biz';
import {SuggestSDKProcessor, SuggestSDK} from '@baidu/bce-suggest-collection-sdk';
import {instanceDiagnosisMap} from '@/pages/sanPages/utils/config';
import {
    CSN_BP_STATUS,
    diagnosisInstanceType,
    INSTANCE_STATUS,
    NatStatus,
    InstanceStatus
} from '@/pages/sanPages/common/enum';
import '@baidu/bce-suggest-collection-sdk/lib/style.css';
import './diagnosisRecord.less';

const processor = new SuggestSDKProcessor();
const colmunMap = {
    attachId: '加载实例ID',
    instanceId: '实例ID',
    region: '地域',
    localRegion: '本端地域',
    peerRegion: '对端地域',
    bandwidth: '带宽',
    bwUtilization: '带宽利用率',
    csnRtId: '路由表ID',
    prefix: '网段',
    activeInstances: '生效实例',
    conflictInstances: '冲突实例',
    ipVersion: '网段类型',
    ecmpRouteCnt: 'ECMP路由数量',
    upperLimit: '上限'
};
const {asComponent, invokeSUI, invokeSUIBIZ, template} = decorators;
const tpl = html`
    <template>
        <s-drawer
            open="{=open=}"
            direction="right"
            otherClose="{{false}}"
            on-close="closeDrawer"
            class="diagnosis-drawer-class {{closeDrawerClass}}"
        >
            <div slot="title" class="diagnosis-header">
                <div class="cell detail-part-item detail-part-title">
                    <span class="diagnosis-header-title">{{instanceId}}</span>
                    <s-clip-board text="{{instanceId}}" class="name-icon" style="margin-left: 0px;" />
                </div>
                <div class="cell detail-part-item detail-part-id">
                    <span class="diagnosis-header-desc">诊断ID：</span>
                    <span class="diagnosis-header-desc">{{diagnoseId || '-'}}</span>
                    <s-clip-board text="{{diagnoseId}}" class="name-icon" style="margin-left: 0px;" />
                </div>
                <div class="cell detail-part-item detail-part-time">
                    <span class="diagnosis-header-desc">诊断时间：</span>
                    <span class="diagnosis-header-desc">{{diagnoseTime || '-'}}</span>
                </div>
            </div>
            <s-loading loading="{{loading}}" class="{{!loading ? 's-no-loading' : ''}}">
                <div class="monitor-wrap" s-if="!loading">
                    <div class="diagnosis-content-top {{satisfactionClass ? 'satisfaction-class' : ''}}">
                        <div class="diagnosis-content-image">
                            <div
                                class="image-class {{diagnoseResult === 'critical' ? 'image-class-critical' : (diagnoseResult === 'warning' ? 'image-class-warning' : '')}}"
                            ></div>
                            <div class="diagnosis-text">
                                <div class="diagnosis-image-text" s-if="diagnoseResult !== 'normal'">
                                    <span>发现</span>
                                    <span s-if="errorLength" class="red-class">{{errorLength}}</span
                                    ><span s-if="errorLength">项严重</span>
                                    <span s-if="errorLength && warningLength">、</span>
                                    <span s-if="!warningLength">问题</span>
                                    <span s-if="warningLength" class="warning-class">{{warningLength}}</span
                                    ><span s-if="warningLength">项警告问题</span>
                                    <span>请及时处理</span>
                                </div>
                                <div class="diagnosis-image-text" s-if="diagnoseResult === 'normal'">
                                    未发现风险问题，实例状态正常
                                </div>
                                <div class="diagnosis-image-desc">系统已完成{{diagnoseLength}}项信息检测</div>
                            </div>
                        </div>
                        <div class="satisfaction" s-if="showSatisfaction">
                            <span class="satisfaction-text">问题是否解决：</span>
                            <div id="satisfaction"></div>
                        </div>
                    </div>
                    <div class="diagnosis-detail-class">
                        <span class="diagnosis-content-detail" style="margin-right: 20px;">诊断详情</span
                        ><s-checkbox checked="{{checkAll}}" on-change="checkedChange">显示全部诊断项</s-checkbox>
                    </div>
                    <s-collapse activeKey="{{activeAllKey}}" s-if="!checkAll && !loading && editLine.length">
                        <s-collapse-panel key="{{index}}" s-for="i,index in editLine">
                            <template slot="header">
                                <span class="diagnosis-header-slot">{{i.header}}</span>
                                <div style="float:right;display: flex;align-items: center;">
                                    <span class="critical-number-class {{i.contentArray | getContentClass('critical')}}"
                                        >{{i.contentArray | getContentNumber('critical')}}</span
                                    >
                                    <span class="warning-number-class {{i.contentArray | getContentClass('warning')}}"
                                        >{{i.contentArray | getContentNumber('warning')}}</span
                                    >
                                    <span class="normal-number-class {{i.contentArray | getContentClass('normal')}}"
                                        >{{i.contentArray | getContentNumber('normal')}}</span
                                    >
                                </div>
                            </template>
                            <div class="diagnosis-detail-panel" s-for="item,index in i.contentArray">
                                <div class="diagnosis-detail-panel-header">
                                    <s-tag
                                        class="{{item.tagStatus === 'critical' ? 'critical-tag-status' : (item.tagStatus === 'warning' ? 'warning-tag-status' : 'normal-tag-status')}}"
                                        enhanced
                                        >{{item.tagStatus | getTagText}}</s-tag
                                    >
                                    <span class="diagnosis-detail-panel-header-text">{{item.itemTitle}}</span>
                                    <s-tip layerWidth="200" content="{{item.itemTipContent}}" />
                                </div>
                                <div class="diagnosis-detail-panel-content" s-if="item.tagStatus !== 'normal'">
                                    <p class="diagnosis-detail-panel-content-text">
                                        <span class="panel-content-item">问题原因：</span
                                        ><span class="panel-content-wrap">{{item.desc || '-'}}</span>
                                    </p>
                                    <p class="diagnosis-detail-panel-content-text">
                                        <span class="panel-content-item">影响：</span
                                        ><span class="panel-content-wrap">{{item.effect || '-'}}</span>
                                    </p>
                                    <p class="diagnosis-detail-panel-content-text">
                                        <span class="panel-content-item">修改建议：</span>
                                        <span class="panel-content-wrap panel-content-wrap-suggest">
                                            <!--bca-disable-next-line-->
                                            {{item.itemSuggestion | raw}}
                                        </span>
                                    </p>
                                    <p class="diagnosis-detail-panel-content-text" s-for="i,index in item.resources">
                                        <span class="panel-content-item {{index === 0 ? '' : 'un-visiable-class'}}"
                                            >资源ID：</span
                                        >
                                        <span class="panel-content-wrap">{{i}}</span>
                                        <s-clip-board text="{{i}}" class="name-icon" />
                                    </p>
                                    <s-table
                                        s-if="{{item.tableShow && item.tagStatus !== 'normal'}}"
                                        style="margin-left: 70px;width: 734px;margin-top: 12px;"
                                        columns="{{item.columns}}"
                                        datasource="{{item.dataSource}}"
                                    >
                                        <div slot="c-region">
                                            <span>{{row.region | getRegion}}</span>
                                        </div>
                                        <div slot="c-localRegion">
                                            <span>{{row.localRegion | getRegion}}</span>
                                        </div>
                                        <div slot="c-peerRegion">
                                            <span>{{row.peerRegion | getRegion}}</span>
                                        </div>
                                        <div slot="c-status">
                                            <span class="{{row.statusClass}}">{{row.status}}</span>
                                        </div>
                                        <div slot="h-status">
                                            <span>状态</span>
                                        </div>
                                    </s-table>
                                </div>
                                <div class="s-divider"></div>
                            </div>
                        </s-collapse-panel>
                    </s-collapse>
                    <s-collapse activeKey="{{activeAllShowKey}}" s-if="checkAll">
                        <s-collapse-panel key="{{index}}" s-for="i,index in editAllLine">
                            <template slot="header">
                                <span class="diagnosis-header-slot">{{i.header}}</span>
                                <div style="float:right;display: flex;align-items: center;">
                                    <span class="critical-number-class {{i.contentArray | getContentClass('critical')}}"
                                        >{{i.contentArray | getContentNumber('critical')}}</span
                                    >
                                    <span class="warning-number-class {{i.contentArray | getContentClass('warning')}}"
                                        >{{i.contentArray | getContentNumber('warning')}}</span
                                    >
                                    <span class="normal-number-class {{i.contentArray | getContentClass('normal')}}"
                                        >{{i.contentArray | getContentNumber('normal')}}</span
                                    >
                                </div>
                            </template>
                            <div class="diagnosis-detail-panel" s-for="item,index in i.contentArray">
                                <div class="diagnosis-detail-panel-header">
                                    <s-tag
                                        class="{{item.tagStatus === 'critical' ? 'critical-tag-status' : (item.tagStatus === 'warning' ? 'warning-tag-status' : 'normal-tag-status')}}"
                                        enhanced
                                        >{{item.tagStatus | getTagText}}</s-tag
                                    >
                                    <span class="diagnosis-detail-panel-header-text">{{item.itemTitle}}</span>
                                    <s-tip layerWidth="200" content="{{item.itemTipContent}}" />
                                </div>
                                <div class="diagnosis-detail-panel-content" s-if="item.tagStatus !== 'normal'">
                                    <p class="diagnosis-detail-panel-content-text">
                                        <span class="panel-content-item">问题原因：</span
                                        ><span class="panel-content-wrap">{{item.desc || '-'}}</span>
                                    </p>
                                    <p class="diagnosis-detail-panel-content-text">
                                        <span class="panel-content-item">影响：</span
                                        ><span class="panel-content-wrap">{{item.effect || '-'}}</span>
                                    </p>
                                    <p class="diagnosis-detail-panel-content-text">
                                        <span class="panel-content-item">修改建议：</span>
                                        <span class="panel-content-wrap panel-content-wrap-suggest">
                                            <!--bca-disable-next-line-->
                                            {{item.itemSuggestion | raw}}
                                        </span>
                                    </p>
                                    <p class="diagnosis-detail-panel-content-text" s-for="i,index in item.resources">
                                        <span class="panel-content-item {{index === 0 ? '' : 'un-visiable-class'}}"
                                            >资源ID：</span
                                        >
                                        <span class="panel-content-wrap">{{i}}</span>
                                        <s-clip-board text="{{i}}" class="name-icon" />
                                    </p>
                                    <s-table
                                        s-if="{{item.tableShow && item.tagStatus !== 'normal'}}"
                                        style="margin-left: 70px;width: 734px;margin-top: 12px;"
                                        columns="{{item.columns}}"
                                        datasource="{{item.dataSource}}"
                                    >
                                        <div slot="c-region">
                                            <span>{{row.region | getRegion}}</span>
                                        </div>
                                        <div slot="c-localRegion">
                                            <span>{{row.localRegion | getRegion}}</span>
                                        </div>
                                        <div slot="c-peerRegion">
                                            <span>{{row.peerRegion | getRegion}}</span>
                                        </div>
                                        <div slot="c-status">
                                            <span class="{{row.statusClass}}">{{row.status}}</span>
                                        </div>
                                        <div slot="h-status">
                                            <span>状态</span>
                                        </div>
                                    </s-table>
                                </div>
                                <div class="s-divider"></div>
                            </div>
                        </s-collapse-panel>
                    </s-collapse>
                </div>
            </s-loading>
        </s-drawer>
    </template>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
class diagnosisRecord extends Component {
    components = {
        's-drawer': Drawer,
        's-collapse': Collapse,
        's-collapse-panel': Collapse.Panel,
        's-clip-board': ClipBoard,
        's-checkbox': Checkbox,
        's-tip': Tip,
        's-tag': Tag,
        's-loading': Loading,
        's-table': Table
    };

    static computed = {
        activeAllKey() {
            let editLine = this.data.get('editLine');
            let array = [];
            editLine.forEach((item, index) => {
                array.push(index);
            });
            return array;
        },
        activeAllShowKey() {
            let editAllLine = this.data.get('editAllLine');
            let array = [];
            editAllLine.forEach((item, index) => {
                array.push(index);
            });
            return array;
        }
    };

    static filters = {
        getTagText(value) {
            return value === 'critical' ? '严重' : value === 'warning' ? '警告' : '正常';
        },
        getContentNumber(item, type) {
            return item ? item.filter(i => i.tagStatus === type).length : 0;
        },
        getContentClass(item, type) {
            return item ? (item.filter(i => i.tagStatus === type).length ? '' : 'num-display-none') : '';
        },
        getRegion(value) {
            return value ? window.$context.getEnum('AllRegion').getTextFromValue(value) : '-';
        }
    };

    initData() {
        return {
            name: '',
            open: true,
            showSatisfaction: true,
            editLine: [],
            editAllLine: [],
            loading: true,
            diagnoseLength: 0,
            errorLength: 0,
            warningLength: 0
        };
    }

    async inited() {
        let diagnoseId = this.data.get('diagnoseId');
        let array = [this.$http.getDiagnosisMonitor(diagnoseId)];
        if (window.$storage.get('csnSts') && window.$storage.get('becSts')) {
            try {
                if (!window.$storage.get('bec-resource-open')) {
                    const {result} = await this.$http.getResource();
                    window.$storage.set('bec-resource-open', !!result);
                    if (!!result) {
                        array.push(this.$http.getEdgeNode());
                    }
                } else {
                    array.push(this.$http.getEdgeNode());
                }
            } catch (error) {
                window.$storage.set('bec-resource-open', false);
            }
        }
        if (this.data.get('instanceType') === 'nat') {
            array.push(this.$http.getNatList({natGatewayId: this.data.get('instanceId')}, {'X-silence': true}));
        }
        Promise.all(array).then(data => {
            let res = data[0];
            let realTypeMap = '';

            let becRegionMap: any = {};
            if (this.data.get('instanceType') === 'csn') {
                let nodeList = data[1];
                nodeList?.edgeNodes?.forEach((region: any) => {
                    becRegionMap[region.nodeId] = region.nodeName;
                });
            } else if (this.data.get('instanceType') === 'nat') {
                let obj = array[2]
                    ? data[2]?.result
                        ? data[2]?.result[0]
                        : ''
                    : data[1]?.result
                      ? data[1]?.result[0]
                      : '';
                if (!obj) {
                    realTypeMap = 'privateNat';
                }
            }

            let editLine: any = [];
            let editAllLine: any = [];
            let findType = realTypeMap ? realTypeMap : this.data.get('instanceType');
            let mapItem = instanceDiagnosisMap[findType];
            let length = 0;
            let errorLength = 0;
            let warningLength = 0;
            res.diagnoseItems.forEach(item => {
                const headerItem = mapItem.find(i => i.name === item.type.toLowerCase());
                let header = headerItem.text;
                let headerContent = headerItem.diagnosisArray;
                let contentArray: any = [];
                let contentAllArray: any = [];
                item.items.forEach(i => {
                    let contentItem = headerContent.find(j => j.name === i.name.toLowerCase());
                    let itemTitle = contentItem?.text;
                    let itemTipContent =
                        contentItem.suggestion[i.result]?.tipContent ||
                        contentItem.suggestion['critical']?.tipContent ||
                        contentItem.suggestion['warning']?.tipContent ||
                        contentItem.suggestion['normal']?.tipContent ||
                        '-';
                    let itemSuggestion =
                        contentItem.suggestion[i.result]?.suggest ||
                        contentItem.suggestion['critical']?.suggest ||
                        contentItem.suggestion['warning']?.suggest ||
                        contentItem.suggestion['normal']?.suggest ||
                        '-';
                    let effect =
                        contentItem.suggestion[i.result]?.effect ||
                        contentItem.suggestion['critical']?.effect ||
                        contentItem.suggestion['warning']?.effect ||
                        contentItem.suggestion['normal']?.effect ||
                        '-';
                    let resourcesArray: any = [];
                    let dataSource: any = [];
                    let columns: any = [];
                    i.resources?.forEach(item => {
                        if (item.split('_').length >= 1) {
                            let regionArray = item.split('_');
                            let regionIn =
                                window.$context.getEnum('AllRegion').getTextFromValue(regionArray[0]) ||
                                (becRegionMap[regionArray[0]]
                                    ? becRegionMap[regionArray[0]] + '（边缘节点）'
                                    : regionArray[0]);
                            let regionOut = regionArray[1]
                                ? window.$context.getEnum('AllRegion').getTextFromValue(regionArray[1]) ||
                                  (becRegionMap[regionArray[1]]
                                      ? becRegionMap[regionArray[1]] + '（边缘节点）'
                                      : regionArray[1])
                                : '';
                            resourcesArray.push(regionIn + (regionOut ? '——' + regionOut : ''));
                        } else {
                            resourcesArray.push(item);
                        }
                    });

                    i?.table?.header?.forEach(j => {
                        columns.push({
                            name: j === '状态' ? 'status' : j,
                            label: colmunMap[j] || j
                        });
                    });

                    i?.table?.data?.forEach(j => {
                        let obj = {};
                        i?.table?.header.forEach((k, index) => {
                            obj[k] = j[index];
                            k === '支付方式' &&
                                (obj[k] =
                                    j[index] === 'postpay' ? '后付费' : j[index] === 'prepay' ? '预付费' : j[index]);
                            k === '状态' &&
                                (obj['status'] =
                                    this.data.get('instanceType') === 'csn'
                                        ? CSN_BP_STATUS.getTextFromValue(j[index]?.toLowerCase()) || j[index]
                                        : this.data.get('instanceType') === 'eip'
                                          ? INSTANCE_STATUS.TXT[j[index]?.toLowerCase()] || j[index]
                                          : this.data.get('instanceType') === 'nat'
                                            ? NatStatus.getTextFromValue(j[index]?.toLowerCase()) || j[index]
                                            : this.data.get('instanceType') === 'etChannel'
                                              ? InstanceStatus.getTextFromValue(j[index]?.toLowerCase()) || j[index]
                                              : '');
                            k === '状态' &&
                                (obj['statusClass'] =
                                    this.data.get('instanceType') === 'csn'
                                        ? CSN_BP_STATUS.fromAlias(j[index]?.toLowerCase()).kclass || ''
                                        : this.data.get('instanceType') === 'eip'
                                          ? INSTANCE_STATUS.CLASS[j[index]?.toLowerCase()] || 'status error'
                                          : this.data.get('instanceType') === 'nat'
                                            ? NatStatus.fromValue(j[index]?.toLowerCase()).styleClass || ''
                                            : this.data.get('instanceType') === 'etChannel'
                                              ? InstanceStatus.fromValue(j[index]?.toLowerCase()).kclass
                                              : '');
                        });
                        dataSource.push(obj);
                    });

                    let descShow = i.desc ? (i.desc[i.desc.length - 1] === '。' ? i.desc : i.desc + '。') : i.desc;

                    if (i.result !== 'normal') {
                        contentArray.push({
                            itemTitle,
                            itemTipContent,
                            itemSuggestion,
                            effect,
                            desc: descShow,
                            tagStatus: i.result,
                            resources: resourcesArray,
                            columns,
                            dataSource,
                            tableShow: columns.length
                        });
                        if (i.result === 'critical') {
                            errorLength++;
                        } else {
                            warningLength++;
                        }
                    }
                    contentAllArray.push({
                        itemTitle,
                        itemTipContent,
                        itemSuggestion,
                        effect,
                        desc: descShow,
                        tagStatus: i.result,
                        resources: resourcesArray,
                        columns,
                        dataSource,
                        tableShow: columns.length
                    });
                    length++;
                });
                if (contentArray.length) {
                    editLine.push({
                        header,
                        contentArray
                    });
                }
                editAllLine.push({
                    header,
                    contentArray: contentAllArray
                });
            });
            let sortArray = [];
            let criticalArray: any = [];
            let warningArray: any = [];
            let normalArray: any = [];
            // 定义排序顺序数组
            const statusOrder = ['critical', 'warning', 'normal'];
            editAllLine.forEach(item => {
                // 自定义排序函数
                item.contentArray.sort((a, b) => {
                    return statusOrder.indexOf(a.tagStatus) - statusOrder.indexOf(b.tagStatus);
                });
                if (item.contentArray.find(i => i.tagStatus === 'critical')) {
                    criticalArray.push(item);
                } else if (item.contentArray.find(i => i.tagStatus === 'warning')) {
                    warningArray.push(item);
                } else {
                    normalArray.push(item);
                }
            });
            sortArray = [...criticalArray, ...warningArray, ...normalArray];

            let sortArrayEditLine = [];
            let criticalArrayEditLine: any = [];
            let warningArrayEditLine: any = [];
            let normalArrayEditLine: any = [];

            editLine.forEach(item => {
                if (item.contentArray.find(i => i.tagStatus === 'critical')) {
                    criticalArrayEditLine.push(item);
                } else if (item.contentArray.find(i => i.tagStatus === 'warning')) {
                    warningArrayEditLine.push(item);
                } else {
                    normalArrayEditLine.push(item);
                }
            });
            sortArrayEditLine = [...criticalArrayEditLine, ...warningArrayEditLine, ...normalArrayEditLine];
            this.data.set('editLine', sortArrayEditLine);
            this.data.set('diagnoseResult', res.diagnoseResult);
            this.data.set('diagnoseLength', length);
            this.data.set('errorLength', errorLength);
            this.data.set('warningLength', warningLength);
            this.data.set('editAllLine', sortArray);
            this.data.set('loading', false);
        });
    }
    attached() {
        // 使用定时器函数检查元素是否存在
        this.checkElementExistence('#satisfaction', 500, 10000)
            .then(() => {
                this.loadSatisfaction();
            })
            .catch(error => {
                console.error(error.message);
            });
    }
    closeDrawer() {
        this.data.set('noShow', true);
        this.data.set('showSatisfaction', false);
        this.data.set('closeDrawerClass', 'close-drawer-class');
    }
    checkedChange({value}) {
        this.data.set('checkAll', value);
    }
    // 定义一个定时器函数，用于检查元素是否存在
    checkElementExistence(selector, interval = 500, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            const timer = setInterval(() => {
                const elapsedTime = Date.now() - startTime;

                if (document.querySelector(selector)) {
                    clearInterval(timer);
                    resolve(true);
                } else if (elapsedTime >= timeout) {
                    clearInterval(timer);
                    reject(new Error('Element not found within the specified timeout.'));
                }
            }, interval);
        });
    }
    loadSatisfaction() {
        processor.applyComponent(
            'Entry',
            {
                mode: 'like',
                suggestSDK: new SuggestSDK(new HttpClient(), window.$context),
                scoreTip: diagnosisInstanceType.getTextFromValue(this.data.get('instanceType')) + `实例诊断`,
                simpleUnlikeText: '未解决',
                simpleLikeText: '已解决',
                resourceId: this.data.get('instanceId'),
                placeholder: '请填写未解决原因，最大限制200字',
                dislikeTagData: ['诊断有误', '修复麻烦', '操作麻烦', '问题依旧', '其他'],
                onClose: () => {
                    this.data.set('showSatisfaction', false);
                    this.data.set('satisfactionClass', true);
                    let payload: any = {
                        diagnoseId: this.data.get('diagnoseId'),
                        unsolved: this.data.get('unsolved')
                    };
                    if (this.data.get('unsolved') > 0) {
                        payload.unsolvedReason = this.data.get('unsolvedReason');
                    }
                    this.data.get('userOpinion') && (payload.userOpinion = this.data.get('userOpinion'));
                    this.$http.feedBackReport(payload).then(res => {
                        Notification.success('我们将认真评估每一条建议，为您提供更好的服务！', {title: '感谢您的反馈'});
                    });
                },
                onTextareaChange: value => {
                    this.data.set('userOpinion', value);
                },
                onLikeDislikeChange: value => {
                    this.data.set('unsolved', value > 1 ? 0 : 1);
                },
                onTagSelectChange: (value, allValue) => {
                    let map: any = {
                        诊断有误: 1,
                        修复麻烦: 2,
                        操作麻烦: 3,
                        问题依旧: 4,
                        其他: 5
                    };
                    let unsolvedReason: any = [];
                    value.forEach(item => {
                        unsolvedReason.push(map[item]);
                    });
                    this.data.set('unsolvedReason', unsolvedReason);
                }
            },
            '#satisfaction'
        );
    }
}
export default Processor.autowireUnCheckCmpt(diagnosisRecord);
