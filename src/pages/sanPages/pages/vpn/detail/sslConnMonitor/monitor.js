/*
 * @Description:
 * @Author: <EMAIL>
 * @Date: 2022-03-29 17:28:11
 */

import {Component} from 'san';
import u from 'lodash';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {BcmSDK} from '@baidu/new-bcm-sdk';
import {BcmChartPanel} from '@baidu/new-bcm-sdk-san';
import moment from 'moment';
import {OutlinedRefresh} from '@baidu/sui-icon';

import monitorConfig from '@/pages/sanPages/utils/monitorConfig';
import './style.less';

const {SslVpnConnMetric, shortcutItems} = monitorConfig;
const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent} = decorators;

const tpl = html`
    <div class="{{klass}}">
        <div class="search-wrap">
            <span class="search-item">
                <label class="search-item-label">时间：</label>
                <s-date-picker-date-range-picker
                    s-ref="timeRange"
                    value="{=instanceTime.timeRange=}"
                    width="{{310}}"
                    mode="second"
                    range="{{instanceTime.range}}"
                    on-change="onTimeChange"
                    shortcut="{{shortcutItems}}"
                />
            </span>
            <span class="search-item">
                <label class="search-item-label">监控项：</label>
                <s-select value="{=metrics.option=}" datasource="{{ds.options}}" on-change="loadMetrics"> </s-select>
            </span>
            <span class="search-item" s-if="isShowUserName">
                <label class="search-item-label">统计项：</label>
                <s-select value="{=metrics.statistics=}" datasource="{{ds.statistics}}" on-change="loadMetrics">
                </s-select>
            </span>
            <span class="search-item user-item" s-if="isShowUserName">
                <label class="search-item-label">用户名：</label>
                <s-select
                    width="200"
                    datasource="{{ds.userList}}"
                    value="{=selectUserList=}"
                    on-change="updateUserList"
                    multiple
                    filterable
                    checkAll="{{false}}"
                />
            </span>
            <s-button class="s-icon-button" on-click="onTimeRefresh"><outlined-refresh class="icon-class" /></s-button>
        </div>
        <div class="ssl-monitor-wrap" s-if="{{userLoaded}}">
            <bcm-chart-panel
                s-ref="bcmChart"
                api-type="dimensions"
                scope="BCE_VPN"
                height="{{400}}"
                options="{{options}}"
                showbigable="{{false}}"
                statistics="{{metrics.statistics}}"
                dimensions="{{metrics.dimensions}}"
                metrics="{=metrics.metrics=}"
                period="{{instanceTime.monitorDefaultPeriod}}"
                unit="{{metrics.unit}}"
                bitUnit="{{metrics.bitUnit}}"
                sdk="{{bcmSdk}}"
                startTime="{=instanceTime.startTime=}"
                endTime="{=instanceTime.endTime=}"
                proccessor="{{proccessor}}"
            />
        </div>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@asComponent('@ssl-conn-monitor')
class SslConnMonitor extends Component {
    static components = {
        'bcm-chart-panel': BcmChartPanel,
        'outlined-refresh': OutlinedRefresh
    };
    static computed = {
        isShowUserName() {
            const monitorItem = this.data.get('metrics.option');
            let flag = true;
            if (monitorItem === 'SSLClientConnectionCount') {
                flag = false;
            }
            return flag;
        }
    };
    initData() {
        return {
            klass: 'ssl-conn-monitor-wrap',
            metrics: {
                option: 'inBps',
                statistics: 'average',
                period: '60',
                metrics: [],
                unit: 'Bps',
                bitUnit: 1000,
                dimensions: []
            },
            ds: {
                options: [
                    {text: '入向带宽', value: 'inBps'},
                    {text: '出向带宽', value: 'outBps'},
                    {text: '客户端在线连接数', value: 'SSLClientConnectionCount'}
                ],
                statistics: [
                    {text: '平均值', value: 'average'},
                    {text: '和值', value: 'sum'},
                    {text: '最大值', value: 'maximum'},
                    {text: '最小值', value: 'minimum'},
                    {text: '样本数', value: 'sampleCount'}
                ],
                userList: []
            },
            selectUserList: [],
            endOriginTime: moment().valueOf(),
            instanceTime: {
                timeRange: {
                    begin: new Date(moment().subtract(1, 'hour').valueOf()),
                    end: new Date(moment().valueOf())
                },
                range: {
                    begin: new Date(moment().subtract(40, 'day').valueOf()),
                    end: new Date(moment().valueOf())
                },
                startTime: moment().subtract(1, 'hour').utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
                endTime: moment().utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
                monitorDefaultPeriod: 60
            },
            shortcutItems,
            bcmSdk: new BcmSDK({client: window.$http, context: window.$context}),
            options: {dataZoom: {start: 0}},
            proccessor: this.proccessor.bind(this),
            userLoaded: false
        };
    }

    inited() {
        this.watch('instanceTime.timeRange', timeRange => {
            this.onTimeChange({value: timeRange});
        });
    }

    attached() {
        this.getUserList();
    }

    getUserList() {
        let vpnId = this.data.get('context').vpnId;
        this.$http
            .getSslUserList(
                {
                    vpnId: vpnId,
                    pageNo: 1,
                    pageSize: 1000
                },
                {'x-silent-codes': ['NoSuchObject']}
            )
            .then(({result}) => {
                this.data.set(
                    'ds.userList',
                    result.map((item, index) => ({
                        text: item.userName,
                        value: item.userName,
                        disabled: index > 9
                    }))
                );
                let userList = [];
                let len = result.length > 10 ? 10 : result.length;
                for (let i = 0; i < len; i++) {
                    userList.push(result[i].userName);
                }
                this.data.set('selectUserList', userList);
                this.initMetricConfig(userList);
                this.data.set('userLoaded', true);
            })
            .catch(() => {
                this.initMetricConfig([]);
                this.data.set('userLoaded', true);
            });
    }

    initMetricConfig(userList) {
        let option = this.data.get('metrics.option');
        this.data.set('metrics.metrics', SslVpnConnMetric[option].metrics);
        this.data.set('metrics.unit', SslVpnConnMetric[option].unit);
        this.data.set('metrics.bitUnit', SslVpnConnMetric[option].bitUnit);
        let vpnId = this.data.get('context').vpnId;
        const monitorItem = this.data.get('metrics.option');
        let dimensions = userList.map(item =>
            userList.length && monitorItem !== 'SSLClientConnectionCount'
                ? `VpnId:${vpnId};Username:${item}`
                : `VpnId:${vpnId}`
        );
        this.data.set('metrics.dimensions', dimensions);
    }

    updateInstanceSelectDisable(value) {
        let datasource = this.data.get('blbInstanceList');
        let newBlbInstanceList = [];
        u.each(datasource, item => {
            let newItem = {...item};
            value.length < selectTopNum && delete newItem.disabled;
            value.length >= selectTopNum && (newItem.disabled = !value.includes(item.value));
            newBlbInstanceList.push(newItem);
        });
        this.data.set('blbInstanceList', newBlbInstanceList);
    }

    updateUserList() {
        this.nextTick(() => {
            let selectUser = this.data.get('selectUserList');
            let userData = this.data.get('ds.userList');
            if (selectUser.length > 9) {
                this.data.set(
                    'ds.userList',
                    userData.map(item => {
                        if (selectUser.includes(item.value)) {
                            return {
                                ...item,
                                disabled: false
                            };
                        } else {
                            return {
                                ...item,
                                disabled: true
                            };
                        }
                    })
                );
            } else {
                this.data.set(
                    'ds.userList',
                    userData.map(item => ({
                        ...item,
                        disabled: false
                    }))
                );
            }
            this.loadMetrics();
        });
    }

    loadMetrics() {
        this.nextTick(() => {
            let component = this.ref('bcmChart');
            if (component) {
                this.initMetricConfig(this.data.get('selectUserList'));
                component.loadMetrics();
            }
        });
    }

    onTimeRefresh() {
        if (this.data.get('instanceTime.timeRange.end').valueOf() >= this.data.get('endOriginTime')) {
            this.data.set('instanceTime.timeRange.end', new Date(moment().valueOf()));
        } else {
            this.loadMetrics();
        }
    }

    proccessor(data) {
        const statistics = this.data.get('metrics.statistics');
        if (data && data.series && data.series.length > 0) {
            data.series.forEach(series => {
                series.name = series.name.split(',')[1] || series.name;
                series.data = series.data.map(item => {
                    if (!item || Object.keys(item).length === 0) {
                        let obj = {};
                        obj[statistics] = 0;
                        return obj;
                    }
                    return item;
                });
            });
        }
        return data;
    }

    onTimeChange({value}) {
        let startTime = moment(value.begin).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        let endTime = moment(value.end).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        let ms = moment(value.end) - moment(value.begin);
        let hourTime = Math.round(ms / 86400000);
        switch (true) {
            case hourTime <= 1:
                this.data.set('instanceTime.monitorDefaultPeriod', 60);
                break;
            case hourTime <= 3:
                this.data.set('instanceTime.monitorDefaultPeriod', 300);
                break;
            case hourTime <= 7:
                this.data.set('instanceTime.monitorDefaultPeriod', 600);
                break;
            case hourTime <= 14:
                this.data.set('instanceTime.monitorDefaultPeriod', 1800);
                break;
            case hourTime <= 40:
                this.data.set('instanceTime.monitorDefaultPeriod', 3600);
                break;
            default:
                break;
        }
        this.data.set('instanceTime.startTime', startTime);
        this.data.set('instanceTime.endTime', endTime);
        this.loadMetrics();
    }
}
export default San2React(Processor.autowireUnCheckCmpt(SslConnMonitor));
