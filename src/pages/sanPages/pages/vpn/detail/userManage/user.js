import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {Notification, Dialog} from '@baidu/sui';
import {OutlinedPlus} from '@baidu/sui-icon';
import {columns, dialogColumns} from './tableField';
import {checker} from '@baiducloud/bce-opt-checker';
import rules from '../../rule';
import './style.less';

const {invokeComp, invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent} = decorators;
const tpl = html`
    <div>
        <div class="vpn-ssl-user">
            <div class="content-box">
                <div class="content-header">
                    <h4 class="vpn-common-label">{{'用户管理'}}</h4>
                </div>
                <div class="tool-tip">
                    <s-tooltip trigger="{{createSslUserRule.disable ? 'hover' : ''}}" placement="right">
                        <!--bca-disable-next-line-->
                        <div slot="content">{{createSslUserRule.message | raw}}</div>
                        <s-button
                            class="add-user-btn"
                            disabled="{{createSslUserRule.disable}}"
                            skin="primary"
                            on-click="onCreate"
                        >
                            <outlined-plus />
                            {{'添加用户'}}
                        </s-button>
                    </s-tooltip>
                </div>
                <s-table
                    class="vpn-ssl-user-table"
                    columns="{{table.columns}}"
                    loading="{{table.loading}}"
                    datasource="{{table.datasource}}"
                >
                    <div slot="empty">
                        <s-empty on-click="onCreate" />
                    </div>
                    <div slot="c-opt" class="operations">
                        <s-button skin="stringfy" on-click="onEdit(row)">编辑</s-button>
                        <s-button skin="stringfy" on-click="onDelete(row)">删除</s-button>
                    </div>
                </s-table>
                <s-pagination
                    s-if="{{pager.total}}"
                    slot="pager"
                    layout="{{'total, pageSize, pager, go'}}"
                    pageSize="{{pager.pageSize}}"
                    total="{{pager.total}}"
                    page="{{pager.page}}"
                    on-pagerChange="onPagerChange"
                    on-pagerSizeChange="onPagerSizeChange"
                />
            </div>
            <s-dialog
                width="{{900}}"
                title="{{isEdit ? '编辑用户' : '创建用户'}}"
                on-close="close"
                open="{=dialogDisplay=}"
            >
                <div class="ssl-user-dialog-container">
                    <s-button class="add-user-btn" s-if="!isEdit" skin="primary" on-click="addUser">
                        <outlined-plus />
                        {{'添加用户'}}
                    </s-button>
                    <s-table
                        class="vpn-ssl-user-table"
                        columns="{{dialogTable.columns}}"
                        datasource="{{dialogTable.datasource}}"
                    >
                        <div slot="empty">
                            <s-empty>
                                <div slot="action"></div>
                            </s-empty>
                        </div>
                        <div slot="c-userName">
                            <div class="input-item">
                                <s-input
                                    disabled="{{isEdit}}"
                                    value="{=row.userName=}"
                                    on-input="inputChange($event, 'userName', rowIndex)"
                                    placeholder="请输入"
                                />
                                <label s-if="row.nameErr" class="form-item-invalid-label">{{row.nameErr}}</label>
                            </div>
                        </div>
                        <div slot="c-password">
                            <div class="input-item">
                                <s-input
                                    value="{=row.password=}"
                                    on-input="inputChange($event, 'password', rowIndex)"
                                    placeholder="请输入"
                                />
                                <label s-if="row.passwordErr" class="form-item-invalid-label"
                                    >{{row.passwordErr}}</label
                                >
                            </div>
                        </div>
                        <div slot="c-description">
                            <div class="delete-row">
                                <div class="input-item">
                                    <s-input
                                        value="{=row.description=}"
                                        on-input="inputChange($event, 'description', rowIndex)"
                                        placeholder="请输入"
                                    />
                                    <label s-if="row.descErr" class="form-item-invalid-label">{{row.descErr}}</label>
                                </div>
                                <s-button
                                    skin="stringfy"
                                    s-if="dialogTable.datasource.length > 1"
                                    on-click="onRemove(rowIndex)"
                                    >删除</s-button
                                >
                            </div>
                        </div>
                    </s-table>
                </div>
                <div slot="footer">
                    <s-button on-click="close">取消</s-button>
                    <s-button skin="primary" disabled="{{addDisabled}}" on-click="dialogConfirm">确定</s-button>
                </div>
            </s-dialog>
        </div>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@asComponent('@vpn-ssl-user')
class VpnSslUser extends Component {
    static components = {
        'outlined-plus': OutlinedPlus
    };
    initData() {
        return {
            table: {
                loading: false,
                columns,
                datasource: []
            },
            dialogTable: {
                columns: dialogColumns,
                datasource: []
            },
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            dialogDisplay: false,
            isEdit: false,
            addDisabled: false
        };
    }

    inited() {
        this.loadPage();
    }

    getPayload() {
        const {pager} = this.data.get('');
        const vpnId = this.data.get('context').vpnId;
        let payload = {
            vpnId,
            pageNo: pager.page,
            pageSize: pager.pageSize
        };
        return {...payload};
    }

    // 检查配额
    async userQuota() {
        const data = await this.$http.getSslUserQuota(this.data.get('context').vpnId, {
            'x-silent-codes': ['NoSuchObject']
        });
        let {createSslUserRule} = checker.check(rules, data, 'createSslUserRule', {quota: data.free});
        this.data.set('createSslUserRule', createSslUserRule);
    }

    loadPage() {
        this.data.set('table.loading', true);
        let payload = this.getPayload();
        this.$http
            .getSslUserList(payload, {'x-silent-codes': ['NoSuchObject']})
            .then(res => {
                this.data.set('table.datasource', res.result);
                this.data.set('pager.total', res.totalCount);
                this.data.set('table.loading', false);
            })
            .catch(() => {
                this.data.set('table.loading', false);
            });
        this.userQuota();
    }

    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }

    onPagerSizeChange(e) {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', e.value.pageSize);
        this.loadPage();
    }

    onCreate() {
        this.data.set('isEdit', false);
        this.data.set('dialogDisplay', true);
        this.data.set('dialogTable.datasource', [
            {
                userName: '',
                password: '',
                description: ''
            }
        ]);
    }

    addUser() {
        this.data.push('dialogTable.datasource', {
            userName: '',
            password: '',
            description: ''
        });
    }

    onEdit(row) {
        this.data.set('isEdit', true);
        this.data.set('dialogDisplay', true);
        this.data.set('dialogTable.datasource', [
            {
                userId: row.userId,
                userName: row.userName,
                password: row.password,
                description: row.description
            }
        ]);
    }

    async onDelete(row) {
        Dialog.warning({
            content: '确认删除？',
            onOk: async () => {
                await this.$http.deleteSslUser(this.data.get('context').vpnId, row.userId);
                this.loadPage();
                Notification.success('删除成功');
            }
        });
    }

    inputChange(e, name, index) {
        this.data.set(`dialogTable.datasource[${index}].${name}`, e.value);
    }

    dialogConfirm() {
        for (let i = 0; i < this.data.get('dialogTable.datasource').length; i++) {
            let item = this.data.get(`dialogTable.datasource[${i}]`);
            let {userName, password, description} = item;
            if (!userName) {
                this.data.set(`dialogTable.datasource[${i}].nameErr`, '请输入用户名');
                return;
            }
            if (!/^[a-zA-Z][\w\-\_\/\.]{0,64}$/.test(userName)) {
                this.data.set(
                    `dialogTable.datasource[${i}].nameErr`,
                    '大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65'
                );
                return;
            } else {
                this.data.set(`dialogTable.datasource[${i}].nameErr`, '');
            }
            if (!password) {
                this.data.set(`dialogTable.datasource[${i}].passwordErr`, '请输入密码');
                return;
            }
            if (
                !(
                    password.length > 7 &&
                    password.length < 18 &&
                    /[a-zA-Z]/.test(password) &&
                    /\d/.test(password) &&
                    /[!@#$%^*()_]/.test(password) &&
                    /^[a-zA-Z\d!@#$%^*()_]+$/.test(password)
                )
            ) {
                this.data.set(
                    `dialogTable.datasource[${i}].passwordErr`,
                    '8～17位字符，英文、数字和符号必须同时存在，符号仅限!@#$%^*(_'
                );
                return;
            } else {
                this.data.set(`dialogTable.datasource[${i}].passwordErr`, '');
            }
            if (description.length > 200) {
                this.data.set(`dialogTable.datasource[${i}].descErr`, '描述不超过200个字符');
                return;
            } else {
                this.data.set(`dialogTable.datasource[${i}].descErr`, '');
            }
        }
        let payload = {
            vpnId: this.data.get('context').vpnId,
            users: this.data.get('dialogTable.datasource').map(item => ({
                userName: item.userName,
                password: item.password,
                description: item.description
            }))
        };
        this.data.set('addDisabled', true);
        if (this.data.get('isEdit')) {
            let userId = this.data.get('dialogTable.datasource')[0].userId;
            payload = {
                userName: payload.users[0].userName,
                password: payload.users[0].password,
                description: payload.users[0].description
            };
            this.$http
                .editSslUser(this.data.get('context').vpnId, userId, payload)
                .then(res => {
                    this.close();
                    this.loadPage();
                })
                .catch(() => {
                    this.data.set('addDisabled', false);
                });
        } else {
            this.$http
                .createSslUser(payload)
                .then(res => {
                    this.close();
                    this.loadPage();
                })
                .catch(() => {
                    this.data.set('addDisabled', false);
                });
        }
    }

    onRemove(rowIndex) {
        this.data.removeAt('dialogTable.datasource', rowIndex - 1);
    }

    close() {
        this.data.set('pager.page', 1);
        this.data.set('dialogTable.datasource', []);
        this.data.set('dialogDisplay', false);
        this.data.set('addDisabled', false);
    }
}
export default San2React(Processor.autowireUnCheckCmpt(VpnSslUser));
