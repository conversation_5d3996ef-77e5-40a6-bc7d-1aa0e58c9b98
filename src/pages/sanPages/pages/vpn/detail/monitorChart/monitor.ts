/**
 * @file vpc/src/vpn/bcm/List.js
 * <AUTHOR>
 */

import {Component} from 'san';
import u from 'lodash';
import {OutlinedRefresh} from '@baidu/sui-icon';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import moment from 'moment';
import {BcmSDK} from '@baidu/new-bcm-sdk';
import {BcmChartPanel} from '@baidu/new-bcm-sdk-san';

import {VpnStatus} from '@/pages/sanPages/common/enum';
import monitorConfig from '@/pages/sanPages/utils/monitorConfig';
import './style.less';

const {VpnEipMetric, VpnMetric, shortcutItems} = monitorConfig;
const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent} = decorators;

const tpl = html`
    <div>
        <div class="{{klass}}">
            <div class="vpn-content-wrap">
                <div class="monitor-wrap">
                    <div class="monitor-item-box">
                        <h4>VPN网关监控信息</h4>
                        <div class="alarm-info">
                            报警信息：
                            <span class="alarm-state">
                                <span class="status normal"></span>
                                {{vpnAlarm.okStateCount}}项状态正常
                            </span>
                            <span class="alarm-state">
                                <span class="status error"></span>
                                {{vpnAlarm.alarmStateCount}}项状态异常
                            </span>
                            <span class="alarm-state">
                                <span class="status warning"></span>
                                {{vpnAlarm.insufficientStateCount}}项数据不足
                            </span>
                            <s-button
                                class="alarm-detail"
                                track-id="ti_vpc_vpn_monitor_detail"
                                tack-name="VPN网关/报警详情"
                                on-click="alarmDetail"
                            >
                                报警详情
                            </s-button>
                        </div>
                        <div class="button-wrap">
                            <span class="alarm-state">
                                <span>时间：</span>
                                <s-date-picker-date-range-picker
                                    value="{=vpnTime.timeRange=}"
                                    width="310"
                                    shortcut="{{shortcutItems}}"
                                    range="{{range}}"
                                    mode="second"
                                    on-change="onTimeChange('vpnTime', $event)"
                                />
                            </span>
                            <s-button class="s-icon-button" on-click="refreshChart('vpn')">
                                <outlined-refresh />
                            </s-button>
                        </div>
                        <div
                            class="vpn-monitor-trends {{urlQuery.vpnType === 'ipsec' ? '' : 'vpn-monitor-ssl-monitor'}}"
                        >
                            <div class="monitor-trend-box" s-for="item,index in vpnChart">
                                <bcm-chart-panel
                                    s-ref="vpn-alarm-chart-{{index}}"
                                    withFilter="{{false}}"
                                    scope="{{item.scope}}"
                                    dimensions="{{item.dimensions}}"
                                    statistics="{{item.statistics}}"
                                    title="{{item.title}}"
                                    options="{{options}}"
                                    api-type="metricName"
                                    startTime="{=vpnTime.startTime=}"
                                    endTime="{=vpnTime.endTime=}"
                                    period="{{vpnTime.monitorDefaultPeriod}}"
                                    metrics="{{item.metrics}}"
                                    unit="{{item.unit}}"
                                    bitUnit="{{item.bitUnit}}"
                                    width="{{'auto'}}"
                                    height="{{230}}"
                                    sdk="{{bcmSdk}}"
                                >
                                </bcm-chart-panel>
                            </div>
                        </div>
                    </div>
                    <div s-if="{{urlQuery.vpnType === 'ipsec' && vpnConn.id}}" class="monitor-item-box">
                        <h4>VPN隧道监控信息</h4>
                        <div class="alarm-info row-line">
                            <span class="wrap-text">VPN隧道：</span>
                            <s-select
                                datasource="{{vpnConn.datasource}}"
                                value="{=vpnConn.id=}"
                                disabled="{{!vpnConn.id}}"
                                style="margin-right: 5px"
                            />
                            <span>报警信息：</span>
                            <span class="alarm-state">
                                <span class="status normal"></span>
                                {{vpnConn.alarm.okStateCount}}项状态正常
                            </span>
                            <span class="alarm-state">
                                <span class="status error"></span>
                                {{vpnConn.alarm.alarmStateCount}}项状态异常
                            </span>
                            <span class="alarm-state">
                                <span class="status warning"></span>
                                {{vpnConn.alarm.insufficientStateCount}}项数据不足
                            </span>
                            <s-button
                                class="alarm-detail"
                                disabled="{{!vpnConn.id}}"
                                track-id="ti_vpc_vpn_monitor_detail"
                                tack-name="VPN隧道/报警详情"
                                on-click="vpnConnAlarmDetail"
                            >
                                报警详情
                            </s-button>
                        </div>
                        <div class="button-wrap">
                            <span class="alarm-state">
                                <span>时间：</span>
                                <s-date-picker-date-range-picker
                                    value="{=vpnConnTime.timeRange=}"
                                    width="310"
                                    shortcut="{{shortcutItems}}"
                                    range="{{range}}"
                                    mode="second"
                                    on-change="onTimeChange('vpnConnTime', $event)"
                                />
                            </span>
                            <s-button class="s-icon-button" on-click="refreshChart('conn')" disabled="{{!vpnConn.id}}">
                                <outlined-refresh />
                            </s-button>
                        </div>
                        <div class="vpn-monitor-trends" s-if="vpnConn.id">
                            <div class="monitor-trend-box" s-for="item,index in vpnConnChart">
                                <bcm-chart-panel
                                    s-ref="conn-alarm-chart-{{index}}"
                                    withFilter="{{false}}"
                                    scope="{{item.scope}}"
                                    dimensions="{{item.dimensions}}"
                                    statistics="{{item.statistics}}"
                                    title="{{item.title}}"
                                    api-type="metricName"
                                    options="{{options}}"
                                    startTime="{=vpnConnTime.startTime=}"
                                    endTime="{=vpnConnTime.endTime=}"
                                    period="{{vpnConnTime.monitorDefaultPeriod}}"
                                    metrics="{{item.metrics}}"
                                    unit="{{item.unit}}"
                                    bitUnit="{{item.bitUnit}}"
                                    width="{{'auto'}}"
                                    height="{{230}}"
                                    sdk="{{bcmSdk}}"
                                >
                                </bcm-chart-panel>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
`;
@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@asComponent('@vpn-monitor')
class VpnDetailMonitor extends Component {
    static components = {
        'bcm-chart-panel': BcmChartPanel,
        'outlined-refresh': OutlinedRefresh
    };
    static filters = {
        statusStyle(status) {
            let config = VpnStatus.fromValue(status);
            return config ? config.styleClass : '';
        },
        statusText(status) {
            let config = VpnStatus.fromValue(status);
            return config ? config.text : '';
        }
    };
    initData() {
        return {
            klass: ['main-wrap vpn-monitor-wrap vpn-common-page'],
            options: {
                color: ['#2468f2', '#5FB333'],
                legend: {
                    x: 'right',
                    y: 'top'
                }
            },
            vpnConn: {
                id: '',
                datasource: [],
                alarm: {}
            },
            range: {
                begin: new Date(moment().subtract(40, 'day').valueOf()),
                end: new Date(moment().valueOf())
            },
            vpnConnTime: {
                timeRange: {
                    begin: new Date(moment().subtract(1, 'hour').valueOf()),
                    end: new Date(moment().valueOf())
                },
                startTime: moment().subtract(1, 'hour').utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
                endTime: moment().utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
                monitorDefaultPeriod: 60
            },

            vpnTime: {
                timeRange: {
                    begin: new Date(moment().subtract(1, 'hour').valueOf()),
                    end: new Date(moment().valueOf())
                },
                startTime: moment().subtract(1, 'hour').utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
                endTime: moment().utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
                monitorDefaultPeriod: 60
            },
            endOriginTime: moment().valueOf(),
            shortcutItems,
            startTimeToSecond: moment().subtract(1, 'hour').utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
            vpnChart: [],
            vpnConnChart: [],
            bcmSdk: new BcmSDK({client: window.$http, context: window.$context}),
            options: {dataZoom: {start: 0}},
            urlQuery: getQueryParams()
        };
    }

    inited() {
        ['vpnTime', 'vpnConnTime'].forEach(it => {
            this.watch(`${it}.timeRange`, timeRange => {
                this.onTimeChange(it, {value: timeRange});
            });
        });
    }

    attached() {
        this.getVpnDetail();
        this.getVpnConnList();
        this.watch('vpnConn.id', value => {
            this.loadVpnConnAlarmInfo();
            this.refreshChart('conn');
        });
    }
    onTimeChange(type, {value}) {
        const key = {
            vpnTime: 'vpn',
            vpnConnTime: 'conn'
        };
        let startTime = moment(value.begin).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        let endTime = moment(value.end).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        let ms = moment(value.end) - moment(value.begin);
        let hourTime = Math.round(ms / 86400000);
        switch (true) {
            case hourTime <= 1:
                this.data.set(`${type}.monitorDefaultPeriod`, 60);
                break;
            case hourTime <= 3:
                this.data.set(`${type}.monitorDefaultPeriod`, 300);
                break;
            case hourTime <= 7:
                this.data.set(`${type}.monitorDefaultPeriod`, 600);
                break;
            case hourTime <= 14:
                this.data.set(`${type}.monitorDefaultPeriod`, 1800);
                break;
            case hourTime <= 40:
                this.data.set(`${type}.monitorDefaultPeriod`, 3600);
                break;
            default:
                break;
        }
        this.data.set(`${type}.startTime`, startTime);
        this.data.set(`${type}.endTime`, endTime);
        this.onRefresh(key[type]);
    }

    alarmDetail() {
        const region = window.$context.getCurrentRegionId();
        redirect(
            `/bcm/#/bcm/alarm/rule/list~scope=BCE_EIP&dimensions=InstanceId:${this.data.get('instance.eipShortId')}&region=${region}`
        );
    }
    vpnConnAlarmDetail() {
        const region = window.$context.getCurrentRegionId();
        redirect(
            `/bcm/#/bcm/alarm/rule/list~scope=BCE_VPN&dimensions=ConnID:${this.data.get('vpnConn.id')}&region=${region}`
        );
    }
    getVpnDetail() {
        return this.$http
            .getVpnDetail(
                {
                    vpnId: this.data.get('urlQuery.vpnId')
                },
                {'x-silent-codes': ['NoSuchObject']}
            )
            .then(instance => {
                this.data.set('instance', instance);
                this.initVpnMonitor();
                this.loadVpnAlarmInfo();
            });
    }
    initVpnMonitor() {
        let instance = this.data.get('instance');
        let chartConfig = [];
        u.each(VpnEipMetric, item => {
            let config = {
                scope: 'BCE_EIP',
                period: 60,
                statistics: 'average',
                title: item.title,
                unit: item.unit,
                bitUnit: item.bitUnit,
                metrics: item.metrics,
                dimensions: `InstanceId:${instance.eipShortId}`
            };
            chartConfig.push(config);
        });
        this.data.set('vpnChart', chartConfig);
    }

    initVpnConnMonitor() {
        let vpnConnId = this.data.get('vpnConn.id');
        let chartConfig = [];
        u.each(VpnMetric, item => {
            let config = {
                scope: 'BCE_VPN',
                period: 60,
                statistics: 'average',
                title: item.title,
                unit: item.unit,
                bitUnit: item.bitUnit,
                metrics: item.metrics,
                dimensions: `ConnID:${vpnConnId}`
            };
            chartConfig.push(config);
        });
        this.data.set('vpnConnChart', chartConfig);
    }

    getVpnConnList() {
        this.$http
            .vpnConnList(
                {
                    vpnId: this.data.get('urlQuery.vpnId')
                },
                window.$context.getCurrentRegionId(),
                {'x-silent-codes': ['NoSuchObject']}
            )
            .then(data => {
                let result = [];
                u.each(data || [], item => {
                    result.push({
                        value: item.vpnConnId,
                        text: item.vpnConnName
                    });
                });
                this.data.set('vpnConn.datasource', result);
                result.length && this.data.set('vpnConn.id', result[0].value);
            });
    }

    refreshChart(type: 'vpn' | 'conn') {
        type === 'vpn' ? this.initVpnMonitor() : this.initVpnConnMonitor();
        const typeMapMonitor = {
            vpn: 'vpnTime',
            conn: 'vpnConnTime'
        };
        if (this.data.get(`${typeMapMonitor[type]}.timeRange.end`).valueOf() >= this.data.get('endOriginTime')) {
            this.data.set(`${typeMapMonitor[type]}.timeRange.end`, new Date(moment().valueOf()));
        } else {
            this.onRefresh(type);
        }
    }

    onRefresh(type) {
        let chartConfig = type === 'conn' ? this.data.get('vpnConnChart') : this.data.get('vpnChart');
        u.map(chartConfig, (item, i) => {
            let chartPanel = this.ref(`${type}-alarm-chart-${i}`);
            chartPanel && chartPanel.loadMetrics();
        });
    }

    loadVpnAlarmInfo() {
        this.$http
            .getAlarmSummary({
                dimensions: 'InstanceId:' + this.data.get('instance.eipShortId'),
                scope: 'BCE_EIP'
            })
            .then(data => {
                this.data.set('vpnAlarm', data);
            });
    }

    loadVpnConnAlarmInfo() {
        this.$http
            .getAlarmSummary({
                dimensions: 'ConnID:' + this.data.get('vpnConn.id'),
                scope: 'BCE_VPN'
            })
            .then(data => this.data.set('vpnConn.alarm', data));
    }
}
export default San2React(Processor.autowireUnCheckCmpt(VpnDetailMonitor));
