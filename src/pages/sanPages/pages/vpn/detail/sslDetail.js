import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {getVpcName} from '@/pages/sanPages/utils/common';
import {utcToTime} from '@/pages/sanPages/utils/helper';
import './style.less';

const kXhrOptions = {'X-silence': true};
const {invokeComp, invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent} = decorators;
const tpl = html`
    <div>
        <div class="{{klass}}">
            <div class="vpn-content-wrap">
                <div class="content-box">
                    <div class="content-header">
                        <h4 class="vpn-common-label">{{'基本信息：'}}</h4>
                    </div>
                    <div class="content-item-box">
                        <div class="content-item">
                            <div class="content-item-key">{{'名称：'}}</div>
                            <div class="content-item-value" s-ref="edit-name">
                                <span class="text-hidden"> {{instance.vpnConnName || '-'}} </span>
                            </div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'所在私有网络：'}}</div>
                            <div class="content-item-value">{{vpcInfo.name || '-'}}{{vpcInfo.cidr | filterCidr}}</div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'接口类型：'}}</div>
                            <div class="content-item-value">{{instance.interfaceType | interfaceTypeUpper}}</div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'VPN服务端网络：'}}</div>
                            <div class="content-item-value" s-ref="edit-name">
                                <span class="text-hidden"> {{instance.localSubnets || '-'}} </span>
                            </div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'VPN客户端网络：'}}</div>
                            <div class="content-item-value" s-ref="edit-name">
                                <span class="text-hidden"> {{instance.remoteSubnets || '-'}} </span>
                            </div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'描述：'}}</div>
                            <div class="content-item-value" s-ref="edit-desc">
                                <span class="text-hidden"> {{instance.description || '-'}} </span>
                            </div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'DNS：'}}</div>
                            <div class="content-item-value" s-ref="edit-desc">
                                <span class="text-hidden"> {{instance.clientDns || '-'}} </span>
                            </div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'协议：'}}</div>
                            <div class="content-item-value">{{instance.protocol}}</div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'端口：'}}</div>
                            <div class="content-item-value">{{instance.port}}</div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'加密算法：'}}</div>
                            <div class="content-item-value">{{'aes256'}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@asComponent('@vpn-ssl-detail')
class VpnDetail extends Component {
    static filters = {
        filterTime(value) {
            return value ? utcToTime(value) : '-';
        },
        filterCidr(value) {
            return value ? `(${value})` : '';
        },
        interfaceTypeUpper(value) {
            return value ? value.toUpperCase() : '-';
        }
    };

    initData() {
        return {
            klass: ['main-wrap vpn-detail-wrap vpn-common-page'],
            instance: {},
            vpcInfo: {},
            unset: '未配置',
            urlQuery: getQueryParams()
        };
    }

    inited() {
        this.data.set('instance', this.data.get('context').instance);
        const vpcInfo = this.data.get('context').vpcInfo || {};
        const vpcId = this.data.get('context').vpcId || '';
        const vpcIdInfo = vpcInfo[vpcId] || {};
        vpcIdInfo.name = getVpcName(vpcIdInfo.name);
        this.data.set('vpcInfo', vpcIdInfo);
    }

    loadPage() {
        this.getDetail();
        this.getVpcInfo();
    }

    getDetail() {
        this.$http
            .getVpnConnDetail(
                {
                    vpnId: this.data.get('urlQuery.vpnId'),
                    vpnConnId: this.data.get('urlQuery.vpnConnId')
                },
                {'x-silent-codes': ['NoSuchObject']}
            )
            .then(instance => {
                this.data.set('instance', instance);
            });
    }
    getVpcInfo() {
        let vpcId = this.data.get('urlQuery.vpcId') || '';
        this.$http.getVpcDetail({vpcIds: [vpcId]}).then(data => {
            let vpc = data[vpcId] || {};
            vpc.name = getVpcName(vpc.name);
            this.data.set('vpcInfo', vpc);
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(VpnDetail));
