import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {EipBindDialog} from '@baidu/bce-eip-sdk-san';
import {checker} from '@baiducloud/bce-opt-checker';
import {EipSDK} from '@baidu/bce-eip-sdk';
import {Notification} from '@baidu/sui';

import Confirm from '@/pages/sanPages/components/confirm';
import InternalIpBindDialog from '../components/internalIpBind';
import {getVpcName} from '@/pages/sanPages/utils/common';
import {kXhrOptions, utcToTime} from '@/pages/sanPages/utils/helper';
import {VpnStatus, vpnFlavor} from '@/pages/sanPages/common/enum';
import Rule from '@/pages/sanPages/utils/rule';
import rules from '../rule';
import './style.less';

const {invokeComp, invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent} = decorators;
const tpl = html`
    <div>
        <div class="{{klass}}">
            <div class="vpn-content-wrap">
                <div class="content-box">
                    <div class="content-header">
                        <h4 class="vpn-common-label">{{'基本信息：'}}</h4>
                    </div>
                    <div class="content-item-box">
                        <div class="content-item">
                            <div class="content-item-key">ID：</div>
                            <div class="content-item-value">
                                <span class="text-hidden">{{instance.vpnId}}</span>
                                <s-clip-board
                                    class="blue-icon"
                                    text="{{instance.vpnId}}"
                                    successMessage="{{'已复制到剪贴板'}}"
                                >
                                    <s-icon name="copy" />
                                </s-clip-board>
                            </div>
                        </div>
                        <div class="content-item" s-if="instance.netType === 'intranet'">
                            <div class="content-item-key">{{'本端私网IP：'}}</div>
                            <div class="content-item-value">
                                {{instance.internalIp || '-' }}
                                <s-icon
                                    s-if="{{!instance.internalIp && instance.status !== 'building'}}"
                                    name="bind"
                                    class="blue-icon left_class"
                                    on-click="bindEipInternalIp"
                                />
                                <s-icon
                                    s-else-if="{{instance.internalIp}}"
                                    name="unbind"
                                    class="blue-icon left_class"
                                    on-click="unbindInternalIp"
                                />
                            </div>
                        </div>
                        <div class="content-item" s-if="instance.netType !== 'intranet'">
                            <div class="content-item-key">{{'公网IP/带宽：'}}</div>
                            <div class="content-item-value">
                                {{instance.eip || '-' }}/{{instance.bandwidthInMbps || '-'}}Mbps
                                <s-icon
                                    s-if="{{!instance.eip}}"
                                    name="bind"
                                    class="blue-icon left_class {{bindEip.disable ? 'grey-class' : ''}}"
                                    on-click="bindEip"
                                />
                                <s-icon s-else name="unbind" class="blue-icon left_class" on-click="unbindEip" />
                            </div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'名称：'}}</div>
                            <div class="content-item-value" s-ref="edit-name">
                                <span class="text-hidden"> {{instance.vpnName || '-'}} </span>
                                <edit-popover
                                    s-if="instance.status !== 'building'"
                                    value="{=instance.vpnName=}"
                                    rule="{{Rule.NAME}}"
                                    on-edit="updateName"
                                >
                                    <a href="javascript:void(0)" style="margin-left: 8px">变更</a>
                                </edit-popover>
                            </div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'所在网络：'}}</div>
                            <div class="content-item-value">
                                {{instance.vpcName || '-'}}{{vpcInfo.cidr | filterCidr}}
                            </div>
                        </div>
                        <div s-if="{{urlQuery.vpnType !== 'ssl'}}" class="content-item">
                            <div class="content-item-key">{{'隧道数量：'}}</div>
                            <div class="content-item-value">{{instance.vpnConnNum}}</div>
                        </div>
                        <div s-else class="content-item">
                            <div class="content-item-key">{{'SSL连接数：'}}</div>
                            <div class="content-item-value">
                                {{instance.maxClient}}
                                <a
                                    href="{{maxClientUrl}}"
                                    style="margin-left: 8px"
                                    s-if="{{instance.status === 'active' && instance.maxClient < 1000}}"
                                    >连接数升级</a
                                >
                            </div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'到期时间：'}}</div>
                            <div class="content-item-value">{{instance.expiredTime | filterTime}}</div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'描述：'}}</div>
                            <div class="content-item-value" s-ref="edit-desc">
                                <span class="text-hidden"> {{instance.description || '-'}} </span>
                                <edit-popover
                                    value="{=instance.description=}"
                                    rule="{{Rule.DESC}}"
                                    on-edit="updateDesc"
                                >
                                    <a href="javascript:void(0)" style="margin-left: 8px">变更</a>
                                </edit-popover>
                            </div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'运行状态：'}}</div>
                            <div class="content-item-value {{instance.status | statusStyle}}">
                                {{instance.status | statusText}}
                            </div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'网关规格：'}}</div>
                            <div class="content-item-value">{{instance.flavor | getFlavor}}</div>
                        </div>
                        <div class="content-item" s-if="isShowGatewayType">
                            <div class="content-item-key">{{'网关类型：'}}</div>
                            <div class="content-item-value">{{instance.netType | getNetType}}</div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'释放保护：'}}</div>
                            <div class="content-item-value">
                                <s-switch
                                    disabled="{{deleteProtectStatus}}"
                                    on-change="updateDeleteProtect"
                                    checked="{=instance.deleteProtect=}"
                                />
                                <!--<s-tip
                                class="inline-tip"
                                skin="warning"
                                placement="topRight"
                                content="请确认您已解除相关的关联设备"
                            />-->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@invokeComp('@edit-popover')
@asComponent('@vpn-detail')
class VpnDetail extends Component {
    static filters = {
        statusStyle(status) {
            let config = VpnStatus.fromValue(status);
            return config ? config.styleClass : '';
        },
        statusText(status) {
            let config = VpnStatus.fromValue(status);
            return config ? config.text : '';
        },
        filterTime(value) {
            return value ? utcToTime(value) : '-';
        },
        filterCidr(value) {
            return value ? `(${value})` : '';
        },
        getFlavor(value) {
            if (this.data.get('urlQuery.vpnType') !== 'gre') {
                return vpnFlavor.getTextFromValue(value);
            } else {
                return value === '' ? '小型' : '中型';
            }
        },
        getNetType(value) {
            return value === 'intranet' ? '私网' : '公网';
        }
    };
    static computed = {
        maxClientUrl() {
            let vpcId = this.data.get('urlQuery.vpcId') || '';
            let vpnId = this.data.get('urlQuery.vpnId');
            return `/network/#/vpn/ssl/upgradeClient?vpcId=${vpcId}&vpnId=${vpnId}`;
        },
        isShowGatewayType() {
            const vpnType = this.data.get('urlQuery.vpnType');
            return ['ipsec', 'gre'].includes(vpnType);
        }
    };
    initData() {
        return {
            klass: ['main-wrap vpn-detail-wrap vpn-common-page'],
            instance: {},
            unset: '未配置',
            Rule: Rule.DETAIL_EDIT,
            route: {},
            deleteProtectStatus: false,
            urlQuery: getQueryParams(),
            bindEip: {}
        };
    }

    inited() {
        this.data.set('route', {query: this.data.get('context')});
    }

    attached() {
        this.loadPage();
    }

    loadPage() {
        this.getDetail().then(() => {
            this.getVpcInfo();
        });
    }

    updateName(value) {
        const updatePayload = {
            vpnName: value
        };
        const instance = this.data.get('instance');
        this.$http.vpnUpdate(u.extend({}, instance, updatePayload)).then(() => {
            Notification.success('修改成功');
            this.data.set('instance.vpnName', value);
            this.data.get('context').updateVpnName();
        });
    }

    updateDesc(value) {
        const updatePayload = {
            description: value
        };
        const instance = this.data.get('instance');
        this.$http.vpnUpdate(u.extend({}, instance, updatePayload)).then(() => {
            Notification.success('修改成功');
            this.data.set('instance.description', value);
        });
    }

    bindEip() {
        if (this.data.get('bindEip')?.disable) {
            return;
        }
        const dialog = new EipBindDialog({
            data: {
                instanceType: 'VPN',
                instanceId: this.data.get('instance.vpnId'),
                listRequester: this.$http.getEipBindList.bind(this.$http),
                submitRequester: this.$http.vpnEipBind.bind(this.$http),
                EipSDK: new EipSDK({client: window.$http, context: window.$context})
            }
        });
        dialog.on('cancel', () => {
            dialog.dispose();
        });
        dialog.on('success', () => {
            Notification.success('绑定成功');
            dialog.dispose();
            this.loadPage();
        });
        dialog.on('create', e => {
            e.params = e.query;
            e.paramSeperator = '~';
            redirect(e);
        });
        dialog.attach(document.body);
    }

    unbindEip() {
        const instance = this.data.get('instance');
        const dialog = new Confirm({
            data: {
                title: '解绑EIP',
                content: `VPN网关需要配合EIP才能正常工作。您确认解绑${instance.eip}吗？`
            }
        });
        dialog.on('confirm', () => {
            const payload = {
                eip: instance.eip,
                instanceType: 'VPN',
                instanceId: instance.vpnId
            };
            this.$http.vpnEipUnbind(payload).then(() => this.loadPage());
        });
        dialog.attach(document.body);
    }

    getDetail() {
        const whiteList = window.$storage.get('commonWhite');
        return this.$http
            .getVpnDetail(
                {
                    vpnId: this.data.get('urlQuery.vpnId')
                },
                kXhrOptions.customSilent
            )
            .then(instance => {
                let {bindEip} = checker.check(rules, instance, '', {eipBlackList: whiteList?.eipBlackList});
                this.data.set('instance', instance);
                this.data.set('bindEip', bindEip);
            });
    }
    getVpcInfo() {
        let vpcId = this.data.get('urlQuery.vpcId') || '';
        this.$http.getVpcDetail({vpcIds: [vpcId]}).then(data => {
            let vpc = data[vpcId] || {};
            vpc.name = getVpcName(vpc.name);
            this.data.set('vpcInfo', vpc);
        });
    }
    unbindInternalIp() {
        let item = this.data.get('instance');
        let confirm = new Confirm({
            data: {
                title: '解绑私网IP',
                content: `您确认要解绑私网IP${item.internalIp}吗？`
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http
                .vpnEipUnbind({
                    internalIp: '',
                    instanceType: 'VPN',
                    instanceId: item.vpnId
                })
                .then(() => {
                    this.data.set('instance.internalIp', '');
                });
        });
    }
    bindEipInternalIp() {
        let item = this.data.get('instance');
        let dialog = new InternalIpBindDialog({
            data: {
                instanceType: 'VPN',
                instanceId: item.vpnId,
                subnetId: item.subnetId,
                vpcId: item.vpcId
            }
        });
        dialog.attach(document.body);
        dialog.on('confirm', value => {
            this.data.set('instance.internalIp', value);
        });
    }
    // 更新释放保护状态
    updateDeleteProtect({value}) {
        this.data.set('deleteProtectStatus', true);
        this.$http
            .vpnUpdateDeleteProject({
                vpnId: this.data.get('urlQuery.vpnId'),
                deleteProtect: value
            })
            .then(() => {
                this.data.set('deleteProtectStatus', false);
                this.data.set('instance.deleteProtect', value);
            })
            .catch(() => {
                this.data.set('deleteProtectStatus', false);
                this.data.set('instance.deleteProtect', !value);
            });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(VpnDetail));
