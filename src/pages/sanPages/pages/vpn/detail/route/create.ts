import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {Notification} from '@baidu/sui';

import {validateRules} from './helper';
import rule from '@/pages/sanPages/utils/rule';
import './style.less';

const {asComponent, invokeSUI, invokeSUIBIZ, template} = decorators;

const tpl = html`
    <div>
        <s-dialog class="vpn-route-create" open="{{true}}" title="{{title}}">
            <s-form s-ref="form" data="{=formData=}" rules="{{rules}}" label-align="left">
                <s-form-item label="目标网段：" prop="cidr">
                    <s-input
                        value="{=formData.cidr=}"
                        placeholder="{{'示例：***********/16'}}"
                        on-input="networksInput($event, index)"
                    />
                    <p class="s-form-item-error">{{ networksInfo}}</p>
                </s-form-item>
                <s-form-item label="目标网段：" s-else prop="cidr">
                    <s-input value="{=formData.cidr=}" on-input="ipv6ListInput($event, index)" />
                    <span class="s-form-item-error">{{ networksInfo}}</span>
                </s-form-item>
                <s-form-item label="As-Path：" prop="asPath">
                    <s-input value="{=formData.asPath=}" />
                </s-form-item>
                <s-form-item label="Community：" prop="communities">
                    <s-input value="{=formData.communities=}" />
                </s-form-item>
            </s-form>
            <div slot="footer">
                <s-button on-click="onClose">取消</s-button>
                <s-button skin="primary" disabled="{{disableSub}}" on-click="doSubmit">确定</s-button>
            </div>
        </s-dialog>
    </div>
`;

@asComponent('@create-vpn-route')
@template(tpl)
@invokeSUI
@invokeSUIBIZ
class channelRouteCreate extends Component {
    initData() {
        return {
            title: '新建路由条目',
            formData: {
                cidr: '',
                asPath: null,
                communities: null
            },
            disableSub: false,
            rules: validateRules,
            nexthopTypeList: [{text: 'VPN', value: 'vpn'}],
            networksInfo: ''
        };
    }
    inputValid(r, value) {
        if (r === undefined) {
            return '';
        }

        let result = '';
        if (r.required) {
            value === '' && (result = r.requiredErrorMessage);
        }
        if (r.pattern) {
            !r.pattern.test(value) && (result = '目标网段不符合规则');
        }
        const dataSource = this.data.get('dataSource');
        if (dataSource.length) {
            const existItem = dataSource.find(item => item.cidr === value || item.cidr === value + '/32');
            if (existItem) {
                result = '该目标网段已存在';
            }
        }
        if (value.startsWith('0.0.0.0')) {
            result = '目标网段不能为0.0.0.0/0';
        }
        if (value.startsWith('**********')) {
            const splitStr = value.split('/');
            if (splitStr[1] && splitStr[1] <= 10) {
                result = '目标网段不能为**********/10下的子网段或者包含**********/10';
            }
        }
        return result;
    }

    networksInput({value}) {
        const result = this.inputValid(rule.CHANNEL.NETWORK, value);
        this.data.set('networksInfo', result);
    }

    ipv6ListInput({value}, index) {
        if (!value) {
            this.data.set(`networksInfo[${index}]`, '请输入IPV6参数');
        } else if (!rule.IPV6_SEG.test(value)) {
            this.data.set(`networksInfo[${index}]`, '参数格式不合法');
        } else {
            this.data.set(`networksInfo[${index}]`, '');
        }
    }
    onClose() {
        this.dispose();
    }

    cidrCheck() {
        const {cidr} = this.data.get('formData');
        this.networksInput({value: cidr});
    }

    async doSubmit() {
        const form = this.ref('form');
        await form.validateFields();
        this.cidrCheck();
        if (this.data.get('networksInfo') !== '') {
            return;
        }
        this.data.set('disableSub', true);
        let payload = u.cloneDeep(this.data.get('formData'));
        payload.vpnId = this.data.get('vpnId');
        if (!payload.cidr.includes('/')) {
            payload.cidr = payload.cidr + '/32';
        }
        if (!payload.asPath) {
            delete payload.asPath;
        } else {
            payload.asPath = payload.asPath.replace(/\，/g, ',');
        }
        if (!payload.communities) {
            delete payload.communities;
        } else {
            payload.communities = payload.communities.replace(/\，/g, ',').replace(/\：/g, ':');
        }
        this.$http
            .createBgpRoute(payload)
            .then(() => {
                this.fire('success');
                Notification.success('创建成功', {placement: 'topRight'});
                this.onClose();
            })
            .finally(() => {
                this.data.set('disableSub', false);
            });
    }
}
export default Processor.autowireUnCheckCmpt(channelRouteCreate);
