import {removeStrAllSpace} from '@/pages/sanPages/utils/helper';

export const validateRules = {
    cidr: [{required: true, message: '目标网段必填'}],
    asPath: [
        {required: false},
        {
            validator(rule, value, callback) {
                if (value) {
                    const str = removeStrAllSpace(value);
                    const splitStr = str.split(/\,|，/g);
                    if (splitStr.length > 5) {
                        return callback('最多只能输入5个数字');
                    }
                    if (splitStr.some(item => (item ? Number.isNaN(+item) : true))) {
                        return callback('只能输入数字类型');
                    }
                    if (splitStr.some(item => (item !== '' ? item <= 0 : true))) {
                        return callback('只能输入大于0的数字');
                    }
                }
                return callback();
            }
        }
    ],
    communities: [
        {required: false},
        {
            validator(rule, value, callback) {
                if (value) {
                    const str = removeStrAllSpace(value);
                    const splitStr = str.split(/\,|，/g);
                    if (splitStr.length > 5) {
                        return callback('最多只能输入5个键值对');
                    }
                    const commonValidate = (type: 'number' | 'lessThan0') => {
                        let validateMessage = '';
                        splitStr.some(item => {
                            const splitItem = item.split(/\:|：/);
                            const filterSplitedItem = splitItem.filter(Boolean);
                            if (filterSplitedItem?.length !== 2) {
                                validateMessage = '只能输入键值对';
                            } else if (filterSplitedItem.some(it => it.includes('.'))) {
                                validateMessage = type === 'number' && '只能输入整数';
                            } else if (
                                splitItem.some(it =>
                                    type === 'number'
                                        ? it
                                            ? Number.isNaN(+it)
                                            : true
                                        : it !== ''
                                          ? it < 0 || it > 65535
                                          : true
                                )
                            ) {
                                validateMessage = type === 'number' ? '只能输入数字类型' : '只能输入0-65535之间的整数';
                            }
                        });
                        return validateMessage;
                    };
                    const validateNumberMessage = commonValidate('number');
                    if (validateNumberMessage) {
                        return callback(validateNumberMessage);
                    }
                    const validateLessThan0Message = commonValidate('lessThan0');
                    if (validateLessThan0Message) {
                        return callback(validateLessThan0Message);
                    }
                }
                return callback();
            }
        }
    ]
};
