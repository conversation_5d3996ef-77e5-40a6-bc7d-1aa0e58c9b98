import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {Notification} from '@baidu/sui';
import {OutlinedEditingSquare, OutlinedPlus} from '@baidu/sui-icon';

import {ChannelRouteType} from '@/pages/sanPages/common/enum';
import Confirm from '@/pages/sanPages/components/confirm';
import {$flag as FLAG, kXhrOptions} from '@/pages/sanPages/utils/helper';
import CreateRoute from './create';
import rules from './rules';
import './style.less';
import user from '../userManage/user';

const {asComponent, invokeSUI, invokeAppComp, invokeSUIBIZ, template, invokeComp} = decorators;
const quotaTip = FLAG.NetworkSupportXS
    ? 'BGP路由已达上限'
    : 'BGP路由已达上限，' +
      `<a href="/quota_center/#/quota/apply/create?serviceType=VPC` +
      `&region=${window?.$context?.getCurrentRegionId()}` +
      '&cloudCenterQuotaName=vpnBgpRouteLimit" target="_blank">' +
      '去申请配额</a>';

const tpl = html`
    <div>
        <div class="vpn-route-title">{{title}}</div>
        <s-app-list-page class="{{klass}}">
            <div slot="bulk" s-if="routeType === 'bgp'">
                <s-tip-button
                    disabled="{{createRoute.disable || isQuotaLimit}}"
                    skin="primary"
                    isDisabledVisibile="{{true}}"
                    on-click="onCreate"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{(createRoute.message || (isQuotaLimit ? quotaTip : '')) | raw }}
                    </div>
                    <outlined-plus />
                    {{ '新建路由条目'}}
                </s-tip-button>
                <s-tooltip class="left_class" trigger="{{deleteRoute.disable ? 'hover' : ''}}" placement="right">
                    <!--bca-disable-next-line-->
                    <div slot="content">{{deleteRoute.message | raw}}</div>
                    <s-button on-click="onDelete" disabled="{{deleteRoute.disable}}"> 删除</s-button>
                </s-tooltip>
            </div>
            <div slot="filter">
                <div class="dc-buttons-wrap">
                    <search-type s-ref="search" searchbox="{=searchbox=}" on-search="onSearch"> </search-type>
                </div>
            </div>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                on-selected-change="tableSelected($event)"
                selection="{{routeType === 'bgp' ? table.selection : {mode: null, selectedIndex: []}}}"
            >
                <div slot="c-ipVersion">
                    <span>{{row.ipVersion | ipType}}</span>
                </div>
                <div slot="c-nexthopType">
                    <span>{{row.nexthopType | nextType}}</span>
                </div>
                <div slot="c-nextHop">
                    <!--bca-disable-next-line-->
                    <span>{{row | handleNexthopVpc | raw}}</span>
                </div>
                <div slot="c-asPath">
                    <span>{{row.asPath || '-'}}</span>
                </div>
                <div slot="c-opt" s-if="routeType === 'bgp' && row.direction === 'in'">
                    <s-button style="padding: 0" skin="stringfy" on-click="handleDelete(row)">删除</s-button>
                </div>
            </s-table>
        </s-app-list-page>
    </div>
`;

@template(tpl)
@invokeComp('@create-channel-route', '@search-type', '@create-vpn-route')
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@asComponent('@vpn-route-list')
class ChannelRouteList extends Component {
    static components = {
        'outlined-editing-square': OutlinedEditingSquare,
        'outlined-plus': OutlinedPlus
    };

    initData() {
        return {
            klass: 'vpn-route-list',
            title: '',
            table: {
                loading: false,
                datasource: [],
                columns: [
                    {name: 'ipVersion', label: '规则类型', width: '160'},
                    {name: 'srcCidr', label: '源网段', width: '160'},
                    {name: 'dstCidr', label: '目标网段', width: '160'},
                    {name: 'cidr', label: '目标网段', width: '160'},
                    {name: 'nextHop', label: '下一跳实例ID', width: '200'},
                    {name: 'asPath', label: 'As-Path', width: '160'},
                    {name: 'communities', label: 'Community', width: '160'},
                    {name: 'opt', label: '操作', width: '160'}
                ],
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                }
            },
            selectedItems: [],
            searchbox: {
                placeholder: '请输入目标网段进行搜索',
                keyword: '',
                keywordType: 'cidr',
                keywordTypes: [{value: 'cidr', text: '目标网段'}]
            },
            createRoute: {
                disable: false,
                message: ''
            },
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            quotaTip,
            editInstance: {},
            routeType: '',
            userRouteNum: 0, // 用户已创建的路由数量
            isInit: true,
            urlQuery: getQueryParams()
        };
    }

    static computed = {
        // 用户手动添加
        isQuotaLimit() {
            const userRouteNum = this.data.get('userRouteNum');
            const vpnBgpRouteLimitNum = this.data.get('vpnBgpRouteLimitNum');
            let flag = false;
            if (userRouteNum >= vpnBgpRouteLimitNum) {
                flag = true;
            }
            return flag;
        }
    };

    static filters = {
        ipType(value) {
            return 'IPv4';
        },
        nextType(value) {
            return ChannelRouteType.getTextFromValue(value);
        },
        handleNexthopVpc(row) {
            const {nextHopType} = row;
            const vpnInfo = this.data.get('context').instance || {};
            const {vpcShortId} = vpnInfo || {};
            if (nextHopType === 'vpc') {
                return `<a target="_blank" href="/network/#/vpc/instance/list?id=${vpcShortId}">${vpcShortId}</a>`;
            }
            return row.nextHop;
        }
    };
    inited() {
        this.loadQuota();
    }

    async attached() {
        await this.queryVpnDetail();
        this.loadPage();
    }
    async queryVpnDetail() {
        try {
            const res = await this.$http.getVpnDetail(
                {
                    vpnId: this.data.get('urlQuery.vpnId')
                },
                kXhrOptions.customSilent
            );
            if (res?.enableBgp) {
                this.data.set('routeType', 'bgp');
            }
        } catch (error) {}
    }
    loadQuota() {
        this.$http.commonQuota({quotaType: 'vpnBgpRouteLimit', serviceType: 'VPC'}).then(res => {
            this.data.set('vpnBgpRouteLimitNum', res);
        });
    }
    resetTable() {
        this.data.set('table.selection', {
            mode: 'multi',
            selectedIndex: []
        });
        this.data.set('selectedItems', []);
    }

    // 动态设置策略路由、目的路由、BGP路由
    setRoutesConfig(data: Record<string, any>) {
        let title = this.data.get('title');
        let columns = this.data.get('table.columns');
        let routeType = this.data.get('routeType');
        const keywordTypes = this.data.get('searchbox.keywordTypes');
        let datasource = [];
        const routes = data?.vpnRoute;
        const policyRoutes = routes?.policyRoutes || [];
        const staticRoutes = routes?.staticRoutes || [];
        const bgpRoutes = routes?.bgpRoutes || [];
        // vpn开启了BGP默认为bgp路由表
        if (!!bgpRoutes.length || routeType === 'bgp') {
            title = 'BGP路由表';
            routeType = 'bgp';
            datasource = bgpRoutes;
            if (!keywordTypes.some(item => ['asPath', 'communities'].includes(item.value))) {
                this.data.push('searchbox.keywordTypes', {value: 'asPath', text: 'As-Path'});
                this.data.push('searchbox.keywordTypes', {value: 'communities', text: 'Community'});
            }
            columns = columns.filter(item => !['srcCidr', 'dstCidr', 'origin', 'nexthopType'].includes(item.name));
        } else if (!!policyRoutes.length) {
            title = '策略路由表';
            routeType = 'strategy';
            datasource = policyRoutes;
            columns = columns.filter(item => !['cidr', 'asPath', 'communities', 'opt'].includes(item.name));
            if (!keywordTypes.some(item => ['srcCidr', 'dstCidr'].includes(item.value))) {
                this.data.set('searchbox.keywordType', 'srcCidr');
                this.data.splice('searchbox.keywordTypes', [0, 1]);
                this.data.push('searchbox.keywordTypes', {value: 'srcCidr', text: '源网段'});
                this.data.push('searchbox.keywordTypes', {value: 'dstCidr', text: '目标网段'});
            }
        } else if (!!staticRoutes.length) {
            title = '目的路由表';
            routeType = 'target';
            datasource = staticRoutes;
            columns = columns.filter(
                item => !['srcCidr', 'dstCidr', 'origin', 'asPath', 'communities', 'opt'].includes(item.name)
            );
        }
        this.data.set('title', title);
        this.data.set('routeType', routeType);
        this.data.set('table.columns', columns);
        this.data.set('table.datasource', datasource);
        const isInit = this.data.get('isInit');
        if (isInit) {
            const userRouteNum = datasource.filter(item => item.direction !== 'received')?.length || 0;
            this.data.set('userRouteNum', userRouteNum);
            this.data.set('isInit', false);
        }
    }

    handleJumpVpc(nextHop: string) {
        window.open(`/network/#/vpc/instance/detail?vpcId=${nextHop}`);
    }

    handleDelete(row: any) {
        const {id} = row;
        this.onDelete(null, id);
    }

    loadPage(payload?: Record<string, any>) {
        this.resetTable();
        this.data.set('table.loading', true);
        const vpnId = this.data.get('context').vpnId;
        return this.$http.getVpnRouteList({vpnId, ...payload}).then(data => {
            this.data.set('table.loading', false);
            this.setRoutesConfig(data);
        });
    }

    onSearch() {
        this.handleLoadWithParam();
    }

    handleLoadWithParam() {
        const {keyword, keywordType} = this.ref('search') ? this.ref('search').getSearchCriteria() : {};
        const payload = {};
        payload[keywordType] = keyword;
        this.loadPage(payload);
    }

    onDelete(e: Event, bgpRouteId?: string) {
        const confirm = new Confirm({
            data: {
                open: true,
                content: bgpRouteId ? '确认删除吗？' : '确认删除选中的路由条目？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            let routeId = bgpRouteId || this.data.get('selectedItems')[0].id;
            const vpnId = this.data.get('context').vpnId;
            this.$http.deleteBgpRoute({vpnId, routeId}).then(() => {
                const userRouteNum = this.data.get('userRouteNum');
                this.data.set('userRouteNum', userRouteNum - 1);
                this.handleLoadWithParam();
                Notification.success('删除成功', {placement: 'topRight'});
            });
        });
    }

    tableSelected({value}) {
        const {deleteRoute} = checker.check(rules, value.selectedItems, 'deleteRoute');
        this.data.set('deleteRoute', deleteRoute);
        this.data.set('selectedItems', value.selectedItems);
    }

    onPagerChange({value}) {
        this.data.set('pager.page', value.page);
        this.data.set('pager.pageSize', value.pageSize);
        this.loadPage();
    }

    onCreate() {
        const vpnId = this.data.get('context').vpnId;
        const dataSource = this.data.get('table.datasource');
        const dialog = new CreateRoute({
            data: {
                vpnId,
                dataSource
            }
        });
        dialog.on('success', () => {
            const userRouteNum = this.data.get('userRouteNum');
            this.data.set('userRouteNum', userRouteNum + 1);
            this.handleLoadWithParam();
        });
        dialog.attach(document.body);
    }
}
export default San2React(Processor.autowireUnCheckCmpt(ChannelRouteList));
