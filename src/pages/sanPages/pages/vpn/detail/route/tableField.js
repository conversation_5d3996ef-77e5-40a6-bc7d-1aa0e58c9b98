import u from 'lodash';
export const natFields = [
    {
        name: 'internalIp',
        label: '原IP',
        render(item) {
            return u.escape(item.internalIp).replace('/32', '');
        }
    },
    {
        name: 'externalIp',
        label: '映射IP',
        render(item) {
            return u.escape(item.externalIp).replace('/32', '');
        }
    },
    {
        name: 'description',
        label: '描述'
    },
    {
        name: 'opt',
        label: '操作'
    }
];
export const idcnatFields = [
    {
        name: 'internalIp',
        label: '原IP',
        render(item) {
            return u.escape(item.internalIp).replace('/32', '');
        }
    },
    {
        name: 'externalIp',
        label: '映射IP',
        render(item) {
            return u.escape(item.externalIp).replace('/32', '');
        }
    },
    {
        name: 'description',
        label: '描述'
    },
    {
        name: 'opt',
        label: '操作'
    }
];
export const idcdnatFields = [
    {
        name: 'protocol',
        label: '协议',
        render(item) {
            return u.escape(item.protocol).toUpperCase();
        }
    },
    {
        name: 'internalIp',
        label: '原IP端口',
        render(item) {
            return item.internalIp + ':' + item.internalPort;
        }
    },
    {
        name: 'externalIp',
        label: '映射IP端口',
        render(item) {
            return item.externalIp + ':' + item.externalPort;
        }
    },
    {
        name: 'sourceCidr',
        label: '源IP',
        render(item) {
            return item.sourceCidr || '-';
        }
    },
    {
        name: 'description',
        label: '描述'
    },
    {
        name: 'opt',
        label: '操作'
    }
];
export const vpcdnatFields = [
    {
        name: 'protocol',
        label: '协议',
        render(item) {
            return u.escape(item.protocol).toUpperCase();
        }
    },
    {
        name: 'internalIp',
        label: '原IP端口',
        render(item) {
            return item.internalIp + ':' + item.internalPort;
        }
    },
    {
        name: 'externalIp',
        label: '映射IP端口',
        render(item) {
            return item.externalIp + ':' + item.externalPort;
        }
    },
    {
        name: 'sourceCidr',
        label: '源IP',
        render(item) {
            return item.sourceCidr || '-';
        }
    },
    {
        name: 'description',
        label: '描述'
    },
    {
        name: 'opt',
        label: '操作'
    }
];
export const tableColumns = {
    nat: natFields,
    idcnat: idcnatFields,
    idcdnat: idcdnatFields,
    vpcdnat: vpcdnatFields
};
