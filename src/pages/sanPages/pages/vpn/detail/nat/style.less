.vpn-nat-list-wrap {
    padding: 24px;
    .s-biz-page-header {
        border-bottom: none !important;
        margin: 0 !important;
        display: inline !important;
        .title {
            color: #151b26!important;
            line-height: 24px!important;
            font-weight: 500!important;
            font-size: 16px!important;
        }
    }
    .s-biz-page-title {
        h2 {
            display: block !important;
            height: 24px !important;
            color: #151b26 !important;
            line-height: 24px !important;
            font-weight: 500 !important;
            margin-bottom: 16px !important;
            font-size: 16px !important;
        }
    }
    .s-biz-page-content {
        margin: 0 !important;
    }
    .icon-faq {
        color: #ccc;
    }
    .inline-tip {
        top: 1px;
        margin-left: 8px;
        position: relative;
        .s-tip-warning {
            justify-content: center;
            .warning_class {
                fill: #999;
            }
        }
    }
    .list-page-tb-left-toolbar {
        display: inline-flex;
    }
    .right_bar {
        display: inline-flex;
    }
    .nat-list-footer {
        display: flex;
        align-items: center;
        padding: 20px 0;
        .s-pagination {
            margin-left: auto;
        }
    }
    .text-hidden {
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        max-width: 100%;
        text-overflow: ellipsis;
    }
    .s-biz-page-footer {
        margin-top: 16px 0 0 !important;
        padding: 0px !important;
    }
    .s-table .s-table-body{
        max-height: calc(~'100vh - 340px');
    }
}
.vpn-create-wrap {
    .s-form-item-label>label {
        display: inline-block;
        min-width: 80px;
        margin-left: 5px;
    }
    .require-label-wrap {
        .s-form-item-label > label {
            &:before {
                content: "*";
                position: absolute;
                left: -5px;
                color: #f33e3e;;
            }
        }
    }
    .s-input-area input {
        box-sizing: content-box;
    }
}

.vpn-nat-create {
    .s-form {
        .s-form-item-label {
            width: 92px !important;
        }
    }
}