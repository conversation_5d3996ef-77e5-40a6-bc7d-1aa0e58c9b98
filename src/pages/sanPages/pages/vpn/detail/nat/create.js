/*
 * @description: vpnnat创建
 * @file: vpn/v2/nat/create.js
 * @author: <EMAIL>
 */
import u from 'lodash';
import {defineComponent} from 'san';
import {Form, Input, Dialog, Select, Button} from '@baidu/sui';
import {html} from '@baiducloud/runtime';
import {checkIpInCidr, checkIsInSubnet} from '@/pages/sanPages/utils/common';
import rule from '@/pages/sanPages/utils/rule';

const {VPN_IP_CIDR, IP_CIDR} = rule;
/* eslint-disable */
const VPN_IP = '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([1-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-4])?$';
/* eslint-disable */
const template = html`
<template>
<s-dialog
    closeAfterMaskClick="{{false}}"
    open="{{true}}"
    class="vpn-create-wrap vpn-nat-create"
    title="{{title}}">
    <s-form s-ref="form"
        data="{=formData=}"
        rules="{{rules}}"
        label-align="left"
        class="detail-form">
        <s-form-item class="require-label-wrap" s-if="isDnatType" prop="protocol" label="协议：">
            <s-select
                datasource="{{protocolList}}"
                width="{{220}}"
                value="{=formData.protocol=}">
            </s-select>
        </s-form-item>
        <s-form-item class="require-label-wrap" label="{{isDnatType ? '原IP端口：' : '原IP：'}}">
            <s-input width="{{240}}" value="{=formData.internalIp=}" placeholder="{{isDnatType ? '请输入原IP端口' : '请输入原IP'}}"></s-input>
            <span s-if="isDnatType">:</span>
            <s-input width="{{50}}" s-if="isDnatType" value="{=formData.internalPort=}" placeholder="1-65535"></s-input>
            <p style="color:#f33e3e;">{{internalIpErr}}</p>
        </s-form-item>
        <s-form-item class="require-label-wrap" label="{{isDnatType ? '映射IP端口：' : '映射IP：' }}">
            <s-input width="{{240}}" value="{=formData.externalIp=}" placeholder="{{isDnatType ? '请输入映射IP端口' : '请输入映射IP'}}"></s-input>
            <span s-if="isDnatType">:</span>
            <s-input width="{{50}}" s-if="isDnatType" value="{=formData.externalPort=}" placeholder="1-65535"></s-input>
            <p style="color:#f33e3e;">{{externalIpErr}}</p>
        </s-form-item>
        <s-form-item s-if="isDnatType" prop="sourceCidr" label="源IP：" help="{{sourceCidrHelp}}">
            <s-input width="{{340}}" value="{=formData.sourceCidr=}" placeholder="IP地址或网段"></s-input>
            <p style="color:#f33e3e;">{{sourceCidrErr}}</p>
        </s-form-item>
        <s-form-item prop="description" label="描述：">
            <s-textarea
                width="{{340}}"
                height="{{60}}"
                value="{=formData.description=}"
                maxLength="200"
                placeholder="支持中英文，数字、下划线或连接符，最多200个字符">
            </s-textarea>
        </s-form-item>
    </s-form>
    <div slot="footer">
        <span style="color:#f33e3e;">{{VpnDnatErr}}</span>
        <s-button on-click="close">取消</s-button>
        <s-button skin="primary" disabled="{{disableSub}}" on-click="dialogConfirm">确定</s-button>
    </div>
</s-dialog>
</template>
`;
/* eslint-enable */
export default defineComponent({
    template,
    components: {
        's-form': Form,
        's-form-item': Form.Item,
        's-input': Input,
        's-textarea': Input.TextArea,
        's-dialog': Dialog,
        's-select': Select,
        's-button': Button
    },
    computed: {
        isDnatType() {
            let type = this.data.get('type');
            return type === 'VPN_IDC_DNAT' || type === 'VPN_VPC_DNAT';
        },
        title() {
            let type = this.data.get('type');
            let instance = this.data.get('instance');
            if (type === 'VPN_VPC_STATIC_NAT') {
                return instance ? '编辑云端静态NAT规则' : '添加云端静态NAT规则';
            } else if (type === 'VPN_IDC_DNAT') {
                return instance ? '编辑云端IP端口静态NAT规则' : '添加云端IP端口静态NAT规则';
            } else if (type === 'VPN_IDC_STATIC_NAT') {
                return instance ? '编辑IDC端静态NAT规则' : '添加IDC端静态NAT规则';
            } else if (type === 'VPN_VPC_DNAT') {
                return instance ? '编辑云端DNAT规则' : '添加云端DNAT规则';
            }
        }
    },
    initData() {
        return {
            title: '添加云端静态NAT规则',
            formData: {
                internalIp: '',
                externalIp: '',
                description: '',
                internalPort: '',
                externalPort: '',
                protocol: 'tcp'
            },
            protocolList: [
                {text: 'TCP', value: 'tcp'},
                {text: 'UDP', value: 'udp'},
            ],
            rules: {
                sourceCidr: [
                    {
                        validator: (rule, value, callback) => {
                            value = value ? value : this.data.get('formData.sourceCidr');
                            let firstIp = value && value.split('.')[0];
                            let valueString = convertCidrToBinary(value);
                            let valueMask = value.split('/')[1] || 32;
                            if (!value) {
                                return callback();
                            }
                            if (firstIp === '0' || +firstIp >= 224) {
                                return callback('IP范围不符合规则');
                            }
                            if (valueString.substring(+valueMask, valueString.length).indexOf('1') > -1) {
                                return callback('IP范围不符合规则');
                            }
                            if (!new RegExp(IP_CIDR).test(value)) {
                                return callback('源IP格式错误');
                            }
                            callback();
                        }
                    }
                ],
                description: [
                    {min: 0, max: 200, message: '长度0到200个字符'},
                    {
                        validator(rule, value, callback) {
                            let pattern = /^[\u4e00-\u9fa5\w\-\_]{0,200}$/;
                            if (!pattern.test(value)) {
                                return callback('支持中英文，数字、下划线或连接符，最多200个字符');
                            }
                            callback();
                        }
                    }
                ]
            },
            vpcInfo: null,
            internalIpErr: '',
            externalIpErr: '',
            sourceCidrErr: '',
            VpnDnatErr: '',
            disableSub: false,
            sourceCidrHelp: '不指定源IP时，默认IDC端全部源IP'
        };
    },
    attached() {
        if (this.data.get('instance')) {
            let instance = {...this.data.get('instance')};
            instance.internalIp = instance.internalIp.split('.');
            instance.internalIp[3] = instance.internalIp[3].replace('/32', '');
            instance.internalIp = instance.internalIp.join('.');
            instance.externalIp = instance.externalIp.split('.');
            instance.externalIp[3] = instance.externalIp[3].replace('/32', '');
            instance.externalIp = instance.externalIp.join('.');
            this.data.set('formData', instance);
        }
        if (this.data.get('isDnatType')) {
            let type = this.data.get('type');
            let tip = type === 'VPN_IDC_DNAT'
            ? '不指定源IP时，默认IDC端全部源IP' : '不指定源IP时，默认云端全部源IP';
            this.data.set('sourceCidrHelp', tip);
        }
        this.loadVpcInfo();
    },
    loadVpcInfo() {
        let vpcId = this.data.get('vpcId');
        this.$http.vpcInfo({vpcIds: [vpcId]})
            .then(data => {
                let vpc = null;
                vpc = data[vpcId];
                this.data.set('vpcInfo', vpc);
            });
    },
    checkInternalIp() {
        let internalIp = this.data.get('formData.internalIp');
        if (!new RegExp(VPN_IP).test(internalIp)) {
            this.data.set('internalIpErr', 'IP范围不符合规则');
            return false;
        }
        return true;
    },
    checkExternalIp() {
        let vpcInfo = this.data.get('vpcInfo');
        let externalIp = this.data.get('formData.externalIp');
        if (!new RegExp(VPN_IP).test(externalIp)) {
            this.data.set('externalIpErr', 'IP范围不符合规则');
            return false;
        } else {
            this.data.set('externalIpErr', '映射IP不可以在VPN网关所在私有网络CIDR范围');
            if (this.data.get('isDnatType')) {
                this.data.set('externalIpErr', '映射IP端口不可以在私有网络CIDR范围之内');
            }
            if (vpcInfo) {
                if (checkIpInCidr(vpcInfo.cidr, externalIp)) {
                    return false;
                }
                if (vpcInfo.auxiliaryCidr && vpcInfo.auxiliaryCidr.length) {
                    let flag = false;
                    vpcInfo.auxiliaryCidr.forEach(item => {
                        if (checkIpInCidr(item, externalIp)) {
                            flag = true;
                        }
                    });
                    if (flag) {
                        return false;
                    }
                }
                this.data.set('externalIpErr', '');
                this.data.set('VpnDnatErr', '');
            }
        }
        return true;
    },
    checkPort(port) {
        return /(^[1-9][0-9]{0,4}$)/.test(port) && port <= 65535;
    },
    checkDnatPort() {
        let hasError = false;
        let configMap = ['internalIpErr', 'externalIpErr'];
        let port = [this.data.get('formData.internalPort'), this.data.get('formData.externalPort')];
        port.map((item, index) => {
            if (!this.checkPort(item)) {
                this.data.set(configMap[index], '端口号格式错误');
                hasError = true;
            }
        });
        return hasError;
    },
    // 原IP、映射IP与源IP必须符合IP范围，不支持E,D类与回环地址
    checkIpRange() {
        let internalIp = this.data.get('formData.internalIp').split('.')[0];
        let externalIp = this.data.get('formData.externalIp').split('.')[0];
        let ipMap = {
            externalIp,
            internalIp
        };
        let hasValid = true;
        u.forOwn(ipMap, (value, key) => {
            if (value === '0' || +value >= 224) {
                this.data.set(`${key}Err`, 'IP范围不符合规则');
                hasValid = false;
            }
        });
        return hasValid;
    },
    // 原IP与映射IP不能重复
    checkIpRepeat(checkSubnet, payload) {
        const instance = this.data.get('instance');
        let internalIpSource = checkSubnet[0].result || [];
        let externalIpSource = checkSubnet[1].result || [];
        let hasError = false;
        if (instance) {
            internalIpSource = internalIpSource.filter(item => item.ruleId !== instance.ruleId);
            externalIpSource = externalIpSource.filter(item => item.ruleId !== instance.ruleId);
        }
        if (internalIpSource.find(item => item.internalIp.replace('/32', '') === payload.internalIp)) {
            this.data.set('internalIpErr', '原IP不可以重复，请重新填写');
            hasError = true;
        }
        if (externalIpSource.find(item => item.externalIp.replace('/32', '') === payload.externalIp)) {
            this.data.set('externalIpErr', '映射IP不可以重复，请重新填写');
            hasError = true;
        }
        return hasError;
    },
    // 源IP地址是否重叠
    checkSourceCidrRepeat(checkSubnet, payload) {
        const instance = this.data.get('instance');
        let ruleId = instance ? instance.ruleId : '';
        let externalIpSource = checkSubnet[1].result || [];
        let vpcInfo = this.data.get('vpcInfo');
        let hasError = false;
        let isVpcType = this.data.get('type') === 'VPN_VPC_DNAT';
        if (vpcInfo && isVpcType && payload.sourceCidr) {
            let payloadCidrIp = payload.sourceCidr.split('/')[0];
            let flag = checkIpInCidr(vpcInfo.cidr, payloadCidrIp) || vpcInfo.auxiliaryCidr
                && vpcInfo.auxiliaryCidr.some(item => checkIpInCidr(item, payloadCidrIp));
            if (!flag) {
                this.data.set('VpnDnatErr', '源IP必须在私有网络CIDR范围之内');
                return true;
            }
        }
        // 过滤出协议与映射IP端口相同的数据
        externalIpSource = externalIpSource.filter(item => item.externalIp === payload.externalIp
            && +item.externalPort === +payload.externalPort && item.protocol === payload.protocol
            && item.ruleId !== ruleId);
        if (externalIpSource.length === 0) {
            return false;
        }
        // 源IP为空时，只能存在一条数据
        if (externalIpSource.find(item => !item.sourceCidr) || !payload.sourceCidr) {
            this.data.set('VpnDnatErr', '协议+映射IP端口+源IP不可以重叠，请重新输入');
            return true;
        }
        hasError = externalIpSource.some(item => {
            if (payload.sourceCidr.indexOf('/') > -1 || item.sourceCidr.indexOf('/') > -1) {
                let payloadCidr = this.parseToCidr(payload.sourceCidr);
                let listCidr = this.parseToCidr(item.sourceCidr);
                return checkIsInSubnet(payloadCidr, listCidr) || checkIsInSubnet(listCidr, payloadCidr);
            }
            return item.sourceCidr === payload.sourceCidr;
        });
        if (hasError) {
            this.data.set('VpnDnatErr', '协议+映射IP端口+源IP不可以重叠，请重新输入');
        }
        return hasError;
    },
    parseToCidr(ip) {
        return ip + (/\//g.test(ip) ? '' : '/32');
    },
    async checkTypeRepeat() {
        const formData = this.data.get('formData');
        const payload = this.getPayload();
        const isDnatType = this.data.get('isDnatType');
        const keyMap = {
            internalIpKey: isDnatType ? 'internalIpPort' : 'internalIp',
            externalIpKey: isDnatType ? 'externalIpPort' : 'externalIp',
            internalIpValue: !isDnatType ? formData.internalIp
            : formData.internalIp + ':' + formData.internalPort,
            externalIpVlaue: !isDnatType ? formData.externalIp
            : formData.externalIp + ':' + formData.externalPort
        };
        let internalPayload = this.getPayload({
            keywordType: keyMap.internalIpKey,
            keyword: keyMap.internalIpValue
        });
        let externalPayload = this.getPayload({
            keywordType: keyMap.externalIpKey,
            keyword: keyMap.externalIpVlaue
        });
        let checkSubnet = await Promise.all([
            this.$http.getVpnRuleList(internalPayload),
            this.$http.getVpnRuleList(externalPayload)
        ]);
        let hasError = isDnatType ? this.checkSourceCidrRepeat(checkSubnet, payload)
            : this.checkIpRepeat(checkSubnet, payload);
        return hasError ? Promise.reject() : Promise.resolve();
    },
    updateVpnNatRule(payload) {
        payload.ruleId = this.data.get('formData.ruleId');
        this.$http.updateVpnNatRule(payload).then(() => {
            this.fire('update');
        }).catch(() => {
            this.data.set('disableSub', false);
        });
    },
    createVpnNatRule(payload) {
        this.$http.createVpnNatRule(payload)
        .then(() => this.fire('create'))
        .catch(() => this.data.set('disableSub', false));
    },
    getPayload(payload = '') {
        let defaultPayload = {
            vpcId: this.data.get('vpcId'),
            vpnId: this.data.get('vpnId'),
            internalIp: this.data.get('formData.internalIp'),
            externalIp: this.data.get('formData.externalIp'),
            type: this.data.get('type'),
            description: this.data.get('formData.description')
        };
        let serachPayload = {
            vpnId: this.data.get('vpnId'),
            type: this.data.get('type'),
            pageNo: 1,
            pageSize: 10
        };
        if (this.data.get('isDnatType')) {
            defaultPayload.internalPort = this.data.get('formData.internalPort');
            defaultPayload.externalPort = this.data.get('formData.externalPort');
            defaultPayload.protocol = this.data.get('formData.protocol');
            defaultPayload.sourceCidr = this.data.get('formData.sourceCidr') || null;
        }
        return payload ? Object.assign({}, serachPayload, payload) : defaultPayload;
    },
    close() {
        this.dispose();
    },
    async dialogConfirm() {
        let payload = this.getPayload();
        let isDnatType = this.data.get('isDnatType');
        let formData = this.data.get('formData');
        this.data.set('externalIpErr', '');
        this.data.set('internalIpErr', '');
        this.data.set('VpnDnatErr', '');
        await this.ref('form').validateFields();
        // 检验原IP与映射IP是否再范围内
        if (!this.checkInternalIp() || !this.checkExternalIp() || !this.checkIpRange()) {
            return;
        }
        // DNAT类型额外检验端口
        if (isDnatType && this.checkDnatPort()) {
            return;
        }
        // 检验是否原IP与映射IP是否重复
        await this.checkTypeRepeat();
        const type = this.data.get('type');
        const checkIp = type === 'VPN_IDC_DNAT' || type === 'VPN_VPC_STATIC_NAT';
        let externalIp = formData.externalPort ? (formData.externalIp + formData.externalPort)
            : formData.externalIp;
        let internalIp = formData.internalPort ? (formData.internalIp + formData.internalPort)
            : formData.internalIp;
        if (checkIp && (externalIp === internalIp)) {
            this.data.set('externalIpErr', '映射IP不可以和原IP相同');
            return;
        }
        formData.ruleId ? this.updateVpnNatRule(payload) : this.createVpnNatRule(payload);
        this.close();
    }
});
