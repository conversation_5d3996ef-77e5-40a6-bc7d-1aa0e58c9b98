/*
 * @description: vpn网关NAT列表
 * @file: network/vpn/v2/detail.js
 * @author: <EMAIL>
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {OutlinedPlus} from '@baidu/sui-icon';

import Create from './create';
import {tableColumns} from './tableFields';
import {getVpcName} from '@/pages/sanPages/utils/helper';
import Confirm from '@/pages/sanPages/components/confirm';
import rules from '../../rule';
import './style.less';

const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent} = decorators;

const tpl = html`
<div>
    <div class="{{klass}}">
        <div class="vpn-content-wrap">
            <s-biz-page>
                <span slot="header">
                    <span class="title">{{title}}</span>
                    <s-tip
                        class="inline-tip"
                        placement="right"
                        content="{{tip}}"
                        skin="question"
                    />
                </span>
                <div class="list-page-tb-left-toolbar" slot="tb-left">
                    <s-tooltip
                        trigger="{{natRuleCreate.disable ? 'hover' : ''}}" placement="top">
                        <!--bca-disable-next-line-->
                        <div slot="content">{{natRuleCreate.message | raw}}</div>
                        <s-button
                            disabled="{{natRuleCreate.disable}}"
                            skin="primary" on-click="onCreate">
                            <outlined-plus/>
                            添加规则</s-button>
                    </s-tooltip>
                    <s-tooltip class="left_class"
                        trigger="{{releaseNatRule.disable ? 'hover' : ''}}" placement="top">
                        <!--bca-disable-next-line-->
                        <div slot="content">{{releaseNatRule.message | raw}}</div>
                        <s-button on-click="onRelease" disabled="{{releaseNatRule.disable}}">
                            删除</s-button>
                    </s-tooltip>
                </div>
                <div slot="tb-right" class="right_bar">
                    <s-search width="{{230}}" class="search-warp"
                        value="{=keyword=}"
                        placeholder="{{searchPlaceholder}}"
                        on-search="onSearch">
                        <s-select slot="options"
                            width="120"
                            datasource="{{keywordTypeList}}"
                            value="{=keywordType=}"
                            on-change="keywordTypeChange">
                        </s-select>
                    </s-search>
                </div>
                <s-table
                    s-ref="table"
                    columns="{{table.columns}}"
                    loading="{{table.loading}}"
                    error="{{table.error}}"
                    datasource="{{table.datasource}}"
                    on-selected-change="tableSelected($event)"
                    selection="{=table.selection=}">
                    <div slot="error">
                        啊呀，出错了?
                        <a href="javascript:;" on-click="refresh">重新加载</a>
                    </div>
                    <div slot="empty">
                        <s-empty
                            on-click="onCreate"
                        />
                    </div>
                    <div slot="c-description">
                        <span class="text-hidden">{{row.description || '-'}}</span>
                    </div>
                    <div slot="c-opt">
                        <span class="operations">
                            <a href="javascript:void(0)" on-click="onEdit(row)">编辑</a>
                            <a href="javascript:void(0)" on-click="onRelease($event, row)">删除</a>
                        </span>
                    </div>
                </s-table>
                <div slot="footer" class="nat-list-footer">
                    <p class="selectTip">{{selectTip}}</p>
                    <s-pagination
                        s-if="{{pager.total}}"
                        slot="footer"
                        layout="{{'total, pageSize, pager, go'}}"
                        pageSize="{{pager.size}}"
                        total="{{pager.total}}"
                        page="{{pager.page}}"
                        on-pagerChange="onPageChange"
                        on-pagerSizeChange="onPageSizeChange" />
                </div>
            </s-biz-page>
        </div>
    </div>
</template>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@asComponent('@vpn-nat-list')
class VpnNatList extends Component {
    static components = {
        'outlined-plus': OutlinedPlus
    };
    static computed = {
        selectTip() {
            let length = this.data.get('selectedItems').length;
            let total = this.data.get('pager.total');
            return `已选中${length}条/共${total}条`;
        }
    };

    initData() {
        let title = '云端静态NAT';
        let type = 'VPN_VPC_STATIC_NAT';
        let currentSidebar = 'nat';
        let emptyText = '您暂未添加云端静态NAT规则';
        let tip =
            '云端私有网络内的原IP映射为新 IP，并以新IP身份与VPN对端互访，用于解决VPN两端内网IP冲突问题，需要结合IDC端静态NAT一同使用。';
        let keywordTypeList = [
            {text: '原IP', value: 'internalIp'},
            {text: '映射IP', value: 'externalIp'},
            {text: '描述', value: 'description'}
        ];
        let keywordNatTypeList = [
            {text: '原IP端口', value: 'internalIpPort'},
            {text: '映射IP端口', value: 'externalIpPort'},
            {text: '描述', value: 'description'}
        ];
        let keywordType = 'internalIp';
        let searchPlaceholder = '请输入原IP进行搜素';
        if (location.hash.indexOf('idcdnat') > -1) {
            title = '云端IP端口静态NAT';
            tip = 'IDC端主动访问私有网络，需访问映射后的IP端口与私有网络内原IP端口进行通信，回包不受影响。';
            keywordTypeList = keywordNatTypeList;
            keywordType = 'internalIpPort';
            type = 'VPN_IDC_DNAT';
            currentSidebar = 'idcdnat';
            emptyText = '您暂未添加云端IP端口静态NAT规则';
            searchPlaceholder = '请输入原IP端口进行搜素';
        } else if (location.hash.indexOf('idcnat') > -1) {
            title = 'IDC端静态NAT';
            type = 'VPN_IDC_STATIC_NAT';
            tip =
                'IDC端内的内网IP映射为新IP，并以新IP身份与云端私有网络互访，用于解决VPN两端内网IP冲突问题，需要结合云端静态NAT一同使用。';
            currentSidebar = 'idcnat';
            emptyText = '您暂未添加IDC端静态NAT规则';
        } else if (location.hash.indexOf('vpcdnat') > -1) {
            title = '云端DNAT';
            type = 'VPN_VPC_DNAT';
            keywordTypeList = keywordNatTypeList;
            keywordType = 'internalIpPort';
            tip = 'VPC端主动访问IDC，需访问映射后的IP端口与IDC侧原IP端口进行通信，回包不受影响。';
            currentSidebar = 'vpcdnat';
            emptyText = '您暂未添加云端DNAT规则';
            searchPlaceholder = '请输入原IP端口进行搜素';
        }
        let columns = tableColumns[currentSidebar];
        return {
            klass: ['main-wrap vpn-nat-list-wrap vpn-common-page'],
            vpcId: '',
            selectedItems: [],
            title,
            type,
            emptyText,
            currentSidebar,
            tip,
            keywordTypeList,
            keywordType,
            keyword: '',
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                columns: columns,
                datasource: []
            },
            pager: {
                size: 10,
                page: 1,
                total: 0
            },
            quota: {
                free: 10,
                total: 100
            },
            natRuleCreate: {
                disable: true
            },
            releaseNatRule: {
                disable: false
            },
            searchPlaceholder,
            hasVpnConn: true,
            urlQuery: getQueryParams()
        };
    }

    attached() {
        this.getDetail();
        this.getVpcInfo();
    }

    getDetail() {
        this.$http
            .getVpnDetail(
                {
                    vpnId: this.data.get('urlQuery.vpnId')
                },
                {'x-silent-codes': ['NoSuchObject']}
            )
            .then(instance => {
                let hasVpnConn = !!instance.vpnConns.length;
                let options = {quota: this.data.get('quota'), status: instance.status, hasVpnConn};
                let {natRuleCreate} = checker.check(rules, [], 'natRuleCreate', options);
                this.data.set('natRuleCreate', natRuleCreate);
                this.data.set('instance', instance);
                this.data.set('hasVpnConn', hasVpnConn);
                this.loadPage();
            });
    }

    getVpcInfo() {
        let vpcId = this.data.get('urlQuery.vpcId') || '';
        this.$http.vpcInfo({vpcIds: [vpcId]}).then(data => {
            let vpc = data[vpcId] || {};
            vpc.name = getVpcName(vpc.name);
            this.data.set('vpcInfo', vpc);
        });
    }
    keywordTypeChange({value}) {
        const placeHolderMap = {
            internalIp: '请输入原IP进行搜索',
            externalIp: '请输入映射IP进行搜索',
            description: '请输入描述进行搜索',
            internalIpPort: '请输入原IP端口进行搜索',
            externalIpPort: '请输入映射端口进行搜索'
        };
        this.data.set('searchPlaceholder', placeHolderMap[value]);
    }

    // 改变页数
    onPageChange(e) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }

    // 改变每页显示个数
    onPageSizeChange(e) {
        this.data.set('pager.size', e.value.pageSize);
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    tableSelected(e) {
        let selectedItems = this.ref('table').getSelectedItems();
        this.data.set('selectedItems', selectedItems);
        let {releaseNatRule} = checker.check(rules, selectedItems, 'releaseNatRule');
        this.data.set('releaseNatRule', releaseNatRule);
    }

    refresh() {
        this.resetTable();
        return this.loadPage();
    }

    // 重置列表
    resetTable() {
        this.data.set('table.selection.selectedIndex', []);
    }

    getPayload() {
        let orders = [
            {
                asc: false,
                column: 'updateTime'
            }
        ];
        const vpnId = this.data.get('urlQuery.vpnId');
        const {type, pager, keyword, keywordType} = this.data.get('');
        return u.extend({}, {vpnId, type, keyword, keywordType, orders}, {pageNo: pager.page, pageSize: pager.size});
    }

    loadPage() {
        this.data.set('table.loading', true);
        let payload = this.getPayload();
        this.resetTable();
        this.$http.getVpnRuleList(payload).then(data => {
            this.data.set('table.datasource', data.result);
            this.data.set('pager.total', data.totalCount);
            this.checkQuota();
            this.data.set('table.loading', false);
        });
    }

    checkQuota() {
        const vpnId = this.data.get('urlQuery.vpnId');
        const {type, instance} = this.data.get('');
        this.$http.getVpnRuleQuota({vpnId, type}).then(res => {
            let title = this.data.get('title');
            let hasVpnConn = this.data.get('hasVpnConn');
            const options = {quota: res, status: instance.status, hasVpnConn, title};
            let {natRuleCreate} = checker.check(rules, [], 'natRuleCreate', options);
            this.data.set('quota', res);
            this.data.set('natRuleCreate', natRuleCreate);
        });
    }

    onCreate() {
        let create = new Create({
            data: {
                vpcId: this.data.get('urlQuery.vpcId'),
                vpnId: this.data.get('urlQuery.vpnId'),
                type: this.data.get('type')
            }
        });
        create.on('create', () => this.loadPage());
        create.attach(document.body);
    }

    onEdit(row) {
        let dialog = new Create({
            data: {
                instance: row,
                vpcId: this.data.get('urlQuery.vpcId'),
                vpnId: this.data.get('urlQuery.vpnId'),
                type: this.data.get('type')
            }
        });
        dialog.on('update', () => {
            this.loadPage();
        });
        dialog.attach(document.body);
    }

    onSearch() {
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    onRelease(e, row) {
        const selectItems = row ? [row] : this.ref('table').getSelectedItems();
        let ruleIdList = selectItems.map(item => item.ruleId);
        const content = selectItems.length > 1 ? `确认删除这${selectItems.length}条IP映射？` : '确认删除此IP映射?';
        const dialog = new Confirm({
            data: {
                title: '删除提示',
                content
            }
        });
        dialog.on('confirm', () => {
            const payload = {
                vpcId: this.data.get('urlQuery.vpcId'),
                vpnId: this.data.get('urlQuery.vpnId'),
                ruleIdList
            };
            this.$http.deleteVpnNatRule(payload).then(() => {
                this.data.set('pager.page', 1);
                this.loadPage();
            });
        });
        dialog.attach(document.body);
    }
}
export default San2React(Processor.autowireUnCheckCmpt(VpnNatList));
