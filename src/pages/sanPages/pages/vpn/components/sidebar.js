/**
 * @file network/vpn/components/sidebar.js
 * <AUTHOR>
 */
import {defineComponent} from 'san';
import {html} from '@baiducloud/runtime';
/* eslint-disable */
const template = html`
<template>
<div class="vpn-common-sidebar">
    <div class="menu-item-wrap">
        <div s-for="item in config" class="{{item | itemClass}}">
            <a href="{{item.disabled ? 'javascript:void(0);' : item.url}}"
                dataTrackId="{{dataTrackId}}" dataTrackName="{{item.name}}">
                {{item.name}}
            </a>
        </div>
    </div>
</div>
</template>
`;
/* eslint-enable */
export default defineComponent({
    template,
    initData() {
        return {
            current: '',
            config: [],
            dataTrackId: 'vpn_sub_sidebar'
        };
    },
    filters: {
        itemClass(item) {
            const klass = ['menu-item'];
            const current = this.data.get('current');
            current === item.id && klass.push('sidebar-current');
            item.disabled && klass.push('disabled');
            return klass;
        }
    },
    inited() {
        let vpcId = this.data.get('vpcId');
        let vpnId = this.data.get('vpnId');
        this.data.set('config', [
            {
                id: 'detail',
                name: '实例信息',
                url: '#/vpc/vpn/detail?vpcId=' + vpcId + '&vpnId=' + vpnId
            },
            {
                id: 'monitor',
                name: '监控',
                url: '#/vpc/vpn/monitor?vpcId=' + vpcId + '&vpnId=' + vpnId
            },
        ]);
    }
});