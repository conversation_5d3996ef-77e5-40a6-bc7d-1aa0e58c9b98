import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import u from 'lodash';
import {Dialog, Icon, Button, Input, Form} from '@baidu/sui';
import './internalIpBind.less';
import RULE from '@/pages/sanPages/utils/rule';
import {checkIsInSubnet} from '@/pages/sanPages/utils/common';

const formValidator = self => ({
    internalIp: [
        {
            validator: (rule, value, callback) => {
                if (!value) {
                    return callback('请输入IP地址');
                }
                if (!u.trim(value)) {
                    return callback('请输入IP地址');
                }
                if (!RULE.IP.test(value)) {
                    return callback('IP地址格式有误');
                }
                const subnet = self.data.get('subnetDetail');
                if (subnet && !checkIsInSubnet(value + '/32', subnet.cidr)) {
                    return callback('IP地址不在当前VPN所在子网内');
                }
                callback();
            }
        }
    ]
});

/* eslint-disable */
const tpl = html`
    <template>
        <s-dialog class="internalIp-confirm" closeAfterMaskClick="{{false}}" open="{=open=}" title="{{title}}">
            <div>
                <s-form s-ref="form" label-align="left" rules="{{rules}}" data="{=formData=}">
                    <s-form-item label="本端私网IP：" prop="internalIp" class="require-label" help="{{internalIpHelp}}">
                        <s-input
                            value="{=formData.internalIp=}"
                            placeholder="请输入IP"
                            width="320"
                            on-input="InputChange"
                        />
                        <p class="error-tip" s-if="errorMessage">{{errorMessage}}</p>
                    </s-form-item>
                </s-form>
            </div>
            <div slot="footer">
                <s-button on-click="close">取消</s-button>
                <s-button skin="primary" on-click="dialogConfirm" disabled="{{disableSub}}">确定</s-button>
            </div>
        </s-dialog>
    </template>
`;
/* eslint-enable */
export default class InternalIpBind extends Component {
    static template = tpl;
    static components = {
        's-dialog': Dialog,
        's-icon': Icon,
        's-button': Button,
        's-input': Input,
        's-form': Form,
        's-form-item': Form.Item
    };
    inited() {
        this.$http.getSubnetResourceDetail(this.data.get('subnetId')).then(data => {
            this.data.set('subnetDetail', data);
            this.data.set('internalIpHelp', `当前VPN网关所在子网网段${data.cidr}`);
        });
    }
    initData() {
        return {
            title: '绑定私网IP',
            content: '',
            open: true,
            formData: {
                internalIp: ''
            },
            rules: formValidator(this),
            internalIpHelp: ''
        };
    }
    async dialogConfirm() {
        await this.ref('form').validateFields();
        let params = {
            vpcId: this.data.get('vpcId'),
            vpcPrivateIpAddresses: this.data.get('formData.internalIp')
        };
        this.data.set('disableSub', true);
        await this.$http.vpcCheckPrivateIp(params).then(res => {
            if (res.vpcPrivateIpAddresses && res.vpcPrivateIpAddresses.length) {
                this.data.set('errorMessage', '当前IP已被占用');
                this.data.set('disableSub', false);
                return;
            } else {
                this.data.set('errorMessage', '');
                this.data.set('disableSub', false);
            }
        });
        if (this.data.get('errorMessage')) {
            return;
        }
        this.data.set('disableSub', true);
        let payload = {
            internalIp: this.data.get('formData.internalIp'),
            instanceId: this.data.get('instanceId'),
            instanceType: this.data.get('instanceType')
        };
        this.$http
            .vpnEipBind(payload)
            .then(res => {
                this.data.set('disableSub', false);
                this.fire('confirm', this.data.get('formData.internalIp'));
                this.data.set('open', false);
            })
            .catch(e => {
                this.data.set('disableSub', false);
            });
    }
    close() {
        this.fire('close');
        this.data.set('open', false);
    }
    InputChange() {
        this.data.set('errorMessage', '');
    }
}
