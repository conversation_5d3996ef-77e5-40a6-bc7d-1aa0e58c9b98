.vpn_list_wrap {
    position: relative;
    .title_vpn {
        position: absolute;
        z-index: 9999;
        top: -90px;
        color: #151b26;
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
        padding-top: 12px;
        padding-bottom: 4px;
        background-color: #fff;
        text-indent: 16px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        .title-left {
            display: flex;
            align-items: center;
            .vpc-select {
                margin-left: -24px;
                width: 200px;
            }
        }
        .title-right {
            display: flex;
            align-items: center;
            margin: 0 16px;
            .help-file {
                margin-right: 16px;
                font-size: 12px;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                color: #151b26;
                line-height: 20px;
                &:hover {
                    color: #2468f2;
                }
                .s-icon {
                    position: relative;
                    top: -1px;
                    right: 4px;
                    margin-right: 0;
                    color: #2468f2;
                    font-size: 14px;
                }
            }
            .function-introduce {
                color: #2468f2;
            }
            .link-wrap {
                display: flex;
                align-items: center;
            }
            .vpn-tip {
                font-size: 12px;
                font-weight: 400;
            }
            .button-shortcut {
                padding: 0;
            }
        }
    }
    .vpn-list-wrap {
        .remote-text-wrap {
            display: flex;
        }
        .s-table {
            .s-table-row:hover {
                .name-icon {
                    display: inline;
                }
            }
            .s-table-body {
                max-height: calc(~'100vh - 412px');
                .vpn-id-widget {
                    white-space: nowrap;
                }
            }
            .name-icon {
                display: none;
                font-size: 12px;
                fill: #2468f2;
            }
            .operations {
                .s-button {
                    padding: 0;
                    margin-right: 12px;
                }
            }
            .s-table-empty {
                border-bottom: none;
            }
        }
        .icon-bind,
        .icon-unbind {
            font-size: 16px;
            color: #0786e9;
        }
        .icon-unbind {
            margin-left: 4px;
        }
        .bind_class {
            .s-button {
                padding: 0;
                margin-left: 8px;
            }
        }
        .header-button-wrap {
            margin-left: auto;
            display: flex;
            align-items: center;
            .link-wrap {
                margin-left: 8px;
            }
        }
        .button-shortcut {
            background-color: #f5f5f5;
            border-color: #ebebeb;
        }
        .vpn-tip {
            background: #fcf7f1;
            padding: 5px;
            margin-left: 10px;
            color: #f38900;
        }
        .title {
            font-size: 16px;
            margin-right: 10px;
        }
        .intro-warp {
            display: inline-block;
            .placeholder-style {
                input::-webkit-input-placeholder {
                    /*WebKit browsers*/
                    color: #000;
                }
                input::-moz-input-placeholder {
                    /*Mozilla Firefox*/
                    color: #000;
                }

                input::-ms-input-placeholder {
                    /*Internet Explorer*/
                    color: #000;
                }
            }
        }
        .vpn-list-header {
            .s-select {
                margin-left: 16px;
            }
            .header-button-wrap {
                margin-right: 16px;
            }
            // border-bottom: 1px solid #ebebeb;
        }
        .icon-edit,
        .icon-copy {
            font-size: 12px;
            color: #0786e9;
        }
        .tip-icon-wrap {
            font-size: 14px;
            border: 1px solid #f18823;
            color: #f18823;
            &:hover {
                color: #fff;
                background-color: #f18823;
            }
        }
        .foot-pager {
            margin-top: 16px;
        }
        .vpn-buttons-wrap {
            display: flex;
            align-items: center;
            .s-cascader {
                margin-right: 5px;
                .s-cascader-panel {
                    display: flex;
                    height: auto;
                    background: none;
                    box-shadow: none;
                    .s-cascader-column {
                        background: #fff;
                        box-shadow: 0 1px 6px #ccc;
                        max-height: 150px;
                    }
                }
            }
            .s-cascader-value {
                vertical-align: middle;
                font-size: 12px;
                padding-top: 0;
                padding-bottom: 0;
                line-height: 30px;
            }
            .s-auto-compelete {
                .s-select {
                    input {
                        width: 170px !important;
                    }
                }
            }
            .refresh-button {
                margin-left: 5px;
                margin-right: 5px;
            }
            .search-content {
                display: flex;
                align-items: center;
                position: relative;
                margin-right: 5px;
            }
            .s-icon.search {
                position: absolute;
                right: 5px;
                color: #615a5a;
            }
            .icon-fresh {
                margin-right: 5px;
            }
        }
    }
    .new-tag {
        display: inline-block;
        background-color: #f72e32;
        border-radius: 16px;
        line-height: 16px;
        min-width: 40px;
        text-align: center;
        font-size: 12px;
        color: #fff;
    }
    .new-instance-tag {
        background-color: #f33e3e;
        border-radius: 2px;
        line-height: 17px;
    }
}
.vpn-edit-wrap {
    width: 200px;
}
.vpn-conn-list-table {
    .s-table-thead {
        .s-table-hcell-sel {
            padding-left: 0px !important;
        }
    }
    .s-table-tbody {
        .s-table-cell-allSaActive {
            .tip-icon {
                color: #9e9898;
                border: 1px solid #9e9898;
                &:hover {
                    color: #2468f2;
                    border: 1px solid #2468f2;
                }
            }
        }
    }
}

.more-opt {
    .s-button {
        display: inline-block;
        padding: 0;
        width: 120px;
        text-align: left;
    }
    .s-menu-item-disabled {
        .s-button-skin-stringfy {
            color: #b4b6ba !important;
        }
    }
}
