import {test} from '@playwright/test';
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, request} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {EipBindDialog} from '@baidu/bce-eip-sdk-san';
import {EipSDK} from '@baidu/bce-eip-sdk';
import {Notification, Table} from '@baidu/sui';
import {OutlinedPlus, OutlinedRefresh, OutlinedLink, OutlinedEditingSquare, OutlinedDownload} from '@baidu/sui-icon';
import {ResourceGroupDialog} from '@baidu/bce-resourcegroup-sdk-san';
import {ResourceGroupSDK} from '@baidu/bce-resourcegroup-sdk';
import {DiagnoseSDKProcessor, AnalysisSDKProcessor} from '@baidu/bce-vpc-sdk-react';
import {columns} from '../list/tableField';
import Monitor from '../list/bcmDetail';
import VpnConnList from '../conn/list/list';
import rules from '../rule';
import InternalIpBindDialog from './internalIpBind';
import Confirm from '@/pages/sanPages/components/confirm';
import DiagnoseConfirm from '@/pages/sanPages/components/diagnoseConfirm/confirm';
import {recharge, alterProductType, utcToTime, $flag as FLAG} from '@/pages/sanPages/utils/helper';
import {VpnStatus, PayType, vpnFlavor, vpnType} from '@/pages/sanPages/common/enum';
import {disable_vpn_region, disable_vpn_flavor_region} from '@/pages/sanPages/common/flag';
import {DocService} from '@/pages/sanPages/common';
import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import DOCUMENT_ICON from '@/img/document_normal.svg?url';
import DOCUMENT_ICON_COLOR from '@/img/document_color.svg?url';
import testID from '@/testId';
import './vpnList.less';
import '@baidu/bce-vpc-sdk-react/lib/style.css';

const processor = new DiagnoseSDKProcessor();
const processorPath = new AnalysisSDKProcessor();
const {asComponent, invokeSUI, invokeSUIBIZ, withSidebar, invokeAppComp, template, invokeComp} = decorators;
const AllRegion = window.$context.getEnum('AllRegion');
const tpl = html`
<div class="vpn_list_wrap" data-testid="{{listVpn}}">
    <div class="title_vpn">
        <div class="title-left">
            <span>VPN网关</span>
            <vpc-select
                class="vpc-select"
                on-change="vpcChange"
                on-int="vpcInt" />
        </div>
        <div class="title-right">
            <a
                s-ref="introduce"
                href="javascript:void(0)"
                class="help-file function-introduce"
                on-click="handleShowCard"
                on-mouseenter="handleMouseEnter('introduce')"
                on-mouseleave="handleMouseLeave('introduce')"
                s-if="{{!FLAG.NetworkSupportXS}}">
                <img class="s-icon" src="{{introduceIcon}}" />功能简介
            </a>
            <s-button skin="stringfy" class="button-shortcut" on-click="showExpireData" s-if="!showMode">
                7天即将到期
            </s-button>
            <s-button s-else on-click="showExpireData" class="button-shortcut">
                7天即将到期
                <s-icon name="close"/>
            </s-button>
            <div s-if="{{!FLAG.NetworkSupportXS}}" class="link-wrap">
                <a class="vpn-tip" target="_BLANK" href="/billing/#/renew/list~serviceType=VPN">
                    <outlined-link class="outlined-link" color="#2468f2"/>自动续费
                </a>
            </div>
            <a href="{{dosMap[vpnType]}}"
                target="_blank"
                class="help-file"
                on-mouseenter="handleMouseEnter('document')"
                on-mouseleave="handleMouseLeave('document')"
                s-if="{{!FLAG.NetworkSupportXS}}">
                <img class="s-icon" src="{{documentIcon}}" />帮助文档
            </a>
        </div>
    </div>
    <s-app-list-page class="{{klass}}">
        <div class="vpn-list-header" slot="pageTitle">
            <introduce-panel
                isShow="{{show}}"
                klass="{{'IPv6-vpn-wrapper'}}"
                title="{{introduceTitle}}"
                description="{{introduceDesc}}"
                on-toggle="handleToggle($event)"
            ></introduce-panel>
        </div>
        <div slot="bulk">
            <s-tip-button
                disabled="{{accountState.disabled || createVpn.disable || iamPass.disable}}"
                skin="primary"
                isDisabledVisibile="{{true}}"
                on-click="onCreate"
                data-test-id="{{listCreateTestId}}"
            >
                <div slot="content">
                    <!--bca-disable-next-line-->
                    {{accountState.message || createVpn.message || iamPass.message | raw}}
                </div>
                <outlined-plus/>
                创建{{vpnType | vpnTitle}} 网关
            </s-tip-button>
            <s-tip-button
                disabled="{{recharge.disable || accountState.disabled}}"
                isDisabledVisibile="{{true}}"
                on-click="onRecharge"
                class="left_class"
            >
                <div slot="content">
                    <!--bca-disable-next-line-->
                    {{recharge.message || accountState.message | raw}}
                </div>
                续费
            </s-tip-button>
            <s-tip-button
                s-if="vpnType !== 'ssl'"
                class="left_class"
                disabled="{{vpnRelease.disabled}}"
                on-click="onRelease"
                isDisabledVisibile="{{true}}"
            >
                <div slot="content">
                    <!--bca-disable-next-line-->
                    {{vpnRelease.message | raw}}
                </div>
                释放
            </s-tip-button>
            <s-tip-button
                s-if="vpnType === 'ssl'"
                class="left_class"
                disabled="{{sslRelease.disable}}"
                on-click="onRelease"
                isDisabledVisibile="{{true}}"
            >
                <div slot="content">
                    <!--bca-disable-next-line-->
                    {{sslRelease.message | raw}}
                </div>
                释放
            </s-tip-button>
            <edit-tag
                class="left_class"
                selectedItems="{{table.selectedItems}}"
                on-success="refresh"
                type="VPN"
            ></edit-tag>
            <div
                s-if="vpnType !== 'ssl'"
                class="intro-warp left_class"
                data-intro="这里是批量操作区"
            >
                <s-tooltip content="请先选择实例对象"
                    trigger="{{operationDisabled ? 'hover' : ''}}" placement="top">
                    <s-select placeholder="批量操作" value="{=operation=}"
                        disabled="{{operationDisabled}}"
                        class="{{!operationDisabled ? 'placeholder-style' : ''}}"
                        on-change="onOperationChange">
                        <s-select-option class="operation-select" s-for="item in OperationType" value="{{item.value}}"
                            label="{{item.label}}" disabled="{{item.disabled}}">
                            <s-tooltip placement="right"
                                trigger="{{item.message ? 'hover' : ''}}" width="200">
                                <div slot="content">
                                <!--bca-disable-next-line-->
                                    {{item.message | raw}}
                                </div>
                                <div>{{item.label}}</div>
                            </s-tooltip>
                        </s-select-option>
                    </s-select>
                </s-tooltip>
            </div>
        </div>
        <div slot="filter">
            <div class="vpn-buttons-wrap">
                <search-tag
                    s-ref="search"
                    serviceType="VPN"
                    searchbox="{=searchbox=}"
                    on-search="onSearch"
                ></search-tag>
                <s-button class="s-icon-button" on-click="refresh" track-name="刷新"><outlined-refresh class="icon-class"/></s-button>
                <s-button on-click="onDownload" class="s-icon-button" track-id="ti_vpc_security_download" track-name="下载"><outlined-download class="icon-class"/></s-button>
                <!--需要添加gre-->
                <custom-column
                    class="left_class"
                    columnList="{{customColumn.datasource}}"
                    initValue="{{customColumn.value}}"
                    type="{{vpnType === 'ipsec' ? 'ipsecVpn' : 'sslVpn'}}"
                    on-init="initColumns"
                    on-change="onCustomColumns">
                </custom-column>
            </div>
        </div>
        <s-table
            columns="{{table.columns}}"
            loading="{{table.loading}}"
            error="{{table.error}}"
            datasource="{{table.datasource}}"
            on-selected-change="tableSelected($event)"
            on-filter="onFilter"
            on-sort="onSort"
            has-Expand-Row="{{true}}"
            expandedIndex="{{expandIndex}}"
            on-exprow-expand="onRowExpand"
            selection="{=table.selection=}"
            track-id="ti_vpc_instance_table"
            track-name="列表操作"
            data-test-id="${testID.vpn.vpnTableList}"
        >
            <div slot="empty">
                <table-empty
                    showAction="{{false}}"
                    desc="{{'暂无'+ ' ' + introduceTitle + ' ' + '网关。'}}"
                />
            </div>
            <div slot="expanded-row">
                <vpn-conn-list
                    vpnType="{{vpnType}}"
                    vpnId="{{row.vpnId}}"
                    vpcId="{{row.vpcId}}"
                    eip="{{row.eip}}"
                    netType="{{row.netType}}"
                    internalIp="{{row.internalIp}}"
                    enableBgp="{{row.enableBgp}}"
                    status="{{row.status}}"
                    on-updateList="handleUpdateList"
                />
            </div>
            <div slot="c-id" class="vpn-id-widget">
                <span class="truncated">
                    <s-tooltip content="{{row.vpnName}}">
                        <a data-testid="${testID.vpn.detailName}" href="#/vpc/vpn/detail?vpcId={{row.vpcId}}&vpnId={{row.vpnId}}&vpnType={{vpnType}}&showRoute={{isShowRouteManage(row)}}">
                            {{row.vpnName}}
                        </a>
                    </s-tooltip>
                </span>
                <s-popover s-if="row.status !== 'building'" s-ref="popover-vpnName-{{rowIndex}}" placement="top" trigger="click"
                    class="edit-popover-class">
                    <div class="edit-wrap" slot="content">
                        <s-input value="{=edit.vpnName.value=}"
                            width="320"
                            placeholder="请输入名称"
                            on-input="onEditInput($event, rowIndex, 'vpnName')"/>
                        <div class="edit-tip">大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65</div>
                        <s-button skin="primary" s-ref="editBtn-vpnName-{{rowIndex}}" disabled="{{true}}"
                            on-click="editConfirm(row, rowIndex, 'vpnName')">确定</s-button>
                        <s-button on-click="editCancel(rowIndex, 'vpnName')">取消</s-button>
                    </div>
                    <outlined-editing-square class="name-icon" on-click="onInstantEdit(row, rowIndex, 'vpnName')"/>
                </s-popover>
                <br>
                <span class="truncated">
                    {{row.vpnId}}
                </span>
                <s-clip-board class="name-icon" text="{{row.vpnId}}"/>
            </div>
            <div slot="c-vpcId">
                <div class="truncated">
                    <s-tooltip content="{{row.vpcName || '-'}}">
                        <a href="#/vpc/instance/detail?vpcId={{row.vpcId}}" class="list-link">{{row.vpcName || '-'}}</a>
                    </s-tooltip>
                </div>
                <br />
                <span class="truncated">
                    {{row.vpcShortId || '-'}}
                </span>
            </div>
            <div slot="c-productType">
                <span>{{row | getProductType}}</span>
                <span s-if="{{row.task === 'auto_renew' && row.productType === 'prepay'}}"
                    class="icon-auto-renew"
                    name="auto-renew" title="已开通自动续费">
                </span>
                <s-popover s-if="row.enableProduct" placement="top">
                    <div slot="content">
                        该实例已开通计费变更-预付费转后付费，将会在到期后转为后付费资源，请关注！如需进行续费、升级等操作，请先取消计费变更，谢谢！
                    </div>
                    <s-icon class="tip-icon-wrap" name="warning-mark"/>
                </s-popover>
            </div>
            <div slot="c-remoteSideIp">
                <template s-if="row.netType !== 'intranet'">
                    <span class="remote-text-wrap">
                        <s-tooltip class="truncated" content="{{row.eip}}">
                            <a s-if="row.eip"
                                href="/eip/#/eip/instance/list?keywordType=INSTANCE_EIP&keyword={{row.eip}}"
                            >{{row.eip}}</a>
                        </s-tooltip>
                        <span s-else>-</span>
                        <span>/{{row.bandwidthInMbps || '-'}}Mbps</span>
                    <span>
                    <s-popover s-if="row.eipResourceStatus === 'STOPPED'" placement="top">
                        <div slot="content">
                            {{row | getTipContent}}
                        </div>
                        <s-icon class="tip-icon-wrap" name="warning-mark"/>
                    </s-popover>
                    <s-icon s-if="row.eip" name="unbind" on-click="unbindEip(row)">
                    </s-icon>
                    <s-tip-button s-else icon="bind"
                        class="bind_class"
                        skin="stringfy"
                        disabled="{{row.bindEip.disable}}"
                        isDisabledVisibile="{{true}}"
                        on-click="bindEip(row)"
                    >
                        <div slot="content">
                            <!--bca-disable-next-line-->
                            {{row.bindEip.message | raw}}
                        </div>
                    </s-tip-button>
                </template>
                <template s-else>
                    <span>-</span>
                </template>
            </div>
            <div slot="c-internalIp">
                <template s-if="row.netType === 'intranet'">
                    <span class="remote-text-wrap">
                        <span s-if="row.internalIp">{{row.internalIp}}</span>
                        <span s-else>-</span>
                    <span>
                    <s-icon s-if="row.internalIp" name="unbind" on-click="unbindInternalIp(row)">
                    </s-icon>
                    <s-tip-button s-else-if="{{row.status !== 'building'}}" icon="bind"
                        class="bind_class"
                        skin="stringfy"
                        isDisabledVisibile="{{true}}"
                        on-click="bindEipInternalIp(row)"
                    >
                    </s-tip-button>
                </template>
                <template s-else>
                    <span>-</span>
                </template>
            </div>
            <div slot="c-enableBgp">
                <!--<s-tooltip
                    content="{{'当前网关下有VPN隧道使用BGP功能，请先删除该网关下隧道。'}}"
                    trigger="{{row.enableBgp && row.vpnConns.length ? 'hover' : ''}}"
                >
                    <s-switch
                        disabled="{{row.disableFlag}}"
                        checked="{=row.enableBgp=}"
                        on-change="handleBgpChange($event, row, rowIndex)"
                    />
                </s-tooltip>-->
                {{row.enableBgp | getEnableBgp}}
            </div>
            <div slot="c-flavor">
                {{row.flavor | getFlavor}}
            </div>
            <div slot="c-netType">
                {{row.netType | getNetType}}
            </div>
            <div slot="c-greBandwidth">
                {{row.flavor | getGreBandwidth}}
            </div>
            <template slot="c-groups">
                <p s-for="item in row.resourceGroups">{{item.name}}</p>
            </template>
            <div slot="c-status">
                <span class="{{row.status | statusClass}}">{{row.status | statusText}}</span>
            </div>
            <div slot="c-tag">
                <span s-if="!row.tags || row.tags.length < 1">
                    -
                </span>
                <div s-else s-for="item,index in row.tags">
                    <span s-if="index <= 1">
                        {{item.tagKey + ':' + item.tagValue}}
                    </span>
                    <div s-if="row.tags.length > 2 && index === 1">...</div>
                </div>
            </div>
            <div slot="h-diagnose">
                <span
                    >诊断
                    <div class="new-tag new-instance-tag">new</div></span
                >
            </div>
            <div slot="c-diagnose">
                <s-button skin="stringfy" on-click="showDiagnose(row)">诊断</s-button>
            </div>
            <div slot="c-expiredTime">{{row | getTime}}</div>
            <div slot="c-description">
                <span class="truncated" title="{{row.description}}">{{row.description || '-'}}</span>
                <s-popover s-ref="popover-description-{{rowIndex}}" placement="top" trigger="click"
                    class="edit-popover-class">
                    <div class="edit-wrap" slot="content">
                        <s-input value="{=edit.description.value=}"
                            width="160"
                            on-input="onEditInput($event, rowIndex, 'description')"/>
                        <div class="edit-tip">描述不能超过200个字符</div>
                        <s-button skin="primary" s-ref="editBtn-description-{{rowIndex}}" disabled="{{true}}"
                            on-click="editConfirm(row, rowIndex, 'description')">确定</s-button>
                        <s-button on-click="editCancel(rowIndex, 'description')">取消</s-button>
                    </div>
                    <outlined-editing-square class="name-icon" on-click="onInstantEdit(row, rowIndex, 'description')"/>
                </s-popover>
            </div>
            <div slot="c-opt" class="operations">
                <s-button skin="stringfy" s-if="isShowMonitor(row)" on-click="showMonitor(row)">监控</s-button>
                <s-button skin="stringfy" s-if="isShowRouteManage(row)" on-click="handleRouteManage(row)">路由管理</s-button>
                <s-button skin="stringfy" on-click="changeResourceGroup(row)">编辑资源分组</s-button>
            </div>
        </s-table>
        <s-pagination
            s-if="{{pager.total}}"
            slot="pager"
            layout="{{'total, pageSize, pager, go'}}"
            pageSize="{{pager.pageSize}}"
            total="{{pager.total}}"
            page="{{pager.page}}"
            on-pagerChange="onPagerChange"
            on-pagerSizeChange="onPagerSizeChange" />
        <resource-group-dialog
            s-if="{{showResource}}"
            sdk="{{resourceSDK}}"
            resource="{{resource}}"
            on-success="onCommit"
            on-cancel="onCancel"/>
    </s-app-list-page>
    <div id="vpnDiagnoseDomIpsec" s-if="{{drawerVisibleVpnDiagnose && vpnType === 'ipsec'}}"></div>
    <div id="vpnDiagnoseDomSsl" s-if="{{drawerVisibleVpnDiagnose && vpnType === 'ssl'}}"></div>
    <div id="vpnDiagnoseDomGre" s-if="{{drawerVisibleVpnDiagnose && vpnType === 'gre'}}"></div>
</div>
`;

@template(tpl)
@asComponent('@vpn-list')
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@invokeComp(
    '@vpn-conn-list',
    '@edit-tag',
    '@search-tag',
    '@vpc-select',
    '@custom-column',
    '@introduce-panel',
    '@table-empty'
)
class VpnList extends Component {
    initData() {
        const allColumns = columns.slice();
        const customColumnDb = allColumns.map(item => ({
            text: item.label,
            value: item.name,
            disabled: item.name === 'id' || item.name === 'opt'
        }));
        return {
            FLAG,
            klass: 'vpn-list-wrap',
            searchbox: {
                keyword: '',
                placeholder: '请输入标签进行搜索',
                defaultLabel: '实例名称',
                keywordType: ['vpnName'],
                keywordTypes: [
                    {
                        value: 'vpnName',
                        text: '实例名称'
                    },
                    {
                        value: 'vpnId',
                        text: '实例ID'
                    },
                    {value: 'tag', text: '标签'},
                    {value: 'resGroupId', text: '资源分组'}
                ]
            },
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                allColumns,
                datasource: [],
                selectedItems: []
            },
            OperationType: [
                {label: '释放', value: 'RELEASE'},
                {label: '计费变更', value: 'ALTER_PRODUCTTYPE'},
                {label: '取消计费变更', value: 'CANCEL_ALTER_PRODUCTTYPE'}
            ],
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            customColumn: {
                value: [
                    'id',
                    'status',
                    'diagnose',
                    'vpcId',
                    'flavor',
                    'productType',
                    'remoteSideIp',
                    'vpnConnNum',
                    'groups',
                    'opt'
                ],
                datasource: customColumnDb
            },
            order: {},
            edit: {
                vpnName: {
                    value: '',
                    error: true,
                    visible: false
                },
                description: {
                    value: '',
                    error: true,
                    visible: false
                }
            },
            showMode: '',
            expandIndex: [0],
            iamPass: {},
            dosMap: {
                ipsec: DocService.ipsec_helpFile,
                ssl: DocService.ssl_helpFile,
                gre: DocService.gre_helpFile
            },
            show: true,
            introduceDescMap: {
                ipsec: 'IPsec VPN网关是一种通过公网加密通道连接您的IDC和私有网络的方式。',
                ssl: 'SSL VPN网关(Secure Session Layer安全会话层）是一种安全加密的网络连接产品，方便用户通过客户端加载证书的方式访问部署在百度智能云VPC中的应用及服务。',
                gre: 'GRE VPN网关(Generic Routing Encapsulation）是一种基于三层隧道的网络连接产品，具有大带宽、低延时、安全、可靠等特点。广泛适用于传统网络连接、IOT终端类应用的连接，如车载端、手持端、机端等形态，满足4G/5G万物互联的应用场景。'
            },
            introduceIcon: INTRODUCE_ICON_COLOR,
            documentIcon: DOCUMENT_ICON,
            vpnType: '',
            vpnRelease: {},
            resourceSDK: new ResourceGroupSDK(window.$http, window.$context),
            accountState: {
                disabled: false,
                message: ''
            },
            drawerVisibleVpnDiagnose: true,
            visibleDrawVpnDiagnose: true,
            openDrawerVpnDiagnose: false,
            listCreateTestId: testID.vpn.greVpnListCreate,
            listVpn: testID.vpn.vpnList
        };
    }
    static components = {
        's-table': Table,
        'vpn-conn-list': VpnConnList,
        'outlined-plus': OutlinedPlus,
        'outlined-refresh': OutlinedRefresh,
        'outlined-link': OutlinedLink,
        'outlined-editing-square': OutlinedEditingSquare,
        'outlined-download': OutlinedDownload,
        'resource-group-dialog': ResourceGroupDialog
    };
    static filters = {
        statusClass(value) {
            return VpnStatus.fromValue(value).styleClass || '';
        },
        statusText(value) {
            return value ? VpnStatus.getTextFromValue(value) : '-';
        },
        getFlavor(value) {
            return value === 'normal' ? '普通型' : vpnFlavor.getTextFromValue(value);
        },
        getNetType(value) {
            return value === 'intranet' ? '私网' : '公网';
        },
        getProductType(item) {
            return PayType.getTextFromValue(item.productType);
        },
        getTime(item) {
            return item.expiredTime ? utcToTime(item.expiredTime) : '-';
        },
        getRemoteIp(item) {
            let eipStr = item.eip
                ? `
            <a href="/eip/#/eip/instance/list?keywordType=INSTANCE_EIP&keyword=${item.eip}">${item.eip}</a>`
                : '-';
            return `<span>${eipStr}/${item.bandwidthInMbps || '-'}Mbps</span>`;
        },
        getTipContent(item) {
            return item.eipProductType === 'prepay'
                ? '您的公网 IP 已到期，续费后才能使用。'
                : '您的公网 IP 已欠费，充值后才能使用。';
        },
        vpnTitle(value) {
            return vpnType.getTextFromValue(value);
        },
        getGreBandwidth(value) {
            return value === 'normal' || !value ? '小型' : '中型';
        },
        getEnableBgp(value: boolean) {
            return value !== undefined ? (value ? '开启' : '关闭') : '-';
        }
    };
    static computed = {
        operationDisabled() {
            const selectedItems = this.data.get('table.selectedItems');
            return selectedItems.length === 0;
        },
        introduceTitle() {
            return vpnType.getTextFromValue(this.data.get('context').vpnType);
        },
        introduceDesc() {
            const introduceDescMap = this.data.get('introduceDescMap');
            const vpnType = this.data.get('context').vpnType;
            return introduceDescMap[vpnType];
        }
    };
    inited() {
        this.data.set('vpnType', this.data.get('context').vpnType);
        this.setPageType();
        this.getIamQuery();
        this.setOperationMessage();
        this.check();
        this.setTableHeader();
        const accountState = window.$storage.get('accountState');
        this.data.set('accountState', accountState);
    }
    attached() {
        window.$storage.get('showVpnIntroduce') === false && this.data.set('show', false);
        this.watch('operation', value => {
            if (value !== '') {
                this.data.set('operation', '');
            }
        });
        this.data.set('introduceEle', this.ref('introduce'));
    }
    setPageType() {
        const vpnType = this.data.get('vpnType');
        const vpnTypeMapTestId = {
            ipsec: testID.vpn.ipsecVpnList,
            ssl: testID.vpn.sslVpnList,
            gre: testID.vpn.greVpnList
        };
        this.data.set('listVpn', vpnTypeMapTestId[vpnType]);
    }
    check() {
        let all = [this.checkVpnRegion(), this.eipBlackList()];
        Promise.all(all).then(res => {
            this.checkVpnCreate();
        });
    }
    checkVpnRegion() {
        let region = window.$context.getCurrentRegionId();
        if (region === AllRegion.HK02) {
            this.data.set('enableVpnRegion', true);
            return;
        }
        this.data.set('enableVpnRegion', u.indexOf(disable_vpn_region, region) === -1);
        return Promise.resolve();
    }
    eipBlackList() {
        const whiteList = window.$storage.get('commonWhite');
        this.data.set('eipBlackList', whiteList?.eipBlackList);
    }
    checkVpnCreate() {
        let enableRegion = this.data.get('enableVpnRegion');
        let eipBlackList = this.data.get('eipBlackList');
        let {createVpn} = checker.check(rules, '', 'createVpn', {enableRegion, eipBlackList});
        this.data.set('createVpn', createVpn);
    }
    vpcInt() {
        this.loadPage(this.data.get('context').vpnId);
    }
    vpcChange() {
        this.loadPage();
    }
    handleBgpChange(e: any, row: Record<string, any>, rowIndex: number) {
        const {vpnId} = row;
        const {value} = e;
        this.data.set(`table.datasource[${rowIndex}].disableFlag`, true);
        this.$http
            .updateVpnBgp({vpnId, enableBgp: value})
            .then(() => {
                this.loadPage();
            })
            .finally(() => {
                this.data.set(`table.datasource[${rowIndex}].disableFlag`, false);
            });
    }
    getPayload(vpnId?: string) {
        const searchParam = this.ref('search') ? this.ref('search').getSearchCriteria() : {};
        const {pager, order, filters, showMode} = this.data.get('');
        let payload = {
            pageNo: pager.page,
            pageSize: pager.pageSize,
            vpnType: this.data.get('context').vpnType,
            vpcId: window.$storage.get('vpcId'),
            orders: [
                {
                    asc: false,
                    column: 'createTime'
                }
            ]
        };
        if (!payload.vpcId) {
            delete payload.vpcId;
        }
        if (showMode) {
            payload.showMode = showMode;
        }
        let reqPayload = {...payload, ...order, ...filters, ...searchParam};
        if (vpnId) {
            reqPayload.keyword = vpnId;
            reqPayload.keywordType = 'vpnId';
            this.data.set('searchbox.keyword', vpnId);
            this.data.set('searchbox.keywordType', ['vpnId']);
        }
        return reqPayload;
    }
    loadPage(vpnId?: string) {
        this.data.set('table.loading', true);
        let payload = this.getPayload(vpnId);
        if (payload.keywordType === 'resGroupId') {
            payload.keywordType = 'resourceGroupName';
        }
        this.resetTable();
        this.$http.getVpnList(payload).then(res => {
            let dataList = this.checkBindAble(res.result);
            this.data.set('expandIndex', [0]);
            this.data.set('table.datasource', dataList);
            this.data.set('pager.total', res.totalCount);
            this.data.set('table.loading', false);
        });
    }
    checkBindAble(data) {
        let eipBlackList = this.data.get('eipBlackList');
        return u.map(data, item => {
            let {bindEip} = checker.check(rules, item, '', {eipBlackList});
            return {
                ...item,
                bindEip,
                disableFlag: !!(item?.enableBgp && item?.vpnConns?.length),
                enableProduct: this.checkEnableProduct(item)
            };
        });
    }
    checkEnableProduct(item) {
        return (
            u.indexOf([VpnStatus.ACTIVE, VpnStatus.UN_CONFIG, VpnStatus.UPDATING], item.status) > -1 &&
            item.productType === PayType.PREPAY &&
            item.orderProductPayType === 'to_postpay'
        );
    }
    onRecharge() {
        let selectedItems = this.data.get('table.selectedItems');
        let ids = u.pluck(selectedItems, 'vpnId');
        recharge('VPN', ids);
    }
    bindEip(item) {
        let dialog = new EipBindDialog({
            data: {
                listRequester: this.$http.getEipBindList.bind(this.$http),
                submitRequester: this.$http.vpnEipBind.bind(this.$http),
                EipSDK: new EipSDK({client: window.$http, context: window.$context}),
                instanceType: 'VPN',
                instanceId: item.vpnId
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => this.loadPage());
        dialog.on('create', e => {
            e.params = e.query;
            e.paramSeperator = '~';
            redirect(e);
        });
    }
    bindEipInternalIp(item) {
        let dialog = new InternalIpBindDialog({
            data: {
                instanceType: 'VPN',
                instanceId: item.vpnId,
                subnetId: item.subnetId,
                vpcId: item.vpcId
            }
        });
        dialog.attach(document.body);
        dialog.on('confirm', () => {
            this.loadPage();
        });
    }
    unbindEip(item) {
        let confirm = new Confirm({
            data: {
                title: '解绑EIP',
                content: `公网类型VPN需要配合EIP才能正常工作。您确认解绑${item.eip}吗？`
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http
                .vpnEipUnbind({
                    eip: '',
                    instanceType: 'VPN',
                    instanceId: item.vpnId
                })
                .then(() => this.loadPage());
        });
    }
    unbindInternalIp(item) {
        let confirm = new Confirm({
            data: {
                title: '解绑私网IP',
                content: `您确认要解绑私网IP${item.internalIp}吗？`
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http
                .vpnEipUnbind({
                    internalIp: '',
                    instanceType: 'VPN',
                    instanceId: item.vpnId
                })
                .then(() => this.loadPage());
        });
    }
    resetTable() {
        this.data.set('table.selection', {
            mode: 'multi',
            selectedIndex: []
        });
        this.data.set('table.selectedItems', []);
    }
    refresh() {
        this.loadPage();
    }
    setTableHeader() {
        const region = window.$context.getCurrentRegionId();
        let allColumns = this.data.get('table.allColumns');
        const vpnType = this.data.get('context').vpnType;
        const addNewItem = {
            name: 'netType',
            label: '网关类型',
            minWidth: 100
        };
        let findIndex = u.findIndex(allColumns, item => item.name === 'vpcId');
        const isIpsec = this.data.get('vpnType') === 'ipsec';
        const isGre = this.data.get('vpnType') === 'gre';
        if (!disable_vpn_flavor_region.includes(region)) {
            const addItem = {
                name: 'flavor',
                label: '规格',
                minWidth: 60
            };
            this.data.splice('table.allColumns', [findIndex, 0, addItem]);
            this.data.splice('customColumn.value', [findIndex, 0, 'flavor']);
            this.data.splice('customColumn.datasource', [
                findIndex,
                0,
                {
                    text: '规格',
                    value: 'flavor'
                }
            ]);
            if (isIpsec) {
                // ipsec展示网关类型和本段私网IP
                this.data.splice('table.allColumns', [findIndex + 1, 0, addNewItem]);
                this.data.splice('customColumn.value', [findIndex + 1, 0, 'netType']);
                this.data.splice('customColumn.datasource', [
                    findIndex + 1,
                    0,
                    {
                        text: '网关类型',
                        value: 'netType'
                    }
                ]);
            }
        } else {
            if (isIpsec) {
                this.data.splice('table.allColumns', [findIndex, 0, addNewItem]);
                this.data.splice('customColumn.value', [findIndex, 0, 'netType']);
                this.data.splice('customColumn.datasource', [
                    findIndex,
                    0,
                    {
                        text: '网关类型',
                        value: 'netType'
                    }
                ]);
            }
        }
        const bgpSwitch = {
            name: 'enableBgp',
            label: 'BGP功能开关',
            width: 200
        };
        const customBgp = {
            text: 'BGP功能开关',
            value: 'enableBgp'
        };
        // ipsec、gre模式展示本端私网IP
        if (isIpsec || isGre) {
            let allColumns = this.data.get('table.allColumns');
            let findNewIndex = u.findIndex(allColumns, item => item.name === 'remoteSideIp');
            const addItem = {
                name: 'internalIp',
                label: '本端私网IP',
                width: 150
            };
            this.data.splice('table.allColumns', [findNewIndex + 1, 0, addItem]);
            this.data.splice('customColumn.value', [findNewIndex + 1, 0, 'internalIp']);
            this.data.splice('customColumn.datasource', [
                findNewIndex + 1,
                0,
                {
                    text: '本端私网IP',
                    value: 'internalIp'
                }
            ]);
        }
        if (vpnType === 'ssl') {
            // ssl-vpn不展示隧道数量
            let customData = this.data.get('customColumn.datasource').filter(item => item.value !== 'vpnConnNum');
            let customVal = this.data.get('customColumn.value').filter(item => item !== 'vpnConnNum');
            allColumns = this.data.get('table.allColumns').filter(item => item.name !== 'vpnConnNum');
            this.data.set('customColumn.datasource', customData);
            this.data.set('customColumn.value', customVal);
            this.data.set('table.allColumns', allColumns);
        }
        // gre显示网络吞吐带宽, 不显示规格
        if (vpnType === 'gre') {
            let customData = this.data.get('customColumn.datasource').filter(item => item.value !== 'flavor');
            let customVal = this.data.get('customColumn.value').filter(item => item !== 'flavor');
            allColumns = this.data.get('table.allColumns').filter(item => item.name !== 'flavor');
            this.data.set('customColumn.datasource', customData);
            this.data.set('customColumn.value', customVal);
            this.data.set('table.allColumns', allColumns);
            this.data.splice('table.allColumns', [
                findIndex,
                0,
                {
                    name: 'greBandwidth',
                    label: 'VPN网关规格',
                    width: 100
                }
            ]);
            this.data.splice('customColumn.value', [findIndex, 0, 'greBandwidth']);
            this.data.splice('customColumn.datasource', [
                findIndex,
                0,
                {
                    text: 'VPN网关规格',
                    value: 'greBandwidth'
                }
            ]);
            this.data.splice('table.allColumns', [
                findIndex + 1,
                0,
                {
                    name: 'netType',
                    label: '网关类型',
                    width: 80
                }
            ]);
            this.data.splice('customColumn.value', [findIndex, 0, 'netType']);
            this.data.splice('customColumn.datasource', [
                findIndex + 1,
                0,
                {
                    text: '网关类型',
                    value: 'netType'
                }
            ]);

            // bgp功能开关，暂时注释掉，后续可能开启
            // const remoteSideIndex = u.findIndex(this.data.get('table.allColumns'), item => item.name === 'remoteSideIp');
            // this.data.splice('table.allColumns', [remoteSideIndex+1, 0, bgpSwitch])
            // this.data.splice('customColumn.value', [remoteSideIndex+1, 0, 'enableBgp']);
            // this.data.splice('customColumn.datasource', [remoteSideIndex+1, 0, customBgp]);
        }
    }
    // 设置表格列
    setTableColumns(customColumnNames) {
        customColumnNames = customColumnNames ? customColumnNames : this.data.get('customColumn.value');
        let allColumns = this.data.get('table.allColumns');
        // ssl、gre只支持预付费,去掉支付方式的筛选
        if (this.data.get('context').vpnType !== 'ipsec') {
            allColumns = allColumns.map(item => {
                if (item.name === 'productType') {
                    return {
                        name: item.name,
                        label: item.label,
                        width: item.width
                    };
                }
                return item;
            });
        }
        let columns = [];
        allColumns.forEach(item => customColumnNames.indexOf(item.name) > -1 && columns.push(item));
        this.data.set('table.columns', columns);
    }
    initColumns(value) {
        this.setTableColumns(value);
    }
    // 自定义表格列
    onCustomColumns(value) {
        this.data.set('customColumn.value', value);
        this.setTableColumns(value);
    }
    tableSelected(e) {
        this.data.set('table.selectedItems', e.value.selectedItems);
        let {recharge, RELEASE} = checker.check(rules, e.value.selectedItems, '');
        this.data.set('recharge', recharge);
        this.data.set('sslRelease', RELEASE);
        this.setOperationMessage();
    }
    // 当点开其他展开项时收起之前的展开项
    onRowExpand(e) {
        const {rowIndex} = e.value;
        this.data.set('expandIndex', [rowIndex]);
    }
    setOperationMessage() {
        let selectedItem = this.data.get('table.selectedItems');
        let checkResult = checker.check(rules, selectedItem, '');
        let OperationType = u.cloneDeep(this.data.get('OperationType'));
        const accountState = window.$storage.get('accountState');
        const {disabled, message} = accountState;
        OperationType.forEach(item => {
            if (checkResult[item.value]) {
                item.disabled = checkResult[item.value].disable;
                item.message = checkResult[item.value].message;
                if (item.value === 'RELEASE') {
                    this.data.set('vpnRelease', {
                        disabled: checkResult[item.value].disable,
                        message: checkResult[item.value].message
                    });
                }
                if (item.value === 'ALTER_PRODUCTTYPE' && !item.disabled && disabled) {
                    item.disabled = true;
                    item.message = message;
                }
            }
        });
        this.data.set('OperationType', OperationType);
    }
    onOperationChange(e) {
        const methodMap = {
            RELEASE: this.onRelease,
            ALTER_PRODUCTTYPE: this.alterProduct,
            CANCEL_ALTER_PRODUCTTYPE: this.cancelAlterProduct
        };
        let requester = methodMap[e.value].bind(this);
        requester();
    }
    onRelease() {
        let selectedItems = this.data.get('table.selectedItems');
        const vpnId = selectedItems[0].vpnId;
        let confirm = new Confirm({
            data: {
                title: '释放前确认',
                content: '确定释放此VPN网关？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http.releaseVpn(vpnId).then(() => this.loadPage());
        });
    }
    alterProduct() {
        let selectedItems = this.data.get('table.selectedItems');
        let ids = u.pluck(selectedItems, 'vpnId');
        let isPrepay = selectedItems[0].productType === PayType.PREPAY;
        let type = isPrepay ? 'TO_POSTPAY' : 'TO_PREPAY';
        let url = isPrepay ? '/api/vpn/vpn/order/confirm/toPostPay' : '/api/vpn/vpn/order/confirm/toPrePay';
        alterProductType('VPN', ids, type, null, url);
    }
    cancelAlterProduct(e) {
        let selectedItems = this.data.get('table.selectedItems');
        let ids = u.pluck(selectedItems, 'vpnId');
        let confirm = new Confirm({
            data: {
                title: '取消计费变更',
                content:
                    '确认取消计费变更？<br>您已开通计费变更-预付费转后付费功能。<br>实例将会在到期后自动转换为后付费的计费方式。'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http.vpnCancelAlterProductType({ids}).then(() => {
                Notification.success('取消计费变更成功');
                this.loadPage();
            });
        });
    }
    // 搜索事件
    onSearch(e) {
        this.data.set('pager.page', 1);
        this.loadPage();
    }
    // 改变页数
    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }
    // 改变每页显示数量
    onPagerSizeChange(e) {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', e.value.pageSize);
        this.loadPage();
    }
    // 点击修改icon
    onInstantEdit(row, rowIndex, type) {
        this.data.set(`edit.${type}.value`, row[`${type}`]);
        this.data.set(`edit.${type}.error`, false);
        const popWrap = this.ref(`popover-${type}-${rowIndex}`);
        popWrap.data.set('visible', !popWrap.data.get('visible'));
    }
    // 编辑弹框-输入名称/描述
    onEditInput(e, rowIndex, type) {
        let result =
            type === 'vpnName' ? e.value === '' || !/^[a-zA-Z][\w\-\_\/\.]{0,64}$/.test(e.value) : e.value.length > 200;
        this.data.set(`edit.${type}.error`, result);
        this.data.set(`edit.${type}.value`, e.value);
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', result);
    }
    // 编辑弹框-提交
    editConfirm(row, rowIndex, type) {
        let edit = this.data.get(`edit.${type}`);
        if (edit.error) {
            return;
        }
        const key = type === 'vpnName' ? 'description' : 'vpnName';
        this.$http
            .vpnUpdate({
                vpcId: row.vpcId,
                vpnId: row.vpnId,
                [type]: edit.value,
                [key]: row[`${key}`]
            })
            .then(() => {
                this.editCancel(rowIndex, type);
                Notification.success('修改成功');
                this.loadPage();
            });
    }
    // 编辑弹框-取消
    editCancel(rowIndex, type) {
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', true);
        this.ref(`popover-${type}-${rowIndex}`).data.set('visible', false);
    }
    // 排序
    onSort(e) {
        this.data.set('order', e.value);
        this.loadPage();
    }
    // 筛选
    onFilter(e) {
        let name = e.field.name;
        let value = e[name] ? e[name] : '';
        this.data.set('filters.' + name, value);
        this.data.set('pager.page', 1);
        this.loadPage();
    }
    onCreate() {
        let vpcId = window.$storage.get('vpcId');
        location.hash = `#/vpc/vpn/create?vpcId=${vpcId}&vpnType=${this.data.get('context').vpnType}`;
    }
    showMonitor(item) {
        let dialog = new Monitor({
            data: {
                instance: item
            }
        });
        dialog.attach(document.body);
    }
    showExpireData() {
        let showMode = this.data.get('showMode');
        showMode = showMode ? '' : 'WILLEXPIRED';
        this.data.set('showMode', showMode);
        this.loadPage();
    }
    onRegionChange() {
        location.reload();
    }
    getIamQuery() {
        this.$http.getInterfaceIam({interfaceName: 'createVpn'}).then(res => {
            let message = '';
            !res.interfacePermission && (message += '创建VPN网关权限');
            !res.createOrderPermission && (message += message ? '、创建订单权限' : '创建订单权限');
            !res.payOrderPermission && (message += message ? '、支付订单权限' : '');
            if (!res.requestId && !res.masterAccount) {
                if (message) {
                    this.data.set('iamPass', {
                        disable: true,
                        message: `您没有${message}，请联系主用户添加`
                    });
                } else {
                    this.data.set('iamPass', {
                        disable: false,
                        message: ''
                    });
                }
            } else {
                this.data.set('iamPass', {
                    disable: false,
                    message: ''
                });
            }
        });
    }
    // 下载
    onDownload() {
        let ids = [];
        ids = this.data.get('table.selectedItems').map(item => {
            return item.vpnId;
        });
        let filter = 'ids=' + ids.join(',');
        window.open(`/api/vpn/download?` + filter);
    }
    /**
     * @description 介绍卡片变化时回调
     * @param {boolean} flag
     */
    handleToggle(flag) {
        this.data.set('show', flag);
        window.$storage.set('showVpnIntroduce', false);
        this.data.set('introduceIcon', INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#151B26';
    }
    handleShowCard() {
        if (!this.data.get('show')) {
            this.data.set('show', true);
            window.$storage.set('showVpnIntroduce', true);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type) {
        if (type === 'introduce') {
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
            const ele = this.data.get('introduceEle');
            ele.style.color = '#2468F2';
        } else {
            this.data.set('documentIcon', DOCUMENT_ICON_COLOR);
        }
    }
    handleMouseLeave(type) {
        const isShow = this.data.get('show');
        if (type === 'introduce') {
            this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
            const ele = this.data.get('introduceEle');
            !isShow && (ele.style.color = '#151B26');
        } else {
            this.data.set('documentIcon', DOCUMENT_ICON);
        }
    }
    isShowMonitor(row) {
        const {netType} = row;
        let flag = true;
        if (netType === 'intranet') {
            flag = false;
        }
        return flag;
    }
    isShowRouteManage(row) {
        const {vpnConns} = row;
        const isShow = vpnConns.some(it => it.healthStatus === 'reachable');
        const vpnType = this.data.get('vpnType');
        return isShow && ['ipsec', 'gre'].includes(vpnType);
    }
    handleRouteManage(row: Record<string, any>) {
        const {vpcId, vpnId, vpnConns} = row;
        const showRoute = vpnConns.some(it => it.healthStatus === 'reachable');
        const vpnType = this.data.get('vpnType');
        location.hash = `#/vpc/vpn/route?vpcId=${vpcId}&vpnId=${vpnId}&vpnType=${vpnType}&showRoute=${showRoute}`;
    }

    // 删除vpn隧道时更新vpn列表
    handleUpdateList() {
        this.loadPage();
    }
    // 编辑资源分组
    changeResourceGroup(row) {
        let resource = {
            resourceId: row.vpnId,
            serviceType: 'VPN'
        };
        this.data.set('resource', resource);
        this.data.set('showResource', true);
    }
    // 编辑资源分组确定后
    onCommit() {
        this.data.set('showResource', false);
        this.loadPage();
    }
    // 编辑资源分组取消后
    onCancel() {
        this.data.set('showResource', false);
    }
    showDiagnose(e) {
        this.data.set('openDrawerVpnDiagnose', true);
        const confirm = new DiagnoseConfirm({
            data: {
                title: '发起诊断',
                instanceId: e.vpnId,
                instanceType: 'vpn',
                region: e.region || window.$context.getCurrentRegionId()
            }
        });
        confirm.on('confirm', () => {
            this.loadVpnDiagnose(e);
        });
        confirm.on('close', () => {
            this.nextTick(() => {
                this.data.set('openDrawerVpnDiagnose', false);
                this.data.set('drawerVisibleVpnDiagnose', false);
                this.data.set('visibleDrawVpnDiagnose', false);
            });
        });
        confirm.attach(document.body);
    }
    loadVpnDiagnose(e) {
        let domType =
            'vpnDiagnoseDom' +
            (this.data.get('vpnType') === 'ipsec' ? 'Ipsec' : this.data.get('vpnType') === 'ssl' ? 'Ssl' : 'Gre');
        this.data.set('openDrawerVpnDiagnose', true);
        this.nextTick(() => {
            this.data.set('drawerVisibleVpnDiagnose', true);
            this.data.set('visibleDrawVpnDiagnose', true);
            if (document.getElementById(domType)) {
                this.loadDiagnoseDrawer(e, domType);
            } else {
                this.loadVpnDiagnose(e);
            }
        });
    }
    loadDiagnoseDrawer(row, domType) {
        const {vpnConns} = row;
        const isShow = vpnConns.some(it => it.healthStatus === 'reachable');
        const vpnType = this.data.get('vpnType');
        this.nextTick(() => {
            processor.applyComponent(
                'DiagnoseDrawer',
                {
                    diagnoseData: {
                        instanceType: 'vpn',
                        vpcId: row.vpcId,
                        vpnId: row.vpnId,
                        instanceId: row.vpnId,
                        region: row.region || window.$context.getCurrentRegionId(),
                        vpnType,
                        showRoute: isShow && ['ipsec', 'gre'].includes(vpnType)
                    },
                    http: request,
                    visible: this.data.get('visibleDrawVpnDiagnose'),
                    isIpsec: vpnType === 'ipsec',
                    instanceType: 'vpn',
                    extraData: '{}',
                    onCloseDrawer: () => {
                        this.data.set('openDrawerVpnDiagnose', false);
                        this.data.set('drawerVisibleVpnDiagnose', false);
                        this.data.set('visibleDrawVpnDiagnose', false);
                    }
                },
                '#' + domType
            );
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(VpnList));
