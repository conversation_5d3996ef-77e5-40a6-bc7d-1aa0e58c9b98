import {defineComponent} from 'san';
import {Select, Popover, Button, Tooltip, Dropdown, Menu} from '@baidu/sui';
import {html} from '@baiducloud/runtime';

export default defineComponent({
    template: html`<template>
        <s-dropdown class="more-opt" trigger="hover">
            <s-menu slot="overlay">
                <s-menu-item disabled="{{item.disabled}}" s-for="item in datasource">
                    <s-tooltip content="{{item.tip}}">
                        <s-button skin="stringfy" on-click="buttonClick(item.value)" disabled="{{item.disabled}}">
                            {{item.text}}
                        </s-button>
                    </s-tooltip>
                </s-menu-item>
            </s-menu>
            <s-button skin="stringfy">客户端软件下载</s-button>
        </s-dropdown>
    </template>`,
    components: {
        's-dropdown': Dropdown,
        's-select': Select,
        's-popover': Popover,
        's-button': Button,
        's-menu': Menu,
        's-menu-item': <PERSON>u.Item,
        's-tooltip': Tooltip
    },
    computed: {
        datasource() {
            let operation = [
                {
                    text: '安卓（Android）',
                    value: 'Android'
                },
                {
                    text: '微软（Windows）',
                    value: 'Windows'
                },
                {
                    text: '苹果（MACOS）',
                    value: 'Macos'
                }
            ];
            return operation;
        }
    },
    buttonClick(type) {
        switch (type) {
            case 'Android': {
                window.open('https://sslvpn-client.bj.bcebos.com/openvpn-connect-3-3-2.apk');
                break;
            }
            case 'Windows': {
                window.open('https://sslvpn-client.cdn.bcebos.com/openvpn-connect-3.5.1.3946_signed.msi');
                break;
            }
            case 'Macos': {
                window.open('https://sslvpn-client.bj.bcebos.com/openvpn-connect-3.4.2.4547_signed.dmg');
                break;
            }
        }
    }
});
