import {PayType, VpnStatus} from '@/pages/sanPages/common/enum';
export const columns = [
    {
        name: 'id',
        label: 'VPN网关名称/ID',
        width: 170
    },
    {
        name: 'status',
        label: '状态',
        width: 90,
        filter: {
            options: [
                {
                    text: '全部',
                    value: ''
                },
                ...VpnStatus.toArray()
            ],
            value: ''
        }
    },
    {
        name: 'diagnose',
        label: '诊断',
        width: 160
    },
    {
        name: 'vpcId',
        label: '所在网络',
        width: 180
    },
    {
        name: 'productType',
        label: '支付方式',
        width: 120,
        filter: {
            options: [
                {
                    text: '全部',
                    value: ''
                },
                {
                    text: PayType.getTextFromAlias('PREPAY'),
                    value: PayType.PREPAY
                },
                {
                    text: PayType.getTextFromAlias('POSTPAY'),
                    value: PayType.POSTPAY
                }
            ],
            value: ''
        }
    },
    {
        name: 'remoteSideIp',
        label: 'VPN云端公网IP/带宽',
        width: 200
    },
    {
        name: 'vpnConnNum',
        label: '隧道数量',
        width: 80
    },
    {
        name: 'description',
        label: '描述',
        width: 140
    },
    {
        name: 'expiredTime',
        label: '到期时间',
        width: 160
    },
    {
        name: 'tag',
        label: '标签',
        sortable: true,
        width: 100
    },
    {
        name: 'groups',
        label: '资源分组',
        width: 100
    },
    {
        name: 'opt',
        label: '操作',
        width: 140
    }
];
