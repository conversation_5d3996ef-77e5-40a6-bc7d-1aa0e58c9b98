import {Component} from 'san';
import u from 'lodash';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import {BillingSdk, OrderItem, UPDATE_STATUS} from '@baiducloud/bce-billing-sdk';
import {OrderConfirm, ShoppingCart, TotalPrice, Protocol} from '@baiducloud/bce-billing-sdk-san';
import Client from '@baiducloud/httpclient';
import {ResourceGroupPanel} from '@baidu/bce-resourcegroup-sdk-san';
import {ResourceGroupSDK} from '@baidu/bce-resourcegroup-sdk';
import {TagEditPanel} from '@baidu/bce-tag-sdk-san';
import {Input} from '@baidu/sui';
import {OutlinedQuestion} from '@baidu/sui-icon';
import {getConfirmConfig} from './helper';
import Assist from '@/utils/assist';
import {disable_vpn_flavor_region} from '@/pages/sanPages/common/flag';
import {PayType, vpnFlavor, TimeType, Year, Month, vpnType as vpnTypeList} from '@/pages/sanPages/common/enum';
import {DocService} from '@/pages/sanPages/common';
import testID from '@/testId';

import {
    convertPrice,
    $flag as FLAG,
    contextPipe,
    showMoney,
    getVPCSupportRegion,
    decryptUrlBtoa
} from '@/pages/sanPages/utils/helper';
import zone from '@/pages/sanPages/utils/zone';

import './style.less';

const {asPage, invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent} = decorators;
const kXhrOptions = {'X-silence': true};
const AllRegion = window.$context.getEnum('AllRegion');
const rules = {
    vpnName: [
        {required: true, message: '名称必填'},
        {
            validator(rule, value, callback) {
                let pattern = /^[a-zA-Z][\w\-\/\.]{0,64}$/;
                if (!pattern.test(value)) {
                    return callback('以字母开头，支持大小写字母、数字以及 -_/. 特殊字符');
                }
                callback();
            }
        }
    ],
    greBandwidth: [{required: true}],
    vpcId: [{required: true}],
    subnetId: [{required: true, message: '请选择子网'}],
    netType: [{required: true}]
};

const vpnTypeMap = {
    ipsec: 'IPsec VPN网关',
    ssl: 'SSL VPN网关',
    gre: 'GRE VPN网关'
};

const tpl = html`
<div>
    <s-app-create-page class="{{klass}}" backTo="{{pageTitle.backTo}}"
    backToLabel="{{pageTitle.label}}" pageTitle="{{pageTitle.title}}">
        <div class="s-step-block">
            <s-steps current="{{steps.current}}">
                <s-steps-step s-for="i in steps.datasource" title="{{i.title}}"/>
            </s-steps>
        </div>
        <div class="content-wrap" s-if="{{steps.current === 1}}">
            <s-form s-ref="form"
                label-align="left"
                data="{=formData=}" rules="{{rules}}">
                <div class="content-wrap-box form-part-wrap">
                    <h4>基本信息</h4>
                    <s-form-item label="付费方式：" prop="productType">
                        <div class="row-line">
                            <ul class="row-line-wrapper">
                                <li s-for="item in productTypeList"
                                    class="row-line-item  {{item.value === formData.productType ? 'row-line-selected' : ''}}"
                                    on-click="onSelectPayType(item)">
                                    <div class="row-line-version">
                                        <img class="img" src="{{item.imgSrc}}" alt=""/>
                                        <div class="row-line-widget">
                                            <p class="row-line-name {{item.value === formData.productType ? 'row-line-name-selected' : ''}}">{{item.text}}</p>
                                            <p class="row-line-desc">{{item.desc}}</p>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </s-form-item>
                    <s-form-item label="当前地域：" prop="region" class="create-region s-form-item-region">
                        <div slot="label" class="label_class ">
                            {{'当前地域：'}}
                            <s-tip
                                placement="top"
                                class="inline-tip">
                                <s-question class="question-class warning-class"></s-question>
                                <span slot="content">
                                    如需修改购买其他区域产品，请{{!FLAG.NetworkSupportXS ? '前往主导航进行切换': '在顶栏重新选择区域'}}。
                                    <a class="assist-tip" href="javascript:void(0)" on-click="showAssist('region')" s-if="FLAG.NetworkSupportAI">了解详情</a>
                                </span>
                            </s-tip>
                        </div>
                        <div class="row-line">
                            <s-radio-radio-group
                                enhanced
                                datasource="{{regionList}}"
                                value="{=formData.region=}"
                                radioType="button"
                                on-change="onRegionChange"
                            >
                            </s-radio-radio-group>
                        </div>
                    </s-form-item>
                </div>
                <div class="content-wrap-box form-part-wrap">
                    <h4>配置信息</h4>
                    <div class="owner-network">
                        <s-form-item label="所在网络：" prop="vpcId">
                            <s-select
                                width="300"
                                value="{=formData.vpcId=}"
                                on-change="vpcChange"
                            >
                                <s-select-option
                                    s-for="item in vpcList"
                                    value="{{item.value}}"
                                    label="{{item.text}}"
                                >
                                    <s-tooltip>
                                        <div slot="content">
                                            {{item.text}}
                                        </div>
                                        <div>{{item.text}}</div>
                                    </s-tooltip>
                                </s-select-option>
                            </s-select>
                            <div class="subnetId-wrapper">
                                <s-form-item label="" prop="subnetId">
                                    <s-select width="{{300}}"
                                        disabled="{{loading}}"
                                        value="{=formData.subnetId=}"
                                        on-change="handleSubnetChange"
                                        data-test-id="${testID.vpn.createSelectSubnet}"
                                    >
                                        <s-select-option
                                            s-for="item, index in subnetDatasource"
                                            value="{{item.value}}"
                                            label="{{item.text}}"
                                            disabled="{{item.soldoutTip}}"
                                            data-test-id="${testID.vpn.createSelectSubnetOption}{{index}}"
                                        >
                                            <s-tooltip>
                                                <div slot="content">
                                                  <span s-if="item.soldoutTip">
                                                      {{'当前子网对应的可用区实例资源售罄，请选择其他可用区的子网或创建新的可用区'}}
                                                      <a href="#/vpc/subnet/list">{{'子网'}}</a>
                                                  </span>
                                                  <span s-else>{{item.text}}</span>
                                                </div>
                                                <div>{{item.text}}</div>
                                            </s-tooltip>
                                        </s-select-option>
                                    </s-select>
                                    <div s-if="formData.subnetId" slot="help">
                                        <div class="zone-tip">
                                            {{'当前' + currZone + '，可用IP共' + availableIPs + '个；' + '如需创建子网，您可以到'}}
                                            <a s-if="!FLAG.NetworkSupportXS" href="#/vpc/subnet/list" target="_blank">私有网络-子网</a>
                                            <span s-else>私有网络-子网</span>
                                            {{'去创建'}}
                                        </div>
                                    </div>
                                </s-form-item>
                            </div>
                        </s-form-item>
                    </div>
                    <s-form-item label="VPN网关名称：" prop="vpnName"
                        help="以字母开头，支持大小写字母、数字以及 -_/. 特殊字符"
                    >
                        <s-input
                            class="input-with-num"
                            on-input="handleNameInput"
                            value="{=formData.vpnName=}"
                            width="320"
                            data-test-id="${testID.vpn.createName}"
                        >
                        </s-input>
                        <span class="input-num-statistics">{{nameLength+'/'+'65'}}</span>
                    </s-form-item>
                    <s-form-item label="VPN网关规格：" prop="flavor" s-if="showFlavor">
                        <div slot="label" class="label_class">
                            {{'VPN网关规格：'}}
                        </div>
                        <div class="row-line flavor-line">
                            <ul class="row-line-wrapper">
                                <li s-for="item in flavorList"
                                    class="row-line-item row-line-item-gateway  {{item.value === formData.flavor ? 'row-line-selected' : ''}}"
                                    on-click="onSelectGateway(item)">
                                    <div class="row-line-version">
                                        <div class="row-line-widget">
                                            <p class="row-line-name {{item.value === formData.flavor ? 'row-line-name-selected' : ''}}">{{item.text}}</p>
                                            <p class="row-line-desc">{{item.desc}}</p>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </s-form-item>
                    <s-form-item label="VPN网关规格" prop="greBandwidth" s-if="vpnType === 'gre'">
                        <div slot="label" class="label_class">
                            {{'VPN网关规格：'}}
                            <s-tip
                              class="inline-tip"
                              content="{{greBandwidthMessage}}"
                              skin="question"
                              />
                        </div>
                        <div class="row-line flavor-line">
                            <s-radio-radio-group
                                enhanced
                                datasource="{{greBandwidthList}}"
                                value="{=formData.greBandwidth=}"
                                radioType="button"
                                on-change="greBandwidthChange($event)"
                            >
                            </s-radio-radio-group>
                        </div>
                    </s-form-item>
                    <s-form-item s-if="vpnType === 'ssl'" label="SSL连接数：" prop="maxClient" class="buy-time">
                        <div class="row-line flavor-line">
                            <s-radio-radio-group
                                enhanced
                                datasource="{{maxClientList}}"
                                value="{=formData.maxClient=}"
                                radioType="button"
                            >
                            </s-radio-radio-group>
                        </div>
                    </s-form-item>
                    <s-form-item data-test-id="${testID.vpn.createGatewayType}" label="网关类型：" prop="netType" s-if="isShowGatewayType">
                        <div slot="label" class="label_class">
                            {{'网关类型：'}}
                            <s-tip
                                class="inline-tip"
                                placement="top"
                                content="{{ipsecVpnMessage}}"
                                skin="question"
                                />
                        </div>
                        <div class="row-line flavor-line">
                            <s-radio-radio-group
                                enhanced
                                datasource="{{netTypeList}}"
                                value="{=formData.netType=}"
                                radioType="button"
                            >
                            </s-radio-radio-group>
                        </div>
                    </s-form-item>
                    <s-form-item label="VPN云端公网IP：" prop="eip" s-if="formData.netType === 'internet'">
                        <s-select
                            width="300"
                            on-change="eipChange"
                            datasource="{{eipList}}"
                            value="{=formData.eip=}"
                        >
                        </s-select>
                    </s-form-item>
                    <s-form-item label="BGP功能开关：" prop="switch" s-if="isShowBgp" class="bgp-wrapper">
                        <s-switch checked="{=formData.enableBgp=}"/>
                    </s-form-item>
                    <s-form-item label=" " s-if="isShowBgp" class="margin-top-zero">
                        <div slot='help'>{{'开启后该网关下的所有VPN隧道均为路由模式，仅支持BGP。'}}</div>
                    </s-form-item>
                    <s-form-item label="VPN描述：" prop="description">
                        <s-textarea
                            value="{=formData.description=}"
                            placeholder="描述不能超过200字符"
                            width="400"
                            height="88"
                            maxLength="200"
                        ></s-textarea>
                    </s-form-item>
                </div>
                <div class="content-wrap-box form-part-wrap">
                    <resource-group-panel refreshAvailable="{{true}}" sdk="{{resourceSDK}}" on-change="resourceChange($event)" />
                </div>
                <div class="content-wrap-box form-part-wrap tag-wrap">
                    <h4 class="title_vpc">标签</h4>
                    <s-form-item prop="tag" s-if="!FLAG.NetworkVpcSupOrganization" label="绑定标签：">
                        <tag-edit-panel
                            s-ref="tagPanel"
                            instances="{{defaultInstances}}"
                            options="{{tagListRequster}}"
                        />
                    </s-form-item>
                </div>
                <div class="content-wrap-box form-part-wrap">
                    <h4>购买信息</h4>
                    <s-form-item s-if="{{formData.productType === 'prepay'}}" label="购买时长：" prop="purchaseLength" class="buy-time">
                        <s-tag-radio-group
                            enhanced
                            class="purchase-btn region-radio-group"
                            radioType="button"
                            value="{=formData.purchaseLength=}"
                            track-id="ti_eip_group_create"
                            datasource="{{purchaseLengthList}}"
                            track-name="购买时长">
                        </s-tag-radio-group>
                    </s-form-item>
                    <s-form-item s-if="{{formData.productType === 'prepay' && !FLAG.NetworkSupportXS}}" label="自动续费：" prop="purchaseLength" class="renew-item-label">
                        <div slot="label" class="label_class">
                            {{'自动续费：'}}
                            <s-tip
                                placement="top"
                                class="inline-tip">
                                <s-question class="question-class warning-class"></s-question>
                                <span slot="content">
                                    开通自动续费后，百度智能云将在实例到期前7/3/1/0天定时进行自动扣款续费，续费成功或失败都将向您发送短信和邮件提醒。
                                    <a class="assist-tip" href="javascript:void(0)" on-click="showAssist()" s-if="FLAG.NetworkSupportAI">了解详情</a>
                                </span>
                            </s-tip>
                        </div>
                        <s-radio-radio-group
                            enhanced
                            class="renew-item-wrapper"
                            datasource="{{autoRenewDatasource}}"
                            radioType="button"
                            value="{=formData.autoRenew=}"
                        />
                        <div class="vpn-renew-wrap row-line" s-if="formData.autoRenew">
                            <span class="renew-title">选择续费周期</span>
                            <s-select
                                width="100"
                                on-change="onRenewUnitChange"
                                datasource="{{renewUnitList}}"
                                value="{=formData.autoRenewTimeUnit=}"
                            >
                            </s-select>
                            <s-select
                                style="margin-left:8px"
                                width="100"
                                on-change="onRenewTimeChange"
                                datasource="{{renewNumberList}}"
                                value="{=formData.autoRenewTime=}"
                            >
                            </s-select>
                            <span class="renew-tip">
                                系统将于到期前7天进行扣费，扣费时长为
                                {{formData.autoRenewTime}}{{formData.autoRenewTimeUnit==='month'?'月':'年'}}
                            </span>
                        </div>
                    </s-form-item>
                    <s-form-item label="释放保护：" class="renew-item-label">
                        <s-radio-radio-group
                            enhanced
                            class="renew-item-wrapper"
                            datasource="{{deleteProtectList}}"
                            radioType="button"
                            value="{=deleteProtect=}"
                        />
                    </s-form-item>
                </div>
            </s-form>
        </div>
        <div s-else class="order-confirm-panel">
            <order-confirm
                s-ref="orderConfirm"
                sdk="{{newBillingSdk}}"
                useCoupon="{{useCoupon}}"
                theme="default"
                showAgreementCheckbox
                couponMergeBy="{{false}}"
                mergeBy="{{false}}"
            />
        </div>
        <div class="buybucket" slot="pageFooter">
            <div class="buybucket-container" s-if="steps.current === 1">
                <s-popover trigger="{{updating ? 'hover' : ''}}" class="popover-class">
                    <div slot="content">
                    <!--bca-disable-next-line-->
                        {{disableTip | raw}}
                    </div>
                    <s-button data-test-id="${testID.vpn.createNextBtn}" on-click="goToConfirm" skin="primary" size="large"
                        class="confirm-btn" disabled="{{updating}}">
                        下一步
                    </s-button>
                </s-popover>
                <s-popover trigger="{{updating ? 'hover' : ''}}" class="popover-class">
                    <div slot="content">
                    <!--bca-disable-next-line-->
                        {{disableTip | raw}}
                    </div>
                    <s-button
                        s-if="{{formData.productType === 'prepay' && !FLAG.NetworkSupportXS}}"
                        on-click="addShoppingCart"
                        disabled="{{cartConfirming || updating}}"
                        size="large">
                        加入购物车
                    </s-button>
                </s-popover>
                <s-button size="large" on-click="cancel">取消</s-button>
                <shopping-cart
                    sdk="{{newBillingSdk}}"
                    addItemToCartAvailable="{{addItemToCartAvailable}}"
                    addItemToCartDisable="{=priceLoading=}"
                    on-change="onShoppingCartChange"
                    theme="default"
                    on-reset="onReset"
                />
            </div>
            <div class="buybucket-container" s-else>
                <div class="buybucket-container-protocol">
                    <billing-protocol s-if="!FLAG.NetworkSupportXS" s-ref="billingProtocol" />
                    <s-button on-click="backToOrder" class="confirm-btn no-mg-bt" size="large">上一步</s-button>
                    <s-button size="large" on-click="cancel">取消</s-button>
                    <s-button data-test-id="${testID.vpn.createSubmitBtn}" skin="primary" size="large"
                      on-click="onConfirm" disabled="{{confirming}}">提交订单</s-button>
                </div>
                <total-price sdk="{{newBillingSdk}}" />
            </div>
        </div>
    </s-app-create-page>
</template>
`;
@asComponent('@vpn-create')
@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class VpnCreate extends Component {
    static components = {
        'order-confirm': OrderConfirm,
        'shopping-cart': ShoppingCart,
        'resource-group-panel': ResourceGroupPanel,
        's-textarea': Input.TextArea,
        'total-price': TotalPrice,
        'billing-protocol': Protocol,
        'tag-edit-panel': TagEditPanel,
        's-question': OutlinedQuestion
    };

    static computed = {
        updating() {
            let formData = this.data.get('formData');
            let inEipBlack = this.data.get('inEipBlack');
            let isRegionHk = window.$context.getCurrentRegionId() === AllRegion.HK02;
            let verify = window.$context.isVerifyUser();
            let priceLoading = this.data.get('priceLoading');
            let accountPurchaseValidation = this.data.get('accountPurchaseValidation');
            let accountStatus = accountPurchaseValidation ? accountPurchaseValidation.status : false;
            let hasQuota = this.data.get('hasQuota');
            return (
                isRegionHk || !verify || !accountStatus || !hasQuota || inEipBlack || priceLoading || !formData.vpcId
            );
        },
        disableTip() {
            let verify = window.$context.isVerifyUser();
            let accountPurchaseValidation = this.data.get('accountPurchaseValidation');
            let hasQuota = this.data.get('hasQuota');
            let priceLoading = this.data.get('priceLoading');
            let formData = this.data.get('formData');
            if (window.$context.getCurrentRegionId() === AllRegion.HK02) {
                return '售罄！请您移步其他地域购买资源。';
            }
            if (!verify) {
                if (FLAG.NetworkSupportXS) {
                    return '温馨提示：您还没有实名认证，请先完成实名认证';
                }
                return `温馨提示：您还没有实名认证，请立即去<a target="_BLANK" href="/qualify/#/qualify/index">
                        认证</a>`;
            }
            if (accountPurchaseValidation && !accountPurchaseValidation.status) {
                return accountPurchaseValidation.failReason
                    ? accountPurchaseValidation.failReason +
                          '，请及时<a href="/finance/#/finance/account/recharge" target="_blank">充值</a>'
                    : '';
            }
            if (this.data.get('inEipBlack')) {
                return '当前操作需要安全部审批！';
            }
            if (!formData.vpcId) {
                return '请选择所在网络';
            }
            if (!hasQuota) {
                if (FLAG.NetworkSupportXS) {
                    return 'VPN配额不足';
                }
                return (
                    `VPN配额不足，` +
                    `<a href="/quota_center/#/quota/apply/create?serviceType=VPN` +
                    `&region=${window?.$context?.getCurrentRegionId()}&cloudCenterQuotaName=vpnQuota"` +
                    `target="_blank">去申请配额</a>`
                );
            }
            if (priceLoading) {
                return '价格加载中';
            }
            return '';
        },
        showFlavor() {
            const currentRegion = window.$context.getCurrentRegionId();
            if (currentRegion === AllRegion.HKG) {
                return true;
            }
            return !disable_vpn_flavor_region.includes(currentRegion);
        },
        pageTitle() {
            let vpnType = this.data.get('vpnType');
            if (vpnType === 'ipsec') {
                return {
                    backTo: '/network/#/vpc/vpn/list',
                    label: '返回',
                    title: '创建IPsec VPN网关'
                };
            } else if (vpnType === 'ssl') {
                return {
                    backTo: '/network/#/vpc/sslvpn/list',
                    label: '返回',
                    title: '创建SSL VPN网关'
                };
            }
            return {
                backTo: '/network/#/vpc/grevpn/list',
                label: '返回',
                title: '创建GRE VPN网关'
            };
        },
        isShowBgp() {
            const vpnType = this.data.get('vpnType');
            const netType = this.data.get('formData.netType');
            return ['ipsec', 'gre'].includes(vpnType) && netType === 'internet';
        },
        isShowGatewayType() {
            const vpnType = this.data.get('vpnType');
            return ['ipsec', 'gre'].includes(vpnType);
        }
    };

    initData() {
        return {
            FLAG,
            DocService,
            klass: 'vpn-create-wrap',
            steps: {
                datasource: [
                    {
                        title: '基本配置'
                    },
                    {
                        title: '确认订单'
                    }
                ],
                current: 1
            },
            purchaseLengthList: [
                {text: '1个月', value: 1},
                {text: '2个月', value: 2},
                {text: '3个月', value: 3},
                {text: '4个月', value: 4},
                {text: '5个月', value: 5},
                {text: '6个月', value: 6},
                {text: '7个月', value: 7},
                {text: '8个月', value: 8},
                {text: '9个月', value: 9},
                {text: '1年', value: 12, mark: '8.3折'},
                {text: '2年', value: 24, mark: '8.3折'},
                {text: '3年', value: 36, mark: '8.3折'}
            ],
            formData: {},
            productTypeList: [
                {
                    text: '包年包月',
                    value: 'prepay',
                    imgSrc: 'http://bce.bdstatic.com/network-frontend/prepay.png',
                    disabled: true,
                    desc: '先付费后使用，价格更低廉'
                },
                {
                    text: '按量付费',
                    value: 'postpay',
                    imgSrc: 'http://bce.bdstatic.com/network-frontend/postpay.png',
                    desc: '先使用后付费，按需开通'
                }
            ],
            renewNumberList: Month.toArray(),
            flavorList: vpnFlavor.toArray('NORMAL', 'HIGH'),
            renewUnitList: TimeType.toArray('MONTH', 'YEAR'),
            regionList: [
                {
                    label: window.$context.getCurrentRegion().label,
                    value: window.$context.getCurrentRegionId()
                }
            ],
            hasQuota: false, // VPN配额
            vpcList: [],
            buyBucketItems: [],
            flavorMessage: '普通型VPN网关，最大转发能力200Mbps',
            sdk: {},
            price: null,
            rules,
            resourceSDK: {},
            sslPrice: null,
            greBandwidthList: [
                {text: '小型', value: 2},
                {text: '中型', value: 5}
            ],
            subnetDatasource: [],
            ipsecVpnMessage: `公网指通过互联网建立加密隧道，需绑定百度智能云EIP产品。
                私网指基于专线接入ET的私网连接建立加密隧道，需确保本端VPN网关和对端VPN网关的IP地址网络可达。`,
            netTypeList: [
                {text: '公网', value: 'internet'},
                {text: '私网', value: 'intranet'}
            ],
            greBandwidthMessage: '最大转发能力200Mbps',
            autoRenewDatasource: [
                {
                    label: '开启',
                    value: true
                },
                {
                    label: '关闭',
                    value: false
                }
            ],
            defaultInstances: [
                {
                    tags: [
                        {
                            tagKey: '默认项目',
                            tagValue: ''
                        }
                    ]
                }
            ],
            tagListRequster: this.tagListRequster.bind(this),
            nameLength: 0,
            currZone: '',
            availableIPs: 0,
            priceLoading: true,
            confirmedPay: false,
            addItemToCartAvailable: false,
            loadNeed: false,
            deleteProtect: false,
            deleteProtectList: [
                {text: '开启', value: true},
                {text: '关闭', value: false}
            ],
            tagsValue: [],
            urlQuery: getQueryParams()
        };
    }

    inited() {
        const client = new Client({}, {}.$context);
        // 创建实例
        const sdk = new BillingSdk({
            client,
            AllRegion: window.$context.getEnum('AllRegion'),
            context: contextPipe(this)
        });
        this.data.set('newBillingSdk', sdk);
        if (this.data.get('requestNeed')) {
            return;
        }
        // region平铺;
        this.data.set('regionList', getVPCSupportRegion(window));
        if (FLAG.NetworkSupportXS) {
            this.data.set('formData.productType', PayType.PREPAY);
            let purchaseLength = this.data.get('purchaseLengthList');
            this.data.set(
                'purchaseLengthList',
                purchaseLength.map(item => {
                    if (item.mark) {
                        return {
                            text: item.text,
                            value: item.value
                        };
                    }
                    return item;
                })
            );
        }
        this.data.set('resourceSDK', new ResourceGroupSDK(this.$http, window.$context));
        this.initFormData();
        this.loadVpcList();
        this.loadEipList();
        this.checkEipBlack();
        this.checkAccount();
        this.sslInit();
        if (this.data.get('vpnType') === 'gre') {
            this.data.set('showFlavor', false);
            this.data.set('formData.greBandwidth', 2);
        }
        const currentRegion = window.$context.getCurrentRegionId();
        let flavorList = this.data.get('flavorList');
        let greBandwidthList = this.data.get('greBandwidthList');
        if (currentRegion === AllRegion.HKG) {
            let hkgFlavorList = flavorList.filter(item => item.value === '');
            let hkgGreBandwidthList = greBandwidthList.filter(item => item.value === 2);
            this.data.set('flavorList', hkgFlavorList);
            this.data.set('greBandwidthList', hkgGreBandwidthList);
        }
    }

    attached() {
        if (this.data.get('requestNeed')) {
            return;
        }
        this.watch('formData.purchaseLength', value => {
            this.loadPrice();
        });
        this.watch('formData.maxClient', value => {
            this.loadPrice();
        });
        this.watch('formData.autoRenew', value => {
            this.updateBandWidth();
        });
        this.watchResolver();
        this.initOrderItems();
    }

    cancel() {
        let pageTitle = this.data.get('pageTitle');
        redirect(pageTitle.backTo);
    }

    watchResolver() {
        this.watch('formData.flavor', () => {
            this.flavorChange();
            this.getAvailableZone(this.data.get('subnetDatasource'));
        });
        this.watch('formData.netType', value => {
            const orderItem = this.data.get('bucketItems');
            if (!orderItem[0]) {
                return;
            }
            const configDetail = this.getConfigDetail();
            const vpnType = this.data.get('vpnType');
            if (vpnType === 'gre' && value === 'intranet') {
                const filterBandwidthConfig = configDetail.filter(item => item.label !== '购买配置');
                orderItem[0].updateConfigDetail(filterBandwidthConfig);
            } else {
                orderItem[0].updateConfigDetail(configDetail);
            }
        });
    }

    getAvailableZone(subnetDatasource: Array<[]>) {
        const vpnType = this.data.get('vpnType');
        const formData = this.data.get('formData');
        const flavor =
            vpnType === 'gre'
                ? formData.greBandwidth === 5
                    ? 'high_performance'
                    : 'normal'
                : formData.flavor
                  ? formData.flavor
                  : 'normal';
        this.$http
            .getAvailableZone({flavor})
            .then(res => {
                if (subnetDatasource?.length) {
                    const soldOutMarkedSubnetData = u.map(subnetDatasource, (item: any) => {
                        return {...item, soldoutTip: !res.includes(item.az)};
                    });
                    this.data.set('subnetDatasource', soldOutMarkedSubnetData);
                }
            })
            .finally(() => this.data.set('loading', false));
    }

    getConfigsFromCalculator() {
        const calculatorConfig = this.data.get('urlQuery.config');
        const decryptConfig = decryptUrlBtoa(calculatorConfig);
        return decryptConfig;
    }

    initFormData() {
        const basicData: Record<string, any> = {
            productType: 'prepay',
            region: window.$context.getCurrentRegionId(),
            purchaseLength: 1,
            flavor: '',
            bandWidth: '-',
            autoRenewTimeUnit: 'month',
            autoRenewTime: 1,
            autoRenew: false,
            netType: 'internet' // 创建VPN网关时选择公网IP 不区分类型
        };
        const vpnType = this.data.get('vpnType');
        if (['ipsec', 'gre'].includes(vpnType)) {
            basicData.enableBgp = false;
        }
        const calculatorConfig = this.getConfigsFromCalculator();
        if (calculatorConfig?.flavor === 'default') {
            calculatorConfig.flavor = '';
        }
        this.data.set('formData', {...basicData, ...calculatorConfig});
    }

    resetFormData() {
        let formData = this.data.get('formData');
        let basicData = {
            productType: 'prepay',
            region: window.$context.getCurrentRegionId(),
            purchaseLength: 1,
            description: '',
            vpnName: '',
            flavor: '',
            autoRenewTimeUnit: 'month',
            autoRenewTime: 1
        };
        if (this.data.get('vpnType') === 'ssl') {
            basicData.maxClient = 5;
        }
        let newData = {...formData, ...basicData};
        this.data.set('formData', newData);
    }

    initOrderItems() {
        this.loadPrice();
    }

    tagListRequster() {
        return this.$http.getSearchTagList({serviceType: [], region: ['global']});
    }

    loadVpcList() {
        this.data.set('vpcId', this.data.get('vpcId'));
        this.data.set('vpnType', this.data.get('vpnType'));
        this.data.set('formData.vpnType', this.data.get('vpnType'));
        let vpcId = this.data.get('vpcId');
        this.$http.vpcList().then(data => {
            let vpcs = u.map(data, item => ({text: `${item.name}（${item.shortId}）`, value: item.vpcId}));
            this.data.set('vpcList', vpcs);
            vpcId = vpcId || (vpcs && vpcs[0].value);
            this.data.set('formData.vpcId', vpcId);
            this.getVpnQuota(vpcId);
            this.loadSubnets(vpcId);
        });
    }

    getVpnQuota(vpcId) {
        this.$http.vpnQuota({vpcId}, kXhrOptions).then(res => {
            this.data.set('hasQuota', res.free > 0);
        });
    }

    loadEipList() {
        let payload = {
            pageSize: 1000,
            status: 'available'
        };
        this.$http.getEipList(payload).then(data => {
            let eips = u.map(data.result || [], item => {
                return {
                    text: `${item.eip}（${item.bandWidth}Mbps）`,
                    value: item.eip,
                    bandWidth: item.bandWidth
                };
            });
            eips.push({
                text: '暂不选择',
                value: '',
                bandWidth: '-'
            });
            this.data.set('eipList', eips);
            this.data.set('formData.bandWidth', eips[0].bandWidth);
            this.data.set('formData.eip', eips[0].value);
            this.updateBandWidth();
        });
    }

    eipChange({value}) {
        let eipList = this.data.get('eipList');
        let currentEip = u.find(eipList, item => item.value === value);
        this.data.set('formData.eip', value);
        this.data.set('formData.bandWidth', currentEip.bandWidth);
        this.updateBandWidth();
    }

    flavorChange() {
        this.loadPrice();
    }

    // 自动续费按月按年
    onRenewUnitChange({value}) {
        this.data.set('formData.autoRenewTimeUnit', value);
        this.changeRenewTimeList(value);
        this.loadPrice();
    }

    onRenewTimeChange({value}) {
        this.data.set('formData.autoRenewTime', value);
        this.updateBandWidth();
        this.loadPrice();
    }

    changeRenewTimeList(value) {
        let timeList = value === 'year' ? Year.toArray() : Month.toArray();
        this.data.set('renewNumberList', timeList);
        this.data.set('formData.autoRenewTime', '');
        this.nextTick(() => {
            this.data.set('formData.autoRenewTime', timeList[0].value);
            this.updateBandWidth();
        });
    }

    updateBandWidth() {
        let bucketItems = this.data.get('bucketItems');
        this.setConfigDetail(bucketItems[0]?.unitPrice, bucketItems[1]?.unitPrice);
    }

    checkAccount(productType = PayType.PREPAY) {
        return this.$http
            .purchaseValidation(
                {
                    serviceType: 'VPN',
                    productType
                },
                kXhrOptions
            )
            .then(res => this.data.set('accountPurchaseValidation', res));
    }

    productTypeChange(value) {
        this.data.set('formData.productType', value);
        value === 'postpay' && this.data.set('formData.autoRenew', false);
        this.checkAccount(value);
        this.loadPrice();
    }

    onSelectPayType(item) {
        const currPayType = this.data.get('formData.productType');
        if (currPayType !== item.value) {
            this.productTypeChange(item.value);
        }
    }

    // 如果是ssl类型的初始化准备
    sslInit() {
        if (this.data.get('vpnType') === 'ssl') {
            this.getMaxclientList();
        }
    }

    // 初始化ssl连接数
    getMaxclientList() {
        this.data.set('maxClientList', [
            {label: 5, value: 5},
            {label: 10, value: 10},
            {label: 20, value: 20},
            {label: 50, value: 50},
            {label: 100, value: 100},
            {label: 200, value: 200},
            {label: 500, value: 500},
            {label: 1000, value: 1000}
        ]);
        this.data.set('formData.maxClient', 5);
    }

    checkEipBlack() {
        const whiteList = window.$storage.get('commonWhite');
        this.data.set('inEipBlack', whiteList?.eipBlackList);
    }

    vpcChange({value}) {
        this.data.set('hasQuota', '');
        this.getVpnQuota(value);
        this.loadSubnets(value);
    }

    loadPrice() {
        const {newBillingSdk, formData} = this.data.get();
        newBillingSdk.clearItems();
        const configs = this.getPriceConfig();

        if (this.data.get('vpnType') === 'ssl') {
            const orderItem = new OrderItem(configs[0]);
            const sslOrderItem = new OrderItem(configs[1]);
            let bucketItems = [orderItem, sslOrderItem];
            this.data.set('bucketItems', bucketItems);
            newBillingSdk.addItems(bucketItems);
        } else {
            if (formData.netType === 'intranet') {
                configs?.[0]?.configDetail?.splice(1, 1);
            }
            const orderItem = new OrderItem(configs[0]);
            this.data.set('bucketItems', [orderItem]);
            newBillingSdk.addItems([orderItem]);
        }
    }
    onReset() {
        this.resetFormData();
        this.loadPrice();
    }

    async addShoppingCart() {
        let form = this.ref('form');
        await form.validateFields();
        this.data.set('cartConfirming', true);
        const showFlavor = this.data.get('flavor');
        let formData = this.data.get('formData');
        let config = getConfirmConfig(formData, showFlavor);
        this.$http
            .addVpnShoppingCart({
                paymentMethod: [],
                items: [
                    {
                        config: config,
                        paymentMethod: []
                    }
                ]
            })
            .then(result => {
                this.data.set('cartConfirming', false);
                window.shoppingCart?.showSuccessTip();
                window.shoppingCart?.refreshCount();
            })
            .catch(result => {
                this.data.set('cartConfirming', false);
            });
    }

    async goToConfirm() {
        const {bucketItems, formData, newBillingSdk} = this.data.get('');
        const form = this.ref('form');
        const tagPanelEle = this.ref('tagPanel');
        try {
            await (form as any).validateFields();
            await (tagPanelEle as any).validate(false);
        } catch (error) {
            return false;
        }
        const tags = await (tagPanelEle as any).getTags();
        if (tags?.length) {
            this.data.set('tagsValue', tags);
        }
        let configDetail = this.getConfigDetail();
        let sslConfigDetail = [
            {label: '地域', value: window.$context.getCurrentRegion().label},
            {label: 'SSL连接数', value: formData.maxClient + '个', showInConfirm: true}
        ];
        if (bucketItems[1]) {
            newBillingSdk.clearItems();
            bucketItems[0].updateConfigDetail(configDetail);
            bucketItems[1].updateConfigDetail(sslConfigDetail);
            this.data.set('bucketItems', bucketItems);
            newBillingSdk.addItems(bucketItems);
            this.data.set('newBillingSdk', newBillingSdk);
        }
        this.data.set('steps.current', 2);
        this.data.set('useCoupon', formData.productType === 'prepay' && FLAG.NetworkSupportXS);
    }

    backToOrder() {
        const tagsValue = this.data.get('tagsValue');
        if (tagsValue?.length) {
            this.data.set('defaultInstances', [{tags: tagsValue}]);
        }
        this.data.set('steps.current', 1);
        let bucketItems = this.data.get('bucketItems');
        this.setConfigDetail(bucketItems[0]?.unitPrice, bucketItems[1]?.unitPrice);
    }

    // 确认订单的时候 询价
    async onConfirm() {
        !FLAG.NetworkSupportXS && (await this.ref('billingProtocol').validateAgreement());
        this.data.set('confirming', true);
        let {newBillingSdk, bucketItems, formData, tagsValue} = this.data.get('');
        const showFlavor = this.data.get('flavor');
        const vpnType = this.data.get('vpnType');
        let config = getConfirmConfig(formData, showFlavor);
        if (formData.netType === 'intranet') {
            delete config.bandWidth;
        }
        config.tags = tagsValue;
        config.deleteProtect = this.data.get('deleteProtect');
        if (config.vpnType === 'gre') {
            delete config.greBandwidth;
        }
        if (!['ipsec', 'gre'].includes(vpnType)) {
            delete config.netType;
        } else {
            config.netType = formData.netType;
            if (config.netType === 'intranet') {
                delete config.eip;
            }
        }
        let params = {
            items: [
                {
                    config,
                    paymentMethod: bucketItems[0].couponId ? [{type: 'coupon', values: [bucketItems[0].couponId]}] : []
                }
            ]
        };
        try {
            let confirmUrl = '/api/vpn/vpn/order/confirm/new?orderType=NEW';
            const data = await this.$http.newConfirmOrder(confirmUrl, params);
            let url = '';
            try {
                const info = await newBillingSdk.checkPayInfo(data);
                url = info.url + '&fromService=VPN';
                info.url && (location.href = url);
            } catch (info) {
                // 跳转到相应页面
                url = info.url + '&fromService=VPN';
                info.url && (location.href = url);
            }
        } catch (err) {}
        this.data.set('confirming', false);
    }

    resourceChange({value}) {
        this.data.set('formData.resourceGroupId', value.data.groupId);
    }
    getPriceConfig() {
        const formData = this.data.get('formData');
        let serviceName = vpnTypeMap[this.data.get('vpnType')];
        const configDetail = this.getConfigDetail();
        if (formData.netType === 'intranet') {
            configDetail.splice(1, 1);
        }
        let configs = [
            {
                serviceType: 'VPN',
                serviceName: serviceName,
                configName: '配置总价',
                productType: formData.productType,
                type: 'NEW',
                scene: 'NEW',
                region: window.$context.getCurrentRegionId(),
                chargeItem: formData.productType === 'prepay' ? 'Cpt2' : 'RunningTimeMinutes',
                flavor: this.getFlavor(),
                timeUnit: formData.productType === 'prepay' ? 'MONTH' : 'MINUTE',
                duration: formData.productType === 'prepay' ? formData.purchaseLength : 1,
                configDetail: this.getConfigDetail()
            }
        ];
        if (this.data.get('vpnType') === 'ssl') {
            configs.unshift({
                serviceType: 'VPN',
                serviceName: serviceName,
                configName: '配置总价',
                productType: formData.productType,
                type: 'NEW',
                scene: 'NEW',
                region: window.$context.getCurrentRegionId(),
                chargeItem: formData.productType === 'prepay' ? 'Cpt2' : 'RunningTimeMinutes',
                flavor: this.getSslFlavors(),
                timeUnit: formData.productType === 'prepay' ? 'MONTH' : 'MINUTE',
                duration: formData.productType === 'prepay' ? formData.purchaseLength : 1,
                configDetail: this.getConfigDetail()
            });
            configs[1].serviceName = 'SSL规格费';
            configs[1].configDetail = this.getSslConfigDetail();
        }
        return configs;
    }
    getFlavor(type = '') {
        const formData = this.data.get('formData');
        let vpnType = this.data.get('vpnType');
        if (type) {
            vpnType = 'ipsec';
        }
        const showFlavor = this.data.get('showFlavor');
        let payload = [];
        // 区分是否有网关规格
        if (showFlavor) {
            // 区分普通型和增强型
            if (formData.flavor === 'high_performance') {
                payload.push({name: 'subServiceType', value: 'high_performance', scale: 1});
                // 区分两种类型的VPN（IPsec VPN 、SSL VPN）
                if (vpnType === 'ipsec') {
                    payload.push({name: 'gateWay', value: 'high_performance', scale: 1});
                } else {
                    payload.push({name: 'connection number', value: formData.maxClient, scale: 1});
                }
            } else {
                payload.push({name: 'subServiceType', value: 'default', scale: 1});
                // 区分两种类型的VPN（IPsec VPN 、SSL VPN）
                if (vpnType === 'ipsec') {
                    payload.push({name: 'gateWay', value: 'default', scale: 1});
                } else {
                    payload.push({name: 'connection number', value: formData.maxClient, scale: 1});
                }
            }
        } else {
            if (vpnType === 'gre') {
                let bandwidthInMbps = formData.greBandwidth + 'G';
                // 适配greVpn
                payload.push(
                    {name: 'subServiceType', value: 'high_performance', scale: 1},
                    {name: 'gre_vpn', value: bandwidthInMbps, scale: 1}
                );
            } else {
                payload.push({name: 'subServiceType', value: 'default', scale: 1});
                if (vpnType === 'ipsec') {
                    payload.push({name: 'gateWay', value: 'default', scale: 1});
                } else {
                    payload.push({name: 'connection number', value: formData.maxClient, scale: 1});
                }
            }
        }
        return payload;
    }
    getSslFlavors() {
        let payload = this.getFlavor('ipsec');
        return payload;
    }
    greBandwidthChange(e: {value: number}) {
        this.data.set('formData.greBandwidth', e.value);
        let flavor = e.value === 2 ? '' : 'high_performance';
        this.data.set('formData.flavor', flavor);
        this.data.set('greBandwidthMessage', e.value === 2 ? '最大转发能力200Mbps' : '最大转发能力1000Mbps');
        this.updateBandWidth();
    }

    loadSubnets(vpcId: string) {
        this.data.set('loading', true);
        this.data.set('formData.subnetId', '');
        this.$http
            .vpcSubnetList({vpcId, attachVm: false})
            .then((res: any) => {
                let datasource = [];
                u.each(res, item =>
                    datasource.push({
                        value: item.subnetId,
                        text: item.name + (item.cidr ? '（' + item.cidr + '）' : ''),
                        az: item.az
                    })
                );
                this.data.set('subnetDatasource', datasource);
                this.getAvailableZone(datasource);
            })
            .catch(() => this.data.set('loading', false));
    }

    onSelectGateway(item) {
        this.data.set('formData.flavor', item.value);
    }

    // 输入网关名称时统计字符时（近似统计）
    handleNameInput({value}) {
        if (value?.length <= 65) {
            this.data.set('nameLength', value.length);
        } else {
            this.data.set('nameLength', 65);
        }
    }
    // 获取当前子网可用区
    handleSubnetChange(e: {value: string}) {
        const subnetId = e.value;
        const subnetDatasource = this.data.get('subnetDatasource');
        const currSubnet = subnetDatasource.find((item: any) => item.value === subnetId);
        this.data.set('currZone', zone.getLabel(currSubnet.az));
        this.$http.getSubnetDetail(subnetId).then((res: any) => {
            if (res?.subnets) {
                const currResSubnets = res.subnets[0];
                this.data.set('availableIPs', currResSubnets?.totalIps - currResSubnets?.usedIps || 0);
            }
        });
    }
    // 新增onShoppingCartChange
    onShoppingCartChange(e) {
        this.data.set('priceLoading', UPDATE_STATUS[e] !== UPDATE_STATUS.DONE);
        if (UPDATE_STATUS[e] === UPDATE_STATUS.DONE) {
            const bucketItems = this.data.get('bucketItems');
            const {newBillingSdk, formData} = this.data.get();
            let unitPrice = bucketItems[0]?.unitPrice;
            let sslPrice = bucketItems[1]?.unitPrice;
            let priceSubText = bucketItems[0]?.priceSubText;
            let price = unitPrice + (sslPrice ? sslPrice : 0);
            // 用来判断是否需要二次询价
            if (formData.productType !== PayType.PREPAY && unitPrice && !priceSubText) {
                newBillingSdk.clearItems();
                let extra = `
                  （预计¥${showMoney(price * 60 * 24, 2)}/天
                  ¥${showMoney(price * 60 * 24 * 30, 2)}/月）
              `;
                bucketItems[0].priceSubText = extra;
                this.data.set('bucketItems', bucketItems);
                newBillingSdk.addItems(bucketItems);
            }
            if (sslPrice && bucketItems[1].configDetail.length !== 0) {
                this.setConfigDetail(unitPrice, sslPrice);
            }
        }
    }
    setConfigDetail(unitPrice, sslPrice) {
        let configuration = this.getConfigDetail(unitPrice, sslPrice);
        const orderItem = this.data.get('bucketItems');
        if (!orderItem[0]) {
            return;
        }
        const netType = this.data.get('formData.netType');
        if (netType === 'intranet') {
            configuration = configuration.filter(item => item.label !== '购买配置');
        }
        orderItem[0].updateConfigDetail(configuration);
    }
    getConfigDetail(unitPrice, sslPrice) {
        const {formData} = this.data.get();
        const quota = formData.productType === PayType.PREPAY ? `1个 * ${formData.purchaseLength}月` : '1个';
        let autoRenewTimeUnit = formData.autoRenewTimeUnit === 'month' ? '月' : '年';
        let autoRenewText = formData.autoRenew ? '开通自动续费' + formData.autoRenewTime + autoRenewTimeUnit : '';

        let configDetail = [
            {label: '地域', value: window.$context.getCurrentRegion().label},
            {label: '购买配置', value: formData.bandWidth + 'Mbps' + autoRenewText},
            {label: '购买时长', value: quota}
        ];
        const extraConfig = {label: 'VPN网关规格', value: vpnFlavor.getTextFromValue(formData.flavor)};
        const vpnTypeConfig = {label: 'VPN网关类型', value: vpnTypeList.getTextFromValue(formData.vpnType)};
        const greExtraConfig = {label: 'VPN网关规格', value: formData.greBandwidth === 2 ? '小型' : '中型'};

        this.data.get('showFlavor') && configDetail.push(extraConfig);
        formData.vpnType === 'gre' && configDetail.push(greExtraConfig);
        configDetail.push(vpnTypeConfig);
        if (sslPrice) {
            let getSslConfigDetail = this.getSslConfigDetail(unitPrice, sslPrice);
            configDetail = [...configDetail, ...getSslConfigDetail];
            this.setSslConfigDetail();
        }
        return configDetail;
    }
    setSslConfigDetail() {
        const orderItem = this.data.get('bucketItems');
        if (!orderItem[1]) {
            return;
        }
        orderItem[1].updateConfigDetail([]);
    }
    getSslConfigDetail(unitPrice, sslPrice) {
        let formData = this.data.get('formData');
        const quota = `1个 * ${formData.purchaseLength}月`;
        let config = [
            {label: '地域', value: window.$context.getCurrentRegion().label, showInCart: false, showInConfirm: false},
            {label: 'SSL连接数', value: formData.maxClient + '个', showInCart: false},
            {label: '购买时长', value: quota, showInCart: false, showInConfirm: false}
            // 购物车需要展示的内容
        ];
        if (unitPrice && sslPrice) {
            config = [
                ...config,
                {
                    label: 'SSL VPN',
                    value: `￥${convertPrice(unitPrice).getPriceOfMinute()}`,
                    showInConfirm: false
                },
                {
                    label: 'SSL规格费',
                    value: `￥${convertPrice(sslPrice).getPriceOfMinute()}`,
                    showInConfirm: false
                }
            ];
        }
        return config;
    }
    // 切换地域
    onRegionChange(e) {
        let value = e.value || e.id;
        if (!value || value === window.$context.getCurrentRegionId()) {
            return;
        }
        this.data.set('formData.region', value);
        window.$context.setRegion(value);
        this.data.set('loadNeed', true);
    }
    showAssist(type: string) {
        Assist.sendMessageToAssist({
            sceneLabel: 'vpn_create',
            message: type === 'region' ? '什么是地域？' : '实例到期如何处理？'
        });
    }
}
export default Processor.autowireUnCheckCmpt(VpnCreate);
