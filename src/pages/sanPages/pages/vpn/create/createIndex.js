import _ from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import './create';

const {template, invokeSUI, invokeAppComp, invokeComp} = decorators;

const tpl = html`
    <template>
        <div s-if="!indexLoading" style="width:100%" class="index-loading-class">
            <s-loading loading style="width:100%">
                <vpn-create
                    requestNeed="{{requestNeed}}"
                    vpnType="{{vpnType}}"
                    vpcId="{{vpcId}}"
                    class="vpn-create-component"
                />
            </s-loading>
        </div>
        <vpn-create
            s-if="indexLoading"
            s-ref="vpcVpn"
            vpnType="{{vpnType}}"
            vpcId="{{vpcId}}"
            class="vpn-create-component"
        />
    </template>
`;

@invokeComp('@vpn-create')
@template(tpl)
@invokeSUI
@invokeAppComp
class CreateIndex extends Component {
    initData() {
        return {
            indexLoading: true,
            requestNeed: true,
            vpnType: '',
            vpcId: ''
        };
    }

    inited() {
        const {vpcId, vpnType} = getQueryParams();
        // 若无vpn 类型 则默认为 ipsec vpn
        this.data.set('vpnType', vpnType || 'ipsec');
        this.data.set('vpcId', vpcId);
        window.$framework.events.on(window.$framework.EVENTS.AFTER_REGION_CHANGED, () => {
            let vpcVpn = this.ref('vpcVpn')?.data;
            const currentRegion = window.$context.getCurrentRegionId();
            if (currentRegion === vpcVpn?.get('formData')?.region && !vpcVpn?.get('loadNeed')) {
                return;
            }
            this.data.set('vpcId', '');
            this.data.set('indexLoading', false);
            this.nextTick(() => {
                // 等一会再执行
                setTimeout(() => {
                    this.data.set('indexLoading', true);
                }, 100);
            });
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(CreateIndex));
