.vpn-create-component {
    width: 100%;

    .vpn-create-wrap {
        height: 100%;
        background-color: #f7f7f9;

        .s-step-block {
            width: 316px !important;
        }

        .resource-group-panel {
            padding: 0px;
            border: 0px;

            dt {
                margin-bottom: 24px;
            }

            .footer {
                display: flex;
                margin-left: 120px;

                a {
                    margin-left: 8px;
                }
            }

            .resouce-group-select {
                label {
                    padding-left: 7px;
                }

                .wrapper {
                    margin-left: 10px;

                    .s-button {
                        border-color: #e8e9eb;
                    }
                }

                .s-button-skin-stringfy {
                    padding: 0 4px;
                }

                .footer {
                    line-height: 20px;
                    height: 20px;
                    margin-top: 8px;

                    .tip {
                        color: #84868c;
                        line-height: 20px;
                    }
                }
            }
        }

        .vpn-purchase-wrap {
            .wrapper {
                display: flex;
            }

            label {
                width: 45px;
            }

            .s-radio-text {
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        .tip-icon {
            color: #9e9898;
            border: 1px solid #9e9898;
            margin-left: 10px;
            box-sizing: content-box;

            &:hover {
                border-color: #2468f2;
            }
        }

        .page-floating-nav {
            z-index: 99;
        }

        // 订单确认页样式统一设置 目前仅考虑1280 需适配其他宽度可媒体查询
        .order-confirm {
            margin-top: 16px;
            width: calc(~'100vw - 260px');
            min-width: 1020px;
        }

        .purchase-btn {
            .s-radio-button:first-child .s-radio-text {
                border-top-left-radius: 4px;
                border-bottom-left-radius: 4px;
                border-left-width: 1px;

                &::before {
                    background-color: unset;
                }
                &:hover::before {
                    background-color: unset;
                }
            }

            .s-radio-button:last-child .s-radio-text {
                border-top-right-radius: 4px;
                border-bottom-right-radius: 4px;
            }

            .s-radio-button-group label:not(.state-disabled) input[type='radio']:checked ~ .s-radio-text {
                background-color: #eef3fe;
            }
        }

        .s-form {
            .s-form-item {
                .s-row {
                    .s-form-item-control-wrapper {
                        margin-left: 10px;
                    }
                }
            }

            .s-form-item-label {
                width: 110px;
                height: 30px;
                label {
                    display: inline-block;
                }
            }

            .s-form-tip-error {
                color: #f33e3e !important;
                padding-bottom: 0 !important;
                margin-top: 8px;
                line-height: 20px !important;
            }

            .s-input {
                border-color: #e8e9eb !important;

                .s-input-area {
                    input {
                        box-sizing: border-box;
                    }
                }
            }

            .s-textarea {
                textarea {
                    border-color: #e8e9eb !important;
                }
            }

            .s-form-item-error {
                padding-top: 10px !important;
                line-height: 20px !important;
            }

            .s-input {
                border-color: #e8e9eb !important;

                .s-input-area {
                    input {
                        box-sizing: border-box;
                    }
                }
            }

            .s-textarea {
                textarea {
                    border-color: #e8e9eb !important;
                }
            }

            .s-input {
                border-color: #e8e9eb !important;

                .s-input-area {
                    input {
                        box-sizing: border-box;
                    }
                }
            }

            .s-textarea {
                textarea {
                    border-color: #e8e9eb !important;
                }
            }
        }

        .s-input {
            box-sizing: border-box;
        }

        .legend-wrap {
            margin: 10px 0;
        }

        .bui-toastlabel-warning {
            width: 100%;
            box-sizing: border-box;
        }

        .content-wrap {
            border-radius: 6px;
            margin: 12px 0 0;
            text-align: left;
            background-color: #f7f7f9 !important;
            box-sizing: content-box;
            min-width: 1020px;

            input {
                box-sizing: border-box;
            }
        }

        .tip-icon-wrap {
            font-size: 14px;
            border: 1px solid #f18823;
            margin-left: 10px;
            color: #f18823;

            &:hover {
                color: #fff;
                background-color: #f18823;
            }
        }

        .s-badge-content {
            z-index: 99;
        }

        .vpn-renew-wrap {
            margin-top: 10px;

            .renew-title {
                margin-right: 10px;
            }

            .renew-tip {
                margin-left: 10px;
            }
        }

        .renew-item-label {
            .renew-item-wrapper {
                display: inline-block;

                .s-radio-text {
                    width: 37px !important;
                }
            }
        }

        .form-part-wrap {
            border-radius: 6px;
            width: calc(~'100vw - 260px');
            background: #fff;

            &:first-child {
                padding: 24px;
            }

            padding: 16px 24px 24px;
            min-width: 1020px;

            h4 {
                display: inline-block;
                border: none;
                zoom: 1;
                margin: 0;
                padding: 0;
                font-family: PingFangSC-Medium;
                font-size: 16px;
                color: #151b26;
                line-height: 24px;
                font-weight: 500;
            }

            .row-line {
                display: flex;
                align-items: center;

                .row-line-wrapper {
                    display: flex;
                    flex-wrap: wrap;

                    .row-line-item {
                        background: #ffffff;
                        border: 1px solid #d4d6d9;
                        border-radius: 6px;
                        display: flex;
                        width: 260px;
                        align-items: center;
                        justify-content: flex-start;
                        padding: 12px 16px;
                        margin-right: 12px;
                        margin-bottom: 4px;
                        cursor: pointer;

                        &:hover {
                            border: 1px solid #2468f2;
                        }

                        .row-line-icon {
                            width: 20px;
                            height: 20px;
                            margin-right: 10px;
                            background-size: 100% 100%;
                            flex-shrink: 0;
                        }

                        .row-line-version {
                            display: flex;
                            align-items: center;

                            .img {
                                width: 32px;
                                height: 32px;
                                margin-right: 20px;
                            }

                            .row-line-widget {
                                .row-line-name {
                                    font-size: 14px;
                                    color: #151b26;
                                    font-weight: 500;
                                    line-height: 22px;
                                }

                                .row-line-name-selected {
                                    color: #2468f2;
                                }

                                .row-line-desc {
                                    font-size: 12px;
                                    color: #5c5f66;
                                    line-height: 20px;
                                    font-weight: 400;
                                    margin-top: 4px;
                                }
                            }
                        }
                    }

                    .row-line-item-gateway {
                        width: 180px;
                    }

                    .row-line-selected {
                        background: #eef3fe;
                        border: 1px solid #2468f2;
                    }
                }
            }

            .label_class {
                .inline-tip {
                    top: 3px;
                    left: -5px;
                    position: relative;
                    .s-tip-warning {
                        justify-content: center;
                        .warning_class {
                            fill: #999;
                        }
                    }
                    .s-tip:hover .s-icon path {
                        fill: #2468f2 !important;
                    }
                }
            }

            .center_class {
                .s-row {
                    .s-form-item-control-wrapper {
                        line-height: 30px;
                    }
                }
            }

            .owner-network {
                .s-form-item-control {
                    display: flex;
                }

                .subnetId-wrapper {
                    display: inline-block;
                    margin-left: 8px;
                    position: relative;

                    .zone-tip {
                        position: absolute;
                        top: 40px;
                        white-space: nowrap;
                        font-family: PingFangSC-Regular;
                        font-size: 12px;
                        color: #84868c;
                        line-height: 20px;
                        font-weight: 400;
                    }

                    .s-form-item {
                        margin-top: 0px !important;

                        .s-form-item-control-wrapper {
                            margin-left: 0px;
                        }
                    }
                }
            }

            .create-region {
                .s-radio-text {
                    width: 61px !important;
                }
            }

            .s-radio-button-group {
                .s-radio-checked {
                    .s-radio-text {
                        background-color: #eef3fe;
                    }
                }

                .s-radio-button {
                    .s-radio-text {
                        width: 76px;
                    }
                }
            }

            .buy-time {
                .s-radio-button {
                    .s-radio-text {
                        width: 20px;
                    }
                }
            }

            .bgp-wrapper {
                .s-form-item-control-wrapper {
                    display: flex;
                    align-items: center;
                }
            }

            .margin-top-zero {
                margin-top: 0 !important;

                .s-form-item-help {
                    padding-top: 3px;
                }
            }

            .input-with-num {
                input {
                    padding-right: 52px;
                }
            }

            .input-num-statistics {
                font-family: PingFangSC-Regular;
                font-size: 12px;
                color: #84868c;
                text-align: right;
                line-height: 20px;
                font-weight: 400;
                margin-left: -46px;
            }

            .title_vpc {
                font-size: 16px;
                font-weight: 500;
            }
        }

        .tag-wrap {
            .s-form-item-label > label {
                min-width: auto;
            }

            .s-form-item {
                margin-top: 24px !important;
            }
        }

        .inline-form {
            h4 {
                float: left;
            }

            .tag-edit-panel {
                margin-left: 130px;
            }
        }

        .buybucket {
            width: 100%;
            left: 0;
            height: 80px !important;

            .buybucket-container {
                width: calc(~'100vw - 240px');
                display: inline-flex;
                height: 80px;
                transform: translateY(0%);
                display: flex;
                align-items: center;
                margin: 0 auto;

                .price-item {
                    padding: 0 40px !important;
                }

                .detail-wrapper {
                    padding-left: 0px;
                }

                .confirm-btn {
                    margin-left: 0 !important;
                }

                .shopping-cart {
                    flex: none;
                }

                .billing-sdk-total-price-wrapper {
                    margin-left: 16px;
                }
                .billing-sdk-protocol-wrapper .buy-agreement {
                    margin-bottom: 8px !important;
                }
            }
        }

        .s-create-page-content {
            padding-bottom: 96px !important;
        }

        .popover-class {
            .s-button {
                margin: 0px;
            }
        }

        .resouce-group-select-main > label {
            width: 110px;
        }

        .order-confirm-panel {
            width: calc(~'100vw - 260px');
        }

        .billing-sdk-order-confirm-wrapper-default {
            padding: 0px;

            .billing-sdk-order-legend .content .item {
                label {
                    width: 100px;
                }
            }
        }

        .s-form-item-region {
            .s-radio-button-group .s-wrapper {
                max-width: 1000px;

                .s-radio-button {
                    margin-bottom: 12px;
                }
            }

            .s-radio-button-enhanced-group .s-radio-button .s-radio-text {
                width: 130px !important;
            }

            .inline-tip {
                position: relative;
                top: 2px;

                .s-tip:hover {
                    background: #fff;

                    path {
                        fill: #2468f2;
                    }
                }

                .warning-class {
                    position: relative;
                    left: -1px;

                    .s-icon {
                        path {
                            fill: #83868c;
                        }
                    }
                }

                &:hover {
                    .warning-class {
                        .s-icon {
                            path {
                                fill: #2468f2;
                            }
                        }
                    }
                }
            }
        }

        .first-config-content {
            padding-bottom: 10px;
        }

        .tag-edit-panel .inline-form .s-form-item {
            margin-top: 0px;
        }

        .billing-sdk-order-legend .content .item {
            label {
                width: 100px;
            }
        }
    }
}

.locale-en {
    .vpn-create-wrap .s-form .s-form-item-label {
        width: 206px;
    }

    .vpn-create-wrap .resouce-group-select .resouce-group-select-main > label {
        width: 206px;
    }

    .vpn-create-wrap .resouce-group-select .footer {
        margin-left: 206px;
    }

    .vpn-conn-create-warp .s-form .s-form-item-label {
        width: 332px;
    }
}

.bui-layer {
    .bui-tiplayer {
        border-radius: 4px;

        .bui-button-label {
            color: #2468f2;
        }
    }
}
