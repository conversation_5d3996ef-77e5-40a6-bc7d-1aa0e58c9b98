import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {BcmSDK} from '@baidu/new-bcm-sdk';
import {BcmChartPanel} from '@baidu/new-bcm-sdk-san';
import moment from 'moment';
import {Dialog, Button, Select, DatePicker} from '@baidu/sui';
import {Tip} from '@baidu/sui-biz';
import {
    OutlinedRefresh,
} from '@baidu/sui-icon';

import monitorConfig from '@/pages/sanPages/utils/monitorConfig';
import './style.less';

const {VpnMetric, shortcutItems, greVpnMetric} = monitorConfig;
const tpl = html`
<div>
    <s-dialog open="{{true}}" title="VPN隧道监控" class="vpn-monitor-dialog"
    width="940"
    >
        <div class="vpn-conn-monitor-box">
            <div class="search-warp">
                <span class='search-item'>
                    <label class="search-item-label">监控指标：</label>
                    <s-select value="{=metrics.option=}" datasource="{{ds.options}}"
                        on-change="loadMetrics">
                    </s-select>
                </span>
                <span class='search-item'>
                    <label class="search-item-label">统计项：</label>
                    <s-select value="{=metrics.statistics=}" datasource="{{ds.statistics}}"
                        on-change="loadMetrics">
                    </s-select>
                </span>
                <span class='search-item'>
                    <label class="search-item-label">时间：</label>
                    <s-date-range-picker
                        s-ref="timeRange"
                        value="{=timeRange=}"
                        width="{{310}}"
                        range="{{range}}"
                        mode="second"
                        on-change="onTimeChange"
                        shortcut="{{shortcutItems}}"
                    />
                </span>
                <s-button class="s-icon-button" on-click="loadMetrics"><outlined-refresh class="icon-class"/></s-button>
                <s-tip skin="question"
                    class="inline-tip"
                    content="最多支持1440个数据点的查询显示，请选择合适的采样周期和聚合时间段。"/>
            </div>
            <div class="ipv6-monitor-wrap">
                <bcm-chart-panel
                    s-ref="bcmChart"
                    api-type="metricName"
                    scope="BCE_VPN"
                    width="{{750}}"
                    height="{{400}}"
                    options="{{options}}"
                    showbigable="{{false}}"
                    statistics="{{metrics.statistics}}"
                    dimensions="{{dimensions}}"
                    metrics="{=metrics.metrics=}"
                    period="{{metrics.period}}"
                    unit="{{metrics.unit}}"
                    bitUnit="{{metrics.bitUnit}}"
                    sdk="{{bcmSdk}}"
                    connect-nulls="{{true}}"
                    startTime="{=startTime=}"
                    endTime="{=endTime=}"
                />
            </div>
        </div>
        <div slot="footer"></div>
    </s-dialog>
</div>
`;

export default class VpnConnMonitor extends Component {
    static template = tpl;
    static components = {
        's-dialog': Dialog,
        's-button': Button,
        's-select': Select,
        's-tip': Tip,
        's-date-range-picker': DatePicker.DateRangePicker,
        'bcm-chart-panel': BcmChartPanel,
        'outlined-refresh': OutlinedRefresh,
    }
    static computed = {
        dimensions() {
            let vpnConnId = this.data.get('vpnConnId');
            if (this.data.get('type') === 'ipsec') {
                return `ConnID:${vpnConnId}`;
            } else {
                return `GreId:${vpnConnId}`;
            }
        }
    }
    initData() {
        return {
            metrics: {
                option: 'bytes',
                statistics: 'average',
                period: '60',
                metrics: [],
                unit: 'bps',
                bitUnit: 1000
            },
            ds: {
                options: [
                    {text: '网络带宽', value: 'bytes'},
                    {text: '包速率', value: 'packets'},
                    {text: '丢包率', value: 'iplr'},
                    {text: '时延', value: 'latency'},
                ],
                statistics: [
                    {text: '平均值', value: 'average'},
                    {text: '和值', value: 'sum'},
                    {text: '最大值', value: 'maximum'},
                    {text: '最小值', value: 'minimum'},
                    {text: '样本数', value: 'sampleCount'}
                ]
            },
            timeRange: {
                begin: new Date(moment().subtract(1, 'hour').valueOf()),
                end: new Date(moment().valueOf())
            },
            range: {
                begin: new Date(moment().subtract(40, 'day').valueOf()),
                end: new Date(moment().valueOf())
            },
            startTime: moment().subtract(1, 'hour').utc().format('YYYY-MM-DDTHH:mm:00') + 'Z',
            endTime: moment().utc().format('YYYY-MM-DDTHH:mm:00') + 'Z',
            shortcutItems,
            bcmSdk: new BcmSDK({client: window.$http, context: window.$context}),
            options: {dataZoom: {start: 0}}
        };
    }

    inited() {
        if (this.data.get('type') !== 'ipsec') {
            this.data.splice('ds.options', [2, 3]);
        }
    }

    attached() {
        this.loadMetrics();
        this.watch('timeRange', this.loadMetrics);
    }

    loadMetrics() {
        let component = this.ref('bcmChart');
        let array = this.data.get('type') !== 'ipsec' ? greVpnMetric : VpnMetric;
        if (component) {
            this.nextTick(() => {
                let option = this.data.get('metrics.option');
                this.data.set('metrics.metrics', array[option].metrics);
                this.data.set('metrics.unit', array[option].unit);
                this.data.set('metrics.bitUnit', array[option].bitUnit);
                component.loadMetrics();
            });
        }
    }
    onTimeChange({value}) {
        this.data.set('startTime', moment(value.begin).utc().format('YYYY-MM-DDTHH:mm:00') + 'Z');
        this.data.set('endTime', moment(value.end).utc().format('YYYY-MM-DDTHH:mm:00') + 'Z');
        let ms = moment(value.end) - moment(value.begin);
        let hourTime = Math.round(ms / 86400000);
        switch (true) {
            case (hourTime <= 1):
                this.data.set('metrics.period', 60);
                break;
            case (hourTime <= 3):
                this.data.set('metrics.period', 300);
                break;
            case (hourTime <= 7):
                this.data.set('metrics.period', 600);
                break;
            case (hourTime <= 14):
                this.data.set('metrics.period', 1800);
                break;
            case (hourTime <= 40):
                this.data.set('metrics.period', 3600);
                break;
            default: break;
        };
    }
}
