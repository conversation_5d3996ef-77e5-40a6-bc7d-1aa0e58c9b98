import {crossDcPageUrl} from '@/utils';
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {Notification, Radio} from '@baidu/sui';
import {OutlinedPlus, OutlinedQuestion} from '@baidu/sui-icon';
import rule from '@/pages/sanPages/utils/rule';
import {convertCidrToBinary} from '@/pages/sanPages/utils/common';
import {
    ikeConfigVersion,
    ikeConfigMode,
    ikeConfigEncAlg,
    ikeConfigAuthAlg,
    ikeConfigPfs,
    ikeConfigRemoteType,
    ipsecConfigEncAlg,
    ipsecConfigAuthAlg,
    ipsecConfigPfs
} from '@/pages/sanPages/common/enum';
import {disable_vpc_10cidr as disableVpc10Cidr} from '@/pages/sanPages/common/flag';
import checkRules from '../../rule';
import {validateRules, checkSubnets, validateLocalRemoteRepeatOrOverlap} from './helper';
import rules from '@/pages/sanPages/utils/rule';
import {confirmValidate} from '@/pages/sanPages/utils/helper';
import './style.less';

const {IP, DOMAIN} = rules;
const kXhrOptions = {'X-silence': true};
const {asPage, invokeSUI, invokeSUIBIZ, invokeAppComp, template} = decorators;

const tpl = html`
<div>
    <s-app-create-page class="{{klass}}" backTo="{{pageTitle.backTo}}"
        backToLabel="{{pageTitle.label}}" pageTitle="{{title}}">
        <div class="content-wrap {{vpnType === 'ssl' ? 'ssl-form-class' : ''}}">
            <s-form s-ref="form" label-align="left"
                rules="{{rules}}" data="{=formData=}">
                <div class="content-item-box form-part-wrap">
                    <h4>基本配置</h4>
                    <s-form-item label="所在私有网络：" prop="sourceVpc">
                        <span class="localip-wrap">{{vpcInfo.name}}({{vpcInfo.cidr}})</span>
                    </s-form-item>
                    <s-form-item label="{{(vpnType === 'ipsec' || vpnType === 'gre') ? 'VPN隧道名称：' : 'VPN服务端名称：'}}" prop="vpnConnName"
                        class="require-label"
                        help="大小写字母、数字以及-_/.特殊字符，必须以字母或者中文开头，长度1-65"
                    >
                        <s-input value="{=formData.vpnConnName=}">
                    </s-form-item>
                    <s-form-item s-if="{{vpnType === 'ssl'}}" label="接口类型" prop="interfaceType"
                        class="require-label"
                        help="{{interfaceTypeTips}}"
                    >
                        <s-radio-group
                            disabled="{{urlQuery.vpnConnId}}"
                            radioType="button"
                            datasource="{{interfaceTypeList}}"
                            value="{=formData.interfaceType=}"
                        >
                        </s-radio-group>
                    </s-form-item>
                    <s-form-item s-if="{{vpnType === 'ipsec'}}" label="共享密钥：" prop="secretKey" class="require-label"
                        help="8～17位字符，英文、数字和符号必须同时存在，符号仅限!@#$%^*()_."
                    >
                        <s-input value="{=formData.secretKey=}"></s-input>
                    </s-form-item>
                    <s-form-item label="" prop="encryptionMode" s-if="{{vpnType === 'ipsec'}}">
                        <template slot="label" class="label_class">
                            {{'通信模式：'}}
                            <s-tip
                                class="inline-tip"
                                content="{{encryptionModeMessage}}"
                                skin="question"
                                />
                        </template>
                        <div class="row-line flavor-line">
                            <s-radio-radio-group
                                datasource="{{encryptionModeList}}"
                                value="{=formData.encryptionMode=}"
                            >
                            </s-radio-radio-group>
                        </div>
                    </s-form-item>
                    <s-form-item s-if="{{vpnType === 'ipsec' && vpnInfo.netType === 'intranet'}}" label="" prop="internalIp">
                        <template slot="label" class="label_class">
                            {{'本端私网IP：'}}
                        </template>
                        <div class="localip-wrap">
                            <span>{{vpnInfo.internalIp || '-'}}</span>
                        </div>
                    </s-form-item>
                    <s-form-item s-if="isShowPublicIp" label="" prop="localIp">
                        <template slot="label" class="label_class">
                            {{'本端VPN网关公网IP/带宽：'}}
                            <s-tip
                                class="inline-tip"
                                placement="top"
                                content="如需更改本端公网IP，可到VPN网关列表配置"
                                skin="question"/>
                        </template>
                        <div class="localip-wrap">
                            <span>{{vpnInfo.eip || '-'}}/{{vpnInfo.bandwidthInMbps || '-'}}Mbps</span>
                        </div>
                    </s-form-item>
                    <s-form-item
                        class="require-label"
                        s-if="{{vpnType === 'ssl' || (vpnType === 'ipsec' && formData.encryptionMode !== 'vti')}}"
                        label="{{vpnType === 'ipsec' ? '本端网络：' : 'VPN服务端网络：'}}"
                        prop="localSubnets">
                        <div class="subnets-wrap" s-for="item, index in formData.localSubnets">
                            <s-select
                                value="{=formData.localSubnets[index]=}"
                                width="200"
                                datasource="{{subnetList}}"
                            >
                            </s-select>
                            <s-icon s-if="index > 0" class="delete-icon" name="close"
                                on-click="deleteSubnet('localSubnets', index)"
                            ></s-icon>
                            <s-input class="custom-input" s-if="item === 'CUSTOM'"
                                value="{=formData.customSubnets[index]=}"
                                width="200"
                                on-input="localSubnetsInput($event, index)"
                            >
                            </s-input>
                            <p class="red-color" s-if="localSubnetsErr[index] !== ''">
                                {{localSubnetsErr[index]}}
                            </p>
                        </div>
                        <s-tip-button skin="stringfy"
                            disabled="{{createLocalSubNet.disable}}"
                            content="{{createLocalSubNet.message}}"
                            isDisabledVisibile="{{true}}"
                            on-click="addSubnet('localSubnets')">
                            <outlined-plus/>
                            添加更多{{vpnType === 'ipsec' ? '本地网络' : '服务端网络'}}</s-tip-button>
                            <p class="red-color" s-if="existTip">
                                {{existTip}}
                                <s-tip
                                    class="inline-tip"
                                    skin="question"
                                    >
                                    <s-qusetion/>
                                    <div slot="content">
                                        <!--bca-disable-next-line-->
                                        {{existOrOverlapTip | raw}}
                                    </div>
                                </s-tip>
                            </p>
                    </s-form-item>
                    <s-form-item  s-if="{{vpnType === 'ipsec' || vpnType === 'gre'}}"  class="require-label" prop="remoteIp">
                        <template slot="label" class="label_class">
                            {{vpnInfo.netType === 'intranet' ? '对端VPN网关IP：' : '对端VPN网关公网IP：'}}
                            <s-tip
                                s-if="vpnType === 'ipsec'"
                                class="inline-tip"
                                content="{{remoteVpnIpTip}}"
                                skin="question"
                                />
                        </template>
                        <s-input value="{=formData.remoteIp=}"  disabled="{{vpnType === 'gre' && urlQuery.vpnConnId}}"></s-input>
                        <p class="remoteIp-tip" s-if="barbaricModeTip && vpnType === 'ipsec'">{{barbaricModeTip}}</p>
                    </s-form-item>
                    <div s-if="isShowFlag">
                        <s-form-item
                            label="{{'本端BGP ASN：'}}"
                            prop="localBgpAsn">
                            <s-input disabled="{{urlQuery.vpnConnId}}" value="{=formData.localBgpAsn=}" />
                        </s-form-item>
                        <s-form-item
                            label=" "
                            class="margin-top-none"
                        >
                            <div slot="help">{{'ASN输入有效范围：1 - 4294967295,且不能与对端网关BGP ASN冲突。'}}</div>
                        </s-form-item>
                        <s-form-item
                            label="{{'对端BGP ASN：'}}"
                            prop="remoteBgpAsn">
                            <s-input disabled="{{urlQuery.vpnConnId}}" value="{=formData.remoteBgpAsn=}" />
                        </s-form-item>
                        <s-form-item
                            label=" "
                            class="margin-top-none"
                        >
                            <div slot="help">{{'ASN输入有效范围：1 - 4294967295,且不能与本端网关BGP ASN冲突。'}}</div>
                        </s-form-item>
                    </div>
                    <s-form-item
                        s-if="{{isShowInternetAddress}}"
                        prop="localConnIp"
                        class="ips-wrap"
                        label="{{internetAddressText.localLabel}}"
                        help="{{'例如：**********（按tab键切换到下一输入框）'}}"
                    >
                        <s-input
                            s-ref="ipInput0"
                            class="{{localIpErrInfo[0] ? 'input-error' : ''}}"
                            value="{=formData.localConnIp[0]=}"
                            placeholder=""
                            width="{{45}}"
                            maxLength="3"
                            on-input="ipInput($event, 0)"
                            disabled="{{urlQuery.vpnConnId}}"
                        />
                        <span>.</span>
                        <s-input
                            s-ref="ipInput1"
                            class="{{localIpErrInfo[1] ? 'input-error' : ''}}"
                            value="{=formData.localConnIp[1]=}"
                            placeholder=""
                            width="{{45}}"
                            maxLength="3"
                            on-input="ipInput($event, 1)"
                            disabled="{{urlQuery.vpnConnId}}"
                        />
                        <span>.</span>
                        <s-input
                            s-ref="ipInput2"
                            class="{{localIpErrInfo[2] ? 'input-error' : ''}}"
                            value="{=formData.localConnIp[2]=}"
                            placeholder=""
                            width="{{45}}"
                            maxLength="3"
                            on-input="ipInput($event, 2)"
                            disabled="{{urlQuery.vpnConnId}}"
                        />
                        <span>.</span>
                        <s-input
                            s-ref="ipInput3"
                            class="{{localIpErrInfo[3] ? 'input-error' : ''}}"
                            value="{=formData.localConnIp[3]=}"
                            placeholder=""
                            width="{{45}}"
                            maxLength="3"
                            on-input="ipInput($event, 3)"
                            disabled="{{urlQuery.vpnConnId}}"
                        />
                        <span>/</span>
                        <s-select on-change="maskChange" value="{=formData.maskCode=}" disabled="{{urlQuery.vpnConnId}}">
                            <s-select-option
                                s-for="item in maskDatasource()"
                                value="{{item.value}}"
                                label="{{item.label}}"
                            ></s-select-option>
                        </s-select>
                        <p s-if="{{localIpErr}}" class="s-form-item-error">{{localIpErr}}</p>
                    </s-form-item>
                    <s-form-item
                        s-if="{{isShowInternetAddress}}"
                        prop="remoteConnIp"
                        label="{{internetAddressText.remoteLabel}}"
                        class="ips-wrap"
                    >
                        <s-input value="{{formData.remoteConnIp[0] || '-'}}"
                            placeholder="" width="{{45}}" disabled="{{true}}" />
                        <span>.</span>
                        <s-input value="{{formData.remoteConnIp[1] || '-'}}"
                            placeholder="" width="{{45}}" disabled="{{true}}" />
                        <span>.</span>
                        <s-input value="{{formData.remoteConnIp[2] || '-'}}"
                            placeholder="" width="{{45}}" disabled="{{true}}" />
                        <span>.</span>
                        <s-tooltip
                            trigger="{{localIpEnable ? 'hover' : ''}}"
                            placement="bottom"
                            content="可输入范围{{remoteRange}}">
                            <s-input
                                value="{=formData.remoteConnIp[3]=}"
                                class="{{localIpErrInfo[4] ? 'input-error' : ''}}"
                                placeholder=""
                                width="{{40}}"
                                maxLength="3"
                                on-input="ipInput($event, 4)"
                                disabled="{{urlQuery.vpnConnId}}"
                            />
                        </s-tooltip>
                        <span>/</span>
                        <s-select on-change="maskChange" value="{=formData.maskCode=}" disabled="{{urlQuery.vpnConnId}}">
                            <s-select-option
                                s-for="item in maskDatasource()"
                                value="{{item.value}}"
                                label="{{item.label}}"
                            ></s-select-option>
                        </s-select>
                        <p s-if="{{localIpErrInfo[4]}}" class="s-form-item-error">{{localIpErrInfo[4] || ''}}</p>
                    </s-form-item>
                    <s-form-item
                        s-if="{{isShowRemoteNet}}"
                        class="require-label"
                        label="{{vpnType === 'ipsec' || vpnType === 'gre' ? '对端网络：' : 'VPN客户端网络：'}}"
                        prop="remoteSubnets">
                        <div class="subnets-wrap" s-for="item, index in formData.remoteSubnets">
                            <s-input
                                value="{=formData.remoteSubnets[index]=}"
                                width="200"
                                placeholder="网段格式，例：*************/24"
                                on-input="remoteSubnetsInput($event, index)"
                            >
                            </s-input>
                            <s-icon s-if="index > 0" class="delete-icon" name="close"
                                on-click="deleteSubnet('remoteSubnets', index)"
                            ></s-icon>
                            <p class="red-color" s-if="remoteSubnetsErr[index] !== ''">
                                {{remoteSubnetsErr[index]}}
                            </p>
                        </div>
                        <span class="remoteSubnets-tip" s-if="{{vpnType === 'ipsec' && formData.remoteSubnets.length > 1 && formData.encryptionMode !== 'vti'}}">
                            对端网络为1个网段以上时请使用IKEv2版本
                        </span>
                        <s-tip-button s-if="{{vpnType === 'ipsec' || vpnType === 'gre'}}" skin="stringfy"
                            disabled="{{createRemoteSubNet.disable}}"
                            content="{{createRemoteSubNet.message}}"
                            isDisabledVisibile="{{true}}"
                            on-click="addSubnet('remoteSubnets')">
                            <outlined-plus/>
                            添加更多目标网络</s-tip-button>
                        <p class="red-color" s-if="existTip">
                              {{existTip}}
                              <s-tip
                                  class="inline-tip"
                                  skin="question"
                                  >
                                  <s-qusetion/>
                                  <div slot="content">
                                      <!--bca-disable-next-line-->
                                      {{existOrOverlapTip | raw}}
                                  </div>
                              </s-tip>
                          </p>
                    </s-form-item>
                    <s-form-item class="require-label"
                        s-if="{{vpnType === 'gre'}}"
                        prop="healthCheckInterval">
                        <template slot="label" class="label_class">
                            {{'健康检查间隔：'}}
                            <s-tip
                                class="inline-tip"
                                placement="right"
                                content="取值范围为取值范围为[100,1000]整数，建议设置为400。"
                                skin="question"/>
                        </template>
                        <s-input-number disabled="{{greDisabled}}" max="{{1000}}" min="{{100}}" value="{=formData.healthCheckInterval=}" precision="0"/>
                    </s-form-item>
                    <s-form-item class="require-label"
                        s-if="{{vpnType === 'gre'}}"
                        prop="healthCheckThreshold">
                        <template slot="label" class="label_class">
                            {{'健康阈值：'}}
                            <s-tip
                                class="inline-tip"
                                placement="right"
                                content="连续健康检查次数，超过此阈值检查失败时，该隧道将被认定为不可用。"
                                skin="question"/>
                        </template>
                        <s-input-number disabled="{{greDisabled}}" max="{{50}}" min="{{3}}" value="{=formData.healthCheckThreshold=}" precision="0"/>
                    </s-form-item>
                    <s-form-item label="描述：" prop="description" s-if="{{vpnType !== 'ssl'}}">
                        <s-input value="{=formData.description=}"
                        placeholder="描述不能超过200字符" width="200"
                        maxLength="200"
                        ></s-input>
                    </s-form-item>
                </div>
                <div class="content-item-box form-part-wrap" s-if="{{vpnType !== 'gre'}}">
                    <s-app-legend class="legend-wrap" label="高级配置">
                        <s-icon class="{{showAdvance ? 'advance-icon actived' : 'advance-icon'}}"
                            name="xialajiantou" slot="extra" on-click="showConfigure"
                        ></s-icon>
                    </s-app-legend>
                    <div s-if="{{vpnType === 'ipsec'}}">
                        <div class="advanced-wrap" s-if="showAdvance">
                            <div class="advanced-item-box">
                                <div class="item-label">IKE配置</div>
                                <s-form-item label="版本：">
                                    <s-select
                                        value="{=formData.ikeConfig.ikeVersion=}"
                                        width="200"
                                        datasource="{{ikeVersionList}}"
                                    >
                                    </s-select>
                                </s-form-item>
                                <s-form-item label="协商模式：">
                                    <s-select
                                        value="{=formData.ikeConfig.ikeMode=}"
                                        width="200"
                                        datasource="{{ikeModeList}}"
                                    >
                                    </s-select>
                                </s-form-item>
                                <s-form-item label="加密算法：">
                                    <s-select
                                        value="{=formData.ikeConfig.ikeEncAlg=}"
                                        width="200"
                                        datasource="{{ikeEncAlgList}}"
                                    >
                                    </s-select>
                                </s-form-item>
                                <s-form-item label="认证算法：">
                                    <s-select
                                        value="{=formData.ikeConfig.ikeAuthAlg=}"
                                        width="200"
                                        datasource="{{ikeAuthAlgList}}"
                                    >
                                    </s-select>
                                </s-form-item>
                                <s-form-item label="本端标识：" prop="ikeLocalId">
                                    <s-select
                                        value="{=formData.ikeConfig.ikeLocalType=}"
                                        width="200"
                                        datasource="{{ikeRemoteTypeList}}"
                                    >
                                    </s-select>
                                    <s-input
                                        value="{=formData.ikeConfig.ikeLocalId=}"
                                        width="200"
                                    >
                                    </s-input>
                                    <div class="tip-wrap" s-if="formData.ikeConfig.ikeLocalType !== 'IP_ADDR'">
                                        <s-tip
                                            class="inline-tip"
                                            placement="top"
                                            content="本端全称域名，例如：{{host}}"
                                            skin="question"/>
                                    </div>
                                </s-form-item>
                                <s-form-item label="远端标识：" prop="ikeRemoteId">
                                    <s-select
                                        value="{=formData.ikeConfig.ikeRemoteType=}"
                                        width="200"
                                        datasource="{{ikeRemoteTypeList}}"
                                    >
                                    </s-select>
                                    <s-input
                                        value="{=formData.ikeConfig.ikeRemoteId=}"
                                        width="200"
                                    >
                                    </s-input>
                                    <div class="tip-wrap" s-if="formData.ikeConfig.ikeRemoteType !== 'IP_ADDR'">
                                        <s-tip
                                            class="inline-tip"
                                            placement="top"
                                            content="本端全称域名，例如：{{host}}"
                                            skin="question"/>
                                    </div>
                                </s-form-item>
                                <s-form-item label="DH分组：">
                                    <s-select
                                        value="{=formData.ikeConfig.ikePfs=}"
                                        width="200"
                                        datasource="{{ikePfsList}}"
                                    >
                                    </s-select>
                                </s-form-item>
                                <s-form-item class="require-label"
                                label="SA生命周期（秒）：" prop="ikeLifeTime">
                                    <s-input
                                        value="{=formData.ikeConfig.ikeLifeTime=}"
                                        width="200"
                                        placeholder="86400"
                                    >
                                    </s-input>
                                </s-form-item>
                            </div>
                            <div class="advanced-item-box">
                                <div class="item-label">IPSec配置</div>
                                <s-form-item label="加密算法：">
                                    <s-select
                                        value="{=formData.ipsecConfig.ipsecEncAlg=}"
                                        width="200"
                                        datasource="{{ipsecEncAlgList}}"
                                    >
                                    </s-select>
                                </s-form-item>
                                <s-form-item label="认证算法：">
                                    <s-select
                                        value="{=formData.ipsecConfig.ipsecAuthAlg=}"
                                        width="200"
                                        datasource="{{ipsecAuthAlgList}}"
                                    >
                                    </s-select>
                                </s-form-item>
                                <s-form-item label="DH分组：">
                                    <s-select
                                        value="{=formData.ipsecConfig.ipsecPfs=}"
                                        width="200"
                                        datasource="{{ipsecPfsList}}"
                                    >
                                    </s-select>
                                </s-form-item>
                                <s-form-item class="require-label"
                                    label="SA生命周期（秒）：" prop="ipsecLifetime">
                                    <s-input
                                        value="{=formData.ipsecConfig.ipsecLifetime=}"
                                        width="200"
                                        placeholder="86400"
                                    >
                                    </s-input>
                                </s-form-item>
                            </div>
                        </div>
                    </div>
                    <div s-else>
                        <div class="advanced-wrap" s-if="showAdvance">
                            <s-form-item label="DNS：" prop="clientDns">
                                <s-input
                                    value="{=formData.clientDns=}"
                                    width="200"
                                    placeholder="请输入"
                                >
                                </s-input>
                            </s-form-item>
                            <s-form-item label="协议：">
                                <s-select value="{=formData.protocol=}" datasource="{{protocolList}}"></s-select>
                            </s-form-item>
                            <s-form-item label="端口：">
                                <s-input-number
                                    step="{{1}}"
                                    step-strictly
                                    value="{=formData.port=}"
                                    min="{{1}}"
                                    max="{{65535}}"
                                    on-blur="handlePortBlur"
                                    on-input="handlePortChange"
                                ></s-input-number>
                                <p class="grevpn-port" s-if="invalidPortTip">{{invalidPortTip}}</p>
                            </s-form-item>
                            <s-form-item label="加密算法：">
                                <span class="localip-wrap">aes256</span>
                            </s-form-item>
                        </div>
                    </div>
                </div>
            </s-form>
        </div>
        <div slot="pageFooter" class="buybucket">
            <div class="buybucket-container">
                <s-button skin="primary"
                    size="large"
                    disabled="{{confirmed}}"
                    on-click="onCreate">
                    确认
                </s-button>
                <s-button size="large" on-click="backToList">取消</s-button>
            </div>
        </div>
    </s-app-create-page>
</div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class VpnConnCreate extends Component {
    static components = {
        's-radio-group': Radio.RadioGroup,
        'outlined-plus': OutlinedPlus,
        's-qusetion': OutlinedQuestion
    };
    static computed = {
        title() {
            let vpnConnId = this.data.get('urlQuery.vpnConnId');
            let vpnType = this.data.get('vpnType');
            if (vpnType === 'ipsec') {
                return vpnConnId ? '编辑VPN隧道' : '创建VPN隧道';
            }
            if (vpnType === 'gre') {
                return vpnConnId ? '编辑GRE VPN隧道' : '创建GRE VPN隧道';
            }
            if (vpnType === 'ssl') {
                return vpnConnId ? '编辑SSL VPN服务端' : '创建SSL VPN服务端';
            }
        },
        pageTitle() {
            let vpnType = this.data.get('vpnType');
            if (vpnType === 'ipsec') {
                return {
                    backTo: '/network/#/vpc/vpn/list',
                    label: '返回'
                };
            }
            if (vpnType === 'ssl') {
                return {
                    backTo: '/network/#/vpc/sslvpn/list',
                    label: '返回'
                };
            } else {
                return {
                    backTo: '/network/#/vpc/grevpn/list',
                    label: '返回'
                };
            }
        },
        interfaceTypeTips() {
            const interfaceType = this.data.get('formData.interfaceType');
            return interfaceType === 'tap' ? '仅支持电脑端' : '支持电脑端及手机端';
        },
        localIpErr() {
            const localIpErrInfo = this.data.get('localIpErrInfo');
            for (let i = 0; i < localIpErrInfo.length; i++) {
                if (localIpErrInfo[i] && i < 4) {
                    return localIpErrInfo[i];
                }
            }
        },
        localIpEnable() {
            const localIp = this.data.get('formData.localConnIp');
            const localIpErrInfo = this.data.get('localIpErrInfo');
            if (
                localIp &&
                localIp[0] &&
                localIp[1] &&
                localIp[2] &&
                localIp[3] &&
                !localIpErrInfo[0] &&
                !localIpErrInfo[1] &&
                !localIpErrInfo[2] &&
                (!localIpErrInfo[3] || localIpErrInfo[3] === rule.CHANNEL.IP_H.repeatMessage)
            ) {
                return true;
            } else {
                return false;
            }
        },
        isShowRemoteNet() {
            const vpnType = this.data.get('vpnType');
            const enableBgp = this.data.get('enableBgp');
            return vpnType === 'ssl' || !enableBgp;
        },
        isShowFlag() {
            const vpnType = this.data.get('vpnType');
            const enableBgp = this.data.get('enableBgp');
            return ['ipsec', 'gre'].includes(vpnType) && enableBgp;
        },
        isShowInternetAddress() {
            const vpnType = this.data.get('vpnType');
            const enableBgp = this.data.get('enableBgp');
            return vpnType === 'gre' || (vpnType === 'ipsec' && enableBgp);
        },
        internetAddressText() {
            const vpnType = this.data.get('vpnType');
            const enableBgp = this.data.get('enableBgp');
            let localLabel = '本端互联地址';
            let remoteLabel = '对端互联地址';
            if (vpnType === 'gre' && !enableBgp) {
                localLabel = '本端隧道互联地址';
                remoteLabel = '对端隧道互联地址';
            }
            return {localLabel, remoteLabel};
        },
        remoteVpnIpTip() {
            return '输入0.0.0.0，表示对端VPN网关公网地址不固定。';
        },
        isShowPublicIp() {
            const vpnType = this.data.get('vpnType');
            const vpnInfo = this.data.get('vpnInfo');
            return (
                (vpnType === 'ipsec' && vpnInfo?.netType !== 'intranet') ||
                (vpnType === 'gre' && vpnInfo?.netType !== 'intranet')
            );
        },
        greDisabled() {
            let flag = false;
            const vpnType = this.data.get('vpnType');
            const vpnConnId = this.data.get('urlQuery.vpnConnId');
            if (vpnType === 'gre' && vpnConnId) {
                flag = true;
            }
            return flag;
        }
    };

    initData() {
        return {
            host: location.host,
            klass: 'vpn-conn-create-warp',
            localSubnetsErr: [],
            remoteSubnetsErr: [],
            formData: {
                localSubnets: [''],
                remoteSubnets: [''],
                customSubnets: [], // 自定义的IP
                description: '',
                localConnIp: [], // 本端隧道互联地址
                remoteConnIp: [], // 对端隧道互联协议
                healthCheckInterval: 400,
                healthCheckThreshold: 20,
                maskCode: 24,
                ikeConfig: {
                    ikeVersion: 'v2',
                    ikeMode: 'main',
                    ikeEncAlg: 'aes',
                    ikeAuthAlg: 'sha2_256',
                    ikeRemoteType: 'IP_ADDR',
                    ikeLocalType: 'IP_ADDR',
                    ikePfs: 'group24',
                    ikeLifeTime: '28800'
                },
                ipsecConfig: {
                    ipsecEncAlg: 'aes',
                    ipsecAuthAlg: 'sha2_256',
                    ipsecLifetime: '28800',
                    ipsecPfs: 'group24'
                },
                interfaceType: 'tap',
                encryptionMode: 'vti',
                protocol: 'tcp',
                port: 1194
            },
            localIpErrInfo: [],
            ikeVersionList: ikeConfigVersion.toArray(),
            ikeModeList: ikeConfigMode.toArray(),
            ikeEncAlgList: ikeConfigEncAlg.toArray(),
            ikeAuthAlgList: ikeConfigAuthAlg.toArray(),
            ikeRemoteTypeList: ikeConfigRemoteType.toArray(),
            ikePfsList: ikeConfigPfs.toArray(),
            ipsecEncAlgList: ipsecConfigEncAlg.toArray(),
            ipsecAuthAlgList: ipsecConfigAuthAlg.toArray(),
            ipsecPfsList: ipsecConfigPfs.toArray(),
            interfaceTypeList: [
                {
                    label: 'TAP',
                    value: 'tap'
                },
                {
                    text: 'TUN',
                    value: 'tun'
                }
            ],
            protocolList: [
                {
                    text: 'UDP',
                    value: 'udp'
                },
                {
                    text: 'TCP',
                    value: 'tcp'
                }
            ],
            cidrQuota: {
                total: 10,
                free: 10
            },
            rules: validateRules(this),
            showAdvance: false,
            maskRange: 31,
            enableBgp: false,
            existConnIp: [],
            existRemoteSubnets: [],
            existSubnets: [],
            existTip: '',
            encryptionModeList: [
                {text: '目的路由模式', value: 'vti'},
                {text: '感兴趣流模式', value: 'acl'}
            ],
            urlQuery: getQueryParams(),
            encryptionModeMessage: `目的路由模式基于目的IP进行路由转发，通常在网段数量大于2时使用，该模式支持BGP。
感兴趣流模式需要填写本端网段和对端网段，通常在网段数量较少时使用。`,
            existOrOverlapTip:
                '重叠的情况包含两种情况：<br />情况1：本端及对端网段和掩码完全一致。<br />情况2：本端及对端网段存在包含关系。',
            barbaricModeTip: '',
            invalidPortTip: '',
            initProtocol: 'tcp',
            initPort: 1194
        };
    }

    inited() {
        this.initMaskRange();
        this.data.set('enableBgp', this.data.get('urlQuery.enableBgp') === 'true' ? true : false);
        if (this.data.get('urlQuery.encryptionMode')) {
            let obj = {
                acl: '感兴趣流模式',
                vti: '目的路由模式'
            };
            this.data.set('encryptionModeList', [
                {
                    text: obj[this.data.get('urlQuery.encryptionMode')],
                    value: this.data.get('urlQuery.encryptionMode')
                }
            ]);
            this.data.set('formData.encryptionMode', this.data.get('urlQuery.encryptionMode'));
        }
        this.initBgpEncryptionModeList();
        if (!this.data.get('urlQuery.vpnConnId')) {
            this.data.set('interfaceTypeList', [
                {
                    text: 'TUN',
                    value: 'tun'
                }
            ]);
            this.data.set('formData.interfaceType', 'tun');
        }
        const vpnType = this.data.get('urlQuery.vpnType');
        this.data.set('vpnType', vpnType);
        if (vpnType === 'ssl') {
            this.data.set('showAdvance', true);
        }
        this.getVpnInfo();
        this.getVpcInfo();
        this.getCidrQuota();
        this.getSubnetList();
        this.getCidrWhiteList();
        for (let i = 0; i < 4; i++) {
            this.data.push('formData.localConnIp', '');
            this.data.push('formData.remoteConnIp', '');
        }
        this.queryVpnConnList();
        this.watchQueue();
    }
    watchQueue() {
        this.watch('formData', value => {
            const {remoteIp, ikeConfig} = value;
            const {ikeVersion, ikeMode} = ikeConfig || {};
            const isShowTip =
                remoteIp === '0.0.0.0' &&
                ((ikeVersion === 'v1' && ikeMode === 'main') ||
                    (ikeVersion === 'v2' && ikeMode === 'aggressive') ||
                    (ikeVersion === 'v2' && ikeMode === 'main'));
            if (isShowTip) {
                this.data.set('barbaricModeTip', '页面底部高级配置处用户版本需改为ikev1且协商模式改为aggressive');
                this.data.set('showAdvance', true);
            } else if (
                (remoteIp === '0.0.0.0' && ikeVersion === 'v1' && ikeMode === 'aggressive') ||
                remoteIp !== '0.0.0.0'
            ) {
                this.data.set('barbaricModeTip', '');
            }
        });
        this.watch('formData.port', value => {
            if (['22', '68', '80', '443', '500', '4500', '8003', '8005', '8006'].includes(value + '')) {
                this.data.set('invalidPortTip', '不支持使用以下端口：22、68、80、443、500、4500、8003、8005、8006');
            } else {
                this.data.set('invalidPortTip', '');
            }
        });
    }

    handlePortBlur(e: any) {
        if (!e?.target?.value) {
            this.nextTick(() => {
                this.data.set('formData.port', 1194);
            });
        }
    }

    handlePortChange({value}) {
        if (['22', '68', '80', '443', '500', '4500', '8003', '8005', '8006'].includes(value + '')) {
            this.data.set('invalidPortTip', '不支持使用以下端口：22、68、80、443、500、4500、8003、8005、8006');
        } else {
            this.data.set('invalidPortTip', '');
        }
    }
    // bgp模式不支持感兴趣流模式
    initBgpEncryptionModeList() {
        const enableBgp = this.data.get('urlQuery.enableBgp');
        const encryptionModeList = this.data.get('encryptionModeList');
        if (enableBgp === 'true') {
            this.data.set(
                'encryptionModeList',
                u.filter(encryptionModeList, item => item.value !== 'acl')
            );
        }
    }

    queryVpnConnList() {
        this.$http
            .vpnConnList({
                vpnId: this.data.get('urlQuery.vpnId'),
                vpnType: this.data.get('urlQuery.vpnType')
            })
            .then(res => {
                if (this.data.get('urlQuery.vpnConnId')) {
                    res = res.filter(item => item.vpnConnId !== this.data.get('urlQuery.vpnConnId'));
                }
                const existSubnets = [];
                this.data.set('greVpnList', res);
                this.data.get('greVpnList').forEach(item => {
                    item.localConnIp && this.data.push('existConnIp', item.localConnIp.split('/')[0]);
                    item.remoteConnIp && this.data.push('existConnIp', item.remoteConnIp.split('/')[0]);
                    this.data.push('existRemoteSubnets', ...item.remoteSubnets);
                    existSubnets.push({localSubnets: item.localSubnets, remoteSubnets: item.remoteSubnets});
                });
                this.data.set('existSubnets', existSubnets);
            });
    }

    initMaskRange() {
        const enableBgp = this.data.get('urlQuery.enableBgp');
        const vpnType = this.data.get('urlQuery.vpnType');
        if (['ipsec', 'gre'].includes(vpnType) && enableBgp === 'true') {
            this.data.set('maskRange', 30);
        }
    }

    initFormData() {
        this.$http
            .getVpnConnDetail({
                vpnId: this.data.get('urlQuery.vpnId'),
                vpnConnId: this.data.get('urlQuery.vpnConnId')
            })
            .then(formData => {
                let ikeConfig = formData.ikeConfig;
                this.data.set('isEdit', true);
                const enableBgp = this.data.get('enableBgp');
                const vpnType = this.data.get('vpnType');
                if (vpnType === 'ipsec') {
                    if (enableBgp) {
                        const localConnIp = formData?.localConnIp?.split('/')[0];
                        formData.localConnIp = localConnIp?.split('.');
                        const remoteConnIpList = formData?.remoteConnIp?.split('/');
                        formData.remoteConnIp = remoteConnIpList?.[0].split('.');
                        formData.maskCode = remoteConnIpList?.[1];
                        this.data.set('formData.localBgpAsn', formData.localBgpAsn);
                        this.data.set('formData.remoteBgpAsn', formData.remoteBgpAsn);
                    }
                    ikeConfig.ikeRemoteType = ikeConfig.ikeRemoteType || 'IP_ADDR';
                    ikeConfig.ikeLocalType = ikeConfig.ikeLocalType || 'IP_ADDR';
                    // 编辑初始化不需要额外赋值
                    ikeConfig.ikeRemoteId = ikeConfig.ikeRemoteId;
                    ikeConfig.ikeLocalId = ikeConfig.ikeLocalId;
                    if (formData.ikeConfig && formData.ikeConfig.ikeLifeTime) {
                        formData.ikeConfig.ikeLifeTime = formData.ikeConfig.ikeLifeTime.replace(/s/g, '');
                    }
                    if (formData.ipsecConfig && formData.ipsecConfig.ipsecLifetime) {
                        formData.ipsecConfig.ipsecLifetime = formData.ipsecConfig.ipsecLifetime.replace(/s/g, '');
                    }
                }

                // 获取编辑回填数据
                if (vpnType === 'gre') {
                    let localConnIp = formData.localConnIp.split('/')[0];
                    formData.localConnIp = localConnIp.split('.');
                    let remoteConnIpList = formData.remoteConnIp.split('/');
                    formData.remoteConnIp = remoteConnIpList[0].split('.');
                    formData.maskCode = remoteConnIpList[1];
                    formData.healthCheckInterval = formData.healthCheckInterval;
                    formData.healthCheckThreshold = formData.healthCheckThreshold;
                    this.data.set('formData.localBgpAsn', formData.localBgpAsn);
                    this.data.set('formData.remoteBgpAsn', formData.remoteBgpAsn);
                }

                if (vpnType === 'ssl') {
                    this.data.set('formData.protocol', formData.protocol);
                    this.data.set('initProtocol', formData.protocol || 'tcp');
                    const port = formData.port ? Number(formData.port) : 1194;
                    this.data.set('formData.port', port);
                    this.data.set('initPort', port);
                }

                this.data.set('isAdvanced', true);
                this.data.set('formData', formData);
                this.data.set('connInfo', u.cloneDeep(formData));
                this.getCidrQuota(); // 编辑时也需要校验
            });
    }

    calAvaliableIp(cidr) {
        this.nextTick(() => {
            if (!this.data.get('localIpEnable')) {
                return;
            }
            let mask = cidr.split('/')[1];
            const binary = convertCidrToBinary(cidr);
            // 最后8位
            const last = binary.slice(24, 32);
            let num = 32 - mask;
            // 网络位取0
            let zero = '';
            for (let i = 0; i < num; i++) {
                zero = zero + '0';
            }
            // 广播位取1
            let broad = '';
            for (let i = 0; i < num; i++) {
                broad = broad + '1';
            }
            // 网络
            let net = last.slice(0, mask - 24) + zero;
            // 广播
            let host = last.slice(0, mask - 24) + broad;
            // 转10进制，掩码为31时特殊处理
            let start, end;
            if (mask === '31') {
                start = parseInt(net, 2);
                end = parseInt(host, 2);
            } else {
                start = parseInt(net, 2) + 1;
                end = parseInt(host, 2) - 1;
            }
            let range = end ? `${start} - ${end}` : start;
            this.data.set('remoteRange', range);
        });
    }

    maskChange() {
        this.nextTick(() => {
            let mask = this.data.get('formData.maskCode');
            // 掩码31时去掉输入0的报错提示
            if (
                mask === 31 &&
                this.data.get('formData.localConnIp[3]') === '0' &&
                this.data.get('formData.remoteConnIp[3]') !== '0'
            ) {
                this.data.set(`localIpErrInfo[${3}]`, '');
            }
            if (
                mask === 31 &&
                this.data.get('formData.localConnIp[3]') !== '0' &&
                this.data.get('formData.remoteConnIp[3]') === '0'
            ) {
                this.data.set(`localIpErrInfo[${4}]`, '');
            }
            let ip = this.data.get('formData.localConnIp');
            let cidr = `${ip[0]}.${ip[1]}.${ip[2]}.${ip[3]}/${mask}`;
            this.calAvaliableIp(cidr);
        });
    }

    ipInput({value}, key) {
        const CONFIG_MAP = {
            0: rule.CHANNEL.IP_H,
            1: rule.CHANNEL.IP_M,
            2: rule.CHANNEL.IP_M,
            3: rule.CHANNEL.IP_H,
            4: rule.CHANNEL.IP_H
        };
        let mask = this.data.get('formData.maskCode');
        if (value === '') {
            this.data.set(`localIpErrInfo[${key}]`, '不能为空');
            return;
        }
        if (!CONFIG_MAP[key].custom(value, mask)) {
            this.data.set(`localIpErrInfo[${key}]`, CONFIG_MAP[key].customErrorMessage);
            return;
        }
        if (key === 3) {
            // 校验互联ip是否在范围内和是否重复
            if (value === this.data.get('formData.remoteConnIp')[3]) {
                this.data.set(`localIpErrInfo[${key}]`, CONFIG_MAP[key].repeatMessage);
                return;
            } else {
                this.data.set(`localIpErrInfo[4]`, '');
            }
            this.nextTick(() => {
                let ip = this.data.get('formData.localConnIp');
                let cidr = `${ip[0]}.${ip[1]}.${ip[2]}.${ip[key]}/${mask}`;
                this.calAvaliableIp(cidr);
            });
        } else if (key === 4) {
            // 校验互联ip是否在范围内和是否重复
            if (value === this.data.get('formData.localConnIp')[3]) {
                this.data.set(`localIpErrInfo[${key}]`, CONFIG_MAP[key].repeatMessage);
                return;
            } else {
                this.data.set(`localIpErrInfo[3]`, '');
            }
            let range = this.data.get('remoteRange');
            // 检验是否在输入范围内
            if (range) {
                const rangeArr = range.split('-');
                if (rangeArr.length > 1) {
                    let min = Number(rangeArr[0]);
                    let max = Number(rangeArr[1]);
                    if (Number(value) < min || Number(value) > max) {
                        this.data.set(`localIpErrInfo[${key}]`, CONFIG_MAP[key].rangeMessage);
                        return;
                    }
                } else {
                    let min = Number(rangeArr[0]);
                    if (Number(value) !== min) {
                        this.data.set(`localIpErrInfo[${key}]`, CONFIG_MAP[key].rangeMessage);
                        return;
                    }
                }
            }
        } else if (value.length === 3) {
            this.ref(`ipInput${key + 1}`) && this.ref(`ipInput${key + 1}`).focus();
        }
        this.data.set(`localIpErrInfo[${key}]`, '');
        key < 3 && this.setRemoteIp(key, value);
    }

    maskDatasource() {
        const maskRange = this.data.get('maskRange');
        let masks = [];
        for (let i = 23; i < maskRange; i++) {
            masks.push({label: i + 1, value: i + 1});
        }
        return masks;
    }

    setRemoteIp(index, value) {
        this.data.set(`formData.remoteConnIp[${index}]`, value);
    }

    getVpnInfo() {
        this.$http.getVpnDetail({vpnId: this.data.get('urlQuery.vpnId')}).then(res => {
            this.data.set('vpnInfo', res);
            this.data.get('urlQuery.vpnConnId') && this.initFormData();
        });
    }

    getVpcInfo() {
        const vpcId = this.data.get('urlQuery.vpcId');
        return this.$http
            .getVpcDetail({
                vpcIds: [vpcId]
            })
            .then(data => {
                const result = data[vpcId?.trim()] || {};
                this.data.set('vpcInfo', result);
            });
    }

    getCidrQuota() {
        const vpnId = this.data.get('urlQuery.vpnId');
        this.$http.vpnCidrQuota({vpnId}, kXhrOptions).then(res => {
            this.data.set('cidrQuota', res);
            this.checkCidrQuota();
        });
    }

    deleteSubnet(type, index) {
        this.data.splice(`formData['${type}']`, [index, 1]);
        this.checkCidrQuota();
    }

    addSubnet(type) {
        let subnetList = this.data.get('subnetList');
        let currentValue = type === 'localSubnets' ? subnetList[0].value : '';
        this.data.push(`formData['${type}']`, currentValue);
        this.checkCidrQuota();
    }

    getCidrWhiteList() {
        // 第一阶段开放fsh、fsg、hkg、su
        // 去掉白名单，改为黑名单控制
        const region = window.$context.getCurrentRegionId();
        if (!u.contains(disableVpc10Cidr, region)) {
            const whiteList = window.$storage.get('commonWhite');
            this.data.set('vpnLocalCidrWhiteList', !whiteList?.VpnLocalCidrBlackList);
        }
        this.data.set('vpnLocalCidrWhiteList', true);
        return;
    }

    checkCidrQuota() {
        let formData = this.data.get('formData');
        let cidrQuota = this.data.get('cidrQuota');
        let {localSubnets, remoteSubnets} = formData;
        let {createLocalSubNet} = checker.check(checkRules, localSubnets, '', cidrQuota);
        let {createRemoteSubNet} = checker.check(checkRules, remoteSubnets, '', cidrQuota);
        this.data.set('createRemoteSubNet', createRemoteSubNet);
        this.data.set('createLocalSubNet', createLocalSubNet);
    }

    getSubnetList() {
        const payload = {
            vpcId: this.data.get('urlQuery.vpcId'),
            attachVm: false
        };
        const isEdit = this.data.get('urlQuery.vpnConnId');
        this.$http.vpcSubnetList(payload, kXhrOptions).then(data => {
            let subnetList = u.map(data, item => ({
                value: item.cidr,
                text: item.name + '（' + item.cidr + '）'
            }));
            subnetList.push({
                text: '自定义配置',
                value: 'CUSTOM'
            });
            this.data.set('subnetList', subnetList);
            !isEdit && this.data.set('formData.localSubnets[0]', subnetList[0].value);
        });
    }

    showConfigure() {
        let showAdvance = this.data.get('showAdvance');
        this.data.set('showAdvance', !showAdvance);
    }

    // 高级配置中本端标识和远端标识手动校验（默认不展开高级配置可以填空，展开或填写其中任意一项的情况则需要校验）
    ikeIdCheck(type) {
        return new Promise((resolve, reject) => {
            const ikeId = type === 'local' ? 'ikeLocalId' : 'ikeRemoteId';
            const ikeType = type === 'local' ? 'ikeLocalType' : 'ikeRemoteType';
            let value = value || this.data.get(`formData.ikeConfig.${ikeId}`);
            let typeVal = this.data.get(`formData.ikeConfig.${ikeType}`);
            let checkPattern = {
                IP_ADDR: IP,
                FQDN: DOMAIN
            };
            if (!value) {
                return reject('标识不能为空');
            }
            if (!checkPattern[typeVal].test(value)) {
                return reject('格式不正确');
            } else {
                resolve();
            }
        });
    }

    validataEmpty() {
        const {localConnIp, remoteConnIp} = this.data.get('formData');
        localConnIp.map((value, index) => {
            this.ipInput({value}, index);
        });
        this.ipInput({value: remoteConnIp[3]}, 4, 'remote');
    }

    async onCreate() {
        let vpnType = this.data.get('vpnType');
        const enbaleBgp = this.data.get('enbaleBgp');
        let form = this.ref('form');
        let vpnLocalCidrWhiteList = this.data.get('vpnLocalCidrWhiteList');
        this.resetErr();
        if (vpnType === 'ipsec') {
            // ipsec隧道如果本端和对端标识填写了其中一项则进行手动校验，校验不通过则展开高级配置
            if (
                (!this.data.get('showAdvance') && this.data.get('formData.ikeConfig.ikeLocalId')) ||
                this.data.get('formData.ikeConfig.ikeRemoteId')
            ) {
                try {
                    await Promise.all([this.ikeIdCheck('local'), this.ikeIdCheck('remote')]);
                } catch (err) {
                    this.data.set('showAdvance', true);
                }
            } else {
                const {connInfo, formData, route} = this.data.get('');
                // 如果之前填写了本端标识和远端标识，则编辑时不能置空
                if (
                    this.data.get('urlQuery.vpnConnId') &&
                    connInfo.ikeConfig.ikeLocalId &&
                    connInfo.ikeConfig.ikeRemoteId
                ) {
                    if (!formData.ikeConfig.ikeLocalId && !formData.ikeConfig.ikeRemoteId) {
                        this.data.set('showAdvance', true);
                    }
                }
            }
        }
        // ssl服务端如果未展开高级配置且填写了dns则展开高级配置并进行校验
        else if (vpnType === 'ssl') {
            if (!this.data.get('showAdvance') && this.data.get('formData.clientDns')) {
                this.data.set('showAdvance', true);
            }
        }
        this.nextTick(async () => {
            await (form as any).validateFields();
            if (vpnType === 'gre') {
                this.validataEmpty();
            }
            if (this.data.get('localIpErrInfo').join('') !== '') {
                return;
            }
            let payload = this.getPayload();
            if (vpnType === 'gre') {
                delete payload.localSubnets;
            }
            if (vpnType === 'ipsec') {
                payload.encryptionMode = this.data.get('formData').encryptionMode;
                // 感兴趣流模式新增本端、对端网络重复或重叠校验
                if (this.data.get('formData.encryptionMode') === 'acl') {
                    const payload = this.getPayload();
                    const {existSubnets} = this.data.get('');
                    const errMessage = validateLocalRemoteRepeatOrOverlap(payload, existSubnets);
                    if (errMessage) {
                        this.data.set('existTip', errMessage);
                        return false;
                    }
                }
                // v2版本不支持野蛮模式
                if (this.data.get('barbaricModeTip')) {
                    return false;
                }
            }
            if (vpnType === 'ssl') {
                if (this.data.get('invalidPortTip')) {
                    return false;
                }
            }
            let existRemoteSubnets = this.data.get('existRemoteSubnets');
            const encryptionMode = this.data.get('formData.encryptionMode');
            let [localSubnetsErr, remoteSubnetsErr] = checkSubnets(
                payload,
                vpnLocalCidrWhiteList,
                vpnType,
                existRemoteSubnets,
                encryptionMode
            );
            this.data.set('remoteSubnetsErr', remoteSubnetsErr);
            this.data.set('localSubnetsErr', localSubnetsErr);
            // 开启bgp不再校验对端网络
            const isPassLocalSubnets = u.some(localSubnetsErr, item => item !== '');
            const isPassRemoteSubnets = enbaleBgp ? false : u.some(remoteSubnetsErr, item => item !== '');
            if (isPassRemoteSubnets || isPassLocalSubnets) {
                return;
            }
            if (payload.encryptionMode === 'vti') {
                delete payload.localSubnets;
            }
            const confirmCallback = () => {
                const requset = this.data.get('urlQuery.vpnConnId')
                    ? this.$http.vpnConnUpdate.bind(this.$http)
                    : this.$http.vpnConnCreate.bind(this.$http);
                let title = this.data.get('urlQuery.vpnConnId') ? '修改成功' : '创建成功';
                this.data.set('confirmed', true);
                requset(payload)
                    .then(res => {
                        Notification.success(title);
                        this.backToList();
                    })
                    .catch(() => {
                        this.data.set('confirmed', false);
                    });
            };
            if (['ipsec', 'gre'].includes(vpnType)) {
                confirmCallback();
            } else {
                const vpnConnId = this.data.get('urlQuery.vpnConnId');
                // 编辑时才弹框提示
                if (vpnConnId) {
                    const initProtocol = this.data.get('initProtocol');
                    const initPort = this.data.get('initPort');
                    const finalProtocol = this.data.get('formData.protocol');
                    const finalPort = this.data.get('formData.port');
                    if (finalProtocol !== initProtocol || +finalPort !== +initPort) {
                        const data = {
                            title: '编辑SSL服务端',
                            content: '当前操作会导致SSL客户端证书失效，需要重新生成证书并使用新证书进行VPN连接。'
                        };
                        confirmValidate(data, confirmCallback);
                    } else {
                        confirmCallback();
                    }
                } else {
                    confirmCallback();
                }
            }
        });
    }

    resetErr() {
        this.data.set('remoteSubnetsErr', []);
        this.data.set('localSubnetsErr', []);
    }

    getPayload() {
        let formData = u.cloneDeep(this.data.get('formData'));
        let vpnInfo = this.data.get('vpnInfo');
        let localSubnets = u.map(formData.localSubnets, (item, index) => {
            if (item === 'CUSTOM') {
                return formData.customSubnets[index];
            }
            return item;
        });
        const vpnType = this.data.get('vpnType');
        let extraData = {
            vpnId: this.data.get('urlQuery.vpnId'),
            vpnType: vpnType
        };
        const enableBgp = this.data.get('enableBgp');
        if (vpnType === 'ipsec') {
            // 当高级配置中的本端标识和对端标识值都为空时ikeRemoteType和ikeLocalType传default
            if (!formData.ikeConfig.ikeLocalId && !formData.ikeConfig.ikeRemoteId) {
                formData.ikeConfig.ikeLocalType = 'default';
                formData.ikeConfig.ikeRemoteType = 'default';
            }
            if (enableBgp) {
                formData.localBgpAsn = +formData.localBgpAsn;
                formData.remoteBgpAsn = +formData.remoteBgpAsn;
                formData.localConnIp = formData.localConnIp.join('.') + '/' + formData.maskCode;
                formData.remoteConnIp = formData.remoteConnIp.join('.') + '/' + formData.maskCode;
                delete formData.remoteSubnets;
            } else {
                delete formData.localConnIp;
                delete formData.remoteConnIp;
            }
            let {ipsecConfig, ikeConfig} = formData;
            ikeConfig.ikeRemoteId = ikeConfig.ikeRemoteId || formData.remoteIp;
            ikeConfig.ikeLocalId = ikeConfig.ikeLocalId || vpnInfo.eip;
            ipsecConfig.ipsecLifetime += 's';
            ikeConfig.ikeLifeTime += 's';
            delete formData.clientDns;
            delete formData.healthCheckInterval;
            delete formData.healthCheckThreshold;
            delete formData.protocol;
            delete formData.port;
        } else if (vpnType === 'ssl') {
            delete formData.ikeConfig;
            delete formData.ipsecConfig;
            delete formData.localConnIp;
            delete formData.remoteConnIp;
            delete formData.healthCheckInterval;
            delete formData.healthCheckThreshold;
            delete formData.description;
            formData.clientDns = formData.clientDns || '';
        } else {
            if (enableBgp) {
                formData.localBgpAsn = +formData.localBgpAsn;
                formData.remoteBgpAsn = +formData.remoteBgpAsn;
                delete formData.remoteSubnets;
            }
            formData.localConnIp = formData.localConnIp.join('.') + '/' + formData.maskCode;
            formData.remoteConnIp = formData.remoteConnIp.join('.') + '/' + formData.maskCode;
            delete formData.ikeConfig;
            delete formData.ipsecConfig;
            delete formData.protocol;
            delete formData.port;
        }
        delete formData.encryptionMode;
        delete formData.maskCode;
        return {...formData, ...extraData, localSubnets};
    }

    backToList() {
        if (this.data.get('vpnType') === 'ipsec') {
            location.hash = '#/vpc/vpn/list';
        } else if (this.data.get('vpnType') === 'ssl') {
            location.hash = '#/vpc/sslvpn/list';
        } else if (this.data.get('vpnType') === 'gre') {
            location.hash = '#/vpc/grevpn/list';
        }
    }

    onRegionChange() {
        this.backToList();
    }

    remoteSubnetsInput({value}, index) {
        if (value === '0.0.0.0' || value === '0.0.0.0/0' || value === '**********/10') {
            this.data.set(
                `remoteSubnetsErr[${index}]`,
                '请输入0.0.0.0、0.0.0.0/0、**********/10之外的私有网段，且不能与本端网络地址相同。'
            );
            this.data.set('confirmed', true);
        } else {
            this.data.set(`remoteSubnetsErr[${index}]`, '');
            this.data.set('confirmed', false);
        }
    }

    localSubnetsInput({value}, index) {
        let vpnType = this.data.get('vpnType');
        if (vpnType === 'ssl' && (value === '0.0.0.0' || value === '0.0.0.0/0' || value === '**********/10')) {
            this.data.set(
                `localSubnetsErr[${index}]`,
                '请输入0.0.0.0、0.0.0.0/0、**********/10之外的私有网段，且不能与本端网络地址相同。'
            );
            this.data.set('confirmed', true);
        } else {
            this.data.set(`localSubnetsErr[${index}]`, '');
            this.data.set('confirmed', false);
        }
    }
}
export default San2React(Processor.autowireUnCheckCmpt(VpnConnCreate));
