import u from 'lodash';
import rule from '@/pages/sanPages/utils/rule';
import {convertCidrToBinary, checkIsInSubnet} from '@/pages/sanPages/utils/common';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import {ContextService} from '@/pages/sanPages/common';

const {VPN, IP, SEG, DOMAIN, Sharedkey, VPN_IP_CIDR_UNLIMIT} = rule;

export const validateRules = self => {
    return {
        vpnConnName: [
            {
                validator: (rule, value, callback) => {
                    value = value || self.data.get('formData.vpnConnName');
                    if (!value) {
                        return callback('名称必填');
                    }
                    let pattern = VPN.NAME.pattern;
                    if (!pattern.test(value)) {
                        return callback(VPN.NAME.patternErrorMessage);
                    }
                    callback();
                }
            }
        ],
        secretKey: [
            {
                validator: (rule, value, callback) => {
                    value = value || self.data.get('formData.secretKey');
                    if (!value) {
                        return callback('共享密钥必填');
                    }
                    let length = value.length;
                    let valid = Sharedkey.test(value);
                    if (!valid) {
                        return callback('8～17位字符，英文、数字和符号必须同时存在，符号仅限!@#$%^*()_.');
                    }
                    callback();
                }
            }
        ],
        remoteIp: [
            {
                validator: (rule, value, callback) => {
                    value = value || self.data.get('formData.remoteIp');
                    let localIp = self.data.get('vpnInfo.eip');
                    let existGreVpnList = self.data.get('greVpnList').find(item => {
                        return item.remoteIp === value;
                    });
                    if (!value) {
                        return callback('对端VPN网关公网IP必填');
                    }
                    let pattern = IP;
                    if (!pattern.test(value)) {
                        return callback('格式不正确');
                    }
                    if (!new RegExp(VPN_IP_CIDR_UNLIMIT).test(value)) {
                        return callback('IP范围不符合规则');
                    }
                    if (existGreVpnList) {
                        return callback('您输入的对端VPN网关公网IP地址与该网关下其它隧道的公网IP重复，请修改');
                    }
                    if (localIp === value) {
                        return callback('您输入的对端VPN网关公网IP地址与本端VPN网关公网IP地址重复，请修改');
                    }
                    callback();
                }
            }
        ],
        ikeLifeTime: [
            {
                validator: (rule, value, callback) => {
                    value = value || self.data.get('formData.ikeConfig.ikeLifeTime');
                    const pattern = /^[0-9]+$/;
                    if (!value) {
                        return callback('请填写SA生命周期');
                    }
                    if (!pattern.test(value) || +value < 60 || +value > 86400) {
                        return callback('SA生命周期范围为60-86400');
                    }
                    callback();
                }
            }
        ],
        ipsecLifetime: [
            {
                validator: (rule, value, callback) => {
                    value = value || self.data.get('formData.ipsecConfig.ipsecLifetime');
                    const pattern = /^[0-9]+$/;
                    if (!value) {
                        return callback('请填写SA生命周期');
                    }
                    if (!pattern.test(value) || +value < 180 || +value > 86400) {
                        return callback('SA生命周期范围为180-86400');
                    }
                    callback();
                }
            }
        ],
        ikeLocalId: [
            {
                validator: (rule, value, callback) => {
                    value = value || self.data.get('formData.ikeConfig.ikeLocalId');
                    let type = self.data.get('formData.ikeConfig.ikeLocalType');
                    let checkPattern = {
                        IP_ADDR: IP,
                        FQDN: DOMAIN
                    };
                    if (!value) {
                        return callback('标识不能为空');
                    }
                    if (!checkPattern[type].test(value)) {
                        return callback('格式不正确');
                    }
                    callback();
                }
            }
        ],
        ikeRemoteId: [
            {
                validator: (rule, value, callback) => {
                    value = value || self.data.get('formData.ikeConfig.ikeRemoteId');
                    let type = self.data.get('formData.ikeConfig.ikeRemoteType');
                    let checkPattern = {
                        IP_ADDR: IP,
                        FQDN: DOMAIN
                    };
                    if (!value) {
                        return callback('标识不能为空');
                    }
                    if (!checkPattern[type].test(value)) {
                        return callback('格式不正确');
                    }
                    callback();
                }
            }
        ],
        clientDns: [
            {
                validator: (rule, value, callback) => {
                    value = value || self.data.get('formData.clientDns');
                    let pattern = IP;
                    if (value && !pattern.test(value)) {
                        return callback('格式不正确');
                    }
                    callback();
                }
            }
        ],
        localConnIp: [
            {required: true, message: '不能为空'},
            {
                validator: (rule, value, callback) => {
                    let existConnIp = self.data.get('existConnIp');
                    value = self.data.get('formData.localConnIp');
                    let currentIp = `${value[0]}.${value[1]}.${value[2]}.${value[3]}`;
                    if (existConnIp && existConnIp.indexOf(currentIp) > -1) {
                        return callback('您输入的隧道互联地址与该网关下其它隧道的互联地址重复，请修改');
                    }
                    callback();
                }
            }
        ],
        encryptionModeMessage: [{required: true, message: '不能为空'}],
        remoteConnIp: [
            {required: true, message: '不能为空'},
            {
                validator: (rule, value, callback) => {
                    let existConnIp = self.data.get('existConnIp');
                    value = self.data.get('formData.remoteConnIp');
                    let currentIp = `${value[0]}.${value[1]}.${value[2]}.${value[3]}`;
                    if (existConnIp && existConnIp.indexOf(currentIp) > -1) {
                        return callback('您输入的隧道互联地址与该网关下其它隧道的互联地址重复，请修改');
                    }
                    callback();
                }
            }
        ],
        localBgpAsn: [
            {required: true, message: '本端BGP ASN必填'},
            {
                validator(rule, value, callback) {
                    let pattern = /^[1-9]\d*$/;
                    const remoteBgpAsn = self.data.get('formData.remoteBgpAsn');
                    if (value === remoteBgpAsn) {
                        return callback('本端BGP ASN不能与对端BGP ASN相同');
                    }
                    if (!pattern.test(value) || +value > 4294967295) {
                        return callback('格式错误');
                    }
                    return callback();
                }
            }
        ],
        remoteBgpAsn: [
            {required: true, message: '对端BGP ASN必填'},
            {
                validator(rule, value, callback) {
                    let pattern = /^[1-9]\d*$/;
                    const localBgpAsn = self.data.get('formData.localBgpAsn');
                    if (value === localBgpAsn) {
                        return callback('对端BGP ASN不能与本端BGP ASN相同');
                    }
                    if (!pattern.test(value) || +value > 4294967295) {
                        return callback('格式错误');
                    }
                    return callback();
                }
            }
        ]
    };
};

const localSubnetsRule = (value, local) => {
    if (!value) {
        return `${local}必填`;
    }
    if (!SEG.test(value)) {
        return 'CIDR格式不合法';
    }

    let string = convertCidrToBinary(value);

    let mask = value.split('/')[1];
    if (u.isUndefined(value)) {
        mask = 32;
    }

    if (string.substring(+mask, string.length).includes('1')) {
        return 'CIDR格式不合法';
    }
    return '';
};

const remoteSubnetsRule = (value, inWhiteList, vpnType, remote, existRemoteSubnets, encryptionMode) => {
    if (!value) {
        return `${remote}必填`;
    }

    if (!SEG.test(value)) {
        return 'CIDR格式不合法';
    }

    let string = convertCidrToBinary(value);

    let mask = value.split('/')[1];
    if (u.isUndefined(mask)) {
        mask = 32;
    }

    if (string.substring(+mask, string.length).includes('1')) {
        return 'CIDR格式不合法';
    }

    // ssl客户端网络只校验网段合法性，下面不需要校验
    if (inWhiteList || vpnType === 'ssl') {
        return '';
    }
    /* eslint-disable */
    let map = {
        '10.63.0.0/16': true,
        '10.132.0.0/16': true,
        '10.133.0.0/16': true,
        '10.135.0.0/16': true,
        '10.136.0.0/16': true,
        '10.180.0.0/16': true,
        '10.181.0.0/16': true,
        '10.190.0.0/16': true,
        '10.191.0.0/16': true
    };
    /* eslint-enable */
    for (let i = 1; i <= 20; i++) {
        map['10.' + i + '.0.0/16'] = true;
    }

    let result = true;
    /* eslint-disable */
    for (let cidr in map) {
        /* eslint-enable */
        if (checkIsInSubnet(value, cidr) || checkIsInSubnet(cidr, value)) {
            result = false;
            break;
        }
    }
    if (!result) {
        return '暂不支持配置该网段';
    }
    // 同一网关下的隧道之间的对端网络不一致 (ipsec 感兴趣流模式同时校验本端网络和对端网络)
    if (
        vpnType !== 'ipsec' &&
        encryptionMode !== 'acl' &&
        existRemoteSubnets &&
        existRemoteSubnets.indexOf(value) > -1
    ) {
        return '您输入的对端网络与该网关下其它隧道的对端网络重复，请修改';
    }
    return '';
};

// 检查自己的网络，有无重复
const checkSubnetsRepeat = (list, item) => {
    return u.filter(list, value => value === item).length > 1;
};

// 检查与其他网络是否有无重复
const checkOtherCidrRepeat = (list, item) => {
    return u.indexOf(list, item) !== -1;
};

// 检查与本地网络有无重叠
const checkLocaleCidr = (localCidrs, value) => {
    return u.find(localCidrs, item => checkIsInSubnet(value, item) || checkIsInSubnet(item, value));
};

// 检查与其他网络有无重叠
const checkSelfCidr = (cidrs, value) => {
    const remoteList = u.filter(cidrs, item => item !== value);
    return u.find(remoteList, item => checkIsInSubnet(value, item) || checkIsInSubnet(item, value));
};

// 检查本端网络重复，重叠
export const checkLocaleAll = (localSubnets, vpnType) => {
    let local = vpnType === 'ipsec' ? '本端网络' : '服务端网络';
    let localSubnetsErr = [];
    for (let i = 0; i < localSubnets.length; i++) {
        const item = localSubnets[i];
        const ruleCheck = localSubnetsRule(item, local);
        if (checkSubnetsRepeat(localSubnets, item)) {
            localSubnetsErr.push(`${local}之间有重复`);
            break;
        }
        if (checkSelfCidr(localSubnets, item)) {
            localSubnetsErr.push(`${local}之间有重叠`);
            break;
        }
        if (ruleCheck !== '') {
            localSubnetsErr.push(ruleCheck);
            break;
        }
        localSubnetsErr.push('');
    }
    return localSubnetsErr;
};

// 检查对端网络有无重复，重叠
export const checkRemoteAll = (
    remoteSubnets,
    localSubnets,
    inWhiteList,
    vpnType,
    existRemoteSubnets,
    encryptionMode
) => {
    let local = vpnType === 'ipsec' || vpnType === 'gre' ? '本端网络' : '服务端网络';
    let remote = vpnType === 'ipsec' || vpnType === 'gre' ? '对端网络' : '客户端网络';
    let remoteSubnetsErr = [];
    // 存在才去检查
    if (remoteSubnets) {
        for (let i = 0; i < remoteSubnets.length; i++) {
            const item = remoteSubnets[i];
            const ruleCheck = remoteSubnetsRule(item, inWhiteList, vpnType, remote, existRemoteSubnets, encryptionMode);
            if (ruleCheck !== '') {
                remoteSubnetsErr.push(ruleCheck);
                break;
            }
            if (checkSubnetsRepeat(remoteSubnets, item)) {
                remoteSubnetsErr.push(`和其他${remote}有重复`);
                break;
            }
            if (checkOtherCidrRepeat(localSubnets, item)) {
                remoteSubnetsErr.push(`和${local}有重复`);
                break;
            }
            if (checkLocaleCidr(localSubnets, item)) {
                remoteSubnetsErr.push(`和${local}有重叠`);
                break;
            }
            if (checkSelfCidr(remoteSubnets, item)) {
                remoteSubnetsErr.push(`和其他${remote}有重叠`);
            }
            remoteSubnetsErr.push('');
        }
    }
    return remoteSubnetsErr;
};

export const checkSubnets = (payload, inWhiteList, vpnType, existRemoteSubnets, encryptionMode) => {
    let {remoteSubnets, localSubnets} = payload;
    let localSubnetsErr = localSubnets && checkLocaleAll(localSubnets, vpnType);
    let remoteSubnetsErr = checkRemoteAll(
        remoteSubnets,
        localSubnets,
        inWhiteList,
        vpnType,
        existRemoteSubnets,
        encryptionMode
    );
    return [localSubnetsErr, remoteSubnetsErr];
};
const validateExist = (subnets: string[], existSubnets: string[]) => {
    let existFlag = false;
    for (let i = 0; i < subnets.length; i++) {
        const subnet = subnets[i];
        for (let j = 0; j < existSubnets.length; j++) {
            const existSubnet = existSubnets[j];
            if (
                subnet === existSubnet ||
                checkIsInSubnet(subnet, existSubnet) ||
                checkIsInSubnet(existSubnet, subnet)
            ) {
                existFlag = true;
                break;
            }
        }
        if (existFlag) {
            break;
        }
    }
    return existFlag;
};

const validateRepOrOverlap = (subnets: Record<string, any>, existSubnets: any[]) => {
    const {currLocalSubnets, currRemoteSubnets} = subnets;
    const isExistRepeatOrOverlap = existSubnets.some(item => {
        let existFlag = false;
        const {localSubnets, remoteSubnets} = item;
        const isCurrLocalExist = validateExist(currLocalSubnets, localSubnets);
        const isCurrRemoteExist = validateExist(currRemoteSubnets, remoteSubnets);
        if (isCurrLocalExist && isCurrRemoteExist) {
            existFlag = true;
        }
        return existFlag;
    });
    return isExistRepeatOrOverlap;
};
/** 校验本端、对端网络是否和同一网关下的其他隧道本端和对端网络存在重复或重叠 */
export const validateLocalRemoteRepeatOrOverlap = (payload, existSubnets) => {
    let errMessage = '';
    const {localSubnets, remoteSubnets} = payload;
    const currentSubnets = {currLocalSubnets: localSubnets, currRemoteSubnets: remoteSubnets};
    const existLocalRepOrOverlap = validateRepOrOverlap(currentSubnets, existSubnets);
    if (existLocalRepOrOverlap) {
        errMessage =
            '您当前VPN隧道的本端网络和对端网络与该VPN网关下的其他隧道网段重叠，请修改本端网络或对端网络任意一个即可。';
    }
    return errMessage;
};
