.vpn-conn-create-warp {
    .s-form {
        .s-form-item-label {
            width: 186px;
            height: 30px;
        }
    }
    .ssl-form-class {
        .s-form {
            .s-form-item-label {
                width: 114px;
                height: 30px;
            }
        }
    }
    .custom-input {
        display: block;
        width: 200px;
        box-sizing: border-box;
    }
    .s-input input {
        box-sizing: border-box;
    }
    .legend-wrap {
        margin: 10px 0;
    }
    .content-wrap {
        border-radius: 6px;
        margin-top: 16px;
        background-color: #f7f7f9 !important;
        text-align: left;
        box-sizing: content-box;
        .s-form-item-with-help {
            margin-bottom: 24px;
        }
        .ips-wrap {
            .s-input input {
                text-align: center;
            }

            .s-select .s-input-area {
                width: 40px;
                input {
                    text-align: left;
                }
            }

            .s-form-item-error {
                padding-bottom: 0;
            }
        }
        .input-error {
            border-color: #f33e3e;

            &:hover {
                border-color: #f33e3e;
            }
        }
    }
    .tip-wrap {
        display: inline-block;
        margin-left: -5px;
        vertical-align: middle;
        .inline-tip {
            top: 3px;
            position: relative;
            .s-tip-warning {
                justify-content: center;
                .warning_class {
                    fill: #999;
                }
            }
            .s-tip:hover .s-icon path {
                fill: #2468f2 !important;
            }
        }
    }
    .tip-icon-wrap {
        font-size: 14px;
        border: 1px solid #f18823;
        margin-left: 10px;
        color: #f18823;
        &:hover {
            color: #fff;
            background-color: #f18823;
        }
    }
    .row-line {
        display: flex;
        align-items: center;
    }
    .grevpn-port {
        margin-top: 8px;
        color: #f33e3e;
    }
    .localip-wrap {
        display: flex;
        align-items: center;
        height: 30px;
    }
    .subnets-wrap {
        .s-select,
        .s-input {
            margin-bottom: 5px;
        }
    }
    .remoteSubnets-tip {
        display: block;
        margin: 5px 0;
        color: #f33e3e;
    }
    .advanced-wrap {
        .s-inputnumber {
            width: 100px !important;
        }
    }
    .advanced-item-box {
        .item-label {
            margin: 40px 0 16px;
            display: block;
            color: #151b26;
            line-height: 24px;
            font-weight: 500;
            font-size: 16px;
        }
    }
    .advance-icon {
        margin-left: 10px;
        transition: all 0.3s ease;
        transform: rotate(0);
    }
    .advance-icon.actived {
        transform: rotate(180deg);
    }
    .require-label {
        position: relative;
        &:before {
            content: '*';
            left: -10px;
            top: 8px;
            position: absolute;
            color: #f33e3e;
            margin-right: 4px;
        }
        .remoteIp-tip {
            color: #f33e3e;
            margin-top: 4px;
        }
    }
    .margin-top-none {
        margin-top: 0 !important;
        label::before {
            display: none;
        }
    }

    .localIp-wrap {
        display: flex;
        span {
            width: 10px;
            text-align: center;
            line-height: 26px;
        }
        .s-input-suffix-container {
            width: 80px;
        }
        .s-selectdropdown {
            width: 80px !important;
            text-align: left;
        }
    }
    .delete-icon {
        color: #2468f2;
    }

    .red-color {
        color: #f33e3e;
        display: flex;
        .inline-tip {
            top: 3px;
            position: relative;
            .s-tip-warning {
                justify-content: center;
                .warning_class {
                    fill: #999;
                }
            }
            .s-tip:hover .s-icon path {
                fill: #2468f2 !important;
            }
        }
    }
    .form-part-wrap {
        border-radius: 6px;
        width: calc(~'100vw - 32px');
        background: #fff;
        padding: 24px;
        margin-bottom: 16px;
        h4 {
            display: block;
            color: #151b26;
            line-height: 24px;
            font-weight: 500;
            font-size: 16px;
            zoom: 1;
            margin: 0 0 16px;
            padding: 0;
        }
        .label_class {
            .inline-tip {
                top: 3px;
                left: -5px;
                position: relative;
                .s-tip-warning {
                    justify-content: center;
                    .warning_class {
                        fill: #999;
                    }
                }
                .s-tip:hover .s-icon path {
                    fill: #2468f2 !important;
                }
            }
        }
        .center_class {
            .s-row {
                .s-form-item-control-wrapper {
                    line-height: 30px;
                }
            }
        }
        .s-legend {
            .s-legend-highlight::before {
                content: none;
            }
        }
        .s-form-item-label {
            .inline-tip {
                top: 3px;
                left: -5px;
                position: relative;
                .s-tip-warning {
                    justify-content: center;
                    .warning_class {
                        fill: #999;
                    }
                }
                .s-tip:hover .s-icon path {
                    fill: #2468f2 !important;
                }
            }
        }
    }
    .inline-form {
        h4 {
            float: left;
        }
        .tag-edit-panel {
            margin-left: 130px;
        }
    }
    .buybucket {
        width: 100%;
        left: 0;
        height: 60px;
        .buybucket-container {
            width: auto;
            float: left;
            height: 48px;
            transform: translateY(12.5%);
            display: flex;
            align-items: center;
            .s-button {
                margin-left: 16px;
            }
        }
    }
    .flavor-line {
        position: relative;
        top: 7px;
    }
}
