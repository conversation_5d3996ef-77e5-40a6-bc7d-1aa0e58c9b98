/*
 * @Description: ca证书
 * @Author: wang<PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2021-11-10 19:11:46
 */

/* eslint-disable */
export const caCert = (eip, row) => `client
dev ${row.interfaceType}
proto ${row?.protocol || 'tcp'}
explicit-exit-notify 3
remote ${eip || 'xxx.xxx.xxx.xxx'} ${row?.port || '1194'} # xxx.xxx.xxx.xxx 为用户绑定SSL VPN服务端的公网IP。
resolv-retry infinite
nobind
persist-key
persist-tun

cipher AES-256-CBC
ns-cert-type server
auth-user-pass

comp-lzo
verb 3


<ca>
-----B<PERSON><PERSON> CERTIFICATE-----
MIIDkDCCAngCCQDLdweKu4iKwzANBgkqhkiG9w0BAQsFADCBiTELMAkGA1UEBhMC
Q04xEDAOBgNVBAgMB0JFSUpJTkcxEDAOBgNVBAcMB0JFSUpJTkcxDjAMBgNVBAoM
BUJBSURVMQ0wCwYDVQQLDARWTkVUMQ8wDQYDVQQDDAZTU0xWUE4xJjAkBgkqhkiG
9w0BCQEWF21hcGVuZ2NoZW5nMDJAYmFpZHUuY29tMB4XDTI0MDIyNjEwMDYzN1oX
DTQ0MDIyMTEwMDYzN1owgYkxCzAJBgNVBAYTAkNOMRAwDgYDVQQIDAdCRUlKSU5H
MRAwDgYDVQQHDAdCRUlKSU5HMQ4wDAYDVQQKDAVCQUlEVTENMAsGA1UECwwEVk5F
VDEPMA0GA1UEAwwGU1NMVlBOMSYwJAYJKoZIhvcNAQkBFhdtYXBlbmdjaGVuZzAy
QGJhaWR1LmNvbTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAM5d4Fgb
laGzZe6EfpbL22S40ZJ1BLVS2CULeDa0xOpyQRqgbDP1kHq4A8xLQATPaoUQ7gRG
f/BYwdocGkHOiUKGkqRy/MDC+4iRfLoGzACGcAnpUEuEYM7eye+gyLug3ARrtxID
B6hL7DNLDouXyqogPlq2Mbq/YdUYvjTKyaSyxeFBPsRdG+4SdQNoDZGplLQBGdd3
RRbhJDT6Pn9AdemhUlhxZBFSXLTW3mhCy4TFUeCbFYPTOdJHNDvWzT0Yf4xuAQqy
48pY57QJtVuLCgd8wNy6niDx8z0KU7VrPLcDOwjyt0of4/BhRhjK9dSCHpje0LV9
5C/ypZR/zLBTEG8CAwEAATANBgkqhkiG9w0BAQsFAAOCAQEABYkGm95BA3iy7DI+
iYvpnnmlqyT9GhXHuuXgObmUf7F1cjUhzlGc7j+3EOP0G/Du6CF8s5kU+DWCGcE/
f09PMYx6RAS6WNyljUKYBUNX1CwLDVpGLh3IbblIt9dBWv2Tdq7PgUyOjrIJTOek
iwwRToprh5lMuWRpEh8VE7wBkylFbVeoPpwfRSzbvc2qIEkOIj8TQBwRl91wnmPo
dWP0QzKE/m0YhrlsvtASe2GuH0lNoome2YM0rdcMSQKUU1YfkAP3IbR1S+Lll52b
QgbpRlRxj2fU03gXkE2Ji0Jos540ri4g+oM3kItVV26P4wG4s0KrKMX98NTvHSuv
uTw1UA==
-----END CERTIFICATE-----
</ca>`;
