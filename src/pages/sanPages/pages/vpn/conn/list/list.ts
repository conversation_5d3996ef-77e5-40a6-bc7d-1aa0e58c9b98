import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {checker} from '@baiducloud/bce-opt-checker';
import {request} from '@/apis';
import _ from 'lodash';
import {OutlinedRefresh, OutlinedSetting, OutlinedPlus} from '@baidu/sui-icon';
import {AnalysisSDKProcessor} from '@baidu/bce-vpc-sdk-react';
import {Notification} from '@baidu/sui';

import MoreOperation from '../../components/MoreOperation';
import Monitor from '../monitorChart/monitor';
import rules from '../../rule';
import {columns} from './tableField';
import {caCert} from './cert';
import {
    VpnConnStatus,
    ipsecAllSaActive,
    SslConnStatus,
    GreHealthStatus,
    bgpVpnStatus
} from '@/pages/sanPages/common/enum';
import Confirm from '@/pages/sanPages/components/confirm';
import {utcToTime} from '@/pages/sanPages/utils/helper';
import './style.less';
import '@baidu/bce-vpc-sdk-react/lib/style.css';

const processorPath = new AnalysisSDKProcessor();
const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent, invokeComp} = decorators;

const ipsecColumn = [
    'id',
    'healthStatus',
    'diagnose',
    'encryptionMode',
    'allSaActive',
    'eip',
    'localCidrs',
    'remoteCidrs',
    'remoteSideIp',
    'createTime',
    'description',
    'opt'
];
const sslColumn = ['vpnConnId', 'status', 'interfaceType', 'localSubnets', 'remoteSubnets', 'opt'];
const greColumn = [
    'id',
    'healthStatus',
    'diagnose',
    'eip',
    'remoteCidrs',
    'remoteSideIp',
    'createTime',
    'description',
    'opt',
    'localConnIp',
    'remoteConnIp',
    'healthCheckInterval',
    'healthCheckThreshold'
];

const tpl = html`
    <div>
        <s-app-list-page class="{{klass}}">
            <div slot="bulk">
                <s-tip-button
                    s-if="vpnType !== 'ssl'"
                    disabled="{{addVpnConn.disable || table.loading}}"
                    skin="primary"
                    isDisabledVisibile="{{true}}"
                    on-click="onCreate"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{addVpnConn.message | raw}}
                    </div>
                    <outlined-plus />
                    {{'创建VPN隧道'}}
                </s-tip-button>
                <s-tip-button
                    s-else
                    disabled="{{addSslServer.disable || table.loading}}"
                    skin="primary"
                    isDisabledVisibile="{{true}}"
                    on-click="onCreate"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{addSslServer.message | raw}}
                    </div>
                    <outlined-plus />
                    {{'创建SSL VPN服务端'}}
                </s-tip-button>
                <s-tip-button
                    class="left_class"
                    s-if="vpnType === 'ipsec' || vpnType === 'gre'"
                    disabled="{{deleteVpnConn.disable}}"
                    isDisabledVisibile="{{true}}"
                    on-click="deleteVpnConn"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{deleteVpnConn.message | raw}}
                    </div>
                    删除</s-tip-button
                >
                <s-tip-button
                    s-else
                    class="left_class"
                    disabled="{{deleteSslVpnConn.disable}}"
                    isDisabledVisibile="{{true}}"
                    on-click="deleteVpnConn"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{deleteSslVpnConn.message | raw}}
                    </div>
                    删除</s-tip-button
                >
            </div>
            <div slot="filter">
                <s-button class="s-icon-button" on-click="refresh" track-name="刷新"
                    ><outlined-refresh class="icon-class"
                /></s-button>
                <s-table-column-toggle
                    class="custom-column-qos s-button icon-column left_class"
                    datasource="{{customColumn.datasource}}"
                    value="{=customColumn.value=}"
                    on-change="onCustomColumns"
                ></s-table-column-toggle>
            </div>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                class="vpn-conn-list-table"
                datasource="{{table.datasource}}"
                on-selected-change="tableSelected($event)"
                on-filter="onFilter"
                on-sort="onSort"
                selection="{=table.selection=}"
                track-id="ti_vpc_instance_table"
                track-name="列表操作"
            >
                <div slot="empty">
                    <table-empty desc="{{'暂无数据。'}}" showAction="{{false}}" />
                    <!--<s-empty on-click="onCreate">
                </s-empty>-->
                </div>
                <span slot="h-allSaActive-lable">
                    {{'安全联盟状态'}}
                    <s-tip
                        class="inline-tip"
                        placement="right"
                        content="{{'IPsec VPN隧道的SA，不含IKE SA'}}"
                        skin="question"
                    />
                </span>
                <div slot="c-id">
                    <div s-if="{{vpnType === 'ipsec' || vpnType === 'gre'}}">
                        <span class="text-hidden conn-name">{{row.vpnConnName}}</span>
                        <br />
                        <span class="text-hidden">{{row.vpnConnId}}</span>
                    </div>
                </div>
                <div slot="c-vpnConnId">
                    <div s-if="{{vpnType === 'ssl'}}">
                        <a href="{{row | sslDetailHref}}" class="truncated"> {{row.vpnConnName}} </a>
                        <br />
                        <span class="text-hidden">{{row.vpnConnId}}</span>
                    </div>
                </div>
                <div slot="c-status">
                    <span s-if="{{vpnType === 'ssl'}}" class="{{row.status | sslStatusClass}}"
                        >{{row.status | sslStatusText}}</span
                    >
                </div>
                <div slot="c-healthStatus">
                    <span s-if="{{vpnType === 'ipsec'}}" class="{{row.healthStatus | statusClass}}">
                        {{row.healthStatus | statusText}}
                    </span>
                    <s-tip
                        s-if="{{vpnType === 'ipsec' && row.healthStatus === 'unreachable' && row.negotiation}}"
                        class="inline-tip"
                        placement="right"
                        content="{{row.negotiation}}"
                        skin="question"
                    />
                    <span class="{{row.healthStatus | greHealthStatusClass}}" s-if="{{vpnType === 'gre'}}">
                        {{row.healthStatus | greHealthStatusText}}</span
                    >
                    <s-tip
                        s-if="{{vpnType === 'gre' && row.healthStatus === 'unreachable'}}"
                        class="inline-tip"
                        placement="right"
                        content="{{'请检测隧道两端公网及隧道互联地址等配置并修改'}}"
                        skin="question"
                    />
                </div>
                <div slot="c-bgpStatus">
                    <span class="{{row.bgpStatus | bgpStatusClass}}">{{row.bgpStatus | getBgpStatus}}</span>
                </div>
                <div slot="c-allSaActive">
                    <span s-if="{{vpnType === 'ipsec' && row.encryptionMode === 'vti'}}">-</span>
                    <span
                        s-if="{{vpnType === 'ipsec' && row.encryptionMode !== 'vti'}}"
                        class="{{row.allSaActive | allSaClass}}"
                    >
                        {{row.allSaActive | allSaText}}
                    </span>
                    <s-popover
                        on-visibleChange="accessShow"
                        class="access-list-popover"
                        placement="right"
                        trigger="click"
                    >
                        <div class="access-list-warp" slot="content">
                            <span class="access-err-title">异常网段展示，建议：请用户检查未协商网段。</span>
                            <s-table
                                class="access-list-table"
                                loading="{{accessTable.loading}}"
                                columns="{{accessTable.columns}}"
                                datasource="{{accessTable.datasource}}"
                            >
                                <div slot="empty">
                                    <s-empty>
                                        <div slot="action"></div>
                                    </s-empty>
                                </div>
                            </s-table>
                        </div>
                        <s-icon
                            on-click="getAccessList(row.vpnConnId)"
                            s-if="{{!row.allSaActive}}"
                            class="tip-icon"
                            name="question-mark"
                        >
                        </s-icon>
                    </s-popover>
                </div>
                <div slot="c-createTime">{{row | getTime}}</div>
                <div slot="h-eip">
                    <template s-if="netType === 'intranet'">本端私网IP</template>
                    <template s-else>本端VPN网关公网IP</template>
                </div>
                <div slot="h-remoteCidrs">
                    <template s-if="netType === 'intranet'">对端VPN网关IP</template>
                    <template s-else>对端VPN网关公网IP</template>
                </div>
                <div slot="c-eip">
                    <template s-if="netType === 'intranet'">{{internalIp || '-'}}</template>
                    <template s-else>{{eip || '-'}}</template>
                </div>
                <div slot="c-remoteCidrs">{{row.remoteIp}}</div>
                <div slot="c-encryptionMode">{{row.encryptionMode | getEncryptionMode}}</div>
                <div slot="c-localCidrs">
                    <span class="truncated">{{row.localSubnets | getSubnets}}</span>
                </div>
                <div slot="c-interfaceType">
                    <span class="truncated">{{row.interfaceType | interfaceTypeUpper}}</span>
                </div>
                <div slot="c-localSubnets">
                    <span class="truncated">{{row.localSubnets | getSubnets}}</span>
                </div>
                <div slot="c-remoteSideIp">
                    <span class="truncated">{{row.remoteSubnets | getSubnets}}</span>
                </div>
                <div slot="c-description">
                    <span class="truncated">{{row.description || '-'}}</span>
                </div>
                <div slot="h-diagnose">
                    <span
                        >路径分析
                        <div class="new-tag new-instance-tag">new</div></span
                    >
                </div>
                <div slot="c-diagnose">
                    <s-button skin="stringfy" on-click="pathAnalysisVpnConn(row)">路径分析</s-button>
                </div>
                <div slot="c-opt" class="operations">
                    <s-button
                        s-if="{{vpnType === 'ssl' && sslUserCount > 0}}"
                        skin="stringfy"
                        on-click="caCertDownload(row)"
                        >证书下载</s-button
                    >
                    <s-button skin="stringfy" disabled="{{row.healthStatus === 'unknown'}}" on-click="editConn(row)"
                        >修改配置</s-button
                    >
                    <s-button s-if="{{vpnType === 'ssl'}}" skin="stringfy" on-click="userManage(row)">
                        用户管理
                    </s-button>
                    <s-button s-if="{{vpnType === 'ssl'}}" skin="stringfy" on-click="goMonitor(row)"> 监控 </s-button>
                    <s-button s-if="{{vpnType !== 'ssl'}}" skin="stringfy" on-click="showMonitor(row)">监控</s-button>
                    <s-button
                        skin="stringfy"
                        on-click="resetIpsec(row)"
                        disabled="{{status !== 'active'}}"
                        s-if="{{vpnType === 'ipsec'}}"
                        >重置</s-button
                    >
                    <s-button s-if="{{vpnType !== 'ssl'}}" skin="stringfy" on-click="toAlarmDetail(row)"
                        >报警详情</s-button
                    >
                    <x-moreoperation s-if="{{vpnType === 'ssl'}}" item="{{row}}" />
                </div>
            </s-table>
        </s-app-list-page>
        <div id="satisfactionNew" s-if="drawerVisible"></div>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@invokeComp('@table-empty')
@asComponent('@vpn-conn-list')
class VpnConnList extends Component {
    static components = {
        'outlined-refresh': OutlinedRefresh,
        'outlined-plus': OutlinedPlus,
        'outlined-setting': OutlinedSetting,
        'x-moreoperation': MoreOperation
    };
    initData() {
        const allColumns = columns.slice();
        const customColumnDb = allColumns.map(item => ({
            text: item.label,
            value: item.name,
            disabled: item.name === 'id' || item.name === 'opt' || item.name === 'vpnConnId'
        }));
        return {
            klass: 'vpn-conn-wrap',
            editTag: {},
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                allColumns,
                datasource: [],
                selectedItems: []
            },
            accessTable: {
                loading: false,
                datasource: [],
                columns: [
                    {name: 'localCidr', label: '本端网络', width: '50%'},
                    {name: 'remoteCidr', label: '对端网络', width: '50%'}
                ]
            },
            customColumn: {
                value: [
                    'id',
                    'vpnConnId',
                    'status',
                    'healthStatus',
                    'diagnose',
                    'encryptionMode',
                    'allSaActive',
                    'eip',
                    'localCidrs',
                    'interfaceType',
                    'localSubnets',
                    'remoteCidrs',
                    'localConnIp',
                    'remoteConnIp',
                    'remoteSubnets',
                    'remoteSideIp',
                    'opt'
                ],
                datasource: customColumnDb
            },
            sslUserCount: 0,
            drawerVisible: true,
            visibleDraw: true,
            openDrawer: false
        };
    }

    static filters = {
        statusClass(value) {
            return VpnConnStatus.fromValue(value).styleClass || '';
        },
        statusText(value) {
            return value ? VpnConnStatus.getTextFromValue(value) : '-';
        },
        allSaClass(value) {
            return ipsecAllSaActive.fromValue(value).styleClass || '';
        },
        allSaText(value) {
            return ipsecAllSaActive.getTextFromValue(value);
        },
        bgpStatusClass(value) {
            return bgpVpnStatus.fromValue(value).styleClass || 'status error';
        },
        getBgpStatus(value) {
            return bgpVpnStatus.getTextFromValue(value) || '未连接';
        },
        sslStatusClass(value) {
            return SslConnStatus.fromValue(value).styleClass || '';
        },
        sslStatusText(value) {
            return value ? SslConnStatus.getTextFromValue(value) : '-';
        },
        getTime(item) {
            return item.createdTime ? utcToTime(item.createdTime) : '-';
        },
        getSubnets(value) {
            return value.length ? value.join(',') : '-';
        },
        sslDetailHref(row) {
            let vpcId = this.data.get('vpcId');
            let vpnType = this.data.get('vpnType');
            let {vpnId, vpnConnId} = row;
            return `#/vpc/vpn/ssl/detail?vpcId=${vpcId}&vpnId=${vpnId}&vpnType=${vpnType}&vpnConnId=${vpnConnId}`;
        },
        interfaceTypeUpper(value) {
            return value ? value.toUpperCase() : '-';
        },
        greHealthStatusClass(value) {
            return GreHealthStatus.fromValue(value).styleClass || '';
        },
        greHealthStatusText(value) {
            return value ? GreHealthStatus.getTextFromValue(value) : '-';
        },
        getEncryptionMode(value) {
            return value ? (value === 'acl' ? '感兴趣流模式' : '目的路由模式') : '-';
        }
    };
    static computed = {
        actionAuth() {
            const vpnType = this.data.get('vpnType');
            const addVpnConn = this.data.get('addVpnConn') || {};
            const addSslServer = this.data.get('addSslServer') || {};
            const actionAuth = vpnType === 'ssl' ? addSslServer : addVpnConn;
            actionAuth.disable = actionAuth?.disable;

            return actionAuth;
        }
    };
    inited() {
        let {deleteVpnConn} = checker.check(rules, [], '');
        let userCount = this.data.get('sslUserCount');
        let {deleteSslVpnConn} = checker.check(rules, [], '', {userCount: userCount});
        this.data.set('deleteVpnConn', deleteVpnConn);
        this.data.set('deleteSslVpnConn', deleteSslVpnConn);
        if (this.data.get('vpnType') === 'ssl') {
            this.getSslUser();
        }
        this.setTableColumns();
        this.loadPage('beforeLoad');
    }

    // 至少创建一个用户才会出现证书管理
    getSslUser() {
        this.$http
            .getSslUserList({
                vpnId: this.data.get('vpnId'),
                pageNo: 1,
                pageSize: 10
            })
            .then(res => {
                this.data.set('sslUserCount', res.totalCount);
            });
    }

    loadPage(type?: string) {
        this.data.set('table.loading', true);
        let payload = {
            vpnId: this.data.get('vpnId'),
            vpnType: this.data.get('vpnType')
        };
        const vpnType = this.data.get('vpnType');
        this.resetTable();
        vpnType !== 'ssl' && this.checkConnQuota();
        this.$http.vpnConnList(payload).then(res => {
            if (type) {
                let encryptionMode = res[0]?.encryptionMode;
                if (encryptionMode === 'vti') {
                    this.data.set(
                        'customColumn.datasource',
                        this.data.get('customColumn.datasource').filter(item => item.value !== 'localCidrs')
                    );
                    this.data.set(
                        'customColumn.value',
                        this.data.get('customColumn.value').filter(item => item !== 'localCidrs')
                    );
                    this.data.set(
                        'table.columns',
                        this.data.get('table.columns').filter(item => item.name !== 'localCidrs')
                    );
                }
            }
            this.data.set('table.loading', false);
            this.data.set('table.datasource', res);
            if (this.data.get('vpnType') === 'ssl') {
                this.checkSslServer();
            }
        });
    }

    checkConnQuota() {
        this.$http
            .getVpnConnQuota({
                vpnId: this.data.get('vpnId')
            })
            .then(data => {
                let status = this.data.get('status');
                let {addVpnConn} = checker.check(rules, {status}, 'addVpnConn', {free: data.free});
                this.data.set('addVpnConn', addVpnConn);
            });
    }

    checkSslServer() {
        let status = this.data.get('status');
        let sslServerNum = this.data.get('table.datasource').length;
        let {addSslServer} = checker.check(rules, {status}, 'addSslServer', {serverNum: sslServerNum});
        this.data.set('addSslServer', addSslServer);
    }

    resetTable() {
        this.data.set('table.selection', {
            mode: 'multi',
            selectedIndex: []
        });
        this.data.set('table.selectedItems', []);
    }

    refresh() {
        this.loadPage();
    }

    // ipsec & gre 列表新增字段
    resolveAddColumns(vpnType) {
        const enableBgp = this.data.get('enableBgp');
        const columnMap = {
            ipsec: ipsecColumn,
            gre: greColumn,
            ssl: sslColumn
        };
        let filterColumns = columnMap[vpnType];
        if (enableBgp) {
            ['ipsec', 'gre'].includes(vpnType) &&
                (filterColumns = filterColumns.filter(item => !['remoteSideIp', 'allSaActive'].includes(item)));
            const remoteCidrsIdx = filterColumns.indexOf('remoteCidrs');
            filterColumns.splice(
                remoteCidrsIdx + 1,
                0,
                'bgpStatus',
                'localConnIp',
                'remoteConnIp',
                'localBgpAsn',
                'remoteBgpAsn'
            );
        }
        return filterColumns;
    }

    // 设置表格列
    setTableColumns(customColumnNames) {
        customColumnNames = customColumnNames ? customColumnNames : this.data.get('customColumn.value');
        let allColumns = this.data.get('table.allColumns');
        let customColumn = this.data.get('customColumn');
        const vpnType = this.data.get('vpnType');
        if (vpnType === 'ipsec') {
            let filterIpsecColumn = this.resolveAddColumns(vpnType);
            allColumns = _.filter(allColumns, item => filterIpsecColumn.indexOf(item.name) > -1);
            this.data.set(
                'customColumn.datasource',
                _.filter(customColumn.datasource, item => filterIpsecColumn.indexOf(item.value) > -1)
            );
            this.data.set(
                'customColumn.value',
                _.filter(customColumn.value, item => filterIpsecColumn.indexOf(item) > -1)
            );
        } else if (vpnType === 'ssl') {
            allColumns = _.filter(allColumns, item => sslColumn.includes(item.name));
            this.data.set(
                'customColumn.datasource',
                _.filter(customColumn.datasource, item => sslColumn.includes(item.value))
            );
            this.data.set(
                'customColumn.value',
                _.filter(customColumn.value, item => sslColumn.includes(item))
            );
        } else {
            const filterGreColumn = this.resolveAddColumns(vpnType);
            allColumns = _.filter(allColumns, item => filterGreColumn.indexOf(item.name) > -1);
            this.data.set(
                'customColumn.datasource',
                _.filter(customColumn.datasource, item => filterGreColumn.indexOf(item.value) > -1)
            );
            this.data.set(
                'customColumn.value',
                _.filter(customColumn.value, item => filterGreColumn.indexOf(item) > -1)
            );
        }
        let columns = [];
        allColumns.forEach(item => customColumnNames.indexOf(item.name) > -1 && columns.push(item));
        this.data.set('table.columns', columns);
    }

    // 自定义表格列
    onCustomColumns({value}) {
        this.data.set('customColumn.value', value);
        this.setTableColumns(value);
    }

    tableSelected(e) {
        this.data.set('table.selectedItems', e.value.selectedItems);
        let {deleteVpnConn} = checker.check(rules, e.value.selectedItems, '');
        let userCount = this.data.get('sslUserCount');
        let {deleteSslVpnConn} = checker.check(rules, e.value.selectedItems, '', {userCount: userCount});
        this.data.set('deleteVpnConn', deleteVpnConn);
        this.data.set('deleteSslVpnConn', deleteSslVpnConn);
    }

    // 排序
    onSort(e) {
        this.data.set('sort', e.value);
        this.loadPage();
    }

    // 筛选
    onFilter(e) {
        let name = e.field.name;
        let value = e[name] ? e[name] : '';
        this.data.set('filters.' + name, value);
        this.loadPage();
    }

    accessShow(e) {
        if (!e.value) {
            this.data.set('accessTable.datasource', []);
            this.data.set('accessTable.loading', false);
        }
    }

    getAccessList(vpnConnId) {
        this.data.set('accessTable.loading', true);
        this.$http
            .vpnConnAccessList(vpnConnId)
            .then(res => {
                this.data.set('accessTable.loading', false);
                const errList = _.filter(res.connAccessList, item => item.status === 'down');
                this.data.set('accessTable.datasource', errList);
            })
            .catch(() => {
                this.data.set('accessTable.loading', false);
            });
    }

    deleteVpnConn() {
        let vpnType = this.data.get('vpnType');
        let selectedItems = this.data.get('table.selectedItems');
        let confirm = new Confirm({
            data: {
                title: '确认',
                content: `确定删除此VPN${vpnType === 'ssl' ? '服务端' : '隧道'}吗？`
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http
                .vpnConnDelete({
                    vpnConnId: selectedItems[0].vpnConnId,
                    vpnId: this.data.get('vpnId')
                })
                .then(() => {
                    this.loadPage();
                    // this.fire('updateList'); 暂时隐藏
                });
        });
    }

    onCreate() {
        let vpnId = this.data.get('vpnId');
        let vpcId = this.data.get('vpcId');
        const vpnType = this.data.get('vpnType');
        let datasource = this.data.get('table.datasource');
        let encryptionMode = datasource[0]?.encryptionMode;
        const enableBgp = this.data.get('enableBgp');
        let url = `#/vpc/vpn/conn/create?vpnId=${vpnId}&vpcId=${vpcId}&vpnType=${vpnType}`;
        encryptionMode && (url = url + `&encryptionMode=${encryptionMode}`);
        if (['ipsec', 'gre'].includes(vpnType)) {
            url = url + `&enableBgp=${enableBgp}`;
        }
        location.hash = url;
    }

    editConn(item) {
        const {vpnConnId, encryptionMode} = item;
        let vpnId = this.data.get('vpnId');
        let vpcId = this.data.get('vpcId');
        const enableBgp = this.data.get('enableBgp');
        let vpnType = this.data.get('vpnType');
        let url =
            `#/vpc/vpn/conn/create?vpnId=${vpnId}` +
            `&vpcId=${vpcId}&vpnConnId=${vpnConnId}&vpnType=${vpnType}&encryptionMode=${encryptionMode}`;
        url = url + `&enableBgp=${enableBgp}`;
        location.hash = url;
    }

    userManage(item) {
        let vpnId = this.data.get('vpnId');
        let vpnType = this.data.get('vpnType');
        location.hash = `#/vpc/vpn/ssl/user?vpnId=${vpnId}&vpnType=${vpnType}&vpnConnId=${item.vpnConnId}`;
    }

    goMonitor(item) {
        let vpnId = this.data.get('vpnId');
        let vpnType = this.data.get('vpnType');
        location.hash = `#/vpc/vpn/ssl/monitor?vpnId=${vpnId}&vpnType=${vpnType}&vpnConnId=${item.vpnConnId}`;
    }

    toAlarmDetail(item) {
        const region = window.$context.getCurrentRegionId();
        redirect(`/bcm/#/bcm/alarm/rule/list~scope=BCE_VPN&dimensions=ConnID:${item.vpnConnId}&region=${region}`);
    }

    showMonitor(item) {
        const dialog = new Monitor({
            data: {
                vpnConnId: item.vpnConnId,
                type: this.data.get('vpnType')
            }
        });
        dialog.attach(document.body);
    }

    onRegionChange() {
        location.reload();
    }

    resetIpsec(row) {
        let confirm = new Confirm({
            data: {
                title: '确认重置此VPN隧道？',
                content: '温馨提示：重置操作将会造成该vpn隧道中断，请提前做好网络变更准备。'
            }
        });
        confirm.on('confirm', () => {
            this.$http
                .resetIPsec(row.vpnId, row.vpnConnId)
                .then(() => {
                    Notification.success('vpn隧道重置成功。');
                    this.loadPage();
                })
                .catch(err => {
                    Notification.error('vpn隧道重置失败。');
                });
        });
        confirm.attach(document.body);
    }
    caCertDownload(row) {
        const {protocol} = row;
        let content = caCert(this.data.get('eip'), row).trim();
        if (protocol === 'tcp') {
            content = content.replace(/^explicit-exit-notify 3\s*$\n?/gm, '');
        }
        const blob = new Blob([content], {type: 'application/json'});

        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = 'client.ovpn';
        document.documentElement.appendChild(a);
        a.click();
        document.documentElement.removeChild(a);
    }
    pathAnalysisVpnConn(e) {
        this.data.set('openDrawer', true);
        this.nextTick(() => {
            this.data.set('drawerVisible', true);
            this.data.set('visibleDraw', true);
            if (document.getElementById('satisfactionNew')) {
                this.loadSatisfactionPath(e);
            } else {
                this.pathAnalysisVpnConn(e);
            }
        });
    }
    loadSatisfactionPath() {
        this.nextTick(() => {
            processorPath.applyComponent(
                'AnalysisGraphDrawer',
                {
                    visible: this.data.get('visibleDraw'),
                    scoreTipTitle: 'VPN通道',
                    onCloseDrawer: () => {
                        this.data.set('openDrawer', false);
                        this.data.set('drawerVisible', false);
                        this.data.set('visibleDraw', false);
                    },
                    http: request
                },
                '#satisfactionNew'
            );
        });
    }
}
export default Processor.autowireUnCheckCmpt(VpnConnList);
