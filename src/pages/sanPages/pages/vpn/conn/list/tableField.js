export const columns = [
    {
        name: 'id',
        label: 'VPN隧道名称/ID',
        width: 170,
        fixed: 'left'
    },
    {
        name: 'vpnConnId',
        label: 'SSL VPN服务端名称/ID',
        width: 170,
        fixed: 'left'
    },
    {
        name: 'status',
        label: '隧道状态',
        width: 120
    },
    {
        name: 'healthStatus',
        label: '健康检查状态',
        width: 120
    },
    {
        name: 'diagnose',
        label: '路径分析',
        width: 160
    },
    {
        name: 'encryptionMode',
        label: '通信模式',
        width: 120
    },
    {
        name: 'allSaActive',
        label: '安全联盟状态',
        width: 140
    },
    {
        name: 'eip',
        label: '本端VPN网关公网IP',
        minWidth: 140
    },
    {
        name: 'localCidrs',
        label: '本端网络',
        width: 130
    },
    {
        name: 'interfaceType',
        label: '接口类型',
        width: 100
    },
    {
        name: 'localSubnets',
        label: 'VPN服务端网络',
        width: 250
    },
    {
        name: 'remoteCidrs',
        label: '对端VPN网关公网IP',
        width: 140
    },
    {
        name: 'bgpStatus',
        label: 'BGP状态',
        width: 120
    },
    {
        name: 'localConnIp',
        label: '本端互联地址',
        width: 140
    },
    {
        name: 'remoteConnIp',
        label: '对端互联地址',
        width: 140
    },
    {
        name: 'localBgpAsn',
        label: '本端BGP ASN',
        width: 120
    },
    {
        name: 'remoteBgpAsn',
        label: '对端BGP ASN',
        width: 120
    },
    {
        name: 'remoteSideIp',
        label: '对端网络',
        width: 160
    },
    {
        name: 'remoteSubnets',
        label: 'VPN客户端网络',
        width: 250
    },
    {
        name: 'createTime',
        label: '创建时间',
        minWidth: 100
    },
    // {
    //     name: 'localConnIp',
    //     label: '本端隧道互联地址',
    //     width: 160
    // },
    // {
    //     name: 'remoteConnIp',
    //     label: '对端隧道互联地址',
    //     width: 160
    // },
    {
        name: 'healthCheckInterval',
        label: '健康检查间隔',
        width: 120
    },
    {
        name: 'healthCheckThreshold',
        label: '健康阈值',
        width: 100
    },
    {
        name: 'description',
        label: '描述',
        minWidth: 80
    },
    {
        name: 'opt',
        label: '操作',
        minWidth: 100,
        fixed: 'right'
    }
];
