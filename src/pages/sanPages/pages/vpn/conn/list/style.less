.vpn-conn-wrap {
    .text-hidden {
        max-width: 90%;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: bottom;
    }
    .custom-column-qos {
        padding: 0 7px !important;
        .s-icon-button {
            border: none;
            padding: 0px;
            margin: 0px;
        }
    }
    .inline-tip {
        top: 3px;
        position: relative;
        .s-tip-warning {
            justify-content: center;
            .warning_class {
                fill: #999;
            }
        }
        .s-tip:hover .s-icon path {
            fill: #2468f2 !important;
        }
    }
    .operations {
        .s-button {
            padding: 0;
            margin-right: 12px;
        }
    }
    .new-tag {
        display: inline-block;
        background-color: #f72e32;
        border-radius: 16px;
        line-height: 16px;
        min-width: 40px;
        text-align: center;
        font-size: 12px;
        color: #fff;
    }
    .new-instance-tag {
        background-color: #f33e3e;
        border-radius: 2px;
        line-height: 17px;
    }
}

.cert-popover {
    .s-popover-content {
        min-width: 520px !important;
        .edit-name-warp {
            display: flex;
            flex-direction: column;
            align-items: center;
            .cert-content {
                background-color: #108cee3b;
                color: #2468f2;
                margin: 10px 0;
            }
        }
    }
}

.access-list-popover {
    .s-popover-content {
        width: 400px;
        .access-list-warp {
            max-height: 400px;
            overflow-y: auto;
            .access-list-table {
                margin-top: 8px;
            }
        }
    }
    .icon-question-mark {
        border-radius: 14px;
        font-size: 14px;
    }
}

.subnet-address-wrap {
    .text-hidden {
        max-width: 90%;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: bottom;
    }
    .custom-column-qos {
        padding: 0 7px !important;
        .s-icon-button {
            border: none;
            padding: 0px;
            margin: 0px;
        }
    }
    .inline-tip {
        top: 3px;
        position: relative;
        .s-tip-warning {
            justify-content: center;
            .warning_class {
                fill: #999;
            }
        }
        .s-tip:hover .s-icon path {
            fill: #2468f2 !important;
        }
    }
    .operations {
        .s-button {
            padding: 0;
            margin-right: 12px;
        }
    }
}
