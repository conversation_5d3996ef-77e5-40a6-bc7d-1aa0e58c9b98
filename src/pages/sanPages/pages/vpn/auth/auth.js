/**
 * @file 对等连接授权
 * <AUTHOR>
 */
import {Component} from 'san';
import u from 'lodash';
import {AppOrderPage} from '@baidu/sui-biz';
import {decorators, html, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {StsConfig} from '@/pages/sanPages/utils/config';
import {isOnline} from '@/pages/sanPages/utils/common';
import {DocService, ContextService} from '@/pages/sanPages/common';
import {getUserId, $flag as FLAG} from '@/pages/sanPages/utils/helper';
import {EventBus, EventName, activeServiceType} from '@/utils';

import './style.less';

const {template} = decorators;
const AllRegion = window.$context.getEnum('AllRegion');
/* eslint-disable */
const tpl = html`
    <div>
        <div class="auth vpn-auth">
            <div class="auth-title">VPN 网关</div>
            <s-order-page
                title="{{title}}"
                desc="{{vpnDescription}}"
                logoSrc="{{logoSrc}}"
                process="{{process}}"
                disabled="{{disabled}}"
                agreed="{{true}}"
                useNewVersion="{{true}}"
                openBtnDisabled="{{openBtnDisabled}}"
                openBtnDisabledTip="{{openBtnDisabledTip}}"
                on-click="open"
            />
            <div></div>
        </div>
    </div>
`;
/* eslint-enable */

@template(tpl)
class VpnAuth extends Component {
    static components = {
        's-order-page': AppOrderPage
    };
    computed = {
        confirmDisabled() {
            return !this.data.get('agreed');
        },
        vpnDescription() {
            const projectName = FLAG.NetworkSupportXS ? '智能云' : ContextService.ProjectName;
            return `通过虚拟专用网络VPN（Virtual Private Network）服务，将${projectName}与客户的多个数据中心快速、灵活搭建VPN隧道，实现混合云。${projectName}VPN网关，基于主备模式的高可靠架构实现支持VPN健康性自动检测，故障自动恢复功能。`;
        }
    };
    initData() {
        return {
            title: 'VPN网关简介',
            logoSrc: 'https://bce.bdstatic.com/network-frontend/vpn-auth-logo.png',
            process: {
                title: 'VPN网关使用流程',
                content: [
                    {
                        title: '创建VPN网关',
                        desc: '包年包月，按需计费两种购买方式，填写配置信息'
                    },
                    {
                        title: '创建VPN隧道',
                        desc: '创建IPsec连接建立加密通信通道'
                    },
                    {
                        title: '配置路由表',
                        desc: 'VPN隧道两端分别配置路由表，实现云环境和用户做网络的流量互通'
                    },
                    {
                        title: '监控报警',
                        desc: '查看带宽，流量，包速率等监控信息，并配置报警策略'
                    }
                ]
            },
            disabled: false,
            openBtnDisabled: false,
            openBtnDisabledTip: ''
        };
    }
    inited() {
        const isSubUser = window.$context.isSubUser();
        if (isSubUser) {
            this.data.set('openBtnDisabled', true);
            this.data.set('openBtnDisabledTip', '当前登录的子账户没有开通服务的权限，请联系主账户开通服务后使用。');
        }
    }
    open() {
        const roleName = StsConfig.VPN.roleName;
        return this.$http
            .iamStsRoleActivate(
                u.extend(
                    {
                        roleName,
                        accountId: getUserId()
                    },
                    isOnline() ? StsConfig.VPN.online : StsConfig.VPN.sandbox
                ),
                {region: AllRegion.BJ}
            )
            .then(() => {
                EventBus.fire(EventName.productActive, activeServiceType.vpn);
                window.$storage.set('vpnSts', true);
            });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(VpnAuth));
