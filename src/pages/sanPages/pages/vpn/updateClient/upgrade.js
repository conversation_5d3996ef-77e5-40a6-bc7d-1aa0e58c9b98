/**
 * @file network/vpn/updateClient/upgrade.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {Button, Form, Radio, Tooltip} from '@baidu/sui';
import {AppCreatePage} from '@baidu/sui-biz';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {BillingSdk, OrderItem, UPDATE_STATUS} from '@baiducloud/bce-billing-sdk';
import {OrderConfirm, ShoppingCart, TotalPrice, Protocol} from '@baiducloud/bce-billing-sdk-san';
import Client from '@baiducloud/httpclient';

import {PayType, VpnStatus, vpnFlavor} from '@/pages/sanPages/common/enum';
import {disable_vpn_flavor_region} from '@/pages/sanPages/common/flag';
import {convertPrice, contextPipe, showMoney, $flag as FLAG} from '@/pages/sanPages/utils/helper';
import './style.less';

const {asPage, service} = decorators;

/* eslint-disable */
const template = html`
    <template>
        <s-page
            class="{{klass}}"
            backTo="{{pageNav.url}}"
            backToLabel="{{pageNav.backLabel}}"
            pageTitle="{{pageNav.title}}"
            style="display: {{confirming ? 'none' : 'block'}}"
        >
            <s-form data="{=formData=}" rules="{{rules}}" s-ref="form">
                <div class="body-part-content base-info form-part-wrap">
                    <h4>当前配置</h4>
                    <div>
                        <span class="item-col">
                            <label class="item-label">连接状态：</label>
                            <span class="item-content {{instance.status | statusStyle}}">
                                {{instance.status | statusText}}
                            </span>
                        </span>
                        <span class="item-col">
                            <label class="item-label">付费方式：</label>
                            <span class="item-content">
                                {{instance.productType === 'prepay' ? '预付费' : '后付费'}}
                            </span>
                        </span>
                        <span class="item-col">
                            <label class="item-label">连接数：</label>
                            <span class="item-content">
                                <span>{{instance.maxClient}}</span>
                            </span>
                        </span>
                    </div>
                </div>
                <div class="body-part-content form-part-wrap">
                    <h4>变更配置</h4>
                    <s-form-item label="SSL连接数：" prop="maxClient">
                        <s-radio-group
                            datasource="{{maxClientList}}"
                            value="{=formData.maxClient=}"
                            on-change="onMaxClientChange"
                            radioType="button"
                        >
                        </s-radio-group>
                    </s-form-item>
                </div>
            </s-form>
            <div slot="pageFooter" class="buybucket">
                <div class="buybucket-container">
                    <s-tooltip placement="top" trigger="{{priceLoading ? 'hover' : ''}}">
                        <!--bca-disable-next-line-->
                        <span slot="content" s-html="{{buybucket.tip}}"></span>
                        <s-button
                            skin="primary"
                            size="large"
                            class="no-mg-bt"
                            on-click="onConfirm"
                            disabled="{{priceLoading}}"
                            >{{'确认订单'}}</s-button
                        >
                    </s-tooltip>
                    <s-button size="large" on-click="cancel">取消</s-button>
                    <shopping-cart
                        sdk="{{newBillingSdk}}"
                        addItemToCartAvailable="{{addItemToCartAvailable}}"
                        addItemToCartDisable="{=priceLoading=}"
                        on-change="onShoppingCartChange"
                        theme="default"
                        on-reset="onReset"
                    ></shopping-cart>
                </div>
            </div>
        </s-page>
        <s-page
            class="{{klass}}"
            backToLabel="{{confirmPageNav.backLabel}}"
            pageTitle="{{confirmPageNav.title}}"
            backTo="{{confirmPageNav.url}}"
            content-in-center
            s-if="confirming"
        >
            <order-confirm
                s-ref="orderConfirm"
                sdk="{{newBillingSdk}}"
                use-coupon="{{instance.productType === 'prepay' && FLAG.NetworkSupportXS}}"
                theme="default"
                mergeBy="{{false}}"
                couponMergeBy="{{false}}"
                showAgreementCheckbox
            />
            <div slot="pageFooter" class="buybucket">
                <div class="buybucket-container">
                    <div class="buybucket-container-protocol">
                        <billing-protocol s-if="!FLAG.NetworkSupportXS" s-ref="billingProtocol" />
                        <s-button size="large" on-click="onBack" class="no-mg-bt">{{'上一步'}}</s-button>
                        <s-button size="large" on-click="cancel">取消</s-button>
                        <s-button skin="primary" size="large" disabled="{{confirmingDis}}" on-click="onPay">
                            {{'提交订单'}}
                        </s-button>
                    </div>
                    <total-price sdk="{{newBillingSdk}}" />
                </div>
            </div>
        </s-page>
    </template>
`;
/* eslint-enable */
class SSLUpgrdadeClient extends Component {
    static template = template;
    static components = {
        's-page': AppCreatePage,
        's-form': Form,
        's-form-item': Form.Item,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        'shopping-cart': ShoppingCart,
        'order-confirm': OrderConfirm,
        's-button': Button,
        's-tooltip': Tooltip,
        'total-price': TotalPrice,
        'billing-protocol': Protocol
    };
    static filters = {
        statusStyle(status) {
            let config = VpnStatus.fromValue(status);
            return config ? config.styleClass : '';
        },
        statusText(status) {
            let config = VpnStatus.fromValue(status);
            return config ? config.text : '';
        }
    };
    static computed = {
        showFlavor() {
            const currentRegion = window.$context.getCurrentRegionId();
            return disable_vpn_flavor_region.indexOf(currentRegion) === -1;
        }
    };

    initData() {
        return {
            FLAG,
            confirming: false,
            klass: ['vpn-maxclient-upgrade'],
            pageNav: {
                title: '连接数升级',
                backLabel: '返回',
                url: '/network/#/vpc/vpn/detail'
            },
            confirmPageNav: {
                title: '连接数升级',
                backLabel: '返回',
                url: '/network/#/vpc/vpn/detail'
            },
            currentRegion: window.$context.getCurrentRegion().label,
            rules: {
                maxClient: [{required: true, message: '请填写名称'}]
            },
            formErrors: null,
            formData: {},
            buybucket: {
                tip: ''
            },
            bucket: {
                price: [
                    {
                        title: '配置费用：',
                        count: '0',
                        extra: ''
                    }
                ],
                datasource: [],
                confirmText: '确认变更',
                disabled: true
            },
            priceLoading: true,
            confirmedPay: false,
            addItemToCartAvailable: false,
            urlQuery: getQueryParams()
        };
    }
    inited() {
        const client = new Client({}, {}.$context);
        // 创建实例
        const sdk = new BillingSdk({
            client,
            AllRegion: window.$context.getEnum('AllRegion'),
            context: contextPipe(this)
        });
        this.data.set('newBillingSdk', sdk);
        const {vpcId, vpnId} = this.data.get('urlQuery');
        this.data.set('pageNav.url', `/network/#/vpc/vpn/detail?vpcId=${vpcId}&vpnId=${vpnId}&vpnType=ssl`);
        this.data.set('confirmPageNav.url', `/network/#/vpc/vpn/detail?vpcId=${vpcId}&vpnId=${vpnId}&vpnType=ssl`);
        this.data.set('id', vpnId);
    }
    attached() {
        this.loadDetail().then(() => this.initOrderItems());
    }

    cancel() {
        const {vpcId, vpnId} = this.data.get('urlQuery');
        location.hash = `#/vpc/vpn/detail?vpcId=${vpcId}&vpnId=${vpnId}&vpnType=ssl`;
    }

    loadDetail() {
        return this.$http
            .getVpnDetail({
                vpnId: this.data.get('id')
            })
            .then(data => {
                let maxClientList = [
                    {label: 5, value: 5},
                    {label: 10, value: 10},
                    {label: 20, value: 20},
                    {label: 50, value: 50},
                    {label: 100, value: 100},
                    {label: 200, value: 200},
                    {label: 500, value: 500},
                    {label: 1000, value: 1000}
                ];
                maxClientList = maxClientList.filter(item => item.value > data.maxClient);
                this.data.set('instance', data);
                this.data.set('maxClientList', maxClientList);
                this.data.set('formData.maxClient', maxClientList[0].value);
                this.data.set('initFlavor', maxClientList[0].value);
            });
    }

    onMaxClientChange(e) {
        this.data.set('formData.maxClient', e.value);
        this.loadPrice();
    }

    getConfig() {
        const {formData, id, instance} = this.data.get();
        let config = {
            maxClient: formData.maxClient,
            vpnId: id,
            flavor: ''
        };
        if (instance.flavor) {
            config.flavor = 'high_performance';
        }
        return config;
    }

    loadPrice() {
        this.data.set('buybucket.tip', '价格加载中');
        let configs = this.getPriceConfig();
        const {newBillingSdk} = this.data.get();
        newBillingSdk.clearItems();
        const orderItem = new OrderItem(configs[0]);
        const sslOrderItem = new OrderItem(configs[1]);
        let bucketItems = [orderItem, sslOrderItem];
        this.data.set('bucketItems', bucketItems);
        newBillingSdk.addItems(bucketItems);
    }

    onConfirm() {
        const form = this.ref('form');
        return form.validateFields().then(() => {
            const {formData, newBillingSdk, bucketItems} = this.data.get('');
            let configDetail = this.getConfigDetail();
            let sslConfigDetail = [
                {label: '地域', value: window.$context.getCurrentRegion().label},
                {label: 'SSL连接数', value: formData.maxClient + '个', showInConfirm: true}
            ];
            if (bucketItems[1]) {
                newBillingSdk.clearItems();
                bucketItems[0].updateConfigDetail(configDetail);
                bucketItems[1].updateConfigDetail(sslConfigDetail);
                this.data.set('bucketItems', bucketItems);
                newBillingSdk.addItems(bucketItems);
                this.data.set('newBillingSdk', newBillingSdk);
            }
            this.data.set('confirming', true);
        });
    }

    onBack() {
        this.data.set('confirming', false);
    }

    async onPay() {
        !FLAG.NetworkSupportXS && (await this.ref('billingProtocol').validateAgreement());
        this.data.set('confirmingDis', true);
        let {newBillingSdk, bucketItems} = this.data.get();
        let params = {
            items: [
                {
                    config: {
                        vpnId: this.data.get('id'),
                        maxClient: this.data.get('formData').maxClient,
                        flavor: this.data.get('instance').flavor
                    },
                    paymentMethod: bucketItems[0].couponId ? [{type: 'coupon', values: [bucketItems[0].couponId]}] : []
                }
            ]
        };
        try {
            let confirmUrl = '/api/vpn/vpn/order/confirm/resize?orderType=RESIZE';
            const data = await this.$http.newConfirmOrder(confirmUrl, params);
            let url = '';
            try {
                const info = await newBillingSdk.checkPayInfo(data);
                url = info.url + '&fromService=VPN';
                info.url && (location.href = url);
            } catch (info) {
                // 跳转到相应页面
                url = info.url + '&fromService=VPN';
                info.url && (location.href = url);
            }
        } catch (err) {}
        this.data.set('confirmingDis', false);
    }

    onReset() {
        const initFlavor = this.data.get('initFlavor');
        this.data.set('formData', {maxClient: initFlavor});
        this.loadPrice();
    }
    initOrderItems() {
        this.loadPrice();
    }

    getPriceConfig() {
        let instance = this.data.get('instance');
        let configs = [
            {
                serviceType: 'VPN',
                configName: '配置总价',
                serviceName: 'VPN网关',
                count: 1,
                chargeItem: instance.productType === 'prepay' ? 'Cpt2' : 'RunningTimeMinutes',
                orderType: 'RESIZE',
                productType: instance.productType,
                region: window.$context.getCurrentRegionId(),
                instanceId: this.data.get('id'),
                flavor: this.getIpsecFlavor(),
                subProductType: instance.productType === 'prepay' ? 'Cpt2' : 'RunningTimeMinutes',
                timeUnit: instance.productType === 'prepay' ? 'MONTH' : 'MINUTE',
                configDetail: this.getConfigDetail()
            },
            {
                serviceType: 'VPN',
                configName: '配置总价',
                serviceName: 'SSL规格费',
                count: 1,
                chargeItem: instance.productType === 'prepay' ? 'Cpt2' : 'RunningTimeMinutes',
                orderType: 'RESIZE',
                productType: instance.productType,
                region: window.$context.getCurrentRegionId(),
                instanceId: this.data.get('id'),
                flavor: this.getFlavor(),
                subProductType: instance.productType === 'prepay' ? 'Cpt2' : 'RunningTimeMinutes',
                timeUnit: instance.productType === 'prepay' ? 'MONTH' : 'MINUTE',
                configDetail: this.getSslConfigDetail()
            }
        ];
        return configs;
    }

    getFlavor() {
        let instance = this.data.get('instance');
        let formData = this.data.get('formData');
        let payload = [];
        if (instance.flavor) {
            payload.push(
                {name: 'subServiceType', value: 'high_performance', scale: 1},
                {name: 'connection number', value: formData.maxClient, scale: 1}
            );
        } else {
            payload.push(
                {name: 'subServiceType', value: 'default', scale: 1},
                {name: 'connection number', value: formData.maxClient, scale: 1}
            );
        }
        return payload;
    }

    getIpsecFlavor() {
        let instance = this.data.get('instance');
        let payload = [];
        if (instance.flavor) {
            payload.push(
                {name: 'subServiceType', value: 'high_performance', scale: 1},
                {name: 'gateWay', value: 'high_performance', scale: 1}
            );
        } else {
            payload.push(
                {name: 'subServiceType', value: 'default', scale: 1},
                {name: 'gateWay', value: 'default', scale: 1}
            );
        }
        return payload;
    }

    onShoppingCartChange(e) {
        this.data.set('priceLoading', UPDATE_STATUS[e] !== UPDATE_STATUS.DONE);
        if (UPDATE_STATUS[e] === UPDATE_STATUS.DONE) {
            const bucketItems = this.data.get('bucketItems');
            const {newBillingSdk, instance} = this.data.get();
            let unitPrice = bucketItems[0]?.unitPrice;
            let sslPrice = bucketItems[1]?.unitPrice;
            let priceSubText = bucketItems[0]?.priceSubText;
            let price = unitPrice + (sslPrice ? sslPrice : 0);
            // 用来判断是否需要二次询价
            if (instance.productType !== PayType.PREPAY && unitPrice && sslPrice && !priceSubText) {
                newBillingSdk.clearItems();
                let extra = `
                  （预计¥${showMoney(price * 60 * 24, 2)}/天
                  ¥${showMoney(price * 60 * 24 * 30, 2)}/月）
              `;
                bucketItems[0].priceSubText = extra;
                this.data.set('bucketItems', bucketItems);
                newBillingSdk.addItems(bucketItems);
            }
            if (sslPrice && bucketItems[1].configDetail.length !== 0) {
                this.setConfigDetail(unitPrice, sslPrice);
            }
        }
    }

    getSslConfigDetail(unitPrice, sslPrice) {
        let formData = this.data.get('formData');
        let config = [
            {label: '地域', value: window.$context.getCurrentRegion().label, showInCart: false},
            {label: 'SSL连接数', value: formData.maxClient + '个', showInConfirm: true}
        ];
        if (unitPrice && sslPrice) {
            config = [
                ...config,
                {
                    label: 'SSL VPN',
                    value: `￥${convertPrice(unitPrice).getPriceOfMinute()}`,
                    showInConfirm: false
                },
                {
                    label: 'SSL规格费',
                    value: `￥${convertPrice(sslPrice).getPriceOfMinute()}`,
                    showInConfirm: false
                }
            ];
        }
        return config;
    }

    setSslConfigDetail() {
        const orderItem = this.data.get('bucketItems');
        if (!orderItem[1]) {
            return;
        }
        orderItem[1].updateConfigDetail([]);
    }

    getConfigDetail(unitPrice, sslPrice) {
        const {instance} = this.data.get();
        let configDetail = [
            {label: '地域', value: window.$context.getCurrentRegion().label},
            {label: 'VPN网关规格', value: vpnFlavor.getTextFromValue(instance.flavor)},
            {label: 'VPN网关类型', value: 'SSL VPN', showInCart: false}
        ];

        if (sslPrice) {
            let getSslConfigDetail = this.getSslConfigDetail(unitPrice, sslPrice);
            configDetail = [...configDetail, ...getSslConfigDetail];
            this.setSslConfigDetail();
        }
        return configDetail;
    }

    setConfigDetail(unitPrice, sslPrice) {
        let configuration = this.getConfigDetail(unitPrice, sslPrice);
        const orderItem = this.data.get('bucketItems');
        if (!orderItem[0]) {
            return;
        }
        orderItem[0].updateConfigDetail(configuration);
    }
}
export default San2React(Processor.autowireUnCheckCmpt(SSLUpgrdadeClient));
