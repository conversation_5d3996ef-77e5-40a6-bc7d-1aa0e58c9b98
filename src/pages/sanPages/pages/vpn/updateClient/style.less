/**
 * @file src/network/vpn/updateClient/style.less
 * <AUTHOR>
 */

.page-sub-title() {
    display: block;
    color: #151b26;
    line-height: 24px;
    font-weight: 500;
    margin-bottom: 16px;
    font-size: 16px;
}
.vpn-maxclient-upgrade {
    background-color: #f7f7f9;
    height: 100%;
    width: 100%;
    overflow: auto;

    .body-part-content {
        border: 1px solid #ebebeb;
        width: calc(~'100vw - 32px');
        padding: 24px;
        text-align: left;
        margin: 16px;
        border-radius: 6px;

        h4 {
            .page-sub-title();
        }

        .item-col {
            width: 33%;
            margin-bottom: 16px;
            display: inline-block;

            .item-label {
                display: inline-block;
                vertical-align: top;
                color: #5e626a;
                margin-right: 16px;
                width: 84px;
            }

            .item-content {
                display: inline-block;
                color: #151a26;
                max-width: 80%;
                word-break: break-all;
                position: relative;
            }
        }
    }

    .base-info {
        background: #f5f5f5;
    }

    .s-radio-button-group {
        .s-radio-text {
            width: 72px;
        }
    }

    .bui-biz-page-content.bui-biz-page-center-content {
        .bui-biz-page-body {
            margin: 0 auto !important;
            border: none !important;
        }
    }
    .order-legend {
        width: calc(~'100vw - 32px');
        padding: 24px;
        text-align: left;
        border-radius: 6px;
    }
    .form-part-wrap {
        border-radius: 6px;
        width: calc(~'100vw - 32px');
        background: #fff !important;
        margin: 16px;
        padding: 24px;
        h4 {
            border: none;
            zoom: 1;
            color: #151b26;
            font-size: 16px;
            font-weight: 500;
            margin: 0 0 16px;
            padding: 0;
        }
        .s-form-item-label {
            width: 118px;
            height: 30px;
            label {
                float: left;
            }
        }
        .label_class {
            .inline-tip {
                top: 3px;
                position: relative;
                .s-tip-warning {
                    justify-content: center;
                    .warning_class {
                        fill: #999;
                    }
                }
                .s-tip:hover .s-icon path {
                    fill: #2468f2 !important;
                }
            }
        }
        .center_class {
            .s-row {
                .s-form-item-control-wrapper {
                    line-height: 30px;
                }
            }
        }
        .vpc-nat-eip-opt {
            margin-top: 0px;
        }
    }
    .buybucket {
        width: 100%;
        left: 0;
        height: 80px !important;
        display: block !important;
        padding-left: 16px !important;
        .buybucket-container {
            width: auto;
            float: left;
            height: 80px;
            transform: translateY(0);
            display: flex;
            align-items: center;
            .no-mg-bt {
                margin-left: 0 !important;
            }
            .billing-sdk-total-price-wrapper {
                margin-left: 16px;
            }
        }
    }
    .dragger-input {
        display: inline-block;
        position: relative;
        left: 5px;
        top: -10px;
    }
    .s-form-item-label-required > label:before {
        left: -7px;
        position: absolute;
    }
}
