import { convertPrice, showMoney } from '@/pages/sanPages/utils/helper';
import {TimeUnit} from '@baiducloud/billing-sdk';
import { PayType, vpnFlavor } from '@/pages/sanPages/common/enum';

export const getOrderConfig = (formData = {}, price = { perMoney: 0.00, money: 0.00 }, showFlavor = false) => {
    const unit = formData.productType === PayType.PREPAY ? '' : '/分钟';
    const unitPrice = convertPrice(+price.perMoney).getPriceOfMinute(unit);
    const extraConfig = { label: 'VPN网关规格', value: vpnFlavor.getTextFromValue(formData.flavor) };
    const vpnTypeConfig = { label: 'VPN网关类型', value: 'SSL VPN', showInCart: false };
    let order = {
        managePrice: false,
        type: 'RESIZE',
        serviceName: 'VPN网关',
        serviceType: 'VPN',
        productType: formData.productType,
        region: formData.region,
        count: 1,
        timeUnit: formData.productType === PayType.PREPAY ? TimeUnit.DAY : 'MINUTE',
        price: price.money,
        unitPrice,
        configuration: [
            '地域：' + window.$context.getCurrentRegion().label,
            'VPN名称：' + formData.vpnName
        ],
        configDetail: [
            { label: '地域', value: window.$context.getCurrentRegion().label, showInConfirm: false },
        ],
        config: {}
    };
    showFlavor && order.configDetail.push(extraConfig);
    order.configDetail.push(vpnTypeConfig);
    return order;
};

export const setInstancePrice = (instance, formData = {}, price, sslPrice) => {
    const isPrepay = formData.productType === PayType.PREPAY;
    const priceSubText = isPrepay ? '' : `
        （预计¥${showMoney((sslPrice ? (+price.money + +sslPrice.money) : +price.money) * 60 * 24, 2)}/天
        ¥${showMoney((sslPrice ? (+price.money + +sslPrice.money) : +price.money) * 60 * 24 * 30, 2)}/月）
    `;
    const unit = isPrepay ? '' : '分钟';
    const unitPrice = convertPrice(price.money).getPriceOfMinute(unit);
    const unitPriceText = '¥' + unitPrice;
    instance.set('priceSubText', priceSubText);
    instance.set('unitPriceText', unitPriceText);
    instance.set('productType', formData.productType);
    instance.set('price', price.money);
    instance.set('unitPrice', price.perMoney);
    instance.set('unit', unit);
    instance.set('priceError', '');
};
