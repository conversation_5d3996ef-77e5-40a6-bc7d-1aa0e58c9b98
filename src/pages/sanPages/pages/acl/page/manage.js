/*
 * @description: acl规则管理
 * @file: network/dcgw/pages/manage.js
 * @author: p<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */
import u from 'lodash';
import {html, decorators, DetailPage, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {Notification} from '@baidu/sui';
import {OutlinedLeft, OutlinedPlus, OutlinedEditingSquare} from '@baidu/sui-icon';

import rules from '../rules';
import {ActionType, ProtocolType, IpVersion} from '@/pages/sanPages/common';
import Create from './create';
import LeadFile from './leadFile';
import Confirm from '@/pages/sanPages/components/confirm';
import {$flag as FLAG, kXhrOptions} from '@/pages/sanPages/utils/helper';
import {parseQuery} from '@/utils';
import testID from '@/testId';

import '../style/manage.less';
let kAllPorts = ['0', '1-65535', '0-65535'];
let kAllIp = '0.0.0.0/0';
let kAllIpv6 = '::/0';

const {invokeSUI, invokeSUIBIZ, template, invokeComp} = decorators;
const tpl = html`
    <template>
        <s-app-detail-page class="{{klass}}">
            <div slot="pageTitle" class="instance-info">
                <s-app-link to="/network/#/vpc/acl/list" class="page-title-nav" data-test-id="${testID.acl.detailBack}"
                    ><icon-left />返回</s-app-link
                >
                <span class="instance-name">{{instance.name || '-'}}</span>
                <span class="status normal" s-if="instance.id">运行中</span>
            </div>
            <div class="content">
                <h4>基本信息</h4>
                <div class="cell">
                    <div class="cell-title">名称：</div>
                    <div class="cell-content" data-testid="${testID.acl.detailName}">{{instance.name || '-'}}</div>
                    <s-popover
                        s-ref="instanceNameEdit"
                        placement="right"
                        trigger="click"
                        class="edit-popover-class"
                        data-test-id="${testID.acl.detailNameEdit}"
                    >
                        <div class="edit-wrap" slot="content">
                            <s-input
                                value="{=instanceName.value=}"
                                data-test-id="${testID.acl.detailNameEditInput}"
                                width="320"
                                placeholder="请输入名称"
                                on-input="onNameInput($event)"
                            />
                            <div class="edit-tip">
                                支持大小写字母，数字，中文和"-_/.”以字母或者中文开头，不超过65个字
                            </div>
                            <s-button
                                skin="primary"
                                s-ref="editBtn"
                                data-test-id="${testID.acl.detailNameEditSub}"
                                disabled="{{true}}"
                                on-click="editNameConfirm(instance)"
                                >确定</s-button
                            >
                            <s-button on-click="editNameCancel(rowIndex)">取消</s-button>
                        </div>
                        <outlined-editing-square s-if="instance.name" class="name-icon" on-click="editName(row)" />
                    </s-popover>
                </div>
                <div class="cell">
                    <div class="cell-title">ID：</div>
                    <div class="cell-content">
                        {{instance.shortId || instance.id || '-'}}
                        <s-clip-board
                            s-if="instance.shortId || instance.id"
                            class="name-icon"
                            text="{{instance.shortId}}"
                        />
                    </div>
                </div>
                <div class="cell">
                    <div class="cell-title">所在网络：</div>
                    <div class="cell-content">{{instance | network}}</div>
                </div>
            </div>
            <div class="content">
                <h4>配置信息</h4>
                <s-table
                    columns="{{table.columns}}"
                    loading="{{table.loading}}"
                    error="{{table.error}}"
                    has-Expand-Row="{{true}}"
                    expandedIndex="{=expandedIndex=}"
                    on-exprow-collapse="onRowExpand($event)"
                    on-exprow-expand="onRowExpand($event)"
                    datasource="{{table.datasource}}"
                >
                    <div slot="empty">
                        <s-empty>
                            <div slot="action"></div>
                        </s-empty>
                    </div>
                    <div slot="error">
                        啊呀，出错了？
                        <a href="javascript:;" on-click="refresh">重新加载</a>
                    </div>
                    <div class="sub-table" slot="sub-table">
                        <div class="opt-line">
                            <div class="inline_class">
                                <s-radio-radio-group
                                    class="ruletype"
                                    datasource="{{ruleTypeList}}"
                                    on-change="ruleTypeChange($event, rowIndex)"
                                    value="{=ruleType[rowIndex]=}"
                                    radioType="button"
                                >
                                </s-radio-radio-group>
                                <s-tooltip
                                    trigger="{{accessOptMap.addAclRule.disabled
                                      || addNewRule[rowIndex].disable ? 'hover' : ''}}"
                                    placement="right"
                                >
                                    <div slot="content">
                                        <!--bca-disable-next-line-->
                                        {{accessOptMap.addAclRule.message ? accessOptMap.addAclRule.message :
                                        addNewRule[rowIndex].message | raw}}
                                    </div>
                                    <s-button
                                        skin="primary"
                                        disabled="{{accessOptMap.addAclRule.disabled
                                          ? true
                                          : addNewRule[rowIndex].disable}}"
                                        on-click="onCreate(row, rowIndex)"
                                        data-test-id="${testID.acl.detailAddRules}{{rowIndex}}"
                                    >
                                        <outlined-plus />新增规则
                                    </s-button>
                                </s-tooltip>
                                <s-tooltip
                                    trigger="{{accessOptMap.deleteAclRule.disabled
                                      || !deleteRuleEnable[rowIndex] ? 'hover' : ''}}"
                                    placement="right"
                                >
                                    <div slot="content">
                                        {{accessOptMap.deleteAclRule.message ? accessOptMap.deleteAclRule.message :
                                        deleteRuleMsg[rowIndex]}}
                                    </div>
                                    <s-button
                                        on-click="deleteRule(rowIndex)"
                                        disabled="{{accessOptMap.deleteAclRule.disabled
                                          ? true
                                          : !deleteRuleEnable[rowIndex]}}"
                                        class="left_class"
                                    >
                                        删除
                                    </s-button>
                                </s-tooltip>
                            </div>
                            <s-select
                                class="ruleiptype"
                                width="100"
                                datasource="{{ruleIpTypeList}}"
                                value="{=ruleIpType[rowIndex]=}"
                                on-change="ruleIpTypeChange($event, rowIndex)"
                            >
                            </s-select>
                            <s-button class="lead-btn" on-click="leadOut(row)">{{'导出' }}</s-button>
                            <s-tooltip
                                class="lead-tooltip"
                                trigger="{{accessOptMap.addAclRule.disabled
                                  || disableMessage[rowIndex] ? 'hover' : ''}}"
                                placement="right"
                            >
                                <div slot="content">
                                    <!--bca-disable-next-line-->
                                    {{(accessOptMap.addAclRule.message ? disableImportTip : disableMessage[rowIndex]) |
                                    raw}}
                                </div>
                                <s-button
                                    disabled="{{accessOptMap.addAclRule.disabled
                                      ? true
                                      : disableAdd[rowIndex]}}"
                                    class="lead-btn"
                                    on-click="leadIn(row, rowIndex)"
                                >
                                    {{'导入' }}
                                </s-button>
                            </s-tooltip>
                        </div>
                        <s-table
                            error="{{table.error}}"
                            loading="{{table.subLoading[rowIndex]}}"
                            columns="{{subTable.columns}}"
                            on-selected-change="subTableSelected($event,rowIndex)"
                            selection="{{subTable.selection}}"
                            datasource="{{subTable.datasource[rowIndex]}}"
                        >
                            <div slot="empty">
                                <s-empty>
                                    <s-button
                                        s-if="!accessOptMap.addAclRule.disabled"
                                        skin="stringfy"
                                        slot="action"
                                        on-click="onCreate(row, rowIndex)"
                                        >马上创建</s-button
                                    >
                                    <s-tooltip s-else>
                                        <div slot="content">
                                            <!--bca-disable-next-line-->
                                            {{accessOptMap.addAclRule.message | raw}}
                                        </div>
                                        <span>马上创建</span>
                                    </s-tooltip>
                                </s-empty>
                            </div>
                            <div slot="c-opt">
                                <span s-if="!row.id">-</span>
                                <s-tooltip s-else>
                                    <div slot="content">
                                        <!--bca-disable-next-line-->
                                        {{accessOptMap.updateAclRule.message | raw}}
                                    </div>
                                    <s-button
                                        skin="stringfy"
                                        disabled="{{accessOptMap.updateAclRule.disabled}}"
                                        on-click="editSubrow(row, rowIndex)"
                                        >编辑</s-button
                                    >
                                </s-tooltip>
                            </div>
                        </s-table>
                        <s-pagination
                            s-if="{{pager[rowIndex].total}}"
                            class="pagination-class-table"
                            slot="footer"
                            layout="{{'total, pageSize, pager, go'}}"
                            pageSize="{{pager[rowIndex].size}}"
                            total="{{pager[rowIndex].total}}"
                            page="{{pager[rowIndex].page}}"
                            on-pagerChange="onPagerChange($event, rowIndex)"
                            on-pagerSizeChange="onPagerSizeChange($event, rowIndex)"
                        />
                    </div>
                </s-table>
            </div>
        </s-app-detail-page>
    </template>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeComp('@acl-create', '@acl-lead-in')
class AclManage extends DetailPage {
    REGION_CHANGE_LOCATION = '#/vpc/acl/list';
    static components = {
        'icon-left': OutlinedLeft,
        'outlined-plus': OutlinedPlus,
        'outlined-editing-square': OutlinedEditingSquare
    };
    static filters = {
        network(instance) {
            return instance.vpcName || '-';
        }
    };
    initData() {
        return {
            expandedIndex: [],
            klass: ['vpc-acl-manage'],
            instance: {},
            instanceName: {
                value: '',
                error: true,
                visible: false
            },
            ruleTypeList: [
                {
                    text: '入站',
                    value: 'ingress'
                },
                {
                    text: '出站',
                    value: 'egress'
                }
            ],
            ruleType: [],
            ruleIpTypeList: [
                {
                    text: '全部规则',
                    value: 'all'
                },
                {
                    text: 'IPv4',
                    value: 4
                },
                {
                    text: 'IPv6',
                    value: 6
                }
            ],
            ruleIpType: [],
            table: {
                loading: false,
                columns: [
                    {
                        name: 'subnetName',
                        label: '名称',
                        render(item) {
                            return u.escape(item.subnetName) + '<br>' + u.escape(item.subnetId);
                        }
                    },
                    {
                        name: 'subnetCidr',
                        label: '网段',
                        render(item) {
                            let subnetCidr = u.escape(item.subnetCidr);
                            let ipv6Cidr = u.escape(item.ipv6Cidr);
                            return subnetCidr + (ipv6Cidr ? '（' + ipv6Cidr + '）' : '');
                        }
                    },
                    {
                        name: 'opt',
                        label: '描述',
                        render(item) {
                            let totalNum = item.aclRules.length;
                            let ingressNum = 0;
                            let egressNum = 0;
                            item.aclRules.forEach(item => {
                                if (item.direction === 'egress') {
                                    egressNum++;
                                } else {
                                    ingressNum++;
                                }
                            });
                            return `已有${totalNum}条规则，入站${ingressNum}条，出站${egressNum}条`;
                        }
                    }
                ],
                datasource: []
            },
            subTable: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: [],
                    disabledIndex(item) {
                        return !item.id;
                    }
                },
                columns: [
                    {
                        name: 'position',
                        label: '优先级',
                        render(item) {
                            return u.escape(item.position);
                        }
                    },
                    {
                        name: 'protocol',
                        label: '协议',
                        render(item) {
                            if (item.protocol === 'all') {
                                return '全部协议';
                            }
                            return u.escape(item.protocol.toUpperCase());
                        }
                    },
                    {
                        name: 'sourceIpAddress',
                        label: '源IP',
                        width: 180,
                        render(item) {
                            if (item.sourceIpAddress === 'all') {
                                return item.ipVersion === IpVersion.IPV6 ? kAllIpv6 : kAllIp;
                            }
                            let sourceIpAddress = u.escape(item.sourceIpAddress);
                            return `<span title="${sourceIpAddress}" class="truncated">${sourceIpAddress}</span>`;
                        }
                    },
                    {
                        name: 'sourcePort',
                        label: '源端口',
                        render(item) {
                            if (item.protocol === ProtocolType.ICMP) {
                                return 'N/A';
                            }
                            if (u.contains(kAllPorts, item.sourcePort)) {
                                return 'all';
                            }
                            return u.escape(item.sourcePort);
                        }
                    },
                    {
                        name: 'destinationIpAddress',
                        label: '目的IP',
                        width: 180,
                        render(item) {
                            if (item.destinationIpAddress === 'all') {
                                return item.ipVersion === IpVersion.IPV6 ? kAllIpv6 : kAllIp;
                            }
                            let destinationIpAddress = u.escape(item.destinationIpAddress);
                            return `<span title="${destinationIpAddress}" class="truncated">${destinationIpAddress}</span>`; //eslint-disable-line
                        }
                    },
                    {
                        name: 'destinationPort',
                        label: '目的端口',
                        render(item) {
                            if (item.protocol === ProtocolType.ICMP) {
                                return 'N/A';
                            }
                            if (u.contains(kAllPorts, item.destinationPort)) {
                                return 'all';
                            }
                            return u.escape(item.destinationPort);
                        }
                    },
                    {
                        name: 'action',
                        label: '策略',
                        render(item) {
                            return ActionType.getTextFromValue(item.action);
                        }
                    },
                    {
                        name: 'description',
                        label: '备注',
                        render(item) {
                            let description = u.escape(item.description);
                            if (!item.id && description === 'default') {
                                description = '默认';
                            }
                            return `<span title="${description}" class="truncated">${description}</span>`;
                        }
                    },
                    {
                        name: 'opt',
                        label: '操作'
                    }
                ],
                datasource: [],
                allDatasource: []
            },
            selectedItem: [],
            allRule: [],
            inRuleQuota: [],
            outRuleQuota: [],
            deleteRuleEnable: [],
            addRuleIn: {},
            addRuleOut: {},
            addNewRule: {},
            addVPCAclRuleIn: {},
            addVPCAclRuleOut: {},
            deleteRuleMsg: ['请选择实例对象'],
            pager: [],
            accessOptMap: {},
            disableImportTip: '您没有导入ACL规则权限，请联系主用户添加。',
            urlQuery: parseQuery(location.hash),
            loading: true
        };
    }

    async attached() {
        this.getDetail({'x-silent-codes': ['InstanceNotFound']});
    }
    
    subTableSelected(e, rowIndex) {
        if (e.value.selectedItems.length > 0 && e.value.selectedItems.length <= 20) {
            this.data.set(`deleteRuleEnable[${rowIndex}]`, true);
            this.data.set(`deleteRuleMsg[${rowIndex}]`, '');
        } else {
            this.data.set(`deleteRuleEnable[${rowIndex}]`, false);
            this.data.set(
                `deleteRuleMsg[${rowIndex}]`,
                e.value.selectedItems.length <= 0
                    ? '请先选择实例对象'
                    : 'ACL规则批量删除的数量不能超过20个，请您减少批量删除的数量'
            );
        }
        this.data.set(`selectedItem[${rowIndex}]`, e.value.selectedItems);
    }

    onRowExpand(e) {
        const {rowIndex} = e.value;
        if (e.value.expandedIndex.indexOf(rowIndex) > -1) {
            this.getAclRuleQuota(rowIndex);
            this.getAclRules(rowIndex);
        }
    }

    disableAddFunc(index, vpcQuota) {
        let ruleType = this.data.get(`ruleType[${index}]`);
        let quota =
            ruleType === 'ingress' ? this.data.get(`inRuleQuota[${index}]`) : this.data.get(`outRuleQuota[${index}]`);
        this.data.set(`disableAdd[${index}]`, vpcQuota <= 0 || quota <= 0);
        return vpcQuota <= 0 || quota <= 0;
    }

    disableMessageFunc(index) {
        let ruleType = this.data.get(`ruleType[${index}]`);
        let total = this.data.get(`subTable.datasource[${index}]`).length;
        let ruledirection = ruleType === 'ingress' ? '入站' : '出站';
        let vpcQuota =
            ruleType === 'ingress'
                ? this.data.get(`vpcIngressQuota[${index}]`)
                : this.data.get(`vpcEgressQuota[${index}]`);
        if (this.disableAddFunc(index, vpcQuota)) {
            vpcQuota <= 0
                ? this.data.set(
                      `disableMessage[${index}]`,
                      '您已添加' + total + '条' + ruledirection + '规则，达到vpc类限额！'
                  )
                : this.data.set(
                      `disableMessage[${index}]`,
                      '您已添加' + total + '条' + ruledirection + '规则，达到子网类限额！'
                  );
        } else {
            this.data.set(`disableMessage[${index}]`, '');
        }
    }

    leadIn(row, index) {
        let leadDialog = new LeadFile({
            data: {
                open: true,
                allRules: this.data.get(`allRule[${index}]`),
                inRuleQuota: this.data.get(`inRuleQuota[${index}]`),
                outRuleQuota: this.data.get(`outRuleQuota[${index}]`),
                inVpcQuota: this.data.get(`vpcIngressQuota[${index}]`),
                outVpcQuota: this.data.get(`vpcEgressQuota[${index}]`),
                subnetId: row.subnetUuid,
                shortId: this.data.get('instance.shortId')
            }
        });
        leadDialog.attach(document.body);
        leadDialog.on('leadComplete', () => {
            this.getDetail();
            this.fire('update');
        });
    }

    leadOut(row) {
        let subnetId = row.subnetUuid;
        let aclId = this.data.get('instance.id');
        window.open(`/api/network/v1/acl/download?subnetId=${subnetId}&aclId=${aclId}`);
    }

    getAclRuleQuota(index) {
        let reqList = [
            this.$http.aclRuleQuota(
                {
                    subnetId: this.data.get(`instance.aclEntrys[${index}].subnetId`)
                },
                kXhrOptions.silence
            )
        ];

        Promise.all(reqList).then(result => {
            this.data.set(`vpcEgressQuota[${index}]`, result[0].vpcEgressQuota.free);
            this.data.set(`vpcIngressQuota[${index}]`, result[0].vpcIngressQuota.free);
            this.data.set(`inRuleQuota[${index}]`, result[0].ingressQuota.free);
            this.data.set(`outRuleQuota[${index}]`, result[0].egressQuota.free);
            let {addRuleIn, addRuleOut} = checker.check(rules, [], '', {
                ruleInQuotaCheck: result[0].ingressQuota.free > 0,
                ruleOutQuotaCheck: result[0].egressQuota.free > 0
            });
            let {addVPCAclRuleIn, addVPCAclRuleOut} = checker.check(rules, [], '', {
                ruleInVPCQuotaCheck: result[0].vpcIngressQuota.free > 0,
                ruleOutVPCQuotaCheck: result[0].vpcEgressQuota.free > 0
            });
            this.data.set('addVPCAclRuleIn', addVPCAclRuleIn);
            this.data.set('addVPCAclRuleOut', addVPCAclRuleOut);
            this.data.set('addRuleIn', addRuleIn);
            this.data.set('addRuleOut', addRuleOut);
            if (this.data.get(`ruleType[${index}]`) === 'ingress') {
                this.data.set(`addNewRule[${index}]`, addVPCAclRuleIn);
                addVPCAclRuleIn.disable ? '' : this.data.set(`addNewRule[${index}]`, addRuleIn);
                this.disableMessageFunc(index);
            } else {
                this.data.set(`addNewRule[${index}]`, addVPCAclRuleOut);
                addVPCAclRuleOut.disable ? '' : this.data.set(`addNewRule[${index}]`, addRuleOut);
                this.disableMessageFunc(index);
            }
        });
    }

    onCreate(row, rowIndex) {
        let ruleType = this.data.get(`ruleType[${rowIndex}]`);
        let quotaType = ruleType === 'ingress' ? 'inRuleQuota' : 'outRuleQuota';
        let VPCQuotaType = ruleType === 'ingress' ? 'vpcIngressQuota' : 'vpcEgressQuota';
        let aclRules = row.aclRules.filter(item => item.direction === ruleType);
        let create = new Create({
            data: {
                open: true,
                ruleType: ruleType,
                title: ruleType === 'ingress' ? '添加入站规则' : '添加出站规则',
                quota: Math.min(10, this.data.get(`${quotaType}[${rowIndex}]`)),
                subnetId: row.subnetId,
                subnetCidr: row.subnetCidr,
                ipv6Cidr: row.ipv6Cidr || '',
                positions: u.map(u.pluck(aclRules, 'position'), item => item + ''),
                vpcQuota: this.data.get(`${VPCQuotaType}[${rowIndex}]`)
            }
        });
        create.on('create', () => {
            this.getAclRuleQuota(rowIndex);
            this.getDetail();
        });
        create.attach(document.body);
    }

    editSubrow(row) {
        let subnetInfo = {};
        this.data.get('table.datasource').forEach((item, index) => {
            if (row.subnetId === item.subnetId) {
                let ruleType = this.data.get(`ruleType[${index}]`);
                subnetInfo.subnetId = item.subnetId;
                subnetInfo.subnetCidr = item.subnetCidr;
                subnetInfo.ipv6Cidr = item.ipv6Cidr || '';
                let aclRules = item.aclRules.filter(rule => rule.direction === ruleType);
                subnetInfo.positions = aclRules.map(rule => rule.position + '');
            }
        });
        let edit = new Create({
            data: {
                open: true,
                ruleType: row.direction,
                title: row.direction === 'ingress' ? '修改入站规则' : '添加出站规则',
                ruleInfo: row,
                subnetId: subnetInfo.subnetId,
                subnetCidr: subnetInfo.subnetCidr,
                ipv6Cidr: subnetInfo.ipv6Cidr,
                positions: subnetInfo.positions,
                quota: 1,
                vpcQuota: 1
            }
        });
        edit.on('edit', () => {
            this.getDetail();
        });
        edit.attach(document.body);
    }

    getDetail(option = {}) {
        this.data.set('table.loading', true);
        this.data.set('expandedIndex', []);
        let requester = 'getAclDetail';
        let param = {
            vpcId: this.data.get('urlQuery.vpcId')
        };
        // todo:20201203版本acl企业版版本还不支持分页及重构四期
        if (!FLAG.NetworkAclOpt) {
            requester = 'oldGetAcl';
            param.id = param.vpcId;
            delete param.vpcId;
        }
        return this.$http[requester](param, option).then(instance => {
            this.data.set('table.loading', false);
            this.data.set('instance', instance);
            this.getIamQuery();
            instance.aclEntrys?.forEach((item, index) => {
                item.subSlot = 'sub-table';
                this.data.set(`allRule[${index}]`, instance.aclEntrys[index].aclRules);
                if (!this.data.get(`ruleType[${index}]`) || this.data.get(`ruleType[${index}]`) === 'ingress') {
                    let rules = instance.aclEntrys[index].aclRules.filter(rule => rule.direction === 'ingress');
                    this.data.set(`ruleType[${index}]`, 'ingress');
                    this.data.set(`subTable.datasource[${index}]`, rules);
                } else {
                    let rules = instance.aclEntrys[index].aclRules.filter(rule => rule.direction === 'egress');
                    this.data.set(`ruleType[${index}]`, 'egress');
                    this.data.set(`subTable.datasource[${index}]`, rules);
                }
                this.data.set(`ruleIpType[${index}]`, 'all');
                let subnetId = this.data.get('urlQuery.subnetId');
                if (item.subnetUuid === subnetId) {
                    this.data.push('expandedIndex', index);
                }
                this.data.push('pager', {
                    size: 10,
                    page: 1,
                    total: 0
                });
                this.getAclRuleQuota(index);
            });
            this.data.set('table.datasource', instance.aclEntrys);
        });
    }

    ruleTypeChange(e, rowIndex) {
        this.getAclRuleQuota(rowIndex);
        this.data.set(`disableMessage${rowIndex}`, '');
        let allRule = this.data.get(`allRule[${rowIndex}]`);
        let ruleIpType = this.data.get(`ruleIpType[${rowIndex}]`);
        let datasource = null;
        if (ruleIpType === 'all') {
            datasource = allRule.filter(rule => {
                return rule.direction === e.value;
            });
        } else {
            datasource = allRule.filter(rule => {
                return rule.direction === e.value && rule.ipVersion === ruleIpType;
            });
        }
        this.data.set(`ruleType[${rowIndex}]`, e.value);
        if (e.value === 'ingress') {
            this.data.set(`addNewRule[${rowIndex}]`, this.data.get('addVPCAclRuleIn'));
            this.data.get(`addNewRule[${rowIndex}].disable`)
                ? ''
                : this.data.set(`addNewRule[${rowIndex}]`, this.data.get('addRuleIn'));
            this.nextTick(() => {
                this.disableMessageFunc(rowIndex);
            });
        } else {
            this.data.set(`addNewRule[${rowIndex}]`, this.data.get('addVPCAclRuleOut'));
            this.data.get(`addNewRule[${rowIndex}].disable`)
                ? ''
                : this.data.set(`addNewRule[${rowIndex}]`, this.data.get('addRuleOut'));
            this.nextTick(() => {
                this.disableMessageFunc(rowIndex);
            });
        }
        this.data.set('subTable.selection.selectedIndex', []);
        this.data.set(`subTable.datasource[${rowIndex}]`, datasource);
        this.getAclRules(rowIndex);
    }

    ruleIpTypeChange(e, rowIndex) {
        let allRule = this.data.get(`allRule[${rowIndex}]`);
        let datasource = null;
        let ipVersion = 4;
        if (e.value === 6) {
            ipVersion = 6;
        } else if (e.value === 'all') {
            ipVersion = '';
        }
        if (ipVersion) {
            datasource = allRule.filter(item => {
                return item.ipVersion === ipVersion && item.direction === this.data.get(`ruleType[${rowIndex}]`);
            });
        } else {
            datasource = allRule.filter(item => item.direction === this.data.get(`ruleType[${rowIndex}]`));
        }
        this.data.set(`subTable.datasource[${rowIndex}]`, datasource);
        this.data.set(`ruleIpType[${rowIndex}]`, e.value);
        this.getAclRules(rowIndex);
    }

    // 点击修改名称icon
    editName() {
        let instance = this.data.get('instance');
        this.data.set('instanceName.value', instance.name);
        this.data.set('instanceName.error', false);
    }

    // 修改名称确认
    editNameConfirm() {
        let instance = this.data.get('instance');
        let instanceName = this.data.get('instanceName');
        if (instanceName.error) {
            return;
        }
        this.$http
            .updateAclName({
                name: instanceName.value,
                id: instance.id
            })
            .then(() => {
                this.editNameCancel();
                this.getDetail();
                Notification.success('修改成功');
            });
    }

    // 输入名称
    onNameInput(e) {
        let result = e.value === '' || !/^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/.test(e.value);
        this.data.set('instanceName.error', result);
        this.data.set('instanceName.value', e.value);
        this.ref('editBtn').data.set('disabled', result);
    }

    // 修改名称取消
    editNameCancel() {
        this.ref('editBtn').data.set('disabled', true);
        this.ref('instanceNameEdit').data.set('visible', false);
    }

    deleteRule(rowIndex) {
        let ids = this.data.get(`selectedItem[${rowIndex}]`).map(item => item.id);
        let confirm = new Confirm({
            data: {
                open: true,
                content: '您确定删除选中的规则吗？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http.aclRuleDelete({ids}).then(() => {
                Notification.success('删除成功');
                this.getDetail();
                this.getAclRuleQuota(rowIndex);
            });
        });
        this.data.set(`disableMessage[${rowIndex}]`, '');
    }
    getAclRules(index) {
        this.data.set(`table.subLoading[${index}]`, true);
        let payload = this.getPayload(index);
        let aclId = this.data.get('instance').shortId;
        this.$http
            .getAclRules(aclId, payload)
            .then(res => {
                this.data.set(`table.subLoading[${index}]`, false);
                this.data.set(`subTable.datasource[${index}]`, res.result);
                this.data.set(`pager[${index}].total`, res.totalCount);
            })
            .catch(e => {
                this.data.set(`table.subLoading[${index}]`, false);
            });
    }
    // 改变页数
    onPagerChange(e, index) {
        this.data.set(`pager[${index}].page`, e.value.page);
        this.getAclRules(index);
    }

    // 改变每页显示个数
    onPagerSizeChange(e, index) {
        this.data.set(`pager[${index}].size`, e.value.pageSize);
        this.data.set(`pager[${index}].page`, 1);
        this.getAclRules(index);
    }
    getPayload(index) {
        const {pager} = this.data.get('');
        let subnetId = this.data.get('table.datasource')[index].subnetId;
        let payload = {
            pageNo: pager[index].page,
            pageSize: pager[index].size,
            direction: this.data.get('ruleType')[index],
            ipVersion: this.data.get('ruleIpType')[index],
            subnetId
        };
        if (payload.ipVersion === 'all') {
            delete payload.ipVersion;
        }
        return payload;
    }
    getIamQuery() {
        const id = this.data.get('instance.vpcId');
        const interfaceNames = ['addAclRule', 'updateAclRule', 'deleteAclRule'];
        this.$http
            .getInterfaceIam({
                id,
                interfaceNames
            })
            .then(res => {
                const {requestId, masterAccount, permissionRespMap} = res;
                if (!requestId && !masterAccount) {
                    const accessOpt = {
                        addAclRule: {
                            disabled: false,
                            message: ''
                        },
                        updateAclRule: {
                            disabled: false,
                            message: ''
                        },
                        deleteAclRule: {
                            disabled: false,
                            message: ''
                        }
                    };
                    const accessOptMapText = {
                        addAclRule: '新增',
                        updateAclRule: '编辑',
                        deleteAclRule: '删除'
                    };
                    _.each(interfaceNames, item => {
                        if (!permissionRespMap[item].interfacePermission) {
                            accessOpt[item].disabled = true;
                            accessOpt[item].message = `您没有${accessOptMapText[item]}ACL规则权限，请联系主用户添加。`;
                        }
                    });
                    this.data.set(`accessOptMap`, accessOpt);
                }
            });
    }
    onBack() {
        location.hash = '#/vpc/acl/list';
    }
}
export default San2React(Processor.autowireUnCheckCmpt(AclManage));
