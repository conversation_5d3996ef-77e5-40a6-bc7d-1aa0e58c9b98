/*
 * @description: 添加acl规则
 * @file: acl/pages/create.js
 * @author: p<PERSON><PERSON><EMAIL>
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {checker} from '@baiducloud/bce-opt-checker';
import {OutlinedPlus} from '@baidu/sui-icon';

import {ProtocolType, ActionType, ContextService} from '@/pages/sanPages/common';
import {checkIpv6Cidr, checkIsInSubnet, convertCidrToBinary} from '@/pages/sanPages/utils/common';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import Rule from '@/pages/sanPages/utils/rule';
import rules from '../rules';
import Confirm from '@/pages/sanPages/components/confirm';
import '../style/manage.less';

function isPort(v) {
    return /[0-9]{1,5}/.test(v) && v <= 65535;
}
function portRange(value) {
    value = u.trim(value);
    if (value.toLowerCase() === 'all') {
        return true;
    }
    if (value.indexOf('-') === -1) {
        return isPort(value);
    }
    let array = value.split('-');
    return isPort(array[0]) && isPort(array[1]) && +array[0] < +array[1];
}
function remoteIp(value, ruleIn, ipType, ipVersion, ipv6Cidr, subnetCidr) {
    value = u.trim(value);
    var checkSubnet = (ruleIn && ipType === 'destination') || (!ruleIn && ipType === 'source');
    if (!checkSubnet && value.toLowerCase() === 'all') {
        return false;
    }
    var ipText = ipType ? (ipType === 'destination' ? '目的IP' : '源IP') : 'IP';
    if (ipVersion === 'ipv6') {
        if (!checkIpv6Cidr(value)) {
            return ipText + '格式不正确';
        }
        if (!(checkSubnet ? checkIsInSubnet(value + (/\//g.test(value) ? '' : '/128'), ipv6Cidr) : true)) {
            return ipText + '不在子网CIDR范围内';
        }
    } else {
        var valueString = convertCidrToBinary(value);
        var valueMask = value.split('/')[1] || 32;
        if (valueString.substring(+valueMask, valueString.length).indexOf('1') > -1) {
            return true;
        }
        var reg = new RegExp(Rule.IP_CIDR);
        if (!reg.test(value)) {
            return ipText + '格式不正确';
        }
        if (!(checkSubnet ? checkIsInSubnet(value + (/\//g.test(value) ? '' : '/32'), subnetCidr) : true)) {
            return ipText + '不在子网CIDR范围内';
        }
    }
    return false;
}

const {asComponent, invokeSUI, invokeSUIBIZ, template} = decorators;
const kXhrOptions = {'X-silence': true};
const tpl = html`
    <template>
        <s-dialog class="vpc-acl-create" open="{=open=}" width="1200" title="{{title}}">
            <div class="ruleIpTypeLine" s-if="!isEdit">
                <span>规则类型：</span>
                <s-radio-radio-group class="ruletype" value="{=ruleIpType=}" radioType="button">
                    <s-radio value="ipv4" label="IPv4"></s-radio>
                    <s-tooltip>
                        <div slot="content">
                            <!--bca-disable-next-line-->
                            {{ipv6Ruletype.message | raw}}
                        </div>
                        <s-radio value="ipv6" label="IPv6" disabled="{{ipv6Ruletype.disable}}"></s-radio>
                    </s-tooltip>
                </s-radio-radio-group>
            </div>
            <s-table
                s-if="ruleIpType === 'ipv4'"
                style="overflow: visible"
                columns="{{table.columns}}"
                datasource="{{table.datasource}}"
            >
                <div slot="empty">
                    <s-empty on-click="addRule" actionText="新增规则" />
                </div>
                <div slot="h-position">
                    {{col.label}}
                    <s-tip class="inline-tip" content="{{tips.position}}" skin="question" />
                </div>
                <div slot="h-sourceIpAddress">
                    {{col.label}}
                    <s-tip class="inline-tip" content="{{tips.sourceIpAddress[ruleType]}}" skin="question" />
                </div>
                <div slot="h-sourcePort">
                    {{col.label}}
                    <s-tip class="inline-tip" content="{{tips.port}}" skin="question" />
                </div>
                <div slot="h-destinationIpAddress">
                    {{col.label}}
                    <s-tip class="inline-tip" content="{{tips.destinationIpAddress[ruleType]}}" skin="question" />
                </div>
                <div slot="h-destinationPort">
                    {{col.label}}
                    <s-tip class="inline-tip" content="{{tips.port}}" skin="question" />
                </div>
                <div slot="c-position">
                    <s-input
                        width="60"
                        value="{=position[rowIndex]=}"
                        on-input="dataInput('position', rowIndex, $event)"
                    />
                    <p style="color: #EB5252" s-if="positionErr[rowIndex]">{{positionErr[rowIndex]}}</p>
                </div>
                <div slot="c-protocol">
                    <s-select
                        width="120"
                        on-change="dataChange('protocol', rowIndex, $event)"
                        datasource="{{protocolList}}"
                        value="{=protocol[rowIndex]=}"
                        class="acl_protocol_class"
                    >
                    </s-select>
                </div>
                <div slot="c-sourceIpAddress">
                    <s-input
                        width="100"
                        value="{=sourceIpAddress[rowIndex]=}"
                        on-input="dataInput('sourceIpAddress', rowIndex, $event)"
                    />
                    <p style="color: #EB5252" s-if="sourceIpAddressErr[rowIndex]">{{sourceIpAddressErr[rowIndex]}}</p>
                </div>
                <div slot="c-sourcePort">
                    <s-input
                        width="80"
                        disabled="{{sourcePortDisable[rowIndex]}}"
                        value="{=sourcePort[rowIndex]=}"
                        on-input="dataInput('sourcePort', rowIndex, $event)"
                        on-blur="dataBlur('sourcePort', rowIndex, $event)"
                    />
                    <p style="color: #EB5252" s-if="sourcePortErr[rowIndex]">{{sourcePortErr[rowIndex]}}</p>
                </div>
                <div slot="c-destinationIpAddress">
                    <s-input
                        width="100"
                        value="{=destinationIpAddress[rowIndex]=}"
                        on-input="dataInput('destinationIpAddress', rowIndex, $event)"
                    />
                    <p style="color: #EB5252" s-if="destinationIpAddressErr[rowIndex]">
                        {{destinationIpAddressErr[rowIndex]}}
                    </p>
                </div>
                <div slot="c-destinationPort">
                    <s-input
                        width="80"
                        disabled="{{destinationPortDisable[rowIndex]}}"
                        value="{=destinationPort[rowIndex]=}"
                        on-blur="dataBlur('destinationPort', rowIndex, $event)"
                        on-input="dataInput('destinationPort', rowIndex, $event)"
                    />
                    <p style="color: #EB5252" s-if="destinationPortErr[rowIndex]">{{destinationPortErr[rowIndex]}}</p>
                </div>
                <div slot="c-action">
                    <s-select
                        width="100"
                        on-change="dataChange('action', rowIndex, $event)"
                        datasource="{{actionList}}"
                        value="{=action[rowIndex]=}"
                    >
                    </s-select>
                </div>
                <div slot="c-description">
                    <s-input
                        width="80"
                        value="{=description[rowIndex]=}"
                        on-input="dataInput('description', rowIndex, $event)"
                    />
                    <p style="color: #EB5252" s-if="descriptionErr[rowIndex]">{{descriptionErr[rowIndex]}}</p>
                </div>
                <div slot="c-opt" class="operations">
                    <s-button s-if="!isEdit" skin="stringfy" on-click="deleteRule(rowIndex)">删除</s-button>
                </div>
            </s-table>
            <s-table
                s-if="ruleIpType === 'ipv6'"
                style="overflow: visible"
                columns="{{table.columns}}"
                datasource="{{table.ipv6Datasource}}"
            >
                <div slot="empty">
                    <s-empty on-click="addRule" />
                </div>
                <div slot="h-position">
                    {{col.label}}
                    <s-tip class="inline-tip" placement="right" content="{{tips.position}}" skin="question" />
                </div>
                <div slot="h-sourceIpAddress">
                    {{col.label}}
                    <s-tip class="inline-tip" content="{{tips.sourceIpAddressIpv6[ruleType]}}" skin="question" />
                </div>
                <div slot="h-sourcePort">
                    {{col.label}}
                    <s-tip class="inline-tip" content="{{tips.port}}" skin="question" />
                </div>
                <div slot="h-destinationIpAddress">
                    {{col.label}}
                    <s-tip class="inline-tip" content="{{tips.destinationIpAddressIpv6[ruleType]}}" skin="question" />
                </div>
                <div slot="h-destinationPort">
                    {{col.label}}
                    <s-tip class="inline-tip" content="{{tips.port}}" skin="question" />
                </div>
                <div slot="c-position">
                    <s-input
                        width="60"
                        value="{=positionIpv6[rowIndex]=}"
                        on-input="dataInput('position', rowIndex, $event)"
                    />
                    <p style="color: #EB5252" s-if="positionErrIpv6[rowIndex]">{{positionErrIpv6[rowIndex]}}</p>
                </div>
                <div slot="c-protocol">
                    <s-select
                        width="120"
                        on-change="dataChange('protocol', rowIndex, $event)"
                        datasource="{{protocolList}}"
                        value="{=protocolIpv6[rowIndex]=}"
                    >
                    </s-select>
                </div>
                <div slot="c-sourceIpAddress">
                    <s-input
                        width="100"
                        value="{=sourceIpAddressIpv6[rowIndex]=}"
                        on-input="dataInput('sourceIpAddress', rowIndex, $event)"
                    />
                    <p style="color: #EB5252" s-if="sourceIpAddressErrIpv6[rowIndex]">
                        {{sourceIpAddressErrIpv6[rowIndex]}}
                    </p>
                </div>
                <div slot="c-sourcePort">
                    <s-input
                        width="80"
                        disabled="{{sourcePortIpv6Disable[rowIndex]}}"
                        value="{=sourcePortIpv6[rowIndex]=}"
                        on-input="dataInput('sourcePort', rowIndex, $event)"
                        on-blur="dataBlur('sourcePort', rowIndex, $event)"
                    />
                    <p style="color: #EB5252" s-if="sourcePortErrIpv6[rowIndex]">{{sourcePortErrIpv6[rowIndex]}}</p>
                </div>
                <div slot="c-destinationIpAddress">
                    <s-input
                        width="100"
                        value="{=destinationIpAddressIpv6[rowIndex]=}"
                        on-input="dataInput('destinationIpAddress', rowIndex, $event)"
                    />
                    <p style="color: #EB5252" s-if="destinationIpAddressErrIpv6[rowIndex]">
                        {{destinationIpAddressErrIpv6[rowIndex]}}
                    </p>
                </div>
                <div slot="c-destinationPort">
                    <s-input
                        width="80"
                        disabled="{{destinationPortIpv6Disable[rowIndex]}}"
                        value="{=destinationPortIpv6[rowIndex]=}"
                        on-blur="dataBlur('destinationPort', rowIndex, $event)"
                        on-input="dataInput('destinationPort', rowIndex, $event)"
                    />
                    <p style="color: #EB5252" s-if="destinationPortErrIpv6[rowIndex]">
                        {{destinationPortErrIpv6[rowIndex]}}
                    </p>
                </div>
                <div slot="c-action">
                    <s-select
                        width="100"
                        on-change="dataChange('action', rowIndex, $event)"
                        datasource="{{actionList}}"
                        value="{=actionIpv6[rowIndex]=}"
                    >
                    </s-select>
                </div>
                <div slot="c-description">
                    <s-input
                        width="80"
                        value="{=descriptionIpv6[rowIndex]=}"
                        on-input="dataInput('description', rowIndex, $event)"
                    />
                    <p style="color: #EB5252" s-if="descriptionErrIpv6[rowIndex]">{{descriptionErrIpv6[rowIndex]}}</p>
                </div>
                <div slot="c-opt" class="operations">
                    <s-button s-if="!isEdit" skin="stringfy" on-click="deleteRule(rowIndex)">删除</s-button>
                </div>
            </s-table>
            <div style="padding:10px 0 0 10px" s-if="!isEdit">
                <s-tooltip trigger="{{addMoreRule.disable ? 'hover' : ''}}" placement="right">
                    <!--bca-disable-next-line-->
                    <div slot="content">{{addMoreRule.message | raw}}</div>
                    <s-button skin="primary" disabled="{{addMoreRule.disable}}" on-click="addRule">
                        <outlined-plus />新增规则
                    </s-button>
                </s-tooltip>
            </div>
            <div slot="footer">
                <s-button on-click="close">取消</s-button>
                <s-button skin="primary" disabled="{{disableSub}}" on-click="create">确定</s-button>
            </div>
        </s-dialog>
    </template>
`;

@asComponent('@acl-create')
@template(tpl)
@invokeSUI
@invokeSUIBIZ
class AclCreate extends Component {
    static components = {
        'outlined-plus': OutlinedPlus
    };
    static computed = {
        disableSub() {
            let checkItems = [
                'position',
                'sourceIpAddress',
                'sourcePort',
                'destinationIpAddress',
                'destinationPort',
                'description'
            ]; //eslint-disable-line
            let disableSub = false;
            this.data.get('table.datasource').forEach((item, index) => {
                checkItems.forEach(checkItem => {
                    if (this.data.get(`${checkItem}Err[${index}]`)) {
                        disableSub = true;
                    }
                });
            });
            this.data.get('table.ipv6Datasource').forEach((item, index) => {
                checkItems.forEach(checkItem => {
                    if (this.data.get(`${checkItem}ErrIpv6[${index}]`)) {
                        disableSub = true;
                    }
                });
            });
            return disableSub;
        }
    };

    initData() {
        return {
            title: '添加入站规则',
            tips: {
                position:
                    '取值范围1-32768，且不能与已有条目重复。数值越小，优先级越高，规则匹配顺序为按优先级由高到低匹配',
                sourceIpAddress: {
                    ingress: '例如：**********/24，或**********，或all（0.0.0.0/0）',
                    egress: '源IP需在该子网CIDR范围内，可填网段或IP，例如：**********/24，或**********'
                },
                sourceIpAddressIpv6: {
                    ingress: '例如：1::/64，或1::1，或all（::/0）',
                    egress: '源IP需在该子网CIDR范围内，可填网段或IP，例如：1::/64，或1::1'
                },
                port: '0-65535之间的整数，或区间，例如：8080，1-65535，或all（所有端口）,单独输入0表示all',
                destinationIpAddress: {
                    egress: '例如：**********/24，或**********，或all（0.0.0.0/0）',
                    ingress: '目的IP需在该子网CIDR范围内，可填网段或IP，例如：**********/24，或**********'
                },
                destinationIpAddressIpv6: {
                    egress: '例如：1::/64，或1::1，或all（::/0）',
                    ingress: '目的IP需在该子网CIDR范围内，可填网段或IP，例如：1::/64，或1::1'
                }
            },
            open: false,
            ruleIpType: 'ipv4',
            protocolList: ProtocolType.toArray(),
            actionList: ActionType.toArray(),
            table: {
                columns: [
                    {
                        name: 'position',
                        label: '优先级',
                        width: 90
                    },
                    {
                        name: 'protocol',
                        label: '协议',
                        width: 140
                    },
                    {
                        name: 'sourceIpAddress',
                        label: '源IP',
                        width: 130
                    },
                    {
                        name: 'sourcePort',
                        label: '源端口'
                    },
                    {
                        name: 'destinationIpAddress',
                        label: '目的IP',
                        width: 130
                    },
                    {
                        name: 'destinationPort',
                        label: '目的端口'
                    },
                    {
                        name: 'action',
                        label: '策略'
                    },
                    {
                        name: 'description',
                        label: '备注'
                    },
                    {
                        name: 'opt',
                        label: '操作'
                    }
                ],
                datasource: [],
                ipv6Datasource: []
            },
            position: [],
            positionIpv6: [],
            positionErr: [],
            positionErrIpv6: [],
            protocol: [ProtocolType.toArray()[0].value],
            protocolIpv6: [ProtocolType.toArray()[0].value],
            protocolErr: [],
            protocolErrIpv6: [],
            sourceIpAddress: [],
            sourceIpAddressIpv6: [],
            sourceIpAddressErr: [],
            sourceIpAddressErrIpv6: [],
            sourcePort: ['all'],
            sourcePortIpv6: ['all'],
            sourcePortErr: [],
            sourcePortErrIpv6: [],
            destinationIpAddress: [],
            destinationIpAddressIpv6: [],
            destinationIpAddressErr: [],
            destinationIpAddressErrIpv6: [],
            destinationPort: [],
            destinationPortIpv6: [],
            destinationPortErr: [],
            destinationPortErrIpv6: [],
            action: [ActionType.toArray()[0].value],
            actionIpv6: [ActionType.toArray()[0].value],
            actionErr: [],
            actionErrIpv6: [],
            description: [],
            descriptionIpv6: [],
            descriptionErr: [],
            descriptionErrIpv6: [],
            destinationPort: [],
            destinationPortIpv6: [],
            destinationPortErr: [],
            destinationPortErrIpv6: [],
            disableSub: false,
            isEdit: false,
            addMoreRule: {
                disable: false
            },
            ipv6Ruletype: {
                disable: false,
                message: ''
            }
        };
    }

    inited() {
        let rules = this.data.get('rules');
        this.data.set('rules', rules);
        if (this.data.get('ruleType') === 'ingress') {
            this.data.push('destinationIpAddress', this.data.get('subnetCidr'));
            this.data.push('destinationIpAddressIpv6', this.data.get('ipv6Cidr'));
        } else {
            this.data.push('sourceIpAddress', this.data.get('subnetCidr'));
            this.data.push('sourceIpAddressIpv6', this.data.get('ipv6Cidr'));
        }
        this.editRule();
    }

    attached() {
        this.ipv6CidrCheck();
    }

    editRule() {
        if (!this.data.get('ruleInfo')) {
            this.checkQuota();
            return;
        } else {
            this.data.set('isEdit', true);
            this.data.splice('table.columns', [8, 1]);
            let ruleInfo = this.data.get('ruleInfo');
            let ruleIpType = ruleInfo.ipVersion === 4 ? 'ipv4' : 'ipv6';
            let editItem = ['position', 'protocol', 'sourceIpAddress', 'destinationIpAddress', 'action', 'description']; //eslint-disable-line
            this.data.set('ruleIpType', ruleIpType);
            if (ruleIpType === 'ipv6') {
                this.data.splice('table.datasource', [0, 1]);
                this.data.push('table.ipv6Datasource', {
                    position: '',
                    protocol: '',
                    sourceIpAddress: '',
                    sourcePort: '',
                    destinationIpAddress: '',
                    destinationPort: '',
                    action: '',
                    description: ''
                });
                editItem.forEach(item => {
                    this.data.set(`${item}Ipv6[0]`, ruleInfo[item]);
                    if (item === 'protocol') {
                        let icmpText = 'N/A';
                        let item = ProtocolType.fromValue(ruleInfo.protocol);
                        let protocol = item.rules && item.rules.length > 0 ? item.rules[0].protocol : '';
                        if (protocol === ProtocolType.ICMP) {
                            this.data.set('sourcePortIpv6[0]', icmpText);
                            this.data.set('destinationPortIpv6[0]', icmpText);
                            this.data.set('sourcePortIpv6Disable[0]', true);
                            this.data.set('destinationPortIpv6Disable[0]', true);
                        } else {
                            this.data.set('sourcePortIpv6[0]', ruleInfo.sourcePort);
                            this.data.set('destinationPortIpv6[0]', ruleInfo.destinationPort);
                        }
                        if (ruleInfo.protocol === ProtocolType.ALL) {
                            this.data.set('sourcePortIpv6Disable[0]', true);
                            this.data.set('destinationPortIpv6Disable[0]', true);
                        }
                    }
                });
            } else {
                this.data.push('table.datasource', {
                    position: '',
                    protocol: '',
                    sourceIpAddress: '',
                    sourcePort: '',
                    destinationIpAddress: '',
                    destinationPort: '',
                    action: '',
                    description: ''
                });
                editItem.forEach(item => {
                    this.data.set(`${item}[0]`, ruleInfo[item]);
                    if (item === 'protocol') {
                        let icmpText = 'N/A';
                        let item = ProtocolType.fromValue(ruleInfo.protocol);
                        let protocol = item.rules && item.rules.length > 0 ? item.rules[0].protocol : '';
                        if (protocol === ProtocolType.ICMP) {
                            this.data.set('sourcePort[0]', icmpText);
                            this.data.set('destinationPort[0]', icmpText);
                            this.data.set('sourcePortDisable[0]', true);
                            this.data.set('destinationPortDisable[0]', true);
                        } else {
                            this.data.set('sourcePort[0]', ruleInfo.sourcePort);
                            this.data.set('destinationPort[0]', ruleInfo.destinationPort);
                        }
                        if (ruleInfo.protocol === ProtocolType.ALL) {
                            this.data.set('sourcePortDisable[0]', true);
                            this.data.set('destinationPortDisable[0]', true);
                        }
                    }
                });
            }
        }
    }

    addRule() {
        let ruleIpType = this.data.get('ruleIpType');
        this.data.push('sourcePort', 'all');
        this.data.push('sourcePortIpv6', 'all');
        if (this.data.get('ruleType') === 'ingress') {
            this.data.push('destinationIpAddress', this.data.get('subnetCidr'));
            this.data.push('destinationIpAddressIpv6', this.data.get('ipv6Cidr'));
        } else {
            this.data.push('sourceIpAddress', this.data.get('subnetCidr'));
            this.data.push('sourceIpAddressIpv6', this.data.get('ipv6Cidr'));
        }
        if (ruleIpType === 'ipv4') {
            this.data.push('table.datasource', {
                position: '',
                protocol: '',
                sourceIpAddress: '',
                sourcePort: '',
                destinationIpAddress: '',
                destinationPort: '',
                action: '',
                description: ''
            });
            this.data.push('protocol', ProtocolType.toArray()[0].value);
            this.data.push('action', ActionType.toArray()[0].value);
        } else {
            this.data.push('table.ipv6Datasource', {
                position: '',
                protocol: '',
                sourceIpAddress: '',
                sourcePort: '',
                destinationIpAddress: '',
                destinationPort: '',
                action: '',
                description: ''
            });
            this.data.push('protocolIpv6', ProtocolType.toArray()[0].value);
            this.data.push('actionIpv6', ActionType.toArray()[0].value);
        }
        this.checkQuota();
    }
    checkQuota() {
        let length = this.data.get('table.datasource').length + this.data.get('table.ipv6Datasource').length;
        let quota = Number(this.data.get('quota'));
        let vpcQuota = Number(this.data.get('vpcQuota'));
        let vpcFree = vpcQuota - length;
        if (vpcFree > 0) {
            let free = quota - length;
            let {addMoreRule} = checker.check(rules, '', 'addMoreRule', {free, quota});
            this.data.set('addMoreRule', addMoreRule);
        } else {
            if (FLAG.NetworkSupportXS) {
                this.data.set('addMoreRule.message', 'VPC内所有ACL规则配额不足');
            } else {
                this.data.set(
                    'addMoreRule.message',
                    'VPC内所有ACL规则配额不足。如需增加配额请提交' +
                        `<a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">` +
                        '工单' +
                        '</a>'
                );
            }
            this.data.set('addMoreRule.disable', true);
        }
    }
    deleteRule(index) {
        let ruleIpType = this.data.get('ruleIpType');
        let editItem = [
            'position',
            'protocol',
            'sourceIpAddress',
            'sourcePort',
            'destinationIpAddress',
            'destinationPort',
            'action',
            'description'
        ]; //eslint-disable-line
        if (ruleIpType === 'ipv4') {
            this.data.splice('table.datasource', [index, 1]);
            editItem.forEach(item => {
                this.data.splice(`${item}`, [index, 1]);
                this.data.splice(`${item}Err`, [index, 1]);
            });
        } else {
            this.data.splice('table.ipv6Datasource', [index, 1]);
            editItem.forEach(item => {
                this.data.splice(`${item}Ipv6`, [index, 1]);
                this.data.splice(`${item}ErrIpv6`, [index, 1]);
            });
        }
        this.checkQuota();
    }
    dataBlur(type, index, e) {
        if (e.value === '0' || e.value === '0-65535' || e.value === '1-65535') {
            this.data.set(`${type}[${index}]`, 'all');
        }
    }
    dataInput(type, index, e) {
        let positions = [].concat(this.data.get('positions'), this.data.get('position'), this.data.get('positionIpv6'));
        let ruleIpType = this.data.get('ruleIpType');
        if (type === 'position') {
            let errType = ruleIpType === 'ipv4' ? 'positionErr' : 'positionErrIpv6';
            if (!e.value) {
                this.data.set(`${errType}[${index}]`, '请填写优先级');
            } else if (!/^[1-9][0-9]*$/.test(e.value)) {
                this.data.set(`${errType}[${index}]`, '请填写正整数');
            } else if (e.value > 32768) {
                this.data.set(`${errType}[${index}]`, '不能大于32768');
            } else if (positions.find(item => item === e.value)) {
                this.data.set(`${errType}[${index}]`, '与已有优先级重复');
            } else {
                this.data.set(`${errType}[${index}]`, '');
            }
        }
        if (type === 'sourceIpAddress') {
            let errType = ruleIpType === 'ipv4' ? 'sourceIpAddressErr' : 'sourceIpAddressErrIpv6';
            let ruleIn = this.data.get('ruleType') === 'ingress';
            let ipv6Cidr = this.data.get('ipv6Cidr');
            let subnetCidr = this.data.get('subnetCidr');
            let result = remoteIp(e.value, ruleIn, 'source', ruleIpType, ipv6Cidr, subnetCidr);

            if (!e.value) {
                this.data.set(`${errType}[${index}]`, 'IP范围必填');
            } else if (result) {
                if (typeof result === 'string') {
                    this.data.set(`${errType}[${index}]`, result);
                } else if (result === true) {
                    this.data.set(`${errType}[${index}]`, 'IP范围不符合规则');
                }
            } else {
                this.data.set(`${errType}[${index}]`, '');
            }
        }
        if (type === 'sourcePort') {
            let errType = ruleIpType === 'ipv4' ? 'sourcePortErr' : 'sourcePortErrIpv6';
            if (!e.value) {
                this.data.set(`${errType}[${index}]`, '请填写源端口');
            } else if (!portRange(e.value)) {
                this.data.set(`${errType}[${index}]`, '不符合规则');
            } else {
                this.data.set(`${errType}[${index}]`, '');
            }
        }
        if (type === 'destinationIpAddress') {
            let errType = ruleIpType === 'ipv4' ? 'destinationIpAddressErr' : 'destinationIpAddressErrIpv6';
            let ruleIn = this.data.get('ruleType') === 'ingress';
            let ipv6Cidr = this.data.get('ipv6Cidr');
            let subnetCidr = this.data.get('subnetCidr');
            let result = remoteIp(e.value, ruleIn, 'destination', ruleIpType, ipv6Cidr, subnetCidr);
            if (!e.value) {
                this.data.set(`${errType}[${index}]`, 'IP范围必填');
            } else if (result) {
                if (typeof result === 'string') {
                    this.data.set(`${errType}[${index}]`, result);
                } else {
                    this.data.set(`${errType}[${index}]`, 'IP范围不符合规则');
                }
            } else {
                this.data.set(`${errType}[${index}]`, '');
            }
        }
        if (type === 'destinationPort') {
            let errType = ruleIpType === 'ipv4' ? 'destinationPortErr' : 'destinationPortErrIpv6';
            if (!e.value) {
                this.data.set(`${errType}[${index}]`, '请填写目的端口');
            } else if (!portRange(e.value)) {
                this.data.set(`${errType}[${index}]`, '不符合规则');
            } else {
                this.data.set(`${errType}[${index}]`, '');
            }
        }
        if (type === 'description') {
            let errType = ruleIpType === 'ipv4' ? 'descriptionErr' : 'descriptionErrIpv6';
            if (e.value && e.value.length > 200) {
                this.data.set(`${errType}[${index}]`, '最大长度200');
            } else {
                this.data.set(`${errType}[${index}]`, '');
            }
        }
        if (ruleIpType === 'ipv4') {
            this.data.set(`${type}[${index}]`, e.value);
        } else {
            this.data.set(`${type}Ipv6[${index}]`, e.value);
        }
    }
    dataChange(type, index, e) {
        let icmpText = 'N/A';
        let ruleIpType = this.data.get('ruleIpType');
        let isIpv4 = ruleIpType === 'ipv4';
        const isEdit = this.data.get('isEdit');
        if (type === 'protocol') {
            let item = ProtocolType.fromValue(e.value);
            let port = item.rules && item.rules.length > 0 ? item.rules[0].port : '';
            let protocol = item.rules && item.rules.length > 0 ? item.rules[0].protocol : '';
            let sourcePort = isIpv4 ? 'sourcePort' : 'sourcePortIpv6';
            let destinationPort = isIpv4 ? 'destinationPort' : 'destinationPortIpv6';
            let description = isIpv4 ? 'description' : 'descriptionIpv6';

            if (protocol === ProtocolType.ICMP || e.value === ProtocolType.ALL) {
                this.data.set(`${sourcePort}[${index}]`, protocol === ProtocolType.ICMP ? icmpText : port);
                this.data.set(`${destinationPort}[${index}]`, protocol === ProtocolType.ICMP ? icmpText : port);
                this.data.set(`${sourcePort}Disable[${index}]`, true);
                this.data.set(`${destinationPort}Disable[${index}]`, true);
            } else {
                this.data.set(`${sourcePort}Disable[${index}]`, false);
                this.data.set(`${sourcePort}[${index}]`, ProtocolType.fromValue('all').rules[0].port);
                this.data.set(`${destinationPort}Disable[${index}]`, false);
                this.data.set(`${destinationPort}[${index}]`, port);
            }
            !isEdit && this.data.set(`${description}[${index}]`, item.desc);
            if (isIpv4) {
                this.data.set(`protocol[${index}]`, protocol);
            } else {
                this.data.set(`protocolIpv6[${index}]`, protocol);
            }
        } else {
            if (isIpv4) {
                this.data.set(`${type}[${index}]`, e.value);
            } else {
                this.data.set(`${type}Ipv6[${index}]`, e.value);
            }
        }
    }
    check() {
        let checkItems = ['position', 'sourceIpAddress', 'sourcePort', 'destinationIpAddress', 'destinationPort'];
        let errTip = {
            position: '请填写优先级',
            sourceIpAddress: 'IP范围必填',
            sourcePort: '请填写源端口',
            destinationIpAddress: 'IP范围必填',
            destinationPort: '请填写目的端口'
        };
        let checkResult = true;
        this.data.get('table.datasource').forEach((item, index) => {
            checkItems.forEach(checkItem => {
                if (!this.data.get(`${checkItem}[${index}]`)) {
                    checkResult = false;
                    this.data.set(`${checkItem}Err[${index}]`, errTip[checkItem]);
                } else {
                    this.data.set(`${checkItem}Err[${index}]`, '');
                }
            });
        });
        this.data.get('table.ipv6Datasource').forEach((item, index) => {
            checkItems.forEach(checkItem => {
                if (!this.data.get(`${checkItem}Ipv6[${index}]`)) {
                    checkResult = false;
                    this.data.set(`${checkItem}ErrIpv6[${index}]`, errTip[checkItem]);
                } else {
                    this.data.set(`${checkItem}ErrIpv6[${index}]`, '');
                }
            });
        });
        return checkResult;
    }
    close() {
        this.data.set('open', false);
    }
    create() {
        if (!this.check()) {
            return;
        }
        let rules = [];
        this.data.get('table.datasource').forEach((item, index) => {
            let rule = {
                action: this.data.get(`action[${index}]`),
                description: this.data.get(`description[${index}]`) || '',
                destinationIpAddress: this.data.get(`destinationIpAddress[${index}]`),
                destinationPort: this.data.get(`destinationPort[${index}]`),
                direction: this.data.get('ruleType'),
                ipVersion: 4,
                position: this.data.get(`position[${index}]`),
                protocol: this.data.get(`protocol[${index}]`),
                sourceIpAddress: this.data.get(`sourceIpAddress[${index}]`),
                sourcePort: this.data.get(`sourcePort[${index}]`),
                subnetId: this.data.get('subnetId')
            };
            rules.push(rule);
        });
        this.data.get('table.ipv6Datasource').forEach((item, index) => {
            let rule = {
                action: this.data.get(`actionIpv6[${index}]`),
                description: this.data.get(`descriptionIpv6[${index}]`) || '',
                destinationIpAddress: this.data.get(`destinationIpAddressIpv6[${index}]`),
                destinationPort: this.data.get(`destinationPortIpv6[${index}]`),
                direction: this.data.get('ruleType'),
                ipVersion: 6,
                position: this.data.get(`positionIpv6[${index}]`),
                protocol: this.data.get(`protocolIpv6[${index}]`),
                sourceIpAddress: this.data.get(`sourceIpAddressIpv6[${index}]`),
                sourcePort: this.data.get(`sourcePortIpv6[${index}]`),
                subnetId: this.data.get('subnetId')
            };
            rules.push(rule);
        });
        rules.forEach(item => {
            let all = 'all';
            let allPort = '0-65535';

            if (item.sourcePort.toLowerCase() === all || item.sourcePort === '0') {
                item.sourcePort = allPort;
            } else if (item.protocol === ProtocolType.ICMP) {
                item.sourcePort = '';
            }
            if (item.destinationPort.toLowerCase() === all || item.destinationPort === '0') {
                item.destinationPort = allPort;
            } else if (item.protocol === ProtocolType.ICMP) {
                item.destinationPort = '';
            }
            item.position = u.trim(item.position + '');
            item.sourceIpAddress = u.trim(item.sourceIpAddress).toLowerCase();
            item.sourcePort = u.trim(item.sourcePort + '');
            item.destinationIpAddress = u.trim(item.destinationIpAddress);
            item.destinationPort = u.trim(item.destinationPort + '');
            item.description = u.trim(item.description);
        });
        if (this.data.get('isEdit')) {
            let ruleInfo = this.data.get('ruleInfo');
            let payload = {
                name: ruleInfo.name,
                id: ruleInfo.id,
                subnetId: ruleInfo.subnetId,
                ...rules[0]
            };
            let confirm = new Confirm({
                data: {
                    open: true,
                    content: '编辑的规则将会立即生效，确定添加吗？'
                }
            });
            confirm.attach(document.body);
            confirm.on('confirm', () => {
                this.$http.aclRuleUpdate(payload).then(() => {
                    this.fire('edit');
                    this.data.set('open', false);
                });
            });
        } else {
            let confirm = new Confirm({
                data: {
                    open: true,
                    content: '添加的规则将会立即生效，确定添加吗？'
                }
            });
            confirm.attach(document.body);
            confirm.on('confirm', () => {
                this.$http.aclRuleCreate({aclRules: rules}).then(() => {
                    this.fire('create');
                    this.data.set('open', false);
                });
            });
        }
    }
    ipv6CidrCheck() {
        let {ipv6Ruletype} = checker.check(rules, '', 'ipv6Ruletype', {
            ipv6Cidr: this.data.get('ipv6Cidr')
        });
        this.data.set('ipv6Ruletype', ipv6Ruletype);
    }
}
export default Processor.autowireUnCheckCmpt(AclCreate);
