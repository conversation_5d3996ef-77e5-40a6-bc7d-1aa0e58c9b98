/*
 * @Description: acl导入组件
 * @author: <EMAIL>
 * @Date: 2022-05-07 15:25:06
 */

import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import SheetJS from './SheetJS';

import '../style/leadFile.less';

const {asComponent, invokeSUI, template} = decorators;

const rules = {
    checkPosition(position) {
        if (!position) {
            return '请填写优先级';
        } else if (!/^[1-9][0-9]*$/.test(position)) {
            return '请填写正整数';
        } else if (position > 32768) {
            return '不能大于32768';
        } else {
            return '';
        }
    },
    checkDirection(direction) {
        if (direction === 'ingress' || direction === 'egress') {
            return true;
        }
        return false;
    },
    checkProtocol(protocol) {
        if (protocol === 'all' || protocol === 'tcp' || protocol === 'udp' || protocol === 'icmp') {
            return true;
        }
        return false;
    },
    checkPort(portRange) {
        let port = portRange.split('-');
        if (port.length === 1 && (port[0] === 'all' || (Number(port[0]) >= 0 && Number(port[0]) <= 65535))) {
            return true;
        } else if (
            port.length === 2 &&
            Number(port[0]) >= 0 &&
            Number(port[0]) <= 65535 &&
            Number(port[1]) >= 0 &&
            Number(port[1]) <= 65535 &&
            Number(port[0]) < Number(port[1])
        ) {
            return true;
        }
        return false;
    },
    checkAction(action) {
        if (action === 'allow' || action === 'deny') {
            return true;
        }
        return false;
    },
    checkSamePort(port1, port2) {
        port1 = u.trim(port1);
        port2 = u.trim(port2);
        const all = [0, '0', 'all'];
        if (all.includes(port1) && all.includes(port2)) {
            return true;
        } else return port1 === port2;
    },
    checkSameAddress(address1, address2) {
        address1 = u.trim(address1);
        address2 = u.trim(address2);
        const all = ['0.0.0.0/0', 'all'];
        if (all.includes(address1) && all.includes(address2)) {
            return true;
        } else return address1 === address2;
    },
    checkIsExist(allRules, rule) {
        let sameDirectionRule = allRules.filter(item => item.direction === rule.direction);
        let flag = false;
        for (let i = 0; i < sameDirectionRule.length; i++) {
            let curRule = sameDirectionRule[i];
            // 同方向优先级相同重复
            if (u.trim(curRule.position) == u.trim(rule.position)) {
                flag = true;
                break;
            }
            // 同ip类型除备注不能重复
            if (Number(curRule.ipVersion) === Number(rule.ipVersion)) {
                if (
                    curRule.protocol === rule.protocol &&
                    this.checkSameAddress(curRule.sourceIpAddress, rule.sourceIpAddress) &&
                    this.checkSamePort(curRule.sourcePort, rule.sourcePort) &&
                    this.checkSameAddress(curRule.destinationIpAddress, rule.destinationIpAddress) &&
                    this.checkSameAddress(curRule.destinationPort, rule.destinationPort) &&
                    curRule.action === rule.action
                ) {
                    flag = true;
                    break;
                }
            }
        }
        return flag;
    }
};

const tableSchema = [
    {name: 'direction', label: '方向'},
    {name: 'position', label: '优先级'},
    {name: 'ipVersion', label: 'IP类型'},
    {name: 'protocol', label: '协议'},
    {name: 'sourceIpAddress', label: '源IP'},
    {name: 'sourcePort', label: '源端口'},
    {name: 'destinationIpAddress', label: '目的IP'},
    {name: 'destinationPort', label: '目的端口'},
    {name: 'action', label: '策略'},
    {name: 'description', label: '备注'},
    {name: 'status', label: '检查'}
];

const tpl = html`
    <div>
        <s-dialog
            width="600"
            class="acl-lead-file"
            closeAfterMaskClick="{{false}}"
            open="{=open=}"
            title="{{'导入规则'}}"
        >
            <div class="tip-grey">{{quotaTips}}</div>
            <s-form>
                <s-form-item label="选择文件：">
                    <xui-sheetjs s-ref="upload" on-upload="uploadEvent" statusShow="{{false}}"> </xui-sheetjs>
                    <div s-if="{{uploading || fileName || uploadSuccess || uploadFail}}" class="upload-message">
                        <div class="upload-file-name">
                            <s-icon name="link"></s-icon>
                            <div s-if="{{uploading}}" class="upload-progress">
                                <s-progress value="{{progressValue}}" width="80"></s-progress>
                            </div>
                            <span s-if="{{!uploading && fileName}}">{{ fileName }}</span>
                        </div>
                        <span s-if="{{uploadSuccess}}" class="upload-success">{{ '上传成功!'}}</span>
                        <span s-if="{{uploadFail}}" class="upload-fail">{{ '文档解析失败'}}</span>
                        <s-button s-if="{{uploadFail}}" skin="stringfy" on-click="reUpload">
                            <s-icon name="bcmrefresh"></s-icon>
                            {{ '重试'}}
                        </s-button>
                        <s-button class="delete-file" skin="stringfy" on-click="clearUploader">
                            <s-icon name="close"></s-icon>
                        </s-button>
                    </div>
                </s-form-item>
                <s-form-item s-if="{{fileUploaded}}">
                    {{ '查看规则'}}
                    <div class="rule-message">
                        <span>{{ '共' + ruleTotal + '条规则' }}</span>
                        <span s-if="{{ruleWrongTotal}}" class="wrong-rules">
                            {{ '其中' + ruleWrongTotal + '条出错，请修改后重试' }}
                        </span>
                    </div>
                </s-form-item>

                <s-form-item s-if="{{ruleIsShow}}">
                    <s-table
                        columns="{{ruleList.schema}}"
                        loading="{{ruleList.loading}}"
                        datasource="{{ruleList.datasource}}"
                    >
                        <div slot="empty">{{ '暂无数据'}}</div>
                        <div slot="c-direction">
                            <span class="{{row | getClass('direction')}}">{{ row.direction }}</span>
                        </div>
                        <div slot="c-protocol">
                            <span class="{{row | getClass('protocol')}}">{{ row.protocol }}</span>
                        </div>
                        <div slot="c-destinationPort">
                            <span class="{{row | getClass('destinationPort')}}">{{ row.destinationPort }}</span>
                        </div>
                        <div slot="c-sourcePort">
                            <span class="{{row | getClass('sourcePort')}}">{{ row.sourcePort }}</span>
                        </div>
                        <div slot="c-action">
                            <span class="{{row | getClass('action')}}">{{ row.action }}</span>
                        </div>
                        <div slot="c-status">
                            <div class="status-warp" s-if="{{row.status}}"><s-icon name="ok-reverse"></s-icon></div>
                            <div class="status-warp" s-else>
                                <s-tooltip s-if="{{row.statusMessage}}" position="tc">
                                    <div slot="content">
                                        <!--bca-disable-next-line-->
                                        {{row.statusMessage | raw}}
                                    </div>
                                    <s-icon name="fail-reverse"></s-icon>
                                </s-tooltip>
                                <s-icon s-else name="fail-reverse"></s-icon>
                            </div>
                        </div>
                    </s-table>
                </s-form-item>
            </s-form>
            <div slot="footer">
                <div style="padding-left: 300px; padding-top:20px; display: inline-block;">
                    <span class="repeat_class" s-if="repeatTip">{{ repeatTip }}</span>
                    <s-tooltip
                        class="lead-tooltip"
                        trigger="{{disableMessage || limitDisableMsg ? 'hover' : ''}}"
                        placement="right"
                    >
                        <div slot="content">
                            <!--bca-disable-next-line-->
                            {{(disableMessage || limitDisableMsg) | raw}}
                        </div>
                        <s-button
                            disabled="{{leadDisable || limitDisable || submitDisabled}}"
                            skin="primary"
                            on-click="leadSubmit"
                        >
                            {{ '确定'}}
                        </s-button>
                    </s-tooltip>
                    <s-button size="larger" on-click="leadCancel"> {{ '取消'}} </s-button>
                </div>
            </div>
        </s-dialog>
    </div>
`;

@asComponent('@acl-lead-in')
@template(tpl)
@invokeSUI
class AclLeadIn extends Component {
    static components = {
        'xui-sheetjs': SheetJS
    };
    initData() {
        return {
            open: false,
            ruleIsShow: false,
            ruleList: {
                datasource: [],
                schema: tableSchema,
                loading: false
            },
            ruleTotal: 0,
            ruleWrongTotal: 0,
            progressValue: 0,
            fileName: '',
            uploading: false,
            uploadSuccess: false,
            uploadFail: false,
            fileUploaded: false,
            leadDisable: false,
            limitDisable: false,
            limitDisableMsg: ''
        };
    }

    static filters = {
        getClass(row, type) {
            if (type === 'direction') {
                return !rules.checkDirection(row.direction) ? 'table-wrong' : '';
            } else if (type === 'protocol') {
                return !rules.checkProtocol(row.protocol) ? 'table-wrong' : '';
            } else if (type === 'action') {
                return !rules.checkAction(row.action) ? 'table-wrong' : '';
            } else if (type === 'destinationPort') {
                if (row.protocol !== 'icmp') {
                    return !rules.checkPort(row.destinationPort) ? 'table-wrong' : '';
                }
                return '';
            } else if (type === 'sourcePort') {
                if (row.protocol !== 'icmp') {
                    return !rules.checkPort(row.sourcePort) ? 'table-wrong' : '';
                }
                return '';
            } else {
                return '';
            }
        }
    };

    static computed = {
        quotaTips() {
            const inRuleQuota = this.data.get('inRuleQuota');
            const outRuleQuota = this.data.get('outRuleQuota');
            let ingressNum = this.data.get('allRules').filter(item => item.direction === 'ingress').length;
            let egressNum = this.data.get('allRules').filter(item => item.direction === 'egress').length;
            return `入向已创建${ingressNum}条，出向已创建${egressNum}条，您入向还可以导入${inRuleQuota}条,
        出向还可以导入${outRuleQuota}条，超出数量不允许导入，相同规则不允许重复导入。`;
        }
    };

    /**
     * 上传事件
     *
     * @param {Object} e 事件参数对象
     * @param {string} e.eventType 事件名称
     */
    uploadEvent(e) {
        let eventType = e.eventType;
        let result = e.result;
        if (eventType === 'uploadStart') {
            this.clearUploader();
            this.data.set('fileName', result.name);
        } else if (eventType === 'uploadProgress') {
            let precent = ((result.loaded / result.total) * 100).toFixed(0);
            this.data.set('uploading', true);
            this.data.set('progressValue', precent);
        } else if (eventType === 'uploadSuccess') {
            this.data.set('uploading', false);
            this.data.set('uploadSuccess', true);
            this.getRuleCheckList(result);
        } else if (eventType === 'uploadError') {
            this.uploadError(e);
        }
    }

    /**
     * 上传出错
     *
     * @param {Object} e 事件参数对象
     */
    uploadError(e) {
        this.data.set('uploading', false);
        this.data.set('uploadSuccess', false);
        this.data.set('uploadFail', true);
    }

    /**
     * 重新上传
     */
    reUpload() {
        (this.ref('upload') as any).retry();
    }

    /**
     * 清空当前文件
     */
    clearUploader() {
        this.data.set('ruleIsShow', false);
        this.data.set('ruleList.datasource', []);
        this.data.set('ruleTotal', 0);
        this.data.set('ruleWrongTotal', 0);
        this.data.set('progressValue', 0);
        this.data.set('fileName', '');
        this.data.set('uploading', false);
        this.data.set('uploadSuccess', false);
        this.data.set('uploadFail', false);
        this.data.set('fileUploaded', false);
        // 清除上一次文件的错误信息
        this.data.set('leadDisable', false);
        this.data.set('disableMessage', '');
        this.data.set('limitDisable', false);
        this.data.set('limitMessage', '');
        (this.ref('upload') as any).reset();
        this.fire('enableBtnOK', false);
    }
    // 过滤掉默认规则 默认优先级最低且自定义不能设置
    excludeDefaultRule(item: Record<string, any>) {
        const {position} = item;
        return !['32770', '32769'].includes(position);
    }

    /**
     * 得到规则列表
     *
     * @param {string} result 规则列表
     */
    getRuleCheckList(result) {
        let num = 0;
        let allRules = this.data.get('allRules');
        let addAllRules = [];
        let inTotalRules = 0;
        let outTotalRules = 0;
        result.forEach(item => {
            // 转换上传规则key
            tableSchema.forEach(schema => {
                if (!item[schema.name]) {
                    item[schema.name] = '';
                }
                if (item[schema.label] != undefined) {
                    item[schema.name] = item[schema.label];
                    delete item[schema.label];
                }
            });

            // 过滤出默认规则
            if (!this.excludeDefaultRule(item)) {
                item = null;
            }
            if (item) {
                item.direction === 'egress' ? outTotalRules++ : inTotalRules++;
                if (
                    !rules.checkDirection(item.direction) ||
                    !rules.checkProtocol(item.protocol) ||
                    (item.protocol === 'icmp'
                        ? false
                        : !rules.checkPort(item.destinationPort) || !rules.checkPort(item.sourcePort))
                ) {
                    num++;
                    item.status = false;
                    this.data.set('leadDisable', true);
                } else if (rules.checkIsExist(allRules, item)) {
                    num++;
                    item.status = false;
                    item.statusMessage = 'ACL已存在相同规则，请修改后重试';
                    this.data.set('leadDisable', true);
                } else if (rules.checkIsExist(addAllRules, item)) {
                    num++;
                    item.status = false;
                    item.statusMessage = '导入规则中存在相同规则，请修改后重试';
                    this.data.set('leadDisable', true);
                    this.data.set('disableMessage', '导入规则中存在相同规则，请修改后重试');
                } else {
                    item.status = true;
                }
                addAllRules.push(item);
            }
        });
        const excludeDefaultRule = u.filter(result, this.excludeDefaultRule);
        // 无规则校验
        if (!excludeDefaultRule.length) {
            this.data.set('leadDisable', true);
            this.data.set('disableMessage', '未检测到规则，请先添加');
        }
        if (excludeDefaultRule.length > 20) {
            this.data.set('limitDisable', true);
            this.data.set('limitDisableMsg', 'ACL规则批量添加的数量不能超过20个，请您减少批量添加的数量');
        } else {
            this.data.set('limitDisable', false);
            this.data.set('limitDisableMsg', '');
        }
        this.checkQuota(inTotalRules, outTotalRules);
        this.data.set('ruleTotal', excludeDefaultRule.length);
        this.data.set('ruleWrongTotal', num);
        this.data.set('ruleList.datasource', excludeDefaultRule);
        this.data.set('fileUploaded', true);
        this.data.set('ruleIsShow', true);
        this.fire('enableBtnOK', !num);
    }

    getRuleImport() {
        this.data.set('submitDisabled', true);
        let data = this.data.get('ruleList.datasource').map(item => ({
            ...item,
            subnetId: this.data.get('subnetId')
        }));
        this.$http
            .aclUpload({
                aclRules: data,
                subnetId: this.data.get('subnetId')
            })
            .then(() => {
                this.data.set('open', false);
                this.fire('leadComplete');
            })
            .finally(() => this.data.set('submitDisabled', false));
    }
    leadSubmit() {
        let ruleTotal = this.data.get('ruleTotal');
        let ruleWrongTotal = this.data.get('ruleWrongTotal');
        if (ruleTotal === 0 || ruleWrongTotal > 0) {
            return;
        }
        this.getRuleImport();
    }
    leadCancel() {
        this.data.set('open', false);
    }
    checkQuota(inRules, outRules) {
        let inRuleQuota = this.data.get('inRuleQuota');
        let outRuleQuota = this.data.get('outRuleQuota');
        let inVpcQuota = this.data.get('inVpcQuota');
        let outVpcQuota = this.data.get('outVpcQuota');
        if (inRules > inVpcQuota || outRules > outVpcQuota) {
            this.data.set('leadDisable', true);
            this.data.set('disableMessage', 'vpc配额超限，请修改后重试');
        } else if (inRules > inRuleQuota || outRules > outRuleQuota) {
            this.data.set('leadDisable', true);
            this.data.set('disableMessage', '子网配额超限，请修改后重试');
        }
    }
}
export default Processor.autowireUnCheckCmpt(AclLeadIn);
