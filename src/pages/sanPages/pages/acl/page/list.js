/*
 * @description: acl列表页
 * @file: network/acl/pages/List.js
 * @author: p<PERSON><PERSON><EMAIL>
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {Message} from '@baidu/sui';
import {OutlinedEditingSquare} from '@baidu/sui-icon';
import {urlSerialize, $flag as FLAG} from '@/pages/sanPages/utils/helper';
import {DocService} from '@/pages/sanPages/common';
import testID from '@/testId';
import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import IntroducePanel from '@/pages/sanPages/components/introducePanel/introducePanel';
import '../style/list.less';

const {invokeSUI, invokeSUIBIZ, template, invokeComp} = decorators;
const tpl = html` <template>
    <s-biz-page class="{{klass}}">
        <div class="vpc-acl-header" slot="header">
            <div class="acl-widget">
                <div class="widget-left">
                    <span class="title">{{title}}</span>
                    <vpc-select
                        class="vpc-select"
                        dataTestId="${testID.acl.listSelect}"
                        on-change="vpcChange"
                        on-int="vpcInt"
                    />
                </div>
                <div class="widget-right">
                    <a
                        s-ref="introduce"
                        href="javascript:void(0)"
                        class="help-file function-introduce"
                        on-click="handleShowCard"
                        on-mouseenter="handleMouseEnter('introduce')"
                        on-mouseleave="handleMouseLeave('introduce')"
                        s-if="{{!FLAG.NetworkSupportXS}}"
                    >
                        <img class="s-icon" src="{{introduceIcon}}" />功能简介
                    </a>
                    <a
                        href="{{DocService.acl_helpFile}}"
                        target="_blank"
                        class="help-file"
                        s-if="{{!FLAG.NetworkSupportXS}}"
                    >
                        <s-icon name="warning-new" />帮助文档
                    </a>
                </div>
            </div>
            <introduce-panel
                isShow="{{show}}"
                klass="{{'endpoint-peerconn-wrapper'}}"
                title="{{introduceTitle}}"
                description="{{description}}"
                introduceOptions="{{introduceOptions}}"
                on-toggle="handleToggle($event)"
            ></introduce-panel>
        </div>
        <s-table
            columns="{{table.columns}}"
            loading="{{table.loading}}"
            error="{{table.error}}"
            datasource="{{table.datasource}}"
            on-selected-change="tableSelected($event)"
            on-filter="onFilter"
            on-sort="onSort"
            data-test-id="${testID.acl.listTable}"
            selection="{=table.selection=}"
        >
            <div slot="empty">
                <s-empty>
                    <div slot="action"></div>
                </s-empty>
            </div>
            <div slot="error">
                啊呀，出错了
                <a href="javascript:;" on-click="refresh">重新加载</a>
            </div>
            <div slot="c-shortId">
                <span class="truncated" title="{{row.name}}">
                    <a
                        href="#/vpc/acl/manage?vpcId={{row.vpcId}}&id={{row.id}}&vpcShortId={{row.vpcShortId}}"
                        data-track-id="ti_vpc_dcgw_detail"
                        data-track-name="详情"
                        class="acl-name"
                        data-testid="${testID.acl.listName}{{rowIndex}}"
                    >
                        {{row.name}}
                    </a>
                </span>
                <s-popover
                    s-ref="{{'instanceNameEdit'+rowIndex}}"
                    placement="right"
                    trigger="click"
                    class="edit-popover-class"
                >
                    <div class="edit-wrap" slot="content">
                        <s-input
                            value="{=instanceName.value=}"
                            width="320"
                            placeholder="请输入名称"
                            on-input="onNameInput($event, rowIndex)"
                        />
                        <div class="edit-tip">支持大小写字母，数字，中文和"-_/.”以字母或者中文开头，不超过65个字</div>
                        <s-button
                            skin="primary"
                            s-ref="{{'editNameBtn'+rowIndex}}"
                            disabled="{{true}}"
                            on-click="editConfirm(row, rowIndex)"
                            >确定</s-button
                        >
                        <s-button on-click="editNameCancel(row, rowIndex)">取消</s-button>
                    </div>
                    <outlined-editing-square
                        s-if="row.name!=='默认安全组'&&row.NetworkAclOpt"
                        class="name-icon"
                        on-click="editName(row)"
                    />
                </s-popover>
                <br />
                <span class="truncated" title="{{row.shortId}}">{{row.shortId||row.id}}</span>
                <s-clip-board s-if="row.shortId||row.id" class="name-icon" text="{{row.shortId||row.id}}" />
            </div>
            <div slot="c-opt">
                <span class="operations">
                    <s-button
                        skin="stringfy"
                        on-click="manageAcl(row)"
                        data-test-id="${testID.acl.manageAcl}{{rowIndex}}"
                        >管理</s-button
                    >
                </span>
            </div>
        </s-table>
        <!--企业版版本不支持重构版本-->
        <s-pagination
            s-if="{{pager.total}}"
            slot="footer"
            layout="{{'total, pageSize, pager, go'}}"
            pageSize="{{pager.size}}"
            total="{{pager.total}}"
            page="{{pager.page}}"
            on-pagerChange="onPagerChange"
            on-pagerSizeChange="onPagerSizeChange"
        />
    </s-biz-page>
</template>`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeComp('@vpc-select')
class AclList extends Component {
    static components = {
        'outlined-editing-square': OutlinedEditingSquare,
        'introduce-panel': IntroducePanel
    };
    initData() {
        return {
            FLAG,
            klass: ['main-wrap-new', 'vpc-acl-list'],
            title: 'ACL',
            vpcId: '',
            vpcList: [
                {
                    text: '所在网络：全部私有网络',
                    value: ''
                }
            ],
            table: {
                loading: false,
                selection: {
                    selectedIndex: []
                },
                columns: [
                    {
                        name: 'shortId',
                        label: 'ACL名称/ID'
                    },
                    {
                        name: 'vpc_id',
                        label: '所在网络',
                        render(item, key, col, rowIndex, colIndex, data) {
                            let vpcName = u.escape(item.vpcName) || '-';
                            let vpcShortId = u.escape(item.vpcShortId || item.vpcId) || '-';
                            let vpcId = u.escape(item.vpcId);
                            return `
                                <span class="truncated" title="${vpcName}">
                                    <a href="#/vpc/instance/detail?vpcId=${vpcId}&from=acl" class="text-hidden" data-testid="${testID.acl.vpcName}${rowIndex}">${vpcName}</a>
                                </span>
                                <br>
                                <span class="truncated" title="${vpcShortId}">${vpcShortId}</span>`;
                        }
                    },
                    {
                        name: 'opt',
                        label: '操作'
                    }
                ],
                datasource: []
            },
            pager: {
                size: 10,
                page: 1,
                total: 0
            },
            instanceName: {
                value: '',
                error: true,
                visible: false
            },
            DocService,
            show: true,
            introduceTitle: 'ACL简介',
            description:
                '访问控制列表ACL（Access Control List）是VPC内的防火墙组件，用于控制子网级别的安全策略，灵活设置一个或多个子网的流量，满足用户不同网络部署的安全需求。',
            introduceIcon: INTRODUCE_ICON_COLOR,
            introduceEle: null
        };
    }

    vpcInt() {
        this.loadAclList();
    }

    loadAclList() {
        this.data.set('table.loading', true);
        let url = this.getPayload();
        return this.$http
            .getAclList(url)
            .then(data => {
                this.data.set('table.loading', false);
                this.data.set('table.datasource', data.result);
                this.data.set('pager.total', data.totalCount);
            })
            .catch(err => this.data.set('table.loading', false));
    }

    getPayload() {
        const {pager} = this.data.get('');
        let payload = {
            vpcId: window.$storage.get('vpcId'),
            pageNo: pager.page,
            pageSize: pager.size
        };
        return urlSerialize(payload);
    }

    manageAcl(item) {
        const {vpcId, id, vpcShortId} = item;
        location.hash = '#/vpc/acl/manage?vpcId=' + vpcId + '&id=' + id + '&vpcShortId=' + vpcShortId;
    }

    vpcChange() {
        this.data.set('pager.page', 1);
        this.loadAclList();
    }

    attached() {
        // 企业版版本不支持重构版本
        // this.loadAclList();
        this.data.set('introduceEle', this.ref('introduce'));
    }

    // 点击修改名称icon
    editName(row) {
        this.data.set('instanceName.value', row.name);
        this.data.set('instanceName.error', false);
    }

    // 修改名称确认
    editConfirm(row, rowIndex) {
        let instanceName = this.data.get('instanceName');
        if (instanceName.error) {
            return;
        }
        this.$http
            .updateAclName({
                name: instanceName.value,
                id: row.id
            })
            .then(() => {
                this.editNameCancel(row, rowIndex);
                this.loadAclList();
                Message.success({
                    content: '修改成功'
                });
            });
    }

    // 输入名称
    onNameInput(e, rowIndex) {
        let result = false;
        if (e.value === '' || !/^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/.test(e.value)) {
            result = true;
        } else {
            result = false;
        }
        this.data.set('instanceName.error', result);
        this.data.set('instanceName.value', e.value);
        this.ref('editNameBtn' + rowIndex).data.set('disabled', result);
    }

    // 修改名称取消
    editNameCancel(row, rowIndex) {
        this.ref('editNameBtn' + rowIndex).data.set('disabled', true);
        this.ref('instanceNameEdit' + rowIndex).data.set('visible', false);
    }

    // 改变页数
    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.loadAclList();
    }

    // 改变每页显示个数
    onPagerSizeChange(e) {
        this.data.set('pager.size', e.value.pageSize);
        this.data.set('pager.page', 1);
        this.loadAclList();
    }
    handleToggle(flag) {
        this.data.set('show', flag);
        this.data.set('introduceIcon', INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#151B26';
    }
    handleShowCard() {
        if (!this.data.get('show')) {
            this.data.set('show', true);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type) {
        this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#2468F2';
    }
    handleMouseLeave(type) {
        const isShow = this.data.get('show');
        this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        !isShow && (ele.style.color = '#151B26');
    }
}
export default San2React(Processor.autowireUnCheckCmpt(AclList));
