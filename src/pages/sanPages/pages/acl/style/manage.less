.vpc-acl-manage {
    min-height: 100%;
    width: 100%;
    .vpc-acl-head {
        line-height: 50px;
        .backbox {
            height: 28px;
            line-height: 28px;
            border: 1px solid #ccc;
            outline: none;
            font-size: 12px;
            font-weight: 400;
            cursor: pointer;
            text-align: center;
            vertical-align: middle;
            color: #000;
            background-color: #fff;
            font-family:
                Microsoft Yahei,
                微软雅黑,
                Tahoma,
                Arial,
                Helvetica,
                STHeiti;
            display: inline-block;
            width: 20px;
            .iconfont {
                font-size: 12px;
            }
        }
        span {
            margin-left: 5px;
            vertical-align: middle;
        }
        span:last-child {
            position: relative;
            top: 1px;
        }
    }
    .instance-not-found-class {
        height: 100%;
    }
    .s-detail-page-content {
        background: #f7f7f7;
        margin: 0;
        padding: 0 16px;
    }
    .instance-info {
        display: flex;
        align-items: center;
        .instance-name {
            font-size: 16px;
            font-weight: 500;
            color: #151b26;
            padding: 0px 12px 0px 16px;
        }
        .s-icon {
            font-size: 14px;
            color: #84868c;
        }
    }
    .inline_class {
        display: inline-flex;
    }
    .content {
        margin-top: 16px;
        padding: 24px;
        background-color: #fff;
        border-radius: 6px;
        h4 {
            font-size: 16px;
            display: block;
            color: #151b26;
            line-height: 24px;
            font-weight: 500;
            margin-bottom: 16px;
            border-bottom: none;
        }
        .cell {
            min-width: 33%;
            display: inline-block;
            margin-bottom: 16px;
            .cell-title {
                display: inline-block;
                vertical-align: top;
                color: #5e626a;
                margin-right: 0px;
            }
            .cell-content {
                display: inline-block;
                color: #151a26;
                max-width: 80%;
                word-break: break-all;
                position: relative;
            }
            .name-icon {
                fill: #2468f2;
                color: #2468f2;
                margin-left: 10px;
            }
        }
        .sub-table {
            .opt-line {
                padding: 10px 0;
                .ruletype {
                    display: inline-block;
                    vertical-align: middle;
                    margin-right: 8px;
                }
                .ruleiptype {
                    float: right;
                    margin-right: 8px;
                }
                .lead-btn {
                    float: right;
                    margin-right: 8px;
                }
                .lead-tooltip {
                    float: right;
                }
            }
        }
        .s-table .s-table-body {
            max-height: calc(~'100vh - 382px');
        }
    }
    .pagination-class-table {
        padding-bottom: 60px;
        background: white;
        .s-pagination-wrapper {
            float: right;
            margin-top: 10px;
            margin-right: 16px;
        }
    }
}
.vpc-acl-create {
    .inline-tip {
        top: 3px;
        position: relative;
        .s-tip-warning {
            justify-content: center;
            .warning_class {
                fill: #999;
            }
        }
        .s-tip:hover .s-icon path {
            fill: #2468f2 !important;
        }
    }
    .bind-tip {
        .s-icon {
            font-size: 12px;
            border: 1px solid;
        }
    }
    .ruleIpTypeLine {
        margin-bottom: 20px;
        .ruletype {
            display: inline-block;
            vertical-align: middle;
            margin-right: 8px;
        }
    }
    .s-table {
        overflow: visible;
        .s-table-container {
            overflow: visible;
            .s-table-cell-text {
                overflow: visible;
            }
        }
        .s-table-cell-protocol {
            .s-table-cell-text {
                height: 100px;
                width: 200px;
                position: relative;
                line-height: 80px;
            }
        }
    }
    .s-dialog-wrapper {
        min-width: 1200px !important;
        overflow: unset !important;
    }
    .acl_protocol_class {
        .s-selectdropdown {
            height: 96px;
        }
    }
    .tip_reverse {
        position: relative;
        .s-tip {
            border: none;
            color: #eb5252;
            .s-icon {
                top: -2px;
            }
        }
        .s-tip:hover {
            background: #e6f0ff;
        }
        color: #eb5252;
        font-size: 12px;
        top: 2px;
    }
    .tip_reverse :hover {
        color: #eb5252 !important;
    }
    .s-table-row :hover {
        .tip_reverse {
            .s-tip {
                background-color: #e6f0ff !important;
            }
        }
    }
}
