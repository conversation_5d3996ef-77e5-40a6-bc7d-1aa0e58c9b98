import u from 'lodash';
import {Ipv6Status} from '@/pages/sanPages/common/enum';
import {ContextService} from '@/pages/sanPages/common';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';

const currentModule = 'IPV6';
export default {
    createIpv6: [
        {
            custom(data, options) {
                if (options.createdIpv6Gateway) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: 'IPv6网关配额不足。每个VPC仅可添加1个IPv6网关'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `IPv6网关配额不足。每个VPC仅可添加1个IPv6网关，如需增加配额请提交<a href="${
                                ContextService.Domains.ticket
                            }/#/ticket/create" target="_blank">工单</a>`
                        };
                    }
                } else if (!options.ipv6Cidr) {
                    return {
                        disable: true,
                        message: '未分配IPV6网段，请先为所属VPC分配IPV6网段'
                    };
                }
            }
        }
    ],
    editTag: [
        {
            required: true,
            message: '请先选中实例'
        }
    ],
    ALTER_PRODUCTTYPE: [
        {
            status: [Ipv6Status.AVAILABLE],
            message(data) {
                if (data.length > 1) {
                    return '部分实例当前状态无法计费变更';
                }
                return '该实例当前状态无法计费变更';
            }
        },
        {
            orderAble: true,
            message: '该实例存在正在审核中的订单。如需操作，请稍后重试'
        },
        {
            custom(data) {
                if (u.chain(data).pluck('subProductType').uniq().value().length !== 1) {
                    return {
                        disable: true,
                        message: '选中实例的支付方式必须一致'
                    };
                }
            }
        }
    ],
    CANCEL_ALTER_PRODUCTTYPE: [
        {
            status: [Ipv6Status.AVAILABLE],
            message(data) {
                if (data.length > 1) {
                    return '部分实例当前状态不支持该操作，请稍候重试';
                }
                return '该实例当前状态不支持该操作，请稍候重试';
            }
        },
        {
            custom(data) {
                if (u.find(data, item => item.orderStatus !== 'shift_charge')) {
                    return {
                        disable: true,
                        message: '未进行计费变更无法进行该操作'
                    };
                }
            }
        }
    ],
    addQos: [
        {
            custom(data, options) {
                if (options.free <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: 'IPv6网关限速策略配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `IPv6网关限速策略配额不足，<a href="${
                                '/quota_center/#/quota/apply/create?serviceType='
                            }${currentModule}&region=${
                                window?.$context?.getCurrentRegionId()
                            }&cloudCenterQuotaName=qosQuota" target="_blank">去申请配额</a>`
                        };
                    }
                }
            }
        }
    ],
    addSegment: [
        {
            custom(data, options) {
                if (options.free <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: 'IPv6网关只出不进策略配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `IPv6网关只出不进策略配额不足，<a href="${
                                '/quota_center/#/quota/apply/create?serviceType='
                            }${currentModule}&region=${
                                window?.$context?.getCurrentRegionId()
                            }&cloudCenterQuotaName=segmentQuota" target="_blank">去申请配额</a>`
                        };
                    }
                }
            }
        }
    ],
    changeResource: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            isSingle: true,
            message: '不支持批量变更'
        }
    ],
    ipv6gwRelease: [
        {
            isSingle: true,
            message: '不支持批量操作，请依次进行操作'
        },
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            custom(data) {
                if (u.find(data, item => item.deleteProtect)) {
                    return {
                        disable: true,
                        message: '该实例开启了释放保护功能，请在实例详情页面中关闭释放保护后再点击释放'
                    };
                }
            }
        }
    ]
};
