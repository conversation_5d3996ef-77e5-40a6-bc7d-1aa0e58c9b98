/**
 * @file src/vpc/ipv6gw/detail/schema.js
 * <AUTHOR>
 */

export default {
    $pageClass: 'main-wrap-new vpc-main-wrap vpc-ipv6gw-list',
    $withSidebar: true,
    $withToolbar: true,
    $breadcrumbs: [
        {text: '产品服务'},
        {text: '私有网络VPC-VPC实例', href: '#/vpc/instance/list'},
        {text: '私有网络VPC-IPv6公网网关'}
    ],
    qosSection: {
        body: {
            columns: [
                {
                    name: 'ipv6',
                    label: 'IP',
                    width: 260
                },
                {
                    name: 'outBandwidthInMbps',
                    label: '出口带宽'
                },
                {
                    name: 'inBandwidthInMbps',
                    label: '入口带宽'
                },
                {
                    name: 'operation',
                    label: '操作'
                }
            ],
        }
    },
    segmentSection: {
        body: {
            columns: [
                {
                    name: 'cidr',
                    label: 'CIDR'
                },
                {
                    name: 'operation',
                    label: '操作'
                }
            ],
        }
    }
};
