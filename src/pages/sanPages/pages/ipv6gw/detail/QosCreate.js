/**
 * @file src/vpc/ipv6gw/detail/QosCreate.js
 * <AUTHOR>
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';

import {checkIsInSubnet} from '@/pages/sanPages/utils/common';
import RULE from '@/pages/sanPages/utils/rule';

const {asComponent, invokeSUIBIZ, invokeSUI, template} = decorators;
const formValidator = self => ({
    ipv6: {
        validator(rule, value, callback) {
            let source = self.data.get('formData');
            let error = '';
            //  获得最后一个冒号后面的部分
            const lastStr = value && value.split(':').pop();
            if (!value) {
                error = '请填写IP';
            } else if (!RULE.IPV6.test(value)) {
                error = '请填写正确的IPv6格式';
            } else if (value[0].indexOf(':') > -1 && value[0].indexOf('.') > -1) {
                error = '请填写标准的IPv6格式';
            } else if (RULE.IP.test(lastStr)) {
                error = '不支持IPv6与IPv4混合';
            } else if (!checkIsInSubnet(value + '/128', source.ipv6Cidr)) {
                error = 'IP需在VPC分配的IPv6网段内';
            }
            error ? callback(error) : callback();
        }
    },
    outBandwidthInMbps: {
        validator(rule, value, callback) {
            let source = self.data.get('formData');
            let error = '';
            if (!value) {
                error = '请填写出向带宽';
            } else if (!/^\d+$/.test(value)) {
                error = '请填写整数';
            } else {
                let max = source.bandwidthInMbps;
                if (value < 0 || value > max) {
                    error = `出口带宽取值范围[0, ${max}]`;
                }
            }
            error ? callback(error) : callback();
        }
    },
    inBandwidthInMbps: {
        validator(rule, value, callback) {
            let source = self.data.get('formData');
            let error = '';
            if (!value) {
                error = '请填写入向带宽';
            } else if (!/^\d+$/.test(value)) {
                error = '请填写整数';
            } else {
                let max = Math.max(+source.bandwidthInMbps || 0, 10);

                if (value < 0 || value > max) {
                    error = `入口带宽取值范围[0, ${max}]`;
                }
            }

            error ? callback(error) : callback();
        }
    }
});

const tpl = html`
    <template>
        <div class="vpc-ipv6gw-qos-create">
            <s-form s-ref="form" rules="{{rules}}" data="{=formData=}" label-align="left">
                <s-form-item label="IP：" prop="ipv6" help="{{'IPv6格式，例1::1'}}">
                    <s-input
                        value="{=formData.ipv6=}"
                        disabled="{{!!qosId}}"
                        width="300"
                        track-id="ti_vpc_ipv6_qos_create"
                        track-name="限速策略/IP"
                    />
                </s-form-item>
                <s-form-item label="出向带宽：" prop="outBandwidthInMbps">
                    <s-input
                        value="{=formData.outBandwidthInMbps=}"
                        track-id="ti_vpc_ipv6_qos_create"
                        width="300"
                        track-name="限速策略/出向带宽"
                    />
                    <p class="qos-unit">Mbps</p>
                </s-form-item>
                <s-form-item label="入向带宽：" prop="inBandwidthInMbps">
                    <s-input
                        value="{=formData.inBandwidthInMbps=}"
                        width="300"
                        track-id="ti_vpc_ipv6_qos_create"
                        track-name="限速策略/入向带宽"
                    />
                    <p class="qos-unit">Mbps</p>
                </s-form-item>
            </s-form>
        </div>
    </template>
`;

@asComponent('@qos-create')
@template(tpl)
@invokeSUIBIZ
@invokeSUI
class QosCreate extends Component {
    initData() {
        return {
            rules: formValidator(this),
            formData: {},
            formErrors: {}
        };
    }

    inited() {
        let bandwidthInMbps = this.data.get('bandwidthInMbps');
        this.data.set('formData.bandwidthInMbps', bandwidthInMbps);
        this.data.set('formData.ipv6Cidr', this.data.get('ipv6Cidr') || this.data.get('ipv6'));
    }

    attached() {
        if (this.data.get('qosId')) {
            this.data.set('formErrors', null);
        }
    }

    doSubmit() {
        const form = this.ref('form');
        return form.validateFields().then(() => {
            let payload = null;
            let formData = this.data.get('formData');
            if (this.data.get('qosId')) {
                payload = u.extend(
                    {
                        qosId: this.data.get('qosId'),
                        gatewayId: this.data.get('gatewayId')
                    },
                    u.pick(formData, 'ipv6', 'outBandwidthInMbps', 'inBandwidthInMbps')
                );
                return this.$http.ipv6gwQosUpdate(payload);
            } else {
                payload = u.extend(
                    {gatewayId: this.data.get('gatewayId')},
                    u.pick(formData, 'ipv6', 'outBandwidthInMbps', 'inBandwidthInMbps')
                );
                return this.$http.ipv6gwQosCreate(payload);
            }
        });
    }
}
export default Processor.autowireUnCheckCmpt(QosCreate);
