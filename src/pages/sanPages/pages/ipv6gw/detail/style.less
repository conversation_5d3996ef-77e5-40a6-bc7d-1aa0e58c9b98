/**
 * @file network/eni/pages/styles/detail.less
 * <AUTHOR>
 */
.clearfix() {
    zoom: 1;
    &:before,
    &:after {
        content: '';
        display: table;
    }
    &:after {
        clear: both;
    }
}

.vpc-ipv6gw-detail {
    .s-detail-page-content {
        background: #f7f7f7;
        margin: 0;
        padding: 0 16px;
    }
    .cell-title {
        display: inline-block;
        vertical-align: top;
        color: #5e626a;
        margin-right: 16px;
        width: 60px;
    }
    .cell-content {
        display: inline-block;
        color: #151a26;
        max-width: 80%;
        word-break: break-all;
        position: relative;
        .inline-tip {
            position: relative;
            top: 3px;
            margin-left: 12px;
            .s-tip-warning:hover path {
                fill: #ff9326;
            }
        }
    }
    .content {
        background: white;
        margin: 16px 0;
        border-radius: 6px;
    }
    .bui-detail-page-body .detail-page-body-subsidebar {
        width: 0px !important;
    }
    .ipv6gw-detail-content {
        padding: 24px 24px 0;
        h2 {
            display: block;
            color: #151b26;
            line-height: 24px;
            font-weight: 500;
            margin-bottom: 16px;
            font-size: 16px;
            span {
                // vertical-align: middle;
            }
        }
        .content-item-box {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            .content-item {
                margin-bottom: 16px;
                width: 33%;
                label {
                    color: #666;
                    vertical-align: middle;
                }
            }
        }
    }
    .ipv6gw-detail-table {
        padding: 24px;
        width: 45%;
        display: inline-block;
        h2 {
            display: block;
            color: #151b26;
            line-height: 24px;
            font-weight: 500;
            margin-bottom: 16px;
            font-size: 16px;
            span {
                vertical-align: middle;
            }
        }
        .list-section {
            margin-top: 10px;
        }
    }
    .s-table .s-table-body {
        max-height: calc(~'100vh - 514px');
    }
    .title_class {
        display: flex;
        align-items: center;
        .name {
            font-size: 16px;
            font-weight: 500;
            color: #151b26;
            padding-left: 16px;
        }
        .s-icon {
            font-size: 14px;
            color: #84868c;
        }
    }
    .operations {
        .s-button {
            padding: 0;
            margin-right: 12px;
        }
    }
}

.vpc-ipv6gw-segment-create,
.vpc-ipv6gw-qos-create {
    .bui-form-item {
        display: flex;
        .bui-form-item-label {
            text-align: left;
            width: 80px;
            line-height: 30px;
        }
    }
    .segment-tip,
    .qos-tip {
        margin-left: 110px;
    }
    .bui-form-item-content {
        display: flex;
    }
    .qos-unit,
    .bui-form-item-invalid-label {
        line-height: 30px;
        margin: 0 5px;
        display: inline-block;
    }
    .s-form {
        .s-form-item {
            margin-bottom: 20px;
        }
        .s-form-item-label {
            width: 118px;
            line-height: 30px;
            height: 30px;
        }
        .segment-tip {
            margin-left: 110px;
        }
    }
}

.form_dialog {
    .bui-dialog {
        border-radius: 6px;
    }
    .bui-dialog-mask {
        background: rgba(7, 12, 20, 0.5);
    }
    .bui-dialog-head {
        border-radius: 6px;
        .bui-dialog-title {
            span {
                font-weight: 500;
                font-size: 16px;
                color: #151a26;
                flex: 1;
            }
        }
    }
    .bui-dialog-body-panel {
        font-size: 12px;
        color: #151a26;
        font-weight: 400;
        padding: 24px;
        min-height: 100px;
        line-height: 20px;
        overflow-y: auto;
    }
    .bui-dialog-title {
        background: #fff;
        border-radius: 6px;
        box-sizing: content-box;
        padding: 16px 24px 0 !important;
        display: flex;
    }
    .bui-dialog-foot {
        border-top: none;
        .bui-button {
            border-radius: 4px;
            padding: 0 16px;
            height: 30px;
            line-height: 30px;
            min-width: 46px;
        }
        .skin-primary {
            background: #2468f2;
            border-color: #2468f2;
        }
    }
}
