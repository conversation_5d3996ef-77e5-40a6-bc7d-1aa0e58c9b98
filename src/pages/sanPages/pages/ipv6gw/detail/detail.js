/**
 * @file network/ipv6gw/detail/detail.js
 * <AUTHOR>
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {Notification} from '@baidu/sui';
import {OutlinedPlus, OutlinedLeft} from '@baidu/sui-icon';

import rules from '../rules';
import {toTime, waitActionDialog} from '@/pages/sanPages/utils/helper';
import schema from './schema';
import {Ipv6SubProductType} from '@/pages/sanPages/common/enum';
import Confirm from '@/pages/sanPages/components/confirm';
import SegmentCreate from './SegmentCreate';
import QosCreate from './QosCreate';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import {parseQuery} from '@/utils';
import Rule from '@/pages/sanPages/utils/rule';
import './style.less';

const {asPage, invokeSUIBIZ, invokeSUI, template, invokeComp, service} = decorators;
/* eslint-disable */
const tpl = html`<template>
    <s-app-detail-page class="{{klass}} {{instanceFound ? '' : 'no-instance-content'}}">
        <div slot="pageTitle" class="title_class">
            <s-app-link to="/network/#/vpc/ipv6gw/list" class="page-title-nav"><icon-left />返回</s-app-link>
            <span class="name">{{ipv6Info.name || '-'}}</span>
            <span class="{{detail.styleClass}}"> {{detail.statusText}} </span>
        </div>
        <s-loading loading="{{loading}}" class="global-loading-class"></s-loading>
        <div class="content" s-if="instanceFound && !loading">
            <div class="ipv6gw-detail-content">
                <h2><span>{{subTitle}}</span></h2>
                <ul class="content-item-box">
                    <li class="content-item">
                        <label class="cell-title">名称：</label>
                        <span class="cell-content">
                            {{ipv6Info.name}}
                            <span s-if="FLAG.NetworkIpv6Opt">
                                <edit-popover value="{=ipv6Info.name=}" rule="{{Rule.NAME}}" on-edit="updateName">
                                    <a href="javascript:void(0)">变更</a>
                                </edit-popover>
                            </span>
                        </span>
                    </li>
                    <li class="content-item">
                        <label class="cell-title">ID：</label>
                        <span class="cell-content">{{ipv6Info.gatewayId}}</span>
                        <s-clip-board text="{{ipv6Info.gatewayId}}" successMessage="已复制到剪贴板" />
                    </li>
                    <li class="content-item">
                        <label class="cell-title">付费方式：</label>
                        <span class="cell-content">{{ipv6Info.subProductType | subProductType}}</span>
                    </li>
                    <li class="content-item">
                        <label class="cell-title">公网带宽：</label>
                        <span class="cell-content">{{ipv6Info.bandwidthInMbps}}Mbps</span>
                    </li>
                    <li class="content-item">
                        <label class="cell-title">所在网络：</label>
                        <span class="cell-content">
                            <a
                                href="#/vpc/instance/list"
                                data-track-id="ti_vpc_eni_detail"
                                data-track-name="实例详情/所在网络"
                            >
                                {{ipv6Info.vpcName}}({{ipv6Info.cidr}})
                                <span>（{{ipv6Info.ipv6Cidr}}）</span>
                            </a>
                        </span>
                    </li>
                    <li class="content-item">
                        <label class="cell-title">创建时间：</label>
                        <span class="cell-content"> {{ipv6Info.createTime | timeFormat}} </span>
                    </li>
                    <li class="content-item">
                        <label class="cell-title">释放保护：</label>
                        <span class="cell-content">
                            <s-switch
                                disabled="{{deleteProtectStatus}}"
                                on-change="updateDeleteProtect"
                                checked="{=ipv6Info.deleteProtect=}"
                            />
                            <!--<s-tip
                            class="inline-tip"
                            skin="warning"
                            placement="topRight"
                            content="请确认您已解除相关的关联设备"
                        />-->
                        </span>
                    </li>
                    <li class="content-item" s-if="FLAG.NetworkIpv6SupportOrganization">
                        <label class="cell-title">项目信息：</label>
                        <span class="cell-content">
                            <span s-for="item,index in ipv6Info.resourceGroups"> {{item.resourceGroupName}} </span>
                        </span>
                    </li>
                </ul>
            </div>
            <div class="ipv6gw-detail-table">
                <h2><span>只出不进策略</span></h2>
                <s-tip-button
                    skin="primary"
                    isDisabledVisibile="{{true}}"
                    on-click="createSegment()"
                    disabled="{{disableCreateSegment.disable}}"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{disableCreateSegment.message | raw}}
                    </div>
                    <outlined-plus />添加策略</s-tip-button
                >
                <div class="list-section">
                    <s-table datasource="{{segmentList.datasource}}" columns="{{segmentList.schema}}">
                        <div slot="empty">
                            <s-empty on-click="createSegment()"> </s-empty>
                        </div>
                        <div slot="c-operation" class="operations">
                            <s-button skin="stringfy" on-click="deleteSegment(row)">删除</s-button>
                        </div>
                    </s-table>
                </div>
            </div>
            <div class="ipv6gw-detail-table">
                <h2><span>IP限速策略</span></h2>
                <s-tip-button
                    isDisabledVisibile="{{true}}"
                    skin="primary"
                    on-click="createQos()"
                    disabled="{{disableCreateQos.disable}}"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{disableCreateQos.message | raw}}
                    </div>
                    <outlined-plus />
                    添加策略
                </s-tip-button>
                <div class="list-section">
                    <s-table
                        datasource="{{qosList.datasource}}"
                        columns="{{qosList.schema}}"
                        on-command="tableCommand($event)"
                    >
                        <div slot="empty">
                            <s-empty on-click="createQos()"> </s-empty>
                        </div>
                        <div slot="c-operation" class="operations">
                            <s-button skin="stringfy" on-click="editQos(row)">修改限速</s-button>
                            <s-button skin="stringfy" on-click="deleteQos(row)">删除</s-button>
                        </div>
                    </s-table>
                </div>
            </div>
        </div>
    </s-app-detail-page>
</template>`;
/* eslint-enable */

@template(tpl)
@invokeSUIBIZ
@invokeSUI
@invokeComp('@qos-create', '@segment-create', '@edit-popover')
class Ipv6gwDetail extends Component {
    static components = {
        'outlined-plus': OutlinedPlus,
        'icon-left': OutlinedLeft
    };
    static filters = {
        timeFormat(time) {
            return toTime(time);
        },
        subProductType(item) {
            return Ipv6SubProductType.getTextFromValue(item);
        }
    };

    initData() {
        return {
            FLAG,
            klass: 'vpc-ipv6gw-detail',
            title: '',
            withSidebar: true,
            subTitle: '基本信息',
            remark: '',
            hasIpv6: true,
            qosList: {
                schema: schema.qosSection.body.columns,
                datasource: []
            },
            segmentList: {
                schema: schema.segmentSection.body.columns,
                datasource: []
            },
            disableCreateQos: {
                message: '',
                disable: false
            },
            disableCreateSegment: {
                message: '',
                disable: false
            },
            Rule: Rule.DETAIL_EDIT,
            instanceFound: true,
            loading: true,
            deleteProtectStatus: false,
            urlQuery: parseQuery(location.hash)
        };
    }

    inited() {
        this.children = [];
    }

    attached() {
        this.loadDetail();
    }

    loadDetail() {
        this.$http
            .ipv6gwDetail({
                vpcId: this.data.get('urlQuery.vpcId')
            })
            .then(data => {
                if (data && data.gatewayId) {
                    this.data.set('ipv6Info', data);
                    this.data.set('qosList.datasource', data.qosList);
                    this.data.set('segmentList.datasource', data.segmentList);
                    this.data.set('hasIpv6', true);
                    this.loadQosQuota(data.gatewayId);
                    this.loadSegmentQuota(data.gatewayId);
                } else {
                    this.data.set('instanceFound', false);
                    this.data.set('hasIpv6', false);
                }
            })
            .finally(e => this.data.set('loading', false));
    }

    updateName(value) {
        this.$http
            .ipv6gwUpdate({
                gatewayId: this.data.get('ipv6Info.gatewayId'),
                name: value
            })
            .then(() => this.data.set('ipv6Info.name', value));
    }

    loadQosQuota(gatewayId) {
        this.$http
            .ipv6gwQosQuota({gatewayId})
            .then(data => {
                let {addQos} = checker.check(rules, '', 'addQos', {free: data.free});
                this.data.set('disableCreateQos.disable', addQos.disable);
                this.data.set('disableCreateQos.message', addQos.message);
            })
            .catch(() => this.data.set('disableCreateQos.disable', true));
    }

    loadSegmentQuota(gatewayId) {
        this.$http
            .ipv6gwSegmentQuota({gatewayId})
            .then(data => {
                let {addSegment} = checker.check(rules, '', 'addSegment', {free: data.free});
                this.data.set('disableCreateSegment.disable', addSegment.disable);
                this.data.set('disableCreateSegment.message', addSegment.message);
            })
            .catch(() => this.data.set('disableCreateQos.disable', true));
    }

    createSegment() {
        const options = this.data.get('ipv6Info');
        const create = waitActionDialog(
            SegmentCreate,
            {
                title: '添加策略',
                width: 600
            },
            options
        );
        create.on('success', () => {
            Notification.success('添加成功');
            this.loadDetail();
        });
    }

    createQos() {
        const options = this.data.get('ipv6Info');
        const create = waitActionDialog(
            QosCreate,
            {
                title: '添加策略',
                width: 600
            },
            options
        );
        create.on('success', () => {
            Notification.success('添加成功');
            this.loadDetail();
        });
    }
    editQos(row) {
        waitActionDialog(
            QosCreate,
            {
                title: '编辑限速策略',
                width: 600
            },
            u.extend({formData: row}, row, this.data.get('ipv6Info'))
        ).on('success', () => {
            Notification.success('修改成功');
            this.loadDetail();
        });
    }
    deleteQos(row) {
        const dialog = new Confirm({
            data: {
                open: true,
                content: '确定要删除该策略？',
                title: '删除'
            }
        });
        dialog.on('confirm', () => {
            this.$http
                .ipv6gwQosDelete({
                    qosIdList: [row.qosId],
                    gatewayId: this.data.get('ipv6Info').gatewayId
                })
                .then(() => {
                    Notification.success('删除成功');
                    this.loadDetail();
                })
                .catch(error => {
                    if (error && error.global) {
                        Notification.error(error.global, {duration: -1});
                    }
                });
        });
        dialog.attach(document.body);
    }

    deleteSegment(row) {
        const dialog = new Confirm({
            data: {
                open: true,
                content: '确定要删除该策略？',
                title: '删除'
            }
        });
        dialog.on('confirm', () => {
            this.$http
                .ipv6SegmentDelete({
                    segmentId: row.segmentId,
                    gatewayId: this.data.get('ipv6Info').gatewayId
                })
                .then(() => {
                    Notification.success('删除成功');
                    this.loadDetail();
                })
                .catch(error => {
                    if (error && error.global) {
                        Notification.error(error.global, {duration: -1});
                    }
                });
        });
        dialog.attach(document.body);
    }

    onRegionChange() {
        location.hash = '#/vpc/ipv6gw/list';
    }

    // 更新释放保护状态
    updateDeleteProtect({value}) {
        this.data.set('deleteProtectStatus', true);
        this.$http
            .ipv6gwUpdateDeleteProject({
                gatewayId: this.data.get('ipv6Info').gatewayId,
                deleteProtect: value
            })
            .then(() => {
                this.data.set('deleteProtectStatus', false);
                this.data.set('ipv6Info.deleteProtect', value);
            })
            .catch(() => {
                this.data.set('deleteProtectStatus', false);
                this.data.set('ipv6Info.deleteProtect', !value);
            });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(Ipv6gwDetail));
