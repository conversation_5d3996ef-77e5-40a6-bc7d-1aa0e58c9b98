/**
 * @file src/vpc/ipv6gw/detail/SegmentCreate.js
 * <AUTHOR>
 */
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';

import {checkIpv6Cidr, checkIsInSubnet} from '@/pages/sanPages/utils/common';
import RULE from '@/pages/sanPages/utils/rule';

const {asComponent, invokeSUI, template} = decorators;
const formValidator = self => ({
    cidr: {
        validator(rule, value, callback) {
            let source = self.data.get('formData');
            let error = '';
            if (!value) {
                error = '请填写CIDR';
            }
            let result = value.split('/');
            if (!error && !RULE.IPV6.test(result[0])) {
                error = '格式不正确';
            }
            if (!error && !result[1]) {
                error = '请填写掩码';
            }
            if (!error && !checkIpv6Cidr(value)) {
                error = '格式不正确';
            }
            if (!error && !checkIsInSubnet(value, source.ipv6Cidr)) {
                error = 'CIDR需在VPC分配的IPv6网段内';
            }
            error ? callback(error) : callback();
        }
    }
});
const tpl = html`
    <template>
        <div class="vpc-ipv6gw-segment-create">
            <s-form s-ref="form" rules="{{rules}}" data="{=formData=}" label-align="left">
                <s-form-item label="CIDR：" prop="cidr" help="{{'IPv6网段，例2000::0:0/64'}}">
                    <s-input
                        value="{=formData.cidr=}"
                        track-id="ti_vpc_ipv6_segment_create"
                        track-name="只出不进策略/CIDR"
                    />
                </s-form-item>
            </s-form>
        </div>
    </template>
`;

@asComponent('@segment-create')
@template(tpl)
@invokeSUI
class SegmentCreate extends Component {
    initData() {
        return {
            rules: formValidator(this),
            formData: {
                cidr: ''
            }
        };
    }

    inited() {
        this.data.set('formData.ipv6Cidr', this.data.get('ipv6Cidr'));
    }

    doSubmit() {
        const form = this.ref('form');
        return form.validateFields().then(() => {
            let payload = {
                gatewayId: this.data.get('gatewayId'),
                cidr: this.data.get('formData.cidr')
            };
            return this.$http.ipv6gwSegmentCreate(payload);
        });
    }
}
export default Processor.autowireUnCheckCmpt(SegmentCreate);
