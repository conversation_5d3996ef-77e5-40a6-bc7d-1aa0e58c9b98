/**
 * @file network/ipv6gw/alter/alter.js
 * <AUTHOR>
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';

import {alterProductType} from '@/pages/sanPages/utils/helper';
import {Ipv6Payment} from '@/pages/sanPages/common/enum';

const {asPage, invokeSUI, invokeSUIBIZ, template} = decorators;
const tpl = html`
    <div class="vpc-ipv6gw-alter">
        <s-form s-ref="form" data="{=formData=}">
            <s-form-item label="当前计费：">
                <div>{{currentLabel}}</div>
            </s-form-item>
            <s-form-item label="计费变更为：">
                <s-radio-radio-group
                    datasource="{{productTypeList}}"
                    value="{=formData.subProductType=}"
                    track-id="ti_vpc_nat_create"
                    track-name="付费方式"
                >
                </s-radio-radio-group>
            </s-form-item>
            <s-form-item name="bandwidth">
                <s-table datasource="{{table.datasource}}" schema="{{table.schema}}">
                    <div slot="empty">
                        <s-empty on-click="onCreate" />
                    </div>
                    <span slot="c-bandwidthInMbps">
                        <s-input
                            width="{{100}}"
                            value="{{row.bandwidthInMbps}}"
                            on-input="setBandWidth(row, $event)"
                            track-id="ti_vpc_ipv6gw_alter"
                            track-name="计费变更/带宽"
                        />
                        <span s-if="bandwidthTip" style="color:red">{{bandwidthTip}}</span>
                    </span>
                </s-table>
            </s-form-item>
        </s-form>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
class Alter extends Component {
    initData() {
        return {
            formData: {},
            table: {
                datasource: null,
                schema: [
                    {name: 'name', label: 'ipv6网关名称', width: 120},
                    {name: 'gatewayId', label: 'ID', width: 120},
                    {name: 'bandwidthInMbps', label: '公网带宽峰值', width: 180}
                ]
            },
            bandwidthMap: {},
            productTypeList: [],
            currentLabel: '',
            bandwidthTip: null
        };
    }

    inited() {
        this.init();
    }

    init() {
        let datas = this.data.get('selectedItems');
        u.each(datas, item => {
            this.data.set(`bandwidthMap['${item.gatewayId}']`, item.bandwidthInMbps);
        });
        this.data.set('table.datasource', datas);
        let productTypeList = null;
        if (datas[0].subProductType === Ipv6Payment.BANDWIDTH) {
            productTypeList = Ipv6Payment.toArray('NETRAFFIC');
        } else {
            productTypeList = Ipv6Payment.toArray('BANDWIDTH');
        }
        this.data.set('productTypeList', productTypeList);
        this.data.set('currentLabel', Ipv6Payment.getTextFromValue(datas[0].subProductType));
        this.data.set('formData.subProductType', this.data.get('productTypeList')[0].value);
    }

    setBandWidth(item, e) {
        if (!e.value || !/^[1-9][0-9]*$/.test(e.value)) {
            this.data.set('bandwidthTip', '请输入正整数');
        } else {
            this.data.set('bandwidthTip', null);
            this.data.set(`bandwidthMap['${item.gatewayId}']`, e.value);
        }
    }

    doSubmit() {
        let ids = u.map(this.data.get('selectedItems'), item => item.gatewayId);
        let url = '/api/ipv6gw/order/confirm/to_postpay';
        let datas = u.map(this.data.get('table.datasource'), item => {
            return u.extend(item, {bandwidthInMbps: +this.data.get(`bandwidthMap['${item.gatewayId}']`)});
        });
        let param = {
            productType: 'postpay',
            subProductType: this.data.get('formData.subProductType'),
            data: datas,
            productTypeBefore: 'postpay',
            subProductTypeBefore: this.data.get('selectedItems')[0].subProductType
        };
        let type = 'TO_POSTPAY';
        sessionStorage.setItem('IPVSIXGW_ALTER', JSON.stringify(param));
        alterProductType('IPVSIXGW', ids, type, null, url);
        return Promise.resolve();
    }

    onRegionChange() {
        location.hash = '#/vpc/ipv6gw/list';
    }
}
export default Processor.autowireUnCheckCmpt(Alter);
