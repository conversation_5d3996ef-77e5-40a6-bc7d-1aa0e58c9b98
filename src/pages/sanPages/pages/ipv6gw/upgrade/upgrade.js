/**
 * @file src/vpc/ipv6gw/Upgrade.js
 * <AUTHOR>
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {BillingSdk, OrderItem, UPDATE_STATUS} from '@baiducloud/bce-billing-sdk';
import {OrderConfirm, ShoppingCart, TotalPrice, Protocol} from '@baiducloud/bce-billing-sdk-san';
import Client from '@baiducloud/httpclient';
import {Notification} from '@baidu/sui';

import {PayType} from '@/pages/sanPages/common/enum';
import {showMoney, contextPipe} from '@/pages/sanPages/utils/helper';
import {Ipv6Status, Ipv6SubProductType, Ipv6Payment} from '@/pages/sanPages/common/enum';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import './style.less';

const {asPage, invokeSUIBIZ, invokeSUI, template, service} = decorators;
/* eslint-disable */
const tpl = html`<template>
    <s-app-create-page
        class="vpc-ipv6gw-upgrade"
        pageTitle="{{pageNav.title}}"
        backToLabel="{{pageNav.backLabel}}"
        backTo="{{pageNav.backUrl}}"
        style="display: {{confirming ? 'none' : 'block'}}"
    >
        <s-form formData="{=formData=}" label-align="left">
            <div class="body-part-content form-part-wrap" style="background:#F5F5F5">
                <h4>当前配置</h4>
                <ul class="content-item-box">
                    <li class="content-item">
                        <label class="cell-title">网关状态：</label>
                        <span class="cell-content">{{formData.statusTxt}}</span>
                    </li>
                    <li class="content-item">
                        <label class="cell-title">计费模式：</label>
                        <span class="cell-content">{{formData.payment}}</span>
                    </li>
                    <li class="content-item">
                        <label class="cell-title">地域：</label>
                        <span class="cell-content">{{formData.region}}</span>
                    </li>
                    <li class="content-item">
                        <label class="cell-title">带宽：</label>
                        <span class="cell-content">{{formData.bandWidth}}Mbps</span>
                    </li>
                </ul>
            </div>
            <div class="body-part-content form-part-wrap">
                <h4>变更配置</h4>
                <s-form-item label="公网带宽：" prop="bandwidthInMbps">
                    <s-slider
                        value="{=formData.bandwidthInMbps=}"
                        length="{{450}}"
                        min="{=network.min=}"
                        max="{=network.max=}"
                        parts="{{2}}"
                        marks="{{marks}}"
                        track-id="ti_vpc_ipv6gw_upgrade_bandwidth"
                        track-name="公网带宽"
                    />
                    <div class="dragger-input">
                        <s-input-number
                            value="{=formData.bandwidthInMbps=}"
                            min="{=network.min=}"
                            max="{=network.max=}"
                            on-input="inputchange"
                        />
                        Mbps
                    </div>
                </s-form-item>
            </div>
        </s-form>
        <div slot="pageFooter" class="buybucket">
            <div class="buybucket-container" s-if="{{postpayBandwidth}}">
                <s-tooltip placement="top" trigger="{{buybucket.disabledConfirm ? 'hover' : ''}}">
                    <!--bca-disable-next-line-->
                    <span slot="content" s-html="{{'请先变更配置'}}"></span>
                    <s-button skin="primary" size="large" on-click="onUpdate" disabled="{{buybucket.disabledConfirm}}"
                        >{{'确认'}}</s-button
                    >
                </s-tooltip>
                <s-button size="large" on-click="cancel">取消</s-button>
            </div>
            <div class="buybucket-container" s-else>
                <s-tooltip placement="top" trigger="{{buybucket.disabledConfirm ? 'hover' : ''}}">
                    <!--bca-disable-next-line-->
                    <span slot="content" s-html="{{'请先变更配置'}}"></span>
                    <s-button
                        skin="primary"
                        size="large"
                        class="no-mg-bt"
                        on-click="onConfirm"
                        disabled="{{buybucket.disabledConfirm || priceLoading}}"
                        >{{'确认订单'}}</s-button
                    >
                </s-tooltip>
                <s-button size="large" on-click="cancel">取消</s-button>
                <shopping-cart
                    sdk="{{newBillingSdk}}"
                    on-reset="onReset"
                    addItemToCartAvailable="{{addItemToCartAvailable}}"
                    addItemToCartDisable="{=priceLoading=}"
                    on-change="onShoppingCartChange"
                    theme="default"
                ></shopping-cart>
            </div>
        </div>
    </s-app-create-page>
    <s-app-create-page
        class="vpc-ipv6gw-upgrade"
        pageTitle="{{confirmPageNav.title}}"
        backToLabel="{{confirmPageNav.backLabel}}"
        backTo="{{confirmPageNav.backUrl}}"
        content-in-center
        s-if="confirming"
    >
        <order-confirm
            s-ref="orderConfirm"
            sdk="{{newBillingSdk}}"
            use-coupon="{{false}}"
            theme="default"
            showAgreementCheckbox
        />
        <div slot="pageFooter" class="buybucket">
            <div class="buybucket-container">
                <div class="buybucket-container-protocol">
                    <billing-protocol s-if="!FLAG.NetworkSupportXS" s-ref="billingProtocol" />
                    <s-button size="large" class="no-mg-bt" on-click="onBack">{{'上一步'}}</s-button>
                    <s-button size="large" on-click="cancel">取消</s-button>
                    <s-button skin="primary" size="large" on-click="onPay"> {{'提交订单'}} </s-button>
                </div>
                <total-price sdk="{{newBillingSdk}}" />
            </div>
        </div>
    </s-app-create-page>
</template>`;
/* eslint-enable */

@template(tpl)
@invokeSUIBIZ
@invokeSUI
class Ipv6gwUpgrade extends Component {
    static components = {
        'order-confirm': OrderConfirm,
        'shopping-cart': ShoppingCart,
        'total-price': TotalPrice,
        'billing-protocol': Protocol
    };
    static computed = {
        marks() {
            let marks = {};
            let min = this.data.get('network.min');
            let max = this.data.get('network.max');
            let middle = u.round(max / 2);
            marks[min] = min + 'Mbps';
            marks[max] = max + 'Mbps';
            marks[middle] = middle + 'Mbps';
            return marks;
        }
    };
    inited() {
        const client = new Client({}, {}.$context);
        // 创建实例
        const sdk = new BillingSdk({
            client,
            AllRegion: window.$context.getEnum('AllRegion'),
            context: contextPipe(this)
        });
        this.data.set('newBillingSdk', sdk);
    }
    attached() {
        this.init();
        this.watch('formData.bandwidthInMbps', bandwidthInMbps => {
            let network = this.data.get('network');
            if (
                this.data.get('bandwidthInMbps') &&
                this.data.get('bandwidthInMbps') !== bandwidthInMbps &&
                bandwidthInMbps >= network.min &&
                bandwidthInMbps <= network.max
            ) {
                !this.data.get('postpayBandwidth') && this.loadPrice();
                this.data.get('postpayBandwidth') && this.data.set('buybucket.disabledConfirm', false);
            } else {
                this.data.set('buybucket.disabledConfirm', true);
            }
        });
    }

    initData() {
        return {
            FLAG,
            stepIndex: 0,
            pageNav: {
                title: '变更配置',
                backLabel: '返回',
                backUrl: '/network/#/vpc/ipv6gw/list'
            },
            confirmPageNav: {
                title: '确认订单',
                backLabel: '返回',
                backUrl: '/network/#/vpc/ipv6gw/list'
            },
            steps: [{text: '升级配置'}, {text: '确认订单'}, {text: '支付成功'}],
            formData: {
                productType: PayType.POSTPAY,
                subProductType: ''
            },
            buybucket: {
                disabledConfirm: true
            },
            network: {
                min: 1,
                max: 2000
            },
            bucketItems: [],
            postpayBandwidth: false,
            priceLoading: true,
            confirmedPay: false,
            addItemToCartAvailable: false,
            urlQuery: getQueryParams()
        };
    }

    init() {
        const init = [
            this.$http.ipv6gwDetail({
                vpcId: this.data.get('urlQuery.vpcId')
            })
        ];
        if (FLAG.NetworkIpv6Opt) {
            init.push(this.$http.getIpv6BandwidthQuota());
        }
        Promise.all(init).then(result => {
            this.loadIpv6Detail(result[0]);
            result[1] && this.setIpv6BandwidthQuota(result[1]);
            if (
                result[0].subProductType === Ipv6SubProductType.NETRAFFIC &&
                result[0].productType === PayType.POSTPAY
            ) {
                this.data.set('postpayBandwidth', true);
            }
        });
    }

    cancel() {
        location.hash = '#/vpc/ipv6gw/list';
    }

    loadIpv6Detail(data) {
        let formData = this.data.get('formData');
        this.data.set(
            'formData',
            u.extend(
                {
                    payment: Ipv6Payment.getTextFromValue(data.subProductType),
                    statusTxt: Ipv6Status.getTextFromValue(data.status),
                    region: window.$context.getCurrentRegion().label,
                    bandWidth: data.bandwidthInMbps
                },
                formData,
                data
            )
        );
        this.data.set('ipv6Detail', data);
        this.data.set('bandwidthInMbps', data.bandwidthInMbps);
    }

    setIpv6BandwidthQuota(data) {
        const subProductType = this.data.get('formData.subProductType');
        const TO_BAND_TYPE = {
            netraffic: 'ipv6TrafficQuota',
            bandwidth: 'ipv6BandwidthQuota'
        };
        const quota = data.quotaType2quota || {};
        this.data.set('network.max', quota[TO_BAND_TYPE[subProductType]] || 2000);
    }

    getPriceConfigs() {
        let formData = this.data.get('formData');
        let configs = {
            serviceType: 'IPVSIXGW',
            region: window.$context.getCurrentRegionId(),
            productType: 'postpay',
            count: 1,
            type: 'RESIZE',
            subServiceType: 'default'
        };
        if (formData.subProductType === Ipv6SubProductType.BANDWIDTH) {
            configs.flavor = [
                {
                    name: 'subServiceType',
                    value: 'default',
                    scale: 1
                },
                {
                    name: 'bandwidth',
                    value: formData.bandwidthInMbps + 'M',
                    scale: 1
                }
            ];
            configs.chargeItem = 'RunningTimeMinutes';
            configs.timeUnit = 'MINUTE';
        } else {
            configs.flavor = [
                {
                    name: 'subServiceType',
                    value: 'default',
                    scale: 1
                }
            ];
            configs.chargeItem = 'WebOutBytes';
            configs.amount = 1024 * 1024 * 1024;
            configs.unitText = 'GB';
        }
        // 新增shoppingCart详情展示页面
        let configuration = this.getConfigDetail();
        configs.configDetail = configuration;
        return configs;
    }

    loadPrice() {
        const {newBillingSdk} = this.data.get();
        newBillingSdk.clearItems();
        const configs = this.getPriceConfigs();
        const orderItem = new OrderItem(configs);
        this.data.set('bucketItems', [orderItem]);
        newBillingSdk.addItems([orderItem]);
        this.data.set('buybucket.disabledConfirm', false);
    }
    onConfirm() {
        let configuration = [
            {
                label: '地域',
                value: window.$context.getCurrentRegion().label
            },
            {
                label: '计费方式',
                value: Ipv6Payment.getTextFromValue(this.data.get('formData.subProductType'))
            },
            {
                label: '公网带宽峰值',
                value: this.data.get('formData.bandwidthInMbps') + 'Mbps'
            }
        ];
        const orderItem = this.data.get('bucketItems');
        orderItem[0].updateConfigDetail(configuration);
        this.data.set('confirming', true);
    }

    onBack() {
        this.data.set('confirming', false);
        this.setConfigDetail();
    }

    async onPay() {
        !FLAG.NetworkSupportXS && (await this.ref('billingProtocol').validateAgreement());
        let {newBillingSdk, bucketItems} = this.data.get();
        let params = {
            items: [
                {
                    config: {
                        bandwidthInMbps: this.data.get('formData.bandwidthInMbps'),
                        gatewayId: this.data.get('urlQuery.gatewayId')
                    },
                    paymentMethod: bucketItems[0].couponId ? [{type: 'coupon', values: [bucketItems[0].couponId]}] : []
                }
            ]
        };
        try {
            let confirmUrl = '/api/ipv6gw/order/confirm/resize?orderType=RESIZE';
            const data = await this.$http.newConfirmOrder(confirmUrl, params);
            let url = '';
            try {
                const info = await newBillingSdk.checkPayInfo(data);
                url = info.url + '&fromService=IPVSIXGW';
                info.url && (location.href = url);
            } catch (info) {
                // 跳转到相应页面
                url = info.url + '&fromService=IPVSIXGW';
                info.url && (location.href = url);
            }
        } catch (err) {}
    }

    onReset() {
        this.data.set('formData.bandwidthInMbps', this.data.get('ipv6Detail').bandwidthInMbps);
    }
    onRegionChange() {
        location.hash = '#/vpc/ipv6gw/list';
    }

    // 按流量后付费带宽升级
    onUpdate() {
        this.$http
            .ipv6gwUpdate({
                gatewayId: this.data.get('urlQuery.gatewayId'),
                bandwidthInMbps: this.data.get('formData.bandwidthInMbps')
            })
            .then(() => {
                location.hash = '#/vpc/ipv6gw/list';
                Notification.success('带宽升级成功');
            })
            .catch(() => {
                location.hash = '#/vpc/ipv6gw/list';
                Notification.error('带宽升级失败');
            });
    }
    // 新增onShoppingCartChange
    onShoppingCartChange(e) {
        this.data.set('priceLoading', UPDATE_STATUS[e] !== UPDATE_STATUS.DONE);
        if (UPDATE_STATUS[e] === UPDATE_STATUS.DONE) {
            const bucketItems = this.data.get('bucketItems');
            const {newBillingSdk} = this.data.get();
            let unitPrice = bucketItems[0]?.unitPrice;
            let priceSubText = bucketItems[0]?.priceSubText;
            // 用来判断是否需要二次询价
            if (
                this.data.get('formData.subProductType') === Ipv6SubProductType.BANDWIDTH &&
                unitPrice &&
                !priceSubText
            ) {
                newBillingSdk.clearItems();
                let extra =
                    '预计' + `${showMoney(+unitPrice * 60 * 24)}/天，` + `${showMoney(+unitPrice * 60 * 24 * 30)}/月`;
                bucketItems[0].priceSubText = extra;
                this.data.set('bucketItems', bucketItems);
                newBillingSdk.addItems(bucketItems);
            }
        }
    }
    getConfigDetail() {
        let configuration = [
            {
                label: '购买配置',
                value: `带宽${this.data.get('formData.bandwidthInMbps')}Mbps`
            }
        ];
        return configuration;
    }
    setConfigDetail() {
        let configuration = this.getConfigDetail();
        const orderItem = this.data.get('bucketItems');
        if (!orderItem[0]) {
            return;
        }
        orderItem[0].updateConfigDetail(configuration);
    }
}
export default San2React(Processor.autowireUnCheckCmpt(Ipv6gwUpgrade));
