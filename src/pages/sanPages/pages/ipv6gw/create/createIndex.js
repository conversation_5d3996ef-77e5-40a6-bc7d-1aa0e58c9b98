import _ from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import './create';

const {template, invokeSUI, invokeAppComp, invokeComp} = decorators;

const tpl = html`
    <template>
        <div s-if="!indexLoading" style="width:100%" class="index-loading-class">
            <s-loading loading style="width:100%">
                <ipv6gw-create requestNeed="{{requestNeed}}" vpcId="{{vpcId}}" class="ipv6gw-create-component" />
            </s-loading>
        </div>
        <ipv6gw-create s-if="indexLoading" s-ref="vpcIpv6gw" vpcId="{{vpcId}}" class="ipv6gw-create-component" />
    </template>
`;

@invokeComp('@ipv6gw-create')
@template(tpl)
@invokeSUI
@invokeAppComp
class CreateIndex extends Component {
    initData() {
        return {
            indexLoading: true,
            requestNeed: true,
            vpcId: '',
            urlQuery: getQueryParams()
        };
    }

    inited() {
        this.data.set('vpcId', this.data.get('urlQuery.vpcId'));
        window.$framework.events.on(window.$framework.EVENTS.AFTER_REGION_CHANGED, () => {
            let vpcIpv6gw = this.ref('vpcIpv6gw')?.data;
            const currentRegion = window.$context.getCurrentRegionId();
            if (currentRegion === vpcIpv6gw?.get('formData')?.region && !vpcIpv6gw?.get('loadNeed')) {
                return;
            }
            this.data.set('vpcId', '');
            this.data.set('indexLoading', false);
            this.nextTick(() => {
                // 等一会再执行
                setTimeout(() => {
                    this.data.set('indexLoading', true);
                }, 100);
            });
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(CreateIndex));
