/**
 * @file src/vpc/ipv6gw/create/Create.js
 * <AUTHOR>
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {BillingSdk, OrderItem, UPDATE_STATUS} from '@baiducloud/bce-billing-sdk';
import {ShoppingCart, OrderConfirm, TotalPrice, Protocol} from '@baiducloud/bce-billing-sdk-san';
import Client from '@baiducloud/httpclient';
import {OutlinedQuestion} from '@baidu/sui-icon';

import {contextPipe, showMoney, getMarksByStep, getVPCSupportRegion} from '@/pages/sanPages/utils/helper';
import {Ipv6SubProductType, PayType} from '@/pages/sanPages/common';
import {TagEditPanel} from '@baidu/bce-tag-sdk-san';
import {ResourceGroupPanel} from '@baidu/bce-resourcegroup-sdk-san';
import {ResourceGroupSDK} from '@baidu/bce-resourcegroup-sdk';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import './style.less';
import Assist from '@/utils/assist';

const formValidator = {
    name: [
        {
            pattern: /^[a-zA-Z][a-zA-Z0-9._\/-]{0,64}$/,
            message: '不符合规范，请重新命名'
        }
    ],
    vpcId: [{required: true, message: '请选择所在网络'}],
    subProductType: [{required: true, message: '请选择公网带宽'}],
    bandwidthInMbps: [{required: true, message: '请选择带宽峰值'}],
    region: [{required: true}]
};
const pageTitle = ['创建IPv6网关', '确认订单'];
const {invokeSUIBIZ, invokeSUI, template, service, asComponent} = decorators;
/* eslint-disable */
const tpl = html` <template>
    <s-app-create-page
        class="{{klass}}"
        backTo="{{pageNav.backUrl}}"
        backToLabel="{{pageNav.backLabel}}"
        pageTitle="{{pageNav.title}}"
    >
        <div class="s-step-block">
            <s-steps current="{{stepIndex + 1}}">
                <s-steps-step s-for="i in steps" title="{{i.title}}" />
            </s-steps>
        </div>
        <div class="form-widget" s-if="stepIndex === 0">
            <s-form s-ref="form" rules="{{rules}}" data="{=formData=}" label-align="left">
                <div class="body-part-content form-part-wrap">
                    <h4>付费及地域</h4>
                    <s-form-item label="付费方式：" prop="productType" class="config-paytype">
                        <span>{{payTypeText}}</span>
                    </s-form-item>
                    <s-form-item label="地域：" prop="region" class="s-form-item-region">
                        <template slot="label" class="label_class">
                            {{'当前地域：'}}
                            <s-tip placement="top" class="inline-tip">
                                <s-question class="question-class warning-class"></s-question>
                                <span slot="content">
                                    如需修改购买其他区域产品，请{{isXSTip}}。
                                    <a
                                        class="assist-tip"
                                        href="javascript:void(0)"
                                        on-click="showAssist('region')"
                                        s-if="FLAG.NetworkSupportAI"
                                        >了解详情</a
                                    >
                                </span>
                            </s-tip>
                        </template>
                        <s-radio-radio-group
                            enhanced
                            radioType="button"
                            value="{=formData.region=}"
                            datasource="{{regionList}}"
                            on-change="onRegionChange"
                            >>
                        </s-radio-radio-group>
                    </s-form-item>
                </div>
                <div class="body-part-content form-part-wrap">
                    <h4>配置信息</h4>
                    <s-form-item label="网关名称：" prop="name">
                        <s-input
                            class="input-with-num"
                            on-input="handleNameInput"
                            value="{=formData.name=}"
                            width="{{320}}"
                            placeholder="请输入IPV6网关名称"
                            track-id="ti_vpc_ipv6gw_create"
                            track-name="名称"
                        />
                        <span class="input-num-statistics">{{nameLength+'/'+'65'}}</span>
                        <p class="name-tip">以字母开头，支持大小写字母、数字以及 -_/. 特殊字符</p>
                    </s-form-item>
                    <s-form-item label="所在网络：" prop="vpcId" class="required-label">
                        <s-select
                            width="{{320}}"
                            value="{=formData.vpcId=}"
                            track-id="ti_vpc_ipv6gw_create"
                            track-name="所在网络"
                        >
                            <s-select-option
                                s-for="item in vpcList"
                                value="{{item.value}}"
                                label="{{item.text}}"
                                disabled="{{item.disabled}}"
                            >
                                <s-tooltip>
                                    <div slot="content">
                                        <!--bca-disable-next-line-->
                                        {{item.tip | raw}}
                                    </div>
                                    <div>{{item.text}}</div>
                                </s-tooltip>
                            </s-select-option>
                        </s-select>
                    </s-form-item>
                    <div class="tip-wrap" s-if="getTip !== ''">{{getTip}}</div>
                    <s-form-item label="公网带宽：" prop="subProductType" class="required-label center_class">
                        <s-radio-radio-group
                            enhanced
                            value="{=formData.subProductType=}"
                            datasource="{{subProductTypeList}}"
                            radioType="button"
                        >
                        </s-radio-radio-group>
                        <p class="assist-tip-form">
                            请合理选择按使用流量或按使用带宽计费方式。
                            <a
                                class="assist-tip"
                                href="javascript:void(0)"
                                on-click="showAssist()"
                                s-if="FLAG.NetworkSupportAI"
                                >了解详情</a
                            >
                        </p>
                    </s-form-item>
                    <s-form-item label="带宽峰值：" prop="bandwidthInMbps" class="required-label">
                        <s-slider
                            value="{=formData.bandwidthInMbps=}"
                            length="{{450}}"
                            parts="{{8}}"
                            marks="{{marks}}"
                            min="{=network.min=}"
                            max="{=ipv6Network.max=}"
                            disabled="{=network.disabled=}"
                            track-id="ti_vpc_ipv6gw_create"
                            track-name="公网带宽"
                        />
                        <div class="dragger-input">
                            <s-input-number
                                disabled="{=network.disabled=}"
                                value="{=formData.bandwidthInMbps=}"
                                min="{=network.min=}"
                                max="{=ipv6Network.max=}"
                                on-input="inputchange"
                            />
                            Mbps
                        </div>
                    </s-form-item>
                </div>
                <div class="resource-part-content form-part-wrap">
                    <h4>标签</h4>
                    <s-form-item prop="tag" label="绑定标签：">
                        <tag-edit-panel
                            create
                            version="v2"
                            instances="{{defaultInstances}}"
                            options="{{tagListRequster}}"
                            s-ref="tagPanel"
                        />
                    </s-form-item>
                </div>
                <div class="resource-part-content form-part-wrap">
                    <resource-group-panel
                        refreshAvailable="{{true}}"
                        sdk="{{resourceSDK}}"
                        on-change="resourceChange($event)"
                    />
                </div>
                <div class="body-part-content form-part-wrap">
                    <h4>购买信息</h4>
                    <s-form-item label="购买个数：" class="required-label buy-number-wrap">
                        <template slot="label" class="label_class">
                            {{'购买个数：'}}
                            <s-tip class="inline-tip" content="每个VPC仅可创建一个IPv6网关">
                                <s-question class="question-class warning-class"></s-question>
                            </s-tip>
                        </template>
                        <span class="inline">1个</span>
                    </s-form-item>
                    <s-form-item label="释放保护：" class="required-label">
                        <s-radio-radio-group
                            enhanced
                            value="{=deleteProtect=}"
                            datasource="{{deleteProtectList}}"
                            radioType="button"
                        >
                        </s-radio-radio-group>
                    </s-form-item>
                </div>
            </s-form>
        </div>
        <order-confirm
            s-ref="orderConfirm"
            sdk="{{newBillingSdk}}"
            use-coupon="{{false}}"
            theme="default"
            s-if="stepIndex === 1"
            showAgreementCheckbox
        />
        <div slot="pageFooter" class="buybucket">
            <div class="buybucket-widget" s-if="stepIndex === 0">
                <div class="buybucket-container">
                    <s-tooltip placement="top" trigger="{{buybucket.disabled && buybucket.tip ? 'hover' : ''}}">
                        <!--bca-disable-next-line-->
                        <span slot="content" s-html="{{buybucket.tip}}"></span>
                        <s-button
                            skin="primary"
                            size="large"
                            class="no-mg-bt"
                            on-click="onConfirm"
                            disabled="{{buybucket.disabled}}"
                            track-id="vpc_IPv6_confirm_order"
                            >{{'确认订单'}}</s-button
                        >
                    </s-tooltip>
                    <s-button size="large" on-click="cancel">取消</s-button>
                    <shopping-cart
                        sdk="{{newBillingSdk}}"
                        on-reset="onReset"
                        addItemToCartAvailable="{{addItemToCartAvailable}}"
                        addItemToCartDisable="{=priceLoading=}"
                        on-change="onShoppingCartChange"
                        theme="default"
                    ></shopping-cart>
                </div>
            </div>
            <div class="buybucket-widget" s-if="stepIndex === 1">
                <div class="buybucket-container">
                    <div class="buybucket-container-protocol">
                        <billing-protocol s-if="!FLAG.NetworkSupportXS" s-ref="billingProtocol" />
                        <s-button size="large" class="no-mg-bt" on-click="onBack">{{'上一步'}}</s-button>
                        <s-button size="large" on-click="cancel">取消</s-button>
                        <s-button
                            skin="primary"
                            disabled="{{confirmedPay}}"
                            size="large"
                            on-click="onPay"
                            track-id="vpc_IPv6_submit_order"
                        >
                            {{'提交订单'}}
                        </s-button>
                    </div>
                    <total-price sdk="{{newBillingSdk}}" />
                </div>
            </div>
        </div>
    </s-app-create-page>
</template>`;
/* eslint-enable */

@asComponent('@ipv6gw-create')
@template(tpl)
@invokeSUI
@invokeSUIBIZ
class Ipv6gwCreate extends Component {
    static components = {
        'order-confirm': OrderConfirm,
        'resource-group-panel': ResourceGroupPanel,
        'shopping-cart': ShoppingCart,
        'total-price': TotalPrice,
        'billing-protocol': Protocol,
        's-question': OutlinedQuestion,
        'tag-edit-panel': TagEditPanel
    };

    static messages = {
        projectChange({value}) {
            value && this.data.set('resourceGroupId', value);
            let allVpc = this.data.get('allVpc');
            let vpcs = this.filterVpcList(allVpc, value);
            this.data.set('vpcList', vpcs);
            this.setVpcId(vpcs);
        }
    };

    static computed = {
        'buybucket.disabled'() {
            let bandwidthInMbps = this.data.get('formData.bandwidthInMbps');
            let formErrors = this.data.get('formErrors');
            let status = this.data.get('purchaseValidation.status');
            let vpcId = this.data.get('formData.vpcId');
            return formErrors || !status || this.data.get('priceLoading') || bandwidthInMbps < 1 || !vpcId;
        },
        'getTip'() {
            let vpcList = this.data.get('vpcList');
            let supportNetOrg = FLAG.NetworkIpv6SupportOrganization;
            if ((!vpcList || vpcList.length === 0) && supportNetOrg) {
                return '所选项目下没有可用的私有网络VPC实例，请先创建所选项目的VPC实例或变更已有VPC实例的项目属性';
            }
            return '';
        },
        'marks'() {
            let min = Number(this.data.get('network.min'));
            let max = Number(this.data.get('ipv6Network.max'));
            return getMarksByStep(max, min, 4);
        },
        'payTypeText'() {
            // PayType.getTextFromValue(PayType.POSTPAY)
            return '按量付费';
        },
        'isXSTip'() {
            return !FLAG.NetworkSupportXS ? '前往主导航进行切换' : '在顶栏重新选择区域';
        }
    }; // 只要校验失败，价格未取到，带宽设置错误，check验证失败按钮就需要隐藏，如此可以在下面不处理按钮隐藏的逻辑

    initData() {
        return {
            FLAG,
            stepIndex: 0,
            steps: [
                {title: '基本配置', key: 'SELECT_CONFIG'},
                {title: '确认订单', key: 'ORDER'}
            ],
            purchaseValidation: true,
            rules: formValidator,
            formData: {
                productType: PayType.POSTPAY,
                subProductType: '',
                region: window.$context.getCurrentRegion().rawId,
                name: '',
                bandwidthInMbps: 1,
                vpcId: ''
            },
            productType: [
                {
                    text: PayType.getTextFromValue(PayType.POSTPAY),
                    value: PayType.POSTPAY
                }
            ],
            regionList: [
                {
                    text: window.$context.getCurrentRegion().label,
                    value: window.$context.getCurrentRegion().rawId
                }
            ],
            buybucket: {
                disabled: false,
                tip: ''
            },
            klass: 'vpc-ipv6gw-create',
            pageNav: {
                title: '创建IPv6网关',
                backLabel: '返回',
                backUrl: '/network/#/vpc/ipv6gw/list'
            },
            confirmPageNav: {
                title: '确认订单',
                backLabel: '返回',
                backUrl: '/network/#/vpc/ipv6gw/list'
            },
            formErrors: null,
            price: null,
            network: {
                min: 1,
                max: {
                    netraffic: 200,
                    bandwidth: 2000
                }
            },
            ipv6Network: {
                max: 200
            },
            resourceSDK: new ResourceGroupSDK(window.$http, window.$context),
            bucketItems: [],
            nameLength: 0,
            priceLoading: true,
            confirmedPay: false,
            addItemToCartAvailable: false,
            loadNeed: false,
            deleteProtect: false,
            deleteProtectList: [
                {text: '开启', value: true},
                {text: '关闭', value: false}
            ],
            defaultInstances: [
                {
                    tags: [
                        {
                            tagKey: '默认项目',
                            tagValue: ''
                        }
                    ]
                }
            ],
            tagListRequster: this.tagListRequster.bind(this)
        };
    }

    inited() {
        this.watch('formData.name', value => {
            let configuration = this.getConfigDetail();
            const orderItem = this.data.get('bucketItems');
            if (!orderItem[0]) {
                return;
            }
            orderItem[0].updateConfigDetail(configuration);
        });
        const client = new Client({}, {}.$context);
        // 创建实例
        const sdk = new BillingSdk({
            client,
            AllRegion: window.$context.getEnum('AllRegion'),
            context: contextPipe(this)
        });
        this.data.set('newBillingSdk', sdk);
        // region平铺
        this.data.set('regionList', getVPCSupportRegion(window));
        this.checkSupTypeList();
    }

    attached() {
        if (this.data.get('requestNeed')) {
            return;
        }
        this.loadPrice();
        this.checkValidation();
        this.loadVpc();
        this.getIpv6BandwidthQuota();
        this.watch(
            'formData.bandwidthInMbps',
            u.debounce(bandwidthInMbps => {
                let network = this.data.get('network');
                const subProductType = this.data.get('formData.subProductType');
                if (
                    bandwidthInMbps &&
                    bandwidthInMbps >= network.min &&
                    bandwidthInMbps <= network.max[subProductType]
                ) {
                    this.loadPrice(); // 改变带宽后继续计算价格
                }
            }, 300)
        );
        this.watch('formData.subProductType', item => {
            this.loadPrice();
            this.resetBandwidthInMbps(item);
        });
        this.watch('purchaseValidation', data => {
            this.data.set(
                'buybucket.tip',
                data.status
                    ? ''
                    : data.failReason
                      ? data.failReason +
                        '，请及时' +
                        '<a href="/finance/#/finance/account/recharge" target="_blank">' +
                        '充值</a>'
                      : ''
            );
        });
    }

    cancel() {
        location.hash = '#/vpc/ipv6gw/list';
    }

    checkSupTypeList() {
        const supList = [
            {
                text: '按使用流量计费',
                value: Ipv6SubProductType.NETRAFFIC
            },
            {
                text: '按使用带宽计费',
                value: Ipv6SubProductType.BANDWIDTH
            }
        ];
        if (!FLAG.NetworkIpv6Opt) {
            supList.shift();
        }
        this.data.set('subProductTypeList', supList);
        this.data.set('formData.subProductType', supList[0].value);
    }

    getInitFormData() {
        let vpcId = '';
        const subList = this.data.get('subProductTypeList');
        if (this.data.get('vpcId')) {
            vpcId = this.data.get('vpcId');
        } else {
            let item = u.find(this.data.get('vpcList'), item => !item.disabled);
            item && (vpcId = item.value);
        }
        return {
            productType: PayType.POSTPAY,
            subProductType: subList[0].value,
            region: window.$context.getCurrentRegion().rawId,
            name: '',
            bandwidthInMbps: 1,
            vpcId
        };
    }

    loadVpc() {
        let request = FLAG.NetworkIpv6Opt ? this.loadVpcList.bind(this) : this.loadVpcListOld.bind(this);
        request().then(vpcs => {
            this.data.set('vpcList', vpcs);
            this.data.set('allVpc', vpcs);
            if (this.data.get('vpcId')) {
                this.data.set('formData.vpcId', this.data.get('vpcId'));
            } else {
                this.setVpcId(vpcs);
            }
        });
    }

    filterVpcList(vpcs, value) {
        // 组织项目需要先过滤当前组织项目下的列表
        if (FLAG.NetworkIpv6SupportOrganization && value) {
            return vpcs.filter(item => {
                let resourceGroupIds = item.resourceGroups.map(resourceGroup => resourceGroup.resourceGroupId);
                return resourceGroupIds.indexOf(value) > -1;
            });
        }
        return vpcs;
    }

    setVpcId(vpcs) {
        const item = u.find(vpcs, item => !item.disabled);
        const vpcId = (item && item.value) || '';
        this.data.set('formData.vpcId', vpcId);
    }

    loadVpcListOld() {
        return this.$http.vpcList().then(data => {
            let vpcs = u.map(data, item => {
                return {
                    ...item,
                    text: `${item.name}（${item.cidr}）`,
                    value: item.vpcId,
                    disabled: !item.ipv6Cidr,
                    tip: !item.ipv6Cidr
                        ? `未分配IPV6网段，<a href="#/vpc/instance/list?vpcName=${item.vpcName}" target="_blank">去分配</a>`
                        : ''
                };
            });
            return Promise.resolve(vpcs);
        });
    }

    loadVpcList() {
        return this.$http.ipv6gwVpcList().then(data => {
            let vpcs = [];
            if (data && data.ipv6GatewayVpcVoList && data.ipv6GatewayVpcVoList.length) {
                vpcs = u.map(data.ipv6GatewayVpcVoList, item => {
                    return {
                        text: `${item.vpcName}（${item.cidr}）`,
                        value: item.vpcId,
                        disabled: !item.ipv6Cidr || (item.ipv6Cidr && item.createdIpv6Gateway),
                        tip: !item.ipv6Cidr
                            ? `未分配IPV6网段，<a href="#/vpc/instance/list?vpcName=${item.vpcName}" target="_blank">去分配</a>`
                            : item.createdIpv6Gateway
                              ? '该网络已创建一个IPV6网关'
                              : ''
                    };
                });
            }
            return Promise.resolve(vpcs);
        });
    }

    checkValidation() {
        return this.$http
            .purchaseValidation({
                serviceType: 'IPVSIXGW',
                productType: this.data.get('formData.productType')
            })
            .then(data => {
                this.data.set('purchaseValidation', data);
            })
            .catch(error => {
                this.data.set('purchaseValidation', false);
                return Promise.reject(error);
            });
    }

    getPriceConfigs(formData) {
        let configs = {
            serviceType: 'IPVSIXGW',
            serviceName: 'IPv6网关',
            region: formData.region,
            productType: 'postpay',
            count: 1,
            scene: 'NEW',
            subServiceType: 'default'
        };
        if (formData.subProductType === Ipv6SubProductType.BANDWIDTH) {
            configs.flavor = [
                {
                    name: 'subServiceType',
                    value: 'default',
                    scale: 1
                },
                {
                    name: 'bandwidth',
                    value: formData.bandwidthInMbps + 'M',
                    scale: 1
                }
            ];
            configs.chargeItem = 'RunningTimeMinutes';
            configs.timeUnit = 'MINUTE';
        } else {
            configs.flavor = [
                {
                    name: 'subServiceType',
                    value: 'default',
                    scale: 1
                }
            ];
            configs.chargeItem = 'WebOutBytes';
            configs.unitText = 'GB';
            configs.amount = 1024 * 1024 * 1024;
        }
        // 新增shoppingCart详情展示页面
        let configuration = this.getConfigDetail();
        configs.configDetail = configuration;
        return configs;
    }

    loadPrice() {
        const {newBillingSdk, formData} = this.data.get();
        newBillingSdk.clearItems();
        const configs = this.getPriceConfigs(formData);
        const orderItem = new OrderItem(configs);
        this.data.set('bucketItems', [orderItem]);
        newBillingSdk.addItems([orderItem]);
    }
    onConfirm() {
        let form = this.ref('form');
        form.validateFields() // 防止空名称提交,这里不再次loadPrice的原因是付费方式等都是固定的，唯一影响价格的只有带宽
            .then(async () => {
                try {
                    await this.ref('tagPanel').validate(false);
                } catch (error) {
                    return;
                }
                let tagData = await this.ref('tagPanel').getTags();
                this.data.set('tagData', tagData || {});
                this.data.set('stepIndex', 1);
                this.data.set('pageNav.title', pageTitle[1]);
            });
    }

    onBack() {
        this.data.set('stepIndex', 0);
        this.data.set('pageNav.title', pageTitle[0]);
    }

    async onPay() {
        !FLAG.NetworkSupportXS && (await this.ref('billingProtocol').validateAgreement());
        this.data.set('confirmedPay', true);
        const extraConfig = {};
        let {newBillingSdk, bucketItems} = this.data.get();
        if (FLAG.NetworkIpv6SupportOrganization) {
            const projectValid = this.ref('projectConfig').validComponentData();
            if (!projectValid) {
                return;
            }
            extraConfig.resourceGroupIds = [this.data.get('resourceGroupId')];
        }
        let params = {
            items: [
                {
                    config: {
                        name: this.data.get('formData.name'),
                        bandwidthInMbps: this.data.get('formData.bandwidthInMbps'),
                        billing: {
                            billingMethod: this.data.get('formData.productType')
                        },
                        subProductType: this.data.get('formData.subProductType'),
                        serviceType: 'IPVSIXGW',
                        vpcId: this.data.get('formData.vpcId'),
                        resourceGroupId: this.data.get('formData.resourceGroupId'),
                        deleteProtect: this.data.get('deleteProtect'),
                        tags: this.data.get('tagData'),
                        ...extraConfig
                    },
                    paymentMethod: bucketItems[0].couponId ? [{type: 'coupon', values: [bucketItems[0].couponId]}] : []
                }
            ]
        };
        try {
            let confirmUrl = '/api/ipv6gw/order/confirm/new?orderType=NEW';
            const data = await this.$http.newConfirmOrder(confirmUrl, params);
            let url = '';
            try {
                const info = await newBillingSdk.checkPayInfo(data);
                url = info.url + '&fromService=IPVSIXGW';
                info.url && (location.href = url);
            } catch (info) {
                // 跳转到相应页面
                url = info.url + '&fromService=IPVSIXGW';
                info.url && (location.href = url);
            }
        } catch (err) {}
        this.data.set('confirmedPay', false);
    }

    onReset() {
        this.data.set('formData', this.getInitFormData());
    }

    getIpv6BandwidthQuota() {
        if (FLAG.NetworkIpv6Opt) {
            this.$http.getIpv6BandwidthQuota().then(data => {
                const quota = data.quotaType2quota || {};
                this.data.set('network.max', {
                    bandwidth: quota.ipv6BandwidthQuota || 2000,
                    netraffic: quota.ipv6TrafficQuota || 200
                });
                this.data.set('ipv6Network.max', quota.ipv6TrafficQuota || 200);
            });
        }
        this.data.set('network.max', {
            bandwidth: 2000,
            netraffic: 200
        });
        this.data.set('ipv6Network.max', 200);
    }

    resetBandwidthInMbps(type) {
        const max = this.data.get('network.max');
        this.data.set('ipv6Network.max', max[type]);
        const bandwidthInMbps = this.data.get('formData.bandwidthInMbps');
        if (bandwidthInMbps > max[type]) {
            this.data.set('formData.bandwidthInMbps', max[type]);
        }
    }

    resourceChange({value}) {
        this.data.set('formData.resourceGroupId', value.data.groupId);
    }
    inputchange(e) {
        let {subProductType} = this.data.get('formData');
        let val = +e.value;
        let bandWidthMin = this.data.get('network.min');
        let network = this.data.get('network.max')[subProductType];
        if (isNaN(val)) {
            return;
        }
        val = val < bandWidthMin ? bandWidthMin : val;
        val = val > network ? network : val;
        this.data.set('formData.bandwidthInMbps', val);
    }
    // 输入网关名称时统计字符时（近似统计）
    handleNameInput({value}) {
        if (value?.length <= 65) {
            this.data.set('nameLength', value.length);
        } else {
            this.data.set('nameLength', 65);
        }
    }
    // 新增onShoppingCartChange
    onShoppingCartChange(e) {
        this.data.set('priceLoading', UPDATE_STATUS[e] !== UPDATE_STATUS.DONE);
        if (UPDATE_STATUS[e] === UPDATE_STATUS.DONE) {
            const bucketItems = this.data.get('bucketItems');
            const {newBillingSdk, formData} = this.data.get();
            let unitPrice = bucketItems[0]?.unitPrice;
            let priceSubText = bucketItems[0]?.priceSubText;
            // 用来判断是否需要二次询价
            if (formData.subProductType === Ipv6SubProductType.BANDWIDTH && unitPrice && !priceSubText) {
                newBillingSdk.clearItems();
                let extra = `${'（预计¥'}${showMoney(unitPrice * 60 * 24, 2)}
                ${'/天'}
                ${'¥'}
                ${showMoney(unitPrice * 60 * 24 * 30, 2)}
                ${'/月'}
                ${'）'}`;
                bucketItems[0].priceSubText = extra;
                this.data.set('bucketItems', bucketItems);
                newBillingSdk.addItems(bucketItems);
            }
        }
    }

    getConfigDetail() {
        let configuration = [
            {
                label: '地域',
                value: window.$context.getCurrentRegion().label
            },
            {
                label: '计费方式',
                value: Ipv6SubProductType.getTextFromValue(this.data.get('formData.subProductType'))
            },
            {
                label: '公网带宽峰值',
                value: this.data.get('formData.bandwidthInMbps') + 'Mbps'
            }
        ];
        if (this.data.get('formData.name')) {
            configuration.push({
                label: '名称',
                value: this.data.get('formData.name')
            });
        }
        return configuration;
    }
    // 切换地域
    onRegionChange(e) {
        let value = e.value || e.id;
        if (!value || value === window.$context.getCurrentRegionId()) {
            return;
        }
        this.data.set('formData.region', value);
        window.$context.setRegion(value);
        this.data.set('loadNeed', true);
    }
    tagListRequster() {
        return this.$http.getSearchTagList({serviceType: [], region: ['global']});
    }
    showAssist(type) {
        Assist.sendMessageToAssist({
            sceneLabel: 'ipv6gw_create',
            message: type === 'region' ? '什么是地域？' : '按使用流量和按使用带宽计费方式的计费公式是什么？'
        });
    }
}
export default Processor.autowireUnCheckCmpt(Ipv6gwCreate);
