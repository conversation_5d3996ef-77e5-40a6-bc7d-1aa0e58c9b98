/**
 * @file eip/group/v2/create/style.less
 * <AUTHOR>
 */
.page-sub-title() {
    display: inline-block;
    color: #333;
    font-size: 16px;
    margin: 8px 0;
    border-left: solid 4px #2468f2;
    padding-left: 14px;
}
.ipv6gw-create-component {
    width: 100%;
    .vpc-ipv6gw-create {
        background-color: #f7f7f9;
        height: 100%;
        overflow: auto;
        width: 100%;
        .s-step-block {
            width: 316px !important;
        }
        .form-widget {
            margin-top: 16px;
            min-width: 1020px;
            .body-part-content {
                width: calc(~'100vw - 260px');
                padding: 24px;
                h4 {
                    .page-sub-title();
                    margin: 0;
                }
            }
            .form-part-wrap {
                border-radius: 6px;
                width: calc(~'100vw - 260px');
                background: #fff;
                &:first-child {
                    padding: 24px;
                }
                padding: 16px 24px 24px;
                min-width: 1020px;
                h4 {
                    display: inline-block;
                    border: none;
                    zoom: 1;
                    margin: 0;
                    padding: 0;
                    font-family: PingFangSC-Medium;
                    font-size: 16px;
                    color: #151b26;
                    line-height: 24px;
                    font-weight: 500;
                }
                .s-form-item-label {
                    width: 96px;
                    height: 30px;
                    .inline-tip {
                        position: absolute;
                        .s-tip {
                            position: relative;
                            top: 3px;
                        }
                        .warning-class {
                            position: relative;
                            left: -1px;
                            .s-icon {
                                path {
                                    fill: #83868c;
                                }
                            }
                        }
                        &:hover {
                            .warning-class {
                                .s-icon {
                                    path {
                                        fill: #2468f2;
                                    }
                                }
                            }
                        }
                    }
                }
                .label_class {
                    .inline-tip {
                        top: 3px;
                        position: relative;
                        .s-tip-warning {
                            justify-content: center;
                            .warning_class {
                                fill: #999;
                            }
                        }
                        .s-tip:hover .s-icon path {
                            fill: #2468f2 !important;
                        }
                    }
                    .s-radio-text {
                        width: 63px !important;
                    }
                }
                .center_class {
                    .s-row {
                        .s-form-item-control-wrapper {
                            line-height: 30px;
                        }
                    }
                }
                .vpc-nat-eip-opt {
                    margin-top: 0px;
                }
                .config-paytype {
                    line-height: 30px;
                }
                .s-radio-button-group {
                    .s-radio-button {
                        .s-radio-text {
                            width: 95px;
                        }
                    }
                    .s-radio-checked {
                        .s-radio-text {
                            border-color: #2468f2 !important;
                        }
                    }
                }
                .input-with-num {
                    input {
                        padding-right: 52px;
                    }
                }
                .input-num-statistics {
                    font-family: PingFangSC-Regular;
                    font-size: 12px;
                    color: #84868c;
                    text-align: right;
                    line-height: 20px;
                    font-weight: 400;
                    margin-left: -46px;
                }
            }
            .s-input {
                border-color: #e8e9eb !important;
                .s-input-area {
                    input {
                        box-sizing: border-box;
                    }
                }
            }
        }
        .name-tip {
            color: #84868c;
            margin-top: 10px;
        }
        .bui-viewstep {
            text-align: center;
            margin: 20px 0 15px 0;
        }
        .bui-biz-page-tip {
            text-align: left;
            width: 1020px;
            margin: 20px auto !important;
        }
        .bui-biz-page-body {
            padding-bottom: 100px;
        }
        .resource-group-panel {
            padding: 0;
            margin: 0;
            border: none;
            dt {
                margin-bottom: 24px;
            }
            h4 {
                padding: 0;
            }
            .footer {
                .tip {
                    font-family: PingFangSC-Regular;
                    font-size: 12px;
                    color: #84868c;
                    font-weight: 400;
                }
            }
            .resouce-group-select {
                label {
                    padding-left: 7px;
                }
                .wrapper {
                    .s-button {
                        border-color: #e8e9eb;
                    }
                }
                .s-button-skin-stringfy {
                    padding: 0 4px;
                }
                .footer {
                    line-height: 20px;
                    height: 20px;
                    margin-top: 8px;
                    .tip {
                        color: #84868c;
                        line-height: 20px;
                    }
                }
            }
        }

        .tip-wrap {
            margin-top: 10px;
            margin-left: 150px;
            color: #fd2828;
        }
        .org-select {
            margin-top: 20px;
            .project-name {
                display: inline-block;
                width: 150px;
            }
        }
        .buy-number-wrap {
            line-height: 30px;
        }
        .bui-form {
            .bui-form-item {
                margin: 20px 0 0 0;
            }
            .required-label {
                position: relative;
                &:before {
                    content: '*';
                    line-height: 30px;
                    position: absolute;
                    left: -10px;
                    color: #f00;
                }
            }
        }
        .skin-warning-tip {
            margin-left: 10px;
            color: #999 !important;
            &:hover {
                color: #fff !important;
                background-color: #f39000;
            }
        }
        .inline-item {
            line-height: 30px;
        }
        .bui-biz-page-content.bui-biz-page-center-content {
            .bui-biz-page-body {
                margin: 0 auto !important;
                border: none !important;
                box-sizing: content-box;
            }
        }
        .bui-toastlabel-warning {
            width: 100%;
            box-sizing: border-box;
        }
        .eip-group-create-confirm {
            .bui-biz-buybucket {
                .bui-biz-buybucket-content {
                    float: right;
                    margin-right: 0;
                }
            }
        }
        // 订单确认页样式统一设置 目前仅考虑1280 需适配其他宽度可媒体查询
        .order-confirm {
            margin-top: 16px;
            width: calc(~'100vw - 260px');
            min-width: 1020px;
        }
        .buybucket {
            width: 100%;
            left: 0;
            height: 80px !important;
            display: flex;
            justify-content: center;
            .buybucket-widget {
                width: calc(~'100vw - 240px') !important;
                margin: 0 auto;
                height: 100%;
                .buybucket-container {
                    width: auto;
                    float: left;
                    height: 80px !important;
                    transform: translateY(0) !important;
                    display: flex;
                    align-items: center;
                    .no-mg-bt {
                        margin-left: 0 !important;
                    }
                    .billing-sdk-total-price-wrapper {
                        margin-left: 16px;
                    }
                }
                .billing-sdk-protocol-wrapper .buy-agreement {
                    margin-bottom: 8px !important;
                }
            }
        }
        .billing-sdk-order-confirm-wrapper-default {
            width: calc(~'100vw - 260px');
            padding: 0;
            .billing-sdk-order-legend .item {
                label {
                    width: 100px;
                }
            }
        }
        .dragger-input {
            display: inline-block;
            position: relative;
            left: 5px;
            top: -10px;
        }
        .s-create-page-content {
            padding-bottom: 96px;
        }
    }
    .tag-edit-panel {
        .s-form-item {
            margin: 0 8px 12px 0 !important;
        }
        .s-form-item-control-wrapper {
            margin-left: 0px !important;
        }
    }
    .tag-v2-panel-container {
        width: 800px !important;
    }
}
.eip-group-create-buybucket {
    .bui-biz-buybucket-layer-content {
        .bui-biz-buybucket-layer-row {
            label {
                width: 80px;
            }
        }
    }
}

.locale-en {
    .vpc-ipv6gw-create .form-part-wrap .s-form-item-label {
        width: 186px;
    }
    .vpc-ipv6gw-create .resouce-group-select .resouce-group-select-main > label {
        width: 186px;
    }
    .vpc-ipv6gw-create .resouce-group-select .footer {
        margin-left: 186px;
    }
}
.bui-layer {
    .bui-tiplayer {
        border-radius: 4px;
        .bui-button-label {
            color: #2468f2;
        }
    }
}
