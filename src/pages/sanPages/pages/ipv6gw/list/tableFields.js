/*
 * @Description:
 * @Author: <EMAIL>
 * @Date: 2022-04-12 16:42:46
 */
import {Ipv6Status} from '@/pages/sanPages/common/enum';

export const columns = [
    {
        name: 'gatewayId',
        label: 'IPv6网关名称/ID',
        width: 220,
        fixed: 'left'
    },
    {
        name: 'status',
        label: '状态',
        width: 100,
        filter: {
            options: [
                {
                    text: '全部',
                    value: ''
                },
                ...Ipv6Status.toArray()
            ],
            value: ''
        }
    },
    {
        name: 'vpcId',
        label: '所在网络',
        width: 160,
    },
    {
        name: 'bandwidthInMbps',
        label: '带宽上限',
        width: 100,
    },
    {
        name: 'productType',
        label: '支付方式',
        width: 110,
    },
    {
        name: 'createTime',
        label: '创建时间',
        width: 150,
    },
    {
        name: 'tag',
        label: '标签',
        width: 120,
    },
    {
        name: 'resourceGroups',
        label: '资源分组',
        width: 90,
    },
    {
        name: 'operation',
        label: '操作',
        width: 160,
        fixed: 'right'
    }
];
