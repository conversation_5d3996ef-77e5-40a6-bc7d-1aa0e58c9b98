/**
 * @file src/vpc/ipv6gw/list/List.js
 * <AUTHOR>
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {TagEditDialog} from '@baidu/bce-tag-sdk-san';
import {Notification} from '@baidu/sui';
import {OutlinedPlus, OutlinedEditingSquare, OutlinedDownload} from '@baidu/sui-icon';

import {Ipv6Payment, Ipv6Status, DocService, Ipv6SubProductType} from '@/pages/sanPages/common';
import rules from '../rules';
import {columns} from './tableFields';
import Confirm from '@/pages/sanPages/components/confirm';
import {alterProductType, toTime, $flag as FLAG} from '@/pages/sanPages/utils/helper';
import Monitor from './monitor/monitor';
import {ResourceGroupDialog} from '@baidu/bce-resourcegroup-sdk-san';
import {ResourceGroupSDK} from '@baidu/bce-resourcegroup-sdk';
import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import DOCUMENT_ICON from '@/img/document_normal.svg?url';
import DOCUMENT_ICON_COLOR from '@/img/document_color.svg?url';
import './style.less';

const {invokeAppComp, invokeSUI, invokeSUIBIZ, template, invokeComp} = decorators;
const tpl = html`
    <template>
        <s-app-list-page class="{{klass}}">
            <div slot="pageTitle">
                <div class="vpc-ipv6-header">
                    <div class="header-left">
                        <span class="title">IPv6网关</span>
                        <s-select class="vpc-select" width="{{240}}" value="{=vpcId=}" on-change="vpcChange($event)">
                            <s-select-option s-for="item in vpcList" value="{{item.value}}" label="{{item.text}}">
                                <s-tooltip>
                                    <div slot="content">{{item.text}}</div>
                                    <div>{{item.text}}</div>
                                </s-tooltip>
                            </s-select-option>
                        </s-select>
                    </div>
                    <div class="header-right">
                        <a
                            s-ref="introduce"
                            href="javascript:void(0)"
                            class="help-file function-introduce"
                            on-click="handleShowCard"
                            on-mouseenter="handleMouseEnter('introduce')"
                            on-mouseleave="handleMouseLeave('introduce')"
                            s-if="{{!FLAG.NetworkSupportXS}}"
                        >
                            <img class="s-icon" src="{{introduceIcon}}" />功能简介
                        </a>
                        <a
                            href="{{DocService.ipv6_helpFile}}"
                            target="_blank"
                            class="help-file"
                            on-mouseenter="handleMouseEnter('document')"
                            on-mouseleave="handleMouseLeave('document')"
                            s-if="{{!FLAG.NetworkSupportXS}}"
                        >
                            <img class="s-icon" src="{{documentIcon}}" />帮助文档
                        </a>
                    </div>
                </div>
                <introduce-panel
                    isShow="{{show}}"
                    klass="{{'IPv6-vpn-wrapper'}}"
                    title="{{introduceTitle}}"
                    description="{{description}}"
                    introduceOptions="{{introduceOptions}}"
                    on-toggle="handleToggle($event)"
                ></introduce-panel>
            </div>
            <div slot="bulk">
                <s-tip-button
                    disabled="{{createIpv6.disable || iamPass.disable || accountState.disabled}}"
                    skin="primary"
                    isDisabledVisibile="{{true}}"
                    on-click="onCreate"
                    track-id="vpc_IPv6_create"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{createIpv6.message || iamPass.message || accountState.message | raw}}
                    </div>
                    <outlined-plus />
                    创建IPv6网关
                </s-tip-button>
                <s-tip-button
                    disabled="{{releaseInfo.disabled}}"
                    isDisabledVisibile="{{true}}"
                    class="left_class"
                    on-click="onIpv6gwRelease"
                >
                    <div slot="content">{{releaseInfo.message}}</div>
                    释放
                </s-tip-button>
                <div class="intro-warp" data-intro="这里是批量操作区">
                    <s-tooltip
                        content="请先选择实例对象"
                        trigger="{{operationDisabled ? 'hover' : ''}}"
                        placement="top"
                    >
                        <s-select
                            placeholder="批量操作"
                            class="{{!operationDisabled ? 'placeholder-style' : ''}}"
                            disabled="{{operationDisabled}}"
                            value="{=operation=}"
                            on-change="onOperationChange"
                        >
                            <s-select-option
                                class="operation-select"
                                s-for="item in OperationType"
                                value="{{item.value}}"
                                label="{{item.label}}"
                                disabled="{{item.disabled}}"
                            >
                                <s-tooltip placement="right" trigger="{{item.message ? 'hover' : ''}}" width="200">
                                    <div slot="content">
                                        <!--bca-disable-next-line-->
                                        {{item.message | raw}}
                                    </div>
                                    <div>{{item.label}}</div>
                                </s-tooltip>
                            </s-select-option>
                        </s-select>
                    </s-tooltip>
                </div>
            </div>
            <div slot="filter" class="inline_class">
                <!--<search-tag
              s-ref="search"
              serviceType="NAT"
              searchbox="{=searchbox=}"
              on-search="onSearch"
          ></search-tag>-->
                <s-button
                    on-click="onDownload"
                    class="s-icon-button"
                    track-id="ti_vpc_security_download"
                    track-name="下载"
                    ><outlined-download class="icon-class"
                /></s-button>
            </div>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                on-selected-change="tableSelected($event)"
                on-filter="onFilter"
                on-sort="onSort"
                selection="{=table.selection=}"
            >
                <div slot="empty">
                    <!--bca-disable-->
                    <s-empty
                        vertical
                        class="empty-wrapper {{iamPass.disable ? 'create-disable' : ''}}"
                        track-id="vpc_IPv6_create"
                        on-click="onCreate"
                    >
                        <p class="empty-desc common" slot="desc">{{"暂无IPv6网关。"}}</p>
                        <s-tooltip s-if="{{createIpv6.disable}}" slot="action">
                            <!--bca-disable-next-line-->
                            <div slot="content">{{createIpv6.message | raw}}</div>
                            <span class="common">立即创建</span>
                        </s-tooltip>
                        <s-button
                            s-else
                            class="empty-action common"
                            slot="action"
                            skin="stringfy"
                            slot="action"
                            on-click="onCreate"
                            track-id="vpc_IPv6_create"
                        >
                            {{"立即创建"}}
                        </s-button>
                    </s-empty>
                    <!--bca-disable-->
                </div>
                <div slot="c-vpcId">
                    <span class="truncated" title="{{row.vpcName}}">
                        <a href="#/vpc/instance/detail?vpcId={{row.vpcId}}" class="text-hidden">{{row.vpcName}}</a>
                    </span>
                    <br />
                    <span class="truncated" title="{{row.vpcShortId}}">{{row.vpcShortId}}</span>
                </div>
                <div slot="c-createTime">{{row.createTime | getTime}}</div>
                <div slot="c-status">
                    <span class="{{row.status | statusClass}}">{{row.status | statusText}}</span>
                </div>
                <div slot="c-gatewayId">
                    <span class="truncated">
                        <s-tooltip content="{{row.name}}">
                            <a href="#/vpc/ipv6gw/detail?vpcId={{row.vpcId}}">{{row.name}}</a>
                        </s-tooltip>
                    </span>
                    <s-popover
                        s-ref="popover-name-{{rowIndex}}"
                        placement="top"
                        trigger="click"
                        class="edit-popover-class"
                    >
                        <div class="edit-wrap" slot="content">
                            <s-input
                                value="{=edit.name.value=}"
                                width="320"
                                placeholder="请输入名称"
                                on-input="onEditInput($event, rowIndex, 'name')"
                            />
                            <div class="edit-tip">大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65</div>
                            <s-button
                                skin="primary"
                                s-ref="editBtn-name-{{rowIndex}}"
                                disabled="{{true}}"
                                on-click="editConfirm(row, rowIndex, 'name')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel(rowIndex, 'name')">取消</s-button>
                        </div>
                        <outlined-editing-square class="name-icon" on-click="onInstantEdit(row, rowIndex, 'name')" />
                    </s-popover>
                    <br />
                    <span class="truncated">{{row.gatewayId}}</span>
                    <s-clip-board class="name-icon" text="{{row.gatewayId}}" />
                </div>
                <div slot="c-tag">
                    <span s-if="!row.tags || row.tags.length < 1"> - </span>
                    <div s-else s-for="item,index in row.tags">
                        <span s-if="index <= 1"> {{item.tagKey + ':' + item.tagValue}} </span>
                        <div s-if="row.tags.length > 2 && index === 1">...</div>
                    </div>
                </div>
                <div slot="c-productType">
                    {{row | productType}}
                    <s-popover s-if="row.enableProduct" placement="top">
                        <div slot="content">{{row | tipContent}}</div>
                        <s-icon class="tip-icon-wrap" name="warning-mark" />
                    </s-popover>
                </div>
                <div slot="c-resourceGroups">
                    <p s-for="item in row.resourceGroups">{{item.name}}</p>
                </div>
                <div slot="c-operation" class="operations">
                    <span class="oprations">
                        <s-tip-button
                            disabled="{{row.deleteProtect}}"
                            isDisabledVisibile="{{true}}"
                            skin="stringfy"
                            on-click="onRelease(row)"
                        >
                            <div slot="content">
                                该实例开启了释放保护功能，请在实例详情页面中关闭释放保护后再点击释放
                            </div>
                            释放
                        </s-tip-button>
                        <s-button skin="stringfy" on-click="toAlarmDetail(row)">报警详情</s-button>
                        <br />
                        <s-button skin="stringfy" on-click="showMonitor(row)">监控</s-button>
                        <s-tip-button
                            disabled="{{row.canNotUpgrade}}"
                            isDisabledVisibile="{{true}}"
                            skin="stringfy"
                            on-click="onUpgrade(row)"
                        >
                            <div slot="content">该实例存在正在审核的订单。如需操作，请稍后重试</div>
                            带宽调整
                        </s-tip-button>
                        <s-button skin="stringfy" on-click="changeResourceGroup(row)">编辑资源分组</s-button>
                    </span>
                </div>
            </s-table>
            <s-pagination
                s-if="{{pager.total}}"
                slot="pager"
                layout="{{'total, pageSize, pager, go'}}"
                pageSize="{{pager.pageSize}}"
                total="{{pager.total}}"
                page="{{pager.page}}"
                on-pagerChange="onPagerChange"
                on-pagerSizeChange="onPagerSizeChange"
            />
            <resource-group-dialog
                s-if="{{showResource}}"
                sdk="{{resourceSDK}}"
                resource="{{resource}}"
                on-success="oncommit"
                on-cancel="onCancel"
            />
        </s-app-list-page>
    </template>
`;

@template(tpl)
@invokeComp('@introduce-panel', '@search-tag')
@invokeAppComp
@invokeSUI
@invokeSUIBIZ
class Ipv6gwList extends Component {
    static components = {
        'resource-group-dialog': ResourceGroupDialog,
        'outlined-plus': OutlinedPlus,
        'outlined-editing-square': OutlinedEditingSquare,
        'outlined-download': OutlinedDownload
    };
    static filters = {
        statusClass(value) {
            return Ipv6Status.fromValue(value).styleClass || '';
        },
        statusText(value) {
            return value ? Ipv6Status.getTextFromValue(value) : '-';
        },
        productType(item) {
            return '后付费' + `-${Ipv6SubProductType.getTextFromValue(item.subProductType)}`;
        },
        tipContent(item) {
            let type = '';
            if (item.subProductType === Ipv6SubProductType.BANDWIDTH) {
                type = '后付费-按带宽转后付费-按流量';
            } else {
                type = '后付费-按流量转后付费-按带宽';
            }
            let tipContent =
                `该实例已开通计费变更${type}，将会在下个整点生效，` +
                '请关注！如需进行升级等操作，请先取消计费变更，谢谢！';
            return tipContent;
        },
        getTime(value) {
            return value ? toTime(value) : '-';
        }
    };

    static computed = {
        operationDisabled() {
            const selectedItems = this.data.get('table.selectedItems');
            return selectedItems.length === 0;
        }
    };

    initData() {
        return {
            klass: 'ipv6-list-wrap',
            editTag: {},
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                columns,
                datasource: [],
                selectedItems: []
            },
            OperationType: [
                {label: '编辑标签', value: 'editTag'},
                {label: '计费变更', value: 'ALTER_PRODUCTTYPE'},
                {label: '取消计费变更', value: 'CANCEL_ALTER_PRODUCTTYPE'}
            ],
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            resourceSDK: new ResourceGroupSDK(window.$http, window.$context),
            iamPass: {},
            DocService,
            FLAG,
            show: true,
            introduceTitle: 'IPv6 网关',
            description:
                'IPv6网关是私有网络通过IPv6连接公网的总出口。您可以按需购买IPv6公网带宽，通过配置只出不进策略和IP限速，灵活配置IPv6互联网出向带宽和入向带宽。',
            introduceIcon: INTRODUCE_ICON_COLOR,
            documentIcon: DOCUMENT_ICON,
            introduceEle: null,
            releaseInfo: {},
            searchbox: {
                keyword: '',
                placeholder: '请输入网关ID进行搜索',
                keywordType: ['gatewayId'],
                keywordTypes: [
                    {value: 'gatewayId', text: '网关ID'},
                    {value: 'tag', text: '标签'},
                    {value: 'resGroupId', text: '资源分组'}
                ],
                multi: true
            },
            accountState: {
                disabled: false,
                message: ''
            }
        };
    }

    inited() {
        this.setOperationMessage();
        this.getIamQuery();
        this.loadVpcList();
        const accountState = window.$storage.get('accountState');
        this.data.set('accountState', accountState);
    }

    attached() {
        window.$storage.get('ipv6gwIntroShow') === false && this.data.set('show', false);
        this.loadPage();
        this.data.set('introduceEle', this.ref('introduce'));
    }

    loadVpcList() {
        this.$http.ipv6gwVpcList().then(data => {
            let vpcs = u.map(data.ipv6GatewayVpcVoList, item => ({
                text: `${item.vpcName}（${item.cidr}）`,
                value: item.vpcId
            }));
            vpcs.unshift({
                text: '所在网络：全部私有网络',
                value: ''
            });
            this.data.set('vpcList', vpcs);
            this.data.set('vpcInfoList', data.ipv6GatewayVpcVoList);
            this.data.set('vpcId', vpcs[0].value);
            this.data.set('vpcInfo', data.ipv6GatewayVpcVoList[0]);
            this.loadPage();
        });
    }

    vpcChange({value}) {
        this.data.set('vpcId', value);
        if (value) {
            this.data.get('vpcInfoList').forEach(item => {
                if (value === item.vpcId) {
                    this.data.set('vpcInfo', item);
                    this.checkIpv6Create(item);
                }
            });
        } else {
            this.data.set('createIpv6.disable', false);
        }
        this.loadPage();
    }

    checkIpv6Create(vpcInfo) {
        let {createIpv6} = checker.check(rules, '', '', {
            createdIpv6Gateway: vpcInfo.createdIpv6Gateway,
            ipv6Cidr: vpcInfo.ipv6Cidr
        });
        this.data.set('createIpv6', createIpv6);
    }

    getPayload() {
        const searchParam = this.ref('search') ? this.ref('search').getSearchCriteria() : {};
        const {pager, order, filters} = this.data.get('');
        let payload = {
            pageNo: pager.page,
            pageSize: pager.pageSize,
            keyword: this.data.get('vpcId'),
            keywordType: 'vpcId'
        };
        if (!payload.vpcId) {
            delete payload.vpcId;
        }
        return {...payload, ...order, ...filters, ...searchParam};
    }

    loadPage() {
        this.data.set('table.loading', true);
        let payload = this.getPayload();
        this.resetTable();
        this.resetMoreOperation();
        this.$http.ipv6gwList(payload).then(res => {
            let dataList = this.checkBindAble(res.result);
            this.data.set('table.datasource', dataList);
            this.data.set('pager.total', res.totalCount);
            this.data.set('table.loading', false);
        });
    }

    checkBindAble(data) {
        return u.map(data, item => {
            return {
                ...item,
                canNotUpgrade: !!item.orderStatus,
                enableProduct: this.checkEnableProduct(item)
            };
        });
    }

    checkEnableProduct(item) {
        return item.status === Ipv6Status.AVAILABLE && item.orderStatus === 'shift_charge';
    }

    tableSelected(e) {
        this.data.set('table.selectedItems', e.value.selectedItems);
        this.setOperationMessage();
    }

    setOperationMessage() {
        let selectedItem = this.data.get('table.selectedItems');
        let checkResult = checker.check(rules, selectedItem, '');
        let OperationType = u.cloneDeep(this.data.get('OperationType'));
        OperationType.forEach(item => {
            if (checkResult[item.value]) {
                item.disabled = checkResult[item.value].disable;
                item.message = checkResult[item.value].message;
            }
        });
        this.data.set('OperationType', OperationType);
        // 设置释放按钮
        if (!u.isEmpty(checkResult.ipv6gwRelease)) {
            let releaseInfo = {};
            releaseInfo.disabled = checkResult.ipv6gwRelease.disable;
            releaseInfo.message = checkResult.ipv6gwRelease.message;
            this.data.set('releaseInfo', releaseInfo);
        } else {
            this.data.set('releaseInfo', {
                disabled: false,
                message: ''
            });
        }
    }

    resetMoreOperation() {
        this.data.set('operation', '');
    }

    onOperationChange(e) {
        const methodMap = {
            editTag: this.editTag,
            ALTER_PRODUCTTYPE: this.alterProduct,
            CANCEL_ALTER_PRODUCTTYPE: this.cancelAlterProduct
        };
        let requester = methodMap[e.value].bind(this);
        requester();
    }

    editTag() {
        const instances = this.data.get('table.selectedItems');
        const actionOptions = {
            serviceType: 'IPVSIXGW',
            instances,
            options: () => {
                return this.$http
                    .getSearchTagList({
                        serviceType: ['IPVSIXGW'],
                        region: [window.$context.getCurrentRegionId()]
                    })
                    .then(result => {
                        return result;
                    });
            },
            parentAction: this,
            submitHandler: tags => {
                const param = {
                    insertTags: tags,
                    resources: u.map(instances, item => ({
                        id: item.gatewayId,
                        resourceId: item.gatewayId,
                        serviceType: 'IPVSIXGW',
                        tags: instances.length > 1 && item.tags ? tags.concat(item.tags) : tags
                    }))
                };
                return this.$http.vpcTagAssign(param).then(data => data);
            },
            helpDocUrl: DocService.vpcTag
        };
        const dialog = new TagEditDialog({
            data: actionOptions
        });
        dialog.attach(document.body);
        dialog.on('success', e => {
            this.loadPage();
            dialog.dispose && dialog.dispose();
        });
        dialog.on('cancel', e => {
            dialog.dispose && dialog.dispose();
            this.resetMoreOperation();
        });
        return dialog;
    }

    alterProduct() {
        // 目前计费变更不支持修改带宽，所以暂时不需要弹窗逻辑
        let datas = this.data.get('table.selectedItems');
        let ids = u.map(datas, item => item.gatewayId);
        let url = '/api/ipv6gw/order/confirm/to_postpay';
        let subProductType = '';
        if (datas[0].subProductType === Ipv6Payment.BANDWIDTH) {
            subProductType = Ipv6Payment.NETRAFFIC;
        } else {
            subProductType = Ipv6Payment.BANDWIDTH;
        }
        let param = {
            productType: 'postpay',
            subProductType: subProductType,
            data: datas,
            productTypeBefore: 'postpay',
            subProductTypeBefore: datas[0].subProductType
        };
        let type = 'TO_POSTPAY';
        sessionStorage.setItem('IPVSIXGW_ALTER', JSON.stringify(param));
        alterProductType('IPVSIXGW', ids, type, null, url);
    }

    cancelAlterProduct(e) {
        let selectedItems = this.data.get('table.selectedItems');
        let instanceIds = u.pluck(selectedItems, 'gatewayId');

        let confirm = new Confirm({
            data: {
                title: '取消计费变更',
                content: '确认取消计费变更？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http
                .ipv6gwCancelAlterProductType({
                    instanceIds,
                    serviceType: 'IPVSIXGW'
                })
                .then(() => {
                    Notification.success('取消计费变更成功');
                    this.loadPage();
                });
        });
    }

    resetTable() {
        this.data.set('table.selection', {
            mode: 'multi',
            selectedIndex: []
        });
        this.data.set('table.selectedItems', []);
    }

    // 点击修改icon
    onInstantEdit(row, rowIndex, type) {
        this.data.set(`edit.${type}.value`, row[`${type}`]);
        this.data.set(`edit.${type}.error`, false);
        const popWrap = this.ref(`popover-${type}-${rowIndex}`);
        popWrap.data.set('visible', !popWrap.data.get('visible'));
    }

    // 编辑弹框-输入名称/描述
    onEditInput(e, rowIndex, type) {
        let result =
            type === 'name' ? e.value === '' || !/^[a-zA-Z][\w\-\_\/\.]{0,64}$/.test(e.value) : e.value.length > 200;
        this.data.set(`edit.${type}.error`, result);
        this.data.set(`edit.${type}.value`, e.value);
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', result);
    }

    // 编辑弹框-提交
    editConfirm(row, rowIndex, type) {
        let edit = this.data.get(`edit.${type}`);
        if (edit.error) {
            return;
        }
        const key = type === 'name' ? 'description' : 'name';
        this.$http
            .ipv6gwUpdate({
                [type]: edit.value,
                gatewayId: row.gatewayId
            })
            .then(() => {
                this.editCancel(rowIndex, type);
                Notification.success('修改成功');
                this.loadPage();
            });
    }

    // 编辑弹框-取消
    editCancel(rowIndex, type) {
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', true);
        this.ref(`popover-${type}-${rowIndex}`).data.set('visible', false);
    }

    // 改变页数
    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }
    // 改变每页显示数量
    onPagerSizeChange(e) {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', e.value.pageSize);
        this.loadPage();
    }

    onSort(e) {
        this.data.set('order', e.value);
        this.loadPage();
    }

    // 筛选
    onFilter(e) {
        let name = e.field.name;
        let value = e[name] ? e[name] : '';
        this.data.set('filters.' + name, value);
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    onCreate() {
        let vpcId = this.data.get('vpcId') || '';
        location.hash = `#/vpc/ipv6gw/create?vpcId=${vpcId}`;
    }

    showMonitor(item) {
        let dialog = new Monitor({
            data: {
                ipv6Id: item.gatewayId
            }
        });
        dialog.attach(document.body);
    }

    onRelease(row) {
        this.$http
            .ipv6gwDelete({
                gatewayId: row.gatewayId
            })
            .then(() => {
                Notification.success('删除成功');
                this.loadPage();
            })
            .catch(error => {
                if (error) {
                    if (error.global) {
                        Notification.error(error.global, {duration: -1});
                    } else if (!error.field) {
                        Notification.error('未知错误', {duration: -1});
                    }
                }
            });
    }

    onUpgrade(row) {
        let url = `#/vpc/ipv6gw/upgrade?vpcId=${row.vpcId}&gatewayId=${row.gatewayId}`;
        location.hash = url;
    }

    toAlarmDetail(row) {
        let region = window.$context.getCurrentRegionId();
        redirect(`/bcm/#/bcm/alarm/rule/list~scope=BCE_IPV6GW&dimensions=InstanceId:${row.gatewayId}&region=${region}`);
    }

    onRegionChange() {
        location.reload();
    }
    // 编辑资源分组确定后
    oncommit() {
        this.data.set('showResource', false);
        this.loadPage();
    }
    // 编辑资源分组取消后
    onCancel() {
        this.data.set('showResource', false);
    }
    // 编辑资源分组
    changeResourceGroup(row) {
        let resource = {
            resourceId: row.gatewayId,
            serviceType: 'IPVSIXGW'
        };
        this.data.set('resource', resource);
        this.data.set('showResource', true);
    }
    getIamQuery() {
        this.$http.getInterfaceIam({interfaceName: 'createIPv6Gateway'}).then(res => {
            let message = '';
            !res.interfacePermission && (message += '创建IPv6网关权限');
            !res.createOrderPermission && (message += message ? '、创建订单权限' : '创建订单权限');
            !res.payOrderPermission && (message += message ? '、支付订单权限' : '');
            if (!res.requestId && !res.masterAccount) {
                if (message) {
                    this.data.set('iamPass', {
                        disable: true,
                        message: `您没有${message}，请联系主用户添加`
                    });
                } else {
                    this.data.set('iamPass', {
                        disable: false,
                        message: ''
                    });
                }
            } else {
                this.data.set('iamPass', {
                    disable: false,
                    message: ''
                });
            }
        });
    }
    // 下载
    onDownload() {
        let ids = [];
        ids = this.data.get('table.selectedItems').map(item => {
            return item.gatewayId;
        });
        let filter = 'ids=' + ids.join(',');
        window.open(`/api/ipv6gw/download?` + filter);
    }
    /**
     * @description 介绍卡片变化时回调
     * @param {boolean} flag
     */
    handleToggle(flag) {
        this.data.set('show', flag);
        window.$storage.set('natIntroShow', flag);
        this.data.set('introduceIcon', INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#151B26';
    }
    handleShowCard() {
        if (!this.data.get('show')) {
            this.data.set('show', true);
            window.$storage.set('natIntroShow', true);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type) {
        if (type === 'introduce') {
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
            const ele = this.data.get('introduceEle');
            ele.style.color = '#2468F2';
        } else {
            this.data.set('documentIcon', DOCUMENT_ICON_COLOR);
        }
    }
    handleMouseLeave(type) {
        const isShow = this.data.get('show');
        if (type === 'introduce') {
            this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
            const ele = this.data.get('introduceEle');
            !isShow && (ele.style.color = '#151B26');
        } else {
            this.data.set('documentIcon', DOCUMENT_ICON);
        }
    }
    onIpv6gwRelease() {
        const selectedItem = this.data.get('table.selectedItems');
        selectedItem[0] && this.onRelease(selectedItem[0]);
    }
    // 搜索事件
    onSearch() {
        this.data.set('pager.page', 1);
        this.loadPage();
    }
}
export default San2React(Processor.autowireUnCheckCmpt(Ipv6gwList));
