.vpc-topology {
    width: 100%;
    background: #f7f7f7;
    height: 100%;
    overflow-y: auto;
    box-sizing: border-box;
    .vpc-topology-widget {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #ffffff;
        .vpcSelect {
            background: #fff;
            height: 47px;
            color: #151b26;
            line-height: 47px;
            font-weight: 500;
            font-size: 16px;
            padding-left: 16px;
            .vpc-select {
                display: inline-block;
                margin-left: 12px;
            }
        }
        .help-file {
            font-size: 12px;
            font-weight: 400;
            font-family: PingFangSC-Regular;
            color: #151b26;
            line-height: 20px;
            position: absolute;
            right: 16px;
            &:hover {
                color: #2468f2;
            }
            .s-icon {
                position: relative;
                top: -1px;
                right: 4px;
                margin-right: 0;
                color: #2468f2;
                font-size: 14px;
            }
        }
        .function-introduce {
            color: #2468f2;
        }
    }
    .vpcRelation {
        position: relative;
        background: #f5f5f5;
        min-height: 100%;
        .contentBox {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            border: 1px solid #ebebeb;
            height: 100%;
            .localVpc {
                display: inline-block;
                position: absolute;
                font-size: 14px;
                color: #999;
                top: 20px;
                left: 20px;
            }
            .dashLine {
                width: 1px;
                height: 100%;
                position: absolute;
                border-right: 1px dashed #999;
                left: 675px;
                top: 0;
            }
            .peerVpc {
                display: inline-block;
                position: absolute;
                font-size: 14px;
                color: #999;
                top: 20px;
                left: 695px;
            }
            .noDiffVpc {
                margin: 0 149px 0;
            }
            .haveDiffVpc {
                margin-right: 149px;
            }
            .noDiffVpc,
            .diffVpc,
            .haveDiffVpc {
                position: relative;
                width: 149px;
                margin-top: 50px;
                display: inline-block;
                vertical-align: top;
            }
            .vpcItem {
                box-sizing: border-box;
                display: inline-block;
                width: 150px;
                height: 50px;
                border: 1px solid #999;
                border-radius: 10px;
                background: #fff url('../../../../img/vpcIcon.svg?url') no-repeat;
                background-size: 20px 20px;
                background-position: 10px 5px;
                padding-left: 35px;
                margin-bottom: 50px;
                .vpcName {
                    font-size: 14px;
                    padding: 5px 0;
                    display: inline-block;
                }
                .cidr {
                    color: #999;
                }
            }
            .noDiffVpc,
            .haveDiffVpc {
                .vpcItem {
                    border: 1px solid rgba(16, 140, 238, 0.5);
                }
                .vpcItem:hover {
                    border: 1px solid #2468f2;
                    color: #2468f2;
                    cursor: pointer;
                    .cidr {
                        color: #2468f2;
                    }
                }
            }
        }
    }
    .resourceRelation {
        position: relative;
        background: #fff;
        .content-box {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            .vpc-box {
                border: 1px dashed #999;
                position: absolute;
                width: 730px;
                height: calc(~'100% - 20px');
                left: 250px;
                top: 20px;
                border-radius: 6px;
                .vpc-title {
                    height: 30px;
                    background: #f5f5f5;
                    border-radius: 6px;
                    line-height: 30px;
                    .vpcName {
                        border-right: 1px solid #fff;
                    }
                    .vpcName,
                    .subnetNum {
                        padding: 0 20px 0;
                        display: inline-block;
                    }
                }
            }
            .publicnode {
                margin-bottom: 50px;
                width: 130px;
                height: 30px;
                text-align: center;
                line-height: 30px;
                border: 1px solid #999;
                box-sizing: border-box;
                border-radius: 15px;
                padding-left: 25px;
                cursor: pointer;
                background: #fff;
                color: #666;
            }
            .targetType {
                margin: 100px 0 0 30px;
                vertical-align: top;
                float: left;
                .internet {
                    margin-bottom: 130px;
                    background: url('../../../../img/internetIcon.svg?url') no-repeat;
                    background-size: 20px 20px;
                    background-position: 10px 5px;
                }
                .peervpc {
                    background: url('../../../../img/vpcIcon.svg?url') no-repeat;
                    background-size: 20px 20px;
                    background-position: 10px 5px;
                }
                .idc {
                    background: url('../../../../img/useridcIcon.svg?url') no-repeat;
                    background-size: 20px 20px;
                    background-position: 10px 5px;
                }
                .publicnode {
                    width: 110px;
                }
            }
            .gateways {
                margin: 100px 0 0 50px;
                float: left;
                z-index: 10;
                position: relative;
                .nat {
                    background: #fff url('../../../../img//natgwIcon.svg?url') no-repeat;
                    background-size: 20px 20px;
                    background-position: 10px 5px;
                    color: #2468f2;
                }
                .vpn {
                    background: #fff url('../../../../img/vpngwIcon.svg?url') no-repeat;
                    background-size: 20px 20px;
                    background-position: 10px 5px;
                    color: #2468f2;
                }
                .peer {
                    background: #fff url('../../../../img/peerconnIcon.svg?url') no-repeat;
                    background-size: 20px 20px;
                    background-position: 10px 5px;
                    color: #2468f2;
                }
                .dcgw {
                    background: #fff url('../../../../img/dcgwIcon.svg?url') no-repeat;
                    background-size: 20px 20px;
                    background-position: 10px 5px;
                    color: #2468f2;
                }
            }
            .route {
                width: 100px;
                float: left;
                margin: 100px 0 0 50px;
                background: url('../../../../img/routeIcon.svg?url') no-repeat;
                background-size: 20px 20px;
                background-position: 10px 5px;
            }
            .subnetList {
                float: left;
                padding-top: 150px;
                z-index: 10;
                position: relative;
                .addSubnet {
                    cursor: pointer;
                    font-size: 12px;
                    color: #2468f2;
                    margin-bottom: 18px;
                    .s-icon {
                        font-size: 12px;
                        fill: #2468f2;
                        display: inline-block;
                        position: relative;
                        top: -2px;
                    }
                }
                .add-subnet-disabled {
                    color: #999;
                    pointer-events: none;
                    cursor: not-allowed;
                    .s-icon {
                        fill: #999;
                    }
                }
                .subnetLine {
                    margin-bottom: 10px;
                    height: 120px;
                    .subnetText {
                        margin-bottom: 40px;
                        .s-icon {
                            fill: #2468f2;
                        }
                        .subnetText-over {
                            max-width: 250px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            display: inline-block;
                            vertical-align: sub;
                        }
                    }
                    .subnetResouces {
                        margin-left: -35px;
                        .publicnode {
                            display: inline-block;
                            width: 130px;
                            margin-bottom: 0;
                        }
                        .bcc {
                            background: url('../../../../img/bccIcon_blue.svg?url') no-repeat;
                            background-size: 20px 20px;
                            background-position: 10px 5px;
                        }
                        .blb {
                            background: url('../../../../img/blbIcon_blue.svg?url') no-repeat;
                            background-size: 20px 20px;
                            background-position: 10px 5px;
                        }
                        .eni {
                            background: url('../../../../img/enicIcon.svg?url') no-repeat;
                            background-size: 20px 20px;
                            background-position: 10px 5px;
                        }
                        .endpoint {
                            background: url('../../../../img/endpointIcon.svg?url') no-repeat;
                            background-size: 20px 20px;
                            background-position: 10px 5px;
                        }
                    }
                }
            }
        }
    }
    .text-hidden {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 100%;
    }

    .topology-content {
        margin: 16px;
        max-height: calc(~'100vh - 140px');
        border-radius: 6px;
        overflow: auto;
        .introduce-panel {
            margin: 0 0 16px 0;
        }
    }
}

.jump-style {
    color: #2468f2 !important;
    cursor: pointer;
}

.topology-popover {
    .topology-gateways {
        .topology-gateways-table {
            max-height: 300px;
            width: 600px;
            overflow-y: auto;
        }
    }
    .pop-title {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 5px;
        height: 30px;
        line-height: 30px;
        button {
            float: right;
        }
    }
}
.popover_table {
    .s-popover-content {
        min-width: 624px !important;
    }
}
.locale-en {
    .vpc-topology .resourceRelation .content-box .subnetList .subnetLine .subnetResouces {
        display: flex;
    }
    .vpc-topology .resourceRelation .content-box .route {
        width: 114px;
    }
    .vpc-topology .resourceRelation .content-box .subnetList .addSubnet {
        margin-left: -20px;
        margin-top: 2px;
    }
    .vpc-topology .resourceRelation .content-box .subnetList .subnetLine .subnetResouces .endpoint {
        width: 180px;
    }
    .vpc-topology .resourceRelation .content-box .subnetList .subnetLine .subnetResouces .eni {
        width: 180px;
    }
    .vpc-topology .resourceRelation .content-box .subnetList .subnetLine .subnetResouces .bcc {
        width: 140px;
    }
    .vpc-topology .resourceRelation .content-box .vpc-box {
        width: 900px;
    }
    .vpc-topology .resourceRelation .content-box .subnetList .subnetLine .subnetResouces .publicnode {
        margin-right: 8px;
    }
}
.vpc-subnet-dialog-wrapper {
    .s-dialog-content {
        width: 910px;
        .vpc-subnet-create {
            .s-form {
                h4 {
                    font-size: 14px !important;
                    color: #151b26 !important;
                    font-weight: 400 !important;
                }
                .s-form-item-error {
                    padding-bottom: 0px !important;
                }
                .s-form-item-help {
                    padding-bottom: 0 !important;
                }
            }
            .tag-edit-panel {
                .footer {
                    margin-top: 10px;
                }
                .reminder {
                    margin-bottom: 0 !important;
                }

                .footer {
                    .s-button {
                        padding: 0 !important;
                        background: white !important;
                        color: #2468f2 !important;
                        .icon-plus {
                            color: #2468f2 !important;
                            fill: #2468f2 !important;
                        }
                    }
                    .s-button:hover {
                        border-color: white !important;
                    }
                    .float-right {
                        float: none !important;
                    }
                }
                .footer > * {
                    margin-right: 16px !important;
                }
            }
            .cidr-tip-err {
                top: -10px !important;
            }
            .resource-form-part-wrap {
                .resource-group-panel {
                    .resouce-group-select-main label {
                        width: 112px !important;
                    }
                }
            }
        }
    }
}
