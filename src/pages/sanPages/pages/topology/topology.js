import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import VpcRelation from './components/vpcRelation';
import ResourceRelation from './components/resourcesRelations';
import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import IntroducePanel from '@/pages/sanPages/components/introducePanel/introducePanel';
import './style.less';

const {asPage, withSidebar, template, invokeComp} = decorators;

const tpl = html`
    <div>
        <div class="vpc-topology" s-ref="topology">
            <div class="vpc-topology-widget">
                <div class="vpcSelect">
                    网络拓扑
                    <vpc-select class="vpc-select" on-change="vpcChange" notSupportAllVpc="{{true}}" on-int="vpcInt" />
                </div>
                <a
                    s-ref="introduce"
                    href="javascript:void(0)"
                    class="help-file function-introduce"
                    on-click="handleShowCard"
                    on-mouseenter="handleMouseEnter('introduce')"
                    on-mouseleave="handleMouseLeave('introduce')"
                    s-if="{{!flag.NetworkSupportXS}}"
                >
                    <img class="s-icon" src="{{introduceIcon}}" />功能简介
                </a>
            </div>
            <div class="topology-content">
                <introduce-panel
                    isShow="{{show}}"
                    klass="{{'endpoint-peerconn-wrapper'}}"
                    title="{{introduceTitle}}"
                    description="{{description}}"
                    introduceOptions="{{introduceOptions}}"
                    on-toggle="handleToggle($event)"
                ></introduce-panel>
                <div s-if="showVpcRelation">
                    <vpc-relation
                        minHeight="{{topologyMinHeight}}"
                        currentVpc="{=currentVpcId=}"
                        on-showResourceRelation="showResourceRelation"
                    >
                    </vpc-relation>
                </div>
                <div s-if="showResourcesRelation">
                    <resource-relation vpcId="{{vpcId}}" vpcShortId="{{vpcShortId}}" />
                </div>
            </div>
        </div>
    </div>
`;

@template(tpl)
@invokeComp('@vpc-select')
class TopologyMain extends Component {
    static components = {
        'vpc-relation': VpcRelation,
        'resource-relation': ResourceRelation,
        'introduce-panel': IntroducePanel
    };
    initData() {
        return {
            currentVpcId: '',
            showVpcRelation: true,
            showResourcesRelation: false,
            topologyMinHeight: '',
            show: true,
            introduceTitle: '网络拓扑简介',
            description: '网络拓扑可以方便直观地展示VPC内资源信息和关联服务，并支持跳转到相应的服务列表。',
            introduceIcon: INTRODUCE_ICON_COLOR,
            introduceEle: null
        };
    }
    attached() {
        this.setSvgMinHeight();
        this.data.set('introduceEle', this.ref('introduce'));
    }

    vpcInt() {
        const vpcId = window.$storage.get('vpcId');
        const vpcs = window.$storage.get('vpcList');
        this.getShortId(vpcId);
        this.data.set('vpcId', vpcId);
        this.data.set('showVpcRelation', false);
        this.data.set('showResourcesRelation', true);
        this.data.set('currentVpcId', vpcs[0] ? vpcs[0].value : '');
    }
    vpcChange() {
        const vpcId = window.$storage.get('vpcId');
        this.getShortId(vpcId);
        this.data.set('vpcId', vpcId);
        if (vpcId === '') {
            this.data.set('showVpcRelation', true);
            this.data.set('showResourcesRelation', false);
        } else {
            this.data.set('showVpcRelation', false);
            this.data.set('showResourcesRelation', true);
        }
    }

    getShortId(vpcId) {
        let vpcs = window.$storage.get('vpcList');
        let vpcShortId = '';
        vpcs.forEach(item => {
            if (item.value === vpcId && item.vpcInfo) {
                vpcShortId = item.vpcInfo.shortId;
            }
        });
        this.data.set('vpcShortId', vpcShortId);
    }
    showResourceRelation(vpcId) {
        this.data.set('vpcId', vpcId);
        this.getShortId(vpcId);
        this.data.set('currentVpcId', vpcId);
        this.data.set('showVpcRelation', false);
        this.data.set('showResourcesRelation', true);
    }
    setSvgMinHeight() {
        this.data.set('topologyMinHeight', this.ref('topology').clientHeight);
    }
    handleToggle(flag) {
        this.data.set('show', flag);
        this.data.set('introduceIcon', INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#151B26';
    }
    handleShowCard() {
        if (!this.data.get('show')) {
            this.data.set('show', true);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type) {
        this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#2468F2';
    }
    handleMouseLeave(type) {
        const isShow = this.data.get('show');
        this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        !isShow && (ele.style.color = '#151B26');
    }
}
export default San2React(Processor.autowireUnCheckCmpt(TopologyMain));
