/**
 * @file src/vpc/ipv6gw/list/List.js
 * <AUTHOR>
 */

import {defineComponent} from 'san';
import {checker} from '@baiducloud/bce-opt-checker';
import {EipBindDialog} from '@baidu/bce-eip-sdk-san';
import {EipSDK} from '@baidu/bce-eip-sdk';
import {
    Tooltip,
    Icon
} from '@baidu/sui';
import {html, decorators, redirect} from '@baiducloud/runtime';

import Confirm from '@/pages/sanPages/components/confirm';
import rules from '../rules';


const template = html`
<div>
    <div class="vpc-vpn-list ui-table">
        <a s-if="item.eip"
            href="/eip/#/eip/instance/list?keywordType=INSTANCE_EIP&keyword={{item.eip}}"
        >{{item.eip}}</a>
        <span s-else>-</span>
        <span s-if="item.bandwidthInMbps">/{{item.bandwidthInMbps}}Mbps</span>
        <span s-else>/ -</span>

        <s-tooltip s-if="item.eipResourceStatus === 'STOPPED'" content="{{tipContent}}">
            <s-icon name="information"/>
        </s-tooltip>

        <span
            on-click="optEip(item)"
            class="cmd-button {{setWrapClass}}"
        >
            <i class="iconfont {{bindStateField[bindState].icon}}" />
        </span>
    </div>
</div>
`;

export default defineComponent({
    template,

    components: {
        's-tooltip': Tooltip,
        's-icon': Icon
    },

    initData() {
        return {
            bindStateField: {
                BIND: {
                    title: '解绑EIP',
                    icon: 'icon-unbind'
                },
                UNBIND: {
                    title: '绑定EIP',
                    icon: 'icon-bind'
                }
            }
        };
    },

    computed: {
        bindState() {
            const eip = this.data.get('item.eip');
            if (eip) {
                return 'BIND';
            } else {
                return 'UNBIND';
            }
        },
        tipContent() {
            const eipProductType = this.data.get('item.eipProductType');
            return (eipProductType === 'prepay')
                ? '您的公网 IP 已到期，续费后才能使用。'
                : '您的公网 IP 已欠费，充值后才能使用。';
        },
        bindable() {
            const item = this.data.get('item');
            const {bindEip} = checker.check(rules, item, '');
            return !bindEip.disable;
        }
    },

    setWrapClass() {
        const bindable = this.data.get('bindable');
        if (bindable) {
            return 'change-ip';
        } else {
            return 'state-disabled';
        }
    },

    optEip() {
        const bindState = this.data.get('bindState');
        switch (bindState) {
            case 'BIND':
                this.unbindEip();
                break;
            case 'UNBIND':
                this.bindEip();
                break;
            default:
                break;
        }
    },

    bindEip() {
        const item = this.data.get('item');
        let dialog = new EipBindDialog({
            data: {
                listRequester: this.$http.getEipBindList.bind(this.$http),
                submitRequester: this.$http.vpnEipBind.bind(this.$http),
                EipSDK: new EipSDK({client: window.$http, context: window.$context}),
                instanceType: 'VPN',
                instanceId: item.vpnId
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => this.fire('getVpnList'));
    },
    unbindEip() {
        const item = this.data.get('item');
        let confirm = new Confirm({
            data: {
                open: true,
                title: '解绑EIP',
                content: `公网BLB需要配合EIP才能正常工作。您确认解绑${item.eip}吗？`
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http.vpnEipUnbind({
                eip: '',
                instanceType: 'VPN',
                instanceId: item.vpnId
            }).then(() => this.fire('getVpnList'));
        });
    }
});
