import {Component} from 'san';
import u from 'lodash';
import {Dialog, Table, Popover, Icon, Button} from '@baidu/sui';
import {OutlinedRefresh, OutlinedPlus, OutlinedDelete} from '@baidu/sui-icon';
import {html, decorators, redirect, HttpClient} from '@baiducloud/runtime';
import {VpcSDK} from '@baidu/bce-vpc-sdk';
import {SubnetCreateDialog} from '@baidu/bce-vpc-sdk-san';

import DeleteCheckConfirm from '@/pages/sanPages/components/deleteCheck';
import zone from '@/pages/sanPages/utils/zone';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import {NatStatus, NatFlavor, DcGatewayStatus, VpnStatus, PeerConnStatus} from '@/pages/sanPages/common';
const {template} = decorators;

const kXhrOptions = {'X-silence': true};

const DEFAULT_LINES = [
    {
        points: '141 115 191 115'
    },
    {
        points: '141 275 191 275'
    },
    {
        points: '141 355 191 355'
    },
    {
        points: '321 115 371 115'
    },
    {
        points: '321 195 346 195 346 115'
    },
    {
        points: '321 275 346 275 346 195'
    },
    {
        points: '321 355 346 355 346 275'
    },
    {
        points: '421 130 421 160 461 160'
    }
];
const NO_NAT_VPN_DEFAULT_LINES = [
    {
        points: '141 115 191 115'
    },
    {
        points: '141 195 191 195'
    },
    {
        points: '321 115 371 115'
    },
    {
        points: '321 195 346 195 346 115'
    },
    {
        points: '421 130 421 160 461 160'
    }
];

const tpl = html`
<div>
    <div class="resourceRelation">
        <svg s-if="subnetLines.length" id="svg" width="1000px" height="{{svgHeight}}">
            <polyline
                s-for="item in subnetLines"
                points="{{item.points}}"
                stroke="#999" fill="none"
                style="stroke-width: 1px;"
            />
        </svg>
        <div class="content-box">
            <div class="vpc-box">
                <div class="vpc-title">
                    <div class="vpcName">{{vpcInfo.name}}&nbsp;|&nbsp;{{vpcInfo.cidr}}</div>
                    <div class="subnetNum">{{subnetCountText}}</div>
                </div>
            </div>
            <div class="targetType">
                <div s-for="item in targetType" class="{{item.class}} publicnode">
                    {{item.text}}
                </div>
            </div>
            <div class="gateways">
                <div
                    s-for="item, key in gateways"
                    class="{{item.class}} publicnode">
                    <s-popover placement="top" trigger="hover"
                        class="topology-popover {{item.class !== 'vpn' ? 'popover_table' : 'vpn_none_table'}}">
                        <div slot="content" class="topology-gateways">
                            <div s-if="item.class === 'vpn'" class="vpn-content">
                                <div>
                                    IPsec VPN网关：
                                    <span
                                        class="jump-style"
                                        on-click="jumpLink('ipsecVpn')"
                                    >{{ipsecVpnNum || 0}}</span>
                                </div>
                                <div>
                                    SSL VPN网关：
                                    <span
                                        class="jump-style"
                                        on-click="jumpLink('sslVpn')"
                                    >{{sslVpnNum || 0}}</span>
                                </div>
                                <div>
                                    GRE VPN网关：
                                    <span
                                        class="jump-style"
                                        on-click="jumpLink('greVpn')"
                                    >{{greVpnNum || 0}}</span>
                                </div>
                            </div>
                            <div s-else class="topology-gateways-table">
                                <div class="pop-title">
                                    {{item.text}}
                                    <s-button class="s-icon-button" on-click="refresh(key)">
                                        <outlined-refresh class="icon-class"/>
                                    </s-button>
                                </div>
                                <s-table
                                    columns="{{table[key+'Schema']}}"
                                    datasource="{{table[key+'List'}}">
                                </s-table>
                            </div>
                        </div>
                        <span>{{item.text}} x {{item.num}}</span>
                    </s-popover>
                </div>
            </div>
            <div class="route publicnode">{{routeName}}</div>
            <div class="subnetList">
                <div class="addSubnet {{!vpcInfo ? 'add-subnet-disabled' : ''}}" on-click="addSubnet">
                    <outlined-plus/>
                    {{subnetName}}
                </div>
                <div s-for="item in subnetList" class="subnetLine">
                    <div class="subnetText">
                        子网：{{item.shortId}}（
                        <span class="subnetText-over">{{item.name}}&nbsp;|&nbsp;{{item.cidr}}</span>
                        ）&nbsp;{{getLabel(item.az)}}
                        <outlined-delete  on-click="deleteSubnet(item)" />
                    </div>
                    <div class="subnetResouces">
                        <div class="bcc publicnode">
                            <s-popover
                                placement="top"
                                trigger="{{isHoverShowPopover('servers', item)}}"
                            >
                                <div slot="content">
                                    <div s-if="item.bccNum > 0">
                                        云服务器BCC：
                                        <span
                                            class="jump-style"
                                            on-click="jumpLink('bcc', '', item)"
                                        >{{item.bccNum}}</span>
                                    </div>
                                    <div s-if="item.bbcNum > 0">
                                        弹性裸金属服务器BBC：
                                        <span
                                            class="jump-style"
                                            on-click="jumpLink('bbc', '', item)"
                                        >{{item.bbcNum}}</span>
                                    </div>
                                    <div s-if="item.dccNum > 0">
                                        专属服务器DCC：
                                        <span
                                            class="jump-style"
                                            on-click="jumpLink('dcc', '', item)"
                                        >{{item.dccNum}}</span>
                                    </div>
                                </div>
                                <span class="jump-style" on-click="jumpLink('bcc', 'servers', item)">
                                    {{bccX}} x {{getAllNum('servers', item)}}
                                </span>
                            </s-popover>
                        </div>
                        <div class="blb publicnode">
                            <s-popover
                                placement="top"
                                trigger="{{isHoverShowPopover('blb', item)}}"
                            >
                                <div slot="content">
                                    <div s-if="item.normalBlbs > 0">
                                        普通型实例：
                                        <span
                                            class="jump-style"
                                            on-click="jumpLink('normalBlbs', '', item)"
                                        >{{item.normalBlbs}}</span>
                                    </div>
                                    <div s-if="item.appBlbs > 0">
                                        应用型实例：
                                        <span
                                            class="jump-style"
                                            on-click="jumpLink('appBlbs', '', item)"
                                        >{{item.appBlbs}}</span>
                                    </div>
                                    <div s-if="item.ipv6Blbs > 0">
                                        ipv6型实例：
                                        <span
                                            class="jump-style"
                                            on-click="jumpLink('ipv6Blbs', '', item)"
                                        >{{item.ipv6Blbs}}</span>
                                    </div>
                                </div>
                                <span class="jump-style" on-click="jumpLink('normalBlbs', 'blb', item)">
                                    {{blbName}} x {{getAllNum('blb', item)}}
                                </span>
                            </s-popover>
                        </div>
                        <div class="eni publicnode jump-style">
                            <span class="jump-style" on-click="jumpLink('eni', '', item)">
                                {{eniName}} x {{item.enics || 0}}
                            </span>
                        </div>
                        <div class="endpoint publicnode jump-style">
                            <span class="jump-style" on-click="jumpLink('endpoint', '', item)">
                                {{endpointName}} x {{item.endpoints || 0}}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <s-dialog
            open="{=open=}"
            width="550"
            title="{{'绑定'}}">

        </s-dialog>
    </div>
</div>
`;

@template(tpl)
export default class CreateHaVip extends Component {
    static components = {
        's-dialog': Dialog,
        's-table': Table,
        's-popover': Popover,
        's-icon': Icon,
        's-button': Button,
        'outlined-refresh': OutlinedRefresh,
        'outlined-plus': OutlinedPlus,
        'outlined-delete': OutlinedDelete
    };
    initData() {
        return {
            routeName: '路由表',
            subnetName: '添加子网',
            bccX: '云服务器',
            blbName: '负载均衡',
            eniName: '弹性网卡',
            endpointName: '服务网卡',
            targetType: [
                {
                    text: 'INTERNET',
                    class: 'internet'
                },
                {
                    text: '对端VPC',
                    class: 'peervpc'
                },
                {
                    text: '用户IDC',
                    class: 'idc'
                }
            ],
            table: {
                natSchema: [
                    {
                        name: 'id',
                        label: 'NAT网关名称/ID',
                        render(row) {
                            return (
                                `<span class="instance-name" title="${row.name}">` +
                                `<a href="#/vpc/nat/detail?vpcId=${row.vpcId}&id=${row.id}">${row.name}</a>` +
                                '<br />' +
                                '</span>' +
                                `<span title="${row.id}">${row.id}</span>`
                            );
                        }
                    },
                    {
                        name: 'status',
                        label: '状态',
                        render(row) {
                            let config = NatStatus.fromValue(row.status);
                            return (
                                '<span class="' +
                                (config ? config.styleClass : 'status error') +
                                '">' +
                                (config ? config.text : '异常') +
                                '</span>'
                            );
                        }
                    },
                    {
                        name: 'flavor',
                        label: '类型',
                        render(row) {
                            return NatFlavor.getTextFromValue(row.flavor) || '超大';
                        }
                    }
                ],
                vpnSchema: [
                    {
                        name: 'id',
                        label: 'VPN网关名称/ID',
                        render(row) {
                            return (
                                `<span class="instance-name" title="${row.vpnName}">` +
                                '<a href="#/vpc/vpn/detail?vpnId=' +
                                `${row.vpnId}&vpcId=${row.vpcId}">${row.vpnName}</a>` +
                                '</span>' +
                                '<br />' +
                                `<span title="${row.vpnId}" class="truncated">${row.vpnId}</span>`
                            );
                        },
                        width: '30%'
                    },
                    {
                        name: 'status',
                        label: '状态',
                        render(item) {
                            let config = VpnStatus.fromValue(item.status);
                            return (
                                '<span class="' +
                                config.styleClass +
                                '">' +
                                VpnStatus.getTextFromValue(item.status) +
                                '</span>'
                            );
                        },
                        width: '15%'
                    },
                    {
                        name: 'remoteSideIp',
                        label: '本端VPN网关公网IP/带宽',
                        width: '35%'
                    },
                    {
                        name: 'vpnConnNum',
                        label: '隧道数量',
                        render(item) {
                            return u.escape(item.vpnConnNum);
                        },
                        width: '20%'
                    }
                ],
                peerSchema: [
                    {
                        name: 'peerConnId',
                        label: '对等连接ID',
                        render(row) {
                            let peerConnId = row.peerConnId;
                            if (row.status === PeerConnStatus.DELETING) {
                                return `<span class="truncated" title="${peerConnId}">${peerConnId}</span>`;
                            }
                            return `<a class="truncated" title="${peerConnId}"
                                href="#/vpc/peerconn/detail?vpcId=${row.localVpcId}&localIfId=${row.localIfId}">
                                ${peerConnId}</a>`;
                        },
                        width: '20%'
                    },
                    {
                        name: 'status',
                        label: '状态',
                        render(item) {
                            let config = PeerConnStatus.fromValue(item.status);
                            return `<span class="${!u.isEmpty(config) ? config.styleClass : 'status unavailable'}">
                            ${!u.isEmpty(config) ? PeerConnStatus.getTextFromValue(item.status) : '不可用'}</span>`;
                        },
                        width: '20%'
                    },
                    {
                        name: 'bandwidth',
                        label: '带宽上限',
                        render(item) {
                            return `${u.escape(item.bandwidth)}Mbps`;
                        },
                        width: '20%'
                    },
                    {
                        name: 'peerVpcShortId',
                        label: '对端网络/ID',
                        render(item) {
                            let peerVpcShortId = u.escape(item.peerVpcShortId);
                            let peerCidr = item.peerCidr;
                            return `<span class="truncated" title="${peerCidr}">${peerCidr}</span><br>
                                <span class="truncated" title="${peerVpcShortId}">${peerVpcShortId}</span>`;
                        },
                        width: '40%'
                    }
                ],
                dcgwSchema: [
                    {
                        name: 'id',
                        label: '网关名称/ID',
                        render(row) {
                            return (
                                `<span class="instance-name" title="${row.name}">` +
                                `<a href="#/vpc/dcgw/detail?vpcId=${row.vpcId}&dcgwId=${row.id}">${row.name}</a>` +
                                '</span>' +
                                '<br />' +
                                `<span title="${row.id}">${row.id}</span>`
                            );
                        }
                    },
                    {
                        name: 'status',
                        label: '状态',
                        render(item) {
                            let config = DcGatewayStatus.fromValue(item.status);
                            return (
                                '<span class="' +
                                (!u.isEmpty(config) ? config.styleClass : 'status unavailable') +
                                '">' +
                                (!u.isEmpty(config) ? DcGatewayStatus.getTextFromValue(item.status) : '不可用') +
                                '</span>'
                            );
                        }
                    },
                    {
                        name: 'speed',
                        label: '出口带宽',
                        render(item) {
                            return u.escape(item.speed) + 'Mbps';
                        }
                    }
                ]
            },
            svgHeight: '1000px',
            sourceList: []
        };
    }
    static computed = {
        ipsecVpnNum() {
            return this.data.get('table.vpnList')?.filter(item => item.vpnType === 'IPSec').length;
        },
        sslVpnNum() {
            return this.data.get('table.vpnList')?.filter(item => item.vpnType === 'SSL').length;
        },
        greVpnNum() {
            return this.data.get('table.vpnList')?.filter(item => item.vpnType === 'GRE').length;
        },
        subnetCountText() {
            const count = this.data.get('subnetList')?.length || 0;
            return `共${count}个子网`;
        }
    };
    inited() {
        this.initPage();
    }
    attached() {
        this.loadPage();
        this.watch('vpcId', () => {
            this.initPage();
            this.loadPage();
        });
    }
    initPage() {
        let initData = {
            nat: {
                text: 'NAT网关',
                num: 0,
                class: 'nat'
            },
            vpn: {
                text: 'VPN网关',
                num: 0,
                class: 'vpn'
            },
            peer: {
                text: '对等连接',
                num: 0,
                class: 'peer'
            },
            dcgw: {
                text: '专线网关',
                num: 0,
                class: 'dcgw'
            }
        };
        let newDefaultLine = DEFAULT_LINES;
        if (!FLAG.NetworkSupportEip) {
            delete initData.nat;
            delete initData.vpn;
            this.data.set('targetType', [
                {
                    text: '对端VPC',
                    class: 'peervpc'
                },
                {
                    text: '用户IDC',
                    class: 'idc'
                }
            ]);
            newDefaultLine = NO_NAT_VPN_DEFAULT_LINES;
        }
        this.data.set('gateways', initData);

        this.data.set('subnetLines', newDefaultLine);
        this.data.set('subnetList', []);
        this.data.set('vpcInfo', {});
        this.data.set('table.natList', []);
        this.data.set('table.vpnList', []);
        this.data.set('table.peerList', []);
        this.data.set('table.dcgwList', []);
    }
    loadPage() {
        this.getVpcInfo();
        if (FLAG.NetworkSupportEip) {
            this.getNatList();
            this.getVpnList();
        }
        this.getDcgwList();
        this.getPeerConnList();
        Promise.all([this.getSubnetList(), this.getEniList(), this.getEndPointList(), this.getBlbList()]).then(
            result => {
                let subnetList = u.cloneDeep(result[0]);
                subnetList.forEach(item => {
                    item.enics = result[1][item.subnetId] || 0;
                    item.endpoints = result[2][item.subnetId] || 0;
                    if (!result[3][item.subnetId]) {
                        item.normalBlbs = 0;
                        item.appBlbs = 0;
                        item.ipv6Blbs = 0;
                    } else {
                        item.normalBlbs = result[3][item.subnetId].normal || 0;
                        item.appBlbs = result[3][item.subnetId].application || 0;
                        item.ipv6Blbs = result[3][item.subnetId].ipv6 || 0;
                    }
                });
                this.data.set('subnetList', subnetList);
            }
        );
    }
    refresh(type) {
        switch (type) {
            case 'nat':
                this.getNatList();
                break;
            case 'vpn':
                this.getVpnList();
                break;
            case 'dcgw':
                this.getDcgwList();
                break;
            case 'peer':
                this.getPeerConnList();
                break;
        }
    }
    getVpcInfo() {
        let vpcId = window.$storage.get('vpcId');
        this.$http.vpcInfo({vpcIds: [vpcId]}).then(result => {
            this.data.set('vpcInfo', result[vpcId]);
        });
    }
    async getSubnetList() {
        let startPointX = 421;
        let startPointY = 160;
        let result = await this.$http.vpcSubnetPageList(
            {
                keywordType: 'name',
                order: 'desc',
                orderBy: 'createdTime',
                pageNo: 1,
                pageSize: 10000,
                vpcId: window.$storage.get('vpcId')
            },
            {}
        );
        let subnetList = result.result || result.page;
        this.data.set('subnetList', subnetList);
        this.data.set('svgHeight', `${Math.max(subnetList.length * 130 + 200, 500)}px`);

        const netLines = [];
        subnetList.forEach((item, index) => {
            let xInterval = 135;
            let yWidth = 22;
            let xOffset = 75;
            let length = 130;
            if (index === 0) {
                length = 65;
            }
            if (window.$context.currentLanguage === 'en-us') {
                yWidth = 24;
                xOffset = 125;
            }
            let typeNum = 4;
            netLines.push({
                points:
                    startPointX +
                    ' ' +
                    startPointY +
                    ' ' +
                    startPointX +
                    ' ' +
                    (startPointY + length) +
                    ' ' +
                    (startPointX + xOffset + xInterval * 3 + (window.$context.currentLanguage === 'en-us' ? 40 : 0)) +
                    ' ' +
                    (startPointY + length)
            });
            while (typeNum--) {
                netLines.push({
                    points:
                        startPointX +
                        xOffset +
                        xInterval * typeNum +
                        (window.$context.currentLanguage === 'en-us' && typeNum === 3 ? 40 : 0) +
                        ' ' +
                        (startPointY + length) +
                        ' ' +
                        (startPointX +
                            xOffset +
                            xInterval * typeNum +
                            (window.$context.currentLanguage === 'en-us' && typeNum === 3 ? 40 : 0)) +
                        ' ' +
                        (startPointY + length + yWidth)
                });
            }
            startPointY = startPointY + length;
        });
        let defaultLine = FLAG.NetworkSupportEip ? DEFAULT_LINES : NO_NAT_VPN_DEFAULT_LINES;
        this.data.set('subnetLines', [...defaultLine, ...netLines]);
        return subnetList;
    }
    async getNatList() {
        let result = [];
        if (window.$storage.get('natSts')) {
            result = await this.$http.getNatList(
                {
                    pageNo: 1,
                    pageSize: 100,
                    vpcId: window.$storage.get('vpcId')
                },
                kXhrOptions
            );
        }
        this.setGateWays('nat', result);
    }
    async getVpnList() {
        let result = [];
        if (window.$storage.get('vpnSts')) {
            result = await this.$http.getVpnList(
                {
                    pageNo: 1,
                    pageSize: 100,
                    vpcId: window.$storage.get('vpcId')
                },
                kXhrOptions
            );
        }
        this.setGateWays('vpn', result);
    }
    async getPeerConnList() {
        let result = await this.$http.peerconnList(
            {
                localVpcShortId: this.data.get('vpcShortId'),
                order: 'desc',
                orderBy: 'id',
                pageNo: 1,
                pageSize: 100,
                vpcId: window.$storage.get('vpcId')
            },
            kXhrOptions
        );
        this.setGateWays('peer', result);
    }
    async getDcgwList() {
        let result = await this.$http.dcgwList(
            {
                pageNo: 1,
                pageSize: 100,
                vpcId: window.$storage.get('vpcId')
            },
            kXhrOptions
        );
        this.setGateWays('dcgw', result);
    }
    setGateWays(type, result) {
        let gateways = u.cloneDeep(this.data.get('gateways'));
        gateways[type].num = result.totalCount || 0;
        this.data.set('gateways', gateways);
        this.data.set(`table.${type}List`, result.result);
    }
    addSubnet() {
        if (this.data.get('vpcInfo')) {
            let dialog = new SubnetCreateDialog({
                sdk: new VpcSDK({
                    client: new HttpClient(),
                    context: window.$context
                }),
                subnetList: this.data.get('subnetList'),
                vpcInfo: this.data.get('vpcInfo'),
                availableService: window.$context.getAvailableService().split(',')
            });
            dialog.on('create', () => {
                this.getSubnetList();
            });
            dialog.attach(document.body);
        }
    }
    async getEniList() {
        let result = await this.$http.getEniList({
            pageNo: 1,
            pageSize: 1000,
            vpcId: window.$storage.get('vpcId')
        });
        let subnetEniMap = {};
        let list = result.result || result.page;
        list.forEach(item => {
            if (subnetEniMap[item.subnetId]) {
                subnetEniMap[item.subnetId]++;
            } else {
                subnetEniMap[item.subnetId] = 1;
            }
        });

        return subnetEniMap;
    }
    async getEndPointList() {
        let result = await this.$http.getAllEndpoint({
            pageNo: 1,
            pageSize: 1000,
            vpcId: window.$storage.get('vpcId')
        });
        let subnetSniMap = {};
        let list = result.result || result.page;
        list.forEach(item => {
            if (subnetSniMap[item.subnetId]) {
                subnetSniMap[item.subnetId]++;
            } else {
                subnetSniMap[item.subnetId] = 1;
            }
        });

        return subnetSniMap;
    }
    async getBlbList() {
        let result = {};
        let subnetBLbMap = {};
        if (window.$storage.get('blbSts')) {
            result = await this.$http.getAllBlb({
                keywordType: 'vpcId',
                keyword: window.$storage.get('vpcId'),
                pageNo: 1,
                pageSize: 10000
            });
            let list = result.result || result.page;
            list.forEach(item => {
                if (!subnetBLbMap[item.subnetId]) {
                    subnetBLbMap[item.subnetId] = {
                        normal: 0,
                        application: 0,
                        ipv6: 0
                    };
                }
            });
            list.forEach(item => {
                subnetBLbMap[item.subnetId][item.blbType]++;
            });
        }
        return subnetBLbMap;
    }
    getLabel(item) {
        return zone.getLabel(item);
    }
    async deleteSubnet(item) {
        await this.checkSource(item);
        let confirm = new DeleteCheckConfirm({
            data: {
                open: true,
                sourceList: this.data.get('sourceList'),
                type: 'subnet'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http.subnetDelete({subnetIds: [item.subnetId]}).then(data => {
                this.getSubnetList();
            });
        });
    }
    jumpLink(comType, type, item) {
        if (type && this.getAllNum(type, item) > 0) {
            return;
        }
        const LINK = {
            bcc: {
                link: `/bcc/#/bcc/instance/list?subnetId=` + item.shortId
            },
            bbc: {
                link: '/bbc/#/bbc/instance/list'
            },
            dcc: {
                link: '/bcc/#/dcc/host/list'
            },
            normalBlbs: {
                link: `/blb/#/blb/list?subnetName=` + item.name
            },
            appBlbs: {
                link: `/blb/#/appblb/list?subnetName=` + item.name
            },
            ipv6Blbs: {
                link: `/blb/#/blb/ipv6/list?subnetName=` + item.name
            },
            eni: {
                link: `/network/#/vpc/eni/list?subnetName=` + item.name
            },
            endpoint: {
                link: `/network/#/vpc/endpoint/list?subnetName=` + item.name
            },
            ipsecVpn: {
                link: '/network/#/vpc/vpn/list'
            },
            sslVpn: {
                link: '/network/#/vpc/sslvpn/list'
            },
            greVpn: {
                link: '/network/#/vpc/grevpn/list'
            }
        };
        window.location = LINK[comType].link;
    }
    getAllNum(key, item) {
        const ITEM_KEY = {
            servers: ['bccNum', 'bbcNum', 'dccNum'],
            blb: ['normalBlbs', 'appBlbs', 'ipv6Blbs']
        };
        let num = 0;
        ITEM_KEY[key]?.map(key => {
            num = num + (item[key] || 0);
        });
        return num;
    }
    isHoverShowPopover(type, item) {
        return this.getAllNum(type, item) > 0 ? 'hover' : 'null';
    }

    checkSource(item) {
        let subnetId = item.subnetId;
        let payload = {
            subnetIds: [subnetId]
        };
        return this.$http
            .checkSubnetBeforeDelete(payload)
            .then(res => {
                let sourceList = [];
                if (Object.keys(res.resourceIpCheck[subnetId]).length > 0) {
                    let arr = Object.keys(res.resourceIpCheck[subnetId]).map(i => {
                        return {
                            sourceName: i,
                            sourceNum: res.resourceIpCheck[subnetId][i]
                        };
                    });
                    sourceList.push({
                        name: item.name,
                        source: arr,
                        subnetId
                    });
                }
                this.data.set('sourceList', sourceList);
            })
            .catch(err => {
                this.data.set('sourceList', []);
            });
    }
}
