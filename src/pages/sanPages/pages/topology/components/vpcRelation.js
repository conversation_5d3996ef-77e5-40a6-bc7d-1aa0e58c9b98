/**
 * @file src/vpc/ipv6gw/list/List.js
 * <AUTHOR>
 */


import {defineComponent} from 'san';
import u from 'lodash';
import {html, decorators, redirect} from '@baiducloud/runtime';

const template = html`
<div>
    <div class="vpcRelation">
        <svg s-if="svgLength" id="svg" width="1000px" height="{{svgHeight}}">
            <div s-for="item in line">
                <path d="{{item.path}}" stroke="#999" fill="none" style="stroke-width: 1px;"></path>
            </div>
        </svg> 
        <div class="contentBox">
            <div class="localVpc">本帐号本地域</div>
            <div class="dashLine"></div>
            <div class="peerVpc">跨帐号或跨地域</div>
            <div class="noDiffVpc" s-if="noDiffVpc.length">
                <div s-for="item in noDiffVpc"
                    class="vpcItem"
                    on-click="entryVpc(item)">
                    <span class="vpcName text-hidden">{{item.vpcName || '-'}}</span><br>
                    <span class="cidr">{{item.cidr}}</span>
                </div>
            </div>
            <div class="haveDiffVpc" s-if="haveDiffVpc.length">
                <div s-for="item in haveDiffVpc"
                    class="vpcItem"
                    on-click="entryVpc(item)">
                    <span class="vpcName text-hidden">{{item.vpcName || '-'}}</span><br>
                    <span class="cidr">{{item.cidr}}</span>
                </div>
            </div>
            <div class="diffVpc" s-if="diffVpc.length">
                <div s-for="item in diffVpc" class="vpcItem">
                    <span class="vpcName text-hidden">{{item.vpcName || '-'}}</span><br>
                    <span class="cidr">{{item.cidr}}</span>
                </div>
            </div>
        </div>
    </div>
</div>
`;
export default defineComponent({
    template,
    initData() {
        return {
            vpcs: [],
            noDiffVpc: [],
            haveDiffVpc: [],
            diffVpc: [],
            lines: [],
            line: {}
        };
    },
    attached() {
        this.initPage();
        this.watch('currentVpc', (vpcId) => {
            this.vpcChange(vpcId);
        });
    },
    computed: {
        svgLength() {
            return Math.max(
                this.data.get('noDiffVpc').length,
                this.data.get('haveDiffVpc').length,
                this.data.get('diffVpc').length
            );
        },
        svgHeight() {
            const h1 = this.data.get('svgLength') * 100 + 50;
            const h2 = this.data.get('minHeight') - 90;
            return Math.max(h1, h2);
        }
    },
    vpcChange(vpcId) {
        this.initPage(vpcId);
    },
    entryVpc(vpc) {
        this.fire('showResourceRelation', vpc.vpcId, vpc.shortId);
    },
    async initPage(vpcId) {
        this.data.set('line', []);
        this.data.set('lines', []);
        this.data.set('noDiffVpc', []);
        this.data.set('haveDiffVpc', []);
        this.data.set('diffVpc', []);

        await Promise.all([
            this.getVpcs(),
            this.getPeerConnList(vpcId)
        ]);
        let vpcs = this.data.get('vpcs');
        let vpcMap = {};
        vpcs.forEach(vpc => {
            vpcMap[vpc.vpcId] = vpc;
        });
        let points = [
            ...this.data.get('noDiffVpc'),
            ...this.data.get('haveDiffVpc'),
            ...this.data.get('diffVpc')
        ];
        this.data.set('noDiffVpc', this.improvingData(vpcMap, this.data.get('noDiffVpc')));
        this.data.set('haveDiffVpc', this.improvingData(vpcMap, this.data.get('haveDiffVpc')));
        this.data.set('diffVpc', this.improvingData(vpcMap, this.data.get('diffVpc')));

        if (!vpcId) {
            let noPeerVpc = u.cloneDeep(vpcMap);
            points.forEach(item => {
                delete noPeerVpc[item.vpcId];
            });
            u.each(noPeerVpc, (item, key) => {
                this.data.push('noDiffVpc', {
                    vpcId: key,
                    vpcName: item.name,
                    cidr: item.cidr
                });
            });
        } else {
            let vpcInfo = vpcMap[vpcId];
            if (!points.length) {
                this.data.push('noDiffVpc', {
                    vpcId: vpcId,
                    vpcName: vpcInfo.name,
                    cidr: vpcInfo.cidr
                });
            }
        }
        let lines = this.data.get('lines');
        lines.forEach(line => {
            let startPonit = null;
            let endPoint = null;
            points.forEach(point => {
                if (line.vpcId1 === point.vpcId) {
                    startPonit = point;
                }
                if (line.vpcId2 === point.vpcId) {
                    endPoint = point;
                }
            });
            if (startPonit && endPoint) {
                if (startPonit.type === endPoint.type) {
                    let controlX = startPonit.leftPointX - 100;
                    let controlY = Math.floor((startPonit.leftPointY + endPoint.leftPointY) / 2);
                    line.path = 'M' + startPonit.leftPointX + ' ' + startPonit.leftPointY + ' '
                    + 'Q' + controlX + ' ' + controlY + ' ' + endPoint.leftPointX + ' ' + endPoint.leftPointY;
                } else {
                    if ((startPonit.type === 'noDiffVpc' && endPoint.type === 'haveDiffVpc')
                    || (startPonit.type === 'haveDiffVpc' && endPoint.type === 'diffVpc')) {
                        line.path = 'M' + startPonit.rightPointX + ' ' + startPonit.rightPointY + ' '
                        + 'L' + endPoint.leftPointX + ' ' + endPoint.leftPointY;
                    } else {
                        line.path = 'M' + startPonit.leftPointX + ' ' + startPonit.leftPointY + ' '
                        + 'L' + endPoint.rightPointX + ' ' + endPoint.rightPointY;
                    }
                }
            }
        });
        let result = {};
        let finalResult = [];
        lines = lines.filter(line => line.path)
        .forEach(item => {
            if (!result[item.path]) {
                result[item.path] = item;
                finalResult.push(item);
            }
        });
        this.data.set('line', finalResult);
    },
    improvingData(vpcMap, points) {
        points.forEach(point => {
            if (!point.cidr) {
                point.cidr = vpcMap[point.vpcId] && vpcMap[point.vpcId].cidr;
            }
            point.vpcName = vpcMap[point.vpcId] && vpcMap[point.vpcId].name;
        });
        return u.cloneDeep(points);
    },
    getVpcs() {
        if (this.data.get('vpcs').length) {
            return Promise.resolve();
        }
        this.$http.vpcList().then(data => {
            this.data.set('vpcs', data);
        });
    },
    async getPeerConnList(vpcId) {
        let params = {
            order: 'desc',
            orderBy: 'id',
            pageNo: 1,
            pageSize: 1000
        };
        if (vpcId && this.data.get('vpcs').length) {
            this.data.get('vpcs').forEach(item => {
                if (item.vpcId === vpcId) {
                    params.localVpcShortId = item.shortId;
                }
            });
            params.vpcId = vpcId;
        }
        let result = await this.$http.peerconnList(params);
        let peerConns = result.result;
        peerConns.forEach(peerConn => {
            if (peerConn.connType === 'diff'
            || peerConn.localRegion !== peerConn.peerRegion) {
                if (peerConn.role !== 'acceptor') {
                    this.data.push('lines', {
                        vpcId1: peerConn.localVpcId,
                        vpcId2: peerConn.peerVpcId,
                        vpcName2: peerConn.peerVpcName,
                        vpcCidr2: peerConn.peerCidr,
                        diffLine: true
                    });
                }
            } else if (peerConn.role === 'acceptor') {
                this.data.push('lines', {
                    vpcId1: peerConn.peerVpcId,
                    vpcId2: peerConn.localVpcId,
                    diffLine: false
                });
            } else {
                this.data.push('lines', {
                    vpcId1: peerConn.localVpcId,
                    vpcId2: peerConn.peerVpcId,
                    diffLine: false
                });
            }
        });
        this.setPoint();
    },
    setPoint() {
        let noDiffVpcLeftPointX = 150;
        let noDiffVpcLeftPointY = 75;

        let noDiffVpcRightPointX = noDiffVpcLeftPointX + 150;
        let noDiffVpcRightPointY = noDiffVpcLeftPointY;

        let haveDiffVpcLeftPointX = noDiffVpcRightPointX + 150;
        let haveDiffVpcLeftPointY = noDiffVpcRightPointY;

        let haveDiffVpcRightPointX = haveDiffVpcLeftPointX + 150;
        let haveDiffVpcRightPointY = haveDiffVpcLeftPointY;

        let diffVpcleftPointX = haveDiffVpcRightPointX + 150;
        let diffVpcleftPointY = haveDiffVpcRightPointY;
        let lines = this.data.get('lines');
        lines.forEach(line => {
            if (line.diffLine) {
                let haveDiffVpc = this.data.get('haveDiffVpc').map(item => item.vpcId);
                let diffVpc = this.data.get('diffVpc').map(item => item.vpcId);
                if (haveDiffVpc.indexOf(line.vpcId1) === -1) {
                    this.data.push('haveDiffVpc', {
                        vpcId: line.vpcId1,
                        leftPointX: haveDiffVpcLeftPointX,
                        leftPointY: haveDiffVpcLeftPointY,
                        rightPointX: haveDiffVpcRightPointX,
                        rightPointY: haveDiffVpcRightPointY,
                        type: 'haveDiffVpc'
                    });
                    haveDiffVpcLeftPointY += 100;
                    haveDiffVpcRightPointY += 100;
                }
                if (diffVpc.indexOf(line.vpcId2) === -1) {
                    this.data.push('diffVpc', {
                        vpcId: line.vpcId2,
                        vpcName: line.vpcName2,
                        cidr: line.vpcCidr2,
                        leftPointX: diffVpcleftPointX,
                        leftPointY: diffVpcleftPointY,
                        type: 'diffVpc'
                    });
                    diffVpcleftPointY += 100;
                }
            }
        });
        lines.forEach(line => {
            if (!line.diffLine) {
                let haveDiffVpc = this.data.get('haveDiffVpc').map(item => item.vpcId);
                let noDiffVpcIds = this.data.get('noDiffVpc').map(item => item.vpcId);
                if (noDiffVpcIds.indexOf(line.vpcId1) === -1
                && haveDiffVpc.indexOf(line.vpcId1) === -1) {
                    this.data.push('noDiffVpc', {
                        vpcId: line.vpcId1,
                        leftPointX: noDiffVpcLeftPointX,
                        leftPointY: noDiffVpcLeftPointY,
                        rightPointX: noDiffVpcRightPointX,
                        rightPointY: noDiffVpcRightPointY,
                        type: 'noDiffVpc'
                    });
                    noDiffVpcLeftPointY += 100;
                    noDiffVpcRightPointY += 100;
                }
            }
        });
    }
});
