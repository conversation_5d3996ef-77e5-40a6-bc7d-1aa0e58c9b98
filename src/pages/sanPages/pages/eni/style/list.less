.vpc-eni-list {
    // width: 0;
    flex: 1;
    .vpc-eni-header {
        .eni-widget {
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .widget-left {
                .title {
                    display: inline-block;
                    margin: 0;
                    color: #151b26;
                    margin-right: 12px;
                    height: 47px;
                    line-height: 47px;
                    font-weight: 500;
                    font-size: 16px;
                    margin-left: 16px;
                }
            }
            .widget-right {
                .help-file {
                    margin-right: 16px;
                    font-size: 12px;
                    font-weight: 400;
                    .s-icon {
                        position: relative;
                        top: -1px;
                        right: 4px;
                        font-size: 14px;
                        margin-right: 0;
                        color: #2468f2;
                    }
                }
            }
        }
    }
    .s-biz-page-toolbar {
        overflow: hidden;
    }
    .s-biz-page-tb-left {
        .icon-plus {
            color: #fff;
        }
    }
    .s-table {
        .s-table-row:hover {
            .name-icon {
                display: inline;
            }
        }
        .name-icon {
            display: none;
            font-size: 12px;
            fill: #2468f2;
        }
        .operation {
            a {
                display: block;
            }
        }
        .s-icon {
            font-size: 12px;
            color: #2468f2;
        }
        .truncated {
            overflow: hidden;
            text-overflow: ellipsis;
            -o-text-overflow: ellipsis;
            white-space: nowrap;
            zoom: 1;
            display: inline-block;
            max-width: 80%;
            vertical-align: middle;
        }
        .sourceEipClass {
            pointer-events: none;
            .iconClass {
                .s-icon-button-able {
                    fill: #b4b6ba !important;
                }
            }
        }
    }
    .filter-buttons-wrap {
        display: flex;
        align-items: center;

        .s-cascader {
            margin-right: 8px;
            .s-cascader-value-arrow {
                top: 0;
            }
        }
        .s-cascader-value {
            vertical-align: middle;
            font-size: 12px;
            padding-top: 0;
            padding-bottom: 0;
            line-height: 30px;
        }
        .s-auto-compelete {
            .s-select {
                input {
                    width: 170px !important;
                }
            }
        }
        .button-item {
            margin-right: 5px;
        }
        .search-content {
            display: flex;
            align-items: center;
            position: relative;
            margin-right: 5px;
        }
        .s-icon.search {
            position: absolute;
            right: 5px;
            color: #615a5a;
        }
        .icon-fresh {
            margin-right: 5px;
        }
        .download-icon {
            margin-right: 8px;
        }
    }
    .table-full-wrap {
        .s-table {
            .s-table-body {
                max-height: calc(~'100vh - 334px');
            }
        }
    }
    .operation_class {
        .s-button {
            padding: 0;
            margin-right: 12px;
        }
    }
    .search-eni {
        .search-res {
            margin: 0px;
            display: inline-flex;
            .s-cascader {
                margin: 0 8px 0 0;
            }
        }
    }
    .desc-class {
        word-break: break-all;
    }
}
