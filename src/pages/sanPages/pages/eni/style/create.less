.eni-create-class {
    background: #fff;
    width: 100%;
    min-height: 100%;
    background-color: #f7f7f9;
    padding-bottom: 20px;
    .label_class {
        .inline-tip {
            top: 3px;
            position: relative;
            .s-tip-warning {
                justify-content: center;
                .warning_class {
                    fill: #999;
                }
            }
            .s-tip:hover .s-icon path {
                fill: #2468f2 !important;
            }
        }
    }
    .form-part-wrap {
        border-radius: 6px;
        width: calc(~'100vw - 32px');
        background: #fff;
        margin: 16px;
        padding: 24px;
        .inline-col {
            line-height: 30px;
        }
        h4 {
            display: inline-block;
            border: none;
            zoom: 1;
            font-size: 16px;
            color: #151b26;
            font-weight: 500;
            margin: 0;
            padding: 0;
        }
        .s-form-item-label {
            width: 92px;
            height: 30px;
        }
        .label_class {
            .inline-tip {
                top: 3px;
                position: relative;
                .s-tip-warning {
                    justify-content: center;
                    .warning_class {
                        fill: #999;
                    }
                }
                .s-tip:hover .s-icon path {
                    fill: #2468f2 !important;
                }
            }
        }
        .center_class {
            .s-row {
                .s-form-item-control-wrapper {
                    line-height: 30px;
                }
            }
        }
    }
    .s-tip-warning {
        position: relative;
        top: 3px;
    }
    .s-table-cell {
        vertical-align: top;
    }
    .s-table-cell-ip {
        .s-table-cell-text {
            div {
                display: flex;
            }
        }
    }
    .ip-item {
        .s-form-item-control-wrapper {
            width: 800px;
        }
        .inline-tip {
            top: 3px;
            position: relative;
            .s-tip-warning {
                justify-content: center;
                .warning_class {
                    fill: #999;
                }
            }
            .s-tip:hover .s-icon path {
                fill: #2468f2 !important;
            }
        }
        .s-table-cell-type {
            .s-table-cell-text {
                line-height: 30px;
            }
        }
        .s-table-cell-ip {
            .s-table-cell-text {
                line-height: 30px;
            }
        }
    }
    .buybucket {
        width: 100%;
        left: 0;
        height: 60px;
        .buybucket-container {
            width: auto;
            float: left;
            height: 48px;
            transform: translateY(12.5%);
            display: flex;
            align-items: center;
        }
        .s-button-skin-primary {
            height: 38px;
        }
    }
    .ipv6-class {
        .s-form-item-control-wrapper {
            line-height: 30px;
        }
    }
    .ipv6-switch-class {
        display: flex;
        .switch_btn {
            margin-right: 8px;
        }
    }
    .ui-form-item-invalid-label {
        color: #f33e3e;
        margin-left: 8px;
    }
    .error_tip {
        margin-left: 5px;
        margin-top: 8px;
    }
    .tag-edit-panel {
        .s-form-item {
            margin: 0 8px 12px 0 !important;
        }
    }
    .tag-v2-panel-container {
        width: 800px !important;
    }
}
.vpc-eni-create {
    .radio_eni_group {
        .s-form-item-control-wrapper {
            line-height: 30px;
            height: 30px;
        }
        .invalid-label {
            color: #f33e3e;
        }
    }
    .s-dialog>.s-dialog-wrapper .s-dialog-content {
        min-height: 240px;
    }
}

.locale-en {
    .eni-create-class .form-part-wrap .s-form-item-label {
        width: 132px;
        height: 30px;
    }
}
