/**
 * @file network/eni/pages/styles/detail.less
 * <AUTHOR>
 */
.clearfix() {
    &:before,
    &:after {
        display: table;
        content: '';
    }
    &:after {
        clear: both;
    }
}
.vpc-main-wrap {
    width: 100%;
    height: 100%;
    overflow-y: scroll;
    .s-biz-page-header {
        font-size: 16px;
        padding: 15px 0;
    }
    .s-detail-page-content {
        margin: 16px;
        padding: 24px;
    }
    li.content-item {
        margin-bottom: 16px;
        width: 33%;
        float: left;
        label {
            color: #666;
        }
    }
}

.vpc-eni-detail {
    .s-detail-page-title {
        display: none;
    }
    .s-detail-page-content {
        margin: 0 !important;
    }
    .s-icon {
        font-size: 12px;
        color: #2468f2;
    }
    .instance-not-found-class {
        height: 100%;
    }
    .instance-name {
        font-size: 16px;
        font-weight: 500;
        color: #151b26;
        padding-right: 12px;
        padding-left: 0px;
    }
    h4 {
        display: block;
        color: #151b26;
        line-height: 24px;
        font-weight: 500;
        margin-bottom: 16px;
        font-size: 16px;
        zoom: 1;
    }
    .cell-title {
        display: inline-block;
        vertical-align: top;
        color: #5e626a;
        margin-right: 16px;
        width: 64px;
    }
    .cell-content {
        display: inline-block;
        color: #151a26;
        max-width: 70%;
        word-break: break-all;
        position: relative;
    }
    .sourceEipClass {
        pointer-events: none;
    }
}

.vpc-eni-ip-wrap {
    .eni-add-ip {
        margin-bottom: 16px;
    }
    .s-pagination-wrapper {
        float: right;
    }
    .instance-name {
        font-size: 16px;
        font-weight: 500;
        color: #151b26;
        padding-right: 12px;
    }
    .sourceEipClass {
        pointer-events: none;
        color: #b4b6ba;
    }
    .s-detail-page-title {
        display: none;
    }
    .eni-ip-footer {
        margin-top: 16px;
    }
    .ipv6-switch {
        display: flex;
        margin-bottom: 16px;
    }
}
.loading-content-bg {
    .s-list-page {
        background: #fff;
    }
}

.ipv6-group {
    display: flex;
    align-items: center;
    .title,
    .s-radio {
        margin-right: 10px;
    }
}

.eni-detail-ip-dialog-custom {
    .ipv6-group {
        .title {
            width: 80px;
            height: 30px;
            line-height: 30px;
        }
        .ip-radio {
            position: relative;
            top: 4px;
        }
    }
    .s-input {
        display: block;
        margin: 12px 0 0 90px;
    }
    .s-form-item-error {
        margin-left: 90px;
    }
}
.ip-detail-tab-header {
    width: 100%;
    .s-biz-page-content {
        background-color: #fff;
        border-radius: 6px;
        margin: 16px !important;
        padding: 24px;
        .s-biz-page-body {
            margin-top: 16px;
        }
    }
    .s-tab-list-page {
        .ip-tab-header {
            padding: 16px 24px 0px;
            background-color: #ffffff;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
            .custom-tab-wrapper {
                display: flex;
                span {
                    height: 100%;
                    cursor: pointer;
                    font-size: 14px;
                    align-items: center;
                    display: inline-block;
                    padding-bottom: 10px;
                    font-weight: 400 !important;
                    color: #151b26;
                    border-bottom: 2px solid #fff;
                    &:first-child {
                        margin-right: 32px;
                    }
                }
                span.actived {
                    border-color: #2468f2;
                    color: #2468f2;
                }
            }
        }
    }
    .space-header {
        margin: 0px;
    }
    .vpn-detail-header {
        display: flex;
        align-items: center;
        background: #fff;
        height: 50px;
        padding-left: 16px;
    }
    .instance-name {
        font-weight: 500;
        color: #151b26;
        padding-right: 16px;
        font-size: 16px;
    }
}
