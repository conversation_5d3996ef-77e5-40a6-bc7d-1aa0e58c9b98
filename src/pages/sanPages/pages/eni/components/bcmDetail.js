import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import u from 'lodash';
import {BcmSDK} from '@baidu/new-bcm-sdk';
import {BcmChartPanel} from '@baidu/new-bcm-sdk-san';
import {Drawer} from '@baidu/sui';

import monitorConfig from '@/pages/sanPages/utils/monitorConfig';
const {EniMetrics} = monitorConfig;

const {asComponent, invokeSUI, invokeSUIBIZ, template} = decorators;
const tpl = html`
    <template>
        <s-drawer open="{=open=}" direction="right" otherClose="{{false}}">
            <div slot="title" class="bcm-header">
                弹性网卡名称：{{name}}
                <a href="{{bcm}}" target="_blank" class="bcm-detail-link">查看更多&gt; </a>
            </div>
            <div class="monitor-wrap">
                <div s-for="item,index in chartConfig">
                    <bcm-chart-panel
                        withFilter="{{false}}"
                        scope="{{item.scope}}"
                        dimensions="{{item.dimensions}}"
                        statistics="{{item.statistics}}"
                        title="{{item.title}}"
                        time="{{item.time}}"
                        height="{{230}}"
                        width="{{350}}"
                        options="{{item.options}}"
                        api-type="metricName"
                        period="{{item.period}}"
                        metrics="{{item.metrics}}"
                        unit="{{item.unit}}"
                        bitUnit="{{item.bitUnit}}"
                        sdk="{{bcmSdk}}"
                    >
                    </bcm-chart-panel>
                </div>
            </div>
        </s-drawer>
    </template>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
class BcmDetail extends Component {
    components = {
        'bcm-chart-panel': BcmChartPanel,
        's-drawer': Drawer
    };

    initData() {
        return {
            name: '',
            open: true,
            chartConfig: [],
            bcmSdk: new BcmSDK({client: window.$http, context: window.$context})
        };
    }

    inited() {
        const payload = this.data.get('payload');
        this.data.set(
            'bcm',
            '#/vpc/eni/monitor?vpcId=' + payload.vpcId + '&eniId=' + payload.eniUuid + '&shortEniId=' + payload.eniId
        );
        this.data.get('type') === 'l2gw' &&
            this.data.set('bcm', '#/vpc/l2gw/monitor?vpcId=' + payload.vpcUuid + '&l2gwId=' + payload.id);
        this.initChart();
    }

    initChart() {
        let payload = this.data.get('payload');
        const metrics = u.pick(EniMetrics, ['bandwidth', 'flow']);
        let chartConfig = [];
        let options = {
            color: ['#2468f2', '#5FB333'],
            legend: {
                x: 'right',
                y: 'top'
            },
            dataZoom: {start: 0}
        };
        u.each(metrics, item => {
            let config = {
                scope: 'BCE_ENIC',
                time: '1h',
                period: 60,
                statistics: item.statistics || 'average',
                title: item.title,
                unit: item.unit,
                bitUnit: item.bitUnit,
                metrics: item.metrics,
                dimensions: `TapId:${payload.eniId}`,
                options
            };
            chartConfig.push(config);
        });
        this.data.set('chartConfig', chartConfig);
    }
}
export default Processor.autowireUnCheckCmpt(BcmDetail);
