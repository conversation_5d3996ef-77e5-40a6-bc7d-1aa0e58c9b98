import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import u from 'lodash';
import {OutlinedQuestionCircle} from '@baidu/sui-icon';

import './bind-instance.less';
import {PayType} from '@/pages/sanPages/common/enum';
import {utc2local} from '@/pages/sanPages/utils/mtools';
import {TIME_FORMAT} from '@/pages/sanPages/utils/constants';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';

const {asComponent, invokeSUI, invokeSUIBIZ, template} = decorators;
const AllRegion = window.$context.getEnum('AllRegion');
const tpl = html`
    <template>
        <s-dialog open="{{true}}" title="{{title || '绑定实例'}}" class="eip-bind-to-instance">
            <div class="bind-head">
                <div class="bind-head-left">
                    <span>挂载到：</span>
                    <s-select
                        datasource="{{serviceTypeDatasource}}"
                        value="{=type=}"
                        disabled="{{serviceDisabled}}"
                        width="{{180}}"
                        on-change="onServiceTypeChange"
                    ></s-select>
                </div>
                <div class="bind-head-right">
                    <s-select
                        datasource="{{keywordTypeDatasource}}"
                        value="{=keywordType=}"
                        width="{{100}}"
                        on-change="onSelectChange"
                    ></s-select>
                    <s-search placeholder="{{placeholder}}" value="{=keyword=}" on-search="onSearch"></s-search>
                </div>
            </div>
            <div class="eip-bind-table-wrap">
                <s-table
                    s-ref="table"
                    columns="{{table.schema}}"
                    datasource="{{table.datasource}}"
                    loading="{{table.loading}}"
                    selection="{{table.select}}"
                    on-selected-change="onTableRowSelected($event)"
                >
                    <div slot="empty">
                        <s-empty>
                            <div slot="action"></div>
                        </s-empty>
                    </div>
                    <div slot="c-resourceGroups">
                        <div s-if="row.resourceGroups && row.resourceGroups.length">
                            <p s-for="item in row.resourceGroups">
                                {{item.organizationName}}：{{item.resourceGroupName}}
                            </p>
                        </div>
                        <span s-else>-</span>
                    </div>
                    <div slot="h-time">
                        {{col.label}}
                        <s-tooltip content="过期时间是预付费实例过期时间，释放时间是后付费实例设置的自动释放时间">
                            <outlined-question-circle />
                        </s-tooltip>
                    </div>
                </s-table>
            </div>
            <div s-if="pager.total" class="eip-bind-pager-wrap">
                <s-pagination
                    s-if="{{pager.total}}"
                    layout="{{'total, pageSize, pager, go'}}"
                    pageSize="{{pager.size}}"
                    page="{{pager.page}}"
                    total="{{pager.total}}"
                    pageSizes="{{pageSizes}}"
                    on-pagerChange="onPagerChange($event)"
                    on-pagerSizeChange="onPagerSizeChange"
                />
            </div>
            <div slot="footer">
                <s-button on-click="close">取消</s-button>
                <s-button disabled="{{!hasSelected}}" skin="primary" on-click="dialogConfirm">确定</s-button>
            </div>
        </s-dialog>
    </template>
`;

@template(tpl)
@asComponent('@bind-instance')
@invokeSUI
@invokeSUIBIZ
class BindInstance extends Component {
    static components = {
        'outlined-question-circle': OutlinedQuestionCircle
    };
    static computed = {
        hasSelected() {
            let selectItem = this.data.get('selectItem');
            return selectItem !== '';
        }
    };

    initData() {
        return {
            selectItem: '',
            table: {
                loading: false,
                schema: [
                    {
                        name: 'id',
                        label: '实例名称/ID',
                        render(item) {
                            const name = item.vpnName || item.name;
                            if (
                                !item.id &&
                                !item.vpnId &&
                                !item.eniId &&
                                !item.podId &&
                                !item.instanceId &&
                                !item.shortId
                            ) {
                                return u.escape(name);
                            }
                            let ID =
                                item.instanceId || item.shortId || item.id || item.vpnId || item.eniId || item.podId;
                            return `<span title="${u.escape(name)}">${u.escape(name)}</span>
                                    <br>
                                    <span title="${u.escape(ID)}">${u.escape(ID)}</span>
                                `;
                        }
                    },
                    {
                        name: 'time',
                        label: '过期/释放时间',
                        render(item) {
                            const productType = item.payment || item.productType;
                            let time = null;
                            if (productType) {
                                // 预付费显示到期时间
                                if (productType === PayType.PREPAY) {
                                    // vpn是expiredTime
                                    time = item.expireTime || item.expiredTime;
                                    if (new Date(item) <= new Date()) {
                                        time = null;
                                    }
                                } else if (productType === PayType.POSTPAY) {
                                    time =
                                        item.releaseTime && new Date(item.releaseTime) > new Date()
                                            ? item.releaseTime
                                            : null;
                                }
                            } else if (item.releaseTime) {
                                time = new Date(item.releaseTime) > new Date() ? item.releaseTime : null;
                            } else {
                                time = item.expireTime || item.expiredTime;
                                if (new Date(item) <= new Date()) {
                                    time = null;
                                }
                            }
                            return time ? utc2local(time, TIME_FORMAT) : '-';
                        }
                    }
                ],
                datasource: [],
                select: {
                    mode: 'single',
                    selectedIndex: []
                }
            },
            pager: {
                size: 5,
                total: 0,
                page: 1
            },
            select: {
                value: '',
                datasource: []
            },
            pageSizes: [5, 10, 20, 50],
            type: 'BCC',
            serviceTypeDatasource: [
                {text: '云服务器（BCC）', value: 'BCC'},
                {text: '专属服务器（DCC）', value: 'DCC'},
                {text: '弹性裸金属服务器（BBC）', value: 'BBC'}
            ],
            keyword: '',
            keywordType: 'name',
            placeholder: '请输入实例名称进行搜索',
            keywordTypeDatasource: [
                {text: '实例名称', value: 'name'},
                {text: '实例ID', value: 'instanceId'}
            ]
        };
    }

    inited() {
        const currRegion = window.$context.getCurrentRegionId();
        const isShouldOpenHpas = [AllRegion.BJ, AllRegion.SU].includes(currRegion);
        if (isShouldOpenHpas) {
            this.data.push('serviceTypeDatasource', {text: '高性能应用服务（HPAS）', value: 'HPAS'});
        }
    }

    attached() {
        this.loadData();
    }

    loadData() {
        const listRequester = this.data.get('listRequester');
        const {page, size} = this.data.get('pager');
        const {keyword, keywordType, type} = this.data.get('');
        let query = {
            pageNo: page,
            pageSize: size,
            keyword,
            keywordType,
            type
        };
        if (listRequester) {
            this.data.set('table.loading', true);
            this.data.set('table.select', {
                mode: 'single',
                selectedIndex: []
            });
            this.data.set('table.datasource', []);
            this.data.set('serviceDisabled', true);
            return listRequester(query)
                .then(page => {
                    const formatedResult = page?.result?.map(item => {
                        return {...item, instanceUuid: item.instanceUuid || item.instanceId};
                    });
                    this.data.set('table.datasource', formatedResult);
                    this.data.set('pager.total', page.totalCount);
                    this.data.set('table.loading', false);
                    this.data.set('serviceDisabled', false);
                })
                .catch(() => {
                    this.data.set('table.loading', false);
                    this.data.set('serviceDisabled', false);
                });
        }
    }

    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.loadData();
    }

    onPagerSizeChange(e) {
        this.data.set('pager.page', 1);
        this.data.set('pager.size', e.value.pageSize);
        this.loadData();
    }

    onTableRowSelected(e) {
        const selectedItems = e.value.selectedItems;
        if (selectedItems[0]) {
            const item = selectedItems[0];
            this.data.set('selectItem', item);
        }
    }

    dialogConfirm() {
        let selectItem = this.data.get('selectItem');
        selectItem.type = this.data.get('type');
        this.fire('confirmed', selectItem);
        this.close();
    }

    close() {
        this.dispose();
    }
    onServiceTypeChange({value}) {
        this.data.set('type', value);
        this.data.set('pager.page', 1);
        this.loadData();
    }
    onSearch() {
        this.data.set('pager.page', 1);
        this.loadData();
    }
    onSelectChange({value}) {
        let placeholder = this.data.get('keywordTypeDatasource').find(item => item.value === value).text;
        this.data.set('keyword', '');
        this.data.set('placeholder', `请输入${placeholder}进行搜索`);
    }
}
export default Processor.autowireUnCheckCmpt(BindInstance);
