import {ContextService} from '@/pages/sanPages/common';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';

export default {
    createEni: [
        {
            required: false,
            message: ''
        },
        {
            custom(data, options) {
                if (options.quota && options.quota.free <= 0) {
                    const domains = window.$context.getDomains();
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: '弹性网卡配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `弹性网卡配额不足，如需增加配额请提交<a href="${
                                ContextService.Domains.ticket
                            }/#/ticket/create" target="_blank">工单</a>`
                        };
                    }
                }
            }
        }
    ],
    addIp: [
        {
            custom(data, options) {
                if (!options.hasQuota) {
                    const domains = window.$context.getDomains();
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: 'IP数配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `IP数配额不足，如需增加配额请提交<a href="${
                                ContextService.Domains.ticket
                            }/#/ticket/create" target="_blank">工单</a>`
                        };
                    }
                } else if (!options.subnetId) {
                    return {
                        disable: true,
                        message: '请先选择子网'
                    };
                }
            }
        }
    ],
    addEniIp: [
        {
            custom(data, options) {
                if (options.noStatus) {
                    return {
                        disable: true,
                        message: '绑定中或解绑中状态无法操作'
                    };
                } else if (options.noFree) {
                    const domains = window.$context.getDomains();
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: '弹性网卡辅助IP配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `弹性网卡辅助IP配额不足，如需增加配额请提交<a href="${
                                ContextService.Domains.ticket
                            }/#/ticket/create" target="_blank">工单</a>`
                        };
                    }
                }
            }
        }
    ],
    joinGroup: [
        {
            custom(data, options) {
                if (!options.data) {
                    return {
                        disable: true,
                        message: ''
                    };
                }
            }
        }
    ],
    addIpv6Ip: [
        {
            custom(data, options) {
                if (!options.hasIpv6Quota) {
                    const domains = window.$context.getDomains();
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: 'IP数配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `IP数配额不足，如需增加配额请提交<a href="${
                                ContextService.Domains.ticket
                            }/#/ticket/create" target="_blank">工单</a>`
                        };
                    }
                }
            }
        }
    ]
};
