import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {Notification} from '@baidu/sui';
import {OutlinedPlus} from '@baidu/sui-icon';
import {EipBindDialog} from '@baidu/bce-eip-sdk-san';
import {EipSDK} from '@baidu/bce-eip-sdk';

import '../../style/detail.less';
import {EniStatus} from '@/pages/sanPages/common/enum';
import rules from '../../rules';
import Confirm from '@/pages/sanPages/components/confirm';
import CreateEniIp from './create';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import testID from '@/testId';

const {invokeSUI, invokeSUIBIZ, template, invokeAppComp, invokeComp, asComponent} = decorators;
const tpl = html`
    <div>
        <s-app-detail-page class="{{klass}}">
            <div data-testid="${testID.eni.ipv6AddressDisplay}" class="ipv6-switch">
                <label class="cell-title">分配IPv6 IP：</label>
                <div class="switch_btn">
                    <s-tooltip trigger="{{addIpv6Disable.disable ? 'hover' : ''}}" placement="right">
                        <!--bca-disable-next-line-->
                        <div slot="content">{{addIpv6Disable.msg | raw}}</div>
                        <s-switch
                            checked="{=isIpv6Addr=}"
                            on-change="ipv6TypeSwitch($event)"
                            disabled="{{addIpv6Disable.disable}}"
                        />
                    </s-tooltip>
                </div>
            </div>
            <div s-if="{{subnetIpv6Cidr}}">
                <div class="eni-add-ip">
                    <s-tip-button skin="primary" disabled="{{instance.source !== 'default' ||  disabled}}" s-if="tip">
                        <span slot="content">
                            <!--bca-disable-next-line-->
                            {{tip | raw}}
                        </span>
                        <outlined-plus />
                        添加辅助IP
                    </s-tip-button>
                    <s-button skin="primary" on-click="onCreate" disabled="{{instance.source !== 'default'}}" s-else>
                        <outlined-plus />
                        添加辅助IP
                    </s-button>
                </div>
                <s-table
                    columns="{{table.columns}}"
                    loading="{{table.loading}}"
                    error="{{table.error}}"
                    selection="{{table.selection}}"
                    datasource="{{table.datasource}}"
                    on-selected-change="tableSelected($event)"
                    on-filter="onFilter"
                    on-sort="onSort"
                >
                    <div slot="empty">
                        <s-empty on-click="onCreate"> </s-empty>
                    </div>
                    <div slot="error">
                        啊呀，出错了
                        <s-button skin="stringfy" on-click="refresh">重新加载</s-button>
                    </div>
                    <div s-if="{{FLAG.NetworkSupportEip}}" slot="c-eip">
                        {{row.eip || '-'}}
                        <span s-if="eniStatus" class="{{instance | sourceEipClass}}">
                            <a s-if="row.eip" on-click="unbindEip(row)" href="javascript:void(0)">
                                <s-icon name="unbind" class="{{instance | sourceEipClass}}"></s-icon>
                            </a>
                            <a s-else on-click="bindEip(row)" href="javascript:void(0)">
                                <s-icon name="bind" class="{{instance | sourceEipClass}}"></s-icon>
                            </a>
                        </span>
                    </div>
                    <div slot="c-primary">
                        <span>{{'辅助IP'}}</span>
                    </div>
                    <div slot="c-operation">
                        <s-button skin="stringfy" s-if="isShowRelease(row)" on-click="onDelete($event, row)"
                            >释放</s-button
                        >
                    </div>
                </s-table>
                <s-pagination
                    class="eni-ip-footer"
                    s-if="{{pager.total}}"
                    layout="{{'total, pageSize, pager, go'}}"
                    pageSize="{{pager.pageSize}}"
                    total="{{pager.total}}"
                    page="{{pager.page}}"
                    on-pagerChange="onPagerChange($event)"
                    on-pagerSizeChange="onPagerSizeChange($event)"
                />
            </div>
        </s-app-detail-page>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class EniV6Detail extends Component {
    static components = {
        'outlined-plus': OutlinedPlus
    };
    static computed = {
        eniStatus() {
            const status = this.data.get('table.status');
            return u.indexOf([EniStatus.ATTACHING, EniStatus.DETACHING], status) === -1;
        }
    };
    static filters = {
        sourceEipClass(instance) {
            const {source, bindDeviceType} = instance || {};
            return source === 'default' && bindDeviceType !== 'HPAS' ? '' : 'sourceEipClass';
        }
    };
    initData() {
        return {
            klass: 'vpc-main-wrap vpc-eni-ip-wrap',
            table: {
                loading: false,
                error: null,
                datasource: [],
                columns: [
                    {
                        name: 'privateIp',
                        label: 'Ipv6IP'
                    },
                    {
                        name: 'eip',
                        label: '公网IP'
                    },
                    {
                        name: 'primary',
                        label: '类型'
                    },
                    {
                        name: 'operation',
                        label: '操作'
                    }
                ]
            },
            disabled: true,
            tip: ' ',
            pager: {
                pageSize: 10,
                page: 1,
                count: 0,
                datasource: [
                    {text: '10', value: 10},
                    {text: '20', value: 20},
                    {text: '50', value: 50},
                    {text: '100', value: 100}
                ]
            },
            FLAG,
            isIpv6Addr: '',
            subnetIpv6Cidr: '',
            limit: 40,
            addIpv6Disable: {
                disable: true,
                msg: ''
            },
            ipv6EipHas: {},
            urlQuery: getQueryParams()
        };
    }

    inited() {
        this.initTableColumns();
    }

    attached() {
        this.setLimitNum();
        this.loadDetail().then(async () => {
            await this.subnetIpv6();
            this.getEniIpQuota();
        });
        this.watch('table.datasource', datasource => {
            this.data.set('isIpv6Addr', datasource.length > 0);
        });
    }
    initTableColumns() {
        let tableColumns = this.data.get('table.columns');
        !FLAG.NetworkSupportEip &&
            this.data.set(
                'table.columns',
                tableColumns.filter(item => item.name !== 'eip')
            );
    }

    getEniIpList() {
        this.data.set('table.loading', true);
        const eniId = this.data.get('urlQuery.eniId');
        return this.$http
            .getEniIpv6List(
                eniId,
                {
                    pageNo: this.data.get('pager.page'),
                    pageSize: this.data.get('pager.pageSize')
                },
                {'x-silent-codes': ['NoSuchObject']}
            )
            .then(res => {
                this.data.set('subnetIpv6Cidr', res.totalCount > 0);
                this.data.set('isIpv6Addr', res.totalCount > 0);
                this.data.set('table.datasource', res.result);
                this.data.set('pager.page', res.pageNo);
                this.data.set('pager.total', res.totalCount);
                this.data.set('pager.pageSize', res.pageSize);
                this.data.set('table.loading', false);
                this.data.set('addIpv6Disable.disable', false);
            });
    }

    loadDetail() {
        return this.$http
            .getEniDetail({eniId: this.data.get('urlQuery.eniId')}, {'x-silent-codes': ['NoSuchObject']})
            .then(data => {
                this.data.set('instance', data);
                this.data.set('table.subnetType', data.subnetType);
                this.data.set('table.status', data.status);
            });
    }

    isShowRelease(row) {
        return u.indexOf([EniStatus.ATTACHING, EniStatus.DETACHING], row.status) === -1;
    }

    getEniIpQuota() {
        let instance = this.data.get('instance');
        const quota = this.data.get('limit');
        const noFree = quota <= 0;
        const noStatus = u.indexOf([EniStatus.ATTACHING, EniStatus.DETACHING], instance.status) > -1;
        let {addEniIp} = checker.check(rules, '', 'addEniIp', {noFree, noStatus});
        this.data.set('disabled', addEniIp.disable);
        if (addEniIp.message) {
            this.data.set('tip', addEniIp.message);
        } else {
            this.data.set('tip', '');
        }
        this.data.set('quota', quota);
    }

    onCreate() {
        const dialog = new CreateEniIp({
            data: {
                instance: this.data.get('instance'),
                ipv6Type: true
            }
        });
        dialog.on('create', () => {
            this.getEniIpQuota();
            this.refreshTable();
        });
        dialog.attach(document.body);
    }

    onDelete(e, row) {
        e.target && e.target.blur();
        const confirm = new Confirm({
            data: {
                content: '确认释放该辅助IP资源？（仅释放内网IP，绑定的公网IP将自动解绑）'
            }
        });
        confirm.on('confirm', () => {
            this.$http.deleteEniIp(this.data.get('urlQuery.eniId'), row.privateIp).then(() => this.refreshTable());
        });
        confirm.attach(document.body);
    }

    refreshTable() {
        this.getEniIpList();
    }

    onRegionChange() {
        location.hash = '#/vpc/eni/list';
    }

    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.data.set('table.selectedIndex', []);
        this.getEniIpList();
    }

    onPagerSizeChange(e) {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', e.value.pageSize);
        this.getEniIpList();
    }

    setLimitNum() {
        const whiteList = window.$storage.get('commonWhite');
        this.data.set('limit', whiteList?.eniAuxiliaryIpWhiteList ? 200 : 40);
    }

    ipv6TypeSwitch(e) {
        let isIpv6Addr = e.value;
        let instance = this.data.get('instance');
        let eniId = instance.eniUuid;
        if (!isIpv6Addr) {
            this.$http.getEniIpv6List(eniId).then(res => {
                const confirm = new Confirm({
                    data: {
                        content: '确认释放IPv6 IP？（仅释放内网IP，绑定的公网IP将自动解绑）'
                    }
                });
                let privateIps = res.result.map(item => {
                    return item.privateIp;
                });
                confirm.on('confirm', () => {
                    this.$http
                        .eniIpv6BatchDelete({
                            eniId: instance.eniId,
                            eniUuid: instance.eniUuid,
                            privateIps
                        })
                        .then(() => this.data.set('subnetIpv6Cidr', false))
                        .catch(e => {
                            this.data.set('isIpv6Addr', !isIpv6Addr);
                        });
                });
                confirm.attach(document.body);
                confirm.on('close', () => {
                    this.data.set('isIpv6Addr', true);
                });
            });
        } else {
            this.$http
                .createEniIp(this.data.get('urlQuery.eniId'), {
                    privateIp: '',
                    isIpv6Addr: true
                })
                .then(() => {
                    this.refreshTable();
                })
                .catch(() => {
                    this.data.set('table.loading', false);
                });
        }
    }
    subnetIpv6() {
        return this.$http
            .getSubnetDetail(this.data.get('instance').subnetId, {'x-silent-codes': ['Subnet.SubnetNotFoundException']})
            .then(res => {
                let showTable = res.subnets[0].ipv6Cidr.length > 0;
                this.data.set('subnetIpv6Cidr', showTable);
                if (showTable) {
                    this.getEniIpList();
                } else {
                    this.data.set('isIpv6Addr', false);
                    this.data.set('addIpv6Disable', {
                        disable: true,
                        msg: '所在子网暂未分配IPv6网段，请先给子网分配IPv6网段。'
                    });
                }
            });
    }
    bindEip(row) {
        const eniId = this.data.get('urlQuery.eniId');
        const dialog = new EipBindDialog({
            data: {
                instanceType: 'ENI',
                instanceId: this.data.get('instance').eniUuid,
                instanceIp: row.primary ? null : row.privateIp,
                EipSDK: new EipSDK({client: window.$http, context: window.$context}),
                listRequester: query => this.$http.getEipv6BindList(query),
                submitRequester: query =>
                    this.$http.eniEipBind(eniId, {
                        eip: query.eip,
                        privateIp: row.privateIp
                    })
            }
        });
        dialog.on('cancel', () => {
            dialog.dispose();
        });
        dialog.on('success', () => {
            Notification.success('绑定成功');
            dialog.dispose();
            this.refreshTable();
        });
        dialog.on('create', ({module, path, query}) => {
            redirect({module, path, params: query});
        });
        dialog.attach(document.body);
    }

    unbindEip(row) {
        const confirm = new Confirm({
            data: {
                content: `您确认解绑${row.eip}吗？`
            }
        });
        confirm.on('confirm', () => {
            this.$http
                .unbindEniIp(this.data.get('urlQuery.eniId'), {eip: row.eip, privateIp: row.privateIp})
                .then(() => this.refreshTable());
        });
        confirm.attach(document.body);
    }
}
export default Processor.autowireUnCheckCmpt(EniV6Detail);
