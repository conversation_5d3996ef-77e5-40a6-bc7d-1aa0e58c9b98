import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';

import {checkIsInSubnet} from '@/pages/sanPages/utils/common';
import RULE from '@/pages/sanPages/utils/rule';
import '../../style/create.less';

const {asComponent, invokeSUI, invokeSUIBIZ, template} = decorators;
const tpl = html`
    <div>
        <s-dialog class="vpc-eni-create" open="{{true}}" title="添加辅助IP">
            <s-form s-ref="form" data="{=formData=}" class="ip-item">
                <s-form-item label="网卡名称：">
                    <span class="text-item">{{instance.name || '-'}}</span>
                </s-form-item>
                <s-form-item label="所在子网：">
                    <span class="text-item">{{instance.subnetName}}（{{instance.subnetCidr}}）</span>
                </s-form-item>
                <s-form-item label="IP地址：" required class="radio_eni_group">
                    <s-radio-radio-group
                        class="ip-radio"
                        datasource="{{datasource}}"
                        value="{=formData.type=}"
                        on-change="typeChange($event, rowIndex)"
                    />
                    <s-input
                        s-if="formData.type === 'custom'"
                        value="{=formData.privateIp=}"
                        width="{{220}}"
                        placeholder="请输入IP地址"
                        on-input="inputChange($event, rowIndex)"
                    />
                    <label s-if="formErrors && formErrors.privateIp" class="invalid-label"
                        >{{formErrors.privateIp}}</label
                    >
                </s-form-item>
            </s-form>
            <div slot="footer">
                <s-button on-click="onClose">取消</s-button>
                <s-button skin="primary" disabled="{{disableSub}}" on-click="doSubmit">确定</s-button>
            </div>
        </s-dialog>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
class CreateEniIp extends Component {
    initData() {
        return {
            formData: {
                type: 'auto',
                privateIp: ''
            },
            formErrors: null,
            datasource: [
                {
                    value: 'auto',
                    text: '自动分配'
                },
                {
                    value: 'custom',
                    text: '指定'
                }
            ]
        };
    }

    typeChange(e) {
        if (e.value === 'auto') {
            this.data.set('formData.privateIp', '');
            this.data.set('formErrors.privateIp', '');
        }
    }

    doSubmit() {
        let formData = this.data.get('formData');
        const instance = this.data.get('instance');
        const ipv6Type = this.data.get('ipv6Type');
        if (formData.type === 'custom' && !ipv6Type) {
            if (!u.trim(formData.privateIp)) {
                this.data.set('formErrors.privateIp', '请填写指定的IP地址');
                return;
            }
            if (formData.privateIp && !RULE.IP.test(formData.privateIp)) {
                this.data.set('formErrors.privateIp', 'IP地址格式错误');
                return;
            }
            if (!checkIsInSubnet(formData.privateIp + '/32', instance.subnetCidr)) {
                this.data.set('formErrors.privateIp', 'IP地址不属于所在子网');
                return;
            }
        } else if (formData.type === 'custom' && ipv6Type) {
            if (formData.privateIp === '') {
                this.data.set('formErrors.privateIp', '请指定IPv6地址');
                return;
            }
            //  获得最后一个冒号后面的部分
            const lastStr = formData.privateIp && formData.privateIp.split(':').pop();
            const hasEmptySpace = formData.privateIp !== formData.privateIp.trim();
            if (!RULE.IPV6.test(formData.privateIp) || hasEmptySpace) {
                this.data.set('formErrors.privateIp', '请填写正确的IPv6格式');
                return;
            } else if (formData.privateIp[0].indexOf(':') > -1 && formData.privateIp[0].indexOf('.') > -1) {
                this.data.set('formErrors.privateIp', '请填写标准的IPv6格式');
                return;
            } else if (RULE.IP.test(lastStr)) {
                this.data.set('formErrors.privateIp', '不支持IPv6与IPv4混合');
                return;
            } else if (!checkIsInSubnet(formData.privateIp + '/128', instance.subnetIpv6Cidr)) {
                this.data.set('formErrors.privateIp', 'IP需在VPC分配的IPv6网段内');
                return;
            } else if (RULE.IPV6.test(formData.privateIp) && !lastStr) {
                this.data.set('formErrors.privateIp', '请填写标准的IPv6格式');
                return;
            }
        }
        this.data.set('disableSub', true);
        let payload = {
            privateIp: formData.privateIp
        };
        if (ipv6Type) {
            payload.isIpv6Addr = true;
        }
        this.$http
            .createEniIp(this.data.get('instance').eniUuid, payload)
            .then(() => {
                this.fire('create');
                this.dispose();
                this.data.set('disableSub', false);
            })
            .catch(() => this.data.set('disableSub', false));
    }

    onClose() {
        this.dispose();
    }
}
export default Processor.autowireUnCheckCmpt(CreateEniIp);
