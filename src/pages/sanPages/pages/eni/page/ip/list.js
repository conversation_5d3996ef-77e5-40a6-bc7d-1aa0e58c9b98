import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {EipBindDialog} from '@baidu/bce-eip-sdk-san';
import {EipSDK} from '@baidu/bce-eip-sdk';
import {Notification} from '@baidu/sui';
import {OutlinedPlus} from '@baidu/sui-icon';

import {EniStatus, ServiceType} from '@/pages/sanPages/common/enum';
import rules from '../../rules';
import Confirm from '@/pages/sanPages/components/confirm';
import CreateEniIp from './create';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import testID from '@/testId';
import '../../style/detail.less';

const {invokeSUI, invokeSUIBIZ, template, invokeAppComp, invokeComp, asComponent} = decorators;
const tpl = html`
    <div>
        <s-app-detail-page class="{{klass}}">
            <div class="eni-add-ip">
                <s-tip-button skin="primary" disabled="{{instance.source !== 'default' ||  disabled}}" s-if="tip">
                    <span slot="content">
                        <!--bca-disable-next-line-->
                        {{tip | raw}}
                    </span>
                    <outlined-plus />
                    添加辅助IP
                </s-tip-button>
                <s-button
                    data-test-id="${testID.eni.ipAddAssistIp}"
                    skin="primary"
                    on-click="onCreate"
                    disabled="{{instance.source !== 'default'}}"
                    s-else
                >
                    <outlined-plus />
                    添加辅助IP
                </s-button>
            </div>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                selection="{{table.selection}}"
                datasource="{{table.datasource}}"
                on-selected-change="tableSelected($event)"
                data-test-id="${testID.eni.ipAddress}"
                on-filter="onFilter"
                on-sort="onSort"
            >
                <div slot="empty">
                    <s-empty on-click="onCreate"> </s-empty>
                </div>
                <div slot="error">
                    啊呀，出错了
                    <s-button skin="stringfy" on-click="refresh">重新加载</s-button>
                </div>
                <div s-if="{{FLAG.NetworkSupportEip}}" slot="c-eip">
                    {{row.eip || '-'}}
                    <span s-if="!natSubnet && eniStatus" class="{{instance | sourceEipClass}}">
                        <a
                            s-if="row.eip"
                            data-testid="${testID.eni.ipUnbindEip}"
                            on-click="unbindEip(row)"
                            href="javascript:void(0)"
                        >
                            <s-icon name="unbind" class="{{instance | sourceEipClass}}"></s-icon>
                        </a>
                        <a
                            s-else
                            data-testid="${testID.eni.ipBindEip}"
                            on-click="bindEip(row)"
                            href="javascript:void(0)"
                        >
                            <s-icon name="bind" class="{{instance | sourceEipClass}}"></s-icon>
                        </a>
                    </span>
                </div>
                <div slot="c-primary">
                    <span>{{row.primary ? '主IP' : '辅助IP'}}</span>
                </div>
                <div slot="c-operation">
                    <s-button skin="stringfy" s-if="isShowRelease(row)" on-click="onDelete($event, row)">释放</s-button>
                </div>
            </s-table>
            <s-pagination
                class="eni-ip-footer"
                s-if="{{pager.total}}"
                layout="{{'total, pageSize, pager, go'}}"
                pageSize="{{pager.pageSize}}"
                total="{{pager.total}}"
                page="{{pager.page}}"
                on-pagerChange="onPagerChange($event)"
                on-pagerSizeChange="onPagerSizeChange($event)"
            />
        </s-app-detail-page>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class EniDetail extends Component {
    static components = {
        'outlined-plus': OutlinedPlus
    };
    initData() {
        return {
            klass: 'vpc-main-wrap vpc-eni-ip-wrap',
            table: {
                loading: false,
                error: null,
                datasource: [],
                columns: [
                    {
                        name: 'privateIp',
                        label: '内网IP'
                    },
                    {
                        name: 'eip',
                        label: '公网IP'
                    },
                    {
                        name: 'primary',
                        label: '类型'
                    },
                    {
                        name: 'operation',
                        label: '操作'
                    }
                ]
            },
            disabled: true,
            tip: ' ',
            pager: {
                pageSize: 10,
                page: 1,
                count: 0,
                datasource: [
                    {text: '10', value: 10},
                    {text: '20', value: 20},
                    {text: '50', value: 50},
                    {text: '100', value: 100}
                ]
            },
            FLAG,
            limit: 40,
            urlQuery: getQueryParams()
        };
    }

    filters = {
        sourceEipClass(instance) {
            const {source, bindDeviceType} = instance || {};
            return source === 'default' && bindDeviceType !== 'HPAS' ? '' : 'sourceEipClass';
        }
    };

    static computed = {
        natSubnet() {
            const subnetType = this.data.get('table.subnetType');
            return subnetType === ServiceType.NAT || subnetType === ServiceType.BBC_NAT;
        },
        eniStatus() {
            const status = this.data.get('table.status');
            return u.indexOf([EniStatus.ATTACHING, EniStatus.DETACHING], status) === -1;
        }
    };

    inited() {
        this.getEniIpList();
        this.initTableColumns();
    }

    attached() {
        if (FLAG.NetworkEniOpt) {
            this.setLimitNum();
        }
        this.loadDetail().then(() => {
            this.getEniIpQuota();
        });
    }
    initTableColumns() {
        let tableColumns = this.data.get('table.columns');
        !FLAG.NetworkSupportEip &&
            this.data.set(
                'table.columns',
                tableColumns.filter(item => item.name !== 'eip')
            );
    }

    getEniIpList() {
        const eniId = this.data.get('urlQuery.eniId');
        if (eniId) {
            this.data.set('table.loading', true);
            this.$http
                .getEniIpList(
                    eniId,
                    {
                        pageNo: this.data.get('pager.page'),
                        pageSize: this.data.get('pager.pageSize')
                    },
                    {'x-silent-codes': ['EniNotExistException']}
                )
                .then(data => {
                    this.data.set('table.datasource', data.result);
                    this.data.set('pager.page', data.pageNo);
                    this.data.set('pager.total', data.totalCount);
                    this.data.set('pager.pageSize', data.pageSize);
                    this.data.set('table.loading', false);
                });
        }
    }

    loadDetail() {
        return this.$http
            .getEniDetail({eniId: this.data.get('urlQuery.eniId')}, {'x-silent-codes': ['NoSuchObject']})
            .then(data => {
                this.data.set('instance', data);
                this.data.set('table.subnetType', data.subnetType);
                this.data.set('table.status', data.status);
            });
    }
    isShowRelease(row) {
        return !row.primary && u.indexOf([EniStatus.ATTACHING, EniStatus.DETACHING], row.status) === -1;
    }

    getEniIpQuota() {
        let instance = this.data.get('instance');
        return this.$http
            .getSubnetDetail(instance.subnetId, {'x-silent-codes': ['Subnet.SubnetNotFoundException']})
            .then(data => {
                const item = data.subnets ? data.subnets[0] : {totalIps: 0, usedIps: 0};
                const total = item.totalIps - item.usedIps;
                const quota = Math.min(total, this.data.get('limit'));
                const noFree = quota <= 0;
                const noStatus = u.indexOf([EniStatus.ATTACHING, EniStatus.DETACHING], instance.status) > -1;
                let {addEniIp} = checker.check(rules, '', 'addEniIp', {noFree, noStatus});
                this.data.set('disabled', addEniIp.disable);
                if (addEniIp.message) {
                    this.data.set('tip', addEniIp.message);
                } else {
                    this.data.set('tip', '');
                }
                this.data.set('quota', quota);
            });
    }

    onCreate() {
        const dialog = new CreateEniIp({
            data: {
                instance: this.data.get('instance'),
                ipv6Type: false
            }
        });
        dialog.on('create', () => {
            this.getEniIpQuota();
            this.refreshTable();
        });
        dialog.attach(document.body);
    }

    bindEip(row) {
        const eniId = this.data.get('urlQuery.eniId');
        const dialog = new EipBindDialog({
            data: {
                instanceType: 'ENI',
                instanceId: this.data.get('instance').eniUuid,
                instanceIp: row.primary ? null : row.privateIp,
                EipSDK: new EipSDK({client: window.$http, context: window.$context}),
                listRequester: query => this.$http.getEipBindList(query),
                submitRequester: query =>
                    this.$http.eniEipBind(eniId, {
                        eip: query.eip,
                        privateIp: row.privateIp
                    })
            }
        });
        dialog.on('cancel', () => {
            dialog.dispose();
        });
        dialog.on('success', () => {
            Notification.success('绑定成功');
            dialog.dispose();
            this.refreshTable();
        });
        dialog.on('create', ({module, path, query}) => {
            redirect({module, path, params: query});
        });
        dialog.attach(document.body);
    }

    unbindEip(row) {
        const confirm = new Confirm({
            data: {
                content: `您确认解绑${row.eip}吗？`
            }
        });
        confirm.on('confirm', () => {
            this.$http
                .unbindEniIp(this.data.get('urlQuery.eniId'), {eip: row.eip, privateIp: row.privateIp})
                .then(() => this.refreshTable());
        });
        confirm.attach(document.body);
    }

    onDelete(e, row) {
        e.target && e.target.blur();
        const confirm = new Confirm({
            data: {
                content: '确认释放该辅助IP资源？（仅释放内网IP）'
            }
        });
        confirm.on('confirm', () => {
            this.$http.deleteEniIp(this.data.get('urlQuery.eniId'), row.privateIp).then(() => this.refreshTable());
        });
        confirm.attach(document.body);
    }

    refreshTable() {
        this.getEniIpList();
    }

    onRegionChange() {
        location.hash = '#/vpc/eni/list';
    }

    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.data.set('table.selectedIndex', []);
        this.getEniIpList();
    }

    onPagerSizeChange(e) {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', e.value.pageSize);
        this.getEniIpList();
    }

    setLimitNum() {
        const whiteList = window.$storage.get('commonWhite');
        this.data.set('limit', whiteList?.eniAuxiliaryIpWhiteList ? 200 : 40);
    }
}
export default Processor.autowireUnCheckCmpt(EniDetail);
