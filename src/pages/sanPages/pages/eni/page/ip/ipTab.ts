import _ from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor, redirect} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import EniIpv6Detail from './ipv6List';
import EniIpv4Detail from './list';

import {EniStatus} from '@/pages/sanPages/common/enum';
import '../../style/detail.less';

const {invokeComp, withSidebar, invokeSUI, invokeSUIBIZ, invokeAppComp, template} = decorators;

const tpl = html`
    <template>
        <div class="ip-detail-tab-header">
            <s-app-list-page class="s-tab-list-page">
                <!--<div slot="pageTitle" class="vpn-detail-header vpn-common-header">
                    <span class="instance-name">{{instance.name}}</span>
                    <span class="{{instance.status | statusStyle}}" s-if="instance.status"
                        >{{instance.status | statusText}}</span
                    >
                </div>-->
                <div class="ip-tab-header">
                    <div class="custom-tab-wrapper">
                        <span
                            class="{{selectedKey === item.key ? 'actived' : ''}}"
                            s-for="item, index in tabs"
                            key="{{item.key}}"
                            on-click="handleClick(item)"
                            data-testid="tab-nav-{{index+1}}"
                            >{{item.label}}</span
                        >
                    </div>
                    <eni-ipv4-detail s-if="selectedKey === 'IPv4'" route="{{route}}"></eni-ipv4-detail>
                    <eni-ipv6-detail s-else route="{{route}}"></eni-ipv6-detail>
                </div>
            </s-app-list-page>
        </div>
    </template>
`;
@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class EniIpDetail extends Component {
    static components = {
        'eni-ipv6-detail': EniIpv6Detail,
        'eni-ipv4-detail': EniIpv4Detail
    };
    filters = {
        statusStyle(value) {
            return EniStatus.fromValue(value).styleClass || '';
        },
        statusText(value) {
            return value ? EniStatus.getTextFromValue(value) : '-';
        }
    };
    initData() {
        return {
            route: {
                query: {
                    vpcId: '',
                    eniId: '',
                    shortEniId: ''
                }
            },
            selectedKey: 'IPv4',
            tabs: [
                {label: 'IPv4地址', key: 'IPv4'},
                {label: 'IPv6地址', key: 'IPv6'}
            ],
            urlQuery: getQueryParams()
        };
    }
    inited() {
        const {vpcId, eniId, shortEniId} = this.data.get('context');
        this.data.set('route', {query: {vpcId, eniId, shortEniId}});
        this.initTabPane();
        this.loadDetail();
    }
    initTabPane() {
        const hash = window.location.hash;
        if (hash.startsWith('#/vpc/eni/ipv6')) {
            this.data.set('selectedKey', 'IPv6');
        } else {
            this.data.set('selectedKey', 'IPv4');
        }
    }
    handleClick(item: any) {
        const {key} = item;
        this.data.set('selectedKey', key);
        const query = location?.hash?.split('?')?.[1] || '';
        const tabUrlMapping = {
            IPv4: '#/vpc/eni/ip',
            IPv6: '#/vpc/eni/ipv6'
        };
        window.location.hash = tabUrlMapping[key] + '?' + query;
    }
    onRegionChange() {
        location.reload();
    }
    loadDetail() {
        return this.$http
            .getEniDetail({eniId: this.data.get('urlQuery.eniId')}, {'x-silent-codes': ['NoSuchObject']})
            .then(data => {
                this.data.set('instance', data);
            });
    }
    onBack() {
        location.hash = '#/vpc/eni/list';
    }
}
export default San2React(Processor.autowireUnCheckCmpt(EniIpDetail));
