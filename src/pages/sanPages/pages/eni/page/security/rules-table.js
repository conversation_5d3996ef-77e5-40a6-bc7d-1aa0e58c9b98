import u from 'lodash';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {Component} from 'san';

import {SecurityIpVersion as IpVersion} from '@/pages/sanPages/common/enum';
import normalRule from '../../../security/util/rule';
import enterpriseRule from '../../../enterpriseSecurity/util/rule';

const ALL = 'all';
const RANGE = '1-65535';
const {asComponent, invokeSUI, invokeSUIBIZ, invokeAppComp, template} = decorators;

const INGRESS = [
    {name: 'priority', label: '优先级', width: 100},
    {name: 'protocol', label: '协议', width: 100},
    {name: 'portRange', label: '目的端口', width: 100},
    {name: 'ethertype', label: '源IP', width: 100},
    {name: 'action', label: '策略', width: 100},
    {name: 'direction', label: '描述', width: 100},
    {name: 'remark', label: '备注', width: 100}
];

const EGRESS = [
    {name: 'priority', label: '优先级', width: 100},
    {name: 'protocol', label: '协议', width: 100},
    {name: 'portRange', label: '目的端口', width: 100},
    {name: 'ethertype', label: '目的IP', width: 100},
    {name: 'action', label: '策略', width: 100},
    {name: 'direction', label: '描述', width: 100},
    {name: 'remark', label: '备注', width: 100}
];

const tpl = html`
    <template>
        <s-radio-radio-group
            s-if="!isSingle"
            class="sub-margin"
            on-change="typeChange($event)"
            value="{=directType=}"
            radioType="button"
            datasource="{{directSource}}"
        />
        <s-table
            s-ref="table"
            columns="{{table.columns[directType]}}"
            loading="{{table.loading}}"
            datasource="{{table.datasource | sortByPriority}}"
        >
            <div slot="empty">
                <s-empty>
                    <div slot="action"></div>
                </s-empty>
            </div>
            <div slot="c-ethertype">{{row | ethertype}}</div>
            <span slot="c-protocol">{{row.protocol | protocol}}</span>
            <span slot="c-portRange">{{row.portRange | portRange}}</span>
            <span slot="c-action">{{row | action}}</span>
            <span slot="c-direction">{{row.name}}</span>
            <span slot="c-localPortRange">{{row | localPortRange}}</span>
            <span slot="c-localIp">{{row | localIp}}</span>
        </s-table>
    </template>
`;

@asComponent('@rules-table')
@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class RulesTable extends Component {
    initData() {
        return {
            directType: 'ingress',
            directSource: [
                {value: 'ingress', text: '入站'},
                {value: 'egress', text: '出站'}
            ],
            table: {
                columns: {
                    ingress: INGRESS,
                    egress: EGRESS
                },
                loading: false,
                datasource: [],
                allSecurityList: {}
            },
            ingressTableColums: [],
            egressTableColums: []
        };
    }

    inited() {
        this.watch('isBindEsg', isBindEsg => {
            this.initTableColumns(isBindEsg);
        });
        this.initTableColumns();
        const loading = this.data.get('loading');
        this.data.set('table.loading', !!loading);
        this.setData();
    }

    static filters = {
        protocol(data) {
            if (!data) {
                return ALL;
            }
            return data;
        },
        portRange(data) {
            if (!data) {
                return RANGE;
            }
            return data;
        },
        ethertype(row) {
            if (row.ipCollectionType) {
                if (row.ipCollectionType === 1) {
                    return 'IP地址组：' + row.ipCollectionId;
                }
                return 'IP地址族：' + row.ipCollectionId;
            }
            if (row.source === 'system') {
                return (
                    '安全组：' +
                    u.escape(row.remoteGroupId) +
                    (row.ethertype ? '(' + u.escape(row.ethertype) + ')' : '')
                );
            }
            return (
                (this.data.get('directType') === 'ingress' ? '源IP：' : '目的IP：') +
                (row.remoteIP === ALL
                    ? row.ethertype === IpVersion.IPV4
                        ? '0.0.0.0/0'
                        : '::/0'
                    : u.escape(row.remoteIP))
            );
        },
        action(row) {
            return row.action === 'allow' ? '允许' : '拒绝';
        },
        sortByPriority(rules) {
            const isSingle = this.data.get('isSingle');
            const isBindEsg = this.data.get('isBindEsg');
            return isBindEsg && !isSingle
                ? u.sortBy(rules, el => {
                      return el.action === 'allow' ? 2 * +el.priority : 2 * +el.priority - 1;
                  })
                : rules;
        },
        localPortRange(row) {
            if (row.protocol === 'all' || row.protocol === 'icmp') {
                return '不涉及';
            }
            if (!row.localPortRange) {
                return '1-65535';
            }
            return u.escape(row.localPortRange);
        },
        localIp(row) {
            return (
                (this.data.get('directType') === 'ingress' ? '目的IP：' : '源IP：') +
                (row.localIp === 'all'
                    ? row.ethertype === IpVersion.IPV4
                        ? '0.0.0.0/0'
                        : '::/0'
                    : u.escape(row.localIp))
            );
        }
    };

    initTableColumns(value) {
        let isBindEsg = value || this.data.get('isBindEsg');
        let directType = this.data.get('directType');
        this.data.set('ingressTableColums', INGRESS);
        this.data.set('egressTableColums', EGRESS);
        if (isBindEsg) {
            this.data.splice('ingressTableColums', [3, 0, {name: 'localIp', label: '目的IP', width: 100}]);
            this.data.splice('ingressTableColums', [4, 0, {name: 'localPortRange', label: '源端口', width: 100}]);
            this.data.splice('egressTableColums', [2, 0, {name: 'localPortRange', label: '源端口', width: 100}]);
            this.data.splice('egressTableColums', [3, 0, {name: 'localIp', label: '源IP', width: 100}]);
        }
        let tableColumns =
            directType === 'ingress' ? this.data.get('ingressTableColums') : this.data.get('egressTableColums');
        if (!isBindEsg) {
            tableColumns = tableColumns.filter(item => {
                return item.name !== 'action' && item.name !== 'priority' && item.name !== 'remark';
            });
        } else {
            tableColumns = tableColumns.filter(item => {
                return item.name !== 'direction';
            });
        }
        this.data.set(`table.columns.${directType}`, tableColumns);
    }

    typeChange({value}) {
        this.nextTick(() => {
            this.initTableColumns();
        });
        const allSecurityList = this.data.get('allSecurityList');
        this.data.set('table.datasource', allSecurityList[value]);
    }

    setData() {
        const activeRules = this.data.get('rules') || [];
        const rules = this.getRules(activeRules);
        const directType = this.data.get('directType');
        const source = {
            ingress: rules.ingressRules,
            egress: rules.egressRules
        };
        this.data.set('allSecurityList', source);
        this.typeChange({value: directType});
    }

    appendRule(collection, rule) {
        // 检查是否存在
        for (let i = 0; i < collection.length; i++) {
            // 严格匹配，也就是检查 source 和 ip
            if (rule.equal(collection[i], true)) {
                return;
            }
        }
        collection.push(rule);
    }

    getRules(activeRules) {
        let rules = {
            ingressRules: [],
            egressRules: []
        };
        let Rule = this.data.get('isBindEsg') ? enterpriseRule : normalRule;
        u.each(activeRules, json => {
            let rule = Rule.fromJSON(json);
            rule.ipCollectionType = json.ipCollectionType;
            rule.ipCollectionUuid = json.ipCollectionUuid;
            rule.ipCollectionId = json.ipCollectionId;
            if (json.direction === 'egress') {
                this.appendRule(rules.egressRules, rule);
            } else if (json.direction === 'ingress') {
                this.appendRule(rules.ingressRules, rule);
            }
        });

        return rules;
    }
}
export default Processor.autowireUnCheckCmpt(RulesTable);
