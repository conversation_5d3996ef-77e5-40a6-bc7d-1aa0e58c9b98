/**
 * @file network/eni/pages/List.js
 * <AUTHOR>
 */

import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {BccSDK} from '@baidu/bce-bcc-sdk';
import {SecurityBindDialog} from '@baidu/bce-bcc-sdk-san';
import {SecurityTypes} from '@baidu/bce-bcc-sdk-enum';
import {router} from 'san-router';

import rules from '../../rules';
import Confirm from '@/pages/sanPages/components/confirm';
import {EniStatus, EndpointStatus} from '@/pages/sanPages/common/enum';
import '../../style/security.less';
import {getVpcName} from '@/pages/sanPages/utils/common';
import RulesTable from './rules-table';
import testID from '@/testId';
import '../../style/security.less';

const typeApi = {
    eni: {
        getGroup: 'getSecurityGroups',
        getDetail: 'getEniDetail',
        joinGroup: 'eniSecurityJoin',
        unbind: 'eniSecurityQuit'
    },
    endpoint: {
        getGroup: 'getEndpointSecurity',
        getDetail: 'getEndpointDetail',
        joinGroup: 'endpointBatchBindSecurity',
        unbind: 'unbindEndpointSecurity'
    }
};
const esgTypeApi = {
    eni: {
        getGroup: 'getEnterpriseSecurityGroups',
        joinGroup: 'bindEnterpriseSecurityGroups',
        unbind: 'enterpriseSecurityBatchUnbindInstance'
    },
    endpoint: {
        getGroup: 'getEnterpriseSecurityGroups',
        joinGroup: 'bindEnterpriseSecurityGroups',
        unbind: 'enterpriseSecurityBatchUnbindInstance'
    }
};
const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, withSidebar, invokeComp} = decorators;
const tpl = html`
    <div>
        <s-app-detail-page class="{{klass}}">
            <div slot="pageTitle" class="vpn-detail-header vpn-common-header">
                <span class="instance-name">{{instance.name}}</span>
                <span class="{{instance.status | statusStyle}}" s-if="instance.status"
                    >{{instance.status | statusText}}</span
                >
            </div>
            <div data-testid="${testID.eni.securityGroupRule}" class="detail-content">
                <h2>当前应用的安全组规则</h2>
                <rules-table s-ref="rules" rules="{{activeRules}}" loading="{{false}}" isBindEsg="{{isBindEsg}}" />
            </div>
            <div data-testid="${testID.eni.securityGroupList}" class="detail-content top_class">
                <h2 class="security_class_h2">关联的安全组列表</h2>
                <s-tip-button
                    class="sub-margin"
                    isDisabledVisibile="{{true}}"
                    disabled="{{disableGroup.disable || (type === 'eni' && instance.source !== 'default') }}"
                    on-click="joinGroup"
                    trigger="{{disableGroup.disable && disableGroup.tip ? 'hover' : ''}}"
                    data-test-id="${testID.eni.securityAssociate}"
                >
                    <span slot="content">
                        <!--bca-disable-next-line-->
                        {{disableGroup.tip | raw}}
                    </span>
                    关联安全组
                </s-tip-button>
                <s-table
                    s-ref="table"
                    columns="{{group.columns}}"
                    loading="{{group.loading}}"
                    datasource="{{group.datasource}}"
                    has-Expand-Row="{{true}}"
                    expandedIndex="{=group.expandedIndex=}"
                    on-exprow-collapse="onRowCollapse($event)"
                    on-exprow-expand="onRowExpand($event)"
                >
                    <div slot="empty">
                        <s-empty>
                            <div slot="action"></div>
                        </s-empty>
                    </div>
                    <div slot="expanded-row">
                        <div class="subrow-content-row">
                            <div>
                                <p class="subrow-content-row-title">入站规则</p>
                                <rules-table
                                    isSingle="{{true}}"
                                    s-ref="rulesIngress"
                                    rules="{{row.rules}}"
                                    directType="ingress"
                                    isBindEsg="{{isBindEsg}}"
                                />
                            </div>
                            <div>
                                <p class="subrow-content-row-title">出站规则</p>
                                <rules-table
                                    isSingle="{{true}}"
                                    s-ref="rulesEgress"
                                    rules="{{row.rules}}"
                                    directType="egress"
                                    isBindEsg="{{isBindEsg}}"
                                />
                            </div>
                        </div>
                    </div>
                    <span slot="c-vpc">{{row | getVpcName}}</span>
                    <div slot="c-securityType">
                        <span>{{isBindEsg ? '企业安全组' : '普通安全组'}}</span>
                    </div>
                    <div slot="c-name">
                        <a
                            s-if="row.vpcId && row.securityGroupId && row.id"
                            href="#/vpc/security/detail?id={{row.id}}&vpcId={{row.vpcId}}&securityGroupId={{row.securityGroupId}}"
                            >{{row.name}}</a
                        >
                        <a s-elif="row.esgUuid" href="#/vpc/enterpriseSecurity/detail?id={{row.esgUuid}}">
                            {{row.name}}
                        </a>
                        <span s-else>{{row.name}}</span>
                    </div>
                    <div slot="c-operation">
                        <s-button s-if="group.datasource.length > 1" skin="stringfy" on-click="unbindSecurity(row)"
                            >取消关联</s-button
                        >
                    </div>
                </s-table>
            </div>
        </s-app-detail-page>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class EniSecurity extends Component {
    static components = {
        'rules-table': RulesTable
    };

    static computed = {
        type() {
            if (/^\#\/vpc\/eni/.test(location.hash)) {
                return 'eni';
            }
            if (/^\#\/vpc\/endpoint/.test(location.hash)) {
                return 'endpoint';
            }
        }
    };

    initData() {
        return {
            klass: 'vpc-main-wrap vpc-eni-security',
            group: {
                loading: true,
                datasource: [],
                columns: [
                    {name: 'name', label: '安全组名称'},
                    {name: 'securityType', label: '安全组类型'},
                    {name: 'vpc', label: '所在网络'},
                    {name: 'desc', label: '描述'},
                    {name: 'operation', label: '操作'}
                ],
                expandedIndex: []
            },
            disableGroup: {
                disable: true,
                tip: ''
            },
            isBindEsg: false
        };
    }

    attached() {
        this.loadDetail();
        this.loadEniDetail();
    }

    checkEniSecurityType() {
        let type = this.data.get('type');
        let instanceUuid = '';
        type === 'eni'
            ? (instanceUuid = this.data.get('context').eniId)
            : (instanceUuid = this.data.get('context').shortId);
        return this.$http
            .checkBindSecurity({instanceUuid})
            .then(res => {
                this.data.set('isBindEsg', res.bindEsg);
                if (res.bindEsg) {
                    let col = this.data.get('group.columns');
                    this.data.set(
                        'group.columns',
                        col.filter(item => item.name !== 'vpc')
                    );
                }
            })
            .catch(() => {
                this.data.set('isBindEsg', false);
            });
    }

    setInitData() {
        const {eniId, shortId} = this.data.get('context');
        this.data.set('id', eniId || shortId);
    }

    filters = {
        statusStyle(value) {
            const type = this.data.get('type');
            return type === 'eni'
                ? EniStatus.fromValue(value).styleClass || ''
                : EndpointStatus.fromValue(value).styleClass || '';
        },
        statusText(value) {
            const type = this.data.get('type');
            return value
                ? type === 'eni'
                    ? EniStatus.getTextFromValue(value)
                    : EndpointStatus.getTextFromValue(value)
                : '';
        },
        getVpcName(row) {
            return getVpcName(row.vpcName);
        }
    };

    getPayload(condition, row = {}) {
        const type = this.data.get('type');
        let payload = {};
        const eniId = this.data.get('context').eniId;
        const shortId = this.data.get('context').shortId;
        if (condition === 'getGroup') {
            type === 'eni' && (payload.eniId = eniId);
            type === 'endpoint' && (payload.instanceId = shortId);
            type === 'endpoint' && (payload.instanceType = 'SNIC');
        } else if (condition === 'getDetail') {
            type === 'eni' && (payload.eniId = eniId);
            type === 'endpoint' && (payload.endpointId = shortId);
        } else if (condition === 'joinGroup') {
            type === 'eni' && (payload.instanceIds = [this.data.get('instance.eniUuid')]);
            type === 'endpoint' && (payload.instanceUuid = this.data.get('instance.shortId'));
            type === 'endpoint' && (payload.instanceType = 'SNIC');
        } else if (condition === 'unbind') {
            if (type === 'eni') {
                payload.instanceId = eniId;
                payload.securityGroupId = row.id;
            } else {
                payload.instanceUuid = shortId;
                payload.instanceType = 'SNIC';
                payload.securityGroupUuids = [row.id];
            }
        }
        return payload;
    }

    getEsgPayload(condition, row = {}) {
        let payload = {};
        let type = this.data.get('type');
        const eniId = this.data.get('context').eniId;
        const shortId = this.data.get('context').shortId;
        const vpcId = this.data.get('context').vpcId;
        if (condition === 'getGroup') {
            payload.instanceUuid = type === 'eni' ? eniId : shortId;
        } else if (condition === 'joinGroup') {
            payload.instances = [
                {
                    instanceUuid: type === 'eni' ? this.data.get('instance.eniUuid') : shortId,
                    vpcUuid: vpcId
                }
            ];
            payload.instanceType = type === 'eni' ? 'ENI' : 'SNIC';
        } else if (condition === 'unbind') {
            payload.instanceUuids = type === 'eni' ? [eniId] : [shortId];
            payload.esgUuids = [row.esgUuid];
            payload.instanceType = type === 'eni' ? 'ENI' : 'SNIC';
        }

        return payload;
    }

    async loadDetail() {
        this.data.set('group.loading', true);
        await this.checkEniSecurityType();
        const type = this.data.get('type');
        if (this.data.get('isBindEsg')) {
            // 企业安全组逻辑
            this.$http[esgTypeApi[type].getGroup](this.getEsgPayload('getGroup')).then(data => {
                this.data.set('activeRules', data.activeEsgRules);
                this.data.set('group.datasource', data.enterpriseSecurityGroups);
                this.data.set('group.loading', false);
                this.data.set('isCanBind', true);
                this.nextTick(() => {
                    const rulesRef = this.ref('rules');
                    rulesRef?.setData();
                    rulesRef?.data.set('table.loading', false);
                });
            });
        } else {
            return this.$http[typeApi[type].getGroup](this.getPayload('getGroup')).then(data => {
                this.data.set('activeRules', data.activeRules);
                this.data.set('group.datasource', data.securityGroups);
                this.data.set('group.loading', false);
                this.data.set('isCanBind', true);
                this.nextTick(() => {
                    const rulesRef = this.ref('rules');
                    rulesRef?.setData();
                    rulesRef?.data.set('table.loading', false);
                });
            });
        }
    }

    loadEniDetail() {
        const type = this.data.get('type');
        return this.$http[typeApi[type].getDetail](this.getPayload('getDetail'), {
            'x-silent-codes': ['NoSuchObject']
        }).then(data => {
            let {joinGroup} = checker.check(rules, '', 'joinGroup', {data});
            this.data.set('instance', data);
            this.data.set('disableGroup.disable', joinGroup.disable);
            this.data.set('disableGroup.tip', joinGroup.message);
        });
    }

    joinGroup(e) {
        const type = this.data.get('type');
        const sdk = new BccSDK(
            {
                vpcId: this.data.get('context').vpcId,
                client: window.$http
            },
            this.$context
        );
        const instance = this.data.get('instance');
        let names = [instance.name];
        let isBindEsg = this.data.get('isBindEsg');
        // 请求函数
        let submitRequester = params => {
            let aipName = typeApi;
            if (params.securityType === SecurityTypes.ENTERPRISE) {
                aipName = esgTypeApi;
                params.esgUuids = params.securityGroupIds;
                u.extend(params, this.getEsgPayload('joinGroup'));
                delete params.securityGroupIds;
                delete params.securityType;
            } else {
                u.extend(params, this.getPayload('joinGroup'));
            }
            return this.$http[aipName[type].joinGroup](params);
        };
        let checkSecurityRequester = params => {
            return this.$http.getUseNestedSgRule(params);
        };
        const dialog = new SecurityBindDialog({
            data: {
                serviceName: type === 'eni' ? '弹性网卡' : '服务网卡',
                names,
                securityGroups: this.data.get('group.datasource'),
                submitParam: {},
                vpcId: this.data.get('context').vpcId,
                submitRequester: submitRequester.bind(this.$http),
                checkSecurityRequester: type === 'eni' ? '' : checkSecurityRequester.bind(this.$http),
                sdk,
                // withEnterprise 是否支持企业安全组，若想要服务网卡也支持，直接传true就行
                withEnterprise: true,
                // 当前安全组类型，可不传
                securityType: isBindEsg ? SecurityTypes.ENTERPRISE : SecurityTypes.NORMAL,
                // snic的企业安全组现在只支持绑定一个
                serviceType: type === 'eni' ? 'eni' : 'snic'
            }
        });
        dialog.on('success', e => {
            this.loadDetail();
        });
        dialog.attach(document.body);
    }

    unbindSecurity(row) {
        const type = this.data.get('type');
        const confirm = new Confirm({
            data: {content: '确认取消关联？'}
        });
        let isBindEsg = this.data.get('isBindEsg');
        let aipName = isBindEsg ? esgTypeApi : typeApi;
        let payload = isBindEsg ? this.getEsgPayload('unbind', row) : this.getPayload('unbind', row);
        confirm.on('confirm', () => {
            this.$http[aipName[type].unbind](payload).then(() => this.loadDetail());
        });
        confirm.attach(document.body);
    }

    showRules(rowIndex) {
        this.data.push('group.expandedIndex', rowIndex);
        this.data.set(`group.datasource[${rowIndex}].expanded`, true);
    }

    hideRules(rowIndex) {
        this.data.remove('group.expandedIndex', rowIndex);
        this.data.set(`group.datasource[${rowIndex}].expanded`, false);
    }

    onRowExpand({value}) {
        this.data.set(`group.datasource[${value.rowIndex}].expanded`, true);
    }

    onRowCollapse({value}) {
        this.data.set(`group.datasource[${value.rowIndex}].expanded`, false);
    }
    onRegionChange() {
        let path = router.locator.current.split('?')[0];
        if (path.includes('eni')) {
            location.hash = '#/vpc/eni/list';
        } else {
            location.hash = '#/vpc/endpoint/list';
        }
    }
}
export default San2React(Processor.autowireUnCheckCmpt(EniSecurity));
