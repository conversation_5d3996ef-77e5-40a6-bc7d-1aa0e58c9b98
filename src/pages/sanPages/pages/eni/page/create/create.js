import u from 'lodash';
import {html, decorators, CreatePage, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {TagEditPanel} from '@baidu/bce-tag-sdk-san';
import {OutlinedPlus} from '@baidu/sui-icon';
import {checker} from '@baiducloud/bce-opt-checker';
import {Input} from '@baidu/sui';

import rules from '../../rules';
import RULE from '@/pages/sanPages/utils/rule';
import {ContextService, ServiceType} from '@/pages/sanPages/common';
import Assist from '@/utils/assist';
import {checkIsInSubnet} from '@/pages/sanPages/utils/common';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import rule from '../list/rules';
import '../../style/create.less';
import testID from '@/testId';

const formValidator = self => ({
    name: [
        {required: true, message: '请填写名称'},
        {pattern: /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{0,64}$/, message: '格式不符合要求'}
    ],
    networkInterfaceTrafficMode: [{required: true, message: '请选择类型'}],
    vpcId: [
        {
            validator(rule, value, callback) {
                if (value === '') {
                    return callback('请选择所在网络');
                }
                return callback();
            }
        }
    ],
    subnetId: [{required: true, message: '请选择所在子网'}],
    securityGroups: [{required: true, message: '请选择安全组'}],
    description: [{maxLength: 200}]
});
const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, service} = decorators;

const tpl = html`
    <template>
        <s-app-create-page
            class="eni-create-class"
            backTo="{{pageNav.backUrl}}"
            backToLabel="{{pageNav.backLabel}}"
            pageTitle="{{pageNav.title}}"
        >
            <s-form s-ref="form" data="{=formData=}" rules="{{rules}}" label-align="left">
                <div class="form-part-wrap">
                    <h4 class="title_eni">基本信息</h4>
                    <s-form-item
                        label="网卡名称："
                        prop="name"
                        help="大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65"
                    >
                        <s-input
                            data-test-id="${testID.eni.createNameInput}"
                            width="{{300}}"
                            value="{=formData.name=}"
                        />
                    </s-form-item>
                    <s-form-item label="类型：" s-if="{{RdmaWhiteList}}" prop="networkInterfaceTrafficMode">
                        <s-radio-radio-group
                            datasource="{{networkInterfaceTrafficModeDatasource}}"
                            radioType="button"
                            value="{=formData.networkInterfaceTrafficMode=}"
                            on-change="networkInterfaceTypeChange($event)"
                        >
                        </s-radio-radio-group>
                        <p class="assist-tip-form">
                            请根据具体使用场景合理选择弹性网卡类型。
                            <a
                                class="assist-tip"
                                href="javascript:void(0)"
                                on-click="showAssist('type')"
                                s-if="FLAG.NetworkSupportAI"
                                >了解详情</a
                            >
                        </p>
                    </s-form-item>
                    <s-form-item label="所在网络：" prop="vpcId">
                        <s-select
                            width="{{300}}"
                            value="{=formData.vpcId=}"
                            on-change="vpcChange($event)"
                            class="{{quotaError ? 'error_select' : ''}}"
                        >
                            <s-select-option s-for="item in vpcs" value="{{item.value}}" label="{{item.text}}">
                                <s-tooltip>
                                    <div slot="content">{{item.text}}</div>
                                    <div>{{item.text}}</div>
                                </s-tooltip>
                            </s-select-option>
                        </s-select>
                    </s-form-item>
                    <div class="error_tip" s-if="{{quotaError}}">
                        <!--bca-disable-next-line-->
                        {{quotaTip | raw}}
                    </div>
                    <s-form-item label="所在子网：" prop="subnetId">
                        <s-select
                            width="{{300}}"
                            datasource="{{subnetDatasource}}"
                            value="{=formData.subnetId=}"
                            disabled="{{loading}}"
                            on-change="subnetChange($event)"
                            data-test-id="${testID.eni.createSubnetSelect}"
                        ></s-select>
                    </s-form-item>
                    <s-form-item label="描述：" prop="description">
                        <s-textarea
                            width="{{300}}"
                            maxLength="200"
                            height="{{100}}"
                            multiline
                            value="{=formData.description=}"
                            track-id="ti_vpc_eni_create"
                            track-name="创建弹性网卡/描述"
                        />
                    </s-form-item>
                </div>
                <div class="form-part-wrap">
                    <h4 class="title_eni">安全组</h4>
                    <s-form-item label="安全组：" prop="securityGroups">
                        <s-radio-radio-group
                            datasource="{{securityGroupDatasource}}"
                            radioType="button"
                            value="{=formData.securityType=}"
                            on-change="handleRadioTypeChange"
                        >
                        </s-radio-radio-group>
                        <p class="assist-tip-form">
                            安全组分为普通安全组和企业安全组。
                            <a
                                class="assist-tip"
                                href="javascript:void(0)"
                                on-click="showAssist('security')"
                                s-if="FLAG.NetworkSupportAI"
                                >了解详情</a
                            >
                        </p>
                    </s-form-item>
                    <s-form-item label=" " prop="securityGroup">
                        <s-select
                            width="{{300}}"
                            datasource="{{currentSecurityDatasource}}"
                            value="{=formData.securityGroups=}"
                            disabled="{{loading}}"
                            multiple
                            data-test-id="${testID.eni.createSecuritySelect}"
                        ></s-select>
                    </s-form-item>
                </div>
                <div class="form-part-wrap">
                    <h4 class="title_eni">IP地址</h4>
                    <s-form-item label="IPv4地址：" prop="privateIps" class="ip-item center_class">
                        <template slot="label" class="label_class">
                            {{'IPv4地址：'}}
                            <s-tip class="inline-tip" content="{{eniCanBindIpTip}}" skin="question" />
                        </template>
                        <div class="ui-form-item-help">{{ipHelp}}</div>
                        <s-table datasource="{{table.datasource}}" columns="{{table.columns}}">
                            <div slot="empty">
                                <s-empty>
                                    <div slot="action"></div>
                                </s-empty>
                            </div>
                            <div slot="c-type">{{row | typeContent}}</div>
                            <div slot="c-ip">
                                <s-radio-radio-group
                                    class="ip-radio"
                                    datasource="{{ipDatasource}}"
                                    value="auto"
                                    on-change="typeChange($event, rowIndex)"
                                />
                                <div>
                                    <s-input
                                        class="ip-input"
                                        s-if="row.isCustom"
                                        width="{{180}}"
                                        placeholder="请输入IP地址"
                                        on-input="inputChange($event, rowIndex)"
                                    />
                                    <label s-if="row.isCustom && row.error" class="ui-form-item-invalid-label"
                                        >{{row.error}}</label
                                    >
                                </div>
                            </div>
                            <div slot="c-operation">
                                <s-button skin="stringfy" s-if="!row.primary" on-click="onRemove(rowIndex)"
                                    >删除</s-button
                                >
                            </div>
                        </s-table>
                        <s-tooltip trigger="{{disableAddIp.message ? 'hover' : ''}}" placement="right">
                            <!--bca-disable-next-line-->
                            <div slot="content">{{disableAddIp.message | raw}}</div>
                            <s-button skin="stringfy" on-click="addIp()" disabled="{{disableAddIp.disable}}"
                                ><outlined-plus class="plus_class" />添加辅助IP</s-button
                            >
                        </s-tooltip>
                    </s-form-item>
                    <s-form-item s-if="subnetIpv6Cidr" label="分配IPv6 IP:" class="ip-item ipv6-class">
                        <div class="ipv6-switch-class">
                            <div class="switch_btn">
                                <s-switch checked="{=formData.allocateIpv6Addr=}" on-change="ipv6TypeSwitch" />
                            </div>
                            <div class="ui-form-item-help" s-if="formData.allocateIpv6Addr">{{ipv6Help}}</div>
                        </div>
                        <s-table
                            s-if="formData.allocateIpv6Addr"
                            datasource="{{ipv6Table.ipv6Datasource}}"
                            columns="{{ipv6Table.ipv6columns}}"
                        >
                            <div slot="empty">
                                <s-empty>
                                    <div slot="action"></div>
                                </s-empty>
                            </div>
                            <div slot="c-type">{{row | typeContent}}</div>
                            <div slot="c-ip">
                                <s-radio-radio-group
                                    class="ip-radio"
                                    datasource="{{ipv6RadioDatasource}}"
                                    value="auto"
                                    on-change="ipv6TypeChange($event, rowIndex)"
                                />
                                <div>
                                    <s-input
                                        class="ip-input"
                                        s-if="{{row.ipv6Set}}"
                                        width="{{180}}"
                                        placeholder="请输入该子网内可用IP"
                                        value="{=row.ipv6Address=}"
                                        on-input="ipv6InputChange($event, rowIndex)"
                                    />
                                    <label s-if="{{row.ipv6Set && row.ipv6Error}}" class="ui-form-item-invalid-label"
                                        >{{row.ipv6Error}}</label
                                    >
                                </div>
                            </div>
                            <div slot="c-operation">
                                <s-button skin="stringfy" s-if="!row.primary" on-click="onIpv6Remove(rowIndex)"
                                    >删除</s-button
                                >
                            </div>
                        </s-table>
                        <s-tooltip
                            s-if="formData.allocateIpv6Addr"
                            trigger="{{disableAddIpv6Ip.message ? 'hover' : ''}}"
                            placement="right"
                        >
                            <!--bca-disable-next-line-->
                            <div slot="content">{{disableAddIpv6Ip.message | raw}}</div>
                            <s-button
                                s-if="formData.allocateIpv6Addr"
                                skin="stringfy"
                                on-click="addIpv6Ip()"
                                disabled="{{disableAddIpv6Ip.disable}}"
                                ><outlined-plus class="plus_class" />添加辅助IP</s-button
                            >
                        </s-tooltip>
                    </s-form-item>
                </div>
                <div class="form-part-wrap">
                    <h4 class="title_eni">标签</h4>
                    <s-form-item prop="tag" label="绑定标签：">
                        <tag-edit-panel
                            create
                            version="v2"
                            instances="{{defaultInstances}}"
                            options="{{tagListRequster}}"
                            s-ref="tagPanel"
                        />
                    </s-form-item>
                </div>
            </s-form>
            <div slot="pageFooter" class="buybucket">
                <div class="buybucket-container">
                    <s-tooltip trigger="{{eniSinDisable.disable ? 'hover' : ''}}" placement="top">
                        <s-button
                            data-test-id="${testID.eni.createSubmit}"
                            skin="primary"
                            disabled="{{eniSinDisable.disable || disableSub}}"
                            on-click="doSubmit"
                            >确定</s-button
                        >
                        <div slot="content">{{eniSinDisable.message}}</div>
                    </s-tooltip>
                    <s-button data-test-id="${testID.eni.createCancel}" size="large" on-click="onClose">
                        取消
                    </s-button>
                </div>
            </div>
        </s-app-create-page>
    </template>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class EniCreate extends CreatePage {
    static template = tpl;
    static components = {
        'outlined-plus': OutlinedPlus,
        's-textarea': Input.TextArea,
        'tag-edit-panel': TagEditPanel
    };

    static filters = {
        typeContent(row) {
            return row.primary ? '主IP' : '辅助IP';
        }
    };

    initData() {
        return {
            pageNav: {
                title: '创建弹性网卡',
                backUrl: '/network/#/vpc/eni/list',
                backLabel: '返回'
            },
            FLAG: FLAG,
            rules: {
                ...formValidator(this)
            },
            formData: {
                subnetId: '',
                securityGroups: [],
                allocateIpv6Addr: false,
                securityType: 'normal',
                networkInterfaceTrafficMode: 'standard'
            },
            formErrors: {},
            subnetDatasource: [],
            initSubnetDatasource: [],
            securityDatasource: [],
            enterpriseSecurityDatasource: [],
            loading: false,
            ipHelp: '已分配IP数：-/-个（当前子网可用IP剩余-个）',
            table: {
                datasource: [
                    {
                        primary: true,
                        privateIp: ''
                    }
                ],
                columns: [
                    {name: 'type', label: '地址类型', width: 90},
                    {name: 'ip', label: 'IP地址'},
                    {name: 'operation', label: '操作', width: 90}
                ]
            },
            ipv6Table: {
                ipv6Datasource: [
                    {
                        primary: false,
                        ipv6Address: ''
                    }
                ],
                ipv6columns: [
                    {name: 'type', label: '地址类型', width: 90},
                    {name: 'ip', label: 'IP地址'},
                    {name: 'operation', label: '操作', width: 90}
                ]
            },
            ipDatasource: [
                {
                    text: '自动分配',
                    value: 'auto'
                },
                {
                    text: '指定',
                    value: 'custom'
                }
            ],
            quota: 40,
            limit: 40,
            resourceGroupId: '',
            ContextService,
            quotaError: false,
            subnetIpv6Cidr: '',
            ipv6RadioDatasource: [
                {
                    text: '自动分配',
                    value: 'auto'
                },
                {
                    text: '指定',
                    value: 'custom'
                }
            ],
            ipv6Set: false,
            securityGroupDatasource: [
                {
                    text: '普通安全组',
                    value: 'normal'
                },
                {
                    text: '企业安全组',
                    value: 'enterprise'
                }
            ],
            networkInterfaceTrafficModeDatasource: [
                {
                    text: '普通型',
                    value: 'standard'
                },
                {
                    text: 'RDMA型',
                    value: 'highPerformance'
                }
            ],
            eniSinDisable: {},
            ipv6Help: '已分配IP数：-/-个',
            defaultInstances: [
                {
                    tags: [
                        {
                            tagKey: '默认项目',
                            tagValue: ''
                        }
                    ]
                }
            ],
            tagListRequster: this.tagListRequster.bind(this)
        };
    }

    static computed = {
        disableAddIp() {
            let quota = this.data.get('quota');
            let hasQuota = this.data.get('table.datasource').length < quota;
            let subnetId = this.data.get('formData.subnetId');
            let {addIp} = checker.check(rules, '', 'addIp', {hasQuota, subnetId});
            return addIp;
        },
        quotaTip() {
            let formData = this.data.get('formData');
            if (!formData?.vpcMap) {
                return '';
            }
            let ticketUrl = `${ContextService.Domains.ticket}/#/ticket/create`;
            if (FLAG.NetworkSupportXS) {
                return `您的私有网络${formData.vpcMap[formData.vpcId]}下弹性网卡数量已经达到配额`;
            } else {
                return `您的私有网络${formData.vpcMap[formData.vpcId]}下弹性网卡数量已经达到配额，如需更多弹性网卡，可以通过<a href="${ticketUrl}" target="_blank">工单</a>申请`;
            }
        },
        disableAddIpv6Ip() {
            let ipv6Quota = this.data.get('ipv6Quota');
            let hasIpv6Quota = this.data.get('ipv6Table.ipv6Datasource').length < ipv6Quota;
            let {addIpv6Ip} = checker.check(rules, '', 'addIpv6Ip', {hasIpv6Quota});
            return addIpv6Ip;
        },
        eniCanBindIpTip() {
            // const quota = this.data.get('quota');
            return `弹性网卡最多支持绑定40个IP，实际可分配IP数由绑定的云主机决定。`;
        }
    };

    inited() {
        let vpcs = window.$storage
            .get('vpcList')
            ?.filter(item => item.value)
            ?.map(item => {
                if (item.vpcInfo.ipv6Cidr) {
                    return {
                        ...item,
                        text: `${item.text}(${item.vpcInfo.ipv6Cidr})`
                    };
                } else return item;
            });
        this.data.set('vpcId', window.$storage.get('vpcId'));
        this.data.set('vpcs', vpcs);
        let vpcMap = {};
        u.each(vpcs, item => {
            vpcMap[item.value] = item.text;
        });
        this.data.set('formData.vpcMap', vpcMap);
        this.data.set('allVpc', vpcs);
        this.getWhiteList();
        let {eniSinDisable} = checker.check(rule, []);
        this.data.set('eniSinDisable', eniSinDisable);
    }

    attached() {
        this.setLimitNum();
        this.watch('formData.vpcId', value => {
            this.data.set('formData.subnetId', '');
            this.data.set('formData.securityGroups', []);
            this.getEniQuota();
            this.loadSubnets();
            this.loadAllSecurityList();
        });
        // 默认选择列表页选中的vpc实例，赋值操作需要放在watch后面，才能够触发watch
        let vpcId = this.data.get('vpcId');
        let vpcs = this.data.get('vpcs');
        if (!vpcId && vpcs.length) {
            this.data.set('formData.vpcId', vpcs[0].value);
        }
        vpcId && this.data.set('formData.vpcId', vpcId);

        this.watch('table.datasource', datasource => {
            const total = this.data.get('total');
            const quota = this.data.get('quota');
            this.data.set(
                'ipHelp',
                `已分配IP数：${datasource.length}/${quota}个` + `（当前子网可用IP剩余${total || '-'}个）`
            );
        });
        this.watch('ipv6Table.ipv6Datasource', datasource => {
            const ipv6Quota = this.data.get('ipv6Quota');
            this.data.set('ipv6Help', `已分配IP数：${datasource.length}/${ipv6Quota}个`);
        });
    }

    typeChange(e, index) {
        this.data.set(`table.datasource[${index}].error`, '');
        this.data.set(`table.datasource[${index}].privateIp`, '');
        this.data.set(`table.datasource[${index}].isCustom`, e.value === 'custom');
    }

    ipv6TypeChange(e, index) {
        this.data.set(`ipv6Table.ipv6Datasource[${index}].ipv6Error`, e.value === 'custom' ? '请指定IPv6地址' : '');
        this.data.set(`ipv6Table.ipv6Datasource[${index}].ipv6Address`, '');
        this.data.set(`ipv6Table.ipv6Datasource[${index}].ipv6Set`, e.value === 'custom');
        this.data.set('disableSub', e.value === 'custom');
    }

    inputChange(e, index) {
        this.data.set(`table.datasource[${index}].privateIp`, e.value);
    }

    onRemove(rowIndex) {
        this.data.removeAt('table.datasource', rowIndex);
    }

    addIp() {
        this.data.push('table.datasource', {primary: false});
    }

    getEniQuota() {
        let vpcId = this.data.get('formData.vpcId');
        return this.$http.getEniQuota({vpcId}).then(data => this.data.set('formData.quotaFree', data.free));
    }

    loadSubnets() {
        let payload = {vpcId: this.data.get('formData.vpcId'), attachVm: false};
        this.data.set('loading', true);
        this.$http
            .getSubnetList(payload)
            .then(data => {
                let datasource = [];
                // BBC类型的子网不能创建弹性网卡
                u.each(data, item => {
                    if (item.subnetType !== ServiceType.BBC && item.subnetType !== ServiceType.BBC_NAT) {
                        let text = '';
                        if (item.ipv6Cidr) {
                            text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}(${item.ipv6Cidr})`;
                        } else {
                            text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}`;
                        }
                        datasource.push({
                            value: item.subnetId,
                            text: text,
                            cidr: item.cidr,
                            ipv6Cidr: item.ipv6Cidr
                        });
                    }
                });
                this.data.set('subnetDatasource', datasource);
                this.data.set('initSubnetDatasource', datasource);
            })
            .finally(() => this.data.set('loading', false));
    }

    loadSubnetDetail(subnetId) {
        this.$http.getSubnetDetail(subnetId).then(data => {
            const item = data.subnets && data.subnets.length > 0 ? data.subnets[0] : {totalIps: 0, usedIps: 0};
            const total = item.totalIps - item.usedIps;
            const quota = Math.min(total, this.data.get('limit'));
            const ipv6Quota = this.data.get('limit');
            this.data.set('quota', quota);
            this.data.set('total', total);
            this.data.set('ipv6Quota', ipv6Quota);
            this.data.set(
                'ipHelp',
                `已分配IP数：${this.data.get('table.datasource').length}/${quota}个（当前子网可用IP剩余${total || '-'}个）`
            );
            this.data.set(
                'ipv6Help',
                `已分配IP数：${this.data.get('ipv6Table.ipv6Datasource').length}` + `/${ipv6Quota}个`
            );
        });
    }

    vpcChange(e) {
        this.data.set('subnetIpv6Cidr', '');
    }

    subnetChange(e) {
        let subnetDatasource = this.data.get('subnetDatasource');
        let index = subnetDatasource.findIndex(item => item.value === e.value);
        let ipv6Cidr = subnetDatasource[index].ipv6Cidr;
        if (ipv6Cidr) {
            this.data.set('subnetIpv6Cidr', ipv6Cidr);
        } else {
            this.data.set('subnetIpv6Cidr', '');
        }
        e.value && this.loadSubnetDetail(e.value);
    }

    async validateIpv6(value) {
        let error = '';
        if (value === '') {
            return (error = '请指定IPv6地址');
        }
        //  获得最后一个冒号后面的部分
        const lastStr = value && value.split(':').pop();
        if (!RULE.IPV6.test(value)) {
            return (error = '请填写正确的IPv6格式');
        } else if (value[0].indexOf(':') > -1 && value[0].indexOf('.') > -1) {
            return (error = '请填写标准的IPv6格式');
        } else if (RULE.IP.test(lastStr)) {
            return (error = '不支持IPv6与IPv4混合');
        } else if (!checkIsInSubnet(value + '/128', this.data.get('subnetIpv6Cidr'))) {
            return (error = 'IP需在VPC分配的IPv6网段内');
        } else if (RULE.IPV6.test(value) && !lastStr) {
            return (error = '请填写标准的IPv6格式');
        }
        let param = {
            vpcId: this.data.get('formData'.vpcId),
            vpcPrivateIpAddresses: value
        };
        let res = await this.$http.vpcCheckPrivateIp(param);
        if (res.vpcPrivateIpAddresses && res.vpcPrivateIpAddresses.length) {
            let ipValidMessage = res.vpcPrivateIpAddresses[0].privateIpAddressType;
            return (error = `IP被${ipValidMessage}占用`);
        } else {
            return (error = '');
        }
    }

    async loadAllSecurityList() {
        this.data.set('loading', true);
        try {
            await Promise.all([this.loadSecurityList(), this.loadEnterpriseSecurityList()]);
            this.data.set('loading', false);
        } catch (error) {
            this.data.set('loading', true);
        }
        this.handleRadioTypeChange({});
    }

    loadSecurityList() {
        return this.$http
            .securityListV3({
                pageNo: 1,
                pageSize: 10000,
                vpcId: this.data.get('formData.vpcId')
            })
            .then(data => {
                let datasource = [];
                u.each(data.result || [], item =>
                    datasource.push({
                        value: item.id,
                        text: item.name
                    })
                );
                this.data.set('securityDatasource', datasource);
            });
    }

    loadEnterpriseSecurityList() {
        return this.$http
            .enterpriseSecurityList({
                pageNo: 1,
                pageSize: 10000
            })
            .then(data => {
                let datasource = [];
                u.each(data.result || [], item =>
                    datasource.push({
                        value: item.esgUuid,
                        text: item.name
                    })
                );
                this.data.set('enterpriseSecurityDatasource', datasource);
            });
    }

    doSubmit() {
        let vpcId = this.data.get('formData.vpcId');
        let result = {flag: false, num: 0};
        const form = this.ref('form');
        let resourceGroupIds;
        return form.validateFields().then(async () => {
            try {
                await this.ref('tagPanel').validate(false);
            } catch (error) {
                return;
            }
            let formData = this.data.get('formData');
            if (formData.quotaFree <= 0) {
                this.data.set('quotaError', true);
            } else {
                this.data.set('quotaError', false);
            }
            if (this.data.get('quotaError')) {
                return;
            }
            let privateIps = [];
            for (let i = 0; i < this.data.get('table.datasource').length; i++) {
                let item = this.data.get(`table.datasource[${i}]`);
                if (!item.isCustom) {
                    privateIps.push({
                        primary: item.primary,
                        privateIp: ''
                    });
                    result.num++;
                    if (i === this.data.get('table.datasource').length - 1) {
                        result.flag = true;
                    }
                } else {
                    if (!item.privateIp) {
                        this.data.set(`table.datasource[${i}].error`, '请指定IP地址');
                        break;
                    }
                    if (!RULE.IP.test(item.privateIp)) {
                        this.data.set(`table.datasource[${i}].error`, 'IP地址格式有误');
                        break;
                    }
                    const subnetItem = u.find(
                        this.data.get('initSubnetDatasource'),
                        item => item.value === formData.subnetId
                    );
                    if (subnetItem && !checkIsInSubnet(item.privateIp + '/32', subnetItem.cidr)) {
                        this.data.set(`table.datasource[${i}].error`, '不属于所在子网');
                        break;
                    }
                    let params = {
                        vpcId,
                        vpcPrivateIpAddresses: item.privateIp
                    };
                    await this.$http.vpcCheckPrivateIp(params).then(res => {
                        if (res.vpcPrivateIpAddresses && res.vpcPrivateIpAddresses.length) {
                            let ipValidMessage = res.vpcPrivateIpAddresses[0].privateIpAddressType;
                            this.data.set(`table.datasource[${i}].error`, `IP被${ipValidMessage}占用`);
                        } else {
                            this.data.set(`table.datasource[${i}].error`, '');
                            privateIps.push({
                                primary: item.primary,
                                privateIp: item.privateIp
                            });
                            result.num++;
                            if (i === this.data.get('table.datasource').length - 1) {
                                result.flag = true;
                            }
                        }
                    });
                }
            }
            if (result.flag && result.num === this.data.get('table.datasource').length) {
                let payload = u.pick(
                    formData,
                    'name',
                    'subnetId',
                    'securityGroups',
                    'description',
                    'networkInterfaceTrafficMode'
                );
                payload.allocateMultiIpv6Addr = this.data.get('formData.allocateIpv6Addr');
                if (this.data.get('formData.allocateIpv6Addr')) {
                    let ipv6Datasource = this.data.get('ipv6Table.ipv6Datasource');
                    let ipv6PrivateIps = [];
                    ipv6Datasource.forEach(item => {
                        ipv6PrivateIps.push({
                            primary: false,
                            privateIp: item.ipv6Address || ''
                        });
                    });
                    payload = {
                        ...payload,
                        ipv6PrivateIps
                    };
                }
                const params = u.extend({privateIps}, payload);
                delete params.vpcMap;
                this.data.set('disableSub', true);
                if (formData.securityType !== 'normal') {
                    params.enterpriseSecurityGroups = params.securityGroups;
                    delete params.securityGroups;
                }
                let tags = await this.ref('tagPanel').getTags();
                params.tags = tags;
                return this.$http
                    .createEni(params)
                    .then(() => {
                        location.hash = '#/vpc/eni/list';
                    })
                    .catch(err => {
                        this.data.set('disableSub', false);
                    });
            }
            return Promise.reject();
        });
    }

    setLimitNum() {
        const whiteList = window.$storage.get('commonWhite');
        this.data.set('limit', whiteList?.eniAuxiliaryIpWhiteList ? 200 : 40);
    }

    onClose() {
        location.hash = '#/vpc/eni/list';
    }
    // 切换安全组类型
    handleRadioTypeChange() {
        this.nextTick(() => {
            let value = this.data.get('formData.securityType');
            let securityDatasource = this.data.get('securityDatasource');
            let enterpriseSecurityDatasource = this.data.get('enterpriseSecurityDatasource');
            let currentSecurityDatasource = value === 'enterprise' ? enterpriseSecurityDatasource : securityDatasource;
            this.data.set('currentSecurityDatasource', currentSecurityDatasource);
            this.data.set('formData.securityGroups', []);
        });
    }

    networkInterfaceTypeChange(e) {
        this.data.set('formData.networkInterfaceTrafficMode', e.value);
    }

    getWhiteList() {
        this.$http.vpcCommonWhiteList({id: 'EriWhiteList'}).then(res => {
            this.data.set('RdmaWhiteList', res);
        });
    }

    addIpv6Ip() {
        this.data.push('ipv6Table.ipv6Datasource', {primary: false, ipv6Set: false, ipv6Address: '', ipv6Error: ''});
    }

    onIpv6Remove(rowIndex) {
        this.data.removeAt('ipv6Table.ipv6Datasource', rowIndex);
        let ipv6DatasourceLength = this.data.get('ipv6Table.ipv6Datasource').length;
        ipv6DatasourceLength <= 0 && this.data.set('formData.allocateIpv6Addr', false);
    }

    ipv6TypeSwitch(e) {
        let ipv6Datasource = this.data.get('ipv6Table.ipv6Datasource');
        // 关闭ipv6的开关清空ipv6table数据
        if (!e.value) {
            ipv6Datasource.forEach((item, rowIndex) => {
                this.onIpv6Remove(rowIndex);
            });
        } else {
            ipv6Datasource.length === 0 && this.addIpv6Ip();
        }
    }

    async ipv6InputChange(e, index) {
        let err = await this.validateIpv6(e.value);
        this.data.set(`ipv6Table.ipv6Datasource[${index}].ipv6Address`, e.value);
        this.data.set(`ipv6Table.ipv6Datasource[${index}].ipv6Error`, err);
        this.data.set('disableSub', err.length > 0);
    }
    tagListRequster() {
        return this.$http.getSearchTagList({serviceType: [], region: ['global']});
    }
    showAssist(type) {
        Assist.sendMessageToAssist({
            sceneLabel: 'enic_create',
            message:
                type !== 'security' ? '弹性网卡普通型和RDMA型的区别是什么？' : '普通安全组和企业安全组的区别是什么？'
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(EniCreate));
