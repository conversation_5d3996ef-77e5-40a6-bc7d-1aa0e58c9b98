import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {BcmSDK} from '@baidu/new-bcm-sdk';
import {BcmChartPanel} from '@baidu/new-bcm-sdk-san';
import {Button, Select, DatePicker, Loading} from '@baidu/sui';
import {AppDetailPage, AppLegend} from '@baidu/sui-biz';
import {OutlinedRefresh} from '@baidu/sui-icon';
import moment from 'moment';

import {EniStatus} from '@/pages/sanPages/common/enum';
import monitorConfig from '@/pages/sanPages/utils/monitorConfig';
import '../../style/bcm.less';

const {EniMetrics, shortcutItems} = monitorConfig;
const {template, withSidebar, invokeComp} = decorators;
const tpl = html`
    <template>
        <s-page class="{{klass}}">
            <div slot="pageTitle" class="vpn-detail-header vpn-common-header">
                <span class="instance-name">{{instance.name}}</span>
                <span class="{{instance.status | statusStyle}}">{{instance.status | statusText}}</span>
            </div>
            <div class="monitor-wrap">
                <div class="monitor-item-box">
                    <h2>监控信息</h2>
                    <div class="button-wrap">
                        <span class="alarm-state">
                            <span>时间：</span>
                            <s-date-range-picker
                                s-ref="timeRange"
                                value="{=timeRange=}"
                                width="{{310}}"
                                mode="second"
                                range="{{range}}"
                                on-change="onTimeChange"
                                shortcut="{{shortcutItems}}"
                            />
                        </span>
                        <s-button class="s-icon-button" on-click="onTimeRefresh">
                            <outlined-refresh class="icon-class" />
                        </s-button>
                        <s-button
                            class="alarm-detail"
                            track-id="ti_vpc_vpn_monitor_detail"
                            tack-name="弹性网卡/报警详情"
                            on-click="alarmDetail"
                        >
                            报警详情
                        </s-button>
                    </div>
                    <div class="eni-monitor-trends">
                        <div class="monitor-trend-box" s-for="item,index in chart">
                            <bcm-chart-panel
                                s-ref="alarm-chart-{{index}}"
                                withFilter="{{false}}"
                                scope="{{item.scope}}"
                                dimensions="{{item.dimensions}}"
                                statistics="{{item.statistics}}"
                                title="{{item.title}}"
                                options="{{options}}"
                                api-type="metricName"
                                startTime="{=startTime=}"
                                endTime="{=endTime=}"
                                period="{{monitorDefaultPeriod}}"
                                metrics="{{item.metrics}}"
                                unit="{{item.unit}}"
                                bitUnit="{{item.bitUnit}}"
                                width="{{'auto'}}"
                                height="{{230}}"
                                sdk="{{bcmSdk}}"
                            >
                            </bcm-chart-panel>
                        </div>
                    </div>
                </div>
            </div>
        </s-page>
    </template>
`;

@template(tpl)
@withSidebar({active: 'vpc-eni-monitor'})
class EniMonitor extends Component {
    components = {
        's-button': Button,
        's-select': Select,
        's-app-legend': AppLegend,
        'bcm-chart-panel': BcmChartPanel,
        's-date-range-picker': DatePicker.DateRangePicker,
        's-page': AppDetailPage,
        'outlined-refresh': OutlinedRefresh,
        's-loading': Loading
    };

    filters = {
        statusStyle(status) {
            let config = EniStatus.fromValue(status);
            return config ? config.styleClass : '';
        },
        statusText(status) {
            let config = EniStatus.fromValue(status);
            return config ? config.text : '';
        }
    };

    initData() {
        return {
            klass: 'vpc-eni-monitor',
            instance: {},
            alarm: {
                okStateCount: 0,
                alarmStateCount: 0,
                insufficientStateCount: 0
            },
            chart: [],
            options: {
                color: ['#2468f2', '#5FB333'],
                legend: {
                    x: 'right',
                    y: 'top'
                },
                dataZoom: {start: 0}
            },
            timeRange: {
                begin: new Date(moment().subtract(1, 'hour').valueOf()),
                end: new Date(moment().valueOf())
            },
            range: {
                begin: new Date(moment().subtract(40, 'day').valueOf()),
                end: new Date(moment().valueOf())
            },
            startTime: moment().subtract(1, 'hour').utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
            endTime: moment().utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
            endOriginTime: moment().valueOf(),
            bcmSdk: new BcmSDK({client: window.$http, context: window.$context}),
            shortcutItems
        };
    }

    attached() {
        this.loadDetail();
        this.data.set('loading', false);
        this.watch('timeRange', timeRange => {
            this.onTimeChange({value: timeRange});
        });
    }

    onTimeChange({value}) {
        let startTime = moment(value.begin).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        let endTime = moment(value.end).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        let ms = moment(value.end) - moment(value.begin);
        let hourTime = Math.round(ms / 86400000);
        switch (true) {
            case hourTime <= 1:
                this.data.set('monitorDefaultPeriod', 60);
                break;
            case hourTime <= 3:
                this.data.set('monitorDefaultPeriod', 300);
                break;
            case hourTime <= 7:
                this.data.set('monitorDefaultPeriod', 600);
                break;
            case hourTime <= 14:
                this.data.set('monitorDefaultPeriod', 1800);
                break;
            case hourTime <= 40:
                this.data.set('monitorDefaultPeriod', 3600);
                break;
            default:
                break;
        }
        this.data.set('startTime', startTime);
        this.data.set('endTime', endTime);
        this.onRefresh();
    }

    onRefresh() {
        let chartConfig = this.data.get('chart');
        u.map(chartConfig, (item, i) => {
            this.ref(`alarm-chart-${i}`).loadMetrics();
        });
    }

    onTimeRefresh() {
        if (this.data.get('timeRange.end').valueOf() >= this.data.get('endOriginTime')) {
            this.data.set('timeRange.end', new Date(moment().valueOf()));
        } else {
            this.onRefresh();
        }
    }

    alarmDetail() {
        const region = window.$context.getCurrentRegionId();
        redirect(
            `/bcm/#/bcm/alarm/rule/list~scope=BCE_ENIC&dimensions=TapId:${this.data.get('context').shortEniId}&region=${region}`
        );
    }

    loadDetail() {
        return this.$http
            .getEniDetail(
                {eniId: this.data.get('context').eniId},
                {
                    'x-silent-codes': ['NoSuchObject']
                }
            )
            .then(data => {
                this.data.set('instance', data);
                this.initMonitor();
            });
    }

    initMonitor() {
        let chartConfig = [];
        u.each(EniMetrics, item => {
            let config = {
                scope: 'BCE_ENIC',
                period: 60,
                statistics: item.statistics || 'average',
                title: item.title,
                unit: item.unit,
                bitUnit: item.bitUnit,
                metrics: item.metrics,
                dimensions: `TapId:${this.data.get('context').shortEniId}`
            };
            chartConfig.push(config);
        });
        this.data.set('chart', chartConfig);
    }
    onRegionChange() {
        location.hash = '#/vpc/eni/list';
    }
}
export default San2React(Processor.autowireUnCheckCmpt(EniMonitor));
