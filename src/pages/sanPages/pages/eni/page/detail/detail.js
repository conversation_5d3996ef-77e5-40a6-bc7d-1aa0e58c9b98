import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {OutlinedEditingSquare} from '@baidu/sui-icon';

import zone from '@/pages/sanPages/utils/zone';
import {EniStatus} from '@/pages/sanPages/common/enum';
import {$flag as FLAG, toTime} from '@/pages/sanPages/utils/helper';
import testID from '@/testId';
import '../../style/detail.less';

const {invokeSUI, invokeSUIBIZ, template, invokeAppComp, invokeComp} = decorators;
const tpl = html`
    <div>
        <s-app-detail-page class="{{klass}}">
            <div slot="pageTitle" class="vpn-detail-header vpn-common-header">
                <span class="instance-name">{{instance.name}}</span>
                <span class="{{instance.status | statusStyle}}" s-if="instance.status"
                    >{{instance.status | statusText}}</span
                >
            </div>
            <h4>基本信息</h4>
            <ul>
                <li class="content-item">
                    <label class="cell-title">名称：</label>
                    <span class="cell-content" data-testid="${testID.eni.detailName}">
                        {{instance.name}}
                        <s-popover
                            class="edit-popover-class {{instance.source | sourceEipClass}}"
                            s-ref="{{'nameEdit'}}"
                            placement="top"
                            trigger="click"
                        >
                            <div class="edit-wrap" slot="content">
                                <s-input
                                    value="{=name=}"
                                    width="320"
                                    placeholder="请输入"
                                    on-input="onInput($event, 'name')"
                                    data-test-id="${testID.eni.detailNameEditInput}"
                                />
                                <div class="edit-tip">
                                    大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65
                                </div>
                                <s-button
                                    skin="primary"
                                    s-ref="{{'nameEditBtn'}}"
                                    disabled="{{true}}"
                                    on-click="onEdit('name')"
                                    >确定</s-button
                                >
                                <s-button on-click="editCancel('name')">取消</s-button>
                            </div>
                            <outlined-editing-square
                                color="{{instance.source === 'default' ? '#2468f2' : '#b4b6ba'}}"
                                on-click="beforeEdit('name')"
                            />
                        </s-popover>
                    </span>
                </li>
                <li class="content-item">
                    <label class="cell-title">ID：</label>
                    <span class="cell-content">{{instance.eniId}}</span>
                    <s-clip-board text="{{instance.eniId}}" successMessage="已复制到剪贴板" />
                </li>
                <li class="content-item">
                    <label class="cell-title">所在网络：</label>
                    <span class="cell-content">
                        <a
                            href="#/vpc/instance/list"
                            data-track-id="ti_vpc_eni_detail"
                            data-track-name="实例详情/所在网络"
                        >
                            {{vpcInfo.name}}
                            <span s-if="vpcInfo.cidr">
                                （{{vpcInfo.cidr}}）{{instance.vpcIpv6Cidr ? ('(' + instance.vpcIpv6Cidr +')') : ''}}
                            </span>
                        </a>
                    </span>
                    <span s-else class="cell-content">
                        <a
                            href="#/vpc/instance/list"
                            data-track-id="ti_vpc_eni_detail"
                            data-track-name="实例详情/所在网络"
                        >
                            {{instance.subnetName}}
                            <span s-if="instance.subnetCidr">
                                （{{instance.subnetCidr}}）{{instance.subnetIpv6Cidr ? ('(' + instance.subnetIpv6Cidr
                                +')') : ''}}
                            </span>
                        </a>
                    </span>
                </li>
                <li class="content-item">
                    <label class="cell-title">所在子网：</label>
                    <span class="cell-content">
                        <a
                            href="#/vpc/subnet/list?vpcId={{vpcInfo.vpcId}}"
                            data-track-id="ti_vpc_eni_detail"
                            data-track-name="实例详情/所在子网"
                        >
                            {{instance.subnetName}}
                            <span s-f="instance.subnetCidr">
                                （{{instance.subnetCidr}}）{{instance.subnetIpv6Cidr ? ('(' + instance.subnetIpv6Cidr
                                +')') : ''}}
                            </span>
                        </a>
                    </span>
                </li>
                <li class="content-item">
                    <label class="cell-title">MAC地址：</label>
                    <span>{{instance.macAddress}}</span>
                </li>
                <li class="content-item">
                    <label class="cell-title">可用区：</label>
                    <span class="cell-content">{{instance.az | zoneText}}</span>
                </li>
                <li class="content-item">
                    <label class="cell-title">挂载主机：</label>
                    <span class="cell-content">{{instance.deviceName || '-'}}|{{instance.deviceShortId || '-'}}</span>
                </li>
                <li class="content-item">
                    <label class="cell-title">创建时间：</label>
                    <span class="cell-content"> {{instance.createdTime | timeFormat}} </span>
                </li>
                <li class="content-item">
                    <label class="cell-title">描述：</label>
                    <span class="cell-content"> {{instance.description || '-'}} </span>
                    <s-popover
                        class="edit-popover-class {{instance.source | sourceEipClass}}"
                        s-ref="{{'descEdit'}}"
                        placement="top"
                        trigger="click"
                    >
                        <div class="edit-wrap" slot="content">
                            <s-input
                                value="{=description=}"
                                width="160"
                                placeholder="请输入"
                                on-input="onInput($event, 'desc')"
                            />
                            <div class="edit-tip">描述不能超过200个字符</div>
                            <s-button
                                skin="primary"
                                s-ref="{{'descEditBtn'}}"
                                disabled="{{true}}"
                                on-click="onEdit('desc')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel('desc')">取消</s-button>
                        </div>
                        <outlined-editing-square
                            color="{{instance.source === 'default' ? '#2468f2' : '#b4b6ba'}}"
                            on-click="beforeEdit('desc')"
                        />
                    </s-popover>
                </li>
            </ul>
        </s-app-detail-page>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class EniDetail extends Component {
    static components = {
        'outlined-editing-square': OutlinedEditingSquare
    };
    initData() {
        return {
            FLAG,
            klass: 'vpc-main-wrap vpc-eni-detail',
            instance: {}
        };
    }

    filters = {
        timeFormat(time) {
            return toTime(time);
        },
        zoneText(az) {
            return zone.getLabel(az);
        },
        statusStyle(value) {
            return EniStatus.fromValue(value).styleClass || '';
        },
        statusText(value) {
            return value ? EniStatus.getTextFromValue(value) : '-';
        },
        sourceEipClass(value) {
            return value === 'default' ? '' : 'sourceEipClass';
        }
    };

    attached() {
        this.loadDetail({'x-silent-codes': ['NoSuchObject']});
        this.loadVpcDetail();
    }

    loadVpcDetail() {
        const vpcId = this.data.get('context').vpcId;
        this.$http
            .getVpcDetail({
                vpcIds: [vpcId]
            })
            .then(data => {
                const result = data[vpcId] || {};
                this.data.set('vpcInfo', result);
            });
    }

    loadDetail(option = {}) {
        return this.$http.getEniDetail({eniId: this.data.get('context').eniId}, option).then(data => {
            this.data.set('instance', data);
        });
    }

    beforeEdit(type) {
        const TYPE_MAP = {
            name: 'name',
            desc: 'description'
        };
        const instance = this.data.get('instance');
        this.data.set(TYPE_MAP[type], instance[TYPE_MAP[type]]);
    }

    onInput(e, type) {
        let result;
        switch (type) {
            case 'name':
                const pattern = /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{0,64}$/;
                result = e.value.length <= 64 && pattern.test(e.value);
                break;
            case 'desc':
                result = e.value.length <= 200;
                break;
        }
        this.ref(`${type}EditBtn`).data.set('disabled', !result);
    }

    onEdit(type) {
        const TYPE_MAP = {
            name: 'name',
            desc: 'description'
        };
        const payload = {
            [TYPE_MAP[type]]: this.data.get(TYPE_MAP[type])
        };
        this.$http
            .updateEni(this.data.get('instance.eniUuid'), payload)
            .then(() => {
                this.editCancel(type);
                this.loadDetail({'x-silent-codes': ['NoSuchObject']});
                this.data.get('context')?.updateName();
            })
            .catch(() => {
                this.editCancel(type);
                this.loadDetail({'x-silent-codes': ['NoSuchObject']});
            });
    }

    editCancel(type) {
        this.ref(`${type}Edit`).data.set('visible', false);
    }
    onRegionChange() {
        location.hash = '#/vpc/eni/list';
    }
}
export default San2React(Processor.autowireUnCheckCmpt(EniDetail));
