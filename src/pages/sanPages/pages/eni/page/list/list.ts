/**
 * @file network/eni/pages/List.js
 * <AUTHOR>
 */

import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {Notification} from '@baidu/sui';
import {OutlinedRefresh, OutlinedPlus, OutlinedEditingSquare, OutlinedDownload} from '@baidu/sui-icon';
import {EVENTS, events} from 'framework';

import {EniStatus} from '@/pages/sanPages/common';
import zone from '@/pages/sanPages/utils/zone';
import '../../style/list.less';
import {$flag as FLAG, utcToTime} from '@/pages/sanPages/utils/helper';
import BindInstance from '../../components/bind-instance';
import Confirm from '@/pages/sanPages/components/confirm';
import BcmDetail from '../../components/bcmDetail';
import rules from './rules';
import {DocService} from '@/pages/sanPages/common';
import testID from '@/testId';
import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import IntroducePanel from '@/pages/sanPages/components/introducePanel/introducePanel';

const {invokeSUI, invokeSUIBIZ, template, invokeComp, invokeAppComp} = decorators;
const tpl = html` <div>
    <s-app-list-page class="{{klass}}">
        <div class="vpc-eni-header" slot="pageTitle">
            <div class="eni-widget">
                <div class="widget-left">
                    <span class="title">{{title}}</span>
                    <vpc-select class="vpc-select" on-change="vpcChange" on-int="vpcInt" />
                </div>
                <div class="widget-right">
                    <a
                        s-ref="introduce"
                        href="javascript:void(0)"
                        class="help-file function-introduce"
                        on-click="handleShowCard"
                        on-mouseenter="handleMouseEnter('introduce')"
                        on-mouseleave="handleMouseLeave('introduce')"
                        s-if="{{!FLAG.NetworkSupportXS}}"
                    >
                        <img class="s-icon" src="{{introduceIcon}}" />功能简介
                    </a>
                    <a
                        href="{{DocService.eni_helpFile}}"
                        target="_blank"
                        class="help-file"
                        s-if="{{!FLAG.NetworkSupportXS}}"
                    >
                        <s-icon name="warning-new" />帮助文档
                    </a>
                </div>
            </div>
            <introduce-panel
                isShow="{{show}}"
                klass="{{'endpoint-peerconn-wrapper'}}"
                title="{{introduceTitle}}"
                description="{{description}}"
                introduceOptions="{{introduceOptions}}"
                on-toggle="handleToggle($event)"
            ></introduce-panel>
        </div>
        <div class="list-page-tb-left-toolbar" slot="bulk">
            <s-tooltip
                trigger="{{eniSinDisable.disable || createBtn.disable || iamPass.disable ? 'hover' : ''}}"
                placement="top"
            >
                <!--bca-disable-next-line-->
                <div slot="content">{{eniSinDisable.message || iamPass.message || createBtn.tip | raw}}</div>
                <s-button
                    skin="primary"
                    on-click="onCreate"
                    data-test-id="${testID.eni.listCreateBtn}"
                    disabled="{{eniSinDisable.disable || iamPass.disable || createBtn.disable}}"
                >
                    <outlined-plus />
                    创建弹性网卡
                </s-button>
            </s-tooltip>
            <s-tip-button
                disabled="{{release.disable}}"
                placement="top"
                on-click="onRelease"
                class="left_class"
                data-test-id="${testID.eni.listDeleteBtn}"
                isDisabledVisibile="{{true}}"
            >
                <div slot="content">
                    <!--bca-disable-next-line-->
                    {{release.message | raw}}
                </div>
                释放
            </s-tip-button>
            <edit-tag
                s-else
                class="left_class"
                selectedItems="{{selectedItems}}"
                on-success="refresh"
                type="ENI"
            ></edit-tag>
        </div>
        <div slot="filter">
            <div class="filter-buttons-wrap">
                <search-tag
                    class="search-eni"
                    s-ref="search"
                    serviceType="ENIC"
                    searchbox="{=searchbox=}"
                    on-search="onSearch"
                    isShowResGroup="{{false}}"
                ></search-tag>
                <s-button on-click="refresh" class="s-icon-button fresh_class left_class" style="margin-right: 0px;"
                    ><outlined-refresh class="icon-class"
                /></s-button>
                <s-button
                    on-click="onDownload"
                    class="s-icon-button download-icon"
                    track-id="ti_vpc_enic_download"
                    track-name="下载"
                    style="margin-right: 8px;"
                    ><outlined-download class="icon-class"
                /></s-button>
                <custom-column
                    columnList="{{customColumn.datasource}}"
                    initValue="{{customColumn.value}}"
                    type="eni"
                    on-init="initColumns"
                    on-change="onCustomColumns"
                >
                </custom-column>
            </div>
        </div>
        <s-table
            columns="{{table.columns}}"
            loading="{{table.loading}}"
            error="{{table.error}}"
            selection="{=table.selection=}"
            datasource="{{table.datasource}}"
            on-selected-change="tableSelected($event)"
            on-filter="onFilter"
            on-sort="onSort"
            data-test-id="${testID.eni.listTable}"
        >
            <div slot="empty">
                <s-empty on-click="onCreate" class="{{iamPass.disable ? 'create-disable' : ''}}"> </s-empty>
            </div>
            <div slot="error">
                啊呀，出错了
                <a href="javascript:;" on-click="refresh">重新加载</a>
            </div>
            <div slot="c-id">
                <span class="truncated" title="{{row.name}}">
                    <a
                        href="#/vpc/eni/detail?vpcId={{row.vpcId}}&eniId={{row.eniUuid}}&shortEniId={{row.eniId}}"
                        data-track-name="详情"
                        data-testid="${testID.eni.listName}{{rowIndex}}"
                    >
                        {{row.name}}
                    </a>
                </span>
                <s-popover
                    s-ref="{{'nameEdit'+rowIndex}}"
                    placement="top"
                    trigger="click"
                    class="edit-popover-class {{row.source | sourceEipClass}}"
                >
                    <div class="edit-wrap" slot="content">
                        <s-input
                            value="{=instance.name=}"
                            width="320"
                            placeholder="请输入名称"
                            on-input="onEditInput($event, rowIndex, 'name')"
                        />
                        <div class="edit-tip">支持大小写字母，数字，中文和"-_/.”以字母或者中文开头，不超过65个字</div>
                        <s-button
                            skin="primary"
                            s-ref="{{'nameEditBtn' + rowIndex}}"
                            disabled="{{true}}"
                            on-click="editConfirm(row, rowIndex, 'name')"
                        >
                            确定</s-button
                        >
                        <s-button on-click="editCancel(row, rowIndex, 'name')">取消</s-button>
                    </div>
                    <outlined-editing-square
                        s-if="row.name"
                        class="name-icon {{row.source | iconClass}}"
                        on-click="beforeEdit(row, 'name')"
                    />
                </s-popover>
                <br />
                <span data-testid="${testID.eni.listId}{{rowIndex}}" class="truncated">{{row.eniId}}</span>
                <s-clip-board s-if="row.eniId" text="{{row.eniId}}" class="name-icon" />
            </div>
            <div slot="c-resourceGroups">
                <div s-if="row.resourceGroups && row.resourceGroups.length">
                    <p s-for="item in row.resourceGroups">{{item.resourceGroupName}}</p>
                </div>
                <span s-else>-</span>
            </div>
            <div slot="c-status">
                <span class="{{row.status | statusClass}}">{{row.status | statusText}}</span>
            </div>
            <div slot="c-networkInterfaceTrafficMode">
                <span>{{row.networkInterfaceTrafficMode | networkInterfaceTrafficModeText}}</span>
            </div>
            <div slot="c-vpcId">
                <span class="truncated">
                    <s-tooltip content="{{row.vpcName || '-'}}">
                        <a
                            data-testid="${testID.eni.listVpc}{{rowIndex}}"
                            href="#/vpc/instance/detail?vpcId={{row.vpcId}}"
                            >{{row.vpcName || '-'}}</a
                        >
                    </s-tooltip>
                </span>
                <br />
                <span class="truncated">{{row.vpcShortId || '-'}}</span>
            </div>
            <div slot="c-vpcId" else>
                <span>
                    {{vpcInfo.name}} {{vpcInfo.cidr ? '（' + vpcInfo.cidr + '）' : ''}} {{vpcInfo.ipv6Cidr ? '（' +
                    vpcInfo.ipv6Cidr + '）' : ''}}
                </span>
            </div>
            <div slot="c-tag">
                <s-tooltip>
                    <div slot="content">
                        <p s-if="!row.tags || row.tags.length < 1">-</p>
                        <p s-else s-for="item in row.tags">{{item.tagKey + ':' + item.tagValue}}</p>
                    </div>
                    <span s-if="!row.tags || row.tags.length < 1"> - </span>
                    <div s-else s-for="item,index in row.tags">
                        <span s-if="index <= 1"> {{item.tagKey + ':' + item.tagValue}} </span>
                        <div s-if="row.tags.length > 2 && index === 1">...</div>
                    </div>
                </s-tooltip>
            </div>
            <div slot="c-subnet">
                <a
                    class="truncated"
                    href="#/vpc/subnet/detail?subnetId={{row.subnetId}}"
                    title="{{row.subnetName}}（{{row.subnetCidr}}）"
                    data-testid="${testID.eni.listSubnet}{{rowIndex}}"
                >
                    {{row.subnetName}}
                </a>
                <br />
                <span s-f="row.subnetCidr">
                    {{row.subnetCidr ? ('(' + row.subnetCidr +')') : ''}} {{row.subnetIpv6Cidr ? ('(' +
                    row.subnetIpv6Cidr +')') : ''}}
                </span>
            </div>
            <div slot="c-ipv6Addr">
                <s-tooltip>
                    <p slot="content" s-for="item in row.ipv6Ips">
                        <span>{{item.privateIp || '-'}}（内) </span>
                        <span s-if="{{FLAG.NetworkSupportEip && item.eip}}">{{item.eip || '-'}}（公）</span>
                    </p>
                    <p s-for="item in row.ipv6sLimite">
                        <span>{{item.privateIp || '-'}}（内） </span>
                        <span s-if="{{FLAG.NetworkSupportEip && item.eip}}">{{item.eip || '-'}}（公）</span>
                    </p>
                    <p s-if="row.ipv6Ips && row.ipv6Ips.length > 3">...</p>
                </s-tooltip>
            </div>
            <div slot="c-az">{{row.az | az}}</div>
            <div slot="c-ips">
                <s-tooltip>
                    <p s-for="item in row.ips" slot="content">
                        <span>{{item.privateIp || '-'}}（内） </span>
                        <span s-if="{{FLAG.NetworkSupportEip}}">{{item.eip || '-'}}（公）</span>
                    </p>
                    <p s-for="item in row.ipsLimite">
                        <span>{{item.privateIp || '-'}}（内） </span>
                        <span s-if="{{FLAG.NetworkSupportEip}}">{{item.eip || '-'}}（公）</span>
                    </p>
                    <p s-if="row.ips && row.ips.length > 3">...</p>
                </s-tooltip>
            </div>
            <div slot="c-device">
                <span s-if="row.deviceShortId && row.bindDeviceType !== 'HPAS'">
                    <a href="{{row | linkHref}}"> {{row.deviceName || '-'}} | {{row.deviceShortId || '-'}} </a>
                </span>
                <span s-elif="{{row.source !== 'default' && row.status === 'inuse'}}"> - | - </span>
                <span s-else> {{row.deviceName || '-'}} | {{row.deviceShortId || '-'}} </span>
            </div>
            <div slot="c-securityGroups">
                <a href="#/vpc/eni/security?vpcId={{row.vpcId}}&eniId={{row.eniUuid}}&shortEniId={{row.eniId}}">
                    {{row | securityNum}}
                </a>
            </div>
            <div slot="c-description" class="desc-class">
                {{row.description || '-'}}
                <s-popover
                    s-ref="{{'descEdit'+rowIndex}}"
                    trigger="click"
                    class="edit-popover-class edit-popover-desc {{row.source | sourceEipClass}}"
                >
                    <div class="edit-wrap" slot="content">
                        <s-input
                            value="{=instance.desc=}"
                            width="160"
                            placeholder="请输入描述"
                            maxLength="200"
                            on-input="onEditInput($event, rowIndex, 'desc')"
                        />
                        <s-button
                            skin="primary"
                            s-ref="{{'descEditBtn'+rowIndex}}"
                            disabled="{{true}}"
                            on-click="editConfirm(row, rowIndex, 'desc')"
                            >确定</s-button
                        >
                        <s-button on-click="editCancel(row, rowIndex, 'desc')">取消</s-button>
                    </div>
                    <outlined-editing-square
                        s-if="row.name"
                        class="name-icon {{row.source | iconClass}}"
                        on-click="beforeEdit(row, 'desc')"
                    />
                </s-popover>
            </div>
            <div slot="c-createTime">{{row | createdTime}}</div>
            <div slot="c-operation" class="operation_class">
                <s-tooltip s-if="row.status === 'available'" trigger="{{row.source === 'ltgw'}}" placement="top">
                    <!--bca-disable-next-line-->
                    <div slot="content">请删除二层网关</div>
                    <s-button
                        skin="stringfy"
                        on-click="onDelete(row)"
                        disabled="{{row.source !== 'default'}}"
                        data-test-id="${testID.eni.listRelease}{{rowIndex}}"
                        >释放</s-button
                    >
                </s-tooltip>
                <br s-if="row.status === 'available'" />
                <s-button
                    data-test-id="${testID.eni.listMonitor}{{rowIndex}}"
                    s-if="FLAG.NetworkEniOpt"
                    skin="stringfy"
                    on-click="onMonitor(row)"
                    >监控</s-button
                >
                <span s-if="isCanOpt(row)">
                    <s-tooltip s-if="row.deviceId">
                        <s-button
                            skin="stringfy"
                            on-click="onDetach(row)"
                            disabled="{{row | isDetachDisabled}}"
                            data-test-id="${testID.eni.listUnmountHost}{{rowIndex}}"
                            >卸载主机</s-button
                        >
                    </s-tooltip>
                    <s-button
                        data-test-id="${testID.eni.listMountHost}{{rowIndex}}"
                        skin="stringfy"
                        s-else
                        on-click="bindEip(row)"
                        disabled="{{row.source !== 'default'}}"
                        >挂载主机</s-button
                    >
                </span>
                <s-button
                    s-if="isCanOpt(row) && row.source === 'default'"
                    skin="stringfy"
                    on-click="handleJumpIpManage(row)"
                    >绑定弹性公网IP</s-button
                >
            </div>
        </s-table>
        <s-pagination
            s-if="{{pager.count}}"
            slot="pager"
            layout="{{'total, pageSize, pager, go'}}"
            pageSize="{{pager.size}}"
            total="{{pager.count}}"
            page="{{pager.page}}"
            on-pagerChange="onPagerChange"
            on-pagerSizeChange="onPagerSizeChange"
        />
    </s-app-list-page>
</div>`;

@template(tpl)
@invokeComp('@bind-instance', '@edit-tag', '@search-tag', '@vpc-select', '@custom-column')
@invokeAppComp
@invokeSUI
@invokeSUIBIZ
class EniList extends Component {
    static components = {
        'outlined-refresh': OutlinedRefresh,
        'outlined-plus': OutlinedPlus,
        'outlined-editing-square': OutlinedEditingSquare,
        'outlined-download': OutlinedDownload,
        'introduce-panel': IntroducePanel
    };
    initData() {
        let allColumns = [
            {
                name: 'id',
                label: '实例名称/ID',
                minWidth: 160,
                fixed: 'left'
            },
            {
                name: 'status',
                label: '状态',
                width: 100,
                filter: {
                    options: [
                        {
                            text: '全部',
                            value: ''
                        },
                        ...EniStatus.toArray()
                    ],
                    value: ''
                }
            },
            {
                name: 'vpcId',
                label: '所在网络',
                width: 160
            },
            {
                name: 'subnet',
                label: '所在子网',
                width: 140
            },
            {
                name: 'ipv6Addr',
                label: 'IPv6 IP',
                width: 250
            },
            {
                name: 'ips',
                label: 'IP地址',
                width: 240
            },
            {
                name: 'az',
                label: '可用区',
                width: 80
            },
            {
                name: 'device',
                label: '挂载主机',
                width: 120
            },
            {
                name: 'securityGroups',
                label: '关联安全组',
                width: 100
            },
            {
                name: 'tag',
                label: '标签',
                width: 100
            },
            {
                name: 'description',
                label: '描述',
                minWidth: 120
            },
            {
                name: 'createTime',
                label: '创建时间',
                width: 120
            },
            {
                name: 'operation',
                label: '操作',
                width: 120,
                sortable: false,
                fixed: 'right'
            }
        ];
        let customColumnDb = allColumns.map(item => ({
            text: item.label,
            value: item.name,
            disabled: item.name === 'id' || item.name === 'opt'
        }));
        return {
            FLAG,
            title: '弹性网卡',
            klass: 'vpc-eni-list',
            vpcId: '',
            table: {
                loading: false,
                error: null,
                datasource: [],
                selection: {
                    mode: 'multi',
                    selectedIndex: [],
                    selectedItems: [],
                    disabledIndex(item) {
                        return item.source !== 'default';
                    }
                },
                allColumns
            },
            searchbox: {
                keyword: '',
                placeholder: '请输入实例名称进行搜索',
                keywordType: ['name'],
                keywordTypes: [
                    {value: 'name', text: '实例名称'},
                    {value: 'eniId', text: '实例ID'},
                    {value: 'privateIp', text: '实例内网IP'},
                    {value: 'eip', text: '实例公网IP'},
                    {value: 'subnetName', text: '所在子网'},
                    {value: 'deviceName', text: '挂载主机名称'},
                    {value: 'deviceShortId', text: '挂载主机ID'},
                    {value: 'tag', text: '标签'}
                ]
            },
            pager: {
                size: 10,
                page: 1,
                count: 0,
                datasource: [
                    {text: '10', value: 10},
                    {text: '20', value: 20},
                    {text: '50', value: 50},
                    {text: '100', value: 100}
                ]
            },
            customColumn: {
                value: [
                    'id',
                    'resourceGroups',
                    'status',
                    'vpcId',
                    'subnet',
                    'ipv6Addr',
                    'ips',
                    'az',
                    'device',
                    'tag',
                    'securityGroups',
                    'operation',
                    'description'
                ],
                datasource: customColumnDb
            },
            eniSinDisable: {},
            iamPass: {},
            DocService,
            urlQuery: getQueryParams(),
            show: true,
            introduceTitle: '弹性网卡简介',
            description:
                '弹性网卡可在多个云主机间自由迁移。通过在云主机上绑定多个弹性网卡，实现高可用网络方案；也可以在弹性网卡上绑定多个内网 IP，每个内网IP都可以绑定一个EIP，用户在配置好IP地址后通过配置应用策略即可实现不同业务流量以不同源IP从网卡流出，实现单主机多IP部署。',
            introduceIcon: INTRODUCE_ICON_COLOR,
            introduceEle: null
        };
    }

    filters = {
        statusClass(value) {
            return EniStatus.fromValue(value).styleClass || 'status error';
        },
        statusText(value) {
            return value ? EniStatus.getTextFromValue(value) : '异常';
        },
        networkInterfaceTrafficModeText(value) {
            if (value === 'standard') {
                return '普通型';
            } else {
                return 'RDMA型';
            }
        },
        az(az) {
            return zone.getLabel(az);
        },
        createdTime(item) {
            return utcToTime(item.createdTime);
        },
        securityNum(item) {
            return item.securityGroups?.length || item.enterpriseSecurityGroups?.length || 0;
        },
        linkHref(item) {
            if (item?.bindDeviceType === 'BBC') {
                return `/bbc/#/bbc/instance/detail/detail?instanceId=${item.deviceId}`;
            } else if (item?.bindDeviceType === 'DCC') {
                return `/bcc/#/dcc/host/detail?id=${item.deviceId}`;
            } else {
                return `/bcc/#/bcc/instance/detail?instanceId=${item.deviceId}`;
            }
        },
        sourceEipClass(value) {
            return value === 'default' ? '' : 'sourceEipClass';
        },
        iconClass(value) {
            return value === 'default' ? '' : 'iconClass';
        },
        isDetachDisabled(row: any) {
            const {bindDeviceType, source} = row;
            return source !== 'default';
        },
        disableDetachTip(row: any) {
            const {bindDeviceType} = row;
            let tip = '';
            // if (bindDeviceType === 'HPAS') {
            //     tip = '暂不支持卸载';
            // }
            return tip;
        }
    };

    async inited() {
        if (this.data.get('urlQuery.subnetName')) {
            this.data.set('searchbox.keywordType', ['subnetName']);
            this.data.set('searchbox.keyword', this.data.get('urlQuery.subnetName'));
        }
        let vpcId = this.data.get('urlQuery.vpcId');
        if (vpcId) {
            window.$storage.set('vpcId', vpcId);
        }
        events.on(EVENTS.ORGANIZATION_CHANGED, () => {
            this.loadPage();
        });
        this.getIamQuery();
        this.initSearchbox();
        this.getWhiteList();
        this.handleJumpFromMirror();
        let {eniSinDisable, release} = checker.check(rules, []);
        this.data.set('eniSinDisable', eniSinDisable);
        this.data.set('release', release);
    }
    attached() {
        this.data.set('introduceEle', this.ref('introduce'));
    }
    initSearchbox() {
        let searchBoxData = this.data.get('searchbox.keywordTypes');
        !FLAG.NetworkSupportEip &&
            this.data.set(
                'searchbox.keywordTypes',
                searchBoxData.filter(item => item.value !== 'eip')
            );
        let subnetId = this.data.get('urlQuery.subnetId');
        if (subnetId) {
            this.$http.getSubnetResourceDetail(subnetId).then(data => {
                this.data.set('subnetDetail', data);
                this.loadPage({subnetId});
            });
        } else {
            this.loadPage();
        }
    }
    getSearchCriteria() {
        const searchParam = this.ref('search') ? this.ref('search').getSearchCriteria() : {};
        const {pager, order, filter} = this.data.get('');
        let payload = {
            pageNo: pager.page,
            pageSize: pager.size,
            vpcId: window.$storage.get('vpcId')
        };
        if (!payload.vpcId) {
            delete payload.vpcId;
        }
        return {...payload, ...order, ...filter, ...searchParam};
    }

    handleJumpFromMirror() {
        const id = this.data.get('urlQuery.id');
        if (id) {
            this.data.set('searchbox.keyword', id);
            this.data.set('searchbox.keywordType', ['eniId']);
            this.nextTick(() => {
                this.onSearch();
            });
        }
    }

    loadPage(subnetId = {}) {
        const payload = this.getSearchCriteria();
        const param = {...payload, ...subnetId};
        if (param.subnetId && !param.vpcId) {
            param.vpcId = this.data.get('subnetDetail').vpcId;
        }
        this.data.set('param', param);
        this.resetTable();
        this.data.set('table.loading', true);
        this.$http
            .getEniList(param)
            .then(data => {
                this.data.set('pager.count', data.totalCount);
                data.result.map(item => (item.ipsLimite = Array.isArray(item.ips) ? item.ips.slice(0, 3) : []));
                data.result.map(
                    item => (item.ipv6sLimite = Array.isArray(item.ipv6Ips) ? item.ipv6Ips.slice(0, 3) : [])
                );
                this.data.set('table.datasource', data.result);
            })
            .finally(() => this.data.set('table.loading', false));
    }

    resetTable() {
        this.data.set('selectedItems', []);
        this.data.set('table.selection.selectedIndex', []);
        this.data.set('table.datasource', []);
    }

    loadVpcInfo() {
        let vpcId = window.$storage.get('vpcId');
        return this.$http
            .getVpcDetail({
                vpcIds: vpcId ? [vpcId] : []
            })
            .then(data => {
                this.data.set('vpcInfo', !vpcId ? u.find(data, item => !u.isEmpty(item)) || {} : data[vpcId] || {});
            });
    }

    getEniQuota() {
        let vpcId = window.$storage.get('vpcId');
        let vpcInfo = this.data.get('vpcInfo');
        return this.$http.getEniQuota({vpcId}).then(data => {
            // 这个配额校验逻辑好像没生效，暂时注释
            // if (!FLAG.NetworkEniOpt) {
            //     let createEni = this.getPermission('', 'createEni', {quota: data, vpcInfo});
            //     this.data.set('createBtn.disabled', createEni.disable);
            //     this.data.set('createBtn.tip', createEni.message);
            // }
            this.data.set('quota', data.total);
        });
    }

    getPermission(data, oprName, options) {
        const result = checker.check(rules, data, oprName, options);
        return result[oprName];
    }

    vpcChange() {
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    // 设置表格列
    setTableColumns(customColumnNames) {
        customColumnNames = customColumnNames ? customColumnNames : this.data.get('customColumn.value');
        let allColumns = this.data.get('table.allColumns');
        let columns = [];
        allColumns.forEach(item => customColumnNames.indexOf(item.name) > -1 && columns.push(item));
        this.data.set('table.columns', columns);
    }

    initColumns(value) {
        this.setTableColumns(value);
    }
    // 自定义表格列
    onCustomColumns(value) {
        this.data.set('customColumn.value', value);
        this.setTableColumns(value);
    }

    vpcInt() {
        if (!FLAG.NetworkEniOpt) {
            this.loadVpcInfo().then(() => this.getEniQuota());
        }
    }

    onCreate() {
        location.hash = '#/vpc/eni/create';
    }

    bindEip(row) {
        const dialog = new BindInstance({
            data: {
                title: '挂载主机',
                listRequester: query =>
                    this.$http.eniServerList(
                        u.extend(
                            {
                                vpcId: row.vpcId,
                                eniId: row.eniUuid
                            },
                            query
                        )
                    )
            }
        });
        dialog.on('confirmed', ({instanceUuid, type}) => {
            const payload: any = {
                deviceId: instanceUuid,
                type
            };
            if (type === 'HPAS') {
                delete payload.type;
                payload.deviceType = 'hpas';
            }
            this.$http.attachEni(row.eniUuid, payload).then(() => {
                Notification.success('挂载成功');
                this.loadPage();
            });
        });
        dialog.attach(document.body);
    }

    onDetach(row) {
        const confirm = new Confirm({
            data: {
                title: '请确认',
                content: '确认卸载该主机？'
            }
        });
        confirm.on('confirm', () => {
            const {deviceId, type, bindDeviceType} = row;
            const payload: any = {deviceId, type};
            if (bindDeviceType === 'HPAS') {
                delete payload.type;
            }
            this.$http.detachEni(row.eniUuid, payload).then(() => this.loadPage());
        });
        confirm.attach(document.body);
    }

    onDelete(row) {
        const confirm = new Confirm({
            data: {
                title: '请确认',
                content: '确认释放该弹性网卡资源？若关联添加了解析，请到内网DNS服务中删除。'
            }
        });
        confirm.on('confirm', () => {
            this.$http.deleteEni(row.eniUuid).then(() => this.loadPage());
        });
        confirm.attach(document.body);
    }

    onFilter(e) {
        let name = e.field.name;
        let value = e[name] ? e[name] : '';
        this.data.set('filter.' + name, value);
        this.loadPage();
    }

    onMonitor(row) {
        const bcm = new BcmDetail({
            data: {
                name: row.name,
                payload: row
            }
        });
        bcm.attach(document.body);
    }

    isCanOpt(row) {
        return u.indexOf([EniStatus.ATTACHING, EniStatus.DETACHING], row.status) === -1;
    }

    beforeEdit(row, type) {
        const keyMap = {
            name: 'name',
            desc: 'description'
        };
        this.data.set(`instance.${type}`, row[keyMap[type]]);
    }

    onEditInput(e, rowIndex, type) {
        let result = true;
        if (type === 'name') {
            const pattern = /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{0,64}$/;
            result = pattern.test(e.value);
        } else if (type === 'description') {
            result = e.value.length <= 200;
        }
        this.data.set(`instance.${type}`, e.value);
        this.ref(`${type}EditBtn${rowIndex}`).data.set('disabled', !result);
    }

    editConfirm(row, rowIndex, type) {
        const keyMap = {
            name: 'name',
            desc: 'description'
        };
        const payload = {
            [keyMap[type]]: this.data.get(`instance.${type}`)
        };
        this.ref(`${type}EditBtn${rowIndex}`).data.set('disabled', true);
        this.$http.updateEni(row.eniUuid, payload).then(() => {
            this.loadPage();
            this.ref(`${type}EditBtn${rowIndex}`).data.set('disabled', false);
        });
    }

    editCancel(row, rowIndex, type) {
        this.ref(`${type}Edit${rowIndex}`).data.set('visible', false);
    }

    onPagerSizeChange({value}) {
        this.data.set('pager.page', 1);
        this.data.set('pager.size', value.pageSize);
        this.loadPage();
    }

    // 搜索事件
    onSearch(e) {
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    onPagerChange({value}) {
        this.data.set('pager.page', value.page);
        this.loadPage();
    }

    tableSelected({value}) {
        const {release} = checker.check(rules, value.selectedItems, '');
        this.data.set('release', release);
        this.data.set('selectedItems', value.selectedItems);
    }

    refresh() {
        let param = this.data.get('param');
        let subnetId = this.data.get('urlQuery.subnetId');
        param.subnetId ? this.loadPage({subnetId}) : this.loadPage();
    }
    onRegionChange() {
        window.$storage.set('vpcId', '');
        location.reload();
    }
    getWhiteList() {
        const whiteList = window.$storage.get('commonWhite');
        if (whiteList?.EriWhiteList) {
            let allColumns = this.data.get('table.allColumns');
            let customColumn = this.data.get('customColumn.value');
            let customColumnDb = this.data.get('customColumn.datasource');
            customColumn.push('networkInterfaceTrafficMode');
            allColumns.splice(2, 0, {
                name: 'networkInterfaceTrafficMode',
                label: '类型',
                width: 100,
                filter: {
                    options: [
                        {
                            text: '全部',
                            value: ''
                        },
                        {
                            text: '普通型',
                            value: 'standard'
                        },
                        {
                            text: 'RDMA型',
                            value: 'highPerformance'
                        }
                    ],
                    value: ''
                }
            });
            customColumnDb.splice(2, 0, {
                text: '类型',
                value: 'networkInterfaceTrafficMode',
                selected: true
            });
            this.setTableColumns(customColumn);
        }
    }
    onRelease() {
        let eniUuids = this.data.get('selectedItems').map(item => item.eniUuid);
        let confirm = new Confirm({
            data: {
                title: '释放前确认',
                content: '确认删除所选中的弹性网卡？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http.eniBatchDelete({eniUuids}).then(() => {
                this.data.set('pager.page', 1);
                this.loadPage();
            });
        });
    }
    getIamQuery() {
        this.$http.getInterfaceIam({interfaceName: 'createEni'}).then(res => {
            if (!res.requestId && !res.masterAccount) {
                if (!res.interfacePermission) {
                    this.data.set('iamPass', {
                        disable: true,
                        message: '您没有创建弹性网卡权限，请联系主用户添加'
                    });
                } else {
                    this.data.set('iamPass', {
                        disable: false,
                        message: ''
                    });
                }
            } else {
                this.data.set('iamPass', {
                    disable: false,
                    message: ''
                });
            }
        });
    }
    // 下载
    onDownload() {
        let ids = [];
        ids = this.data.get('selectedItems').map(item => {
            return item.eniId;
        });
        let filter = 'ids=' + ids.join(',');
        window.open(`/api/enic/download?` + filter);
    }
    handleToggle(flag) {
        this.data.set('show', flag);
        this.data.set('introduceIcon', INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#151B26';
    }
    handleShowCard() {
        if (!this.data.get('show')) {
            this.data.set('show', true);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type) {
        this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#2468F2';
    }
    handleMouseLeave(type) {
        const isShow = this.data.get('show');
        this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        !isShow && (ele.style.color = '#151B26');
    }
    handleJumpIpManage(row: any) {
        const {vpcId, eniUuid, eniId} = row;
        window.location.hash = `#/vpc/eni/ip?vpcId=${vpcId}&eniId=${eniUuid}&shortEniId=${eniId}`;
    }
}
export default San2React(Processor.autowireUnCheckCmpt(EniList));
