import {ContextService} from '@/pages/sanPages/common';
const AllRegion = ContextService.getEnum('AllRegion');
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';

export default {
    createEni: [
        {
            required: false,
            message: ''
        },
        {
            custom(data, options) {
                if (options.quota && options.quota.free <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: '弹性网卡配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `弹性网卡配额不足，<a href="/quota_center/#/quota/apply/create?serviceType=ENI&region=${
                                window.$context.getCurrentRegionId()
                            }&cloudCenterQuotaName=eniQuota" target="_blank">去申请配额</a>`
                        };
                    }
                }
            }
        }
    ],
    changeResource: [
        {
            required: true,
            message: '请先选择实例对象'
        }
    ],
    addIp: [
        {
            custom(data, options) {
                if (!options.hasQuota) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: 'IP数配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `IP数配额不足。如需增加配额请提交<a href="${
                                ContextService.Domains.ticket
                            }/#/ticket/create" target="_blank">工单</a>`
                        };
                    }
                } else if (!options.subnetId) {
                    return {
                        disable: true,
                        message: '请先选择子网'
                    };
                }
            }
        }
    ],
    addEniIp: [
        {
            custom(data, options) {
                if (options.noStatus) {
                    return {
                        disable: true,
                        message: ''
                    };
                } else if (options.noFree) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: '弹性网卡辅助IP配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `弹性网卡辅助IP配额不足。如需增加配额请提交<a href="${
                                ContextService.Domains.ticket
                            }/#/ticket/create" target="_blank">工单</a>`
                        };
                    }
                }
            }
        }
    ],
    joinGroup: [
        {
            custom(data, options) {
                if (!options.data) {
                    return {
                        disable: true,
                        message: ''
                    };
                }
            }
        }
    ],
    editTag: [
        {
            required: true,
            message: '请先选择实例对象'
        }
    ],
    eniSinDisable: [
        {
            required: false
        },
        {
            custom(data) {
                if (window.$context.getCurrentRegionId() === AllRegion.SIN) {
                    return {
                        disable: true,
                        message: '新加坡地域资源售罄，请您切换到其他地域创建'
                    };
                }
            }
        }
    ],
    release: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            custom(data, options) {
                if (data.some(item => item.status !== 'available')) {
                    return {
                        disable: true,
                        message: '当前部分实例状态不支持释放'
                    };
                }
                if (data.length > 20) {
                    return {
                        disable: true,
                        message: '弹性网卡批量删除的数量不能超过20个，请您减少批量删除的数量'
                    };
                }
                // if (data.some(item => item.ipv6Ips && item.ipv6Ips.find(item => item.eip))) {
                //     return {
                //         disable: true,
                //         message: data.length > 1 ? '当前部分实例绑定弹性公网IPv6，请先解绑后可行释放' : '当前实例已绑定弹性公网IPv6，请先解绑后可行释放'
                //     };
                // }
            }
        }
    ]
};
