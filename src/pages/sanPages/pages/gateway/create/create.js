/*
 * @Description: 网关限速创建页
 */

import {html, decorators, redirect, CreatePage, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import u from 'lodash';
import {DirectionType} from '@/pages/sanPages/common/enum';
import {convertCidrToBinary} from '@/pages/sanPages/utils/common';
import RULE from '@/pages/sanPages/utils/rule';
import {serviceTypeUrl, checkSts} from '@/pages/sanPages/utils/config';
import {parseQuery} from '@/utils';

import './create.less';
const requestUrl = {
    vpc: '/api/network/v1/flowlog/vpcList'
};

const AllRegion = window.$context.getEnum('AllRegion');

const {invokeSUI, invokeSUIBIZ, invokeAppComp, template} = decorators;

const tpl = html`
   <template>
        <s-app-create-page class="vpc-gateway-create"
            backTo="{{pageNav.backUrl}}"
            backToLabel="{{pageNav.backLabel}}"
            pageTitle="{{pageNav.title}}">
            <s-form s-ref="form"
                data="{=formData=}"
                rules="{{rules}}"
                label-col="{{labelCol}}"
                wrapper-col="{{wrapperCol}}"
                label-align="left">
                <div class="content-box form-part-wrap">
                    <h4>配置信息</h4>
                    <s-form-item class="gateway-name-wrapper" prop="name" label="规则名称：">
                        <s-input
                            value="{=formData.name=}"
                            placeholder="请输入规则名称"
                            width="400">
                            <template slot="suffix">{{formData.name.length}}/65</template>
                        </s-input>
                    </s-form-item>
                    <s-form-item prop="ruleType" label="规则类型：">
                        <s-radio-radio-group
                            datasource="{{ruleTypeTypeList}}"
                            value="{=formData.ruleType=}"
                            radioType="button"
                            disabled="{{isEdit}}"
                        >
                        </s-radio-radio-group>
                    </s-form-item>
                    <s-form-item prop="resourceType" label="资源类型：">
                        <s-radio-radio-group
                            width="180"
                            radioType="button"
                            disabled="{{isEdit}}"
                            class="no-res-wrap"
                            on-change="getResourceIds($event)"
                            value="{=formData.resourceType=}"
                        >
                            <span s-for="item, index in resourceTypes">
                                <s-popover trigger="{{item.disabled ? 'hover' : ''}}">
                                    <div slot="content">
                                        <!--bca-disable-next-line-->
                                        {{item.value | noOpenTip | raw}}
                                    </div>
                                    <s-radio
                                        value="{{item.value}}"
                                        disabled="{{item.disabled}}"
                                        class="{{(index === 0 && 'radius-left') || (index === resourceTypes.length - 1 && 'radius-right')}}"
                                        label="{{item.text}}">
                                    </s-radio>
                                </s-popover>
                            </span>
                        <s-radio-radio-group/>
                    </s-form-item>
                    <s-form-item s-if="{{formData.resourceType !== 'csn' && !isEdit}}" prop="resourceVpc" label="资源所在网络：">
                        <s-select
                            filterable
                            width="200"
                            disabled="{{isEdit || noResOpen}}"
                            placeholder="请选择所在网络"
                            datasource="{{resourceIds}}"
                            value="{=formData.resourceVpc=}"
                            on-change="resourceVpcChange" />
                    </s-form-item>
                    <s-form-item s-if="{{formData.resourceType !== 'csn'}}" prop="resourceId" label="实例名称/ID">
                        <s-select
                            filterable
                            width="200"
                            disabled="{{isEdit || noResOpen}}"
                            placeholder="请选择"
                            datasource="{{resourceIdList}}"
                            value="{=formData.resourceId=}"
                        />
                    </s-form-item>
                    <s-form-item s-if="{{formData.resourceType === 'csn'}}" prop="resourceId" label="实例ID：">
                        <s-select
                            filterable
                            width="200"
                            disabled="{{isEdit || noResOpen}}"
                            placeholder="请选择"
                            datasource="{{resourceIdList}}"
                            value="{=formData.resourceId=}"
                            on-change="getCsnResource" />
                    </s-form-item>
                    <s-form-item
                        class="center_class"
                        s-if="{{formData.resourceType === 'csn'}}"
                        prop="limitType"
                        label="限速类型：">
                        <s-radio-radio-group
                            disabled="{{isEdit}}"
                            datasource="{{limitTypeList}}"
                            on-change="limitTypeChange($event, rowIndex)"
                            value="{=formData.limitType=}"
                        >
                        </s-radio-radio-group>
                    </s-form-item>
                    <s-form-item s-if="{{formData.resourceType === 'csn' && formData.limitType === 'network'}}" prop="csnNet" label="网络实例ID：">
                        <s-select
                            width="200"
                            disabled="{{isEdit}}"
                            placeholder="请选择"
                            datasource="{{csnNetResouce}}"
                            value="{=formData.csnNet=}"/>
                    </s-form-item>
                    <s-form-item
                        s-if="{{formData.resourceType === 'csn' && formData.limitType === 'cross'}}"
                        prop="subServiceType"
                        label="互通类型：">
                        <s-radio-radio-group
                            disabled="{{isEdit}}"
                            radioType="button"
                            datasource="{{subServiceList}}"
                            on-change="subServiceTypeChange"
                            value="{=formData.subServiceType=}"
                        >
                        </s-radio-radio-group>
                    </s-form-item>
                    <s-form-item
                        label="互通地域："
                        prop="peerRegion"
                        s-if="{{formData.limitType === 'cross' && formData.subServiceType === 'PEER_CLOUD'}}"
                    >
                        <s-select
                            value="{=localRegionList[0].value=}"
                            datasource="{{localRegionList}}"
                            placeholder="请选择"
                            disabled="{{true}}">
                        </s-select>
                        <s-icon name="sort-normal" />
                        <s-select
                            disabled="{{isEdit}}"
                            value="{=formData.peerRegion=}"
                            placeholder="请选择">
                            <s-select-option
                                s-for="item in peerRegionList"
                                value="{{item.value}}"
                                label="{{item.text}}"
                            ></s-select-option>
                        </s-select>
                    </s-form-item>
                    <s-form-item
                        s-if="{{formData.subServiceType === 'PEER_EDGE' && formData.limitType === 'cross'}}"
                        label="互通地域："
                        prop="peerRegion">
                        <s-table
                            class="region-table"
                            s-ref="table"
                            columns="{{peerEdge.columns}}"
                            datasource="{{peerEdge.datasource}}">
                            <div slot="c-localRegion">
                                <s-select
                                    value="{=row.localRegionList[0].value=}"
                                    datasource="{{row.localRegionList}}"
                                    width="130"
                                    disabled="{{true}}"
                                ></s-select>
                            </div>
                            <div slot="c-peerRegion">
                                <s-select
                                    disabled="{{isEdit}}"
                                    on-change="peerRegionChange"
                                    datasource="{{row.peerRegionList}}"
                                    value="{=formData.peerRegion=}"
                                    width="130"
                                ></s-select>
                            </div>
                        </s-table>
                    </s-form-item>
                    <s-form-item prop="direction" label="限速方向：">
                        <s-radio-radio-group
                            disabled="{{isEdit}}"
                            datasource="{{directionList}}"
                            value="{=formData.direction=}"
                            radioType="button"
                        >
                        </s-radio-radio-group>
                    </s-form-item>
                    <s-form-item prop="cidr" label="源网段：">
                        <s-input
                            disabled="{{isEdit}}"
                            value="{=formData.cidr=}"
                            placeholder="请输入源网段"
                            width="400">
                        </s-input>
                    </s-form-item>
                    <s-form-item prop="bandwidth" label="带宽：" help="{{maxBandWidth ? '当前最大带宽' + maxBandWidth  : ''}}">
                        <template slot="label">
                            {{'带宽：'}}
                            <s-tip
                                placement="top"
                                class="inline-tip"
                                content="{{bandWidthTip}}"
                                skin="question"/>
                        </template>
                        <s-input-number value="{=formData.bandwidth=}" min="{{1}}" max="{{maxBandWidth}}"/>Mbps
                        </s-input>
                    </s-form-item>
                     <s-form-item prop="description" label="描述：">
                        <s-input-text-area value="{=formData.description=}"
                            width="366"
                            height="80"
                            placeholder="请输入" maxLength="{{200}}"/>
                    </s-form-item>
                </div>
            </s-form>
            <div slot="pageFooter" class="buybucket">
                <div class="buybucket-container">
                    <s-button
                        size="large"
                        disabled="{{confirmed}}"
                        skin="primary"
                        on-click="createGateway">
                        确定
                    </s-button>
                    <s-button
                        size="large"
                        on-click="cancel">
                        取消
                    </s-button>
                </div>
            </div>
        </s-app-create-page>
   </template>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class GatewayCreate extends CreatePage {
    initData() {
        return {
            labelCol: {span: 3},
            wrapperCol: {span: 19},
            pageNav: {
                title: '创建网关限速',
                backUrl: '/network/#/vpc/gateway/pc/list',
                backLabel: '返回网关限速列表'
            },
            ruleTypeTypeList: [
                {
                    text: 'IPv4',
                    value: '4'
                }
            ],
            resourceTypes: [
                {
                    text: '对等连接',
                    value: 'peerconn'
                },
                {
                    text: '专线网关',
                    value: 'et'
                },
                {
                    text: '云智能网',
                    value: 'csn'
                }
            ],
            limitTypeList: [
                {
                    text: '网络实例带宽',
                    value: 'network'
                },
                {
                    text: '跨地域带宽',
                    value: 'cross'
                }
            ],
            subServiceList: [
                {
                    text: '云间互通',
                    value: 'PEER_CLOUD'
                },
                {
                    text: '云边互通',
                    value: 'PEER_EDGE'
                }
            ],
            directionList: DirectionType.toArray('EGRESS'),
            formData: {
                ruleType: '4',
                resourceType: 'peerconn',
                name: '',
                direction: 'egress',
                resourceId: '',
                resourceVpc: '',
                description: '',
                limitType: 'network',
                subServiceType: 'LOCAL'
            },
            rules: {
                name: [
                    {required: true, message: '请输入名称'},
                    {
                        validator: (rule, value, callback) => {
                            let pattern = /^[a-zA-Z][\w\-\/\.]{0,64}$/;
                            if (!pattern.test(value)) {
                                return callback('支持大小写字母、数字以及-_ /.特殊字符，必须以字母开头，长度1-65');
                            }
                            callback();
                        }
                    }
                ],
                description: [{min: 0, max: 200, message: '长度0到200个字符'}],
                resourceVpc: [{required: true, message: '请选择'}],
                resourceId: [{required: true, message: '请选择'}],
                resourceType: [{required: true, message: '请选择'}],
                bandwidth: [{required: true, message: '请输入带宽'}],
                cidr: [
                    {required: true, message: '请输入源网段'},
                    {
                        validator: (rule, value, callback) => {
                            if (!new RegExp(RULE.IP_CIDR).test(value)) {
                                return callback('CIDR不合法');
                            }
                            let valueString = convertCidrToBinary(value);
                            let valueMask = value.split('/')[1];
                            if (valueString.substring(+valueMask, valueString.length).indexOf('1') > -1) {
                                return callback('CIDR不合法');
                            }
                            // 限制0.0.0.0或0.0.0.0/0网段
                            if (value === '0.0.0.0' || value === '0.0.0.0/0') {
                                return callback('CIDR不合法');
                            }
                            callback();
                        }
                    }
                ],
                limitType: [{required: true, message: '请选择'}],
                subServiceType: [{required: true, message: '请选择'}],
                peerRegion: [{required: true, message: '请选择'}],
                csnNet: [{required: true, message: '请选择'}]
            },
            localRegionList: [],
            peerRegionList: [],
            peerEdge: {
                columns: [
                    {
                        name: 'localRegion',
                        label: '中心地域',
                        width: 120
                    },
                    {
                        name: 'peerRegion',
                        label: '边缘节点',
                        width: 120
                    }
                ],
                datasource: []
            },
            urlQuery: parseQuery(window.location.hash)
        };
    }

    static computed = {
        bandWidthTip() {
            let {resourceType} = this.data.get('formData');
            return resourceType === 'peerconn'
                ? '带宽取值范围：用户购买的对等连接带宽最大值'
                : resourceType === 'et'
                  ? '带宽取值范围：用户购买的专线网关带宽最大值'
                  : '带宽取值范围：用户购买的云智能网带宽最大值';
        },
        maxBandWidth() {
            let {subServiceType, limitType, peerRegion, resourceType, csnNet} = this.data.get('formData');
            let bandwidth = 1000;
            if (resourceType === 'csn' && limitType === 'cross') {
                let peerList = [];
                if (subServiceType === 'PEER_CLOUD') {
                    peerList = this.data.get('peerRegionList');
                } else {
                    peerList = this.data.get('peerEdge.datasource[0].peerRegionList');
                }
                bandwidth = peerList?.find(item => item.value === peerRegion)?.bandwidth;
            } else {
                let resourceList =
                    resourceType === 'csn' ? this.data.get('csnNetResouce') : this.data.get('resourceIdList');
                bandwidth = resourceList?.find(item => item.value === csnNet)?.bandwidth;
            }
            return bandwidth;
        }
    };

    static filters = {
        noOpenTip(value) {
            const isSubUser = window.$context.isSubUser();
            let serviceText = this.data.get('resourceTypes').find(item => item.value === value)?.text;
            let url = serviceTypeUrl[value];
            let str = `您的账号当前未开通${serviceText}服务，请前往${serviceText}控制台开通。<a href="${url}">去开通</a>`;
            if (isSubUser) {
                str = `该功能需要开通${serviceText}服务，当前登录的子账号没有开通产品服务的权限，请联系主账号授予开通服务权限，或联系主账户开通服务后授权使用。`;
            }
            return str;
        }
    };

    async inited() {
        let resourceTypes = this.data.get('resourceTypes');
        this.data.set('resourceTypes', resourceTypes);
        const {id, resourceType} = this.data.get('urlQuery');
        if (resourceType) {
            let resourceList = resourceTypes.filter(item => item.value === resourceType);
            resourceList = checkSts(resourceList);
            this.data.set('resourceTypes', resourceList);
            resourceList[0].disabled && this.data.set('noResOpen', true);
            this.data.set('formData.resourceType', resourceType);
        }
        id && this.data.set('isEdit', true);
        id && this.getGateWayDetail(id);
        if (resourceType === 'csn') {
            if (window.$storage.get('becSts') && window.$storage.get('csnSts')) {
                try {
                    if (!window.$storage.get('bec-resource-open')) {
                        const {result} = await this.$http.getResource();
                        window.$storage.set('bec-resource-open', !!result);
                        !!result && this.getCsnEdgeNode();
                    } else {
                        this.getCsnEdgeNode();
                    }
                } catch (error) {
                    window.$storage.set('bec-resource-open', false);
                }
            }
            const localRegionList = [
                {
                    value: window.$context.getCurrentRegionId(),
                    text: AllRegion.getTextFromValue(window.$context.getCurrentRegionId())
                }
            ];
            this.data.set('localRegionList', localRegionList);
            this.data.set('peerEdge.datasource', [
                {
                    localRegionList,
                    peerRegionList: []
                }
            ]);
            if (!id && window.$storage.get('csnSts')) {
                this.getCsnResource();
            }
        } else {
            this.getVpcResourceList();
        }
    }

    getCsnEdgeNode() {
        this.$http.getEdgeNode().then(res => {
            this.data.set('edgeNodes', res.edgeNodes);
        });
    }

    getCrossRegions() {
        let {resourceId, subServiceType, limitType} = this.data.get('formData');
        if (resourceId && subServiceType && limitType === 'cross') {
            this.$http
                .gatewayLimitRuleRegions({
                    serviceType: 'CSN',
                    csnId: resourceId,
                    peerCloud: subServiceType === 'PEER_CLOUD' ? true : false
                })
                .then(res => {
                    let localRegionList = this.data.get('localRegionList');
                    // 过滤当前选中的region
                    let regions = res.regions.filter(item => item !== localRegionList[0].value);
                    if (subServiceType === 'PEER_CLOUD') {
                        const peerRegionList = regions.map(data => ({
                            value: data.region,
                            text: AllRegion.getTextFromValue(data.region),
                            bandwidth: data.bandwidth
                        }));
                        this.data.set('peerRegionList', peerRegionList);
                    } else {
                        const peerRegionList = regions.map(data => ({
                            value: data.region,
                            text: u.find(this.data.get('edgeNodes') || [], item => item.nodeId === data.region)
                                .nodeName,
                            bandwidth: data.bandwidth
                        }));
                        this.data.set('peerEdge.datasource', [
                            {
                                localRegionList,
                                peerRegionList
                            }
                        ]);
                    }
                });
        }
    }

    getGateWayDetail(id) {
        this.$http.gatewayLimitRuleDetail(id).then(data => {
            this.data.set(
                'resourceTypes',
                this.data.get('resourceTypes').filter(item => item.value === data.serviceType)
            );
            this.data.set('formData.ruleType', data.ruleType);
            this.data.set('formData.name', data.name);
            this.data.set('formData.description', data.description);
            this.data.set('formData.resourceType', data.serviceType);
            this.data.set('formData.bandwidth', data.bandwidth);
            this.data.set('formData.direction', data.direction);
            this.data.set('formData.cidr', data.cidr);
            this.data.set('formData.resourceId', data.resourceId);
            this.data.set('formData.resourceVpc', data.resourceId);
            if (data.serviceType === 'csn') {
                let resouce = data.resourceId?.split(':');
                if (data.subServiceType === 'LOCAL') {
                    this.data.set('formData.limitType', 'network');
                    this.data.set('formData.csnNet', resouce[1]);
                } else {
                    this.data.set('formData.limitType', 'cross');
                    this.data.set('formData.peerRegion', data.peerRegion);
                    this.data.set('formData.subServiceType', data.subServiceType);
                }
                this.getCsnResource({value: resouce[0]});
            } else {
                // this.getResourceIds({value: data.serviceType});
            }
        });
    }
    getResourceIds({value}) {
        this.data.set('formData.direction', 'egress');
        let {resourceVpc} = this.data.get('formData');
        let payload = {
            serviceType: value,
            vpcUuid: resourceVpc
        };
        if (!resourceVpc) {
            return;
        }
        this.$http.gatewayResList(payload).then(res => {
            const resourceIdList = u.map(res.result, item => {
                return {
                    text: item.name + '/' + item.showResourceId,
                    value: item.resourceId,
                    bandwidth: item.bandwidth
                };
            });
            this.data.set('resourceIdList', resourceIdList);
        });
    }
    getCsnResource(e) {
        let value = e?.value;
        let payload = {
            serviceType: 'csn'
        };
        value && (payload.csnId = value);
        this.$http.gatewayResList(payload).then(res => {
            const resourceIdList = u.map(res.result, item => {
                return {
                    text: item.name + '/' + item.showResourceId,
                    value: item.resourceId,
                    bandwidth: item.bandwidth
                };
            });
            if (value) {
                this.data.set('csnNetResouce', resourceIdList);
            } else {
                this.data.set('resourceIdList', resourceIdList);
            }
            this.getCrossRegions();
        });
    }
    subServiceTypeChange() {
        this.nextTick(() => {
            this.data.set('formData.peerRegion', '');
            this.getCrossRegions();
        });
    }
    limitTypeChange(e) {
        if (e.value === 'cross') {
            this.nextTick(() => {
                this.data.set('formData.subServiceType', 'PEER_CLOUD');
                this.getCrossRegions();
            });
        }
    }
    getVpcResourceList() {
        let payload = {pageNo: 1, pageSize: 10000};
        this.$http.loadResourceIds(requestUrl['vpc'], payload).then(res => {
            const resourceIds = u.map(res, item => {
                return {
                    text: `${item.name}/(${item.shortId})`,
                    value: item.vpcId
                };
            });
            this.data.set('resourceIds', resourceIds);
        });
    }
    cancel() {
        let type = this.data.get('formData.resourceType');
        if (type === 'peerconn') {
            location.hash = '#/vpc/gateway/pc/list';
        } else if (type === 'et') {
            location.hash = '#/vpc/gateway/et/list';
        } else {
            location.hash = '#/vpc/gateway/csn/list';
        }
    }
    onRegionChange() {
        this.cancel();
    }
    peerRegionChange(e) {
        this.data.set('formData.peerRegion', e.value);
    }
    createGateway() {
        return this.ref('form')
            .validateFields()
            .then(() => {
                this.data.set('confirmed', true);
                const formData = this.data.get('formData');
                let payload = {};
                if (formData.resourceType === 'csn') {
                    payload = {
                        name: formData.name,
                        description: formData.description,
                        ipVersion: 4,
                        serviceType: formData.resourceType,
                        direction: formData.direction,
                        cidr: formData.cidr,
                        bandwidth: formData.bandwidth
                    };
                    if (formData.limitType === 'network') {
                        payload.subServiceType = 'LOCAL';
                        payload.resourceId = `${formData.resourceId}:${formData.csnNet}`;
                    } else {
                        payload.subServiceType = formData.subServiceType;
                        payload.resourceId = formData.resourceId;
                        payload.peerRegion = formData.peerRegion;
                    }
                } else {
                    payload = {
                        name: formData.name,
                        description: formData.description,
                        ipVersion: 4,
                        serviceType: formData.resourceType,
                        resourceId: formData.resourceId,
                        direction: formData.direction,
                        cidr: formData.cidr,
                        bandwidth: formData.bandwidth
                    };
                }
                if (this.data.get('isEdit')) {
                    let payload = {
                        id: this.data.get('urlQuery.id'),
                        bandwidth: formData.bandwidth,
                        name: formData.name,
                        description: formData.description
                    };
                    return this.$http.editGateway(payload);
                }
                return this.$http.createGateWayRule(payload);
            })
            .then(() => {
                this.cancel();
            })
            .catch(() => this.data.set('confirmed', false));
    }
    resourceVpcChange({value}) {
        this.data.set('formData.resourceVpc', value);
        let payload = {
            value: this.data.get('formData.resourceType')
        };
        this.getResourceIds(payload);
    }
}
export default San2React(Processor.autowireUnCheckCmpt(GatewayCreate));
