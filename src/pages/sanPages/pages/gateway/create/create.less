.vpc-gateway-create {
    background: #fff;
    width: 100%;
    min-height: 100%;
    background-color: #f7f7f9;
    padding-bottom: 20px;
    .content-box {
        text-align: left;
        margin: 20px auto;
        padding: 20px;
        .form-resource {
            margin-left: 92px !important;
        }
        .gateway-name-wrapper {
            .s-input-area {
                input {
                    padding-left: 10px;
                }
            }
        }
        .resource-button-wrap {
            .s-radio-text {
                width: 72px;
            }
        }
        .s-form-item-label > label {
            margin-right: 0;
        }
        .s-form-item-label {
            width: 92px;
        }
        .collector-button {
            height: 30px;
            box-sizing: border-box;
        }
        .s-form-item-with-help {
            margin-bottom: 24px;
        }
    }
    .s-biz-page-content {
        .s-biz-page-fixed-footer {
            width: 100%;
        }
    }
    .form-part-wrap {
        border-radius: 6px;
        width: calc(~'100vw - 32px');
        background: #fff;
        margin: 16px;
        padding: 24px;
        h4 {
            display: inline-block;
            border: none;
            zoom: 1;
            font-size: 16px;
            color: #151b26;
            font-weight: 500;
            margin: 0;
            padding: 0;
        }
        .s-form-item-label {
            width: 92px;
            height: 30px;
        }
        .label_class {
            .inline-tip {
                top: 3px;
                position: relative;
                .s-tip-warning {
                    justify-content: center;
                    .warning_class {
                        fill: #999;
                    }
                }
                .s-tip:hover .s-icon path {
                    fill: #2468f2 !important;
                }
            }
        }
        .center_class {
            .s-row {
                .s-form-item-control-wrapper {
                    line-height: 30px;
                }
            }
        }
    }
    .center_class {
        .s-row {
            .s-form-item-control-wrapper {
                line-height: 30px;
            }
        }
    }
    .icon-sort-normal {
        display: inline-block;
        color: #666;
        transform: rotate(90deg);
        vertical-align: middle;
        margin: 0 10px;
    }
    .region-table {
        width: 450px;
    }
    .buybucket {
        width: 100%;
        left: 0;
        height: 60px;
        .buybucket-container {
            width: auto;
            float: left;
            height: 48px;
            transform: translateY(12.5%);
            display: flex;
            align-items: center;
        }
    }
    .inline-tip {
        top: 3px;
        position: relative;
        .s-tip-warning {
            justify-content: center;
            .warning_class {
                fill: #999;
            }
        }
        .s-tip:hover .s-icon path {
            fill: #2468f2 !important;
        }
    }
}

.locale-en {
    .vpc-gateway-create .form-part-wrap .s-form-item-label {
        width: 240px;
    }
}
