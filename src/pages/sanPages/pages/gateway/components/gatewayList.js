import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {Notification} from '@baidu/sui';
import {OutlinedRefresh, OutlinedPlus, OutlinedEditingSquare} from '@baidu/sui-icon';

import {columns} from '../list/tableField';
import {GatewaySearchType, GatewayServiceType, GatewayRuleType, subServiceTypeList} from '@/pages/sanPages/common/enum';
import {utcToTime} from '@/pages/sanPages/utils/helper';
import Confirm from '@/pages/sanPages/components/confirm';
import './gatewayList.less';
import rules from '../rules';

const {asComponent, invokeSUI, invokeSUIBIZ, withSidebar, invokeAppComp, template, invokeComp} = decorators;

const tpl = html`
    <div class="gateway-list-wrap">
        <s-app-list-page class="{{klass}}">
            <div slot="bulk">
                <s-tip-button
                    disabled="{{createGateway.disable}}"
                    skin="primary"
                    isDisabledVisibile="{{true}}"
                    on-click="onCreate"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{ createGateway.message | raw }}
                    </div>
                    <outlined-plus />
                    创建限速规则
                </s-tip-button>
                <s-tooltip class="left_class" trigger="{{release.disable ? 'hover' : ''}}" placement="top">
                    <!--bca-disable-next-line-->
                    <div slot="content">{{release.message | raw}}</div>
                    <s-button on-click="onDelete" disabled="{{release.disable}}"> 删除</s-button>
                </s-tooltip>
            </div>
            <div slot="filter">
                <div class="gateway-buttons-wrap">
                    <s-search
                        width="{{230}}"
                        class="search-warp"
                        value="{=payload.keyword=}"
                        placeholder="{{searchholder}}"
                        on-search="enterSearch($event)"
                    >
                        <s-select
                            slot="options"
                            width="120"
                            datasource="{{searchType}}"
                            value="{=payload.keywordType=}"
                            on-change="searchTypeChange"
                        >
                        </s-select>
                    </s-search>
                    <s-button class="s-icon-button" on-click="refresh" track-name="刷新">
                        <outlined-refresh class="icon-class" />
                    </s-button>
                </div>
            </div>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                on-selected-change="tableSelected($event)"
                selection="{=table.selection=}"
            >
                <div slot="empty">
                    <s-empty on-click="onCreate"> </s-empty>
                </div>
                <div slot="c-id">
                    <span class="text-hidden gateway-name">{{ row.name ? row.name : '-' }}</span>
                    <s-popover
                        s-ref="popover-name-{{rowIndex}}"
                        placement="top"
                        trigger="click"
                        class="edit-popover-class"
                    >
                        <div class="edit-wrap" slot="content">
                            <s-input
                                value="{=edit.name.value=}"
                                width="320"
                                placeholder="请输入名称"
                                on-input="onEditInput($event, rowIndex, 'name')"
                            />
                            <div class="edit-tip">大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65</div>
                            <s-button
                                skin="primary"
                                s-ref="editBtn-name-{{rowIndex}}"
                                disabled="{{true}}"
                                on-click="editConfirm(row, rowIndex, 'name')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel(rowIndex, 'name')">取消</s-button>
                        </div>
                        <outlined-editing-square class="name-icon" on-click="onInstantEdit(row, rowIndex, 'name')" />
                    </s-popover>
                    <br />
                    <span class="truncated gateway-id" title="{{row.id}}">{{ row.id }}</span>
                    <s-clip-board class="name-icon" text="{{row.id}}" />
                </div>
                <div slot="c-ipVersion">
                    <span>{{row.ipVersion | ruleTypeText}}</span>
                </div>
                <div slot="c-bandwidth">
                    <span>{{row.bandwidth}}Mbps</span>
                </div>
                <div slot="c-serviceType">
                    <span>{{row.serviceType | serviceTypeText}}</span>
                </div>
                <div slot="c-subServiceType">
                    <span>{{row.subServiceType | subServiceTypeText}}</span>
                </div>
                <div slot="c-peerRegion">
                    <span>{{row.peerRegion | edgeNodesText}}</span>
                </div>
                <div slot="c-direction">
                    <span>{{row.direction | directionText}}</span>
                </div>
                <div slot="c-createdTime">
                    <span>{{row.createdTime | timeFormat}}</span>
                </div>
                <div slot="c-description">
                    <span class="truncated">{{ row.description || '-' }}</span>
                    <s-popover
                        s-ref="popover-description-{{rowIndex}}"
                        placement="top"
                        trigger="click"
                        class="edit-popover-class"
                    >
                        <div class="edit-wrap" slot="content">
                            <s-input
                                value="{=edit.description.value=}"
                                width="160"
                                maxLength="{{200}}"
                                on-input="onEditInput($event, rowIndex, 'description')"
                            />
                            <div class="edit-tip">描述不能超过200个字符</div>
                            <s-button
                                skin="primary"
                                s-ref="editBtn-description-{{rowIndex}}"
                                disabled="{{true}}"
                                on-click="editConfirm(row, rowIndex, 'description')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel(rowIndex, 'description')">取消</s-button>
                        </div>
                        <outlined-editing-square
                            class="name-icon"
                            on-click="onInstantEdit(row, rowIndex, 'description')"
                        />
                    </s-popover>
                </div>
                <div slot="c-opt" class="operations">
                    <s-button class="operations_btn" skin="stringfy" on-click="editGateway(row)">编辑</s-button>
                    <s-button class="operations_btn" skin="stringfy" on-click="onDelete(row)">删除</s-button>
                    <!--<s-button skin="stringfy" on-click="onMonitor(row)">监控</s-button>-->
                </div>
            </s-table>
            <s-pagination
                s-if="{{pager.total}}"
                slot="pager"
                layout="{{'pageSize, pager'}}"
                pageSize="{{pager.pageSize}}"
                total="{{pager.total}}"
                page="{{pager.page}}"
                resetPageWhenSizeChange="{{true}}"
                on-pagerChange="onPagerChange"
            />
        </s-app-list-page>
    </div>
`;

@template(tpl)
@asComponent('@gateway-list')
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@invokeComp('@vpc-select')
class GatewayList extends Component {
    static components = {
        'outlined-refresh': OutlinedRefresh,
        'outlined-plus': OutlinedPlus,
        'outlined-editing-square': OutlinedEditingSquare
    };
    initData() {
        return {
            klass: 'gateway-list-wrap',
            searchType: GatewaySearchType.toArray(),
            searchholder: '请输入规则名称进行搜索',
            payload: {
                keyword: '',
                keywordType: 'name'
            },
            table: {
                loading: false,
                columns,
                datasource: [],
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                selectedItems: []
            },
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            release: {},
            edgeNodes: []
        };
    }

    static computed = {
        enableDelete() {
            const table = this.data.get('table');
            return table.selection && table.selection.selectedIndex && table.selection.selectedIndex.length > 0;
        }
    };

    static filters = {
        ruleTypeText(type) {
            return GatewayRuleType.getTextFromValue(type) || '-';
        },
        serviceTypeText(type) {
            return GatewayServiceType.getTextFromValue(type) || '-';
        },
        subServiceTypeText(type) {
            let typeText = subServiceTypeList.getTextFromValue(type) || '-';
            if (type === 'LOCAL') {
                return typeText;
            } else {
                return `跨地域：${typeText}`;
            }
        },
        edgeNodesText(nodeId) {
            let edgeNodes = this.data.get('edgeNodes');
            let edgeText = '';
            if (edgeNodes.length) {
                edgeText = edgeNodes.filter(item => item.nodeId === nodeId)[0]?.nodeName || '-';
            } else {
                edgeText = '';
            }
            return edgeText;
        },
        directionText(type) {
            return type === 'ingress' ? '入向' : '出向';
        },
        timeFormat(time) {
            return utcToTime(time);
        }
    };

    inited() {
        if (this.data.get('context').serviceType === 'csn') {
            this.getCsnEdgeNode();
            this.data.splice('table.columns', [
                3,
                0,
                {
                    name: 'subServiceType',
                    label: '限速类型',
                    width: 120
                },
                {
                    name: 'peerRegion',
                    label: '互通地域',
                    width: 120
                }
            ]);
        }
        this.loadPage();
    }

    tableSelected(e) {
        this.data.set('table.selectedItems', e.value.selectedItems);
        let {release} = checker.check(rules, e.value.selectedItems);
        this.data.set('release', release);
    }

    getPayload() {
        const searchParam = this.data.get('payload');
        const {pager} = this.data.get('');
        const vpcId = window.$storage.get('vpcInfo')?.shortId || window.$storage.get('vpcId') || '';
        let payload = {
            serviceType: this.data.get('context').serviceType,
            pageNo: pager.page,
            pageSize: pager.pageSize
        };
        return {...payload, vpcId, ...searchParam};
    }

    async getCsnEdgeNode() {
        // 请求csn接口之前判断csn是否授权
        if (window.$storage.get('csnSts') && window.$storage.get('becSts')) {
            try {
                if (!window.$storage.get('bec-resource-open')) {
                    const {result} = await this.$http.getResource();
                    window.$storage.set('bec-resource-open', !!result);
                    if (!!result) {
                        this.$http.getEdgeNode().then(res => {
                            this.data.set('edgeNodes', res.edgeNodes);
                        });
                    }
                } else {
                    this.$http.getEdgeNode().then(res => {
                        this.data.set('edgeNodes', res.edgeNodes);
                    });
                }
            } catch (error) {
                window.$storage.set('bec-resource-open', false);
            }
        }
    }

    enterSearch(e) {
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    loadPage() {
        this.data.set('table.loading', true);
        this.getQuota();
        let payload = this.getPayload();
        return this.$http.getGatewayList(payload).then(data => {
            this.data.set('table.datasource', data.result);
            this.data.set('pager.total', data.totalCount);
            this.data.set('table.loading', false);
        });
    }

    resetTable() {
        this.data.set('pager.page', 1);
        this.data.set('table.selection.selectedIndex', []);
    }

    refresh() {
        this.resetTable();
        return this.loadPage();
    }

    onDelete(row) {
        let confirm = new Confirm({
            data: {
                open: true,
                content: '确认删除选中的限速规则？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            let ids = [];
            if (row?.id) {
                ids = [row.id];
            } else {
                ids = this.data.get('table.selectedItems').map(item => item.id);
            }
            let count = this.data.get('pager.total');
            this.$http.deleteGateway({ids}).then(() => {
                this.refresh().then(() => {
                    // 防止在第二页删除数据后没数据页码对不上的问题
                    this.data.set('pager.total', count);
                });
                Notification.success('删除成功', {placement: 'topRight'});
            });
        });
    }

    onCreate() {
        location.hash = '#/vpc/gateway/create?resourceType=' + this.data.get('context').serviceType;
    }

    editGateway(row) {
        location.hash = `#/vpc/gateway/create?id=${row.id}&resourceType=${this.data.get('context').serviceType}`;
    }

    getQuota() {
        let {release} = checker.check(rules, []);
        this.data.set('release', release);
        this.$http.getGatewayQuota().then(res => {
            this.data.set('quota', res.accountQuota);
            this.checkVpnCreate(res.accountQuota.free);
        });
    }

    checkVpnCreate(quota) {
        let {createGateway} = checker.check(rules, [], '', {
            quotaCheck: quota > 0
        });
        this.data.set('createGateway', createGateway);
    }

    searchTypeChange({value}) {
        let tip = `请输入${GatewaySearchType.getTextFromValue(value)}进行搜索`;
        this.data.set('searchholder', tip);
    }

    async editConfirm(row, rowIndex, type) {
        let edit = this.data.get(`edit.${type}`);
        if (edit.error) {
            return;
        }
        this.$http
            .editGateway({
                id: row.id,
                [type]: edit.value
            })
            .then(() => {
                this.editCancel(rowIndex, type);
                Notification.success('修改成功');
                this.loadPage();
            });
    }

    editCancel(rowIndex, type) {
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', true);
        this.ref(`popover-${type}-${rowIndex}`).data.set('visible', false);
    }

    onInstantEdit(row, rowIndex, type) {
        this.data.set(`edit.${type}.value`, row[`${type}`]);
        this.data.set(`edit.${type}.error`, false);
        const editWrap = this.ref(`popover-${type}-${rowIndex}`);
        editWrap.data.set('visible', !editWrap.data.get('visible'));
    }

    onEditInput(e, rowIndex, type) {
        let result =
            type === 'name' ? e.value === '' || !/^[a-zA-Z][\w\-\_\/\.]{0,64}$/.test(e.value) : e.value.length > 200;
        this.data.set(`edit.${type}.error`, result);
        this.data.set(`edit.${type}.value`, e.value);
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', result);
    }

    vpcChange() {
        this.data.set('pager.page', '1');
        this.loadPage();
    }

    onPagerChange({value}) {
        this.resetTable();
        this.data.set('pager.page', value.page);
        this.data.set('pager.pageSize', value.pageSize);
        this.loadPage();
    }
}
export default San2React(Processor.autowireUnCheckCmpt(GatewayList));
