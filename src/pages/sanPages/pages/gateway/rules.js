import {ContextService} from '@/pages/sanPages/common';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';


export default {
    release: [
        {
            required: true,
            message: '请先选择限速规则'
        }
    ],
    createGateway: [
        {
            required: false,
        },
        {
            custom(data, options) {
                if (!options.quotaCheck) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: '网关限速规则配额不足'
                        };
                    }
                    else {
                        return {
                            disable: true,
                            message: `网关限速规则配额不足。如需增加配额请提交<a href="${
                                ContextService.Domains.ticket
                            }/#/ticket/create" target="_blank">工单</a>`
                        };
                    }
                }
            }
        }
    ]
};
