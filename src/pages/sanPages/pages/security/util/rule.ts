/* eslint-disable @typescript-eslint/member-ordering */
/**
 * @file src/bcc/security/rule.js ~ 2014/11/27 16:25:58
 * <AUTHOR>
 * */
import u from 'lodash';
import {SecurityIpVersion as IpVersion} from '@/pages/sanPages/common/enum';

const kALL = 'all';
const kALLPORT = '1-65535';
const kUSER = 'user';
const kSYSTEM = 'system';
const kICMP = 'icmp';
export default class Rule {
    [key: string]: any;
    /**
     * 规则对象
     *
     * @class Rule
     * @param {string} name 协议名称.
     * @param {string} protocol 协议类型.
     * @param {string} portRange 端口的范围.
     * @param {string} ethertype 规则类型.
     * @param {string} ksource 自定义source
     */
    constructor(name: string, protocol: string, portRange: string, ethertype: string, ksource?: string) {
        this.name = name;
        this.protocol = protocol;
        this.portRange = u.trim(portRange);
        /**
         * 安全组的ID
         * @type {string}
         */
        this.remoteGroupId = null;
        this.remoteGroupShortId = null;
        this.remoteGroupName = null;
        /**
         * @type {string}
         */
        this.remoteIP = ksource || kALL;
        this.ethertype = ethertype || IpVersion.IPV4;
        /**
         * 用来标识设置的是用户ip还是安全组id，根据这个字段的值
         * 来决定向后端提交 remoteIP 还是 remoteGroupId 字段
         * 如果是 source === 'user'，提交 remoteIP，忽略 remoteGroupId
         * 如果是 source === 'system'，提交 remoteGroupId，忽略 remoteIP
         * @type {string}
         */
        this.source = kUSER;
        this.ksource = ksource; // 用来兼容自定义source
        this.key = this.getKey();
    }

    /**
     * 判断两条规则是否相同.
     *
     * @param {Rule} rule 需要检测的规则.
     * @return {boolean}
     */
    equal(rule) {
        let x = this.protocol === rule.protocol && this.source === rule.source && this.ethertype === rule.ethertype;
        if (x && this.protocol !== kICMP) {
            x = x && this.portRange === u.trim(rule.portRange);
        }
        if (x && this.source === kUSER) {
            // 普通IP查重都加上/32
            let remoteIP = u.clone(this.remoteIP);
            let rRemoteIP = u.clone(rule.remoteIP);
            let suffix = rule.ethertype === IpVersion.IPV6 ? '/128' : '/32';
            if (remoteIP !== kALL && remoteIP !== '::/0' && remoteIP !== '0:0:0:0/0' && remoteIP.indexOf('/') < 0) {
                remoteIP += suffix;
            }
            if (rRemoteIP !== kALL && rRemoteIP !== '::/0' && rRemoteIP !== '0:0:0:0/0' && rRemoteIP.indexOf('/') < 0) {
                rRemoteIP += suffix;
            }
            x = x && remoteIP === rRemoteIP;
        } else if (x && this.source === kSYSTEM) {
            // /api/network/v1/security/detail里返回的remoteGroupId是短id
            // 而前端纪录的remoteGroupId是长id
            x = x && (this.remoteGroupId === rule.remoteGroupId || this.remoteGroupShortId === rule.remoteGroupShortId);
        }
        // 查重 都不考虑备注了
        // // 全部协议 & 全部端口时不检查备注
        // if (x) {
        //     if (this.protocol === kALL && this.portRange === kALLPORT) {
        //         return x;
        //     }
        //     return x && this.name === rule.name;
        // }
        return x;
    }
    clone() {
        let cloned = new Rule(this.name, this.protocol, this.portRange, this.ethertype);
        cloned.remoteIP = this.remoteIP;
        cloned.remoteGroupId = this.remoteGroupId;
        cloned.remoteGroupShortId = this.remoteGroupShortId || this.remoteGroupId;
        cloned.remoteGroupName = this.remoteGroupName;
        cloned.source = this.source;
        cloned.key = this.key;
        return cloned;
    }
    getKey() {
        return [
            this.protocol,
            this.portRange,
            this.source === kUSER ? 'a-' + this.source : 'b-' + this.source,
            this.remoteIP,
            this.remoteGroupId,
            this.name,
            this.remoteGroupShortId,
            this.ethertype
        ].join('#');
    }

    /**
     * 根据后端返回的json数据，查找规则的名字.
     *
     * @param {Object} rule 后端接口返回的json
     * @return {string} 常用规则的名字或者默认的名字.
     */
    findName(rule) {
        let kCommonServices = require('./services');
        let found = u.find(kCommonServices, function (service) {
            return !!u.find(service.rules, function (foo) {
                // 判断 foo 和 rule 是否相等
                // 检查 protocol 和 portRange
                if (foo.protocol === rule.protocol && foo.portRange === rule.portRange) {
                    return true;
                }
                return false;
            });
        });
        if (found) {
            return found.name;
        } else if (rule.protocol === 'tcp') {
            return '自定义TCP';
        } else if (rule.protocol === 'udp') {
            return '自定义UDP';
        }
        return '全部协议';
    }
    // remoteGroupName目前仅供显示使用，拷贝带着，比较不用考虑。
    static fromJSON = r => {
        let name = r.name || '';
        let protocol = r.protocol === '' ? kALL : r.protocol;
        let portRange = r.portRange === '' ? kALLPORT : r.portRange;
        let ethertype = r.ethertype || IpVersion.IPV4;
        let rule = new Rule(name, protocol, portRange, ethertype);
        rule.source = r.source || (r.remoteGroupId ? kSYSTEM : kUSER);
        // 保证all与0.0.0.0/0 与 ::/0 的相同性
        rule.remoteIP =
            r.remoteIP === '' || r.remoteIP === 'all'
                ? ethertype === IpVersion.IPV4
                    ? '0.0.0.0/0'
                    : '::/0'
                : r.remoteIP;
        rule.remoteGroupId = r.remoteGroupId || null;
        rule.remoteGroupName = r.remoteGroupName || null;
        rule.remoteGroupShortId = r.remoteGroupShortId || rule.remoteGroupId;
        rule.id = r.id || '';
        rule.securityGroupRuleId = r.securityGroupRuleId || '';
        rule.updatedTime = r.updatedTime;
        if (rule.source === kUSER) {
            rule.remoteGroupId = null;
            rule.remoteGroupName = null;
        } else if (rule.source === kSYSTEM) {
            rule.remoteIP = null;
        }
        if (protocol === kICMP) {
            rule.portRange = '不涉及';
        }
        rule.key = rule.getKey();
        return rule;
    };
    static toJSON = (r, direction) => {
        let remoteIP = r.source.toLowerCase() === kUSER ? r.remoteIP : null;
        let remoteGroupId = r.source.toLowerCase() === kUSER ? null : r.remoteGroupId;
        let protocol = r.protocol.toLowerCase() === kALL ? '' : r.protocol;
        let portRange = r.portRange;
        let ethertype = r.ethertype;
        let updatedTime = r.updatedTime;
        if (protocol === '') {
            portRange = '';
        }
        // icmp协议没有端口的概念，和console rd约定传null
        else if (protocol === kICMP) {
            portRange = null;
        }
        return {
            name: r.name,
            // 跟RD协商的结果是，如果是全部协议的时候，protocol字段为空.
            protocol: protocol,
            // 如果用户填all，转为全部端口给console rd，否则会报错
            portRange: portRange === kALL ? kALLPORT : u.trim(portRange),
            direction: direction,
            // 如果用户选择的是all，那么就用 '' 代替，否则后端处理的时候会出错的.
            remoteIP: remoteIP === kALL ? '' : remoteIP,
            remoteGroupId: remoteGroupId,
            ethertype: ethertype,
            updatedTime: updatedTime
        };
    };
}
