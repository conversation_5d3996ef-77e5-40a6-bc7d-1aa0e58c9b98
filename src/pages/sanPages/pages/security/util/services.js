/**
 * @file src/bcc/security/services.js ~ 2014/11/27 20:28:09
 * <AUTHOR>
 * */
import Rule from './rule';
var kPingRule       = 'PING请求';
var kV4PingRule     = 'PING请求_IPv4';
var kV6PingRule     = 'PING请求_IPv6';
var kSSHRule        = 'SSH远程终端';
var kRDPRule        = 'RDP远程桌面';
var kHTTPRule       = 'HTTP服务';
var kHTTPSRule      = 'HTTPS服务';
var kFTPRule        = 'FTP文件传输';
var kMYSQLRule      = 'MySQL数据库';
var kSQLServerRule  = 'SQL Server数据库';
var kDNSRule        = 'DNS服务';
var kSNMPRule       = 'SNMP';
var kSNMPTrapRule   = 'SNMP Trap';
var kSMTPRule       = 'SMTP邮件发送服务';
var kPOP3Rule       = 'POP3邮件接收服务';
var kCustomTCPRule  = '自定义TCP';
var kCustomUDPRule  = '自定义UDP';
var kCustomAllRule  = '全部协议';
var kAllPorts       = '1-65535';
var kAllProtocol    = 'all';
var kTCPProtocol    = 'tcp';
var kUDPProtocol    = 'udp';
var kICMPPrototol   = 'icmp';
export default [
    {
        name: kCustomAllRule,
        key: 'CustomAll',
        rules: [
            new Rule(kCustomAllRule, kAllProtocol, kAllPorts)
        ]
    },
    {
        name: kCustomTCPRule,
        key: 'CustomTCP',
        rules: [
            new Rule(kCustomTCPRule, kTCPProtocol, kAllPorts)
        ]
    },
    {
        name: kCustomUDPRule,
        key: 'CustomUDP',
        rules: [
            new Rule(kCustomUDPRule, kUDPProtocol, kAllPorts)
        ]
    },
    {
        name: kPingRule,
        key: 'PING',
        rules: [
            new Rule(kPingRule, kICMPPrototol, '不涉及')
        ]
    },
    {
        name: kV4PingRule,
        key: 'PING_IPV4',
        val: 'icmp_ipv4',
        rules: [
            new Rule(kV4PingRule, kICMPPrototol, '不涉及', 'IPv4')
        ]
    },
    {
        name: kV6PingRule,
        key: 'PING_IPV6',
        val: 'icmp_ipv6',
        rules: [
            new Rule(kV6PingRule, kICMPPrototol, '不涉及', 'IPv6')
        ]
    },
    {
        name: kHTTPRule,
        key: 'HTTP',
        val: 'tcp_http',
        rules: [
            new Rule(kHTTPRule, kTCPProtocol, '80')
        ]
    },
    {
        name: kHTTPSRule,
        key: 'HTTPS',
        val: 'tcp_https',
        rules: [
            new Rule(kHTTPSRule, kTCPProtocol, '443')
        ]
    },
    {
        name: kFTPRule,
        key: 'FTP',
        val: 'tcp_ftp',
        rules: [
            new Rule(kFTPRule, kTCPProtocol, '21'),
            new Rule(kFTPRule, kTCPProtocol, '20')
        ]
    },
    {
        name: kRDPRule,
        key: 'RDP',
        val: 'tcp_rdp',
        rules: [
            new Rule(kRDPRule, kTCPProtocol, '3389')
        ]
    },
    {
        name: kDNSRule,
        key: 'DNS',
        val: 'tcp_dns',
        rules: [
            new Rule(kDNSRule, kTCPProtocol, '53'),
            new Rule(kDNSRule, kUDPProtocol, '53')
        ]
    },
    {
        name: kDNSRule,
        key: 'DNS',
        val: 'udp_dns',
        rules: [
            new Rule(kDNSRule, kUDPProtocol, '53'),
            new Rule(kDNSRule, kTCPProtocol, '53')
        ]
    },
    {
        name: kPOP3Rule,
        key: 'POP3',
        val: 'tcp_pop3',
        rules: [
            new Rule(kPOP3Rule, kTCPProtocol, '110')
        ]
    },
    {
        name: kMYSQLRule,
        key: 'MYSQL',
        val: 'tcp_mysql',
        rules: [
            new Rule(kMYSQLRule, kTCPProtocol, '3306')
        ]
    },
    {
        name: kSQLServerRule,
        key: 'SQL Server',
        val: 'tcp_sql_server',
        rules: [
            new Rule(kSQLServerRule, kTCPProtocol, '1433')
        ]
    },
    {
        name: kSNMPRule,
        key: 'SNMP',
        val: 'tcp_snmp',
        rules: [
            new Rule(kSNMPRule, kTCPProtocol, '161'),
            new Rule(kSNMPRule, kUDPProtocol, '161')
        ]
    },
    {
        name: kSMTPRule,
        key: 'SMTP',
        val: 'tcp_smtp',
        rules: [
            new Rule(kSMTPRule, kTCPProtocol, '25')
        ]
    },
    {
        name: kSNMPTrapRule,
        key: 'SNMP Trap',
        val: 'tcp_snmp_trap',
        rules: [
            new Rule(kSNMPTrapRule, kTCPProtocol, '162'),
            new Rule(kSNMPTrapRule, kUDPProtocol, '162')
        ]
    },
    {
        name: kSSHRule,
        key: 'SSH',
        val: 'tcp_ssh',
        rules: [
            new Rule(kSSHRule, kTCPProtocol, '22')
        ]
    }
];