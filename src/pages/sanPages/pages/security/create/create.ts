/*
 * @description: 创建安全组
 * @file: network/security/pages/create.js
 * @author: p<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {TagEditPanel} from '@baidu/bce-tag-sdk-san';
import {checker} from '@baiducloud/bce-opt-checker';
import rules from '../rules';
import RuleList from '../components/ruleList';
import Rule from '../util/rule';
import Confirm from '@/pages/sanPages/components/confirm';
import {securityTemp} from '@/pages/sanPages/common/enum';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import Assist from '@/utils/assist';
import testID from '@/testId';
import './style.less';

const {invokeSUI, invokeSUIBIZ, invokeComp, invokeAppComp, template, service} = decorators;

/* eslint-disable */
const tpl = html`
    <template>
        <s-app-create-page
            class="{{klass}}"
            backTo="{{pageNav.backUrl}}"
            backToLabel="{{pageNav.backLabel}}"
            pageTitle="{{pageNav.title}}"
            data-test-id="${testID.security.createBack}"
        >
            <div class="content form-part-wrap">
                <h4>{{'基础信息'}}</h4>
                <s-form
                    s-ref="form"
                    data="{=formData=}"
                    rules="{{rules}}"
                    label-col="{{labelCol}}"
                    wrapper-col="{{wrapperCol}}"
                    label-align="left"
                >
                    <s-form-item prop="vpcId" label="{{'所在网络：'}}">
                        <s-select
                            width="{{220}}"
                            value="{=formData.vpcId=}"
                            on-change="vpcChange($event)"
                            data-test-id="${testID.security.createSelectVpc}"
                        >
                            <s-select-option
                                s-for="item in vpcList"
                                value="{{item.value}}"
                                label="{{item.text}}"
                                data-test-id="${testID.security.createSelectVpc}{{index}}"
                            >
                                <s-tooltip>
                                    <div slot="content">{{item.text}}</div>
                                    <div>{{item.text}}</div>
                                </s-tooltip>
                            </s-select-option>
                        </s-select>
                    </s-form-item>
                    <s-form-item
                        prop="name"
                        label="{{'安全组名称：'}}"
                        help="大小写字母、数字，中文以及-_/.特殊字符，必须以字母、中文开头，但不能以default开头，长度1-65"
                    >
                        <s-input
                            value="{=formData.name=}"
                            placeholder="{{'请输入名称'}}"
                            width="{{220}}"
                            data-test-id="${testID.security.createName}"
                        ></s-input>
                    </s-form-item>
                    <s-form-item prop="temp" label="{{'模版：'}}">
                        <s-select
                            width="{{220}}"
                            value="{=formData.temp=}"
                            data-test-id="${testID.security.createSelectTemp}"
                        >
                            <s-select-option
                                s-for="item in tempList"
                                value="{{item.value}}"
                                label="{{item.text}}"
                                data-test-id="${testID.security.createSelectTemp}{{index}}"
                            >
                                <s-tooltip>
                                    <div slot="content">{{item.text}}</div>
                                    <div>{{item.text}}</div>
                                </s-tooltip>
                            </s-select-option>
                        </s-select>
                        <p class="assist-tip-form">
                            选择<a href="#/vpc/set/list">参数模板</a>后会自动匹配对应的规则。
                            <a
                                class="assist-tip"
                                href="javascript:void(0)"
                                on-click="showAssist()"
                                s-if="FLAG.NetworkSupportAI"
                                >了解详情</a
                            >
                        </p>
                    </s-form-item>
                    <s-form-item label="{{'描述：'}}">
                        <s-input-text-area
                            maxLength="200"
                            width="220"
                            height="60"
                            value="{=formData.description=}"
                            placeholder="{{'最多200个字符'}}"
                        ></s-input-text-area>
                    </s-form-item>
                </s-form>
            </div>
            <div class="content form-part-wrap">
                <h4>{{'端口设置'}}</h4>
                <rule-list
                    on-editting="handleEditting"
                    vpcId="{{formData.vpcId}}"
                    tempId="{{formData.temp}}"
                    s-ref="ruleList"
                ></rule-list>
            </div>
            <div s-if="!FLAG.NetworkSecuritySupportOrganization" class="content form-part-wrap bottom_class">
                <h4>{{'标签'}}</h4>
                <div class="s-form-item-name">
                    <div class="s-form-item-label"><label>绑定标签：</label></div>
                    <div class="s-form-item-content">
                        <tag-edit-panel
                            s-ref="tagPanel"
                            instances="{{defaultInstances}}"
                            options="{{tagListRequster}}"
                        />
                    </div>
                </div>
            </div>
            <div slot="pageFooter" class="buybucket">
                <div class="buybucket-container">
                    <s-tooltip trigger="{{create.disable || iamPass.disable ? 'hover' : ''}}" placement="top">
                        <div slot="content">
                            <!--bca-disable-next-line-->
                            {{create.message || iamPass.message | raw}}
                        </div>
                        <s-button
                            disabled="{{updating || iamPass.disable || editting}}"
                            loading="{{createLoading}}"
                            class="opt-button"
                            size="large"
                            skin="primary"
                            on-click="onCreate"
                            data-test-id="${testID.security.createSubmit}"
                        >
                            {{'确定'}}
                        </s-button>
                    </s-tooltip>
                    <s-button class="opt-button" on-click="cancel" size="large">{{'取消'}}</s-button>
                </div>
            </div>
        </s-app-create-page>
    </template>
`;
/* eslint-enable */
@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class VpcInstanceIndex extends Component {
    static components = {
        'tag-edit-panel': TagEditPanel,
        'rule-list': RuleList
    };
    static messages = {
        projectChange({value}) {
            value && this.data.set('resourceGroupId', value);
        }
    };
    static computed = {
        updating() {
            let createStatus = this.data.get('create');
            let confirmed = this.data.get('confirmed');
            return confirmed || createStatus.disable;
        }
    };
    initData() {
        return {
            FLAG,
            klass: ['vpc-security-create'],
            defaultInstances: [
                {
                    tags: [
                        {
                            tagKey: '默认项目',
                            tagValue: ''
                        }
                    ]
                }
            ],
            pageNav: {
                title: '创建安全组',
                backUrl: '/network/#/vpc/security/list',
                backLabel: '返回'
            },
            tagListRequster: this.tagListRequster.bind(this),
            validateErrorInfo: null,
            labelCol: {span: 4},
            wrapperCol: {span: 19},
            formData: {
                vpcId: '',
                name: '',
                temp: '1',
                description: ''
            },
            rules: {
                name: [
                    {required: true, message: '名称必填'},
                    {
                        pattern: /^(?!default)[a-zA-Z\u4e00-\u9fa5][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/,
                        message:
                            '大小写字母、数字，中文以及-_/.特殊字符，必须以字母、中文开头，但不能以default开头，长度1-65'
                    },
                    {
                        validator: (rule, value, callback) => {
                            if (value === '默认安全组') {
                                return callback('名称不合法，请修改后重试。');
                            }
                            callback();
                        }
                    }
                ]
            },
            vpcList: [],
            resourceGroupId: '',
            vpcProject: [],
            create: {},
            tempList: securityTemp.toArray(),
            iamPass: {},
            createLoading: false,
            editting: false,
            urlQuery: getQueryParams()
        };
    }
    inited() {
        this.getIamQuery();
    }
    // 请求标签list
    tagListRequster() {
        return this.$http.getSearchTagList({serviceType: [], region: ['global']});
    }
    vpcChange(e) {
        if (FLAG.NetworkSecuritySupportOrganization) {
            this.data.set('vpcProject', []);
            this.data.get('vpcList').forEach(item => {
                if (item.value === e.value) {
                    this.nextTick(() => {
                        this.data.set(
                            'vpcProject',
                            item.vpcInfo.resourceGroups.map(item => item.resourceGroupId)
                        );
                    });
                }
            });
        } else {
            this.checkQuota(e.value);
        }
    }
    // 获取VPC列表
    getVpcList() {
        this.$http.vpcList().then(data => {
            let list = u.map(data, item => {
                return {
                    text: `${item.name}（${item.cidr}）`,
                    value: item.vpcId,
                    vpcInfo: item
                };
            });
            this.data.set('vpcList', list);
            let vpcId = this.data.get('urlQuery.vpcId');
            if (vpcId) {
                list.forEach(item => {
                    if (item.value === vpcId) {
                        this.data.set('formData.vpcId', vpcId);
                        this.data.set('vpcInfo', item.vpcInfo);
                    }
                });
            } else {
                this.data.set('formData.vpcId', list[0].value);
                this.data.set('vpcInfo', list[0].vpcInfo);
            }
            if (FLAG.NetworkSecuritySupportOrganization) {
                this.data.set(
                    'vpcProject',
                    data[0].resourceGroups.map(item => item.resourceGroupId)
                );
            } else {
                this.checkQuota(this.data.get('formData.vpcId'));
            }
        });
    }
    checkQuota(vpcId) {
        this.$http.securityQuota({vpcId}).then(result => {
            let {create} = checker.check(rules, [], 'create', {
                quotaCheck: result.free > 0
            });
            this.data.set('create', create);
        });
    }

    // 获取安全组列表
    getAllSecurityGroups() {
        let param = {
            pageNo: 1,
            pageSize: 1000
        };
        const vpcId = this.data.get('urlQuery.vpcId');
        if (vpcId) {
            param.vpcId = vpcId;
        }
        this.$http.securityListV3(param).then(sgs => {
            let result = [];
            u.each(sgs.result, group => {
                result[group.name === '默认安全组' ? 'unshift' : 'push']({
                    text: group.name,
                    value: group.id,
                    title: group.securityGroupId || group.id,
                    securityGroupId: group.securityGroupId
                });
            });
            this.data.set('securityGroupList', result);
        });
    }

    handleEditting(val) {
        this.data.set('editting', val);
    }

    handleCreateRequest(payload: Record<string, any>) {
        this.data.set('createLoading', true);
        this.$http
            .securityCreate(payload)
            .then(() => {
                window.location.hash = '#/vpc/security/list';
            })
            .finally(() => {
                this.data.set('createLoading', false);
            });
    }

    async onCreate() {
        let tags = '';
        if (FLAG.NetworkSecuritySupportOrganization) {
            const projectValid = this.ref('projectConfig').validComponentData();
            if (!projectValid) {
                return;
            }
        } else {
            try {
                await this.ref('tagPanel').validate(false);
                await this.ref('form').validateFields();
            } catch (error) {
                return;
            }
        }
        tags = await this.ref('tagPanel').getTags();
        let rules = this.ref('ruleList').getRules();
        let result = [];
        rules.in.forEach(item => {
            result.push(Rule.toJSON(item, 'ingress'));
        });
        rules.out.forEach(item => {
            result.push(Rule.toJSON(item, 'egress'));
        });
        let formData = this.data.get('formData');
        let payload = {
            desc: formData.description,
            name: formData.name,
            vpcId: formData.vpcId,
            rules: result
        };
        if (this.data.get('FLAG.NetworkSecuritySupportOrganization')) {
            payload.resourceGroupIds = [this.data.get('resourceGroupId')];
        } else {
            payload.tags = tags;
        }
        if (rules.in.length === 0) {
            let confirm = new Confirm({
                data: {
                    open: true,
                    content:
                        '您未添加任何入站开放端口，将使该云服务器无法和外部进行任何通信，您仅能通过VNC功能进行管理。是否确认继续操作？'
                }
            });
            confirm.attach(document.body);
            confirm.on('confirm', () => {
                this.handleCreateRequest(payload);
            });
        } else {
            this.handleCreateRequest(payload);
        }
    }
    cancel() {
        location.hash = '#/vpc/security/list';
    }
    attached() {
        this.getVpcList();
        this.getAllSecurityGroups();
    }
    getIamQuery() {
        this.$http.getInterfaceIam({interfaceName: 'createSecurityGroup'}).then(res => {
            if (!res.requestId && !res.masterAccount) {
                if (!res.interfacePermission) {
                    this.data.set('iamPass', {
                        disable: true,
                        message: '您没有创建安全组权限，请联系主用户添加'
                    });
                } else {
                    this.data.set('iamPass', {
                        disable: false,
                        message: ''
                    });
                }
            } else {
                this.data.set('iamPass', {
                    disable: false,
                    message: ''
                });
            }
        });
    }
    showAssist() {
        Assist.sendMessageToAssist({
            sceneLabel: 'security_create',
            message: '入站允许22，80，443，3389端口分别起什么作用？'
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(VpcInstanceIndex));
