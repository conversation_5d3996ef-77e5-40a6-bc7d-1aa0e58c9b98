.vpc-security-create {
    min-height: 100%;
    width: 100%;
    .vpc-security-head{
        line-height: 50px;
        .backbox {
            height: 28px;
            line-height: 28px;
            border: 1px solid #ccc;
            outline: none;
            font-size: 12px;
            font-weight: 400;
            cursor: pointer;
            text-align: center;
            vertical-align: middle;
            color: #000;
            background-color: #fff;
            font-family: Microsoft Yahei,微软雅黑,Tahoma,Arial,Helvetica,STHeiti;
            display: inline-block;
            width: 20px;
            .iconfont {
                font-size: 12px;
            }
        }
        span {
            margin-left: 5px;
            vertical-align: middle;
        }
        span:last-child {
            position: relative;
            top: 1px;
        }
    }
    .content {
        .alert-tip {
            margin-top: 16px;
            margin-bottom: 12px;
        }
        .tip {
            font-size: 12px;
            margin-top: 10px;
            color: #999;
        }
        .ruletype, .allow-all-port, .tool-tip{
            margin-bottom: 16px;
        }
        .org-select {
            margin-bottom: 20px;
            .project-name {
                width: 97px;
                display: inline-block;
            }
        }
        .cell {
            min-width: 33%;
            display: inline-block;
            .cell-title {
                color: #999999;
                display: inline-block;
            }
            .cell-content {
                display: inline-block;
            }
            .icon-edit {
                color: #2468f2;
                font-size: 12px;
                margin-left: 10px;
            }
        }
        .s-form-item-label {
            width: 80px;
            height: 30px;
        }
    }
    .opt-button {
        width: 65px;
        height: 30px;
    }
    .form-part-wrap {
        border-radius: 6px;
        width: calc(~'100vw - 32px');
        background: #fff;
        margin: 16px 16px 0;
        padding: 24px;
        h4 {
            margin: 0;
            padding: 0;
            display: block;
            zoom: 1;
            font-size: 16px;
            color: #151B26;
            font-weight: 500;
        }
    }
    .bottom_class {
        margin-bottom: 16px;
    }
    .s-form-item-name {
        margin: 24px 0 0;
        .s-form-item-label {
            float: left;
            label {
                float: left;
            }
        }
        .s-form-item-content {
            float: left;

            .tag-edit-panel {
                width: auto;
            }
        }
    }
    .buybucket {
        width: 100%;
        left: 0;
        height: 60px;
        .buybucket-container {
            width: auto;
            float: left;
            height: 48px;
            transform: translateY(12.5%);
            display: flex;
            align-items: center;
        }
    }
    .s-table {
        margin-top: 0 !important;
        top: 16px;
    }
}

.locale-en {
  .vpc-security-create .content .s-form-item-label {
    width: 132px;
    height: 30px;
  }
  .vpc-security-create {
    .alert-tip {
      .s-alert-content {
        line-height: 12px !important;
      }
    }
  }
}
