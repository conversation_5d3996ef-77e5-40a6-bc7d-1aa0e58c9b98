import u from 'lodash';
import {utcToTime} from '@/pages/sanPages/utils/helper';

export const columns = [
    {
        name: 'name',
        label: '安全组名称',
        fixed: 'left',
        minWidth: '160'
    },
    {
        name: 'associateNum',
        label: '关联实例数量/配额',
        width: '140'
    },
    {
        name: 'vpcName',
        label: '所在网络',
        minWidth: '150',
        render(item) {
            return (item.vpcName || '-') + '<br>' + (item.vpcShortId || '-');
        }
    },
    {
        name: 'desc',
        label: '描述',
        minWidth: '140',
        render(item) {
            return u.escape(item.desc) || '-';
        }
    },
    {
        name: 'tag',
        label: '标签',
        width: '120',
        render(item) {
            if (!item.tags || item.tags.length < 1) {
                return '-';
            }
            var tagHtml = '';
            var tags = '';
            u.each(item.tags, function (item, index) {
                var tagKey = u.escape(item.tagKey);
                var tagValue = u.escape(item.tagValue);
                tags += '{' + tagKey + ' : ' + tagValue + '} ';
                if (index < 2) {
                    tagHtml += tagKey + ' : ' + tagValue + '<br>';
                }
            });
            item.tags.length > 2 && (tagHtml += '...');
            return '<div title="' + tags + '">' + tagHtml + '</div>';
        }
    },
    {
        name: 'createTime',
        label: '创建时间',
        width: '170',
        render(item) {
            if (item.createdTime) {
                return utcToTime(item.createdTime);
            } else {
                return '-';
            }
        }
    },
    {
        name: 'updatedTime',
        label: '更新时间',
        width: '170',
        render(item) {
            if (item.updatedTime) {
                return utcToTime(item.updatedTime);
            } else {
                return '-';
            }
        }
    },
    {
        name: 'opt',
        label: '操作',
        width: '120',
        fixed: 'right'
    }
];
