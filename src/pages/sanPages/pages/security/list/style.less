.vpc-security-list {
    min-height: 100%;
    background: #f7f7f9 !important;
    .s-table {
        .s-table-row:hover {
            .name-icon {
                display: inline-block;
            }
        }
        .name-icon {
            display: none;
            font-size: 12px;
            fill: #2468f2;
        }
        .s-table-body {
            max-height: calc(~'100vh - 426px');
        }
    }
    .inline_class {
        display: inline-flex;
    }
    .list-page-tb-left-toolbar {
        display: inline-flex;
    }
    .s-biz-page-header {
        margin: 0px !important;
        border: none !important;
        height: auto !important;
        .vpc-select {
            margin-left: 16px;
        }
        .introduce-panel {
            margin-top: 0px;
        }
    }
    .s-biz-page-footer {
        padding-bottom: 0 !important;
        margin-top: 16px !important;
    }
    .vpc-security-header {
        line-height: 47px;
        background: #fff;
        .title {
            display: inline-block;
            margin: 0;
            color: #151a26;
            font-size: 14px;
            font-weight: 400;
            margin-right: 10px;
        }
        .network {
            font-size: 12px;
            color: #999;
            vertical-align: middle;
        }
    }
    .s-cascader {
        font-size: 0;
        .s-cascader-value-arrow {
            top: 0%;
        }
    }
    .s-cascader-value {
        vertical-align: middle;
        border: none;
        font-size: 12px;
        padding-top: 0;
        padding-bottom: 0;
        line-height: 30px;
        min-width: 100px !important;
    }
    .name-icon {
        font-size: 12px;
        color: #2468f2;
    }
    .selectTip {
        margin-left: 5px;
        color: #999;
    }
    .operations {
        .s-button {
            padding: 0;
            margin-right: 16px;
        }
    }
    .security-res {
        .search-res {
            margin: 0px;
            display: inline-flex;
            .s-cascader {
                margin: 0 8px 0 0;
            }
        }
    }
    .ip-num-class {
        color: #f39000;
    }
    .ip-num-quota {
        color: #30bf13;
    }
}
.security-copy {
    .s-alert-skin-warning {
        height: 50px;
        margin-bottom: 12px;
    }
    .copy-tip {
        background: #fcf7f1;
        color: #333333;
        padding: 9px 20px 9px 40px;
        margin-bottom: 10px;
        position: relative;
        line-height: 20px;
        .icon-warning {
            color: #f39000;
            margin-right: 5px;
            position: absolute;
            left: 20px;
            font-size: 12px;
        }
    }
    .tip {
        font-size: 12px;
        margin-top: 10px;
        color: #999;
    }
    .remote-error-class {
        margin-left: 90px;
    }
    .s-form-item-help {
        width: 410px;
    }
    .s-form-item-error {
        width: 410px;
    }
}
.security-rule-list {
    .disable-line {
        .s-table-cell-text {
            color: #ccc !important;
            a {
                color: #ccc !important;
            }
        }
    }
    .ruletype {
        display: inline-block;
    }
    .lead-btn,
    .ruleiptype {
        float: right;
        margin-right: 8px;
    }
}
