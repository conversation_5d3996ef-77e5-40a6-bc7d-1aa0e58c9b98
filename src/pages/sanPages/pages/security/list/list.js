/*
 * @description: 安全组列表页
 * @file: network/security/pages/list.js
 * @author: p<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {Notification, Message, Input} from '@baidu/sui';
import {OutlinedRefresh, OutlinedPlus, OutlinedEditingSquare, OutlinedDownload} from '@baidu/sui-icon';

import rules from '../rules';
import Rule from '../util/rule';
import Confirm from '@/pages/sanPages/components/confirm';
import Copy from '../components/copy';
import Preview from '../components/preview';
import {columns} from './tableFields';
import VpcSelect from '@/pages/sanPages/components/vpcSelect/vpcSelect';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import testID from '@/testId';
import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import IntroducePanel from '@/pages/sanPages/components/introducePanel/introducePanel';
import './style.less';

const {asComponent, invokeSUI, invokeAppComp, invokeSUIBIZ, template, service, invokeComp} = decorators;
const tpl = html` <div>
    <s-biz-page class="{{klass}}">
        <div class="vpc-security-header" slot="header">
            <vpc-select class="vpc-select" on-change="vpcChange" dataTestId="${testID.security.listVPCSelect}" />
            <!--<introduce-panel
                isShow="{{show}}"
                klass="{{'endpoint-peerconn-wrapper'}}"
                title="{{introduceTitle}}"
                description="{{description}}"
                introduceOptions="{{introduceOptions}}"
                on-toggle="handleToggle($event)"
            ></introduce-panel>-->
        </div>
        <div class="list-page-tb-left-toolbar" slot="tb-left">
            <s-tooltip content="{{iamPass.message}}" trigger="{{iamPass.disable ? 'hover' : ''}}" placement="top">
                <s-button
                    disabled="{{iamPass.disable}}"
                    track-id="vpc_security_create"
                    data-test-id="${testID.security.listCreateBtn}"
                    track-name="创建普通安全组"
                    skin="primary"
                    on-click="onCreate"
                >
                    <outlined-plus />
                    {{'创建安全组'}}
                </s-button>
            </s-tooltip>
            <s-tooltip class="left_class" trigger="{{release.disable ? 'hover' : ''}}" placement="top">
                <div slot="content">
                    <!--bca-disable-next-line-->
                    {{release.message | raw}}
                </div>
                <s-button
                    on-click="onRelease"
                    disabled="{{release.disable || deleteLoading}}"
                    data-test-id="${testID.security.listDeleteBtn}"
                >
                    {{'删除'}}</s-button
                >
            </s-tooltip>
            <edit-tag
                s-else
                class="left_class"
                selectedItems="{{selectedItems}}"
                on-success="refresh"
                type="SECURITY_GROUP"
            ></edit-tag>
            <s-tooltip class="left_class" trigger="{{preview.disable ? 'hover' : ''}}" placement="top">
                <!--bca-disable-next-line-->
                <div slot="content">{{preview.message | raw}}</div>
                <s-button
                    on-click="onPreview"
                    disabled="{{preview.disable || deleteLoading}}"
                    data-test-id="${testID.security.listPreviewRules}"
                >
                    {{'预览规则'}}</s-button
                >
            </s-tooltip>
        </div>
        <div slot="tb-right" class="inline_class">
            <search-tag
                s-ref="search"
                serviceType="SECURITY_GROUP"
                searchbox="{=searchbox=}"
                on-search="onSearch"
                isShowResGroup="{{false}}"
            ></search-tag>
            <s-button on-click="refresh" class="s-icon-button left_class"
                ><outlined-refresh class="icon-class"
            /></s-button>
            <s-button on-click="onDownload" class="s-icon-button" track-id="ti_vpc_security_download" track-name="下载"
                ><outlined-download class="icon-class"
            /></s-button>
        </div>
        <s-table
            columns="{{table.columns}}"
            loading="{{table.loading || loadSgIpQuota}}"
            error="{{table.error}}"
            datasource="{{table.datasource}}"
            on-selected-change="tableSelected($event)"
            on-filter="onFilter"
            on-sort="onSort"
            selection="{=table.selection=}"
            data-test-id="${testID.security.listTable}"
        >
            <div slot="empty">
                <s-empty
                    on-click="onCreate"
                    track-id="vpc_security_create"
                    class="{{iamPass.disable ? 'create-disable' : ''}}"
                    data-test-id="${testID.security.listCreateBtn}"
                >
                </s-empty>
            </div>
            <div slot="error">
                {{'啊呀，出错了？'}}
                <a href="javascript:;" on-click="refresh">{{'重新加载'}}</a>
            </div>
            <div slot="c-name">
                <a
                    href="#/vpc/security/detail?id={{row.id}}&vpcId={{row.vpcId}}&securityGroupId={{row.securityGroupId}}"
                    class="truncated"
                    title="{{row.name}}"
                    track-id="vpc_security_enter_detail"
                    track-name="详情"
                    data-testid="${testID.security.listInstanceName}{{rowIndex}}"
                >
                    {{row.name}}
                </a>
                <s-popover
                    s-ref="{{'instanceNameEdit'+rowIndex}}"
                    placement="right"
                    trigger="click"
                    class="edit-popover-class"
                >
                    <div class="edit-wrap" slot="content">
                        <s-input
                            value="{=edit.name.value=}"
                            width="320"
                            placeholder="请输入名称"
                            on-input="onNameInput($event, rowIndex)"
                        />
                        <div class="edit-tip">
                            大小写字母、数字，中文以及-_/.特殊字符，必须以字母、中文开头，但不能以default开头，长度1-65
                        </div>
                        <s-button
                            skin="primary"
                            s-ref="{{'editNameBtn'+rowIndex}}"
                            disabled="{{true}}"
                            on-click="editConfirm(row, rowIndex, 'name')"
                            >确定</s-button
                        >
                        <s-button on-click="editNameCancel(rowIndex)">取消</s-button>
                    </div>
                    <outlined-editing-square
                        s-if="row.name!=='默认安全组'"
                        class="name-icon"
                        on-click="editName(row)"
                        data-test-id="${testID.security.listEditDescription}{{rowIndex}}"
                    />
                </s-popover>
                <br />
                <span class="truncated" title="{{row.securityGroupId}}">{{row.securityGroupId}}</span>
                <s-clip-board class="name-icon" text="{{row.securityGroupId}}" />
            </div>
            <div slot="c-desc">
                <span class="truncated" title="{{row.desc}}">{{row.desc || '-'}}</span>
                <s-popover
                    s-ref="{{'instanceDescEdit'+rowIndex}}"
                    placement="top"
                    trigger="click"
                    class="edit-popover-class"
                >
                    <div class="edit-wrap" slot="content">
                        <s-textarea
                            value="{=edit.desc.value=}"
                            width="200"
                            height="48"
                            on-input="onDescInput($event, rowIndex)"
                        />
                        <div class="edit-tip">描述不能超过200个字符</div>
                        <s-button
                            skin="primary"
                            s-ref="{{'editDescBtn'+rowIndex}}"
                            disabled="{{true}}"
                            on-click="editConfirm(row, rowIndex, 'desc')"
                            >确定</s-button
                        >
                        <s-button on-click="editDescCancel(rowIndex)">取消</s-button>
                    </div>
                    <outlined-editing-square class="name-icon" on-click="editDesc(row, rowIndex, 'description')" />
                </s-popover>
            </div>
            <div slot="c-associateNum">{{row | getAssociateNum}}/{{sgBindQuota || 2000}}</div>
            <div slot="c-tag">
                <span s-if="!row.tags || row.tags.length < 1"> - </span>
                <div s-else s-for="item,index in row.tags">
                    <span s-if="index <= 1"> {{item.tagKey + ':' + item.tagValue}} </span>
                    <div s-if="row.tags.length > 2 && index === 1">...</div>
                </div>
            </div>
            <div slot="c-vpcName">
                <span class="truncated" title="{{row.vpcName}}">
                    <a
                        href="#/vpc/instance/detail?vpcId={{row.vpcId}}"
                        class="text-hidden"
                        data-testid="${testID.security.listVPCName}{{rowIndex}}"
                    >
                        {{row.vpcName}}</a
                    >
                </span>
                <br />
                <span class="truncated" title="{{row.vpcShortId}}">{{row.vpcShortId || '-'}}</span>
            </div>
            <div slot="c-opt">
                <span class="operations">
                    <s-tooltip content="{{iamPass.message ? noCopyAuthTip: ''}}">
                        <s-button
                            disabled="{{iamPass.disable}}"
                            skin="stringfy"
                            on-click="copy(row)"
                            data-test-id="${testID.security.listCopy}{{rowIndex}}"
                            >{{'复制'}}</s-button
                        >
                    </s-tooltip>
                    <span s-if="row.name!=='默认安全组'">
                        <s-tooltip trigger="{{row.associateNum!==0?'hover':''}}">
                            <!--bca-disable-next-line-->
                            <div slot="content">{{(row | release) | raw}}</div>
                            <s-button skin="stringfy" disabled="{{row.associateNum!==0}}" on-click="delete(row)"
                                >删除</s-button
                            >
                        </s-tooltip>
                    </span>
                    <s-button
                        s-else
                        skin="stringfy"
                        on-click="resetDefault(row)"
                        data-test-id="${testID.security.listReset}{{rowIndex}}"
                        >{{'恢复默认规则'}}</s-button
                    >
                </span>
            </div>
        </s-table>
        <s-pagination
            s-if="{{pager.total}}"
            slot="footer"
            layout="{{'total, pageSize, pager, go'}}"
            pageSize="{{pager.size}}"
            total="{{pager.total}}"
            page="{{pager.page}}"
            on-pagerChange="onPagerChange"
            on-pagerSizeChange="onPageSizeChange"
        />
    </s-biz-page>
</div>`;
@asComponent('@vpc-security-list')
@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@invokeComp('@search-tag', '@edit-tag', '@vpc-select')
class VpcSecurityList extends Component {
    static components = {
        'outlined-refresh': OutlinedRefresh,
        'outlined-plus': OutlinedPlus,
        'outlined-editing-square': OutlinedEditingSquare,
        'outlined-download': OutlinedDownload,
        'vpc-select': VpcSelect,
        'introduce-panel': IntroducePanel,
        's-textarea': Input.TextArea
    };

    static filters = {
        release(item) {
            let {release} = checker.check(rules, [item]);
            return release.message;
        },
        getAssociateNum(item) {
            var count = item.associateNum;
            if (u.isNull(count) || u.isUndefined(count) || count < 0) {
                return '-';
            }
            return count;
        }
    };

    initData() {
        let filterColumns = columns;
        return {
            FLAG,
            klass: ['main-wrap-new', 'vpc-security-list'],
            vpcId: '',
            vpcList: [
                {
                    text: '所在网络：全部私有网络',
                    value: ''
                }
            ],
            searchbox: {
                keyword: '',
                placeholder: '请输入安全组名称进行搜索',
                keywordType: ['SECURITY_NAME'],
                keywordTypes: [
                    {value: 'SECURITY_NAME', text: '安全组名称'},
                    {value: 'SECURITY_ID', text: '安全组ID'},
                    {value: 'tag', text: '标签'}
                ]
            },
            selectedItems: [],
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                columns: filterColumns,
                datasource: []
            },
            pager: {
                size: 10,
                page: 1,
                total: 0
            },
            createSecGroup: {},
            release: {},
            edit: {
                name: {
                    value: '',
                    error: true,
                    visible: false
                },
                desc: {
                    value: '',
                    error: true,
                    visible: false
                }
            },
            instanceName: {
                value: '',
                error: true,
                visible: false
            },
            iamPass: {},
            noCopyAuthTip: '您没有复制安全组权限，请联系主用户添加',
            deleteLoading: false,
            urlQuery: getQueryParams(),
            show: true,
            introduceTitle: '安全组简介',
            description:
                '安全组是在VPC网络内为BCC实例和DCC专属实例、负载均衡实例、云数据库实例中创建的安全防火墙，定义IP＋端口的入站和出站访问策略，从而提高云服务器、负载均衡、云数据库等实例的安全性。',
            introduceIcon: INTRODUCE_ICON_COLOR,
            introduceEle: null
        };
    }
    inited() {
        this.getIamQuery();
        this.data.set('loadSgIpQuota', true);
        this.$http
            .commonQuota({quotaType: 'SgBindInstanceQuota', serviceType: 'VPC'})
            .then(res => {
                this.data.set('sgBindQuota', res);
            })
            .finally(() => {
                this.data.set('loadSgIpQuota', false);
            });
        this.data.get('context').callbackFn(this.handleShowCard.bind(this));
        const id = this.data.get('urlQuery.id');
        if (id) {
            this.data.set('searchbox.keywordType', ['SECURITY_ID']);
            this.data.set('searchbox.keyword', id);
        }
    }
    tableSelected(e) {
        this.data.set('selectedItems', e.value.selectedItems);

        let {release, preview} = checker.check(rules, e.value.selectedItems);
        this.data.set('release', release);
        this.data.set('preview', preview);
    }

    onCreate() {
        location.hash = '#/vpc/security/create?vpcId=' + window.$storage.get('vpcId');
    }

    attached() {
        let vpcId = this.data.get('urlQuery.vpcId');
        if (vpcId) {
            window.$storage.set('vpcId', vpcId);
        }
        if (!this.data.get('FLAG').NetworkSecuritySupportOrganization) {
            this.getSecurityTags();
        }
        this.loadPage();
        let {release, preview} = checker.check(rules, []);
        this.data.set('release', release);
        this.data.set('preview', preview);
    }
    onPagerChange(e) {
        this.resetTable();
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }
    onPageSizeChange(e) {
        this.data.set('pager.page', 1);
        this.data.set('pager.size', e.value.pageSize);
        if (this.data.get('pager.page') === 1) {
            this.loadPage();
        }
    }
    getSecurityTags() {
        this.ref('search').getTags();
    }
    onRelease() {
        let names = this.data
            .get('selectedItems')
            .map(item => item.name)
            .join('、');
        let confirm = new Confirm({
            data: {
                open: true,
                content: '确认删除安全组：' + names + '?'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.data.set('deleteLoading', true);
            let ids = this.data.get('selectedItems').map(item => item.id);
            this.$http
                .securityBatchDelete({securityGroupIds: ids})
                .then(() => {
                    this.refresh();
                    Notification.success('删除成功', {placement: 'topRight'});
                })
                .finally(() => {
                    this.data.set('deleteLoading', false);
                });
        });
    }
    delete(row) {
        let confirm = new Confirm({
            data: {
                open: true,
                content: '确认删除安全组：' + row.name + '吗？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            let ids = [row.id];
            this.$http.securityBatchDelete({securityGroupIds: ids}).then(() => {
                this.refresh();
                Notification.success('删除成功', {placement: 'topRight'});
            });
        });
    }
    getTip(row) {
        var security = row;
        var content = '';
        if (security && security.associateNum) {
            content =
                '当前已经启用这一安全组规则，您的任何修改将在保存后立即生效。' +
                '请确认您所设置的安全规则对当前云服务器的正常服务无任何影响！';
        }
        return content;
    }
    resetDefault(row) {
        let content = this.getTip(row);
        if (content) {
            let confirm = new Confirm({
                data: {
                    open: true,
                    content
                }
            });
            confirm.attach(document.body);
            confirm.on('confirm', () => {
                this.resetReq(row);
            });
        } else {
            this.resetReq(row);
        }
    }
    resetReq(row) {
        var rules = [];
        rules.push(
            Rule.toJSON(
                {
                    source: 'user',
                    protocol: 'all',
                    remoteIP: 'all'
                },
                'ingress'
            )
        );
        rules.push(
            Rule.toJSON(
                {
                    source: 'system',
                    protocol: 'all',
                    remoteGroupId: row.id
                },
                'ingress'
            )
        );
        rules.push(
            Rule.toJSON(
                {
                    source: 'user',
                    protocol: 'all',
                    remoteIP: 'all'
                },
                'egress'
            )
        );
        let payload = {
            desc: '',
            id: row.id,
            name: row.name,
            vpcId: row.vpcId,
            rules
        };
        return this.$http.securityUpdate(payload).then(() => {
            Notification.success('恢复成功');
        });
    }
    edit(item) {
        location.hash = '#/vpc/security/edit?vpcId=' + item.vpcId + '&dcgwId=' + item.id;
    }
    onSearch() {
        this.data.set('pager.page', 1);
        this.resetTable();
        return this.loadPage();
    }
    refresh() {
        this.resetTable();
        return this.loadPage();
    }
    // 重置列表
    resetTable() {
        this.data.set('pager.page', 1);
        this.data.set('table.selection.selectedIndex', []);
    }
    getSearchCriteria() {
        const {pager, order} = this.data.get('');
        const vpcId = window.$storage.get('vpcId');
        const searchParam = this.ref('search') ? this.ref('search').getSearchCriteria() : {};
        if (FLAG.NetworkSubnetSupportOrganization) {
            const organizationId = window.$framework.organization.getCurrentOrganization().id;
            const currentResourceGroupIds = window.$framework.organization.getCurrentResourceGroup().id;
            const resourceGroupIds = currentResourceGroupIds === 'all' ? [] : [currentResourceGroupIds];
            u.assign(searchParam, {
                organizationId,
                resourceGroupIds
            });
        }
        return u.extend({}, searchParam, order, {vpcId}, {pageNo: pager.page, pageSize: pager.size});
    }

    loadPage(payload) {
        this.data.set('table.loading', true);
        payload = payload || this.getSearchCriteria();
        this.$http.securityListV3(payload).then(data => {
            this.data.set('table.datasource', data.result);
            this.data.set('pager.total', data.totalCount);
            this.data.set('table.loading', false);
        });
    }
    copy(row) {
        let copy = new Copy({
            data: {
                open: true,
                id: row.id,
                vpcId: row.vpcId,
                info: row,
                desc: row.desc
            }
        });
        copy.on('success', () => {
            this.loadPage();
        });
        copy.attach(document.body);
    }

    onPreview() {
        const preview = new Preview({
            data: {
                open: true,
                ids: this.data.get('selectedItems').map(item => item.id)
            }
        });
        preview.attach(document.body);
    }

    vpcChange() {
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    // 点击修改名称icon
    editName(row) {
        this.data.set('edit.name.value', row.name);
        this.data.set('edit.name.error', false);
    }
    editDesc(row) {
        this.data.set('edit.desc.value', row.desc);
        this.data.set('edit.desc.error', false);
    }
    // 修改名称确认
    editConfirm(row, rowIndex, type) {
        let instance = this.data.get(`edit.${type}`);
        if (instance.error) {
            return;
        }
        this.$http
            .updateSecurityField({
                [type]: instance.value,
                id: row.id,
                vpcId: row.vpcId
            })
            .then(() => {
                if (type === 'desc') {
                    this.editDescCancel(rowIndex);
                } else {
                    this.editNameCancel(rowIndex);
                }
                this.loadPage();
                Message.success({
                    content: '修改成功'
                });
            });
    }
    // 输入名称
    onNameInput(e, rowIndex) {
        let result = false;
        if (e.value === '' || !/^(?!default)[a-zA-Z\u4e00-\u9fa5][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/.test(e.value)) {
            result = true;
        } else {
            result = false;
        }
        this.data.set('edit.name.error', result);
        this.data.set('edit.name.value', e.value);
        this.ref('editNameBtn' + rowIndex).data.set('disabled', result);
    }
    // 编辑弹框-输入名称/描述
    onDescInput(e, rowIndex) {
        let result = e.value.length > 200;
        this.data.set('edit.desc.error', result);
        this.data.set('edit.desc.value', e.value);
        this.ref('editDescBtn' + rowIndex).data.set('disabled', result);
    }
    // 修改名称取消
    editNameCancel(rowIndex) {
        this.ref('editNameBtn' + rowIndex).data.set('disabled', true);
        this.ref('instanceNameEdit' + rowIndex).data.set('visible', false);
    }
    editDescCancel(rowIndex) {
        this.ref('editDescBtn' + rowIndex).data.set('disabled', true);
        this.ref('instanceDescEdit' + rowIndex).data.set('visible', false);
    }
    onRegionChange() {
        location.reload();
    }
    getIamQuery() {
        this.$http.getInterfaceIam({interfaceName: 'createSecurityGroup'}).then(res => {
            if (!res.requestId && !res.masterAccount) {
                if (!res.interfacePermission) {
                    this.data.set('iamPass', {
                        disable: true,
                        message: '您没有创建安全组权限，请联系主用户添加'
                    });
                } else {
                    this.data.set('iamPass', {
                        disable: false,
                        message: ''
                    });
                }
            } else {
                this.data.set('iamPass', {
                    disable: false,
                    message: ''
                });
            }
        });
    }
    // 下载
    onDownload() {
        let ids = [];
        ids = this.data.get('selectedItems').map(item => {
            return item.securityGroupId;
        });
        let filter = 'ids=' + ids.join(',');
        window.open(`/api/network/v1/security/sg/download?` + filter);
    }
    handleToggle(flag) {
        this.data.set('show', flag);
        this.data.set('introduceIcon', INTRODUCE_ICON);
        this.data.get('context').handleToggle();
    }
    handleShowCard() {
        if (!this.data.get('show')) {
            this.data.set('show', true);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type) {
        this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#2468F2';
    }
    handleMouseLeave(type) {
        const isShow = this.data.get('show');
        this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        !isShow && (ele.style.color = '#151B26');
    }
}
export default San2React(Processor.autowireUnCheckCmpt(VpcSecurityList));
