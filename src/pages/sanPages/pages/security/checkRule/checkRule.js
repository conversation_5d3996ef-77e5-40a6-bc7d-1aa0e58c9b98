import {defineComponent} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Alert, Button, Table, Icon, Popover} from '@baidu/sui';
import u from 'lodash';

import {checkIsInSubnet} from '@/pages/sanPages/utils/common';
import Rule from '../util/rule';
import ERule from '../../enterpriseSecurity/util/rule';
import {SecurityIpVersion as IpVersion} from '@/pages/sanPages/common/enum';
import {checkColumns} from './columns';
import './checkRule.less';

const template = html`
    <div>
        <s-dialog
            width="800"
            class="security-check-rule"
            closeAfterMaskClick="{{false}}"
            open="{=open=}"
            title="{{'规则检查'}}"
        >
            <s-alert skin="warning" class="alert-tip"> {{ruleTip}} </s-alert>
            <p class="conflict-nums">
                当前安全组{{ruleType === 'in' ? '入' : '出'}}站有{{table.datasource.length}}条冗余规则
            </p>
            <s-table
                columns="{{table.columns}}"
                on-selected-change="tableSelected($event)"
                selection="{=table.selection=}"
                datasource="{{table.datasource}}"
            >
                <div slot="c-status" class="status_class">
                    <div class="status-warp">
                        <s-popover class="check-rule-popover">
                            <s-icon name="fail-reverse"></s-icon>
                            <div slot="content">
                                <p>该规则被以下规则视为冗余</p>
                                <s-table columns="{{table.hoverColumns}}" datasource="{{row.parent}}"> </s-table>
                            </div>
                        </s-popover>
                    </div>
                </div>
                >
            </s-table>
            <div slot="footer">
                <div style="padding-left: 300px; padding-top:20px; display: inline-block;">
                    <s-button size="larger" on-click="checkCancel"> {{'取消'}} </s-button>
                    <s-button
                        disabled="{{!table.selection.selectedIndex.length || submitDisabled}}"
                        skin="primary"
                        on-click="deleteRule"
                    >
                        {{'删除'}}
                    </s-button>
                </div>
            </div>
        </s-dialog>
    </div>
`;

export default defineComponent({
    template,
    components: {
        's-dialog': Dialog,
        's-alert': Alert,
        's-table': Table,
        's-popover': Popover,
        's-icon': Icon,
        's-button': Button
    },
    initData() {
        return {
            open: false,
            table: {
                selection: {
                    mode: 'multi',
                    selectedIndex: [],
                    disabledIndex: []
                },
                datasource: [],
                hoverDatasource: [],
                hoverColumns: checkColumns,
                columns: [...checkColumns, {name: 'status', label: '检查', width: 60}]
            }
        };
    },

    computed: {
        ruleTip() {
            if (this.data.get('securityType') === 'enterprise') {
                return '温馨提示：安全组规则A的所有条件被规则B完全包含，并且规则 A 的优先级小于等于规则 B的优先级，则规则A 被认定为冗余规则。';
            } else {
                return '温馨提示：安全组规则 A的所有条件被规则B完全包含，则规则A 被认定为冗余规则。';
            }
        }
    },

    inited() {
        if (this.data.get('ruleType') === 'in') {
            checkColumns[3].label = '来源';
        } else {
            checkColumns[3].label = '目的';
        }
        if (this.data.get('securityType') === 'enterprise') {
            this.data.splice('table.hoverColumns', [
                1,
                0,
                {
                    name: 'priority',
                    label: '优先级',
                    width: 90
                }
            ]);
            this.data.splice('table.columns', [
                1,
                0,
                {
                    name: 'priority',
                    label: '优先级',
                    width: 90
                }
            ]);
            if (this.data.get('ruleType') === 'in') {
                this.data.splice('table.hoverColumns', [
                    3,
                    0,
                    {
                        name: 'localIp',
                        label: '目的IP',
                        width: 90,
                        render(item) {
                            return (
                                '目的IP：' +
                                (item.localIp === 'all'
                                    ? item.ethertype === IpVersion.IPV4
                                        ? '0.0.0.0/0'
                                        : '::/0'
                                    : u.escape(item.localIp))
                            );
                        }
                    }
                ]);
                this.data.splice('table.hoverColumns', [
                    6,
                    0,
                    {
                        name: 'localPortRange',
                        label: '源端口',
                        width: 90,
                        render(item) {
                            if (item.protocol === 'all' || item.protocol === 'icmp') {
                                return '不涉及';
                            }
                            if (!item.localPortRange) {
                                return '1-65535';
                            }
                            return u.escape(item.localPortRange);
                        }
                    }
                ]);
                this.data.splice('table.columns', [
                    3,
                    0,
                    {
                        name: 'localIp',
                        label: '目的IP',
                        width: 90,
                        render(item) {
                            return (
                                '目的IP：' +
                                (item.localIp === 'all'
                                    ? item.ethertype === IpVersion.IPV4
                                        ? '0.0.0.0/0'
                                        : '::/0'
                                    : u.escape(item.localIp))
                            );
                        }
                    }
                ]);
                this.data.splice('table.columns', [
                    6,
                    0,
                    {
                        name: 'localPortRange',
                        label: '源端口',
                        width: 90,
                        render(item) {
                            if (item.protocol === 'all' || item.protocol === 'icmp') {
                                return '不涉及';
                            }
                            if (!item.localPortRange) {
                                return '1-65535';
                            }
                            return u.escape(item.localPortRange);
                        }
                    }
                ]);
            }
            if (this.data.get('ruleType') === 'out') {
                this.data.splice('table.columns', [
                    3,
                    0,
                    {
                        name: 'localPortRange',
                        label: '源端口',
                        width: 90,
                        render(item) {
                            if (item.protocol === 'all' || item.protocol === 'icmp') {
                                return '不涉及';
                            }
                            if (!item.localPortRange) {
                                return '1-65535';
                            }
                            return u.escape(item.localPortRange);
                        }
                    }
                ]);
                this.data.splice('table.columns', [
                    4,
                    0,
                    {
                        name: 'localIp',
                        label: '源IP',
                        width: 90,
                        render(item) {
                            return (
                                '源IP：' +
                                (item.localIp === 'all'
                                    ? item.ethertype === IpVersion.IPV4
                                        ? '0.0.0.0/0'
                                        : '::/0'
                                    : u.escape(item.localIp))
                            );
                        }
                    }
                ]);
                this.data.splice('table.hoverColumns', [
                    3,
                    0,
                    {
                        name: 'localPortRange',
                        label: '源端口',
                        width: 90,
                        render(item) {
                            if (item.protocol === 'all' || item.protocol === 'icmp') {
                                return '不涉及';
                            }
                            if (!item.localPortRange) {
                                return '1-65535';
                            }
                            return u.escape(item.localPortRange);
                        }
                    }
                ]);
                this.data.splice('table.hoverColumns', [
                    4,
                    0,
                    {
                        name: 'localIp',
                        label: '源IP',
                        width: 90,
                        render(item) {
                            return (
                                '源IP：' +
                                (item.localIp === 'all'
                                    ? item.ethertype === IpVersion.IPV4
                                        ? '0.0.0.0/0'
                                        : '::/0'
                                    : u.escape(item.localIp))
                            );
                        }
                    }
                ]);
            }
        }
        this.getConflictRule();
    },

    /**
     * 计算冗余规则
     *
     * 普通安全组：类型相同；协议、端口、source/target均被包含
     * 企业安全组：类型相同；协议、端口、source/target均被包含，且优先级低
     */
    getConflictRule() {
        let datasource = this.data.get('ruleDatasource');
        let data = [];
        datasource.forEach((current, currentIndex) => {
            // 判断是否当前是否包含
            let isContain = false;
            datasource.forEach((sub, subIndex) => {
                if (currentIndex !== subIndex) {
                    let isSubContain = false;
                    let isRuleContain = this.judgeContain(current, sub);
                    if (this.data.get('securityType') === 'enterprise') {
                        isSubContain = isRuleContain && Number(current.priority) >= Number(sub.priority);
                    } else {
                        isSubContain = isRuleContain;
                    }
                    if (isContain) {
                        // 继续找包含冗余的规则
                        if (isSubContain) {
                            current.parent.push(sub);
                        }
                    } else {
                        // 首次找到
                        if (isSubContain) {
                            isContain = true;
                            current.parent = [sub];
                        }
                    }
                }
            });
            if (isContain) {
                let currentMap =
                    this.data.get('securityType') === 'normal' ? Rule.fromJSON(current) : ERule.fromJSON(current);
                currentMap.parent = current.parent.map(rule => {
                    if (this.data.get('securityType') === 'normal') {
                        return {
                            ...Rule.fromJSON(rule),
                            remark: rule.name
                        };
                    } else {
                        return ERule.fromJSON(rule);
                    }
                });
                currentMap.remark = this.data.get('securityType') === 'normal' ? current.name : current.remark;
                currentMap.ipCollectionType = current.ipCollectionType;
                currentMap.ipCollectionUuid = current.ipCollectionUuid;
                currentMap.ipCollectionId = current.ipCollectionId;
                data.push(currentMap);
            }
        });
        this.data.set('table.datasource', data);
    },

    // 计算类型，协议(全部协议包含其他的协议)，端口，source是否包含
    judgeContain(source, target) {
        const isEthertypeContain = source.ethertype === target.ethertype;
        if (!isEthertypeContain) {
            return false;
        } else {
            const isProtocolContain =
                source.protocol === target.protocol || target.protocol === '' || target.protocol === 'all';
            let isPortContain = false;
            let sourcePort = source.portRange.split('-');
            let targetPort = target.portRange.split('-');
            let allPort = ['', '1-65535'];
            if (allPort.includes(source.portRange)) {
                if (allPort.includes(target.portRange)) {
                    isPortContain = true;
                } else {
                    isPortContain = false;
                }
            } else {
                if (allPort.includes(target.portRange)) {
                    isPortContain = true;
                } else {
                    if (sourcePort.length === 1 && targetPort.length === 1) {
                        if (source.portRange === target.portRange) {
                            isPortContain = true;
                        } else {
                            isPortContain = false;
                        }
                    } else if (sourcePort.length === 1 && targetPort.length === 2) {
                        isPortContain =
                            Number(sourcePort[0]) >= Number(targetPort[0]) &&
                            Number(sourcePort[0]) <= Number(targetPort[1]);
                    } else if (sourcePort.length === 2 && targetPort.length === 1) {
                        isPortContain = false;
                    } else {
                        if (source.portRange === target.portRange) {
                            isPortContain = true;
                        } else {
                            isPortContain =
                                Number(sourcePort[0]) >= Number(targetPort[0]) &&
                                Number(sourcePort[1]) <= Number(targetPort[1]);
                        }
                    }
                }
            }
            let isIpContain = false;
            if (
                (!target.ipCollectionType && target.remoteIP === '') ||
                target.remoteIP === '0.0.0.0/0' ||
                target.remoteIP === 'all'
            ) {
                isIpContain = true;
            } else if (source.remoteIP === '' || source.remoteIP === '0.0.0.0/0' || source.remoteIP === 'all') {
                isIpContain = false;
            } else {
                isIpContain = checkIsInSubnet(target.remoteIP, source.remoteIP);
            }
            return isProtocolContain && isPortContain && isIpContain;
        }
    },

    tableSelected(e) {
        this.data.set('selectedRule', e.value.selectedItems);
    },

    deleteRule() {
        let datasource = this.data.get('table.datasource');
        let selectedIndex = this.data.get('table.selection.selectedIndex');
        let result = [];
        let securityIndex = [];
        if (this.data.get('securityType') === 'enterprise') {
            selectedIndex.forEach(index => {
                result.push(datasource[index].esgRuleUuid);
                securityIndex.push(index);
            });
        } else {
            selectedIndex.forEach(index => {
                result.push(datasource[index].id);
                securityIndex.push(index);
            });
        }
        this.data.set('submitDisabled', true);
        this.fire('checkConfirm', result);
    },

    checkCancel() {
        this.data.set('open', false);
    }
});
