import u from 'lodash';
import {SecurityIpVersion as IpVersion} from '@/pages/sanPages/common/enum';

export const checkColumns = [
  {
    name: 'ethertype', label: '类型',
    width: 90,
    render(item) {
      if (!item.edit) {
        return u.escape(item.ethertype);
      }
      return item.ethertype;
    }
  },
  {
    name: 'protocol', label: '协议',
    width: 90,
    render(item) {
      if (!item.edit) {
        if (item.protocol === 'all') {
          return '全部协议';
        }
        return u.escape(item.protocol);
      }
      return item.protocol;
    }
  },
  {
    name: 'portRange',
    label: '目的端口',
    width: 90,
    render(item) {
      if (!item.edit) {
        return u.escape(item.portRange);
      }
      return item.portRange;
    }
  },
  {
    name: 'source', label: '来源',
    width: 140,
    render(item) {
      if (!item.edit) {
        if (item.ipCollectionType) {
          if (item.ipCollectionType === 1) {
              return 'IP地址组：' + item.ipCollectionId;
          }
          return 'IP地址族：' + item.ipCollectionId;
        }
        if (item.source === 'user') {
          return (item.direction === 'ingress' ? '源IP：' : '目的IP：')
            + (item.remoteIP === 'all'
              ? (item.ethertype === IpVersion.IPV4 ? '0.0.0.0/0' : '::/0')
              : u.escape(item.remoteIP));
        }
        var id = item.remoteGroupShortId || item.remoteGroupId;
        return '安全组：' + u.escape(item.remoteGroupName)
          + (id ? '（' + u.escape(id) + '）' : '');
      }
      return item.source;
    }
  },
  {
    name: 'remark', label: '备注',
    width: 140,
    render(item) {
      if (!item.edit) {
        return u.escape(item.remark);
      }
      return item.remark;
    }
  }
];
