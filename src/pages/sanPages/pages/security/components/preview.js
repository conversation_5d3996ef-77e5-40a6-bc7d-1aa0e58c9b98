import {Component} from 'san';
import u from 'lodash';
import {html} from '@baiducloud/runtime';
import {Dialog, Radio, Button, Table} from '@baidu/sui';

import Rule from '../util/rule';
import {DirectionType} from '@/pages/sanPages/common/enum';
import {SecurityIpVersion as IpVersion} from '@/pages/sanPages/common/enum';

const tpl = html`
<template>
  <s-dialog
    class="security-preview"
    open="{=open=}"
    width="800"
    title="{{'预览规则'}}">
    <s-radio-group
      value="{=direction=}"
      radioType="button"
      datasource="{{directionList}}"
      class="security-direction-radio"
      on-change="ruleTypeChange($event)"
    >
    </s-radio-group>
    <s-table
      columns="{{table.columns}}"
      loading="{{table.loading}}"
      datasource="{{datasource}}">
      <div slot="c-name">
        <span title="{{row.securityGroupName}}">{{row.securityGroupName}}</span>
        <br>
        <span class="truncated" title="{{row.securityGroupId}}">{{row.securityGroupId}}</span>
      </div>
      <div slot="c-protocol">
        <span>{{row | protocol}}</span>
      </div>
      <div slot="c-portRange">
        <span>{{row | portRange}}</span>
      </div>
      <div slot="c-source">
        <span>{{row | source}}</span>
      </div>
      <div slot="c-action">
        <span>{{row | action}}</span>
      </div>
      <div slot="c-remark">
        <span>{{row | remark}}</span>
      </div>
    </s-table>
    <div slot="footer">
      <s-button skin="primary" on-click="close">{{'确定'}}</s-button>
    </div>
  </s-dialog>
</template>
`;

export default class SecurityPreview extends Component {
  static template = tpl;
  static components = {
    's-dialog': Dialog,
    's-button': Button,
    's-radio-group': Radio.RadioGroup,
    's-table': Table
  }

  static computed = {
    datasource() {
      if (this.data.get('direction') === 'ingress') {
        return this.data.get('table.inDatasource');
      } else {
        return this.data.get('table.outDatasource');
      }
    },
  }

  static filters = {
    priority(row) {
      if (!row.priority) {
        return u.escape(row.priority);
      }
      return row.priority;
    },
    protocol(row) {
      if (row.protocol === 'all') {
        return '全部协议';
      }
      return u.escape(row.protocol);
    },
    portRange(row) {
      return u.escape(row.portRange);
    },
    source(row) {
      if (row.source === 'user') {
        return (this.data.get('direction') === 'ingress' ? '源IP：' : '目的IP：')
          + (row.remoteIP === 'all'
            ? (row.ethertype === IpVersion.IPV4 ? '0.0.0.0/0' : '::/0')
            : u.escape(row.remoteIP));
      }
      var id = row.remoteGroupShortId || row.remoteGroupId;
      return '安全组：' + u.escape(row.remoteGroupName)
        + (id ? '（' + u.escape(id) + '）' : '');
    },
    action(row) {
      return row.action === 'allow' ? '允许' : '拒绝';
    },
    remark(row) {
      return row.remark || '-';
    }
  }

  initData() {
    return {
      open: false,
      directionList: DirectionType.toArray(),
      direction: DirectionType.INGRESS,
      ruleList: [],
      table: {
        loading: false,
        inDatasource: [],
        outDatasource: [],
        columns: [
          {
            name: 'name', label: '安全组',
            width: 90
          },
          {
            name: 'ethertype', label: '类型',
            width: 60,
            render(item) {
              return u.escape(item.ethertype);
            }
          },
          {
            name: 'protocol', label: '协议',
            width: 60,
            render(item) {
              if (item.protocol === 'all') {
                return '全部协议';
              }
              return u.escape(item.protocol);
            }
          },
          {
            name: 'portRange',
            label: '端口',
            width: 60,
            render(item) {
              return u.escape(item.portRange);
            }
          },
          {
            name: 'source', label: 'Source',
            width: 90,
            render(item) {
              if (item.source === 'user') {
                return (this.id === 'rulesInTable' ? '源IP：' : '目的IP：')
                  + (item.remoteIP === 'all'
                  ? (item.ethertype === IpVersion.IPV4 ? '0.0.0.0/0' : '::/0')
                  : u.escape(item.remoteIP));
              }
              var id = item.remoteGroupShortId || item.remoteGroupId;
              return '安全组：' + u.escape(item.remoteGroupName)
                + (id ? '（' + u.escape(id) + '）' : '');
            }
          },
          {
            name: 'remark', label: '备注',
            width: 90
          }
        ]
      }
    };
  }

  ruleTypeChange(e) {
    if (e.value === 'ingress') {
      this.data.set('table.columns[4].label', 'Source');
    } else {
      this.data.set('table.columns[4].label', 'Target');
    }
  }

  inited() {
    this.getPreviewRules();
  }

  close() {
    this.data.set('open', false);
  }

  getPreviewRules() {
    this.data.set('table.loading', true);
    this.$http.getSecurityPreview({
      securityGroupUuids: this.data.get('ids')
    }).then(res => {
      this.data.set('table.loading', false);
      let inDatasource = res.ingressRules;
      if (inDatasource.length) {
        inDatasource = inDatasource.map(item => {
          item.remoteIP = item.remoteIpPrefix;
          let newItem = Rule.fromJSON(item);
          newItem.remark = item.remark;
          newItem.securityGroupName = item.securityGroupName;
          newItem.securityGroupId = item.securityGroupId;
          return newItem;
        });
      }
      this.data.set('table.inDatasource', inDatasource);
      let outDatasource = res.egressRules;
      if (outDatasource.length) {
        outDatasource = outDatasource.map(item => {
          item.remoteIP = item.remoteIpPrefix;
          let newItem = Rule.fromJSON(item);
          newItem.remark = item.remark;
          newItem.securityGroupName = item.securityGroupName;
          newItem.securityGroupId = item.securityGroupId;
          return newItem;
        });
      }
      this.data.set('table.outDatasource', outDatasource);
    }).catch(() => {
      this.data.set('table.loading', false);
    });
  }
}
