/*
 * @description: 安全组支持批量创建
 */
import {Component} from 'san';

import {html, decorators} from '@baiducloud/runtime';
import {Dialog, Icon, Button, Table, Input, Select, Tooltip, Radio} from '@baidu/sui';
import {Empty, Tip} from '@baidu/sui-biz';
import u from 'lodash';

import Rule from '../util/rule';
import kCommonServices from '../util/services';
import Confirm from '@/pages/sanPages/components/confirm';
import {SecurityIpVersion as IpVersion} from '@/pages/sanPages/common/enum';
import {checkIpv6Cidr, convertCidrToBinary} from '@/pages/sanPages/utils/common';
import regs from '@/pages/sanPages/utils/rule';
import './createRule.less';

import {utcToTime} from '@/pages/sanPages/utils/helper';

const {service} = decorators;

function isPort(v) {
    return /^[1-9][0-9]{0,4}/.test(v) && v <= 65535;
}
function portRange(value) {
    value = u.trim(value);
    if (value.toLowerCase() === 'all') {
        return false;
    }
    if (value.indexOf('-') === -1) {
        return isPort(value);
    }
    let array = value.split('-');
    return isPort(array[0]) && isPort(array[1]) && +array[0] < +array[1];
}
function checkIp(value, ipVersion) {
    value = u.trim(value);
    if (value.toLowerCase() === 'all') {
        return '';
    }
    if (ipVersion === 'IPv6') {
        if (!checkIpv6Cidr(value)) {
            let valueString = convertCidrToBinary(value);
            let valueMask = value.split('/')[1];
            // 掩码分割的前部分
            let preValueString = valueString.substring(0, +valueMask);
            // 掩码分割的前后部分
            let tailValueString = valueString.substring(+valueMask, valueString.length);
            if (valueMask && tailValueString.includes('1')) {
                let addLen = 128 - preValueString.length;
                let fixIpBinary = preValueString + new Array(addLen).fill('0').join('');
                // 每隔16位按:分割
                let ipBinaryArr = fixIpBinary
                    .replace(/(.{16})/g, '$1:')
                    .substring(0, 128)
                    .split(':');
                let ipArr = ipBinaryArr.map(binary => {
                    // 先转为10进制，再转为16进制
                    return parseInt(binary, 2).toString(16);
                });
                let fixIp = ipArr.join(':') + '/' + valueMask;
                // 连续:0替换::
                let replaceIp = fixIp.replace(/(:0){2,}/g, '::');
                return `网段与掩码不匹配，建议改为${replaceIp}`;
            }
            return 'IP格式不正确';
        }
    } else {
        let reg = new RegExp(regs.IP_CIDR);
        if (!reg.test(value)) {
            return 'IP格式不正确';
        }
        let valueString = convertCidrToBinary(value);
        let valueMask = value.split('/')[1];
        // 掩码分割的前部分
        let preValueString = valueString.substring(0, +valueMask);
        // 掩码分割的前后部分
        let tailValueString = valueString.substring(+valueMask, valueString.length);
        if (valueMask && tailValueString.includes('1')) {
            let addLen = 32 - preValueString.length;
            let fixIpBinary = preValueString + new Array(addLen).fill('0').join('');
            // 每隔8位按.分割
            let ipBinaryArr = fixIpBinary
                .replace(/(.{8})/g, '$1.')
                .substring(0, 32)
                .split('.');
            let ipArr = ipBinaryArr.map(binary => parseInt(binary, 2));
            let fixIp = ipArr.join('.') + '/' + valueMask;
            return `网段与掩码不匹配，建议改为${fixIp}`;
        }
    }
    return '';
}

/* eslint-disable */
const tpl = html`
    <template>
        <s-dialog class="security-rule-create" open="{=open=}" width="1200" title="{{title}}">
            <div class="ruleIpTypeLine" s-if="!isEdit">
                <span>规则类型：</span>
                <s-radio-radio-group
                    enhanced
                    class="ruletype"
                    on-change="ruleIpChange"
                    value="{=ruleIpType=}"
                    radioType="button"
                >
                    <s-radio value="ipv4" label="IPv4"></s-radio>
                    <s-radio value="ipv6" label="IPv6"></s-radio>
                </s-radio-radio-group>
            </div>
            <s-table
                s-if="ruleIpType === 'ipv4'"
                style="overflow: visible"
                columns="{{table.columns}}"
                datasource="{{table.datasource}}"
            >
                <div slot="c-ethertype">
                    <s-select
                        width="90"
                        on-change="itemChange('ethertypes',rowIndex,$event)"
                        datasource="{{ethertypeList}}"
                        value="{=ethertypes[rowIndex]=}"
                    />
                </div>
                <div slot="c-protocol">
                    <s-select
                        width="140"
                        on-change="itemChange('protocols',rowIndex,$event)"
                        datasource="{{protocolList}}"
                        value="{=protocols[rowIndex]=}"
                    />
                </div>
                <div slot="c-portRange">
                    <s-input
                        width="100"
                        disabled="{{portRangeDisable[rowIndex]}}"
                        on-input="itemChange('portRanges',rowIndex,$event)"
                        value="{=portRanges[rowIndex]=}"
                        placeholder="1-65535"
                    >
                    </s-input>
                    <p style="color: #EB5252" s-if="portRangeErr[rowIndex]">{{portRangeErr[rowIndex]}}</p>
                </div>
                <div slot="c-source">
                    <div>
                        <s-select
                            width="{{80}}"
                            on-change="itemChange('sources',rowIndex,$event)"
                            datasource="{{sourceList}}"
                            value="{=sources[rowIndex]=}"
                        />
                        <s-input
                            s-if="sources[rowIndex]==='user'"
                            on-input="itemChange('remoteIPs',rowIndex,$event)"
                            width="{{100}}"
                            value="{=remoteIPs[rowIndex]=}"
                            placeholder="all"
                        ></s-input>
                        <s-select
                            s-if="sources[rowIndex]==='system'"
                            on-change="itemChange('remoteGroupIds',rowIndex,$event)"
                            width="{{120}}"
                            datasource="{{securityGroupList}}"
                            value="{=remoteGroupIds[rowIndex]=}"
                            filterable
                        />
                        <p style="color: #EB5252" s-if="remoteIPErr[rowIndex]">{{remoteIPErr[rowIndex]}}</p>
                    </div>
                </div>
                <div slot="c-name">
                    <s-input width="100" on-input="itemChange('names',rowIndex,$event)" value="{=names[rowIndex]=}">
                    </s-input>
                </div>
                <div slot="c-opt" class="operations">
                    <s-button s-if="!isEdit" skin="stringfy" on-click="deleteRule(rowIndex)">删除</s-button>
                    <s-tip
                        s-if="{{statusMessage[rowIndex]}}"
                        content="{{statusMessage[rowIndex]}}"
                        position="tc"
                        inComponent
                        class="tip_reverse"
                    >
                        <s-icon name="fail-reverse"></s-icon>
                    </s-tip>
                </div>
            </s-table>
            <s-table
                s-if="ruleIpType === 'ipv6'"
                style="overflow: visible"
                columns="{{table.columns}}"
                datasource="{{table.ipv6Datasource}}"
            >
                <div slot="empty">
                    <s-empty on-click="addRule" actionText="新增规则" />
                </div>
                <div slot="c-ethertype">
                    <s-select
                        width="90"
                        on-change="itemIpv6Change('ethertypes',rowIndex,$event)"
                        datasource="{{ethertypeIpv6List}}"
                        value="{=ethertypesIpv6[rowIndex]=}"
                    />
                </div>
                <div slot="c-protocol">
                    <s-select
                        width="140"
                        on-change="itemIpv6Change('protocols',rowIndex,$event)"
                        datasource="{{protocolListIpv6}}"
                        value="{=protocolsIpv6[rowIndex]=}"
                    />
                </div>
                <div slot="c-portRange">
                    <s-input
                        width="100"
                        disabled="{{portRangeDisableIpv6[rowIndex]}}"
                        on-input="itemIpv6Change('portRanges',rowIndex,$event)"
                        value="{=portRangesIpv6[rowIndex]=}"
                        placeholder="1-65535"
                    >
                    </s-input>
                    <p style="color: #EB5252" s-if="portRangeErrIpv6[rowIndex]">{{portRangeErrIpv6[rowIndex]}}</p>
                </div>
                <div slot="c-source">
                    <div>
                        <s-select
                            width="{{80}}"
                            on-change="itemIpv6Change('sources',rowIndex,$event)"
                            datasource="{{sourceList}}"
                            value="{=sourcesIpv6[rowIndex]=}"
                        />
                        <s-input
                            s-if="sourcesIpv6[rowIndex]==='user'"
                            on-input="itemIpv6Change('remoteIPs',rowIndex,$event)"
                            width="{{100}}"
                            value="{=remoteIPsIpv6[rowIndex]=}"
                            placeholder="all"
                        ></s-input>
                        <s-select
                            s-if="sourcesIpv6[rowIndex]==='system'"
                            on-change="itemIpv6Change('remoteGroupIds',rowIndex,$event)"
                            width="{{120}}"
                            datasource="{{securityGroupList}}"
                            value="{=remoteGroupIdsIpv6[rowIndex]=}"
                            filterable
                        />
                        <p style="color: #EB5252" s-if="remoteIPErrIpv6[rowIndex]">{{remoteIPErrIpv6[rowIndex]}}</p>
                    </div>
                </div>
                <div slot="c-name">
                    <s-input
                        width="100"
                        on-input="itemIpv6Change('names',rowIndex,$event)"
                        value="{=namesIpv6[rowIndex]=}"
                    >
                    </s-input>
                </div>
                <div slot="c-opt" class="operations">
                    <s-button s-if="!isEdit" skin="stringfy" on-click="deleteIpv6Rule(rowIndex)">删除</s-button>
                    <s-tip
                        s-if="{{statusMessage[rowIndex]}}"
                        content="{{statusMessage[rowIndex]}}"
                        position="tc"
                        inComponent
                        class="tip_reverse"
                    >
                        <s-icon name="fail-reverse"></s-icon>
                    </s-tip>
                </div>
            </s-table>
            <div style="padding:10px 0 0 10px" s-if="!isEdit">
                <s-tooltip trigger="{{quotaCheck.disable ? 'hover' : ''}}" placement="right">
                    <!--bca-disable-next-line-->
                    <div slot="content">{{quotaCheck.message | raw}}</div>
                    <s-button skin="primary" disabled="{{quotaCheck.disable}}" on-click="addRule">
                        <outlined-plus />新增规则
                    </s-button>
                </s-tooltip>
            </div>
            <div slot="footer">
                <s-button on-click="close">取消</s-button>
                <s-button
                    skin="primary"
                    disabled="{{disableSub || (!table.datasource.length && !table.ipv6Datasource.length)}}"
                    on-click="create"
                    >确定</s-button
                >
            </div>
        </s-dialog>
    </template>
`;
/* eslint-enable */
export default class SecurityCreateDialog extends Component {
    static template = tpl;
    initData() {
        return {
            title: '',
            content: '',
            open: true,
            ruleIpType: 'ipv4',
            table: {
                columns: [
                    {
                        name: 'ethertype',
                        label: '类型',
                        width: 90,
                        render(item) {
                            if (!item.edit) {
                                return u.escape(item.ethertype);
                            }
                            return item.ethertype;
                        }
                    },
                    {
                        name: 'protocol',
                        label: '协议',
                        width: 140,
                        render(item) {
                            if (!item.edit) {
                                if (item.protocol === 'all') {
                                    return '全部协议';
                                }
                                return u.escape(item.protocol);
                            }
                            return item.protocol;
                        }
                    },
                    {
                        name: 'portRange',
                        label: '端口',
                        width: 90,
                        render(item) {
                            if (!item.edit) {
                                return u.escape(item.portRange);
                            }
                            return item.portRange;
                        }
                    },
                    {
                        name: 'source',
                        label: '来源',
                        width: 90,
                        render(item) {
                            if (!item.edit) {
                                if (item.source === 'user') {
                                    return (
                                        (this.id === 'rulesInTable' ? '源IP：' : '目的IP：') +
                                        (item.remoteIP === 'all'
                                            ? item.ethertype === IpVersion.IPV4
                                                ? '0.0.0.0/0'
                                                : '::/0'
                                            : u.escape(item.remoteIP))
                                    );
                                }
                                let id = item.remoteGroupShortId || item.remoteGroupId;
                                return (
                                    '安全组：' + u.escape(item.remoteGroupName) + (id ? '（' + u.escape(id) + '）' : '')
                                );
                            }
                            return item.source;
                        }
                    },
                    {
                        name: 'name',
                        label: '备注',
                        width: 90,
                        render(item) {
                            if (!item.edit) {
                                return u.escape(item.name);
                            }
                            return item.name;
                        }
                    },
                    {
                        name: 'updatedTime',
                        label: '更新时间',
                        width: 120,
                        render(item) {
                            return utcToTime(item.updatedTime);
                        }
                    },
                    {
                        name: 'opt',
                        label: '操作',
                        width: 90
                    }
                ],
                datasource: [],
                ipv6Datasource: []
            },
            ethertypeList: IpVersion.toArray('IPV4'),
            ethertypeIpv6List: IpVersion.toArray('IPV6'),
            protocolList: [
                {text: '全部协议', value: 'all'},
                {text: 'tcp', value: 'tcp'},
                {text: 'udp', value: 'udp'},
                {text: 'icmp', value: 'icmp'},
                {text: 'tcp(HTTP)', value: 'tcp_http'},
                {text: 'tcp(HTTPS)', value: 'tcp_https'},
                {text: 'tcp(FTP)', value: 'tcp_ftp'},
                {text: 'tcp(RDP)', value: 'tcp_rdp'},
                {text: 'udp(DNS)', value: 'udp_dns'},
                {text: 'tcp(DNS)', value: 'tcp_dns'},
                {text: 'tcp(POP3)', value: 'tcp_pop3'},
                {text: 'tcp(MYSQL)', value: 'tcp_mysql'},
                {text: 'tcp(SQL SERVER)', value: 'tcp_sql_server'},
                {text: 'tcp(SNMP)', value: 'tcp_snmp'},
                {text: 'tcp(SMTP)', value: 'tcp_smtp'},
                {text: 'tcp(SNMP TRAP)', value: 'tcp_snmp_trap'},
                {text: 'tcp(SSH)', value: 'tcp_ssh'}
            ],
            protocolListIpv6: [
                {text: '全部协议', value: 'all'},
                {text: 'tcp', value: 'tcp'},
                {text: 'udp', value: 'udp'},
                {text: 'icmp', value: 'icmp'},
                {text: 'tcp(HTTP)', value: 'tcp_http'},
                {text: 'tcp(HTTPS)', value: 'tcp_https'},
                {text: 'tcp(FTP)', value: 'tcp_ftp'},
                {text: 'tcp(RDP)', value: 'tcp_rdp'},
                {text: 'udp(DNS)', value: 'udp_dns'},
                {text: 'tcp(DNS)', value: 'tcp_dns'},
                {text: 'tcp(POP3)', value: 'tcp_pop3'},
                {text: 'tcp(MYSQL)', value: 'tcp_mysql'},
                {text: 'tcp(SQL SERVER)', value: 'tcp_sql_server'},
                {text: 'tcp(SNMP)', value: 'tcp_snmp'},
                {text: 'tcp(SMTP)', value: 'tcp_smtp'},
                {text: 'tcp(SNMP TRAP)', value: 'tcp_snmp_trap'},
                {text: 'tcp(SSH)', value: 'tcp_ssh'}
            ],
            securityGroupList: [],
            ethertypes: [],
            protocols: [],
            portRanges: [],
            sources: [],
            remoteGroupIds: [],
            editRow: [],
            remoteIPs: [],
            names: [],
            portRangeDisable: [],
            portRangeErr: [],
            remoteIPErr: [],

            ethertypesIpv6: [],
            protocolsIpv6: [],
            portRangesIpv6: [],
            sourcesIpv6: [],
            remoteGroupIdsIpv6: [],
            editRowIpv6: [],
            remoteIPsIpv6: [],
            namesIpv6: [],
            portRangeDisableIpv6: [],
            portRangeErrIpv6: [],
            remoteIPErrIpv6: [],
            statusMessage: [],
            preIndex: -1,
            usedNestSecurityNum: 0,
            initUsedNestSecNum: 0
        };
    }
    static components = {
        's-dialog': Dialog,
        's-icon': Icon,
        's-button': Button,
        's-table': Table,
        's-input': Input,
        's-select': Select,
        's-radio-radio-group': Radio.RadioGroup,
        's-radio': Radio,
        's-tooltip': Tooltip,
        's-empty': Empty,
        's-tip': Tip
    };
    static computed = {
        sourceList() {
            let result = [
                {text: '源IP', value: 'user'},
                {text: '安全组', value: 'system'}
            ];
            if (this.data.get('ruleType') === 'out') {
                result[0].text = '目的IP';
            }
            return result;
        },
        quotaCheck() {
            const availableQuota = this.data.get('availableRuleQuota');
            const ruleIpType = this.data.get('ruleIpType');
            const ruleNum =
                ruleIpType === 'ipv4'
                    ? this.data.get('table.datasource').length
                    : this.data.get('table.ipv6Datasource').length;
            const disable = availableQuota <= ruleNum;
            const message = disable
                ? `安全组规则配额已达上限，<a href="/quota_center/#/quota/apply/create?serviceType=SECURITYGROUP&region=${window?.$context?.getCurrentRegionId()}&cloudCenterQuotaName=securityRulePerGroup" target="_blank">去申请配额</a>`
                : '';
            return {disable, message};
        }
    };
    inited() {
        this.getAllSecurityGroups();
        this.initUsedNestSecNum();
        this.data.splice('protocolList', [
            3,
            1,
            {
                text: 'icmp(PING_IPV4)',
                value: 'icmp_ipv4'
            }
        ]);
        this.data.splice('protocolListIpv6', [
            3,
            1,
            {
                text: 'icmp(PING_IPV6)',
                value: 'icmp_ipv6'
            }
        ]);
        this.watch('table.datasource', datas => {
            let editItems = ['protocol', 'portRange', 'source', 'ethertype', 'remoteGroupId', 'remoteIP', 'name', 'id'];
            datas.forEach((item, index) => {
                if (
                    !this.data.get(`portRangeDisable[${index}]`) &&
                    this.data.get(`portRangeDisable[${index}]`) !== false
                ) {
                    this.data.set(`portRangeDisable[${index}]`, false);
                }
                if (!this.data.get(`portRangeErr[${index}]`) && this.data.get(`portRangeErr[${index}]`) !== '') {
                    this.data.set(`portRangeErr[${index}]`, '');
                }
                if (!this.data.get(`remoteIPErr[${index}]`) && this.data.get(`remoteIPErr[${index}]`) !== '') {
                    this.data.set(`remoteIPErr[${index}]`, '');
                }
                editItems.forEach(editItem => {
                    if (editItem === 'protocol') {
                        let protocolItem = this.data.get(`${editItem}s[${index}]`) || item[editItem];
                        if (protocolItem === 'icmp' || protocolItem === 'all') {
                            this.data.set(`portRangeDisable[${index}]`, true);
                        }
                    }
                    if (!this.data.get(`${editItem}s[${index}]`)) {
                        this.data.set(`${editItem}s[${index}]`, item[editItem]);
                    }
                });
            });
        });

        this.watch('table.ipv6Datasource', datas => {
            let editItems = ['protocol', 'portRange', 'source', 'ethertype', 'remoteGroupId', 'remoteIP', 'name', 'id'];
            datas.forEach((item, index) => {
                if (
                    !this.data.get(`portRangeDisableIpv6[${index}]`) &&
                    this.data.get(`portRangeDisableIpv6[${index}]`) !== false
                ) {
                    this.data.set(`portRangeDisableIpv6[${index}]`, false);
                }
                if (
                    !this.data.get(`portRangeErrIpv6[${index}]`) &&
                    this.data.get(`portRangeErrIpv6[${index}]`) !== ''
                ) {
                    this.data.set(`portRangeErrIpv6[${index}]`, '');
                }
                if (!this.data.get(`remoteIPErrIpv6[${index}]`) && this.data.get(`remoteIPErrIpv6[${index}]`) !== '') {
                    this.data.set(`remoteIPErrIpv6[${index}]`, '');
                }
                editItems.forEach(editItem => {
                    if (editItem === 'protocol') {
                        let protocolItem = this.data.get(`${editItem}sIpv6[${index}]`) || item[editItem];
                        if (protocolItem === 'icmp' || protocolItem === 'all') {
                            this.data.set(`portRangeDisableIpv6[${index}]`, true);
                        }
                    }
                    if (!this.data.get(`${editItem}sIpv6[${index}]`)) {
                        this.data.set(`${editItem}sIpv6[${index}]`, item[editItem]);
                    }
                });
            });
        });
    }
    initUsedNestSecNum() {
        this.data.set('initUsedNestSecNum', this.data.get('usedNestSecurityNum')); // 存一下已使用安全组数量
        this.data.set('usedNestSecurityNum', this.data.get('usedNestSecurityNum'));
    }
    dialogConfirm() {
        this.fire('confirm');
        this.data.set('open', false);
    }
    close() {
        this.fire('close');
        this.data.set('open', false);
    }
    addRule() {
        let ruleIpType = this.data.get('ruleIpType');
        if (ruleIpType === 'ipv4') {
            this.data.push('table.datasource', {
                ethertype: IpVersion.IPV4,
                source: 'user',
                name: '全部协议',
                protocol: 'all',
                remoteIP: 'all',
                portRange: '1-65535',
                action: 'allow',
                ruleIpType: 'ipv4'
            });
        } else {
            this.data.push('table.ipv6Datasource', {
                ethertype: IpVersion.IPV6,
                source: 'user',
                name: '全部协议',
                protocol: 'all',
                remoteIP: 'all',
                portRange: '1-65535',
                action: 'allow',
                ruleIpType: 'ipv4'
            });
        }
    }
    deleteRule(index) {
        this.data.set(`statusMessage[${index}]`, '');
        let editItem = ['protocol', 'portRange', 'source', 'ethertype', 'remoteGroupId', 'remoteIP', 'name', 'id']; //eslint-disable-line
        this.data.splice('table.datasource', [index, 1]);
        editItem.forEach(item => {
            this.data.splice(`${item}s`, [index, 1]);
        });
        this.data.set(`portRangeDisable[${index}]`, false);
        this.data.set(`portRangeErr[${index}]`, '');
        this.data.set(`remoteIPErr[${index}]`, '');
    }
    deleteIpv6Rule(index) {
        this.data.set(`statusMessage[${index}]`, '');
        let editItem = ['protocol', 'portRange', 'source', 'ethertype', 'remoteGroupId', 'remoteIP', 'name', 'id']; //eslint-disable-line
        this.data.splice('table.ipv6Datasource', [index, 1]);
        editItem.forEach(item => {
            this.data.splice(`${item}sIpv6`, [index, 1]);
        });
        this.data.set(`portRangeDisableIpv6[${index}]`, false);
        this.data.set(`portRangeErrIpv6[${index}]`, '');
        this.data.set(`remoteIPErrIpv6[${index}]`, '');
    }
    getNestSecurityNum(): number {
        const ruleIpType = this.data.get('ruleIpType');
        const remoteGroupIds =
            ruleIpType === 'ipv4' ? this.data.get('remoteGroupIds') : this.data.get('remoteGroupIdsIpv6');
        return remoteGroupIds.length;
    }
    itemChange(type, index, e) {
        this.data.set(`statusMessage[${index}]`, '');
        if (type === 'protocols') {
            this.protocolChangeMap(e.value, index);
        }
        if (type === 'portRanges') {
            if (!e.value) {
                this.data.set(`portRangeErr[${index}]`, '端口范围必填');
            } else if (!portRange(e.value)) {
                this.data.set(`portRangeErr[${index}]`, '端口范围不符合规则');
            } else {
                this.data.set(`portRangeErr[${index}]`, '');
            }
        }
        if (type === 'remoteIPs') {
            let errMsg = checkIp(e.value, 'IPv4');
            if (!e.value) {
                this.data.set(`remoteIPErr[${index}]`, 'IP范围必填');
            } else if (errMsg) {
                this.data.set(`remoteIPErr[${index}]`, errMsg);
            } else {
                this.data.set(`remoteIPErr[${index}]`, '');
            }
        }
        if (type === 'sources') {
            const usedNestSecurityNum = this.data.get('usedNestSecurityNum');
            const totalNestSecurityQuota = this.data.get('totalNestSecurityQuota');
            const preIndex = this.data.get('preIndex');
            if (e.value === 'system') {
                if (usedNestSecurityNum >= totalNestSecurityQuota) {
                    this.data.set(
                        `remoteIPErr[${index}]`,
                        `入站和出站规则最多支持${totalNestSecurityQuota}个嵌套安全组`
                    );
                } else {
                    this.data.set(`remoteIPErr[${index}]`, '');
                    this.data.set(`remoteIPs[${index}]`, '');
                    this.data.set('usedNestSecurityNum', usedNestSecurityNum + 1);
                }
                this.data.set(`remoteGroupIds[${index}]`, this.data.get('securityGroupList')[0].value);
            } else {
                this.data.set(`remoteIPErr[${preIndex}]`, '');
                this.data.set(`remoteGroupIds[${index}]`, '');
                if (!usedNestSecurityNum === totalNestSecurityQuota) {
                    this.data.set('usedNestSecurityNum', usedNestSecurityNum - 1);
                }
                this.itemChange('remoteIPs', index, {value: this.data.get('remoteIPs')[index]});
            }
            this.data.set('preIndex', index);
        }
        // 切换类型重置
        if (type === 'ethertypes') {
            this.data.set(`portRangeErr[${index}]`, '');
            this.data.set(`remoteIPErr[${index}]`, '');
        }
        this.data.set(`${type}[${index}]`, e.value);
    }
    protocolChangeMap(val, index) {
        if (val === 'all') {
            this.data.set(`portRanges[${index}]`, '1-65535');
            this.data.set(`remarks[${index}]`, '全部协议');
            this.data.set(`protocols[${index}]`, val);
            this.data.set(`portRangeDisable[${index}]`, true);
        } else if (val === 'icmp') {
            this.data.set(`portRanges[${index}]`, '不涉及');
            this.data.set(`remarks[${index}]`, '');
            this.data.set(`protocols[${index}]`, val);
            this.data.set(`portRangeDisable[${index}]`, true);
        } else if (val === 'tcp' || val === 'udp') {
            this.data.set(`portRangeDisable[${index}]`, false);
            this.data.set(`remarks[${index}]`, '');
            this.data.set(`protocols[${index}]`, val);
            this.data.set(`portRanges[${index}]`, '1-65535');
        } else {
            let protocolItem = kCommonServices.find(item => item.val === val);
            let protocolRule = protocolItem?.rules[0];
            let items = ['portRange', 'name'];
            // icmp端口范围禁用并更新对应的ip类型
            if (val === 'icmp_ipv4' || val === 'icmp_ipv6') {
                this.data.set(`ethertypes[${index}]`, protocolRule.ethertype);
                this.data.set(`portRangeDisable[${index}]`, true);
            } else {
                this.data.set(`portRangeDisable[${index}]`, false);
            }
            items.forEach(item => {
                this.data.set(`${item}s[${index}]`, protocolRule[item]);
            });
        }
    }
    getAllSecurityGroups() {
        let param = {
            pageNo: 1,
            pageSize: 1000
        };
        if (this.data.get('vpcId')) {
            param.vpcId = this.data.get('vpcId');
        }
        return this.$http.securityListV3(param).then(sgs => {
            let result = [];
            u.each(sgs.result, group => {
                result[group.name === '默认安全组' ? 'unshift' : 'push']({
                    text: group.name,
                    value: group.securityGroupId,
                    securityGroupId: group.securityGroupId
                });
                this.data.set(`securityGroupsIdNameMap['${group.securityGroupId}']`, group.name);
            });
            this.data.set('securityGroupList', result);
        });
    }
    reset() {
        this.data.set('ethertypes', []);
        this.data.set('protocols', []);
        this.data.set('portRanges', []);
        this.data.set('sources', []);
        this.data.set('remoteGroupIds', []);
        this.data.set('remoteIPs', []);
        this.data.set('names', []);
        this.data.set('ids', []);
        this.data.set('statusMessage', []);
    }
    create() {
        let datasource = [];
        let originData = [];
        if (this.data.get('ruleIpType') === 'ipv4') {
            if (this.data.get('portRangeErr').findIndex(item => item) > -1) {
                return;
            }
            if (this.data.get('remoteIPErr').findIndex(item => item) > -1) {
                return;
            }
        } else {
            if (this.data.get('portRangeErrIpv6').findIndex(item => item) > -1) {
                return;
            }
            if (this.data.get('remoteIPErrIpv6').findIndex(item => item) > -1) {
                return;
            }
        }
        if (this.data.get('ruleIpType') === 'ipv4') {
            for (let index = 0; index < this.data.get('ethertypes').length; index++) {
                let editItems = ['protocol', 'portRange', 'source', 'remoteGroupId', 'remoteIP', 'name', 'id'];
                let rule = {};
                editItems.forEach(editItem => {
                    if (editItem === 'remoteIP') {
                        let remoteIP = this.data.get(`${editItem}s[${index}]`).trim();
                        rule[editItem] = remoteIP;
                    } else {
                        rule[editItem] = this.data.get(`${editItem}s[${index}]`);
                    }
                });
                // 一些快捷模板类型协议传值修改为本身的协议
                rule.protocol = this.data.get(`protocols[${index}]`).split('_')[0];
                rule.ethertype = this.data.get(`ethertypes[${index}]`);
                this.data.get('securityGroupList').forEach(item => {
                    if (item.value === rule.remoteGroupId) {
                        rule.remoteGroupShortId = item.title;
                    }
                });

                if (rule.remoteGroupId) {
                    rule.remoteGroupName = this.data.get('securityGroupsIdNameMap')[rule.remoteGroupId];
                }
                rule = Rule.fromJSON(rule);

                if (rule.protocol === 'icmp') {
                    rule.portRange = '';
                }
                datasource.push(rule);
                originData.push(rule);
            }
        } else {
            for (let index = 0; index < this.data.get('ethertypesIpv6').length; index++) {
                let editItems = ['protocol', 'portRange', 'source', 'remoteGroupId', 'remoteIP', 'name', 'id'];
                let rule = {};
                editItems.forEach(editItem => {
                    if (editItem === 'remoteIP') {
                        let remoteIP = this.data.get(`${editItem}sIpv6[${index}]`).trim();
                        rule[editItem] = remoteIP;
                    } else {
                        rule[editItem] = this.data.get(`${editItem}sIpv6[${index}]`);
                    }
                });
                // 一些快捷模板类型协议传值修改为本身的协议
                rule.protocol = this.data.get(`protocolsIpv6[${index}]`).split('_')[0];
                rule.ethertype = this.data.get(`ethertypesIpv6[${index}]`);
                this.data.get('securityGroupList').forEach(item => {
                    if (item.value === rule.remoteGroupId) {
                        rule.remoteGroupShortId = item.title;
                    }
                });

                if (rule.remoteGroupId) {
                    rule.remoteGroupName = this.data.get('securityGroupsIdNameMap')[rule.remoteGroupId];
                }
                rule = Rule.fromJSON(rule);

                if (rule.protocol === 'icmp') {
                    rule.portRange = '';
                }
                datasource.push(rule);
            }
        }

        let ruleType = this.data.get('ruleType');
        let array = [];
        ruleType === 'in' ? (array = this.data.get('ingressRules')) : (array = this.data.get('egressRules'));
        // 先保证填写的与外部的不会重复
        datasource.forEach((item, i) => {
            // 添加的数据已存在于表格中，提示用户
            if (this.ruleIndexOfTable(item, array, -1) !== -1) {
                this.data.set(`statusMessage[${i}]`, '规则您已添加，请勿重复添加！');
            }
        });

        // 再保证填写的不会重复
        originData.forEach((item, i) => {
            if (this.ruleIndexOfTable(item, datasource, i) !== -1 && i !== 0 && !this.data.get(`statusMessage[${i}]`)) {
                this.data.set(`statusMessage[${i}]`, '规则您已添加，请勿重复添加！');
            }
        });
        let statusMessageArray = this.data.get('statusMessage') || [];
        if (statusMessageArray.find(item => item)) {
            return;
        }

        this.createClient(datasource);
    }

    itemIpv6Change(type, index, e) {
        this.data.set(`statusMessage[${index}]`, '');
        if (type === 'protocols') {
            this.protocolChangeMapIpv6(e.value, index);
        }
        if (type === 'portRanges') {
            if (!e.value) {
                this.data.set(`portRangeErrIpv6[${index}]`, '端口范围必填');
            } else if (!portRange(e.value)) {
                this.data.set(`portRangeErrIpv6[${index}]`, '端口范围不符合规则');
            } else {
                this.data.set(`portRangeErrIpv6[${index}]`, '');
            }
        }
        if (type === 'remoteIPs') {
            let errMsg = checkIp(e.value, 'IPv6');
            if (!e.value) {
                this.data.set(`remoteIPErrIpv6[${index}]`, 'IP范围必填');
            } else if (errMsg) {
                this.data.set(`remoteIPErrIpv6[${index}]`, errMsg);
            } else {
                this.data.set(`remoteIPErrIpv6[${index}]`, '');
            }
        }
        if (type === 'sources') {
            const usedNestSecurityNum = this.data.get('usedNestSecurityNum');
            const totalNestSecurityQuota = this.data.get('totalNestSecurityQuota');
            const preIndex = this.data.get('preIndex');
            if (e.value === 'system') {
                if (usedNestSecurityNum >= totalNestSecurityQuota) {
                    this.data.set(
                        `remoteIPErrIpv6[${index}]`,
                        `入站和出站规则最多支持${totalNestSecurityQuota}个嵌套安全组`
                    );
                } else {
                    this.data.set(`remoteIPErrIpv6[${index}]`, '');
                    this.data.set(`remoteIPsIpv6[${index}]`, '');
                    this.data.set('usedNestSecurityNum', usedNestSecurityNum + 1);
                }
                this.data.set(`remoteGroupIdsIpv6[${index}]`, this.data.get('securityGroupList')[0].value);
            } else {
                this.data.set(`remoteIPErrIpv6[${preIndex}]`, '');
                this.data.set(`remoteGroupIdsIpv6[${index}]`, '');
                if (!usedNestSecurityNum === totalNestSecurityQuota) {
                    this.data.set('usedNestSecurityNum', usedNestSecurityNum - 1);
                }
            }
            this.data.set('preIndex', index);
        }
        // 切换类型重置
        if (type === 'ethertypes') {
            this.data.set(`portRangeErrIpv6[${index}]`, '');
            this.data.set(`remoteIPErrIpv6[${index}]`, '');
        }
        this.data.set(`${type}Ipv6[${index}]`, e.value);
    }

    protocolChangeMapIpv6(val, index) {
        if (val === 'all') {
            this.data.set(`portRangesIpv6[${index}]`, '1-65535');
            this.data.set(`remarksIpv6[${index}]`, '全部协议');
            this.data.set(`protocolsIpv6[${index}]`, val);
            this.data.set(`portRangeDisableIpv6[${index}]`, true);
        } else if (val === 'icmp') {
            this.data.set(`portRangesIpv6[${index}]`, '不涉及');
            this.data.set(`remarksIpv6[${index}]`, '');
            this.data.set(`protocolsIpv6[${index}]`, val);
            this.data.set(`portRangeDisableIpv6[${index}]`, true);
        } else if (val === 'tcp' || val === 'udp') {
            this.data.set(`portRangeDisableIpv6[${index}]`, false);
            this.data.set(`remarksIpv6[${index}]`, '');
            this.data.set(`protocolsIpv6[${index}]`, val);
            this.data.set(`portRangesIpv6[${index}]`, '1-65535');
        } else {
            let protocolItem = kCommonServices.find(item => item.val === val);
            let protocolRule = protocolItem?.rules[0];
            let items = ['portRange', 'name'];
            // icmp端口范围禁用并更新对应的ip类型
            if (val === 'icmp_ipv4' || val === 'icmp_ipv6') {
                this.data.set(`ethertypesIpv6[${index}]`, protocolRule.ethertype);
                this.data.set(`portRangeDisableIpv6[${index}]`, true);
            } else {
                this.data.set(`portRangeDisableIpv6[${index}]`, false);
            }
            items.forEach(item => {
                this.data.set(`${item}sIpv6[${index}]`, protocolRule[item]);
            });
        }
    }
    resetIpv6() {
        this.data.set('ethertypesIpv6', []);
        this.data.set('protocolsIpv6', []);
        this.data.set('portRangesIpv6', []);
        this.data.set('sourcesIpv6', []);
        this.data.set('remoteGroupIdsIpv6', []);
        this.data.set('remoteIPsIpv6', []);
        this.data.set('namesIpv6', []);
        this.data.set('idsIpv6', []);
        this.data.set('statusMessage', []);
    }
    ruleIpChange() {
        this.reset();
        this.resetIpv6();
        this.data.set('table.datasource', []);
        this.data.set(`portRangeDisable`, []);
        this.data.set(`portRangeErr`, []);
        this.data.set(`remoteIPErr`, []);
        this.data.set('table.ipv6Datasource', []);
        this.data.set(`portRangeDisableIpv6`, []);
        this.data.set(`portRangeErrIpv6`, []);
        this.data.set(`remoteIPErrIpv6`, []);
        this.data.set('preIndex', -1);
        this.data.set('usedNestSecurityNum', this.data.get('initUsedNestSecNum'));
    }
    getTip() {
        let security = this.data.get('instance') || {};
        let content = '';
        if (security.bindInstance) {
            content =
                '当前已经启用这一安全组规则，您的任何修改将在保存后立即生效。' +
                '请确认您所设置的安全规则对当前云服务器的正常服务无任何影响！';
        }
        return content;
    }
    ruleIndexOfTable(rule, datasource, ruleIndex) {
        let index = -1;
        u.find(datasource, function (x, i) {
            if (i !== ruleIndex && rule.equal(x)) {
                index = i;
                return true;
            }
        });
        return index;
    }
    createClient(rules) {
        let reqRule = [];
        rules.map(rule => {
            if (rule.protocol === 'icmp') {
                rule.portRange = '';
            }
            reqRule.push({
                direction: this.data.get('ruleType') === 'in' ? 'ingress' : 'egress',
                remoteIpPrefix: u.trim(rule.remoteIP),
                ethertype: rule.ethertype,
                portRange: u.trim(rule.portRange),
                protocol: rule.protocol,
                name: rule.name,
                remoteGroupId: rule.remoteGroupId
            });
        });
        const callback = () => {
            return this.$http.securityAddRule({
                securityGroupId: this.data.get('id'),
                rules: reqRule
            });
        };
        this.submitEditRule(callback);
    }
    submitEditRule(callback) {
        let content = this.getTip();
        const ruleList = this.data.get('ingressRules');
        const selectedIndex = this.data.get('selectedIndex') || [];
        if (
            !content &&
            this.data.get('ruleType') === 'in' &&
            ruleList.length !== 0 &&
            ruleList.length === selectedIndex.length
        ) {
            content =
                '您未添加任何入站开放端口，将使该云服务器无法和外部进行任何通信，您仅能通过VNC功能进行管理。是否确认继续操作？';
        }
        if (content) {
            let confirm = new Confirm({
                data: {
                    open: true,
                    content
                }
            });
            confirm.attach(document.body);
            confirm.on('confirm', () => {
                callback().then(() => {
                    this.fire('create');
                    this.data.set('open', false);
                });
            });
            confirm.on('close', () => {});
        } else {
            this.data.set('disableSub', true);
            callback()
                .then(() => {
                    this.data.set('disableSub', false);
                    this.fire('create');
                    this.data.set('open', false);
                })
                .catch(e => {
                    this.data.set('disableSub', false);
                });
        }
    }
}
