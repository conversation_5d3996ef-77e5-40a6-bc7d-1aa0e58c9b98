/*
 * @description: 安全组规则复制
 * @file: security/pages/copyRule.js
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import {SecurityIpVersion as IpVersion} from '@/pages/sanPages/common/enum';
import {Form, Input, Dialog, Select, Button, Icon, Tooltip, Alert, Notification} from '@baidu/sui';

import {checkIpv6Cidr, convertCidrToBinary} from '@/pages/sanPages/utils/common';
import kCommonServices from '../util/services';
import {kXhrOptions, $flag as FLAG} from '@/pages/sanPages/utils/helper';
import regs from '@/pages/sanPages/utils/rule';
import Rule from '../util/rule';

function isPort(v) {
    return /^[1-9][0-9]{0,4}/.test(v) && v <= 65535;
}
function portRange(value) {
    value = u.trim(value);
    if (value.toLowerCase() === 'all') {
        return true;
    }
    if (value.indexOf('-') === -1) {
        return isPort(value);
    }
    let array = value.split('-');
    return isPort(array[0]) && isPort(array[1]) && +array[0] < +array[1];
}
function checkIp(value, ipVersion) {
    value = u.trim(value);
    if (value.toLowerCase() === 'all') {
        return '';
    }
    if (ipVersion === 'IPv6') {
        if (!checkIpv6Cidr(value)) {
            let valueString = convertCidrToBinary(value);
            let valueMask = value.split('/')[1];
            // 掩码分割的前部分
            let preValueString = valueString.substring(0, +valueMask);
            // 掩码分割的前后部分
            let tailValueString = valueString.substring(+valueMask, valueString.length);
            if (valueMask && tailValueString.indexOf('1') > -1) {
                let addLen = 128 - preValueString.length;
                let fixIpBinary = preValueString + new Array(addLen).fill('0').join('');
                // 每隔16位按:分割
                let ipBinaryArr = fixIpBinary
                    .replace(/(.{16})/g, '$1:')
                    .substring(0, 128)
                    .split(':');
                let ipArr = ipBinaryArr.map(binary => {
                    // 先转为10进制，再转为16进制
                    return parseInt(binary, 2).toString(16);
                });
                let fixIp = ipArr.join(':') + '/' + valueMask;
                // 连续:0替换::
                let replaceIp = fixIp.replace(/(:0){2,}/g, '::');
                return `网段与掩码不匹配，建议改为${replaceIp}`;
            }
            return 'IP格式不正确';
        }
    } else {
        var reg = new RegExp(regs.IP_CIDR);
        if (!reg.test(value)) {
            return 'IP格式不正确';
        }
        let valueString = convertCidrToBinary(value);
        let valueMask = value.split('/')[1];
        // 掩码分割的前部分
        let preValueString = valueString.substring(0, +valueMask);
        // 掩码分割的前后部分
        let tailValueString = valueString.substring(+valueMask, valueString.length);
        if (valueMask && tailValueString.indexOf('1') > -1) {
            let addLen = 32 - preValueString.length;
            let fixIpBinary = preValueString + new Array(addLen).fill('0').join('');
            // 每隔8位按.分割
            let ipBinaryArr = fixIpBinary
                .replace(/(.{8})/g, '$1.')
                .substring(0, 32)
                .split('.');
            let ipArr = ipBinaryArr.map(binary => parseInt(binary, 2));
            let fixIp = ipArr.join('.') + '/' + valueMask;
            return `网段与掩码不匹配，建议改为${fixIp}`;
        }
    }
    return '';
}

const {service} = decorators;
const tpl = html`
<template>
<s-dialog
    class="security-copy"
    open="{=open=}"
    width="550"
    title="{{'复制规则'}}">
    <s-form s-ref="form"
        data="{=formData=}"
        rules="{{rules}}"
        label-col="{{labelCol}}"
        wrapper-col="{{wrapperCol}}"
        label-align="left">
        <s-form-item prop="ethertype" label="{{'类型：'}}">
            <s-select
                width="320"
                datasource="{{ethertypeList}}"
                on-change="itemChange('ethertypes', $event)"
                value="{=formData.ethertype=}">
        </s-form-item>
        <s-form-item prop="protocol" label="{{'协议：'}}">
            <s-select
                width="320"
                on-change="itemChange('protocols', $event)"
                datasource="{{protocolList}}"
                value="{=formData.protocol=}">
        </s-form-item>
        <s-form-item prop="portRange" label="{{'端口：'}}">
            <s-input
                width="320"
                value="{=formData.portRange=}"
                on-input="itemChange('portRanges', $event)"
                disabled="{{formData.portRangeDisable}}"
                placeholder="1-65535">
            </s-input>
            <p style="color: #EB5252" s-if="formData.portRangeErr">{{formData.portRangeErr}}</p>
        </s-form-item>
        <s-form-item prop="remoteIP" label="{{'来源：'}}">
            <s-select
                width="{{80}}"
                datasource="{{sourceList}}"
                on-change="itemChange('sources', $event)"
                value="{=formData.source=}"/>
                <s-input
                    s-if="formData.source === 'user'"
                    width="{{220}}"
                    on-input="itemChange('remoteIPs', $event)"
                    value="{=formData.remoteIP=}"
                    placeholder="all"></s-input>
                <s-select
                    s-if="formData.source === 'system'"
                    width="{{220}}"
                    value="{=formData.remoteGroupId=}"
                    filterable>
                    <s-select-option
                      s-for="item in securityGroupList"
                      value="{{item.value}}"
                      label="{{item.text}}">
                        <s-tooltip placement="right">
                          <div slot="content">
                            {{item.text}}
                          </div>
                          <div>{{item.text}}</div>
                        </s-tooltip>
                    </s-select-option>
                </s-select>
                <p style="color: #EB5252" class="remote-error-class" s-if="remoteIPErr">{{remoteIPErr}}</p>
        </s-form-item>
        <s-form-item label="{{'备注：'}}" prop="name">
            <s-textarea
                maxLength="200"
                on-input="itemChange('names', $event)"
                width="320"
                height="60"
                value="{=formData.name=}"
                placeholder="{{'请输入备注'}}"
            ></s-textarea>
        </s-form-item>
    </s-form>
    <div slot="footer">
        <s-button on-click="close">{{'取消'}}</s-button>
        <s-tooltip
            trigger="{{create.disable ? 'hover' : ''}}" placement="top">
            <!--bca-disable-next-line-->
            <div slot="content">{{create.message | raw}}</div>
            <s-button
                skin="primary"
                disabled="{{create.disable || secLoading}}"
                on-click="create">
                {{'确定'}}
            </s-button>
        </s-tooltip>
    </div>
</s-dialog>
</template>
`;
export default class VpcInstanceIndex extends Component {
    static template = tpl;
    static computed = {
        sourceList() {
            var result = [
                {text: '源IP', value: 'user'},
                {text: '安全组', value: 'system'}
            ];
            if (this.data.get('ruleType') === 'out') {
                result[0].text = '目的IP';
            }
            return result;
        }
    };
    static components = {
        's-form': Form,
        's-form-item': Form.Item,
        's-input': Input,
        's-dialog': Dialog,
        's-button': Button,
        's-select': Select,
        's-tooltip': Tooltip,
        's-textarea': Input.TextArea,
        's-select-option': Select.Option,
        's-icon': Icon,
        's-alert': Alert
    };
    initData() {
        let labelCol = {span: 4};
        let wrapperCol = {span: 19};
        if (location.search.indexOf('locale=en') > -1) {
            labelCol = {span: 6};
            wrapperCol = {span: 17};
        }
        return {
            FLAG,
            open: false,
            labelCol,
            wrapperCol,
            formData: {},
            rules: {
                protocol: [{required: true, message: '请选择协议'}]
            },
            ethertypeList: IpVersion.toArray(),
            protocolList: [
                {text: '全部协议', value: 'all'},
                {text: 'tcp', value: 'tcp'},
                {text: 'udp', value: 'udp'},
                {text: 'icmp', value: 'icmp'},
                {text: 'tcp(HTTP)', value: 'tcp_http'},
                {text: 'tcp(HTTPS)', value: 'tcp_https'},
                {text: 'tcp(FTP)', value: 'tcp_ftp'},
                {text: 'tcp(RDP)', value: 'tcp_rdp'},
                {text: 'udp(DNS)', value: 'udp_dns'},
                {text: 'tcp(DNS)', value: 'tcp_dns'},
                {text: 'tcp(POP3)', value: 'tcp_pop3'},
                {text: 'tcp(MYSQL)', value: 'tcp_mysql'},
                {text: 'tcp(SQL SERVER)', value: 'tcp_sql_server'},
                {text: 'tcp(SNMP)', value: 'tcp_snmp'},
                {text: 'tcp(SMTP)', value: 'tcp_smtp'},
                {text: 'tcp(SNMP TRAP)', value: 'tcp_snmp_trap'},
                {text: 'tcp(SSH)', value: 'tcp_ssh'}
            ],
            create: {}
        };
    }
    inited() {
        this.data.set('secLoading', true);
        this.$http
            .getSecurityRules(this.data.get('id'), {pageNo: 1, pageSize: 100000}, kXhrOptions.customSilent)
            .then(res => {
                this.data.set('originDatasource', res.result || []);
                this.data.set('secLoading', false);
            })
            .catch(e => {
                this.data.set('secLoading', false);
            });
        if (this.data.get('formData').ethertype === 'IPv4') {
            this.data.splice('protocolList', [
                3,
                1,
                {
                    text: 'icmp(PING_IPV4)',
                    value: 'icmp_ipv4'
                }
            ]);
        } else {
            this.data.splice('protocolList', [
                3,
                1,
                {
                    text: 'icmp(PING_IPV6)',
                    value: 'icmp_ipv6'
                }
            ]);
        }
        this.itemChange('protocols', {value: this.data.get('formData').protocol});
    }

    close() {
        this.data.set('open', false);
    }

    async create() {
        await this.ref('form').validateFields();
        let formData = this.data.get('formData');
        if (formData.portRangeErr) {
            return;
        }
        let errMsg = checkIp(formData.remoteIP, formData.ethertype);
        if (!formData.remoteIP) {
            this.data.set('remoteIPErr', 'IP范围必填');
            return;
        } else if (errMsg) {
            this.data.set('remoteIPErr', errMsg);
            return;
        } else {
            this.data.set('remoteIPErr', '');
        }

        let ruleType = this.data.get('ruleType');
        let originDatasource =
            ruleType === 'in'
                ? this.data.get('originDatasource').filter(item => item.direction === 'ingress')
                : this.data.get('originDatasource').filter(item => item.direction === 'egress');
        let rule = {
            name: formData.name,
            ethertype: formData.ethertype,
            protocol: formData.protocol.split('_')[0],
            portRange: formData.portRange,
            remoteGroupId: formData.remoteGroupId,
            source: formData.source,
            remoteIP: formData.remoteIP?.trim()
        };
        this.data.get('securityGroupList').forEach(item => {
            if (item.value === rule.remoteGroupId) {
                rule.remoteGroupShortId = item.title;
            }
        });

        if (rule.remoteGroupId) {
            rule.remoteGroupName = this.data.get('securityGroupsIdNameMap')[rule.remoteGroupId];
        }
        rule = Rule.fromJSON(rule);

        if (rule.protocol === 'icmp') {
            rule.portRange = '';
        }
        let rules = [rule];
        let array = [];
        originDatasource.forEach(item => {
            let newItem = Rule.fromJSON(item);
            array.push(newItem);
        });
        // 添加的数据已存在于表格中，提示用户
        if (this.ruleIndexOfTable(rule, array, -1) !== -1) {
            Notification.error('该条规则您已添加，请勿重复添加！');
            return;
        }

        let reqRule = [];
        rules.map(rule => {
            if (rule.protocol === 'icmp') {
                rule.portRange = '';
            }
            reqRule.push({
                direction: this.data.get('ruleType') === 'in' ? 'ingress' : 'egress',
                remoteIpPrefix: u.trim(rule.remoteIP),
                ethertype: rule.ethertype,
                portRange: rule.portRange,
                protocol: rule.protocol,
                name: rule.name,
                remoteGroupId: rule.remoteGroupId
            });
        });
        this.$http
            .securityAddRule({
                securityGroupId: this.data.get('id'),
                rules: reqRule
            })
            .then(result => {
                this.data.set('open', false);
                this.fire('success');
            });
    }
    itemChange(type, e) {
        if (type === 'protocols') {
            this.protocolChangeMap(e.value);
        }
        if (type === 'portRanges') {
            if (!e.value) {
                this.data.set('formData.portRangeErr', '端口范围必填');
            } else if (!portRange(e.value)) {
                this.data.set('formData.portRangeErr', '端口范围不符合规则');
            } else {
                this.data.set('formData.portRangeErr', '');
            }
        }
        if (type === 'remoteIPs') {
            let errMsg = checkIp(e.value, this.data.get('formData.ethertype'));
            if (!e.value) {
                this.data.set('remoteIPErr', 'IP范围必填');
            } else if (errMsg) {
                this.data.set('remoteIPErr', errMsg);
            } else {
                this.data.set('remoteIPErr', '');
            }
        }
        if (type === 'ethertypes') {
            this.data.set('formData.protocol', '');
            let list = [
                {text: '全部协议', value: 'all'},
                {text: 'tcp', value: 'tcp'},
                {text: 'udp', value: 'udp'},
                {text: 'icmp', value: 'icmp'},
                {text: 'tcp(HTTP)', value: 'tcp_http'},
                {text: 'tcp(HTTPS)', value: 'tcp_https'},
                {text: 'tcp(FTP)', value: 'tcp_ftp'},
                {text: 'tcp(RDP)', value: 'tcp_rdp'},
                {text: 'udp(DNS)', value: 'udp_dns'},
                {text: 'tcp(DNS)', value: 'tcp_dns'},
                {text: 'tcp(POP3)', value: 'tcp_pop3'},
                {text: 'tcp(MYSQL)', value: 'tcp_mysql'},
                {text: 'tcp(SQL SERVER)', value: 'tcp_sql_server'},
                {text: 'tcp(SNMP)', value: 'tcp_snmp'},
                {text: 'tcp(SMTP)', value: 'tcp_smtp'},
                {text: 'tcp(SNMP TRAP)', value: 'tcp_snmp_trap'}
            ];
            if (e.value === 'IPv4') {
                list.splice(3, 1, {
                    text: 'icmp(PING_IPV4)',
                    value: 'icmp_ipv4'
                });
            } else {
                list.splice(3, 1, {
                    text: 'icmp(PING_IPV6)',
                    value: 'icmp_ipv6'
                });
            }
            this.data.set('protocolList', list);
        }
    }
    protocolChangeMap(val) {
        if (val === 'all') {
            this.data.set('formData.portRange', '1-65535');
            this.data.set('formData.name', '全部协议');
            this.data.set('formData.protocol', val);
            this.data.set('formData.portRangeDisable', true);
        } else if (val === 'icmp') {
            this.data.set('formData.portRange', '不涉及');
            this.data.set('formData.name', '');
            this.data.set('formData.protocol', val);
            this.data.set('formData.portRangeDisable', true);
        } else if (val === 'tcp' || val === 'udp') {
            this.data.set('formData.portRangeDisable', false);
            this.data.set('formData.name', '');
            this.data.set('formData.protocol', val);
            this.data.set('formData.portRange', '1-65535');
        } else {
            let protocolItem = kCommonServices.find(item => item.val === val);
            let protocolRule = protocolItem?.rules[0];
            let items = ['portRange', 'name'];
            // icmp端口范围禁用并更新对应的ip类型
            if (val === 'icmp_ipv4' || val === 'icmp_ipv6') {
                this.data.set('formData.ethertype', protocolRule.ethertype);
                this.data.set('formData.portRangeDisable', true);
            } else {
                this.data.set('formData.portRangeDisable', false);
            }
            items.forEach(item => {
                this.data.set(`formData.${item}`, protocolRule[item]);
            });
        }
    }
    ruleIndexOfTable(rule, datasource, ruleIndex) {
        var index = -1;
        u.find(datasource, function (x, i) {
            if (i !== ruleIndex && rule.equal(x)) {
                index = i;
                return true;
            }
        });
        return index;
    }
}
