/*
 * @description: 安全组复制
 * @file: security/pages/copy.js
 * @author: p<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import {Form, Input, Dialog, Select, Button, Icon, Tooltip, Alert, Notification} from '@baidu/sui';
import {checker} from '@baiducloud/bce-opt-checker';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';

import rules from '../rules';

const {service} = decorators;
const tpl = html`
<template>
<s-dialog
    class="security-copy"
    open="{=open=}"
    width="550"
    title="{{'复制安全组'}}">
    <s-alert skin="warning" s-if="FLAG.NetworkSecuritySupCorssCopy">
        {{copyTip | raw}}
    </s-alert>
    <s-form s-ref="form"
        data="{=formData=}"
        rules="{{rules}}"
        label-col="{{labelCol}}"
        wrapper-col="{{wrapperCol}}"
        label-align="left">
        <s-form-item prop="vpcId" label="{{'目标地域：'}}" s-if="FLAG.NetworkSecuritySupCorssCopy">
            <s-select
                width="400"
                datasource="{{regionList}}"
                value="{=formData.region=}">
        </s-form-item>
        <s-form-item prop="name" label="{{'安全组名称：'}}"
            help="{{'大小写字母、数字，中文以及-_/.特殊字符，必须以字母、中文开头，但不能以default开头，长度1-65'}}">
            <s-input
                width="400"
                value="{=formData.name=}"
                placeholder="{{'请输入安全组名称'}}">
            </s-input>
        </s-form-item>
        <s-form-item prop="speed" label="{{'所在网络：'}}" s-if="FLAG.NetworkSecuritySupCorssCopy">
            <s-select
                width="400"
                datasource="{{vpcList}}"
                on-change="vpcChange($event)"
                value="{=formData.vpcId=}">
        </s-form-item>
        <s-form-item label="{{'描述：'}}">
            <s-textarea
                maxLength="200"
                width="400"
                height="60"
                value="{=formData.description=}"
                placeholder="{{'请输入描述'}}"
            ></s-textarea>
        </s-form-item>
    </s-form>
    <div slot="footer">
        <s-button on-click="close">{{'取消'}}</s-button>
        <s-tooltip
            trigger="{{create.disable ? 'hover' : ''}}" placement="top">
            <!--bca-disable-next-line-->
            <div slot="content">{{create.message | raw}}</div>
            <s-button
                skin="primary"
                disabled="{{create.disable || copying}}"
                on-click="create">
                {{'确定'}}
            </s-button>
        </s-tooltip>
    </div>
</s-dialog>
</template>
`;
export default class VpcInstanceIndex extends Component {
    static template = tpl;
    static components = {
        's-form': Form,
        's-form-item': Form.Item,
        's-input': Input,
        's-dialog': Dialog,
        's-button': Button,
        's-select': Select,
        's-tooltip': Tooltip,
        's-textarea': Input.TextArea,
        's-icon': Icon,
        's-alert': Alert
    };
    initData() {
        let labelCol = {span: 4};
        let wrapperCol = {span: 19};
        const currentRegion = window.$context.getCurrentRegionId();
        const regionList = u.map(window.$context.SERVICE_TYPE.NETWORK.region, (value, key) => {
            return {
                value: key,
                text: value
            };
        });
        if (location.search.indexOf('locale=en') > -1) {
            labelCol = {span: 6};
            wrapperCol = {span: 17};
        }
        return {
            FLAG,
            open: false,
            labelCol,
            wrapperCol,
            regionList: regionList,
            formData: {
                region: currentRegion,
                name: '',
                vpcId: '',
                description: ''
            },
            rules: {
                name: [
                    {
                        required: true,
                        message: '不能为空'
                    },
                    {
                        pattern: /^(?!default)[a-zA-Z\u4e00-\u9fa5][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/,
                        message:
                            '名称不符合规则：大小写字母、数字，中文以及-_/.特殊字符，必须以字母、中文开头，但不能以default开头，长度1-65'
                    }
                ]
            },
            copyTip:
                '跨VPC复制安全组，仅复制Source为『源IP』的入站规则，不复制『安全组』入站规则；<br>' +
                '同VPC复制安全组时，复制Source为『源IP』和『安全组』入站规则。',
            create: {},
            copying: false
        };
    }
    inited() {
        this.getVpcList();
        this.checkQuota(this.data.get('vpcId'));
        this.watch('formData.region', () => {
            this.getVpcList();
        });
    }
    getVpcList() {
        if (!FLAG.NetworkSecuritySupCorssCopy) {
            return;
        }
        this.$http.vpcList({}, {headers: {region: this.data.get('formData.region')}}).then(data => {
            let vpcId = this.data.get('vpcId');
            let initVpcId;
            let list = u.map(data, item => {
                return {
                    text: `${item.name}（${item.cidr}）`,
                    value: item.vpcId
                };
            });
            if (data.some(item => item.vpcId === vpcId)) {
                initVpcId = vpcId;
            } else {
                initVpcId = list.length ? list[0].value : '';
            }
            this.data.set('vpcList', list);

            this.nextTick(() => this.data.set('formData.vpcId', initVpcId));
        });
    }
    vpcChange(e) {
        this.checkQuota(e.value);
    }
    close() {
        this.data.set('open', false);
    }
    checkQuota(vpcId) {
        this.$http.securityQuota({vpcId}, {region: this.data.get('formData.region')}).then(result => {
            let {create} = checker.check(rules, [], 'create', {
                quotaCheck: result.free > 0
            });
            this.data.set('create', create);
        });
    }
    getCopySecurityRuleFromApi() {
        return this.$http
            .securityCopyRule({
                securityGroupId: this.data.get('id')
            })
            .then(result => {
                return result;
            });
    }
    getCopySecurityRule() {
        let payload = {
            pageNo: 1,
            pageSize: 100000
        };
        return this.$http.getSecurityRules(this.data.get('id'), payload).then(result => {
            return result;
        });
    }
    async create() {
        // 避免鼠标连点多次重复请求，san 状态变化渲染时间好像比较慢，大于鼠标快速点击时间间隔
        if (this.data.get('copying')) {
            return;
        }
        await this.ref('form').validateFields();
        const formData = this.data.get('formData');
        this.data.set('copying', true);
        const payload = {
            desc: formData.description,
            securityGroupUuid: this.data.get('id'),
            name: formData.name,
            targetVpcUuid: formData.vpcId,
            targetRegion: formData.region
        };
        this.$http
            .securityCopyNew(payload)
            .then(result => {
                if (result.id) {
                    Notification.success('安全组复制成功');
                    this.data.set('open', false);
                    this.fire('success');
                }
            })
            .catch(err => {
                Notification.error('安全组复制失败');
            })
            .finally(() => {
                this.data.set('copying', false);
            });
    }
}
