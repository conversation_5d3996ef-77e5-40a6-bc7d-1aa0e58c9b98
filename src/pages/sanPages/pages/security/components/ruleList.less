.security-rule-list {
    .alert-tip {
        height: auto;
    }
    .security-tip {
        background: #fcf7f1;
        color: #333333;
        padding: 9px 20px 9px 40px;
        margin-bottom: 10px;
        position: relative;
        line-height: 20px;
        .icon-warning {
            color: #f39000;
            margin-right: 5px;
            position: absolute;
            left: 20px;
            font-size: 12px;
        }
    }
    .disable-line {
        .s-table-cell-text {
            color: #ccc !important;
            a {
                color: #ccc !important;
            }
        }
    }
    .ruletype {
        display: inline-block;
    }
    .lead-btn,
    .ruleiptype {
        float: right;
        margin-right: 8px;
    }
    .lead-tooltip {
        float: right;
    }
    .left_class {
        margin-left: 8px;
    }
    .leftbar_class {
        float: left;
        display: inline-flex;
    }
    .edit-btn {
        display: inline-block;
        margin-right: 8px;
    }
    .s-table {
        .s-table-container {
            .no-padding-btn {
                padding: 0;
            }
        }
    }
    .security-margin-left {
        margin-left: 8px;
    }
}
