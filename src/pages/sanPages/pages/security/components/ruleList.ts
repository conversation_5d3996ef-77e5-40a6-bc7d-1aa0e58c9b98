/*
 * @description: 安全组规则列表
 * @file: network/security/pages/ruleList.js
 * @author: p<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {Notification} from '@baidu/sui';
import {OutlinedPlus} from '@baidu/sui-icon';
import Rule from '../util/rule';
import LeadRule from '../leadFile/leadFile';
import checkRule from '../checkRule/checkRule';
import kCommonServices from '../util/services';
import Create from './create';

import CopyRule from './copyRule';
import Confirm from '@/pages/sanPages/components/confirm';
import {SecurityIpVersion as IpVersion, Mask, securityTemp} from '@/pages/sanPages/common/enum';
import {checkIpv6Cidr, convertCidrToBinary} from '@/pages/sanPages/utils/common';
import regs from '@/pages/sanPages/utils/rule';
import './ruleList.less';
import {kXhrOptions, utcToTime, $flag as FLAG} from '@/pages/sanPages/utils/helper';
import testID from '@/testId';

let kDefaultAllRule = new Rule('暴露全部端口到公网和内网', 'all', '1-65535', IpVersion.IPV4);
let kV6DefaultAllRule = new Rule('暴露全部端口到公网和内网', 'all', '1-65535', IpVersion.IPV6);
const {asComponent, invokeSUI, invokeSUIBIZ, template, service} = decorators;

function isPort(v) {
    return /^[1-9][0-9]{0,4}/.test(v) && v <= 65535;
}
function portRange(value) {
    value = u.trim(value);
    if (value.toLowerCase() === 'all') {
        return false;
    }
    if (value.indexOf('-') === -1) {
        return isPort(value);
    }
    let array = value.split('-');
    return isPort(array[0]) && isPort(array[1]) && +array[0] < +array[1];
}
function checkIp(value, ipVersion) {
    value = u.trim(value);
    if (value.toLowerCase() === 'all') {
        return '';
    }
    if (ipVersion === 'IPv6') {
        if (!checkIpv6Cidr(value)) {
            let valueString = convertCidrToBinary(value);
            let valueMask = value.split('/')[1];
            // 掩码分割的前部分
            let preValueString = valueString.substring(0, +valueMask);
            // 掩码分割的前后部分
            let tailValueString = valueString.substring(+valueMask, valueString.length);
            if (valueMask && tailValueString.includes('1')) {
                let addLen = 128 - preValueString.length;
                let fixIpBinary = preValueString + new Array(addLen).fill('0').join('');
                // 每隔16位按:分割
                let ipBinaryArr = fixIpBinary
                    .replace(/(.{16})/g, '$1:')
                    .substring(0, 128)
                    .split(':');
                let ipArr = ipBinaryArr.map(binary => {
                    // 先转为10进制，再转为16进制
                    return parseInt(binary, 2).toString(16);
                });
                let fixIp = ipArr.join(':') + '/' + valueMask;
                // 连续:0替换::
                let replaceIp = fixIp.replace(/(:0){2,}/g, '::');
                return `网段与掩码不匹配，建议改为${replaceIp}`;
            }
            return 'IP格式不正确';
        }
    } else {
        let reg = new RegExp(regs.IP_CIDR);
        if (!reg.test(value)) {
            return 'IP格式不正确';
        }
        let valueString = convertCidrToBinary(value);
        let valueMask = value.split('/')[1];
        // 掩码分割的前部分
        let preValueString = valueString.substring(0, +valueMask);
        // 掩码分割的前后部分
        let tailValueString = valueString.substring(+valueMask, valueString.length);
        if (valueMask && tailValueString.includes('1')) {
            let addLen = 32 - preValueString.length;
            let fixIpBinary = preValueString + new Array(addLen).fill('0').join('');
            // 每隔8位按.分割
            let ipBinaryArr = fixIpBinary
                .replace(/(.{8})/g, '$1.')
                .substring(0, 32)
                .split('.');
            let ipArr = ipBinaryArr.map(binary => parseInt(binary, 2));
            let fixIp = ipArr.join('.') + '/' + valueMask;
            return `网段与掩码不匹配，建议改为${fixIp}`;
        }
    }
    return '';
}
/* eslint-disable */
const tpl = html`
    <div class="security-rule-list">
        <s-alert skin="warning" class="alert-tip">
            {{'温馨提示：普通安全组是"允许访问"的规则集合，每添加一个规则，代表允许一个source访问某个或某个范围的端口。若安全组中未添加任何规则，表示所关联BCC实例的所有端口都不能被外界访问'}}
        </s-alert>
        <s-radio-radio-group
            enhanced
            class="ruletype"
            datasource="{{ruleTypeList}}"
            on-change="ruleTypeChange($event)"
            value="{=ruleType=}"
            radioType="button"
            data-test-id="${testID.security.createChangeRuleType}"
        >
        </s-radio-radio-group>
        <div class="allow-all-port">
            <span>{{'允许访问所有端口：'}}</span>
            <s-switch
                disabled="{{changeFrom === 'switch' || accessOpt.addSgRule.disabled || allDataLoading || isEditAll || isEdit}}"
                checked="{=allowAllPort=}"
                on-change="allowAllPortChange($event)"
            />
        </div>
        <div class="tool-tip">
            <div class="leftbar_class">
                <div s-if="{{!isEditAll}}">
                    <s-tooltip
                        trigger="{{accessOpt.addSgRule.message || disableMessage ? 'hover' : ''}}"
                        placement="right"
                    >
                        <div slot="content">
                            <!--bca-disable-next-line-->
                            {{(accessOpt.addSgRule.message ? accessOpt.addSgRule.message : disableMessage) | raw }}
                        </div>
                        <s-button
                            disabled="{{accessOpt.addSgRule.disabled ? true : isEdit || disableAdd || changeFrom === 'switch'}}"
                            skin="primary"
                            tracn-id="vpc_security_add_rule"
                            on-click="onCreate"
                            data-test-id="${testID.security.createAddRuleBtn}"
                        >
                            <outlined-plus />
                            {{'添加规则'}}
                        </s-button>
                    </s-tooltip>
                    <s-tooltip
                        trigger="{{accessOpt.updateSgRule.disabled ? 'hover' : ''}}"
                        content="{{accessOpt.updateSgRule.message}}"
                    >
                        <s-button
                            disabled="{{accessOpt.updateSgRule.disabled || noRuleDataSource || isEdit}}"
                            class="left_class"
                            s-if="isDetail"
                            on-click="handleEditAll"
                            >{{'编辑全部'}}</s-button
                        >
                    </s-tooltip>
                    <s-tooltip content="{{accessOpt.deleteSgRule.message}}" placement="right">
                        <s-button
                            class="left_class"
                            on-click="deleteRule"
                            disabled="{{accessOpt.deleteSgRule.disabled ? true : existSelected}}"
                            data-test-id="${testID.security.createDeleteRule}"
                        >
                            {{'删除'}}
                        </s-button>
                    </s-tooltip>
                </div>
                <div s-else>
                    <s-tooltip content="{{selectTip}}" trigger="{{existSelected ? 'hover' : ''}}" placement="top">
                        <s-button
                            loading="{{isEdittingAll}}"
                            skin="primary"
                            on-click="handleSave"
                            disabled="{{existSelected}}"
                        >
                            {{'保存'}}
                        </s-button>
                    </s-tooltip>
                    <s-tooltip>
                        <s-button class="left_class" on-click="handleCancelEditAll">{{'取消'}}</s-button>
                    </s-tooltip>
                </div>
            </div>
            <div s-if="!isEditAll">
                <s-select
                    class="ruleiptype"
                    width="100"
                    datasource="{{ruleIpTypeList}}"
                    value="{=ruleIpType=}"
                    disabled="{{isEdit}}"
                    on-change="ruleIpTypeChange($event, rowIndex)"
                >
                </s-select>
                <s-button
                    class="lead-btn"
                    s-if="isDetail && FLAG.NetworkSecuritySupLead"
                    disabled="{{isEdit}}"
                    on-click="leadOut"
                    >{{'导出' }}</s-button
                >
                <s-tooltip
                    class="lead-tooltip"
                    trigger="{{accessOpt.addSgRule.message || disableMessage ? 'hover' : ''}}"
                    placement="right"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{(accessOpt.addSgRule.message ? '您没有导入安全组规则权限，请联系主用户添加。' :
                        disableMessage) | raw }}
                    </div>
                    <s-button
                        disabled="{{accessOpt.addSgRule.disabled ? true : disableAdd || allDataLoading || isEdit}}"
                        class="lead-btn"
                        s-if="isDetail && FLAG.NetworkSecuritySupLead"
                        on-click="leadIn"
                    >
                        {{'导入' }}
                    </s-button>
                </s-tooltip>
                <s-button s-if="isDetail" class="lead-btn" disabled="{{isEdit}}" on-click="checkRule"
                    >{{'规则检查' }}</s-button
                >
            </div>
        </div>
        <s-table
            columns="{{table.columns}}"
            loading="{{table.loading || allDataLoading}}"
            error="{{table.error}}"
            rowClassName="{{table.rowClassName}}"
            on-selected-change="tableSelected($event)"
            selection="{=table.selection=}"
            datasource="{{filterDatasource}}"
            data-test-id="${testID.security.ruleTable}"
        >
            <div slot="error">
                {{'啊呀，出错了？'}}
                <a href="javascript:void(0);" on-click="refresh">{{'重新加载'}}</a>
            </div>
            <div slot="empty">
                <s-empty>
                    <s-button
                        s-if="!accessOpt.addSgRule.disabled"
                        skin="stringfy"
                        slot="action"
                        class="security-margin-left"
                        on-click="onCreate"
                        data-test-id="${testID.security.createAddRuleBtn}"
                        >添加规则></s-button
                    >
                    <s-tooltip s-else content="{{accessOpt.addSgRule.message}}"><span>添加规则></span></s-tooltip>
                </s-empty>
            </div>
            <div slot="c-ethertype">
                <s-select
                    s-if="editRow[rowIndex] && editType === 'add'"
                    width="90"
                    on-change="itemChange('ethertypes',rowIndex,$event)"
                    datasource="{{ethertypeList}}"
                    value="{=ethertypes[rowIndex]=}"
                />
                <span s-else>{{row.ethertype}}</span>
            </div>
            <div slot="c-protocol">
                <s-select
                    getPopupContainer="{{handleGetPopupContainer}}"
                    s-if="editRow[rowIndex]"
                    width="140"
                    on-change="itemChange('protocols',rowIndex,$event)"
                    datasource="{{protocolList}}"
                    value="{=protocols[rowIndex]=}"
                />
                <span s-else>{{row | protocol}}</span>
            </div>
            <div slot="c-portRange">
                <s-input
                    width="100"
                    disabled="{{portRangeDisable[rowIndex]}}"
                    s-if="editRow[rowIndex]"
                    on-input="itemChange('portRanges',rowIndex,$event)"
                    value="{=portRanges[rowIndex]=}"
                    placeholder="1-65535"
                >
                </s-input>
                <span s-else>{{row | portRange}}</span>
                <p style="color: #EB5252" s-if="portRangeErr[rowIndex]">{{portRangeErr[rowIndex]}}</p>
            </div>
            <div slot="c-source">
                <div s-if="editRow[rowIndex]">
                    <s-select
                        width="{{80}}"
                        on-change="itemChange('sources',rowIndex,$event)"
                        datasource="{{sourceList}}"
                        value="{=sources[rowIndex]=}"
                    />
                    <s-input
                        s-if="sources[rowIndex]==='user'"
                        on-input="itemChange('remoteIPs',rowIndex,$event)"
                        width="{{100}}"
                        value="{=remoteIPs[rowIndex]=}"
                        placeholder="all"
                    ></s-input>
                    <s-select
                        s-if="sources[rowIndex]==='system'"
                        on-change="itemChange('remoteGroupIds',rowIndex,$event)"
                        width="{{120}}"
                        value="{=remoteGroupIds[rowIndex]=}"
                        filterable
                    >
                        <s-select-option s-for="item in securityGroupList" value="{{item.value}}" label="{{item.text}}">
                            <s-tooltip placement="right">
                                <div slot="content">{{item.text}}</div>
                                <div>{{item.text}}</div>
                            </s-tooltip>
                        </s-select-option>
                    </s-select>
                    <p style="color: #EB5252" s-if="remoteIPErr[rowIndex]">{{remoteIPErr[rowIndex]}}</p>
                </div>
                <span s-else>{{row | source}}</span>
            </div>
            <div slot="c-name">
                <s-input
                    width="100"
                    on-input="itemChange('names',rowIndex,$event)"
                    s-if="editRow[rowIndex]"
                    value="{=names[rowIndex]=}"
                >
                </s-input>
                <span s-else>{{row | name}}</span>
            </div>
            <div slot="c-opt">
                <div
                    s-if="{{row.protocol!=='all' || row.portRange!=='1-65535'
                || row.remoteIP!=='all' || !row.esgRuleUuid}}"
                >
                    <div class="edit-btn" s-if="!editRow[rowIndex] || isEditAll">
                        <s-tooltip s-if="accessOpt.updateSgRule.disabled" content="{{accessOpt.updateSgRule.message}}">
                            <a href="javascript:void(0)" style="color:#999">{{'编辑'}}</a>
                        </s-tooltip>
                        <span s-else>
                            <a
                                href="javascript:void(0)"
                                s-if="!editArray.length && !isEditAll"
                                on-click="edit(row,rowIndex)"
                                data-test-id="${testID.security.createAddRuleEdit}{{rowIndex}}"
                                >{{'编辑'}}</a
                            >
                            <a href="javascript:void(0)" s-else style="color:#999">{{'编辑'}}</a>
                        </span>
                    </div>
                    <div class="edit-btn" s-if="isDetail && !editRow[rowIndex] || isEditAll">
                        <s-tooltip s-if="accessOpt.addSgRule.disabled || disableAdd || isEditAll">
                            <div slot="content">
                                <!--bca-disable-next-line-->
                                {{accessOpt.addSgRule.message ? '您没有复制安全组规则权限，请联系主用户添加。' :
                                (disableAdd ? disableMessage : '') | raw}}
                            </div>
                            <a href="javascript:void(0)" style="color:#999;cursor: not-allowed;">{{'复制'}}</a>
                        </s-tooltip>
                        <span s-else>
                            <a href="javascript:void(0)" on-click="copyRule(row,rowIndex)">{{'复制'}}</a>
                        </span>
                    </div>
                    <s-button
                        skin="stringfy"
                        class="no-padding-btn"
                        disabled="{{portRangeErr[rowIndex] || remoteIPErr[rowIndex]}}"
                        href="javascript:void(0)"
                        s-if="editRow[rowIndex] && !isEditAll"
                        on-click="editConfirm(rowIndex, editType)"
                        data-test-id="${testID.security.createAddRuleSubmit}{{rowIndex}}"
                        >{{'确定'}}
                    </s-button>
                    <s-button
                        skin="stringfy"
                        href="javascript:void(0)"
                        s-if="editRow[rowIndex] && !isEditAll"
                        on-click="editCancel(row, rowIndex, editType)"
                        data-test-id="${testID.security.createAddRuleCancel}{{rowIndex}}"
                        >{{'取消'}}</s-button
                    >
                </div>
            </div>
        </s-table>
        <s-pagination
            s-if="{{pager.total && isDetail && !isEditAll}}"
            class="pagination-class-table"
            slot="footer"
            layout="{{'total, pageSize, pager, go'}}"
            pageSize="{{pager.size}}"
            total="{{pager.total}}"
            page="{{pager.page}}"
            on-pagerChange="onPagerChange"
            on-pagerSizeChange="onPagerSizeChange"
        />
    </div>
`;
/* eslint-enable */

@template(tpl)
@asComponent('@rule-list')
@invokeSUI
@invokeSUIBIZ
class RuleList extends Component {
    static components = {
        'outlined-plus': OutlinedPlus
    };
    static computed = {
        datasource() {
            if (this.data.get('ruleType') === 'in') {
                return this.data.get('table.inDatasource');
            }
            return this.data.get('table.outDatasource');
        },
        filterDatasource() {
            let datasource = this.data.get('datasource');
            if (this.data.get('ruleIpType') === 'all') {
                return datasource;
            } else if (this.data.get('ruleIpType') === 4) {
                return datasource.filter(item => item.ethertype === IpVersion.IPV4);
            }
            return datasource.filter(item => item.ethertype === IpVersion.IPV6);
        },
        disableAdd() {
            let totalRule = this.data.get('table.inDatasource').length + this.data.get('table.outDatasource').length;
            let isDetail = this.data.get('isDetail');
            let ruleType = this.data.get('ruleType') === 'out' ? 'egress' : 'ingress';
            let originLength = this.data.get('originDatasource').filter(item => item.direction === ruleType).length;
            return originLength >= this.data.get('ruleQuota') || (!isDetail && totalRule > 1000);
        },
        disableMessage() {
            let totalRule = this.data.get('table.inDatasource').length + this.data.get('table.outDatasource').length;
            let isDetail = this.data.get('isDetail');
            let quota = this.data.get('ruleQuota');
            let ruledirection = this.data.get('ruleType') === 'in' ? '入站' : '出站';
            if (this.data.get('disableAdd') && !isDetail && quota > 1000 && totalRule > 1000) {
                return '安全组规则批量添加的数量不能超过1000个，请您减少批量添加的数量';
            } else if (this.data.get('disableAdd') && isDetail) {
                return (
                    `安全组规则配额已达上限，` +
                    `<a href="/quota_center/#/quota/apply/create?serviceType=SECURITYGROUP` +
                    `&region=${window?.$context?.getCurrentRegionId()}` +
                    `&cloudCenterQuotaName=securityRulePerGroup" target="_blank">去申请配额</a>`
                );
            } else if (this.data.get('disableAdd') && quota <= 1000 && totalRule > quota) {
                return '您已添加' + quota + '条' + ruledirection + '规则，达到限额！';
            }
            return '';
        },
        allowAllPort() {
            const tempId = this.data.get('tempId');
            let ruleList = [];
            if (this.data.get('ruleType') !== 'out') {
                ruleList = this.data.get('allInRuleDataSource');
            } else {
                ruleList = this.data.get('allOutRuleDataSource');
            }
            if (!this.data.get('isDetail')) {
                ruleList = this.data.get('datasource');
            }
            let allowAllrule = ruleList.filter(item => {
                return (
                    item.protocol === 'all' &&
                    item.portRange === '1-65535' &&
                    ((item.ethertype === IpVersion.IPV4 &&
                        (item.remoteIP === 'all' || item.remoteIP === '0.0.0.0/0')) ||
                        (item.ethertype === IpVersion.IPV6 && (item.remoteIP === 'all' || item.remoteIP === '::/0')))
                );
            });
            let length = allowAllrule.length;
            if (length === 2 || tempId === '3') {
                return true;
            }
            return false;
        },
        sourceList() {
            let result = [
                {text: '源IP', value: 'user'},
                {text: '安全组', value: 'system'}
            ];
            if (this.data.get('ruleType') === 'out') {
                result[0].text = '目的IP';
            }
            return result;
        },
        existSelected(): boolean {
            const selectedRule = this.data.get('selectedRule');
            return selectedRule.length === 0;
        },
        noRuleDataSource(): boolean {
            const filterDatasource = this.data.get('filterDatasource');
            return filterDatasource.length === 0;
        }
    };
    static filters = {
        protocol(row: Record<string, any>) {
            if (row.protocol === 'all') {
                return '全部协议';
            }
            return u.escape(row.protocol);
        },
        portRange(row: Record<string, any>) {
            return u.escape(row.portRange);
        },
        source(row: Record<string, any>) {
            const {source, remoteIP, ethertype, ksource, remoteGroupShortId, remoteGroupId, remoteGroupName} = row;
            const ruleType = this.data.get('ruleType');
            if (source === 'user') {
                return (
                    (ruleType === 'in' ? '源IP：' : '目的IP：') +
                    (remoteIP === 'all'
                        ? ethertype === IpVersion.IPV4
                            ? ksource || '0.0.0.0/0'
                            : ksource || '::/0'
                        : u.escape(remoteIP))
                );
            }
            let id = remoteGroupShortId || remoteGroupId;
            return '安全组：' + u.escape(remoteGroupName) + (id ? '（' + u.escape(id) + '）' : '');
        },
        name(row: Record<string, any>) {
            return u.escape(row.name);
        }
    };
    initData() {
        return {
            FLAG,
            ruleTypeList: [
                {
                    text: '入站',
                    value: 'in'
                },
                {
                    text: '出站',
                    value: 'out'
                }
            ],
            ruleType: 'in',
            ethertypeList: IpVersion.toArray(),
            ruleIpTypeList: [
                {
                    text: '全部规则',
                    value: 'all'
                },
                {
                    text: 'IPv4',
                    value: 4
                },
                {
                    text: 'IPv6',
                    value: 6
                }
            ],
            ruleIpType: 'all',
            selectedRule: [],
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: [],
                    disabledIndex: []
                },
                columns: [
                    {
                        name: 'ethertype',
                        label: '类型',
                        width: 90,
                        render(item) {
                            if (!item.edit) {
                                return u.escape(item.ethertype);
                            }
                            return item.ethertype;
                        }
                    },
                    {
                        name: 'protocol',
                        label: '协议',
                        width: 140,
                        render(item) {
                            if (!item.edit) {
                                if (item.protocol === 'all') {
                                    return '全部协议';
                                }
                                return u.escape(item.protocol);
                            }
                            return item.protocol;
                        }
                    },
                    {
                        name: 'portRange',
                        label: '端口',
                        width: 90,
                        render(item) {
                            if (!item.edit) {
                                return u.escape(item.portRange);
                            }
                            return item.portRange;
                        }
                    },
                    {
                        name: 'source',
                        label: '来源',
                        width: 90,
                        render(item) {
                            if (!item.edit) {
                                if (item.source === 'user') {
                                    return (
                                        (this.id === 'rulesInTable' ? '源IP：' : '目的IP：') +
                                        (item.remoteIP === 'all'
                                            ? item.ethertype === IpVersion.IPV4
                                                ? '0.0.0.0/0'
                                                : '::/0'
                                            : u.escape(item.remoteIP))
                                    );
                                }
                                let id = item.remoteGroupShortId || item.remoteGroupId;
                                return (
                                    '安全组：' + u.escape(item.remoteGroupName) + (id ? '（' + u.escape(id) + '）' : '')
                                );
                            }
                            return item.source;
                        }
                    },
                    {
                        name: 'name',
                        label: '备注',
                        width: 90,
                        render(item) {
                            if (!item.edit) {
                                return u.escape(item.name);
                            }
                            return item.name;
                        }
                    },
                    {
                        name: 'updatedTime',
                        label: '更新时间',
                        width: 120,
                        render(item) {
                            return utcToTime(item.updatedTime);
                        }
                    },
                    {
                        name: 'opt',
                        label: '操作',
                        width: 90
                    }
                ],
                inDatasource: [],
                outDatasource: []
            },
            securityGroupList: [],
            isDetail: false,
            protocolList: [
                {text: '全部协议', value: 'all'},
                {text: 'tcp', value: 'tcp'},
                {text: 'udp', value: 'udp'},
                {text: 'icmp', value: 'icmp'},
                {text: 'tcp(HTTP)', value: 'tcp_http'},
                {text: 'tcp(HTTPS)', value: 'tcp_https'},
                {text: 'tcp(FTP)', value: 'tcp_ftp'},
                {text: 'tcp(RDP)', value: 'tcp_rdp'},
                {text: 'udp(DNS)', value: 'udp_dns'},
                {text: 'tcp(DNS)', value: 'tcp_dns'},
                {text: 'tcp(POP3)', value: 'tcp_pop3'},
                {text: 'tcp(MYSQL)', value: 'tcp_mysql'},
                {text: 'tcp(SQL SERVER)', value: 'tcp_sql_server'},
                {text: 'tcp(SNMP)', value: 'tcp_snmp'},
                {text: 'tcp(SMTP)', value: 'tcp_smtp'},
                {text: 'tcp(SNMP TRAP)', value: 'tcp_snmp_trap'},
                {text: 'tcp(SSH)', value: 'tcp_ssh'}
            ],
            ethertypes: [],
            protocols: [],
            portRanges: [],
            sources: [],
            remoteGroupIds: [],
            editRow: [],
            remoteIPs: [],
            names: [],
            portRangeDisable: [],
            portRangeErr: [],
            remoteIPErr: [],
            rules: [],
            securityGroupsIdNameMap: {},
            isEdit: false,
            ruleQuota: 50,
            instance: null,
            editArray: [],
            instanceDetail: null,
            pager: {
                size: 10,
                page: 1,
                total: 0
            },
            originDatasource: [],
            allInRuleDataSource: [],
            allOutRuleDataSource: [],
            isEditAll: false,
            selectTip: '请先选择安全组规则',
            isEdittingAll: false,
            usedNestSecurityNum: 0,
            usedNestSecNumCopy: 0,
            availableNestSecNum: 10,
            totalNestSecurityQuota: 10,
            preIndex: -1,
            allDataLoading: true,
            accessOpt: {},
            handleGetPopupContainer: () => document.body
        };
    }
    inited() {
        this.ipV6GatewayWhiteList();
        this.getRemoteSgRulesQuota();
        if (!this.data.get('isDetail')) {
            // 初始化模版
            this.setInitTemp();
            // 切换模版
            this.watch('tempId', value => {
                this.setInitTemp();
            });
            this.data.set('allDataLoading', false);
        } else {
            this.loadAllSecRulesData();
        }
        this.watch('filterDatasource', datas => {
            this.reset();
            let editItems = ['protocol', 'portRange', 'source', 'ethertype', 'remoteGroupId', 'remoteIP', 'name', 'id'];
            datas.forEach((item, index) => {
                this.data.set(`portRangeDisable[${index}]`, false);
                this.data.set(`portRangeErr[${index}]`, '');
                this.data.set(`remoteIPErr[${index}]`, '');
                editItems.forEach(editItem => {
                    if (editItem === 'protocol' && (item[editItem] === 'icmp' || item[editItem] === 'all')) {
                        this.data.set(`portRangeDisable[${index}]`, true);
                    }
                    this.data.set(`${editItem}s[${index}]`, item[editItem]);
                });
            });
        });
        this.watch('vpcId', item => {
            this.getAllSecurityGroups();
        });
        this.watch('accessOpt', value => {
            this.data.set('accessOpt', value);
        });
    }
    attached() {
        let array = [this.checkRuleQuota(), this.getAllSecurityGroups()];
        if (this.data.get('isDetail')) {
            array.push(this.getDetail());
        }
        Promise.all(array);
    }
    setInitTemp() {
        const tempId = this.data.get('tempId');
        const temp = securityTemp.fromValue(tempId)?.temp;
        const inCustomTemp = temp.in;
        const outCustomTemp = temp.out;
        const instanceRuleTemp = u.map(inCustomTemp, item => {
            const {name, protocol, portRange, ethertype, ksource} = item;
            return new Rule(name, protocol, portRange, ethertype, ksource);
        });
        const outInstanceRuleTemp = u.map(outCustomTemp, item => {
            const {name, protocol, portRange, ethertype} = item;
            return new Rule(name, protocol, portRange, ethertype);
        });
        // 选模版即覆盖原有规则
        this.data.set(`table.inDatasource`, instanceRuleTemp);
        this.data.set(`table.outDatasource`, outInstanceRuleTemp);
    }
    getRemoteSgRulesQuota() {
        const securityGroupUuid = this.data.get('id') || '';
        this.$http.getRemoteSgRuleQuota({securityGroupUuid}).then((res: any) => {
            if (res) {
                const {free, total} = res;
                const used = total - free;
                this.data.set('totalNestSecurityQuota', total);
                this.data.set('usedNestSecurityNum', used);
                this.data.set('usedNestSecNumCopy', used); // 防止编辑时改了usedNestSecurityNum但没实际提交
                this.data.set('availableNestSecNum', free);
            }
        });
    }
    // 出入站切换
    ruleTypeChange(e: Event) {
        this.data.set('isEditAll', false);
        this.getRemoteSgRulesQuota(); // 刷新一下配额
        if (e.value === 'in') {
            this.data.set('table.columns[3].label', '来源');
        } else {
            this.data.set('table.columns[3].label', '目的');
        }
        // 如果有新增则删除该项
        if (this.data.get('editType') === 'add') {
            this.editCancel('', '', 'add');
        }
        this.data.set('ruleType', e.value);
        this.resetTable();
        this.clearDisabledIndex();
        this.haveAllPortRule();
        this.resetEdit();
        if (this.data.get('isDetail')) {
            // 切换出入站规则时重置分页
            this.data.set('pager.page', 1);
            this.data.set('pager.size', 10);
            this.getDetail();
        }
    }
    getDetail() {
        this.data.set('table.inDatasource', []);
        this.data.set('table.outDatasource', []);
        this.data.set('table.loading', true);
        let payload = this.getPayload();
        return this.$http.getSecurityRules(this.data.get('id'), payload, kXhrOptions.customSilent).then(result => {
            this.data.set('instance', result);
            this.data.set('table.loading', false);
            result.result.forEach(item => {
                let newItem = Rule.fromJSON(item);
                if (item.direction === 'ingress') {
                    this.data.push('table.inDatasource', newItem);
                } else if (item.direction === 'egress') {
                    this.data.push('table.outDatasource', newItem);
                }
                this.clearDisabledIndex();
                this.haveAllPortRule();
            });
            this.data.set(`pager.total`, result.totalCount);
        });
    }
    getPayload() {
        const {pager} = this.data.get('');
        let payload = {
            pageNo: pager.page,
            pageSize: pager.size,
            ethertype: this.data.get('ruleIpType') === 4 ? 'IPv4' : this.data.get('ruleIpType') === 6 ? 'IPv6' : '',
            direction: this.data.get('ruleType') === 'in' ? 'ingress' : 'egress'
        };
        if (!payload.ethertype) {
            delete payload.ethertype;
        }
        return payload;
    }
    tableSelected(e: any) {
        const {selectedIndex, selectedItems} = e.value;
        this.data.set('selectedRule', selectedItems);
        if (selectedIndex.length === 10) {
            const filterDatasource = this.data.get('filterDatasource');
            const allIndex = u.map(filterDatasource, (item, index) => index);
            const disabledIndex = u.difference(allIndex, selectedIndex);
            this.data.set('table.selection.disabledIndex', disabledIndex);
        } else {
            this.data.set('table.selection.disabledIndex', []);
        }
    }
    deleteRule() {
        let ruleType = this.data.get('ruleType');
        let datasource = this.data.get(`table.${ruleType}Datasource`);
        let selectedIndex = this.data.get('table.selection.selectedIndex');
        let result = [];
        let securityIndex = [];
        selectedIndex.map(index => {
            result.push(datasource[index].id);
            securityIndex.push(index);
        });
        this.deleteClient(result, securityIndex);
    }

    deleteClient(securityGroupRuleIds: string[], securityIndex: number[] = [], type: string = '') {
        if (!this.data.get('isDetail')) {
            const ruleType = this.data.get('ruleType');
            const datasource = this.data.get(`table.${ruleType}Datasource`);
            const ruleList = [];
            datasource.map((item, index) => {
                if (!securityIndex.includes(index)) {
                    ruleList.push(item);
                }
            });
            this.data.set(`table.${ruleType}Datasource`, ruleList);
            this.resetTable();
            this.clearDisabledIndex();
            this.resetEdit();
            this.haveAllPortRule();
            return;
        }

        // 删除的时候同时做兼容
        securityGroupRuleIds = securityGroupRuleIds.filter(item => item);
        if (!securityGroupRuleIds?.length) {
            return;
        }
        const callback = () => {
            return this.$http
                .securityDeleteRule({
                    securityGroupId: this.data.get('instanceDetail')?.id || this.data.get('id'),
                    securityGroupRuleIds
                })
                .then(() => this.resetTable())
                .catch(() => type && this.data.set('allowAllPort', false));
        };
        this.submitEditRule(callback);
    }

    editClient(rule: Record<string, any>, index: number) {
        if (!this.data.get('isDetail')) {
            const ruleType = this.data.get('ruleType');
            this.data.set(`table.${ruleType}Datasource[${index}]`, rule);
            this.resetEdit(index);
            this.clearDisabledIndex();
            this.data.set('editType', '');
            return;
        }

        const ruleType = this.data.get('ruleType');
        const callback = () => {
            return this.$http
                .securityUpdateRule({
                    securityGroupId: this.data.get('id'),
                    direction: ruleType === 'in' ? 'ingress' : 'egress',
                    securityGroupRuleId: rule.id,
                    protocol: rule.protocol,
                    portRange: u.trim(rule.portRange),
                    ethertype: rule.ethertype,
                    name: rule?.name?.replace(/[\t]+/g, ''),
                    remoteGroupId: rule.remoteGroupId,
                    remoteIpPrefix: u.trim(rule.remoteIP)
                })
                .then(() => this.resetEdit(index));
        };
        this.submitEditRule(callback);
    }
    createClient(rules: any[], type = '') {
        if (!this.data.get('isDetail')) {
            this.clearDisabledIndex();
            this.haveAllPortRule();
            this.resetEdit();
            this.data.set('editType', '');
            this.data.set('table.selection', {
                mode: 'multi',
                selectedIndex: []
            });
            return;
        }

        let reqRule = [];
        rules.map(rule => {
            if (rule.protocol === 'icmp') {
                rule.portRange = '';
            }
            reqRule.push({
                direction: this.data.get('ruleType') === 'in' ? 'ingress' : 'egress',
                remoteIpPrefix: u.trim(rule.remoteIP),
                ethertype: rule.ethertype,
                portRange: u.trim(rule.portRange),
                protocol: rule.protocol,
                name: rule?.name?.replace(/[\t]+/g, ''),
                remoteGroupId: rule.remoteGroupId
            });
        });
        const callback = () => {
            return this.$http.securityAddRule({
                securityGroupId: this.data.get('id'),
                rules: reqRule
            });
        };
        this.submitEditRule(callback);
    }
    editConfirm(index: number, editType: string) {
        if (this.data.get(`portRangeErr[${index}]`)) {
            return;
        }
        if (this.data.get(`remoteIPErr[${index}]`)) {
            return;
        }
        let editItems = ['protocol', 'portRange', 'source', 'remoteGroupId', 'remoteIP', 'name', 'id'];
        let rule: Record<string, any> = {};
        let ruleType = this.data.get('ruleType');

        editItems.forEach(editItem => {
            if (editItem === 'remoteIP') {
                let remoteIP = this.data.get(`${editItem}s[${index}]`)?.trim();
                rule[editItem] = remoteIP;
            } else {
                rule[editItem] = this.data.get(`${editItem}s[${index}]`);
            }
        });
        // 一些快捷模板类型协议传值修改为本身的协议
        rule.protocol = this.data.get(`protocols[${index}]`).split('_')[0];
        // ip类型编辑时无法修改
        if (this.data.get('editType') === 'edit') {
            rule.ethertype = this.data.get(`table.${ruleType}Datasource[${index}].ethertype`);
        } else {
            rule.ethertype = this.data.get(`ethertypes[${index}]`);
        }
        this.data.get('securityGroupList').forEach(item => {
            if (item.value === rule.remoteGroupId) {
                rule.remoteGroupShortId = item.title;
            }
        });

        if (rule.remoteGroupId) {
            rule.remoteGroupName = this.data.get('securityGroupsIdNameMap')[rule.remoteGroupId];
        }
        rule = Rule.fromJSON(rule);

        if (rule.protocol === 'icmp') {
            rule.portRange = '';
        }
        let array = ruleType === 'in' ? this.data.get('allInRuleDataSource') : this.data.get('allOutRuleDataSource');
        if (!rule.id) {
            array = this.data.get(`table.${ruleType}Datasource`).map(item => {
                let newItem = Rule.fromJSON(item);
                return newItem;
            });
        }
        if (editType === 'edit') {
            array = array.filter(item => item.id !== rule.id);
        }
        // 添加的数据已存在于表格中，提示用户
        if (this.ruleIndexOfTable(rule, array, rule.id ? -1 : index) !== -1) {
            Notification.error('该条规则您已添加，请勿重复添加！');
            return;
        }
        if (editType === 'edit') {
            this.editClient(rule, index);
        } else {
            const ruleType = this.data.get('ruleType');
            this.data.set(`table.${ruleType}Datasource[${index}]`, rule);
            this.createClient([rule]);
        }
    }

    editCancel(row: any, index: number, editType: string) {
        if (editType === 'edit') {
            this.data.set(`portRangeErr[${index}]`, '');
            this.data.set(`remoteIPErr[${index}]`, '');
            let editItems = ['protocol', 'portRange', 'source', 'remoteGroupId', 'remoteIP', 'name', 'id'];
            editItems.forEach(editItem => {
                this.data.set(`${editItem}s[${index}]`, row[editItem]);
                if (editItem === 'protocol' && row[editItem] !== 'icmp' && row[editItem] !== 'all') {
                    this.data.set(`portRangeDisable[${index}]`, false);
                }
            });
            this.resetEdit(index);
        } else {
            let ruleType = this.data.get('ruleType');
            this.data.shift(`table.${ruleType}Datasource`);
            this.resetEdit(index);
        }
        this.data.set('editType', '');
        // 恢复复选框
        this.data.set('table.selection', {
            mode: 'multi',
            selectedIndex: []
        });
        this.haveAllPortRule();
    }

    protocolChangeMap(val: string, index: number) {
        if (val === 'all') {
            this.data.set(`portRanges[${index}]`, '1-65535');
            this.data.set(`remarks[${index}]`, '全部协议');
            this.data.set(`protocols[${index}]`, val);
            this.data.set(`portRangeDisable[${index}]`, true);
        } else if (val === 'icmp') {
            this.data.set(`portRanges[${index}]`, '不涉及');
            this.data.set(`remarks[${index}]`, '');
            this.data.set(`protocols[${index}]`, val);
            this.data.set(`portRangeDisable[${index}]`, true);
        } else if (val === 'tcp' || val === 'udp') {
            this.data.set(`portRangeDisable[${index}]`, false);
            this.data.set(`remarks[${index}]`, '');
            this.data.set(`protocols[${index}]`, val);
            this.data.set(`portRanges[${index}]`, '1-65535');
        } else {
            let protocolItem = kCommonServices.find(item => item.val === val);
            let protocolRule = protocolItem?.rules[0];
            let items = ['portRange', 'name'];
            // icmp端口范围禁用并更新对应的ip类型
            if (val === 'icmp_ipv4' || val === 'icmp_ipv6') {
                this.data.set(`ethertypes[${index}]`, protocolRule.ethertype);
                this.data.set(`portRangeDisable[${index}]`, true);
            } else {
                this.data.set(`portRangeDisable[${index}]`, false);
            }
            items.forEach(item => {
                this.data.set(`${item}s[${index}]`, protocolRule[item]);
            });
        }
    }

    // 获取创建时已经使用的嵌套安全组数量
    getNestSecurityNum(): number {
        const inDatasource = this.data.get(`table.inDatasource`);
        const outDatasource = this.data.get(`table.outDatasource`);
        const inNestSecurityNum = inDatasource.filter((item: any) => item.remoteGroupId)?.length || 0;
        const outNestSecurityNum = outDatasource.filter((item: any) => item.remoteGroupId)?.length || 0;
        const usedNestSecurityNum = inNestSecurityNum + outNestSecurityNum;
        return usedNestSecurityNum;
    }

    itemChange(type: string, index: number, e: any) {
        if (type === 'protocols') {
            this.protocolChangeMap(e.value, index);
        }
        if (type === 'portRanges') {
            if (!e.value) {
                this.data.set(`portRangeErr[${index}]`, '端口范围必填');
            } else if (!portRange(e.value)) {
                this.data.set(`portRangeErr[${index}]`, '端口范围不符合规则');
            } else {
                this.data.set(`portRangeErr[${index}]`, '');
            }
        }
        if (type === 'remoteIPs') {
            let errMsg = checkIp(e.value, this.data.get('ethertypes')[index]);
            if (!e.value) {
                this.data.set(`remoteIPErr[${index}]`, 'IP范围必填');
            } else if (errMsg) {
                this.data.set(`remoteIPErr[${index}]`, errMsg);
            } else {
                this.data.set(`remoteIPErr[${index}]`, '');
            }
        }
        if (type === 'sources') {
            const isEditAll = this.data.get('isEditAll');
            const isDetail = this.data.get('isDetail');
            const isEdit = this.data.get('isEdit');
            const preIndex = this.data.get('preIndex');
            const totalNestSecurityQuota = this.data.get('totalNestSecurityQuota');

            let usedNestSecurityNum = this.getNestSecurityNum(); // 创建页已使用的配额
            if (isEditAll || (isDetail && isEdit)) {
                usedNestSecurityNum = this.data.get('usedNestSecurityNum'); // 详情页已使用的配额
            }
            if (e.value === 'system') {
                if (usedNestSecurityNum >= totalNestSecurityQuota) {
                    this.data.set(
                        `remoteIPErr[${index}]`,
                        `入站和出站规则最多支持${totalNestSecurityQuota}个嵌套安全组`
                    );
                } else {
                    this.data.set(`remoteIPErr[${index}]`, '');
                    this.data.set(`remoteIPs[${index}]`, '');
                    this.data.set('usedNestSecurityNum', usedNestSecurityNum + 1);
                }
                this.data.set(`remoteGroupIds[${index}]`, this.data.get('securityGroupList')[0].value);
            } else {
                this.data.set(`remoteIPErr[${preIndex}]`, '');
                this.data.set(`remoteGroupIds[${index}]`, '');
                if (!usedNestSecurityNum === totalNestSecurityQuota) {
                    this.data.set('usedNestSecurityNum', usedNestSecurityNum - 1);
                }
                this.itemChange('remoteIPs', index, {value: this.data.get('remoteIPs')[index]});
            }
            this.data.set('preIndex', index);
        }
        // 切换类型重置
        if (type === 'ethertypes') {
            let baseData = {
                priority: 1000,
                ethertype: e.value,
                source: 'user',
                name: '暴露全部端口到公网和内网',
                protocol: 'all',
                remoteIP: 'all',
                portRange: '1-65535',
                action: 'allow'
            };
            let ruletype = this.data.get('ruleType');
            if (ruletype === 'in') {
                this.data.set(`table.inDatasource[${index}]`, baseData);
            } else {
                this.data.set(`table.outDatasource[${index}]`, baseData);
            }
            this.data.set(`portRangeErr[${index}]`, '');
            this.data.set(`remoteIPErr[${index}]`, '');
        }
        this.data.set(`${type}[${index}]`, e.value);
    }

    haveAllPortRule() {
        let ruleList = [];
        if (this.data.get('ruleType') !== 'out') {
            ruleList = this.data.get('allInRuleDataSource');
        } else {
            ruleList = this.data.get('allOutRuleDataSource');
        }
        if (!this.data.get('isDetail')) {
            ruleList = this.data.get('datasource');
        }
        let ipv4AllPortRule = false;
        let ipv6AllPortRule = false;
        let disableIndexList = [];
        ruleList.forEach((item, index) => {
            disableIndexList.push(index);
            if (
                item.protocol === 'all' &&
                item.portRange === '1-65535' &&
                item.ethertype === IpVersion.IPV4 &&
                (item.remoteIP === 'all' || item.remoteIP === '0.0.0.0/0')
            ) {
                ipv4AllPortRule = true;
                disableIndexList[index] = -1;
            }
            if (
                item.protocol === 'all' &&
                item.portRange === '1-65535' &&
                item.ethertype === IpVersion.IPV6 &&
                (item.remoteIP === 'all' || item.remoteIP === '::/0')
            ) {
                ipv6AllPortRule = true;
                disableIndexList[index] = -1;
            }
        });
        return {
            ipv4AllPortRule,
            ipv6AllPortRule
        };
    }
    clearDisabledIndex() {
        this.data.set('table.selection.disabledIndex', []);
    }
    setAllPortRule(ruleType: string = '') {
        !ruleType && (ruleType = this.data.get('ruleType'));
        let haveAllPortRule = this.haveAllPortRule();
        let addRule = [];
        if (!haveAllPortRule.ipv4AllPortRule) {
            addRule.push(kDefaultAllRule);
        }
        if (!haveAllPortRule.ipv6AllPortRule) {
            addRule.push(kV6DefaultAllRule);
        }
        return addRule;
    }
    allowAllPortChange(e: Event) {
        this.data.set('changeFrom', 'switch');
        // 允许访问所有端口切换标识，清空所有已选择项
        this.data.set('allowSwitchFlag', true);
        if (e.value) {
            // 开关打开时，清空自定义模版，展示之前的
            // this.data.set('table.inDatasource', []);
            const addRule = this.setAllPortRule();
            if (addRule.length > 0) {
                const ruleType = this.data.get('ruleType');
                addRule.map(item => {
                    !this.data.get('isDetail') && this.data.push(`table.${ruleType}Datasource`, item);
                });
                this.createClient(addRule, 'allowPort');
            }
        } else {
            let securityGroupRuleIds = [];
            let securityIndex = [];
            let datasource = u.cloneDeep(this.data.get('originDatasource')) || [];
            if (this.data.get('ruleType') !== 'out') {
                datasource = this.data.get('allInRuleDataSource');
            } else {
                datasource = this.data.get('allOutRuleDataSource');
            }
            if (!this.data.get('isDetail')) {
                datasource = this.data.get(`table.${this.data.get('ruleType')}Datasource`);
            }
            datasource.map((item, index) => {
                if (
                    item.protocol === 'all' &&
                    item.portRange === '1-65535' &&
                    (item.remoteIP === 'all' || item.remoteIP === '0.0.0.0/0' || item.remoteIP === '::/0')
                ) {
                    securityGroupRuleIds.push(item.id);
                    securityIndex.push(index);
                }
            });
            this.deleteClient(securityGroupRuleIds, securityIndex, 'notAllowPort');
        }
    }
    resetTable() {
        this.data.set('table.selection.selectedIndex', []);
        this.data.set('selectedRule', []);
    }

    resetEdit(index?: number) {
        index ? this.data.set(`editRow[${index}]`, false) : this.data.set('editRow', []);
        this.data.set('isEdit', false);
        this.data.set('editArray', []);
        this.fire('editting', false);
    }

    edit(row: Record<string, any>, rowIndex: number) {
        this.data.set('editType', 'edit');
        this.data.set('editArray', [rowIndex]);
        this.data.set(`editRow[${rowIndex}]`, true);
        this.data.set('isEdit', true);
        this.fire('editting', true);
    }
    // 全部编辑
    handleEditAll() {
        this.data.set('isEditAll', true);
        const ruleIpType = this.data.get('ruleIpType');
        let filterDatasource = this.data.get('table.inDatasource');
        const ruleType = this.data.get('ruleType');
        // 筛选入站、出站
        if (ruleType !== 'in') {
            filterDatasource = this.data.get('table.outDatasource');
        }
        // 筛选IPv4、IPv6
        if (ruleIpType !== 'all') {
            filterDatasource = filterDatasource.filter((item: any) => {
                return item.ethertype === (ruleIpType === 4 ? IpVersion.IPV4 : IpVersion.IPV6);
            });
        }
        const formatedDatasource = u.map(filterDatasource, item => {
            return Rule.fromJSON(item);
        });
        // 规则数超过10条则不允许全选
        if (formatedDatasource.length > 10) {
            this.data.set('table.selection.disabledSelectAll', true);
        }
        this.data.set('filterDatasource', formatedDatasource);
        u.each(filterDatasource, (item, index) => {
            this.data.set(`editRow[${index}]`, true);
        });
    }

    handleSave() {
        const portRangeErr = this.data.get('portRangeErr');
        const remoteIpErr = this.data.get('remoteIPErr');
        const selectedIndex = this.data.get('table.selection.selectedIndex');
        const selectArray = [];
        let originData = [];
        const isExistErr = selectedIndex.some((item: number) => portRangeErr[item] || remoteIpErr[item]);
        if (isExistErr) {
            return false;
        }
        const ruleType = this.data.get('ruleType');
        const selectedRule = this.data.get('selectedRule');

        const editItems = ['protocol', 'portRange', 'source', 'remoteGroupId', 'remoteIP', 'name', 'id'];
        const editedRules = u.map(selectedRule, (item, index) => {
            const rule: Record<string, any> = {};
            u.each(editItems, key => {
                if (key === 'remoteIP') {
                    const remoteIp = this.data.get(`${key}s[${selectedIndex[index]}]`)?.trim();
                    rule[key] = remoteIp;
                } else {
                    rule[key] = this.data.get(`${key}s[${selectedIndex[index]}]`);
                }
            });
            // 一些快捷模板类型协议传值修改为本身的协议
            rule.protocol = rule.protocol.split('_')[0];
            rule.ethertype = item.ethertype;
            rule.securityGroupRuleId = item.securityGroupRuleId;
            const securityGroupList = this.data.get('securityGroupList');
            u.each(securityGroupList, security => {
                if (security.value === rule.remoteGroupId) {
                    rule.remoteGroupShortId = security.title;
                    rule.remoteSecurityGroupUuid = security.securityGroupUuid;
                }
            });
            if (rule.remoteGroupId) {
                rule.remoteGroupName = this.data.get('securityGroupsIdNameMap')[rule.remoteGroupId];
            }
            let newRule = Rule.fromJSON(rule);
            if (rule.protocol === 'icmp') {
                rule.portRange = '';
                newRule.portRange = '';
            }
            const {
                name,
                protocol,
                ethertype,
                securityGroupRuleId,
                portRange,
                remoteGroupId,
                remoteIP,
                remoteSecurityGroupUuid
            } = rule;
            const payload = {
                name: name?.replace(/[\t]+/g, ''),
                protocol,
                ethertype,
                securityGroupRuleId,
                portRange: u.trim(portRange),
                remoteGroupId: remoteSecurityGroupUuid,
                remoteIpPrefix: remoteSecurityGroupUuid ? '' : u.trim(remoteIP),
                direction: ruleType === 'in' ? 'ingress' : 'egress'
            };
            selectArray.push(newRule);
            originData.push(newRule);
            return payload;
        });

        let originDatasource = u.cloneDeep(this.data.get('originDatasource')) || [];
        let allRuleList = [];
        if (this.data.get('ruleType') !== 'out') {
            originDatasource = originDatasource.filter((item: any) => item.direction === 'ingress');
        } else {
            originDatasource = originDatasource.filter((item: any) => item.direction === 'egress');
        }
        originDatasource.forEach(item => {
            if (!editedRules.find(i => i.securityGroupRuleId === item.securityGroupRuleId)) {
                let newItem = Rule.fromJSON(item);
                allRuleList.push(newItem);
            }
        });

        // 先保证填写的与外部的不会重复
        for (const item of selectArray) {
            if (this.ruleIndexOfTable(item, allRuleList, -1) !== -1) {
                Notification.error('当前保存规则您已添加，请勿重复添加！');
                return;
            }
        }
        let errorTip = [];
        // 再保证填写的不会重复
        for (const index in originData) {
            let i = Number(index);
            if (this.ruleIndexOfTable(originData[index], selectArray, i) !== -1) {
                Notification.error('当前保存规则已重复，请勿重复添加！');
                errorTip.push(originData[index]);
                break;
            }
        }
        if (errorTip.length) {
            return;
        }

        const payload = {
            securityGroupUuid: this.data.get('id'),
            securityGroupRules: editedRules
        };
        this.data.set('isEdittingAll', true);
        this.$http
            .batchUpdateSecurity(payload)
            .then((res: any) => {
                if (res?.success) {
                    Notification.success('编辑规则成功');
                    this.getRemoteSgRulesQuota(); // 刷新下当前可用配额
                    this.data.set('isEditAll', false);
                    this.data.set('editRow', []);
                    this.loadAllSecRulesData();
                    this.getDetail();
                    this.reset();
                    this.clearDisabledIndex();
                    // 恢复复选框
                    this.data.set('table.selection', {
                        mode: 'multi',
                        selectedIndex: []
                    });
                }
            })
            .finally(() => {
                this.data.set('isEdittingAll', false);
            });
    }

    handleCancelEditAll() {
        this.data.set(`portRangeErr`, []);
        this.data.set(`remoteIPErr`, []);
        const datasource = this.data.get('datasource');
        let filterDatasource = datasource;
        const ruleIpType = this.data.get('ruleIpType');
        if (ruleIpType === 4) {
            filterDatasource = datasource.filter((item: any) => item.ethertype === IpVersion.IPV4);
        } else if (ruleIpType === 6) {
            filterDatasource = datasource.filter((item: any) => item.ethertype === IpVersion.IPV6);
        }
        this.data.set('filterDatasource', filterDatasource);
        this.data.set('isEditAll', false);
        this.data.set('editRow', []);
        // 恢复复选框
        this.data.set('table.selection', {
            mode: 'multi',
            selectedIndex: []
        });
    }
    // ipv6白名单
    ipV6GatewayWhiteList() {
        this.data.splice('protocolList', [
            3,
            1,
            {
                text: 'icmp(PING_IPV4)',
                value: 'icmp_ipv4'
            },
            {
                text: 'icmp(PING_IPV6)',
                value: 'icmp_ipv6'
            }
        ]);
    }
    checkRuleQuota() {
        return this.$http.securityRuleQuota().then(result => {
            this.data.set('ruleQuota', result.total);
        });
    }
    // 获取安全组列表
    getAllSecurityGroups() {
        let param = {
            pageNo: 1,
            pageSize: 10000
        };
        if (this.data.get('vpcId')) {
            param.vpcId = this.data.get('vpcId');
        }
        return this.$http.securityListV3(param).then(sgs => {
            let result = [];
            u.each(sgs.result, group => {
                result[group.name === '默认安全组' ? 'unshift' : 'push']({
                    text: group.name,
                    value: group.securityGroupId,
                    securityGroupId: group.securityGroupId,
                    securityGroupUuid: group.id
                });
                this.data.set(`securityGroupsIdNameMap['${group.securityGroupId}']`, group.name);
            });
            this.data.set('securityGroupList', result);
        });
    }
    ruleIndexOfTable(rule: Record<string, any>, datasource: any[], ruleIndex: number) {
        let index = -1;
        u.find(datasource, function (x, i) {
            if (i !== ruleIndex && rule.equal(x)) {
                index = i;
                return true;
            }
        });
        return index;
    }
    // 创建规则
    onCreate() {
        if (this.data.get('isDetail')) {
            let ruleType = this.data.get(`ruleType`);
            let inArray = [];
            let outArray = [];
            let ingressRules = this.data.get('allInRuleDataSource');
            let egressRules = this.data.get('allOutRuleDataSource');
            ingressRules.forEach(item => {
                let newItem = Rule.fromJSON(item);
                inArray.push(newItem);
            });
            egressRules.forEach(item => {
                let newItem = Rule.fromJSON(item);
                outArray.push(newItem);
            });
            // 出站或入站规则数量
            const direction = ruleType === 'out' ? 'egress' : 'ingress';
            const originLength = this.data.get('originDatasource').filter(item => item.direction === direction).length;
            const ruleQuota = this.data.get('ruleQuota');
            const availableRuleQuota = ruleQuota - originLength; // 剩余配额
            // 嵌套安全组的配额
            const usedNestSecurityNum = this.data.get('usedNestSecNumCopy');
            const totalNestSecurityQuota = this.data.get('totalNestSecurityQuota');
            let create = new Create({
                data: {
                    open: true,
                    title: ruleType === 'in' ? '添加入站规则' : '添加出站规则',
                    vpcId: this.data.get('vpcId'),
                    ruleQuota,
                    ingressRules: inArray,
                    egressRules: outArray,
                    instance: this.data.get('instance'),
                    ruleType,
                    id: this.data.get('id'),
                    usedNestSecurityNum,
                    totalNestSecurityQuota,
                    availableRuleQuota
                }
            });
            create.on('create', () => {
                this.loadAllSecRulesData();
                this.getDetail();
                this.getRemoteSgRulesQuota();
            });
            create.attach(document.body);
        } else {
            // 隐藏复选框
            this.data.set('table.selection', {
                mode: '',
                selectedIndex: [],
                disabledIndex: []
            });
            this.data.set('selectedRule', []);
            this.data.set('isEdit', true);
            this.data.set('editType', 'add');
            let ruletype = this.data.get('ruleType');
            let baseData = {
                ethertype: IpVersion.IPV4,
                source: 'user',
                name: '暴露全部端口到公网和内网',
                protocol: 'all',
                remoteIP: 'all',
                portRange: '1-65535',
                action: 'allow'
            };
            if (ruletype === 'in') {
                this.data.unshift('table.inDatasource', baseData);
            } else {
                this.data.unshift('table.outDatasource', baseData);
            }
            this.data.set('editArray', [0]);
            this.data.set('editRow[0]', true);
            this.fire('editting', true);
        }
    }

    getRules() {
        return {
            in: this.data.get('table.inDatasource'),
            out: this.data.get('table.outDatasource')
        };
    }

    getEditRow() {
        return {
            editRow: this.data.get('editRow')
        };
    }

    checkRule() {
        let originDatasource = this.data.get('instance.result').filter(item => {
            if (this.data.get('ruleType') === 'in') {
                return item.direction === 'ingress';
            }
            return item.direction === 'egress';
        });
        let haveAllPortRule = this.haveAllPortRule();
        const dialog = new checkRule({
            data: {
                open: true,
                securityType: 'normal',
                ruleType: this.data.get('ruleType'),
                ruleDatasource: originDatasource,
                ipV6GatewayWhiteList: true,
                haveAllPortRule
            }
        });
        dialog.attach(document.body);
        dialog.on('checkConfirm', result => {
            const callback = () => {
                return this.$http
                    .securityDeleteRule({
                        securityGroupId: this.data.get('instanceDetail')?.id || this.data.get('id'),
                        securityGroupRuleIds: result
                    })
                    .then(() => {
                        dialog.dispose && dialog.dispose();
                        this.resetTable();
                    });
            };
            this.submitEditRule(callback);
        });
    }

    async leadIn() {
        let result = this.data.get('originDatasource') || [];
        let inArray = [];
        let outArray = [];
        let ingressRules = result.filter(item => item.direction === 'ingress');
        let egressRules = result.filter(item => item.direction === 'egress');
        ingressRules.forEach(item => {
            let newItem = Rule.fromJSON(item);
            inArray.push(newItem);
        });
        egressRules.forEach(item => {
            let newItem = Rule.fromJSON(item);
            outArray.push(newItem);
        });
        let leadDialog = new LeadRule({
            data: {
                open: true,
                ingressRules: inArray,
                egressRules: outArray,
                ruleQuota: this.data.get('ruleQuota'),
                securityGroupId: this.data.get('id'),
                vpcId: this.data.get('vpcId')
            }
        });
        leadDialog.attach(document.body);
        leadDialog.on('leadComplete', () => {
            this.loadAllSecRulesData();
            this.getDetail();
            this.getRemoteSgRulesQuota();
        });
    }
    leadOut() {
        let id = this.data.get('id');
        window.open('/api/network/v1/security/download?securityGroupId=' + id);
    }
    reset() {
        this.data.set('ethertypes', []);
        this.data.set('protocols', []);
        this.data.set('portRanges', []);
        this.data.set('sources', []);
        this.data.set('remoteGroupIds', []);
        this.data.set('remoteIPs', []);
        this.data.set('names', []);
        this.data.set('ids', []);
        this.data.set('changeFrom', ''); // switch弹出 dialog 取消时，开关要还原会原来的
    }
    getTip() {
        let security = this.data.get('instanceDetail') || {};
        let content = '';
        if (security.bindInstance) {
            content =
                '当前已经启用这一安全组规则，您的任何修改将在保存后立即生效。' +
                '请确认您所设置的安全规则对当前云服务器的正常服务无任何影响！';
        }
        return content;
    }
    afterSubmitResolver(callback: Function, type: string) {
        if (callback && typeof callback === 'function') {
            callback().then(() => {
                this.loadAllSecRulesData();
                this.getDetail();
                this.reset();
                this.clearDisabledIndex();
                this.resetEdit();
                this.getRemoteSgRulesQuota();
                // 恢复复选框
                this.data.set('table.selection', {
                    mode: 'multi',
                    selectedIndex: []
                });
                this.data.set('editType', '');
                this.haveAllPortRule();
                Notification.success(
                    `${this.data.get('editType') === 'add' ? '创建' : type === 'delete' ? '删除' : '修改'}成功`
                );
                if (this.data.get('allowSwitchFlag')) {
                    this.resetTable();
                    this.data.set('allowSwitchFlag', false);
                }
            });
        }
    }
    submitEditRule(callback: Function, type?: string) {
        let content = this.getTip();
        const ruleList = this.data.get('table.inDatasource');
        const selectedIndex = this.data.get('table.selection.selectedIndex');
        if (
            !content &&
            this.data.get('ruleType') === 'in' &&
            ruleList.length !== 0 &&
            ruleList.length === selectedIndex.length
        ) {
            content =
                '您未添加任何入站开放端口，将使该云服务器无法和外部进行任何通信，您仅能通过VNC功能进行管理。是否确认继续操作？';
        }
        if (content) {
            let confirm = new Confirm({
                data: {
                    open: true,
                    content
                }
            });
            confirm.attach(document.body);
            confirm.on('confirm', () => {
                this.afterSubmitResolver(callback, type);
            });
            confirm.on('close', data => this.tipDialogCancel());
        } else {
            this.afterSubmitResolver(callback, type);
        }
    }
    tipDialogCancel() {
        const change_from = this.data.get('changeFrom');
        if (change_from === 'switch') {
            this.data.set('allowAllPort', !this.data.get('allowAllPort'));
        }
        this.data.set('changeFrom', '');
    }
    ruleIpTypeChange(e: Event) {
        this.data.set('ruleIpType', e.value);
        if (this.data.get('isDetail')) {
            // fix：切换页码为1
            this.data.set(`pager.page`, 1);
            this.getDetail();
        }
    }
    // 改变页数
    onPagerChange(e: Event) {
        this.data.set(`pager.page`, e.value.page);
        this.getDetail();
    }

    // 改变每页显示个数
    onPagerSizeChange(e: Event) {
        this.data.set(`pager.size`, e.value.pageSize);
        this.data.set(`pager.page`, 1);
        this.getDetail();
    }
    copyRule(row: Record<string, any>, rowIndex: number) {
        let leadDialog = new CopyRule({
            data: {
                open: true,
                formData: row,
                securityGroupId: this.data.get('id'),
                ruleType: this.data.get('ruleType'),
                securityGroupList: this.data.get('securityGroupList'),
                id: this.data.get('id'),
                securityGroupsIdNameMap: this.data.get('securityGroupsIdNameMap')
            }
        });
        leadDialog.attach(document.body);
        leadDialog.on('success', () => {
            this.loadAllSecRulesData();
            this.getDetail();
        });
    }
    loadAllSecRulesData() {
        this.data.set('allDataLoading', true);
        this.$http
            .getSecurityRules(this.data.get('id'), {pageNo: 1, pageSize: 100000}, kXhrOptions.customSilent)
            .then(res => {
                const dataSource = res.result || [];
                const allInRuleDataSource = [];
                const allOutRuleDataSource = [];
                dataSource
                    .filter((item: any) => item.direction === 'ingress')
                    .forEach((item: any) => {
                        let newItem = Rule.fromJSON(item);
                        allInRuleDataSource.push(newItem);
                    });
                dataSource
                    .filter((item: any) => item.direction === 'egress')
                    .forEach((item: any) => {
                        let newItem = Rule.fromJSON(item);
                        allOutRuleDataSource.push(newItem);
                    });
                this.data.set('originDatasource', res.result || []);
                this.data.set('allInRuleDataSource', allInRuleDataSource);
                this.data.set('allOutRuleDataSource', allOutRuleDataSource);
                this.data.set('allDataLoading', false);
            })
            .catch(e => {
                this.data.set('allDataLoading', false);
            });
    }
}
export default Processor.autowireUnCheckCmpt(RuleList);
