.security-rule-create {
    .inline-tip {
        top: 3px;
        position: relative;
        .s-tip-warning {
            justify-content: center;
            .warning_class {
                fill: #999;
            }
        }
        .s-tip:hover .s-icon path {
            fill: #2468f2 !important;
        }
    }
    .bind-tip {
        .s-icon {
            font-size: 12px;
            border: 1px solid;
        }
    }
    .ruleIpTypeLine {
        margin-bottom: 20px;
        .ruletype {
            display: inline-block;
            vertical-align: middle;
            margin-right: 8px;
        }
    }
    .s-table {
        overflow: visible;
        .s-table-container {
            overflow: visible;
            .s-table-cell-text {
                overflow: visible;
            }
        }
        .s-table-cell-protocol {
            .s-table-cell-text {
                height: 100px;
                width: 200px;
                position: relative;
                line-height: 80px;
            }
        }
    }
    .s-dialog-wrapper {
        min-width: 1200px !important;
        overflow: unset !important;
    }
    .acl_protocol_class {
        .s-selectdropdown {
            height: 96px;
        }
    }
    .tip_reverse {
        position: relative;
        .s-tip {
            border: none;
            color: #eb5252;
            .s-icon {
                top: -2px;
            }
        }
        .s-tip:hover {
            background: #e6f0ff;
        }
        color: #eb5252;
        font-size: 12px;
        top: 2px;
    }
    .tip_reverse :hover {
        color: #eb5252 !important;
    }
    .s-table-row :hover {
        .tip_reverse {
            .s-tip {
                background-color: #e6f0ff !important;
            }
        }
    }
}
