/**
 * @file network/security/editRule.js
 * <AUTHOR>
 */
import u from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';

import {
    Dialog,
    Button,
    Select,
    Input,
    Form,
    Radio
} from '@baidu/sui';
import Rule from '../util/rule';
import kCommonServices from '../util/services';
import rules from '@/pages/sanPages/utils/rule';
import {SecurityIpVersion} from '@/pages/sanPages/common/enum';
import {checkIpv6Cidr} from '@/pages/sanPages/utils/common';
import './editRule.less';

const kDefaultAllRule = new Rule('全部协议', 'all', '1-65535');
const kV6DefaultAllRule = new Rule('全部协议', 'all', '1-65535', SecurityIpVersion.IPV6);
const formateRules = self => ({
    protocol: [
        {required: true, message: '请选择协议'}
    ],
    portRange: [
        {required: true, message: '端口范围必填'},
        {
            validator(rule, value, cb) {
                let source = self.data.get('formData');
                if (value.toLowerCase() === 'all' || source.protocol === 'icmp') {
                    return cb();
                }
                var isPort = function (v) {
                    return /(^[1-9][0-9]{0,4}$)/.test(v) && v <= 65535;
                };
                if (value.indexOf('-') === -1) {
                    if (!isPort(value)) {
                        return cb('端口范围不符合规则');
                    }
                    return cb();
                }
                var array = value.split('-');
                if (!(isPort(array[0]) && isPort(array[1]) && +array[0] < +array[1]
                && (array[1] ? +array[0] !== 0 : false))) {
                    return cb('端口范围不符合规则');
                }
                return cb();
            }
        }
    ],
    remoteIP: [
        {required: true, message: 'IP范围必填'},
        {
            validator(rule, value, cb) {
                let source = self.data.get('formData');
                value = u.trim(value);
                if (source.source === 'user') {
                    var ipVersion = source.ethertype;
                    if (value.toLowerCase() === 'all') {
                        return cb();
                    }
                    if (ipVersion && (ipVersion === SecurityIpVersion.IPV6)) {
                        if (!checkIpv6Cidr(value)) {
                            return cb('IP格式不正确');
                        }
                    }
                    else {
                        var reg = new RegExp(rules.IP_CIDR);
                        if (!reg.test(value)) {
                            return cb('IP格式不正确');
                        }
                    }
                }
                return cb();
            }
        }
    ],
    name: [
        {
            validator(rule, value, cb) {
                if (value.length > 255) {
                    return cb('最多255个字符');
                }
                return cb();
            }
        }
    ]
});
const tpl = html`
<template>
    <s-dialog
        closeAfterMaskClick="{{false}}"
        width="600"
        open="{=open=}"
        title="{{title}}">
        <div class="bcc-security-edit-rule">
            <p>{{'温馨提示：普通安全组是『允许访问』的规则集合，没有『拒绝访问』的规则。'}}</p>
            <s-form
                s-ref="form"
                data="{=formData=}"
                rules="{=rules=}">
                <s-form-item
                    label="{{'规则类型：'}}"
                    prop="ethertype">
                    <s-radio-group
                        value="{=formData.ethertype=}"
                        radioType="button"
                        datasource="{{ethertypeList}}"
                    >
                    </s-radio-group>
                </s-form-item>
                <s-form-item label="{{'协议：'}}"
                    prop="protocol">
                    <s-select width="{{200}}"
                        datasource="{{protocolList}}"
                        value="{=formData.protocol=}"
                        on-change="protocolChange($event)"
                        track-id="ti_vpc_security_rule_{{ruleType}}_protocol"
                        track-name="添加{{ruleTypeLabel}}站规则/协议" />
                </s-form-item>
                <s-form-item label="{{'端口范围：'}}"
                    prop="portRange">
                    <s-input width="{{200}}"
                        value="{=formData.portRange=}"
                        disabled="{{portRangeDisabled}}"
                        on-input="portRangeChange($event)"
                        track-id="ti_vpc_security_rule_{{ruleType}}_port"
                        track-name="添加{{ruleTypeLabel}}站规则/端口范围" />
                </s-form-item>
                <s-form-item label="{{sourceLabel}}"
                    prop="source">
                    <s-select width="{{200}}"
                        datasource="{{sourceList}}"
                        value="{=formData.source=}"
                        on-change="sourceChange($event)"
                        track-id="ti_vpc_security_rule_{{ruleType}}_source"
                        track-name="添加{{ruleTypeLabel}}站规则/source" />
                </s-form-item>
                <s-form-item
                    s-if="formData.source==='user'"
                    prop="remoteIP"
                    class="out-formitem">
                    <s-input width="{{200}}"
                        value="{=formData.remoteIP=}"
                        track-id="ti_vpc_security_rule_{{ruleType}}_port"
                        track-name="添加{{ruleTypeLabel}}站规则/{{sourceLabel}}/{{sourceLabelCh}}" />
                    <p class="rule-item-note" s-if="formData.ethertype === 'IPv4'">
                        {{'例如：**********/24，或**********，或all（0.0.0.0/0）。'}}
                    </p>
                    <p class="rule-item-note" s-else>{{'例如：1::/64，或1::1，或all（::/0）。'}}</p>
                </s-form-item>
                <s-form-item s-else class="out-formitem">
                    <s-select width="{{200}}"
                        filterable
                        datasource="{{securityGroupList}}"
                        value="{=formData.remoteGroupId=}"
                        track-id="ti_vpc_security_rule_{{ruleType}}_remote_group_id"
                        track-name="添加{{ruleTypeLabel}}站规则/source/安全组" />
                </s-form-item>
                <s-form-item label="{{'备注：'}}" prop="name">
                    <s-input width="{{200}}"
                        value="{=formData.name=}"
                        track-id="ti_vpc_security_rule_{{ruleType}}_port"
                        track-name="添加{{ruleTypeLabel}}站规则/备注" />
                </s-form-item>
                <span class="rule-item-note out-formitem">{{'0-255个字符'}}</span>
            </s-form>
            <div class="common-services">
                <h5>{{'快捷模板'}}</h5>
                <ul id="commonServices" s-for="item,index in commonServices">
                    <li class="{{item.className}}">
                        <a data-index="{{index}}"
                            title="{{item.name}}"
                            class="{{['ui-button',item.key===currentService?'skin-primary-button':'']}}"
                            on-click="clickTpl(item,index)"
                            data-track-id="ti_vpc_security_rule_{{ruleType}}_template"
                            data-track-name="添加{{ruleTypeLabel}}站规则/快捷模版/{item.name}">{{item.key}}</a>
                    </li>
                </ul>
            </div>
        </div>
        <div slot="footer">
            <s-button on-click="close">{{'取消'}}</s-button>
            <s-button skin="primary" on-click="confirm">{{'确定'}}</s-button>
        </div>
    </s-dialog>
</template>
`;
export default class CreateRule extends Component {
    static template = tpl;
    static components = {
        's-form': Form,
        's-form-item': Form.Item,
        's-input': Input,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-dialog': Dialog,
        's-button': Button,
        's-select': Select
    };
    static computed = {
        commonServices() {
            /* eslint-disable */
            var classNames = {
                cs_c1: ['PING', 'HTTP', 'FTP', 'DNS', 'SNMP'],
                cs_c2: ['SSH', 'HTTPS', 'RDP', 'POP3', 'SMTP'],
                cs_c3: ['MYSQL', 'SQL Server', 'SNMP Trap']
            };
            var services = null;
            classNames.cs_c1 = classNames.cs_c1.slice(1);
                classNames.cs_c3 = classNames.cs_c3.concat(['PING_IPV4', 'PING_IPV6']);
                classNames = {
                    cs_c1: ['HTTP', 'FTP', 'DNS', 'SNMP', 'SSH'],
                    cs_c2: ['HTTPS', 'RDP', 'POP3', 'SMTP'],
                    cs_c3: ['PING_IPV4', 'PING_IPV6', 'MYSQL', 'SQL Server', 'SNMP Trap']
                };
                services = u.filter(kCommonServices,
                    function (item) {
                        return item.key !== 'PING';
                    });
            /*eslint-enable*/
            u.each(services, function (element) {
                u.find(classNames, function (v, k) {
                    if (v.indexOf(element.key) !== -1) {
                        element.className = k;
                    }
                });
            });
            return services.slice(3);
        },
        sourceList() {
            var result = [
                {text: '源IP', value: 'user'},
                {text: '安全组', value: 'system'}
            ];
            if (this.data.get('ruleType') === 'out') {
                result[0].text = '目的IP';
            }
            return result;
        },
        securityGroupList() {
            let securityGroups = this.data.get('securityGroups');
            if (securityGroups) {
                return u.map(securityGroups, item => {
                    return {
                        text: item.text + `（${item.value}）`,
                        value: item.value
                    };
                });
            } else {
                return [];
            }
        },
        sourceLabel() {
            if (this.data.get('ruleType') === 'in') {
                return 'source';
            } else {
                return 'target';
            }
        },
        sourceLabelCh() {
            if (this.data.get('ruleType') === 'in') {
                return '源IP';
            } else {
                return '目的IP';
            }
        },
        ruleTypeLabel() {
            if (this.data.get('ruleType') === 'in') {
                return '入';
            } else {
                return '出';
            }
        }
    };
    initData() {
        return {
            ethertypeList: SecurityIpVersion.toArray(),
            protocolList: [
                {text: '全部协议', value: 'all'},
                {text: 'tcp', value: 'tcp'},
                {text: 'udp', value: 'udp'},
                {text: 'icmp', value: 'icmp'}
            ],
            portRangeDisabled: true,
            currentService: '',
            formData: {
                ethertype: SecurityIpVersion.IPV4,
                source: 'user',
                name: '全部协议',
                protocol: 'all',
                remoteIP: 'all',
                portRange: '1-65535'
            },
            oldProtocol: '',
            rules: formateRules(this),
            formErrors: null
        };
    }
    attached() {
        this.watch('formData.ethertype', value => {
            this.nextTick(() => this.ref('form').validateFields());
        });
    }
    protocolChange(e) {
        let value = e.value;
        if (value === 'all') {
            this.data.set('formData.portRange', '1-65535');
            this.data.set('portRangeDisabled', true);
            this.data.set('formData.name', '全部协议');
        } else if (value === 'icmp') {
            this.data.set('formData.portRange', '不涉及');
            this.data.set('portRangeDisabled', true);
            this.data.set('formData.name', '');
        } else {
            this.data.set('portRangeDisabled', false);
            this.data.set('formData.name', '');
        }
        if (this.data.get('oldProtocol') === 'icmp') {
            this.data.set('formData.portRange', '1-65535');
        }
        this.data.set('oldProtocol', value);
        this.data.set('currentService', '');
    }
    portRangeChange() {
        this.data.set('currentService', '');
    }
    sourceChange(e) {
        if (e.value === 'system') {
            this.data.set('formData.remoteGroupId', this.data.get('securityGroupList')[0].value);
        }
    }
    clickTpl(item, index) {
        let ethertype = this.data.get('formData.ethertype');
        let needToggle = item.key === this.data.get('currentService');
        let rule = needToggle ? (ethertype === SecurityIpVersion.IPV6 ? kV6DefaultAllRule : kDefaultAllRule)
        : this.data.get('commonServices')[index].rules[0];
        if (rule.name && rule.name.indexOf('IPv6') === -1
        && rule.name.indexOf('IPv4') === -1) {
            rule.ethertype = ethertype;
        }
        this.data.set('formData.protocol', rule.protocol);
        this.protocolChange({value: rule.protocol});
        this.data.set('formData.portRange', rule.portRange);
        this.nextTick(()=>{
            for (var key in this.data.get('formData')) {
                this.data.get('formData').hasOwnProperty(key)
                && this.data.set(`formData['${key}']`, rule[key]);
            }
            this.nextTick(() => this.ref('form').validateFields());
            needToggle ? this.data.set('currentService', '') : this.data.set('currentService', item.key);
        });
    }
    close() {
        this.data.set('open', false);
    }
    confirm() {
        this.ref('form').validateFields()
            .then(async () => {
                this.fire('create', this.data.get('formData'));
                this.data.set('open', false);
        });
    }
};
