export const ddcTypeColumns = {
    name: 'ddcType',
    label: '类型',
    width: 100,
    render(item) {
        const TYPE = {
            redis: 'redis',
            mysql: 'mysql'
        };
        return TYPE[item.type];
    }
};

export const ddcRemarkColumns = {
    name: 'ddcRemark',
    label: '描述',
    width: 80,
    render(item) {
        return item.remark || '-';
    }
};

export const ddcIpColumns = {
    name: 'ddcIp',
    sortable: true,
    label: '实例内网IP',
    width: 100,
    render(item) {
        return item.lbIp || '-';
    }
};

export const scsTypeColumns = {
    name: 'scsType',
    label: '类型',
    width: 100,
    render(item) {
        return item.scsType;
    }
};

export const scsIpColumns = {
    name: 'lbIp',
    sortable: true,
    label: '实例IP',
    width: 100,
    render(item) {
        return item.lbIp || '-';
    }
};

export const rdsTypeColumns = {
    name: 'remark',
    label: '实例类型',
    width: 100,
    render(item) {
        return item.remark;
    }
};

export const rdsDBColumns = {
    name: 'productType',
    label: '数据库类型',
    width: 100,
    render(item) {
        return item.productType || '-';
    }
};

export const blbTypeColumns = {
    name: 'blbType', label: '实例类型',
    width: 100,
    render(item) {
        const TYPE_MAP = {
            normal: '普通型实例',
            application: '应用型实例',
            ipv6: 'IPv6实例'
        };
        return `<span class="truncated" title="${TYPE_MAP[item.blbType]}">
                    ${TYPE_MAP[item.blbType] || '-'}
                </span>`;
    }
};

export const snicIpColumns = {
    name: 'ovip',
    label: '实例IP',
    sortable: true,
    width: 100,
    render(item) {
        return item.ovip || '-';
    }
};

export const rabbitMqColumns = {
    name: 'type',
    label: '架构类型',
    width: 100,
    render(item) {
        return item.type || '-';
    }
};