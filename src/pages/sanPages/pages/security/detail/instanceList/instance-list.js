/*
 * @description: 安全组已绑定资源列表页
 * @file: security/pages/detail/list.js
 * @author: p<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */

import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {Notification} from '@baidu/sui';
import {OutlinedPlus, OutlinedRefresh} from '@baidu/sui-icon';
const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent} = decorators;

import rules from '../../rules';
import Confirm from '@/pages/sanPages/components/confirm';
import Bind from './bind';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import {
    ddcTypeColumns,
    ddcRemarkColumns,
    ddcIpColumns,
    blbTypeColumns,
    scsTypeColumns,
    scsIpColumns,
    snicIpColumns,
    rdsTypeColumns,
    rdsDBColumns,
    rabbitMqColumns
} from './columns';
import {associationProduct} from '@/pages/sanPages/common/enum';
import testID from '@/testId';
import './style.less';

const tpl = html` <div>
    <s-biz-page title="{{title}}" class="{{klass}}">
        <div class="list-page-tb-left-toolbar" slot="tb-left">
            <s-tooltip trigger="{{disableMessage ? 'hover' : ''}}" placement="top" class="inline_class">
                <div slot="content">
                    <!--bca-disable-next-line-->
                    {{disableMessage | raw}}
                </div>
                <s-button
                    disabled="{{createDisable}}"
                    skin="primary"
                    on-click="relateInstance"
                    track-id="vpc_security_association_{{bindTxt | associationProduct}}"
                    data-test-id="${testID.security.detailBindInstance}{{type}}"
                >
                    <outlined-plus />{{bindTxt}}
                </s-button>
            </s-tooltip>
            <s-tooltip
                trigger="{{cancelRelated.disable ? 'hover' : ''}}"
                placement="top"
                class="inline_class left_class"
            >
                <div slot="content">
                    <!--bca-disable-next-line-->
                    {{cancelRelated.message | raw}}
                </div>
                <s-button
                    on-click="onRelease"
                    disabled="{{cancelRelated.disable}}"
                    data-test-id="${testID.security.detailUnbindInstance}{{type}}"
                >
                    {{'取消关联'}}</s-button
                >
            </s-tooltip>
        </div>
        <div slot="tb-right" class="inline_class">
            <s-search
                width="{{230}}"
                class="search-warp"
                value="{=keyword=}"
                placeholder="{{placeholder}}"
                on-search="onSearch"
                data-test-id="${testID.security.detailSearch}{{type}}"
            >
                <s-select
                    slot="options"
                    width="120"
                    datasource="{{keywordTypeList}}"
                    value="{=keywordType=}"
                    on-change="keywordTypeChange($event)"
                >
                </s-select>
            </s-search>
            <s-button class="s-icon-button" on-click="refresh" data-test-id="${testID.security.detailRefresh}{{type}}"
                ><outlined-refresh class="icon-class"
            /></s-button>
        </div>
        <s-table
            columns="{{table.columns}}"
            loading="{{table.loading}}"
            error="{{table.error}}"
            datasource="{{table.datasource}}"
            on-selected-change="tableSelected($event)"
            on-sort="onSort"
            selection="{=table.selection=}"
            data-test-id="${testID.security.detailTable}{{type}}"
        >
            <div slot="error">
                啊呀，出错了？
                <a href="javascript:;" on-click="refresh">{{'重新加载'}}</a>
            </div>
            <div slot="empty">
                <s-empty>
                    <div slot="action"></div>
                </s-empty>
            </div>
            <div slot="c-opt">
                <s-tooltip
                    s-if="row.associationNum<=1 && type !== 'blb'"
                    content="{{row | releaseTip}}"
                    trigger="hover"
                    placement="right"
                >
                    <s-button skin="stringfy" style="color:#999">{{'取消关联'}}</s-button>
                </s-tooltip>
                <s-button skin="stringfy" s-else on-click="onRelease('row', row)">{{'取消关联'}}</s-button>
            </div>
        </s-table>
        <s-pagination
            s-if="{{pager.total}}"
            slot="footer"
            layout="{{'total, pageSize, pager, go'}}"
            pageSize="{{pager.size}}"
            total="{{pager.total}}"
            page="{{pager.page}}"
            on-pagerChange="onPageChange"
            on-pagerSizeChange="onPageSizeChange"
        />
    </s-biz-page>
</div>`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class SecurityInstanceList extends Component {
    static components = {
        'outlined-plus': OutlinedPlus,
        'outlined-refresh': OutlinedRefresh
    };
    static filters = {
        releaseTip(item) {
            if (item.associationNum <= 1) {
                return '一个实例至少关联一个安全组，该实例只关联了一个安全组，无法进行该操作';
            } else {
                return '';
            }
        },
        associationProduct(text) {
            return associationProduct.getValueFromText(text);
        }
    };
    initData() {
        return {
            klass: ['security-instance-list'],
            vpcId: '',
            selectedItems: [],
            title: '',
            emptyText: '暂无数据',
            keywordTypeList: [
                {text: '实例名称', value: 'name', placeholder: '请输入实例名称进行搜索'},
                {text: '实例ID', value: 'instanceId', placeholder: '请输入实例ID进行搜索'},
                {text: '实例内网IP', value: 'internalIp', placeholder: '请输入实例内网IP进行搜索'},
                {text: '实例公网IP', value: 'publicIp', placeholder: '请输入实例公网IP进行搜索'}
            ],
            placeholder: '请输入实例名称进行搜索',
            keywordType: 'name',
            keyword: '',
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                columns: [
                    {
                        name: 'name',
                        label: '实例名称/ID',
                        sortable: true,
                        width: 100,
                        render(item) {
                            let name = u.escape(item.name) || u.escape(item.scsName);
                            let id =
                                u.escape(item.id) ||
                                u.escape(item.eniId) ||
                                u.escape(item.blbShortId) ||
                                u.escape(item.instanceId) ||
                                u.escape(item.shortId);
                            return `<span class="truncated" title="${name}" data-testid="${testID.security.detailTableName}{{rowIndex}}">
                                        ${name}
                                    </span>
                                    </br><span class="truncated" title="${id}">${id}</span>`;
                        }
                    },
                    {
                        name: 'internalIp',
                        label: '实例IP',
                        sortable: true,
                        width: 120,
                        render(item) {
                            if (item.eniId) {
                                let result = [];
                                u.each(item.ips, (data, index) => {
                                    if (index <= 2) {
                                        let ipData = FLAG.NetworkSupportEip
                                            ? (data.eip || '-') +
                                              '（公）/' +
                                              '&nbsp;&nbsp;' +
                                              (data.privateIp || '-') +
                                              '（内）'
                                            : (data.privateIp || '-') + '（内）';
                                        result.push(ipData);
                                    }
                                });

                                if (item.ips && item.ips.length > 3) {
                                    result.push('...');
                                }

                                return result.join('<br>');
                            } else if (item.blbId) {
                                let ipv6Data = FLAG.NetworkSupportEip
                                    ? (item.publicIp || '-') + '（公）/' + (item.internalIp || '-') + '（内）'
                                    : (item.internalIp || '-') + '（内）';
                                if (item.blbType === 'ipv6') {
                                    ipv6Data = FLAG.NetworkSupportEip ? '-(公)' + '<br>' + '-(内)' : '-(内)';
                                }
                                return ipv6Data;
                            } else if (item.clusterId && item.lbInfos) {
                                let gaiaDbIps = [];
                                u.each(item.lbInfos, data => {
                                    gaiaDbIps.push(data.lbIp);
                                });
                                return gaiaDbIps.join('<br>');
                            } else {
                                return FLAG.NetworkSupportEip
                                    ? (item.eip || '-') + '（公）/' + (item.internalIp || '-') + '（内）'
                                    : (item.internalIp || '-') + '（内）';
                            }
                        }
                    },
                    {
                        name: 'ipv6',
                        label: 'IPv6 IP',
                        width: 100,
                        render(item) {
                            return item.ipv6 || '-';
                        }
                    },
                    {
                        name: 'desc',
                        label: '描述',
                        width: 80,
                        render(item) {
                            let desc = u.escape(item.desc) || u.escape(item.description);
                            return desc || '-';
                        }
                    },
                    {
                        name: 'opt',
                        label: '操作',
                        width: 80
                    }
                ],
                datasource: []
            },
            order: {
                orderBy: 'createTime',
                order: 'desc'
            },
            pager: {
                size: 10,
                page: 1,
                total: 0
            },
            cancelRelated: {
                disable: false
            },
            bindTxt: '关联云服务器',
            blbUseSecurityWhite: false
        };
    }

    static computed = {
        createDisable() {
            let type = this.data.get('context').type;
            let securityInSecurity = this.data.get('securityInSecurity');
            return (type === 'blb' || type === 'snic') && securityInSecurity;
        },
        disableMessage() {
            let type = this.data.get('context').type;
            let securityInSecurity = this.data.get('securityInSecurity');
            if ((type === 'blb' || type === 'snic') && securityInSecurity) {
                if (type === 'blb') return '当前安全组规则中存在嵌套安全组，不允许关联负载均衡';
                return '当前安全组规则中存在嵌套安全组，不允许关联服务网卡';
            }
        }
    };

    inited() {
        const type = this.data.get('context').type;
        this.data.set('type', type); // san 模版支持直接取props，react需要重新设置下data
        if (type === 'blb' || type === 'snic') {
            this.checkSecurity();
        }
        // 设置不同实例id
        let idMap = {
            bcc: 'instanceId',
            bbc: 'instanceId',
            eni: 'eniId',
            blb: 'blbShortId',
            ddc: 'id',
            snic: 'shortId',
            scs: 'id',
            rds: 'shortId',
            rabbitmq: 'instanceId'
        };
        let instanceId = idMap[type] || 'id';
        this.data.splice('table.columns', [
            0,
            1,
            {
                name: 'name',
                label: '实例名称/ID',
                sortable: true,
                width: 100,
                render(item) {
                    let name = u.escape(item.name) || u.escape(item.scsName);
                    let id = u.escape(item[instanceId]) || u.escape(item.clusterId);
                    return `<span class="truncated" title="${name}">
                            ${name}
                        </span>
                        </br><span class="truncated" title="${id}">${id}</span>`;
                }
            }
        ]);
        let title = '关联云服务器';
        if (type === 'eni') {
            title = '关联弹性网卡';
            this.data.splice('table.columns', [2, 1]);
        } else if (type === 'snic') {
            title = '关联服务网卡';
            this.data.splice('table.columns', [2, 1]);
            this.data.splice('table.columns', [1, 1, snicIpColumns]);
            this.data.pop('keywordTypeList');
        } else if (type === 'bbc') {
            title = '关联弹性裸金属服务器';
            this.data.splice('table.columns', [2, 1]);
        } else if (type === 'blb') {
            title = '关联负载均衡';
            this.data.splice('table.columns', [1, 0, blbTypeColumns]);
        } else if (type === 'ddc') {
            title = '关联云数据库专属集群';
            this.data.splice('table.columns', [1, 0, ddcTypeColumns]);
            this.data.splice('table.columns', [2, 1, ddcIpColumns]);
            this.data.splice('table.columns', [3, 2]);
            this.data.splice('table.columns', [3, 0, ddcRemarkColumns]);
            this.data.pop('keywordTypeList');
        } else if (type === 'scs') {
            title = _('关联云数据库 Redis');
            this.data.splice('table.columns', [1, 0, scsTypeColumns]);
            this.data.splice('table.columns', [2, 1, scsIpColumns]);
            this.data.splice('table.columns', [3, 2]);
        } else if (type === 'rds') {
            title = _('关联云数据库RDS');
            this.data.splice('table.columns', [1, 0, rdsTypeColumns]);
            this.data.splice('table.columns', [2, 1, rdsDBColumns]);
        } else if (type === 'rabbitmq') {
            title = _('关联消息服务 for RabbitMQ');
            this.data.splice('table.columns', [1, 2]);
            this.data.splice('table.columns', [1, 0, rabbitMqColumns]);
            this.data.splice('keywordTypeList', [2, 2]);
            this.data.push('keywordTypeList', {text: '架构类型', value: 'type', placeholder: '请输入架构类型进行搜索'});
        } else if (type === 'gaiadb') {
            title = _('关联云数据库GaiaDB-S');
            this.data.set(
                'table.columns',
                this.data.get('table.columns').map(item => {
                    if (item.name === 'name') {
                        item.label = '集群名称/ID';
                    }
                    if (item.name === 'internalIp') {
                        item.label = '集群访问入口IP';
                        item.sortable = false;
                    }
                    return item;
                })
            );
            this.data.splice('table.columns', [2, 1]);
            this.data.splice('keywordTypeList', [0, 4]);
            this.data.set('placeholder', '请输入集群名称进行搜索');
            this.data.set('keywordTypeList', [
                {text: '集群名称', value: 'name', placeholder: '请输入集群名称进行搜索'},
                {text: '集群ID', value: 'instanceId', placeholder: '请输入集群ID进行搜索'},
                {text: '集群内网IP', value: 'internalIp', placeholder: '请输入集群内网IP进行搜索'},
                {text: '集群公网IP', value: 'publicIp', placeholder: '请输入集群公网IP进行搜索'}
            ]);
        }
        if (!FLAG.NetworkSupportEip) {
            let keywordTypeList = this.data.get('keywordTypeList');
            this.data.set(
                'keywordTypeList',
                keywordTypeList.filter(item => item.value !== 'publicIp')
            );
        }
        this.data.set('bindTxt', title);
        this.data.set('title', title);
        let {cancelRelated} = checker.check(rules, [], 'cancelRelated');
        this.data.set('cancelRelated', cancelRelated);
    }

    keywordTypeChange(e) {
        this.data.get('keywordTypeList').filter(item => {
            if (item.value === e.value) {
                this.data.set('placeholder', item.placeholder);
            }
        });
    }

    onPageChange(e) {
        this.data.set('pager.page', e.value.page);
        this.data.set('table.selection.selectedIndex', []);
        this.loadPage();
    }

    onPageSizeChange(e) {
        this.data.set('pager.size', e.value.pageSize);
        if (this.data.get('pager.page') === 1) {
            this.loadPage();
        } else {
            this.data.set('pager.page', 1);
        }
    }

    onSort(e) {
        let {value} = e;
        // 兼容内网Ip排序参数
        if (value.orderBy === 'ovip' || value.orderBy === 'ddcIp' || value.orderBy === 'lbIp') {
            value.orderBy = 'internalIp';
        }
        this.data.set('order', value);
        this.loadPage();
    }

    tableSelected(e) {
        this.data.set('selectedItems', e.value.selectedItems);
        let {cancelRelatedBlb, cancelRelated} = checker.check(rules, e.value.selectedItems);
        if (this.data.get('context').type === 'blb') {
            this.data.set('cancelRelated', cancelRelatedBlb);
        } else {
            this.data.set('cancelRelated', cancelRelated);
        }
    }

    refresh() {
        this.resetTable();
        return this.loadPage();
    }

    // 重置列表
    resetTable() {
        this.data.set('pager.page', 1);
        this.data.set('table.selection.selectedIndex', []);
    }

    onRelease(row, data) {
        let instanceUuids = [];
        let items = null;
        let type = this.data.get('context').type;
        if (row === 'row') {
            items = [data];
        } else {
            items = this.data.get('selectedItems');
        }
        if (type === 'eni') {
            instanceUuids = items.map(item => item.eniUuid);
        } else if (type === 'blb') {
            instanceUuids = items.map(item => item.blbId);
        } else if (type === 'snic') {
            instanceUuids = items.map(item => item.shortId);
        } else if (type === 'scs' || type === 'rabbitmq' || type === 'ddc') {
            instanceUuids = items.map(item => item.lbId);
        } else if (type === 'gaiadb') {
            instanceUuids = items.map(item => item.clusterId);
        } else if (type === 'rds') {
            instanceUuids = items.map(item => {
                if (item.type === 'blb') {
                    return item.lbId;
                } else if (item.type === 'snic') {
                    return item.snicId;
                }
            });
        } else {
            instanceUuids = items.map(item => item.instanceUuid);
        }
        let securityGroupUuids = [this.data.get('context').id];
        let confirm = new Confirm({
            data: {
                open: true,
                content: '确定取消关联？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            const params = {
                instanceType: type.toUpperCase(),
                instanceUuids,
                securityGroupUuids
            };
            if (type === 'snic') {
                params.vpcUuid = this.data.get('context').vpcId;
            }
            this.$http.securityBatchUnbindInstance(params).then(() => {
                this.refresh();
                Notification.success('成功取消关联');
            });
        });
    }

    relateInstance() {
        let bind = new Bind({
            data: {
                open: true,
                type: this.data.get('context').type,
                vpcId: this.data.get('context').vpcId,
                id: this.data.get('context').id
            }
        });
        bind.attach(document.body);
        bind.on('bind', () => {
            this.loadPage();
            this.fire('update');
        });
    }

    async onSearch() {
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    getSearchCriteria() {
        let {order, pager} = this.data.get('');
        let payload = {pageNo: pager.page, pageSize: pager.size, securityGroupId: this.data.get('context').id};
        let keyword = this.data.get('keyword').trim();
        if (keyword) {
            payload.keywordType = this.data.get('keywordType');
            payload.keyword = keyword;
        }
        payload.vpcId = this.data.get('context').vpcId;
        return u.extend({}, payload, order);
    }

    async loadPage() {
        this.data.set('table.loading', true);
        let payload = this.getSearchCriteria();
        let type = this.data.get('context').type;
        let result = null;
        try {
            payload.serverType = type.toUpperCase();
            result = await this.$http.getBindInstanceList(payload);
            this.data.set('table.datasource', result.result);
            this.data.set('pager.total', result.totalCount);
            this.data.set('table.loading', false);
        } catch (err) {
            this.data.set('table.loading', false);
        }
    }

    attached() {
        this.loadPage();
    }

    checkSecurity() {
        this.$http.getUseNestedSgRule({securityGroupUuids: [this.data.get('context').id]}).then(res => {
            this.data.set('securityInSecurity', res.value);
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(SecurityInstanceList));
