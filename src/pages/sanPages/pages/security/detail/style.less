.vpc-security-detail {
    min-height: 100%;
    width: 100%;
    position: relative;
    padding: 12px 16px;
    .vpc-security-head {
        line-height: 50px;
        .backbox {
            height: 28px;
            line-height: 28px;
            border: 1px solid #ccc;
            outline: none;
            font-size: 12px;
            font-weight: 400;
            cursor: pointer;
            text-align: center;
            vertical-align: middle;
            color: #000;
            background-color: #fff;
            font-family:
                Microsoft Yahei,
                微软雅黑,
                Tahoma,
                Arial,
                Helvetica,
                STHeiti;
            display: inline-block;
            width: 20px;
            .iconfont {
                font-size: 12px;
            }
        }
        span {
            margin-left: 5px;
            vertical-align: middle;
        }
        span:last-child {
            position: relative;
            top: 1px;
        }
    }
    .content-box {
        .alert-tip {
            margin: 12px 0px;
        }
        .ruletype,
        .allow-all-port,
        .tool-tip {
            margin-bottom: 12px;
        }
        h4 {
            display: block;
            color: #151b26;
            line-height: 24px;
            font-weight: 500;
            font-size: 16px;
            margin-bottom: 8px;
        }
        .cell {
            width: 24%;
            display: inline-block;
            margin-bottom: 8px;
            .cell-title {
                display: inline-block;
                vertical-align: top;
                color: #5e626a;
                margin-right: 16px;
                width: 72px;
            }
            .cell-content {
                display: inline-block;
                color: #151a26;
                max-width: 70%;
                word-break: break-all;
                position: relative;
                .truncated {
                    max-width: 100%;
                }
            }
            .icon-edit,
            .icon-copy {
                color: #2468f2;
                font-size: 12px;
                margin-left: 10px;
            }
        }
        .legend-wrap {
            margin: 0px 0px 8px 0px;
            .s-legend-highlight {
                &::before {
                    display: none;
                }
            }
            .advance-icon {
                margin-left: 10px;
                transition: all 0.3s ease;
                transform: rotate(0);
            }
            .advance-icon.actived {
                transform: rotate(180deg);
            }
        }
    }
    .s-table {
        top: 11px;
        margin: 0 !important;
        .s-table-thead {
            tr > th {
                position: sticky;
                top: 0;
            }
        }
        .s-table-body {
            max-height: calc(~'100vh - 282px');
            // overflow: auto;
        }
        .s-table-empty {
            max-height: calc(~'100vh - 564px');
            overflow: auto;
        }
    }
    .security-rule-list {
        .s-pagination {
            margin-top: 20px;
            display: flex;
            justify-content: end;
        }
    }
}
.security-instance-list {
    .s-dialog-wrapper {
        overflow: unset !important;
    }
    padding: 24px;
    .s-biz-page-header {
        border-bottom: none !important;
        margin: 0 !important;
        display: inline !important;
    }
    .s-biz-page-title {
        h2 {
            display: block !important;
            height: 24px !important;
            color: #151b26 !important;
            line-height: 24px !important;
            font-weight: 500 !important;
            margin-bottom: 16px !important;
            font-size: 16px !important;
        }
    }
    .s-biz-page-content {
        margin: 0 !important;
    }
    .s-biz-page-tb-left,
    .s-biz-page-tb-right {
        .inline_class {
            display: inline-flex !important;
        }
    }
    .toolbar-line {
        margin-bottom: 10px;
        overflow: auto;
        .selectTip {
            color: #999;
            display: inline-block;
            padding-top: 8px;
        }
        .opts {
            display: inline-block;
            float: right;
            .s-search {
                margin-left: 8px;
            }
        }
        .search-err {
            margin-left: 156px;
            color: #f33e3e;
        }
    }
    .s-pagination {
        display: inline-block;
        margin-top: 16px;
        float: right;
    }
    .s-biz-page-footer {
        zoom: 1;
        padding-bottom: 16px !important;
        margin-top: 0px !important;
    }
    .s-table {
        .s-table-body {
            max-height: calc(~'100vh - 368px');
            overflow: auto;
        }
    }
    .s-alert {
        height: auto;
        margin: 0px 0px 8px;
    }
}

.security-detail-main-wrap {
    width: 100%;
    .app-tab-page {
        padding: 0;
        background: #f7f7f7 !important;
        .bui-tab-header {
            border-right: 1px solid #ebebeb;
            border-bottom: none;
        }
        .skin-accordion {
            min-height: 540px !important;
        }
        .bui-tab-nav-item {
            min-width: 140px;
            width: auto !important;
        }
    }
    .instance-not-found-class {
        height: 100%;
    }
    .s-detail-page-content {
        margin: 0;
        .app-tab-page {
            padding: 16px;
            border-radius: 6px;
            background: #fff;
            .bui-tab {
                border-radius: 6px;
                border: none;
            }
        }
    }
    .bui-tab-header {
        border-bottom: none !important;
    }
    .space-header {
        height: 50px;
        display: flex;
        align-items: center;
        .status {
            margin-left: 10px;
        }
    }
    .backbox {
        margin-right: 5px;
        font-size: 16px;
    }
    .title_class {
        display: flex;
        align-items: center;
        .iconfont {
            font-size: 14px;
            color: #84868c;
        }
        .instance-name {
            padding-left: 16px;
        }
    }
    .search-err {
        color: #f33e3e;
        margin-left: 164px;
    }
}
