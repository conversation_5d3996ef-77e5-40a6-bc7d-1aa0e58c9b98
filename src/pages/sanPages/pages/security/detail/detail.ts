/**
 * @file security/pages/detail/detail.js
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {Notification} from '@baidu/sui';
import {OutlinedEditingSquare} from '@baidu/sui-icon';

import RuleList from '../components/ruleList';
import Rule from '@/pages/sanPages/utils/rule';
import {kXhrOptions} from '@/pages/sanPages/utils/helper';
import testID from '@/testId';
import './style.less';

const {invokeSUI, invokeSUIBIZ, invokeComp, template} = decorators;
/* eslint-disable */
const tpl = html` <div>
    <div class="{{klass}}">
        <div class="content-box">
            <h4>基本信息</h4>
            <!--<s-app-legend class="legend-wrap" label="基本信息">
                <s-icon
                    class="{{showAdvance ? 'advance-icon actived' : 'advance-icon'}}"
                    name="xialajiantou"
                    slot="extra"
                    on-click="handleShowBaseInfo"
                ></s-icon>
            </s-app-legend>-->
            <div class="cell">
                <div class="cell-title">{{'所在网络：'}}</div>
                <div class="cell-content">{{instance.vpcName}}</div>
            </div>
            <div class="cell">
                <div class="cell-title">{{'安全组名称：'}}</div>
                <div class="cell-content" ref="name">
                    <span class="truncated" data-testid="${testID.security.detailSecurityName}">{{instance.name}}</span>
                    <edit-popover value="{=instance.name=}" rule="{{nameRule}}" on-edit="updateName">
                        <outlined-editing-square s-if="!!!instance.defaultSecurityGroup" color="#2468f2" />
                    </edit-popover>
                </div>
            </div>
            <div class="cell">
                <div class="cell-title">{{'安全组ID：'}}</div>
                <div class="cell-content">{{instance.securityGroupShortId}}</div>
                <s-clip-board
                    class="name-icon"
                    text="{{instance.securityGroupShortId}}"
                    data-test-id="${testID.security.detailCopyId}"
                />
            </div>
            <div class="cell">
                <div class="cell-title">{{'描述：'}}</div>
                <div class="cell-content">
                    <span class="truncated">{{instance.description}}</span>
                    <edit-popover value="{=instance.description=}" rule="{{descRule}}" on-edit="updateDesc">
                        <outlined-editing-square color="#2468f2" />
                    </edit-popover>
                </div>
            </div>
        </div>
        <div class="content-box">
            <h4>端口设置</h4>
            <rule-list
                rules="{{instance.rules}}"
                id="{{id}}"
                instanceDetail="{{instance}}"
                vpcId="{{vpcId}}"
                isDetail="{{true}}"
                accessOpt="{{accessOpt}}"
            ></rule-list>
        </div>
    </div>
</div>`;

/* eslint-enable */
@template(tpl)
@invokeComp('@edit-popover')
@invokeSUI
@invokeSUIBIZ
class SecurityDetail extends Component {
    static components = {
        'outlined-editing-square': OutlinedEditingSquare,
        'rule-list': RuleList
    };
    initData() {
        return {
            klass: ['vpc-security-detail'],
            instanceName: {
                value: '',
                error: true,
                visible: false
            },
            instanceDesc: {
                value: '',
                error: true,
                visible: false
            },
            instance: {},
            accessOpt: {
                addSgRule: {
                    disabled: false,
                    message: ''
                },
                deleteSgRule: {
                    disabled: false,
                    message: ''
                },
                updateSgRule: {
                    disabled: false,
                    message: ''
                }
            },
            nameRule: Rule.NAME_SUPPORT_CHINESE,
            descRule: Rule.DETAIL_EDIT.DESC,
            showAdvance: false
        };
    }
    inited() {
        this.getIamQuery();
        this.data.set('id', this.data.get('context').id);
        this.data.set('vpcId', this.data.get('context').vpcId);
        this.data.set('instance', this.data.get('context').instance);
    }
    updateName(value: string) {
        this.$http
            .updateSecurityField({
                id: this.data.get('instance.id'),
                vpcId: this.data.get('instance.vpcId'),
                name: value
            })
            .then(() => {
                Notification.success('修改成功');
                this.data.set('instance.name', value);
                this.data.get('context')?.updateName();
            });
    }

    updateDesc(value: string) {
        this.$http
            .updateSecurityField({
                id: this.data.get('instance.id'),
                vpcId: this.data.get('instance.vpcId'),
                desc: value
            })
            .then(() => {
                Notification.success('修改成功');
                this.data.set('instance.description', value);
            });
    }

    getIamQuery() {
        let queryId = null;
        const id = this.data.get('context').id;
        const securityGroupId = this.data.get('context').securityGroupId;
        queryId = securityGroupId ? securityGroupId : id;
        if (queryId) {
            const interfaceNames = ['addSgRule', 'updateSgRule', 'deleteSgRule'];
            this.$http
                .getInterfaceIam({
                    id: queryId,
                    interfaceNames
                })
                .then(res => {
                    const {requestId, masterAccount, permissionRespMap} = res;
                    if (!requestId && !masterAccount) {
                        const accessOpt = {...this.data.get('accessOpt')};
                        const accessOptMapText = {
                            addSgRule: '添加',
                            deleteSgRule: '删除',
                            updateSgRule: '编辑'
                        };
                        _.each(interfaceNames, item => {
                            if (!permissionRespMap[item].interfacePermission) {
                                accessOpt[item].disabled = true;
                                accessOpt[item].message =
                                    `您没有${accessOptMapText[item]}安全组规则权限，请联系主用户添加。`;
                            }
                        });
                        this.data.set('accessOpt', accessOpt);
                    }
                });
        }
    }
    handleShowBaseInfo() {
        let showAdvance = this.data.get('showAdvance');
        this.data.set('showAdvance', !showAdvance);
    }
}
export default San2React(Processor.autowireUnCheckCmpt(SecurityDetail));
