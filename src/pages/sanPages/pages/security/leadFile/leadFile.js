/**
 * @file network/security/leadFile.js
 * <AUTHOR>
 */
import u from 'lodash';
import {defineComponent} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Form, Button, Table, Icon, Progress, Tooltip, Pagination} from '@baidu/sui';
import {Tip, Empty} from '@baidu/sui-biz';
import {checkIpv6Cidr} from '@/pages/sanPages/utils/common';
import regs from '@/pages/sanPages/utils/rule';
import Rule from '../util/rule';
import SheetJS from './SheetJS';
import './leadFile.less';
const rules = {
    checkDirection(direction) {
        if (direction === 'ingress' || direction === 'egress') {
            return true;
        }
        return false;
    },
    checkProtocol(protocol) {
        if (['all', 'tcp', 'udp', 'icmp'].includes(protocol?.toLowerCase())) {
            return true;
        }
        return false;
    },
    checkPort(portRange) {
        let port = portRange.split('-');
        if (
            port.length === 1 &&
            ((Number(port[0]) >= 1 && Number(port[0]) <= 65535) || ['ALL', 'all'].includes(port[0]))
        ) {
            return true;
        } else if (
            port.length === 2 &&
            Number(port[0]) >= 1 &&
            Number(port[0]) <= 65535 &&
            Number(port[1]) >= 1 &&
            Number(port[1]) <= 65535 &&
            Number(port[0]) < Number(port[1])
        ) {
            return true;
        }
        return false;
    },
    checkEthertype(ethertype) {
        if (ethertype === 'IPv4' || ethertype === 'IPv6') {
            return true;
        }
        return false;
    },
    checkSourceCidr(ethertype, source) {
        source = source?.trim();
        if (ethertype === 'IPv4') {
            var reg = new RegExp(regs.IP_CIDR);
            if (!reg.test(source)) {
                return false;
            }
            return true;
        } else if (ethertype === 'IPv6') {
            if (!checkIpv6Cidr(source)) {
                return false;
            }
            return true;
        }
        return false;
    },
    checkIsExist(allRules, rule, type) {
        let result = true;
        for (let i = 0; i < allRules.length; i++) {
            let source = '';
            if (!allRules[i].edit) {
                if (allRules[i].source === 'user') {
                    source =
                        allRules[i].remoteIP === 'all'
                            ? allRules[i].ethertype === 'IPv4'
                                ? '0.0.0.0/0'
                                : '::/0'
                            : u.escape(allRules[i].remoteIP);
                } else {
                    let id = allRules[i].remoteGroupShortId || allRules[i].remoteGroupId;
                    source = u.escape(allRules[i].remoteGroupName) + (id ? '(' + u.escape(id) + ')' : '');
                }
            } else {
                source = allRules[i].source;
            }
            if (
                type === rule.direction?.trim() &&
                allRules[i].protocol === rule.protocol?.trim() &&
                (allRules[i].portRange === '不涉及' || allRules[i].portRange === rule.portRange?.trim()) &&
                allRules[i].ethertype === rule.ethertype?.trim() &&
                source === rule.source?.trim()
            ) {
                result = false;
                break;
            }
        }
        return result;
    }
};
function renderItem(item, key) {
    if (
        (key === 'direction' && !rules.checkDirection(item.direction)) ||
        (key === 'protocol' && !rules.checkProtocol(item.protocol)) ||
        (key === 'portRange' && item.protocol !== 'icmp' && !rules.checkPort(item.portRange)) ||
        (key === 'ethertype' && !rules.checkEthertype(item.ethertype)) ||
        (key === 'source' && !rules.checkSourceCidr(item.ethertype, item.source))
    ) {
        return '<span class="table-wrong">' + u.escape(item[key]) + '</span>';
    }
    return u.escape(item[key]);
}

const tableSchema = [
    {
        name: 'direction',
        label: '方向',
        render(item, key) {
            return renderItem(item, key);
        }
    },
    {
        name: 'protocol',
        label: '协议',
        render(item, key) {
            return renderItem(item, key);
        }
    },
    {
        name: 'portRange',
        label: '端口',
        render(item, key) {
            return renderItem(item, key);
        }
    },
    {
        name: 'ethertype',
        label: '类型',
        render(item, key) {
            return renderItem(item, key);
        }
    },
    {
        name: 'source',
        label: '来源/目的',
        render(item, key) {
            return renderItem(item, key);
        }
    },
    {
        name: 'name',
        label: '备注',
        render(item, key) {
            return renderItem(item, key);
        }
    },
    {
        name: 'status',
        label: '检查',
        render(item, key) {
            return renderItem(item, key);
        }
    }
];
const template = html`
    <div>
        <s-dialog
            width="600"
            class="security-lead-file"
            closeAfterMaskClick="{{false}}"
            open="{=open=}"
            title="{{'导入规则'}}"
        >
            <div class="tip-grey">{{quotaTips}}</div>
            <s-form>
                <s-form-item label="{{'选择文件：'}}">
                    <xui-sheetjs s-ref="upload" on-upload="uploadEvent" statusShow="{{false}}"> </xui-sheetjs>
                    <div s-if="{{uploading || fileName || uploadSuccess || uploadFail}}" class="upload-message">
                        <div class="upload-file-name">
                            <s-icon name="link"></s-icon>
                            <div s-if="{{uploading}}" class="upload-progress">
                                <s-progress value="{{progressValue}}" width="80"></s-progress>
                            </div>
                            <span s-if="{{!uploading && fileName}}">{{fileName}}</span>
                        </div>
                        <span s-if="{{uploadSuccess}}" class="upload-success">{{'上传成功!'}}</span>
                        <span s-if="{{uploadFail}}" class="upload-fail">{{'文档解析失败'}}</span>
                        <s-button s-if="{{uploadFail}}" skin="stringfy" on-click="reUpload">
                            <s-icon name="bcmrefresh"></s-icon>
                            {{'重试'}}
                        </s-button>
                        <s-button class="delete-file" skin="stringfy" on-click="clearUploader">
                            <s-icon name="close"></s-icon>
                        </s-button>
                    </div>
                </s-form-item>
                <s-form-item s-if="{{fileUploaded}}">
                    {{'查看规则'}}
                    <div class="rule-message">
                        <span>{{'共' + ruleTotal + '条规则'}}</span>
                        <span s-if="{{ruleWrongTotal}}" class="wrong-rules">
                            {{'其中' + ruleWrongTotal + '条出错，请修改后重试'}}
                        </span>
                    </div>
                </s-form-item>
                <s-form-item s-if="{{ruleIsShow}}">
                    <s-table
                        columns="{{ruleList.schema}}"
                        loading="{{ruleList.loading}}"
                        datasource="{{ruleList.datasource}}"
                    >
                        <div slot="empty">
                            <s-empty>
                                <div slot="action"></div>
                            </s-empty>
                        </div>
                        <div slot="c-status" class="status_class">
                            <div class="status-warp" s-if="{{row.status}}"><s-icon name="ok-reverse"></s-icon></div>
                            <div class="status-warp" s-else>
                                <s-tip
                                    s-if="{{row.statusMessage}}"
                                    content="{{row.statusMessage}}"
                                    position="tc"
                                    inComponent
                                    class="tip_reverse"
                                >
                                    <s-icon name="fail-reverse"></s-icon>
                                </s-tip>
                                <s-icon s-else name="fail-reverse"></s-icon>
                            </div>
                        </div>
                    </s-table>
                    <s-pagination
                        class="file-tablePage"
                        layout="{{'total, pageSize, pager, go'}}"
                        pageSize="{{pager.size}}"
                        total="{{pager.total}}"
                        page="{{pager.page}}"
                        on-pagerChange="onPageChange"
                        on-pagerSizeChange="onPageSizeChange($event)"
                    />
                </s-form-item>
            </s-form>
            <div slot="footer">
                <div style="padding-left: 300px; padding-top:20px; display: inline-block;">
                    <s-button size="larger" on-click="leadCancel"> {{'取消'}} </s-button>
                    <s-tooltip
                        content="{{limitDisableMsg}}"
                        trigger="{{limitDisableMsg ? 'hover' : ''}}"
                        placement="right"
                    >
                        <s-button disabled="{{limitDisable}}" skin="primary" on-click="leadSubmit">
                            {{'确定'}}
                        </s-button>
                    </s-tooltip>
                </div>
            </div>
        </s-dialog>
    </div>
`;
export default defineComponent({
    template,
    components: {
        's-form': Form,
        's-form-item': Form.Item,
        's-button': Button,
        's-table': Table,
        's-icon': Icon,
        's-tip': Tip,
        's-progress': Progress,
        'xui-sheetjs': SheetJS,
        's-dialog': Dialog,
        's-empty': Empty,
        's-tooltip': Tooltip,
        's-pagination': Pagination
    },
    initData() {
        return {
            open: false,
            ruleIsShow: false,
            ruleList: {
                datasource: [],
                schema: tableSchema,
                loading: false
            },
            ruleTotal: 0,
            ruleWrongTotal: 0,
            progressValue: 0,
            fileName: '',
            uploading: false,
            uploadSuccess: false,
            uploadFail: false,
            fileUploaded: false,
            limitDisable: false,
            limitDisableMsg: '',
            pager: {
                size: 10,
                page: 1,
                total: 0,
                datasource: [
                    {text: '10', value: 10},
                    {text: '20', value: 20},
                    {text: '50', value: 50},
                    {text: '100', value: 100}
                ],
                fileArr: []
            }
        };
    },

    computed: {
        quotaTips() {
            const quota = this.data.get('ruleQuota');
            let ingressNum = this.data.get('ingressRules').length;
            let egressNum = this.data.get('egressRules').length;
            return `入向已创建${ingressNum}条，出向已创建${egressNum}条，您入向还可以导入${quota - ingressNum}条,
                出向还可以导入${quota - egressNum}条，超出数量不允许导入，相同规则不允许重复导入。`;
        }
    },

    /**
     * 上传事件
     *
     * @param {Object} e 事件参数对象
     * @param {string} e.eventType 事件名称
     */
    uploadEvent(e) {
        let eventType = e.eventType;
        let result = e.result;
        if (eventType === 'uploadStart') {
            this.clearUploader();
            this.data.set('fileName', result.name);
        } else if (eventType === 'uploadProgress') {
            let precent = ((result.loaded / result.total) * 100).toFixed(0);
            this.data.set('uploading', true);
            this.data.set('progressValue', precent);
        } else if (eventType === 'uploadSuccess') {
            this.data.set('uploading', false);
            this.data.set('uploadSuccess', true);
            if (result.length > 1000) {
                result = result.slice(0, 1000);
            }
            // 设置分页
            this.data.set('fileArr', result);
            this.getRuleCheckList(result);
        } else if (eventType === 'uploadError') {
            this.uploadError();
        }
    },

    /**
     * 上传出错
     *
     * @param {Object} e 事件参数对象
     */
    uploadError(e) {
        this.data.set('uploading', false);
        this.data.set('uploadSuccess', false);
        this.data.set('uploadFail', true);
    },

    /**
     * 重新上传
     */
    reUpload() {
        this.ref('upload').retry();
    },

    /**
     * 清空当前文件
     */
    clearUploader() {
        this.data.set('ruleIsShow', false);
        this.data.set('ruleList.datasource', []);
        this.data.set('ruleTotal', 0);
        this.data.set('ruleWrongTotal', 0);
        this.data.set('progressValue', 0);
        this.data.set('fileName', '');
        this.data.set('uploading', false);
        this.data.set('uploadSuccess', false);
        this.data.set('uploadFail', false);
        this.data.set('fileUploaded', false);
        this.ref('upload').reset();
        this.fire('enableBtnOK', false);
    },

    /**
     * 得到规则列表
     *
     * @param {string} result 规则列表
     */
    getRuleCheckList(result) {
        const quota = this.data.get('ruleQuota');
        let ingressRules = this.data.get('ingressRules');
        let egressRules = this.data.get('egressRules');
        let inQuota = quota - ingressRules.length;
        let eQuota = quota - egressRules.length;
        let leadIngressLength = 0;
        let leadEgressLength = 0;
        u.each(result, item => {
            item.方向 === 'ingress' ? leadIngressLength++ : leadEgressLength++;
        });
        if (leadIngressLength > inQuota) {
            this.data.set('limitDisable', true);
            this.data.set(
                'limitDisableMsg',
                `安全组入向规则批量添加的数量不能超过${inQuota}个，请您减少批量添加的数量`
            );
        } else if (leadEgressLength > eQuota) {
            this.data.set('limitDisable', true);
            this.data.set('limitDisableMsg', `安全组出向规则批量添加的数量不能超过${eQuota}个，请您减少批量添加的数量`);
        } else {
            this.data.set('limitDisable', false);
            this.data.set('limitDisableMsg', '');
        }
        this.data.set('ruleTotal', result.length);
        let num = 0;
        result.forEach((item, index, array) => {
            tableSchema.forEach(schema => {
                if (!item[schema.name]) {
                    item[schema.name] = '';
                }
                if (item[schema.label]) {
                    item[schema.name] = item[schema.label];
                    delete item[schema.label];
                }
            });
            let cloneResult = u.cloneDeep(array).filter((param, i) => i !== index);
            let ingressNowData = cloneResult.filter(item => item.方向 === 'ingress' || item.direction === 'ingress');
            let engressNowData = cloneResult.filter(item => item.方向 === 'egress' || item.direction === 'egress');
            if (
                !rules.checkDirection(item.direction) ||
                !rules.checkProtocol(item.protocol) ||
                (item.protocol === 'icmp' ? false : !rules.checkPort(item.portRange)) ||
                !rules.checkEthertype(item.ethertype) ||
                !rules.checkSourceCidr(item.ethertype, item.source)
            ) {
                num++;
                item.status = false;
                !rules.checkSourceCidr(item.ethertype, item.source) && (item.statusMessage = 'IP格式不正确');
            } else if (item.direction === 'ingress' && !rules.checkIsExist(ingressRules, item, 'ingress')) {
                num++;
                item.status = false;
                item.statusMessage = '安全组已存在相同规则，请修改后重试';
            } else if (item.direction === 'egress' && !rules.checkIsExist(egressRules, item, 'egress')) {
                num++;
                item.status = false;
                item.statusMessage = '安全组已存在相同规则，请修改后重试';
            } else if (item.direction === 'egress' && this.checkTableIsExist(engressNowData, item, 'egress')) {
                num++;
                item.status = false;
                item.statusMessage = '安全组已存在相同规则，请修改后重试';
            } else if (item.direction === 'ingress' && this.checkTableIsExist(ingressNowData, item, 'ingress')) {
                num++;
                item.status = false;
                item.statusMessage = '安全组已存在相同规则，请修改后重试';
            } else {
                item.status = true;
            }
        });
        this.data.set('ruleWrongTotal', num);
        this.setPageSlice(result);
        this.data.set('fileUploaded', true);
        this.data.set('ruleIsShow', true);
        this.fire('enableBtnOK', !num);
    },

    /**
     * 导入
     *
     * @param {string} securityGroupId securityGroupId
     * @param {string} vpcId vpcId
     */
    getRuleImport(securityGroupId, vpcId) {
        let data = this.data.get('fileArr');
        this.data.set('limitDisable', true);
        this.$http
            .securityRuleImport({data, securityGroupId, vpcId})
            .then(() => {
                this.data.set('open', false);
                this.data.set('limitDisable', false);
                this.fire('leadComplete');
            })
            .catch(err => {
                this.data.set('limitDisable', false);
            });
    },
    leadSubmit() {
        let ruleTotal = this.data.get('ruleTotal');
        let ruleWrongTotal = this.data.get('ruleWrongTotal');
        if (ruleTotal === 0 || ruleWrongTotal > 0) {
            return;
        }
        this.getRuleImport(this.data.get('securityGroupId'), this.data.get('vpcId'));
    },
    leadCancel() {
        this.data.set('open', false);
    },

    // 设置分页
    setPageSlice(result) {
        let {size, page} = this.data.get('pager');
        this.data.set('pager.total', result.length);
        let finalPage = Math.ceil(result.length / size);
        let sliceRes =
            page < finalPage ? result.slice((page - 1) * size, size * page) : result.slice((page - 1) * size);
        this.data.set('ruleList.datasource', sliceRes);
    },
    onPageSizeChange(e) {
        let fileArr = this.data.get('fileArr');
        this.data.set('pager.page', 1);
        this.data.set('pager.size', e.value.pageSize);
        this.setPageSlice(fileArr);
    },
    onPageChange({value}) {
        let fileArr = this.data.get('fileArr');
        let pager = this.data.get('pager');
        pager.page = value.page;
        pager.size = value.pageSize;
        this.data.set('pager', pager);
        this.setPageSlice(fileArr);
    },
    checkTableIsExist(allRules, rule, type) {
        let result = [];
        allRules.forEach(item => {
            tableSchema.forEach(schema => {
                if (!item[schema.name]) {
                    item[schema.name] = '';
                }
                if (item[schema.label]) {
                    item[schema.name] = item[schema.label];
                    delete item[schema.label];
                }
            });
            let newItem = Rule.fromJSON(item);
            newItem.edit = true;
            result.push(newItem);
        });
        return !rules.checkIsExist(result, rule, type);
    }
});
