.security-lead-file {
    .status-warp {
        width: 12px;
    }
    .table-wrong {
        color: #eb5252;
    }
    .upload-message {
        display: inline-block;
        vertical-align: middle;
        height: 30px;
        line-height: 30px;
        margin-left: 10px;

        &:hover {
            background-color: #f7f7f7;
        }

        .upload-file-name {
            display: inline-block;
            margin: 0 4px;
            .icon-link {
                font-size: 12px;
            }
        }

        .upload-progress {
            display: inline-block;
            margin-right: 30px;
        }

        .upload-success {
            color: #2cb663;
        }

        .upload-fail {
            color: #eb5252;
        }

        .delete-file {
            display: none;
        }
        &:hover .delete-file {
            display: inline-block;
        }
        .skin-stringfy-button {
            color: #000000;
            .iconfont {
                color: #000000;
            }
        }
    }
    .rule-message {
        margin-left: 20px;
        display: inline-block;
        vertical-align: middle;
        .wrong-rules {
            color: #eb5252;
            margin-left: 10px;
        }
    }
    .icon-fail-reverse {
        color: #eb5252;
        font-size: 12px;
    }
    .icon-fail-reverse:hover {
        color: #eb5252 !important;
    }
    .icon-ok-reverse {
        color: #2cb663;
        font-size: 12px;
    }
    .icon-ok-reverse:hover {
        color: #2cb663 !important;
    }
    .tip_reverse {
        position: relative;
        top: 5px;
        .s-tip {
            border: none;
        }
        .s-tip:hover {
            background: #e6f0ff;
        }
    }
    .s-table {
        .s-table-row:hover {
            .s-table-cell {
                .status_class {
                    .s-tip {
                        background: #e6f0ff !important;
                    }
                }
            }
        }
    }
    .file-tablePage {
      float: right;
      margin-top: 8px;
    }
}
