import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import '../components';
import {DocService} from '@/pages/sanPages/common';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import DOCUMENT_ICON from '@/img/document_normal.svg?url';
import DOCUMENT_ICON_COLOR from '@/img/document_color.svg?url';

import '../style/list.less';

const {invokeComp, invokeSUI, invokeSUIBIZ, template} = decorators;
const tpl = html`
    <template>
        <s-app-list-page class="{{klass}}">
            <div class="diagnosis-header" slot="pageTitle">
                <span class="title">{{title}}</span>
                <div class="link-wrap">
                    <a
                        s-ref="introduce"
                        href="javascript:void(0)"
                        class="help-file function-introduce"
                        on-click="handleShowCard"
                        on-mouseenter="handleMouseEnter('introduce')"
                        on-mouseleave="handleMouseLeave('introduce')"
                        s-if="{{!FLAG.NetworkSupportXS}}"
                    >
                        <img class="s-icon" src="{{introduceIcon}}" />功能简介
                    </a>

                    <a s-if="{{!FLAG.NetworkSupportXS}}" href="{{DocService.path_index}}"
                        ><s-icon name="warning-new" class="warning-class" />帮助文档</a
                    >
                </div>
            </div>
            <div class="diagnosis-intro" s-if="{{show}}">
                <span class="diagnosis-title">
                    <span class="title">自助问题诊断功能简介</span>
                    <s-button class="hide-desc" skin="stringfy" on-click="handleToggle(false)">隐藏</s-button>
                </span>
                <div class="diagnosis-desc">{{description}}</div>
            </div>
            <div class="diagnosis-container">
                <div class="diagnosis-tabs">
                    <div
                        s-for="tab, index in tabs"
                        class="diagnosis-tab {{activeTab === tab.key ? 'active-tab' : ''}}"
                        on-click="changeTab(tab.key)"
                    >
                        <span class="tab-title">{{tab.title}}</span>
                        <span class="tab-desc">{{tab.desc}}</span>
                    </div>
                </div>
                <div class="diagnosis-content">
                    <div s-if="{{activeTab === 'connect'}}" class="connect">
                        <diagnosis-connect />
                    </div>
                    <div s-if="{{activeTab === 'use'}}" class="use">
                        <diagnosis-use />
                    </div>
                    <div s-if="{{activeTab === 'billing'}}" class="billing">
                        <diagnosis-billing />
                    </div>
                    <div s-if="{{activeTab === 'quota'}}" class="quota">
                        <diagnosis-quota />
                    </div>
                </div>
            </div>
        </s-app-list-page>
    </template>
`;

@template(tpl)
@invokeComp('@diagnosis-connect', '@diagnosis-quota', '@diagnosis-billing', '@diagnosis-use')
@invokeSUI
@invokeSUIBIZ
class VpcDiagnosis extends Component {
    initData() {
        return {
            klass: 'vpc-diagnosis',
            tabs: [
                {
                    key: 'connect',
                    title: '网络连接问题',
                    desc: '针对网络内实例无法访问、跨网络互访不通、等问题进行排查'
                },
                {
                    key: 'use',
                    title: '使用方案问题',
                    desc: '提供VPC如何跨区互联、连接公网、连接IDC等建议'
                },
                {
                    key: 'billing',
                    title: '费用问题',
                    desc: '常见计费模式操作建议以及指导文档'
                },
                {
                    key: 'quota',
                    title: '配额问题',
                    desc: 'VPC相关配额超限进行定位，快速申请'
                }
            ],
            activeTab: 'connect',
            DocService,
            FLAG,
            title: '自助问题诊断',
            show: true,
            introduceEle: null,
            introduceTitle: '二层网关',
            description:
                'VPC自助问题排查可以帮助您排查VPC实例访问不通、访问异常、配额、组网方式、费用等问题，实例排查期间可能会对您的实例探测并进行诊断分析，不会对您的实例配置和正常业务造成影响。',
            introduceIcon: INTRODUCE_ICON_COLOR,
            documentIcon: DOCUMENT_ICON
        };
    }

    attached() {
        this.data.set('introduceEle', this.ref('introduce'));
    }
    changeTab(key) {
        this.data.set('activeTab', key);
    }
    /**
     * @description 介绍卡片变化时回调
     * @param {boolean} flag
     */
    handleToggle(flag) {
        this.data.set('show', flag);
        this.data.set('introduceIcon', INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#151B26';
    }
    handleShowCard() {
        if (!this.data.get('show')) {
            this.data.set('show', true);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type) {
        if (type === 'introduce') {
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
            const ele = this.data.get('introduceEle');
            ele.style.color = '#2468F2';
        } else {
            this.data.set('documentIcon', DOCUMENT_ICON_COLOR);
        }
    }
    handleMouseLeave(type) {
        const isShow = this.data.get('show');
        if (type === 'introduce') {
            this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
            const ele = this.data.get('introduceEle');
            !isShow && (ele.style.color = '#151B26');
        } else {
            this.data.set('documentIcon', DOCUMENT_ICON);
        }
    }
}
export default San2React(Processor.autowireUnCheckCmpt(VpcDiagnosis));
