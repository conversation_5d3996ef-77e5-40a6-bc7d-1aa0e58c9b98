.diagnosis-connect {
    .connect-content {
        padding: 24px;
        .suggestion {
            display: inline-flex;
            white-space: nowrap;
            height: 32px;
            line-height: 32px;
            background-color: #e6f0ff;
            // display: flex;
            padding: 0 17px;
            border-radius: 4px;
            margin: 25px 0;
            a {
                margin-right: 12px;
            }
        }
        .pathanalise {
            .s-form-item {
                margin: 24px 0 0;
            }
            .s-form-item-label {
                width: 106px;
                height: 30px;
            }
            .source-dest-container {
                margin-top: 24px;
                h3 {
                    font-size: 16px;
                    color: #303540;
                    font-weight: 500;
                    margin-bottom: 16px;
                }
                .source-dest-group {
                    display: flex;
                    .container {
                        flex: 1;
                        padding: 16px;
                        background: #f7f7f9;
                        border-radius: 6px;
                        .title {
                            font-size: 16px;
                            color: #303540;
                            font-weight: 500;
                        }
                        .path-wrap {
                            .s-form-item-label {
                                margin-top: 24px;
                            }
                            .source-item {
                                margin: 0;
                                .s-form-item-control-wrapper {
                                    flex: 1;
                                    .s-radio-button {
                                        margin-top: 24px;
                                    }
                                }
                            }
                        }
                    }
                    .source-container {
                        margin-right: 12px;
                    }
                    .source-instance {
                        margin-left: 106px;
                    }
                }
            }
        }
        .probe {
            .s-form-item-label {
                width: 120px;
                text-align: left;
            }
            .probe-frequency-item {
                .s-row {
                    .s-form-item-control {
                        display: flex;
                        align-items: center;
                        .frequency-unit {
                            margin-left: 12px;
                        }
                    }
                }
            }
            .destip-wrapper {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                .destip-input {
                    margin-right: 8px;
                }
                .s-radio {
                    margin: 10px;
                }
                .destip {
                    .s-row {
                        .s-form-item-control-wrapper {
                            .s-form-item {
                                display: inline;
                                float: left;
                            }
                        }
                    }
                }
            }
            .probe-input {
                .s-form-item-label {
                    display: flex;
                }
            }
            .destip-wrapper {
                display: table;
                .destip {
                    display: table-cell;
                    margin: 24px 0;
                    .s-row {
                        margin-right: 20px;
                        .s-form-item-control-wrapper {
                            .s-form-item {
                                display: inline;
                                float: left;
                            }
                        }
                    }
                }
                .destip-port-wrapper {
                    .destip {
                        float: left;
                    }
                    .destport {
                        margin: 24px 0;
                    }
                }
            }
            .label_class {
                .inline-tip {
                    top: 3px;
                    position: relative;
                    .s-tip-warning {
                        justify-content: center;
                        .warning_class {
                            fill: #999;
                        }
                    }
                    .s-tip:hover .s-icon path {
                        fill: #2468f2 !important;
                    }
                }
            }
            .destip-other {
                .destport {
                    margin-top: 0 !important;
                }
            }
            .probe-auto-item {
                margin: 24px 0;
                .s-row {
                    display: flex;
                    align-items: center;
                }
            }
            .ip-item {
                .s-form-item-control-wrapper {
                    max-width: 510px;
                    .ip-radio {
                        display: inline-block;
                        .s-radio {
                            margin: 10px;
                        }
                    }
                }
                .s-trigger-container {
                    margin-top: 5px;
                }
                .s-table-container {
                    .probe-custom-ip-wrapper {
                        display: inline-flex;
                        flex-direction: column;
                        .probe-custom-ip-err {
                            margin-top: 5px;
                            color: #f33e3e;
                        }
                    }
                }
            }
        }
    }
    .opt-container {
        margin-top: 20px;
        height: 80px;
        box-shadow: 0 1px 10px 0 rgba(21, 27, 38, 0.1);
        display: flex;
        align-items: center;
        padding: 0 25px;
        .diagnosis-btn {
            margin-right: 16px;
        }
    }
}
