import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';

import {DocService} from '../../../common';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import '../style/billing.less';

const {asComponent, template, invokeSUI} = decorators;

const tpl = html`
    <div class="diagnosis-billing">
        <h3>常见计费方式介绍</h3>
        <div class="billing-normal">
            <s-table columns="{{table.columns}}" datasource="{{table.datasource}}">
                <div slot="c-desc">
                    {{row.desc}}
                    <a s-if="!FLAG.NetworkSupportXS" href="{{row.link}}">{{row.linkName}}</a>
                </div>
            </s-table>
        </div>
    </div>
`;

@template(tpl)
@asComponent('@diagnosis-billing')
@invokeSUI
class DiagnosisBilling extends Component {
    initData() {
        return {
            FLAG,
            table: {
                columns: [
                    {name: 'product', label: '计费模式', width: 160},
                    {name: 'desc', label: '描述及操作建议'}
                ],
                datasource: [
                    {
                        product: '流日志计费模式',
                        desc: '流日志公测期间免费，数据存储在日志服务中，将按日志服务的标准收费。详情查看',
                        link: DocService.bls_pay,
                        linkName: '日志服务收费标准'
                    },
                    {
                        product: '流量镜像计费模式',
                        desc: '流量镜像公测期间免费'
                    },
                    {
                        product: '服务网卡计费模式',
                        desc: '服务网卡根据运营时间和进出服务网卡的双向流量收取实例费和流量费。详情查看',
                        link:
                            (window.$context?.Domains?.portal || 'https://cloud.baidu.com') + '/product-price/vpc.html',
                        linkName: '计费规则'
                    }
                ]
            }
        };
    }
}
export default Processor.autowireUnCheckCmpt(DiagnosisBilling);
