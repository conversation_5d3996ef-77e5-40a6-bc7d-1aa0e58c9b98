import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';

import {DocService} from '@/pages/sanPages/common';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import '../style/use.less';

const {asComponent, template, invokeSUI} = decorators;

const tpl = html`
    <div class="diagnosis-use">
        <h3>常见产品使用问题</h3>
        <div class="use-normal">
            <s-table columns="{{table.columns}}" datasource="{{table.datasource}}">
                <div slot="c-desc">
                    {{row.desc}}
                    <a s-if="!FLAG.NetworkSupportXS" s-for="link, index in row.link" href="{{link}}"
                        >{{row.linkName[index]}}</a
                    >
                </div>
            </s-table>
        </div>
    </div>
`;

@template(tpl)
@asComponent('@diagnosis-use')
@invokeSUI
class DiagnosisUse extends Component {
    initData() {
        return {
            FLAG,
            table: {
                columns: [
                    {name: 'problem', label: '使用问题', width: 160},
                    {name: 'desc', label: '操作建议'}
                ],
                datasource: [
                    {
                        problem: '跨VPC互联的解决方案',
                        desc: '默认情况下，不同VPC之间完全隔离，内网之间不互通。如需互通，用户可以开通对等连接或者云智能网服务，实现VPC之间的高速互联。此外，还可通过VPN网关实现实现外网互通。',
                        link: [DocService.peer_index, DocService.csn_index, DocService.vpn_index],
                        linkName: ['对等连接', '云智能网', 'VPN网关']
                    },
                    {
                        problem: 'VPC如何连接公网',
                        desc: '您可以通过BCC实例固定公网IP、弹性公网IP、NAT网关、负载均衡使私有网络中的云资源可以访问公网或被公网访问。',
                        link: [
                            (window.$context?.Domains?.portal || 'https://cloud.baidu.com') + '/solution/internet.html'
                        ],
                        linkName: ['公网访问解决方案']
                    },
                    {
                        problem: 'VPC如何连接IDC',
                        desc: '通过VPN或者专线ET服务，将VPC与用户IDC网络互通，构建安全、定制的混合云网络，实现原有业务轻松、安全的迁移到云端。',
                        link: [
                            DocService.vpn_index,
                            (window.$context?.Domains?.portal || 'https://cloud.baidu.com') + '/product/et.html'
                        ],
                        linkName: ['VPN网关', '专线接入']
                    },
                    {
                        problem: '多用户访问控制问题',
                        desc: '您可以通过多用户访问控制管理云账户下资源的访问权限，对不同的工作人员赋予使用产品的不同权限，实现多用户协同操作资源。',
                        link: [DocService.iam_multi],
                        linkName: ['权限策略']
                    }
                ]
            }
        };
    }
}
export default Processor.autowireUnCheckCmpt(DiagnosisUse);
