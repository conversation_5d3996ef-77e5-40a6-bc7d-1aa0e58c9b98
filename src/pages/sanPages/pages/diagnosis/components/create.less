.quota-alarm-dialog {
    .processing:before {
        color: #108CEE;
    }
    .alarm-form-flex {
        .s-form-item-control-wrapper {
            display: flex;
            align-items: center;
        }
    }
    .s-form-item-label {
        width: 114px;
    }
    .loading {
        position:absolute;
        left: 55%;
        top: 70%;
        margin-left:-50px;
        margin-top:-50px;
    }
    .inline-tip {
        top: 3px;
        left: 5px;
        position: relative;
        .s-tip-warning {
            justify-content: center;
            .warning_class {
                fill: #999;
            }
        }
    }
    .error_class {
        color: #d0021b;
        margin-left: 114px;
        font-size: 12px;
        padding-top: 3px;
        margin-bottom: 24px;
    }
    .errorShow_class {
        margin-bottom: 0px;
    }
    .s-form  {
        .s-form-item {
            &:first-child {
                margin-top: 0px !important;
            }
        }
        .s-form-item-label-required>label:before {
            left: 0px !important;
        }
    }
    .s-cascader {
        .s-cascader-value-arrow {
            position: absolute;
            top: 8px;
            color: #b4b6ba!important;
            fill: #b4b6ba!important;
        }
    }
}

.dialog-none-footer {
    .s-dialog-footer {
        display: none;
    }
}
