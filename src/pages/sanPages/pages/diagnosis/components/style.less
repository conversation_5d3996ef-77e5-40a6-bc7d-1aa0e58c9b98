.app-tab-page {
    padding: 0;
    .s-tabnav-scroll {
        width: 140px;
        background: white;
        .s-tabnav-nav-item {
            height: 40px;
            margin-bottom: 0px !important;
            text-align: left;
            margin-top: 4px;
            padding-left: 20px;
            span {
                line-height: 21px;
            }
        }
    }
    .s-tabpane-wrapper {
        flex: 1;
        overflow: auto;
        background-color: #fff;
        .app-tab-page-panel {
            .s-tabpane {
                .quota-instance-list {
                    width: 100% !important;
                    height: 100%;
                    overflow-y: auto;
                    background: #f7f7f9 !important;
                    // flex: 1;
                    // width: 0;
                    // overflow-y: scroll;
                    .s-biz-page-header {
                        margin: 0px !important;
                        background-color: #fff;
                        height: auto !important;
                        line-height: 47px;
                        border: none !important;
                        .s-biz-page-title {
                            h2 {
                                height: 47px !important;
                                color: #151b26 !important;
                                line-height: 47px !important;
                                font-weight: 500 !important;
                                font-size: 16px !important;
                                margin-left: 16px !important;
                            }
                        }
                    }
                    .s-biz-page-content {
                        border-radius: 6px;
                        margin: 0px !important;
                        padding: 16px;
                        background: #fff;
                        .s-biz-page-toolbar {
                            margin: 0px;
                        }
                        .s-biz-page-body {
                            margin-top: 0px;
                            // .s-table {
                            //     .s-table-body {
                            //         max-height: calc(~'100vh - 326px');
                            //     }
                            // }
                        }
                        .s-biz-page-footer {
                            padding-bottom: 0px;
                            margin-top: 16px;
                        }
                    }
                    // .s-biz-page-header {
                    //     h2 {
                    //         font-weight: 700 !important;
                    //     }
                    // }
                    // .s-biz-page-toolbar {
                    //     height: 30px;
                    // }
                    .s-collapse {
                        border: none;
                        background-color: #ffffff;
                        max-height: calc(~'100vh - 214px');
                        overflow: auto;
                        .s-collapsepanel {
                            border: 1px solid #eee;
                            .s-collapsepanel-header {
                                background-color: #f7f7f9;
                            }
                            .s-collapsepanel-content {
                                border-top: 1px solid #ffffff;
                            }
                        }
                        .s-collapsepanel-content-box {
                            padding-top: 0px;
                        }
                    }
                    .quato-servicetype {
                        display: inline-block;
                        width: 180px;
                        font-weight: 700;
                    }
                    .quato-text {
                        margin-left: 10px;
                        margin-right: 10px;
                    }
                    .refresh_class {
                        margin-top: 16px;
                        margin-left: 10px;
                    }
                    .select-wrap {
                        display: inline-block;
                    }
                    .table-wrap {
                        margin-top: 16px;
                    }
                    .collapse-wrap {
                        display: flex;
                        align-items: flex-start;
                        flex-flow: row wrap;
                        .s-select,
                        .s-search {
                            margin-top: 16px;
                            margin-left: 10px;
                        }
                        .button-collapse-wrap {
                            margin-top: 16px;
                            margin-left: 10px;
                        }
                        .s-search {
                            display: flex;
                        }
                        .quato-text {
                            margin: 16px 12px 0;
                            min-width: 112px;
                            line-height: 28px;
                        }
                    }
                    .ruletype {
                        margin-top: 16px;
                    }
                    .loading-wrap {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100vw;
                        height: 100vh;
                        background-color: rgba(0, 0, 0, 0.1);
                        z-index: 100;
                    }
                    .loading {
                        position: absolute;
                        left: 50%;
                        top: 50%;
                        margin-left: -50px;
                        margin-top: -50px;
                    }
                    .s-collapsepanel-header {
                        height: 48px;
                        display: flex;
                        align-items: center;
                        font-size: 14px;
                    }
                    .s-pagination {
                        margin: 10px 0;
                    }
                    .search-wrap {
                        float: right;
                        margin-top: 8px;
                    }
                    .operation {
                        .s-button {
                            padding: 0;
                            margin-right: 12px;
                        }
                    }
                }
                .progress_wrap {
                    .s-progress {
                        width: 200px !important;
                        border-radius: 5px;
                        .s-progress-complete {
                            border-radius: 5px;
                        }
                        .s-progress-info {
                            display: none;
                        }
                    }
                }
            }
        }
    }
    .help_class {
        position: absolute;
        top: 32px;
        right: 32px;
        .s-icon {
            position: absolute;
            top: 0px;
            left: -20px;
        }
    }
    .none_left {
        margin-left: 0px !important;
    }
}

.quota-apply-dialog {
    font-size: 12px;
    .s-form-item-label {
        width: 100px;
        text-align: left;
    }
    .require {
        .s-row {
            .s-form-item-label {
                label:before {
                    content: '*';
                    color: #d0021b;
                    margin-right: 4px;
                }
            }
        }
    }
    .s-form-item-control-wrapper {
        margin-left: 20px;
    }
    .s-form-item {
        margin-top: 20px;
        margin-bottom: 0;
        &:first-child {
            margin: 0;
        }
    }
    .s-form-item-content {
        margin-left: 80px;
    }
    .check_class {
        .s-row {
            .s-form-item-control-wrapper {
                display: flex;
                align-items: center;
            }
        }
        .s-checkbox {
            margin-right: 20px;
        }
    }
    .text-item {
        line-height: 30px;
    }
    .error_select {
        .s-input-suffix-container {
            border-color: #d0021b;
        }
    }
    .s-radio-button-group {
        .s-radio-text {
            width: 96px;
        }
    }
    .result_text {
        margin-top: 5px;
        color: #999999;
    }
    .person-tip-wrap {
        margin-left: 120px;
        margin-top: 10px;
        .person-tip-item {
            background: #fcf7f1;
            color: #333;
            padding: 5px;
            margin-right: 5px;
            .s-icon {
                color: #f39000;
                font-size: 10px;
            }
        }
    }
    .form_num_item {
        margin-top: 10px;
    }
    .inline-tip {
        top: 3px;
        position: relative;
        .s-tip-warning {
            justify-content: center;
            .warning_class {
                fill: #999;
            }
        }
        .s-tip:hover .s-icon path {
            fill: #2468f2 !important;
        }
    }
}

.quota-tab-wrap {
    padding: 16px !important;
    .s-tabs-vertical.s-tabs-left > .s-tabnav {
        margin-right: 0px;
        border-right: 1px solid #e8e9eb;
    }
    .s-tabs-line.s-tabs-vertical.s-tabs-left > .s-tabnav .s-tabnav-nav-item {
        text-align: left;
    }
    .s-tabs-line.s-tabs-vertical.s-tabs-left > .s-tabnav .s-tabnav-nav {
        border-right: none;
        &:first-child {
            margin-top: 10px;
        }
    }
    .s-tabnav-scroll .s-tabnav-nav-item {
        height: 32px;
        &:first-child {
            margin-top: 0px !important;
        }
        span {
            line-height: 16px;
        }
    }
    .s-tabs-skin-accordion {
        border-color: white !important;
        border-radius: 6px;
    }
}
