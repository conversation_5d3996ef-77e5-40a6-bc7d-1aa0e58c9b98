/*
 * @description: 关联弹窗
 */
import _ from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {OutlinedQuestion} from '@baidu/sui-icon';
const {asComponent, invokeSUI, invokeSUIBIZ, template, invokeAppComp} = decorators;
import {judgeOneNone, judgeRange} from './judge';
import {ContextService} from '../../../common';
const AllRegion = ContextService.getEnum('AllRegion');
import './style.less';

const formValidator = self => ({
    reason: [{required: true, message: '申请理由必填'}],
    num: [
        {
            validator: (rule, value, callback) => {
                let range = self.data.get('range');
                let placeholder = self.data.get('placeholder');
                let applicationType = self.data.get('formData.applicationType');
                let defaultValue = self.data.get('formData.value');
                if (applicationType === 'RANGE') {
                    if (!value) {
                        return callback('申请配额数量必填');
                    }
                    if (isNaN(Number(value))) {
                        return callback('请输入数字');
                    }
                    if (value.indexOf('.') > -1) {
                        return callback('请输入整数');
                    }
                    if (range) {
                        let array = range.split('、');
                        if (array[1]) {
                            let arr0 = array[0].split(',');
                            let arr1 = array[1].split(',');
                            if (+value < +arr0[0] || (+arr0[1] < +value && +value < +arr1[0]) || +value > +arr1[1]) {
                                return callback(placeholder);
                            }
                        } else {
                            let arr = array[0].split(',');
                            if (+value < +arr[0] || +value > +arr[1]) {
                                return callback(placeholder);
                            }
                            if (+value === +defaultValue) {
                                return callback('申请配额数量必须大于当前配额量');
                            }
                            return callback();
                        }
                    }
                }
                return callback();
            }
        }
    ]
});

const tpl = html`
    <template>
        <s-dialog class="quota-apply-dialog" closeAfterMaskClick="{{false}}" open="{=open=}" title="{{title}}">
            <s-form
                s-ref="form"
                data="{=formData=}"
                rules="{{rules}}"
                label-col="{{labelCol}}"
                wrapper-col="{{wrapperCol}}"
            >
                <s-form-item label="配额名称：">
                    <span class="text-item">{{formData.showName}}</span>
                </s-form-item>
                <s-form-item label="配额描述：">
                    <span class="text-item">{{formData.description}}</span>
                </s-form-item>
                <s-form-item label="已使用/全部配额：">
                    <span class="text-item">{{formData.used || '-'}}/{{formData.value}}</span>
                </s-form-item>
                <s-form-item label="地域：">
                    <span class="text-item">{{formData.region | getRegionText}}</span>
                </s-form-item>
                <div
                    class="person-tip-wrap"
                    s-if="{{formData.serviceType === 'BBC' || formData.serviceType === 'BCC'}}"
                >
                    <span class="person-tip-item">
                        <s-icon name="warning"></s-icon>
                        {{'您当前配额为全局region生效配额，即在当前region申请后会同步生效其他支持region，请知晓'}}
                    </span>
                </div>
                <s-form-item label="申请配额数量：" prop="num" class="require" class="form_num_item">
                    <template slot="label" class="label_class">
                        {{'申请配额数量：'}}
                        <s-tip class="inline-tip" skin="question">
                            <s-qusetion />
                            <!--bca-disable-next-line-->
                            <div slot="content">{{formData | getContent | raw}}</div>
                        </s-tip>
                    </template>
                    <s-input
                        width="340"
                        placeholder="{{placeholder}}"
                        s-if="{{formData.applicationType === 'RANGE'}}"
                        value="{=formData.num=}"
                    ></s-input>
                    <s-select datasource="{{datasource}}" value="{=formData.enum=}" s-else></s-select>
                </s-form-item>
                <s-form-item label="申请理由：" prop="reason" class="require">
                    <s-input-text-area
                        value="{=formData.reason=}"
                        width="{{341}}"
                        height="{{90}}"
                        placeholder="{{'请输入'}}"
                    />
                </s-form-item>
            </s-form>
            <div slot="footer">
                <s-button on-click="close">取消</s-button>
                <s-button skin="primary" on-click="dialogConfirm" disabled="{{confirmDisabled}}">确定</s-button>
            </div>
        </s-dialog>
    </template>
`;

@template(tpl)
@asComponent('@quota-apply')
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class QuotaApply extends Component {
    static filters = {
        getRegionText(region) {
            return AllRegion.getTextFromValue(region) || '';
        },
        getContent(formData) {
            let link = `${window.$context.getDomains().ticket}#/ticket/create`;
            formData.serviceType === 'BCC' && (link = link + '?useticket=1&productId=2&questionId=453&channel=2');
            return `如果额度不能满足需要，可提交<a href="${link}">工单</a>沟通及申请`;
        }
    };
    static components = {
        's-qusetion': OutlinedQuestion
    };
    initData() {
        return {
            title: '',
            content: '',
            open: true,
            labelCol: {span: 7},
            wrapperCol: {span: 11},
            rules: {},
            editDatasource: [
                {label: '是', value: true},
                {label: '否', value: false}
            ],
            formData: {},
            datasource: [],
            rules: formValidator(this),
            placeholder: '',
            range: '',
            confirmDisabled: false
        };
    }
    inited() {
        this.initFormData();
    }
    initFormData() {
        let formData = this.data.get('formData');
        let automaticApprovalInterval = formData.automaticApprovalInterval?.split(',');
        let manualApprovalInterval = formData.manualApprovalInterval?.split(',');
        // 进行范围判断，判断此时的提示性文字和取值校验
        // 取值各位3种，有applicationType字段来判断最后一种是范围还是枚举
        // ALL、NONE、'1,100'
        // 去除包含 ALL的和全为NONE的一共三种
        // 进行分清讨论，而且同事满足范围和枚举的情况
        if (formData.automaticApprovalInterval !== 'ALL' && formData.manualApprovalInterval !== 'ALL') {
            if (!(formData.automaticApprovalInterval === 'NONE' && formData.manualApprovalInterval === 'NONE')) {
                if (automaticApprovalInterval[0] === 'NONE' || manualApprovalInterval[0] === 'NONE') {
                    let range =
                        automaticApprovalInterval[0] === 'NONE' ? manualApprovalInterval : automaticApprovalInterval;
                    range = range.map(item => +item);
                    if (formData.applicationType === 'RANGE') {
                        range = judgeOneNone(range, +formData.value, formData.direction);
                        if (formData.name === 'channelBgpRouteLimit') {
                            if (range) {
                                let rangeArray = range.split('、');
                                let automaticApprovalInterval = rangeArray[0].split(',');
                                if (automaticApprovalInterval[0] < 200) {
                                    if (automaticApprovalInterval[1] > 200) {
                                        automaticApprovalInterval[1] = 200;
                                        range = automaticApprovalInterval.join(',');
                                    }
                                }
                            }
                        }
                        let placeholder = range ? `申请范围[${range}]` : '';
                        this.data.set('range', range);
                        this.data.set('placeholder', placeholder);
                    } else {
                        if (formData.direction) {
                            range = range.filter(item => {
                                if (formData.direction > 1) {
                                    return item > +formData.value;
                                } else {
                                    return item < +formData.value;
                                }
                            });
                        }
                        let datasource = range.map(item => {
                            return {
                                label: item,
                                value: item
                            };
                        });
                        this.data.set('datasource', datasource);
                    }
                } else {
                    if (+automaticApprovalInterval[0] > +manualApprovalInterval[0]) {
                        let temp = manualApprovalInterval;
                        manualApprovalInterval = automaticApprovalInterval;
                        automaticApprovalInterval = temp;
                    }
                    automaticApprovalInterval = automaticApprovalInterval.map(item => +item);
                    manualApprovalInterval = manualApprovalInterval.map(item => +item);
                    if (formData.applicationType === 'RANGE') {
                        let range = judgeRange(
                            automaticApprovalInterval,
                            manualApprovalInterval,
                            Number(formData.value),
                            formData.direction
                        );
                        if (formData.name === 'channelBgpRouteLimit') {
                            if (range) {
                                let rangeArray = range.split('、');
                                if (rangeArray.length > 1) {
                                    let automaticApprovalInterval = rangeArray[0].split(',');
                                    let manualApprovalInterval = rangeArray[1].split(',');
                                    if (automaticApprovalInterval[0] < 200) {
                                        if (manualApprovalInterval[0] > 200) {
                                            if (automaticApprovalInterval[1] > 200) {
                                                automaticApprovalInterval[1] = 200;
                                            }
                                            range = automaticApprovalInterval.join(',');
                                        } else {
                                            if (manualApprovalInterval[1] > 200) {
                                                manualApprovalInterval[1] = 200;
                                            }
                                            range =
                                                automaticApprovalInterval.join(',') +
                                                '、' +
                                                manualApprovalInterval.join(',');
                                        }
                                    }
                                } else {
                                    let rangeArray = range.split('、');
                                    let automaticApprovalInterval = rangeArray[0].split(',');
                                    if (automaticApprovalInterval[0] < 200) {
                                        if (automaticApprovalInterval[1] > 200) {
                                            automaticApprovalInterval[1] = 200;
                                            range = automaticApprovalInterval.join(',');
                                        }
                                    }
                                }
                            }
                        }
                        let placeholder = range.split('、');
                        if (placeholder.length > 1) {
                            this.data.set('placeholder', `申请范围[${placeholder[0]}]、[${placeholder[1]}]`);
                        } else {
                            if (placeholder[0]) {
                                this.data.set('placeholder', `申请范围[${placeholder[0]}]`);
                            } else {
                                this.data.set('placeholder', '');
                            }
                        }
                        this.data.set('range', range);
                    } else {
                        let range = [...automaticApprovalInterval.concat(manualApprovalInterval)];
                        if (formData.direction) {
                            range = range.filter(item => {
                                if (formData.direction > 1) {
                                    return item > +formData.value;
                                } else {
                                    return item < +formData.value;
                                }
                            });
                        }
                        let datasource = range.map(item => {
                            return {
                                label: item,
                                value: item
                            };
                        });
                        this.data.set('datasource', datasource);
                    }
                }
            }
        }
        this.data.set('formData.mode', true);
    }
    async dialogConfirm() {
        await this.ref('form').validateFields();
        this.data.set('confirmDisabled', true);
        let formData = this.data.get('formData');
        let payload = {
            type: 'QUOTA',
            serviceType: formData.serviceType,
            productType: formData.productType,
            region: formData.region,
            value: formData.num,
            name: formData.showName,
            reason: formData.reason
        };
        this.fire('confirm', payload);
        this.data.set('open', false);
    }
    close() {
        this.fire('close');
        this.data.set('open', false);
    }
}
export default Processor.autowireUnCheckCmpt(QuotaApply);
