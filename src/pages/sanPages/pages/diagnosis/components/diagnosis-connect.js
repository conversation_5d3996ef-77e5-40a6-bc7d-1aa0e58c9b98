import {Component} from 'san';
import u from 'lodash';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {checker} from '@baiducloud/bce-opt-checker';
import {Radio, Notification} from '@baidu/sui';
import {OutlinedPlus} from '@baidu/sui-icon';
import {Tip} from '@baidu/sui-biz';

import {DocService} from '../../../common';
import {
    pathTypeList,
    RouteStatus,
    VpnStatus,
    DcGatewayStatus,
    ProbeProtocol,
    RouteType,
    AnalyzeStatus
} from '../../../common/enum';
import '../style/connect.less';
import {formValidator} from '../../pathanalise/components/helper';
import BcmDetail from '../../pathanalise/components/drawPathanalise';
import {formValidator as probeFormValidator} from '../../probe/components/helper';
import rule from '../../../utils/rule';
import probeRules from '../../probe/rules';
import {disable_vpn_region} from '../../../common/flag';
import {$flag as FLAG, getVpcAvaliableRegion} from '@/pages/sanPages/utils/helper';
import RULE from '../../../utils/rule';
import {checkIsInSubnet} from '../../../utils/common';

const AllRegion = window.$context.getEnum('AllRegion');
const {asComponent, template, invokeSUI} = decorators;
const kXhrOptions = {'X-silence': true};
const {IP} = rule;

const tpl = html`
    <div class="diagnosis-connect">
        <div class="connect-content">
            <s-radio-group
                radioType="button"
                datasource="{{connectProblem}}"
                value="{=currentProblem=}"
                on-change="problemChange"
            >
            </s-radio-group>
            <div class="suggestion">
                {{notify[currentProblem].suggestion}}
                <div s-if="notify[currentProblem].link && !FLAG.NetworkSupportXS" class="link">
                    <a s-for="link, index in notify[currentProblem].link" href="{{link}}">
                        {{notify[currentProblem].linkName[index]}}
                    </a>
                </div>
            </div>
            <div s-if="currentProblem === 'inner' || currentProblem === 'cross'" class="pathanalise">
                <s-form s-ref="pathFormRef" rules="{{validateRule}}" data="{=pathFormData=}" label-align="left">
                    <s-form-item label="诊断方式：">
                        <s-radio-group
                            radioType="button"
                            datasource="{{diagnosisMethod.path.datasource}}"
                            value="{=diagnosisMethod.path.value=}"
                        >
                        </s-radio-group>
                    </s-form-item>
                    <s-form-item
                        label="路径名称："
                        prop="name"
                        help="大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65"
                    >
                        <s-input width="{{300}}" value="{=pathFormData.name=}" />
                    </s-form-item>
                    <s-form-item label="描述：" prop="description">
                        <s-input-text-area
                            width="{{300}}"
                            maxLength="{{200}}"
                            height="{{100}}"
                            multiline
                            value="{=pathFormData.description=}"
                        />
                    </s-form-item>
                    <s-form-item label="协议：" prop="protocol">
                        <s-select
                            width="{{300}}"
                            datasource="{{protocolDatasource}}"
                            on-change="protocolChange"
                            value="{=pathFormData.protocol=}"
                        >
                        </s-select>
                    </s-form-item>
                    <div class="source-dest-container">
                        <h3>源参数和目的参数</h3>
                        <div class="source-dest-group">
                            <div class="source-container container">
                                <span class="title">源参数设置</span>
                                <s-form-item
                                    label="源地域： "
                                    prop="sourceRegion"
                                    s-if="pathFormData.sourceType !== 'PUBLIC_IP' && pathFormData.sourceType !== 'DC'"
                                >
                                    <s-select
                                        width="{{300}}"
                                        datasource="{{regionList}}"
                                        value="{=pathFormData.sourceRegion=}"
                                        on-change="sourceRegionChange($event)"
                                    >
                                    </s-select>
                                </s-form-item>
                                <!--<s-form-item s-if="pathFormData.sourceType !== 'DC'" label="源所在网络： " prop="vpcId">
                                    <s-select
                                        width="{{300}}"
                                        filterable
                                        datasource="{{resVpcList}}"
                                        value="{=pathFormData.vpcId=}"
                                        on-change="resVpcChange($event)"
                                    >
                                    </s-select>
                                </s-form-item>-->
                                <div class="path-wrap">
                                    <s-form-item class="source-item" label="选择源：" prop="sourceType">
                                        <s-radio-group
                                            radioType="button"
                                            datasource="{{sourceTypeList}}"
                                            value="{=pathFormData.sourceType=}"
                                            disabled="{{sourceTypeLoading}}"
                                            on-change="sourceTypeChange"
                                        >
                                        </s-radio-group>
                                    </s-form-item>
                                    <s-form-item
                                        class="source-instance"
                                        prop="sourceId"
                                        s-if="pathFormData.sourceType !== 'PUBLIC_IP' && pathFormData.sourceType !== 'DC'"
                                    >
                                        <s-select
                                            width="{{300}}"
                                            s-if="pathFormData.sourceType !== 'BCC'"
                                            filterable
                                            placeholder="请选择源实例"
                                            datasource="{{sourceIdList}}"
                                            on-change="sourceIdChange"
                                            value="{=pathFormData.sourceId=}"
                                        >
                                        </s-select>
                                        <s-input
                                            width="300"
                                            s-if="pathFormData.sourceType === 'BCC'"
                                            value="{=pathFormData.sourceId=}"
                                            placeholder="请输入BCC实例ID，例如：i-Uu1CIcJH"
                                        >
                                        </s-input>
                                    </s-form-item>
                                    <s-form-item
                                        class="source-instance"
                                        s-if="pathFormData.sourceType === 'DC' || pathFormData.sourceType === 'VPN_CONN'"
                                        prop="connId"
                                    >
                                        <s-select
                                            width="{{300}}"
                                            filterable
                                            placeholder="{{pathFormData.sourceType === 'DC' ? '请选择通道' : '请选择VPN通道'}}"
                                            datasource="{{connIdList}}"
                                            value="{=pathFormData.connId=}"
                                        >
                                        </s-select>
                                    </s-form-item>
                                    <s-form-item
                                        class="source-instance"
                                        prop="sourceIp"
                                        s-if="pathFormData.sourceType !== 'EIP'"
                                    >
                                        <s-select
                                            width="{{300}}"
                                            loading="{{loadIpLoading}}"
                                            s-if="pathFormData.sourceType === 'BCC' && sourceBccExit"
                                            datasource="{{sourceIpDataSource}}"
                                            value="{=pathFormData.sourceIp=}"
                                        >
                                        </s-select>
                                        <s-input
                                            width="{{300}}"
                                            s-else
                                            placeholder="请输入源IP地址"
                                            value="{=pathFormData.sourceIp=}"
                                        >
                                        </s-input>
                                    </s-form-item>
                                </div>
                                <s-form-item label="源端口：" prop="sourcePort">
                                    <s-input
                                        width="300"
                                        disabled="{{inputDis}}"
                                        value="{=pathFormData.sourcePort=}"
                                        placeholder="1-65535"
                                    >
                                    </s-input>
                                </s-form-item>
                            </div>
                            <div class="dest-container container">
                                <span class="title">目的参数设置</span>
                                <s-form-item
                                    label="目的地域： "
                                    s-if="pathFormData.destType !== 'IDC'"
                                    prop="desRegion"
                                >
                                    <s-select
                                        width="{{300}}"
                                        datasource="{{regionList}}"
                                        value="{=pathFormData.desRegion=}"
                                        on-change="desRegionChange($event)"
                                    >
                                    </s-select>
                                </s-form-item>
                                <!--<s-form-item
                                    s-if="pathFormData.destType !== 'DC'"
                                    label="目的所在网络： "
                                    prop="desVpcId"
                                >
                                    <s-select
                                        width="{{300}}"
                                        filterable
                                        datasource="{{desVpcList}}"
                                        value="{=pathFormData.desVpcChange=}"
                                        on-change="desVpcChange($event)"
                                    >
                                    </s-select>
                                </s-form-item>-->
                                <div class="path-wrap">
                                    <s-form-item class="source-item" label="选择目的：" prop="destType">
                                        <s-radio-group
                                            radioType="button"
                                            datasource="{{destTypeDatasource}}"
                                            disabled="{{desTypeLoading}}"
                                            value="{=pathFormData.destType=}"
                                            on-change="destTypeChange"
                                        >
                                        </s-radio-group>
                                    </s-form-item>
                                    <s-form-item
                                        class="source-instance"
                                        s-if="pathFormData.destType !== 'IDC'"
                                        prop="destId"
                                    >
                                        <s-select
                                            width="{{300}}"
                                            filterable
                                            placeholder="请选择目的实例"
                                            s-if="pathFormData.destType !== 'BCC'"
                                            datasource="{{destIdDatasource}}"
                                            value="{=pathFormData.destId=}"
                                        >
                                        </s-select>
                                        <s-input
                                            width="300"
                                            s-if="pathFormData.destType === 'BCC'"
                                            value="{=pathFormData.destId=}"
                                            placeholder="请输入BCC实例ID，例如：i-Uu1CIcJH"
                                        >
                                        </s-input>
                                    </s-form-item>
                                    <s-form-item class="source-instance" prop="destIp">
                                        <s-select
                                            width="{{300}}"
                                            loading="{{loadDestIpLoading}}"
                                            s-if="pathFormData.destType === 'BCC' || pathFormData.sourceType === 'BBC'"
                                            datasource="{{destIpDatasource}}"
                                            value="{=pathFormData.destIp=}"
                                        >
                                        </s-select>
                                        <s-input
                                            width="{{300}}"
                                            s-else
                                            placeholder="请输入目的IP地址"
                                            value="{=pathFormData.destIp=}"
                                        >
                                        </s-input>
                                    </s-form-item>
                                </div>
                                <s-form-item label="目的端口：" prop="destPort">
                                    <s-input
                                        width="300"
                                        disabled="{{inputDis}}"
                                        value="{=pathFormData.destPort=}"
                                        placeholder="1-65535"
                                    >
                                    </s-input>
                                </s-form-item>
                            </div>
                        </div>
                    </div>
                </s-form>
            </div>
            <div class="probe" s-if="currentProblem === 'lag'">
                <s-form s-ref="probeFormRef" rules="{{probeValidateRule}}" data="{=probeFormData=}" label-align="left">
                    <s-form-item
                        label="探测名称："
                        prop="name"
                        help="大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65"
                    >
                        <s-input width="{{300}}" value="{=probeFormData.name=}" />
                    </s-form-item>
                    <s-form-item label="所在网络： " prop="vpcId">
                        <s-select
                            width="{{300}}"
                            datasource="{{vpcList}}"
                            value="{=probeFormData.vpcId=}"
                            on-change="vpcChange($event)"
                        >
                        </s-select>
                    </s-form-item>
                    <s-form-item label="所在子网：" prop="subnetId">
                        <s-select
                            width="{{300}}"
                            datasource="{{subnetDatasource}}"
                            value="{=probeFormData.subnetId=}"
                            on-change="subnetChange"
                            disabled="{{loading}}"
                        >
                        </s-select>
                    </s-form-item>
                    <s-form-item class="probe-way-item" label="探测方式：" prop="protocol">
                        <s-radio-radio-group
                            datasource="{{protocolList}}"
                            radioType="button"
                            value="{=probeFormData.protocol=}"
                            on-change="probeProtocolChange($event)"
                        />
                    </s-form-item>
                    <s-form-item
                        s-if="{{probeFormData.protocol === 'UDP'}}"
                        label="探测字符串："
                        class="probe-input"
                        prop="payload"
                    >
                        <div slot="label" class="label_class">
                            {{'探测字符串：'}}
                            <s-tip
                                class="inline-tip"
                                content="用户侧的UDP server根据探测字符串进行回包，若回包则表示网络连通。"
                                skin="question"
                            />
                        </div>
                        <s-input width="{{300}}" value="{=probeFormData.payload=}" />
                    </s-form-item>
                    <s-form-item
                        s-if="{{probeFormData.protocol === 'DNS'}}"
                        label="探测域名："
                        class="probe-input"
                        prop="payload"
                    >
                        <div slot="label" class="label_class">
                            {{'探测域名：'}}
                            <s-tip
                                class="inline-tip"
                                content="DNS服务器对探测域名进行解析并回包，解析成功则表示DNS服务正常。"
                                skin="question"
                            />
                        </div>
                        <s-input width="{{300}}" value="{=probeFormData.payload=}" />
                    </s-form-item>
                    <s-form-item class="probe-frequency-item" label="探测频率：" prop="frequency">
                        <s-radio-radio-group
                            class="frequency-radio"
                            datasource="{{frequencyDatasource}}"
                            radioType="button"
                            value="{=probeFormData.frequency=}"
                        >
                        </s-radio-radio-group>
                        <span class="frequency-unit">次/分钟</span>
                    </s-form-item>
                    <s-form-item label="探测源IP：" prop="sourceIps" class="ip-item">
                        <s-table datasource="{{table.datasource}}" columns="{{table.columns}}">
                            <div slot="empty">暂无数据</div>
                            <div slot="c-ip">
                                <s-radio-radio-group
                                    class="ip-radio"
                                    datasource="{{ipDatasource}}"
                                    value="{{row.isCustom}}"
                                    on-change="typeChange($event, rowIndex)"
                                />
                                <div class="probe-custom-ip-wrapper">
                                    <s-input
                                        s-if="row.isCustom"
                                        placeholder="请输入IP地址"
                                        value="{=row.sourceIp=}"
                                        on-input="inputChange($event, rowIndex)"
                                    />
                                    <label s-if="row.isCustom && row.error" class="probe-custom-ip-err"
                                        >{{ row.error }}</label
                                    >
                                </div>
                            </div>
                            <div slot="c-operation">
                                <s-button skin="stringfy" s-if="!row.primary" on-click="onRemove(rowIndex)"
                                    >删除</s-button
                                >
                            </div>
                        </s-table>
                        <s-tooltip trigger="{{disableAddIp.message ? 'hover' : ''}}" placement="right">
                            <div slot="content">{{ disableAddIp.message }}</div>
                            <s-button skin="stringfy" on-click="addIp()" disabled="{{disableAddIp.disable}}"
                                ><outlined-plus /> 添加源IP</s-button
                            >
                        </s-tooltip>
                    </s-form-item>
                    <div class="destip-wrapper destip-dns" s-if="{{probeFormData.protocol === 'DNS'}}">
                        <div class="destip-port-wrapper">
                            <s-form-item label="探测目的IP：" class="destip-dns-required">
                                <div>
                                    <s-radio-radio-group
                                        class="ip-radio"
                                        datasource="{{dnsDatasource}}"
                                        value="{{dnsRadio}}"
                                        on-change="switch($event)"
                                    />
                                </div>
                                <s-form-item prop="destIp" class="destip">
                                    <s-input
                                        class="destip-input"
                                        width="{{200}}"
                                        value="{=probeFormData.destIp=}"
                                        placeholder="{{dnsRadio ? '请输入探测目的IP' : ''}}"
                                        disabled="{{!dnsRadio}}"
                                    />
                                </s-form-item>
                                <s-form-item prop="destPort" class="destport">
                                    <s-input
                                        class="destip-input"
                                        width="{{200}}"
                                        value="{=probeFormData.destPort=}"
                                        placeholder="{{dnsRadio ? '请输入探测目的端口' : ''}}"
                                        disabled="{{!dnsRadio}}"
                                    />
                                </s-form-item>
                            </s-form-item>
                        </div>
                    </div>
                    <div class="destip-wrapper destip-other" s-else>
                        <s-form-item label="探测目的IP：" prop="destIp" class="destip">
                            <s-input
                                class="destip-input"
                                width="{{200}}"
                                value="{=probeFormData.destIp=}"
                                placeholder="请输入探测目的IP"
                            />
                        </s-form-item>
                        <s-form-item prop="destPort" class="destport" s-if="{{probeFormData.protocol !== 'ICMP'}}">
                            <s-input
                                s-if="{{probeFormData.protocol !== 'ICMP'}}"
                                class="destip-input"
                                width="{{200}}"
                                value="{=probeFormData.destPort=}"
                                placeholder="请输入探测目的端口"
                            />
                        </s-form-item>
                    </div>
                    <s-form-item
                        class="probe-auto-item"
                        s-if="probeFormData.protocol !== 'DNS' || dnsRadio"
                        label="自动生成路由："
                        prop="autoGenerateRouteRule"
                    >
                        <s-switch checked="{=probeFormData.autoGenerateRouteRule=}" on-change="autoGenerateChange" />
                    </s-form-item>
                    <div
                        s-if="probeFormData.autoGenerateRouteRule && (probeFormData.protocol !== 'DNS' || dnsRadio)"
                        class="probe-route-wrap"
                    >
                        <s-form-item label="源端下一跳路由：" prop="nextHopType" class="route-item">
                            <s-form-item class="probe-route-type probe-route-select">
                                <s-select
                                    width="200"
                                    placeholder="请选择路由类型"
                                    datasource="{{routeTypeList}}"
                                    value="{=probeFormData.nextHopType=}"
                                    on-change="nextHopTypeChange"
                                />
                            </s-form-item>
                            <s-form-item prop="nextHop" class="probe-route-select">
                                <s-select
                                    width="200"
                                    filterable
                                    datasource="{{routeList}}"
                                    value="{=probeFormData.nextHop=}"
                                />
                            </s-form-item>
                        </s-form-item>
                    </div>
                    <s-form-item label="描述：" prop="description">
                        <s-input-text-area
                            width="{{300}}"
                            maxLength="{{200}}"
                            height="{{100}}"
                            multiline
                            value="{=probeFormData.description=}"
                        />
                    </s-form-item>
                </s-form>
            </div>
        </div>
        <div class="opt-container">
            <div class="btn-group">
                <s-tooltip placement="top" trigger="{{probeSinDisable.disable ? 'hover' : ''}}">
                    <div slot="content">{{probeSinDisable.message}}</div>
                    <s-button
                        skin="primary"
                        loading="{{diagnosisLoading || allPathLoading}}"
                        disabled="{{probeSinDisable.disable || disableSub}}"
                        on-click="startDiagnosis"
                        class="diagnosis-btn"
                        size="large"
                        >开始诊断</s-button
                    >
                </s-tooltip>
                <s-button size="large" on-click="diagnosisHistory"> 诊断历史 </s-button>
            </div>
        </div>
    </div>
`;

@template(tpl)
@invokeSUI
@asComponent('@diagnosis-connect')
class DiagnosisConnect extends Component {
    static components = {
        's-radio-group': Radio.RadioGroup,
        'outlined-plus': OutlinedPlus,
        's-tip': Tip
    };
    static computed = {
        disableAddIp() {
            let num = this.data.get('table.datasource').length;
            let subnetId = this.data.get('probeFormData.subnetId');
            let {addIp} = checker.check(probeRules, '', 'addIp', {num, subnetId});
            return addIp;
        }
    };
    initData() {
        return {
            connectProblem: [
                {
                    text: '实例间无法访问',
                    value: 'inner'
                },
                {
                    text: '跨VPC的实例不通',
                    value: 'cross'
                },
                {
                    text: '访问云服务延迟大',
                    value: 'lag'
                }
            ],
            currentProblem: 'inner',
            notify: {
                inner: {
                    suggestion:
                        '进出BCC的流量既受安全组规则控制，也受BCC所在子网绑定的网络ACL规则控制，需要确保安全组和网络ACL规则放行。',
                    link: [DocService.acl_index, DocService.security_index],
                    linkName: ['ACL操作指南', '安全组操作指南']
                },
                cross: {
                    suggestion:
                        '跨VPC互访，需要通过购买VPC对等连接、云智能网并配置路由进行打通，生效在BCC上的安全组、网络ACL配置允许规则。',
                    link: [
                        DocService.peer_index,
                        DocService.csn_operation,
                        DocService.acl_index,
                        DocService.security_index
                    ],
                    linkName: ['对等连接操作指南', '云智能网操作指南', '网络ACL操作指南', '安全组操作指南']
                },
                lag: {
                    suggestion:
                        '同一可用区内实例之间的网络延时最小，针对访问延迟大的场景，需要检查访问源和目标是否存在跨可用区的场景。'
                }
            },
            diagnosisMethod: {
                path: {
                    datasource: [
                        {
                            text: '路径分析',
                            value: 'path'
                        }
                    ],
                    value: 'path'
                }
            },
            pathFormData: {
                name: '',
                sourceType: pathTypeList.BCC,
                sourceRegion: '',
                sourceVpcId: '',
                sourceIp: '',
                sourcePort: '',
                sourceId: '',
                destType: pathTypeList.BCC,
                destRegion: '',
                destVpcId: '',
                destIp: '',
                destPort: '',
                destId: '',
                protocol: '',
                description: ''
            },
            protocolDatasource: [
                {
                    text: 'TCP',
                    value: 'tcp'
                },
                {
                    text: 'UDP',
                    value: 'udp'
                },
                {
                    text: 'ICMP',
                    value: 'icmp'
                }
            ],
            validateRule: formValidator(this, 'pathFormData'),
            vpcMap: {},
            pathDisable: {},
            sourceTypeList: pathTypeList.toArray(),
            destTypeDatasource: pathTypeList.toArray().slice(0, 2).concat({text: '混合云网关', value: 'IDC'}),
            sourceRegionList: [{text: '华北-北京', value: 'bj'}],
            sourceIdList: [],
            resVpcList: [],
            desVpcList: [],
            probeFormData: {
                name: '',
                vpcId: '',
                subnetId: '',
                frequency: 20,
                protocol: 'ICMP',
                packetLen: 64,
                payload: '',
                destIp: '',
                destPort: '',
                autoGenerateRouteRule: false,
                nextHopType: '',
                nextHop: '',
                description: ''
            },
            probeValidateRule: probeFormValidator(this, 'probeFormData'),
            subnetDatasource: [],
            initSubnetDatasource: [],
            protocolList: ProbeProtocol.toArray(),
            frequencyDatasource: [
                {text: 10, value: 10},
                {text: 20, value: 20},
                {text: 30, value: 30}
            ],
            table: {
                datasource: [
                    {
                        isCustom: false,
                        primary: true,
                        sourceIp: ''
                    }
                ],
                columns: [
                    {name: 'ip', label: 'IP地址'},
                    {name: 'operation', label: '操作', width: 90}
                ]
            },
            ipDatasource: [
                {
                    text: '自动分配',
                    value: false
                },
                {
                    text: '指定',
                    value: true
                }
            ],
            dnsRadio: false,
            dnsDatasource: [
                {
                    text: '百度智能云DNS服务器',
                    value: false
                },
                {
                    text: '用户自建DNS服务器',
                    value: true
                }
            ],
            routeTypeList: [],
            routeList: [],
            vpcList: [],
            flag: FLAG,
            FLAG,
            allPathArray: [],
            allPathList: [],
            sourceBccExit: true,
            destBccExit: true
        };
    }
    inited() {
        this.getAllPath();
        const regionArray = getVpcAvaliableRegion();
        this.data.set('regionList', regionArray?.regionList);
        this.data.set('pathFormData.sourceRegion', regionArray?.regionList[0].value);
        this.data.set('pathFormData.desRegion', regionArray?.regionList[0].value);
        this.getSourceVpc(regionArray?.regionList[0].value);
        this.getDesVpcList(regionArray?.regionList[0].value);
        this.getInstanceListBySubnets(this.data.get('pathFormData.sourceRegion'));
        this.loadDesBccList(this.data.get('pathFormData.desRegion'));
        this.watch('pathFormData.destId', value => {
            this.data.set('pathFormData.destIp', '');
            if (value) {
                this.setDestIpList();
            }
        });
        this.watch('pathFormData.sourceId', value => {
            this.data.set('pathFormData.sourceIp', '');
            if (value) {
                this.setSourceIpList();
            }
        });
    }

    getGwList(name) {
        let query = {
            vpcId: this.data.get('vpcInfo').shortId,
            pageNo: 1,
            pageSize: 10000
        };
        if (name) {
            query.keywordType = 'name';
            query.keyword = name;
        }
        return this.$http.dcgwList(query, kXhrOptions).then(res => {
            let result = [];
            u.each(res.result, item => {
                if (u.indexOf([DcGatewayStatus.RUNNING], item.status) > -1) {
                    let name = item.name + '/' + item.id;
                    result.push({
                        value: item.id,
                        text: name
                    });
                }
            });
            return Promise.resolve(result);
        });
    }

    loadResVpnList(region) {
        let query = {
            pageNo: 1,
            pageSize: 10000
        };
        let vpcId = this.data.get('pathFormData.vpcId');
        let vpcInfo = u.filter(this.data.get('resVpcs'), item => item.vpcInfo.shortId === vpcId)[0]?.vpcInfo;
        return this.$http
            .getVpnList(query, {
                headers: {region: this.data.get('pathFormData.sourceRegion') || window.$context.getCurrentRegionId()}
            })
            .then(data => {
                let result = [];
                u.each(data.result, item => {
                    if (!vpcId) {
                        if (u.indexOf([VpnStatus.ACTIVE], item.status) > -1) {
                            if (item.vpnType !== 'SSL') {
                                result.push({
                                    value: item.vpnId,
                                    text: `${item.vpnName}/${item.vpnId}`,
                                    vpnType: item.vpnType
                                });
                            }
                        }
                    } else {
                        if (u.indexOf([VpnStatus.ACTIVE], item.status) > -1) {
                            if (item.vpcId === vpcInfo?.vpcId) {
                                if (item.vpnType !== 'SSL') {
                                    result.push({
                                        value: item.vpnId,
                                        text: `${item.vpnName}/${item.vpnId}`,
                                        vpnType: item.vpnType
                                    });
                                }
                            }
                        }
                    }
                });
                this.data.set('sourceIdList', result);
            });
    }

    getInstanceListBySubnets(region) {
        let vpcId = this.data.get('pathFormData.vpcId');
        let vpcInfo = u.filter(this.data.get('resVpcs'), item => item.vpcInfo.shortId === vpcId)[0]?.vpcInfo;
        let payload = {
            pageNo: 1,
            pageSize: 1000,
            serverType: 'BCC',
            enableBid: true,
            isAdvancedSearch: false
        };
        return this.$http.getBccList(payload, {headers: {region}}).then(data => {
            let result = [];
            u.each(data.result, item => {
                if (!vpcId) {
                    if (!u.isEmpty(RouteStatus.fromValue(item.status)) && !item.specificType) {
                        let text = '';
                        text = item.name + (item.internalIp ? `（${item.internalIp}）` : '');
                        result.push({
                            value: item.instanceId,
                            text,
                            id: item.id
                        });
                    }
                } else {
                    if (!u.isEmpty(RouteStatus.fromValue(item.status)) && !item.specificType) {
                        if (item.vpcId === vpcInfo?.vpcId) {
                            let text = '';
                            text = item.name + (item.internalIp ? `（${item.internalIp}）` : '');
                            result.push({
                                value: item.instanceId,
                                text,
                                id: item.id
                            });
                        }
                    }
                }
            });
            this.data.set('sourceIdList', result);
        });
    }

    loadResDcList(value) {
        return this.$http
            .getChannelList({
                type: 'available'
            })
            .then(res => {
                let dcphyList = res.infos.reduce((arr, next) => {
                    let obj = {};
                    obj[next.dcphyId]
                        ? ''
                        : (obj[next.dcphyId] =
                              true &&
                              arr.push({
                                  text: next.id,
                                  value: next.id,
                                  dcphyId: next.dcphyId,
                                  status: next.status
                              }));
                    return arr;
                }, []);
                dcphyList = dcphyList.filter(item => item.dcphyId === value && item.status === 'established');
                this.data.set('connIdList', dcphyList);
            });
    }

    getSourceVpc(region) {
        this.getResVpclist(region).then(res => {
            let vpcList = this.data.get('resVpcList');
            this.getResVpcs(vpcList);
            let vpcs = this.data.get('resVpcs');
            this.data.set(
                'resVpcList',
                vpcs.map(item => ({
                    text: `${item.vpcInfo.name}（${item.vpcInfo.cidr}）`,
                    value: item.vpcInfo.shortId
                }))
            );
        });
    }

    getResVpclist(region) {
        return this.$http.vpcList({}, {headers: {region}}).then(data => {
            let vpcs = u.map(data, item => ({text: `${item.name}（${item.cidr}）`, value: item.vpcId, vpcInfo: item}));
            if (!this.data.get('notSupportAllVpc')) {
                vpcs.unshift({
                    text: '所在网络：全部私有网络',
                    value: ''
                });
            }
            this.data.set('resVpcList', vpcs);
        });
    }

    getResVpcs(data) {
        let vpcs = data
            .filter(item => item.value)
            .map(item => {
                if (item.vpcInfo.ipv6Cidr) {
                    return {
                        ...item,
                        text: `${item.text}(${item.vpcInfo.ipv6Cidr})`
                    };
                } else return item;
            });
        this.data.set('resVpcs', vpcs);
    }

    getDesVpcList(region) {
        this.getDesVpcArray(region).then(res => {
            let vpcList = this.data.get('desVpcList');
            this.getDesVpcs(vpcList);
            let vpcs = this.data.get('desVpcs');
            this.data.set(
                'desVpcList',
                vpcs.map(item => ({
                    text: `${item.vpcInfo.name}（${item.vpcInfo.cidr}）`,
                    value: item.vpcInfo.shortId
                }))
            );
        });
    }

    getDesVpcArray(region) {
        return this.$http.vpcList({}, {headers: {region}}).then(data => {
            let vpcs = u.map(data, item => ({text: `${item.name}（${item.cidr}）`, value: item.vpcId, vpcInfo: item}));
            if (!this.data.get('notSupportAllVpc')) {
                vpcs.unshift({
                    text: '所在网络：全部私有网络',
                    value: ''
                });
            }
            this.data.set('desVpcList', vpcs);
        });
    }

    getDesVpcs(data) {
        let vpcs = data
            .filter(item => item.value)
            .map(item => {
                if (item.vpcInfo.ipv6Cidr) {
                    return {
                        ...item,
                        text: `${item.text}(${item.vpcInfo.ipv6Cidr})`
                    };
                } else return item;
            });
        this.data.set('desVpcs', vpcs);
    }

    desVpcChange({value}) {
        this.data.set('pathFormData.desVpcId', value);
        this.data.set('destIdDatasource', []);
        this.data.set('destIpDatasource', []);
        this.data.set('pathFormData.destId', '');
        this.data.set('pathFormData.destIp', '');
        this.data.set('desTypeLoading', true);
        if (this.data.get('pathFormData.destType') === 'BCC') {
            this.loadDesBccList(this.data.get('pathFormData.desRegion'))
                .then(() => {
                    this.data.set('desTypeLoading', false);
                })
                .catch(() => {
                    this.data.set('desTypeLoading', false);
                });
        } else if (this.data.get('pathFormData.destType') === 'BBC') {
            this.loadDesBbcList(this.data.get('pathFormData.desRegion'))
                .then(() => {
                    this.data.set('desTypeLoading', false);
                })
                .catch(() => {
                    this.data.set('desTypeLoading', false);
                });
        } else if (this.data.get('pathFormData.destType') === 'SUBNET') {
            this.loadDesSubnets(this.data.get('pathFormData.desRegion'))
                .then(() => {
                    this.data.set('desTypeLoading', false);
                })
                .catch(() => {
                    this.data.set('desTypeLoading', false);
                });
        }
    }

    resVpcChange({value}) {
        this.data.set('pathFormData.vpcId', value);
        this.data.set('sourceIdList', []);
        this.data.set('pathFormData.sourceId', '');
        this.data.set('sourceIpDataSource', []);
        this.data.set('pathFormData.sourceIp', '');
        this.data.set('sourceTypeLoading', true);
        if (this.data.get('pathFormData.sourceType') === 'BCC') {
            this.getInstanceListBySubnets(this.data.get('pathFormData.sourceRegion'))
                .then(() => {
                    this.data.set('sourceTypeLoading', false);
                })
                .catch(() => {
                    this.data.set('sourceTypeLoading', false);
                });
        } else if (this.data.get('pathFormData.sourceType') === 'BBC') {
            this.loadBbcList(this.data.get('pathFormData.sourceRegion'))
                .then(() => {
                    this.data.set('sourceTypeLoading', false);
                })
                .catch(() => {
                    this.data.set('sourceTypeLoading', false);
                });
        } else if (this.data.get('pathFormData.sourceType') === 'SUBNET') {
            this.loadResSubnets(this.data.get('pathFormData.sourceRegion'))
                .then(() => {
                    this.data.set('sourceTypeLoading', false);
                })
                .catch(() => {
                    this.data.set('sourceTypeLoading', false);
                });
        } else if (this.data.get('pathFormData.sourceType') === 'DC') {
            this.data.set('connIdList', []);
            this.data.set('pathFormData.connId', '');
            this.loadResDcGwList(this.data.get('pathFormData.sourceRegion'))
                .then(() => {
                    this.data.set('sourceTypeLoading', false);
                })
                .catch(() => {
                    this.data.set('sourceTypeLoading', false);
                });
        } else if (this.data.get('pathFormData.sourceType') === 'VPN_CONN') {
            this.data.set('connIdList', []);
            this.data.set('pathFormData.connId', '');
            this.loadResVpnList(this.data.get('pathFormData.sourceRegion'))
                .then(() => {
                    this.data.set('sourceTypeLoading', false);
                })
                .catch(() => {
                    this.data.set('sourceTypeLoading', false);
                });
        }
    }

    protocolChange({value}) {
        if (value === 'ALL' || value === 'icmp') {
            this.data.set('inputDis', true);
            this.data.set('pathFormData.destPort', '');
            this.data.set('pathFormData.sourcePort', '');
        } else {
            this.data.set('inputDis', false);
        }
    }

    loadResSubnets(region) {
        let vpcId = this.data.get('pathFormData.vpcId');
        let vpcInfo = u.filter(this.data.get('resVpcs'), item => item.vpcInfo.shortId === vpcId)[0]?.vpcInfo;
        let payload = {vpcId: vpcInfo?.vpcId, attachVm: false};
        return this.$http.getSubnetList(payload, {headers: {region}}).then(data => {
            let datasource = [];
            u.each(data, item => {
                // 暂时不支持ipv6先注释掉
                // let text = '';
                // if (item.ipv6Cidr) {
                //   text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}(${item.ipv6Cidr})`;
                // }
                // else {
                //   text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}`;
                // }
                let text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}`;
                datasource.push({
                    value: item.shortId,
                    subnetId: item.subnetId,
                    text: text,
                    cidr: item.cidr,
                    ipv6Cidr: item.ipv6Cidr
                });
            });
            this.data.set('sourceIdList', datasource);
        });
    }

    loadDesSubnets(region) {
        let vpcId = this.data.get('pathFormData.desVpcId');
        let vpcInfo = u.filter(this.data.get('desVpcs'), item => item.vpcInfo.shortId === vpcId)[0]?.vpcInfo;
        let payload = {vpcId: vpcInfo?.vpcId, attachVm: false};
        return this.$http.getSubnetList(payload, {headers: {region}}).then(data => {
            let datasource = [];
            u.each(data, item => {
                // 暂时不支持ipv6先注释掉
                // let text = '';
                // if (item.ipv6Cidr) {
                //   text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}(${item.ipv6Cidr})`;
                // }
                // else {
                //   text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}`;
                // }
                let text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}`;
                datasource.push({
                    value: item.shortId,
                    subnetId: item.subnetId,
                    text: text,
                    cidr: item.cidr,
                    ipv6Cidr: item.ipv6Cidr
                });
            });
            this.data.set('destIdDatasource', datasource);
        });
    }

    loadDesBccList(region) {
        let vpcId = this.data.get('pathFormData.desVpcId');
        let vpcInfo = u.filter(this.data.get('desVpcs'), item => item.vpcInfo.shortId === vpcId)[0]?.vpcInfo;
        let payload = {
            pageNo: 1,
            pageSize: 1000,
            serverType: 'BCC',
            enableBid: true,
            isAdvancedSearch: false
        };
        return this.$http.getBccList(payload, {headers: {region}}).then(data => {
            let result = [];
            u.each(data.result, item => {
                if (!vpcId) {
                    if (!u.isEmpty(RouteStatus.fromValue(item.status)) && !item.specificType) {
                        let text = '';
                        text = item.name + (item.internalIp ? `（${item.internalIp}）` : '');
                        result.push({
                            value: item.instanceId,
                            text,
                            id: item.id
                        });
                    }
                } else {
                    if (!u.isEmpty(RouteStatus.fromValue(item.status)) && !item.specificType) {
                        if (item.vpcId === vpcInfo?.vpcId) {
                            let text = '';
                            text = item.name + (item.internalIp ? `（${item.internalIp}）` : '');
                            result.push({
                                value: item.instanceId,
                                text,
                                id: item.id
                            });
                        }
                    }
                }
            });
            this.data.set('destIdDatasource', result);
        });
    }

    loadDesBbcList(region) {
        let vpcId = this.data.get('pathFormData.desVpcId');
        let vpcInfo = u.filter(this.data.get('desVpcs'), item => item.vpcInfo.shortId === vpcId)[0]?.vpcInfo;
        let payload = {
            pageNo: 1,
            pageSize: 10000
        };
        return this.$http.getBbcList(payload, {headers: {region}}).then(data => {
            let datasource = [];
            u.each(data.result, item => {
                if (!vpcId) {
                    if (!u.isEmpty(RouteStatus.fromValue(item.status))) {
                        let text = '';
                        text = item.name + (item.internalIp ? `（${item.internalIp}）` : '');
                        datasource.push({
                            value: item.instanceId,
                            text,
                            id: item.id
                        });
                    }
                } else {
                    if (item.vpcId === vpcInfo?.vpcId) {
                        if (!u.isEmpty(RouteStatus.fromValue(item.status))) {
                            let text = '';
                            text = item.name + (item.internalIp ? `（${item.internalIp}）` : '');
                            datasource.push({
                                value: item.instanceId,
                                text,
                                id: item.id
                            });
                        }
                    }
                }
            });
            this.data.set('destIdDatasource', datasource);
        });
    }

    loadBbcList(region) {
        let vpcId = this.data.get('pathFormData.vpcId');
        let vpcInfo = u.filter(this.data.get('resVpcs'), item => item.vpcInfo.shortId === vpcId)[0]?.vpcInfo;
        let payload = {
            pageNo: 1,
            pageSize: 10000
        };
        return this.$http.getBbcList(payload, {headers: {region}}).then(data => {
            let datasource = [];
            u.each(data.result, item => {
                if (!vpcId) {
                    if (!u.isEmpty(RouteStatus.fromValue(item.status))) {
                        let text = '';
                        text = item.name + (item.internalIp ? `（${item.internalIp}）` : '');
                        datasource.push({
                            value: item.instanceId,
                            text,
                            id: item.id
                        });
                    }
                } else {
                    if (item.vpcId === vpcInfo?.vpcId) {
                        if (!u.isEmpty(RouteStatus.fromValue(item.status))) {
                            let text = '';
                            text = item.name + (item.internalIp ? `（${item.internalIp}）` : '');
                            datasource.push({
                                value: item.instanceId,
                                text,
                                id: item.id
                            });
                        }
                    }
                }
            });
            this.data.set('sourceIdList', datasource);
        });
    }

    getPathList() {
        let payload = {
            pageNo: 1,
            pageSize: 10000
        };
        this.$http.getPathanaliseList(payload).then(data => {
            this.data.set('pathList', data.result);
        });
    }

    setSourceIpList() {
        let sourceIdList = this.data.get('sourceIdList') || [];
        let sourceId = this.data.get('pathFormData.sourceId');
        if (this.data.get('pathFormData.sourceType') === 'BCC') {
            this.bccIdChange({value: sourceId});
        }
        if (this.data.get('pathFormData.sourceType') === 'SUBNET') {
            let sourceSubnet = sourceIdList.find(item => item.value === sourceId).cidr || '';
            this.data.set('sourceSubnet', sourceSubnet);
        }
        if (this.data.get('pathFormData.sourceType') === 'BBC') {
            let id = sourceIdList.find(item => item.value === sourceId).id || '';
            this.getResEnics(id);
        }
    }

    getResEnics(instanceId) {
        let payload = {
            instanceId
        };
        this.$http
            .getBbcEnics(payload, this.data.get('pathFormData.sourceRegion') || window.$context.getCurrentRegionId())
            .then(res => {
                let result =
                    res[0]?.privateIpSet.map(item => {
                        return {
                            text: item.privateIpAddress,
                            value: item.privateIpAddress
                        };
                    }) || [];
                this.data.set('sourceIpDataSource', result);
            });
    }

    getDesEnics(instanceId) {
        let payload = {
            instanceId
        };
        this.$http
            .getBbcEnics(payload, this.data.get('pathFormData.desRegion') || window.$context.getCurrentRegionId())
            .then(res => {
                let result =
                    res[0]?.privateIpSet.map(item => {
                        return {
                            text: item.privateIpAddress,
                            value: item.privateIpAddress
                        };
                    }) || [];
                this.data.set('destIpDatasource', result);
            });
    }

    getResBccEnics(instanceId) {
        let payload = {
            instanceId
        };
        this.data.set('loadIpLoading', true);
        this.$http
            .getBccEnics(payload, this.data.get('pathFormData.sourceRegion') || window.$context.getCurrentRegionId())
            .then(res => {
                let result = [];
                res.enis.map(item => {
                    item.ips?.map(i => {
                        if (IP.test(i.privateIp)) {
                            result.push({
                                text: i.privateIp,
                                value: i.privateIp
                            });
                        }
                    });
                });
                this.data.set('sourceIpDataSource', result);
            })
            .finally(() => this.data.set('loadIpLoading', false));
    }

    getDesBccEnics(instanceId) {
        let payload = {
            instanceId
        };
        this.data.set('loadDestIpLoading', true);
        this.$http
            .getBccEnics(payload, this.data.get('pathFormData.desRegion') || window.$context.getCurrentRegionId())
            .then(res => {
                let result = [];
                res.enis.map(item => {
                    item.ips?.map(i => {
                        if (IP.test(i.privateIp)) {
                            result.push({
                                text: i.privateIp,
                                value: i.privateIp
                            });
                        }
                    });
                });
                this.data.set('destIpDatasource', result);
            })
            .finally(() => this.data.set('loadDestIpLoading', false));
    }

    getVpnConnList(vpnId, vpnType) {
        let payload = {
            vpnId,
            vpnType
        };
        this.$http.vpnConnList(payload, this.data.get('pathFormData.sourceRegion')).then(res => {
            let array = [];
            u.each(res, item => {
                array.push({
                    value: item.vpnConnId,
                    text: `${item.vpnConnName}/${item.vpnConnId}`
                });
            });
            this.data.set('connIdList', array);
        });
    }

    loadResDcGwList(region) {
        let vpcId = this.data.get('pathFormData.vpcId');
        let vpcInfo = u.filter(this.data.get('resVpcs'), item => item.vpcInfo.shortId === vpcId)[0]?.vpcInfo;
        let payload = {
            pageNo: 1,
            pageSize: 10000
        };
        return this.$http
            .getChannelList({
                type: 'available'
            })
            .then(res => {
                let dcphyList = res.infos.reduce((arr, next) => {
                    let obj = {};
                    obj[next.dcphyId]
                        ? ''
                        : (obj[next.dcphyId] =
                              true &&
                              arr.push({
                                  text: next.id,
                                  value: next.id,
                                  dcphyId: next.dcphyId,
                                  status: next.status
                              }));
                    return arr;
                }, []);
                dcphyList = dcphyList.filter(item => item.status === 'established');
                this.data.set('connIdList', dcphyList);
            });
    }

    setDestIpList() {
        let destIdDatasource = this.data.get('destIdDatasource') || [];
        let destId = this.data.get('pathFormData.destId');
        if (this.data.get('pathFormData.destType') === 'BCC') {
            // let id = destIdDatasource.find(item => item.value === destId).id || '';
            this.bccDestIdChange({value: destId});
        }
        if (this.data.get('pathFormData.destType') === 'SUBNET') {
            let desSubnet = destIdDatasource.find(item => item.value === destId).cidr || '';
            this.data.set('desSubnet', desSubnet);
        }
        if (this.data.get('pathFormData.destType') === 'BBC') {
            let id = destIdDatasource.find(item => item.value === destId).id || '';
            this.getDesEnics(id);
        }
    }

    sourceRegionChange({value}) {
        this.data.set('resVpcList', []);
        this.data.set('sourceIdList', []);
        this.data.set('pathFormData.vpcId', '');
        this.data.set('pathFormData.sourceId', '');
        this.data.set('sourceIpDataSource', []);
        this.data.set('pathFormData.sourceIp', '');
        this.getSourceVpc(value);
        if (this.data.get('pathFormData.sourceType') === 'BCC') {
            this.getInstanceListBySubnets(value);
        } else if (this.data.get('pathFormData.sourceType') === 'BBC') {
            this.loadBbcList(value);
        } else if (this.data.get('pathFormData.sourceType') === 'SUBNET') {
            this.loadResSubnets(value);
        } else if (this.data.get('pathFormData.sourceType') === 'VPN_CONN') {
            this.data.set('connIdList', []);
            this.data.set('pathFormData.connId', '');
            this.loadResVpnList(value);
        } else if (this.data.get('pathFormData.sourceType') === 'DC') {
            this.data.set('connIdList', []);
            this.data.set('pathFormData.connId', '');
            this.loadResDcGwList(value);
        }
    }

    sourceTypeChange({value}) {
        this.data.set('pathFormData.sourceId', '');
        this.data.set('pathFormData.sourceIp', '');
        this.data.set('sourceIdList', []);
        this.data.set('sourceIpDataSource', []);
        if (value === 'BCC') {
            this.getInstanceListBySubnets(this.data.get('pathFormData.sourceRegion'));
        } else if (value === 'BBC') {
            this.loadBbcList(this.data.get('pathFormData.sourceRegion'));
        } else if (value === 'SUBNET') {
            this.loadResSubnets(this.data.get('pathFormData.sourceRegion'));
        } else if (value === 'VPN_CONN') {
            this.data.set('connIdList', []);
            this.data.set('pathFormData.connId', '');
            this.loadResVpnList(this.data.get('pathFormData.sourceRegion'));
        } else if (value === 'DC') {
            this.data.set('connIdList', []);
            this.data.set('pathFormData.connId', '');
            this.loadResDcGwList(this.data.get('pathFormData.sourceRegion'));
        }
    }

    sourceIdChange({value}) {
        if (this.data.get('pathFormData.sourceType') === 'VPN_CONN') {
            this.data.set('connIdList', []);
            this.data.set('pathFormData.connId', '');
            let sourceIdList = this.data.get('sourceIdList');
            let type = sourceIdList.find(item => item.value === value)?.vpnType;
            this.getVpnConnList(value, type.toLowerCase());
        } else if (this.data.get('pathFormData.sourceType') === 'DC') {
            this.data.set('connIdList', []);
            this.data.set('pathFormData.connId', '');
            this.loadResDcList(value);
        }
    }

    desRegionChange({value}) {
        this.data.set('desVpcList', []);
        this.data.set('destIdDatasource', []);
        this.data.set('destIpDatasource', []);
        this.data.set('pathFormData.destId', '');
        this.data.set('pathFormData.desVpcId', '');
        this.data.set('pathFormData.destIp', '');
        this.getDesVpcList(value);
        if (this.data.get('pathFormData.sourceType') === 'BCC') {
            this.loadDesBccList(value);
        } else if (this.data.get('pathFormData.sourceType') === 'BBC') {
            this.loadDesBbcList(value);
        } else if (this.data.get('pathFormData.sourceType') === 'SUBNET') {
            this.loadDesSubnets(value);
        }
    }

    destTypeChange({value}) {
        this.data.set('destIdDatasource', []);
        this.data.set('destIpDatasource', []);
        this.data.set('pathFormData.destId', '');
        this.data.set('pathFormData.destIp', '');
        if (value === 'BCC') {
            this.loadDesBccList(this.data.get('pathFormData.desRegion'));
        } else if (value === 'BBC') {
            this.loadDesBbcList(this.data.get('pathFormData.desRegion'));
        } else if (value === 'SUBNET') {
            this.loadDesSubnets(this.data.get('pathFormData.desRegion'));
        }
    }

    hasRepeat(arr) {
        const nums = [];
        for (let i = 0; i < arr.length; i++) {
            if (nums.includes(arr[i])) {
                return true;
            } else if (arr[i] !== '') {
                nums.push(arr[i]);
            }
        }
        return false;
    }

    sourceIpCheck(index, val) {
        let customIpList = this.data.get('table.datasource').map(item => item.sourceIp);
        let probeFormData = this.data.get('probeFormData');
        if (!probeFormData.subnetId) {
            this.data.set(`table.datasource[${index}].error`, '请选择所在子网');
            return;
        }
        if (!val) {
            this.data.set(`table.datasource[${index}].error`, '请填写IP地址');
            return;
        }
        if (!RULE.IP.test(val)) {
            this.data.set(`table.datasource[${index}].error`, 'IP地址格式有误');
            return;
        }
        if (this.hasRepeat(customIpList)) {
            this.data.set(`table.datasource[${index}].error`, '源IP地址不能重复');
            return;
        }
        const subnetItem = u.find(this.data.get('initSubnetDatasource'), item => item.value === probeFormData.subnetId);
        if (subnetItem && !checkIsInSubnet(val + '/32', subnetItem.cidr)) {
            this.data.set(`table.datasource[${index}].error`, '不属于所在子网');
            return;
        }
        this.data.set(`table.datasource[${index}].error`, '');
    }

    // 路径分析结果轮询，超过30s终止
    loadAnalyze(pathName, anylyzeId) {
        let count = 0;
        const timer = setInterval(() => {
            this.$http.pathanaliseSpecDetail(anylyzeId).then(res => {
                if (res.status !== AnalyzeStatus.ANALISING) {
                    this.data.set('diagnosisLoading', false);
                    clearInterval(timer);
                    const bcm = new BcmDetail({
                        data: {
                            content: res,
                            name: pathName
                        }
                    });
                    bcm.attach(document.body);
                } else if (count >= 29) {
                    Notification.warning('诊断时间超时，请前往路径分析查看诊断状态');
                    this.data.set('diagnosisLoading', false);
                    clearInterval(timer);
                } else {
                    count++;
                }
            });
        }, 1000);
    }

    // 完整form校验，包含自定义IP的校验
    async completeCheck() {
        return new Promise((resolve, reject) => {
            this.ref('probeFormRef')
                .validateFields()
                .then(() => {
                    let ipList = this.data.get('table.datasource');
                    for (let i = 0; i < ipList.length; i++) {
                        if (ipList[i].isCustom) {
                            this.sourceIpCheck(i, ipList[i].sourceIp);
                        }
                        let err = this.data.get(`table.datasource[${i}]`).error;
                        if (err) {
                            reject();
                            return;
                        }
                    }
                    resolve();
                })
                .catch(e => {
                    reject(e);
                });
        });
    }

    async startDiagnosis() {
        if (this.data.get('currentProblem') === 'inner' || this.data.get('currentProblem') === 'cross') {
            const form = this.ref('pathFormRef');
            form.validateFields().then(() => {
                let pathFormData = this.data.get('pathFormData');
                let payload = {
                    name: pathFormData.name,
                    description: pathFormData.description,
                    sourceType: pathFormData.sourceType,
                    sourceRegion: pathFormData.sourceRegion,
                    sourceIp: pathFormData.sourceIp,
                    sourceVpcId: pathFormData.vpcId,
                    sourcePort: pathFormData.sourcePort ? Number(pathFormData.sourcePort) : pathFormData.sourcePort,
                    sourceId: pathFormData.sourceId,
                    destType: pathFormData.destType,
                    destRegion: pathFormData.desRegion,
                    destVpcId: pathFormData.desVpcId,
                    destPort: pathFormData.destPort ? Number(pathFormData.destPort) : pathFormData.destPort,
                    destIp: pathFormData.destIp,
                    destId: pathFormData.destId,
                    protocol: pathFormData.protocol
                };
                if (payload.protocol === 'ALL') {
                    delete payload.protocol;
                }
                if (payload.sourceType === 'VPN_CONN' || pathFormData.sourceType === 'DC') {
                    payload.sourceId = pathFormData.connId;
                }
                if (payload.destType === 'IDC') {
                    delete payload.destId;
                    delete payload.destVpcId;
                    delete payload.destRegion;
                }
                if (pathFormData.sourceType === 'DC') {
                    delete payload.sourceVpcId;
                }
                let pathObj = {
                    destId: payload.destId || '',
                    destIp: payload.destIp || '',
                    destPort: payload.destPort || '',
                    sourceId: payload.sourceId || '',
                    sourceIp: payload.sourceIp || '',
                    sourcePort: payload.sourcePort || '',
                    protocol: payload.protocol || 'all'
                };
                let allPathArray = this.data.get('allPathArray');
                this.data.set('diagnosisLoading', true);
                if (this.ruleIndexOfTable(pathObj, allPathArray) !== -1) {
                    let index = this.ruleIndexOfTable(pathObj, allPathArray);
                    let obj = this.data.get('allPathList')[index];
                    this.$http
                        .startPathanalise(obj.shortId)
                        .then(res => {
                            this.loadAnalyze(obj.name, res.id);
                        })
                        .catch(() => {
                            this.data.set('diagnosisLoading', false);
                        });
                    return;
                }
                this.$http
                    .createPathanalise(payload)
                    .then(res => {
                        this.getAllPath();
                        const pathId = res.id;
                        if (pathId) {
                            this.$http
                                .startPathanalise(pathId)
                                .then(res => {
                                    this.loadAnalyze(pathFormData.name, res.id);
                                })
                                .catch(() => {
                                    this.data.set('diagnosisLoading', false);
                                });
                        } else {
                            this.data.set('diagnosisLoading', false);
                        }
                    })
                    .catch(() => {
                        this.data.set('diagnosisLoading', false);
                    });
            });
        } else if (this.data.get('currentProblem') === 'lag') {
            await this.completeCheck();
            let probeFormData = this.data.get('probeFormData');
            delete probeFormData.vpcMap;
            let sourceIpList = this.data.get('table.datasource');
            let sourceIpNum = 0;
            let sourceIps = [];
            u.forEach(sourceIpList, item => {
                if (item.isCustom) {
                    sourceIps.push(item.sourceIp);
                } else {
                    sourceIpNum++;
                }
            });

            let payload = {
                ...probeFormData,
                sourceIps,
                sourceIpNum
            };
            if (sourceIpNum < 1) {
                delete payload.sourceIpNum;
            }
            this.data.set('diagnosisLoading', true);
            this.$http
                .createProbe(payload)
                .then(res => {
                    location.hash = `#/vpc/probe/detail?vpcId=${res.vpcId}&probeId=${res.probeId}`;
                })
                .catch(() => {
                    this.data.set('diagnosisLoading', false);
                });
        }
    }

    diagnosisHistory() {
        if (this.data.get('currentProblem') === 'inner' || this.data.get('currentProblem') === 'cross') {
            location.hash = '#/vpc/pathanalise/list';
        } else if (this.data.get('currentProblem') === 'lag') {
            location.hash = '#/vpc/probe/list';
        }
    }

    autoGenerateChange() {
        this.data.set('probeFormData.nextHopType', '');
        this.data.set('probeFormData.nextHop', '');
    }

    typeChange(e, index) {
        this.data.set(`table.datasource[${index}].error`, '');
        this.data.set(`table.datasource[${index}].sourceIp`, '');
        this.data.set(`table.datasource[${index}].isCustom`, e.value);
    }

    inputChange(e, index) {
        this.data.set(`table.datasource[${index}].sourceIp`, e.value);
        this.sourceIpCheck(index, e.value);
    }

    onRemove(rowIndex) {
        this.data.removeAt('table.datasource', rowIndex);
    }

    addIp() {
        this.data.push('table.datasource', {
            isCustom: false,
            primary: false,
            sourceIp: ''
        });
    }

    nextHopTypeChange({value}) {
        this.data.set('routeList', []);
        this.data.set('probeFormData.nextHop', '');
        this.loadSource(value);
    }

    loadSource(value) {
        let type = value || this.data.get('probeFormData.nextHopType');
        const requesetMap = {
            custom: this.getInstanceListBySubnets,
            nat: this.getNatList,
            vpn: this.getVpnList,
            peerConn: this.getPeerConnList,
            dcGateway: this.getGwList,
            vpc2tgw: this.getTgwList
        };
        if (!requesetMap[type]) {
            return;
        }
        return requesetMap[type].apply(this).then(res => {
            this.data.set('routeList', res);
        });
    }

    enableVpnRegion() {
        let region = window.$context.getCurrentRegionId();
        if (region === AllRegion.HK02) {
            return true;
        }
        return u.indexOf(disable_vpn_region, region) === -1;
    }

    natWhiteList() {
        return this.data.get('flag.NetworkSupportNat');
    }

    peerconnWhiteList() {
        const serversType = Object.keys(window.$context.SERVICE_TYPE);
        return serversType.indexOf('PEERCONN') > -1 && window.$context.getCurrentRegionId() !== AllRegion.BJKS;
    }

    // 不在白名单的路由类型不展示
    getWhiteList() {
        // 先隐藏掉实例，nat，vpn
        let all = [this.enableVpnRegion(), this.natWhiteList(), this.peerconnWhiteList()];
        Promise.all(all).then(white => {
            let [VPN, NAT, PEERCONN] = white;
            const whiteList = [];
            const whiteMap = {
                CUSTOM: true,
                NAT: NAT,
                VPN: VPN,
                PEERCONN: PEERCONN,
                GW: true,
                TGW: true
            };
            u.each(whiteMap, (item, key) => {
                item && whiteList.push(key);
            });
            let routeTypeList = RouteType.toArray(...whiteList);
            this.data.set('routeTypeList', routeTypeList);
        });
    }

    getVpcs(data) {
        let vpcs = data
            .filter(item => item.value)
            .map(item => {
                if (item.vpcInfo.ipv6Cidr) {
                    return {
                        ...item,
                        text: `${item.text}(${item.vpcInfo.ipv6Cidr})`
                    };
                } else return item;
            });
        this.data.set('vpcs', vpcs);
    }

    switch(e) {
        this.data.set(`dnsRadio`, e.value);
        this.probeProtocolChange(e);
    }

    probeProtocolChange(e) {
        let dnsRadio = this.data.get('dnsRadio');
        if ((e.value === 'DNS' || !e.value) && !dnsRadio) {
            this.data.set('probeFormData.destIp', '***************');
            this.data.set('probeFormData.destPort', '53');
        } else {
            this.data.set('probeFormData.destIp', '');
            this.data.set('probeFormData.destPort', '');
        }
    }

    initSourceIp() {
        this.data.set('table.datasource', [
            {
                isCustom: false,
                primary: true,
                sourceIp: ''
            }
        ]);
    }

    getProbeQuota() {
        let vpcId = this.data.get('probeFormData.vpcId');
        let vpcInfo = u.filter(this.data.get('vpcs'), item => item.vpcInfo.shortId === vpcId)[0]?.vpcInfo;
        return this.$http.getProbeQuota({vpcId: vpcInfo.vpcId}).then(data => this.data.set('quotaFree', data.free));
    }

    loadSubnets() {
        let vpcId = this.data.get('probeFormData.vpcId');
        let vpcInfo = u.filter(this.data.get('vpcs'), item => item.vpcInfo.shortId === vpcId)[0]?.vpcInfo;
        let payload = {vpcId: vpcInfo.vpcId};
        this.data.set('loading', true);
        this.$http
            .getSubnetList(payload)
            .then(data => {
                let datasource = [];
                u.each(data, item => {
                    // 暂时不支持ipv6先注释掉
                    // let text = '';
                    // if (item.ipv6Cidr) {
                    //   text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}(${item.ipv6Cidr})`;
                    // }
                    // else {
                    //   text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}`;
                    // }
                    let text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}`;
                    datasource.push({
                        value: item.shortId,
                        subnetId: item.subnetId,
                        text: text,
                        cidr: item.cidr,
                        ipv6Cidr: item.ipv6Cidr
                    });
                });
                this.data.set('subnetDatasource', datasource);
                this.data.set('initSubnetDatasource', datasource);
            })
            .finally(() => this.data.set('loading', false));
    }

    vpcChange(e) {
        this.data.set('probeFormData.vpcId', e.value);
        this.data.set('probeFormData.nextHopType', '');
        this.data.set('probeFormData.nextHop', '');
        this.data.set('probeFormData.routeList', []);
        this.data.set('probeFormData.subnetId', '');
        this.initSourceIp();
        this.getProbeQuota();
        this.loadSubnets();
    }

    problemChange({value}) {
        if (value === 'lag') {
            if (window.$context.getCurrentRegionId() === AllRegion.SIN) {
                this.data.set('probeSinDisable.disable', true);
                this.data.set('probeSinDisable.message', '新加坡地域资源售罄，请您切换到其他地域创建');
            }
            if (FLAG.NetworkSupportXS) {
                this.data.set('dnsDatasource[0].text', '智能云DNS服务器');
            }
            this.getWhiteList();
            let vpcList = window.$storage.get('vpcList');
            this.getVpcs(vpcList);
            let vpcs = this.data.get('vpcs');
            let vpcMap = {};
            u.each(vpcs, item => {
                vpcMap[item.value] = item.text;
            });
            this.data.set(
                'vpcList',
                vpcs.map(item => ({
                    text: `${item.vpcInfo.name}（${item.vpcInfo.cidr}）`,
                    value: item.vpcInfo.shortId
                }))
            );
            this.data.set('probeFormData.vpcMap', vpcMap);
        }
    }
    getAllPath() {
        let payload = {
            pageNo: 1,
            pageSize: 100000
        };
        this.data.set('allPathLoading', true);
        this.$http
            .getPathanaliseList(payload)
            .then(data => {
                let array = data.result.map(item => {
                    return {
                        destId: item.destId || '',
                        destIp: item.destIp || '',
                        destPort: item.destPort || '',
                        sourceId: item.sourceId || '',
                        sourceIp: item.sourceIp || '',
                        sourcePort: item.sourcePort || '',
                        protocol: item.protocol || ''
                    };
                });
                this.data.set('allPathArray', array);
                this.data.set('allPathList', data.result);
                this.data.set('allPathLoading', false);
            })
            .catch(e => {
                this.data.set('allPathLoading', false);
            });
    }
    ruleIndexOfTable(rule, datasource) {
        var index = -1;
        u.find(datasource, (x, i) => {
            if (this.equal(x, rule)) {
                index = i;
                return true;
            }
        });
        return index;
    }
    equal(a, b) {
        //取对象属性名
        var aProps = Object.getOwnPropertyNames(a);
        let num = 0;
        //循环取出属性名，再判断属性值是否一致
        for (var i = 0; i < aProps.length; i++) {
            var propName = aProps[i];
            if (a[propName] === b[propName]) {
                num++;
            }
        }
        return num === aProps.length;
    }
    bccIdChange({value}) {
        this.data.set('sourceBccExit', true);
        if (!value) {
            return;
        }
        if (value.length !== 10 || !value.startsWith('i-')) {
            return;
        }
        let str = value.slice(2);
        if (!/[a-zA-Z0-9]{8}$/.test(str)) {
            return;
        }
        let payload = {
            pageNo: 1,
            pageSize: 1000,
            serverType: 'BCC',
            enableBid: true,
            isAdvancedSearch: false,
            filters: [
                {
                    keyword: value,
                    keywordType: 'instanceId',
                    subKeywordType: ''
                }
            ]
        };
        this.data.set('loadExitBccLoading', true);
        this.$http
            .getBccList(payload, {headers: {region: this.data.get('pathFormData.sourceRegion')}})
            .then(async res => {
                if (res.result && res.result[0]?.id) {
                    this.data.set('sourceBccExit', true);
                    this.getResBccEnics(res.result[0]?.id);
                } else {
                    this.data.set('sourceBccExit', false);
                    await this.ref('form')?.validateFields(['sourceId']);
                }
                this.data.set('loadExitBccLoading', false);
            })
            .catch(e => this.data.set('loadExitBccLoading', false));
    }
    bccDestIdChange({value}) {
        this.data.set('destBccExit', true);
        if (!value) {
            return;
        }
        if (value.length !== 10 || !value.startsWith('i-')) {
            return;
        }
        let str = value.slice(2);
        if (!/[a-zA-Z0-9]{8}$/.test(str)) {
            return;
        }
        let payload = {
            pageNo: 1,
            pageSize: 1000,
            serverType: 'BCC',
            enableBid: true,
            isAdvancedSearch: false,
            filters: [
                {
                    keyword: value,
                    keywordType: 'instanceId',
                    subKeywordType: ''
                }
            ]
        };
        this.data.set('loadExitDestBccLoading', true);
        this.$http
            .getBccList(payload, {headers: {region: this.data.get('pathFormData.desRegion')}})
            .then(async res => {
                if (res.result && res.result[0]?.id) {
                    this.data.set('destBccExit', true);
                    this.getDesBccEnics(res.result[0]?.id);
                } else {
                    this.data.set('destBccExit', false);
                    await this.ref('form')?.validateFields(['destId']);
                }
                this.data.set('loadExitDestBccLoading', false);
            })
            .catch(e => this.data.set('loadExitDestBccLoading', false));
    }
}
export default Processor.autowireUnCheckCmpt(DiagnosisConnect);
