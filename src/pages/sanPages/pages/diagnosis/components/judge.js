/**
 * @file  judge.js
 *
 * <AUTHOR>
 */

// 用来判断此时a与[x,y]的关系，同时通过direction来获取范围
// direction > 1 相较当前值增大，并满足范围
export const judgeOneNone = (range, target, direction) => {
    if (direction) {
        if (range[0] <= target && range[1] >= target) {
            if (direction > 0) {
                return target + ',' + range[1];
            } else {
                return range[0] + ',' + target;
            }
        }
        if (target < range[0]) {
            if (direction > 0) {
                return range[0] + ',' + range[1];
            }
            return '';
        }
        if (target > range[1]) {
            if (direction < 0) {
                return range[0] + ',' + range[1];
            }
            return '';
        }
    } else {
        return range[0] + ',' + range[1];
    }
};

// 此时已保证自动审核的范围小于手动审核的范围
export const judgeRange = (auto, manual, target, direction) => {
    if (direction) {
        if (direction > 0) {
            // 50,100 200,300
            // 75
            if (auto[0] < target && auto[1] > target) {
                if ((manual[0] - auto[1] === 1) || (manual[0] - auto[1] === 0)) {
                    return target + ',' + manual[1];
                }
                return target + ',' + auto[1] + '、' + manual[0] + ',' + manual[1];
            }
            // 220
            if (manual[0] < target && manual[1] > target) {
                return target + ',' + manual[1];
            }
            // 150
            if (auto[1] <= target && manual[0] >= target) {
                return manual[0] + ',' + manual[1];
            }
            // 20
            if (target <= auto[0]) {
                if ((manual[0] - auto[1] === 1) || (manual[0] - auto[1] === 0)) {
                    return auto[0] + ',' + manual[1];
                }
                return auto[0] + ',' + auto[1] + '、' + manual[0] + ',' + manual[1];
            }
            return '';
        } else {
            if (auto[0] < target && auto[1] > target) {
                return auto[0] + ',' + target;
            }
            if (manual[0] < target && manual[1] > target) {
                if ((manual[0] - auto[1] === 1) || (manual[0] - auto[1] === 0)) {
                    return auto[0] + ',' + target;
                }
                return auto[0] + ',' + auto[1] + '、' + manual[0] + ',' + target;
            }
            if (auto[1] < target && manual[0] > target) {
                return auto[0] + ',' + auto[1];
            }
            if (target > manual[1]) {
                if ((manual[0] - auto[1] === 1) || (manual[0] - auto[1] === 0)) {
                    return auto[0] + ',' + manual[1];
                }
                return auto[0] + ',' + auto[1] + '、' + manual[0] + ',' + manual[1];
            }
            return '';
        }
    } else {
        if ((manual[0] - auto[1] === 1) || (manual[0] - auto[1] === 0)) {
            return auto[0] + ',' + manual[1];
        }
        return auto[0] + ',' + auto[1] + '、' + manual[0] + ',' + manual[1];
    }
};
