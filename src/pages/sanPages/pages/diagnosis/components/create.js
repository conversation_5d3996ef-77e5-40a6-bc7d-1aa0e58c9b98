/*
 * @description: 创建配额告警弹窗
 */
import _ from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
const {asComponent, invokeSUI, invokeSUIBIZ, template, invokeAppComp} = decorators;
import {
    alarmType,
    alarmConfig,
    eipServiceType,
    computProductType,
    databaseProductType,
    manageProductType
} from '../../../common/enum';
import {ContextService} from '../../../common';
import './create.less';

const AllRegion = ContextService.getEnum('AllRegion');

const formValidator = self => ({
    name: [
        {required: true, message: '名称必填'},
        {
            pattern: /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{2,49}$/,
            message: '长度限制为3-50个字符，以中英文开头，支持大小写字母，数字和 - _ . /'
        }
    ],
    thresholdType: [{required: true, message: '不能为空'}],
    alertType: [{required: true, message: '不能为空'}],
    threshold: [
        {required: true, message: '不能为空'},
        {
            validator(rule, value, callback) {
                let source = self.data.get('formData');
                var reg = new RegExp(/^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,}[0-9]{0,}$/);
                if (!value) {
                    return callback('不能为空');
                }
                if (!reg.test(value)) {
                    return callback('请填写大于0的数字');
                }
                if (source.thresholdType === 'PERCENTAGE') {
                }
                return callback();
            }
        }
    ],
    quotaName: [{required: true, message: '不能为空'}],
    serviceType: [{required: true, message: '不能为空'}],
    region: [{required: true, message: '不能为空'}]
});

const tpl = html`
<template>
<s-dialog
    class="quota-alarm-dialog {{isCheck ? 'dialog-none-footer' : ''}}"
    closeAfterMaskClick="{{false}}"
    open="{=open=}"
    title="{{title}}">
    <s-loading s-if="{{!isCreate}}" loading="{{loading}}" class="loading"></s-loading>
    <s-form
        s-ref="form"
        s-if="{{isCreate || !loading}}"
        rules="{{rules}}"
        data="{=formData=}"
        label-align="left"
    >
        <s-form-item label="配额名称："
            class="alarm-form-flex"
            s-if="{{hasService}}"
        >
            <span>{{row.name || '-'}}</span>
        </s-form-item>
        <s-form-item label="配额描述："
            class="alarm-form-flex"
            s-if="{{hasService}}"
        >
            <span>{{row.description || '-'}}</span>
        </s-form-item>
        <s-form-item label="已使用/全部配额："
            s-if="{{hasService}}"
            class="alarm-form-flex"
        >
            <span>{{row.used || '-'}} / {{row.value}}</span>
        </s-form-item>
        <s-form-item label="地域："
            s-if="{{hasService}}"
            class="alarm-form-flex"
        >
            <span>{{row.region | getRegion}}</span>
        </s-form-item>
        <s-form-item label="产品名称："
            prop="serviceType"
            s-if="{{!hasService}}"
        >
            <s-cascader
                datasource="{{productDst}}"
                value="{=formData.serviceType=}"
                width="300"
                on-change="serviceTypeChange">
            </s-cascader>
        </s-form-item>
        <s-form-item label="地域："
            prop="region"
            s-if="{{!hasService && showRegion}}"
        >
            <s-select
                width="292"
                datasource="{{regionList}}"
                value="{=formData.region=}" />
        </s-form-item>
        <s-form-item label="配额名称："
            s-if="{{!hasService}}"
            prop="quotaName"
        >
            <s-select
                width="292"
                value="{=formData.quotaName=}"
                on-change="quotaNameChange">
                <s-tooltip
                    s-for="item in quotaNameList"
                    trigger="{{item.disabled ? 'hover' : ''}}"
                    placement="top"
                    content="{{'当前配额不支持用量查询，暂不支持配置报警'}}"
                >
                    <s-select-option
                        value="{{item.value}}"
                        label="{{item.text}}"
                        disabled="{{item.disabled}}"
                    ></s-select-option>
                </s-tooltip>
            </select>
        </s-form-item>
        <!--<s-form-item label="配额描述："
            s-if="{{!hasService && showItem}}"
            class="alarm-form-flex"
        >
             <span>1</span>
        </s-form-item>

        <s-form-item label="已使用/全部配额："
            s-if="{{!hasService && showItem}}"
            class="alarm-form-flex"
        >
            <span>1</span>
        </s-form-item>-->

        <s-form-item label="告警名称："
            s-if="{{!isCheck}}"
            prop="name"
            help="以中英文开头，支持大小写字母，数字和'-_/.'3-50字符."
        >
            <s-input width="{{300}}" value="{=formData.name=}" />
        </s-form-item>
        <s-form-item
            s-if="{{!isCheck}}"
            label="告警类型："
            prop="alertType"
        >
            <s-radio-radio-group
                datasource="{{alarmTypeList}}"
                radioType="button"
                value="{=formData.alertType=}"
            >
            <s-radio-radio-group/>
        </s-form-item>
        <s-form-item
            s-if="{{!isCheck}}"
            label="告警配置方式："
            prop="thresholdType"
            class="{{errorShow ? 'errorShow_class' : ''}}"
        >
            <s-radio-radio-group
                datasource="{{alarmConfigLost}}"
                radioType="button"
                value="{=formData.thresholdType=}"
                on-change="onChange"
            >
            <s-radio-radio-group/>
        </s-form-item>
        <p class="error_class" s-if="{{errorShow}}">当前告警已存在</p>
        <s-form-item
            label="告警阈值："
            s-if="{{!isCheck}}"
            prop="threshold"
        >
            <s-input width="{{300}}" value="{=formData.threshold=}" />
        </s-form-item>
        <!--<s-form-item
            label="告警回调："
            s-if="{{!isCheck}}"
            prop="callBackURL"
        >
            <template slot="label">
                {{'告警回调：'}}
                <s-tip skin="warning"
                    class="inline-tip"
                    content="{{'报警回调，可填写公网可访问到的URL作为告警回掉地址（域名或IP[:端口][/path]），请及时把报警消息推送到该地址'}}"/>
            </template>
            <s-input width="{{300}}" value="{=formData.callBackURL=}" />
        </s-form-item>-->
        <s-form-item prop="desc" label="{{'告警描述：'}}"
            s-if="{{!isCheck}}">
            <s-input-text-area
                value="{=formData.desc=}"
                width="{{300}}"
                height="{{80}}"
                maxLength="200"
                placeholder="{{'请输入'}}"
            />
        </s-form-item>
        <s-form-item label="告警名称："
            s-if="{{isCheck}}"
            class="alarm-form-flex"
        >
            <span>{{formRow.name || '-'}}</span>
        </s-form-item>
        <s-form-item label="告警类型："
            s-if="{{isCheck}}"
            class="alarm-form-flex"
        >
            <span>{{formRow.alertType | getAlertType}}</span>
        </s-form-item>
        <s-form-item label="告警配置方式："
            s-if="{{isCheck}}"
            class="alarm-form-flex"
        >
            <span>{{formRow.thresholdType | getThsoldType}}</span>
        </s-form-item>
        <s-form-item label="告警阈值："
            s-if="{{isCheck}}"
            class="alarm-form-flex"
        >
            <span>{{formRow.threshold || '-'}}</span>
        </s-form-item>
        <s-form-item label="通知渠道："
            class="alarm-form-flex"
        >
            <span>{{'短信、邮件'}}</span>
        </s-form-item>
        <!--<s-form-item label="告警回调："
            s-if="{{isCheck}}"
            class="alarm-form-flex alarm-form-bottom"
        >
            <span>{{formRow.callBackURL || '-'}}</span>
        </s-form-item>-->
    </s-form>
    <div slot="footer">
        <s-button on-click="close">取消</s-button>
        <s-button skin="primary" disabled="{{editDisabled || disableSub}}" on-click="doSubmit">确定</s-button>
    </div>
</s-dialog>
</template>
`;

@template(tpl)
@asComponent('@create-alarm')
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class CreateAlarm extends Component {
    static filters = {
        getRegion(value) {
            return AllRegion.getTextFromValue(value);
        },
        getAlertType(value) {
            return alarmType.getTextFromValue(value) || '-';
        },
        getThsoldType(value) {
            return alarmConfig.getTextFromValue(value) || '-';
        }
    };

    static computed = {
        editDisabled() {
            const formData = this.data.get('formData');
            const formRow = this.data.get('formRow') || {};
            if (formRow.alertType) {
                formRow.callBackURL = formRow.callBackURL ? formRow.callBackURL : '';
                formRow.desc = formRow.desc ? formRow.desc : '';
                formRow.threshold = formRow.threshold
                    ? Number(formRow.threshold)
                        ? Number(formRow.threshold)
                        : formRow.threshold
                    : '';
            }
            formData.threshold = formData.threshold
                ? Number(formData.threshold)
                    ? Number(formData.threshold)
                    : formData.threshold
                : '';
            if (
                formData.name !== formRow.name ||
                formData.alertType !== formRow.alertType ||
                formData.thresholdType !== formRow.thresholdType ||
                formData.callBackURL !== formRow.callBackURL ||
                formData.desc !== formRow.desc ||
                formData.threshold !== formRow.threshold
            ) {
                return false;
            } else {
                return true;
            }
        }
    };

    initData() {
        return {
            title: '',
            content: '',
            open: true,
            formData: {
                alertType: 'USED',
                thresholdType: 'NUMERICAL',
                callBackURL: '',
                desc: '',
                threshold: ''
            },
            alarmTypeList: alarmType.toArray(),
            alarmConfigLost: alarmConfig.toArray(),
            rules: formValidator(this),
            quotaNameList: [],
            productValue: [],
            productDst: [
                {
                    value: 'comput',
                    text: '计算',
                    children: computProductType.toArray()
                },
                {
                    value: 'network',
                    text: '网络',
                    children: eipServiceType.toArray()
                },
                {
                    value: 'database',
                    text: '数据库',
                    children: databaseProductType.toArray()
                },
                {
                    value: 'manage',
                    text: '管理运维',
                    children: manageProductType.toArray()
                }
            ],
            loading: true,
            errorShow: false
        };
    }
    inited() {
        this.$http
            .alarmList({
                pageNo: 1,
                pageSize: 1000
            })
            .then(res => {
                const array = res.result.map(item => {
                    return {
                        region: item.region,
                        quotaName: item.quotaName,
                        serviceType: item.serviceType,
                        productType: item.productType,
                        alertType: item.alertType,
                        thresholdType: item.thresholdType
                    };
                });
                this.data.set('hasQuotaAlarm', array);
            });
        if (this.data.get('formRow')) {
            let formData = this.data.get('formRow');
            this.data.set('formData.alertType', formData.alertType);
            this.data.set('formData.thresholdType', formData.thresholdType);
            this.data.set('formData.threshold', formData.threshold + '');
            this.data.set('formData.callBackURL', formData.callBackURL);
            this.data.set('formData.desc', formData.desc);
            this.data.set('formData.name', formData.name);
            this.$http
                .getQuotaList(formData.serviceType, formData.region, {
                    pageNo: 1,
                    pageSize: 10,
                    name: formData.quotaName
                })
                .then(res => {
                    this.data.set('row', res.result[0]);
                    this.data.set('loading', false);
                });
        }
    }
    close() {
        this.fire('close');
        this.data.set('open', false);
    }
    async doSubmit() {
        this.data.set('errorShow', false);
        let form = this.ref('form');
        await form.validateFields();
        let payload = this.getPayload();
        let rule = {
            region: payload.region,
            quotaName: payload.quotaName,
            serviceType: payload.serviceType,
            productType: payload.productType,
            alertType: payload.alertType,
            thresholdType: payload.thresholdType
        };

        const rules = [...this.data.get('hasQuotaAlarm'), rule];
        let arr = [];
        rules.forEach((item, index, source) => {
            if (this.ruleIndexOfTable(item, arr) === -1) {
                arr.push(item);
            } else {
                this.data.set('errorShow', true);
            }
        });

        if (this.data.get('errorShow')) {
            return;
        }

        this.data.set('disableSub', true);
        if (this.data.get('isCreate')) {
            this.$http
                .createAlarm(payload)
                .then(res => {
                    this.fire('confirm', true);
                    this.data.set('open', false);
                })
                .catch(() => {
                    this.data.set('disableSub', false);
                });
        } else {
            this.$http
                .editAlarm(this.data.get('formRow').alertId, payload)
                .then(res => {
                    this.fire('confirm', true);
                    this.data.set('open', false);
                })
                .catch(() => {
                    this.data.set('disableSub', false);
                });
        }
    }
    getPayload() {
        const formData = this.data.get('formData');
        const payload = {
            name: formData.name,
            desc: formData.desc,
            alertType: formData.alertType,
            thresholdType: formData.thresholdType,
            threshold: Number(formData.threshold),
            callBackURL: formData.callBackURL
        };
        if (this.data.get('isCreate')) {
            if (this.data.get('hasService')) {
                payload.quotaName = this.data.get('row').showName;
                payload.productType = this.data.get('row').productType;
                payload.serviceType = this.data.get('row').serviceType;
                payload.region = this.data.get('row').region;
            } else {
                payload.quotaName = formData.quotaName;
                payload.productType = formData.serviceType[1].toUpperCase();
                payload.serviceType = formData.serviceType[2]
                    ? formData.serviceType[2].toUpperCase()
                    : formData.serviceType[1].toUpperCase();
                payload.region = formData.region;
            }
        }
        return payload;
    }
    serviceTypeChange({value}) {
        this.data.set('formData.region', '');
        this.data.set('formData.quotaName', '');
        this.data.set('regionList', []);
        this.data.set('quotaNameList', []);
        let serviceType = value[2] ? value[2].toUpperCase() : value[1].toUpperCase();
        this.$http.getRegion(serviceType).then(data => {
            let regionList = [];
            //按照framework中region排序
            let arr = ['bj', 'bd', 'gz', 'su', 'fsh', 'fwh', 'hb-fsg', 'bjfsg', 'gzfsg', 'hkg', 'sin', 'global'];
            let reArray = [];
            arr.forEach(item => {
                if (data.regions.includes(item)) {
                    reArray.push(item);
                }
            });
            for (let i of reArray) {
                let region = {};
                region.value = i;
                region.text = AllRegion.getTextFromValue(i);
                regionList.push(region);
            }
            if (regionList.length > 1) {
                this.data.set('showRegion', true);
            } else {
                this.data.set('showRegion', false);
            }
            this.data.set('regionList', regionList);
            this.$http
                .getQuotaList(serviceType, regionList[0].value, {
                    pageNo: 1,
                    pageSize: 100
                })
                .then(res => {
                    let quotaNameList = [];
                    res.result.forEach(item => {
                        let payload = {
                            text: item.showName,
                            value: item.showName,
                            disabled: item.alert ? false : true
                        };
                        quotaNameList.push(payload);
                    });
                    this.data.set('quotaNameList', quotaNameList);
                });
        });
    }
    ruleIndexOfTable(rule, datasource) {
        var index = -1;
        _.find(datasource, (x, i) => {
            if (this.equal(x, rule)) {
                index = i;
                return true;
            }
        });
        return index;
    }
    equal(a, b) {
        //取对象属性名
        var aProps = Object.getOwnPropertyNames(a);
        let num = 0;
        //循环取出属性名，再判断属性值是否一致
        for (var i = 0; i < aProps.length; i++) {
            var propName = aProps[i];
            if (a[propName] === b[propName]) {
                num++;
            }
        }
        return num === aProps.length;
    }
}
export default Processor.autowireUnCheckCmpt(CreateAlarm);
