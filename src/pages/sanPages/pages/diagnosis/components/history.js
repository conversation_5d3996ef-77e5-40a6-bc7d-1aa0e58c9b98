/*
 * @description: 申请配额弹窗
 */
import _ from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
const {asComponent, invokeSUI, invokeSUIBIZ, template, invokeAppComp} = decorators;
import {applyStatus, examineStatus} from '../../../common/enum';
import {utcToTime} from '../../../utils/helper';
import {ContextService} from '../../../common';
import './history.less';

const AllRegion = ContextService.getEnum('AllRegion');
const tpl = html`
    <template>
        <s-dialog
            class="quota-history-dialog"
            closeAfterMaskClick="{{false}}"
            open="{=open=}"
            title="{{title}}"
            width="{{800}}"
        >
            <s-table
                class="table-content"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                maxHeight="410"
            >
                <div slot="error">
                    加载失败
                    <a href="javascript:;" on-click="refresh">{{'重新加载'}}</a>
                </div>
                <div slot="empty">
                    <s-empty vertical />
                </div>
                <div slot="c-status">
                    <span class="{{row.status | statusClass}}">{{row.status | statusText}}</span>
                </div>
                <div slot="c-createTime">
                    <span>{{row.createTime | timeFormat}}</span>
                </div>
                <div slot="c-conclusion">
                    <span>{{row.conclusion | getExamineStatus}}</span>
                </div>
                <div slot="c-region">
                    <span>{{row.region | getRegion}}</span>
                </div>
                <div slot="c-remark">
                    <span>{{row.remark || '-'}}</span>
                </div>
            </s-table>
        </s-dialog>
    </template>
`;

@template(tpl)
@asComponent('@quota-history')
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class History extends Component {
    static filters = {
        timeFormat(time) {
            return utcToTime(time);
        },
        statusClass(value) {
            return applyStatus.fromValue(value).kclass || '';
        },
        statusText(value) {
            return value ? applyStatus.getTextFromValue(value) : '-';
        },
        getRegion(value) {
            return AllRegion.getTextFromValue(value);
        },
        getExamineStatus(value) {
            return examineStatus.getTextFromValue(value) || '-';
        }
    };
    initData() {
        return {
            title: '',
            content: '',
            open: true,
            table: {
                loading: false,
                columns: [
                    {name: 'shortId', label: '申请ID', width: 140},
                    {name: 'region', label: '申请地域', width: 154},
                    {name: 'name', label: '配额名称', width: 175},
                    {name: 'status', label: '申请状态', width: 113},
                    {name: 'value', label: '申请配额数量', width: 98},
                    {name: 'createTime', label: '申请时间', width: 140},
                    {name: 'conclusion', label: '申请审批结果', width: 98},
                    {name: 'remark', label: '审批拒绝原因', width: 146}
                ],
                datasource: []
            }
        };
    }
    attached() {
        this.getHistory();
    }
    getHistory() {
        let {name, region, serviceType} = this.data.get('formData');
        this.data.set('table.loading', true);
        this.$http
            .getApplyHistoryList({name, region, serviceType, type: 'QUOTA'})
            .then(res => {
                this.data.set('table.loading', false);
                this.data.set('table.datasource', res.result);
            })
            .catch(() => {
                this.data.set('table.loading', false);
            });
    }
    refresh() {
        this.getHistory();
    }
    close() {
        this.fire('close');
        this.data.set('open', false);
    }
}
export default Processor.autowireUnCheckCmpt(History);
