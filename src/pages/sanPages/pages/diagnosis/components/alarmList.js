/*
 * @description: 申请配额弹窗
 */
import _ from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
const {asComponent, invokeSUI, invokeSUIBIZ, template, invokeAppComp} = decorators;
import {alarmType, alarmConfig} from '../../../common/enum';
import {utcToTime} from '../../../utils/helper';
import {ContextService} from '../../../common';
import './alarmList.less';

const AllRegion = ContextService.getEnum('AllRegion');
const tpl = html`
    <template>
        <s-dialog
            class="quota-history-dialog"
            closeAfterMaskClick="{{false}}"
            open="{=open=}"
            title="{{title}}"
            width="{{800}}"
        >
            <s-table
                class="table-content"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                maxHeight="410"
            >
                <div slot="error">
                    加载失败
                    <a href="javascript:;" on-click="refresh">{{'重新加载'}}</a>
                </div>
                <div slot="empty">
                    <s-empty vertical />
                </div>
                <div slot="c-createTime">
                    <span>{{row.createTime | timeFormat}}</span>
                </div>
                <div slot="c-thresholdType">
                    <span>{{row.thresholdType | getThresholdType}}</span>
                </div>
                <div slot="c-alertType">
                    <span>{{row.alertType | getAlertType}}</span>
                </div>
                <div slot="c-region">
                    <span>{{row.region | getRegion}}</span>
                </div>
            </s-table>
        </s-dialog>
    </template>
`;

@template(tpl)
@asComponent('@alarm-history')
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class History extends Component {
    static filters = {
        timeFormat(time) {
            return utcToTime(time);
        },
        getRegion(value) {
            return AllRegion.getTextFromValue(value);
        },
        getAlertType(value) {
            return alarmType.getTextFromValue(value) || '-';
        },
        getThresholdType(value) {
            return alarmConfig.getTextFromValue(value) || '-';
        }
    };
    initData() {
        return {
            title: '',
            content: '',
            open: true,
            table: {
                loading: false,
                columns: [
                    {name: 'name', label: '告警名称', width: 150},
                    {name: 'serviceType', label: '产品名称', width: 154},
                    {name: 'quotaName', label: '配额名称', width: 210},
                    {name: 'region', label: '地域', width: 123},
                    {name: 'alertType', label: '告警类型', width: 95},
                    {name: 'threshold', label: '告警阈值', width: 70},
                    {name: 'createTime', label: '创建时间', width: 135}
                ],
                datasource: []
            }
        };
    }
    attached() {
        this.getHistory();
    }
    getHistory() {
        let payload = {
            pageNo: 1,
            pageSize: 1000,
            quotaName: this.data.get('row').showName,
            productType: this.data.get('row').productType,
            serviceType: this.data.get('row').serviceType,
            region: this.data.get('row').region
        };
        this.data.set('table.loading', true);
        this.$http
            .alarmList(payload)
            .then(res => {
                this.data.set('table.loading', false);
                this.data.set('table.datasource', res.result);
            })
            .catch(() => {
                this.data.set('table.loading', false);
            });
    }
    refresh() {
        this.getHistory();
    }
    close() {
        this.fire('close');
        this.data.set('open', false);
    }
}
export default Processor.autowireUnCheckCmpt(History);
