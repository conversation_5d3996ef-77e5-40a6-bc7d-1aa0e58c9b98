import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {Tabs} from '@baidu/sui';

import {QuotaServiceList} from '../../../common/enum';
import {DocService} from '../../../common';
import {judge<PERSON><PERSON><PERSON>, judgeOne<PERSON>one} from './judge';
import Apply from './apply';
import History from './history';
import Create from './create';
import AlarmList from './alarmList';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import '../style/quota.less';

const {asComponent, template, invokeSUI, invokeComp} = decorators;

const tpl = html`
    <div class="diagnosis-quota">
        <h3>VPC配额情况</h3>
        <!--bca-disable-next-line-->
        <div class="quota-notify" s-html="quotaNotifyContent"></div>
        <div s-if="{{tabs.length}}" class="quota-container">
            <s-tabs on-change="onChange">
                <s-tabpane s-for="i in tabs" label="{{i.label}}" key="{{i.key}}">
                    <s-table columns="{{table.columns}}" loading="{{table.loading}}" datasource="{{table.datasource}}">
                        <div slot="c-description">
                            <span>{{row | getDescription}}</span>
                        </div>
                        <div slot="c-value">
                            <span>{{row | getValue}}</span>
                        </div>
                        <div slot="c-used">
                            <div s-if="{{!row.used && row.used !== 0}}">-</div>
                            <div s-else>
                                <div>{{row.used}}</div>
                                <s-progress
                                    class="progress_wrap"
                                    percent="{{row | getPercent}}"
                                    skin="{{skin}}"
                                    width="{{200}}"
                                />
                            </div>
                        </div>
                        <div slot="c-opt" class="operation">
                            <s-tooltip
                                trigger="{{(!row.rangeStatus || (row.applicationStatus !== 'EFFECTED' ||
                  row.applicationStatus !== 'REFUSED') && row.applicationStatus) ? 'hover' : ''}}"
                                s-if="row.apply"
                                placement="right"
                            >
                                <!--bca-disable-next-line-->
                                <div slot="content">{{row | getContent | raw}}</div>
                                <s-button
                                    disabled="{{(!row.rangeStatus || (row.applicationStatus !== 'EFFECTED' ||
                      row.applicationStatus !== 'REFUSED') && row.applicationStatus)}}"
                                    skin="stringfy"
                                    on-click="createQuota(row, index)"
                                    >申请
                                </s-button>
                            </s-tooltip>
                            <s-button skin="stringfy" on-click="createHistory(row)" s-if="row.apply">申请历史</s-button>
                            <s-tooltip trigger="{{!row.apply ? 'hover' : ''}}" placement="right" s-if="!row.apply">
                                <!--bca-disable-next-line-->
                                <div slot="content">当前配额状态不支持调整。</div>
                                <s-button disabled="{{!row.apply}}" skin="stringfy" on-click="updateQuota"
                                    >不可调整
                                </s-button>
                            </s-tooltip>
                            <s-tooltip trigger="{{!row.alert ? 'hover' : ''}}" placement="right" s-if="row.alert">
                                <!--bca-disable-next-line-->
                                <div slot="content">当前配额不支持创建告警</div>
                                <s-button disabled="{{!row.alert}}" skin="stringfy" on-click="createAlarm(row, index)"
                                    >创建告警
                                </s-button>
                            </s-tooltip>
                            <s-button s-if="{{row.alert}}" skin="stringfy" on-click="alarmList(row, index)"
                                >告警项
                            </s-button>
                        </div>
                    </s-table>
                    <s-pagination
                        s-if="{{pager.total}}"
                        layout="{{'total, pageSize, pager, go'}}"
                        pageSize="{{pager.size}}"
                        total="{{pager.total}}"
                        page="{{pager.page}}"
                        resetPageWhenSizeChange="{{true}}"
                        on-pagerChange="onPagerChange"
                    />
                </s-tabpane>
            </s-tabs>
        </div>
    </div>
`;

@template(tpl)
@asComponent('@diagnosis-quota')
@invokeComp('@quota-apply', '@quota-history', '@create-alarm', '@alarm-history')
@invokeSUI
class DiagnosisQuota extends Component {
    static components = {
        's-tab': Tabs,
        's-tabpane': Tabs.TabPane
    };
    static filters = {
        getDescription(row) {
            return row.description || '-';
        },
        getValue(row) {
            return row.value || '-';
        },
        getPercent(row) {
            if (isNaN(row.value)) {
                return row.used;
            }
            return (row.used / row.value) * 100;
        },
        getContent(row) {
            if (!row.rangeStatus) {
                return `不支持配额申请，如需增加配额请提交<a href="${window.$context.getDomains().ticket}/#/ticket/create" target="_blank">工单</a>`;
            }
            if (
                (row.applicationStatus !== 'EFFECTED' || row.applicationStatus !== 'REFUSED') &&
                row.applicationStatus
            ) {
                let link = `${window.$context.getDomains().ticket}#/ticket/create`;
                row.serviceType === 'BCC' && (link = link + '?useticket=1&productId=2&questionId=453&channel=2');
                return `上次申请未审核之前禁止申请，如需补充申请内容说明可以提交
          <a href="${link}">工单</a>申请`;
            }
            return '';
        }
    };

    static computed = {
        quotaNotifyContent() {
            const docUrl = this.data.get('DocService.quota_index');
            const isXS = this.data.get('FLAG.NetworkSupportXS');

            if (!isXS) {
                return `如遇到关于VPC配额的疑问时，请查看<a href="${docUrl}">VPC配额说明文档</a>，遇到VPC内配额问题时，请查看下方列表并进行管理，当您遇到更多产品关于配额问题时，请去<a href="/quota_center/#/quota/network/list?serviceType=VPC">配额管理</a>`;
            } else {
                return '如遇到关于VPC配额的疑问时，请查看VPC配额说明文档，遇到VPC内配额问题时，请查看下方列表并进行管理，当您遇到更多产品关于配额问题时，请去配额管理';
            }
        }
    };

    initData() {
        return {
            FLAG,
            DocService,
            tabs: [],
            table: {
                columns: [
                    {name: 'showName', label: '配额名称', width: 252, fixed: 'left'},
                    {name: 'description', label: '描述', width: 320},
                    {name: 'value', label: '配额', width: 120},
                    {name: 'used', label: '已使用', width: 220},
                    {name: 'opt', label: '操作', width: 180, fixed: 'right'}
                ],
                datasource: []
            },
            pager: {
                size: 10,
                page: 1,
                total: 0
            },
            skin: 'normal'
        };
    }
    inited() {
        this.getServiceType();
    }
    setQuotaList(serviceType) {
        let pager = this.data.get('pager');
        let payload = {
            pageNo: pager.page,
            pageSize: pager.size
        };
        this.data.set('table.loading', true);
        return this.$http.getQuotaList(serviceType, window.$context.getCurrentRegionId(), payload).then(res => {
            let result = this.checkStatus(res.result);
            this.data.set('table.datasource', res.result);
            this.data.set('pager.total', res.totalSize);
            this.data.set('table.loading', false);
        });
    }
    onPagerChange({value}) {
        this.data.set('pager.page', value.page);
        this.data.set('pager.size', value.pageSize);
        this.setQuotaList(this.data.get('activeTab'));
    }
    onChange(e) {
        this.data.set('activeTab', e.value.key);
        this.setQuotaList(e.value.key);
    }
    getServiceType() {
        const payload = {
            type: 'QUOTA',
            productType: 'VPC',
            region: window.$context.getCurrentRegionId()
        };
        this.$http.getQuotaServiceType(payload).then(res => {
            let tabs = res.quotaCenterCommonResponses.map(item => ({
                label: `${QuotaServiceList.getTextFromAlias(item.serviceType)}(${item.quotaNum})`,
                key: item.serviceType
            }));
            this.data.set('tabs', tabs);
            this.data.set('activeTab', tabs[0].key);
            this.setQuotaList(tabs[0].key);
        });
    }
    createQuota(row, index) {
        let apply = new Apply({
            data: {
                open: true,
                title: '申请配额',
                formData: row
            }
        });
        apply.attach(document.body);
        apply.on('confirm', value => {
            this.$http.applyQuota(value).then(res => {
                this.setQuotaList(this.data.get('activeTab'));
            });
        });
    }
    createHistory(row) {
        let history = new History({
            data: {
                open: true,
                title: '申请配额',
                formData: row
            }
        });
        history.attach(document.body);
    }
    createAlarm(row, index) {
        let create = new Create({
            data: {
                open: true,
                title: '创建告警',
                hasService: true,
                row: row,
                isCreate: true
            }
        });
        create.attach(document.body);
        create.on('confirm', value => {
            this.setQuotaList(this.data.get('activeTab'));
        });
    }
    alarmList(row, index) {
        let create = new AlarmList({
            data: {
                open: true,
                title: '告警列表',
                row: row
            }
        });
        create.attach(document.body);
    }
    checkStatus(result) {
        let dataSource = [];
        result.forEach(item => {
            let row = item;
            let automaticApprovalInterval = item.automaticApprovalInterval?.split(',');
            let manualApprovalInterval = item.manualApprovalInterval?.split(',');
            if (automaticApprovalInterval && manualApprovalInterval) {
                if (!(automaticApprovalInterval[0] === 'NONE' && manualApprovalInterval[0] === 'NONE')) {
                    if (automaticApprovalInterval[1] && manualApprovalInterval[1]) {
                        let range = judgeRange(
                            automaticApprovalInterval,
                            manualApprovalInterval,
                            Number(item.value),
                            item.direction
                        );
                        if (row.name === 'channelBgpRouteLimit') {
                            if (range) {
                                let rangeArray = range.split('、');
                                if (rangeArray.length > 1) {
                                    let automaticApprovalInterval = rangeArray[0].split(',');
                                    let manualApprovalInterval = rangeArray[1].split(',');
                                    if (automaticApprovalInterval[0] < 200) {
                                        if (manualApprovalInterval[0] > 200) {
                                            if (automaticApprovalInterval[1] > 200) {
                                                automaticApprovalInterval[1] = 200;
                                            }
                                            range = automaticApprovalInterval.join(',');
                                        } else {
                                            if (manualApprovalInterval[1] > 200) {
                                                manualApprovalInterval[1] = 200;
                                            }
                                            range =
                                                automaticApprovalInterval.join(',') +
                                                '、' +
                                                manualApprovalInterval.join(',');
                                        }
                                        row.rangeStatus = range ? true : false;
                                    } else {
                                        row.rangeStatus = false;
                                    }
                                } else {
                                    let automaticApprovalInterval = rangeArray[0].split(',');
                                    if (automaticApprovalInterval[0] > 200) {
                                        row.rangeStatus = false;
                                    } else {
                                        if (automaticApprovalInterval[1] > 200) {
                                            automaticApprovalInterval[1] = 200;
                                            range = automaticApprovalInterval.join(',');
                                        }
                                        row.rangeStatus = range ? true : false;
                                    }
                                }
                            }
                        } else {
                            row.rangeStatus = range ? true : false;
                        }
                    }
                    if (automaticApprovalInterval[0] === 'NONE' || manualApprovalInterval[0] === 'NONE') {
                        let range =
                            automaticApprovalInterval[0] === 'NONE'
                                ? manualApprovalInterval
                                : automaticApprovalInterval;
                        range = range.map(item => +item);
                        range = judgeOneNone(range, Number(item.value), item.direction);
                        if (row.name === 'channelBgpRouteLimit') {
                            if (range) {
                                let rangeArray = range.split('、');
                                let automaticApprovalInterval = rangeArray[0].split(',');
                                if (automaticApprovalInterval[0] > 200) {
                                    row.rangeStatus = false;
                                } else {
                                    if (automaticApprovalInterval[1] > 200) {
                                        automaticApprovalInterval[1] = 200;
                                        range = automaticApprovalInterval.join(',');
                                    }
                                    row.rangeStatus = range ? true : false;
                                }
                            } else {
                                row.rangeStatus = false;
                            }
                        } else {
                            row.rangeStatus = range ? true : false;
                        }
                    }
                }
            } else {
                row.rangeStatus = true;
            }
            dataSource.push(row);
        });
        return dataSource;
    }
}
export default Processor.autowireUnCheckCmpt(DiagnosisQuota);
