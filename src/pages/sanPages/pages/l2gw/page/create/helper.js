import { convertPrice, showMoney } from '@/pages/sanPages/utils/helper';
import { PayType, vpnFlavor, vpnType } from '@/pages/sanPages/common/enum';

export const getOrderConfig = (formData = {}, price = { perMoney: 0.00, money: 0.00 }, showFlavor = false) => {
    const isPrepay = formData.productType === PayType.PREPAY;
    const unitPrice = convertPrice(+price.perMoney).getPriceOfMinute();
    const quota = isPrepay ? `1个 * ${formData.purchaseLength}月` : '1个';
    const extraConfig = { label: 'VPN网关规格', value: vpnFlavor.getTextFromValue(formData.flavor) };
    const vpnTypeConfig = { label: 'VPN网关类型', value: vpnType.getTextFromValue(formData.vpnType), showInCart: false };
    const greExtraConfig = { label: '网络吞吐带宽', value: formData.greBandwidth + 'G'};
    let order = {
        managePrice: false,
        type: 'NEW',
        serviceName: 'VPN网关',
        serviceType: 'VPN',
        productType: formData.productType,
        region: formData.region,
        count: 1,
        time: formData.purchaseLength,
        price: price.money,
        resourceGroupId: formData.resourceGroupId,
        unitPrice,
        configuration: [
            '地域：' + window.$context.getCurrentRegion().label,
            'VPN名称：' + formData.vpnName
        ],
        configDetail: [
            { label: '地域', value: window.$context.getCurrentRegion().label, showInConfirm: false },
            { label: '购买配置', value: formData.bandWidth + 'Mbps' },
            { label: '购买时长', value: quota },
        ],
        config: {}
    };
    showFlavor && order.configDetail.push(extraConfig);
    formData.vpnType === 'gre' && order.configDetail.push(greExtraConfig);
    order.configDetail.push(vpnTypeConfig);
    return order;
};

export const setInstancePrice = (instance, formData = {}, price, sslPrice) => {
    const isPrepay = formData.productType === PayType.PREPAY;
    const priceSubText = isPrepay ? '' : `
        （预计¥${showMoney((sslPrice ? (+price.money + +sslPrice.money) : +price.money) * 60 * 24, 2)}/天
        ¥${showMoney((sslPrice ? (+price.money + +sslPrice.money) : +price.money) * 60 * 24 * 30, 2)}/月）
    `;
    const unit = isPrepay ? '' : '分钟';
    const unitPrice = convertPrice(price.money).getPriceOfMinute(unit);
    const unitPriceText = '¥' + unitPrice;
    instance.set('priceSubText', priceSubText);
    instance.set('unitPriceText', unitPriceText);
    instance.set('productType', formData.productType);
    instance.set('price', price.money);
    instance.set('unitPrice', price.perMoney);
    instance.set('unit', unit);
    instance.set('priceError', '');
};

export const getConfirmConfig = (formData, showFlavor) => {
    let { autoRenew, autoRenewTimeUnit, autoRenewTime, ...dataConfig } = formData;
    let renewPayload = {};
    if (autoRenew && formData.productType === 'prepay') {
        renewPayload.renewReservation = {
            reservationTimeUnit: autoRenewTimeUnit,
            reservationLength: autoRenewTime
        };
    }
    let billingPayload = {
        billing: {
            billingMethod: formData.productType,
            reservation: {
                reservationLength: formData.productType === 'prepay' ? formData.purchaseLength : '',
                reservationTimeUnit: 'month'
            }
        },
    };
    if (showFlavor) {
        billingPayload.flavor = formData.flavor;
    }
    return {
        vpnCount: 1,
        serviceType: 'VPN',
        ...dataConfig,
        ...billingPayload,
        ...renewPayload
    };
};
