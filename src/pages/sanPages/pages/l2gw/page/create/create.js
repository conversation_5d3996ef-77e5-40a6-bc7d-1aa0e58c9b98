import {Component} from 'san';
import u from 'lodash';
import {html, decorators, redirect, ServiceFactory, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {BillingSDK, OrderItem} from '@baiducloud/billing-sdk';
import {OrderConfirm, ShoppingCart} from '@baiducloud/billing-sdk/san';
import {ResourceGroupPanel} from '@baidu/bce-resourcegroup-sdk-san';
import {ResourceGroupSDK} from '@baidu/bce-resourcegroup-sdk';

import zone from '@/pages/sanPages/utils/zone';
import {checkIsInSubnet} from '@/pages/sanPages/utils/common';
import {
    PayType,
    l2gwFlavor,
    TimeType,
    Year,
    Month,
    DcGatewayStatus,
    VpnStatus,
    sdwanStatus
} from '@/pages/sanPages/common/enum';
import {DocService, ContextService} from '@/pages/sanPages/common';

import {getOrderConfig, setInstancePrice, getConfirmConfig} from './helper';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import RULE from '@/pages/sanPages/utils/rule';
import '../../style/create.less';

const {invokeSUI, invokeSUIBIZ, invokeAppComp, template} = decorators;
const kXhrOptions = {'X-silence': true};
const AllRegion = window.$context.getEnum('AllRegion');
const cookie = ServiceFactory.resolve('$cookie');
const validateRules = self => {
    return {
        l2gwName: [
            {required: true, message: '名称必填'},
            {
                validator(rule, value, callback) {
                    let pattern = /^[a-zA-Z][\w\-\/\.]{0,64}$/;
                    if (!pattern.test(value)) {
                        return callback('长度限制为1-65个字符，以字母开头，只允许包含字母、数字及 - _ . /');
                    }
                    callback();
                }
            }
        ],
        greBandwidth: [{required: true}],
        subnetId: [{required: true, message: '请选择子网'}],
        tunnelBindInstanceId: [{required: true, message: '请选择隧道连接实例ID'}],
        tunnelType: [{required: true, message: '请选择隧道连接方式'}],
        zone: [{required: true, message: '请选择可用区'}],
        localTunnelIp: [
            {pattern: RULE.IP, message: '格式不符合要求'},
            {
                validator: (rule, value, callback) => {
                    const source = self.data.get('formData');
                    const subnetList = self.data.get('subnetDatasource');
                    if (source.type !== 'custom') {
                        callback();
                        return;
                    }
                    if (!value) {
                        return callback('请填写指定IP地址');
                    }
                    if (!u.trim(value)) {
                        return callback('请填写指定IP地址');
                    }
                    const subnet = u.find(subnetList, item => item.value === source.subnetId);
                    if (subnet && !checkIsInSubnet(source.localTunnelIp + '/32', subnet.cidr)) {
                        return callback('IP地址不在所选子网内');
                    }
                    callback();
                }
            }
        ]
    };
};

const contextPipe = {
    getOrderSuccessUrl() {
        return window.$context.getOrderSuccessUrl() || {};
    },
    getCurrentRegion() {
        return window.$context.getCurrentRegionId();
    },
    getCsrfToken() {
        // 返回cookie中的信息
        return cookie.get('bce-user-info');
    },
    SERVICE_TYPE: window.$context.SERVICE_TYPE
};

const tpl = html`
<div>
    <s-app-create-page class="{{klass}}" backTo="{{pageTitle.backTo}}"
    backToLabel="{{pageTitle.label}}" pageTitle="{{pageTitle.title}}">
        <!--<div class="s-step-block">
            <s-steps current="{{steps.current}}">
                <s-steps-step s-for="i in steps.datasource" title="{{i.title}}"/>
            </s-steps>
        </div>-->
        <div class="content-wrap" s-if="{{steps.current === 1}}">
            <s-form s-ref="form"
                label-align="left"
                data="{=formData=}" rules="{{rules}}">
                <div class="content-wrap-box form-part-wrap no-style-display">
                    <h4>基本信息</h4>
                    <s-form-item label="付费方式：" prop="productType">
                        <s-radio-radio-group
                            datasource="{{productTypeList}}"
                            value="{=formData.productType=}"
                            on-change="productTypeChange"
                            radioType="button"
                        >
                        </s-radio-radio-group>
                    </s-form-item>
                    <s-form-item label="当前地域：" prop="region">
                        <div slot="label" class="label_class">
                            {{'当前地域：'}}
                            <s-tip
                                class="inline-tip"
                                content="如需修改购买其他区域产品，请{{!FLAG.NetworkSupportXS ? '前往主导航进行切换': '在顶栏重新选择区域'}}"
                                skin="question"
                                />
                        </div>
                        <div class="row-line">
                            <s-radio-radio-group
                                datasource="{{regionList}}"
                                value="{=formData.region=}"
                                radioType="button"
                            >
                            </s-radio-radio-group>
                        </div>
                    </s-form-item>
                </div>
                <div class="content-wrap-box form-part-wrap">
                    <h4>配置信息</h4>
                    <s-form-item label="网关名称：" prop="l2gwName"
                        help="大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65"
                    >
                        <s-input value="{=formData.l2gwName=}" width="300"></s-input>
                    </s-form-item>
                    <s-form-item label="所在网络：" prop="vpcId">
                        <s-select
                            width="300"
                            filterable
                            value="{=formData.vpcId=}"
                            on-change="vpcChange"
                        >
                            <s-select-option
                                s-for="item in vpcList"
                                value="{{item.value}}"
                                label="{{item.text}}"
                            >
                                <s-tooltip>
                                    <div slot="content">
                                        {{item.text}}
                                    </div>
                                    <div>{{item.text}}</div>
                                </s-tooltip>
                            </s-select-option>
                        </s-select>
                    </s-form-item>
                    <s-form-item label="可用区：" prop="zone">
                      <s-select
                          width="300"
                          filterable
                          datasource="{{zoneList}}"
                          value="{=formData.zone=}"
                          on-change="zoneChange"
                      >
                      </s-select>
                    </s-form-item>
                    <s-form-item label="所在子网：" prop="subnetId">
                        <s-select width="{{300}}"
                            disabled="{{loading}}"
                            filterable
                            value="{=formData.subnetId=}"
                            on-change="subnetChange"
                        >
                            <s-select-option
                                s-for="item in subnetDatasource"
                                value="{{item.value}}"
                                label="{{item.text}}"
                            >
                                <s-tooltip>
                                    <div slot="content">
                                        {{item.text}}
                                    </div>
                                    <div>{{item.text}}</div>
                                </s-tooltip>
                            </s-select-option>
                        </s-select>
                    </s-form-item>
                    <s-form-item label="规格：" prop="flavor">
                        <div slot="label" class="label_class">
                            {{'规格：'}}
                            <s-tip
                                class="inline-tip"
                                content="{{flavorMessage}}"
                                skin="question"
                                />
                        </div>
                        <div class="row-line flavor-line">
                            <s-radio-radio-group
                                datasource="{{flavorList}}"
                                value="{=formData.flavor=}"
                                radioType="button"
                            >
                            </s-radio-radio-group>
                        </div>
                    </s-form-item>
                    <!--<s-form-item prop="localTunnelIp" label="本端隧道IP地址：" class="localTunnelIp_item">
                      <s-radio-radio-group
                          datasource="{{ipDatasource}}"
                          value="{=formData.type=}"
                          on-change="ipTypeChange($event)" />
                      <s-input
                          s-if="{{formData.type === 'custom'}}"
                          width="{{240}}"
                          placeholder="请输入该子网内可用IP"
                          value="{=formData.localTunnelIp=}"/>
                      <div s-if="messageShow" class="s-form-item-invalid-div">{{errorMessage}}</div>
                    </s-form-item>
                    <s-form-item label="隧道连接方式：" prop="tunnelType">
                      <s-select
                          width="300"
                          datasource="{{tunnelTypeList}}"
                          value="{=formData.tunnelType=}"
                          on-change="tunnelTypeChange($event)"
                      >
                      </s-select>
                    </s-form-item>
                    <s-form-item label="隧道连接实例ID：" prop="tunnelBindInstanceId">
                      <s-select
                          width="300"
                          filterable
                          datasource="{{tunnelBindInstanceList}}"
                          value="{=formData.tunnelBindInstanceId=}"
                      >
                      </s-select>
                    </s-form-item>-->
                    <s-form-item label="描述：" prop="description">
                        <s-input value="{=formData.description=}"
                        placeholder="描述不能超过200字符" width="300"
                        maxLength="200"
                        ></s-input>
                    </s-form-item>
                </div>
                <!--<div class="content-wrap-box form-part-wrap">
                    <resource-group-panel sdk="{{resourceSDK}}" on-change="resourceChange($event)" />
                </div>-->
                <!--<div class="content-wrap-box form-part-wrap" s-if="{{formData.productType === 'prepay'}}">
                    <h4>购买信息</h4>
                    <s-form-item label="购买时长：" prop="purchaseLength">
                        <s-tag-radio-group
                            radioType="button"
                            value="{=formData.purchaseLength=}"
                            track-id="ti_eip_group_create"
                            datasource="{{purchaseLengthList}}"
                            track-name="购买时长">
                        </s-tag-radio-group>
                    </s-form-item>
                    <s-form-item s-if="{{!FLAG.NetworkSupportXS}}" label="自动续费：" prop="purchaseLength" class="renew-item-label">
                        <s-switch checked="{=formData.autoRenew=}"></s-switch>
                        <a href="{{DocService.autorenew}}"
                            target="_blank"
                            data-track-name="自动续费文档" class="left_class">什么是自动续费？
                        </a>
                        <div class="l2gw-renew-wrap row-line" s-if="formData.autoRenew">
                            <span class="renew-title">选择续费周期</span>
                            <s-select
                                width="100"
                                on-change="onRenewUnitChange"
                                datasource="{{renewUnitList}}"
                                value="{=formData.autoRenewTimeUnit=}"
                            >
                            </s-select>
                            <s-select
                                style="margin-left:8px"
                                width="100"
                                on-change="onRenewTimeChange"
                                datasource="{{renewNumberList}}"
                                value="{=formData.autoRenewTime=}"
                            >
                            </s-select>
                            <span class="renew-tip">
                                系统将于到期前7天进行扣费，扣费时长为
                                {{formData.autoRenewTime}}{{formData.autoRenewTimeUnit==='month'?'月':'年'}}
                            </span>
                        </div>
                    </s-form-item>
                </div>-->
            </s-form>
        </div>
        <!--<div s-else class="order-confirm-panel">
            <order-confirm
                s-ref="orderConfirm"
                mergeBy="{{false}}"
                couponMergeBy="{{false}}"
                items="{{buyBucketItems}}"
                sdk="{{sdk}}"
                useCoupon="{{useCoupon}}"
                theme="default"
                showAgreementCheckbox
            />
        </div>-->
        <div class="buybucket" slot="pageFooter">
            <div class="buybucket-container" s-if="steps.current === 1">
                <s-popover trigger="{{updating ? 'hover' : ''}}" class="popover-class">
                    <div slot="content">
                    <!--bca-disable-next-line-->
                        {{disableTip | raw}}
                    </div>
                    <s-button on-click="goToConfirm" skin="primary" size="large"
                        class="buybucket-content" disabled="{{updating}}">
                        确定
                    </s-button>
                </s-popover>
                <!--<s-popover trigger="{{updating ? 'hover' : ''}}" class="popover-class">
                    <div slot="content">-->
                    <!--bca-disable-next-line-->
                        <!--{{disableTip | raw}}
                    </div>
                    <s-button
                        s-if="{{formData.productType === 'prepay' && !FLAG.NetworkSupportXS}}"
                        on-click="addShoppingCart"
                        disabled="{{cartConfirming || updating}}"
                        size="large"
                        class="buybucket-content">
                        加入购物车
                    </s-button>
                </s-popover>-->
                <s-button size="large" on-click="cancel">取消</s-button>
                <!--<shopping-cart
                    showTotalPrice="{{true}}"
                    showPriceItems="{{false}}"
                    class="buybucket-content"
                    items="{{buyBucketItems}}"
                    on-reset="onReset"
                />-->
            </div>
            <!--<div class="buybucket-container" s-else>
                <s-button  on-click="backToOrder" size="large">上一步</s-button>
                <s-button size="large" on-click="cancel">取消</s-button>
                <s-button skin="primary" size="large" class="buybucket-content"
                on-click="onConfirm" disabled="{{confirming}}">提交订单</s-button>
            </div>-->
        </div>
    </s-app-create-page>
</template>
`;
@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class L2GWCreate extends Component {
    static components = {
        'order-confirm': OrderConfirm,
        'shopping-cart': ShoppingCart,
        'resource-group-panel': ResourceGroupPanel
    };

    static computed = {
        updating() {
            let formData = this.data.get('formData');
            let l2gwStock = this.data.get('l2gwStock');
            let isRegionHk = window.$context.getCurrentRegionId() === AllRegion.HK02;
            let verify = window.$context.isVerifyUser();
            let price = this.data.get('price');
            let accountPurchaseValidation = this.data.get('accountPurchaseValidation');
            let accountStatus = accountPurchaseValidation ? accountPurchaseValidation.status : false;
            let hasQuota = this.data.get('hasQuota');
            let loadingPrice = this.data.get('loadingPrice');
            let allDcgwList = this.data.get('allDcgwList') || [];
            let allVpnList = this.data.get('allVpnList') || [];
            if (
                !allDcgwList.find(item => item.vpcId === formData.vpcId && item.status === 'running') &&
                !allVpnList.find(item => item.vpcId === formData.vpcId && item.status === 'active')
            ) {
                return true;
            }
            return isRegionHk || !verify || !formData.vpcId || !l2gwStock;
        },
        disableTip() {
            let verify = window.$context.isVerifyUser();
            let accountPurchaseValidation = this.data.get('accountPurchaseValidation');
            let hasQuota = this.data.get('hasQuota');
            let price = this.data.get('price');
            let loadingPrice = this.data.get('loadingPrice');
            let formData = this.data.get('formData');
            let allDcgwList = this.data.get('allDcgwList') || [];
            let allVpnList = this.data.get('allVpnList') || [];
            let l2gwStock = this.data.get('l2gwStock');
            if (window.$context.getCurrentRegionId() === AllRegion.HK02) {
                return '售罄！请您移步其他地域购买资源。';
            }
            if (!verify) {
                if (FLAG.NetworkSupportXS) {
                    return '温馨提示：您还没有实名认证，请先完成实名认证';
                } else {
                    return `温馨提示：您还没有实名认证，请立即去<a target="_BLANK" href="/qualify/#/qualify/index">
                        认证</a>`;
                }
            }
            // if (accountPurchaseValidation && !accountPurchaseValidation.status) {
            //     return (accountPurchaseValidation.failReason
            //         ? accountPurchaseValidation.failReason
            //         + '，请及时<a href="/finance/#/finance/account/recharge" target="_blank">充值</a>' : '');
            // }
            // if (this.data.get('inEipBlack')) {
            //     return '当前操作需要安全部审批！';
            // }
            if (!formData.vpcId) {
                return '请选择所在网络';
            }
            if (!hasQuota) {
                if (FLAG.NetworkSupportXS) {
                    return '二层网关配额不足';
                } else {
                    return `二层网关配额不足，如需增加配额请提交
                    <a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">工单</a>`;
                }
            }
            // if (!price || loadingPrice) {
            //     return '价格加载中';
            // }
            if (
                !allDcgwList.find(item => item.vpcId === formData.vpcId && item.status === 'running') &&
                !allVpnList.find(item => item.vpcId === formData.vpcId && item.status === 'active')
            ) {
                return '请选择已绑定专线网关或VPN网关的VPC';
            }
            if (!l2gwStock) {
                return '当前子网下二层网关库存不足，请切换其他子网或稍后再试';
            }
            return '';
        }
    };

    static filters = {
        getYear(value) {
            return `注：购买${value / 12}年8.3折`;
        }
    };

    initData() {
        return {
            FLAG,
            DocService,
            pageTitle: {
                backTo: '/network/#/vpc/l2gw/list',
                label: '返回',
                title: '创建实例'
            },
            klass: 'l2gw-create-wrap',
            steps: {
                datasource: [
                    {
                        title: '选择产品'
                    },
                    {
                        title: '确认订单'
                    }
                ],
                current: 1
            },
            purchaseLengthList: [
                {text: '1个月', value: 1},
                {text: '2个月', value: 2},
                {text: '3个月', value: 3},
                {text: '4个月', value: 4},
                {text: '5个月', value: 5},
                {text: '6个月', value: 6},
                {text: '7个月', value: 7},
                {text: '8个月', value: 8},
                {text: '9个月', value: 9},
                {text: '1年', value: 12, mark: '8.3折'},
                {text: '2年', value: 24, mark: '8.3折'},
                {text: '3年', value: 36, mark: '8.3折'}
            ],
            zoneList: [],
            formData: {},
            productTypeList: PayType.toArray('PREPAY', 'POSTPAY'),
            renewNumberList: Month.toArray(),
            flavorList: [
                {
                    alias: 'NORMAL',
                    text: '普通型',
                    value: 'normal'
                }
                // {
                //     alias: 'STANDARD',
                //     text: '标准型',
                //     value: 'standard'
                // },
                // {
                //     alias: 'HIGH',
                //     text: '增强型',
                //     value: 'high_performance'
                // }
            ],
            ipDatasource: [
                {
                    text: '自动分配',
                    value: 'auto'
                },
                {
                    text: '指定',
                    value: 'custom'
                }
            ],
            tunnelTypeList: [
                {
                    text: '专线网关',
                    value: 'ETGW'
                },
                {
                    text: 'VPN网关',
                    value: 'VPN'
                },
                {
                    text: 'SMART-WAN',
                    value: 'SD-WAN'
                }
            ],
            renewUnitList: TimeType.toArray('MONTH', 'YEAR'),
            regionList: [
                {
                    label: window.$context.getCurrentRegion().label,
                    value: window.$context.getCurrentRegionId()
                }
            ],
            hasQuota: false, // L2GW配额
            vpcList: [],
            buyBucketItems: [],
            flavorMessage: '普通型(带宽范围1-3Gbps,最大发包50万pps，连接子网数1)',
            sdk: {},
            price: null,
            rules: validateRules(this),
            resourceSDK: {},
            subnetDatasource: [],
            l2gwStock: true,
            urlQuery: getQueryParams()
        };
    }

    inited() {
        this.$http.getZoneList().then(data => {
            const zoneList = u.map(u.map(data, 'zone'), item => {
                return {
                    value: item,
                    text: zone.getLabel(item)
                };
            });
            this.data.set('zoneList', zoneList);
        });
        if (FLAG.NetworkSupportXS) {
            this.data.set('formData.productType', PayType.PREPAY);
            let purchaseLength = this.data.get('purchaseLengthList');
            this.data.set(
                'purchaseLengthList',
                purchaseLength.map(item => {
                    if (item.mark) {
                        return {
                            text: item.text,
                            value: item.value
                        };
                    } else return item;
                })
            );
        }
        this.data.set('resourceSDK', new ResourceGroupSDK(this.$http, window.$context));
        this.initFormData();
        this.loadVpcList();
        this.checkAccount();
        // const currentRegion = window.$context.getCurrentRegionId();
        // let flavorList = this.data.get('flavorList');
        // if (currentRegion === AllRegion.HKG) {
        //    let hkgFlavorList = flavorList.filter(item => item.value === '');
        //    this.data.set('flavorList', hkgFlavorList);
        // }
    }

    attached() {
        this.watch('formData.purchaseLength', value => {
            this.loadPrice();
        });
        this.watch('formData.maxClient', value => {
            this.loadPrice();
        });
        this.watch('formData.autoRenew', value => {
            this.updateBandWidth();
        });
        this.watch('formData.flavor', value => {
            this.flavorChange(value);
        });
        if (!window.$storage.get('l2gwSts')) {
            this.cancel();
        }
        // this.initOrderItems();
    }

    cancel() {
        location.hash = '#/vpc/l2gw/list';
    }

    initFormData() {
        let basicData = {
            productType: 'prepay',
            region: window.$context.getCurrentRegionId(),
            purchaseLength: 1,
            flavor: 'normal',
            bandWidth: '-',
            autoRenewTimeUnit: 'month',
            autoRenewTime: 1,
            type: 'auto',
            tunnelBindInstanceId: '',
            tunnelType: '',
            localTunnelIp: ''
        };
        this.data.set('formData', basicData);
    }

    resetFormData() {
        let formData = this.data.get('formData');
        let basicData = {
            productType: 'prepay',
            region: window.$context.getCurrentRegionId(),
            purchaseLength: 1,
            description: '',
            l2gwName: '',
            flavor: '',
            autoRenewTimeUnit: 'month',
            autoRenewTime: 1
        };
        let newData = {...formData, ...basicData};
        this.data.set('formData', newData);
    }

    initOrderItems() {
        let formData = this.data.get('formData');
        let showFlavor = this.data.get('showFlavor');
        this.loadPrice().then(price => {
            let orderConfig = getOrderConfig(formData, price, showFlavor);
            let order = new OrderItem(orderConfig, contextPipe);
            this.data.set('buyBucketItems', [order]);
        });
    }

    loadVpcList() {
        this.data.set('vpcId', this.data.get('urlQuery.vpcId'));
        let vpcId = this.data.get('vpcId');
        let array = [this.loadDcgwList(), this.loadVpnList(), this.$http.vpcList()];
        Promise.all(array).then(result => {
            this.data.set('allDcgwList', result[0]);
            this.data.set('allVpnList', result[1]);
            let data = result[2];
            let vpcs = u.map(data, item => ({
                text: `${item.name}（${item.shortId}）`,
                value: item.vpcId,
                shortId: item.shortId
            }));
            this.data.set('vpcList', vpcs);
            vpcId = vpcId || (vpcs && vpcs[0].value);
            this.data.set('formData.vpcId', vpcId);
            this.getL2gwQuota(vpcId);
            // this.loadSubnets(vpcId);
        });
    }

    getL2gwQuota(vpcId) {
        this.$http.l2gwQuota({vpcId}, kXhrOptions).then(res => {
            this.data.set('hasQuota', res.free > 0);
        });
    }

    flavorChange(value) {
        const placeholder = value
            ? value === 'high_performance'
                ? '增强型（6-10Gbps、最大发包是200万pps、连接子网数6'
                : '标准型(带宽范围3-5Gbps,最大发包100万pps，连接子网数3)'
            : '普通型(带宽范围1-3Gbps,最大发包50万pps，连接子网数1)';
        let instance = this.data.get('buyBucketItems[0]');
        instance.updateOrderItem('configDetail[3].value', l2gwFlavor.getTextFromValue(value));
        this.data.set('flavorMessage', placeholder);
        this.data.set('instance', instance);
        this.loadPrice();
    }

    // 自动续费按月按年
    onRenewUnitChange({value}) {
        this.data.set('formData.autoRenewTimeUnit', value);
        this.changeRenewTimeList(value);
        this.loadPrice();
    }

    onRenewTimeChange({value}) {
        this.data.set('formData.autoRenewTime', value);
        this.updateBandWidth();
        this.loadPrice();
    }

    changeRenewTimeList(value) {
        let timeList = value === 'year' ? Year.toArray() : Month.toArray();
        this.data.set('renewNumberList', timeList);
        this.data.set('formData.autoRenewTime', '');
        this.nextTick(() => {
            this.data.set('formData.autoRenewTime', timeList[0].value);
            this.updateBandWidth();
        });
    }

    updateBandWidth() {
        let formData = this.data.get('formData');
        let bandWidth = formData.bandWidth;
        let greBandwidth = formData.greBandwidth;
        let instance = this.data.get('buyBucketItems[0]');
        let autoRenewTimeUnit = formData.autoRenewTimeUnit === 'month' ? '月' : '年';
        let autoRenewText = formData.autoRenew ? '开通自动续费' + formData.autoRenewTime + autoRenewTimeUnit : '';
        instance?.updateOrderItem('configDetail[1].value', bandWidth + 'Mbps ' + autoRenewText);
        greBandwidth && instance.updateOrderItem('configDetail[3].value', greBandwidth + 'Gbps');
        this.data.set('buyBucketItems', [instance]);
    }

    updateOrderItems() {
        let formData = this.data.get('formData');
        let instance = this.data.get('buyBucketItems[0]');
        if (!instance) {
            return;
        }
        let price = this.data.get('price');
        let isPrepay = formData.productType === PayType.PREPAY;
        let quota = isPrepay ? `1个 * ${formData.purchaseLength}月` : '1个';
        let unit = isPrepay ? '' : '/分钟';
        setInstancePrice(instance, formData, price);
        instance.updateOrderItem('configDetail[2].value', quota);
        this.data.set('buyBucketItems', [instance]);
    }

    checkAccount(productType = PayType.PREPAY) {
        return this.$http
            .purchaseValidation(
                {
                    serviceType: 'L2GW',
                    productType
                },
                kXhrOptions
            )
            .then(res => this.data.set('accountPurchaseValidation', res));
    }

    productTypeChange({value}) {
        this.data.set('formData.productType', value);
        value === 'postpay' && this.data.set('formData.autoRenew', false);
        this.checkAccount(value);
        this.loadPrice();
    }

    vpcChange({value}) {
        this.data.set('hasQuota', '');
        this.data.set('tunnelBindInstanceList', []);
        this.data.set('subnetDatasource', []);
        this.data.set('formData.subnetId', '');
        this.data.set('formData.tunnelBindInstanceId', '');
        this.data.set('tunnelBindInstanceList', []);
        this.getL2gwQuota(value);
        this.loadSubnets(value, this.data.get('formData.zone'));
        // this.loadSubnets(value);
        if (this.data.get('formData.tunnelType')) {
            this.tunnelTypeChange({value: this.data.get('formData.tunnelType')});
        }
    }

    loadPrice() {
        let configs = this.getPriceConfig();
        this.data.set('loadingPrice', true);
        return this.$http
            .priceV3({configs})
            .then(res => {
                this.data.set('price', {
                    money: res[0]?.price,
                    perMoney: res[0]?.price
                });
                this.data.set('loadingPrice', false);
                this.updateOrderItems();
                return {
                    money: res[0]?.price,
                    perMoney: res[0]?.price
                };
            })
            .catch(err => this.data.set('loadingPrice', false));
    }

    getPricePayload() {
        const formData = this.data.get('formData');
        const payload = {
            billing: {
                billingMethod: formData.productType,
                reservation: {
                    reservationLength: formData.productType === 'prepay' ? formData.purchaseLength : ''
                }
            }
        };
        payload.flavor = formData.flavor;

        return payload;
    }

    onReset() {
        this.resetFormData();
        this.loadPrice();
    }

    async addShoppingCart() {
        let form = this.ref('form');
        await form.validateFields();
        this.data.set('cartConfirming', true);
        this.loadPrice().then(() => {
            const showFlavor = this.data.get('flavor');
            let formData = this.data.get('formData');
            let config = getConfirmConfig(formData, showFlavor);
            this.$http
                .addVpnShoppingCart({
                    paymentMethod: [],
                    items: [
                        {
                            config: config,
                            paymentMethod: []
                        }
                    ]
                })
                .then(result => {
                    this.data.set('cartConfirming', false);
                    window.shoppingCart?.showSuccessTip();
                    window.shoppingCart?.refreshCount();
                })
                .catch(result => {
                    this.data.set('cartConfirming', false);
                });
        });
    }

    setSdk() {
        let formData = this.data.get('formData');
        let instance = this.data.get('buyBucketItems[0]');
        const sdkOptions = {
            type: 'NEW',
            serviceType: 'L2GW',
            serviceName: '二层网关',
            productType: formData.productType,
            region: formData.region,
            items: [instance]
        };
        const sdk = new BillingSDK(sdkOptions, contextPipe);
        this.data.set('sdk', sdk);
    }

    async goToConfirm() {
        let formData = this.data.get('formData');
        let form = this.ref('form');
        await form.validateFields();
        let config = {
            name: formData.l2gwName,
            description: formData.description,
            // tunnelType: formData.tunnelType,
            // tunnelBindInstanceId: formData.tunnelBindInstanceId,
            resourceGroupId: formData.resourceGroupId,
            vpcId: formData.vpcId,
            subnetId: formData.subnetId,
            flavor: formData.flavor
            // l2gwType: formData.flavor,
            // localTunnelIp: formData.type !== 'custom' ?  '' : formData.localTunnelIp
        };
        let submitRequester = null;
        if (!this.data.get('urlQuery.l2gwId')) {
            submitRequester = this.$http.l2gwCreate;
        } else {
            submitRequester = this.$http.l2gwUpdate;
        }
        try {
            await submitRequester.call(this.$http, config);
            location.hash = '#/vpc/l2gw/list';
        } catch (err) {
            this.data.set('confirmed', false);
        }
        // this.data.set('steps.current', 2);
        // this.setSdk();
        // this.data.set('useCoupon', formData.productType === 'prepay');
    }

    backToOrder() {
        this.data.set('steps.current', 1);
    }

    // 确认订单的时候 询价
    async onConfirm() {
        await this.ref('orderConfirm').validateAgreement();
        this.data.set('confirming', true);
        this.loadPrice().then(() => {
            const showFlavor = this.data.get('flavor');
            let formData = this.data.get('formData');
            let instance = this.data.get('buyBucketItems[0]');
            let config = getConfirmConfig(formData, showFlavor);
            instance.set('config', config);
            let sdk = this.data.get('sdk');
            sdk.confirmOrder({
                url: '/api/vpn/vpn/order/confirm/new',
                type: 'NEW',
                instances: [instance]
            })
                .then(result => {
                    window.location.href = result.url;
                })
                .catch(result => {
                    result.url && (window.location.href = result.url);
                    this.data.set('confirming', false);
                });
        });
    }

    onRegionChange() {
        location.hash = '#/vpc/l2gw/list';
    }
    resourceChange({value}) {
        this.data.set('formData.resourceGroupId', value.data.groupId);
    }
    getPriceConfig() {
        const formData = this.data.get('formData');
        let configs = [
            {
                serviceType: 'L2GW',
                serviceName: '二层网关',
                productType: formData.productType,
                type: 'NEW',
                scene: 'NEW',
                region: window.$context.getCurrentRegionId(),
                chargeItem: formData.productType === 'prepay' ? 'Cpt2' : 'RunningTimeMinutes',
                flavor: this.getFlavor(),
                timeUnit: formData.productType === 'prepay' ? 'month' : 'minute',
                duration: formData.productType === 'prepay' ? formData.purchaseLength : 1
            }
        ];
        return configs;
    }
    getFlavor() {
        let payload = [];
        // 区分是否有网关规格
        payload.push({name: 'subServiceType', value: 'high_performance', scale: 1});
        return payload;
    }

    loadSubnets(vpcLongId, value) {
        this.data.set('loading', true);
        this.data.set('formData.subnetId', '');
        let vpcList = this.data.get('vpcList');
        let vpcId = vpcList.find(item => item.value === vpcLongId)?.shortId;
        let query = {
            vpcId,
            zone: value
        };
        this.$http
            .getSubnetFromZone(query)
            .then(res => {
                let datasource = [];
                res &&
                    u.each(res, item =>
                        datasource.push({
                            value: item.subnetId,
                            text: item.name + (item.cidr ? '（' + item.cidr + '）' : ''),
                            cidr: item.cidr
                        })
                    );
                this.data.set('subnetDatasource', datasource);
                this.data.set('loading', false);
            })
            .catch(() => this.data.set('loading', false));
    }
    tunnelTypeChange({value}) {
        this.data.set('formData.tunnelBindInstanceId', '');
        this.data.set('tunnelBindInstanceList', []);
        if (value === 'ETGW') {
            this.loadDcgwList();
        } else if (value === 'VPN') {
            this.loadVpnList();
        } else {
            this.loadSdwanList();
        }
    }
    loadDcgwList() {
        let query = {
            vpcId: this.data.get('formData.vpcId'),
            pageNo: 1,
            pageSize: 100000
        };
        return this.$http.dcgwList(query, kXhrOptions).then(res => {
            let result = [];
            u.each(res.result, item => {
                if (u.indexOf([DcGatewayStatus.RUNNING], item.status) > -1) {
                    let name = item.name + '/' + item.id;
                    result.push({
                        value: item.id,
                        text: name
                    });
                }
            });
            this.data.set('tunnelBindInstanceList', result);
            return Promise.resolve(res.result);
        });
    }
    loadVpnList() {
        let vpcId = this.data.get('formData.vpcId');
        let query = {
            vpcId,
            pageNo: 1,
            pageSize: 100000
        };
        return this.$http.getVpnList(query, kXhrOptions).then(data => {
            let result = [];
            u.each(data.result, item => {
                if (u.indexOf([VpnStatus.ACTIVE], item.status) > -1) {
                    result.push({
                        value: item.vpnId,
                        text: `${item.vpnName}/${item.vpnId}`
                    });
                }
            });
            this.data.set('tunnelBindInstanceList', result);
            return Promise.resolve(data.result);
        });
    }
    loadSdwanList() {
        const payload = {
            pageNo: 1,
            pageSize: 100000
        };
        this.$http.getEdgeList(payload, kXhrOptions).then(data => {
            let result = data.result;
            u.each(result, item => {
                if (u.indexOf([sdwanStatus.ONLINE], item.status) > -1) {
                    result.push({
                        value: item.edgeId,
                        text: `${item.name}/${item.edgeId}`
                    });
                }
            });
            this.data.set('tunnelBindInstanceList', result);
        });
    }
    zoneChange({value}) {
        this.data.set('formData.subnetId', '');
        this.data.set('subnetDatasource', []);
        this.loadSubnets(this.data.get('formData.vpcId'), value);
    }
    subnetChange({value}) {
        let payload = {
            subnetId: value,
            count: 1,
            flavor: 'normal'
        };
        this.$http.checkL2gwStock(payload).then(res => {
            this.data.set('l2gwStock', res.hasStock);
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(L2GWCreate));
