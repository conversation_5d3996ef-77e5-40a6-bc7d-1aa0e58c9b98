import {PayType, l2gwStatus} from '@/pages/sanPages/common/enum';
export const columns = [
    {
        name: 'id',
        label: '网关名称/ID',
        width: 165,
        fixed: 'left'
    },
    {
        name: 'status',
        label: '状态',
        width: 110,
        filter: {
            options: [
                {
                    text: '全部',
                    value: ''
                },
                ...l2gwStatus.toArray()
            ],
            value: ''
        }
    },
    {
      name: 'flavor',
      label: '规格',
      width: 100,
    },
    {
        name: 'vpcId',
        label: '所在网络',
        width: 180,
    },
    {
      name: 'subnetId',
      label: '子网',
      width: 150,
    },
    // {
    //     name: 'productType',
    //     label: '支付方式',
    //     width: 120,
    //     filter: {
    //         options: [
    //             {
    //                 text: '全部',
    //                 value: ''
    //             },
    //             {
    //                 text: PayType.getTextFromAlias('PREPAY'),
    //                 value: PayType.PREPAY
    //             },
    //             {
    //                 text: PayType.getTextFromAlias('POSTPAY'),
    //                 value: PayType.POSTPAY
    //             }
    //         ],
    //         value: '',
    //     }
    // },
    // {
    //     name: 'l2gwConnType',
    //     label: '隧道连接方式',
    //     width: 180,
    // },
    // {
    //     name: 'l2gwConnId',
    //     label: '隧道连接实例ID',
    //     width: 180
    // },
    // {
    //     name: 'localTunnelIp',
    //     label: '本端隧道IP地址',
    //     width: 120
    // },
    {
      name: 'tunnelIp',
      label: '本端隧道IP',
      width: 110
    },
    {
        name: 'primaryIp',
        label: '主接口IP',
        width: 110
    },
    {
      name: 'standbyIp',
      label: '备接口IP',
      width: 110
    },
    {
        name: 'description',
        label: '描述',
        width: 120
    },
    // {
    //     name: 'groups',
    //     label: '资源分组',
    //     width: 100
    // },
    {
        name: 'opt',
        label: '操作',
        width: 80,
        fixed: 'right'
    }
];
