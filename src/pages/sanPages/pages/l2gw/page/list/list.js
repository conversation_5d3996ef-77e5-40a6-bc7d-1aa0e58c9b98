/*
 * @description: 专线网关列表页
 * @file: network/l2gw/pages/List.js
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {Notification} from '@baidu/sui';
import {OutlinedRefresh, OutlinedPlus, OutlinedExclamation, OutlinedEditingSquare} from '@baidu/sui-icon';

import rules from '../../rules';
import {columns} from './tableField';
import Confirm from '@/pages/sanPages/components/confirm';
import BcmDetail from '@/pages/sanPages/pages/eni/components/bcmDetail';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import {StsConfig} from '@/pages/sanPages/utils/config';
import {DocService, ContextService} from '@/pages/sanPages/common';
import {isOnline} from '@/pages/sanPages/utils/common';
import {l2gwStatus, PayType, l2gwFlavor} from '@/pages/sanPages/common';
import {ResourceGroupDialog} from '@baidu/bce-resourcegroup-sdk-san';
import {ResourceGroupSDK} from '@baidu/bce-resourcegroup-sdk';
import '../../style/list.less';
import L2gwConnList from '../conn/list/list';
import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import DOCUMENT_ICON from '@/img/document_normal.svg?url';
import DOCUMENT_ICON_COLOR from '@/img/document_color.svg?url';

const {invokeSUI, invokeSUIBIZ, template, invokeComp} = decorators;
const tpl = html`
<template>
<s-app-list-page class="{{klass}}">
    <div slot="pageTitle">
        <div class="l2gw-list-header">
            <span class="title">{{title}}</span>
            <vpc-select
                class="vpc-select"
                on-int="vpcInt"
                on-change="vpcChange" />
            <div class="header-button-wrap">
                <div s-if="{{!FLAG.NetworkSupportXS}}" class="link-wrap">
                    <a
                        s-ref="introduce"
                        href="javascript:void(0)"
                        class="help-file function-introduce"
                        on-click="handleShowCard"
                        on-mouseenter="handleMouseEnter('introduce')"
                        on-mouseleave="handleMouseLeave('introduce')"
                        s-if="{{!FLAG.NetworkSupportXS}}"
                    >
                        <img class="s-icon" src="{{introduceIcon}}" />功能简介
                    </a>
                    <div class="service-best-practice"></div>
                    <a target="_BLANK" href="{{DocService.l2gw_index}}" style="margin-right:16px">
                        二层网关最佳实践
                    </a>
                    <s-icon name="warning-new" class="warning-class"/>
                    <a href="{{DocService.l2gw_helpFile}}"
                        target="_blank"
                        class="help-file"
                        s-if="{{!FLAG.NetworkSupportXS}}">
                        帮助文档
                    </a>
                </div>
            </div>
        </div>
        <introduce-panel
            isShow="{{show}}"
            klass="{{'endpoint-peerconn-wrapper'}}"
            title="{{introduceTitle}}"
            description="{{description}}"
            on-toggle="handleToggle($event)"
        ></introduce-panel>
    </div>
    <div class="list-page-tb-left-toolbar" slot="bulk">
        <s-tooltip
            trigger="{{createL2gw.disable || !l2gwSts ? 'hover' : ''}}" placement="top">
            <!--bca-disable-next-line-->
            <div slot="content">{{createL2gw.message || '当前登录的子账号没有开通产品服务的权限，请联系主账号授予开通服务权限' | raw}}</div>
            <s-button
                disabled="{{createL2gw.disable || quotaLoading || !l2gwSts}}"
                skin="primary" on-click="onCreate">
                <outlined-plus/>
                创建二层网关
            </s-button>
        </s-tooltip>
        <s-tooltip class="left_class"
            trigger="{{release.disable ? 'hover' : ''}}" placement="top">
            <!--bca-disable-next-line-->
            <div slot="content">{{release.message | raw}}</div>
            <s-button on-click="onRelease" disabled="{{release.disable}}">
                释放</s-button>
        </s-tooltip>
        <!--<edit-tag
            class="left_class"
            selectedItems="{{selectedItems}}"
            on-success="refresh"
            type="L2GW"
        ></edit-tag>-->
        <!--<div
            class="intro-warp left_class"
            data-intro="这里是批量操作区"
        >
            <s-tooltip content="请先选择实例对象"
                trigger="{{operationDisabled ? 'hover' : ''}}" placement="top">
                <s-select placeholder="批量操作" value="{=operation=}"
                    disabled="{{operationDisabled}}"
                    class="{{!operationDisabled ? 'placeholder-style' : ''}}"
                    on-change="onOperationChange">
                    <s-select-option class="operation-select" s-for="item in OperationType" value="{{item.value}}"
                        label="{{item.label}}" disabled="{{item.disabled}}">
                        <s-tooltip placement="right"
                            trigger="{{item.message ? 'hover' : ''}}" width="200">
                            <div slot="content">-->
                            <!--bca-disable-next-line-->
                                <!--{{item.message | raw}}
                            </div>
                            <div>{{item.label}}</div>
                        </s-tooltip>
                    </s-select-option>
                </s-select>
            </s-tooltip>
        </div>-->
    </div>
    <div slot="filter" class="toolbar_right">
        <s-search width="{{230}}"
            value="{=searchbox.keyword=}"
            placeholder="{{searchbox.placeholder}}"
            on-search="onSearch">
            <s-select slot="options" width="{{130}}"
                value="{=searchbox.keywordType=}" on-change="onSearchboxChange">
                <s-select-option s-for="item in searchbox.keywordTypes"
                    value="{{item.value}}" label="{{item.label}}"/>
            </s-select>
        </s-search>
        <s-button on-click="refresh" class="s-icon-button"><outlined-refresh class="icon-class"/></s-button>
        <custom-column
            class="left_class"
            columnList="{{customColumn.datasource}}"
            initValue="{{customColumn.value}}"
            type="l2gw"
            on-init="initColumns"
            on-change="onCustomColumns">
        </custom-column>
    </div>
    <s-table
        columns="{{table.columns}}"
        loading="{{table.loading}}"
        error="{{table.error}}"
        datasource="{{table.datasource}}"
        on-selected-change="tableSelected($event)"
        on-filter="onFilter"
        has-Expand-Row="{{true}}"
        expandedIndex="{{expandIndex}}"
        on-exprow-expand="onRowExpand"
        selection="{=table.selection=}">
        <div slot="empty">
            <s-empty on-click="onCreate">
            </s-empty>
        </div>
        <div slot="expanded-row">
            <l2gw-conn-list
                vpcId="{{row.vpcUuid}}"
                status="{{row.status}}"
                l2gwId="{{row.id}}"
                instance="{{row}}"></l2gw-conn-list>
        </div>
        <div slot="c-id" class="l2gw-id-widget">
            <span class="truncated">
                <s-tooltip content="{{row.name}}">
                    <a href="#/vpc/l2gw/detail?vpcId={{row.vpcUuid}}&l2gwId={{row.id}}">
                        {{row.name}}
                    </a>
                </s-tooltip>
            </span>
            <s-popover s-ref="popover-name-{{rowIndex}}" placement="top" trigger="click"
                class="edit-popover-class">
                <div class="edit-wrap" slot="content">
                    <s-input value="{=edit.name.value=}"
                        width="320"
                        placeholder="请输入名称"
                        on-input="onEditInput($event, rowIndex, 'name')"/>
                    <div class="edit-tip">大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65</div>
                    <s-button skin="primary" s-ref="editBtn-name-{{rowIndex}}" disabled="{{true}}"
                        on-click="editConfirm(row, rowIndex, 'name')">确定</s-button>
                    <s-button on-click="editCancel(rowIndex, 'name')">取消</s-button>
                </div>
                <outlined-editing-square class="name-icon" on-click="onInstantEdit(row, rowIndex, 'name')"/>
            </s-popover>
            <br>
            <span class="truncated">
                {{row.id}}
            </span>
            <s-clip-board class="name-icon" text="{{row.id}}"/>
        </div>
        <div slot="c-vpcId">
            <div class="truncated">
                <s-tooltip content="{{row.vpcName || '-'}}">
                    <a href="#/vpc/instance/detail?vpcId={{row.vpcUuid}}" class="list-link">{{row.vpcName || '-'}}</a>
                </s-tooltip>
            </div>
            <span>（{{row.vpcCidr}}）</span>
            <br />
            <span class="truncated">
                {{row.vpcId || '-'}}
            </span>
        </div>
        <div slot="c-productType">
            <span>{{row | getProductType}}</span>
            <s-popover s-if="row.enableProduct" placement="top">
                <div slot="content">
                    该实例已开通计费变更-预付费转后付费，将会在到期后转为后付费资源，请关注！如需进行续费、升级等操作，请先取消计费变更，谢谢！
                </div>
                <s-icon class="tip-icon-wrap" name="warning-mark"/>
            </s-popover>
        </div>
        <div slot="c-flavor">
            {{row.flavor | getFlavor}}
        </div>
        <template slot="c-groups">
            <p s-for="item in row.resourceGroups">{{item.name}}</p>
        </template>
        <div slot="c-status">
            <span class="{{row.status | statusClass}}">{{row.status | statusText}}</span>
        </div>
        <div slot="c-l2gwConnType">
          <span>{{row | getTunnelType}}</span>
        </div>
        <div slot="c-l2gwConnType">
          <span>{{row | getTunnelType}}</span>
        </div>
        <div slot="c-tunnelIp">
          <span>{{row.tunnelIp || '-'}}</span>
        </div>
        <div slot="c-standbyIp">
          <span>{{row.standbyIp || '-'}}</span>
        </div>
        <div slot="c-primaryIp">
          <span>{{row.primaryIp || '-'}}</span>
        </div>
        <div slot="c-subnetId">
          <a class="truncated" href="#/vpc/subnet/detail?subnetId={{row.subnetUuid}}"
              title="{{row.subnetName}}（{{row.subnetCidr}}）">
              {{row.subnetName}}
          </a>
          <br>
          <span s-f="row.subnetCidr">
              {{row.subnetCidr ? ('(' + row.subnetCidr +')') : ''}}
              {{row.subnetIpv6Cidr ? ('(' + row.subnetIpv6Cidr +')') : ''}}
          </span>
        </div>
        <div slot="c-description">
            <span class="truncated" title="{{row.description}}">{{row.description || '-'}}</span>
            <s-popover s-ref="popover-description-{{rowIndex}}" placement="top" trigger="click"
                class="edit-popover-class">
                <div class="edit-wrap" slot="content">
                    <s-input value="{=edit.description.value=}"
                        width="160"
                        on-input="onEditInput($event, rowIndex, 'description')"/>
                    <div class="edit-tip">描述不能超过200个字符</div>
                    <s-button skin="primary" s-ref="editBtn-description-{{rowIndex}}" disabled="{{true}}"
                        on-click="editConfirm(row, rowIndex, 'description')">确定</s-button>
                    <s-button on-click="editCancel(rowIndex, 'description')">取消</s-button>
                </div>
                <outlined-editing-square class="name-icon" on-click="onInstantEdit(row, rowIndex, 'description')"/>
            </s-popover>
        </div>
        <div slot="c-opt" class="operations">
            <s-button skin="stringfy" on-click="showMonitor(row)" s-if="row.eniId">监控</s-button>
            <s-tooltip
                class="left_class"
                trigger="{{isShowHover(row) ? 'hover' : ''}}"
                placement="top"
            >
                <!--bca-disable-next-line-->
                <div slot="content">{{row | disableReleaseTip | raw}}</div>
                <s-button skin="stringfy" disabled="{{row | disableReleaseTip}}" on-click="onDelete(row)">释放</s-button>
            </s-tooltip>
        </div>
    </s-table>
    <s-pagination
        s-if="{{pager.total}}"
        slot="pager"
        layout="{{'total, pageSize, pager, go'}}"
        pageSize="{{pager.pageSize}}"
        total="{{pager.total}}"
        page="{{pager.page}}"
        on-pagerChange="onPagerChange"
        on-pagerSizeChange="onPagerSizeChange" />
    <resource-group-dialog
        s-if="{{showResource}}"
        sdk="{{resourceSDK}}"
        resource="{{resource}}"
        on-success="oncommit"
        on-cancel="onCancel"/>
</s-biz-page>
</template>`;

@template(tpl)
@invokeComp('@bind-dcphy', '@edit-tag', '@vpc-select', '@search-tag', '@custom-column', '@introduce-panel')
@invokeSUI
@invokeSUIBIZ
class L2gwList extends Component {
    static components = {
        'resource-group-dialog': ResourceGroupDialog,
        'outlined-refresh': OutlinedRefresh,
        'outlined-plus': OutlinedPlus,
        's-warning': OutlinedExclamation,
        'outlined-editing-square': OutlinedEditingSquare,
        'l2gw-conn-list': L2gwConnList
    };

    static filters = {
        statusClass(value) {
            return l2gwStatus.fromValue(value).styleClass || '';
        },
        statusText(value) {
            return value ? l2gwStatus.getTextFromValue(value) : '-';
        },
        getProductType(item) {
            return PayType.getTextFromValue(item.productType);
        },
        getFlavor(value) {
            return l2gwFlavor.getTextFromValue(value);
        },
        getTunnelType(item) {
            return item.tunnelType === 'ETGW'
                ? '专线网关'
                : item.tunnelType === 'VPN'
                  ? 'VPN网关'
                  : item.tunnelType === 'SD-WAN'
                    ? 'SMART-WAN'
                    : '';
        },
        disableReleaseTip(row) {
            const {status} = row;
            let tip = '';
            if (['deleted', 'error', 'switching'].includes(status)) {
                tip = '当前状态不允许释放';
            }
            return tip;
        }
    };

    initData() {
        const allColumns = columns.slice();
        const customColumnDb = allColumns.map(item => ({
            text: item.label,
            value: item.name,
            disabled: item.name === 'id' || item.name === 'opt'
        }));
        return {
            FLAG,
            DocService,
            klass: 'vpc-l2gw-list',
            searchbox: {
                keyword: '',
                placeholder: '请输入二层网关名称进行搜索',
                keywordType: 'name',
                keywordTypes: [
                    {value: 'name', label: '二层网关名称'},
                    {value: 'id', label: '二层网关ID'}
                ]
            },
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                allColumns,
                datasource: [],
                selectedItems: []
            },
            OperationType: [
                {label: '计费变更', value: 'ALTER_PRODUCTTYPE'},
                {label: '取消计费变更', value: 'CANCEL_ALTER_PRODUCTTYPE'}
            ],
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            customColumn: {
                value: ['id', 'status', 'flavor', 'vpcId', 'subnetId', 'tunnelIp', 'description', 'opt'],
                datasource: customColumnDb
            },
            order: {},
            edit: {
                name: {
                    value: '',
                    error: true,
                    visible: false
                },
                description: {
                    value: '',
                    error: true,
                    visible: false
                }
            },
            showMode: '',
            expandIndex: [],
            noSupportTag: true,
            resourceSDK: new ResourceGroupSDK(window.$http, window.$context),
            title: '二层网关',
            show: true,
            introduceEle: null,
            introduceTitle: '二层网关',
            description:
                '百度智能云二层网关（LAYER TWO GATEWAY，简称LTGW）是一款基于私有网络VPC提供的大二层等增强网络能力，底层Overlay到专线或VPN，帮助企业用户实现在IDC内主机私网IP地址不变的情况下，进行主机实例粒度的迁移，极大地促进了用户快速上云的进程，实现了用户构建混合云网络实现弹性、容灾等场景的需求。',
            introduceIcon: INTRODUCE_ICON_COLOR,
            documentIcon: DOCUMENT_ICON,
            urlQuery: getQueryParams()
        };
    }
    async inited() {
        this.setOperationMessage();
        try {
            await this.checkIamStsRole();
        } catch {}
    }
    // 设置表格列
    setTableColumns(customColumnNames) {
        customColumnNames = customColumnNames ? customColumnNames : this.data.get('customColumn.value');
        let allColumns = this.data.get('table.allColumns');
        let columns = [];
        allColumns.forEach(item => customColumnNames.indexOf(item.name) > -1 && columns.push(item));
        this.data.set('table.columns', columns);
    }
    initColumns(value) {
        this.setTableColumns(value);
    }
    // 自定义表格列
    onCustomColumns(value) {
        this.data.set('customColumn.value', value);
        this.setTableColumns(value);
    }

    setOperationMessage() {
        let selectedItem = this.data.get('table.selectedItems');
        let checkResult = checker.check(rules, selectedItem, '');
        let OperationType = u.cloneDeep(this.data.get('OperationType'));
        OperationType.forEach(item => {
            if (checkResult[item.value]) {
                item.disabled = checkResult[item.value].disable;
                item.message = checkResult[item.value].message;
            }
        });
        this.data.set('OperationType', OperationType);
    }

    tableSelected(e) {
        this.data.set('selectedItems', e.value.selectedItems);
        let {release} = checker.check(rules, e.value.selectedItems);
        this.data.set('release', release);
    }
    onRowExpand(e) {
        const {rowIndex} = e.value;
        this.data.set('expandIndex', [rowIndex]);
    }

    onCreate() {
        location.hash = '#/vpc/l2gw/create?vpcId=' + window.$storage.get('vpcId');
    }

    attached() {
        window.$storage.get('showL2gwIntroduce') === false && this.data.set('show', false);
        let vpcId = this.data.get('urlQuery.vpcId');
        if (vpcId) {
            window.$storage.set('vpcId', vpcId);
        }
        let {release} = checker.check(rules, []);
        this.data.set('release', release);
        this.data.set('introduceEle', this.ref('introduce'));
    }

    // 点击修改icon
    onInstantEdit(row, rowIndex, type) {
        this.data.set(`edit.${type}.value`, row[`${type}`]);
        this.data.set(`edit.${type}.error`, false);
        const editWrap = this.ref(`popover-${type}-${rowIndex}`);
        editWrap.data.set('visible', !editWrap.data.get('visible'));
    }

    // 编辑弹框-输入名称/描述
    onEditInput(e, rowIndex, type) {
        let result =
            type === 'name'
                ? e.value === '' || !/^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/.test(e.value)
                : e.value.length > 200;
        this.data.set(`edit.${type}.error`, result);
        this.data.set(`edit.${type}.value`, e.value);
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', result);
    }

    editConfirm(row, rowIndex, type) {
        let payload = {};
        let edit = this.data.get(`edit.${type}`);
        if (edit.error) {
            return;
        }
        payload[type] = edit.value;
        this.$http.l2gwUpdate(row.id, payload).then(() => {
            this.editCancel(rowIndex, type);
            Notification.success('修改成功');
            this.loadPage();
        });
    }

    editCancel(rowIndex, type) {
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', true);
        this.ref(`popover-${type}-${rowIndex}`).data.set('visible', false);
    }

    onPagerChange(e) {
        this.resetTable();
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }

    onPagerSizeChange(e) {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', e.value.pageSize);
        this.loadPage();
    }

    onFilter(e) {
        let name = e.field.name;
        let value = e[name] ? e[name] : '';
        this.data.set('filter.' + name, value);
        this.loadPage();
    }

    onRelease() {
        let confirm = new Confirm({
            data: {
                open: true,
                content: '确认释放选中的二层网关？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            let id = this.data.get('selectedItems').map(item => item.id);
            this.$http.l2gwRelease({l2GatewayId: id[0]}).then(() => {
                this.refresh();
                Notification.success('释放成功', {placement: 'topRight'});
            });
        });
    }

    onDelete(row) {
        let confirm = new Confirm({
            data: {
                open: true,
                content: '确认释放选中的二层网关？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http.l2gwRelease({l2GatewayId: row.id}).then(() => {
                this.refresh();
                Notification.success('释放成功', {placement: 'topRight'});
            });
        });
    }

    edit(item) {
        location.hash = '#/vpc/l2gw/create?vpcId=' + item.vpcUuid + '&l2gwId=' + item.id;
    }

    onSearch() {
        this.data.set('pager.page', 1);
        this.resetTable();
        return this.loadPage();
    }

    refresh() {
        this.resetTable();
        return this.loadPage();
    }

    // 重置列表
    resetTable() {
        this.data.set('pager.page', 1);
        this.data.set('table.selection.selectedIndex', []);
    }

    getSearchCriteria() {
        let searchParam = this.data.get('searchbox');
        let searchPayload = {};
        if (searchParam.keywordType === 'name') {
            searchPayload['name'] = searchParam.keyword;
        } else {
            searchPayload['l2GatewayId'] = searchParam.keyword;
        }
        const {pager, order, filter} = this.data.get('');
        const vpcId = window.$storage.get('vpcId');
        return u.extend({}, searchPayload, order, {vpcId}, {pageNo: pager.page, pageSize: pager.pageSize}, filter);
    }

    loadPage(payload) {
        if (!this.data.get('l2gwSts')) {
            return;
        }
        this.getL2GWQuota();
        this.data.set('table.loading', true);
        payload = payload || this.getSearchCriteria();
        if (payload.keywordType === 'resGroupId') {
            payload.keywordType = 'resourceGroupName';
        }
        this.$http
            .l2gwList(payload)
            .then(data => {
                this.data.set('table.datasource', data.result);
                this.data.set('pager.total', data.totalCount);
                this.data.set('table.loading', false);
            })
            .catch(() => this.data.set('table.loading', false));
    }

    vpcInt() {
        this.loadPage();
    }

    vpcChange() {
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    onRegionChange() {
        location.reload();
    }
    oncommit() {
        this.data.set('showResource', false);
        this.loadPage();
    }
    onCancel() {
        this.data.set('showResource', false);
    }
    changeResourceGroup(row) {
        let resource = {
            resourceId: row.id,
            serviceType: 'L2GW'
        };
        this.data.set('resource', resource);
        this.data.set('showResource', true);
    }
    getL2GWQuota() {
        this.data.set('quotaLoading', true);
        return this.$http
            .l2gwQuota()
            .then(data => {
                let message =
                    '二层网关配额不足。如需增加配额请提交' +
                    `<a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">` +
                    '工单' +
                    '</a>';
                this.data.set('createL2gw.disable', data.free <= 0);
                this.data.set('createL2gw.message', data.free > 0 ? '' : message);
                this.data.set('quota', data.total);
            })
            .finally(e => this.data.set('quotaLoading', false));
    }
    onOperationChange(e) {
        const methodMap = {
            ALTER_PRODUCTTYPE: this.alterProduct,
            CANCEL_ALTER_PRODUCTTYPE: this.cancelAlterProduct
        };
        let requester = methodMap[e.value].bind(this);
        requester();
    }
    alterProduct() {
        let selectedItems = this.data.get('table.selectedItems');
        let ids = u.pluck(selectedItems, 'id');
        let isPrepay = selectedItems[0].productType === PayType.PREPAY;
        let type = isPrepay ? 'TO_POSTPAY' : 'TO_PREPAY';
        let url = isPrepay ? '/api/vpn/vpn/order/confirm/toPostPay' : '/api/l2gw/order/confirm/toPostPay';
        alterProductType('VPN', ids, type, null, url);
    }
    cancelAlterProduct(e) {
        let selectedItems = this.data.get('table.selectedItems');
        let ids = u.pluck(selectedItems, 'id');
        let confirm = new Confirm({
            data: {
                title: '取消计费变更',
                content:
                    '确认取消计费变更？<br>您已开通计费变更-预付费转后付费功能。<br>实例将会在到期后自动转换为后付费的计费方式。'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http.vpnCancelAlterProductType({ids}).then(() => {
                Notification.success('取消计费变更成功');
                this.loadPage();
            });
        });
    }
    showMonitor(row) {
        const bcm = new BcmDetail({
            data: {
                name: row.name,
                payload: row,
                type: 'l2gw'
            }
        });
        bcm.attach(document.body);
    }
    onSearchboxChange({value}) {
        const placeholder = {
            name: '请输入二层网关名称进行搜索',
            id: '请输入二层网关ID进行搜索'
        };
        this.data.set('searchbox.placeholder', placeholder[value]);
        this.data.set('searchbox.keyword', '');
    }
    // 二层网关服务授权
    checkIamStsRole() {
        const AllRegion = this.$context.getEnum('AllRegion');
        const roleName = StsConfig.L2GW.roleName;
        const isSubUser = window.$context.isSubUser();
        if (!window.$storage.get('l2gwSts') && !isSubUser) {
            return this.$http
                .iamStsRoleActivate(
                    u.extend(
                        {
                            roleName,
                            accountId: window.$context.getUserId()
                        },
                        isOnline() ? StsConfig.L2GW.online : StsConfig.L2GW.sandbox
                    ),
                    {region: AllRegion.BJ}
                )
                .then(() => this.data.set('l2gwSts', true));
        } else {
            this.data.set('l2gwSts', true);
        }
    }
    /**
     * @description 介绍卡片变化时回调
     * @param {boolean} flag
     */
    handleToggle(flag) {
        this.data.set('show', flag);
        window.$storage.set('showL2gwIntroduce', false);
        this.data.set('introduceIcon', INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#151B26';
    }
    handleShowCard() {
        if (!this.data.get('show')) {
            window.$storage.set('showL2gwIntroduce', true);
            this.data.set('show', true);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type) {
        if (type === 'introduce') {
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
            const ele = this.data.get('introduceEle');
            ele.style.color = '#2468F2';
        } else {
            this.data.set('documentIcon', DOCUMENT_ICON_COLOR);
        }
    }
    handleMouseLeave(type) {
        const isShow = this.data.get('show');
        if (type === 'introduce') {
            this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
            const ele = this.data.get('introduceEle');
            !isShow && (ele.style.color = '#151B26');
        } else {
            this.data.set('documentIcon', DOCUMENT_ICON);
        }
    }
    isShowHover(row) {
        return ['deleted', 'error', 'switching'].includes(row.status);
    }
}
export default San2React(Processor.autowireUnCheckCmpt(L2gwList));
