.vpn-confirm-wrap {
    .s-dialog > .s-dialog-wrapper .s-dialog-header {
        background: #fff;
    }
    .s-dialog > .s-dialog-wrapper .s-dialog-content {
        min-height: 50px;
        .s-icon {
            margin-right: 15px;
        }
    }
    .icon-warning-new {
        color: #fbb515;
        font-size: 22px;
        vertical-align: middle;
        margin-right: 20px;
    }
}
.l2gw-detail-main-warp {
    width: 100%;
    .app-tab-page {
        padding: 0;
        background: #f7f7f7 !important;
        .bui-tab-header {
            border-right: 1px solid #ebebeb;
            border-bottom: none;
        }
        .skin-accordion {
            min-height: 540px !important;
        }
        .bui-tab-nav-item {
            width: 150px !important;
        }
    }
    .instance-not-found-class {
        height: 100%;
    }
    .s-detail-page-content {
        margin: 0;
        .app-tab-page {
            padding: 16px;
            border-radius: 6px;
            background: #fff;
            .bui-tab {
                border-radius: 6px;
                border: none;
            }
        }
    }
    .space-header {
        height: 50px;
        display: flex;
        align-items: center;
        .status {
            margin-left: 10px;
        }
    }
    .backbox {
        margin-right: 5px;
        font-size: 16px;
    }
    .s-detail-page-content {
        background: #f7f7f7;
        margin: 0;
    }
    .instance-info {
        display: flex;
        align-items: center;
        .page-title-nav {
            font-size: 14px;
            color: #84868c !important;
        }
        .s-icon {
            font-size: 14px;
            color: #84868c;
        }
        .instance-name {
            font-size: 16px;
            font-weight: 500;
            color: #151b26;
            padding: 0px 12px 0px 16px;
            margin: 0px;
        }
    }
    .inline_class {
        display: inline-flex;
    }
}
.l2gw-common-page {
    .l2gw-content-wrap {
        display: flex;
        .subsidebar-wrap {
            min-width: 140px;
            background: #f5f5f5;
        }
    }
    .space-header {
        display: flex;
        align-items: center;
    }
    .l2gw-instance-edit {
        position: absolute;
        padding: 10px;
        width: 300px;
        left: 30px;
        top: 20px;
        border: 1px solid #dcdcdc;
        background: #fff;
        z-index: 999;
    }
    .instance-edit-desc {
        display: flex;
        align-items: center;
        width: auto;
        .s-form-item {
            margin-right: 10px;
            margin-bottom: 0;
        }
    }
    .l2gw-common-label {
        display: block;
        color: #151b26;
        line-height: 24px;
        font-weight: 500;
        margin-bottom: 16px;
        font-size: 16px;
        zoom: 1;
    }
    .blue-icon {
        color: #2468f2;
        cursor: pointer;
    }
    .l2gw-edit-wrap {
        margin: 0 5px;
        position: relative;
        flex-shrink: 0;
    }
    .edit-detail-pop {
        position: relative;
        top: -5px;
    }
    .clip-board-main {
        position: relative;
        top: -5px;
    }
}
.l2gw-detail-wrap {
    .content-box {
        padding: 24px;
    }
    .icon-copy {
        margin-left: 10px;
        font-size: 14px;
    }
    .content-item-box {
        display: flex;
        flex-wrap: wrap;
        .content-item {
            display: flex;
            width: 33%;
            margin-bottom: 16px;
            align-items: center;
        }
        .content-item-key {
            display: inline-block;
            vertical-align: top;
            color: #5e626a;
            margin-right: 16px;
            width: 84px;
        }
        .content-item-value {
            align-items: center;
            position: relative;
            max-width: 70%;
            .text-hidden {
                display: inline-block;
                text-overflow: ellipsis;
                word-break: break-all;
                overflow: hidden;
            }
        }
    }
}
.l2gw-common-sidebar {
    height: 600px;
    .menu-item a {
        display: block;
        padding: 10px 20px;
        color: #333;
    }
    .menu-item.sidebar-current a {
        border-left: 4px solid #2468f2;
        color: #2468f2;
        background: #fff;
    }
}
