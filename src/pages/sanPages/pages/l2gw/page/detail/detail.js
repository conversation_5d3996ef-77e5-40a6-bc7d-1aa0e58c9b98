import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {Notification} from '@baidu/sui';
import {San2React} from '@baidu/bce-react-toolkit';

import {getVpcName} from '@/pages/sanPages/utils/common';
import {kXhrOptions, utcToTime} from '@/pages/sanPages/utils/helper';
import {l2gwStatus, l2gwFlavor} from '@/pages/sanPages/common/enum';
import Rule from '@/pages/sanPages/utils/rule';
import './style.less';

const {invokeComp, invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent} = decorators;
const tpl = html`
    <div>
        <div class="{{klass}}">
            <div class="l2gw-content-wrap">
                <div class="content-box">
                    <div class="content-header">
                        <h4 class="l2gw-common-label">{{'基本信息：'}}</h4>
                    </div>
                    <div class="content-item-box">
                        <div class="content-item">
                            <div class="content-item-key">ID：</div>
                            <div class="content-item-value">
                                <span class="text-hidden">{{instance.id}}</span>
                                <s-clip-board
                                    class="blue-icon"
                                    text="{{instance.id}}"
                                    successMessage="{{'已复制到剪贴板'}}"
                                >
                                    <s-icon name="copy" />
                                </s-clip-board>
                            </div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'名称：'}}</div>
                            <div class="content-item-value" s-ref="edit-name">
                                <span class="text-hidden"> {{instance.name || '-'}} </span>
                                <edit-popover value="{=instance.name=}" rule="{{Rule.NAME}}" on-edit="updateName">
                                    <a href="javascript:void(0)" style="margin-left: 8px">变更</a>
                                </edit-popover>
                            </div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'所在网络：'}}</div>
                            <div class="content-item-value">
                                <a href="#/vpc/instance/detail?vpcId={{instance.vpcUuid}}">
                                    {{instance.vpcName || '-'}}{{vpcInfo.cidr | filterCidr}}
                                </a>
                            </div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'所在子网：'}}</div>
                            <div class="content-item-value">
                                <a href="#/vpc/subnet/detail?subnetId={{instance.subnetUuid}}">
                                    {{instance.subnetName}}
                                    <span s-f="instance.subnetCidr">
                                        （{{instance.subnetCidr}}）{{instance.subnetIpv6Cidr ? ('(' +
                                        instance.subnetIpv6Cidr +')') : ''}}
                                    </span>
                                </a>
                            </div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'创建时间：'}}</div>
                            <div class="content-item-value">{{instance.createdTime | filterTime}}</div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'描述：'}}</div>
                            <div class="content-item-value" s-ref="edit-desc">
                                <span class="text-hidden"> {{instance.description || '-'}} </span>
                                <edit-popover
                                    value="{=instance.description=}"
                                    rule="{{Rule.DESC}}"
                                    on-edit="updateDesc"
                                >
                                    <a href="javascript:void(0)" style="margin-left: 8px">变更</a>
                                </edit-popover>
                            </div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'运行状态：'}}</div>
                            <div class="content-item-value {{instance.status | statusStyle}}">
                                {{instance.status | statusText}}
                            </div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'规格：'}}</div>
                            <div class="content-item-value">{{instance.flavor | getFlavor}}</div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'本端隧道IP：'}}</div>
                            <div class="content-item-value">{{instance.tunnelIp || '-'}}</div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'主接口IP：'}}</div>
                            <div class="content-item-value">{{instance.primaryIp || '-'}}</div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'备接口IP：'}}</div>
                            <div class="content-item-value">{{instance.standbyIp || '-'}}</div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'弹性网卡实例ID：'}}</div>
                            <div class="content-item-value">
                                <a href="{{instance | getEniHref}}">{{instance.eniId || '-'}}</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@invokeComp('@edit-popover')
@asComponent('@l2gw-detail')
class L2gwInstanceDetail extends Component {
    static filters = {
        statusStyle(status) {
            let config = l2gwStatus.fromValue(status);
            return config ? config.styleClass : '';
        },
        statusText(status) {
            let config = l2gwStatus.fromValue(status);
            return config ? config.text : '';
        },
        filterTime(value) {
            return value ? utcToTime(value) : '-';
        },
        filterCidr(value) {
            return value ? `(${value})` : '';
        },
        getFlavor(value) {
            return l2gwFlavor.getTextFromValue(value);
        },
        getEniHref(instance) {
            return instance && instance.eniUuid && instance.eniId
                ? `#/vpc/eni/detail?vpcId=${instance.vpcId}&eniId=${instance.eniUuid}&shortEniId=${instance.eniId}`
                : 'javascript:void(0);';
        }
    };
    initData() {
        return {
            klass: ['main-wrap l2gw-detail-wrap l2gw-common-page'],
            instance: {},
            unset: '未配置',
            Rule: Rule.DETAIL_EDIT
        };
    }

    attached() {
        this.loadPage();
    }

    loadPage() {
        this.getDetail();
        this.getVpcInfo();
    }

    updateName(value) {
        const updatePayload = {
            name: value
        };
        this.$http.l2gwUpdate(this.data.get('context').l2gwId, u.extend({}, updatePayload)).then(() => {
            Notification.success('修改成功');
            this.data.set('instance.name', value);
            this.data.get('context').updateName();
        });
    }

    updateDesc(value) {
        const updatePayload = {
            description: value
        };
        this.$http.l2gwUpdate(this.data.get('context').l2gwId, u.extend({}, updatePayload)).then(() => {
            Notification.success('修改成功');
            this.data.set('instance.description', value);
        });
    }

    getDetail() {
        return this.$http
            .l2gwDetail(this.data.get('context').l2gwId, {'x-silent-codes': ['NoSuchObject']})
            .then(instance => {
                this.data.set('instance', instance);
            });
    }
    getVpcInfo() {
        let vpcId = this.data.get('context').vpcId || '';
        this.$http.getVpcDetail({vpcIds: [vpcId]}).then(data => {
            let vpc = data[vpcId] || {};
            vpc.name = getVpcName(vpc.name);
            this.data.set('vpcInfo', vpc);
        });
    }
}

export default San2React(Processor.autowireUnCheckCmpt(L2gwInstanceDetail));
