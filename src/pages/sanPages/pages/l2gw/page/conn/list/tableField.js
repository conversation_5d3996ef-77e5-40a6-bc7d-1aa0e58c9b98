import {l2gwConnStatus} from '@/pages/sanPages/common/enum';
export const columns = [
    {
        name: 'id',
        label: '隧道名称/ID',
        width: 170,
        fixed: 'left'
    },
    {
        name: 'status',
        label: '状态',
        width: 120,
        // filter: {
        //   options: [
        //       {
        //           text: '全部',
        //           value: ''
        //       },
        //       ...l2gwConnStatus.toArray()
        //   ],
        //   value: ''
        // }
    },
    {
        name: 'subnetCidr',
        label: '隧道子网',
        minWidth: 140
    },
    {
        name: 'eip',
        label: '接口IP',
        minWidth: 140
    },

    {
        name: 'serialNumber',
        label: 'VXLAN隧道号',
        width: 140
    },
    // {
    //   name: 'localPeerIp',
    //   label: '本端隧道IP地址',
    //   width: 150
    // },
    {
        name: 'remoteIp',
        label: '对端隧道IP地址',
        width: 150
    },
    {
      name: 'description',
      label: '描述',
      width: 80
    },
    {
        name: 'opt',
        label: '操作',
        minWidth: 140,
        fixed: 'right'
    }
];
