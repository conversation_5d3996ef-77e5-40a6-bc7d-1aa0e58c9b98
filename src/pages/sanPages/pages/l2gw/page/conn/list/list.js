import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {checker} from '@baiducloud/bce-opt-checker';
import _ from 'lodash';
import {utcToTime} from '@/pages/sanPages/utils/helper';
import {OutlinedRefresh, OutlinedSetting, OutlinedPlus, OutlinedEditingSquare} from '@baidu/sui-icon';
import {Notification} from '@baidu/sui';

import {columns} from './tableField';
import Confirm from '@/pages/sanPages/components/confirm';
import {l2gwConnStatus, ipsecAllSaActive, SslConnStatus, GreHealthStatus} from '@/pages/sanPages/common/enum';
import Monitor from '../monitorChart/monitor';
import rules from '../../../rules';
import './style.less';

const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent} = decorators;

const l2gwColumn = ['id', 'status', 'subnetCidr', 'eip', 'serialNumber', 'remoteIp', 'description', 'opt'];

const tpl = html`
    <div>
        <s-app-list-page class="{{klass}}">
            <div slot="bulk">
                <s-tip-button
                    disabled="{{addL2gwConn.disable || instance.status !== 'active' || table.loading}}"
                    skin="primary"
                    isDisabledVisibile="{{true}}"
                    on-click="onCreate"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{addL2gwConn.message || '当前二层网关状态不支持创建' | raw}}
                    </div>
                    <outlined-plus />
                    {{'创建二层连接隧道'}}
                </s-tip-button>
                <s-tip-button
                    class="left_class"
                    disabled="{{deleteL2gwConn.disable}}"
                    isDisabledVisibile="{{true}}"
                    on-click="deleteL2gwConn"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{deleteL2gwConn.message | raw}}
                    </div>
                    释放
                </s-tip-button>
            </div>
            <div slot="filter">
                <s-button class="s-icon-button" on-click="refresh" track-name="刷新"
                    ><outlined-refresh class="icon-class"
                /></s-button>
                <s-table-column-toggle
                    class="custom-column-qos s-button icon-column left_class"
                    datasource="{{customColumn.datasource}}"
                    value="{=customColumn.value=}"
                    on-change="onCustomColumns"
                ></s-table-column-toggle>
            </div>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                class="vpn-conn-list-table"
                datasource="{{table.datasource}}"
                on-selected-change="tableSelected($event)"
                on-filter="onFilter"
                on-sort="onSort"
                selection="{=table.selection=}"
                track-id="ti_vpc_instance_table"
                track-name="列表操作"
            >
                <div slot="empty">
                    <s-empty on-click="onCreate"> </s-empty>
                </div>
                <div slot="h-status">
                    <span>隧道状态</span>
                    <s-tip class="inline-tip" content="VTEP状态" skin="question" />
                </div>
                <div slot="c-id">
                    <div s-if="{{vpnType === 'ipsec' || vpnType === 'gre'}}">
                        <span class="text-hidden conn-name">{{row.vpnConnName}}</span>
                        <br />
                        <span class="text-hidden">{{row.vpnConnId}}</span>
                    </div>
                </div>
                <div slot="c-description">
                    <span class="truncated" title="{{row.description}}">{{row.description || '-'}}</span>
                    <s-popover
                        s-ref="popover-description-{{rowIndex}}"
                        placement="top"
                        trigger="click"
                        class="edit-popover-class"
                    >
                        <div class="edit-wrap" slot="content">
                            <s-input
                                value="{=edit.description.value=}"
                                width="160"
                                on-input="onEditInput($event, rowIndex, 'description')"
                            />
                            <div class="edit-tip">描述不能超过200个字符</div>
                            <s-button
                                skin="primary"
                                s-ref="editBtn-description-{{rowIndex}}"
                                disabled="{{true}}"
                                on-click="editConfirm(row, rowIndex, 'description')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel(rowIndex, 'description')">取消</s-button>
                        </div>
                        <outlined-editing-square
                            class="name-icon"
                            on-click="onInstantEdit(row, rowIndex, 'description')"
                        />
                    </s-popover>
                </div>
                <div slot="c-status">
                    <span class="{{row.healthStatus | statusClass}}">{{row.healthStatus | statusText}}</span>
                </div>
                <div slot="c-id">
                    <span class="truncated">
                        <s-tooltip content="{{row.name}}">
                            <a href="#/vpc/l2gw/tunnel/detail?l2gwId={{l2gwId}}&tunnelId={{row.id}}"> {{row.name}} </a>
                        </s-tooltip>
                    </span>
                    <s-popover
                        s-ref="popover-name-{{rowIndex}}"
                        placement="top"
                        trigger="click"
                        class="edit-popover-class"
                    >
                        <div class="edit-wrap" slot="content">
                            <s-input
                                value="{=edit.name.value=}"
                                width="320"
                                placeholder="请输入名称"
                                on-input="onEditInput($event, rowIndex, 'name')"
                            />
                            <div class="edit-tip">大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65</div>
                            <s-button
                                skin="primary"
                                s-ref="editBtn-name-{{rowIndex}}"
                                disabled="{{true}}"
                                on-click="editConfirm(row, rowIndex, 'name')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel(rowIndex, 'name')">取消</s-button>
                        </div>
                        <outlined-editing-square class="name-icon" on-click="onInstantEdit(row, rowIndex, 'name')" />
                    </s-popover>
                    <br />
                    <span class="truncated"> {{row.id}} </span>
                    <s-clip-board class="name-icon" text="{{row.id}}" />
                </div>
                <div slot="c-createTime">{{row | getTime}}</div>
                <div slot="c-eip">{{row.localPeerIp || '-'}}</div>
                <div slot="c-subnetCidr">
                    <span class="truncated">
                        <a href="#/vpc/subnet/ip?subnetId={{row.subnetUuid}}"> {{row.subnetCidr}} </a>
                    </span>
                </div>
                <div slot="c-opt" class="operations">
                    <!--<s-button skin="stringfy" on-click="editConn(row)"
                    >修改配置</s-button>-->
                    <s-button skin="stringfy" on-click="showMonitor(row)">监控</s-button>
                    <!--<s-button skin="stringfy" on-click="toAlarmDetail(row)">报警详情</s-button>-->
                    <s-button skin="stringfy" on-click="deleteTunnle(row)">释放</s-button>
                    <s-button skin="stringfy" on-click="updateInternalIp(row)">变更主机内网IP</s-button>
                </div>
            </s-table>
            <s-pagination
                s-if="{{pager.total}}"
                slot="pager"
                layout="{{'total, pageSize, pager, go'}}"
                pageSize="{{pager.pageSize}}"
                total="{{pager.total}}"
                page="{{pager.page}}"
                on-pagerChange="onPagerChange"
                on-pagerSizeChange="onPagerSizeChange"
            />
        </s-app-list-page>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@asComponent('@l2gw-conn-list')
class L2gwConnList extends Component {
    static components = {
        'outlined-refresh': OutlinedRefresh,
        'outlined-plus': OutlinedPlus,
        'outlined-setting': OutlinedSetting,
        'outlined-editing-square': OutlinedEditingSquare
    };
    initData() {
        const allColumns = columns.slice();
        const customColumnDb = allColumns.map(item => ({
            text: item.label,
            value: item.name,
            disabled: item.name === 'id' || item.name === 'opt'
        }));
        return {
            klass: 'l2gw-conn-wrap',
            editTag: {},
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                allColumns,
                datasource: [],
                selectedItems: []
            },
            customColumn: {
                value: ['id', 'status', 'subnetCidr', 'eip', 'serialNumber', 'remoteIp', 'description', 'opt'],
                datasource: customColumnDb
            },
            sslUserCount: 0,
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            }
        };
    }

    static filters = {
        statusClass(value) {
            return l2gwConnStatus.fromValue(value).styleClass || '';
        },
        statusText(value) {
            return value ? l2gwConnStatus.getTextFromValue(value) : '-';
        },
        allSaClass(value) {
            return ipsecAllSaActive.fromValue(value).styleClass || '';
        },
        allSaText(value) {
            return ipsecAllSaActive.getTextFromValue(value);
        },
        getTime(item) {
            return item.createdTime ? utcToTime(item.createdTime) : '-';
        }
    };

    inited() {
        let {deleteL2gwConn} = checker.check(rules, [], '');
        this.data.set('deleteL2gwConn', deleteL2gwConn);
        this.setTableColumns();
        this.loadPage('beforeLoad');
    }

    loadPage(type) {
        this.data.set('table.loading', true);
        const {pager, filter} = this.data.get('');
        let payload = {
            l2GatewayId: this.data.get('l2gwId'),
            pageNo: pager.page,
            pageSize: pager.pageSize
        };
        this.resetTable();
        // this.checkConnQuota();
        this.$http
            .l2gwTunnelList(_.extend({}, payload, filter))
            .then(res => {
                this.data.set('table.datasource', res.result);
                this.data.set('pager.total', res.totalCount);
                if (
                    (this.data.get('instance').flavor === 'normal' && res.totalCount === 1) ||
                    (this.data.get('instance').flavor === 'high_performance' && res.totalCount === 6)
                ) {
                    this.data.set('addL2gwConn', {
                        disable: true,
                        message: '当前二层连接隧道数量已达到上限'
                    });
                } else {
                    this.data.set('addL2gwConn', {
                        disable: false,
                        message: ''
                    });
                }
                this.data.set('table.loading', false);
            })
            .catch(e => {
                this.data.set('table.loading', false);
            });
    }

    checkConnQuota() {
        this.$http
            .l2gwTunnelQuota({
                id: this.data.get('l2gwId')
            })
            .then(data => {
                let status = this.data.get('status');
                let {addL2gwConn} = checker.check(rules, {status}, 'addL2gwConn', {free: data.free});
                this.data.set('addL2gwConn', addL2gwConn);
            });
    }

    resetTable() {
        this.data.set('table.selection', {
            mode: 'multi',
            selectedIndex: []
        });
        this.data.set('table.selectedItems', []);
    }

    refresh() {
        this.loadPage();
    }

    // 设置表格列
    setTableColumns(customColumnNames) {
        customColumnNames = customColumnNames ? customColumnNames : this.data.get('customColumn.value');
        let allColumns = this.data.get('table.allColumns');
        let customColumn = this.data.get('customColumn');
        allColumns = _.filter(allColumns, item => l2gwColumn.indexOf(item.name) > -1);
        this.data.set(
            'customColumn.datasource',
            _.filter(customColumn.datasource, item => l2gwColumn.indexOf(item.value) > -1)
        );
        this.data.set(
            'customColumn.value',
            _.filter(customColumn.value, item => l2gwColumn.indexOf(item) > -1)
        );
        let columns = [];
        allColumns.forEach(item => customColumnNames.indexOf(item.name) > -1 && columns.push(item));
        this.data.set('table.columns', columns);
    }

    // 自定义表格列
    onCustomColumns({value}) {
        this.data.set('customColumn.value', value);
        this.setTableColumns(value);
    }

    tableSelected(e) {
        this.data.set('table.selectedItems', e.value.selectedItems);
        let {deleteL2gwConn} = checker.check(rules, e.value.selectedItems, '');
        let userCount = this.data.get('sslUserCount');
        let {deleteSslVpnConn} = checker.check(rules, e.value.selectedItems, '', {userCount: userCount});
        this.data.set('deleteL2gwConn', deleteL2gwConn);
        this.data.set('deleteSslVpnConn', deleteSslVpnConn);
    }

    // 排序
    onSort(e) {
        this.data.set('sort', e.value);
        this.loadPage();
    }

    // 筛选
    onFilter(e) {
        let name = e.field.name;
        let value = e[name] ? e[name] : '';
        this.data.set('filter.' + 'healthStatus', value);
        this.loadPage();
    }

    deleteL2gwConn() {
        let selectedItems = this.data.get('table.selectedItems');
        let confirm = new Confirm({
            data: {
                title: '确认',
                content: `二层连接隧道释放将影响后续IP迁移上云，请谨慎操作。`
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http
                .l2gwTunnelRelease({
                    l2gwTunnelId: selectedItems.map(item => item.id)[0]
                })
                .then(() => this.loadPage());
        });
    }
    deleteTunnle(row) {
        let confirm = new Confirm({
            data: {
                title: '确认',
                content: `二层连接隧道释放将影响后续IP迁移上云，请谨慎操作。`
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http
                .l2gwTunnelRelease({
                    l2gwTunnelId: row.id
                })
                .then(() => this.loadPage());
        });
    }
    updateInternalIp(row) {
        let url = `#/vpc/l2gw/tunnel/ip?l2gwId=${this.data.get('instance').id}&tunnelId=${row.id}`;
        location.hash = url;
    }

    onCreate() {
        let vpcId = this.data.get('vpcId');
        let url = `#/vpc/l2gw/conn/create?l2gwId=${this.data.get('instance').id}&vpcId=${vpcId}`;
        location.hash = url;
    }

    editConn(item) {
        let vpnId = this.data.get('vpnId');
        let vpcId = this.data.get('vpcId');
        location.hash = `#/vpc/vpn/conn/create?vpnId=${vpnId}&vpcId=${vpcId}&vpnConnId=${item.vpnConnId}`;
    }

    toAlarmDetail(item) {
        const region = window.$context.getCurrentRegionId();
    }

    showMonitor(item) {
        const dialog = new Monitor({
            data: {
                formData: item
            }
        });
        dialog.attach(document.body);
    }

    onRegionChange() {
        location.reload();
    }
    onPagerChange(e) {
        this.resetTable();
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }

    onPagerSizeChange(e) {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', e.value.pageSize);
        this.loadPage();
    }
    // 编辑弹框-输入名称/描述
    onEditInput(e, rowIndex, type) {
        let result =
            type === 'name'
                ? e.value === '' || !/^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/.test(e.value)
                : e.value.length > 200;
        this.data.set(`edit.${type}.error`, result);
        this.data.set(`edit.${type}.value`, e.value);
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', result);
    }

    editConfirm(row, rowIndex, type) {
        let payload = {};
        let edit = this.data.get(`edit.${type}`);
        if (edit.error) {
            return;
        }
        payload[type] = edit.value;
        this.$http.l2gwTunnelUpdate(row.id, payload).then(() => {
            this.editCancel(rowIndex, type);
            Notification.success('修改成功');
            this.loadPage();
        });
    }

    editCancel(rowIndex, type) {
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', true);
        this.ref(`popover-${type}-${rowIndex}`).data.set('visible', false);
    }
    // 点击修改icon
    onInstantEdit(row, rowIndex, type) {
        this.data.set(`edit.${type}.value`, row[`${type}`]);
        this.data.set(`edit.${type}.error`, false);
        const editWrap = this.ref(`popover-${type}-${rowIndex}`);
        editWrap.data.set('visible', !editWrap.data.get('visible'));
    }
}
export default Processor.autowireUnCheckCmpt(L2gwConnList);
