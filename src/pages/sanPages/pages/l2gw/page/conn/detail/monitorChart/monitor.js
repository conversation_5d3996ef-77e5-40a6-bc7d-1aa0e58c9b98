import {Component} from 'san';
import u from 'lodash';
import {OutlinedRefresh} from '@baidu/sui-icon';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import moment from 'moment';
import {BcmSDK} from '@baidu/new-bcm-sdk';
import {San2React} from '@baidu/bce-react-toolkit';
import {BcmChartPanel} from '@baidu/new-bcm-sdk-san';

import monitorConfig from '@/pages/sanPages/utils/monitorConfig';
import './style.less';

const {l2gwMetric, shortcutItems} = monitorConfig;
const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent} = decorators;

const tpl = html`
    <div>
        <div class="{{klass}}">
            <div class="vpn-content-wrap">
                <div class="monitor-wrap">
                    <div class="monitor-item-box">
                        <h4>监控信息</h4>
                        <div class="button-wrap">
                            <span class="alarm-state">
                                <span>时间：</span>
                                <s-date-picker-date-range-picker
                                    s-ref="timeRange"
                                    value="{=timeRange=}"
                                    width="{{310}}"
                                    mode="second"
                                    range="{{range}}"
                                    on-change="onTimeChange"
                                    shortcut="{{shortcutItems}}"
                                />
                            </span>
                            <s-button class="s-icon-button" on-click="onTimeRefresh">
                                <outlined-refresh class="icon-class" />
                            </s-button>
                            <s-button class="alarm-detail" on-click="alarmDetail"> 报警详情 </s-button>
                        </div>
                        <div class="eni-monitor-trends">
                            <div class="monitor-trend-box" s-for="item,index in chart">
                                <bcm-chart-panel
                                    s-ref="alarm-chart-{{index}}"
                                    withFilter="{{false}}"
                                    scope="{{item.scope}}"
                                    dimensions="{{item.dimensions}}"
                                    statistics="{{item.statistics}}"
                                    title="{{item.title}}"
                                    options="{{options}}"
                                    api-type="metricName"
                                    startTime="{=startTime=}"
                                    endTime="{=endTime=}"
                                    period="{{monitorDefaultPeriod}}"
                                    metrics="{{item.metrics}}"
                                    unit="{{item.unit}}"
                                    bitUnit="{{item.bitUnit}}"
                                    width="{{'auto'}}"
                                    height="{{230}}"
                                    sdk="{{bcmSdk}}"
                                >
                                </bcm-chart-panel>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
`;
@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@asComponent('@l2gw-tunnel-monitor')
class L2gwTunnelMonitor extends Component {
    static components = {
        'bcm-chart-panel': BcmChartPanel,
        'outlined-refresh': OutlinedRefresh
    };
    initData() {
        return {
            klass: ['main-wrap vpn-monitor-wrap vpn-common-page'],
            options: {
                color: ['#2468f2', '#5FB333'],
                legend: {
                    x: 'right',
                    y: 'top'
                },
                dataZoom: {start: 0}
            },
            instance: {},
            chart: [],
            timeRange: {
                begin: new Date(moment().subtract(1, 'hour').valueOf()),
                end: new Date(moment().valueOf())
            },
            range: {
                begin: new Date(moment().subtract(40, 'day').valueOf()),
                end: new Date(moment().valueOf())
            },
            startTime: moment().subtract(1, 'hour').utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
            endTime: moment().utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
            endOriginTime: moment().valueOf(),
            bcmSdk: new BcmSDK({client: window.$http, context: window.$context}),
            shortcutItems
        };
    }
    attached() {
        this.initMonitor();
        this.watch('timeRange', timeRange => {
            this.onTimeChange({value: timeRange});
        });
    }

    onTimeChange({value}) {
        let startTime = moment(value.begin).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        let endTime = moment(value.end).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        let ms = moment(value.end) - moment(value.begin);
        let hourTime = Math.round(ms / 86400000);
        switch (true) {
            case hourTime <= 1:
                this.data.set('monitorDefaultPeriod', 60);
                break;
            case hourTime <= 3:
                this.data.set('monitorDefaultPeriod', 300);
                break;
            case hourTime <= 7:
                this.data.set('monitorDefaultPeriod', 600);
                break;
            case hourTime <= 14:
                this.data.set('monitorDefaultPeriod', 1800);
                break;
            case hourTime <= 40:
                this.data.set('monitorDefaultPeriod', 3600);
                break;
            default:
                break;
        }
        this.data.set('startTime', startTime);
        this.data.set('endTime', endTime);
        this.onRefresh();
    }

    onRefresh() {
        let chartConfig = this.data.get('chart');
        u.map(chartConfig, (item, i) => {
            this.ref(`alarm-chart-${i}`).loadMetrics();
        });
    }
    onTimeRefresh() {
        if (this.data.get('timeRange.end').valueOf() >= this.data.get('endOriginTime')) {
            this.data.set('timeRange.end', new Date(moment().valueOf()));
        } else {
            this.onRefresh();
        }
    }
    alarmDetail() {
        const region = window.$context.getCurrentRegionId();
        let dimensions = `CecId:${this.data.get('context').l2gwId};VxlanTunnelId:${this.data.get('context').tunnelId}`;
        redirect(`/bcm/#/bcm/alarm/rule/list~scope=BEC_LTGW&dimensions=${dimensions}&region=${region}`);
    }

    initMonitor() {
        let chartConfig = [];
        u.each(l2gwMetric, item => {
            let config = {
                scope: 'BEC_LTGW',
                period: 60,
                statistics: item.statistics || 'average',
                title: item.title,
                unit: item.unit,
                bitUnit: item.bitUnit,
                metrics: item.metrics,
                dimensions: `CecId:${this.data.get('context').l2gwId};VxlanTunnelId:${this.data.get('context').tunnelId}`
            };
            chartConfig.push(config);
        });
        this.data.set('chart', chartConfig);
    }
    onRegionChange() {
        location.hash = '#/vpc/l2gw/list';
    }
}

export default San2React(Processor.autowireUnCheckCmpt(L2gwTunnelMonitor));
