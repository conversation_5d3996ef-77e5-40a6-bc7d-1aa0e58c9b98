import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {OutlinedPlus} from '@baidu/sui-icon';

import {tableColumns} from './tableFields';

const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent} = decorators;

const tpl = html`
<div>
    <div class="{{klass}}">
        <div class="vpn-content-wrap">
            <s-biz-page>
                <s-table
                    s-ref="table"
                    columns="{{table.columns}}"
                    loading="{{table.loading}}"
                    error="{{table.error}}"
                    datasource="{{table.datasource}}">
                    <div slot="error">
                        啊呀，出错了?
                        <a href="javascript:;" on-click="refresh">重新加载</a>
                    </div>
                    <div slot="empty">
                        <s-empty>
                            <div slot="action">
                            </div>
                        </s-empty>
                    </div>
                </s-table>
                <div slot="footer" class="nat-list-footer">
                    <s-pagination
                        s-if="{{pager.total}}"
                        slot="footer"
                        layout="{{'total, pageSize, pager, go'}}"
                        pageSize="{{pager.size}}"
                        total="{{pager.total}}"
                        page="{{pager.page}}"
                        on-pagerChange="onPageChange"
                        on-pagerSizeChange="onPageSizeChange" />
                </div>
            </s-biz-page>
        </div>
    </div>
</template>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@asComponent('@l2gw-arp-list')
class TunnelArpList extends Component {
    static components = {
        'outlined-plus': OutlinedPlus
    };

    initData() {
        return {
            klass: ['main-wrap vpn-nat-list-wrap vpn-common-page'],
            vpcId: '',
            selectedItems: [],
            title: '',
            type: '',
            emptyText: '',
            keyword: '',
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                columns: tableColumns,
                datasource: []
            },
            pager: {
                size: 10,
                page: 1,
                total: 0
            },
            quota: {
                free: 10,
                total: 100
            }
        };
    }

    inited() {
        this.getDetail();
    }

    getDetail() {
        this.$http
            .l2gwTunnelDeatil(this.data.get('context').tunnelId, {'x-silent-codes': ['NoSuchObject']})
            .then(instance => {
                this.data.set('instance', instance);
                this.loadPage();
            });
    }

    // 改变页数
    onPageChange(e) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }

    // 改变每页显示个数
    onPageSizeChange(e) {
        this.data.set('pager.size', e.value.pageSize);
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    refresh() {
        this.resetTable();
        return this.loadPage();
    }

    // 重置列表
    resetTable() {
        this.data.set('table.selection.selectedIndex', []);
    }

    getPayload() {
        const {pager} = this.data.get('');
        let payload = {
            pageNo: pager.page,
            pageSize: pager.size
        };
        return payload;
    }

    loadPage() {
        this.data.set('table.loading', true);
        let payload = this.getPayload();
        this.resetTable();
        this.$http
            .getTunnleArpList(this.data.get('context').tunnelId, payload, {'x-silent-codes': ['NoSuchObject']})
            .then(data => {
                let array = data.result?.map(item => {
                    let res = item.mac.split(':');
                    let i = res.slice(3).join(':');
                    i = '**:**:**:' + i;
                    return {
                        ip: item.ip,
                        mac: i
                    };
                });
                this.data.set('table.datasource', array);
                this.data.set('pager.total', data.totalCount);
                this.data.set('table.loading', false);
            });
    }
}

export default San2React(Processor.autowireUnCheckCmpt(TunnelArpList));
