import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {checker} from '@baiducloud/bce-opt-checker';
import {San2React} from '@baidu/bce-react-toolkit';
import {OutlinedPlus} from '@baidu/sui-icon';

import Create from './create';
import {tableColumns} from './tableFields';
import {getVpcName} from '@/pages/sanPages/utils/helper';
import Confirm from '@/pages/sanPages/components/confirm';
import rules from '../../../../rules';
import './style.less';

const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent} = decorators;

const tpl = html`
<div>
    <div class="{{klass}}">
        <div class="vpn-content-wrap">
            <s-biz-page>
                <s-table
                    s-ref="table"
                    columns="{{table.columns}}"
                    loading="{{table.loading}}"
                    error="{{table.error}}"
                    datasource="{{table.datasource}}">
                    <div slot="error">
                        啊呀，出错了?
                        <a href="javascript:;" on-click="refresh">重新加载</a>
                    </div>
                    <div slot="empty">
                        <s-empty>
                            <div slot="action">
                            </div>
                        </s-empty>
                    </div>
                    <div slot="c-name">
                      <span>{{row.name}}</span>
                      <br/>
                      <span>{{row.instanceId}}</span>
                    </div>
                    <div slot="c-description">
                        <span class="text-hidden">{{row.description || '-'}}</span>
                    </div>
                    <div slot="c-subnetId">
                      <span class="truncated">
                          <a href="#/vpc/subnet/ip?subnetId={{row.subnetUuid}}">
                              {{row.subnetName}}
                          </a>
                      </span>
                    </div>
                    <div slot="c-opt">
                        <span class="operations">
                            <a href="javascript:void(0)" on-click="onEdit(row)">变更主机内网IP</a>
                        </span>
                    </div>
                </s-table>
                <div slot="footer" class="nat-list-footer">
                    <s-pagination
                        s-if="{{pager.total}}"
                        slot="footer"
                        layout="{{'total, pageSize, pager, go'}}"
                        pageSize="{{pager.size}}"
                        total="{{pager.total}}"
                        page="{{pager.page}}"
                        on-pagerChange="onPageChange"
                        on-pagerSizeChange="onPageSizeChange" />
                </div>
            </s-biz-page>
        </div>
    </div>
</template>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@asComponent('@l2gw-tunnel-ip-list')
class TunnelIpList extends Component {
    static components = {
        'outlined-plus': OutlinedPlus
    };
    static computed = {
        selectTip() {
            let length = this.data.get('selectedItems').length;
            let total = this.data.get('pager.total');
            return `已选中${length}条/共${total}条`;
        }
    };

    initData() {
        return {
            klass: ['main-wrap vpn-nat-list-wrap vpn-common-page'],
            vpcId: '',
            selectedItems: [],
            title: '',
            type: '',
            emptyText: '',
            keyword: '',
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                columns: tableColumns,
                datasource: []
            },
            pager: {
                size: 10,
                page: 1,
                total: 0
            },
            quota: {
                free: 10,
                total: 100
            },
            natRuleCreate: {
                disable: false
            },
            releaseNatRule: {
                disable: false
            },
            hasVpnConn: true
        };
    }

    inited() {
        this.getDetail();
    }

    getDetail() {
        this.$http
            .l2gwTunnelDeatil(this.data.get('context').tunnelId, {'x-silent-codes': ['NoSuchObject']})
            .then(instance => {
                this.data.set('instance', instance);
                this.loadPage();
            });
    }

    getVpcInfo() {
        let vpcId = this.data.get('context').vpcId || '';
        this.$http.vpcInfo({vpcIds: [vpcId]}).then(data => {
            let vpc = data[vpcId] || {};
            vpc.name = getVpcName(vpc.name);
            this.data.set('vpcInfo', vpc);
        });
    }

    // 改变页数
    onPageChange(e) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }

    // 改变每页显示个数
    onPageSizeChange(e) {
        this.data.set('pager.size', e.value.pageSize);
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    refresh() {
        this.resetTable();
        return this.loadPage();
    }

    // 重置列表
    resetTable() {
        this.data.set('table.selection.selectedIndex', []);
    }

    getPayload() {
        const {pager, instance} = this.data.get('');
        let payload = {
            pageNo: pager.page,
            pageSize: pager.size,
            serverType: 'BCC',
            keywordType: 'subnetId',
            keyword: instance?.subnetId
        };
        return payload;
    }

    loadPage() {
        this.data.set('table.loading', true);
        let payload = this.getPayload();
        this.resetTable();
        this.$http.getBccList(payload).then(data => {
            this.data.set('table.datasource', data.result);
            this.data.set('pager.total', data.totalCount);
            this.data.set('table.loading', false);
        });
    }

    checkQuota() {
        const vpnId = this.data.get('context').vpnId;
        const {type, instance} = this.data.get('');
        this.$http.getVpnRuleQuota({vpnId, type}).then(res => {
            let title = this.data.get('title');
            let hasVpnConn = this.data.get('hasVpnConn');
            const options = {quota: res, status: instance.status, hasVpnConn, title};
            let {natRuleCreate} = checker.check(rules, [], 'natRuleCreate', options);
            this.data.set('quota', res);
            this.data.set('natRuleCreate', natRuleCreate);
        });
    }

    onCreate() {
        // let create = new Create({
        //     data: {
        //         vpcId: this.data.get('context').vpcId,
        //         vpnId: this.data.get('context').vpnId,
        //         type: this.data.get('type')
        //     }
        // });
        // create.on('create', () => this.loadPage());
        // create.attach(document.body);
    }

    onEdit(row) {
        let dialog = new Create({
            data: {
                instances: [row],
                serviceName: 'BCC',
                tunnelInstance: this.data.get('instance'),
                l2GatewayTunnelId: this.data.get('instance').id
            }
        });
        dialog.on('success', () => {
            this.loadPage();
        });
        dialog.attach(document.body);
    }

    onRelease(e, row) {
        const selectItems = row ? [row] : this.ref('table').getSelectedItems();
        let ruleIdList = selectItems.map(item => item.ruleId);
        const content = selectItems.length > 1 ? `确认删除这${selectItems.length}条IP映射？` : '确认删除此IP映射?';
        const dialog = new Confirm({
            data: {
                title: '删除提示',
                content
            }
        });
        dialog.on('confirm', () => {
            const payload = {
                vpcId: this.data.get('context').vpcId,
                vpnId: this.data.get('context').vpnId,
                ruleIdList
            };
            this.$http.deleteVpnNatRule(payload).then(() => {
                this.data.set('pager.page', 1);
                this.loadPage();
            });
        });
        dialog.attach(document.body);
    }
}

export default San2React(Processor.autowireUnCheckCmpt(TunnelIpList));
