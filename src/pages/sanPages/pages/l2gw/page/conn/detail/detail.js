import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {Notification} from '@baidu/sui';
import {San2React} from '@baidu/bce-react-toolkit';

// import InternalIpBindDialog from '../../components/internalIpBind';
import {getVpcName} from '@/pages/sanPages/utils/common';
import {utcToTime} from '@/pages/sanPages/utils/helper';
import {l2gwConnStatus, l2gwFlavor} from '@/pages/sanPages/common/enum';
import Rule from '@/pages/sanPages/utils/rule';
import './style.less';

const {invokeComp, invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent} = decorators;
const tpl = html`
    <div>
        <div class="{{klass}}">
            <div class="l2gw-content-wrap">
                <div class="content-box">
                    <div class="content-header">
                        <h4 class="l2gw-common-label">{{'基本信息：'}}</h4>
                    </div>
                    <div class="content-item-box">
                        <div class="content-item">
                            <div class="content-item-key">ID：</div>
                            <div class="content-item-value">
                                <span class="text-hidden">{{instance.id}}</span>
                                <s-clip-board class="blue-icon" text="{{instance.id}}" successMessage="{{'已复制到剪贴板'}}">
                                    <s-icon name="copy" />
                                </s-clip-board>
                            </div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'隧道名称：'}}</div>
                            <div class="content-item-value" s-ref="edit-name">
                                <span class="text-hidden"> {{instance.name || '-'}} </span>
                                <edit-popover value="{=instance.name=}" rule="{{Rule.NAME}}" on-edit="updateName">
                                    <a href="javascript:void(0)" style="margin-left: 8px">变更</a>
                                </edit-popover>
                            </div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'隧道子网：'}}</div>
                            <div class="content-item-value">{{instance.subnetCidr || '-'}}</div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'运行状态：'}}</div>
                            <div class="content-item-value {{instance.healthStatus | statusStyle}}">{{instance.healthStatus | statusText}}</div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'接口IP：'}}</div>
                            <div class="content-item-value">{{instance.localPeerIp || '-'}}</div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'VXLAN隧道号：'}}</div>
                            <div class="content-item-value">{{instance.serialNumber || '-'}}</div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'对端隧道IP地址：'}}</div>
                            <div class="content-item-value">{{instance.remoteIp || '-'}}</div>
                        </div>
                        <div class="content-item">
                            <div class="content-item-key">{{'描述：'}}</div>
                            <div class="content-item-value" s-ref="edit-desc">
                                <span class="text-hidden"> {{instance.description || '-'}} </span>
                                <edit-popover value="{=instance.description=}" rule="{{Rule.DESC}}" on-edit="updateDesc">
                                    <a href="javascript:void(0)" style="margin-left: 8px">变更</a>
                                </edit-popover>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@invokeComp('@edit-popover')
@asComponent('@l2gw-tunnel-detail')
class L2gwTunnelDetail extends Component {
    static filters = {
        statusStyle(status) {
            let config = l2gwConnStatus.fromValue(status);
            return config ? config.styleClass : '';
        },
        statusText(status) {
            let config = l2gwConnStatus.fromValue(status);
            return config ? config.text : '';
        },
        filterTime(value) {
            return value ? utcToTime(value) : '-';
        },
        filterCidr(value) {
            return value ? `(${value})` : '';
        },
        getFlavor(value) {
            return l2gwFlavor.getTextFromValue(value);
        }
    };
    initData() {
        return {
            klass: ['main-wrap l2gw-tunnel-detail-wrap l2gw-common-page'],
            instance: {},
            unset: '未配置',
            Rule: Rule.DETAIL_EDIT
        };
    }

    attached() {
        this.loadPage();
    }

    loadPage() {
        this.getDetail();
    }

    updateName(value) {
        const updatePayload = {
            name: value
        };
        this.$http.l2gwTunnelUpdate(this.data.get('context').tunnelId, u.extend({}, updatePayload)).then(() => {
            Notification.success('修改成功');
            this.data.set('instance.name', value);
            this.data.get('context').updateName();
        });
    }

    updateDesc(value) {
        const updatePayload = {
            description: value
        };
        this.$http.l2gwTunnelUpdate(this.data.get('context').tunnelId, u.extend({}, updatePayload)).then(() => {
            Notification.success('修改成功');
            this.data.set('instance.description', value);
        });
    }

    getDetail() {
        return this.$http.l2gwTunnelDeatil(this.data.get('context').tunnelId, {'x-silent-codes': ['NoSuchObject']}).then(instance => {
            this.data.set('instance', instance);
        });
    }
    getVpcInfo() {
        let vpcId = this.data.get('context').vpcId || '';
        this.$http.getVpcDetail({vpcIds: [vpcId]}).then(data => {
            let vpc = data[vpcId] || {};
            vpc.name = getVpcName(vpc.name);
            this.data.set('vpcInfo', vpc);
        });
    }
}

export default San2React(Processor.autowireUnCheckCmpt(L2gwTunnelDetail));
