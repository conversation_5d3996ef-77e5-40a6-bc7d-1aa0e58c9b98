import zone from '@/pages/sanPages/utils/zone';
export const tableColumns = [
    {
        name: 'name', label: '实例名称',
    },
    {
        name: 'vpcId', label: '所在VPC',
        render(item) {
            return item.vpcName + '（' + item.vpcCidr + '）';
        }
    },
    {
      name: 'logicalZone', label: '所在可用区',
      render(item) {
          return zone.getLabel(item.logicalZone);
      }
    },
    {
        name: 'subnetId', label: '所在子网',
        render(item) {
            return item.subnetName;
        }
    },
    {
        name: 'internalIp', label: '内网IP',
        render(item) {
            return item.internalIp || '-';
        }
    },
    {
        name: 'opt', label: '操作',
    }
];
