/**
 * @file ip-change-dialog.js 变更内网ip
 *
 * <AUTHOR>
 * @created: 2020/04/21
 */

import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {
    Button,
    Tip,
    Table,
    Dialog,
    notification,
    TextBox,
    CheckBox,
    ToastLabel
} from '@baiducloud/bce-ui/san';

import './style.less';

const Tips = {
    BCC: '自动重启服务器可自动令IP更改生效，重启预计需要几分钟。',
    DCC: '自动重启服务器可自动令IP更改生效，重启预计需要几分钟。',
    BBC: '自动重启服务器可自动令IP更改生效，重启预计需要几分钟。当前不支持客户自行修改IP。'
};

const Pattern = /^\s*([1-9]?\d|1\d?\d?|2[0-4]\d|25[0-5])(\.([1-9]?\d|1\d?\d?|2[0-4]\d|25[0-5])){3}$/;

const ErrorText = '请确保填写的IP地址属于所在子网的IP段地址范围内且不能和当前内网IP重复';

const convertIpToBinary = part => {
    part = +part;
    let binary = part.toString(2);
    let pre = '';

    for (let i = 0; i < 8 - binary.length; i++) {
        pre += '0';
    }

    return pre + binary;
};

/* eslint-disable */
const template = html`
<template>
<ui-dialog open="{{open}}" on-close="onCancel" title="{{title}}" width="750" class="bcc-instance-batch-ipchange">
    <xui-toastlabel level="warning" text="{{'温馨提示：变更内网IP,服务器会关机重启，请保存当前设备配置。'}}"  />
    <p>变更以下实例的内网IP地址</p>
    <ui-table
        class="table"
        schema="{{table.schema}}"
        selected-index="{=table.selectedIndex=}"
        loading="{{table.loading}}"
        on-selected-change="onTableRowSelected($event)"
        datasource="{{table.datasource}}">
        <div slot="c-privateIp">
            <ui-textbox placeholder="请输入更改后的内网IP" width="140" class="{{errors[rowIndex] || errorClass[rowIndex] ? 'error' : ''}}" value="{=formData[rowIndex]=}" on-input="onInput($event, rowIndex)"/>
            <p class="error-text">{{errors[rowIndex]}}</p>
        </div>
    </ui-table>
    <div>
        <ui-checkbox
            disabled
            checked="{=boot=}"
            title="自动重启该服务器"
        />
        <ui-tip message="{{tip}}" layer-width="{{200}}"/>
    </div>
    <div slot="foot">
        <span class="error-text foot-tip">{{errorTip}}</span>
        <ui-button skin="primary"
            disabled="{{confirmDisable}}"
            label="确定"
            on-click="onSubmit"
        />
        <ui-button label="取消" on-click="onCancel"/>
    </div>
</ui-dialog>
</template>
`;
/* eslint-enable */
export default class IpChangeDialog extends Component {
    static template = template

    static components = {
        'ui-table': Table,
        'ui-button': Button,
        'ui-dialog': Dialog,
        'ui-textbox': TextBox,
        'ui-checkbox': CheckBox,
        'ui-tip': Tip,
        'xui-toastlabel': ToastLabel
    }

    initData() {
        return {
            title: '变更内网IP地址',
            open: true,
            boot: true,
            formData: [],
            errors: [],
            errorClass: [],
            table: {
                selectedIndex: [],
                datasource: [],
                loading: true,
                schema: [
                    {name: 'name', label: '实例名称'},
                    {name: 'vpcCidr', label: '所在VPC'},
                    {name: 'subnetCidr', label: '所在子网'},
                    {name: 'internalIp', label: '当前内网IP'},
                    {name: 'privateIp', label: '更改后内网IP', width: 160},
                ]
            },
            errorTip: '',
            submiting: false,
            submitRequester: null
        };
    }

    static computed = {
        confirmDisable() {
            const formData = this.data.get('formData');
            const emptyPrivateIp = _.some(formData, privateIp => _.trim(privateIp) === '');
            const submiting = this.data.get('submiting');
            return emptyPrivateIp || submiting;
        }
    }

    inited() {
        const {serviceName, instances} = this.data.get();
        this.$http.getBccInfos({bccIds: [instances[0].id]}).then(vpcInfos => {
          let datasource = _.clone(instances);
          _.each(datasource, item => {
              const {vpc, subnet} = vpcInfos[item.id];
              item.vpcCidr = vpc.cidr;
              item.subnetCidr = subnet.cidr;
              item.vpcId = vpc.vpcId;
              item.subnetId = subnet.subnetUuid;
              item.privateIp = item.internalIp ? _.trim(item.internalIp.split('[')[0]) : '';
          });

          this.data.set('table.loading', false);
          this.data.set('tip', Tips[serviceName]);
          this.data.set('table.datasource', datasource);
          this.data.set('formData', _.fill(Array(instances.length), ''));
        });
    }

    onInput({value}, rowIndex) {
        this.data.set('errorTip', '');
        this.data.set('errorClass', []);
        this.data.set(`formData[${rowIndex}]`, value);
        if (value === '') {
            this.data.set(`errors[${rowIndex}]`, '请输入内网IP地址');
        } else if (!Pattern.test(_.trim(value))) {
            this.data.set(`errors[${rowIndex}]`, 'IP地址格式错误，请重新输入');
        } else {
            this.data.set(`errors[${rowIndex}]`, '');
        }
    }

    onCancel() {
        this.data.set('open', false);
        this.fire('cancel');
    }

    async onSubmit() {
        try {
            const {submitRequester, formData, table: {datasource}, boot, errors} = this.data.get();

            if (_.some(errors, error => !!error)) {
                return;
            }

            let validIp = true;
            _.each(datasource, (instance, index) => {
                    if (!this.validIp(formData[index], instance.privateIp, instance.subnetCidr, index)) {
                        validIp = false;
                    }
                }
            );

            validIp = validIp && this.checkUniqIp(formData);

            if (!validIp) {
                return;
            }

            this.data.set('submiting', true);
            this.fire('ok');

            // 提交
            let params = {
                boot
            };
            params.updateFixIpRequests = _.map(datasource, (instance, index) => {
                return {
                    instanceId: instance.instanceId,
                    vpcId: instance.vpcId,
                    oldIpList: [
                        {
                            privateIp: instance.privateIp,
                            subnetId: instance.subnetId
                        }
                    ],
                    newIpList: [
                        {
                            privateIp: _.trim(formData[index]),
                            subnetId: instance.subnetId
                        }
                    ]
                };
            });
            let tunnleInstance = {
              l2GatewayTunnelId: this.data.get('l2GatewayTunnelId'),
              ipAddress: formData[0]
            };
            this.$http.releaseIpBccTunnel(tunnleInstance).then(res => {
                this.$http.updateInternalIpBcc(params).then(bccRes => {
                  this.data.set('open', false);
                  this.data.set('submiting', false);
                  notification.success('变更成功');
                  this.fire('success');
                }).catch(e => {
                  this.$http.deleteIpTunnel({migrateIpTaskId: res.id, l2GatewayId: res.l2GatewayId}).then(deleteRes => {
                    this.data.set('submiting', false);
                  }).catch(ed => {
                    this.data.set('submiting', false);
                  });
                });
            }).catch(e => {
              this.data.set('submiting', false);
            });
        }
        catch (e) {}
    }

    validIp(newPrivateIp, privateIp, subnetCidr, index) {
        try {
            let cidr = subnetCidr.split('/');
            let subnetMask = +cidr[1];
            let ip = cidr[0];
            let ips = [];
            let values = [];
            let ipArray = ip.split('.');
            let valueArray = newPrivateIp.split('.');

            for (let i = 0, ilen = ipArray.length; i < ilen; i += 1) {
                ips.push(convertIpToBinary(ipArray[i]));
                values.push(convertIpToBinary(valueArray[i]));
            }

            ips = ips.join('');
            values = values.join('');

            // 校验是否与当前内网IP相同，是否在IP网段范围内
            if (_.trim(newPrivateIp) === privateIp
                || ips.substring(0, subnetMask) !== values.substring(0, subnetMask)
                    || values.substring(subnetMask, values.length).indexOf('0') === -1) {
                this.data.set('errorTip', ErrorText);
                this.data.set(`errorClass[${index}]`, true);
                return false;
            }

            return true;
        }
        catch (e) {
            return false;
        }
    }

    checkUniqIp(formData) {
        const ipList = _.map(formData, privateIp => _.trim(privateIp));
        return _.every(ipList, (ip, index) => {
            const repeatIndex = ipList.indexOf(ip, index + 1);
            if (repeatIndex > -1) {
                this.data.set('errorTip', '请确保更改后的多个IP地址没有重复');
                this.data.set(`errorClass[${index}]`, true);
                this.data.set(`errorClass[${repeatIndex}]`, true);
                return false;
            }
            return true;
        });
    }
};
