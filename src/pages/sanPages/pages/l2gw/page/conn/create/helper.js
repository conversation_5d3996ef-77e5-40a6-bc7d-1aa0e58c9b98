import u from 'lodash';

import rule from '@/pages/sanPages/utils/rule';
import {convertCidrToBinary, checkIsInSubnet} from '@/pages/sanPages/utils/common';
const {VPN, IP, SEG, DOMAIN} = rule;

export const validateRules = (self) => {
    return {
        l2gwConnName: [
            {
                validator: (rule, value, callback) => {
                    value = value || self.data.get('formData.l2gwConnName');
                    if (!value) {
                        return callback('名称必填');
                    }
                    let pattern =  VPN.NAME.pattern;
                    if (!pattern.test(value)) {
                        return callback(VPN.NAME.patternErrorMessage);
                    }
                    callback();
                }
            }
        ],
        remoteIp: [
            {required: true, message: '对端隧道IP地址必填'},
            {
                validator: (rule, value, callback) => {
                    value = value || self.data.get('formData.remoteIp');
                    if (!value) {
                        return callback('对端隧道IP地址必填');
                    }
                    let pattern = IP;
                    if (!pattern.test(value)) {
                        return callback('格式不正确');
                    }
                    callback();
                }
            }
        ],
        localPeerIp: [
          {required: true, message: '接口IP必填'},
          {
              validator: (rule, value, callback) => {
                  value = value || self.data.get('formData.localPeerIp');
                  const subnetList = self.data.get('subnetDatasource');
                  const source = self.data.get('formData');
                  if (!value) {
                      return callback('接口IP必填');
                  }
                  let pattern = IP;
                  if (!pattern.test(value)) {
                      return callback('格式不正确');
                  }
                  const subnet = u.find(subnetList, item => item.value === source.subnetId);
                  if (subnet && !checkIsInSubnet(value + '/32', subnet.cidr)) {
                      return callback('IP地址不在所选子网内');
                  }
                  callback();
              }
          }
        ],
        serialNumber: [
            {
                validator: (rule, value, callback) => {
                    value = value || self.data.get('formData.serialNumber');
                    const pattern = /^[0-9]+$/;
                    if (!value) {
                        return callback('请填写VXLAN隧道号');
                    }
                    if (!pattern.test(value) || +value < 1 || +value > 16777214) {
                        return callback('VXLAN隧道号范围为1-16777214');
                    }
                    callback();
                }
            }
        ],
        subnetId: [
          {required: true, message: '请选择二层连接隧道子网'},
          {
            validator: (rule, value, callback) => {
                value = value || self.data.get('formData.subnetId');
                if (!value) {
                    return callback('请选择二层连接隧道子网');
                }
                let existTunnel = self.data.get('existSubList').find(item => {
                  return item === value;
                });
                if (existTunnel) {
                  return callback('该二层连接隧道子网已创建');
                }
                callback();
            }
          }

        ],
    };
};


const localSubnetsRule = (value, local) => {
    if (!value) {
        return `${local}必填`;
    }
    if (!SEG.test(value)) {
        return 'CIDR格式不合法';
    }

    let string = convertCidrToBinary(value);

    let mask = value.split('/')[1];
    if (u.isUndefined(value)) {
        mask = 32;
    }

    if (string.substring(+mask, string.length).indexOf('1') > -1) {
        return 'CIDR格式不合法';
    }
    return '';
};

const remoteSubnetsRule = (value, inWhiteList, vpnType, remote, existRemoteSubnets) => {
    if (!value) {
        return `${remote}必填`;
    }

    if (!SEG.test(value)) {
        return 'CIDR格式不合法';
    }

    let string = convertCidrToBinary(value);

    let mask = value.split('/')[1];
    if (u.isUndefined(mask)) {
        mask = 32;
    }

    if (string.substring(+mask, string.length).indexOf('1') > -1) {
        return 'CIDR格式不合法';
    }

    // ssl客户端网络只校验网段合法性，下面不需要校验
    if (inWhiteList || vpnType === 'ssl') {
        return '';
    }
    /* eslint-disable */
    let map = {
        '10.63.0.0/16': true,
        '10.132.0.0/16': true,
        '10.133.0.0/16': true,
        '10.135.0.0/16': true,
        '10.136.0.0/16': true,
        '10.180.0.0/16': true,
        '10.181.0.0/16': true,
        '10.190.0.0/16': true,
        '10.191.0.0/16': true
    };
    /* eslint-enable */
    for (let i = 1; i <= 20; i++) {
        map['10.' + i + '.0.0/16'] = true;
    }

    let result = true;
    /* eslint-disable */
    for (let cidr in map) {
    /* eslint-enable */
        if (checkIsInSubnet(value, cidr) || checkIsInSubnet(cidr, value)) {
            result = false;
            break;
        }
    }
    if (!result) {
        return '暂不支持配置该网段';
    }
    // 同一网关下的隧道之间的对端网络不一致
    if (existRemoteSubnets && existRemoteSubnets.indexOf(value) > -1) {
        return '您输入的对端网络与该网关下其它隧道的对端网络重复，请修改';
    }
    return '';
};

// 检查自己的网络，有无重复
const checkSubnetsRepeat = (list, item) => {
    return u.filter(list, value => value === item).length > 1;
};

// 检查与其他网络是否有无重复
const checkOtherCidrRepeat = (list, item) => {
    return u.indexOf(list, item) !== -1;
};

// 检查与本地网络有无重叠
const checkLocaleCidr = (localCidrs, value) => {
    return u.find(localCidrs, item => checkIsInSubnet(value, item)
    || checkIsInSubnet(item, value));
};

// 检查与其他网络有无重叠
const checkSelfCidr = (cidrs, value) => {
    const remoteList = u.filter(cidrs, item => item !== value);
    return u.find(remoteList, item => checkIsInSubnet(value, item)
    || checkIsInSubnet(item, value));
};

// 检查本端网络重复，重叠
export const checkLocaleAll = (localSubnets, vpnType) => {
    let local = vpnType === 'ipsec' ? '本端网络' : '服务端网络';
    let localSubnetsErr = [];
    for (let i = 0; i < localSubnets.length; i++) {
        const item = localSubnets[i];
        const ruleCheck = localSubnetsRule(item, local);
        if (checkSubnetsRepeat(localSubnets, item)) {
            localSubnetsErr.push(`${local}之间有重复`);
            break;
        }
        if (checkSelfCidr(localSubnets, item)) {
            localSubnetsErr.push(`${local}之间有重叠`);
            break;
        }
        if (ruleCheck !== '') {
            localSubnetsErr.push(ruleCheck);
            break;
        };
        localSubnetsErr.push('');
    }
    return localSubnetsErr;
};

// 检查对端网络有无重复，重叠
export const checkRemoteAll = (remoteSubnets, localSubnets, inWhiteList, vpnType, existRemoteSubnets) => {
    let local = vpnType === 'ipsec' || vpnType === 'gre' ? '本端网络' : '服务端网络';
    let remote = vpnType === 'ipsec' || vpnType === 'gre' ? '对端网络' : '客户端网络';
    let remoteSubnetsErr = [];
    for (let i = 0; i < remoteSubnets.length; i++) {
        const item = remoteSubnets[i];
        const ruleCheck = remoteSubnetsRule(item, inWhiteList, vpnType, remote, existRemoteSubnets);
        if (ruleCheck !== '') {
            remoteSubnetsErr.push(ruleCheck);
            break;
        };
        if (checkSubnetsRepeat(remoteSubnets, item)) {
            remoteSubnetsErr.push(`和其他${remote}有重复`);
            break;
        }
        if (checkOtherCidrRepeat(localSubnets, item)) {
            remoteSubnetsErr.push(`和${local}有重复`);
            break;
        }
        if (checkLocaleCidr(localSubnets, item)) {
            remoteSubnetsErr.push(`和${local}有重叠`);
            break;
        }
        if (checkSelfCidr(remoteSubnets, item)) {
            remoteSubnetsErr.push(`和其他${remote}有重叠`);
        }
        remoteSubnetsErr.push('');
    }
    return remoteSubnetsErr;
};

export const checkSubnets = (payload, inWhiteList, vpnType, existRemoteSubnets) => {
    let {remoteSubnets, localSubnets} = payload;
    let localSubnetsErr = localSubnets && checkLocaleAll(localSubnets, vpnType);
    let remoteSubnetsErr = checkRemoteAll(remoteSubnets, localSubnets, inWhiteList, vpnType, existRemoteSubnets);
    return [localSubnetsErr, remoteSubnetsErr];
};
