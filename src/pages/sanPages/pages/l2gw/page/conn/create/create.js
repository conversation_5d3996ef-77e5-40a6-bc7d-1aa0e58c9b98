import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {Notification, Radio} from '@baidu/sui';
import {OutlinedPlus} from '@baidu/sui-icon';
import rule from '@/pages/sanPages/utils/rule';
import {convertCidrToBinary} from '@/pages/sanPages/utils/common';
import {disable_vpc_10cidr as disableVpc10Cidr} from '@/pages/sanPages/common/flag';
import {validateRules} from './helper';
import {parseQuery} from '@/utils';
import rules from '@/pages/sanPages/utils/rule';
import './style.less';

const {IP, DOMAIN} = rules;
const kXhrOptions = {'X-silence': true};
const {invokeSUI, invokeSUIBIZ, invokeAppComp, template} = decorators;
const MASK_RANGE = 31;

const tpl = html`
<div>
    <s-app-create-page class="{{klass}}" backTo="{{pageTitle.backTo}}"
        backToLabel="{{pageTitle.label}}" pageTitle="{{title}}">
        <div class="content-wrap">
            <s-form s-ref="form" label-align="left"
                rules="{{rules}}" data="{=formData=}">
                <div class="content-item-box form-part-wrap">
                    <s-form-item label="隧道名称：" prop="l2gwConnName"
                        class="require-label"
                        help="大小写字母、数字以及-_/.特殊字符，必须以字母或者中文开头，长度1-65"
                    >
                        <s-input value="{=formData.l2gwConnName=}" width="{{320}}">
                    </s-form-item>
                    <s-form-item label="隧道子网：" prop="subnetId" help="仅展示隧道子网掩码大于等于23的网段。">
                      <s-select width="{{300}}"
                            disabled="{{loading}}"
                            filterable
                            value="{=formData.subnetId=}"
                            on-change="subnetChange"
                        >
                        <s-select-option
                            s-for="item in subnetDatasource"
                            value="{{item.value}}"
                            disabled="{{item.isCreateDisabled}}"
                            label="{{item.text}}"
                        >
                            <s-tooltip placement="right">
                                <div slot="content">
                                    {{item.tipShow}}
                                </div>
                                <div>{{item.text}}</div>
                            </s-tooltip>
                        </s-select-option>
                      </s-select>
                    </s-form-item>
                    <s-form-item label="接口IP：" prop="localPeerIp"

                    >
                        <s-input value="{=formData.localPeerIp=}" width="{{320}}">
                    </s-form-item>
                    <s-form-item prop="remoteIp" label="对端隧道IP地址：">
                      <s-input
                          width="{{320}}"
                          placeholder="请输入该子网内可用IP"
                          value="{=formData.remoteIp=}"/>
                    </s-form-item>
                    <s-form-item label="VXLAN隧道号：" prop="serialNumber"
                        class="require-label"
                      >
                        <template slot="label" class="label_class">
                            {{'VXLAN隧道号：'}}
                            <s-tip
                                class="inline-tip"
                                content="{{xlanPortMessage}}"
                                skin="question"
                                />
                        </template>
                        <s-input value="{=formData.serialNumber=}" width="{{320}}">
                    </s-form-item>
                    <s-form-item label="隧道端口：" prop="port">
                        <span class="localip-wrap">4789</span>
                    </s-form-item>
                    <s-form-item label="描述：" prop="description">
                        <s-input value="{=formData.description=}"
                        placeholder="描述不能超过200字符" width="300"
                        maxLength="200"
                        ></s-input>
                    </s-form-item>
                </div>
            </s-form>
        </div>
        <div slot="pageFooter" class="buybucket">
            <div class="buybucket-container">
                <s-button skin="primary"
                    size="large"
                    disabled="{{confirmed}}"
                    on-click="onCreate">
                    确认
                </s-button>
                <s-button size="large" on-click="backToList">取消</s-button>
            </div>
        </div>
    </s-app-create-page>
</div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class L2gwConnCreate extends Component {
    static components = {
        's-radio-group': Radio.RadioGroup,
        'outlined-plus': OutlinedPlus
    };
    static computed = {
        title() {
            let l2gwConnId = this.data.get('urlQuery.l2gwConnId');
            return l2gwConnId ? '编辑二层连接隧道' : '创建二层连接隧道';
        },
        pageTitle() {
            return {
                backTo: '/network/#/vpc/l2gw/list',
                label: '返回'
            };
        }
    };

    initData() {
        return {
            host: location.host,
            klass: 'l2gw-conn-create-warp',
            formData: {
                type: 'auto',
                l2gwConnName: ''
            },
            ipDatasource: [
                {
                    text: '自动分配',
                    value: 'auto'
                },
                {
                    text: '指定',
                    value: 'custom'
                }
            ],
            xlanPortMessage: '即VNI，取值范围1-16777214',
            rules: validateRules(this),
            urlQuery: parseQuery(location.hash)
        };
    }

    inited() {
        let payload = {
            l2GatewayId: this.data.get('urlQuery.l2gwId'),
            pageNo: 1,
            pageSize: 100000
        };
        this.$http.l2gwTunnelList(payload).then(res => {
            this.data.set(
                'existSubList',
                res.result.map(item => item.subnetUuid)
            );
        });
        this.getSubnetList();
    }

    initFormData() {
        this.$http
            .getL2gwConnDetail({
                vpnId: this.data.get('urlQuery.vpnId'),
                vpnConnId: this.data.get('urlQuery.vpnConnId')
            })
            .then(formData => {
                let ikeConfig = formData.ikeConfig;
                let vpnInfo = this.data.get('vpnInfo');
                this.data.set('isEdit', true);
                if (this.data.get('vpnType') === 'ipsec') {
                    ikeConfig.ikeRemoteType = ikeConfig.ikeRemoteType || 'IP_ADDR';
                    ikeConfig.ikeLocalType = ikeConfig.ikeLocalType || 'IP_ADDR';
                    // 编辑初始化不需要额外赋值
                    ikeConfig.ikeRemoteId = ikeConfig.ikeRemoteId;
                    ikeConfig.ikeLocalId = ikeConfig.ikeLocalId;
                    if (formData.ikeConfig && formData.ikeConfig.ikeLifeTime) {
                        formData.ikeConfig.ikeLifeTime = formData.ikeConfig.ikeLifeTime.replace(/s/g, '');
                    }
                    if (formData.ipsecConfig && formData.ipsecConfig.ipsecLifetime) {
                        formData.ipsecConfig.ipsecLifetime = formData.ipsecConfig.ipsecLifetime.replace(/s/g, '');
                    }
                }

                // 获取编辑回填数据
                if (this.data.get('vpnType') === 'gre') {
                    let localConnIp = formData.localConnIp.split('/')[0];
                    formData.localConnIp = localConnIp.split('.');
                    let remoteConnIpList = formData.remoteConnIp.split('/');
                    formData.remoteConnIp = remoteConnIpList[0].split('.');
                    formData.maskCode = remoteConnIpList[1];
                    formData.healthCheckInterval = formData.healthCheckInterval;
                    formData.healthCheckThreshold = formData.healthCheckThreshold;
                }

                this.data.set('isAdvanced', true);
                this.data.set('formData', formData);
                this.data.set('connInfo', u.cloneDeep(formData));
            });
    }

    calAvaliableIp(cidr) {
        this.nextTick(() => {
            if (!this.data.get('localIpEnable')) {
                return;
            }
            let mask = cidr.split('/')[1];
            const binary = convertCidrToBinary(cidr);
            // 最后8位
            const last = binary.slice(24, 32);
            let num = 32 - mask;
            // 网络位取0
            let zero = '';
            for (let i = 0; i < num; i++) {
                zero = zero + '0';
            }
            // 广播位取1
            let broad = '';
            for (let i = 0; i < num; i++) {
                broad = broad + '1';
            }
            // 网络
            let net = last.slice(0, mask - 24) + zero;
            // 广播
            let host = last.slice(0, mask - 24) + broad;
            // 转10进制，掩码为31时特殊处理
            let start, end;
            if (mask === '31') {
                start = parseInt(net, 2);
                end = parseInt(host, 2);
            } else {
                start = parseInt(net, 2) + 1;
                end = parseInt(host, 2) - 1;
            }
            let range = end ? `${start} - ${end}` : start;
            this.data.set('remoteRange', range);
        });
    }

    maskChange() {
        this.nextTick(() => {
            let mask = this.data.get('formData.maskCode');
            // 掩码31时去掉输入0的报错提示
            if (
                mask === 31 &&
                this.data.get('formData.localConnIp[3]') === '0' &&
                this.data.get('formData.remoteConnIp[3]') !== '0'
            ) {
                this.data.set(`localIpErrInfo[${3}]`, '');
            }
            if (
                mask === 31 &&
                this.data.get('formData.localConnIp[3]') !== '0' &&
                this.data.get('formData.remoteConnIp[3]') === '0'
            ) {
                this.data.set(`localIpErrInfo[${4}]`, '');
            }
            let ip = this.data.get('formData.localConnIp');
            let cidr = `${ip[0]}.${ip[1]}.${ip[2]}.${ip[3]}/${mask}`;
            this.calAvaliableIp(cidr);
        });
    }

    ipInput({value}, key) {
        const CONFIG_MAP = {
            0: rule.CHANNEL.IP_H,
            1: rule.CHANNEL.IP_M,
            2: rule.CHANNEL.IP_M,
            3: rule.CHANNEL.IP_H,
            4: rule.CHANNEL.IP_H
        };
        let mask = this.data.get('formData.maskCode');
        if (value === '') {
            this.data.set(`localIpErrInfo[${key}]`, '不能为空');
            return;
        }
        if (!CONFIG_MAP[key].custom(value, mask)) {
            this.data.set(`localIpErrInfo[${key}]`, CONFIG_MAP[key].customErrorMessage);
            return;
        }
        if (key === 3) {
            // 校验互联ip是否在范围内和是否重复
            if (value === this.data.get('formData.remoteConnIp')[3]) {
                this.data.set(`localIpErrInfo[${key}]`, CONFIG_MAP[key].repeatMessage);
                return;
            } else {
                this.data.set(`localIpErrInfo[4]`, '');
            }
            this.nextTick(() => {
                let ip = this.data.get('formData.localConnIp');
                let cidr = `${ip[0]}.${ip[1]}.${ip[2]}.${ip[key]}/${mask}`;
                this.calAvaliableIp(cidr);
            });
        } else if (key === 4) {
            // 校验互联ip是否在范围内和是否重复
            if (value === this.data.get('formData.localConnIp')[3]) {
                this.data.set(`localIpErrInfo[${key}]`, CONFIG_MAP[key].repeatMessage);
                return;
            } else {
                this.data.set(`localIpErrInfo[3]`, '');
            }
            let range = this.data.get('remoteRange');
            // 检验是否在输入范围内
            if (range) {
                const rangeArr = range.split('-');
                if (rangeArr.length > 1) {
                    let min = Number(rangeArr[0]);
                    let max = Number(rangeArr[1]);
                    if (Number(value) < min || Number(value) > max) {
                        this.data.set(`localIpErrInfo[${key}]`, CONFIG_MAP[key].rangeMessage);
                        return;
                    }
                } else {
                    let min = Number(rangeArr[0]);
                    if (Number(value) !== min) {
                        this.data.set(`localIpErrInfo[${key}]`, CONFIG_MAP[key].rangeMessage);
                        return;
                    }
                }
            }
        } else if (value.length === 3) {
            this.ref(`ipInput${key + 1}`) && this.ref(`ipInput${key + 1}`).focus();
        }
        this.data.set(`localIpErrInfo[${key}]`, '');
        key < 3 && this.setRemoteIp(key, value);
    }

    maskDatasource() {
        let masks = [];
        for (let i = 23; i < MASK_RANGE; i++) {
            masks.push({label: i + 1, value: i + 1});
        }
        return masks;
    }

    getCidrQuota() {
        const vpnId = this.data.get('urlQuery.vpnId');
        this.$http.vpnCidrQuota({vpnId}, kXhrOptions).then(res => {
            this.data.set('cidrQuota', res);
            this.checkCidrQuota();
        });
    }

    deleteSubnet(type, index) {
        this.data.splice(`formData['${type}']`, [index, 1]);
        this.checkCidrQuota();
    }

    addSubnet(type) {
        let subnetList = this.data.get('subnetList');
        let currentValue = type === 'localSubnets' ? subnetList[0].value : '';
        this.data.push(`formData['${type}']`, currentValue);
        this.checkCidrQuota();
    }

    getCidrWhiteList() {
        // 第一阶段开放fsh、fsg、hkg、su
        // 去掉白名单，改为黑名单控制
        const region = window.$context.getCurrentRegionId();
        if (!u.contains(disableVpc10Cidr, region)) {
            const whiteList = window.$storage.get('commonWhite');
            this.data.set('vpnLocalCidrWhiteList', !whiteList?.VpnLocalCidrBlackList);
        }
        this.data.set('vpnLocalCidrWhiteList', true);
        return;
    }

    checkCidrQuota() {
        let formData = this.data.get('formData');
        let cidrQuota = this.data.get('cidrQuota');
        let {localSubnets, remoteSubnets} = formData;
        // let {createLocalSubNet} = checker.check(checkRules, localSubnets, '', cidrQuota);
        // let {createRemoteSubNet} = checker.check(checkRules, remoteSubnets, '', cidrQuota);
        // this.data.set('createRemoteSubNet', createRemoteSubNet);
        // this.data.set('createLocalSubNet', createLocalSubNet);
    }

    getSubnetList() {
        this.$http.l2gwDetail(this.data.get('urlQuery.l2gwId'), kXhrOptions.customSilent).then(instance => {
            this.data.set('instance', instance);
            const payload = {
                vpcId: instance.vpcId,
                zone: instance.az
            };
            this.$http.getSubnetFromZone(payload, kXhrOptions).then(data => {
                let subnetList = [];
                data.forEach(item => {
                    if (item.cidr.split('/')[1] >= 23) {
                        subnetList.push({
                            value: item.subnetId,
                            text: item.name + '（' + item.cidr + '）',
                            tipShow:
                                instance.subnetId !== item.shortId
                                    ? item.name + '（' + item.cidr + '）'
                                    : '网关本地隧道ip,与隧道子网是同一个vpc同一个AZ的2个不同子网',
                            isCreateDisabled: instance.subnetId !== item.shortId ? false : true,
                            cidr: item.cidr
                        });
                    }
                });
                this.data.set('subnetDatasource', subnetList);
            });
        });
    }

    showConfigure() {
        let showAdvance = this.data.get('showAdvance');
        this.data.set('showAdvance', !showAdvance);
    }

    // 高级配置中本端标识和远端标识手动校验（默认不展开高级配置可以填空，展开或填写其中任意一项的情况则需要校验）
    ikeIdCheck(type) {
        return new Promise((resolve, reject) => {
            const ikeId = type === 'local' ? 'ikeLocalId' : 'ikeRemoteId';
            const ikeType = type === 'local' ? 'ikeLocalType' : 'ikeRemoteType';
            let value = value || this.data.get(`formData.ikeConfig.${ikeId}`);
            let typeVal = this.data.get(`formData.ikeConfig.${ikeType}`);
            let checkPattern = {
                IP_ADDR: IP,
                FQDN: DOMAIN
            };
            if (!value) {
                return reject('标识不能为空');
            }
            if (!checkPattern[typeVal].test(value)) {
                return reject('格式不正确');
            } else {
                resolve();
            }
        });
    }

    validataEmpty() {
        const {localConnIp, remoteConnIp} = this.data.get('formData');
        localConnIp.map((value, index) => {
            this.ipInput({value}, index);
        });
        this.ipInput({value: remoteConnIp[3]}, 4, 'remote');
    }

    async onCreate() {
        let form = this.ref('form');
        this.nextTick(async () => {
            await form.validateFields();
            let payload = this.getPayload();
            let requset = this.data.get('urlQuery.l2gwTunnelId')
                ? this.$http.l2gwTunnelUpdate.bind(this.$http)
                : this.$http.createL2gwTunnel.bind(this.$http);
            let title = this.data.get('urlQuery.l2gwTunnelId') ? '修改成功' : '创建成功';
            this.data.set('confirmed', true);
            requset(payload)
                .then(res => {
                    Notification.success(title);
                    this.backToList();
                })
                .catch(() => {
                    this.data.set('confirmed', false);
                });
        });
    }

    resetErr() {
        this.data.set('remoteSubnetsErr', []);
        this.data.set('localSubnetsErr', []);
    }

    getPayload() {
        let formData = u.cloneDeep(this.data.get('formData'));
        return {
            l2GatewayId: this.data.get('urlQuery.l2gwId'),
            name: formData.l2gwConnName,
            description: formData.description,
            subnetId: formData.subnetId,
            localPeerIp: formData.localPeerIp,
            remoteIp: formData.remoteIp,
            serialNumber: formData.serialNumber
        };
    }

    backToList() {
        location.hash = '#/vpc/l2gw/list';
    }

    onRegionChange() {
        this.backToList();
    }
}
export default San2React(Processor.autowireUnCheckCmpt(L2gwConnCreate));
