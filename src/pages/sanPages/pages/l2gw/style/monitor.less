.l2gw-monitor-box {
    height: 100%;
    padding: 0 24px;
    h4 {
        display: block;
        font-size: 16px;
        margin: 24px 0 16px;
        color: #151b26;
        line-height: 24px;
        font-weight: 500;
    }
    .button-wrap {
        display: flex;
        align-items: center;
        flex-direction: row;
        flex-wrap: wrap;
        line-height: 40px;
        & > div:nth-child(n+2) {
            margin-left: 10px;
        }
    }
    .refresh-button {
        margin-top: 3px;
    }
    .l2gw-monitor-trends {
        margin-top: 16px;
        display: flex;
        flex-wrap: wrap;
        .monitor-trend-box {
            width: 49%;
            margin-top: 10px;
            .ui-bcmchart {
                width: 100%;
            }
        }
    }
    .s-daterangepicker {
        .s-input-area {
            display: inline-flex;
        }
    }
    .options_class {
        display: flex;
        align-items: center;
    }
    .flex_class {
        .tip_class {
            margin-top: 5px;
            margin-left: 8px;
        }

    }
    .check_class {
        .s-checkbox {
            .s-checkbox-input {
                top: 3px;
            }
            .s-radio-text {
                position: relative;
                top: 2px;
            }
        }
    }
}

.monitor_bottom {
    padding-bottom: 16px;
}
