.l2gw-create-wrap {
    height: 100%;
    background-color: #f7f7f9;
    .resource-group-panel {
        padding: 0px;
        border: 0px;
        dt {
            margin-bottom: 24px;
        }
        .footer {
            display: flex;
            margin-left: 130px;
            a {
                margin-left: 8px;
            }
        }
    }
    .order-confirm-panel {
        margin-top: 20px;
    }
    .l2gw-purchase-wrap {
        .wrapper {
            display: flex;
        }
        label {
            width: 45px;
        }
        .s-radio-text {
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
    .tip-icon {
        color: #9e9898;
        border: 1px solid #9e9898;
        margin-left: 10px;
        box-sizing: content-box;
        &:hover {
            border-color: #2468f2;
        }
    }
    .row-line {
        display: flex;
        align-items: center;
    }
    .flavor-line span {
        display: flex;
        align-items: center;
        height: 30px;
    }
    .page-floating-nav {
        z-index: 99;
    }
    .s-form {
        .s-form-item-label {
            width: 126px;
            height: 30px;
        }
        .s-form-help-label {
            margin-left: 126px;
        }
        .s-form-tip-error {
            margin-left: 126px;
            color: #f33e3e !important;
            padding-bottom: 0 !important;
        }
    }
    .s-input {
        box-sizing: border-box;
    }
    .legend-wrap {
        margin: 10px 0;
    }
    .content-wrap {
        border-radius: 6px;
        margin: 12px 0;
        text-align: left;
        background-color: #f7f7f9 !important;
        box-sizing: content-box;
        input {
            box-sizing: border-box;
        }
    }
    .tip-icon-wrap {
        font-size: 14px;
        border: 1px solid #f18823;
        margin-left: 10px;
        color: #f18823;
        &:hover {
            color: #fff;
            background-color: #f18823;
        }
    }
    .s-badge-content {
        z-index: 99;
    }
    .row-line {
        display: flex;
        align-items: center;
    }
    .l2gw-renew-wrap {
        margin-top: 10px;
        .renew-title {
            margin-right: 10px;
        }
        .renew-tip {
            margin-left: 10px;
        }
    }
    .renew-item-label {
        .s-form-item-label {
            line-height: inherit;
        }
    }
    .form-part-wrap {
        border-radius: 6px;
        width: calc(~'100vw - 32px');
        background: #fff;
        padding: 24px;
        margin-bottom: 16px;
        h4 {
            display: inline-block;
            border: none;
            zoom: 1;
            color: #151b26;
            font-size: 16px;
            font-weight: 500;
            margin: 0;
            padding: 0;
        }
        .label_class {
            .inline-tip {
                top: 3px;
                position: relative;
                .s-tip-warning {
                    justify-content: center;
                    .warning_class {
                        fill: #999;
                    }
                }
                .s-tip:hover .s-icon path {
                    fill: #2468f2 !important;
                }
            }
        }
        .center_class {
            .s-row {
                .s-form-item-control-wrapper {
                    line-height: 30px;
                }
            }
        }
    }
    .inline-form {
        h4 {
            float: left;
        }
        .tag-edit-panel {
            margin-left: 130px;
        }
    }
    .buybucket {
        width: 100%;
        left: 0;
        height: 60px;
        .buybucket-container {
            width: auto;
            float: left;
            height: 48px;
            transform: translateY(12.5%);
            display: flex;
            align-items: center;
            .s-button {
                margin-left: 16px;
            }
        }
    }
    .popover-class {
        .s-button {
            margin: 0px;
        }
    }
    .resouce-group-select-main > label {
        width: 130px;
    }
    .s-form-item-label > label {
        display: inline-block;
        min-width: 80px;
        margin-left: 5px;
    }
    .require-label-wrap {
        .s-form-item-label > label {
            &:before {
                content: '*';
                position: absolute;
                left: -5px;
                color: #f33e3e;
            }
        }
    }
    .s-input-area input {
        box-sizing: content-box;
    }
    .localTunnelIp_item {
        .s-form-item-control-wrapper {
            margin-top: 8px;
        }
    }
    .no-style-display {
        display: none;
    }
}

.locale-en {
    .l2gw-create-wrap .s-form .s-form-item-label {
        width: 206px;
    }
    .l2gw-create-wrap .s-form .s-form-help-label {
        width: 206px;
    }
    .l2gw-create-wrap .resouce-group-select .resouce-group-select-main > label {
        width: 206px;
    }
    .l2gw-create-wrap .resouce-group-select .footer {
        margin-left: 206px;
    }
    .l2gw-conn-create-warp .s-form .s-form-item-label {
        width: 332px;
    }
}
