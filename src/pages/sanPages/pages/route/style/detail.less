.route-list-wrap {
    .route-header-wrap {
        display: inline-block;
        margin-left: 5px;
        .icon-warning {
            color: #fbb515;
            vertical-align: middle;
        }
    }
    .s-biz-page-content {
        margin: 0 !important;
        .s-biz-page-tip {
            margin: 0 !important;
            .s-alert {
                margin: 0;
            }
        }
        .s-biz-page-toolbar {
            margin: 16px 0 0 !important;
        }
    }
    .s-biz-page-header {
        display: none;
    }
    .tip-wrap {
        display: inline-block;
        color: #f38900;
        background: #fcf7f1;
        padding: 5px 15px;
    }
    .name-icon {
        font-size: 12px;
        fill: #2468f2;
        display: none;
    }
    .s-table-row {
        &:hover {
            .clip-wrap {
                display: inline-block;
            }
            .name-icon {
                display: inline;
            }
        }
    }
    .text-hidden {
        overflow: hidden;
        display: inline-block;
        vertical-align: middle;
        align-items: center;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 80%;
    }
    .blue-icon {
        font-size: 12px;
        color: #2468f2;
    }
    .ip-select {
        margin-right: 8px;
    }
    .path-common {
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #151b26;
        line-height: 20px;
        font-weight: 400;
        &:last-child {
            margin-top: 4px;
        }
        .path-wrapper {
            display: flex;
            align-items: center;
            .path {
                margin: 0 6px 0 8px;
            }
            .analysis {
                cursor: pointer;
            }
            .inline-tip {
                margin-left: 4px;
                margin-bottom: -6px;
            }
        }
    }
}

.edit-name-wrap {
    .button-wrap {
        margin-top: 5px;
    }
}
.confirm-wrap {
    .s-dialog > .s-dialog-wrapper .s-dialog-header {
        background: #fff;
    }
    .s-dialog > .s-dialog-wrapper .s-dialog-content {
        min-height: 50px;
        .s-icon {
            margin-right: 15px;
        }
    }
    .icon-warning-new {
        color: #fbb515;
        font-size: 22px;
        vertical-align: middle;
        margin-right: 20px;
    }
}
.route-detail-box {
    .s-detail-page-title {
        display: none;
    }
    .s-detail-page-content {
        background: #f7f7f7;
        margin: 0;
    }
    .title_class {
        width: 100%;
        display: flex;
        align-items: center;
        .instance-name {
            font-size: 16px;
            font-weight: 500;
            color: #151b26;
            padding-left: 16px;
            .status {
                padding-left: 12px;
            }
        }
        .s-button {
            .iconfont {
                font-size: 14px;
                color: #84868c;
            }
        }
    }
    .float_right {
        float: right;
    }
    .space-header {
        display: flex;
        align-items: center;
    }
    .route-instance-info {
        display: flex;
        align-items: center;
        .instance-name {
            font-size: 16px;
            margin-left: 22px;
        }
    }
    .route-detail-divider {
        height: 1px;
        background-color: #e8e9eb;
        margin: 0 24px;
    }
    .route-detail-wrap {
        padding: 24px;
        background-color: #fff;
        border-radius: 6px;
        h4 {
            display: block;
            color: #151b26;
            line-height: 24px;
            font-weight: 500;
            margin-bottom: 16px;
            font-size: 16px;
        }
        .cell-title {
            display: inline-block;
            vertical-align: top;
            color: #5e626a;
            margin-right: 8px;
            width: 72px;
        }
        .cell-content {
            display: inline-block;
            color: #151a26;
            max-width: 80%;
            word-break: break-all;
            position: relative;
        }
        .s-biz-page-header {
            display: none;
        }
    }
    .basic-item-wrap {
        display: flex;
        flex-wrap: wrap;
        .basic-item-box {
            display: flex;
            width: 33%;
            align-items: center;
            margin-bottom: 16px;
        }
        .basic-item-key {
            flex-shrink: 0;
        }
        .basic-item-value {
            // flex: 1;
            display: flex;
            align-items: center;
            .icon-copy {
                margin-left: 5px;
            }
        }
    }
    .blue-color {
        margin-left: 8px;
    }
    .text-hidden {
        display: inline-block;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        vertical-align: middle;
    }
    .icon-copy {
        font-size: 12px;
        color: #2468f2;
    }
    .s-biz-page-footer {
        padding: 0 !important;
    }
    .s-table {
        margin-top: 16px !important;
    }
    .s-table .s-table-body {
        max-height: calc(~'100vh - 486px');
    }
}

.edit-route-detail {
    .s-popover-content {
        padding: 12px !important;
        .edit-wrap {
            margin-bottom: 40px;
            .s-input {
                display: block;
            }
            .s-button {
                float: left;
                margin: 10px 5px;
            }
        }
    }
}

.route-detail-wrap {
    background: white;
    .s-detail-page-content {
        margin: 0;
        .app-tab-page {
            padding: 16px;
            border-radius: 6px;
            background: #f7f7f7;
            .bui-tab {
                border-radius: 6px;
                border: none;
                height: 100%;
                .bui-tab-content {
                    padding: 0;
                }
            }
        }
    }
    .instance-info {
        display: flex;
        align-items: center;
        .s-button {
            .iconfont {
                font-size: 14px;
                color: #84868c;
            }
        }
        .instance-name {
            padding-left: 16px;
            font-size: 16px !important;
            font-weight: 500;
            color: #151b26;
        }
        .status {
            padding-left: 12px;
            margin-top: 2px;
            font-weight: 500;
        }
    }

    .s-tabpane-wrapper {
        flex: 1;
    }

    .s-tabs-vertical {
        .s-tabnav-nav {
            display: block;
        }
        .s-tabnav-nav-item {
            text-align: left;
            margin: 0;
            padding: 15px;
        }
    }
    .route-bind-wrapper {
        h4 {
            font-size: 16px;
            color: #151b26;
            line-height: 24px;
            font-weight: 500;
            margin: 24px 24px 16px;
        }
        .s-biz-page-header {
            display: none;
        }
        .s-biz-page-content {
            margin: 16px 24px 0;
            .s-biz-page-body {
                margin-top: 24px;
            }
        }
    }
}
.route-delete-item-dialog {
    width: 600px;
    .s-dialog-content {
        .route-delete-alert {
            margin: 0;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }
    }
}
