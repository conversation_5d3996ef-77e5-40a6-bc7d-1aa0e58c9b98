.route-create-wrap {
    .s-form-item {
        .s-form-item-label {
            min-width: 100px;
        }
    }
    .s-input input {
        box-sizing: border-box;
    }
    .route-class-type {
        margin-top: 10px;
        .wrap-pop {
            display: inline-block;
        }
    }
    .tip-icon {
        color: #9e9898;
        border: 1px solid #9e9898;
        margin-left: 10px;
        &:hover {
            border-color: #2468f2;
        }
    }
    .multi-check-box {
        display: flex;
        align-items: center;
        margin-top: 10px;
        .s-switch {
            margin-left: 15px;
        }
        .inline-tip {
            top: 2px;
            position: relative;
            .s-tip-warning {
                justify-content: center;
                .warning_class {
                    fill: #999;
                }
            }
            .s-tip:hover .s-icon path {
                fill: #2468f2 !important;
            }
        }
        .main-path-tip {
            margin-left: 8px;
        }
    }
    .nexthop-item-wrap {
        margin-bottom: 5px;
        .nexthop-name {
            display: inline-block;
            min-width: 40px;
        }
        .delete-icon {
            font-size: 12px;
        }
    }
    .custom-wrap {
        margin-left: 100px;
        margin-top: -15px;
    }
    .nexthop-err {
        color: #f11212;
        margin: 5px 0;
        font-size: 12px;
    }
    .require-wrap {
        .s-form-item-label label {
            &:before {
                content: '*';
                margin-right: 4px;
                position: absolute;
                left: -7px;
                color: #f33e3e;
            }
        }
        .nexthop-select {
            .s-select-filter {
                width: 93%;
            }
        }
    }
    .s-radio-button-group .s-radio-text,
    .s-radio-button .s-radio-text {
        display: flex;
        align-items: center;
    }
    .dest_class {
        .s-row {
            flex-wrap: nowrap;
        }
    }
    .showTip {
        margin-top: 3px;
        color: #f33e3e;
        width: 335px;
        word-break: break-all;
        word-wrap: break-word;
    }
    .bui-select {
        border-radius: 4px;
        height: 32px;
        &:hover {
            border-color: #2468f2;
        }
        &:hover:after {
            color: #2468f2;
        }
    }
    .bui-select-active {
        border-color: #2468f2;
    }
}
.route-dialog-wrap {
    .s-dialog-content {
        box-sizing: border-box;
        width: 750px;
    }
    .s-dialog-wrapper .s-dialog-content {
        padding: 24px 30px !important;
    }
    .s-dialog-mask {
        z-index: 365610 !important;
    }
    .s-dialog-wrapper {
        z-index: 365613 !important;
        overflow: unset !important;
    }
    .custom-wrapper {
        .custom-toolbar {
            margin-bottom: 20px;
            .custom-tbright {
                float: right;
            }
            .search-class {
                .s-input-area {
                    width: 63px;
                }
            }
            .s-input-area {
                width: 140px;
            }
        }
        .custom-err {
            color: #f33e3e;
        }
    }
    .custom-table {
        .s-row-flex {
            align-items: flex-start;
        }
        .s-form-item-control {
            width: 550px;
        }
    }
    .radio-tip-wrap {
        display: flex;
        .radio-tip {
            margin-top: 10px;
            line-height: 32px;
            height: 32px;
            margin-left: 8px;
        }
    }
    .route-multi-tip {
        line-height: 20px;
        margin-top: 4px;
        color: #ff9326;
    }
}

.vpc-route-create {
    background: #fff;
    width: 100%;
    min-height: 100%;
    background-color: #f7f7f9;
    .form-part-wrap {
        border-radius: 6px;
        width: calc(~'100vw - 32px');
        background: #fff;
        margin: 16px;
        padding: 24px;
        .title_vpc {
            margin-top: 12px;
        }
        .inline-col {
            line-height: 30px;
        }
        h4 {
            display: inline-block;
            border: none;
            zoom: 1;
            font-size: 16px;
            color: #151b26;
            font-weight: 500;
            margin: 0;
            padding: 0;
        }
        .s-form-item-label {
            width: 92px;
            height: 30px;
        }
        .label_class {
            .inline-tip {
                top: 3px;
                position: relative;
                .s-tip-warning {
                    justify-content: center;
                    .warning_class {
                        fill: #999;
                    }
                }
                .s-tip:hover .s-icon path {
                    fill: #2468f2 !important;
                }
            }
        }
        .center_class {
            .s-row {
                .s-form-item-control-wrapper {
                    line-height: 30px;
                }
            }
        }
        .resource-form-part-wrap {
            h4 {
                border: none;
                padding-left: 0;
                font-size: 16px;
                font-weight: 500;
            }
            .resource-group-panel {
                border: none;
                padding: 0;
                .resouce-group-select {
                    .footer {
                        margin-left: 112px;
                    }
                    .wrapper {
                        margin-left: 10px;
                    }
                }
            }
            margin: 10px auto 0 auto;
        }
        .s-form-item-label-left {
            width: 121px;
        }
        .s-table {
            .s-table-container {
                overflow: visible;
            }
        }
    }
    .s-create-page-footer {
        width: 100%;
        .buybucket {
            width: 100%;
            left: 0;
            height: 60px;
            .buybucket-container {
                width: auto;
                float: left;
                height: 48px;
                transform: translateY(12.5%);
                display: flex;
                align-items: center;
            }
        }
    }
}

.locale-en {
    .route-create-wrap .s-form-item .s-form-item-label {
        min-width: 192px;
    }
}
