.route-detail-wrap {
    background: white;
    .s-detail-page-content {
        margin: 0;
        .app-tab-page {
            padding: 16px;
            border-radius: 6px;
            background: #f7f7f7;
            .bui-tab {
                border-radius: 6px;
                border: none;
                height: 100%;
                .bui-tab-content {
                    padding: 0;
                }
            }
        }
    }
    .instance-info {
        display: flex;
        align-items: center;
        .s-button {
            .iconfont {
                font-size: 14px;
                color: #84868c;
            }
        }
        .instance-name {
            padding-left: 16px;
            font-size: 16px !important;
            font-weight: 500;
            color: #151b26;
        }
        .status {
            padding-left: 12px;
            margin-top: 2px;
            font-weight: 500;
        }
    }

    .s-tabpane-wrapper {
        flex: 1;
    }

    .s-tabs-vertical {
        .s-tabnav-nav {
            display: block;
        }
        .s-tabnav-nav-item {
            text-align: left;
            margin: 0;
            padding: 15px;
        }
    }
    .route-bind-wrapper {
        h4 {
            font-size: 16px;
            color: #151b26;
            line-height: 24px;
            font-weight: 500;
            margin: 24px 24px 16px;
        }
        .s-biz-page-header {
            display: none;
        }
        .s-biz-page-content {
            margin: 16px 24px 0;
            .s-biz-page-body {
                margin-top: 24px;
            }
        }
    }
}
