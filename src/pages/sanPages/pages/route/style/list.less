.vpc-route-list {
    height: 100%;
    overflow-y: auto;
    background: #f7f7f9 !important;
    .s-biz-page-header {
        margin: 0px !important;
        background-color: #fff;
        height: auto !important;
    }
    .s-biz-page-content {
        background-color: #fff;
        border-radius: 6px;
        margin: 16px !important;
        padding: 24px;
        .s-biz-page-toolbar {
            margin: 0px;
            .list-page-tb-left-toolbar {
                display: inline-flex;
            }
            .toolbar_right {
                display: inline-flex;
                .left_class {
                    margin-right: 8px;
                }
            }
            .download-icon {
                margin-right: 8px;
            }
            .s-cascader-value {
                min-width: 100px !important;
            }
        }
        .s-biz-page-body {
            margin-top: 16px;
            .s-table {
                .s-table-body {
                    max-height: calc(~'100vh - 284px');
                    overflow: auto;
                }
            }
        }
        .s-biz-page-footer {
            padding-bottom: 0;
            margin-top: 16px;
        }
    }
    .tip-icon {
        color: #9e9898;
        border: 1px solid #9e9898;
        margin-left: 10px;
        &:hover {
            border-color: #2468f2;
        }
    }
    .header-wrap {
        background-color: #f7f7f9;
        .header-widget {
            padding-left: 16px;
            background-color: #ffffff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .widget-left {
                .title {
                    display: inline-block;
                    margin: 0;
                    color: #151b26;
                    margin-right: 12px;
                    height: 47px;
                    line-height: 47px;
                    font-weight: 500;
                    font-size: 16px;
                }
            }
            .widget-right {
                .help-file {
                    margin-right: 16px;
                    font-size: 12px;
                    font-weight: 400;
                    .s-icon {
                        position: relative;
                        top: -1px;
                        right: 4px;
                        font-size: 14px;
                        margin-right: 0;
                        color: #2468f2;
                    }
                }
            }
        }
    }
    .name-icon {
        fill: #2468f2;
        font-size: 12px;
    }
    .click-disable {
        .s-icon {
            cursor: not-allowed;
            fill: #b8babf !important;
            :active {
                fill: #b8babf !important;
            }
        }
        .s-icon-button-able:hover {
            fill: #b8babf !important;
        }
        font-size: 12px;
    }
    .s-table {
        .s-table-row:hover {
            .name-icon {
                display: inline;
            }
        }
        .name-icon {
            display: none;
            font-size: 12px;
            fill: #2468f2;
        }
        .operations {
            .s-button {
                padding: 0;
                margin-right: 12px;
            }
        }
    }
    .space-header {
        border-bottom: none !important;
    }
    .router_layer {
        display: flex;
        align-items: center;
        .inline-tip {
            top: 3px;
            position: relative;
            .s-tip-warning {
                justify-content: center;
                .warning_class {
                    fill: #999;
                }
            }
            .s-tip:hover .s-icon path {
                fill: #2468f2 !important;
            }
        }
    }
    .route-desc {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        .s-trigger-container {
            display: block;
        }
    }
    .s-dropdown-wrap {
        margin: 4px 4px 0 4px;
    }
}
