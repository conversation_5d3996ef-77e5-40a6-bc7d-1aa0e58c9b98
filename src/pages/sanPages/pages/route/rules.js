import {$flag as FLAG} from '@/pages/sanPages/utils/helper';

const currentModule = 'ROUTE';
export default {
    createRoute: [
        {
            custom(data, options) {
                if (options.quota && options.quota.free <= 0 && options.v6Quota && options.v6Quota.free <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: '路由配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `路由配额不足，<a href="${
                                '/quota_center/#/quota/apply/create?serviceType='
                            }${currentModule}&region=${
                                window?.$context?.getCurrentRegionId()
                            }&cloudCenterQuotaName=routeRuleQuota" target="_blank">去申请配额</a>`
                        };
                    }
                }
            }
        }
    ],
    noneMultiType: [
        {
            custom(data, options) {
                if (options.quota && options.quota.free <= 0) {
                    return {
                        disable: true,
                        message: '负载均衡路由组已达到配额'
                    };
                }
            }
        }
    ],
    ipv6NoneMultiType: [
        {
            custom(data, options) {
                if (options.quota && options.quota.free <= 0) {
                    return {
                        disable: true,
                        message: '负载均衡路由组已达到配额'
                    };
                }
            }
        }
    ],
    createV4Route: [
        {
            custom(data, options) {
                if (options.quota <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: '路由配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `IPv4路由配额不足，<a href="${
                                '/quota_center/#/quota/apply/create?serviceType='
                            }${currentModule}&region=${
                                window?.$context?.getCurrentRegionId()
                            }&cloudCenterQuotaName=routeRuleQuota" target="_blank">去申请配额</a>`
                        };
                    }
                }
            }
        }
    ],
    createV6Route: [
        {
            custom(data, options) {
                if (options.v6Quota <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: '路由配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `IPv6路由配额不足，<a href="${
                                '/quota_center/#/quota/apply/create?serviceType='
                            }${currentModule}&region=${
                                window?.$context?.getCurrentRegionId()
                            }&cloudCenterQuotaName=routeRuleQuota" target="_blank">去申请配额</a>`
                        };
                    }
                }
            }
        }
    ]
};
