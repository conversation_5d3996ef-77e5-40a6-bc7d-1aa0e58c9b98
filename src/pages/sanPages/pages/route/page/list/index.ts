import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {Notification, Input} from '@baidu/sui';
import {OutlinedRefresh, OutlinedDownload, OutlinedEditingSquare, OutlinedPlus} from '@baidu/sui-icon';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import {DocService} from '@/pages/sanPages/common';
import testID from '@/testId';
import Confirm from '@/pages/sanPages/components/confirm';
import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import IntroducePanel from '@/pages/sanPages/components/introducePanel/introducePanel';
import {resourceTypeMapText} from './tableField';
import '../../style/list.less';

const {asPage, invokeSUI, invokeSUIBIZ, template, withSidebar, invokeComp} = decorators;
const tpl = html` <template>
    <s-biz-page class="{{klass}}">
        <div class="header-wrap" slot="header">
            <div class="header-widget">
                <div class="widget-left">
                    <span class="title">{{title}}</span>
                    <vpc-select
                        class="vpc-select"
                        on-int="vpcInt"
                        on-change="vpcChange"
                        dataTestId="${testID.route.listSelect}"
                    />
                </div>
                <div class="widget-right">
                    <a
                        s-ref="introduce"
                        href="javascript:void(0)"
                        class="help-file function-introduce"
                        on-click="handleShowCard"
                        on-mouseenter="handleMouseEnter('introduce')"
                        on-mouseleave="handleMouseLeave('introduce')"
                        s-if="{{!FLAG.NetworkSupportXS}}"
                    >
                        <img class="s-icon" src="{{introduceIcon}}" />功能简介
                    </a>
                    <a
                        href="{{DocService.route_helpFile}}"
                        target="_blank"
                        class="help-file"
                        s-if="{{!FLAG.NetworkSupportXS}}"
                    >
                        <s-icon name="warning-new" />帮助文档
                    </a>
                </div>
            </div>
            <introduce-panel
                isShow="{{show}}"
                klass="{{'endpoint-peerconn-wrapper'}}"
                title="{{introduceTitle}}"
                markerDesc="{{markerDesc}}"
                introduceOptions="{{introduceOptions}}"
                on-toggle="handleToggle($event)"
            ></introduce-panel>
        </div>
        <div class="list-page-tb-left-toolbar" slot="tb-left">
            <s-button
                skin="primary"
                on-click="onCreate"
                data-test-id="${testID.route.createRoute}"
                track-id="ti_vpc_route_create"
                track-name="创建路由表"
                ><outlined-plus />{{'创建路由表'}}
            </s-button>
        </div>
        <div slot="tb-right" class="toolbar_right">
            <s-search
                width="{{230}}"
                class="search-warp"
                value="{=searchbox.keyword=}"
                placeholder="{{searchbox.placeholder}}"
                on-search="onSearch"
            >
                <s-select
                    slot="options"
                    width="120"
                    datasource="{{searchbox.keywordTypes}}"
                    value="{=searchbox.keywordType=}"
                    on-change="onSearchboxChange($event)"
                >
                </s-select>
            </s-search>
            <s-button class="s-icon-button fresh_class left_class" on-click="refresh"><outlined-refresh /></s-button>
            <s-button
                on-click="onDownload"
                class="s-icon-button download-icon"
                track-id="ti_vpc_subnet_download"
                track-name="下载"
                ><outlined-download class="icon-class"
            /></s-button>
            <custom-column
                columnList="{{customColumn.datasource}}"
                initValue="{{customColumn.value}}"
                type="route"
                on-init="initColumns"
                on-change="onCustomColumns"
            >
            </custom-column>
        </div>
        <s-table
            columns="{{table.columns}}"
            loading="{{table.loading}}"
            error="{{table.error}}"
            datasource="{{table.datasource}}"
            on-selected-change="tableSelected($event)"
            data-test-id="${testID.route.listTable}"
            on-filter="onFilter"
            on-sort="onSort"
            selection="{=table.selection=}"
        >
            <div slot="h-type-lable">
                <div>
                    {{"类型"}}
                    <s-tip class="inline-tip" content="{{routeTypeTip}}" skin="question" />
                </div>
            </div>
            <div slot="h-bindResourceNum-lable">
                <div>{{'绑定资源数量'}}<s-tip class="inline-tip" content="{{bindSourceTip}}" skin="question" /></div>
            </div>
            <div slot="c-type">{{row | typeDisplay}}</div>
            <div slot="empty">
                <s-empty>
                    <div slot="action"></div>
                </s-empty>
            </div>
            <div slot="error">
                啊呀，出错了？
                <a href="javascript:;" on-click="refresh">重新加载</a>
            </div>
            <div slot="c-routeTableId">
                <span class="truncated" title="{{row.name}}">
                    <s-tooltip content="{{row.routeTableName||row.name||'-'}}">
                        <a
                            href="#/vpc/route/detail?vpcId={{row.vpcUuid||row.vpcId}}&routeTableId={{row.routeTableId}}&routeTableUuid={{row.routeTableUuid}}"
                            data-track-id="ti_vpc_dcgw_detail"
                            data-track-name="详情"
                            data-testid="${testID.route.listName}{{rowIndex}}"
                        >
                            {{row.routeTableName||row.name||'-'}}
                        </a>
                    </s-tooltip>
                </span>
                <s-popover
                    s-if="row.vrf !== 0"
                    s-ref="popover-routeTableName-{{rowIndex}}"
                    placement="top"
                    trigger="{{row.vrf === 0 ? '' : 'click'}}"
                    class="edit-popover-class"
                >
                    <div class="edit-wrap" slot="content">
                        <s-input
                            value="{=edit.routeTableName.value=}"
                            width="320"
                            placeholder="请输入名称"
                            on-input="onEditInput($event, rowIndex, 'routeTableName')"
                        />
                        <div class="edit-tip">支持大小写字母，数字，中文和"-_/.”以字母或者中文开头，不超过65个字</div>
                        <s-button
                            skin="primary"
                            s-ref="editBtn-routeTableName-{{rowIndex}}"
                            disabled="{{true}}"
                            on-click="editConfirm(row, rowIndex, 'routeTableName')"
                            >确定</s-button
                        >
                        <s-button on-click="editCancel(rowIndex, 'routeTableName')">取消</s-button>
                    </div>
                    <outlined-editing-square
                        s-if="FLAG.NetworkRouteOpt"
                        class="name-icon"
                        on-click="onInstantEdit(row, rowIndex, 'routeTableName')"
                    />
                </s-popover>
                <br />
                <span
                    class="truncated"
                    title="{{row.routeTableId||row.id}}"
                    data-testid="${testID.route.listId}{{rowIndex}}"
                    >{{row.routeTableId||row.id}}</span
                >
                <s-clip-board s-if="row.routeTableId||row.id" class="name-icon" text="{{row.routeTableId||row.id}}" />
            </div>
            <div slot="c-bindResourceNum">
                <span>{{row.bindInfos.length || '-'}}</span>
            </div>
            <div slot="c-bindResource">
                <!--bca-disable-next-line-->
                <span>{{row | bindResource | raw}}</span>
            </div>
            <div slot="c-description" class="route-desc">
                <s-tooltip trigger="{{row.description ? 'hover': ''}}">
                    <span slot="content">{{row.description}}</span>
                    <div class="truncated" title="{{row.description}}">{{row.description || '-'}}</div>
                </s-tooltip>
                <s-popover
                    s-if="row.vrf !== 0"
                    s-ref="popover-description-{{rowIndex}}"
                    placement="top"
                    trigger="click"
                    class="edit-popover-class"
                >
                    <div class="edit-wrap" slot="content">
                        <s-textarea
                            width="200"
                            height="48"
                            placeholder="请输入"
                            value="{=edit.description.value=}"
                            width="160"
                            on-input="onEditInput($event, rowIndex, 'description')"
                        />
                        <div class="edit-tip">描述不能超过200个字符</div>
                        <s-button
                            skin="primary"
                            s-ref="editBtn-description-{{rowIndex}}"
                            disabled="{{true}}"
                            on-click="editConfirm(row, rowIndex, 'description')"
                            >确定</s-button
                        >
                        <s-button on-click="editCancel(rowIndex, 'description')">取消</s-button>
                    </div>
                    <outlined-editing-square class="name-icon" on-click="onInstantEdit(row, rowIndex, 'description')" />
                </s-popover>
            </div>
            <div slot="c-opt">
                <span class="operations">
                    <s-button
                        skin="stringfy"
                        on-click="manageRoute(row)"
                        data-test-id="${testID.route.manageRoute}{{rowIndex}}"
                        >管理</s-button
                    >
                    <s-tooltip placement="left" content="{{row | deleteTip}}">
                        <s-button
                            s-if="row.vrf !== 0"
                            disabled="{{row | disabledDelete}}"
                            skin="stringfy"
                            data-test-id="${testID.route.deleteRoute}{{rowIndex}}"
                            on-click="deleteRoute(row)"
                            >删除</s-button
                        >
                    </s-tooltip>
                </span>
            </div>
        </s-table>
        <s-pagination
            s-if="{{pager.total}}"
            slot="footer"
            layout="{{'total, pageSize, pager, go'}}"
            pageSize="{{pager.size}}"
            total="{{pager.total}}"
            page="{{pager.page}}"
            on-pagerChange="onPagerChange"
            on-pagerSizeChange="onPagerSizeChange"
        />
    </s-biz-page>
</template>`;

@template(tpl)
@invokeComp('@search-tag', '@vpc-select', '@custom-column')
@invokeSUI
@invokeSUIBIZ
class RouteList extends Component {
    static components = {
        'outlined-download': OutlinedDownload,
        'outlined-refresh': OutlinedRefresh,
        'outlined-editing-square': OutlinedEditingSquare,
        'introduce-panel': IntroducePanel,
        'outlined-plus': OutlinedPlus,
        's-textarea': Input.TextArea
    };
    static filters = {
        typeDisplay(row: Record<string, any>) {
            return row?.vrf === 0 ? '系统' : '自定义';
        },
        disabledDelete(row: Record<string, any>) {
            const {vrf, bindInfos} = row;
            return vrf === 0 || !!bindInfos?.length;
        },
        deleteTip(row: Record<string, any>) {
            const {bindInfos, vrf} = row;
            let deleteTip = '';
            if (vrf !== 0 && bindInfos?.length) {
                deleteTip = `已关联了TGW的自定义路由表不可删除，如需删除，请先解除绑定关系`;
            }
            return deleteTip;
        },
        bindResource(row: Record<string, any>) {
            const bindInfos = row.bindInfos || [];
            return bindInfos.length
                ? bindInfos
                      .map(item => {
                          return `<a target="_blank" href="/csn/#/csn/tgwinstance?csnId=${item?.extraInfo?.csnId}&current=tgwinstance&tgwId=${item?.resourceId}" style="cursor: pointer">TGW：${item?.resourceId}</a><br />`;
                      })
                      .join('')
                : '';
        }
    };
    initData() {
        const allColumns = [
            {name: 'routeTableId', label: '路由表名称/ID'},
            {
                name: 'vpc_id',
                label: '所在网络',
                render(item, key, col, rowIndex, colIndex, data) {
                    let vpcName = u.escape(item.vpcName) || '-';
                    let vpcShortId = u.escape(item.vpcId);
                    let vpcId = u.escape(item.vpcUuid) || '-';
                    // todo:20201203版本路由表企业版版本不支持全部网络搜索
                    if (FLAG.NetworkRouteOpt) {
                        return `
                      <span class="truncated" title="${vpcName}">
                          <a href="#/vpc/instance/detail?vpcId=${vpcId}&from=route" class="text-hidden"
                              track-id="ti_vpc_instance_detail" track-name="详情" data-testid="${testID.route.vpcName}${rowIndex}">${vpcName}</a>
                      </span>
                      <br>
                      <span class="truncated" title="${vpcShortId}">${vpcShortId}</span>`;
                    }
                    return `
                      <span class="truncated" title="${vpcName}">${vpcName}</span>
                      <br>
                      <span class="truncated" title="${vpcShortId}">${vpcShortId}</span>`;
                }
            },
            {
                name: 'type',
                label: '类型',
                filter: {
                    options: [
                        {text: '全部', value: ''},
                        {text: '系统', value: 'System'},
                        {text: '自定义', value: 'Custom'}
                    ],
                    value: ''
                }
            },
            {name: 'bindResourceNum', label: '绑定资源数量'},
            {name: 'bindResource', label: '绑定资源'},
            {name: 'description', label: '描述'},
            {name: 'opt', label: '操作'}
        ];
        const disableColumns = allColumns.map(item => ({
            text: item.label,
            value: item.name,
            disabled: ['opt', 'routeTableId'].includes(item.name)
        }));
        return {
            FLAG,
            klass: ['main-wrap-new', 'vpc-route-list'],
            title: '路由表',
            vpcId: '',
            vpcList: [
                {
                    text: '所在网络：全部私有网络',
                    value: ''
                }
            ],
            allVpc: [],
            table: {
                loading: false,
                selection: {
                    selectedIndex: []
                },
                columns: allColumns,
                datasource: [],
                allColumns
            },
            customColumn: {
                datasource: disableColumns,
                value: ['routeTableId', 'vpc_id', 'type', 'bindResourceNum', 'bindResource', 'description', 'opt']
            },
            pager: {
                size: 10,
                page: 1,
                total: 0
            },
            edit: {
                routeTableName: {
                    value: '',
                    error: true,
                    visible: false
                },
                description: {
                    value: '',
                    error: true,
                    visible: false
                }
            },
            DocService,
            searchbox: {
                keyword: '',
                placeholder: '请输入路由表名称进行搜索',
                keywordType: 'routeTableName',
                keywordTypes: [
                    {value: 'routeTableName', text: '路由表名称'},
                    {value: 'routeTableId', text: '路由表ID'}
                ]
            },
            show: true,
            introduceTitle: '路由表简介',
            markerDesc: [
                '路由表是VPC中的流量控制器，通过全局一张路由表，实现对全局和子网级别的流量控制。您可以自定义路由规则，控制网络流量的导向目的地。',
                '路由表类型分别为系统路由表和自定义路由表。说明：CSN不使用路由精细化控制路由，无需创建自定义路由表并绑定TGW。详细使用请查看<a href="https://cloud.baidu.com/doc/VPC/s/jjwvytyw0">路由表</a>。'
            ],
            introduceIcon: INTRODUCE_ICON_COLOR,
            introduceEle: null,
            routeTypeTip:
                '路由表类型分别为系统路由表和自定义路由表。说明：如CSN不使用精细化控制路由，无需创建自定义路由表并绑定TGW。',
            bindSourceTip: '当前所有子网默认绑定到系统路由表'
        };
    }
    attached() {
        this.data.set('introduceEle', this.ref('introduce'));
    }
    vpcInt() {
        this.loadPage();
    }
    vpcChange() {
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    getSearchCriteria() {
        const {pager, keyword, keywordType} = this.data.get('');
        return u.extend({}, {keyword, keywordType}, {pageNo: pager.page, pageSize: pager.size});
    }

    loadPage() {
        this.data.set('table.loading', true);
        let payload = this.getPayload();
        return this.$http.routeTableList(payload).then(data => {
            this.data.set('table.loading', false);
            const formatedData = u.map(data.result || [], item => {
                return {
                    ...item,
                    disableFlag: false
                };
            });
            this.data.set('table.datasource', formatedData);
            this.data.set('pager.total', data.totalCount);
        });
    }

    getPayload() {
        const {
            filters,
            pager: {page, size},
            searchbox: {keyword, keywordType}
        } = this.data.get('');
        let payload = {
            pageNo: page,
            pageSize: size,
            keyword,
            keywordType,
            type: filters?.type,
            vpcUuid: window.$storage.get('vpcId')
        };
        return payload;
    }

    onCreate() {
        location.hash = '#/vpc/route/create';
    }

    setTableColumns(customColumnNames) {
        customColumnNames = customColumnNames ? customColumnNames : this.data.get('customColumn.value');
        const allColumns = this.data.get('table.allColumns');
        let columns = [];
        allColumns.forEach(item => customColumnNames.indexOf(item.name) > -1 && columns.push(item));
        this.data.set('table.columns', columns);
    }

    initColumns(cols) {
        this.setTableColumns(cols);
    }

    onCustomColumns(value) {
        this.data.set('customColumn.value', value);
        this.setTableColumns(value);
    }

    manageRoute(item: any) {
        const {vpcUuid, routeTableUuid, routeTableId} = item;
        location.hash = `#/vpc/route/detail?vpcId=${vpcUuid}&routeTableId=${routeTableId}&routeTableUuid=${routeTableUuid}`;
    }

    deleteRoute(row: Record<string, any>) {
        const confirm = new Confirm({
            data: {
                open: true,
                content: '请确定是否要删除该自定义路由表？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            const {routeTableId} = row;
            this.$http
                .deleteCustomeRoute({
                    routeTableId
                })
                .then(() => {
                    this.loadPage();
                    Notification.success('删除成功');
                });
        });
    }
    resetTable() {
        this.data.set('pager.page', 1);
        this.data.set('table.selection.selectedIndex', []);
    }

    refresh() {
        this.resetTable();
        return this.loadPage();
    }

    onFilter(e) {
        let name = e.field.name;
        let value = e[name] ? e[name] : '';
        this.data.set('filters.' + name, value);
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    onSearch() {
        return this.refresh();
    }

    onSearchboxChange({value}) {
        const {keywordTypes} = this.data.get('searchbox');
        let index = keywordTypes.findIndex(item => item.value === value);
        if (index > -1) {
            this.data.set('searchbox.keyword', '');
            this.data.set('searchbox.placeholder', `请输入${keywordTypes[index].text}进行搜索`);
        }
    }

    // 改变页数
    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }

    // 改变每页显示个数
    onPagerSizeChange(e) {
        this.data.set('pager.size', e.value.pageSize);
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    // 点击修改名称icon
    onInstantEdit(row, rowIndex, type) {
        this.data.set(`edit.${type}.value`, row[type]);
        this.data.set(`edit.${type}.error`, false);
        const a = this.ref(`popover-${type}-${rowIndex}`);
        a.data.set('visible', !a.data.get('visible'));
    }

    // 编辑弹框-输入名称/描述
    onEditInput(e, rowIndex, type) {
        let result =
            type === 'routeTableName'
                ? e.value === '' || !/^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/.test(e.value)
                : e.value.length > 200;
        this.data.set(`edit.${type}.error`, result);
        this.data.set(`edit.${type}.value`, e.value);
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', result);
    }

    // 修改确认
    editConfirm(row, rowIndex, type: 'routeTableName' | 'description') {
        const edit = this.data.get(`edit.${type}`);
        if (edit.error) {
            return;
        }
        const key = type === 'routeTableName' ? 'description' : 'name';
        const submitType = type === 'routeTableName' ? 'name' : type;
        const rowKey = type === 'routeTableName' ? 'description' : 'routeTableName';
        this.$http
            .updateRouteName({
                [key]: row[`${rowKey}`],
                [submitType]: edit.value,
                id: row.routeTableUuid
            })
            .then(() => {
                this.editCancel(rowIndex, type);
                this.loadPage();
                Notification.success('修改成功');
            });
    }
    // 修改名称取消
    editCancel(rowIndex, type: 'routeTableName' | 'description') {
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', true);
        this.ref(`popover-${type}-${rowIndex}`).data.set('visible', false);
    }
    onRegionChange() {
        window.$storage.set('vpcId', '');
        location.reload();
    }

    onDownload() {
        window.open(`/api/network/v1/route/route_table/download`);
    }
    handleToggle(flag) {
        this.data.set('show', flag);
        this.data.set('introduceIcon', INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#151B26';
    }
    handleShowCard() {
        if (!this.data.get('show')) {
            this.data.set('show', true);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type) {
        this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#2468F2';
    }
    handleMouseLeave(type) {
        const isShow = this.data.get('show');
        this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        !isShow && (ele.style.color = '#151B26');
    }
}
export default San2React(Processor.autowireUnCheckCmpt(RouteList));
