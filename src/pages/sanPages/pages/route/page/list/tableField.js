import u from 'lodash';
import {RouteType, PathType} from '@/pages/sanPages/common/enum';
export const SCHEMA = [
    {
        name: 'sourceAddress',
        minWidth: 220,
        label: '源网段',
        render(item) {
            let sourceName = u.escape(item.sourceName);
            let sourceAddress = u.escape(item.sourceAddress);
            let source = sourceName ? sourceName + '(' + sourceAddress + ')' : sourceAddress;
            return `<span title="${source}" class="truncated">${source}</span>`;
        },
        sortable: true
    },
    {
        name: 'destinationAddress',
        minWidth: 180,
        label: '目标网段',
        render(item) {
            return u.escape(item.destinationAddress);
        },
        sortable: true
    },
    {
        name: 'nexthop',
        minWidth: 120,
        label: '下一跳'
    },
    {
        name: 'nexthopType',
        minWidth: 100,
        label: '路由类型',
        filter: {
            options: RouteType.getFiltertype(),
            value: ''
        },
        render(item) {
            let pathType = '';
            if (item.nexthopType === RouteType.GW) {
                if (item.pathType === PathType.ECMP) {
                    pathType = '专线-负载均衡';
                } else if (item.pathType === PathType.NORMAL) {
                    pathType = '单线';
                } else if (item.pathType === PathType.ACTIVE || item.pathType === PathType.STANDBY) {
                    pathType = '专线-主备';
                    if (item.multiRouteRuleVos && item.multiRouteRuleVos[0]?.nexthopType === 'vpn') {
                        pathType = '专线-主，IPsec VPN备';
                    }
                }
            }
            if (item.nexthopType === RouteType.VPN) {
                if (item.pathType === PathType.STANDBY) {
                    return '专线网关（专线-主，IPsec VPN备）';
                }
            }
            return RouteType.getTextFromValue(item.nexthopType) + (pathType ? '（' + pathType + '）' : '');
        }
    },
    {
        name: 'status',
        minWidth: 120,
        label: 'VPC状态'
    },
    {
        name: 'preemptiveMode',
        minWidth: 100,
        label: '主路径抢占'
    },
    {
        name: 'csnInfo',
        minWidth: 90,
        label: 'CSN发布状态'
    },
    {
        name: 'description',
        minWidth: 100,
        label: '描述'
    },
    {
        name: 'opt',
        minWidth: 120,
        label: '操作'
    }
];

export const DELETE_SCHEMA = [
    {
        name: 'sourceAddress',
        minWidth: 220,
        label: '源网段',
        render(item) {
            let sourceName = u.escape(item.sourceName);
            let sourceAddress = u.escape(item.sourceAddress);
            let source = sourceName ? sourceName + '(' + sourceAddress + ')' : sourceAddress;
            return `<span title="${source}" class="truncated">${source}</span>`;
        }
    },
    {
        name: 'destinationAddress',
        minWidth: 180,
        label: '目标网段',
        render(item) {
            return u.escape(item.destinationAddress);
        }
    },
    {
        name: 'nexthop',
        minWidth: 140,
        label: '下一跳'
    },
    {
        name: 'nexthopType',
        minWidth: 100,
        label: '路由类型',
        render(item) {
            let pathType = '';
            if (item.nexthopType === RouteType.GW) {
                if (item.pathType === PathType.ECMP) {
                    pathType = '专线-负载均衡';
                } else if (item.pathType === PathType.NORMAL) {
                    pathType = '单线';
                } else if (item.pathType === PathType.ACTIVE || item.pathType === PathType.STANDBY) {
                    pathType = '专线-主备';
                    if (item.multiRouteRuleVos && item.multiRouteRuleVos[0]?.nexthopType === 'vpn') {
                        pathType = '专线-主，IPsec VPN备';
                    }
                }
            }
            if (item.nexthopType === RouteType.VPN) {
                if (item.pathType === PathType.STANDBY) {
                    return '专线网关（专线-主，IPsec VPN备）';
                }
            }
            return RouteType.getTextFromValue(item.nexthopType) + (pathType ? '（' + pathType + '）' : '');
        }
    },
    {
        name: 'description',
        minWidth: 100,
        label: '描述'
    }
];

export const resourceTypeMapText = {
    CSN_TGW: 'TGW'
};
