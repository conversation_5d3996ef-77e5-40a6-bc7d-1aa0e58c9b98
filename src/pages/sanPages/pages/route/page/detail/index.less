.bind-dialog-wrapper {
    .s-form {
        .s-form-item-label {
            width: 72px;
            text-align: left;
        }
        .s-form-item-control-wrapper {
            line-height: 30px;
            margin-left: 12px;
            .s-transfer {
                .s-transfer-wrapper-left {
                    min-width: 180px;
                }
                .s-transfer-wrapper-right {
                    min-width: 180px;
                    .s-transfer-content {
                        height: calc(100% - 12px);
                    }
                }
                .s-transfer-wrapper-title {
                    .s-checkbox {
                        display: flex;
                    }
                }
                .s-transfer-content {
                    .s-transfer-content-searchbox-wrapper {
                        margin: 0;
                        height: 46px;
                        .s-search {
                            height: 30px;
                            border-radius: 4px;
                            .s-search-suffix {
                                display: flex;
                                align-items: center;
                                width: 20px;
                            }
                        }
                    }
                    .s-transfer-content-wrapper {
                        padding-top: 12px;
                    }
                    .s-transfer-content-item {
                        .item-content-wrap {
                            margin-left: 8px;
                        }
                    }
                }
            }
        }
    }
}
.route-bind-wrapper {
    h4 {
        font-size: 16px;
        color: #151b26;
        line-height: 24px;
        font-weight: 500;
        margin: 24px 24px 16px;
    }
    .s-biz-page-header {
        display: none;
    }
    .s-biz-page-content {
        margin: 16px 24px 0;
        .s-biz-page-body {
            margin-top: 24px;
        }
    }
    .route-bind-tgw {
        .tgw-link {
            cursor: pointer;
        }
    }
}
