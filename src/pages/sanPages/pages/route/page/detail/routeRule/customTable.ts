/*
 * @description: VPC实例列表页
 * @file: network/subnet/pages/List.js
 * @author: p<PERSON><PERSON><PERSON><EMAIL>
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';

import '../../../style/create.less';

const {asComponent, invokeSUI, invokeSUIBIZ, invokeAppComp, template} = decorators;
const tpl = html` <template>
    <div class="{{klass}}">
        <div class="custom-toolbar">
            <span>实例类型：</span>
            <s-select
                width="{{170}}"
                value="{=instanceType=}"
                datasource="{{customTypeDataSource}}"
                on-change="onSearchChange"
            ></s-select>
            <s-search
                value="{=keyword=}"
                placeholder="{{placeholder}}"
                on-search="onSearch($event)"
                class="custom-tbright"
            >
                <s-select
                    slot="options"
                    value="{=keywordType=}"
                    datasource="{{selectDatasource}}"
                    on-change="selectChange($event)"
                    class="search-class"
                />
            </s-search>
        </div>
        <s-table
            columns="{{table.allColumns}}"
            datasource="{{table.datasource}}"
            on-selected-change="onSelectChange"
            selection="{{selection}}"
            loading="{{table.loading}}"
        >
            <div slot="c-id">
                <span>{{row.instanceName}}</span>
                <br />
                <span>{{row.instanceId}}</span>
            </div>
        </s-table>
        <span class="custom-err" s-if="customErr">{{customErr}}</span>
    </div>
</template>`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class CustomTable extends Component {
    initData() {
        let allColumns = [
            {name: 'id', label: '实例名称/ID', width: 60},
            {name: 'internalIp', label: 'IP地址', width: 60}
        ];
        return {
            klass: ['custom-wrapper'],
            table: {
                loading: false,
                allColumns,
                datasource: []
            },
            selection: {
                mode: 'single',
                selectedIndex: [0]
            },
            customTypeDataSource: [
                {text: '云服务器BCC', value: 'BCC'},
                {text: '弹性裸金属服务器BBC', value: 'BBC'}
            ],
            instanceType: 'BCC',
            keyword: '',
            placeholder: '请输入实例名称前缀进行搜索',
            selectDatasource: [
                {text: '实例名称', value: 'instanceName'},
                {text: '实例ID', value: 'instanceId'},
                {text: 'IP地址', value: 'internalIp'}
            ],
            keywordType: 'instanceName',
            customErr: ''
        };
    }

    inited() {
        this.loadCustomList();
    }

    loadCustomList() {
        this.data.set('table.loading', true);
        let vpcUuid = this.data.get('vpcUuid');
        let instanceType = this.data.get('instanceType');
        let keyword = this.data.get('keyword');
        let keywordType = this.data.get('keywordType');
        this.$http.customList(vpcUuid, keyword, keywordType, instanceType).then(res => {
            this.data.set('table.loading', false);
            const nexthopId = this.data.get('nexthopId');
            const existIndex = u.findIndex(res.result || [], (item: any) => item.instanceUuid === nexthopId);
            const editIndex = existIndex !== -1 ? existIndex : 0;
            this.data.set('selection.selectedIndex', [editIndex]);
            this.data.set('table.datasource', res.result);
            this.data.set('customErr', res.result.length <= 0 ? '请选择实例' : '');
            this.fire('customSelect', res.result.length <= 0 ? {} : res.result[editIndex]);
        });
    }

    onSearchChange(e) {
        this.data.set('instanceType', e.value);
        this.data.set('keyword', '');
        this.data.set('keywordType', 'instanceName');
        this.data.set('placeholder', '请输入实例名称前缀进行搜索');
        this.loadCustomList();
    }

    selectChange({value}) {
        let text = this.data.get('selectDatasource').find(item => item.value === value).text;
        this.data.set('placeholder', `请输入${text}前缀进行搜索`);
        this.data.set('keywordType', value);
        this.data.set('keyword', '');
    }

    onSearch({value}) {
        this.data.set('keyword', value);
        this.loadCustomList();
    }

    onSelectChange(e) {
        const datasource = this.data.get('table.datasource');
        const currentSelectItem = datasource[e.value.selectedIndex[0]];
        this.data.set('customErr', currentSelectItem ? '' : '请选择实例');
        this.data.set('selectedItem', currentSelectItem);
        this.fire('customSelect', this.data.get('selectedItem'));
    }
}
export default Processor.autowireUnCheckCmpt(CustomTable);
