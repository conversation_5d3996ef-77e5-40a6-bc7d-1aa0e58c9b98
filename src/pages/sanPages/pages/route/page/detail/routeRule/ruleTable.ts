import {columns} from './../../../../flowlog/tableFields';
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/member-ordering */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';

import {checker} from '@baiducloud/bce-opt-checker';
import {Alert} from '@baidu/sui';
import {OutlinedRefresh, OutlinedDownload, OutlinedPlus, OutlinedEditingSquare} from '@baidu/sui-icon';

import {SCHEMA, DELETE_SCHEMA} from '../../list/tableField';
import rules from '../../../rules';
import CreateRule from './createRule';
import {getVpcName} from '@/pages/sanPages/utils/common';
import {$flag as FLAG, kXhrOptions} from '@/pages/sanPages/utils/helper';
import {RouteType, PathType, CSN_ROUTE_STATUS, RouteStatus} from '@/pages/sanPages/common/enum';
import {ContextService} from '@/pages/sanPages/common';
import Confirm from '@/pages/sanPages/components/confirm';
import PATH_SUCCESS from '@/img/path_success.svg?url';
import PATH_ERROR from '@/img/path_error.svg?url';
import PATH_UNAVAILABLE from '@/img/path_unavailable.svg?url';
import ANALYSIS_SUCCESS from '@/img/analysis_success.svg?url';
import ANALYSIS_ERROR from '@/img/analysis_error.svg?url';
import Monitor from '@/pages/sanPages/pages/dc/channel/monitor/monitor';

const {asComponent, invokeSUI, invokeSUIBIZ, invokeComp, template} = decorators;
const tpl = html`
    <div>
        <s-biz-page class="{{klass}}">
            <div class="list-page-tb-left-toolbar" slot="tb-left">
                <s-tooltip
                    trigger="{{accessOpt.createRouteRule.disabled || enableCreate.disable || loadingAllData ? 'hover' : ''}}"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{accessOpt.createRouteRule.message ? accessOpt.createRouteRule.message : enableCreate.message |
                        raw}}
                    </div>
                    <s-button
                        skin="primary"
                        on-click="onCreate"
                        disabled="{{accessOpt.createRouteRule.disabled ? true : (enableCreate.disable || loadingAllData || !routeId)}}"
                        ><outlined-plus />添加路由</s-button
                    >
                </s-tooltip>
            </div>
            <s-alert skin="warning" slot="tip" s-if="{{enableCreate.disable}}">
                <!--bca-disable-next-line-->
                {{tip | raw}}
            </s-alert>
            <div slot="tb-right">
                <s-select
                    class="ip-select"
                    value="{{ipVersion}}"
                    width="{{100}}"
                    datasource="{{ipVersionList}}"
                    on-change="typeSelect"
                >
                </s-select>
                <s-search
                    width="{{230}}"
                    value="{=searchbox.keyword=}"
                    placeholder="{{searchbox.placeholder}}"
                    on-search="onSearch"
                >
                    <s-select
                        slot="options"
                        width="{{130}}"
                        value="{=searchbox.keywordType=}"
                        on-change="onSearchboxChange"
                    >
                        <s-select-option
                            s-for="item in searchbox.keywordTypes"
                            value="{{item.value}}"
                            label="{{item.text}}"
                        />
                    </s-select>
                </s-search>
                <s-button on-click="refresh" class="s-icon-button"><outlined-refresh class="icon-class" /></s-button>
                <s-button on-click="onDownload" class="s-icon-button"
                    ><outlined-download class="icon-class"
                /></s-button>
            </div>
            <s-table
                s-ref="table"
                on-sort="onSort"
                on-filter="onFilter"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
            >
                <div slot="empty">
                    <table-empty
                        actionAuth="{{accessOpt.createRouteRule}}"
                        desc="{{'暂无路由条目'}}"
                        actionText="立即添加"
                        on-click="onCreate"
                    />
                </div>
                <div slot="error">
                    啊呀，出错了？
                    <a href="javascript:;" on-click="loadPage">重新加载</a>
                </div>
                <div slot="c-csnInfo">
                    <s-popover content="{{row.csnStatusInfo}}" trigger="{{row.csnStatusInfo ? 'hover' : ''}}">
                        <span>{{row | getCsnInfo}}</span>
                    </s-popover>
                </div>
                <div slot="c-nexthop">
                    <div s-if="typeof row.nexthop === 'string'"></div>
                    <a href="/csn#/csn/detail?csnId={{vpcInfo.csnId}}&current=detail" s-if="row.nexthopType === 'CSN'"
                        >{{vpcInfo.csnId}}</a
                    >
                    <span s-else class="truncated">
                        <!--bca-disable-next-line-->
                        {{row | nexthopExtra | raw}}
                    </span>
                </div>
                <div slot="c-description">
                    <span class="text-hidden">{{row.description || '-'}}</span>
                    <s-popover
                        class="edit-popover-class edit-route-detail"
                        s-if="row.nexthopType !== 'CSN' && row.nexthopType !== RouteType.SYSTEM && !accessOpt.updateRouteRule.disabled"
                        s-ref="{{'instanceDescEdit' + rowIndex}}"
                        placement="top"
                        trigger="click"
                    >
                        <div class="edit-wrap" slot="content">
                            <s-input
                                value="{=instance.desc=}"
                                width="320"
                                placeholder="请输入"
                                on-input="onNameInput($event,row, rowIndex, 'Desc')"
                            />
                            <s-button
                                skin="primary"
                                s-ref="{{'editBtnDesc' + rowIndex}}"
                                disabled="{{true}}"
                                on-click="onEdit(row,rowIndex)"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel(row,rowIndex, 'Desc')">取消</s-button>
                        </div>
                        <outlined-editing-square class="name-icon" on-click="beforeEdit(row)" />
                    </s-popover>
                </div>
                <div slot="c-preemptiveMode">
                    <s-popover
                        s-if="{{row.pathType === PathType.ACTIVE || row.pathType === PathType.STANDBY}}"
                        content="VPN为备路径模式，不支持主路径抢占"
                        trigger="{{row.reserveVpn ? 'hover' : 'null'}}"
                    >
                        <s-switch
                            disabled="{{row.reserveVpn}}"
                            checked="{=row.preemptiveMode=}"
                            on-change="changePathMode(row, $event)"
                        />
                    </s-popover>
                    <div s-else>-</div>
                </div>
                <div slot="c-status">
                    <div class="path-common" s-for="item, idx in row.status">
                        <!--支持主备切换的专线网关实例VPC状态-->
                        <div
                            class="path-wrapper"
                            s-if="{{(row.pathType === PathType.ACTIVE || row.pathType === PathType.STANDBY) && !row.reserveVpn}}"
                        >
                            <img src="{{row.pathTypes[idx] === PathType.ACTIVE ? PATH_SUCCESS : PATH_ERROR}}" />
                            <span class="path"> {{row | getETTextByPathType(idx)}} </span>
                            <img
                                s-if="row.effect[idx]"
                                class="analysis"
                                src="{{ANALYSIS_SUCCESS}}"
                                on-click="handleShowMonitor(row)"
                            />
                            <s-tip
                                s-if="row.effect[idx]"
                                class="inline-tip"
                                content="{{row | pathTip(idx)}}"
                                skin="question"
                            />
                            <img
                                s-if="!row.effect[idx]"
                                class="analysis"
                                src="{{ANALYSIS_ERROR}}"
                                on-click="handleShowMonitor(row)"
                            />
                            <s-tip
                                s-if="!row.effect[idx]"
                                class="inline-tip"
                                content="{{row | pathTip(idx)}}"
                                skin="question"
                            />
                        </div>
                        <!--之前VPC状态列逻辑-->
                        <div s-else class="path-wrapper">
                            <img src="{{item | getConfigByStatus}}" />
                            <span class="path">{{item | getTextByStatus}}</span>
                            <!--支持专线网关多线路由类型-负载均衡的专线网关实例VPC状态-->
                            <img
                                s-if="checkShowMonitor(row)"
                                class="analysis"
                                src="{{ ANALYSIS_SUCCESS }}"
                                on-click="handleShowMonitor(row)"
                            />
                        </div>
                    </div>
                </div>
                <div slot="c-opt">
                    <s-popover
                        s-if="{{row.pathType === PathType.ACTIVE || row.pathType === PathType.STANDBY}}"
                        content="VPN为备路径模式，不支持主备切换"
                        trigger="{{row.reserveVpn ? 'hover' : 'null'}}"
                    >
                        <s-button skin="stringfy" on-click="switchGw(row, rowIndex)" disabled="{{row.reserveVpn}}"
                            >主备切换</s-button
                        >
                    </s-popover>
                    <s-tooltip content="{{accessOpt.updateRouteRule.message}}">
                        <s-button
                            s-if="isCanEdit(row)"
                            skin="stringfy"
                            disabled="{{accessOpt.updateRouteRule.disabled}}"
                            on-click="editRoute(row, rowIndex)"
                            >编辑</s-button
                        >
                    </s-tooltip>
                    <s-popover
                        s-if="row.nexthopType !== RouteType.SYSTEM"
                        content="{{row.notDelContent}}"
                        trigger="{{row.notDelContent ? 'hover' : 'null'}}"
                    >
                        <s-tooltip content="{{accessOpt.deleteRouteRule.message}}">
                            <s-button
                                skin="stringfy"
                                on-click="deleteRoute(row, rowIndex)"
                                disabled="{{accessOpt.deleteRouteRule.disabled ? true : row.notDelContent}}"
                                >{{'删除'}}</s-button
                            >
                        </s-tooltip>
                    </s-popover>
                    <!--发布、撤回显示条件：有vpcInfo.csnId和system类型并且当发布和撤回同时置灰时不显示-->
                    <s-button
                        disabled="{{(row.csnInfo && row.csnInfo === 'published') || !row.allowPublishCsn}}"
                        s-if="{{(vpcInfo.csnId && row.nexthopType === RouteType.SYSTEM)
                        && !(((row.csnInfo && row.csnInfo === 'published') || !row.allowPublishCsn) && (row.csnInfo !== 'published'))}}"
                        skin="stringfy"
                        on-click="publishRoute(true, row)"
                        >发布</s-button
                    >
                    <s-button
                        disabled="{{row.csnInfo !== 'published'}}"
                        s-if="{{(vpcInfo.csnId && row.nexthopType === RouteType.SYSTEM)
                        && !(((row.csnInfo && row.csnInfo === 'published') || !row.allowPublishCsn) && (row.csnInfo !== 'published'))}}"
                        skin="stringfy"
                        on-click="publishRoute(false, row)"
                        >撤回</s-button
                    >
                </div>
            </s-table>
            <s-pagination
                s-if="{{pager.total > 10}}"
                slot="footer"
                layout="{{'total, pageSize, pager, go'}}"
                pageSize="{{pager.size}}"
                total="{{pager.total}}"
                page="{{pager.page}}"
                on-pagerChange="onPagerChange"
                on-pagerSizeChange="onPagerSizeChange"
            />
            <s-dialog
                class="route-delete-item-dialog"
                width="{{600}}"
                on-close="handleCancel"
                open="{{deleteVisible}}"
                title="您确认要删除该路由条目吗？"
            >
                <s-alert class="route-delete-alert" skin="warning">
                    路由条目删除可能导致业务中断，请在此确认变更后的影响。
                </s-alert>
                <s-table columns="{{deleteRowColumn}}" datasource="{{deleteRowDatasource}}">
                    <div slot="c-nexthop">
                        <div s-if="typeof row.nexthop === 'string'"></div>
                        <a
                            href="/csn#/csn/detail?csnId={{vpcInfo.csnId}}&current=detail"
                            s-if="row.nexthopType === 'CSN'"
                            >{{vpcInfo.csnId}}</a
                        >
                        <span s-else class="truncated">
                            <!--bca-disable-next-line-->
                            {{row | nexthopExtra | raw}}
                        </span>
                    </div>
                    <div slot="c-description">
                        <span class="text-hidden">{{row.description || '-'}}</span>
                    </div>
                </s-table>
                <div slot="footer">
                    <s-button on-click="handleCancel">取消</s-button>
                    <s-button skin="primary" on-click="handleConfirm" loading="{{deleteLoading}}">确定</s-button>
                </div>
            </s-dialog>
        </s-biz-page>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeComp('@table-empty')
class RouteTable extends Component {
    static components = {
        'outlined-refresh': OutlinedRefresh,
        'outlined-download': OutlinedDownload,
        'outlined-plus': OutlinedPlus,
        'outlined-editing-square': OutlinedEditingSquare,
        's-alert': Alert
    };
    initData() {
        return {
            flag: FLAG,
            klass: ['route-list-wrap'],
            table: {
                loading: false,
                datasource: [],
                columns: SCHEMA
            },
            pager: {
                page: 1,
                total: 10,
                pageSize: 10
            },
            routeRelay: {
                disable: false,
                checked: false
            },
            quota: {
                free: 10,
                total: 0
            },
            PathType,
            RouteType,
            vpcInfo: '',
            searchbox: {
                keyword: '',
                placeholder: '请输入源网段前缀进行搜索',
                keywordType: 'sourceAddress',
                keywordTypes: [
                    {value: 'sourceAddress', text: '源网段'},
                    {value: 'destinationAddress', text: '目标网段'}
                ]
            },
            ipVersionList: [
                {value: '', text: '全部条目'},
                {value: '4', text: 'IPv4'},
                {value: '6', text: 'IPv6'}
            ],
            ipVersion: '',
            v6Quota: {
                free: 10,
                total: 0
            },
            allRouteList: [],
            accessOpt: {},
            PATH_SUCCESS,
            PATH_ERROR,
            PATH_UNAVAILABLE,
            ANALYSIS_SUCCESS,
            ANALYSIS_ERROR,
            currentRegion: window?.$context?.getCurrentRegion(),
            deleteVisible: false,
            deleteRowColumn: DELETE_SCHEMA,
            deleteRowDatasource: [],
            deleteLoading: false
        };
    }

    static computed = {
        enableCreate() {
            let quota = this.data.get('quota');
            let v6Quota = this.data.get('v6Quota');
            let {createRoute} = checker.check(rules, '', 'createRoute', {
                quota,
                v6Quota
            });
            return createRoute;
        },
        tip() {
            let quota = this.data.get('quota');
            let v6Quota = this.data.get('v6Quota');
            let vpcInfo = this.data.get('vpcInfo');
            if (!vpcInfo) {
                return '';
            }
            if (quota.free <= 0 && v6Quota.free <= 0) {
                let ticketUrl = `${ContextService.Domains.ticket}/#/ticket/create`;
                if (FLAG.NetworkSupportXS) {
                    return `您的私有网络${vpcInfo.name}下路由数量已经达到配额`;
                }
                return (
                    `您的私有网络${vpcInfo.name}下路由数量已经达到配额，如需更多路由，可以通过<a href="${ticketUrl}" target="_blank">工单</a>申请`
                );
            }
        }
    };

    static filters = {
        getCsnInfo(row) {
            if (!row.csnInfo) {
                return '-';
            }
            return CSN_ROUTE_STATUS.getTextFromValue(row.csnInfo) || '-';
        },
        nexthopExtra(row) {
            let nexthopExtra = '';
            if (typeof row.nexthop === 'string') {
                nexthopExtra = u.escape(item.nexthop);
                if (nexthopExtra === 'local') {
                    nexthopExtra = '本地';
                }
                if (nexthopExtra === 'default gateway') {
                    nexthopExtra = '默认网关';
                }
            } else {
                let nexthopList = row.nexthop.map(item => {
                    return item ? item : '-';
                });
                nexthopExtra = nexthopList.join('<br>');
            }
            return nexthopExtra;
        },
        getETTextByPathType(row, idx) {
            return row.pathTypes[idx] === PathType.ACTIVE
                ? '主路径'
                : row.pathTypes[idx] === PathType.STANDBY
                  ? '备路径'
                  : '';
        },
        pathTip(row, idx) {
            const mainText = row.pathTypes[idx] === PathType.ACTIVE ? '主' : '备';
            const pathColor = row.pathTypes[idx] === PathType.ACTIVE ? '绿色' : '红色';
            const monitorTip = row.effect[idx]
                ? '绿色监控图标表示流量在当前路径。'
                : '红色监控图标代表流量不在当前路径。';
            return `${pathColor}小圆圈表示当前是${mainText}路径，${monitorTip}`;
        },
        // 判断是否存在对应状态的配置
        getConfigByStatus(status) {
            status = status || RouteStatus.ACTIVE;
            const config = RouteStatus.fromValue(status);
            return !u.isEmpty(config) ? PATH_SUCCESS : PATH_UNAVAILABLE;
        },
        // 通过vpc状态获取文案
        getTextByStatus(status) {
            status = status || RouteStatus.ACTIVE;
            const config = RouteStatus.fromValue(status);
            return !u.isEmpty(config) ? RouteStatus.getTextFromValue(status) : '不可用';
        }
    };
    inited() {
        this.ipV6GatewayWhiteList();
        this.watch('accessOpt', value => {
            this.data.set('accessOpt', value);
        });
        this.loadAllRule();
    }
    loadAllRule() {
        let payload = {
            pageNo: 1,
            pageSize: 100000,
            vpcId: this.data.get('vpcId')
        };
        this.data.set('loadingAllData', true);
        return this.$http
            .rulePageList(payload, {'x-silent-codes': ['BadRequest']})
            .then(res => {
                this.data.set('allRouteList', res.routeRules);
            })
            .finally(() => {
                this.data.set('loadingAllData', false);
            });
    }
    attached() {
        this.data.set('table.loading', true);
        this.getRouteQuota();
        this.getVpcInfo();
        this.setTableDataSource();
    }
    setTableDataSource() {
        const detail = this.data.get('detail');
        if (detail?.routeRules) {
            this.checkGwRoute(detail.routeRules);
            this.data.set('routeId', detail.id);
            this.data.set('table.datasource', this.setDatasource(detail.routeRules));
            this.data.set('pager.total', detail.totalCount);
        }
        this.data.set('table.loading', false);
    }
    loadPage() {
        let payload = this.getPayload();
        payload = {...payload};
        this.data.set('payload', payload);
        this.data.set('table.loading', true);
        return this.$http.rulePageList(payload, {'x-silent-codes': ['BadRequest']}).then(res => {
            this.getRouteQuota();
            this.data.set('table.loading', false);
            this.checkGwRoute(res.routeRules);
            this.data.set('routeId', res.id);
            this.data.set('table.datasource', this.setDatasource(res.routeRules));
            this.data.set('pager.total', res.totalCount);
        });
    }

    refresh() {
        this.resetPayload();
        this.loadPage();
    }

    resetPayload() {
        this.data.set('sort', {});
        this.data.set('filters', {});
    }

    checkGwRoute(routeRules) {
        u.each(routeRules, rule => {
            if (rule.nexthopType === 'vpc2tgw') {
                rule.nexthop = rule.nexthopId;
            }
            rule.nexthop = [this.getNexthop(rule, 0) + rule.nexthop];
            rule.ids = [rule.id];
            rule.ruleIds = [rule.id];
            rule.pathTypes = [rule.pathType];
            rule.nexthopIds = [rule.nexthopId];
            rule.status = [rule.status];
            rule.effect = [rule.effect];
            // 多路由规则追加
            if (rule.multiRouteRuleVos && rule.multiRouteRuleVos.length > 0) {
                rule.multiRouteRuleVos.map((item, index) => {
                    rule.nexthop.push(this.getNexthop(item, index + 1) + item.nexthop);
                    rule.ids.push(item.id);
                    rule.ruleIds.push(item.id);
                    rule.status.push(item.status);
                    rule.pathTypes.push(item.pathType);
                    rule.nexthopIds.push(item.nexthopId);
                    rule.effect.push(item.effect);
                });
            } // 专线网关负载均衡与主备的情况
        });
    }

    switchGw(item) {
        let index = -1;
        u.each(item.pathTypes || [], (type, i) => {
            if (type === PathType.ACTIVE) {
                index = i;
            }
        });
        if (index > -1 && item.ids[index]) {
            this.$http.ruleSwitch({activeRuleId: item.ids[index]}).then(() => this.loadPage());
        }
    }

    onCreate() {
        let allRouteList = this.data.get('allRouteList');
        const detail = this.data.get('detail');
        this.checkGwRoute(allRouteList);
        let array = this.setDatasource(allRouteList);
        let createDialog = new CreateRule({
            data: {
                routeId: this.data.get('routeId'),
                routeRules: array,
                vpcInfo: this.data.get('vpcInfo'),
                vpcId: this.data.get('vpcId'),
                quota: this.data.get('quota.free'),
                v6Quota: this.data.get('v6Quota.free'),
                vrf: detail?.vrf
            }
        });
        createDialog.attach(document.body);
        createDialog.on('confirmed', () => {
            this.loadPage();
            this.loadAllRule();
        });
    }

    editRoute(editRule) {
        let createDialog = new CreateRule({
            data: {
                routeId: this.data.get('routeId'),
                routeRules: this.data.get('table.datasource'),
                vpcInfo: this.data.get('vpcInfo'),
                vpcId: this.data.get('vpcId'),
                editRule
            }
        });
        createDialog.on('confirmed', () => this.loadPage());
        createDialog.attach(document.body);
    }

    changePathMode(row, {value}) {
        const payload = this.getChangeModePayload(row, value);
        this.$http.ruleUpdate(payload).then(() => this.loadPage());
    }

    getChangeModePayload(row, value) {
        const payload = {};
        payload.preemptiveMode = value;
        payload.nextHopList = row.pathTypes.map((item, index) => {
            let extra = {};
            if (item === PathType.ACTIVE) {
                extra = {
                    name: '主路径',
                    pathType: PathType.ACTIVE
                };
            } else {
                extra = {
                    name: '备路径',
                    pathType: PathType.STANDBY
                };
            }
            return {
                ...extra,
                nexthopId: row.nexthopIds[index],
                nexthopType: RouteType.GW
            };
        });
        if (row.sourceName === '') {
            row.sourceExtra = row.sourceAddress;
        }
        return {
            ...row,
            ruleIds: row.ids,
            vpcId: this.data.get('vpcId'),
            ...payload
        };
    }

    getNexthop(item, index) {
        let nexthop = '';
        if (item.pathType === PathType.ECMP) {
            nexthop = '路径' + (index + 1) + '：';
        } else if (item.pathType === PathType.ACTIVE) {
            nexthop = '主路径：';
        } else if (item.pathType === PathType.STANDBY) {
            nexthop = '备路径：';
        }
        return nexthop;
    }

    deleteRoute(item) {
        this.data.set('deleteVisible', true);
        this.data.set('deleteRowDatasource', [item]);
    }

    getPayload() {
        const {pager, filters, vpcId, sort, searchbox, ipVersion, detail} = this.data.get('');
        let {keywordType, keyword} = searchbox;
        let payload: Record<string, any> = {
            pageNo: pager.page,
            pageSize: pager.pageSize,
            vpcId
        };
        // 自定义路由表需要传routeTableUuid
        if (detail?.vrf !== 0) {
            payload.routeTableUuid = detail?.routeTableUuid;
        }
        ipVersion && (payload.ipVersion = ipVersion);
        keyword && ((payload.keyword = keyword), (payload.keywordType = keywordType));
        filters?.nexthopType && (payload.nexthopType = filters.nexthopType);
        return {...payload, ...sort};
    }

    getVpcInfo() {
        let vpcId = this.data.get('vpcId') || '';
        return this.$http.vpcInfo({vpcIds: [vpcId]}, kXhrOptions.silence).then(data => {
            let vpc = null;
            if (!vpcId) {
                vpc = u.find(data, item => !u.isEmpty(item)) || {};
                this.data.set('vpcId', vpc.vpcId);
            } else {
                vpc = data[vpcId] || {};
            }
            vpc.name = getVpcName(vpc.name);
            this.data.set('vpcInfo', vpc);
            this.data.set('routeRelay.checked', vpc.relay);
        });
    }

    getRouteQuota() {
        let all = [this.getIpv4Quota(), this.getIpv6Quota()];
        return Promise.all(all).then(res => {
            this.data.set('quota', res[0]);
            this.data.set('v6Quota', res[1]);
        });
    }

    getIpv4Quota() {
        return this.$http.ruleQuota({vpcId: this.data.get('vpcId')}, {'x-silent-codes': ['InstanceNotFound']});
    }

    getIpv6Quota() {
        return this.$http.getRouteV6Quota({vpcId: this.data.get('vpcId')}, {'x-silent-codes': ['InstanceNotFound']});
    }

    ipV6GatewayWhiteList() {
        let column = this.data.get('table.columns');
        u.each(column, (item, index) => {
            if (item.name === 'nexthopType' && !u.find(item.filter.options, list => list.value === 'ipv6gateway')) {
                item.filter.options.push({
                    text: 'IPv6公网网关',
                    value: 'ipv6gateway'
                });
                this.data.set(`table.columns[${index}]`, item);
            }
        });
    }

    beforeEdit(row) {
        this.data.set('instance.desc', row.description);
    }

    onEdit(item) {
        const payload = this.getUpdatePayload(item);
        return this.$http.ruleUpdate(payload).then(res => this.loadPage());
    }

    getUpdatePayload(row) {
        const instance = this.data.get('instance');
        const extraPayload = {};
        if (row.nexthopType === RouteType.GW && row.pathType !== PathType.NORMAL && row.nexthopIds.length > 1) {
            extraPayload.nextHopList = u.map(row.nexthopIds, (item, index) => {
                return {
                    name: row.nexthop[index],
                    nexthopId: item,
                    nexthopType: RouteType.GW,
                    pathType: row.pathTypes[index]
                };
            });
        }
        if (row.nexthopType === RouteType.ENI) {
            row.nexthopId = row.primaryId;
        }
        return {
            ...row,
            description: instance.desc,
            ruleIds: row.ids,
            vpcId: this.data.get('vpcId'),
            ...extraPayload
        };
    }

    editCancel(row, rowIndex, type) {
        this.ref(`instance${type}Edit${rowIndex}`).data.set('visible', false);
    }

    onNameInput(e, row, rowIndex, type) {
        let result;
        let editType = type.toLocaleLowerCase();
        if (type === 'Name') {
            result = e.value.length <= 64 && /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{0,64}$/.test(e.value);
        } else {
            result = e.value.length <= 200;
        }
        this.data.set(`instance.${editType}`, e.value);
        this.ref(`editBtn${type}${rowIndex}`).data.set('disabled', !result);
    }

    ipTypeChange({value}) {}

    // 改变页数
    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }

    // 改变每页显示个数
    onPagerSizeChange(e) {
        this.data.set('pager.pageSize', e.value.pageSize);
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    // 排序
    onSort(e) {
        this.data.set('sort', e.value);
        this.loadPage();
    }

    // 筛选
    onFilter(e) {
        let name = e.field.name;
        let value = e[name] ? e[name] : '';
        let filters = {
            nexthopType: value
        };
        this.data.set('filters', filters);
        this.loadPage();
    }

    isCanEdit(row) {
        return (
            row.nexthopType !== RouteType.SYSTEM &&
            row.nexthopType !== RouteType.CSN &&
            row.pathType !== PathType.ACTIVE &&
            row.pathType !== PathType.STANDBY &&
            row.nexthopType !== RouteType.IPV6 &&
            FLAG.NetworkAclOpt
        );
    }

    setDatasource(data) {
        return data.map(item => {
            if (item.nexthopType === RouteType.CSN) {
                item.notDelContent = '如需禁用该路由，请至CSN侧路由管理页面，关闭发布该路由。';
            }
            if (item.csnInfo === 'learned') {
                item.csnStatusInfo = 'CSN类型的路由，无需再发布至CSN实例。';
            }
            if (item.pathType === PathType.ACTIVE || item.pathType === PathType.STANDBY) {
                if (
                    (item.multiRouteRuleVos && item.multiRouteRuleVos[0]?.nexthopType === 'vpn') ||
                    item.nexthopType === 'vpn'
                ) {
                    item.reserveVpn = true;
                }
            }
            return item;
        });
    }

    publishRoute(isPublic, row) {
        let vpcInfo = this.data.get('vpcInfo');
        let payload = {
            ruleId: row.sysRuleId,
            csnId: vpcInfo.csnId,
            instanceId: vpcInfo.vpcId,
            sourceAddress: row.sourceAddress,
            destAddress: row.destinationAddress,
            region: window.$context.getCurrentRegionId(),
            routeType: 'sys',
            role: 'member'
        };
        row.ipVersion && (payload.ipVersion = row.ipVersion);
        if (isPublic) {
            this.$http.publishRoute(payload).then(() => this.loadPage());
        } else {
            this.$http
                .cancelPublishRoute({
                    ruleId: row.sysRuleId,
                    role: 'member',
                    region: window.$context.getCurrentRegionId()
                })
                .then(() => this.loadPage());
        }
    }

    onSearch() {
        this.data.set('pager.page', 1);
        return this.loadPage();
    }

    onDownload() {
        let routeTableUuid = this.data.get('routeTableUuid');
        window.open(`/api/network/v1/route/rule/download?routeTableUuid=${routeTableUuid}`);
    }

    typeSelect({value}) {
        this.data.set('ipVersion', value);
        this.loadPage();
    }

    handleShowMonitor(row) {
        const currentRegion = this.data.get('currentRegion');
        const dialog = new Monitor({
            data: {
                dcgwId: row.nexthopId || '',
                initWidth: 1040,
                title: '流量数据',
                currentRegion
            }
        });
        dialog.attach(document.body);
    }

    // 判断是否显示流量监控按钮
    checkShowMonitor(row) {
        // 专线网关多线路由类型下，选择负载均衡多线模式时，无论是否可用，都需要显示流量监控按钮，能查看历史时间段流量数据
        return row.nexthopType === RouteType.GW && row.pathType === PathType.ECMP;
    }
    handleCancel() {
        this.data.set('deleteVisible', false);
    }
    handleConfirm() {
        const item = this.data.get('deleteRowDatasource')[0];
        let payload = {
            routeRuleIds: item.ids || [item.id]
        };
        this.data.set('deleteLoading', true);
        this.$http
            .ruleDelete(payload)
            .then(() => {
                this.data.set('deleteVisible', false);
                this.loadPage();
                this.loadAllRule();
            })
            .finally(() => {
                this.data.set('deleteLoading', false);
            });
    }
}
export default Processor.autowireUnCheckCmpt(RouteTable);
