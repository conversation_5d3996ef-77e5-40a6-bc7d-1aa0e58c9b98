/* eslint-disable @typescript-eslint/member-ordering */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {OutlinedLeft, OutlinedEditingSquare} from '@baidu/sui-icon';

import {Notification, Input} from '@baidu/sui';
import {$flag as FLAG, kXhrOptions} from '@/pages/sanPages/utils/helper';
import {RouteTableStatus} from '@/pages/sanPages/common/enum';
import testID from '@/testId';
import RuleTable from './routeRule/ruleTable';
import '../../style/detail.less';

const {invokeSUI, invokeSUIBIZ, template, invokeComp, asComponent} = decorators;
const tpl = html`
    <div>
        <s-app-detail-page class="{{klass}}">
            <div class="route-detail-wrap">
                <h4>基本信息</h4>
                <div class="basic-item-wrap">
                    <div class="basic-item-box">
                        <div class="basic-item-key cell-title">名称：</div>
                        <div class="basic-item-value">
                            <span class="text-hidden cell-content" data-testid="${testID.route.detailName}"
                                >{{instance.name || '-'}}</span
                            >
                            <s-popover
                                s-if="instance.vrf !== 0"
                                s-ref="instanceNameEdit"
                                placement="right"
                                trigger="click"
                                class="edit-popover-class"
                                data-test-id="${testID.route.detailNameEdit}"
                            >
                                <div class="edit-wrap" slot="content">
                                    <s-input
                                        value="{=instanceName.value=}"
                                        data-test-id="${testID.route.detailNameEditInput}"
                                        width="320"
                                        placeholder="请输入名称"
                                        on-input="onNameInput($event, 'name')"
                                    />
                                    <div class="edit-tip">
                                        支持大小写字母，数字，中文和"-_/.”以字母或者中文开头，不超过65个字'
                                    </div>
                                    <s-button
                                        skin="primary"
                                        s-ref="editNameBtn"
                                        data-test-id="${testID.route.detailNameEditSub}"
                                        disabled="{{true}}"
                                        on-click="editConfirm('name')"
                                        >确定</s-button
                                    >
                                    <s-button on-click="editNameCancel('name')">取消</s-button>
                                </div>
                                <span class="blue-color">
                                    <outlined-editing-square
                                        color="#2468f2"
                                        s-if="flag.NetworkRouteOpt"
                                        on-click="editName('name')"
                                    />
                                </span>
                            </s-popover>
                        </div>
                    </div>
                    <div class="basic-item-box">
                        <div class="basic-item-key cell-title">ID：</div>
                        <div class="basic-item-value cell-content">
                            <span class="text-hidden">{{instance.id || instance.routeTableId}}</span>
                            <s-clip-board class="name-icon" text="{{instance.id || instance.routeTableId}}">
                                <s-icon name="copy" />
                            </s-clip-board>
                        </div>
                    </div>
                    <div class="basic-item-box">
                        <div class="basic-item-key cell-title">所在网络：</div>
                        <div class="basic-item-value cell-content">{{instance.vpcName || '-'}}</div>
                    </div>
                    <div class="basic-item-box">
                        <div class="basic-item-key cell-title">路由表类型：</div>
                        <div class="basic-item-value cell-content">{{routeType}}</div>
                    </div>
                    <div class="basic-item-box">
                        <div class="basic-item-key cell-title">描述：</div>
                        <span class="basic-item-value cell-content">{{instance.description | getDescription}}</span>
                        <s-popover
                            s-if="instance.vrf!==0"
                            s-ref="instanceDescEdit"
                            placement="right"
                            trigger="click"
                            class="edit-popover-class"
                        >
                            <div class="edit-wrap" slot="content">
                                <s-textarea
                                    value="{=instanceName.value=}"
                                    width="200"
                                    height="48"
                                    placeholder="请输入描述"
                                    on-input="onNameInput($event, 'description')"
                                />
                                <div class="edit-tip">描述不能超过200个字符</div>
                                <s-button
                                    skin="primary"
                                    s-ref="editDescBtn"
                                    disabled="{{true}}"
                                    on-click="editConfirm('description')"
                                    >确定</s-button
                                >
                                <s-button on-click="editNameCancel('description')">取消</s-button>
                            </div>
                            <span class="blue-color">
                                <outlined-editing-square
                                    color="#2468f2"
                                    s-if="flag.NetworkRouteOpt"
                                    on-click="editName('description')"
                                />
                            </span>
                        </s-popover>
                    </div>
                </div>
            </div>
            <div class="route-detail-divider"></div>
            <div class="route-detail-wrap">
                <h4>路由条目</h4>
                <rule-table
                    vpcId="{{vpcId}}"
                    detail="{{detail}}"
                    routeTableUuid="{{instance.routeTableUuid}}"
                    accessOpt="{{accessOpt}}"
                >
                </rule-table>
            </div>
        </s-app-detail-page>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
class RouteDetail extends Component {
    static components = {
        'icon-left': OutlinedLeft,
        'outlined-editing-square': OutlinedEditingSquare,
        'rule-table': RuleTable,
        's-textarea': Input.TextArea
    };
    initData() {
        return {
            flag: FLAG,
            klass: 'route-detail-box',
            route: {},
            accessOpt: {
                createRouteRule: {
                    disabled: false,
                    message: ''
                },
                updateRouteRule: {
                    disabled: false,
                    message: ''
                },
                deleteRouteRule: {
                    disabled: false,
                    message: ''
                }
            },
            instanceFound: true,
            urlQuery: getQueryParams()
            // loading: true
        };
    }

    filters = {
        statusClass(value: string) {
            return RouteTableStatus.fromValue(value).styleClass || '';
        },
        statusText(value: string) {
            return value ? RouteTableStatus.getTextFromValue(value) : '';
        },
        getDescription(value: string) {
            return value || '-';
        }
    };

    static computed = {
        routeType() {
            let routeType = '';
            const instance = this.data.get('instance');
            if (instance?.vrf === 0) {
                routeType = '系统';
            } else {
                routeType = '自定义';
            }
            return routeType || '-';
        }
    };

    inited() {
        const instance = this.data.get('context').instance;
        this.data.set('instance', instance);
        this.getIamQuery();
        this.data.set('detail', instance);
        this.data.set('vpcId', this.data.get('context').vpcId);
    }
    back() {
        location.hash = '#/vpc/route/list';
    }

    // 点击修改名称icon
    editName(type: 'name' | 'description') {
        let instance = this.data.get('instance');
        const value = type === 'name' ? instance.name : instance.description;
        this.data.set('instanceName.value', value);
        this.data.set('instanceName.error', false);
    }

    // 修改名称确认
    editConfirm(type: 'name' | 'description') {
        let instanceName = this.data.get('instanceName');
        let instance = this.data.get('instance');
        if (instanceName.error) {
            return;
        }
        const key = type === 'name' ? 'description' : 'name';
        this.$http
            .updateRouteName({
                [key]: instance[key],
                [type]: instanceName.value,
                id: instance.routeTableUuid
            })
            .then(() => {
                this.editNameCancel(type);
                this.data.get('context')?.updateName();
                Notification.success('修改成功!');
            });
    }

    // 输入名称
    onNameInput({value}, type: 'name' | 'description') {
        let result =
            type === 'name'
                ? value === '' || !/^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/.test(value)
                : value.length > 200;
        this.data.set('instanceName.error', result);
        this.data.set('instanceName.value', value);
        const refName = type === 'name' ? 'editNameBtn' : 'editDescBtn';
        this.ref(refName).data.set('disabled', result);
    }

    // 修改名称取消
    editNameCancel(type: 'name' | 'description') {
        if (type === 'name') {
            this.ref('editNameBtn').data.set('disabled', true);
            this.ref('instanceNameEdit').data.set('visible', false);
        } else {
            this.ref('editDescBtn').data.set('disabled', true);
            this.ref('instanceDescEdit').data.set('visible', false);
        }
    }

    onBack() {
        location.hash = '#/vpc/route/list';
    }
    getIamQuery() {
        const id = this.data.get('instance.id');
        const interfaceNames = ['createRouteRule', 'updateRouteRule', 'deleteRouteRule'];
        this.$http
            .getInterfaceIam({
                id,
                interfaceNames
            })
            .then(res => {
                const {requestId, masterAccount, permissionRespMap} = res;
                if (!requestId && !masterAccount) {
                    const accessOpt = {...this.data.get('accessOpt')};
                    const accessOptMapText = {
                        createRouteRule: '添加',
                        updateRouteRule: '编辑',
                        deleteRouteRule: '删除'
                    };
                    _.each(interfaceNames, item => {
                        if (!permissionRespMap[item].interfacePermission) {
                            accessOpt[item].disabled = true;
                            accessOpt[item].message = `您没有${accessOptMapText[item]}路由权限，请联系主用户添加。`;
                        }
                    });
                    this.data.set('accessOpt', accessOpt);
                }
            });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(RouteDetail));
