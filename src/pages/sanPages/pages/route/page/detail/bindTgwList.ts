import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {Alert, Notification} from '@baidu/sui';
import {OutlinedRefresh, OutlinedDownload, OutlinedPlus, OutlinedEditingSquare} from '@baidu/sui-icon';

import BindDialog from './bindTgwDialog';
import {$flag as FLAG, kXhrOptions} from '@/pages/sanPages/utils/helper';
import {RouteType, PathType} from '@/pages/sanPages/common/enum';
import Confirm from '@/pages/sanPages/components/confirm';
import testID from '@/testId';
import './index.less';

const {asComponent, invokeSUI, invokeSUIBIZ, invokeComp, template} = decorators;
const tpl = html`
    <div class="route-bind-wrapper">
        <h4 data-testid="${testID.route.tgwName}">TGW管理</h4>
        <s-biz-page class="{{klass}}">
            <div class="list-page-tb-left-toolbar" slot="tb-left">
                <s-tooltip trigger="{{isDisableBind ? 'hover' : ''}}">
                    <div slot="content">{{disableBindTip}}</div>
                    <s-button
                        data-test-id="${testID.route.detailBindTgw}"
                        skin="primary"
                        on-click="onCreate"
                        disabled="{{isDisableBind}}"
                        ><outlined-plus />绑定TGW</s-button
                    >
                </s-tooltip>
            </div>
            <s-alert skin="warning" slot="tip" s-if="{{enableCreate.disable}}">
                <!--bca-disable-next-line-->
                {{tip | raw}}
            </s-alert>
            <div slot="tb-right">
                <!--<s-search
                    width="{{230}}"
                    value="{=searchbox.keyword=}"
                    placeholder="{{searchbox.placeholder}}"
                    on-search="onSearch"
                >
                    <s-select
                        slot="options"
                        width="{{130}}"
                        value="{=searchbox.keywordType=}"
                        on-change="onSearchboxChange"
                    >
                        <s-select-option
                            s-for="item in searchbox.keywordTypes"
                            value="{{item.value}}"
                            label="{{item.text}}"
                        />
                    </s-select>
                </s-search>-->
                <s-button on-click="refresh" class="s-icon-button"><outlined-refresh class="icon-class" /></s-button>
            </div>
            <s-table
                s-ref="table"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
            >
                <div slot="empty">
                    <table-empty
                        dataTestId="${testID.route.detailBindTgwTableEmpty}"
                        actionAuth="{{disableOptions}}"
                        desc="{{'暂无已绑定TGW'}}"
                        actionText="立即绑定"
                        on-click="onCreate"
                    />
                </div>
                <div slot="error">
                    啊呀，出错了？
                    <a href="javascript:;" on-click="loadPage">重新加载</a>
                </div>
                <div slot="c-name">
                    <span>{{row.extraInfo.tgwName || '-'}}</span>
                    <div><a on-click="handleLinkToCsn(row)" class="tgw-link">{{row.resourceId}}</a></div>
                </div>
                <div slot="c-opt">
                    <s-tooltip content="{{row | systemDisableTip}}">
                        <s-button
                            skin="stringfy"
                            on-click="handleUnbind(row, rowIndex)"
                            disabled="{{row | systemDisableTip}}"
                            data-test-id="${testID.route.detailUnbindTgw}{{rowIndex}}"
                            >解绑</s-button
                        >
                    </s-tooltip>
                </div>
            </s-table>
            <s-pagination
                s-if="{{pager.total > 10}}"
                slot="footer"
                layout="{{'total, pageSize, pager, go'}}"
                pageSize="{{pager.size}}"
                total="{{pager.total}}"
                page="{{pager.page}}"
                on-pagerChange="onPagerChange"
                on-pagerSizeChange="onPagerSizeChange"
            />
        </s-biz-page>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeComp('@table-empty')
class BindTGW extends Component {
    static components = {
        'outlined-refresh': OutlinedRefresh,
        'outlined-download': OutlinedDownload,
        'outlined-plus': OutlinedPlus,
        'outlined-editing-square': OutlinedEditingSquare,
        's-alert': Alert
    };
    initData() {
        return {
            flag: FLAG,
            klass: ['route-bind-tgw'],
            table: {
                loading: false,
                datasource: [],
                columns: [
                    {name: 'name', width: 300, label: 'TGW名称/ID'},
                    {name: 'opt', width: 100, label: '操作'}
                ]
            },
            pager: {
                page: 1,
                total: 10,
                pageSize: 10
            },
            PathType,
            RouteType,
            vpcInfo: '',
            searchbox: {
                keyword: '',
                placeholder: '请输入TGW名称进行搜索',
                keywordType: 'name',
                keywordTypes: [{value: 'name', text: 'TGW名称'}]
            },
            tgwList: [],
            isDisableBind: true,
            disableBindTip: ''
        };
    }
    static computed = {
        disableOptions() {
            const isDisableBind = this.data.get('isDisableBind');
            const disableBindTip = this.data.get('disableBindTip');
            return {disable: isDisableBind, message: disableBindTip};
        }
    };
    static filters = {
        systemDisableTip(row: any) {
            let tip = '';
            const {vrf} = row;
            if (vrf === 0) {
                tip = '系统路由表不支持解绑';
            }
            return tip;
        }
    };
    inited() {
        if (!window.$storage.get('csnSts')) {
            this.data.set('isDisableBind', true);
            this.data.set('disableBindTip', '您没有csn服务权限，请联系主用户添加产品权限。');
        } else {
            this.loadCsnTgw();
        }
        this.loadPage();
    }
    loadPage() {
        this.data.set('table.loading', true);
        const keyword = this.data.get('context')?.detail?.id;
        const vpcUuid = this.data.get('context').vpcId;
        const payload = {
            keyword,
            keywordType: 'routeTableId',
            pageNo: 1,
            pageSize: 10,
            vpcUuid
        };
        this.$http
            .routeTableList(payload, {'x-silent-codes': ['BadRequest']})
            .then(res => {
                if (res?.result) {
                    const data = res.result?.[0];
                    if (data) {
                        // 如果未开通csn服务权限则不再判断
                        if (window.$storage.get('csnSts')) {
                            // 系统路由表
                            if (data?.vrf === 0) {
                                this.data.set('isDisableBind', true);
                                this.data.set('disableBindTip', '系统路由表不支持绑定TGW');
                            } else if (data?.bindInfos?.length) {
                                this.data.set('isDisableBind', true);
                                this.data.set('disableBindTip', '当前路由表已绑定TGW');
                            } else if (!data?.attachCsn) {
                                this.data.set('isDisableBind', true);
                                this.data.set('disableBindTip', '自定义路由表所在的VPC暂未加载到CSN中，无法绑定TGW。');
                            } else {
                                this.data.set('isDisableBind', false);
                                this.data.set('disableBindTip', '');
                            }
                        }
                        const formatedRes = u.map(data?.bindInfos || [], item => {
                            return {...item, vrf: data.vrf};
                        });
                        this.data.set('table.datasource', formatedRes);
                    }
                }
            })
            .finally(() => {
                this.data.set('table.loading', false);
            });
    }
    loadCsnTgw() {
        const vpcId = this.data.get('context').vpcId;
        this.$http.getCsnTgwList({vpcId}).then(res => {
            if (res?.length) {
                const formateData = u.map(res, item => ({label: `${item.name}（${item.id}）`, value: item.id}));
                this.data.set('tgwList', formateData);
            }
        });
    }
    handleLinkToCsn(row: any) {
        const {
            extraInfo: {csnId, tgwId}
        } = row;
        window.open(`/csn/#/csn/tgwinstance?csnId=${csnId}&current=tgwinstance&tgwId=${tgwId}`);
    }
    handleUnbind(row: any) {
        const confirm = new Confirm({
            data: {
                open: true,
                content: '请确定是否要解绑？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            const {resourceId, resourceType} = row;
            const routeTableId = this.data.get('context')?.detail?.id;
            const payload = {
                resourceId,
                resourceType,
                routeTableId
            };
            this.$http.unBindResource(payload).then(res => {
                this.loadPage();
                Notification.success('解绑成功！');
            });
        });
    }
    refresh() {
        this.loadPage();
    }
    onCreate() {
        const tgwList = this.data.get('tgwList');
        const detail = this.data.get('context').detail;
        const createDialog = new BindDialog({
            data: {
                action: 'bindTgw',
                tgwList,
                detail
            }
        });
        createDialog.on('confirmed', () => {
            this.loadPage();
        });
        createDialog.attach(document.body);
    }
    // 改变页数
    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }
    // 改变每页显示个数
    onPagerSizeChange(e) {
        this.data.set('pager.pageSize', e.value.pageSize);
        this.data.set('pager.page', 1);
        this.loadPage();
    }
    onSearch() {
        this.data.set('pager.page', 1);
        // return this.loadPage();
    }
}
export default San2React(Processor.autowireUnCheckCmpt(BindTGW));
