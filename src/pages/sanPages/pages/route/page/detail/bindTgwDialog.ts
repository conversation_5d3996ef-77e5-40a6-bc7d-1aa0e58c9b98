import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {Notification, Dialog, Tooltip} from '@baidu/sui';
import testID from '@/testId';
import './index.less';

interface BindParams {
    resourceId: string;
    routeTableId: string;
    resourceType: 'CSN_TGW';
}
const {template, invokeSUI, invokeSUIBIZ, asComponent} = decorators;

const tpl = html`
    <template>
        <s-dialog class="bind-dialog-wrapper" open="{=open=}" width="{{520}}" title="{{title}}">
            <s-form s-ref="form" data="{=formData=}" rules="{{rules}}">
                <s-form-item prop="name" label="路由表名称："> {{detail.name}} </s-form-item>
                <s-form-item prop="id" label="路由表ID："> {{detail.routeTableId || detail.id}} </s-form-item>
                <s-form-item prop="resourceId" label="{{instanceLabel}}">
                    <s-select
                        s-if="tgwList.length > 1"
                        width="240"
                        placeholder="{{placeholder}}"
                        value="{=formData.resourceId=}"
                        datasource="{{tgwList}}"
                    />
                    <span s-else>{{tgwList[0].value}}</span>
                </s-form-item>
            </s-form>
            <div slot="footer">
                <s-button on-click="handleClose">取消</s-button>
                <s-button
                    data-test-id="${testID.route.detailBindTgwSure}"
                    loading="{{loading}}"
                    skin="primary"
                    disabled="{{disableSub}}"
                    on-click="handleConfirm"
                    >确定</s-button
                >
            </div>
        </s-dialog>
    </template>
`;
@template(tpl)
@invokeSUI
@invokeSUIBIZ
class bindTgwDialog extends Component {
    static components = {
        's-dialog': Dialog,
        's-tooltip': Tooltip
    };
    static computed = {
        leftTitle() {
            return `可选项（${0}/10）`;
        },
        rightTitle() {
            const selectedList = this.data.get('selectedList');
            return `已选项（${selectedList.length}）`;
        },
        title() {
            const action = this.data.get('action');
            const titleMapText = {
                bindTgw: '绑定TGW'
            };
            return titleMapText[action];
        },
        instanceLabel() {
            const action = this.data.get('action');
            const labelMapText = {
                bindTgw: 'TGW实例：'
            };
            return labelMapText[action];
        },
        placeholder() {
            const action = this.data.get('action');
            const placeholderTextMap = {
                bindTgw: '请选择TGW实例'
            };
            return placeholderTextMap[action];
        }
    };
    initData() {
        return {
            open: true,
            loading: false,
            selectedList: [],
            action: '',
            tgwList: [],
            detail: {},
            formData: {
                resourceId: ''
            },
            rules: {
                resourceId: [{required: true, message: '请选择'}]
            }
        };
    }
    inited() {
        const {action, tgwList, detail} = this.data.get('');
        this.data.set('action', action);
        this.data.set('tgwList', tgwList);
        this.data.set('formData.resourceId', tgwList[0]?.value);
        this.data.set('detail', detail);
    }
    handleClose() {
        this.data.set('open', false);
    }
    async handleConfirm() {
        await (this.ref('form') as any).validateFields();
        const {action, formData, detail} = this.data.get('');
        const params: BindParams = {
            ...formData,
            routeTableId: detail.routeTableId || detail.id,
            resourceType: action === 'bindTgw' ? 'CSN_TGW' : ''
        };
        this.data.set('loading', true);
        this.$http
            .bindResource(params)
            .then(res => {
                this.fire('confirmed');
                this.data.set('open', false);
                Notification.success('绑定TGW成功！');
            })
            .finally(() => {
                this.data.set('loading', false);
            });
    }
}
export default Processor.autowireUnCheckCmpt(bindTgwDialog);
