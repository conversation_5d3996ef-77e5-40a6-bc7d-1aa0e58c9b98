/* eslint-disable prettier/prettier */
// 自定义路由表时一期做了没上，后续可能还会上
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {Alert} from '@baidu/sui';
import {OutlinedRefresh, OutlinedDownload, OutlinedPlus, OutlinedEditingSquare} from '@baidu/sui-icon';

import BindDialog from './bindTgwDialog';
import {getVpcName} from '@/pages/sanPages/utils/common';
import {$flag as FLAG, kXhrOptions} from '@/pages/sanPages/utils/helper';
import {RouteType, PathType} from '@/pages/sanPages/common/enum';

const {asComponent, invokeSUI, invokeSUIBIZ, invokeComp, template} = decorators;
const tpl = html`
    <div class="route-bind-wrapper">
        <h4>子网管理</h4>
        <s-biz-page class="{{klass}}">
            <div class="list-page-tb-left-toolbar" slot="tb-left">
                <s-tooltip
                    trigger="{{accessOpt.createRouteRule.disabled || enableCreate.disable || loadingAllData ? 'hover' : ''}}"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{accessOpt.createRouteRule.message ? accessOpt.createRouteRule.message : enableCreate.message |
                        raw}}
                    </div>
                    <s-button
                        skin="primary"
                        on-click="onCreate"
                        disabled="{{accessOpt.createRouteRule.disabled ? true : (enableCreate.disable || loadingAllData)}}"
                        ><outlined-plus />绑定子网</s-button
                    >
                </s-tooltip>
            </div>
            <s-alert skin="warning" slot="tip" s-if="{{enableCreate.disable}}">
                <!--bca-disable-next-line-->
                {{tip | raw}}
            </s-alert>
            <div slot="tb-right">
                <s-select
                    class="ip-select"
                    value="{{ipVersion}}"
                    width="{{100}}"
                    datasource="{{ipVersionList}}"
                    on-change="typeSelect"
                >
                </s-select>
                <s-search
                    width="{{230}}"
                    value="{=searchbox.keyword=}"
                    placeholder="{{searchbox.placeholder}}"
                    on-search="onSearch"
                >
                    <s-select
                        slot="options"
                        width="{{130}}"
                        value="{=searchbox.keywordType=}"
                        on-change="onSearchboxChange"
                    >
                        <s-select-option
                            s-for="item in searchbox.keywordTypes"
                            value="{{item.value}}"
                            label="{{item.text}}"
                        />
                    </s-select>
                </s-search>
                <s-button on-click="refresh" class="s-icon-button"><outlined-refresh class="icon-class" /></s-button>
                <s-button on-click="onDownload" class="s-icon-button"
                    ><outlined-download class="icon-class"
                /></s-button>
            </div>
            <s-table
                s-ref="table"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
            >
                <div slot="empty">
                    <table-empty
                        actionAuth="{{accessOpt.createRouteRule}}"
                        desc="{{'暂无子网'}}"
                        actionText="立即添加"
                        on-click="onCreate"
                    />
                </div>
                <div slot="error">
                    啊呀，出错了？
                    <a href="javascript:;" on-click="loadPage">重新加载</a>
                </div>
                <div slot="c-name">
                    <a></a>
                    <div>{{row.name}}</div>
                </div>
                <div slot="c-opt">
                    <s-popover
                        content="VPN为备路径模式，不支持主备切换"
                        trigger="{{row.reserveVpn ? 'hover' : 'null'}}"
                    >
                        <s-button skin="stringfy" on-click="switchGw(row, rowIndex)" disabled="{{row.reserveVpn}}"
                            >解绑</s-button
                        >
                    </s-popover>
                </div>
            </s-table>
            <s-pagination
                s-if="{{pager.total > 10}}"
                slot="footer"
                layout="{{'total, pageSize, pager, go'}}"
                pageSize="{{pager.size}}"
                total="{{pager.total}}"
                page="{{pager.page}}"
                on-pagerChange="onPagerChange"
                on-pagerSizeChange="onPagerSizeChange"
            />
        </s-biz-page>
    </div>
`;

@asComponent('@route-bind-subnet')
@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeComp('@table-empty')
class BindSubnet extends Component {
    static components = {
        'outlined-refresh': OutlinedRefresh,
        'outlined-download': OutlinedDownload,
        'outlined-plus': OutlinedPlus,
        'outlined-editing-square': OutlinedEditingSquare,
        's-alert': Alert
    };
    initData() {
        return {
            flag: FLAG,
            klass: ['route-bind-subnet'],
            table: {
                loading: false,
                datasource: [{name: 'test'}],
                columns: [
                    {name: 'name', minWidth: 280, label: '子网名称/ID'},
                    {name: 'ipv4', minWidth: 200, label: 'IPv4网段'},
                    {name: 'ipv6', minWidth: 220, label: 'IPv6网段'},
                    {name: 'zone', minWidth: 180, label: '所在可用区'},
                    {name: 'opt', minWidth: 160, label: '操作'}
                ]
            },
            pager: {
                page: 1,
                total: 10,
                pageSize: 10
            },
            routeRelay: {
                disable: false,
                checked: false
            },
            quota: {
                free: 10,
                total: 0
            },
            PathType,
            RouteType,
            vpcInfo: '',
            searchbox: {
                keyword: '',
                placeholder: '请输入子网名称进行搜索',
                keywordType: 'name',
                keywordTypes: [{value: 'name', text: '子网名称'}]
            },
            ipVersionList: [
                {value: '', text: '全部网段'},
                {value: '4', text: 'IPv4'},
                {value: '6', text: 'IPv6'}
            ],
            ipVersion: ''
        };
    }

    static computed = {};

    static filters = {};
    inited() {
        this.loadPage();
    }
    loadAllRule() {
        let payload = {
            pageNo: 1,
            pageSize: 100000,
            vpcId: this.data.get('vpcId')
        };
        this.data.set('loadingAllData', true);
        return this.$http
            .rulePageList(payload, {'x-silent': true})
            .then(res => {
                this.data.set('allRouteList', res.routeRules);
            })
            .finally(() => {
                this.data.set('loadingAllData', false);
            });
    }
    attached() {
        this.getVpcInfo();
    }

    loadPage() {
        this.data.set('table.loading', true);
        return this.$http.rulePageList({}).then(res => {
            this.data.set('table.loading', false);
            this.data.set('routeId', res.id);
            this.data.set('pager.total', res.totalCount);
        });
    }

    refresh() {
        this.resetPayload();
        this.loadPage();
    }

    resetPayload() {
        this.data.set('sort', {});
        this.data.set('filters', {});
    }

    onCreate() {
        let createDialog = new BindDialog({
            data: {
                action: 'bindSubnet'
            }
        });
        createDialog.on('confirmed', () => {
            this.loadPage();
        });
        createDialog.attach(document.body);
    }

    getVpcInfo() {
        let vpcId = this.data.get('vpcId') || '';
        return this.$http.vpcInfo({vpcIds: [vpcId]}, kXhrOptions.silence).then(data => {
            let vpc = null;
            if (!vpcId) {
                vpc = u.find(data, item => !u.isEmpty(item)) || {};
                this.data.set('vpcId', vpc.vpcId);
            } else {
                vpc = data[vpcId] || {};
            }
            vpc.name = getVpcName(vpc.name);
            this.data.set('vpcInfo', vpc);
            this.data.set('routeRelay.checked', vpc.relay);
        });
    }

    // 改变页数
    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }

    // 改变每页显示个数
    onPagerSizeChange(e) {
        this.data.set('pager.pageSize', e.value.pageSize);
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    onSearch() {
        this.data.set('pager.page', 1);
        return this.loadPage();
    }

    onDownload() {
        let routeTableUuid = this.data.get('routeTableUuid');
        window.open(`/api/network/v1/route/rule/download?routeTableUuid=${routeTableUuid}`);
    }

    typeSelect({value}) {
        this.data.set('ipVersion', value);
        this.loadPage();
    }
}
export default Processor.autowireUnCheckCmpt(BindSubnet);
