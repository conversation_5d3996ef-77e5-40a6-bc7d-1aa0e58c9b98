import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import u from 'lodash';
import {Component} from 'san';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import testID from '@/testId';
import '../../style/create.less';

const {asPage, invokeSUI, invokeSUIBIZ, template} = decorators;
const validateRules = (self: Record<string, any>) => {
    return {
        name: [
            {required: true, message: '名称必填'},
            {
                pattern: /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/,
                message: '大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65'
            }
        ],
        vpcId: [{required: true, message: '请选择'}]
    };
};
const tpl = html`
    <template>
        <s-app-create-page
            class="vpc-route-create"
            backTo="{{pageNav.backUrl}}"
            backToLabel="{{pageNav.backLabel}}"
            pageTitle="{{pageNav.title}}"
        >
            <s-form rules="{{rules}}" s-ref="form" data="{=formData=}" label-align="left">
                <div class="form-part-wrap">
                    <s-alert skin="warning"
                        >温馨提示：云上产品可关联默认VPC及默认路由表，如默认VPC及默认路由表无法满足您的网段规划，您可在此创建自定义路由表，自定义路由表通过在路由表的左侧导航栏中进行“绑定TGW”操作，可控制TGW进入VPC的流量。</s-alert
                    >
                    <h4 class="title_vpc">配置信息</h4>
                    <s-form-item
                        label="路由表名称："
                        prop="name"
                        help="大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65"
                    >
                        <s-input value="{=formData.name=}" width="400" data-test-id="${testID.route.createNameInput}" />
                    </s-form-item>
                    <s-form-item label="所在网络：" prop="vpcId">
                        <s-select width="{{395}}" loading="{{vpcLoading}}" value="{=formData.vpcId=}">
                            <s-select-option s-for="item in vpcList" value="{{item.value}}" label="{{item.text}}">
                                <s-tooltip>
                                    <div slot="content">
                                        <!--bca-disable-next-line-->
                                        {{item.text | raw}}
                                    </div>
                                    <div>{{item.text}}</div>
                                </s-tooltip>
                            </s-select-option>
                        </s-select>
                    </s-form-item>
                    <s-form-item label="描述：" prop="description">
                        <s-input-text-area
                            maxLength="200"
                            disabled="{{type === 'editIpv6'}}"
                            value="{=formData.description=}"
                            width="{{401}}"
                            height="{{60}}"
                        />
                    </s-form-item>
                </div>
            </s-form>
            <div slot="pageFooter" class="buybucket">
                <div class="buybucket-container">
                    <s-tooltip trigger="{{quotaDisable || supportDisable ? 'hover' : ''}}">
                        <div slot="content">
                            <!--bca-disable-next-line-->
                            {{(quotaDisableTip || supportDisableTip) | raw}}
                        </div>
                        <s-button
                            size="large"
                            on-click="handleSubmit"
                            skin="primary"
                            disabled="{{quotaDisable || supportDisable}}"
                            loading="{{loading}}"
                            data-test-id="${testID.route.createSubmit}"
                        >
                            确定
                        </s-button>
                    </s-tooltip>
                    <s-button size="large" on-click="redirectToList"> 取消 </s-button>
                </div>
            </div>
        </s-app-create-page>
    </template>
`;
@template(tpl)
@invokeSUI
@invokeSUIBIZ
class CreateRoute extends Component {
    initData() {
        return {
            pageNav: {
                title: '创建路由表',
                backUrl: '/network/#/vpc/route/list',
                backLabel: '返回路由表列表'
            },
            rules: validateRules(this),
            formData: {
                name: '',
                vpcId: '',
                description: ''
            },
            loading: false,
            vpcLoading: false,
            quotaDisable: false,
            quotaDisableTip: '',
            supportDisable: false,
            supportDisableTip: '',
            vpcList: [],
            FLAG
        };
    }

    inited() {
        this.loadVpcList();
    }

    loadVpcList() {
        this.data.set('vpcLoading', true);
        return this.$http
            .vpcList()
            .then(data => {
                const vpcList = u.map(data, item => ({
                    text: `${item.name}（${item.cidr}）${item.ipv6Cidr ? `(${item.ipv6Cidr})` : ''}`,
                    value: item.vpcId
                }));
                this.data.set('vpcList', vpcList);
                const vpcId = window.$storage.get('vpcId');
                this.data.set('formData.vpcId', vpcId || vpcList[0].value);
            })
            .finally(() => {
                this.data.set('vpcLoading', false);
            });
    }

    attached() {
        this.watchQueue();
    }

    watchQueue() {
        this.watch('formData.vpcId', vpcId => {
            this.vpcInfoChange(vpcId);
            this.getCustomVpcDetail(vpcId);
        });
    }

    async handleSubmit() {
        await this.ref('form')?.validateFields();
        this.data.set('loading', true);
        const payload = this.data.get('formData');
        this.$http
            .createCustomeRoute(payload)
            .then(res => {
                if (res?.routeTableId) {
                    this.redirectToList();
                }
            })
            .finally(() => {
                this.data.set('loading', false);
            });
    }

    vpcInfoChange(value: string) {
        this.$http.getCustomRouteQuota({vpcUuid: value}).then(res => {
            if (res?.free <= 0) {
                this.data.set('quotaDisable', true);
                if (FLAG.NetworkSupportXS) {
                    this.data.set('quotaDisableTip', '该VPC自定义路由表已达到配额上限');
                } else {
                    this.data.set(
                        'quotaDisableTip',
                        `该VPC自定义路由表已达到配额上限，如需更多配额，请<a href="/quota_center/#/quota/apply/create?serviceType=ROUTE&region=${window?.$context?.getCurrentRegionId()}&cloudCenterQuotaName=RouteTableQuotaPerVpcQuota" target="_blank">去申请配额</a>`
                    );
                }
            } else {
                this.data.set('quotaDisable', false);
                this.data.set('quotaDisableTip', '');
            }
        });
    }

    getCustomVpcDetail(vpcId: string) {
        this.$http.getCustomVpcDetail({vpcId}).then(res => {
            if (res?.supportCustomRouteTable) {
                this.data.set('supportDisable', false);
                this.data.set('supportDisableTip', '');
            } else {
                this.data.set('supportDisable', true);
                this.data.set(
                    'supportDisableTip',
                    '普三机型（通用型g3、计算型c3、密集计算型ic3、内存型m3）不支持自定义路由表，如需使用，请更换其他机型。'
                );
            }
        });
    }

    redirectToList() {
        location.hash = '#/vpc/route/list';
    }

    onRegionChange() {
        location.hash = '#/vpc/route/list';
    }
}
export default San2React(Processor.autowireUnCheckCmpt(CreateRoute));
