/**
 * @file src/bcc/security/rule.js ~ 2014/11/27 16:25:58
 * <AUTHOR>
 * */
import u from 'lodash';
import {SecurityIpVersion as IpVersion} from '@/pages/sanPages/common/enum';

const kALL = 'all';
const kALLPORT = '1-65535';
const kUSER = 'user';
const kICMP = 'icmp';
export default class Rule {
    [prop: string]: any;
    /**
     * 规则对象
     *
     * @class Rule
     * @param {string} name 协议名称.
     * @param {string} protocol 协议类型.
     * @param {string} portRange 端口的范围.
     * @param {string} ethertype 规则类型.
     * @param {string} localPortRange 入站目的端口，出站源端口.
     * @param {string} ksource 自定义source
     */
    constructor(
        name,
        protocol,
        portRange,
        ethertype,
        localPortRange?: string,
        action?: string,
        priority?: string,
        direction?: string,
        ksource?: string
    ) {
        this.name = name;
        this.remark = name;
        this.protocol = protocol;
        this.portRange = u.trim(portRange);

        (this.action = action),
            (this.priority = priority),
            (this.localPortRange = localPortRange),
            (this.direction = direction),
            /**
             * 安全组的ID
             * @type {string}
             */
            (this.remoteGroupId = null);
        this.remoteGroupShortId = null;
        this.remoteGroupName = null;
        /**
         * @type {string}
         */
        this.remoteIP = ksource || kALL;
        this.ethertype = ethertype || IpVersion.IPV4;
        this.localIp = kALL;
        /**
         * 用来标识设置的是用户ip还是安全组id，根据这个字段的值
         * 来决定向后端提交 remoteIP 还是 remoteGroupId 字段
         * 如果是 source === 'user'，提交 remoteIP，忽略 remoteGroupId
         * 如果是 source === 'system'，提交 remoteGroupId，忽略 remoteIP
         * @type {string}
         */
        this.source = kUSER;
        this.ksource = ksource; // 用来兼容自定义source
        this.key = this.getKey();
    }

    /**
     * 判断两条规则是否相同.
     *
     * @param {Rule} rule 需要检测的规则.
     * @return {boolean}
     */
    equal(rule) {
        var x =
            this.protocol === rule.protocol &&
            this.source === rule.source &&
            this.action === rule.action &&
            this.priority === rule.priority &&
            this.ethertype === rule.ethertype;
        if (x && this.protocol !== kICMP) {
            x = x && this.portRange === u.trim(rule.portRange);
        }
        if (x && this.protocol !== kICMP) {
            x = x && this.localPortRange === rule.localPortRange;
        }
        if (x && this.source === kUSER) {
            // 普通IP查重都加上/32
            var remoteIP = u.clone(this.remoteIP);
            var rRemoteIP = u.clone(rule.remoteIP);
            var suffix = rule.ethertype === IpVersion.IPV6 ? '/128' : '/32';
            if (remoteIP !== kALL && remoteIP !== '::/0' && remoteIP !== '0:0:0:0/0' && remoteIP.indexOf('/') < 0) {
                remoteIP += suffix;
            }
            if (rRemoteIP !== kALL && rRemoteIP !== '::/0' && rRemoteIP !== '0:0:0:0/0' && rRemoteIP.indexOf('/') < 0) {
                rRemoteIP += suffix;
            }
            x = x && remoteIP === rRemoteIP;
        } else if (x && this.source !== kUSER) {
            x = x && this.ipSet === rule.ipSet;
        }
        if (x) {
            // 普通IP查重都加上/32
            var localIp = u.clone(this.localIp);
            var rLocalIp = u.clone(rule.localIp);
            var suffix = rule.ethertype === IpVersion.IPV6 ? '/128' : '/32';
            if (localIp !== kALL && localIp !== '::/0' && localIp !== '0:0:0:0/0' && localIp.indexOf('/') < 0) {
                localIp += suffix;
            }
            if (rLocalIp !== kALL && rLocalIp !== '::/0' && rLocalIp !== '0:0:0:0/0' && rLocalIp.indexOf('/') < 0) {
                rLocalIp += suffix;
            }
            x = x && localIp === rLocalIp;
        }
        // 查重 都不考虑备注了
        // // 全部协议 & 全部端口时不检查备注
        // if (x) {
        //     if (this.protocol === kALL && this.portRange === kALLPORT) {
        //         return x;
        //     }
        //     return x && this.name === rule.name;
        // }
        return x;
    }
    clone() {
        var cloned = new Rule(this.name, this.protocol, this.portRange, this.ethertype);
        cloned.remoteIP = this.remoteIP;
        cloned.remoteGroupId = this.remoteGroupId;
        cloned.remoteGroupShortId = this.remoteGroupShortId || this.remoteGroupId;
        cloned.remoteGroupName = this.remoteGroupName;
        cloned.source = this.source;
        cloned.key = this.key;
        return cloned;
    }
    getKey() {
        return [
            this.protocol,
            this.portRange,
            this.localPortRange,
            this.source === kUSER ? 'a-' + this.source : 'b-' + this.source,
            this.source === kUSER ? this.remoteIP : this.ipSet,
            this.localIp,
            this.remoteGroupId,
            this.name,
            this.remoteGroupShortId,
            this.ethertype
        ].join('#');
    }

    /**
     * 根据后端返回的json数据，查找规则的名字.
     *
     * @param {Object} rule 后端接口返回的json
     * @return {string} 常用规则的名字或者默认的名字.
     */
    findName(rule) {
        var kCommonServices = require('./services');
        var found = u.find(kCommonServices, function (service) {
            return !!u.find(service.rules, function (foo) {
                // 判断 foo 和 rule 是否相等
                // 检查 protocol 和 portRange
                if (foo.protocol === rule.protocol && foo.portRange === rule.portRange) {
                    return true;
                }
                return false;
            });
        });
        if (found) {
            return found.name;
        } else if (rule.protocol === 'tcp') {
            return '自定义TCP';
        } else if (rule.protocol === 'udp') {
            return '自定义UDP';
        }
        return '全部协议';
    }
    // remoteGroupName目前仅供显示使用，拷贝带着，比较不用考虑。
    static fromJSON = r => {
        var name = r.name || '';
        var protocol = r.protocol === '' ? kALL : r.protocol;
        var portRange = r.portRange === '' ? kALLPORT : r.portRange;
        var ethertype = r.ethertype || IpVersion.IPV4;
        var rule = new Rule(name, protocol, portRange, ethertype);
        rule.source = r.source || kUSER;
        // 保证all与0.0.0.0/0 与 ::/0 的相同性
        rule.remoteIP =
            r.remoteIP === '' || r.remoteIP === 'all'
                ? ethertype === IpVersion.IPV4
                    ? '0.0.0.0/0'
                    : '::/0'
                : r.remoteIP;
        rule.remoteGroupId = r.remoteGroupId || null;
        rule.remoteGroupName = r.remoteGroupName || null;
        rule.remoteGroupShortId = r.remoteGroupShortId || rule.remoteGroupId;
        rule.esgRuleUuid = r.esgRuleUuid || '';
        rule.updatedTime = r.updatedTime;
        rule.localIp =
            r.localIp === '' || r.localIp === 'all' ? (ethertype === IpVersion.IPV4 ? '0.0.0.0/0' : '::/0') : r.localIp;
        rule.localPortRange = r.localPortRange === '' ? kALLPORT : r.localPortRange;
        rule.direction = r.direction;
        rule.ipSet = r.ipSet;
        if (rule.source === kUSER) {
            rule.remoteGroupId = null;
            rule.remoteGroupName = null;
        }
        if (protocol === kICMP) {
            rule.portRange = '不涉及';
            rule.localPortRange = '不涉及';
        }
        if (protocol === kALL) {
            rule.localPortRange = '不涉及';
        }
        rule.key = rule.getKey();
        rule.action = r.action;
        rule.priority = r.priority;
        rule.remark = r.remark;
        return rule;
    };
    static toJSON = (r, direction) => {
        var remoteIP = r.source.toLowerCase() === kUSER ? r.remoteIP : null;
        var remoteGroupId = r.source.toLowerCase() === kUSER ? null : r.remoteGroupId;
        // 与rd确认后，仍旧传all
        var protocol = r.protocol.toLowerCase() === kALL ? 'all' : r.protocol;
        var portRange = r.portRange;
        var ethertype = r.ethertype;
        var updatedTime = r.updatedTime;
        var localPortRange = r.localPortRange;
        if (protocol === '') {
            portRange = '';
        }
        // icmp协议没有端口的概念，和console rd约定传null
        else if (protocol === kICMP) {
            portRange = null;
        }
        return {
            name: r.name,
            // 跟RD协商的结果是，如果是全部协议的时候，protocol字段为空.
            protocol: protocol,
            // 如果用户填all，转为全部端口给console rd，否则会报错
            portRange: portRange === kALL ? kALLPORT : u.trim(portRange),
            direction: direction,
            remoteIP: remoteIP,
            remoteGroupId: remoteGroupId,
            ethertype: ethertype,
            action: r.action,
            priority: r.priority,
            remark: r.remark,
            updatedTime: updatedTime,
            localIp: r.localIp,
            localPortRange: localPortRange
        };
    };
}
