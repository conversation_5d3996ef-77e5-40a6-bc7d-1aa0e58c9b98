/*
 * @description: 创建安全组
 * @file: network/security/pages/create.js
 * @author: p<PERSON><PERSON><PERSON><EMAIL>
 */
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {TagEditPanel} from '@baidu/bce-tag-sdk-san';
import {checker} from '@baiducloud/bce-opt-checker';
import Rule from '../util/rule';
import Confirm from '@/pages/sanPages/components/confirm';
import {securityTemp} from '@/pages/sanPages/common/enum';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import EnterpriseRuleList from '../components/ruleList';

import './style.less';

import rules from '../rules';
import testID from '@/testId';

const {invokeSUI, invokeSUIBIZ, invokeComp, invokeAppComp, template, service} = decorators;

/* eslint-disable */
const tpl = html`
    <template>
        <s-app-create-page
            class="{{klass}}"
            backTo="{{pageNav.backUrl}}"
            backToLabel="{{pageNav.backLabel}}"
            pageTitle="{{pageNav.title}}"
            data-test-id="${testID.enterpriseSecurity.createBack}"
        >
            <div class="content form-part-wrap">
                <h4>{{'基础信息'}}</h4>
                <s-form
                    s-ref="form"
                    data="{=formData=}"
                    rules="{{rules}}"
                    label-col="{{labelCol}}"
                    wrapper-col="{{wrapperCol}}"
                    label-align="left"
                >
                    <s-form-item
                        prop="name"
                        label="{{'安全组名称：'}}"
                        help="大小写字母、数字，中文以及-_/.特殊字符，必须以字母、中文开头，长度1-65"
                    >
                        <s-input
                            value="{=formData.name=}"
                            placeholder="{{'请输入名称'}}"
                            width="{{220}}"
                            data-test-id="${testID.enterpriseSecurity.createName}"
                        ></s-input>
                    </s-form-item>
                    <s-form-item prop="temp" label="{{'模版：'}}">
                        <s-select
                            width="{{220}}"
                            value="{=formData.temp=}"
                            data-test-id="${testID.enterpriseSecurity.createSelectTemp}"
                        >
                            <s-select-option
                                s-for="item in tempList"
                                value="{{item.value}}"
                                label="{{item.text}}"
                                data-test-id="${testID.enterpriseSecurity.createSelectTemp}{{rowIndex}}"
                            >
                                <s-tooltip>
                                    <div slot="content">{{item.text}}</div>
                                    <div>{{item.text}}</div>
                                </s-tooltip>
                            </s-select-option>
                        </s-select>
                    </s-form-item>
                    <s-form-item label="{{'描述：'}}">
                        <s-input-text-area
                            maxLength="200"
                            width="220"
                            height="60"
                            value="{=formData.description=}"
                            placeholder="{{'最多200个字符'}}"
                        ></s-input-text-area>
                    </s-form-item>
                </s-form>
            </div>
            <div class="content form-part-wrap">
                <h4>{{'端口设置'}}</h4>
                <enterprise-rule-list s-ref="ruleList" on-editting="handleEditting" tempId="{{formData.temp}}">
                </enterprise-rule-list>
            </div>
            <div s-if="!FLAG.NetworkSecuritySupportOrganization" class="content  form-part-wrap bottom_class">
                <h4>{{'标签'}}</h4>
                <div class="s-form-item-name">
                    <div class="s-form-item-label"><label>绑定标签：</label></div>
                    <div class="s-form-item-content">
                        <tag-edit-panel
                            s-ref="tagPanel"
                            instances="{{defaultInstances}}"
                            options="{{tagListRequster}}"
                        />
                    </div>
                </div>
            </div>
            <div slot="pageFooter" class="buybucket">
                <div class="buybucket-container">
                    <s-tooltip trigger="{{create.disable ? 'hover' : ''}}" placement="top">
                        <!--bca-disable-next-line-->
                        <div slot="content">{{create.message | raw}}</div>
                        <s-button
                            disabled="{{updating || editting}}"
                            class="opt-button"
                            size="large"
                            skin="primary"
                            on-click="onCreate"
                            data-test-id="${testID.enterpriseSecurity.createSubmit}"
                        >
                            {{'确定'}}
                        </s-button>
                    </s-tooltip>
                    <s-button class="opt-button" size="large" on-click="cancel">{{'取消'}}</s-button>
                </div>
            </div>
        </s-app-create-page>
    </template>
`;
/* eslint-enable */
@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class VpcInstanceIndex extends Component {
    static components = {
        'tag-edit-panel': TagEditPanel,
        'enterprise-rule-list': EnterpriseRuleList
    };
    static messages = {
        projectChange({value}) {
            value && this.data.set('resourceGroupId', value);
        }
    };
    static computed = {
        updating() {
            let createStatus = this.data.get('create');
            let confirmed = this.data.get('confirmed');
            return confirmed || createStatus.disable;
        }
    };

    initData() {
        return {
            FLAG,
            klass: ['vpc-enterpriseSecurity-create'],
            defaultInstances: [
                {
                    tags: [
                        {
                            tagKey: '默认项目',
                            tagValue: ''
                        }
                    ]
                }
            ],
            pageNav: {
                title: '创建安全组',
                backUrl: '/network/#/vpc/enterpriseSecurity/list',
                backLabel: '返回'
            },
            tagListRequster: this.tagListRequster.bind(this),
            labelCol: {span: 4},
            wrapperCol: {span: 19},
            formData: {
                name: '',
                temp: '1',
                description: ''
            },
            rules: {
                name: [
                    {required: true, message: '名称必填'},
                    {
                        pattern: /^(?!default)[a-zA-Z\u4e00-\u9fa5][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/,
                        message: '大小写字母、数字，中文以及-_/.特殊字符，必须以字母、中文开头，长度1-65'
                    }
                ]
            },
            resourceGroupId: '',
            vpcProject: [],
            create: {},
            tempList: securityTemp.toArray(),
            editting: false
        };
    }
    inited() {
        this.getIpSetList();
        this.checkQuota();
    }

    checkQuota() {
        this.$http.enterpriseSecurityQuota().then(result => {
            let {create} = checker.check(rules, [], 'create', {
                quotaCheck: result.free > 0
            });
            this.data.set('create', create);
        });
    }
    // 请求标签list
    tagListRequster() {
        return this.$http.getSearchTagList({serviceType: [], region: ['global']});
    }

    handleEditting(val) {
        this.data.set('editting', val);
    }
    async onCreate() {
        let tags = '';
        if (FLAG.NetworkSecuritySupportOrganization) {
            const projectValid = this.ref('projectConfig').validComponentData();
            if (!projectValid) {
                return;
            }
        } else {
            try {
                await this.ref('tagPanel').validate(false);
                await this.ref('form').validateFields();
            } catch (error) {
                return;
            }
        }
        tags = await this.ref('tagPanel').getTags();
        let rules = this.ref('ruleList').getRules();
        let result = [];
        rules.in.forEach(item => {
            let payload = Rule.toJSON(item, 'ingress');
            if (item.ipSet) {
                if (item.source !== 'user') {
                    let ipGroupList = this.data.get('ipGroupList') || [];
                    payload.ipCollectionType = 1;
                    if (ipGroupList.findIndex(i => i.ipGroupUuid === item.ipSet) > -1) {
                        payload.ipCollectionType = 2;
                    }
                    payload.ipCollectionUuid = item.ipSet;
                    delete payload.remoteIP;
                }
            }
            if (item.protocol === 'icmp' || item.protocol === 'all') {
                delete payload.localPortRange;
            }
            result.push(payload);
        });
        rules.out.forEach(item => {
            let payload = Rule.toJSON(item, 'egress');
            if (item.ipSet) {
                if (item.source !== 'user') {
                    let ipGroupList = this.data.get('ipGroupList') || [];
                    payload.ipCollectionType = 1;
                    if (ipGroupList.findIndex(i => i.ipGroupUuid === item.ipSet) > -1) {
                        payload.ipCollectionType = 2;
                    }
                    payload.ipCollectionUuid = item.ipSet;
                    delete payload.remoteIP;
                }
            }
            if (item.protocol === 'icmp' || item.protocol === 'all') {
                delete payload.localPortRange;
            }
            result.push(payload);
        });
        let formData = this.data.get('formData');
        let payload = {
            desc: formData.description,
            name: formData.name,
            rules: result
        };
        if (FLAG.NetworkSecuritySupportOrganization) {
            payload.resourceGroupIds = [this.data.get('resourceGroupId')];
        } else {
            payload.tags = tags;
        }
        if (rules.in.length === 0) {
            let confirm = new Confirm({
                data: {
                    open: true,
                    content:
                        '您未添加任何入站开放端口，将使该云服务器无法和外部进行任何通信，您仅能通过VNC功能进行管理。是否确认继续操作？'
                }
            });
            confirm.attach(document.body);
            confirm.on('confirm', () => {
                this.$http.enterpriseSecurityCreate(payload).then(() => {
                    location.hash = '#/vpc/enterpriseSecurity/list';
                });
            });
        } else {
            this.$http.enterpriseSecurityCreate(payload).then(() => {
                location.hash = '#/vpc/enterpriseSecurity/list';
            });
        }
    }
    cancel() {
        location.hash = '#/vpc/enterpriseSecurity/list';
    }
    getIpSetList() {
        let all = [this.getIpGroupList()];
        Promise.all(all).then(res => {
            let array = [];
            res[0].result.forEach(item => {
                array.push({
                    group: 'IP地址族',
                    text: item.name + '（' + item.ipGroupId + '）',
                    value: item.ipGroupUuid,
                    ipType: item.ethertype
                });
            });
            this.data.set('ipGroupList', res[0].result || []);
        });
    }
    getIpGroupList() {
        let payload = {
            pageNo: 1,
            pageSize: 10000
        };
        return this.$http.ipGroupList(payload);
    }
}
export default San2React(Processor.autowireUnCheckCmpt(VpcInstanceIndex));
