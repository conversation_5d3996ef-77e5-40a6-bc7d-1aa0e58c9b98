/**
 * @file 安全组操作禁用配置
 * <AUTHOR>
 */
import _ from 'lodash';
import {ContextService} from '../../common';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';

export default {
    create: [
        {
            required: false,
        },
        {
            custom(data, opt) {
                if (!opt.quotaCheck) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: '该VPC可添加安全组数量已达上限'
                        };
                    }
                    else {
                        return {
                            disable: true,
                            message: `该VPC可添加安全组数量已达上限，如需更多配额，请提交
                            <a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">工单</a>`,
                        };
                    }
                }
            }
        }
    ],
    release: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            custom(data) {
                if (_.some(data, item => item.defaultSecurityGroup)) {
                    return {
                        disable: true,
                        message: '无法删除默认安全组'
                    };
                }
                if (_.some(data, item => item.associateNum && item.associateNum > 0)) {
                    return {
                        disable: true,
                        message: '已关联了实例（包括回收站中的实例）的安全组，不可删除；如需删除，请先解除关联关系'
                    };
                }
                if (data.length > 20) {
                    return {
                        disable: true,
                        message: '企业安全组批量删除的数量不能超过20个，请您减少批量删除的数量'
                    };
                }
            }
        }
    ],
    preview: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            custom(data) {
                if (data.length > 10) {
                    return {
                        disable: true,
                        message: '规则预览选择安全组数量不得超过10个'
                    };
                }
            }
        }
    ],
    editTag: [
        {
            required: true,
            message: '请先选择实例对象'
        },
    ],
    changeResource: [
        {
            required: true,
            message: '请先选择实例对象'
        }
    ],
    cancelRelated: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            custom(data) {
                if (_.find(data, item => item.associationNum <= 1)) {
                    return {
                        disable: true,
                        message: '部分实例当前仅关联了一个安全组，不可批量操作'
                    };
                }
            }
        }
    ],
    cancelRelatedBlb: [
        {
            required: true,
            message: '请先选择实例对象'
        }
    ]
};
