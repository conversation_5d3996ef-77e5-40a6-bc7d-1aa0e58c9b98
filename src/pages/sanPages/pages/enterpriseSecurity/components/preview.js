import {Component} from 'san';
import u from 'lodash';
import {html} from '@baiducloud/runtime';
import {Dialog, Radio, Button, Table} from '@baidu/sui';

import Rule from '../util/rule';
import {SecurityIpVersion as IpVers<PERSON>, DirectionType} from '@/pages/sanPages/common/enum';

const tpl = html`
<template>
  <s-dialog
    class="security-preview"
    open="{=open=}"
    width="900"
    title="{{'预览规则'}}">
    <s-radio-group
      value="{=direction=}"
      radioType="button"
      datasource="{{directionList}}"
      class="security-direction-radio"
      on-change="ruleTypeChange($event)"
    >
    </s-radio-group>
    <s-table
      columns="{{table.columns}}"
      loading="{{table.loading}}"
      datasource="{{datasource}}">
      <div slot="c-name">
        <span title="{{row.esgName}}">{{row.esgName}}</span>
        <br>
        <span class="truncated" title="{{row.esgId}}">{{row.esgId}}</span>
      </div>
      <div slot="c-protocol">
        <span>{{row | protocol}}</span>
      </div>
      <div slot="c-portRange">
        <span>{{row | portRange}}</span>
      </div>
      <div slot="c-source">
        <span>{{row | source}}</span>
      </div>
      <div slot="c-action">
        <span>{{row | action}}</span>
      </div>
      <div slot="c-remark">
        <span>{{row | remark}}</span>
      </div>
    </s-table>
    <div slot="footer">
      <s-button skin="primary" on-click="close">{{'确定'}}</s-button>
    </div>
  </s-dialog>
</template>
`;

export default class SecurityPreview extends Component {
  static template = tpl;
  static components = {
    's-dialog': Dialog,
    's-button': Button,
    's-radio-group': Radio.RadioGroup,
    's-table': Table
  }

  static computed = {
    datasource() {
      if (this.data.get('direction') === 'ingress') {
        return this.data.get('table.inDatasource');
      } else {
        return this.data.get('table.outDatasource');
      }
    },
  }

  static filters = {
    priority(row) {
      if (!row.priority) {
        return u.escape(row.priority);
      }
      return row.priority;
    },
    protocol(row) {
      if (row.protocol === 'all') {
        return '全部协议';
      }
      return u.escape(row.protocol);
    },
    portRange(row) {
      return u.escape(row.portRange);
    },
    source(row) {
      if (row.ipCollectionType) {
        if (row.ipCollectionType === 1) {
          return 'IP地址组：' + row.ipCollectionId;
        }
        return 'IP地址族：' + row.ipCollectionId;
      }
      if (row.source === 'user') {
        return (this.data.get('direction') === 'ingress' ? '源IP：' : '目的IP：')
          + (row.remoteIP === 'all'
            ? (row.ethertype === IpVersion.IPV4 ? '0.0.0.0/0' : '::/0')
            : u.escape(row.remoteIP));
      }
      var id = row.remoteGroupShortId || row.remoteGroupId;
      return '安全组：' + u.escape(row.remoteGroupName)
        + (id ? '（' + u.escape(id) + '）' : '');
    },
    action(row) {
      return row.action === 'allow' ? '允许' : '拒绝';
    },
    remark(row) {
      return row.remark || '-';
    }
  }

  initData() {
    return {
      open: false,
      directionList: DirectionType.toArray(),
      direction: DirectionType.INGRESS,
      ruleList: [],
      table: {
        loading: false,
        inDatasource: [],
        outDatasource: [],
        columns: [
          {
            name: 'priority', label: '优先级',
            width: 60
          },
          {
            name: 'name', label: '企业安全组',
            width: 90
          },
          {
            name: 'ethertype', label: '类型',
            width: 60,
            render(item) {
                if (!item.edit) {
                  return u.escape(item.ethertype);
                }
                return item.ethertype;
            }
          },
          {
            name: 'protocol', label: '协议',
            width: 60,
            render(item) {
              if (!item.edit) {
                if (item.protocol === 'all') {
                  return '全部协议';
                }
                return u.escape(item.protocol);
              }
              return item.protocol;
            }
          },
          {
            name: 'portRange',
            label: '目的端口',
            width: 80,
            render(item) {
              if (!item.edit) {
                  return u.escape(item.portRange);
              }
              return item.portRange;
            }
          },
          {
            name: 'source', label: '源IP',
            width: 90,
            render(item) {
              if (!item.edit) {
                if (item.source === 'user') {
                  return (this.id === 'rulesInTable' ? '源IP：' : '目的IP：')
                    + (item.remoteIP === 'all'
                    ? (item.ethertype === IpVersion.IPV4 ? '0.0.0.0/0' : '::/0')
                    : u.escape(item.remoteIP));
                }
                var id = item.remoteGroupShortId || item.remoteGroupId;
                return '安全组：' + u.escape(item.remoteGroupName)
                  + (id ? '（' + u.escape(id) + '）' : '');
              }
              return item.source;
            }
          },
          {
            name: 'action', label: '策略',
            width: 60
          },
          {
            name: 'remark', label: '备注',
            width: 90
          }
        ]
      }
    };
  }

  ruleTypeChange(e) {
    if (e.value === 'ingress') {
      this.data.set('table.columns[5].label', '源IP');
      this.nextTick(() => {
        this.data.splice('table.columns', [4, 2]);
        this.data.splice('table.columns', [5, 0, {
          name: 'localIp',
          label: '目的IP',
          width: 90,
          render(item) {
            return '目的IP：' + (item.localIp === 'all'
              ? (item.ethertype === IpVersion.IPV4 ? '0.0.0.0/0' : '::/0')
              : u.escape(item.localIp));
          }
        }]);
        this.data.splice('table.columns', [6, 0,
          {
            name: 'localPortRange',
            label: '源端口',
            width: 90,
            render(item) {
              return item.localPortRange ? item.localPortRange : '1-65535';
            }
          }
        ]);
        this.data.set('table.columns[7].label', '源IP');
      });
    } else {
      this.data.set('table.columns[5].label', '目的IP');
      this.nextTick(() => {
        this.data.splice('table.columns', [5, 2]);
        this.data.splice('table.columns', [4, 0,
          {
            name: 'localPortRange',
            label: '源端口',
            width: 90,
            render(item) {
              return item.localPortRange ? item.localPortRange : '1-65535';
            }
          }
        ]);
        this.data.splice('table.columns', [5, 0, {
          name: 'localIp',
          label: '源IP',
          width: 90,
          render(item) {
            return '源IP：' + (item.localIp === 'all'
              ? (item.ethertype === IpVersion.IPV4 ? '0.0.0.0/0' : '::/0')
              : u.escape(item.localIp));
          }
        }]);
        this.data.set('table.columns[7].label', '目的IP');
      });
    }
  }

  inited() {
    this.data.splice('table.columns', [5, 0, {
      name: 'localIp',
      label: '目的IP',
      width: 90,
      render(item) {
        return '目的IP：' + (item.localIp === 'all'
          ? (item.ethertype === IpVersion.IPV4 ? '0.0.0.0/0' : '::/0')
          : u.escape(item.localIp));
      }
    }]);
    this.data.splice('table.columns', [6, 0,
      {
        name: 'localPortRange',
        label: '源端口',
        width: 90,
        render(item) {
          return item.localPortRange ? item.localPortRange : '1-65535';
        }
      }
    ]);
    this.getPreviewRules();
  }

  close() {
    this.data.set('open', false);
  }

  getPreviewRules() {
    this.data.set('table.loading', true);
    this.$http.getEnterpriseSecurityPreview({
      esgUuids: this.data.get('ids')
    }).then(res => {
      this.data.set('table.loading', false);
      let inDatasource = res.ingressRules;
      if (inDatasource.length) {
        inDatasource = inDatasource.map(item => {
          item.remoteIP = item.remoteIP;
          item.localIp = item.localIp;
          let newItem = Rule.fromJSON(item);
          newItem.esgName = item.esgName;
          newItem.esgId = item.esgId;
          newItem.ipCollectionType = item.ipCollectionType;
          newItem.ipCollectionUuid = item.ipCollectionUuid;
          newItem.ipCollectionId = item.ipCollectionId;
          return newItem;
        });
      }
      this.data.set('table.inDatasource', inDatasource);
      let outDatasource = res.egressRules;
      if (outDatasource.length) {
        outDatasource = outDatasource.map(item => {
          item.remoteIP = item.remoteIP;
          item.localIp = item.localIp;
          let newItem = Rule.fromJSON(item);
          newItem.esgName = item.esgName;
          newItem.esgId = item.esgId;
          newItem.ipCollectionType = item.ipCollectionType;
          newItem.ipCollectionUuid = item.ipCollectionUuid;
          newItem.ipCollectionId = item.ipCollectionId;
          return newItem;
        });
      }
      this.data.set('table.outDatasource', outDatasource);
    }).catch(() => {
      this.data.set('table.loading', false);
    });
  }
}
