/*
 * @description: 安全组复制
 * @file: security/pages/copy.js
 * @author: p<PERSON><PERSON><EMAIL>
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import {Form, Input, Dialog, Select, Button, Icon, Tooltip, Notification} from '@baidu/sui';
import {checker} from '@baiducloud/bce-opt-checker';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';

import rules from '../rules';
import testID from '@/testId';

const {service} = decorators;
const tpl = html`
<template>
<s-dialog
    class="security-copy"
    open="{=open=}"
    width="550"
    title="{{'复制安全组'}}">
    <s-form s-ref="form"
        data="{=formData=}"
        rules="{{rules}}"
        label-col="{{labelCol}}"
        wrapper-col="{{wrapperCol}}"
        label-align="left">
        <s-form-item prop="vpcId" label="{{'目标地域：'}}">
            <s-select
                width="200"
                datasource="{{regionList}}"
                on-change="regionChange"
                value="{=formData.region=}">
        </s-form-item>
        <s-form-item prop="name" label="{{'安全组名称：'}}"
            help="{{'大小写字母、数字，中文以及-_/.特殊字符，必须以字母、中文开头，长度1-65'}}">
            <s-input
                width="200"
                value="{=formData.name=}"
                placeholder="{{'请输入安全组名称'}}"
                data-test-id="${testID.enterpriseSecurity.listCopyNameInput}"
            >
            </s-input>
        </s-form-item>
        <s-form-item label="{{'描述：'}}">
            <s-textarea
                maxLength="200"
                width="220"
                height="60"
                value="{=formData.description=}"
                placeholder="{{'请输入描述'}}"
            ></s-textarea>
        </s-form-item>
    </s-form>
    <div slot="footer">
        <s-button on-click="close">{{'取消'}}</s-button>
        <s-tooltip
            trigger="{{create.disable ? 'hover' : ''}}" placement="top">
            <!--bca-disable-next-line-->
            <div slot="content">{{create.message | raw}}</div>
            <s-button
                skin="primary"
                disabled="{{create.disable || copying}}"
                on-click="create">
                {{'确定'}}
            </s-button>
        </s-tooltip>
    </div>
</s-dialog>
</template>
`;
export default class VpcInstanceIndex extends Component {
    static template = tpl;
    static components = {
        's-form': Form,
        's-form-item': Form.Item,
        's-input': Input,
        's-dialog': Dialog,
        's-button': Button,
        's-select': Select,
        's-tooltip': Tooltip,
        's-textarea': Input.TextArea,
        's-icon': Icon
    };
    initData() {
        let labelCol = {span: 4};
        let wrapperCol = {span: 19};
        const currentRegion = window.$context.getCurrentRegionId();
        const regionList = u.map(window.$context.SERVICE_TYPE.NETWORK.region, (value, key) => {
            return {
                value: key,
                text: value
            };
        });
        if (location.search.indexOf('locale=en') > -1) {
            labelCol = {span: 6};
            wrapperCol = {span: 17};
        }
        return {
            open: false,
            labelCol,
            wrapperCol,
            regionList: regionList,
            formData: {
                region: currentRegion,
                name: '',
                description: ''
            },
            rules: {
                name: [
                    {
                        required: true,
                        message: '不能为空'
                    },
                    {
                        pattern: /^(?!default)[a-zA-Z\u4e00-\u9fa5][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/,
                        message:
                            '名称不符合规则：大小写字母、数字，中文以及-_/.特殊字符，必须以字母、中文开头，长度1-65'
                    }
                ]
            },
            create: {},
            copying: false,
            FLAG
        };
    }
    inited() {
        this.checkQuota();
    }
    close() {
        this.data.set('open', false);
    }
    regionChange({value}) {
        this.data.set('formData.region', value);
        this.checkQuota();
    }
    checkQuota() {
        this.$http.enterpriseSecurityQuota({}, {region: this.data.get('formData.region')}).then(result => {
            let {create} = checker.check(rules, [], 'create', {
                quotaCheck: result.free > 0
            });
            this.data.set('create', create);
        });
    }
    async create() {
        await this.ref('form').validateFields();
        this.data.set('copying', true);
        let formData = this.data.get('formData');
        let payload = {
            desc: formData.description,
            esgUuid: this.data.get('esgUuid'),
            name: formData.name,
            targetRegion: formData.region
        };
        this.$http
            .enterpriseSecurityCopy(payload)
            .then(result => {
                Notification.success('企业安全组复制成功');
                this.data.set('open', false);
                this.fire('success');
            })
            .catch(err => {
                Notification.error('企业安全组复制失败');
            })
            .finally(() => {
                this.data.set('copying', false);
            });
    }
}
