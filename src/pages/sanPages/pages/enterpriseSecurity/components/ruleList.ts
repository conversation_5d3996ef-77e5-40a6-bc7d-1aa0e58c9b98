/*
 * @description: 安全组规则列表
 * @file: network/security/pages/ruleList.js
 * @author: p<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {Notification} from '@baidu/sui';
import {OutlinedPlus} from '@baidu/sui-icon';

import Rule from '../util/rule';
import LeadRule from '../leadFile/leadFile';
import checkRule from '../../security/checkRule/checkRule';
import kCommonServices from '../util/services';
import Confirm from '@/pages/sanPages/components/confirm';
import {SecurityIpVersion as IpVersion, enterpriseSecurityTemp} from '@/pages/sanPages/common/enum';
import {checkIpv6Cidr, convertCidrToBinary} from '@/pages/sanPages/utils/common';
import regs from '@/pages/sanPages/utils/rule';
import './ruleList.less';
import {kXhrOptions, utcToTime, $flag as FLAG} from '@/pages/sanPages/utils/helper';
import testID from '@/testId';

let kDefaultAllRule = new Rule('暴露全部端口到公网和内网', 'all', '1-65535', IpVersion.IPV4, '不涉及', 'allow', '1000');
let kV6DefaultAllRule = new Rule(
    '暴露全部端口到公网和内网',
    'all',
    '1-65535',
    IpVersion.IPV6,
    '不涉及',
    'allow',
    '1000'
);
const {asComponent, invokeSUI, invokeSUIBIZ, template} = decorators;

function isPort(v) {
    return /^[1-9][0-9]{0,4}/.test(v) && v <= 65535;
}
function portRange(value) {
    if (!value) {
        return '端口范围必填';
    }
    value = u.trim(value);
    if (value.toLowerCase() === 'all') {
        return '端口不符合规则';
    }
    if (value.indexOf(',') !== -1) {
        let valueArray = value.split(',');
        if (valueArray.length > 15) {
            return '最多支持15个端口';
        }
        let portMap = {};
        for (let item of valueArray) {
            if (portMap[item]) {
                return '端口重复';
            }
            portMap[item] = true;
            if (!isPort(item)) {
                return '端口不符合规则';
            }
        }
        return '';
    }
    if (value.indexOf('-') === -1) {
        return isPort(value) ? '' : '端口范围不符合规则';
    }
    let array = value.split('-');
    return isPort(array[0]) && isPort(array[1]) && +array[0] < +array[1] ? '' : '端口范围不符合规则';
}
function checkIp(value, ipVersion) {
    value = u.trim(value);
    if (value.toLowerCase() === 'all') {
        return '';
    }
    if (ipVersion === 'IPv6') {
        if (!checkIpv6Cidr(value)) {
            let valueString = convertCidrToBinary(value);
            let valueMask = value.split('/')[1];
            // 掩码分割的前部分
            let preValueString = valueString.substring(0, +valueMask);
            // 掩码分割的前后部分
            let tailValueString = valueString.substring(+valueMask, valueString.length);
            if (valueMask && tailValueString.includes('1')) {
                let addLen = 128 - preValueString.length;
                let fixIpBinary = preValueString + new Array(addLen).fill('0').join('');
                // 每隔16位按:分割
                let ipBinaryArr = fixIpBinary
                    .replace(/(.{16})/g, '$1:')
                    .substring(0, 128)
                    .split(':');
                let ipArr = ipBinaryArr.map(binary => {
                    // 先转为10进制，再转为16进制
                    return parseInt(binary, 2).toString(16);
                });
                let fixIp = ipArr.join(':') + '/' + valueMask;
                // 连续:0替换::
                let replaceIp = fixIp.replace(/(:0){2,}/g, '::');
                return `网段与掩码不匹配，建议改为${replaceIp}`;
            }
            return 'IP格式不正确';
        }
    } else {
        let reg = new RegExp(regs.IP_CIDR);
        if (!reg.test(value)) {
            return 'IP格式不正确';
        }
        let valueString = convertCidrToBinary(value);
        let valueMask = value.split('/')[1];
        // 掩码分割的前部分
        let preValueString = valueString.substring(0, +valueMask);
        // 掩码分割的前后部分
        let tailValueString = valueString.substring(+valueMask, valueString.length);
        if (valueMask && tailValueString.includes('1')) {
            let addLen = 32 - preValueString.length;
            let fixIpBinary = preValueString + new Array(addLen).fill('0').join('');
            // 每隔8位按.分割
            let ipBinaryArr = fixIpBinary
                .replace(/(.{8})/g, '$1.')
                .substring(0, 32)
                .split('.');
            let ipArr = ipBinaryArr.map(binary => parseInt(binary, 2));
            let fixIp = ipArr.join('.') + '/' + valueMask;
            return `网段与掩码不匹配，建议改为${fixIp}`;
        }
    }
    return '';
}
function localportRange(value) {
    if (!value) {
        return '';
    }
    value = u.trim(value);
    if (value.toLowerCase() === 'all') {
        return '端口不符合规则';
    }
    if (value.indexOf(',') !== -1) {
        let valueArray = value.split(',');
        if (valueArray.length > 15) {
            return '最多支持15个端口';
        }
        let portMap = {};
        for (let item of valueArray) {
            if (portMap[item]) {
                return '端口重复';
            }
            portMap[item] = true;
            if (!isPort(item)) {
                return '端口不符合规则';
            }
        }
        return '';
    }
    if (value.indexOf('-') === -1) {
        return isPort(value) ? '' : '端口范围不符合规则';
    }
    let array = value.split('-');
    return isPort(array[0]) && isPort(array[1]) && +array[0] < +array[1] ? '' : '端口范围不符合规则';
}
/* eslint-disable */
const tpl = html`
    <div class="security-rule-list">
        <s-alert skin="warning" class="alert-tip">
            <div slot="description" class="enterprise-warm-tip">
                <div class="label">{{'温馨提示：'}}</div>
                <div class="value">
                    <p>{{'若安全组中未添加任何规则，表示所关联BCC实例的所有端口都不能被外界访问。'}}</p>
                    <p>
                        {{'企业安全组入站或出站默认配额为150，如一条规则使用离散端口或参数模版，可能会超出，有问题可申请'}}
                        <a href="https://console.bce.baidu.com/quota_center/#/quota/network/list?serviceType=VPC"
                            >{{'配额管理'}}</a
                        >。
                    </p>
                </div>
            </div>
        </s-alert>
        <s-radio-radio-group
            enhanced
            class="ruletype"
            datasource="{{ruleTypeList}}"
            on-change="ruleTypeChange($event)"
            value="{=ruleType=}"
            radioType="button"
            data-test-id="${testID.enterpriseSecurity.createChangeRuleType}"
        >
        </s-radio-radio-group>
        <div class="allow-all-port">
            <span>{{'允许访问所有端口：'}}</span>
            <s-switch
                disabled="{{changeFrom === 'switch'}}"
                checked="{=allowAllPort=}"
                on-change="allowAllPortChange($event)"
            />
        </div>
        <div class="tool-tip">
            <div class="leftbar_class">
                <s-tooltip trigger="{{disableMessage ? 'hover' : ''}}" placement="right">
                    <!--bca-disable-next-line-->
                    <div slot="content">{{disableMessage | raw}}</div>
                    <s-button
                        disabled="{{isEdit || disableAdd}}"
                        skin="primary"
                        track-id="vpc_enterpriseSecurity_add_rule"
                        on-click="onCreate"
                        data-test-id="${testID.enterpriseSecurity.createAddRuleBtn}"
                    >
                        <outlined-plus />
                        {{'添加规则'}}
                    </s-button>
                </s-tooltip>
                <s-button
                    class="left_class"
                    on-click="deleteRule"
                    disabled="{{selectedRule.length === 0}}"
                    data-test-id="${testID.enterpriseSecurity.createDeleteRule}"
                >
                    {{'删除'}}
                </s-button>
            </div>
            <s-select
                class="ruleiptype"
                width="100"
                datasource="{{ruleIpTypeList}}"
                value="{=ruleIpType=}"
                on-change="ruleIpTypeChange($event, rowIndex)"
            >
            </s-select>
            <s-button class="lead-btn" s-if="isDetail && FLAG.NetworkSecuritySupLead" on-click="leadOut"
                >{{'导出' }}</s-button
            >
            <s-tooltip class="lead-tooltip" trigger="{{disableMessage ? 'hover' : ''}}" placement="right">
                <!--bca-disable-next-line-->
                <div slot="content">{{disableMessage | raw}}</div>
                <s-button
                    disabled="{{disableAdd}}"
                    class="lead-btn"
                    track-id="vpc_enterpriseSecurity_add_rule"
                    s-if="isDetail && FLAG.NetworkSecuritySupLead"
                    on-click="leadIn"
                >
                    {{'导入' }}
                </s-button>
                <s-button s-if="isDetail" class="lead-btn" on-click="checkRule">{{'规则检查' }}</s-button>
            </s-tooltip>
        </div>
        <s-table
            columns="{{table.columns}}"
            loading="{{table.loading || allDataLoading}}"
            error="{{table.error}}"
            rowClassName="{{table.rowClassName}}"
            on-selected-change="tableSelected($event)"
            selection="{=table.selection=}"
            datasource="{{filterDatasource}}"
            data-test-id="${testID.enterpriseSecurity.ruleTable}"
        >
            <div slot="error">
                {{'啊呀，出错了？'}}
                <a href="javascript:void(0);" on-click="refresh">{{'重新加载'}}</a>
            </div>
            <div slot="empty">
                <s-empty on-click="onCreate" actionText="添加规则"> </s-empty>
            </div>
            <div slot="h-priority">
                <span>{{'优先级'}}</span>
                <s-tip class="inline-tip" content="{{'优先级范围为1-1000，1为最高优先级，默认1000'}}" skin="question" />
            </div>
            <div slot="c-priority">
                <s-input-number
                    min="{{1}}"
                    max="{{1000}}"
                    value="{=prioritys[rowIndex]=}"
                    s-if="editRow[rowIndex]"
                    width="90"
                    on-change="itemChange('prioritys',rowIndex,$event)"
                />
                <span s-else>{{row | priority}}</span>
            </div>
            <div slot="c-ethertype">
                <s-select
                    s-if="editRow[rowIndex] && editType === 'add'"
                    width="90"
                    on-change="itemChange('ethertypes',rowIndex,$event)"
                    datasource="{{ethertypeList}}"
                    value="{=ethertypes[rowIndex]=}"
                />
                <span s-else>{{row.ethertype}}</span>
            </div>
            <div slot="c-protocol">
                <s-select
                    getPopupContainer="{{handleGetPopupContainer}}"
                    s-if="editRow[rowIndex]"
                    width="140"
                    on-change="itemChange('protocols',rowIndex,$event)"
                    datasource="{{protocolList}}"
                    value="{=protocols[rowIndex]=}"
                />
                <span s-else>{{row | protocol}}</span>
            </div>
            <div slot="c-portRange">
                <s-input
                    width="90"
                    disabled="{{portRangeDisable[rowIndex]}}"
                    s-if="editRow[rowIndex]"
                    on-input="itemChange('portRanges',rowIndex,$event)"
                    value="{=portRanges[rowIndex]=}"
                    placeholder="1-65535"
                >
                </s-input>
                <span s-else>{{row | portRange}}</span>
                <p style="color: #EB5252" s-if="portRangeErr[rowIndex]">{{portRangeErr[rowIndex]}}</p>
            </div>
            <div slot="c-source">
                <div s-if="editRow[rowIndex]">
                    <s-select
                        width="{{90}}"
                        on-change="itemChange('sources',rowIndex,$event)"
                        datasource="{{sourceList}}"
                        value="{=sources[rowIndex]=}"
                    />
                    <s-input
                        s-if="sources[rowIndex]==='user'"
                        on-input="itemChange('remoteIPs',rowIndex,$event)"
                        width="{{90}}"
                        value="{=remoteIPs[rowIndex]=}"
                        placeholder="all"
                    ></s-input>
                    <s-select
                        filterable
                        width="{{130}}"
                        s-if="sources[rowIndex]==='ipSet'"
                        on-change="itemChange('ipSet',rowIndex,$event)"
                        datasource="{{ipSetList}}"
                        value="{=ipSet[rowIndex]=}"
                    />
                    <p style="color: #EB5252" s-if="ipSetErr[rowIndex]">{{'请选择参数模板'}}</p>
                    <p style="color: #EB5252" s-if="remoteIPErr[rowIndex]">{{remoteIPErr[rowIndex]}}</p>
                </div>

                <s-popover
                    s-else-if="{{row.ipCollectionType}}"
                    visibleArrow="{{false}}"
                    placement="right"
                    class="enterprise-rule-popover"
                >
                    <div slot="content">
                        <div
                            class="rule-item"
                            s-if="row.ipAddressList.length"
                            s-for="item, index in row.ipAddressList"
                            key="{{index}}"
                            on-click="handleJumpToParams(row, item)"
                        >
                            {{item}}
                        </div>
                        <div s-else>暂无数据</div>
                    </div>
                    <span class="params-item">{{row | source}}</span></s-popover
                >
                <span s-else>{{row | source}}</span>
            </div>
            <div slot="c-localPortRange">
                <div s-if="editRow[rowIndex]">
                    <s-input
                        width="90"
                        disabled="{{localPortRangeDisable[rowIndex]}}"
                        on-input="itemChange('localPortRanges',rowIndex,$event)"
                        value="{=localPortRanges[rowIndex]=}"
                        placeholder="1-65535"
                    >
                    </s-input>
                </div>
                <span s-else>{{row | localPortRange}}</span>
                <p style="color: #EB5252" s-if="localPortRangeErr[rowIndex]">{{localPortRangeErr[rowIndex]}}</p>
            </div>
            <div slot="c-localIp">
                <div s-if="editRow[rowIndex]">
                    <s-input
                        on-input="itemChange('localIps',rowIndex,$event)"
                        width="{{90}}"
                        value="{=localIps[rowIndex]=}"
                        placeholder="all"
                    >
                    </s-input>
                    <p style="color: #EB5252" s-if="localIpErr[rowIndex]">{{localIpErr[rowIndex]}}</p>
                </div>
                <span s-else>{{row | localIp}}</span>
            </div>
            <div slot="c-action">
                <s-select
                    width="{{90}}"
                    datasource="{{actionList}}"
                    on-change="itemChange('actions',rowIndex,$event)"
                    s-if="editRow[rowIndex]"
                    value="{=actions[rowIndex]=}"
                />
                <span s-else>{{row | action}}</span>
            </div>
            <div slot="c-remark">
                <s-input
                    width="90"
                    on-input="itemChange('remarks',rowIndex,$event)"
                    s-if="editRow[rowIndex]"
                    value="{=remarks[rowIndex]=}"
                >
                </s-input>
                <span s-else>{{row | remark}}</span>
            </div>
            <div slot="c-opt">
                <div
                    s-if="{{row.protocol!=='all' || row.portRange!=='1-65535'
            || row.remoteIP!=='all' || !row.esgRuleUuid}}"
                >
                    <span s-if="!editRow[rowIndex]">
                        <a
                            href="javascript:void(0)"
                            s-if="!editArray.length"
                            on-click="edit(row,rowIndex)"
                            data-testid="${testID.enterpriseSecurity.createAddRuleEdit}{{rowIndex}}"
                            >{{'编辑'}}</a
                        >
                        <a href="javascript:void(0)" s-else style="color:#999">{{'编辑'}}</a>
                    </span>
                    <a
                        disabled="{{portRangeErr[rowIndex] || remoteIPErr[rowIndex] || localPortRangeErr[rowIndex]}}"
                        href="javascript:void(0)"
                        s-if="editRow[rowIndex]"
                        on-click="editConfirm(rowIndex, editType)"
                        data-test-id="${testID.enterpriseSecurity.createAddRuleSubmit}{{rowIndex}}"
                        >{{'确定'}}
                    </a>
                    <a
                        href="javascript:void(0)"
                        s-if="editRow[rowIndex]"
                        on-click="editCancel(row, rowIndex, editType)"
                        data-testid="${testID.enterpriseSecurity.createAddRuleCancel}{{rowIndex}}"
                        >{{'取消'}}</a
                    >
                </div>
            </div>
        </s-table>
        <s-pagination
            s-if="{{pager.total && isDetail}}"
            class="pagination-class-table"
            slot="footer"
            layout="{{'total, pageSize, pager, go'}}"
            pageSize="{{pager.size}}"
            total="{{pager.total}}"
            page="{{pager.page}}"
            on-pagerChange="onPagerChange"
            on-pagerSizeChange="onPagerSizeChange"
        />
    </div>
`;
/* eslint-enable */

@asComponent('@enterprise-rule-list')
@template(tpl)
@invokeSUI
@invokeSUIBIZ
class RuleList extends Component {
    static components = {
        'outlined-plus': OutlinedPlus
    };
    static computed = {
        datasource() {
            if (this.data.get('ruleType') === 'in') {
                return this.data.get('table.inDatasource');
            }
            return this.data.get('table.outDatasource');
        },
        filterDatasource() {
            let datasource = this.data.get('datasource');
            if (this.data.get('ruleIpType') === 'all') {
                return datasource;
            } else if (this.data.get('ruleIpType') === 4) {
                return datasource.filter(item => item.ethertype === IpVersion.IPV4);
            }
            return datasource.filter(item => item.ethertype === IpVersion.IPV6);
        },
        disableAdd() {
            // 企业安全组规则计算方式改动：总规则数 * 每个规则总ip数 * 源离散端口数 * 目的离散端口数；
            const ruleType = this.data.get('ruleType');
            const ruleQuota = this.data.get('ruleQuota');
            const freeQuota = ruleType === 'in' ? ruleQuota.ingressQuota?.free : ruleQuota.egressQuota?.free;
            const totalRule = this.data.get('table.inDatasource').length + this.data.get('table.outDatasource').length;
            const isDetail = this.data.get('isDetail');
            return freeQuota <= 0 || (!isDetail && totalRule > 20);
        },
        disableMessage() {
            const totalRule = this.data.get('table.inDatasource').length + this.data.get('table.outDatasource').length;
            const isDetail = this.data.get('isDetail');
            const disableAdd = this.data.get('disableAdd');
            if (disableAdd && !isDetail && totalRule > 20) {
                return '企业安全组规则批量添加的数量不能超过20个，请您减少批量添加的数量';
            } else if (disableAdd) {
                return '您的安全组规则已达到配额上限！';
            }
            return '';
        },
        allowAllPort() {
            const tempId = this.data.get('tempId');
            let ruleList = this.data.get('datasource');
            let allowAllrule = ruleList.filter(item => {
                return (
                    item.protocol === 'all' &&
                    item.portRange === '1-65535' &&
                    item.action === 'allow' &&
                    ((item.ethertype === IpVersion.IPV4 &&
                        (item.remoteIP === 'all' || item.remoteIP === '0.0.0.0/0')) ||
                        (item.ethertype === IpVersion.IPV6 && (item.remoteIP === 'all' || item.remoteIP === '::/0')))
                );
            });
            let length = allowAllrule.length;
            if (this.data.get('ipV6GatewayWhiteList')) {
                if (length === 2 || tempId === '3') {
                    return true;
                }
                return false;
            }
            if (length === 1) {
                return true;
            }
            return false;
        },
        sourceList() {
            let result = [{text: '源IP', value: 'user'}];
            if (this.data.get('ruleType') === 'out') {
                result[0].text = '目的IP';
            }
            let instance = this.data.get('instance') || {};
            const whiteList = window.$storage.get('commonWhite');
            if (whiteList?.IpCollectionWhiteList) {
                result.push({text: '参数模板', value: 'ipSet'});
            }
            return result;
        }
    };
    static filters = {
        priority(row: Record<string, any>) {
            if (!row.priority) {
                return u.escape(row.priority);
            }
            return row.priority;
        },
        protocol(row: Record<string, any>) {
            if (row.protocol === 'all') {
                return '全部协议';
            }
            return u.escape(row.protocol);
        },
        portRange(row: Record<string, any>) {
            return u.escape(row.portRange);
        },
        localPortRange(row: Record<string, any>) {
            if (row.protocol === 'all' || row.protocol === 'icmp') {
                return '不涉及';
            }
            if (!row.localPortRange) {
                return '1-65535';
            }
            return u.escape(row.localPortRange);
        },
        source(row: Record<string, any>) {
            if (row.ipCollectionType) {
                if (row.ipCollectionType === 1) {
                    return 'IP地址组：' + row.ipCollectionId;
                }
                return 'IP地址族：' + row.ipCollectionId;
            }
            let ipParamList = this.data.get('ipParamList') || [];
            let ipGroupList = this.data.get('ipGroupList') || [];
            if (row.ipSet) {
                let text = '';
                if (ipParamList.findIndex(item => item.ipSetUuid === row.ipSet) > -1) {
                    text = 'IP地址组：' + ipParamList.find(item => item.ipSetUuid === row.ipSet).ipSetId;
                } else {
                    text = 'IP地址族：' + ipGroupList.find(item => item.ipGroupUuid === row.ipSet).ipGroupId;
                }
                return text;
            }
            if (row.source === 'user') {
                return (
                    (this.data.get('ruleType') === 'in' ? '源IP：' : '目的IP：') +
                    (row.remoteIP === 'all'
                        ? row.ethertype === IpVersion.IPV4
                            ? row.ksource || '0.0.0.0/0'
                            : row.ksource || '::/0'
                        : u.escape(row.remoteIP))
                );
            }
            let id = row.remoteGroupShortId || row.remoteGroupId;
            return '安全组：' + u.escape(row.remoteGroupName) + (id ? '（' + u.escape(id) + '）' : '');
        },
        action(row: Record<string, any>) {
            return row.action === 'allow' ? '允许' : '拒绝';
        },
        remark(row: Record<string, any>) {
            return row.remark || '-';
        },
        localIp(row: Record<string, any>) {
            return (
                (this.data.get('ruleType') === 'in' ? '目的IP：' : '源IP：') +
                (row.localIp === 'all'
                    ? row.ethertype === IpVersion.IPV4
                        ? '0.0.0.0/0'
                        : '::/0'
                    : u.escape(row.localIp))
            );
        }
    };
    initData() {
        return {
            ruleTypeList: [
                {
                    text: '入站',
                    value: 'in'
                },
                {
                    text: '出站',
                    value: 'out'
                }
            ],
            ruleType: 'in',
            ethertypeList: IpVersion.toArray(),
            ruleIpTypeList: [
                {
                    text: '全部规则',
                    value: 'all'
                },
                {
                    text: 'IPv4',
                    value: 4
                },
                {
                    text: 'IPv6',
                    value: 6
                }
            ],
            ruleIpType: 'all',
            selectedRule: [],
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: [],
                    disabledIndex: []
                },
                columns: [
                    {
                        name: 'ethertype',
                        label: '类型',
                        width: 110,
                        render(item) {
                            if (!item.edit) {
                                return u.escape(item.ethertype);
                            }
                            return item.ethertype;
                        }
                    },
                    {
                        name: 'priority',
                        label: '优先级',
                        width: 110
                    },
                    {
                        name: 'protocol',
                        label: '协议',
                        width: 160,
                        render(item) {
                            if (!item.edit) {
                                if (item.protocol === 'all') {
                                    return '全部协议';
                                }
                                return u.escape(item.protocol);
                            }
                            return item.protocol;
                        }
                    },
                    {
                        name: 'portRange',
                        label: '端口',
                        width: 100,
                        render(item) {
                            if (!item.edit) {
                                return u.escape(item.portRange);
                            }
                            return item.portRange;
                        }
                    },
                    {
                        name: 'source',
                        label: '来源',
                        width: 150,
                        render(item) {
                            if (!item.edit) {
                                if (item.source === 'user') {
                                    return (
                                        (this.id === 'rulesInTable' ? '源IP：' : '目的IP：') +
                                        (item.remoteIP === 'all'
                                            ? item.ethertype === IpVersion.IPV4
                                                ? '0.0.0.0/0'
                                                : '::/0'
                                            : u.escape(item.remoteIP))
                                    );
                                }
                                let id = item.remoteGroupShortId || item.remoteGroupId;
                                return (
                                    '安全组：' + u.escape(item.remoteGroupName) + (id ? '（' + u.escape(id) + '）' : '')
                                );
                            }
                            return item.source;
                        }
                    },
                    {
                        name: 'action',
                        label: '策略',
                        width: 110
                    },
                    {
                        name: 'updatedTime',
                        label: '更新时间',
                        width: 120,
                        render(item) {
                            return utcToTime(item.updatedTime);
                        }
                    },
                    {
                        name: 'remark',
                        label: '备注',
                        width: 110
                    },
                    {
                        name: 'opt',
                        label: '操作',
                        width: 100,
                        fixed: 'right'
                    }
                ],
                inDatasource: [],
                outDatasource: []
            },
            securityGroupList: [],
            ipV6GatewayWhiteList: true,
            isDetail: false,
            protocolList: [
                {text: '全部协议', value: 'all'},
                {text: 'tcp', value: 'tcp'},
                {text: 'udp', value: 'udp'},
                {text: 'icmp', value: 'icmp'},
                {text: 'tcp(HTTP)', value: 'tcp_http'},
                {text: 'tcp(HTTPS)', value: 'tcp_https'},
                {text: 'tcp(FTP)', value: 'tcp_ftp'},
                {text: 'tcp(RDP)', value: 'tcp_rdp'},
                {text: 'udp(DNS)', value: 'udp_dns'},
                {text: 'tcp(DNS)', value: 'tcp_dns'},
                {text: 'tcp(POP3)', value: 'tcp_pop3'},
                {text: 'tcp(MYSQL)', value: 'tcp_mysql'},
                {text: 'tcp(SQL SERVER)', value: 'tcp_sql_server'},
                {text: 'tcp(SNMP)', value: 'tcp_snmp'},
                {text: 'tcp(SMTP)', value: 'tcp_smtp'},
                {text: 'tcp(SNMP TRAP)', value: 'tcp_snmp_trap'},
                {text: 'tcp(SSH)', value: 'tcp_ssh'}
            ],
            ethertypes: [],
            protocols: [],
            portRanges: [],
            sources: [],
            remoteGroupIds: [],
            editRow: [],
            remoteIPs: [],
            names: [],
            actions: [],
            remarks: [],
            prioritys: [],
            portRangeDisable: [],
            portRangeErr: [],
            remoteIPErr: [],
            ipSetErr: [],
            rules: [],
            securityGroupsIdNameMap: {},
            isEdit: false,
            ruleQuota: {
                ingressQuota: {
                    total: 50
                },
                egressQuota: {
                    total: 50
                }
            },
            instance: null,
            editArray: [],
            actionList: [
                {text: '允许', value: 'allow'},
                {text: '拒绝', value: 'deny'}
            ],
            ipSetResourceList: [],
            ipSetList: [],
            pager: {
                size: 10,
                page: 1,
                total: 0
            },
            localPortRanges: [],
            localIps: [],
            localPortRangeDisable: [],
            localPortRangeErr: [],
            localIpErr: [],
            associatedNum: 0,
            FLAG,
            handleGetPopupContainer: () => document.body
        };
    }
    inited() {
        this.getIpSetList();
        this.initTableColumns();
        this.ipV6GatewayWhiteList();
        if (!this.data.get('isDetail')) {
            // 初始化模版
            this.setInitTemp();
            // 切换模版
            this.watch('tempId', value => {
                this.setInitTemp();
            });
        }
        this.watchStateQueue();
    }
    attached() {
        let array = [this.checkRuleQuota(), this.getAllSecurityGroups()];
        if (this.data.get('isDetail')) {
            array.push(this.getDetail());
        }
        Promise.all(array);
    }
    mergeInstance() {
        this.$http
            .getEnterpriseSecurityInstances({
                esgUuid: this.data.get('id'),
                instanceTypes: 'BCC,BBC,ENI,BLB,SNIC'
            })
            .then(res => {
                let instance = this.data.get('instance');
                this.data.set('instance', {...instance, ...res});
            });
    }
    setInitTemp() {
        const tempId = this.data.get('tempId');
        const temp = enterpriseSecurityTemp.fromValue(tempId)?.temp;
        const inCustomTemp = temp.in;
        const outCustomTemp = temp.out;
        const inInstanceRuleTemp = u.map(inCustomTemp, item => {
            const {name, protocol, portRange, ethertype, ksource, action, priority, localPortRange} = item;
            return new Rule(name, protocol, portRange, ethertype, localPortRange, action, priority, '', ksource);
        });
        const outInstanceRuleTemp = u.map(outCustomTemp, item => {
            const {name, protocol, portRange, ethertype, action, priority, localPortRange} = item;
            return new Rule(name, protocol, portRange, ethertype, localPortRange, action, priority, '');
        });
        // 切换模版即覆盖原有规则
        this.data.set(`table.inDatasource`, inInstanceRuleTemp);
        this.data.set(`table.outDatasource`, outInstanceRuleTemp);
    }
    initTableColumns() {
        this.data.set(
            'table.columns',
            this.data.get('table.columns').map(item => {
                item.name === 'portRange' && (item.label = '目的端口');
                return item;
            })
        );
        this.data.splice('table.columns', [
            5,
            0,
            {
                name: 'localPortRange',
                label: '源端口',
                width: 110,
                render(item) {
                    if (!item.edit) {
                        return u.escape(item.localPortRange);
                    }
                    return item.localPortRange;
                }
            }
        ]);
        this.data.splice('table.columns', [
            4,
            0,
            {
                name: 'localIp',
                label: '目的IP',
                width: 150,
                render(item) {
                    if (!item.edit) {
                        return (
                            '目的IP：' +
                            (item.localIp === 'all'
                                ? item.ethertype === IpVersion.IPV4
                                    ? '0.0.0.0/0'
                                    : '::/0'
                                : u.escape(item.localIp))
                        );
                    }
                    return item.localIp;
                }
            }
        ]);
    }
    watchStateQueue() {
        this.watch('datasource', datas => {
            this.reset();
            let editItems = [
                'protocol',
                'portRange',
                'source',
                'ethertype',
                'remoteGroupId',
                'remoteIP',
                'name',
                'esgRuleUuid',
                'action',
                'priority',
                'remark',
                'localPortRange',
                'localIp'
            ];
            datas.forEach((item, index) => {
                this.data.set(`portRangeDisable[${index}]`, false);
                this.data.set(`localPortRangeDisable[${index}]`, false);
                this.data.set(`portRangeErr[${index}]`, '');
                this.data.set(`localPortRangeErr[${index}]`, '');
                this.data.set(`remoteIPErr[${index}]`, '');
                this.data.set(`localIpErr[${index}]`, '');
                this.data.set(`ipSetErr[${index}]`, '');
                editItems.forEach(editItem => {
                    if (editItem === 'protocol' && (item[editItem] === 'icmp' || item[editItem] === 'all')) {
                        this.data.set(`portRangeDisable[${index}]`, true);
                        this.data.set(`localPortRangeDisable[${index}]`, true);
                    }
                    this.data.set(`${editItem}s[${index}]`, item[editItem]);
                });
            });
        });
        this.watch('vpcId', item => {
            this.getAllSecurityGroups();
        });
    }
    ruleTypeChange(e: Event) {
        let tableColumns = this.data.get('table.columns');
        if (e.value === 'in') {
            let columns = u.map(tableColumns, item => {
                item.name === 'portRange' && (item.label = '目的端口');
                item.name === 'source' && (item.label = '来源');
                return item;
            });
            this.data.set('table.columns', columns);
            this.nextTick(() => {
                this.data.splice('table.columns', [3, 2]);
                this.data.splice('table.columns', [
                    5,
                    0,
                    {
                        name: 'localPortRange',
                        label: '源端口',
                        width: 90,
                        render(item) {
                            if (!item.edit) {
                                return u.escape(item.localPortRange);
                            }
                            return item.localPortRange;
                        }
                    }
                ]);
                this.data.splice('table.columns', [
                    4,
                    0,
                    {
                        name: 'localIp',
                        label: '目的IP',
                        width: 150,
                        render(item) {
                            if (!item.edit) {
                                return (
                                    '目的IP：' +
                                    (item.localIp === 'all'
                                        ? item.ethertype === IpVersion.IPV4
                                            ? '0.0.0.0/0'
                                            : '::/0'
                                        : u.escape(item.localIp))
                                );
                            }
                            return item.localIp;
                        }
                    }
                ]);
            });
        } else {
            let columns = u.map(tableColumns, item => {
                item.name === 'portRange' && (item.label = '目的端口');
                item.name === 'source' && (item.label = '目的');
                return item;
            });
            this.data.set('table.columns', columns);
            this.nextTick(() => {
                this.data.splice('table.columns', [4, 1]);
                this.data.splice('table.columns', [5, 1]);
                this.data.splice('table.columns', [
                    3,
                    0,
                    {
                        name: 'localPortRange',
                        label: '源端口',
                        width: 90,
                        render(item) {
                            if (!item.edit) {
                                return u.escape(item.localPortRange);
                            }
                            return item.localPortRange;
                        }
                    }
                ]);
                this.data.splice('table.columns', [
                    4,
                    0,
                    {
                        name: 'localIp',
                        label: '源IP',
                        width: 90,
                        render(item) {
                            if (!item.edit) {
                                return (
                                    '源IP：' +
                                    (item.localIp === 'all'
                                        ? item.ethertype === IpVersion.IPV4
                                            ? '0.0.0.0/0'
                                            : '::/0'
                                        : u.escape(item.localIp))
                                );
                            }
                            return item.localIp;
                        }
                    }
                ]);
            });
        }
        // 如果有新增则删除该项
        if (this.data.get('editType') === 'add') {
            this.editCancel('', '', 'add');
        }
        this.data.set('ruleType', e.value);
        this.resetTable();
        this.clearDisabledIndex();
        this.haveAllPortRule();
        this.resetEdit();
        if (this.data.get('isDetail')) {
            // 切换出入站规则时重置分页
            this.data.set('pager.page', 1);
            this.data.set('pager.size', 10);
            this.getDetail();
        }
    }
    getParamsList() {
        const datasource = this.data.get('filterDatasource');
        // 根据ipCollectionUuid去重并保存对应的安全组规则id，规则id暂时没用到，作为保留项
        const ipSetMap = {};
        const ipGroupMap = {};
        u.each(datasource, item => {
            const {esgRuleUuid, ipCollectionType, ipCollectionUuid} = item;
            if (ipCollectionType === 1) {
                if (!ipSetMap[ipCollectionUuid]) {
                    ipSetMap[ipCollectionUuid] = [];
                    ipSetMap[ipCollectionUuid].push(esgRuleUuid);
                } else {
                    ipSetMap[ipCollectionUuid].push(esgRuleUuid);
                }
            }
            if (ipCollectionType === 2) {
                if (!ipGroupMap[ipCollectionUuid]) {
                    ipGroupMap[ipCollectionUuid] = [];
                    ipGroupMap[ipCollectionUuid].push(esgRuleUuid);
                } else {
                    ipGroupMap[ipCollectionUuid].push(esgRuleUuid);
                }
            }
        });

        // 拿到去重后的ip地址组、族 ipCollectionUuid
        const ipSetUuids = Object.keys(ipSetMap);
        const ipGroupUuids = Object.keys(ipGroupMap);

        // 服务batch query限制
        const ipSetUuidsChunk = u.chunk(ipSetUuids, 10);
        const ipGroupUuidsChunk = u.chunk(ipGroupUuids, 10);

        // 生成必要请求队列
        const ipSetPromiseQueue = ipSetUuidsChunk.map(item => {
            return this.$http.batchQueryIpSet({ipSetUuids: item});
        });
        const ipGroupPromiseQueue = ipGroupUuidsChunk.map(item => {
            return this.$http.batchQueryIpGroup({ipGroupUuids: item});
        });

        // 拉取当前规则下所有ip、ip地址组
        Promise.all([...ipSetPromiseQueue, ...ipGroupPromiseQueue]).then(res => {
            if (res?.length) {
                const flatRes: Array<any> = u.flatten(res);
                const datasourceWithIpAdress = u.map(datasource, item => {
                    const {ipCollectionUuid} = item;
                    if (ipCollectionUuid) {
                        const existParamsItem = flatRes.find(ipItem =>
                            [ipItem.ipSetUuid, ipItem.ipGroupUuid].includes(ipCollectionUuid)
                        );
                        let ipAddressList = [];
                        if (existParamsItem) {
                            const {ipSets, ipAddresses} = existParamsItem;
                            ipAddressList = (ipAddresses || ipSets || []).map(ip => ip.ipAddress || ip.ipSetId);
                        }
                        return {...item, ipAddressList};
                    }
                    return item;
                });
                this.data.set('filterDatasource', datasourceWithIpAdress);
            }
        });
    }
    getDetail() {
        this.data.set('table.inDatasource', []);
        this.data.set('table.outDatasource', []);
        this.data.set('table.loading', true);
        let payload = this.getPayload();
        return this.$http.getEnterpriseSecurityRule(payload, kXhrOptions.customSilent).then(result => {
            this.data.set('table.loading', false);
            result.result.forEach(item => {
                let newItem = Rule.fromJSON(item);
                if (item.ipCollectionUuid) {
                    newItem.source = 'ipSet';
                }
                newItem.ipCollectionType = item.ipCollectionType;
                newItem.ipCollectionUuid = item.ipCollectionUuid;
                newItem.ipCollectionId = item.ipCollectionId;
                newItem.direction = item.direction;
                if (item.direction === 'ingress') {
                    this.data.push('table.inDatasource', newItem);
                } else if (item.direction === 'egress') {
                    this.data.push('table.outDatasource', newItem);
                }
                this.clearDisabledIndex();
                this.haveAllPortRule();
            });
            this.getParamsList();
            this.data.set(`pager.total`, result.totalCount);
        });
    }
    tableSelected(e: Event) {
        this.data.set('selectedRule', e.value.selectedItems);
    }
    deleteRule() {
        let ruleType = this.data.get('ruleType');
        let datasource = this.data.get(`table.${ruleType}Datasource`);
        let selectedIndex = this.data.get('table.selection.selectedIndex');
        let result = [];
        let securityIndex = [];
        selectedIndex.map(index => {
            result.push(datasource[index].esgRuleUuid);
            securityIndex.push(index);
        });
        this.deleteClient(result, securityIndex);
    }

    deleteClient(securityGroupRuleIds: Record<string, any>, securityIndex: number[] = []) {
        if (!this.data.get('isDetail')) {
            const ruleType = this.data.get('ruleType');
            const datasource = this.data.get(`table.${ruleType}Datasource`);
            const ruleList = [];
            datasource.map((item, index) => {
                if (!securityIndex.includes(index)) {
                    ruleList.push(item);
                }
            });
            this.data.set(`table.${ruleType}Datasource`, ruleList);
            this.resetTable();
            this.clearDisabledIndex();
            this.resetEdit();
            this.haveAllPortRule();
            this.data.set('changeFrom', '');
            return;
        }

        const callback = () => {
            return this.$http
                .enterpriseSecurityDeleteRule({
                    esgUuid: this.data.get('id'),
                    esgRuleUuids: securityGroupRuleIds
                })
                .then(() => this.resetTable());
        };
        this.submitEditRule(callback, 'delete');
    }

    editClient(rule: Record<string, any>, index: number) {
        if (!this.data.get('isDetail')) {
            const ruleType = this.data.get('ruleType');
            this.data.set(`table.${ruleType}Datasource[${index}]`, rule);
            this.resetEdit(index);
            this.clearDisabledIndex();
            this.data.set('editType', '');
            return;
        }

        const ruleType = this.data.get('ruleType');
        let payload = {
            esgUuid: this.data.get('id'),
            direction: ruleType === 'in' ? 'ingress' : 'egress',
            esgRuleUuid: rule.esgRuleUuid,
            protocol: rule.protocol,
            portRange: u.trim(rule.portRange),
            ethertype: rule.ethertype,
            name: rule.name,
            remoteGroupId: rule.remoteGroupId,
            remoteIpPrefix: u.trim(rule.remoteIP),
            action: rule.action,
            priority: rule.priority,
            remark: rule?.remark?.replace(/[\t]+/g, ''),
            localIpPrefix: rule.localIp
        };
        if (rule.protocol !== 'icmp' && rule.protocol !== 'all') {
            payload.localPortRange = u.trim(rule.localPortRange);
        }
        if (rule.ipSet) {
            if (rule.source !== 'user') {
                let ipGroupList = this.data.get('ipGroupList') || [];
                payload.ipCollectionType = 1;
                if (ipGroupList.findIndex(i => i.ipGroupUuid === rule.ipSet) > -1) {
                    payload.ipCollectionType = 2;
                }
                payload.ipCollectionUuid = rule.ipSet;
                delete payload.remoteIpPrefix;
            }
        }
        const callback = () => {
            return this.$http.enterpriseSecurityUpdateRule(payload).then(() => this.resetEdit(index));
        };
        this.submitEditRule(callback, '');
    }
    createClient(rules: any[]) {
        if (!this.data.get('isDetail')) {
            this.clearDisabledIndex();
            this.haveAllPortRule();
            this.resetEdit();
            this.data.set('editType', '');
            this.data.set('changeFrom', '');
            return;
        }

        let reqRule = [];
        rules.map(rule => {
            if (rule.protocol === 'icmp') {
                rule.portRange = '';
            }
            let payload = {
                direction: this.data.get('ruleType') === 'in' ? 'ingress' : 'egress',
                remoteIpPrefix: u.trim(rule.remoteIP),
                ethertype: rule.ethertype,
                portRange: u.trim(rule.portRange),
                protocol: rule.protocol,
                remoteGroupId: rule.remoteGroupId,
                action: rule.action,
                priority: rule.priority,
                remark: rule?.remark?.replace(/[\t]+/g, ''),
                localIpPrefix: rule.localIp
            };
            if (rule.protocol !== 'icmp' && rule.protocol !== 'all') {
                payload.localPortRange = u.trim(rule.localPortRange);
            }
            if (rule.ipSet) {
                if (rule.source !== 'user') {
                    let ipGroupList = this.data.get('ipGroupList') || [];
                    payload.ipCollectionType = 1;
                    if (ipGroupList.findIndex(i => i.ipGroupUuid === rule.ipSet) > -1) {
                        payload.ipCollectionType = 2;
                    }
                    payload.ipCollectionUuid = rule.ipSet;
                    delete payload.remoteIpPrefix;
                }
            }
            reqRule.push(payload);
        });
        const callback = () => {
            return this.$http.enterpriseSecurityAddRule({
                esgUuid: this.data.get('id'),
                rules: reqRule
            });
        };
        this.submitEditRule(callback, '');
    }
    editConfirm(index, editType) {
        if (this.data.get(`sources[${index}]`) === 'ipSet' && !this.data.get(`ipSet[${index}]`)) {
            this.data.set(`ipSetErr[${index}]`, true);
            return;
        }
        this.data.set(`ipSetErr[${index}]`, false);

        if (this.data.get(`portRangeErr[${index}]`)) {
            return;
        }
        if (this.data.get(`remoteIPErr[${index}]`)) {
            return;
        }
        if (this.data.get(`localPortRangeErr[${index}]`)) {
            return;
        }
        if (this.data.get(`localIpErr[${index}]`)) {
            return;
        }
        let editItems = [
            'protocol',
            'portRange',
            'source',
            'remoteGroupId',
            'remoteIP',
            'name',
            'esgRuleUuid',
            'action',
            'priority',
            'remark',
            'localPortRange',
            'localIp'
        ];
        let rule = {};
        let ruleType = this.data.get('ruleType');

        editItems.forEach(editItem => {
            if (editItem === 'remoteIP') {
                let remoteIP = this.data.get(`${editItem}s[${index}]`).trim();
                rule[editItem] = remoteIP;
            } else if (editItem === 'localIp') {
                let localIp = this.data.get(`${editItem}s[${index}]`).trim();
                rule[editItem] = localIp;
            } else if (editItem === 'localPortRange') {
                let localPortRange = this.data.get(`${editItem}s[${index}]`);
                rule[editItem] = localPortRange ? localPortRange.replace(/\s+/g, '') : '1-65535';
            } else {
                rule[editItem] = this.data.get(`${editItem}s[${index}]`);
            }
        });
        // 一些快捷模板类型协议传值修改为本身的协议
        rule.protocol = this.data.get(`protocols[${index}]`).split('_')[0];
        // ip类型编辑时无法修改
        if (this.data.get('editType') === 'edit') {
            rule.ethertype = this.data.get(`table.${ruleType}Datasource[${index}].ethertype`);
        } else {
            rule.ethertype = this.data.get(`ethertypes[${index}]`);
        }
        this.data.get('securityGroupList').forEach(item => {
            if (item.value === rule.remoteGroupId) {
                rule.remoteGroupShortId = item.title;
            }
        });

        if (rule.remoteGroupId) {
            rule.remoteGroupName = this.data.get('securityGroupsIdNameMap')[rule.remoteGroupId];
        }
        rule.ipSet = this.data.get(`ipSet[${index}]`);
        rule = Rule.fromJSON(rule);

        if (rule.protocol === 'icmp') {
            rule.portRange = '';
        }

        // 添加的数据已存在于表格中，提示用户
        let originDatasource = this.data.get('instance.rules') || [];
        // 编辑的时候删除当前规则
        if (editType === 'edit') {
            originDatasource = originDatasource.filter(item => item.esgRuleUuid !== rule.esgRuleUuid);
        }
        originDatasource =
            ruleType === 'in'
                ? originDatasource.filter(item => item.direction === 'ingress')
                : originDatasource.filter(item => item.direction === 'egress');
        let array = [];
        originDatasource.forEach(item => {
            let newItem = Rule.fromJSON(item);
            if (item.ipCollectionUuid) {
                newItem.source = 'ipSet';
            }
            array.push(newItem);
        });
        if (rule.source === 'user') {
            array = array.filter(item => !item.ipCollectionUuid);
        } else {
            array = array.filter(item => item.ipCollectionUuid);
        }
        if (!rule.esgRuleUuid) {
            array = this.data.get(`table.${ruleType}Datasource`).map(item => {
                let newItem = Rule.fromJSON(item);
                if (item.ipCollectionUuid) {
                    newItem.source = 'ipSet';
                }
                return newItem;
            });
        }
        if (this.ruleIndexOfTable(rule, array, index) !== -1 && rule.source === 'user') {
            Notification.error('该条规则您已添加，请勿重复添加！');
            return;
        }
        if (editType === 'edit') {
            this.editClient(rule, index);
        } else {
            const ruleType = this.data.get('ruleType');
            this.data.set(`table.${ruleType}Datasource[${index}]`, rule);
            this.createClient([rule]);
        }
    }

    editCancel(row, index, editType) {
        if (editType === 'edit') {
            this.data.set(`portRangeErr[${index}]`, '');
            this.data.set(`localPortRangeErr[${index}]`, '');
            this.data.set(`remoteIPErr[${index}]`, '');
            this.data.set(`localIpErr[${index}]`, '');
            let editItems = [
                'protocol',
                'portRange',
                'source',
                'remoteGroupId',
                'remoteIP',
                'name',
                'esgRuleUuid',
                'action',
                'priority',
                'remark',
                'localPortRange',
                'localIp'
            ];
            editItems.forEach(editItem => {
                this.data.set(`${editItem}s[${index}]`, row[editItem]);
                if (editItem === 'protocol' && row[editItem] !== 'icmp') {
                    this.data.set(`portRangeDisable[${index}]`, false);
                    this.data.set(`localPortRangeDisable[${index}]`, false);
                }
            });
            this.resetEdit(index);
        } else {
            let ruleType = this.data.get('ruleType');
            this.data.shift(`table.${ruleType}Datasource`);
            this.resetEdit(index);
        }
        index >= 0 && this.data.set(`ipSet[${index}]`, '');
        this.data.set('editType', '');
        // 恢复复选框
        this.data.set('table.selection', {
            mode: 'multi',
            selectedIndex: []
        });
        this.haveAllPortRule();
    }

    protocolChangeMap(val, index) {
        if (val === 'all') {
            this.data.set(`portRanges[${index}]`, '1-65535');
            this.data.set(`localPortRanges[${index}]`, '不涉及');
            this.data.set(`remarks[${index}]`, '全部协议');
            this.data.set(`protocols[${index}]`, val);
            this.data.set(`portRangeDisable[${index}]`, true);
            this.data.set(`localPortRangeDisable[${index}]`, true);
        } else if (val === 'icmp') {
            this.data.set(`portRanges[${index}]`, '不涉及');
            this.data.set(`localPortRanges[${index}]`, '不涉及');
            this.data.set(`remarks[${index}]`, '');
            this.data.set(`protocols[${index}]`, val);
            this.data.set(`portRangeDisable[${index}]`, true);
            this.data.set(`localPortRangeDisable[${index}]`, true);
        } else if (val === 'tcp' || val === 'udp') {
            this.data.set(`portRangeDisable[${index}]`, false);
            this.data.set(`localPortRangeDisable[${index}]`, false);
            this.data.set(`remarks[${index}]`, '');
            this.data.set(`protocols[${index}]`, val);
            this.data.set(`portRanges[${index}]`, '1-65535');
            this.data.set(`localPortRanges[${index}]`, '1-65535');
        } else {
            let protocolItem = kCommonServices.find(item => item.val === val);
            let protocolRule = protocolItem?.rules[0];
            let items = ['portRange', 'remark', 'localPortRange'];
            // icmp端口范围禁用并更新对应的ip类型
            if (val === 'icmp_ipv4' || val === 'icmp_ipv6') {
                this.data.set(`ethertypes[${index}]`, protocolRule.ethertype);
                this.data.set(`portRangeDisable[${index}]`, true);
                this.data.set(`localPortRangeDisable[${index}]`, true);
            } else {
                this.data.set(`portRangeDisable[${index}]`, false);
                this.data.set(`localPortRangeDisable[${index}]`, false);
            }
            items.forEach(item => {
                this.data.set(`${item}s[${index}]`, protocolRule[item]);
            });
        }
    }

    itemChange(type, index, e) {
        if (type === 'protocols') {
            this.protocolChangeMap(e.value, index);
            if (e.value === 'icmp_ipv4') {
                this.data.set(`ipSet[${index}]`, '');
                this.data.set(
                    'ipSetList',
                    this.data.get('ipSetResourceList').filter(item => item.ipType === 'IPv4')
                );
            } else if (e.value === 'icmp_ipv6') {
                this.data.set(`ipSet[${index}]`, '');
                this.data.set(
                    'ipSetList',
                    this.data.get('ipSetResourceList').filter(item => item.ipType === 'IPv6')
                );
            }
        }
        if (type === 'portRanges') {
            this.data.set(`portRangeErr[${index}]`, portRange(e.value));
        }
        if (type === 'localPortRanges') {
            this.data.set(`localPortRangeErr[${index}]`, localportRange(e.value));
        }
        if (type === 'remoteIPs') {
            let errMsg = checkIp(e.value, this.data.get('ethertypes')[index]);
            if (!e.value) {
                this.data.set(`remoteIPErr[${index}]`, 'IP范围必填');
            } else if (errMsg) {
                this.data.set(`remoteIPErr[${index}]`, errMsg);
            } else {
                this.data.set(`remoteIPErr[${index}]`, '');
            }
        }
        if (type === 'localIps') {
            let errMsg = checkIp(e.value, this.data.get('ethertypes')[index]);
            if (e.value && errMsg) {
                this.data.set(`localIpErr[${index}]`, errMsg);
            } else {
                this.data.set(`localIpErr[${index}]`, '');
            }
        }
        if (type === 'sources') {
            this.data.set(`remoteGroupIds[${index}]`, []);
            this.data.set(`remoteIPErr[${index}]`, '');
            this.data.set(`ipSetErr[${index}]`, false);
        }
        // 切换类型重置
        if (type === 'ethertypes') {
            if (e.value === 'IPv6') {
                this.setIpSetList('IPv6', index);
            } else {
                this.setIpSetList('IPv4');
            }
            let baseData = {
                priority: '1000',
                ethertype: e.value,
                source: 'user',
                name: '暴露全部端口到公网和内网',
                protocol: 'all',
                remoteIP: 'all',
                portRange: '1-65535',
                action: 'allow',
                localIp: 'all',
                localPortRange: '不涉及'
            };
            this.data.set(`localPortRangeErr[${index}]`, '');
            this.data.set(`localIpErr[${index}]`, '');
            let ruletype = this.data.get('ruleType');
            if (ruletype === 'in') {
                this.data.set(`table.inDatasource[${index}]`, baseData);
            } else {
                this.data.set(`table.outDatasource[${index}]`, baseData);
            }
            this.data.set(`portRangeErr[${index}]`, '');
            this.data.set(`remoteIPErr[${index}]`, '');
            this.data.set(`ipSetErr[${index}]`, '');
        }
        if (type === 'ipSet') {
            this.data.set(`ipSetErr[${index}]`, '');
        }
        this.data.set(`${type}[${index}]`, e.value);
    }

    haveAllPortRule() {
        let ruleList = this.data.get('datasource');
        let ipv4AllPortRule = false;
        let ipv6AllPortRule = false;
        let disableIndexList = [];
        ruleList.forEach((item, index) => {
            disableIndexList.push(index);
            if (
                item.protocol === 'all' &&
                item.portRange === '1-65535' &&
                item.action === 'allow' &&
                item.ethertype === IpVersion.IPV4 &&
                (item.remoteIP === 'all' || item.remoteIP === '0.0.0.0/0')
            ) {
                ipv4AllPortRule = true;
                disableIndexList[index] = -1;
            }
            if (
                item.protocol === 'all' &&
                item.portRange === '1-65535' &&
                item.action === 'allow' &&
                item.ethertype === IpVersion.IPV6 &&
                (item.remoteIP === 'all' || item.remoteIP === '::/0')
            ) {
                ipv6AllPortRule = true;
                disableIndexList[index] = -1;
            }
        });
        return {
            ipv4AllPortRule,
            ipv6AllPortRule
        };
    }
    clearDisabledIndex() {
        this.data.set('table.selection.disabledIndex', []);
    }
    setAllPortRule(ruleType = '') {
        !ruleType && (ruleType = this.data.get('ruleType'));
        let haveAllPortRule = this.haveAllPortRule();
        let addRule = [];
        if (!haveAllPortRule.ipv4AllPortRule) {
            addRule.push(kDefaultAllRule);
        }
        if (this.data.get('ipV6GatewayWhiteList')) {
            if (!haveAllPortRule.ipv6AllPortRule) {
                addRule.push(kV6DefaultAllRule);
            }
        }
        return addRule;
    }
    allowAllPortChange(e?: any) {
        this.data.set('changeFrom', 'switch');
        // 允许访问所有端口切换标识，清空所有已选择项
        this.data.set('allowSwitchFlag', true);
        if (e?.value) {
            const addRule = this.setAllPortRule();
            if (addRule.length > 0) {
                const ruleType = this.data.get('ruleType');
                addRule.map(item => {
                    this.data.push(`table.${ruleType}Datasource`, item);
                });
                this.createClient(addRule);
            }
        } else {
            let securityGroupRuleIds = [];
            let securityIndex = [];
            let datasource = this.data.get(`table.${this.data.get('ruleType')}Datasource`);
            datasource.map((item, index) => {
                if (
                    item.protocol === 'all' &&
                    item.portRange === '1-65535' &&
                    (item.remoteIP === 'all' || item.remoteIP === '0.0.0.0/0' || item.remoteIP === '::/0') &&
                    item.action === 'allow'
                ) {
                    securityGroupRuleIds.push(item.esgRuleUuid);
                    securityIndex.push(index);
                }
            });
            this.deleteClient(securityGroupRuleIds, securityIndex);
        }
    }
    resetTable() {
        this.data.set('table.selection.selectedIndex', []);
        this.data.set('selectedRule', []);
    }

    resetEdit(index?: number) {
        index ? this.data.set(`editRow[${index}]`, false) : this.data.set('editRow', []);
        this.data.set('isEdit', false);
        this.data.set('editArray', []);
        this.fire('editting', false);
    }

    edit(row, rowIndex) {
        this.data.set(
            'ipSetList',
            this.data.get('ipSetResourceList').filter(item => item.ipType === row.ethertype)
        );
        if (row.ipCollectionUuid) {
            this.data.set(`ipSet[${rowIndex}]`, row.ipCollectionUuid);
        } else if (row.ipSet) {
            this.data.set(`ipSet[${rowIndex}]`, row.ipSet);
        }
        this.data.set('editType', 'edit');
        this.data.set('editArray', [rowIndex]);
        this.data.set(`editRow[${rowIndex}]`, true);
        this.data.set('isEdit', true);
    }
    // ipv6白名单
    ipV6GatewayWhiteList() {
        this.data.splice('protocolList', [
            3,
            1,
            {
                text: 'icmp(PING_IPV4)',
                value: 'icmp_ipv4'
            },
            {
                text: 'icmp(PING_IPV6)',
                value: 'icmp_ipv6'
            }
        ]);
    }
    checkRuleQuota() {
        let payload = {};
        const id = this.data.get('id');
        if (id) {
            payload.esgUuid = id;
        }
        return this.$http
            .enterpriseSecurityRuleQuota(payload, {'x-silent-codes': ['Esg.EsgResourceNotExist']})
            .then(result => {
                this.data.set('ruleQuota', result);
            });
    }
    // 获取安全组列表
    getAllSecurityGroups() {
        let param = {};
        if (this.data.get('vpcId')) {
            param.vpcId = this.data.get('vpcId');
        }
        return this.$http.enterpriseSecurityList(param).then(sgs => {
            let result = [];
            u.each(sgs.result, group => {
                result[group.name === '默认安全组' ? 'unshift' : 'push']({
                    text: group.name,
                    value: group.esgId,
                    esgId: group.esgId
                });
                this.data.set(`securityGroupsIdNameMap['${group.esgId}']`, group.name);
            });
            this.data.set('securityGroupList', result);
        });
    }
    ruleIndexOfTable(rule: Record<string, any>, datasource: any[], ruleIndex: number) {
        let index = -1;
        u.find(datasource, function (x, i) {
            if (i !== ruleIndex && rule.equal(x)) {
                index = i;
                return true;
            }
        });
        return index;
    }
    // 创建规则
    onCreate() {
        // 隐藏复选框
        this.data.set('table.selection', {
            mode: '',
            selectedIndex: [],
            disabledIndex: []
        });
        this.data.set('selectedRule', []);
        this.data.set('isEdit', true);
        this.data.set('editType', 'add');
        let ruletype = this.data.get('ruleType');
        let baseData = {
            priority: '1000',
            ethertype: IpVersion.IPV4,
            source: 'user',
            remark: '暴露全部端口到公网和内网',
            protocol: 'all',
            remoteIP: 'all',
            portRange: '1-65535',
            action: 'allow',
            localIp: 'all',
            localPortRange: '不涉及'
        };
        this.setIpSetList('IPv4');
        if (ruletype === 'in') {
            this.data.unshift('table.inDatasource', baseData);
        } else {
            this.data.unshift('table.outDatasource', baseData);
        }
        this.data.set('editArray', [0]);
        this.data.set('editRow[0]', true);
        this.data.set('ipSet[0]', '');
        this.fire('editting', true);
    }

    getRules() {
        return {
            in: this.data.get('table.inDatasource'),
            out: this.data.get('table.outDatasource')
        };
    }

    checkRule() {
        let originDatasource = this.data.get('instance.rules').filter(item => {
            if (this.data.get('ruleType') === 'in') {
                return item.direction === 'ingress';
            }
            return item.direction === 'egress';
        });
        let haveAllPortRule = this.haveAllPortRule();
        const dialog = new checkRule({
            data: {
                open: true,
                securityType: 'enterprise',
                ruleType: this.data.get('ruleType'),
                ruleDatasource: originDatasource,
                ipV6GatewayWhiteList: true,
                haveAllPortRule
            }
        });
        dialog.attach(document.body);
        dialog.on('checkConfirm', result => {
            const callback = () => {
                return this.$http
                    .enterpriseSecurityDeleteRule({
                        esgUuid: this.data.get('id'),
                        esgRuleUuids: result
                    })
                    .then(() => {
                        dialog.dispose && dialog.dispose();
                        this.resetTable();
                    })
                    .catch(() => {
                        dialog.data.set('submitDisabled', false);
                    });
            };
            this.submitEditRule(callback, '');
        });
    }

    leadIn() {
        let originDatasource = this.data.get('instance.rules') || [];
        let inArray = [];
        let outArray = [];
        let ingressRules = originDatasource.filter(item => item.direction === 'ingress');
        let egressRules = originDatasource.filter(item => item.direction === 'egress');
        ingressRules.forEach(item => {
            let newItem = Rule.fromJSON(item);
            if (item.ipCollectionUuid) {
                newItem.source = 'ipSet';
            }
            newItem.ipCollectionType = item.ipCollectionType;
            newItem.ipCollectionUuid = item.ipCollectionUuid;
            newItem.ipCollectionId = item.ipCollectionId;
            inArray.push(newItem);
        });
        egressRules.forEach(item => {
            let newItem = Rule.fromJSON(item);
            if (item.ipCollectionUuid) {
                newItem.source = 'ipSet';
            }
            newItem.ipCollectionType = item.ipCollectionType;
            newItem.ipCollectionUuid = item.ipCollectionUuid;
            newItem.ipCollectionId = item.ipCollectionId;
            outArray.push(newItem);
        });
        let leadDialog = new LeadRule({
            data: {
                open: true,
                ingressRules: inArray,
                egressRules: outArray,
                ruleQuota: this.data.get('ruleQuota'),
                esgUuid: this.data.get('id'),
                vpcId: this.data.get('vpcId')
            }
        });
        leadDialog.attach(document.body);
        leadDialog.on('leadComplete', () => {
            this.loadAllSecRulesData();
            this.getDetail();
            this.checkRuleQuota(); // 刷新配额
        });
    }
    leadOut() {
        let id = this.data.get('id');
        window.open('/api/network/v1/enterprise/security/rule/download?esgUuid=' + id);
    }
    reset() {
        this.data.set('ethertypes', []);
        this.data.set('protocols', []);
        this.data.set('portRanges', []);
        this.data.set('sources', []);
        this.data.set('remoteGroupIds', []);
        this.data.set('remoteIPs', []);
        this.data.set('names', []);
        this.data.set('actions', []);
        this.data.set('remarks', []);
        this.data.set('prioritys', []);
        this.data.set('esgRuleUuids', []);
        // this.data.set('changeFrom', ''); // switch弹出 dialog 取消时，开关要还原会原来的
        this.data.set('localPortRanges', []);
        this.data.set('localIps', []);
    }
    getTip() {
        let security = this.data.get('instance') || {};
        let content = '';
        if (security.associateNum) {
            content =
                '当前已经启用这一安全组规则，您的任何修改将在保存后立即生效。' +
                '请确认您所设置的安全规则对当前云服务器的正常服务无任何影响！';
        }
        return content;
    }
    afterSubmitResolver(callback: Function, type: string) {
        if (callback && typeof callback === 'function') {
            callback().then(() => {
                this.checkRuleQuota(); // 刷新下配额
                this.loadAllSecRulesData();
                this.getDetail();
                this.reset();
                this.clearDisabledIndex();
                this.resetEdit();
                // 恢复复选框
                this.data.set('table.selection', {
                    mode: 'multi',
                    selectedIndex: []
                });
                this.data.set('changeFrom', '');
                this.data.set('editType', '');
                this.haveAllPortRule();
                Notification.success(
                    `${this.data.get('editType') === 'add' ? '创建' : type === 'delete' ? '删除' : '修改'}成功`
                );
                if (this.data.get('allowSwitchFlag')) {
                    this.resetTable();
                    this.data.set('allowSwitchFlag', false);
                }
            });
        }
    }
    submitEditRule(callback: Function, type: string) {
        let content = this.getTip();
        const ruleList = this.data.get('table.inDatasource');
        const selectedIndex = this.data.get('table.selection.selectedIndex');
        if (
            !content &&
            this.data.get('ruleType') === 'in' &&
            ruleList.length !== 0 &&
            ruleList.length === selectedIndex.length
        ) {
            content =
                '您未添加任何入站开放端口，将使该云服务器无法和外部进行任何通信，您仅能通过VNC功能进行管理。是否确认继续操作？';
        }
        if (content) {
            let confirm = new Confirm({
                data: {
                    open: true,
                    content
                }
            });
            confirm.attach(document.body);
            confirm.on('confirm', () => {
                this.afterSubmitResolver(callback, type);
            });
            confirm.on('close', data => this.tipDialogCancel());
        } else {
            this.afterSubmitResolver(callback, type);
        }
    }
    tipDialogCancel() {
        const change_from = this.data.get('changeFrom');
        if (change_from === 'switch') {
            this.data.set('changeFrom', '');
            const allowAllPort = this.data.get('allowAllPort');
            this.data.set('allowAllPort', !allowAllPort);
            if (allowAllPort) {
                const datasource = this.data.get(`table.${this.data.get('ruleType')}Datasource`);
                const excludeAllPortData = datasource.filter((item, index) => {
                    const {protocol, portRange, remoteIP, action, esgRuleUuid} = item;
                    return !(
                        protocol === 'all' &&
                        portRange === '1-65535' &&
                        ['all', '0.0.0.0/0', '::/0'].includes(remoteIP) &&
                        action === 'allow' &&
                        !esgRuleUuid
                    );
                });
                this.data.set(`table.${this.data.get('ruleType')}Datasource`, excludeAllPortData);
            }
        }
    }
    getIpSetList() {
        let all = [this.getIpList(), this.getIpGroupList()];
        Promise.all(all).then(res => {
            let array = [];
            res[0]?.result?.forEach(item => {
                array.push({
                    group: 'IP地址组',
                    text: item.name + '（' + item.ipSetId + '）',
                    value: item.ipSetUuid,
                    ipType: item.ethertype
                });
            });
            res[1]?.result?.forEach(item => {
                array.push({
                    group: 'IP地址族',
                    text: item.name + '（' + item.ipGroupId + '）',
                    value: item.ipGroupUuid,
                    ipType: item.ethertype
                });
            });
            this.data.set('ipParamList', res[0].result || []);
            this.data.set('ipGroupList', res[1].result || []);
            this.data.set('ipSetResourceList', array);
        });
    }
    getIpList() {
        let payload = {
            pageNo: 1,
            pageSize: 10000
        };
        return this.$http.ipSetList(payload, {'x-silent': true});
    }
    getIpGroupList() {
        let payload = {
            pageNo: 1,
            pageSize: 10000
        };
        return this.$http.ipGroupList(payload, {'x-silent': true});
    }
    setIpSetList(type: 'IPv4' | 'IPv6' = 'IPv4', index: number) {
        this.data.set('ipSetList', '');
        this.data.set(`ipSet[${index}]`, '');
        this.nextTick(() => {
            this.data.set(`ipSet[${index}]`, '');
            this.data.set(
                'ipSetList',
                this.data.get('ipSetResourceList').filter(item => item.ipType === type)
            );
        });
    }
    ruleIpTypeChange(e: Event) {
        this.data.set('ruleIpType', e.value);
        if (this.data.get('isDetail')) {
            this.getDetail();
        }
    }
    // 改变页数
    onPagerChange(e: Event) {
        this.data.set(`pager.page`, e.value.page);
        this.getDetail();
    }

    // 改变每页显示个数
    onPagerSizeChange(e: Event) {
        this.data.set(`pager.size`, e.value.pageSize);
        this.data.set(`pager.page`, 1);
        this.getDetail();
    }
    getPayload() {
        const {pager} = this.data.get('');
        let payload = {
            esgUuid: this.data.get('id'),
            pageNo: pager.page,
            pageSize: pager.size,
            ethertype: this.data.get('ruleIpType') === 4 ? 'IPv4' : this.data.get('ruleIpType') === 6 ? 'IPv6' : '',
            direction: this.data.get('ruleType') === 'in' ? 'ingress' : 'egress'
        };
        if (!payload.ethertype) {
            delete payload.ethertype;
        }
        return payload;
    }
    loadAllSecRulesData() {
        this.data.set('allDataLoading', true);
        this.$http
            .getEnterpriseSecurityDetail(
                {
                    esgUuid: this.data.get('id')
                },
                kXhrOptions.customSilent
            )
            .then(result => {
                this.data.set('instance', result);
            })
            .finally(() => {
                this.data.set('allDataLoading', false);
            });
    }
    handleJumpToParams(row: Record<string, any>, item: string) {
        const {ipCollectionUuid, ipCollectionType} = row;
        if (ipCollectionType === 1) {
            window.open(`#/vpc/param/address?ipSetUuid=${ipCollectionUuid}&ip=${item}`);
        } else if (ipCollectionType === 2) {
            window.open(`#/vpc/group/address?ipGroupUuid=${ipCollectionUuid}`);
        }
    }
}
export default Processor.autowireUnCheckCmpt(RuleList);
