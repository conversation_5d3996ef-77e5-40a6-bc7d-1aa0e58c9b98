.security-rule-list {
    .security-tip {
        background: #fcf7f1;
        color: #333333;
        padding: 9px 20px 9px 40px;
        margin-bottom: 10px;
        position: relative;
        line-height: 20px;
        .icon-warning {
            color: #f39000;
            margin-right: 5px;
            position: absolute;
            left: 20px;
            font-size: 12px;
        }
    }
    .disable-line {
        .s-table-cell-text {
            color: #ccc !important;
            a {
                color: #ccc !important;
            }
        }
    }
    .ruletype {
        display: inline-block;
    }
    .lead-btn,
    .ruleiptype {
        float: right;
        margin-right: 8px;
    }
    .lead-tooltip {
        float: right;
    }
    .leftbar_class {
        float: left;
        display: inline-flex;
    }
    .s-table {
        margin-top: 12px;
        .inline-tip {
            top: 3px;
            position: relative;
            .s-tip-warning {
                justify-content: center;
                .warning_class {
                    fill: #999;
                }
            }
            .s-tip:hover .s-icon path {
                fill: #2468f2 !important;
            }
        }
        .s-table-header {
            overflow: unset !important;
        }
        .s-table-body {
            overflow-x: unset !important;
        }
    }
    .alert-tip {
        .s-alert-icon-desc {
            line-height: 18px;
        }
    }
    .enterprise-warm-tip {
        display: flex;
        .label {
            width: 60px;
        }
        .value {
            flex: 1;
            line-height: 18px;
        }
    }
}
.enterprise-rule-popover {
    .s-popover-body {
        max-width: 130px;
        .s-popover-content {
            max-height: 500px;
            padding: 12px !important;
            overflow: auto;
        }
    }
    .params-item {
        cursor: pointer;
    }
    .rule-item {
        margin-top: 8px;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #151b26;
        line-height: 16px;
        font-weight: 400;
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        &:first-child {
            margin-top: 0;
        }
        &:hover {
            color: #2468f2;
        }
    }
}
