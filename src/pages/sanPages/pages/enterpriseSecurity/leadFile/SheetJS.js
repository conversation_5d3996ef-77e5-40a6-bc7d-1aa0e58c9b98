/**
 * @file leadFile/SheetJS.js
 * <AUTHOR>
 */
import {defineComponent, DataTypes} from 'san';
import {Button, Loading} from '@baidu/sui';
import {getXlsx} from '@/pages/sanPages/utils/common';
import './SheetJS.less';

const template = `
<div class="sheetjs-panel">
    <s-button class="upload-btn"
        icon="{{config.icon}}"
        disabled="{{config.disabled}}"
        is-disabled-show-tip="{{showTip}}"
        size="{{config.size}}"
        skin="{{config.skin}}"
        tip="{{config.tip}}"
        tip-position="{{config.tipPosition}}">
        {{config.text}}
        <input class="file-upload" type="file" s-ref="fileInput" title=""
            on-change="fileUpload"
            value="{=filePath=}"
            disabled="{{config.disabled}}"
            accept="{{accept}}"/>
    </s-button>
    <div class="status-show" s-if="{{statusShow}}">
        <span s-if="{{fileName}}">{{fileName}}</span>
        <s-loading size="small" loading="{{uploading}}"></s-loading>
        <span class="error-message" s-if="{{error}}">{{error}}</span>
    </div>
</div>
`;
export default defineComponent({
    template,
    components: {
        's-button': Button,
        's-loading': Loading
    },
    dataTypes: {
        /**
         * 配置button样式
         * @default {}
         */
        config: DataTypes.object,
        /**
         * 组件状态信息是否显示
         * @default true
         */
        statusShow: DataTypes.bool,
        /**
         * 配置input上传文件格式
         * @default '.csv'
         */
        accept: DataTypes.string
    },
    initData() {
        return {
            config: {
                disabled: false,
                icon: 'plus',
                text: '点击选择文件',
                showTip: false,
                size: '',
                skin: 'primary',
                tip: '',
                tipPosition: 'tc'
            },
            uploading: false,
            error: '',
            accept: '.csv',
            statusShow: true,
            files: '',
            filePath: '',
            fileName: '',
            headers: ['方向', '优先级', '协议', '目的端口', '类型', '远端IP', '策略', '备注']
        };
    },
    inited() {
        this.data.splice('headers', [6, 0, '源端口', '本端IP', '参数模板']);
    },
    fileUpload(e) {
        let files = e.target.files;
        this.data.set('files', files);
        if (files && files[0]) {
            this.fileAnalysis(files[0]);
        } else {
            this.data.set('error', '读取文件出错');
        }
    },
    csvToObject(csvString) {
        let csvarry = csvString.trim().split('\n');
        // 兼容下载文件表头末尾没有\r的问题
        if (csvarry[1] && csvarry[1][csvarry[1].length - 1].charCodeAt(0) === 13) {
            csvarry = csvString.trim().split('\r\n');
            csvarry[0].split('\n');
            csvarry = [...csvarry[0].split('\n'), ...csvarry.slice(1)];
        }
        let headerConfig = this.data.get('headers');
        let datas = [];
        let headers = csvarry[0].split(',');
        let splitChar = ',';
        // 兼容excel保存会将','保存为 String.fromCharCode(9)
        if (headers.length === 1) {
            splitChar = String.fromCharCode(9);
            headers = csvarry[0].split(String.fromCharCode(splitChar));
        }
        // 避免编码不兼容造成的读取失败
        for (let i = 0; i < headerConfig.length; i++) {
            if (headers.indexOf(headerConfig[i]) === -1) {
                headers[i] = headerConfig[i];
            }
        }
        for (let i = 1; i < csvarry.length; i++) {
            let data = {};
            let temp = csvarry[i].split(splitChar);
            // 兼容适配多端口，从第四项开始读取,遇到类型ip结束读取
            let portlist = [];
            let sourceList = [];
            let ipTypeIndex = temp.indexOf('IPv4') > -1 ? temp.indexOf('IPv4') : temp.indexOf('IPv6');
            let actionIndex = temp.indexOf('allow') > -1 ? temp.indexOf('allow') : temp.indexOf('deny');
            for (let j = 0; j < temp.length; j++) {
                // 如果存在多端口
                if (ipTypeIndex > 4) {
                    if (j >= 3 && j < ipTypeIndex) {
                        // 处理'"1' '5"'这种格式问题
                        let portNum = temp[j].replace(/[^0-9]/gi, '');
                        portlist.push(portNum);
                        // 设置多端口
                        if (j === ipTypeIndex - 1) {
                            data[headers[3]] = portlist.join(',');
                        }
                        continue;
                    }
                    // 将多端口后面匹配项对齐
                    if (j >= 3 && portlist.length > 1) {
                        data[headers[j - portlist.length + 1]] = temp[j];
                    } else {
                        data[headers[j]] = temp[j];
                    }
                }
                // 处理源端口
                if (actionIndex - ipTypeIndex > 5) {
                    if (j >= ipTypeIndex + 2 && j < actionIndex - 2) {
                        // 处理'"1' '5"'这种格式问题
                        let sourceNum = temp[j].replace(/[^0-9]/gi, '');
                        sourceList.push(sourceNum);
                        // 设置多端口
                        if (j === actionIndex - 3) {
                            data[headers[6]] = sourceList.join(',');
                        }
                        continue;
                    }
                    if (j >= ipTypeIndex + 2 && sourceList.length > 1) {
                        data[headers[j - sourceList.length - portlist.length + 2]] = temp[j];
                    } else {
                        data[headers[j]] = temp[j];
                    }
                } else {
                    // 处理端口号'"1' '5"' '"223"'这种格式问题，console为了区分离散类型导出的时候会给端口加上引号
                    if ((j === 3 || j === 6) && !temp[j].includes('-')) {
                        // 端口号兼容all ALL
                        if (temp[j].includes('all')) {
                            temp[j] = 'all';
                        } else if (temp[j].includes('ALL')) {
                            temp[j] = 'ALL';
                        } else {
                            temp[j] = temp[j].replace(/[^0-9]/gi, '');
                        }
                    }
                    data[headers[j]] = temp[j];
                }
            }
            datas.push(data);
        }
        return datas;
    },
    preParse(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            // 开始读取时触发
            reader.onloadstart = () => {
                this.data.set('uploading', true);
            };
            // 读取操作完成时触发
            reader.onload = e => {
                try {
                    let binary = e.target.result;
                    const encodingRight = binary.indexOf('�') === -1;
                    if (encodingRight) {
                        resolve('utf-8');
                    } else {
                        resolve('gbk');
                    }
                } catch (err) {
                    resolve('error');
                }
            };
            // 在读取操作发生错误时触发
            reader.onerror = e => {
                resolve('error');
            };
            reader.readAsText(file);
        });
    },
    async fileAnalysis(file) {
        const reader = new FileReader();
        const XLSX = await getXlsx();
        let isCsv = file.type.indexOf('csv');
        // 开始读取时触发
        reader.onloadstart = () => {
            this.fire('upload', {
                eventType: 'uploadStart',
                result: file
            });
            this.data.set('uploading', true);
            this.data.set('error', null);
        };
        // 读取操作完成时触发
        reader.onload = e => {
            try {
                let data = [];
                if (isCsv) {
                    let binary = e.target.result;
                    data = this.csvToObject(binary);
                } else {
                    const bstr = e.target.result;
                    /* globals XLSX */
                    const wb = XLSX.read(bstr, {type: 'binary', raw: true});
                    // Get first worksheet
                    const wsname = wb.SheetNames[0];
                    const ws = wb.Sheets[wsname];
                    /* globals XLSX */
                    data = XLSX.utils.sheet_to_json(ws);
                }
                this.fire('upload', {
                    eventType: 'uploadSuccess',
                    result: data ? data : []
                });
                this.data.set('fileName', file.name);
                this.data.set('filePath', '');
            } catch (err) {
                this.fire('upload', {
                    eventType: 'uploadError',
                    result: err
                });
                this.data.set('error', '读取文件出错');
            }
        };
        // 读取操作结束时触发，不论成功或失败
        reader.onloadend = e => {
            this.fire('upload', {
                eventType: 'uploadLoadend',
                result: e
            });
            this.data.set('uploading', false);
        };
        // 在读取操作发生错误时触发
        reader.onerror = e => {
            this.fire('upload', {
                eventType: 'uploadError',
                result: e
            });
            this.data.set('error', '读取文件出错');
        };
        // 在读取Blob时触发
        reader.onprogress = e => {
            this.fire('upload', {
                eventType: 'uploadProgress',
                result: {
                    loaded: e.loaded,
                    total: e.total
                }
            });
        };
        if (isCsv) {
            let fileType = await this.preParse(file);
            if (fileType === 'error') {
                fileItem.fileStatus = 'error';
                fileItem.message = _('读取文件出错');
                this.data.set('uploading', false);
                this.data.push('files', fileItem);
            } else {
                reader.readAsText(file, fileType);
            }
        } else {
            reader.readAsBinaryString(file);
        }
    },
    reset() {
        this.data.set('files', '');
        this.data.set('filePath', '');
    },
    retry() {
        let files = this.data.get('files');
        if (files && files[0]) {
            this.fileAnalysis(files[0]);
        } else {
            this.data.set('error', '读取文件出错');
        }
    }
});
