.security-lead-file {
    .bui-form {
        font-size: 12px;

        .bui-form-item {
            margin-top: 20px;

            &.bui-form-item-hidden {
                display: none;
            }

            .bui-form-item-label {
                color: #666;
                width: 100px;
                height: 30px;
                line-height: 30px;
                float: left;
            }

            &.bui-form-item-align-top .bui-form-item-label {
                vertical-align: top;
                padding-top: 3px;
            }

            .bui-form-item-content {
                display: inline-block;

                .bui-form-item-tip {
                    color: #999;
                    margin-top: 3px;
                }

                .bui-form-item-invalid-label {
                    color: #ea2e2e;
                }
            }

            .icon-ok-reverse {
                color: #2cb663;
                font-size: 12px;
            }
            .icon-fail-reverse {
                color: #eb5252;
                font-size: 12px;
            }

            .webuploader-pick {
                background-color: #2468f2;
                color: #ffffff;

                .icon-plus {
                    font-size: 12px;
                    margin: 0 2px;
                }
            }
            .webuploader-pick-hover {
                background-color: #209bfd;
            }

            .rule-message {
                margin-left: 20px;
                display: inline-block;
                vertical-align: middle;
                .wrong-rules {
                    color: #eb5252;
                    margin-left: 10px;
                }
            }

            .upload-message {
                display: inline-block;
                vertical-align: middle;
                height: 30px;
                line-height: 30px;
                margin-left: 10px;

                &:hover {
                    background-color: #f7f7f7;
                }

                .upload-file-name {
                    display: inline-block;
                    margin: 0 4px;
                    .icon-link {
                        font-size: 12px;
                    }
                }

                .upload-progress {
                    display: inline-block;
                    margin-right: 30px;
                }

                .upload-success {
                    color: #2cb663;
                }

                .upload-fail {
                    color: #eb5252;
                }

                .delete-file {
                    display: none;
                }
                &:hover .delete-file {
                    display: inline-block;
                }
                .skin-stringfy-button {
                    color: #000000;
                    .iconfont {
                        color: #000000;
                    }
                }
            }

            .table-wrong {
                color: #eb5252;
            }
        }
    }
    .table-wrong {
        color: #eb5252;
    }
    .table-ellipsis {
        white-space: nowrap;
        display: inline-block;
        text-overflow: ellipsis;
        overflow: hidden;
        width: 100%;
    }
    .upload-message {
        display: inline-block;
        vertical-align: middle;
        height: 30px;
        line-height: 30px;
        margin-left: 10px;

        &:hover {
            background-color: #f7f7f7;
        }

        .upload-file-name {
            display: inline-block;
            margin: 0 4px;
            .icon-link {
                font-size: 12px;
            }
        }

        .upload-progress {
            display: inline-block;
            margin-right: 30px;
        }

        .upload-success {
            color: #2cb663;
        }

        .upload-fail {
            color: #eb5252;
        }

        .delete-file {
            display: none;
        }
        &:hover .delete-file {
            display: inline-block;
        }
        .skin-stringfy-button {
            color: #000000;
            .iconfont {
                color: #000000;
            }
        }
    }
    .rule-message {
        margin-left: 20px;
        display: inline-block;
        vertical-align: middle;
        .wrong-rules {
            color: #eb5252;
            margin-left: 10px;
        }
    }
    .icon-fail-reverse {
        color: #eb5252;
        font-size: 12px;
    }
    .icon-fail-reverse:hover {
        color: #eb5252 !important;
    }
    .icon-ok-reverse {
        color: #2cb663;
        font-size: 12px;
    }
    .icon-ok-reverse:hover {
        color: #2cb663 !important;
    }
    .tip_reverse {
        position: relative;
        top: 5px;
        .s-tip {
            border: none;
        }
        .s-tip:hover {
            background: #e6f0ff;
        }
    }
    .s-table {
        .s-table-row:hover {
            .s-table-cell {
                .status_class {
                    .s-tip {
                        background: #e6f0ff !important;
                    }
                }
            }
        }
    }
    .status-warp {
        width: 12px;
    }
}
