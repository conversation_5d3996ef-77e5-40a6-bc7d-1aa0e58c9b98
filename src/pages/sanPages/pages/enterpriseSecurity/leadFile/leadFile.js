/**
 * @file network/security/leadFile.js
 * <AUTHOR>
 */
import u from 'lodash';
import {defineComponent} from 'san';
import Rule from '../util/rule';
import {html} from '@baiducloud/runtime';
import {Dialog, Form, Button, Table, Progress, Icon, Tooltip, Notification} from '@baidu/sui';
import {Tip, Empty} from '@baidu/sui-biz';
import SheetJS from './SheetJS';
import './leadFile.less';
const rules = {
    checkDirection(direction) {
        if (direction === 'ingress' || direction === 'egress') {
            return true;
        }
        return false;
    },
    checkPosition(priority) {
        let value = Number(priority);
        if (!isNaN(value) && 1 <= value <= 1000) {
            return true;
        }
        return false;
    },
    checkProtocol(protocol) {
        if (['all', 'tcp', 'udp', 'icmp', 'ALL', 'TCP', 'UDP', 'ICMP'].includes(protocol)) {
            return true;
        }
        return false;
    },
    checkPort(portRange) {
        let port = portRange.split('-');
        // 如果是区间类型
        if (port.length === 2) {
            if (
                Number(port[0]) >= 1 &&
                Number(port[0]) <= 65535 &&
                Number(port[1]) >= 1 &&
                Number(port[1]) <= 65535 &&
                Number(port[0]) < Number(port[1])
            ) {
                return true;
            }
            return false;
        } else {
            // 兼容处理多端口
            port = port[0].split(',');
            for (let i = 0; i < port.length; i++) {
                if (!(Number(port[i]) >= 1 && Number(port[i]) <= 65535) && !['ALL', 'all'].includes(port[i])) {
                    return false;
                }
            }
            return true;
        }
    },
    checkEthertype(ethertype) {
        if (ethertype === 'IPv4' || ethertype === 'IPv6') {
            return true;
        }
        return false;
    },
    checkAction(action) {
        if (action === 'allow' || action === 'deny') {
            return true;
        }
        return false;
    },
    checkIsExist(allRules, rule, type) {
        let result = true;
        for (let i = 0; i < allRules.length; i++) {
            let source = '';
            let localIp = '';
            if (!allRules[i].edit) {
                if (allRules[i].source === 'user') {
                    source =
                        allRules[i].remoteIP === 'all'
                            ? allRules[i].ethertype === 'IPv4'
                                ? '0.0.0.0/0'
                                : '::/0'
                            : u.escape(allRules[i].remoteIP);
                } else {
                    let id = allRules[i].remoteGroupShortId || allRules[i].remoteGroupId;
                    source = u.escape(allRules[i].remoteGroupName) + (id ? '(' + u.escape(id) + ')' : '');
                }
            } else {
                source = allRules[i].source;
            }
            if (allRules[i].localIp) {
                localIp =
                    allRules[i].localIp === 'all'
                        ? allRules[i].ethertype === 'IPv4'
                            ? '0.0.0.0/0'
                            : '::/0'
                        : u.escape(allRules[i].localIp);
            }
            if (
                type === rule.direction &&
                allRules[i].protocol === rule.protocol &&
                allRules[i].action === rule.action &&
                (allRules[i].portRange === '不涉及' || allRules[i].portRange === rule.portRange) &&
                allRules[i].ethertype === rule.ethertype &&
                source === rule.remoteIpPrefix &&
                (allRules[i].localIp
                    ? (allRules[i].localPortRange === '不涉及' || allRules[i].localPortRange === rule.localPortRange) &&
                      localIp === rule.localIp
                    : true)
            ) {
                result = false;
                break;
            }
        }
        return result;
    },
    checkLocalPort(localPortRange) {
        if (!localPortRange) {
            return true;
        }
        let port = localPortRange.split('-');
        // 如果是区间类型
        if (port.length === 2) {
            if (
                Number(port[0]) >= 1 &&
                Number(port[0]) <= 65535 &&
                Number(port[1]) >= 1 &&
                Number(port[1]) <= 65535 &&
                Number(port[0]) < Number(port[1])
            ) {
                return true;
            }
            return false;
        } else {
            // 兼容处理多端口
            port = port[0].split(',');
            for (let i = 0; i < port.length; i++) {
                const NumberPort = Number(port[i]);
                const isNaNFlag = isNaN(NumberPort);
                if (isNaNFlag) {
                    if (['不涉及', 'all', 'ALL'].includes(port[i])) {
                        return true;
                    } else {
                        return false;
                    }
                } else if (!(NumberPort >= 1 && NumberPort <= 65535)) {
                    return false;
                }
            }
            return true;
        }
    },
    checkLocalIsExist(allRules, rule, type) {
        let result = true;
        for (let i = 0; i < allRules.length; i++) {
            let localIp = '';
            if (!allRules[i].edit) {
                localIp =
                    allRules[i].localIp === 'all'
                        ? allRules[i].ethertype === 'IPv4'
                            ? '0.0.0.0/0'
                            : '::/0'
                        : u.escape(allRules[i].localIp);
            } else {
                localIp = allRules[i].localIp;
            }
            if (
                type === rule.direction &&
                allRules[i].protocol === rule.protocol &&
                allRules[i].action === rule.action &&
                (allRules[i].localPortRange === '不涉及' || allRules[i].localPortRange === rule.localPortRange) &&
                allRules[i].ethertype === rule.ethertype &&
                localIp === rule.localIp
            ) {
                result = false;
                break;
            }
        }
        return result;
    }
};

function renderItem(item, key) {
    if (
        (key === 'direction' && !rules.checkDirection(item.direction)) ||
        (key === 'protocol' && !rules.checkProtocol(item.protocol)) ||
        (key === 'priority' && !rules.checkPosition(item.priority)) ||
        (key === 'action' && !rules.checkAction(item.action)) ||
        (key === 'portRange' && item.protocol !== 'icmp' && !rules.checkPort(item.portRange)) ||
        (key === 'ethertype' && !rules.checkEthertype(item.ethertype)) ||
        (key === 'localPortRange' &&
            item.protocol !== 'icmp' &&
            item.protocol === 'all' &&
            !rules.checkLocalPort(item.localPortRange))
    ) {
        return '<span class="table-wrong">' + u.escape(item[key]) + '</span>';
    }
    if (key === 'action') {
        return item.action === 'allow' ? '允许' : '拒绝';
    }
    if (key === 'template') {
        return '<span title="' + u.escape(item[key]) + '" class="table-ellipsis">' + u.escape(item[key]) + '</span>';
    }

    return u.escape(item[key]);
}

const tableSchema = [
    {
        name: 'direction',
        label: '方向',
        render(item, key) {
            return renderItem(item, key);
        }
    },
    {
        name: 'priority',
        label: '优先级',
        render(item, key) {
            return renderItem(item, key);
        }
    },
    {
        name: 'protocol',
        label: '协议',
        render(item, key) {
            return renderItem(item, key);
        }
    },
    {
        name: 'portRange',
        label: '目的端口',
        render(item, key) {
            return renderItem(item, key);
        }
    },
    {
        name: 'ethertype',
        label: '类型',
        render(item, key) {
            return renderItem(item, key);
        }
    },
    {
        name: 'remoteIpPrefix',
        label: '远端IP',
        render(item, key) {
            return renderItem(item, key);
        }
    },
    {
        name: 'localPortRange',
        label: '源端口',
        render(item, key) {
            return renderItem(item, key);
        }
    },
    {
        name: 'localIp',
        label: '本端IP',
        render(item, key) {
            return renderItem(item, key);
        }
    },
    {
        name: 'template',
        label: '参数模板',
        render(item, key) {
            return renderItem(item, key);
        }
    },
    {
        name: 'action',
        label: '策略',
        render(item, key) {
            return renderItem(item, key);
        }
    },
    {
        name: 'remark',
        label: '备注',
        render(item, key) {
            return renderItem(item, key);
        }
    },
    {name: 'status', label: '检查'}
];
const template = html`
    <div>
        <s-dialog
            width="600"
            class="security-lead-file"
            closeAfterMaskClick="{{false}}"
            open="{=open=}"
            title="{{'导入规则'}}"
        >
            <div class="tip-grey">{{quotaTips}}</div>
            <s-form>
                <s-form-item label="{{'选择文件：'}}">
                    <xui-sheetjs s-ref="upload" on-upload="uploadEvent" statusShow="{{false}}"> </xui-sheetjs>
                    <div s-if="{{uploading || fileName || uploadSuccess || uploadFail}}" class="upload-message">
                        <div class="upload-file-name">
                            <s-icon name="link"></s-icon>
                            <div s-if="{{uploading}}" class="upload-progress">
                                <s-progress value="{{progressValue}}" width="80"></s-progress>
                            </div>
                            <span s-if="{{!uploading && fileName}}">{{fileName}}</span>
                        </div>
                        <span s-if="{{uploadSuccess}}" class="upload-success">{{'上传成功!'}}</span>
                        <span s-if="{{uploadFail}}" class="upload-fail">{{'文档解析失败'}}</span>
                        <s-button s-if="{{uploadFail}}" skin="stringfy" on-click="reUpload">
                            <s-icon name="bcmrefresh"></s-icon>
                            {{'重试'}}
                        </s-button>
                        <s-button class="delete-file" skin="stringfy" on-click="clearUploader">
                            <s-icon name="close"></s-icon>
                        </s-button>
                    </div>
                </s-form-item>
                <s-form-item s-if="{{fileUploaded}}">
                    {{'查看规则'}}
                    <div class="rule-message">
                        <span>{{ruleCountText}}</span>
                        <span s-if="{{ruleWrongTotal}}" class="wrong-rules">
                            {{ruleErrorText}}
                        </span>
                    </div>
                </s-form-item>
                <s-form-item s-if="{{ruleIsShow}}">
                    <s-table
                        columns="{{ruleList.schema}}"
                        loading="{{ruleList.loading}}"
                        datasource="{{ruleList.datasource}}"
                    >
                        <div slot="empty">
                            <s-empty>
                                <div slot="action"></div>
                            </s-empty>
                        </div>
                        <div slot="c-status" class="status_class">
                            <div class="status-warp" s-if="{{row.status}}"><s-icon name="ok-reverse"></s-icon></div>
                            <div class="status-warp" s-else>
                                <s-tip
                                    s-if="{{row.statusMessage}}"
                                    content="{{row.statusMessage}}"
                                    position="tc"
                                    class="tip_reverse"
                                >
                                    <s-icon name="fail-reverse"></s-icon>
                                </s-tip>
                                <s-icon s-else name="fail-reverse"></s-icon>
                            </div>
                        </div>
                    </s-table>
                </s-form-item>
            </s-form>
            <div slot="footer">
                <div style="padding-left: 300px; padding-top:20px; display: inline-block;">
                    <s-tooltip trigger="{{limitDisableMsg ? 'hover' : ''}}" placement="right">
                        <!--bca-disable-next-line-->
                        <div slot="content">{{limitDisableMsg | raw}}</div>
                        <s-button
                            disabled="{{ruleTotal === 0 || ruleWrongTotal > 0 || limitDisable}}"
                            skin="primary"
                            on-click="leadSubmit"
                        >
                            {{'确定'}}
                        </s-button>
                    </s-tooltip>
                    <s-button size="larger" on-click="leadCancel"> {{'取消'}} </s-button>
                </div>
            </div>
        </s-dialog>
    </div>
`;
export default defineComponent({
    template,
    components: {
        's-form': Form,
        's-form-item': Form.Item,
        's-button': Button,
        's-table': Table,
        's-tip': Tip,
        's-progress': Progress,
        'xui-sheetjs': SheetJS,
        's-dialog': Dialog,
        's-empty': Empty,
        's-icon': Icon,
        's-tooltip': Tooltip
    },
    initData() {
        return {
            open: false,
            ruleIsShow: false,
            ruleList: {
                datasource: [],
                schema: tableSchema,
                loading: false
            },
            ruleTotal: 0,
            ruleWrongTotal: 0,
            progressValue: 0,
            fileName: '',
            uploading: false,
            uploadSuccess: false,
            uploadFail: false,
            fileUploaded: false,
            limitDisable: false,
            limitDisableMsg: ''
        };
    },

    computed: {
        quotaTips() {
            const {ingressQuota, egressQuota} = this.data.get('ruleQuota');
            const hasCreatedInNum = ingressQuota.total - ingressQuota.free;
            const hasCreatedOutNum = egressQuota.total - egressQuota.free;
            return `入向已创建${hasCreatedInNum}条，出向已创建${hasCreatedOutNum}条，您入向还可以导入${ingressQuota.free}条,
                出向还可以导入${egressQuota.free}条，超出数量不允许导入，相同规则不允许重复导入。`;
        },
        ruleCountText() {
            const ruleTotal = this.data.get('ruleTotal');
            return `共${ruleTotal}条规则`;
        },
        ruleErrorText() {
            const ruleWrongTotal = this.data.get('ruleWrongTotal');
            return `其中${ruleWrongTotal}条出错，请修改后重试`;
        }
    },

    /**
     * 上传事件
     *
     * @param {Object} e 事件参数对象
     * @param {string} e.eventType 事件名称
     */
    uploadEvent(e) {
        let eventType = e.eventType;
        let result = e.result;
        if (eventType === 'uploadStart') {
            this.clearUploader();
            this.data.set('fileName', result.name);
        } else if (eventType === 'uploadProgress') {
            let precent = ((result.loaded / result.total) * 100).toFixed(0);
            this.data.set('uploading', true);
            this.data.set('progressValue', precent);
        } else if (eventType === 'uploadSuccess') {
            this.data.set('uploading', false);
            this.data.set('uploadSuccess', true);
            if (result.length > 50) {
                result = result.slice(0, 50);
            }
            this.getRuleCheckList(result);
        } else if (eventType === 'uploadError') {
            this.uploadError();
        }
    },

    /**
     * 上传出错
     *
     * @param {Object} e 事件参数对象
     */
    uploadError(e) {
        this.data.set('uploading', false);
        this.data.set('uploadSuccess', false);
        this.data.set('uploadFail', true);
    },

    /**
     * 重新上传
     */
    reUpload() {
        this.ref('upload').retry();
    },

    /**
     * 清空当前文件
     */
    clearUploader() {
        this.data.set('ruleIsShow', false);
        this.data.set('ruleList.datasource', []);
        this.data.set('ruleTotal', 0);
        this.data.set('ruleWrongTotal', 0);
        this.data.set('progressValue', 0);
        this.data.set('fileName', '');
        this.data.set('uploading', false);
        this.data.set('uploadSuccess', false);
        this.data.set('uploadFail', false);
        this.data.set('fileUploaded', false);
        this.ref('upload').reset();
        this.fire('enableBtnOK', false);
    },

    /**
     * 得到规则列表
     *
     * @param {string} result 规则列表
     */
    getRuleCheckList(result) {
        this.data.set('ruleTotal', result.length);
        if (result.length > 50) {
            this.data.set('limitDisable', true);
            this.data.set('limitDisableMsg', '企业安全组规则批量添加的数量不能超过50个，请您减少批量添加的数量');
        } else {
            this.data.set('limitDisable', false);
            this.data.set('limitDisableMsg', '');
        }
        let num = 0;
        let ingressRules = this.data.get('ingressRules');
        let egressRules = this.data.get('egressRules');
        result.forEach((item, index, array) => {
            tableSchema.forEach(schema => {
                if (!item[schema.name]) {
                    item[schema.name] = '';
                }
                // if (item[schema.label]) {
                item[schema.name] = item[schema.label];
                delete item[schema.label];
                // }
            });
            let cloneResult = u.cloneDeep(array).filter((param, i) => i !== index);
            let ingressNowData = cloneResult.filter(item => item.方向 === 'ingress' || item.direction === 'ingress');
            let engressNowData = cloneResult.filter(item => item.方向 === 'egress' || item.direction === 'egress');
            // 离散型源和目的端口不能出现以0开头的数字
            const trimedPortRangeArr = item.portRange.replace(/\s+/g, '').split(/,|，/g);
            const trimedLocalPortRangeArr = item.localPortRange.replace(/\s+/g, '').split(/,|，/g);
            if (
                !rules.checkDirection(item.direction) ||
                !rules.checkPosition(item.priority) ||
                !rules.checkProtocol(item.protocol) ||
                !rules.checkAction(item.action) ||
                (item.protocol === 'icmp' ? false : !rules.checkPort(item.portRange)) ||
                !rules.checkEthertype(item.ethertype) ||
                (item.protocol === 'icmp' || item.protocol === 'all'
                    ? false
                    : !rules.checkLocalPort(item.localPortRange))
            ) {
                num++;
                item.status = false;
            } else if (item.direction === 'ingress' && !rules.checkIsExist(ingressRules, item, 'ingress')) {
                num++;
                item.status = false;
                item.statusMessage = '安全组已存在相同规则，请修改后重试';
            } else if (item.direction === 'egress' && !rules.checkIsExist(egressRules, item, 'egress')) {
                num++;
                item.status = false;
                item.statusMessage = '安全组已存在相同规则，请修改后重试';
            } else if (item.direction === 'egress' && this.checkTableIsExist(engressNowData, item, 'egress')) {
                num++;
                item.status = false;
                item.statusMessage = '安全组已存在相同规则，请修改后重试';
            } else if (item.direction === 'ingress' && this.checkTableIsExist(ingressNowData, item, 'ingress')) {
                num++;
                item.status = false;
                item.statusMessage = '安全组已存在相同规则，请修改后重试';
            } else if (
                trimedPortRangeArr.some(port => port.startsWith('0')) ||
                trimedLocalPortRangeArr.some(port => port.startsWith('0'))
            ) {
                num++;
                item.status = false;
                item.statusMessage = '源或目的端口不能以“0”开头';
            } else {
                item.status = true;
            }
        });
        this.data.set('ruleWrongTotal', num);
        this.data.set('ruleList.datasource', result);
        this.data.set('fileUploaded', true);
        this.data.set('ruleIsShow', true);
        this.fire('enableBtnOK', !num);
    },

    /**
     * 导入
     *
     * @param {string} esgUuid esgUuid
     */
    getRuleImport(esgUuid) {
        let data = this.data.get('ruleList.datasource');
        let isExistRemoteTemplate = false;
        data.forEach(item => {
            // 同时填写了远端IP和参数模板不再执行
            if (isExistRemoteTemplate) {
                return false;
            }
            if (item.template) {
                let str = item.template;
                if (str.includes('IP地址组')) {
                    item.ipCollectionType = 1;
                    item.ipCollectionUuid = str.split('/')[1];
                } else if (str.includes('IP地址族')) {
                    item.ipCollectionType = 2;
                    item.ipCollectionUuid = str.split('/')[1];
                }
            }
            if (item.remoteIpPrefix && item.ipCollectionUuid) {
                isExistRemoteTemplate = true;
            }
            if (!item.remoteIpPrefix) {
                delete item.remoteIpPrefix;
            }
            item.localIpPrefix = item.localIp;
            item.remoteGroupId = [];
            delete item.localIp;
            delete item.template;
        });
        if (isExistRemoteTemplate) {
            Notification.warning('远端IP和参数模板不能同时填写，请检查。');
            return false;
        }
        this.$http.enterpriseSecurityAddRule({rules: data, esgUuid}).then(() => {
            this.data.set('open', false);
            this.fire('leadComplete');
        });
    },
    leadSubmit() {
        let ruleTotal = this.data.get('ruleTotal');
        let ruleWrongTotal = this.data.get('ruleWrongTotal');
        if (ruleTotal === 0 || ruleWrongTotal > 0) {
            return;
        }
        this.getRuleImport(this.data.get('esgUuid'));
    },
    leadCancel() {
        this.data.set('open', false);
    },
    checkTableIsExist(allRules, rule, type) {
        let result = [];
        allRules.forEach(item => {
            tableSchema.forEach(schema => {
                if (!item[schema.name]) {
                    item[schema.name] = '';
                }
                if (item[schema.label]) {
                    item[schema.name] = item[schema.label];
                    delete item[schema.label];
                }
            });
            let newItem = Rule.fromJSON(item);
            newItem.edit = true;
            result.push(newItem);
        });
        return !rules.checkIsExist(result, rule, type);
    }
});
