/*
 * @description: 安全组已绑定资源列表页
 * @file: security/pages/detail/list.js
 * @author: p<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */

import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {Notification} from '@baidu/sui';
import {OutlinedPlus, OutlinedRefresh} from '@baidu/sui-icon';
const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent} = decorators;

import rules from '../../rules';
import Confirm from '@/pages/sanPages/components/confirm';
import Bind from './bind';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import {
    ddcTypeColumns,
    ddcRemarkColumns,
    ddcIpColumns,
    blbTypeColumns,
    scsTypeColumns,
    scsIpColumns,
    snicIpColumns,
    rdsTypeColumns,
    rdsDBColumns,
    rabbitMqColumns
} from './columns';
import {associationProduct} from '@/pages/sanPages/common/enum';
import testID from '@/testId';
import './style.less';

const tpl = html` <div>
    <s-biz-page title="{{title}}" class="{{klass}}">
        <div class="list-page-tb-left-toolbar" slot="tb-left">
            <s-tooltip trigger="{{create.message ? 'hover' : ''}}" placement="right" class="inline_class">
                <div slot="content">
                    <!--bca-disable-next-line-->
                    {{create.message | raw}}
                </div>
                <s-button
                    disabled="{{create.disable}}"
                    skin="primary"
                    on-click="relateInstance"
                    track-id="vpc_enterpriseSecurity_association_{{bindTxt | associationProduct}}"
                    data-test-id="${testID.enterpriseSecurity.detailBindInstance}{{type}}"
                >
                    <outlined-plus />{{bindTxt}}
                </s-button>
            </s-tooltip>
            <s-tooltip
                trigger="{{cancelRelated.disable ? 'hover' : ''}}"
                placement="right"
                class="inline_class left_class"
            >
                <!--bca-disable-next-line-->
                <div slot="content">{{cancelRelated.message | raw}}</div>
                <s-button
                    on-click="onRelease"
                    disabled="{{cancelRelated.disable}}"
                    data-test-id="${testID.enterpriseSecurity.detailUnbindInstance}{{type}}"
                >
                    {{'取消关联'}}</s-button
                >
            </s-tooltip>
        </div>
        <div slot="tb-right" class="inline_class">
            <s-search
                width="{{230}}"
                class="search-warp"
                value="{=keyword=}"
                placeholder="{{placeholder}}"
                on-search="onSearch"
                data-test-id="${testID.enterpriseSecurity.detailSearch}{{type}}"
            >
                <s-select
                    slot="options"
                    width="120"
                    datasource="{{keywordTypeList}}"
                    value="{=keywordType=}"
                    on-change="keywordTypeChange($event)"
                >
                </s-select>
            </s-search>
            <s-button
                class="s-icon-button"
                on-click="refresh"
                data-test-id="${testID.enterpriseSecurity.detailRefresh}{{type}}"
                ><outlined-refresh class="icon-class"
            /></s-button>
        </div>
        <s-table
            class="instance_table"
            columns="{{table.columns}}"
            loading="{{table.loading}}"
            error="{{table.error}}"
            datasource="{{table.datasource}}"
            on-selected-change="tableSelected($event)"
            on-sort="onSort"
            selection="{=table.selection=}"
            data-test-id="${testID.enterpriseSecurity.detailTable}{{type}}"
        >
            <div slot="error">
                啊呀，出错了？
                <a href="javascript:;" on-click="refresh">{{'重新加载'}}</a>
            </div>
            <div slot="empty">
                <s-empty>
                    <div slot="action"></div>
                </s-empty>
            </div>
            <div slot="c-opt" class="opreation">
                <s-tooltip s-if="row.associationNum<=1 && type !== 'blb'" trigger="hover" placement="right">
                    <!--bca-disable-next-line-->
                    <div slot="content">{{(row | releaseTip) | raw}}</div>
                    <s-button skin="stringfy" style="color:#999">{{'取消关联'}}</s-button>
                </s-tooltip>
                <s-button s-else skin="stringfy" on-click="onRelease('row', row)">{{'取消关联'}}</s-button>
            </div>
        </s-table>
        <s-pagination
            s-if="{{pager.total}}"
            slot="footer"
            layout="{{'total, pageSize, pager, go'}}"
            pageSize="{{pager.size}}"
            total="{{pager.total}}"
            page="{{pager.page}}"
            on-pagerChange="onPageChange"
            on-pagerSizeChange="onPageSizeChange"
        />
    </s-biz-page>
</div>`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@asComponent('@enterprise-security-instance-list')
class SecurityInstanceList extends Component {
    static components = {
        'outlined-plus': OutlinedPlus,
        'outlined-refresh': OutlinedRefresh
    };
    static filters = {
        releaseTip(item) {
            if (item.associationNum <= 1) {
                return '一个实例至少关联一个安全组，该实例只关联了一个安全组，无法进行该操作';
            } else {
                return '';
            }
        },
        associationProduct(text) {
            return associationProduct.getValueFromText(text);
        }
    };
    initData() {
        return {
            klass: ['enterpriseSecurity-instance-list'],
            vpcId: '',
            selectedItems: [],
            title: '',
            emptyText: '暂无数据',
            keywordTypeList: [
                {text: '实例名称', value: 'name'},
                {text: '实例ID', value: 'instanceId'},
                {text: '实例内网IP', value: 'internalIp'},
                {text: '实例公网IP', value: 'publicIp'}
            ],
            placeholder: '请输入实例名称进行搜索',
            keywordType: 'name',
            keyword: '',
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                columns: [
                    {
                        name: 'name',
                        label: '实例名称/ID',
                        sortable: true,
                        width: 100,
                        render(item) {
                            let name = u.escape(item.name) || u.escape(item.scsName);
                            let id =
                                u.escape(item.id) ||
                                u.escape(item.eniId) ||
                                u.escape(item.blbShortId) ||
                                u.escape(item.instanceId) ||
                                u.escape(item.shortId);
                            return `<span class="truncated" title="${name}" data-testid="${testID.enterpriseSecurity.detailTableName}{{rowIndex}}">
                                        ${name}
                                    </span>
                                    </br><span class="truncated" title="${id}">${id}</span>`;
                        }
                    },
                    {
                        name: 'internalIp',
                        label: '实例IP',
                        sortable: true,
                        width: 120,
                        render(item) {
                            if (item.eniPrivateIps) {
                                let result = [];
                                u.each(item.eniPrivateIps, (data, index) => {
                                    if (index <= 2) {
                                        let ipData = FLAG.NetworkSupportEip
                                            ? (data.eip || '-') +
                                              '（公）/' +
                                              '&nbsp;&nbsp;' +
                                              (data.privateIp || '-') +
                                              '（内）'
                                            : (data.privateIp || '-') + '（内）';
                                        result.push(ipData);
                                    }
                                });

                                if (item.eniPrivateIps.length > 3) {
                                    result.push('...');
                                }

                                return result.join('<br>');
                            } else if (item.blbId) {
                                let ipv6Data = FLAG.NetworkSupportEip
                                    ? (item.publicIp || '-') + '（公）/' + (item.internalIp || '-') + '（内）'
                                    : (item.internalIp || '-') + '（内）';
                                if (item.blbType === 'ipv6') {
                                    ipv6Data = FLAG.NetworkSupportEip ? '-(公)' + '<br>' + '-(内)' : '-(内)';
                                }
                                return ipv6Data;
                            } else {
                                return FLAG.NetworkSupportEip
                                    ? (item.publicIp || '-') + '（公）/' + (item.internalIp || '-') + '（内）'
                                    : (item.internalIp || '-') + '（内）';
                            }
                        }
                    },
                    {
                        name: 'ipv6',
                        label: 'IPv6 IP',
                        width: 100,
                        render(item) {
                            return item.ipv6 || '-';
                        }
                    },
                    {
                        name: 'desc',
                        label: '描述',
                        width: 80,
                        render(item) {
                            let desc = u.escape(item.desc) || u.escape(item.description);
                            return desc || '-';
                        }
                    },
                    {
                        name: 'opt',
                        label: '操作',
                        width: 80
                    }
                ],
                datasource: []
            },
            order: {
                orderBy: 'createTime',
                order: 'desc'
            },
            pager: {
                size: 10,
                page: 1,
                total: 0
            },
            create: {
                disable: false
            },
            cancelRelated: {
                disable: false
            },
            bindTxt: '关联云服务器'
        };
    }

    async inited() {
        let type = this.data.get('context').type;
        this.initTab(type);
    }

    initTab(type) {
        // 设置不同实例id
        let idMap = {
            bcc: 'instanceId',
            bbc: 'instanceId',
            eni: 'eniId',
            blb: 'instanceId',
            ddc: 'id',
            snic: 'instanceId',
            scs: 'id',
            rds: 'id',
            rabbitmq: 'instanceId'
        };
        let instanceId = idMap[type] || 'id';
        this.data.splice('table.columns', [
            0,
            1,
            {
                name: 'name',
                label: '实例名称/ID',
                sortable: true,
                width: 100,
                render(item) {
                    let name = u.escape(item.name) || u.escape(item.scsName);
                    let id = u.escape(item[instanceId] || item['instanceId']);
                    return `<span class="truncated" title="${name}">
                              ${name}
                          </span>
                          </br><span class="truncated" title="${id}">${id}</span>`;
                }
            }
        ]);
        let title = '关联云服务器';
        if (type === 'eni') {
            title = '关联弹性网卡';
            this.data.splice('table.columns', [2, 1]);
        } else if (type === 'snic') {
            title = '关联服务网卡';
            this.data.splice('table.columns', [2, 1]);
            this.data.splice('table.columns', [1, 1, snicIpColumns]);
            this.data.pop('keywordTypeList');
        } else if (type === 'bbc') {
            title = '关联弹性裸金属服务器';
            this.data.splice('table.columns', [2, 1]);
        } else if (type === 'blb') {
            title = '关联负载均衡';
            this.data.splice('table.columns', [1, 0, blbTypeColumns]);
        } else if (type === 'ddc') {
            title = '关联云数据库专属集群';
            this.data.splice('table.columns', [1, 0, ddcTypeColumns]);
            this.data.splice('table.columns', [2, 1, ddcIpColumns]);
            this.data.splice('table.columns', [3, 2]);
            this.data.splice('table.columns', [3, 0, ddcRemarkColumns]);
            this.data.pop('keywordTypeList');
        } else if (type === 'scs') {
            title = _('关联云数据库SCS');
            this.data.splice('table.columns', [1, 0, scsTypeColumns]);
            this.data.splice('table.columns', [2, 1, scsIpColumns]);
            this.data.splice('table.columns', [3, 2]);
        } else if (type === 'rds') {
            title = _('关联云数据库RDS');
            this.data.splice('table.columns', [1, 0, rdsTypeColumns]);
            this.data.splice('table.columns', [2, 1, rdsDBColumns]);
        } else if (type === 'rabbitmq') {
            title = _('关联消息服务 for RabbitMQ');
            this.data.splice('table.columns', [1, 2]);
            this.data.splice('table.columns', [1, 0, rabbitMqColumns]);
            this.data.splice('keywordTypeList', [2, 2]);
            this.data.push('keywordTypeList', {text: '架构类型', value: 'type'});
        }
        if (!FLAG.NetworkSupportEip) {
            let keywordTypeList = this.data.get('keywordTypeList');
            this.data.set(
                'keywordTypeList',
                keywordTypeList.filter(item => item.value !== 'publicIp')
            );
        }
        this.data.set('bindTxt', title);
        this.data.set('title', title);
        let {cancelRelated} = checker.check(rules, [], 'cancelRelated');
        this.data.set('cancelRelated', cancelRelated);
    }

    keywordTypeChange(e) {
        switch (e.value) {
            case 'name':
                this.data.set('placeholder', '请输入实例名称进行搜索');
                break;
            case 'instanceId':
                this.data.set('placeholder', '请输入实例ID进行搜索');
                break;
            case 'internalIp':
                this.data.set('placeholder', '请输入实例内网IP进行搜索');
                break;
            case 'publicIp':
                this.data.set('placeholder', '请输入实例公网IP进行搜索');
                break;
            case 'type':
                this.data.set('placeholder', '请输入架构类型进行搜索');
                break;
            default:
                break;
        }
    }

    onPageChange(e) {
        this.data.set('pager.page', e.value.page);
        this.data.set('table.selection.selectedIndex', []);
        this.loadPage();
    }

    onPageSizeChange(e) {
        this.data.set('pager.size', e.value.pageSize);
        if (this.data.get('pager.page') === 1) {
            this.loadPage();
        } else {
            this.data.set('pager.page', 1);
        }
    }

    tableSelected(e) {
        this.data.set('selectedItems', e.value.selectedItems);
        let {cancelRelatedBlb, cancelRelated} = checker.check(rules, e.value.selectedItems);
        if (this.data.get('context').type === 'blb') {
            this.data.set('cancelRelated', cancelRelatedBlb);
        } else {
            this.data.set('cancelRelated', cancelRelated);
        }
    }

    onSort(e) {
        let {value} = e;
        // 兼容内网Ip排序参数
        if (value.orderBy === 'ovip' || value.orderBy === 'ddcIp' || value.orderBy === 'lbIp') {
            value.orderBy = 'internalIp';
        }
        this.data.set('order', value);
        this.loadPage();
    }

    refresh() {
        this.resetTable();
        return this.loadPage();
    }

    // 重置列表
    resetTable() {
        this.data.set('pager.page', 1);
        this.data.set('table.selection.selectedIndex', []);
    }

    onRelease(row, data) {
        let instanceUuids = [];
        let items = null;
        let type = this.data.get('context').type;
        if (row === 'row') {
            items = [data];
        } else {
            items = this.data.get('selectedItems');
        }
        instanceUuids = items.map(item => item.instanceUuid);
        let esgUuids = [this.data.get('context').id];
        let confirm = new Confirm({
            data: {
                open: true,
                content: '确定取消关联？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            const params = {
                instanceType: type.toUpperCase(),
                instanceUuids,
                esgUuids
            };
            this.$http.enterpriseSecurityBatchUnbindInstance(params).then(() => {
                this.refresh();
                Notification.success('成功取消关联');
            });
        });
    }

    relateInstance() {
        let bind = new Bind({
            data: {
                open: true,
                type: this.data.get('context').type,
                id: this.data.get('context').id
            }
        });
        bind.attach(document.body);
        bind.on('bind', () => {
            this.loadPage();
            this.fire('update');
        });
    }

    async onSearch() {
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    getSearchCriteria() {
        let {order, pager} = this.data.get('');
        let payload = {pageNo: pager.page, pageSize: pager.size, esgUuid: this.data.get('context').id};
        let keyword = this.data.get('keyword').trim();
        if (keyword) {
            payload.keywordType = this.data.get('keywordType');
            payload.keyword = keyword;
        }
        return u.extend({}, payload, order);
    }

    async loadPage() {
        this.data.set('table.loading', true);
        let payload = this.getSearchCriteria();
        let type = this.data.get('context').type;
        if (type === 'snic' || type === 'blb') {
            delete payload.order;
            delete payload.orderBy;
        }
        let result = null;
        try {
            payload.instanceType = type.toUpperCase();
            result = await this.$http.getEnterpriseBindInstanceList(payload, {
                'x-silent-codes': ['Esg.EsgResourceNotExist']
            });
            this.data.set('table.datasource', result.result);
            this.data.set('pager.total', result.totalCount);
            this.data.set('table.loading', false);
        } catch (err) {
            this.data.set('table.loading', false);
        }
    }

    attached() {
        this.loadPage();
    }
}
export default San2React(Processor.autowireUnCheckCmpt(SecurityInstanceList));
