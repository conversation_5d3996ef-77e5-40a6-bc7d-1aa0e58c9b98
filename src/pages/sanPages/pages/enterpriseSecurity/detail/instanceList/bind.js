/*
 * @description: 安全组绑定实例
 * @file: dcgw/pages/nat/create.js
 * @author: p<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */
import u from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import {Input, Dialog, Pagination, Search, Tooltip, Table, Select, Button, Icon, Alert} from '@baidu/sui';
import {Empty} from '@baidu/sui-biz';

import {
    ddcTypeColumns,
    ddcRemarkColumns,
    ddcIpColumns,
    blbTypeColumns,
    scsTypeColumns,
    scsIpColumns,
    snicIpColumns,
    rdsTypeColumns,
    rdsDBColumns,
    rabbitMqColumns
} from './columns';

const IPSearch = /^[0-9.]+$/;
/* eslint-disable */
const tpl = html`
    <template>
        <s-dialog width="600" class="{{klass}}" closeAfterMaskClick="{{false}}" open="{=open=}" title="{{title}}">
            <div>
                <s-alert skin="warning">
                    <!--bca-disable-next-line-->
                    {{withHref | raw}}
                </s-alert>
                <div class="toolbar-line">
                    <span class="selectTip">{{selectTip}}</span>
                    <div class="opts inline_class">
                        <span class="span_class">VPC实例</span>
                        <s-select on-change="vpcChange" datasource="{{vpcList}}" value="{=vpcId=}"> </s-select>
                        <div class="search-wrapp">
                            <s-select
                                class="left_class"
                                on-change="keywordTypeChange($event)"
                                datasource="{{keywordTypeList}}"
                                value="{=keywordType=}"
                            >
                            </s-select>
                            <s-input-search
                                placeholder="{{placeholder}}"
                                value="{=keyword=}"
                                on-search="onSearch"
                                on-input="inputSearch"
                            />
                            <div s-if="{{searchErr}}" class="search-err">{{searchErr}}</div>
                        </div>
                    </div>
                </div>
                <s-table
                    columns="{{table.columns}}"
                    loading="{{table.loading}}"
                    error="{{table.error}}"
                    datasource="{{table.datasource}}"
                    on-selected-change="tableSelected($event)"
                    selection="{=table.selection=}"
                >
                    <div slot="error">
                        <!--bca-disable-next-line-->
                        {{table.error ? table.error : '啊呀，出错了？' | raw}}
                        <a href="javascript:;" on-click="refresh">{{'重新加载'}}</a>
                    </div>
                    <div slot="empty">
                        <s-empty>
                            <div slot="action"></div>
                        </s-empty>
                    </div>
                </s-table>
                <s-pagination
                    s-if="{{pager.total}}"
                    class="pagination"
                    layout="{{'total, pageSize, pager, go'}}"
                    pageSize="{{pager.size}}"
                    total="{{pager.total}}"
                    page="{{pager.page}}"
                    pageSizes="{{pager.pageSizes}}"
                    on-pagerChange="onPageChange"
                    on-pagerSizeChange="onPageSizeChange"
                />
            </div>
            <div slot="footer">
                <div style="padding-left: 300px; padding-top:20px; display: inline-block;">
                    <s-button size="larger" on-click="cancel">{{'取消'}}</s-button>
                    <s-button disabled="{{!selectedItems.length || isConfirm}}" skin="primary" on-click="onBind"
                        >{{'确定'}}</s-button
                    >
                </div>
            </div>
        </s-dialog>
    </template>
`;
/* eslint-enable */
export default class Bind extends Component {
    static template = tpl;
    static components = {
        's-button': Button,
        's-alert': Alert,
        's-table': Table,
        's-textbox': Input,
        's-pagination': Pagination,
        's-input-search': Search,
        's-select': Select,
        's-dialog': Dialog,
        's-icon': Icon,
        's-tooltip': Tooltip,
        's-empty': Empty
    };
    static computed = {
        selectTip() {
            let type = this.data.get('type');
            if (type === 'bbc' || type === 'bcc') {
                return `已选中${this.data.get('table.selection.selectedIndex').length}条`;
            } else {
                return `已选中${this.data.get('table.selection.selectedIndex').length}条/共${this.data.get('pager.total')}条`;
            }
        },
        withHref() {
            let type = this.data.get('type');
            if (type === 'bcc') {
                return (
                    '温馨提示：企业安全组只展示企业安全组关联的实例，如需切换类型，请到BCC详情页操作安全组或批量操作请参考：<a href="https://cloud.baidu.com/doc/BCC/s/Tkaw7s5ec" blank="target">关联企业安全组</a>'
                );
            } else {
                return '温馨提示：企业安全组只展示企业安全组关联的实例';
            }
        }
    };
    initData() {
        return {
            klass: ['enterpriseSecurity-instance-list'],
            vpcId: '',
            vpcList: [],
            selectedItems: [],
            title: '',
            emptyText: '暂无数据',
            keywordTypeList: [
                {text: '实例名称', value: 'name'},
                {text: '实例ID', value: 'instanceId'},
                {text: '实例内网IP', value: 'internalIp'},
                {text: '实例公网IP', value: 'publicIp'}
            ],
            placeholder: '请输入实例名称进行搜索',
            keywordType: 'name',
            keyword: '',
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: [],
                    disabledIndex(item) {
                        return item.associationNum >= 10;
                    }
                },
                columns: [
                    {
                        name: 'name',
                        label: '实例名称/ID',
                        width: 100,
                        render(item) {
                            let name = u.escape(item.name) || u.escape(item.scsName) || u.escape(item.instanceName);
                            let id =
                                u.escape(item.id) ||
                                u.escape(item.eniId) ||
                                u.escape(item.blbShortId) ||
                                u.escape(item.instanceId) ||
                                u.escape(item.shortId);
                            return `<span class="truncated" title="${name}">
                                        ${name}
                                    </span>
                                    </br><span class="truncated" title="${id}">${id}</span>`;
                        }
                    },
                    {
                        name: 'internalIp',
                        label: '实例IP',
                        width: 100,
                        render(item) {
                            if (item.eniPrivateIps) {
                                let result = [];
                                u.each(item.eniPrivateIps, (data, index) => {
                                    if (index <= 2) {
                                        let ipData = FLAG.NetworkSupportEip
                                            ? (data.eip || '-') + '（公）' + '<br>' + (data.privateIp || '-') + '（内）'
                                            : (data.privateIp || '-') + '（内）';
                                        result.push(ipData);
                                    }
                                });

                                if (item.eniPrivateIps.length > 3) {
                                    result.push('...');
                                }

                                return result.join('<br>');
                            } else if (item.blbType === 'ipv6') {
                                return FLAG.NetworkSupportEip ? '-(公)' + '<br>' + '-(内)' : '-(内)';
                            } else {
                                return FLAG.NetworkSupportEip
                                    ? (item.publicIp || '-') + '（公）' + '<br>' + (item.internalIp || '-') + '（内）'
                                    : (item.internalIp || '-') + '（内）';
                            }
                        }
                    },
                    {
                        name: 'ipv6',
                        label: 'IPv6 IP',
                        width: 100,
                        render(item) {
                            return item.ipv6 || '-';
                        }
                    },
                    {
                        name: 'desc',
                        label: '描述',
                        width: 80,
                        render(item) {
                            let desc = u.escape(item.desc) || u.escape(item.description);
                            return desc || '-';
                        }
                    }
                ],
                datasource: []
            },
            pager: {
                size: 10,
                page: 1,
                total: 0,
                pageSizes: [10, 20, 50, 100]
            },
            searchErr: ''
        };
    }
    inited() {
        let type = this.data.get('type');
        let title = '关联云服务器';
        if (type === 'eni') {
            title = '关联弹性网卡';
            this.data.splice('table.columns', [2, 1]);
        } else if (type === 'snic') {
            title = '关联服务网卡';
            this.data.splice('table.columns', [2, 1]);
            this.data.splice('table.columns', [1, 1, snicIpColumns]);
            this.data.set('pager.pageSizes', [10]);
            this.data.pop('keywordTypeList');
        } else if (type === 'bbc') {
            title = '关联弹性裸金属服务器';
            this.data.splice('table.columns', [2, 1]);
        } else if (type === 'blb') {
            title = '关联负载均衡';
            this.data.splice('table.columns', [1, 0, blbTypeColumns]);
        } else if (type === 'ddc') {
            title = '关联云数据库专属集群列表';
            this.data.splice('table.columns', [1, 0, ddcTypeColumns]);
            this.data.splice('table.columns', [2, 1, ddcIpColumns]);
            this.data.splice('table.columns', [3, 2]);
            this.data.splice('table.columns', [3, 0, ddcRemarkColumns]);
            this.data.pop('keywordTypeList');
        } else if (type === 'scs') {
            title = _('关联云数据库 Redis');
            this.data.splice('table.columns', [1, 0, scsTypeColumns]);
            this.data.splice('table.columns', [2, 1, scsIpColumns]);
            this.data.splice('table.columns', [3, 2]);
            this.data.push('keywordTypeList', {text: '实例类型', value: 'scsType'});
        } else if (type === 'rds') {
            title = _('关联云数据库RDS');
            this.data.splice('table.columns', [1, 0, rdsTypeColumns]);
            this.data.splice('table.columns', [2, 1, rdsDBColumns]);
            this.data.splice('table.columns', [3, 2]);
            this.data.push('keywordTypeList', {text: '实例类型', value: 'remark'});
            this.data.push('keywordTypeList', {text: '数据库类型', value: 'productType'});
        } else if (type === 'rabbitmq') {
            title = _('关联消息服务 for RabbitMQ');
            this.data.splice('table.columns', [1, 2]);
            this.data.splice('table.columns', [1, 0, rabbitMqColumns]);
            this.data.splice('keywordTypeList', [2, 2]);
            this.data.push('keywordTypeList', {text: '架构类型', value: 'type'});
        }

        if (!FLAG.NetworkSupportEip) {
            let keywordTypeList = this.data.get('keywordTypeList');
            this.data.set(
                'keywordTypeList',
                keywordTypeList.filter(item => item.value !== 'publicIp')
            );
        }
        this.data.set('title', title);
    }
    attached() {
        this.getVpclist();
    }
    keywordTypeChange(e) {
        let type = this.data.get('type');
        switch (e.value) {
            case 'name':
                this.data.set('placeholder', '请输入实例名称进行搜索');
                break;
            case 'instanceId':
                this.data.set('placeholder', '请输入实例ID进行搜索');
                break;
            case 'internalIp':
                this.data.set(
                    'placeholder',
                    type === 'bcc' ? '请输入实例内网IP进行精确搜索' : '请输入实例内网IP进行搜索'
                );
                break;
            case 'publicIp':
                this.data.set('placeholder', '请输入实例公网IP进行搜索');
                break;
            case 'scsType':
                this.data.set('placeholder', '请输入实例类型进行搜索');
                break;
            case 'type':
                this.data.set('placeholder', '请输入架构类型进行搜索');
                break;
            default:
                break;
        }
    }
    onPageChange(e) {
        this.data.set('pager.page', e.value.page);
        this.data.set('table.selection.selectedIndex', []);
        this.loadPage();
    }
    onPageSizeChange(e) {
        this.data.set('pager.size', e.value.pageSize);
        if (this.data.get('pager.page') === 1) {
            this.loadPage();
        } else {
            this.data.set('pager.page', 1);
        }
    }
    tableSelected(e) {
        this.data.set('selectedItems', e.value.selectedItems);
    }
    // 重置列表
    resetTable() {
        this.data.set('pager.page', 1);
        this.data.set('table.selection.selectedIndex', []);
    }
    async onSearch() {
        if (!this.data.get('searchErr')) {
            this.resetTable();
            this.loadPage();
        }
    }
    getSearchCriteria() {
        let pager = this.data.get('pager');
        let payload = {pageNo: pager.page, pageSize: pager.size, esgUuid: this.data.get('id')};
        let keyword = this.data.get('keyword');
        if (keyword) {
            payload.keywordType = this.data.get('keywordType');
            payload.keyword = keyword;
        }
        payload.vpcUuid = this.data.get('vpcId');
        return payload;
    }
    async loadPage() {
        this.data.set('table.loading', true);
        let payload = this.getSearchCriteria();
        let type = this.data.get('type');
        let result = null;
        try {
            payload.instanceType = type.toUpperCase();
            if (type === 'bcc' || type === 'bbc') {
                if (payload.keywordType === 'name') {
                    delete payload.keywordType;
                    payload.keywordType = 'instanceName';
                }
                result = await this.$http.getEnterpriseSecurityTopN(payload);
            } else {
                result = await this.$http.getEnterpriseUnbindInstanceList(payload);
                this.data.set('pager.total', result.totalCount);
            }
            this.data.set('table.datasource', type === 'bbc' || type === 'bcc' ? result : result.result);
            this.data.set('table.loading', false);
        } catch (err) {
            this.data.set('table.datasource', []);
            this.data.set('table.loading', false);
        }
    }
    async onBind() {
        const type = this.data.get('type');
        let instances = this.data.get('selectedItems').map(item => {
            let payloadItem = {vpcUuid: this.data.get('vpcId')};
            payloadItem.instanceUuid = item.instanceUuid;
            return payloadItem;
        });
        let payload = {
            instances,
            esgUuids: [this.data.get('id')],
            instanceType: type.toUpperCase()
        };
        this.data.set('isConfirm', true);
        try {
            await this.$http.enterpriseSecurityBindInstance(payload);
            this.data.set('open', false);
            this.fire('bind');
        } catch (err) {}
        this.data.set('isConfirm', false);
    }
    cancel() {
        this.data.set('open', false);
    }

    vpcChange({value}) {
        this.data.set('vpcId', value);
        this.resetTable();
        this.loadPage();
    }

    getVpclist() {
        this.$http.vpcList().then(data => {
            let vpcs = u.map(data, item => ({text: `${item.name}（${item.cidr}）`, value: item.vpcId}));
            this.data.set('vpcList', vpcs);
            vpcs[0] && this.data.set('vpcId', vpcs[0].value);
            this.loadPage();
        });
    }

    inputSearch({value}) {
        let keywordType = this.data.get('keywordType');
        if ((keywordType === 'internalIp' || keywordType === 'publicIp') && value && !IPSearch.test(value)) {
            this.data.set('searchErr', '输入IP格式有误');
        } else {
            this.data.set('searchErr', '');
        }
    }
}
