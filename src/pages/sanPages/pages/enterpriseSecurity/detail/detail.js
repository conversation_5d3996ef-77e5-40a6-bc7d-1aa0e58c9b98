/**
 * @file security/pages/detail/detail.js
 * <AUTHOR>
 */
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {Notification} from '@baidu/sui';
import {OutlinedEditingSquare} from '@baidu/sui-icon';

import Rule from '@/pages/sanPages/utils/rule';
import EnterpriseRuleList from '../components/ruleList';
import {kXhrOptions} from '@/pages/sanPages/utils/helper';
import testID from '@/testId';
import './style.less';

const {asComponent, invokeSUI, invokeSUIBIZ, invokeComp, template} = decorators;
/* eslint-disable */
const tpl = html` <div>
    <div class="{{klass}}">
        <div class="content-box">
            <h4>基本信息</h4>
            <!--<s-app-legend class="legend-wrap" label="基本信息">
                <s-icon
                    class="{{showAdvance ? 'advance-icon actived' : 'advance-icon'}}"
                    name="xialajiantou"
                    slot="extra"
                    on-click="handleShowBaseInfo"
                ></s-icon>
            </s-app-legend>-->
            <div class="cell">
                <div class="cell-title">{{'安全组名称：'}}</div>
                <div class="cell-content" ref="name" data-testid="${testID.enterpriseSecurity.detailSecurityName}">
                    {{instance.name}}
                    <edit-popover value="{=instance.name=}" rule="{{nameRule}}" on-edit="updateName">
                        <outlined-editing-square color="#2468f2" />
                    </edit-popover>
                </div>
            </div>
            <div class="cell">
                <div class="cell-title">{{'安全组ID：'}}</div>
                <div class="cell-content">{{instance.esgId}}</div>
                <s-clip-board text="{{instance.esgId}}" data-test-id="${testID.enterpriseSecurity.detailCopyId}" />
            </div>
            <div class="cell">
                <div class="cell-title">{{'描述：'}}</div>
                <div class="cell-content">
                    <span class="truncated">{{instance.desc}}</span>
                    <edit-popover value="{=instance.desc=}" rule="{{descRule}}" on-edit="updateDesc">
                        <outlined-editing-square color="#2468f2" />
                    </edit-popover>
                </div>
            </div>
        </div>
        <div class="content-box">
            <h4>端口设置</h4>
            <enterprise-rule-list
                rules="{{instance.rules}}"
                id="{{esgUuid}}"
                vpcId="{{vpcId}}"
                isDetail="{{true}}"
                instance="{{instance}}"
                associatedNum="{{instance.associateNum}}"
            ></enterprise-rule-list>
        </div>
    </div>
</div>`;

/* eslint-enable */
@template(tpl)
@invokeComp('@edit-popover')
@invokeSUI
@invokeSUIBIZ
// @asComponent('@enterprise-security-detail')
class SecurityDetail extends Component {
    static components = {
        'outlined-editing-square': OutlinedEditingSquare,
        'enterprise-rule-list': EnterpriseRuleList
    };
    initData() {
        return {
            klass: ['vpc-enterpriseSecurity-detail'],
            instanceName: {
                value: '',
                error: true,
                visible: false
            },
            instanceDesc: {
                value: '',
                error: true,
                visible: false
            },
            instance: {},
            nameRule: Rule.NAME_SUPPORT_CHINESE,
            descRule: Rule.DETAIL_EDIT.DESC,
            associatedNum: 0,
            esgUuid: '',
            showAdvance: false
        };
    }
    inited() {
        const instance = this.data.get('context').instance || {};
        const id = this.data.get('context')?.id;
        this.data.set('instance', instance);
        this.data.set('esgUuid', instance?.esgUuid || id);
    }

    updateName(value) {
        this.$http
            .updateEnterpriseSecurityField({
                esgUuid: this.data.get('instance.esgUuid'),
                name: value
            })
            .then(() => {
                Notification.success('修改成功');
                this.data.set('instance.name', value);
                this.data.get('context')?.updateName();
            });
    }

    updateDesc(value) {
        this.$http
            .updateEnterpriseSecurityField({
                esgUuid: this.data.get('instance.esgUuid'),
                desc: value
            })
            .then(() => {
                Notification.success('修改成功');
                this.data.set('instance.desc', value);
            });
    }
    handleShowBaseInfo() {
        let showAdvance = this.data.get('showAdvance');
        this.data.set('showAdvance', !showAdvance);
    }
}
export default San2React(Processor.autowireUnCheckCmpt(SecurityDetail));
