.vpc-enterpriseSecurity-list {
    min-height: 100%;
    background: #f7f7f9 !important;
    .s-table {
        .s-table-row:hover {
            .name-icon {
                display: inline;
            }
        }
        .name-icon {
            display: none;
            font-size: 12px;
            fill: #2468f2;
        }
        .s-table-body {
            max-height: calc(~'100vh - 376px');
            overflow-y: auto;
            .enterprise-name-widget {
                white-space: nowrap;
            }
        }
    }
    .inline_class {
        display: inline-flex;
    }
    .list-page-tb-left-toolbar {
        display: inline-flex;
    }
    .s-biz-page-header {
        margin: 0px !important;
        border: none !important;
        height: auto !important;
        .vpc-select {
            margin-left: 16px;
        }
    }
    .s-biz-page-footer {
        padding-bottom: 0 !important;
        margin-top: 16px !important;
    }
    .s-biz-page-header {
        height: 0;
        border: none;
    }
    .s-cascader {
        font-size: 0;
        .s-cascader-value-arrow {
            top: 0%;
        }
    }
    .s-cascader-value {
        vertical-align: middle;
        border: none;
        font-size: 12px;
        padding-top: 0;
        padding-bottom: 0;
        line-height: 30px;
        min-width: 100px !important;
    }
    .icon-copy,
    .icon-edit {
        font-size: 12px;
        color: #2468f2;
    }
    .selectTip {
        margin-left: 5px;
        color: #999;
    }
    .operations {
        .s-button {
            padding: 0;
            margin-right: 12px;
        }
    }
    .esecurity-res {
        .search-res {
            margin: 0px;
            display: inline-flex;
            .s-cascader {
                margin: 0 8px 0 0;
            }
        }
    }
}
.security-copy {
    .tip {
        font-size: 12px;
        margin-top: 10px;
        color: #999;
    }
    .s-alert-skin-warning {
        height: 50px;
    }
}
.security-rule-list {
    .disable-line {
        .s-table-cell-text {
            color: #ccc !important;
            a {
                color: #ccc !important;
            }
        }
    }
    .ruletype {
        display: inline-block;
    }
    .lead-btn,
    .ruleiptype {
        float: right;
        margin-right: 8px;
    }
}
.security-preview {
    .security-direction-radio {
        margin-bottom: 20px;
    }
}

.locale-en {
    .security-copy .s-alert-skin-warning {
        height: auto;
    }
}
