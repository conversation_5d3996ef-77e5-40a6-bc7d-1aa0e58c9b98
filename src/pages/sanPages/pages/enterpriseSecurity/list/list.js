/*
 * @description: 安全组列表页
 * @file: network/security/pages/list.js
 * @author: p<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {Notification, Message, Input} from '@baidu/sui';
import {OutlinedRefresh, OutlinedPlus, OutlinedEditingSquare} from '@baidu/sui-icon';

import rules from '../rules';
import Confirm from '@/pages/sanPages/components/confirm';
import Copy from '../components/copy';
import Preview from '../components/preview';
import {columns} from './tableFields';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import {resetNewRegion} from '@/utils/helper';
import testID from '@/testId';
import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import IntroducePanel from '@/pages/sanPages/components/introducePanel/introducePanel';
import './style.less';

const {asComponent, invokeSUI, invokeAppComp, invokeSUIBIZ, template, service, invokeComp} = decorators;
const tpl = html` <div>
    <s-biz-page class="{{klass}}">
        <div class="vpc-security-header" slot="header">
            <!--<introduce-panel
                isShow="{{show}}"
                klass="{{'endpoint-peerconn-wrapper'}}"
                title="{{introduceTitle}}"
                description="{{description}}"
                introduceOptions="{{introduceOptions}}"
                on-toggle="handleToggle($event)"
            ></introduce-panel>-->
        </div>
        <div class="list-page-tb-left-toolbar" slot="tb-left">
            <s-tooltip content="{{iamPass.message}}" trigger="{{iamPass.disable ? 'hover' : ''}}" placement="top">
                <s-button
                    disabled="{{iamPass.disable}}"
                    track-id="vpc_enterpriseSecurity_create"
                    skin="primary"
                    on-click="onCreate"
                    data-test-id="${testID.enterpriseSecurity.listCreateBtn}"
                >
                    <outlined-plus />{{'创建安全组'}}
                </s-button>
            </s-tooltip>
            <s-tooltip class="left_class" trigger="{{release.disable ? 'hover' : ''}}" placement="top">
                <!--bca-disable-next-line-->
                <div slot="content">{{release.message | raw}}</div>
                <s-button
                    on-click="onRelease"
                    disabled="{{release.disable}}"
                    data-test-id="${testID.enterpriseSecurity.listDeleteBtn}"
                >
                    {{'删除'}}</s-button
                >
            </s-tooltip>
            <edit-tag
                s-else
                class="left_class"
                selectedItems="{{selectedItems}}"
                on-success="refresh"
                type="ENTERPRISE_SECURITY_GROUP"
            ></edit-tag>
            <s-tooltip class="left_class" trigger="{{preview.disable ? 'hover' : ''}}" placement="top">
                <!--bca-disable-next-line-->
                <div slot="content">{{preview.message | raw}}</div>
                <s-button
                    on-click="onPreview"
                    disabled="{{preview.disable}}"
                    data-test-id="${testID.enterpriseSecurity.listPreviewRules}"
                >
                    {{'预览规则'}}</s-button
                >
            </s-tooltip>
        </div>
        <div slot="tb-right" class="inline_class">
            <search-tag
                s-ref="search"
                serviceType="SECURITY_GROUP"
                searchbox="{=searchbox=}"
                on-search="onSearch"
                isShowResGroup="{{false}}"
            ></search-tag>
            <s-button on-click="refresh" class="s-icon-button left_class"
                ><outlined-refresh class="icon-class"
            /></s-button>
        </div>
        <s-table
            columns="{{table.columns}}"
            loading="{{table.loading}}"
            error="{{table.error}}"
            datasource="{{table.datasource}}"
            on-selected-change="tableSelected($event)"
            on-filter="onFilter"
            on-sort="onSort"
            selection="{=table.selection=}"
            data-test-id="${testID.enterpriseSecurity.listTable}"
        >
            <div slot="empty">
                <s-empty
                    class="{{iamPass.disable ? 'create-disable' : ''}}"
                    on-click="onCreate"
                    track-id="vpc_enterpriseSecurity_create"
                />
            </div>
            <div slot="error">
                {{'啊呀，出错了？'}}
                <a href="javascript:;" on-click="refresh">{{'重新加载'}}</a>
            </div>
            <div slot="c-name" class="enterprise-name-widget">
                <a
                    href="#/vpc/enterpriseSecurity/detail?id={{row.esgUuid}}"
                    class="truncated"
                    title="{{row.name}}"
                    track-id="vpc_enterpriseSecurity_enter_detail"
                    track-name="查看企业安全组详情"
                    data-testid="${testID.enterpriseSecurity.listInstanceName}{{rowIndex}}"
                >
                    {{row.name}}
                </a>
                <s-popover
                    s-ref="{{'instanceNameEdit'+rowIndex}}"
                    placement="right"
                    trigger="click"
                    class="edit-popover-class"
                    data-test-id="${testID.enterpriseSecurity.listEditName}{{rowIndex}}"
                >
                    <div class="edit-wrap" slot="content">
                        <s-input
                            value="{=edit.name.value=}"
                            width="320"
                            placeholder="请输入名称"
                            on-input="onNameInput($event, rowIndex)"
                            data-test-id="${testID.enterpriseSecurity.listEditNameInput}{{rowIndex}}"
                        />
                        <div class="edit-tip">
                            大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65
                        </div>
                        <s-button
                            skin="primary"
                            s-ref="{{'editNameBtn'+rowIndex}}"
                            disabled="{{true}}"
                            on-click="editConfirm(row, rowIndex, 'name')"
                            data-test-id="${testID.enterpriseSecurity.listEditNameSub}{{rowIndex}}"
                            >确定</s-button
                        >
                        <s-button on-click="editNameCancel(rowIndex)">取消</s-button>
                    </div>
                    <outlined-editing-square
                        s-if="row.name!=='默认安全组'"
                        class="name-icon"
                        on-click="editName(row)"
                    />
                </s-popover>
                <br />
                <span class="truncated" title="{{row.esgId}}">{{row.esgId}}</span>
                <s-clip-board
                    class="name-icon"
                    text="{{row.esgId}}"
                    data-test-id="${testID.enterpriseSecurity.listCopy}{{rowIndex}}"
                />
            </div>
            <div slot="c-desc">
                <span class="truncated" title="{{row.desc}}">{{row.desc || '-'}}</span>
                <s-popover
                    s-ref="{{'instanceDescEdit'+rowIndex}}"
                    placement="top"
                    trigger="click"
                    class="edit-popover-class"
                    data-testid="${testID.enterpriseSecurity.listEditDesc}{{rowIndex}}"
                >
                    <div class="edit-wrap" slot="content">
                        <s-textarea
                            value="{=edit.desc.value=}"
                            width="200"
                            height="48"
                            placeholder="请输入"
                            on-input="onDescInput($event, rowIndex)"
                            data-test-id="${testID.enterpriseSecurity.listEditDescInput}{{rowIndex}}"
                        />
                        <div class="edit-tip">描述不能超过200个字符</div>
                        <s-button
                            skin="primary"
                            s-ref="{{'editDescBtn'+rowIndex}}"
                            disabled="{{true}}"
                            on-click="editConfirm(row, rowIndex, 'desc')"
                            data-test-id="${testID.enterpriseSecurity.listEditDescSub}{{rowIndex}}"
                            >确定</s-button
                        >
                        <s-button on-click="editDescCancel(rowIndex)">取消</s-button>
                    </div>
                    <outlined-editing-square class="name-icon" on-click="editDesc(row, rowIndex, 'description')" />
                </s-popover>
            </div>
            <div slot="c-associateNum">{{row | getAssociateNum}}</div>
            <div slot="c-tag">
                <span s-if="!row.tags || row.tags.length < 1"> - </span>
                <div s-else s-for="item,index in row.tags">
                    <span s-if="index <= 1"> {{item.tagKey + ':' + item.tagValue}} </span>
                    <div s-if="row.tags.length > 2 && index === 1">...</div>
                </div>
            </div>
            <div slot="c-opt">
                <span class="operations">
                    <s-button
                        skin="stringfy"
                        on-click="copy(row)"
                        data-test-id="${testID.enterpriseSecurity.listCopy}{{rowIndex}}"
                        >{{'复制'}}</s-button
                    >
                    <s-tooltip trigger="{{row.associateNum!==0?'hover':''}}">
                        <!--bca-disable-next-line-->
                        <div slot="content">{{(row | release) | raw}}</div>
                        <s-button
                            skin="stringfy"
                            disabled="{{row.associateNum!==0}}"
                            on-click="delete(row)"
                            data-test-id="${testID.enterpriseSecurity.listDelete}{{rowIndex}}"
                            >删除</s-button
                        >
                    </s-tooltip>
                </span>
            </div>
        </s-table>
        <s-pagination
            s-if="{{pager.total}}"
            slot="footer"
            layout="{{'total, pageSize, pager, go'}}"
            pageSize="{{pager.size}}"
            total="{{pager.total}}"
            page="{{pager.page}}"
            on-pagerChange="onPagerChange"
            on-pagerSizeChange="onPageSizeChange"
        />
    </s-biz-page>
</div>`;
@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@invokeComp('@search-tag', '@edit-tag', '@vpc-select')
class VpcSecurityList extends Component {
    static components = {
        'outlined-refresh': OutlinedRefresh,
        'outlined-plus': OutlinedPlus,
        'outlined-editing-square': OutlinedEditingSquare,
        'introduce-panel': IntroducePanel,
        's-textarea': Input.TextArea
    };
    static filters = {
        release(item) {
            let {release} = checker.check(rules, [item]);
            return release.message;
        },
        getAssociateNum(item) {
            var count = item.associateNum;
            if (u.isNull(count) || u.isUndefined(count) || count < 0) {
                return '-';
            }
            return count;
        }
    };

    initData() {
        let filterColumns;
        if (!FLAG.NetworkSecuritySupportOrganization) {
            filterColumns = columns.filter(item => item.name !== 'resourceGroups');
        } else {
            filterColumns = columns.filter(item => item.name !== 'tag');
        }
        return {
            FLAG,
            klass: ['main-wrap-new', 'vpc-enterpriseSecurity-list'],
            title: '安全组',
            vpcList: [
                {
                    text: '所在网络：全部私有网络',
                    value: ''
                }
            ],
            searchbox: {
                keyword: '',
                placeholder: '请输入安全组名称进行搜索',
                keywordType: ['ESG_NAME'],
                keywordTypes: [
                    {value: 'ESG_NAME', text: '安全组名称'},
                    {value: 'ESG_ID', text: '安全组ID'},
                    {value: 'tag', text: '标签'}
                ]
            },
            selectedItems: [],
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                columns: filterColumns,
                datasource: []
            },
            pager: {
                size: 10,
                page: 1,
                total: 0
            },
            createSecGroup: {},
            release: {},
            preview: {},
            edit: {
                name: {
                    value: '',
                    error: true,
                    visible: false
                },
                desc: {
                    value: '',
                    error: true,
                    visible: false
                }
            },
            instanceName: {
                value: '',
                error: true,
                visible: false
            },
            iamPass: {},
            show: true,
            introduceTitle: '企业安全组简介',
            description:
                '企业安全组是在VPC网络内为BCC实例和DCC专属实例、负载均衡实例、云数据库实例中创建的安全防火墙，定义IP＋端口的入站和出站访问策略，从而提高云服务器、负载均衡、云数据库等实例的安全性。',
            introduceIcon: INTRODUCE_ICON_COLOR,
            introduceEle: null,
            urlQuery: getQueryParams()
        };
    }
    inited() {
        this.getIamQuery();
        this.data.get('context').callbackFn(this.handleShowCard.bind(this));
        const id = this.data.get('urlQuery.id');
        if (id) {
            this.data.set('searchbox.keywordType', ['ESG_ID']);
            this.data.set('searchbox.keyword', id);
        }
    }
    tableSelected(e) {
        this.data.set('selectedItems', e.value.selectedItems);

        let {release, preview} = checker.check(rules, e.value.selectedItems);
        this.data.set('release', release);
        this.data.set('preview', preview);
    }

    onCreate() {
        location.hash = '#/vpc/enterpriseSecurity/create';
    }

    attached() {
        if (!this.data.get('FLAG').NetworkSecuritySupportOrganization) {
            this.getSecurityTags();
        }
        this.loadPage();
        let {release, preview} = checker.check(rules, []);
        this.data.set('release', release);
        this.data.set('preview', preview);
    }
    onPagerChange(e) {
        this.resetTable();
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }
    onPageSizeChange(e) {
        this.data.set('pager.page', 1);
        this.data.set('pager.size', e.value.pageSize);
        if (this.data.get('pager.page') === 1) {
            this.loadPage();
        }
    }
    getSecurityTags() {
        this.ref('search').getTags();
    }
    onRelease() {
        let names = this.data
            .get('selectedItems')
            .map(item => item.name)
            .join('、');
        let confirm = new Confirm({
            data: {
                open: true,
                content: '确认删除安全组：' + names + '?'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            let ids = this.data.get('selectedItems').map(item => item.esgUuid);
            this.$http.enterpriseSecurityDelete({esgUuids: ids}).then(() => {
                this.refresh();
                Notification.success('删除成功', {placement: 'topRight'});
            });
        });
    }
    delete(row) {
        let confirm = new Confirm({
            data: {
                open: true,
                content: '确认删除安全组：' + row.name + '吗？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            let ids = [row.esgUuid];
            this.$http.enterpriseSecurityDelete({esgUuids: ids}).then(() => {
                this.refresh();
                Notification.success('删除成功', {placement: 'topRight'});
            });
        });
    }
    edit(item) {
        location.hash = '#/vpc/security/edit?dcgwId=' + item.esgUuid;
    }
    onSearch() {
        this.data.set('pager.page', 1);
        this.resetTable();
        return this.loadPage();
    }
    refresh() {
        this.resetTable();
        return this.loadPage();
    }
    // 重置列表
    resetTable() {
        this.data.set('pager.page', 1);
        this.data.set('table.selection.selectedIndex', []);
    }
    getSearchCriteria() {
        const {pager, order} = this.data.get('');
        const searchParam = this.ref('search') ? this.ref('search').getSearchCriteria() : {};
        if (FLAG.NetworkSubnetSupportOrganization) {
            const organizationId = window.$framework.organization.getCurrentOrganization().id;
            const currentResourceGroupIds = window.$framework.organization.getCurrentResourceGroup().id;
            const resourceGroupIds = currentResourceGroupIds === 'all' ? [] : [currentResourceGroupIds];
            u.assign(searchParam, {
                organizationId,
                resourceGroupIds
            });
        }
        return u.extend({}, searchParam, order, {pageNo: pager.page, pageSize: pager.size});
    }

    loadPage(payload) {
        this.data.set('table.loading', true);
        payload = payload || this.getSearchCriteria();
        this.$http.enterpriseSecurityList(payload).then(data => {
            this.data.set('table.datasource', data.result);
            this.data.set('pager.total', data.totalCount);
            this.data.set('table.loading', false);
        });
    }
    copy(row) {
        let copy = new Copy({
            data: {
                open: true,
                esgUuid: row.esgUuid,
                info: row,
                desc: row.desc
            }
        });
        copy.on('success', () => {
            this.loadPage();
        });
        copy.attach(document.body);
    }

    onPreview() {
        const preview = new Preview({
            data: {
                open: true,
                ids: this.data.get('selectedItems').map(item => item.esgUuid)
            }
        });
        preview.attach(document.body);
    }

    // 点击修改名称icon
    editName(row) {
        this.data.set('edit.name.value', row.name);
        this.data.set('edit.name.error', false);
    }
    editDesc(row) {
        this.data.set('edit.desc.value', row.desc);
        this.data.set('edit.desc.error', false);
    }
    // 修改名称确认
    editConfirm(row, rowIndex, type) {
        let instance = this.data.get(`edit.${type}`);
        if (instance.error) {
            return;
        }
        this.$http
            .updateEnterpriseSecurityField({
                [type]: instance.value,
                esgUuid: row.esgUuid
            })
            .then(() => {
                if (type === 'desc') {
                    this.editDescCancel(rowIndex);
                } else {
                    this.editNameCancel(rowIndex);
                }
                this.loadPage();
                Message.success({
                    content: '修改成功'
                });
            });
    }
    // 输入名称
    onNameInput(e, rowIndex) {
        let result = false;
        if (e.value === '' || !/^(?!default)[a-zA-Z\u4e00-\u9fa5][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/.test(e.value)) {
            result = true;
        } else {
            result = false;
        }
        this.data.set('edit.name.error', result);
        this.data.set('edit.name.value', e.value);
        this.ref('editNameBtn' + rowIndex).data.set('disabled', result);
    }
    // 编辑弹框-输入名称/描述
    onDescInput(e, rowIndex) {
        let result = e.value.length > 200;
        this.data.set('edit.desc.error', result);
        this.data.set('edit.desc.value', e.value);
        this.ref('editDescBtn' + rowIndex).data.set('disabled', result);
    }
    // 修改名称取消
    editNameCancel(rowIndex) {
        this.ref('editNameBtn' + rowIndex).data.set('disabled', true);
        this.ref('instanceNameEdit' + rowIndex).data.set('visible', false);
    }
    editDescCancel(rowIndex) {
        this.ref('editDescBtn' + rowIndex).data.set('disabled', true);
        this.ref('instanceDescEdit' + rowIndex).data.set('visible', false);
    }
    onRegionChange() {
        location.reload();
    }
    getIamQuery() {
        this.$http.getInterfaceIam({interfaceName: 'createEsg'}).then(res => {
            if (!res.requestId && !res.masterAccount) {
                if (!res.interfacePermission) {
                    this.data.set('iamPass', {
                        disable: true,
                        message: '您没有创建企业安全组权限，请联系主用户添加'
                    });
                } else {
                    this.data.set('iamPass', {
                        disable: false,
                        message: ''
                    });
                }
            } else {
                this.data.set('iamPass', {
                    disable: false,
                    message: ''
                });
            }
        });
    }
    handleToggle(flag) {
        this.data.set('show', flag);
        this.data.set('introduceIcon', INTRODUCE_ICON);
    }
    handleShowCard() {
        if (!this.data.get('show')) {
            this.data.set('show', true);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type) {
        this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#2468F2';
    }
    handleMouseLeave(type) {
        const isShow = this.data.get('show');
        this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        !isShow && (ele.style.color = '#151B26');
    }
}
export default San2React(Processor.autowireUnCheckCmpt(VpcSecurityList));
