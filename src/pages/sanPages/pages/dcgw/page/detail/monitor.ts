/*
 * @description: 创建专线网关
 * @file: network/dcgw/pages/create.js
 * @author: p<PERSON><PERSON><PERSON><EMAIL>
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {VirtualList} from '@baidu/sui';
import {San2React} from '@baidu/bce-react-toolkit';
import {BcmChartPanel} from '@baidu/new-bcm-sdk-san';
import {BcmSDK} from '@baidu/new-bcm-sdk';
import {OutlinedRefresh} from '@baidu/sui-icon';

import moment from 'moment';

import monitorConfig from '@/pages/sanPages/utils/monitorConfig';
import {DcGatewayStatus} from '@/pages/sanPages/common/enum';
import '../../style/monitor.less';

const {DcgwMetrics, shortcutItems, DcgwTopNMetrics} = monitorConfig;

const {asComponent, invokeSUI, invokeSUIBIZ, template, invokeComp} = decorators;
const tpl = html`
    <div>
        <div class="dcgw-monitor-box">
            <h4>
                监控
                <s-button style="margin-left:10px" on-click="toAlarmDetail">报警详情</s-button>
            </h4>
            <div class="button-wrap">
                <span>时间范围：</span>
                <s-tip
                    s-if="{{!dcgwServer.datasource.length}}"
                    skin="question"
                    class="tip-class tip-margin"
                    content="当前无数据，系统仅保留近30天数据。"
                    layer-width="300"
                />
                <s-date-picker-date-range-picker
                    value="{=dcgwTime.timeRange=}"
                    width="310"
                    shortcut="{{shortcutItems}}"
                    range="{{range}}"
                    mode="second"
                    on-change="onTimeChange('dcgwTime', $event)"
                />
                <s-button
                    class="s-icon-button refresh-button"
                    on-click="onTimeRefresh()"
                    track-id="ti_vpc_dcgw_bcm_refresh"
                    track-name="网关监控信息/刷新"
                >
                    <outlined-refresh />
                </s-button>
                <s-tip
                    skin="question"
                    class="tip-class"
                    content="专线网关的流量等于专线通道下所有通道流量之和。"
                    layer-width="300"
                />
            </div>
            <div class="dcgw-monitor-trends">
                <div class="monitor-trend-box" s-for="item,index in chartConfig">
                    <bcm-chart-panel
                        s-ref="dcgw-alarm-chart-{{index}}"
                        showbigable
                        withFilter="{{false}}"
                        scope="{{item.scope}}"
                        dimensions="{{item.dimensions}}"
                        statistics="{{item.statistics}}"
                        title="{{item.title}}"
                        options="{{options}}"
                        api-type="{{apiType}}"
                        startTime="{=dcgwTime.startTime=}"
                        endTime="{=dcgwTime.endTime=}"
                        period="{{dcgwTime.monitorDefaultPeriod}}"
                        metrics="{{item.metrics}}"
                        unit="{{item.unit}}"
                        bitUnit="{{item.bitUnit}}"
                        width="{{'auto'}}"
                        height="{{230}}"
                        sdk="{{bcmSdk}}"
                    >
                    </bcm-chart-panel>
                </div>
            </div>
        </div>
        <div class="dcgw-monitor-box monitor_bottom">
            <h4>专线网关实例监控信息</h4>
            <div class="button-wrap">
                <div class="options_class">
                    时间范围：
                    <s-tip
                        s-if="{{!dcgwServer.datasource.length}}"
                        skin="question"
                        class="tip-class tip-margin"
                        content="当前无数据，系统仅保留近30天数据。"
                        layer-width="300"
                    />
                    <s-date-picker-date-range-picker
                        disabled="{{!dcgwServer.datasource.length}}"
                        value="{=serverTime.timeRange=}"
                        width="{{310}}"
                        shortcut="{{shortcutItems}}"
                        range="{{range}}"
                        on-change="onTimeChange('serverTime', $event)"
                        mode="second"
                    />
                </div>
                <div class="options_class">
                    统计方式：
                    <s-select
                        datasource="{{statisticsDatasource}}"
                        value="{=statistics=}"
                        disabled="{{!dcgwServer.datasource.length}}"
                    />
                </div>
                <div class="options_class flex_class">
                    VPC网段使用IP：
                    <s-select
                        datasource="{{dcgwServer.datasource}}"
                        value="{=dcgwServer.value=}"
                        disabled="{{!dcgwServer.datasource.length}}"
                        width="{{170}}"
                        multiple
                        filterable
                        virtualThreshold="10"
                        checkAll="{{!(!displayTopN && dcgwServer.datasource.length > 10)}}"
                    >
                    </s-select>
                    <s-tip class="tip_class" skin="question" content="{{dcgwServerTip}}" layer-width="300" />
                </div>
                <template>
                    <div class="options_class check_class">
                        <s-checkbox checked="{=displayTopN=}" label="{{'显示TOPN' | i18n}}" />
                    </div>
                    <div s-if="{{displayTopN}}" class="options_class">
                        监控项：
                        <s-select datasource="{{dcgwServerMetrics}}" value="{=dcgwServerMetric=}" width="100" />
                    </div>
                </template>
                <div class="options_class">
                    <s-button
                        class="s-icon-button"
                        disabled="{{!dcgwServer.datasource.length}}"
                        on-click="onTimeRefresh('server')"
                        ><outlined-refresh class="icon-class"
                    /></s-button>
                </div>
            </div>
            <div class="dcgw-monitor-trends">
                <div class="monitor-trend-box" s-for="item,index in serverChart">
                    <bcm-chart-panel
                        s-ref="server-alarm-chart-{{index}}"
                        withFilter="{{false}}"
                        scope="{{item.scope}}"
                        dimensions="{{item.dimensions}}"
                        statistics="{{item.statistics}}"
                        title="{{item.title}}"
                        api-type="dimensions"
                        options="{{options}}"
                        startTime="{=serverTime.startTime=}"
                        endTime="{=serverTime.endTime=}"
                        period="{{serverTime.monitorDefaultPeriod}}"
                        metrics="{{item.metrics}}"
                        unit="{{item.unit}}"
                        bitUnit="{{item.bitUnit}}"
                        width="{{'auto'}}"
                        height="{{230}}"
                        sdk="{{bcmSdk}}"
                        proccessor="{{proccessor}}"
                    >
                    </bcm-chart-panel>
                </div>
            </div>
        </div>
    </div>
`;

@asComponent('@dcgw-monitor')
@template(tpl)
@invokeSUI
@invokeSUIBIZ
class DcgwMonitor extends Component {
    static components = {
        'bcm-chart-panel': BcmChartPanel,
        'outlined-refresh': OutlinedRefresh,
        's-virtual-list': VirtualList
    };

    static filters = {
        statusStyle(status: string) {
            let config = DcGatewayStatus.fromValue(status);
            return config ? config.styleClass : '';
        },
        statusText(status: string) {
            let config = DcGatewayStatus.fromValue(status);
            return config ? config.text : '';
        }
    };

    static computed = {
        dcgwServerTip() {
            if (this.data.get('displayTopN')) {
                return '最多选择10个监控对象';
            }
            return 'VPC网段使用IP数量少于10个时，可展示所有VPC网段使用IP的监控信息。超过10个时，用户可自定义选择需要展示的VPC网段使用IP的监控信息，最多可选10个。';
        }
    };

    initData() {
        return {
            alarmInfo: {
                okStateCount: 0,
                alarmStateCount: 0,
                insufficientStateCount: 0
            },
            range: {
                begin: new Date(moment().subtract(40, 'day').valueOf()),
                end: new Date(moment().valueOf())
            },
            dcgwTime: {
                timeRange: {
                    begin: new Date(moment().subtract(1, 'hour').valueOf()),
                    end: new Date(moment().valueOf())
                },
                startTime: moment().subtract(1, 'hour').utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
                endTime: moment().utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
                monitorDefaultPeriod: 60
            },
            serverTime: {
                timeRange: {
                    begin: new Date(moment().subtract(30, 'day').valueOf()),
                    end: new Date(moment().valueOf())
                },
                startTime: moment().subtract(30, 'day').utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
                endTime: moment().utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
                monitorDefaultPeriod: 60
            },
            endOriginTime: moment().valueOf(),
            apiType: 'metricName',
            bcmSdk: new BcmSDK({client: window.$http, context: window.$context}),
            shortcutItems,
            instance: {},
            statistics: 'average',
            statisticsDatasource: [
                {
                    text: '平均值',
                    value: 'average'
                },
                {
                    text: '最大值',
                    value: 'maximum'
                },
                {
                    text: '最小值',
                    value: 'minimum'
                },
                {
                    text: '和值',
                    value: 'sum'
                }
            ],
            displayTopN: false,
            dcgwServerMetrics: [
                {
                    text: '入流量',
                    value: 'DetailWebInBytes'
                },
                {
                    text: '出流量',
                    value: 'DetailWebOutBytes'
                },
                {
                    text: '入带宽',
                    value: 'DetailWebInBitsPerSecond'
                },
                {
                    text: '出带宽',
                    value: 'DetailWebOutBitsPerSecond'
                },
                {
                    text: '入包速率',
                    value: 'DetailWebInPkgPerSecond'
                },
                {
                    text: '出包速率',
                    value: 'DetailWebOutPkgPerSecond'
                }
            ],
            dcgwServerMetric: 'DetailWebOutBytes',
            proccessor: this.proccessor.bind(this),
            options: {dataZoom: {start: 0}}
        };
    }

    inited() {
        ['dcgwTime', 'serverTime'].forEach(it => {
            this.watch(`${it}.timeRange`, timeRange => {
                this.onTimeChange(it, {value: timeRange});
            });
        });
    }

    attached() {
        this.getDetail();
        this.initMonitorChart();
        this.loadDcgwServerList();
        this.watch('dcgwServer.value', (value: any) => {
            if (!this.data.get('displayTopN')) {
                this.updateDcgwserverDisable(value);
            }
            this.loadDcgwServerMetrics();
        });
        this.watch('statistics', () => {
            if (this.data.get('displayTopN')) {
                this.getServerDcgwWhiteData();
            } else {
                this.loadDcgwServerMetrics();
            }
        });
        this.watch('displayTopN', (val: boolean) => {
            if (val) {
                this.getServerDcgwWhiteData();
            } else {
                const dcgwServers = this.data.get('dcgwServers');
                this.data.set('dcgwServer.datasource', dcgwServers);
                let dcgwServerDatasource = [];
                if (dcgwServers.length > 0) {
                    dcgwServerDatasource = [dcgwServers[0].value];
                }
                this.data.set('dcgwServer.value', dcgwServerDatasource);
            }
        });
        this.watch('dcgwServerMetric', () => {
            if (this.data.get('displayTopN')) {
                this.getServerDcgwWhiteData();
            } else {
                this.loadDcgwServerList();
            }
        });
    }

    loadDcgwServerList() {
        return this.$http
            .getVpcNetworkIp({
                userId: this.$context.getUserId(),
                namespace: 'BCE_DEDICATEDCONN_GATEWAY',
                metricName: this.data.get('dcgwServerMetric'),
                region: this.$context.getCurrentRegion().id,
                resourceId: this.data.get('context').dcgwId,
                dimension: 'ClientIp'
            })
            .then((data: any) => {
                let result = [];
                u.each(data, item => {
                    result.push({
                        value: item.value,
                        text: item.value
                    });
                });
                this.data.set('dcgwServers', result);
                this.data.set('dcgwServer.datasource', result);
                if (result.length > 0) {
                    let selectValue = this.data.get('dcgwServer.value') || [];
                    let newSelectValue = selectValue.filter(
                        (item: string) => result.findIndex((res: any) => res.value === item) > -1
                    );
                    this.data.set('dcgwServer.value', newSelectValue.length > 0 ? newSelectValue : [result[0].value]);
                } else {
                    this.data.set('dcgwServer.value', []);
                }
            });
    }
    updateDcgwserverDisable(value: any) {
        if (value.length < 10) {
            this.data.set('dcgwServer.datasource', this.data.get('dcgwServers'));
            return;
        }
        const datasource = u.cloneDeep(this.data.get('dcgwServer.datasource'));
        for (let i = 0; i < datasource.length; i++) {
            const item = datasource[i];
            item.disabled = !value.includes(item.value);
            datasource[i] = item;
        }
        this.data.set('dcgwServer.datasource', datasource);
    }

    loadDcgwServerMetrics() {
        let chartConfig = [];
        let dimensions = [];
        const statistics = this.data.get('statistics');
        u.each(this.data.get('dcgwServer.value'), value => {
            if (dimensions.length < 10) {
                dimensions.push('InstanceId:' + this.data.get('context').dcgwId + ';ClientIp:' + value);
            }
        });
        u.each(DcgwTopNMetrics, item => {
            let config = {
                scope: 'BCE_DEDICATEDCONN_GATEWAY',
                period: 60,
                statistics,
                title: item.title,
                unit: item.unit,
                bitUnit: item.bitUnit,
                metrics: item.metrics,
                dimensions
            };
            chartConfig.push(config);
        });
        this.data.set('serverChart', chartConfig);
        this.nextTick(() => {
            this.onRefresh('server');
        });
    }

    initMonitorChart() {
        let chartConfig = [];
        u.each(DcgwMetrics, item => {
            let config = {
                scope: 'BCE_DEDICATEDCONN_GATEWAY',
                period: 60,
                statistics: item.statistics || 'average',
                title: item.title,
                unit: item.unit,
                bitUnit: item.bitUnit,
                metrics: item.metrics,
                dimensions: 'InstanceId:' + this.data.get('context').dcgwId
            };
            chartConfig.push(config);
        });
        this.data.set('chartConfig', chartConfig);
    }

    // 跳转到报警详情
    toAlarmDetail() {
        redirect('/bcm/#/bcm/alarm/rule/list~scope=BCE_DEDICATEDCONN_GATEWAY');
    }

    getDetail() {
        return this.$http
            .dcgwDetail(
                {
                    dcgwId: this.data.get('context').dcgwId
                },
                {'x-silent-codes': ['NoSuchObject']}
            )
            .then((instance: any) => {
                const apiType = instance.instanceType === 'rdsproxy' ? 'dimensions' : 'metricName';
                this.data.set('instance', instance);
                this.data.set('apiType', apiType);
                this.onRefresh();
            });
    }

    onTimeRefresh(type: 'dcgw' | 'server' = 'dcgw') {
        const typeMapMonitor = {
            dcgw: 'dcgwTime',
            server: 'serverTime'
        };
        if (this.data.get(`${typeMapMonitor[type]}.timeRange.end`).valueOf() >= this.data.get('endOriginTime')) {
            this.data.set(`${typeMapMonitor[type]}.timeRange.end`, new Date(moment().valueOf()));
        } else {
            this.onRefresh();
        }
    }

    onTimeChange(type: string, {value}) {
        const key = {
            dcgwTime: 'dcgw',
            serverTime: 'server'
        };
        const startTime = moment(value.begin).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        const endTime = moment(value.end).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        let ms = moment(value.end) - moment(value.begin);
        let hourTime = Math.round(ms / 86400000);
        switch (true) {
            case hourTime <= 1:
                this.data.set(`${type}.monitorDefaultPeriod`, 60);
                break;
            case hourTime <= 3:
                this.data.set(`${type}.monitorDefaultPeriod`, 300);
                break;
            case hourTime <= 7:
                this.data.set(`${type}.monitorDefaultPeriod`, 600);
                break;
            case hourTime <= 14:
                this.data.set(`${type}.monitorDefaultPeriod`, 1800);
                break;
            case hourTime <= 40:
                this.data.set(`${type}.monitorDefaultPeriod`, 3600);
                break;
            default:
                break;
        }
        this.data.set(`${type}.startTime`, startTime);
        this.data.set(`${type}.endTime`, endTime);
        if (type === 'serverTime' && this.data.get('displayTopN')) {
            this.getServerDcgwWhiteData();
        } else {
            this.nextTick(() => {
                this.onRefresh(key[type]);
            });
        }
    }

    onRefresh(type = 'dcgw') {
        let chartConfig = type === 'server' ? this.data.get('serverChart') : this.data.get('chartConfig');
        u.map(chartConfig, (item, i) => {
            this.ref(`${type}-alarm-chart-${i}`)?.loadMetrics();
        });
    }
    proccessor(data: any) {
        const statistics = this.data.get('statistics');
        if (data && data.series && data.series.length > 0) {
            data.series.forEach((series: any) => {
                series.name = series.name.split(',')[1] || series.name;
                series.data = series.data.map(item => {
                    if (!item || Object.keys(item).length === 0) {
                        let obj = {};
                        obj[statistics] = 0;
                        return obj;
                    }
                    return item;
                });
            });
        }
        return data;
    }
    async getServerDcgwWhiteData() {
        const {
            statistics,
            serverTime: {startTime, endTime},
            dcgwServerMetric
        } = this.data.get('');
        const dcgwId = this.data.get('context').dcgwId;
        const dimension = `InstanceId:${dcgwId}`;
        const region = this.$context.getCurrentRegion().id;
        const params = {
            statistics,
            startTime,
            endTime,
            topNum: 10,
            dimension,
            service: 'BCE_DEDICATEDCONN_GATEWAY',
            region,
            userId: this.$context.getUserId(),
            metricName: dcgwServerMetric
        };
        const [{topDatas} = {topDatas: []}] = await this.$http.getServerNatWhite(params);
        const topNServer = topDatas.map(
            (item: any) => item.dimensions.filter(dimens => dimens.name === 'ClientIp')[0].value
        );
        let top = topNServer.map(item => ({
            text: item,
            value: item
        }));
        this.data.set('dcgwServer.datasource', top);
        let natServerDatasource = [];
        if (top.length > 0) {
            natServerDatasource = top.map((item: any) => item.value);
        }
        this.data.set('dcgwServer.value', natServerDatasource);
    }
}

export default San2React(Processor.autowireUnCheckCmpt(DcgwMonitor));
