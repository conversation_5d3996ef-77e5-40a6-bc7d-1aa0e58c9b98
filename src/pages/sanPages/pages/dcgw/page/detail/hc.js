/*
 * @description: 创建专线网关
 * @file: network/dcgw/pages/create.js
 * @author: p<PERSON><PERSON><PERSON><EMAIL>
 */
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {OutlinedPlus} from '@baidu/sui-icon';
import {San2React} from '@baidu/bce-react-toolkit';

import Dialog from './hcDialog';
import {SCHEMA} from './schema';
import {healthCheckStatus} from '@/pages/sanPages/common/enum';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import '../../style/hc.less';

/* eslint-disable */
const {asComponent, invokeSUI, invokeSUIBIZ, template, invokeComp} = decorators;
const tpl = html`
    <div>
        <div class="{{klass}} dcgw-hc-v2">
            <div class="content-box">
                <s-alert skin="warning"> {{'关闭链路探测的专线网关在成功绑定物理专线后状态始终为可用。'}}<p>默认链路探测配置下，关闭BGP邻居无法主动切流，推荐关闭物理端口或虚接口。</p> </s-alert>
                <h4>
                    链路探测
                    <s-tip-button
                        placement="top"
                        disabled="{{createHc.disable}}"
                        skin="primary"
                        isDisabledVisibile="{{true}}"
                        on-click="addHc"
                        class="hc_button_class"
                    >
                        <div slot="content">{{createHc.message}}</div>
                        <outlined-plus />
                        {{'新增规则'}}
                    </s-tip-button>
                </h4>
                <s-table columns="{{columns}}" datasource="{{healthDatasource}}" loading="{{loading}}">
                    <div slot="empty">
                        <s-empty on-click="addHc"> </s-empty>
                    </div>
                    <div slot="h-healthCheckInterval">
                        <span>健康检查间隔</span>
                        <s-tip
                            class="inline-tip"
                            placement="right"
                            content="{{'输入范围为1～60间的整数，建议设置为3秒'}}"
                            skin="question"
                        />
                    </div>
                    <div slot="h-unhealthThreshold">
                        <span>不健康阈值</span>
                        <s-tip
                            class="inline-tip"
                            placement="right"
                            content="{{'连续健康检查失败次数，超过这个阈值，专线网关所绑定专线通道将被认定为异常。'}}"
                            skin="question"
                        />
                    </div>
                    <div slot="h-healthThreshold">
                        <span>健康阈值</span>
                        <s-tip
                            class="inline-tip"
                            placement="right"
                            content="{{'连续健康检查成功次数，超过这个阈值，专线网关所绑定专线通道将被认定为可用。'}}"
                            skin="question"
                        />
                    </div>
                    <div slot="h-autoGenerateRouteRule">
                        <span>自动生成路由</span>
                        <s-tip
                            class="inline-tip"
                            placement="right"
                            content="{{'默认开启，为用户自动生成一条路由用于连通性探测，若关闭则需要用户手动添加到IDC互联IP的路由用于连通性探测。'}}"
                            skin="question"
                        />
                    </div>
                    <div slot="c-status">
                        <span class="{{row.status | statusClass}}">{{row | getStatus}}</span>
                        <s-tip
                            s-if="{{row.status === 'init_failed'}}"
                            class="inline-tip"
                            placement="right"
                            content="请用户检测配置，如用户端设备加去往{{FLAG.NetworkSupportXS ? '' : '百度'}}云端路由。"
                            skin="question"
                        />
                    </div>
                    <div slot="c-healthCheckAction">
                        <span>{{row | getHealthCheckAction}}</span>
                    </div>
                    <div slot="c-ping">
                        <span>PING(ICMP)</span>
                    </div>
                    <div slot="c-action">
                        <a href="javascript:void(0)" on-click="editHc(row)">编辑</a>
                    </div>
                </s-table>
                <s-pagination
                    s-if="{{pager.total}}"
                    slot="footer"
                    layout="{{'total, pageSize, pager, go'}}"
                    pageSize="{{pager.size}}"
                    total="{{pager.total}}"
                    page="{{pager.page}}"
                    on-pagerChange="onPageChange"
                    on-pagerSizeChange="onPageSizeChange"
                />
            </div>
        </div>
    </div>
`;
/* eslint-enable */

@template(tpl)
@invokeComp('@dcgw-instance-edit', '@hc-dialog')
@invokeSUI
@invokeSUIBIZ
@asComponent('@dcgw-hc-list')
class HealthCheck extends Component {
    static components = {
        'outlined-plus': OutlinedPlus
    };
    static filters = {
        getHealthCheckAction(row) {
            if (row.healthCheckAction === 'open') {
                return '开启';
            } else if (row.healthCheckAction === 'pause') {
                return '暂停';
            } else {
                return '关闭';
            }
        },
        statusClass(value) {
            return healthCheckStatus.fromValue(value).styleClass || '';
        },
        getStatus(row) {
            let config = healthCheckStatus.fromValue(row.status).text;
            return config || '-';
        }
    };
    initData() {
        return {
            FLAG,
            klass: ['dcgw-detail-v2'],
            columns: SCHEMA,
            healthDatasource: [],
            pager: {
                size: 10,
                page: 1,
                total: 0
            },
            loading: true,
            createHc: {}
        };
    }
    attached() {
        this.getDetail().then(() => {
            this.loadPage();
        });
    }
    loadPage() {
        let dcgwId = this.data.get('context').dcgwId;
        let {page, size} = this.data.get('pager');
        let instance = this.data.get('instance');
        this.$http
            .healthCheckList(dcgwId, {pageNo: page, pageSize: size})
            .then(data => {
                this.data.set('loading', false);
                this.data.set('healthDatasource', data.result);
                this.data.set('pager.total', data.totalCount);
                this.data.set('createHc', {});
                if (instance.status !== 'running') {
                    this.data.set('createHc', {
                        disable: true,
                        message: '仅可用状态的专线网关可添加规则'
                    });
                    return;
                }
                let createHc = this.data.get('createHc');
                if (data.totalCount === 1) {
                    if (!instance.associatedChannels && !createHc.disable) {
                        this.data.set('createHc', {
                            disable: true,
                            message: '当前状态仅支持添加1个链路探测，如需添加多个链路探测，请首先使用专线通道关联功能。'
                        });
                    } else {
                        this.data.set('createHc', {});
                    }
                } else {
                    if (!createHc.disable) {
                        this.data.set('createHc', {});
                    }
                }
            })
            .catch(() => this.data.set('loading', false));
    }
    addHc() {
        let dcgwId = this.data.get('context').dcgwId;
        const create = new Dialog({
            data: {
                title: '添加链路探测规则',
                open: true,
                formData: {},
                healthCheckId: '',
                dcgwId,
                healthDatasource: this.data.get('healthDatasource')
            }
        });
        create.on('onConfirm', () => {
            this.data.set('loading', true);
            this.loadPage();
        });
        create.attach(document.body);
    }
    editHc(row) {
        let dcgwId = this.data.get('context').dcgwId;
        const create = new Dialog({
            data: {
                title: '编辑链路探测规则',
                open: true,
                formData: {},
                healthCheckId: row.healthCheckId,
                dcgwId,
                healthDatasource: this.data.get('healthDatasource'),
                origin: row
            }
        });
        create.on('onConfirm', () => {
            this.data.set('loading', true);
            this.loadPage();
        });
        create.attach(document.body);
    }
    onPageChange({value}) {
        let pager = this.data.get('pager');
        pager.page = value.page;
        pager.size = value.pageSize;
        this.data.set('pager', pager);
        this.loadPage();
    }
    onPageSizeChange({value}) {
        let pager = this.data.get('pager');
        pager.page = 1;
        pager.size = value.pageSize;
        this.data.set('pager', pager);
        this.loadPage();
    }
    getDetail() {
        return this.$http
            .dcgwDetail(
                {
                    dcgwId: this.data.get('context').dcgwId
                },
                {'x-silent-codes': ['NoSuchObject']}
            )
            .then(res => {
                this.data.set('instance', res);
            });
    }
}

export default San2React(Processor.autowireUnCheckCmpt(HealthCheck));
