/*
 * @description: 专线网关列表页
 * @file: network/dcgw/pages/List.js
 * @author: p<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {checker} from '@baiducloud/bce-opt-checker';
import {San2React} from '@baidu/bce-react-toolkit';
import {Notification} from '@baidu/sui';
import {OutlinedPlus} from '@baidu/sui-icon';

import rules from '../../rules';
import Create from './create';
import {DcGatewayStatus} from '@/pages/sanPages/common/enum';
import Confirm from '@/pages/sanPages/components/confirm';

const {asComponent, invokeSUI, invokeSUIBIZ, template, invokeComp} = decorators;
const tpl = html` <template>
    <s-biz-page class="dcgw-nat-list">
        <span slot="header">
            {{title}}
            <s-tip class="inline-tip" placement="top" content="{{tip}}" skin="question" />
        </span>
        <div class="list-page-tb-left-toolbar" slot="tb-left">
            <s-tooltip trigger="{{natRuleCreate.disable ? 'hover' : ''}}" placement="top">
                <!--bca-disable-next-line-->
                <div slot="content">{{natRuleCreate.message | raw}}</div>
                <s-button disabled="{{natRuleCreate.disable}}" skin="primary" on-click="onCreate">
                    <outlined-plus />
                    添加规则</s-button
                >
            </s-tooltip>
            <s-tooltip class="left_class" trigger="{{releaseNatRule.disable ? 'hover' : ''}}" placement="top">
                <div slot="content">
                    <!--bca-disable-next-line-->
                    {{releaseNatRule.message | raw}}
                </div>
                <s-button on-click="onRelease" disabled="{{releaseNatRule.disable}}"> 释放</s-button>
            </s-tooltip>
        </div>
        <div slot="tb-right" class="right_bar">
            <s-select
                style="background:#f5f5f5;"
                on-change="keywordTypeChange"
                datasource="{{keywordTypeList}}"
                value="{=keywordType=}"
            >
            </s-select>
            <s-search class="left_class" placeholder="{{searchPlaceholder}}" value="{=keyword=}" on-search="onSearch" />
        </div>
        <s-table
            columns="{{table.columns}}"
            loading="{{table.loading}}"
            error="{{table.error}}"
            datasource="{{table.datasource}}"
            on-selected-change="tableSelected($event)"
            selection="{=table.selection=}"
        >
            <div slot="error">
                {{table.error ? table.error : '啊呀，出错了？'}}
                <a href="javascript:;" on-click="refresh">重新加载</a>
            </div>
            <div slot="empty">
                <s-empty actionText=""> </s-empty>
            </div>
            <div slot="c-opt">
                <span class="operations">
                    <a href="javascript:void(0)" on-click="edit(row)">编辑</a>
                    <a href="javascript:void(0)" on-click="delete(row)">删除</a>
                </span>
            </div>
        </s-table>
        <s-pagination
            s-if="{{pager.total}}"
            slot="footer"
            layout="{{'total, pageSize, pager, go'}}"
            pageSize="{{pager.size}}"
            total="{{pager.total}}"
            page="{{pager.page}}"
            on-pagerChange="onPageChange"
            on-pagerSizeChange="onPageSizeChange"
        />
        <p class="selectTip">{{selectTip}}</p>
    </s-biz-page>
</template>`;

@asComponent('@dcgw-nat-list')
@invokeComp('@dcgw-nat-create')
@template(tpl)
@invokeSUI
@invokeSUIBIZ
class DcgwNatList extends Component {
    static components = {
        'outlined-plus': OutlinedPlus
    };
    static computed = {
        selectTip() {
            let length = this.data.get('selectedItems').length;
            let total = this.data.get('pager.total');
            return `已选中${length}条/共${total}条`;
        }
    };

    initData() {
        let title = '云端静态NAT';
        let type = 'VPC_STATIC_NAT';
        let currentSidebar = 'nat';
        let emptyText = '您暂未添加云端静态NAT规则';
        let tip =
            '云端私有网络内的原IP映射为新 IP，并以新IP身份与专线对端互访，用于解决专线两端内网IP冲突问题，需要结合IDC端静态NAT一同使用。';
        let columns = [
            {
                name: 'internalIp',
                label: '原IP',
                render(item) {
                    return u.escape(item.internalIp).replace('/32', '');
                }
            },
            {
                name: 'externalIp',
                label: '映射IP',
                render(item) {
                    return u.escape(item.externalIp).replace('/32', '');
                }
            },
            {
                name: 'description',
                label: '描述',
                render(item) {
                    let desc = u.escape(item.description) || '-';
                    return `<span class="truncated">${desc}</span>`;
                }
            },
            {
                name: 'opt',
                label: '操作'
            }
        ];
        if (location.hash.indexOf('idcdnat') > -1) {
            title = '云端IP端口静态NAT';
            tip = 'IDC端主动访问私有网络，需访问映射后的IP端口与私有网络内原IP端口进行通信，回包不受影响。';
            type = 'IDC_DNAT';
            columns.unshift({
                name: 'protocol',
                label: '协议',
                render(item) {
                    return u.escape(item.protocol).toUpperCase();
                }
            });
            columns[1] = {
                name: 'internalIp',
                label: '原IP',
                render(item) {
                    return u.escape(item.internalIp).replace('/32', '') + ':' + u.escape(item.internalPort);
                }
            };
            columns[2] = {
                name: 'externalIp',
                label: '映射IP',
                render(item) {
                    return u.escape(item.externalIp).replace('/32', '') + ':' + u.escape(item.externalPort);
                }
            };
            currentSidebar = 'idcdnat';
            emptyText = '您暂未添加云端IP端口静态NAT规则';
        } else if (location.hash.indexOf('idcnat') > -1) {
            title = 'IDC端静态NAT';
            type = 'IDC_STATIC_NAT';
            tip =
                'IDC端内的内网IP映射为新IP，并以新IP身份与云端私有网络互访，用于解决专线两端内网IP冲突问题，需要结合云端静态NAT一同使用';
            currentSidebar = 'idcnat';
            emptyText = '您暂未添加IDC端静态NAT规则';
        }
        return {
            klass: ['dcgw-detail-v2'],
            vpcId: '',
            selectedItems: [],
            title,
            type,
            emptyText,
            currentSidebar,
            keywordTypeList: [
                {text: '原IP', value: 'internalIp'},
                {text: '映射IP', value: 'externalIp'},
                {text: '描述', value: 'description'}
            ],
            keywordType: 'description',
            keyword: '',
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                columns: columns,
                datasource: []
            },
            pager: {
                size: 10,
                page: 1,
                total: 0
            },
            openDialog: false,
            editTag: false,
            dialogContent: '',
            optType: '',
            natRuleCreate: {
                disable: false
            },
            releaseNatRule: {
                disable: false
            },
            searchPlaceholder: '请输入描述进行搜索',
            tip: tip
        };
    }

    keywordTypeChange(e) {
        if (e.value) {
            switch (e.value) {
                case 'internalIp':
                    this.data.set('searchPlaceholder', '请输入原IP进行搜索');
                    break;
                case 'externalIp':
                    this.data.set('searchPlaceholder', '请输入映射IP进行搜索');
                    break;
                case 'description':
                    this.data.set('searchPlaceholder', '请输入描述进行搜索');
                    break;
                default:
                    break;
            }
        }
    }

    onPageChange({value}) {
        this.data.set('pager.page', value.page);
        this.data.set('table.selection.selectedIndex', []);
        this.loadPage();
    }

    onPageSizeChange({value}) {
        this.data.set('pager.size', value.pageSize);
        if (this.data.get('pager.page') === 1) {
            this.loadPage();
        } else {
            this.data.set('pager.page', 1);
        }
    }

    tableSelected({value}) {
        this.data.set('selectedItems', value.selectedItems);
        let {releaseNatRule} = checker.check(rules, value.selectedItems);
        this.data.set('releaseNatRule', releaseNatRule);
    }

    refresh() {
        this.resetTable();
        return this.loadPage();
    }

    // 重置列表
    resetTable() {
        this.data.set('pager.page', 1);
        this.data.set('table.selection.selectedIndex', []);
    }

    delete(row) {
        let confirm = new Confirm({
            data: {
                open: true,
                content: '确认删除该IP映射？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http.dcgwNatRuleDelete({ids: [row.ruleId], dcgwId: this.data.get('dcgwId')}).then(() => {
                this.refresh();
                Notification.success('删除成功');
                this.getQuota();
            });
        });
    }

    edit(row) {
        let create = new Create({
            data: {
                instance: row,
                open: true,
                vpcId: this.data.get('vpcId'),
                dcgwId: this.data.get('dcgwId'),
                type: this.data.get('type')
            }
        });
        create.on('update', () => {
            this.loadPage();
        });
        create.attach(document.body);
    }

    onRelease() {
        let num = this.data.get('selectedItems').length;
        let ids = this.data.get('selectedItems').map(item => item.ruleId);
        let confirm = new Confirm({
            data: {
                open: true,
                content: `确定删除这${num}条规则？`
            }
        });
        confirm.on('confirm', () => {
            this.$http.dcgwNatRuleDelete({ids, dcgwId: this.data.get('dcgwId')}).then(() => {
                this.refresh();
                this.getQuota();
                Notification.success('删除成功');
            });
        });
        confirm.attach(document.body);
    }

    onCreate() {
        let create = new Create({
            data: {
                open: true,
                vpcId: this.data.get('vpcId'),
                dcgwId: this.data.get('dcgwId'),
                type: this.data.get('type')
            }
        });
        create.on('create', () => {
            this.loadPage();
            this.getQuota();
        });
        create.attach(document.body);
    }

    onSearch() {
        return this.refresh();
    }

    getSearchCriteria() {
        const {dcgwId, type, pager, keyword, keywordType} = this.data.get('');
        return u.extend({}, {dcgwId, type, keyword, keywordType}, {pageNo: pager.page, pageSize: pager.size});
    }

    loadPage(payload) {
        this.data.set('table.loading', true);
        payload = payload || this.getSearchCriteria();
        this.$http
            .dcgwNatRuleList(payload)
            .then(data => {
                this.data.set('table.datasource', data.result);
                this.data.set('pager.total', data.totalCount);
                this.data.set('table.loading', false);
            })
            .catch(() => this.data.set('table.loading', false));
    }

    getDetail() {
        return this.$http
            .dcgwDetail({
                dcgwId: this.data.get('dcgwId')
            })
            .then(instance => {
                let config = DcGatewayStatus.fromValue(instance.status);
                instance.statusClass = config.styleClass;
                instance.statusText = config.text;
                this.data.set('instance', instance);
                let {natRuleCreate} = checker.check(rules, [], 'natRuleCreate', {quota: 0, status: instance.status});
                this.data.set('natRuleCreate', natRuleCreate);
            });
    }

    getQuota() {
        const {dcgwId, type, instance} = this.data.get('');
        this.$http.dcgwNatRuleQuota({dcgwId, type}).then(result => {
            let title = this.data.get('title');
            let {natRuleCreate} = checker.check(rules, [], 'natRuleCreate', {
                quota: result.free,
                status: instance.status,
                title
            });
            this.data.set('natRuleCreate', natRuleCreate);
        });
    }

    attached() {
        this.data.set('dcgwId', this.data.get('context').dcgwId);
        this.data.set('vpcId', this.data.get('context').vpcId);

        this.getDetail().then(() => this.getQuota());
        this.loadPage();
        let {releaseNatRule} = checker.check(rules, []);
        this.data.set('releaseNatRule', releaseNatRule);
    }
}

export default San2React(Processor.autowireUnCheckCmpt(DcgwNatList));
