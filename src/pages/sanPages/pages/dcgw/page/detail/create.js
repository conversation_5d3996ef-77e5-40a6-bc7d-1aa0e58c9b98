/*
 * @description: 专线nat创建
 * @file: dcgw/pages/nat/create.js
 * @author: p<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';

import {checkIpInCidr} from '@/pages/sanPages/utils/common';

const IP_CIDR =
    '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([1-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-4])?$'; //eslint-disable-line
/* eslint-disable */
const {asComponent, invokeSUI, invokeSUIBIZ, template} = decorators;
const tpl = html`
    <template>
        <s-dialog
            class="dcgw-nat-create"
            closeAfterMaskClick="{{false}}"
            open="{=open=}"
            title="{{title}}"
            width="{{510}}"
        >
            <s-form
                s-ref="form"
                label-align="left"
                data="{=formData=}"
                rules="{{rules}}"
                label-col="{{labelCol}}"
                wrapper-col="{{wrapperCol}}"
                label-align="left"
            >
                <s-form-item s-if="type==='IDC_DNAT'" prop="protocol" label="协议">
                    <s-select datasource="{{protocolList}}" width="{{220}}" value="{=formData.protocol=}"> </s-select>
                </s-form-item>
                <s-form-item label="原IP：">
                    <s-input width="{{240}}" value="{=formData.internalIp=}" placeholder="请输入原IP"></s-input>
                    <span s-if="type==='IDC_DNAT'">:</span>
                    <s-input
                        width="{{50}}"
                        s-if="type==='IDC_DNAT'"
                        value="{=formData.internalPort=}"
                        placeholder="1-65535"
                    ></s-input>
                    <p style="color:#f33e3e;">{{internalIpErr}}</p>
                </s-form-item>
                <s-form-item label="映射IP：">
                    <s-input width="{{240}}" value="{=formData.externalIp=}" placeholder="请输入映射IP"></s-input>
                    <span s-if="type==='IDC_DNAT'">:</span>
                    <s-input
                        width="{{50}}"
                        s-if="type==='IDC_DNAT'"
                        value="{=formData.externalPort=}"
                        placeholder="1-65535"
                    ></s-input>
                    <p style="color:#f33e3e;">{{externalIpErr}}</p>
                </s-form-item>
                <s-form-item prop="description" label="描述：">
                    <s-input-text-area
                        width="{{220}}"
                        height="{{60}}"
                        value="{=formData.description=}"
                        maxLength="200"
                        placeholder="请输入"
                    >
                    </s-input-text-area>
                </s-form-item>
            </s-form>
            <div slot="footer">
                <s-button on-click="close">取消</s-button>
                <s-button skin="primary" disabled="{{disableSub}}" on-click="dialogConfirm">确定</s-button>
            </div>
        </s-dialog>
    </template>
`;
/* eslint-enable */

@template(tpl)
@asComponent('@dcgw-nat-create')
@invokeSUI
@invokeSUIBIZ
class DcgwNatCreate extends Component {
    static computed = {
        title() {
            let type = this.data.get('type');
            let instance = this.data.get('instance');
            if (type === 'VPC_STATIC_NAT') {
                return instance ? '编辑云端静态NAT规则' : '添加云端静态NAT规则';
            } else if (type === 'IDC_DNAT') {
                return instance ? '编辑云端IP端口静态NAT规则' : '添加云端IP端口静态NAT规则';
            } else if (type === 'IDC_STATIC_NAT') {
                return instance ? '编辑IDC端静态NAT规则' : '添加IDC端静态NAT规则';
            }
        }
    };

    initData() {
        return {
            open: false,
            title: '添加云端静态NAT规则',
            labelCol: {span: 4},
            wrapperCol: {span: 19},
            formData: {
                internalIp: '',
                externalIp: '',
                description: '',
                internalPort: '',
                externalPort: '',
                protocol: 'TCP'
            },
            protocolList: [
                {text: 'TCP', value: 'TCP'},
                {text: 'UDP', value: 'UDP'}
            ],
            rules: {
                description: [{min: 0, max: 200, message: '长度0到200个字符'}]
            },
            vpcInfo: null,
            internalIpErr: '',
            externalIpErr: '',
            disableSub: false
        };
    }

    attached() {
        if (this.data.get('instance')) {
            let instance = {...this.data.get('instance')};
            instance.internalIp = instance.internalIp.split('.');
            instance.internalIp[3] = instance.internalIp[3].replace('/32', '');
            instance.internalIp = instance.internalIp.join('.');
            instance.externalIp = instance.externalIp.split('.');
            instance.externalIp[3] = instance.externalIp[3].replace('/32', '');
            instance.externalIp = instance.externalIp.join('.');
            instance.protocol = instance.protocol.toUpperCase();
            this.data.set('formData', instance);
        }
        this.loadVpcInfo();
    }

    loadVpcInfo() {
        let vpcId = this.data.get('vpcId');
        this.$http.vpcInfo({vpcIds: [vpcId]}).then(data => {
            let vpc = null;
            vpc = data[vpcId] || {};
            this.data.set('vpcInfo', vpc);
        });
    }

    checkInternalIp() {
        let vpcInfo = this.data.get('vpcInfo');
        let internalIp = this.data.get('formData.internalIp');
        if (!new RegExp(IP_CIDR).test(internalIp)) {
            this.data.set('internalIpErr', 'IP范围不符合规则');
            return false;
        } else {
            this.data.set('internalIpErr', '');
            if (vpcInfo && this.data.get('type') === 'IDC_DNAT') {
                if (checkIpInCidr(vpcInfo.cidr, internalIp)) {
                    return true;
                }
                if (vpcInfo.auxiliaryCidr && vpcInfo.auxiliaryCidr.length) {
                    let flag = false;
                    vpcInfo.auxiliaryCidr.forEach(item => {
                        if (checkIpInCidr(item, internalIp)) {
                            flag = true;
                        }
                    });
                    if (flag) {
                        return true;
                    }
                }
                this.data.set('internalIpErr', '原IP必须在私有网络CIDR范围内');
            } else {
                return true;
            }
        }
        return false;
    }

    checkExternalIp() {
        let externalIp = this.data.get('formData.externalIp');
        if (!new RegExp(IP_CIDR).test(externalIp)) {
            this.data.set('externalIpErr', 'IP范围不符合规则');
            return false;
        }
        return true;
    }

    checkPort(port) {
        return /(^[1-9][0-9]{0,4}$)/.test(port) && port <= 65535;
    }

    close() {
        this.data.set('open', false);
    }

    async dialogConfirm() {
        let type = this.data.get('type');
        let editValue = ['internalIp', 'externalIp', 'description', 'protocol', 'internalPort', 'externalPort'];
        let payload = {
            vpcId: this.data.get('vpcId'),
            dcgwId: this.data.get('dcgwId'),
            internalIp: this.data.get('formData.internalIp'),
            externalIp: this.data.get('formData.externalIp'),
            type: type,
            description: this.data.get('formData.description')
        };
        this.data.set('externalIpErr', '');
        this.data.set('internalIpErr', '');
        if (!this.checkInternalIp() || !this.checkExternalIp()) {
            return;
        }
        if (type === 'IDC_DNAT') {
            payload.protocol = this.data.get('formData.protocol');
            payload.internalPort = this.data.get('formData.internalPort');
            payload.externalPort = this.data.get('formData.externalPort');
            if (!this.checkPort(payload.internalPort)) {
                this.data.set('internalIpErr', '端口号格式错误');
                return;
            } else {
                this.data.set('internalIpErr', '');
            }
            if (!this.checkPort(payload.externalPort)) {
                this.data.set('externalIpErr', '端口号格式错误');
                return;
            }
        }
        let instance = this.data.get('instance');
        this.data.set('disableSub', true);
        if (this.data.get('formData.ruleId')) {
            payload.ruleId = this.data.get('formData.ruleId');
            editValue.forEach(key => {
                if (payload[key] === instance[key] || instance[key] === payload[key] + '/32') {
                    delete payload[key];
                }
            });
            this.$http
                .dcgwNatRuleUpdate(payload)
                .then(() => {
                    this.data.set('open', false);
                    this.fire('update');
                })
                .catch(() => {
                    this.data.set('disableSub', false);
                });
        } else {
            let internalPayload = {
                dcgwId: this.data.get('dcgwId'),
                keywordType: 'internalIp',
                keyword: this.data.get('formData.internalIp'),
                type,
                pageNo: 1,
                pageSize: 10
            };
            let externalPayload = {
                dcgwId: this.data.get('dcgwId'),
                keywordType: 'externalIp',
                keyword: this.data.get('formData.externalIp'),
                type,
                pageNo: 1,
                pageSize: 100
            };
            if (type === 'IDC_DNAT') {
                internalPayload.keywordType = 'ipPort';
                internalPayload.keyword = internalPayload.keyword + ':' + this.data.get('formData.internalPort');
                externalPayload.keywordType = 'ipPort';
                externalPayload.keyword = externalPayload.keyword + ':' + this.data.get('formData.externalPort');
            }
            let checkSubnet = await Promise.all([
                this.$http.dcgwNatRuleList(internalPayload),
                this.$http.dcgwNatRuleList(externalPayload)
            ]);
            checkSubnet[0].result &&
                checkSubnet[0].result.length &&
                checkSubnet[0].result.find(
                    item => item.internalIp.replace('/32', '') === payload.internalIp.replace('/32', '')
                ) &&
                this.data.set('internalIpErr', '原IP不可以重复，请重新填写');
            checkSubnet[1].result &&
                checkSubnet[0].result.length &&
                checkSubnet[0].result.find(
                    item => item.externalIp.replace('/32', '') === payload.externalIp.replace('/32', '')
                ) &&
                this.data.set('externalIpErr', '映射IP不可以重复，请重新填写');
            if (this.data.get('internalIpErr') || this.data.get('externalIpErr')) {
                this.data.set('disableSub', false);
                return;
            }
            this.$http
                .dcgwNatRuleCreate(payload)
                .then(() => {
                    this.data.set('open', false);
                    this.fire('create');
                })
                .catch(() => {
                    this.data.set('disableSub', false);
                });
        }
    }
}
export default Processor.autowireUnCheckCmpt(DcgwNatCreate);
