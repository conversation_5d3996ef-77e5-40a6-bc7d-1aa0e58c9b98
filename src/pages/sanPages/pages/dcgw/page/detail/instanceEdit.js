/*
 * @description: 编辑弹窗
 * @file: dcgw/pages/nat/create.js
 * @author: p<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';

const {asComponent, invokeSUI, invokeSUIBIZ, template, withSidebar, invokeComp} = decorators;
const tpl = html`
    <template>
        <div s-if="open" class="{{kalss}}">
            <s-form
                s-ref="form"
                data="{=formData=}"
                rules="{{rules}}"
                label-col="{{labelCol}}"
                wrapper-col="{{wrapperCol}}"
                label-align="left"
            >
                <s-form-item
                    s-if="type==='name'"
                    prop="name"
                    help="大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65"
                >
                    <s-input value="{=formData.name=}" width="320"></s-input>
                </s-form-item>
                <s-form-item s-if="type==='speed'" prop="speed">
                    <s-input-number
                        on-input="handleSpeedChange"
                        value="{=formData.speed=}"
                        step="{{1}}"
                        min="{{2}}"
                        max="{{maxSpeed}}"
                    />
                </s-form-item>
                <s-form-item s-if="type==='desc'" prop="desc">
                    <s-input-text-area
                        width="{{220}}"
                        height="{{60}}"
                        value="{=formData.description=}"
                        maxLength="200"
                        placeholder="请输入"
                    >
                    </s-input-text-area>
                </s-form-item>
            </s-form>
            <s-button skin="primary" on-click="onConfirm" disabled="{{disableSubmit}}"> 确认 </s-button>
            <s-button on-click="onClose"> 取消 </s-button>
        </div>
    </template>
`;

@template(tpl)
@asComponent('@dcgw-instance-edit')
@invokeSUI
@invokeSUIBIZ
class InstanceEdit extends Component {
    static computed = {
        disableSubmit() {
            const type = this.data.get('type');
            const speed = this.data.get('instance.speed');
            const currSpeed = this.data.get('formData.speed');
            let flag = false;
            if (type === 'speed' && speed === currSpeed) {
                flag = true;
            }
            return flag;
        }
    };
    initData() {
        return {
            labelCol: {span: 4},
            wrapperCol: {span: 19},
            formData: {
                name: '',
                speed: '',
                description: ''
            },
            kalss: ['dcgw-instance-edit'],
            rules: {
                name: [
                    {pattern: /^(?!default)[a-zA-Z\u4e00-\u9fa5][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/, message: '格式错误'}
                ],
                speed: [
                    {required: true, message: '不能为空'},
                    {pattern: /^[0-9]*$/, message: '请填写数字'}
                ],
                description: [{min: 0, max: 200, message: '长度0到200个字符'}]
            },
            maxSpeed: 100000
        };
    }

    inited() {
        let {type, instance} = this.data.get();
        if (type === 'name') {
            this.data.push('kalss', 'dcgw-instance-edit-name');
            this.data.set('formData.name', instance.name);
        } else if (type === 'speed') {
            this.data.push('kalss', 'dcgw-instance-edit-speed');
            this.data.set('formData.speed', instance.speed);
        } else if (type === 'desc') {
            this.data.push('kalss', 'dcgw-instance-edit-desc');
            this.data.set('formData.description', instance.description);
        }
    }

    onClose() {
        this.data.set('open', false);
    }

    handleSpeedChange(e) {
        const {value} = e;
        const speed = this.data.get('instance.speed');
        if (+value === speed) {
            this.data.set('disableSubmit', true);
        } else {
            this.data.set('disableSubmit', false);
        }
    }

    async onConfirm() {
        await this.ref('form').validateFields();
        let payload = {
            dcgwId: this.data.get('dcgwId')
        };
        let {type} = this.data.get();
        if (type === 'name') {
            payload.name = this.data.get('formData.name');
        } else if (type === 'speed') {
            payload.speed = this.data.get('formData.speed');
        } else if (type === 'desc') {
            payload.description = this.data.get('formData.description');
        }
        this.$http.dcgwUpdate(payload).then(() => {
            this.fire('success');
            this.data.set('open', false);
        });
    }
}
export default Processor.autowireUnCheckCmpt(InstanceEdit);
