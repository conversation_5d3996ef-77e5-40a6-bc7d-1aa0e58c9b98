export const SCHEMA = [
    {
        name: 'healthCheckAction',
        label: '探测开关状态',
        width: 120,
        fixed: 'left'
    },
    {
        name: 'dcphyId',
        label: '物理专线ID',
        width: 150
    },
    {
        name: 'channelId',
        label: '专线通道ID',
        width: 200
    },
    {
        name: 'status',
        label: '链路探测状态',
        width: 160
    },
    {
        name: 'ping',
        label: '探测方法',
        width: 160,
    },
    {
        name: 'healthCheckSourceIp',
        label: '源IP',
        width: 160
    },
    {
        name: 'healthCheckDestIp',
        label: '目的IP',
        width: 160
    },
    {
        name: 'healthCheckInterval',
        label: '健康检查间隔',
        width: 180
    },
    {
        name: 'unhealthThreshold',
        label: '不健康阈值',
        width: 150
    },
    {
        name: 'healthThreshold',
        label: '健康阈值',
        width: 150
    },
    {
        name: 'action',
        label: '操作',
        width: 150,
        fixed: 'right'
    }
];