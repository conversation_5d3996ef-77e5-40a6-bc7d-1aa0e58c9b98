import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';

const {asComponent, invokeSUI, invokeSUIBIZ, template, invokeAppComp} = decorators;
import '../../style/hcDialog.less';
import rule from '@/pages/sanPages/utils/rule';
import {checkIsInSubnet} from '@/pages/sanPages/utils/common';
import RULE from '@/pages/sanPages/utils/rule';

/* eslint-disable */
const tpl = html`
    <template>
        <div>
            <s-dialog class="dcgw-hc-create" title="{{title}}" open="{{open}}">
                <s-alert skin="warning" class="alert-tip">
                    <p>{{'使用链路探测，请先在专线通道路由表添加下一跳为专线网关的路由，否则链路探测不通。'}}</p>
                    <p>
                        {{'目的IP使用自定义IP，请在专线网关所在的vpc路由表中添加目的路由，并确保对端有路由及没有ACL等限制。'}}
                    </p>
                </s-alert>
                <s-form s-ref="form" data="{=formData=}" rules="{{rules}}" class="hc-create">
                    <s-form-item prop="ipVersion" label="{{'规则类型：'}}">
                        <s-radio-radio-group
                            class="s-doc-radio"
                            style="display:inline-block"
                            datasource="{{healthIpversion}}"
                            value="{=formData.ipVersion=}"
                            disabled="{{dcphyDisabled}}"
                            radioType="radio"
                            on-change="healthIpversionChange"
                        >
                        </s-radio-radio-group>
                    </s-form-item>
                    <s-form-item prop="subnetId" label="{{'探测所在子网：'}}">
                        <s-select
                            width="{{200}}"
                            value="{=formData.subnetId=}"
                            disabled="{{dcphyDisabled}}"
                            on-change="subnetChange"
                        >
                            <s-select-option
                                s-for="item in subnetSourceList"
                                value="{{item.value}}"
                                label="{{item.text}}"
                            >
                                <s-tooltip>
                                    <div slot="content">{{item.text}}</div>
                                    <div>{{item.text}}</div>
                                </s-tooltip>
                            </s-select-option>
                        </s-select>
                    </s-form-item>
                    <s-form-item s-if="{{healthCheckId}}" prop="name" label="{{'探测开关状态：'}}">
                        <s-radio-radio-group
                            class="s-doc-radio"
                            style="display:inline-block"
                            datasource="{{healthCheckAction}}"
                            value="{=formData.healthCheckAction=}"
                            radioType="radio"
                            on-change="healthActionChange"
                        >
                        </s-radio-radio-group>
                        <s-tooltip
                            s-if="{{(formData.healthCheckAction === 'open' || formData.healthCheckAction === 'pause')
                        && healthCheckId}}"
                            placement="right"
                            class="tooltip-class"
                            content="“开启”表示开启链路探测、“关闭”表示删除链路探测配置、“暂停”表示不删除链路探测配置，只是暂停链路探测。"
                        >
                            <s-icon class="warning-icon" name="faq" />
                        </s-tooltip>
                    </s-form-item>
                    <s-form-item
                        prop="dcphyId"
                        label="{{'物理专线ID：'}}"
                        s-if="{{formData.healthCheckAction!=='close'}}"
                    >
                        <s-select
                            disabled="{{dcphyDisabled || !hasSubChannel}}"
                            width="200"
                            datasource="{{dcphyDataSource}}"
                            value="{=formData.dcphyId=}"
                            on-change="selectChange"
                        >
                        </s-select>
                    </s-form-item>
                    <s-form-item
                        prop="channelId"
                        label="{{'专线通道ID：'}}"
                        s-if="{{formData.healthCheckAction!=='close'}}"
                    >
                        <s-select
                            disabled="{{channelDisabled || !hasSubChannel}}"
                            width="200"
                            datasource="{{channelDataSource}}"
                            value="{=formData.channelId=}"
                        >
                        </s-select>
                    </s-form-item>
                    <s-form-item
                        prop="PING"
                        label="探测方法"
                        class="ping-class"
                        s-if="{{formData.healthCheckAction!=='close'}}"
                    >
                        <div>PING(ICMP)</div>
                    </s-form-item>
                    <s-form-item label="{{'源IP：'}}" s-if="{{formData.healthCheckAction!=='close'}}">
                        <s-radio-radio-group
                            class="s-doc-radio"
                            datasource="{{healthCheckSourceIpType}}"
                            value="{=formData.healthCheckSourceIpType=}"
                            disabled="{{formData.healthCheckAction==='pause' || healthCheckId}}"
                            radioType="radio"
                        >
                        </s-radio-radio-group>
                    </s-form-item>
                    <s-form-item
                        class="ips-wrap"
                        s-if="{{
                    formData.healthCheckSourceIpType === 'custom' &&
                    formData.healthCheckAction!=='close' &&
                    formData.ipVersion === 4
                }}"
                    >
                        <s-input
                            disabled="{{formData.healthCheckAction==='pause' || healthCheckId}}"
                            class="{{sourceIpErrInfo[0] ? 'input-error' : ''}}"
                            value="{=formData.sourceIp[0]=}"
                            placeholder=""
                            width="{{20}}"
                            on-input="ipInput($event, 0)"
                        />
                        <span>.</span>
                        <s-input
                            disabled="{{formData.healthCheckAction==='pause' || healthCheckId}}"
                            class="{{sourceIpErrInfo[1] ? 'input-error' : ''}}"
                            value="{=formData.sourceIp[1]=}"
                            placeholder=""
                            width="{{20}}"
                            on-input="ipInput($event, 1)"
                        />
                        <span>.</span>
                        <s-input
                            disabled="{{formData.healthCheckAction==='pause' || healthCheckId}}"
                            class="{{sourceIpErrInfo[2] ? 'input-error' : ''}}"
                            value="{=formData.sourceIp[2]=}"
                            placeholder=""
                            width="{{20}}"
                            on-input="ipInput($event, 2)"
                        />
                        <span>.</span>
                        <s-input
                            disabled="{{formData.healthCheckAction==='pause' || healthCheckId}}"
                            class="{{sourceIpErrInfo[3] ? 'input-error' : ''}}"
                            value="{=formData.sourceIp[3]=}"
                            placeholder=""
                            width="{{20}}"
                            on-input="ipInput($event, 3)"
                        />
                        <span class="s-form-item-error">{{sourceIpErr}}</span>
                    </s-form-item>
                    <s-form-item
                        class="ips-wrap"
                        s-if="{{
                    formData.healthCheckSourceIpType === 'custom'
                    && formData.healthCheckAction!=='close'
                    && formData.ipVersion === 6
                }}"
                    >
                        <s-input
                            disabled="{{formData.healthCheckAction==='pause' || healthCheckId}}"
                            value="{=formData.sourceIp=}"
                            on-input="ipv6Input($event, 3)"
                        />
                        <span class="s-form-item-error">{{sourceIpv6Err}}</span>
                    </s-form-item>
                    <s-form-item
                        label="{{'目的IP：'}}"
                        class="ping-class"
                        s-if="{{formData.healthCheckAction!=='close'}}"
                    >
                        <s-radio-radio-group
                            class="s-doc-radio"
                            datasource="{{healthCheckDestIpType}}"
                            value="{=formData.healthCheckDestIpType=}"
                            disabled="{{formData.healthCheckAction==='pause' || healthCheckId}}"
                            radioType="radio"
                        >
                        </s-radio-radio-group>
                        <div s-else>绑定通道IDC互联IP</div>
                    </s-form-item>
                    <s-form-item
                        class="ips-wrap"
                        s-if="{{formData.healthCheckDestIpType === 'custom'
                    && formData.healthCheckAction!=='close'
                    && formData.ipVersion === 4
                }}"
                    >
                        <s-input
                            disabled="{{formData.healthCheckAction==='pause' || healthCheckId}}"
                            class="{{desIpErrInfo[0] ? 'input-error' : ''}}"
                            value="{=formData.desIp[0]=}"
                            placeholder=""
                            width="{{20}}"
                            on-input="desIpInput($event, 0)"
                        />
                        <span>.</span>
                        <s-input
                            disabled="{{formData.healthCheckAction==='pause' || healthCheckId}}"
                            class="{{desIpErrInfo[1] ? 'input-error' : ''}}"
                            value="{=formData.desIp[1]=}"
                            placeholder=""
                            width="{{20}}"
                            on-input="desIpInput($event, 1)"
                        />
                        <span>.</span>
                        <s-input
                            disabled="{{formData.healthCheckAction==='pause' || healthCheckId}}"
                            class="{{desIpErrInfo[2] ? 'input-error' : ''}}"
                            value="{=formData.desIp[2]=}"
                            placeholder=""
                            width="{{20}}"
                            on-input="desIpInput($event, 2)"
                        />
                        <span>.</span>
                        <s-input
                            disabled="{{formData.healthCheckAction==='pause' || healthCheckId}}"
                            class="{{desIpErrInfo[3] ? 'input-error' : ''}}"
                            value="{=formData.desIp[3]=}"
                            placeholder=""
                            width="{{20}}"
                            on-input="desIpInput($event, 3)"
                        />
                        <span class="s-form-item-error">{{desIpErr}}</span>
                    </s-form-item>
                    <s-form-item
                        class="ips-wrap"
                        s-if="{{
                    formData.healthCheckDestIpType === 'custom'
                    && formData.healthCheckAction!=='close'
                    && formData.ipVersion === 6
                }}"
                    >
                        <s-input
                            disabled="{{formData.healthCheckAction==='pause' || healthCheckId}}"
                            value="{=formData.desIp=}"
                            on-input="ipv6DestInput($event, 3)"
                        />
                        <span class="s-form-item-error">{{destIpv6Err}}</span>
                    </s-form-item>
                    <s-form-item label="健康检查间隔：" s-if="{{formData.healthCheckAction!=='close'}}">
                        <template slot="label" class="label_class">
                            {{'健康检查间隔：'}}
                            <s-tip
                                class="inline-tip"
                                placement="right"
                                content="{{'输入范围为1～60间的整数，建议设置为3秒'}}"
                                skin="question"
                            />
                        </template>
                        <s-input-number
                            disabled="{{formData.healthCheckAction==='pause'}}"
                            value="{=formData.healthCheckInterval=}"
                            step="1"
                            max="{{60}}"
                            min="{{1}}"
                        />
                    </s-form-item>
                    <s-form-item class="radio-box" label="不健康阈值：" s-if="{{formData.healthCheckAction!=='close'}}">
                        <template slot="label" class="label_class">
                            {{'不健康阈值：'}}
                            <s-tip
                                class="inline-tip"
                                placement="right"
                                content="{{'连续健康检查失败次数，超过这个阈值，专线网关所绑定专线通道将被认定为异常。'}}"
                                skin="question"
                            />
                        </template>
                        <s-radio-radio-group
                            style="display:inline-block"
                            disabled="{{formData.healthCheckAction==='pause'}}"
                            class="s-doc-radio"
                            datasource="{{unhealthThreshold}}"
                            value="{=formData.unhealthThreshold=}"
                            radioType="button"
                        >
                        </s-radio-radio-group>
                    </s-form-item>
                    <s-form-item label="健康阈值：" s-if="{{formData.healthCheckAction!=='close'}}">
                        <template slot="label" class="label_class">
                            {{'健康阈值：'}}
                            <s-tip
                                class="inline-tip"
                                placement="right"
                                content="{{'连续健康检查成功次数，超过这个阈值，专线网关所绑定专线通道将被认定为可用。'}}"
                                skin="question"
                            />
                        </template>
                        <s-radio-radio-group
                            style="display:inline-block"
                            class="s-doc-radio"
                            disabled="{{formData.healthCheckAction==='pause'}}"
                            datasource="{{healthThreshold}}"
                            value="{=formData.healthThreshold=}"
                            radioType="button"
                        >
                        </s-radio-radio-group>
                    </s-form-item>
                    <s-form-item
                        disabled="{{formData.healthCheckAction==='pause'}}"
                        s-if="{{formData.healthCheckAction!=='close' && formData.healthCheckDestIpType !== 'custom'}}"
                        class="radio-box switch-box"
                        label="自动生成路由："
                    >
                        <template slot="label" class="label_class">
                            {{'自动生成路由：'}}
                            <s-tip
                                class="inline-tip"
                                placement="right"
                                content="{{'默认开启，为用户自动生成一条路由用于连通性探测，若关闭则需要用户手动添加到IDC互联IP的路由用于连通性探测。'}}"
                                skin="question"
                            />
                        </template>
                        <s-switch
                            disabled="{{formData.healthCheckAction==='pause' || healthCheckId}}"
                            checked="{=formData.autoGenerateRouteRule=}"
                        />
                    </s-form-item>
                </s-form>
                <div slot="footer">
                    <s-button on-click="onConfirm" disabled="{{disabled || !subnetSourceList.length}}" skin="primary"
                        >{{'确定'}}</s-button
                    >
                    <s-button on-click="closeDialog">{{'取消'}}</s-button>
                </div>
            </s-dialog>
        </div>
    </template>
`;
/* eslint-enable */

@template(tpl)
@asComponent('@hc-dialog')
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class HcDialog extends Component {
    static computed = {
        sourceIpErr() {
            const sourceIpErrInfo = this.data.get('sourceIpErrInfo');
            for (let i = 0; i < sourceIpErrInfo.length; i++) {
                if (sourceIpErrInfo[i] && i < 4) {
                    return sourceIpErrInfo[i];
                }
            }
        },
        desIpErr() {
            const desIpErrInfo = this.data.get('desIpErrInfo');
            for (let i = 0; i < desIpErrInfo.length; i++) {
                if (desIpErrInfo[i] && i < 4) {
                    return desIpErrInfo[i];
                }
            }
        },
        subnetSourceList() {
            let subnetSource = this.data.get('subnetSource');
            if (this.data.get('formData.ipVersion') === 6) {
                let datasource = [];
                u.each(subnetSource, item => {
                    if (item.ipv6Cidr) {
                        let text = `${item.name} ${item.ipv6Cidr ? '(' + item.ipv6Cidr + ')' : ''}`;
                        datasource.push({
                            value: item.shortId,
                            text: text,
                            cidr: item.cidr,
                            ipv6Cidr: item.ipv6Cidr
                        });
                    }
                });
                return datasource;
            } else {
                let datasource = [];
                u.each(subnetSource, item => {
                    let text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}`;
                    datasource.push({
                        value: item.shortId,
                        text: text,
                        cidr: item.cidr,
                        ipv6Cidr: item.ipv6Cidr
                    });
                });
                return datasource;
            }
        }
    };
    initData() {
        const threshold = [
            {
                label: '2',
                value: 2
            },
            {
                label: '3',
                value: 3
            },
            {
                label: '4',
                value: 4
            },
            {
                label: '5',
                value: 5
            }
        ];
        return {
            disabled: false,
            healthCheckAction: [
                {label: '开启', value: 'open'},
                {label: '暂停', value: 'pause'},
                {label: '关闭', value: 'close'}
            ],
            healthCheckSourceIpType: [
                {label: '自动分配', value: 'auto'},
                {label: '自定义', value: 'custom'}
            ],
            healthCheckDestIpType: [
                {label: '绑定通道IDC互联IP', value: 'auto'},
                {label: '自定义', value: 'custom'}
            ],
            healthIpversion: [{label: 'IPv4', value: 4}],
            unhealthThreshold: threshold,
            healthThreshold: threshold,
            sourceIpErrInfo: [],
            desIpErrInfo: [],
            rules: {
                channelId: [{required: true, message: '请选择专线通道ID'}],
                dcphyId: [{required: true, message: '请选择物理专线ID'}],
                subnetId: [{required: true, message: '请选择所在子网'}]
            },
            dcphyDataSource: [],
            channelDataSource: [],
            dcphyDisabled: false,
            channelDisabled: false
        };
    }
    inited() {
        this.initFormData();
    }
    initFormData() {
        let formData = {
            ipVersion: 4,
            healthThreshold: 3,
            unhealthThreshold: 3,
            healthCheckInterval: 3,
            healthCheckSourceIpType: 'auto',
            healthCheckDestIpType: 'auto',
            autoGenerateRouteRule: true,
            sourceIp: [],
            desIp: []
        };
        for (let i = 0; i < 4; i++) {
            formData.sourceIp.push('');
            formData.desIp.push('');
        }
        if (!this.data.get('healthCheckId')) {
            let healthCheckAction = u.cloneDeep(this.data.get('healthCheckAction'));
            this.data.set('healthCheckAction', healthCheckAction.splice(0, 1));
            formData.healthCheckAction = 'open';
            this.data.set('formData', formData);
        } else {
            let healthCheckId = this.data.get('healthCheckId');
            this.$http.healthCheckDetail(healthCheckId).then(data => {
                data.healthCheckSourceIpType = 'custom';
                data.autoGenerateRouteRule = true;
                if (data.ipVersion === 4) {
                    data.sourceIp = data.healthCheckSourceIp.split('.');
                } else {
                    data.sourceIp = data.healthCheckSourceIp;
                }
                data.desIp = data.healthCheckDestIp.split('.');
                data.healthCheckDestIpType = 'custom';
                if (data.status === 'init_failed') {
                    this.data.splice('healthCheckAction', [1, 1]);
                }
                this.data.set('formData', data);
                this.data.set('dcphyDisabled', true);
                this.data.set('channelDisabled', true);
            });
        }
        this.getDetail().then(() => {
            let instance = this.data.get('instance');
            let dephyDataSource;
            if (instance.associatedChannels) {
                instance.associatedChannels.push({
                    dcphy_id: instance.dcphyId,
                    channel_id: instance.channelId
                });
                dephyDataSource = u.groupBy(instance.associatedChannels, 'dcphy_id');
            } else {
                let arr = [];
                arr.push({
                    dcphy_id: instance.dcphyId,
                    channel_id: instance.channelId
                });
                dephyDataSource = u.groupBy(arr, 'dcphy_id');
            }
            let dcphyDataSource = [];
            let healthDatasource = this.data.get('healthDatasource').map(item => item.dcphyId);
            for (let i in dephyDataSource) {
                if (healthDatasource.includes(i)) {
                    dcphyDataSource.push({
                        text: i,
                        value: i,
                        disabled: true
                    });
                } else {
                    dcphyDataSource.push({
                        text: i,
                        value: i,
                        disabled: false
                    });
                }
            }
            this.data.set('dcphyDataSource', dcphyDataSource);
            this.data.set('dephyDataSource', dephyDataSource);
            this.getChannelDetail().then(() => {
                if (this.data.get('channelOpenIpv6')) {
                    this.data.push('healthIpversion', {
                        label: 'IPv6',
                        value: 6
                    });
                }
            });
            this.getSubnetSource().then(() => {
                if (this.data.get('healthCheckId')) {
                    // 编辑时初始子网校验cidr
                    this.nextTick(() => {
                        this.subnetChange({value: this.data.get('formData.subnetId')});
                    });
                }
            });
        });
    }
    validataEmpty() {
        const {sourceIp} = this.data.get('formData');
        sourceIp.map((value, index) => {
            this.ipInput({value}, index);
        });
    }
    desIpValidataEmpty() {
        const {desIp} = this.data.get('formData');
        desIp.map((value, index) => {
            this.desIpInput({value}, index);
        });
    }
    async onConfirm() {
        const form = this.ref('form');
        const formData = u.cloneDeep(this.data.get('formData'));
        if (!this.data.get('healthCheckId')) {
            await form.validateFields();
        }
        if (
            formData.healthCheckSourceIpType === 'custom' &&
            formData.ipVersion === 4 &&
            formData.healthCheckAction !== 'close'
        ) {
            this.validataEmpty();
        }
        if (
            formData.healthCheckDestIpType === 'custom' &&
            formData.ipVersion === 4 &&
            formData.healthCheckAction !== 'close'
        ) {
            this.desIpValidataEmpty();
        }
        if (formData.ipVersion === 4 && this.data.get('desIpErr') && formData.healthCheckDestIpType === 'custom') {
            return;
        }
        if (formData.ipVersion === 4 && this.data.get('sourceIpErr') && formData.healthCheckSourceIpType === 'custom') {
            return;
        }
        if (
            formData.ipVersion === 6 &&
            this.data.get('sourceIpv6Err') &&
            formData.healthCheckSourceIpType === 'custom'
        ) {
            return;
        }
        if (formData.ipVersion === 6 && this.data.get('destIpv6Err') && formData.healthCheckDestIpType === 'custom') {
            return;
        }
        let payload = {
            ipVersion: formData.ipVersion,
            healthCheckAction: formData.healthCheckAction,
            dcphyId: formData.dcphyId,
            channelId: formData.channelId,
            healthCheckSourceIp: '',
            healthCheckDestIp: '',
            healthCheckInterval: formData.healthCheckInterval,
            healthThreshold: formData.healthThreshold,
            unhealthThreshold: formData.unhealthThreshold,
            autoGenerateRouteRule: formData.autoGenerateRouteRule,
            subnetId: formData.subnetId
        };
        if (formData.healthCheckSourceIpType === 'custom' && formData.ipVersion === 4) {
            payload.healthCheckSourceIp = formData.sourceIp.join('.');
        }
        if (formData.healthCheckDestIpType === 'custom' && formData.ipVersion === 4) {
            payload.healthCheckDestIp = formData.desIp.join('.');
        }
        if (formData.healthCheckSourceIpType === 'custom' && formData.ipVersion === 6) {
            payload.healthCheckSourceIp = formData.sourceIp;
        }
        if (formData.healthCheckDestIpType === 'custom' && formData.ipVersion === 6) {
            payload.healthCheckDestIp = formData.desIp;
        }
        this.data.set('disabled', true);
        if (!this.data.get('healthCheckId')) {
            payload.dcgwId = this.data.get('dcgwId');
            this.$http
                .createHealthCheck(payload)
                .then(() => {
                    this.fire('onConfirm');
                    this.closeDialog();
                    this.data.set('disabled', false);
                })
                .catch(() => this.data.set('disabled', false));
        } else {
            let healthCheckId = this.data.get('healthCheckId');
            delete payload.dcphyId;
            delete payload.channelId;
            delete payload.autoGenerateRouteRule;
            this.$http
                .editHealthCheck(healthCheckId, payload)
                .then(() => {
                    this.fire('onConfirm');
                    this.closeDialog();
                    this.data.set('disabled', false);
                })
                .catch(() => this.data.set('disabled', false));
        }
    }
    closeDialog() {
        this.dispose();
    }
    ipInput({value}, key) {
        const CONFIG_MAP = {
            0: rule.CHANNEL.IP_M,
            1: rule.CHANNEL.IP_M,
            2: rule.CHANNEL.IP_M,
            3: rule.CHANNEL.IP_M
        };

        if (value === '') {
            this.data.set(`sourceIpErrInfo[${key}]`, CONFIG_MAP[key].requiredErrorMessage);
            return;
        }
        if (!CONFIG_MAP[key].custom(value)) {
            this.data.set(`sourceIpErrInfo[${key}]`, CONFIG_MAP[key].customErrorMessage);
            return;
        }

        let ipArr = this.data.get('formData.sourceIp');
        ipArr[key] = value;
        let ips = ipArr.join('.');
        if (RULE.IP.test(ips)) {
            if (!checkIsInSubnet(ips + '/32', this.data.get('subnetCidr'))) {
                this.data.set(`sourceIpErrInfo[${key}]`, 'IP地址不属于所在子网，请重新输入');
                return;
            } else {
                this.data.set('sourceIpErrInfo', []);
            }
        }

        this.data.set(`sourceIpErrInfo[${key}]`, '');
    }
    ipv6Input({value}) {
        let err = '';
        if (!value) {
            err = '请输入';
        } else if (!RULE.IPV6.test(value)) {
            err = '请输入合法的IPv6地址';
        } else if (!checkIsInSubnet(value + (/\//g.test(value) ? '' : '/128'), this.data.get('subnetIpv6Cidr'))) {
            err = '您输入的IPv6地址不在探测子网内，请重新输入';
        } else if (this.data.get('subnetIpv6Cidr') === value) {
            err = 'ipv6地址和子网重复，请重新输入';
        }
        this.data.set('sourceIpv6Err', err);
    }
    ipv6DestInput({value}) {
        let err = '';
        if (!value) {
            err = '请输入';
        } else if (!RULE.IPV6.test(value)) {
            err = '请输入合法的IPv6地址';
        }
        this.data.set('destIpv6Err', err);
    }
    desIpInput({value}, key) {
        const CONFIG_MAP = {
            0: rule.CHANNEL.IP_M,
            1: rule.CHANNEL.IP_M,
            2: rule.CHANNEL.IP_M,
            3: rule.CHANNEL.IP_M
        };

        if (value === '') {
            this.data.set(`desIpErrInfo[${key}]`, CONFIG_MAP[key].requiredErrorMessage);
            return;
        }
        if (!CONFIG_MAP[key].custom(value)) {
            this.data.set(`desIpErrInfo[${key}]`, CONFIG_MAP[key].customErrorMessage);
            return;
        }
        this.data.set(`desIpErrInfo[${key}]`, '');
    }
    selectChange(e) {
        this.data.set('formData.channelId', '');
        this.data.set('formData.dcphyId', e.value);
        let dephyDataSource = this.data.get('dephyDataSource');
        let channelDataSource = [];
        for (let i in dephyDataSource) {
            if (i === e.value) {
                dephyDataSource[i].forEach(item => {
                    channelDataSource.push({
                        text: item.channel_id,
                        value: item.channel_id
                    });
                });
            }
        }
        this.data.set('channelDataSource', channelDataSource);
    }
    healthIpversionChange(e) {
        this.data.set('formData.subnetId', '');
        if (e.value === 6) {
            this.data.set('formData.sourceIp', '');
            this.data.set('formData.desIp', '');
            this.data.set('sourceIpErrInfo', []);
            this.data.set('desIpErrInfo', []);
        } else {
            this.data.set('formData.sourceIp', []);
            this.data.set('formData.desIp', []);
            this.data.set('sourceIpv6Err', '');
            this.data.set('destIpv6Err', '');
        }
    }
    subnetChange(e) {
        let subnetSourceList = this.data.get('subnetSourceList');
        let index = subnetSourceList.findIndex(item => item.value === e.value);
        if (this.data.get('formData.ipVersion') === 6) {
            let ipv6Cidr = subnetSourceList[index].ipv6Cidr;
            this.data.set('subnetIpv6Cidr', ipv6Cidr);
        } else {
            let cidr = subnetSourceList[index].cidr;
            this.data.set('subnetCidr', cidr);
        }
    }
    getDetail() {
        return this.$http
            .dcgwDetail({
                dcgwId: this.data.get('dcgwId')
            })
            .then(res => {
                this.data.set('instance', res);
            });
    }
    getChannelDetail() {
        const channnelId = this.data.get('instance').channelId;
        return this.$http.channelDetail(channnelId).then(res => {
            this.data.set('channelOpenIpv6', res.enableIpv6);
            this.data.set('hasSubChannel', !!res.subChannel?.length);
            if (!this.data.get('hasSubChannel')) {
                this.selectChange({
                    value: this.data.get('dcphyDataSource')[0].value
                });
                this.data.set('formData.channelId', this.data.get('channelDataSource')[0].value);
            }
        });
    }
    getSubnetSource() {
        const vpcId = this.data.get('instance').vpcId;
        return this.$http
            .getSubnetList({
                vpcId: vpcId,
                attachVm: false
            })
            .then(data => {
                this.data.set('subnetSource', data);
            });
    }
}
export default Processor.autowireUnCheckCmpt(HcDialog);
