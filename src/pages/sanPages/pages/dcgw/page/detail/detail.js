/*
 * @description: 创建专线网关
 * @file: network/dcgw/pages/create.js
 * @author: p<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */
import {Component} from 'san';
import {San2React} from '@baidu/bce-react-toolkit';
import {OutlinedEditingSquare} from '@baidu/sui-icon';
import {html, decorators, Processor} from '@baiducloud/runtime';

import {toTime} from '@/pages/sanPages/utils/helper';
import {getVpcName} from '@/pages/sanPages/utils/common';
import InstanceEdit from './instanceEdit';
import {DcGatewayStatus, HealthCheckAction} from '@/pages/sanPages/common/enum';
import '../../style/detail.less';

/* eslint-disable */
const {asComponent, invokeSUI, invokeSUIBIZ, template, invokeComp} = decorators;
const tpl = html`
    <div>
        <div class="{{klass}}">
            <div class="content-box">
                <h4>基本信息</h4>
                <div class="cell">
                    <div class="cell-title">所在网络：</div>
                    <div class="cell-content">{{vpcInfo.name}}<span>&nbsp;&nbsp;（{{vpcInfo.shortId}}）</span></div>
                </div>
                <div class="cell">
                    <div class="cell-title">网关名称：</div>
                    <div class="cell-content" ref="name">
                        {{instance.name}}
                        <outlined-editing-square class="name-icon" on-click="edit('name')" />
                    </div>
                </div>
                <div class="cell">
                    <div class="cell-title">状态：</div>
                    <div class="cell-content">
                        <span class="{{instance.statusClass}}">{{instance.statusText}}</span>
                    </div>
                </div>
                <div class="cell">
                    <div class="cell-title">出口带宽：</div>
                    <div class="cell-content">
                        {{instance.speed}}Mbps
                        <outlined-editing-square class="name-icon" on-click="edit('speed')" s-if="maxSpeedQuota" />
                    </div>
                </div>
                <div class="cell">
                    <div class="cell-title">绑定物理专线：</div>
                    <div class="cell-content">{{instance.dcphyId || '-'}}</div>
                </div>
                <div class="cell">
                    <div class="cell-title">绑定通道ID：</div>
                    <div class="cell-content">{{instance.channelId || '-'}}</div>
                </div>
                <div class="cell">
                    <div class="cell-title">创建时间：</div>
                    <div class="cell-content">{{instance.createTime}}</div>
                </div>
                <div class="cell">
                    <div class="cell-title">描述：</div>
                    <div class="cell-content">
                        {{instance.description || '-'}}
                        <outlined-editing-square class="name-icon" on-click="edit('desc')" />
                    </div>
                </div>
            </div>
        </div>
    </div>
`;
/* eslint-enable */

@asComponent('@dcgw-detail')
@invokeComp('@dcgw-instance-edit')
@template(tpl)
@invokeSUI
@invokeSUIBIZ
class Detail extends Component {
    static components = {
        'outlined-editing-square': OutlinedEditingSquare
    };
    initData() {
        return {
            klass: ['dcgw-detail-v2'],
            instance: {},
            unset: '未配置',
            editIns: null
        };
    }

    edit(type) {
        const editIns = this.data.get('editIns');
        editIns?.dispose?.();
        let edit = new InstanceEdit({
            data: {
                open: true,
                type,
                dcgwId: this.data.get('context').dcgwId,
                instance: this.data.get('instance'),
                maxSpeed: this.data.get('maxSpeed') || 100000
            }
        });
        this.data.set('editIns', edit);
        edit.on('success', () => {
            this.getDetail();
            if (type === 'name') {
                this.data.get('context').getDetail();
            }
        });
        edit.attach(this.el);
    }

    getDetail(option = {}) {
        return this.$http
            .dcgwDetail(
                {
                    dcgwId: this.data.get('context').dcgwId
                },
                option
            )
            .then(instance => {
                let config = DcGatewayStatus.fromValue(instance.status);
                instance.statusClass = config.styleClass;
                instance.statusText = config.text;
                instance.createTime = toTime(instance.createTime);
                if (instance.subnets && instance.subnets.length > 0) {
                    instance.subnets = instance.subnets.join(', ');
                } else {
                    instance.subnets = '未配置';
                }
                if (instance.ipv6Subnets && instance.ipv6Subnets.length > 0) {
                    instance.ipv6Subnets = instance.ipv6Subnets.join(', ');
                } else {
                    instance.ipv6Subnets = '未配置';
                }
                instance.healthCheckActionText = HealthCheckAction.getTextFromValue(instance.healthCheckAction);
                this.data.set('instance', instance);
            });
    }

    getVpcInfo() {
        let vpcId = this.data.get('context').vpcId || '';
        this.$http.vpcInfo({vpcIds: [vpcId]}).then(data => {
            let vpc = data[vpcId] || {};
            vpc.name = getVpcName(vpc.name);
            this.data.set('vpcInfo', vpc);
        });
    }

    inited() {
        this.data.set('maxSpeedQuota', false);
        this.$http
            .dcgwSpeedQuota()
            .then(result => {
                this.data.set('maxSpeed', result.total);
            })
            .finally(() => this.data.set('maxSpeedQuota', true));
    }

    attached() {
        this.getVpcInfo();
        this.getDetail({'x-silent-codes': ['NoSuchObject']});
    }
    disposed() {
        this.data.set('editIns', null);
    }
}

export default San2React(Processor.autowireUnCheckCmpt(Detail));
