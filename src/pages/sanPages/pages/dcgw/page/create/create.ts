/*
 * @description: 创建专线网关
 * @file: network/dcgw/pages/create.js
 * @author: p<PERSON><PERSON><PERSON><EMAIL>
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, CreatePage, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {Notification, Alert} from '@baidu/sui';
import {ResourceGroupPanel} from '@baidu/bce-resourcegroup-sdk-san';
import {ResourceGroupSDK} from '@baidu/bce-resourcegroup-sdk';
import {TagEditPanel} from '@baidu/bce-tag-sdk-san';

import rule from '@/pages/sanPages/utils/rule';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import {StsConfig} from '@/pages/sanPages/utils/config';
import {parseQuery} from '@/utils';
import {convertCidrToBinary, checkIsInSubnet} from '@/pages/sanPages/utils/common';
import testID from '@/testId';
import '../../style/create.less';

let subnetRule = [
    {required: true, message: '不能为空'},
    {pattern: new RegExp(rule.IP_CIDR), message: 'IP范围不符合规则'},
    {
        validator(rule, value, callback) {
            let valueString = convertCidrToBinary(value);
            let valueMask = value.split('/')[1] || (value.split('/')[0] === '0.0.0.0' ? 0 : 32);
            if (valueString.substring(+valueMask, valueString.length).includes('1')) {
                return callback('CIDR格式不合法');
            }
            return callback();
        }
    }
];
/* eslint-disable */
const {invokeSUI, invokeSUIBIZ, template, invokeComp} = decorators;
const tpl = html`
    <template>
        <s-app-create-page
            class="{{klass}}"
            backTo="{{pageNav.backUrl}}"
            backToLabel="{{pageNav.backLabel}}"
            pageTitle="{{pageNav.title}}"
        >
            <s-form
                s-ref="form"
                data="{=formData=}"
                rules="{{rules}}"
                label-col="{{labelCol}}"
                wrapper-col="{{wrapperCol}}"
                label-align="left"
            >
                <div class="content-box form-part-wrap">
                    <h4>基本配置</h4>
                    <s-form-item prop="vpcId" label="所在私有网络：">
                        <s-select
                            width="{{220}}"
                            disabled="{{selectVpcDisable}}"
                            value="{=formData.vpcId=}"
                            on-change="vpcChange($event)"
                        >
                            <s-select-option s-for="item in vpcList" value="{{item.value}}" label="{{item.text}}">
                                <s-tooltip>
                                    <div slot="content">{{item.text}}</div>
                                    <div>{{item.text}}</div>
                                </s-tooltip>
                            </s-select-option>
                        </s-select>
                    </s-form-item>
                    <s-form-item prop="name" label="网关名称：">
                        <s-input
                            value="{=formData.name=}"
                            placeholder="请输入名称"
                            width="{{220}}"
                            data-test-id="${testID.dcgw.createNameInput}"
                        ></s-input>
                    </s-form-item>
                    <s-form-item prop="speed" label="出口带宽：">
                        <s-input-number
                            width="{{220}}"
                            value="{=formData.speed=}"
                            step="{{1}}"
                            max="{{maxSpeed}}"
                            min="{{2}}"
                        />
                        <span class="speed-unit">Mbps</span>
                    </s-form-item>
                    <s-form-item prop="dcphyIdType" class="radio-box" label="绑定物理专线：">
                        <s-radio-radio-group
                            class="s-doc-radio"
                            disabled="{{dcphyIdTypeDisable}}"
                            data-test-id="${testID.dcgw.dcCheckRadio}"
                            datasource="{{dcphyIdType}}"
                            value="{=formData.dcphyIdType=}"
                            on-change="dcphyIdTypeChange($event)"
                            radioType="radio"
                        >
                        </s-radio-radio-group>
                    </s-form-item>
                    <div class="dcphy-box">
                        <s-form-item s-if="formData.dcphyIdType==='bind'" prop="dcphyId" class="dcphy">
                            <s-select
                                disabled="{{dcInputDisable}}"
                                value="{=formData.dcphyId=}"
                                datasource="{{dcphyList}}"
                                on-change="dcphyChange($event)"
                                width="{{220}}"
                                filterable
                                placeholder="请选择物理专线ID"
                            ></s-select>
                        </s-form-item>
                        <s-form-item s-if="formData.dcphyIdType==='bind'" prop="channelId" class="channel">
                            <s-select
                                disabled="{{dcInputDisable}}"
                                value="{=formData.channelId=}"
                                datasource="{{channelList}}"
                                width="{{220}}"
                                filterable
                                placeholder="请选择专线通道ID"
                            ></s-select>
                        </s-form-item>
                    </div>
                    <s-form-item label="描述：">
                        <s-input-text-area
                            maxLength="200"
                            width="220"
                            height="60"
                            value="{=formData.description=}"
                            placeholder="请输入"
                        ></s-input-text-area>
                    </s-form-item>
                </div>
                <div class="resource-form-part-wrap form-part-wrap" s-if="!selectVpcDisable">
                    <h4>标签</h4>
                    <s-form-item prop="tag" label="绑定标签：">
                        <tag-edit-panel
                            create
                            version="v2"
                            instances="{{defaultInstances}}"
                            options="{{tagListRequster}}"
                            s-ref="tagPanel"
                        />
                    </s-form-item>
                </div>
                <div class="resource-form-part-wrap form-part-wrap" s-if="!selectVpcDisable">
                    <resource-group-panel
                        refreshAvailable="{{true}}"
                        sdk="{{resourceSDK}}"
                        on-change="resourceChange($event)"
                    />
                </div>
            </s-form>
            <div slot="pageFooter" class="buybucket">
                <div class="buybucket-container">
                    <s-tooltip trigger="{{vpcErr ? 'hover' : ''}}">
                        <!--bca-disable-next-line-->
                        <div slot="content">{{vpcErr | raw}}</div>
                        <s-button
                            disabled="{{vpcErr || confirmed}}"
                            skin="primary"
                            data-test-id="${testID.dcgw.createSubmit}"
                            on-click="onCreate"
                            size="large"
                            track-id="vpc_dcgw_confirm_create"
                        >
                            确定
                        </s-button>
                    </s-tooltip>
                    <s-button size="large" on-click="cancel">取消</s-button>
                </div>
            </div>
        </s-app-create-page>
    </template>
`;
/* eslint-enable */

@template(tpl)
@invokeComp()
@invokeSUI
@invokeSUIBIZ
class DcgwCreate extends CreatePage {
    REGION_CHANGE_LOCATION = '#/vpc/dcgw/list';
    static components = {
        'resource-group-panel': ResourceGroupPanel,
        'tag-edit-panel': TagEditPanel
    };
    initData() {
        const threshold = [
            {
                label: '2',
                value: 2
            },
            {
                label: '3',
                value: 3
            },
            {
                label: '4',
                value: 4
            },
            {
                label: '5',
                value: 5
            }
        ];
        return {
            klass: ['dcgw-create-v2'],
            open: false,
            labelCol: {span: 4},
            wrapperCol: {span: 19},
            resourceIds: {},
            vpcList: [],
            subnetErr: [],
            maxSpeed: 100000,
            pageNav: {
                title: '创建专线网关',
                backUrl: '/network/#/vpc/dcgw/list',
                backLabel: '返回'
            },
            dcphyIdType: [
                {label: '绑定物理专线', value: 'bind'},
                {label: '暂不绑定', value: 'unbind'}
            ],
            dcphyList: [], // 物理专线
            channelList: [], // 专线通道
            subnetsType: [
                {label: '本VPC网段', value: 'vpc'},
                {label: '自定义', value: 'custom'}
            ],
            healthCheckActionDisable: false,
            healthCheckSourceIpType: [
                {label: '自动分配', value: 'auto'},
                {label: '自定义', value: 'custom'}
            ],
            unhealthThreshold: threshold,
            healthThreshold: threshold,
            vpcInfo: null,
            subnets: ['subnet0'],
            dcphyIdTypeDisable: false,
            dcInputDisable: false,
            selectVpcDisable: false,
            formData: {
                name: '',
                vpcId: '',
                description: '',
                speed: 2,
                dcphyId: '',
                channelId: '',
                dcphyIdType: 'bind',
                subnetsType: 'vpc',
                subnet0: '',
                healthCheckAction: 'close',
                healthThreshold: 3,
                unhealthThreshold: 3,
                healthCheckInterval: 3,
                autoGenerateRouteRule: true,
                healthCheckSourceIpType: 'auto',
                healthCheckSourceIp: '',
                healthCheckDestIp: '',
                ipv6Type: 'vpc'
            },
            rules: {
                name: [
                    {required: true, message: '名称必填'},
                    {
                        pattern: /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/,
                        message: '支持大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65'
                    }
                ],
                speed: [
                    {required: true, message: '不能为空'},
                    {pattern: /^[0-9]*$/, message: '请填写数字'}
                ],
                dcphyId: [
                    {required: true, message: '不能为空'},
                    {pattern: /^dcphy-[\w\-\/\.]{12}$/, message: '物理专线ID不符合规则'}
                ],
                channelId: [
                    {required: true, message: '不能为空'},
                    {pattern: /^dedicatedconn-[\w\-\/\.]{12}$/, message: '专线通道ID不符合规则'}
                ],
                healthCheckSourceIp: [{required: true, message: '不能为空'}],
                subnet0: subnetRule
            },
            healthCheckAction: [
                {label: '开启', value: 'open'},
                {label: '关闭', value: 'close'}
            ],
            hideAutoGenerateRouteRule: false,
            healthCheckId: '',
            vpcErr: '',
            flag: 1,
            dcgDetail: {},
            resourceSDK: new ResourceGroupSDK(window.$http, window.$context),
            defaultInstances: [
                {
                    tags: [
                        {
                            tagKey: '默认项目',
                            tagValue: ''
                        }
                    ]
                }
            ],
            tagListRequster: this.tagListRequster.bind(this),
            urlQuery: parseQuery(location.hash),
            allPhyList: []
        };
    }

    dcphyIdTypeChange(e: any) {
        if (e.value === 'bind') {
            this.data.set('healthCheckActionDisable', false);
        } else {
            this.data.set('healthCheckActionDisable', true);
            this.data.set('formData.healthCheckAction', 'close');
        }
    }
    vpcChange(e: any) {
        this.getQuota(e.value);
        u.each(this.data.get('vpcList'), item => {
            if (e.value === item.value) {
                this.data.set('vpcInfo', item.vpcInfo);
            }
        });
    }

    getQuota(vpcId: string) {
        this.$http
            .getDcgwQuota(
                {
                    vpcId
                },
                {'X-silence': true}
            )
            .then((quota: {free: number}) => {
                if (!this.data.get('urlQuery.dcgwId') && (quota.quota.free <= 0 || quota.quotaPerVpc.free <= 0)) {
                    if (FLAG.NetworkSupportXS) {
                        this.data.set('vpcErr', '该网络配额不足');
                    } else {
                        this.data.set(
                            'vpcErr',
                            `该网络配额不足，<a href="/quota_center/#/quota/apply/create?serviceType=DCGW` +
                                `&region=${window.$context.getCurrentRegionId()}` +
                                `&cloudCenterQuotaName=dcgwQuota" target="_blank">去申请配额</a>`
                        );
                    }
                } else {
                    this.data.set('vpcErr', '');
                }
            });
    }

    async isEdit() {
        if (this.data.get('urlQuery.dcgwId')) {
            this.data.set('isEdit', true);
            this.data.set('dcphyIdTypeDisable', true);
            this.data.set('selectVpcDisable', true);
            let data = await this.$http.dcgwDetail({dcgwId: this.data.get('urlQuery.dcgwId')});
            this.data.set('dcgDetail', data);
            if (!data.dcphyId) {
                this.data.set('formData.dcphyIdType', 'unbind');
            } else {
                this.data.set('formData.dcphyIdType', 'bind');
                this.data.set('dcInputDisable', true);
            }
            this.data.set('autoCreateRouter', data.autoCreateRouter);
            this.data.set('healthCheckId', data.healthCheckId);
            let vpcInfo = this.data.get('vpcInfo');
            let vpcSubnets = [vpcInfo.cidr];
            let subnets = null;
            if (vpcInfo.auxiliaryCidr) {
                vpcSubnets = [vpcInfo.cidr, ...vpcInfo.auxiliaryCidr];
            }
            if (data && data.subnets && !u.isEqual(data.subnets, vpcSubnets)) {
                this.data.set('formData.subnetsType', 'custom');
                subnets = [...data.subnets];
                delete data.subnets;
            } else {
                subnets = vpcSubnets;
            }
            if (data.healthCheckAction === 'open' || data.healthCheckAction === 'pause') {
                this.data.set('healthCheckAction', [
                    {label: '开启', value: 'open'},
                    {label: '暂停', value: 'pause'},
                    {label: '关闭', value: 'close'}
                ]);
                this.data.set('hideAutoGenerateRouteRule', true);
            }
            if (!data.dcphyId) {
                this.data.set('healthCheckActionDisable', true);
            }
            subnets && this.data.set('formData.subnet0', subnets.shift());
            subnets &&
                subnets.forEach((item: any, index: number) => {
                    let flag = this.data.get('flag');
                    let str = 'subnet' + flag;
                    this.data.push('subnets', str);
                    this.data.set(`rules['${str}']`, subnetRule);
                    this.data.set(`formData.subnet${flag}`, item);
                    flag++;
                    this.data.set('flag', flag);
                });
            u.each(Object.keys(this.data.get('formData')), key => {
                if (data[key]) {
                    this.data.set(`formData['${key}']`, data[key]);
                }
            });
            // 如果之前存在ecmp路由，则无法关闭ipv6
            this.$http
                .rulePageList({
                    pageNo: 1,
                    pageSize: 1000,
                    vpcId: this.data.get('urlQuery.vpcId'),
                    subKeywordType: 'nexthopType',
                    subKeyword: 'dcGateway'
                })
                .then((res: any) => {
                    let nexthopIdList = res.routeRules.reduce((total: any, cur: any) => {
                        if (cur.pathType === 'ecmp' && cur.ipVersion == 6) {
                            // 当前条目的所有的下一跳
                            let allNexthopId = cur.multiRouteRuleVos
                                .map(route => route.nexthopId)
                                .concat([cur.nexthopId]);
                            return total.concat(allNexthopId);
                        }
                        return total;
                    }, []);
                });
        }
    }

    getVpcList() {
        return this.$http.vpcList().then((data: any) => {
            let list = u.map(data, item => {
                return {
                    text: `${item.name}（${item.cidr}）`,
                    value: item.vpcId,
                    vpcInfo: item
                };
            });
            this.data.set('vpcList', list);
            let vpcId = this.data.get('urlQuery.vpcId');
            if (vpcId) {
                list.forEach(item => {
                    if (item.value === vpcId) {
                        this.data.set('formData.vpcId', vpcId);
                        this.data.set('vpcInfo', item.vpcInfo);
                    }
                });
            } else {
                this.data.set('formData.vpcId', list[0].value);
                this.data.set('vpcInfo', list[0].vpcInfo);
            }
            this.getQuota(this.data.get('formData.vpcId'));
            return list;
        });
    }

    getData(tags: any) {
        let payload = {
            ...this.data.get('formData'),
            tags: tags
        };
        if (payload.subnetsType === 'vpc') {
            let vpcInfo = this.data.get('vpcInfo');
            if (vpcInfo.auxiliaryCidr) {
                payload.subnets = [vpcInfo.cidr, ...vpcInfo.auxiliaryCidr];
            } else {
                payload.subnets = [vpcInfo.cidr];
            }
            u.each(this.data.get('subnets'), item => {
                delete payload[item];
            });
        } else {
            payload.subnets = [];
            u.each(this.data.get('subnets'), item => {
                payload.subnets.push(payload[item]);
                delete payload[item];
            });
        }
        payload.speed = +payload.speed;
        u.isString(payload.subnets) && (payload.subnets = [payload.subnets]);
        let dcphyIdType = payload.dcphyIdType;
        if (dcphyIdType === 'unbind') {
            delete payload.subnets;
            delete payload.dcphyId;
            delete payload.channelId;
        }
        if (payload.healthCheckSourceIpType === 'auto') {
            delete payload.healthCheckSourceIp;
        }
        delete payload.healthCheckSourceIpType;
        delete payload.subnetsType;
        delete payload.dcphyIdType;
        if (!this.data.get('urlQuery.dcgwId')) {
            return {
                ...payload,
                vpcShortId: this.data.get('vpcInfo').shortId,
                vpcId: this.data.get('formData.vpcId')
            };
        }
        if (this.data.get('hideAutoGenerateRouteRule')) {
            delete payload.autoGenerateRouteRule;
            payload.autoCreateRouter = this.data.get('autoCreateRouter');
        }
        delete payload.ipv6Type;
        return {
            ...payload,
            dcgwId: this.data.get('urlQuery.dcgwId'),
            healthCheckId: this.data.get('healthCheckId')
        };
    }

    checkSubnet(payload: any) {
        let index = null;
        if (this.data.get('formData.dcphyIdType') === 'unbind') {
            return true;
        }
        for (let i = 0; i < payload.subnets.length; i++) {
            const subnet = payload.subnets[i];
            for (let j = i + 1; j < payload.subnets.length; j++) {
                const item = payload.subnets[j];
                if (checkIsInSubnet(subnet, item) || checkIsInSubnet(item, subnet)) {
                    index = j;
                    break;
                }
            }
        }
        if (index !== null) {
            this.data.set(`subnetErr[${index}]`, '云端网络配置冲突');
            return false;
        }
        return true;
    }

    /**
     * 专线网关绑定时的校验,专线通道必须处于可用状态,未绑定专线网关,专线通道底下没有子通道，它也不是其他通道的子通道
     * @param {string} channelId
     */
    bindChannelCheck(channelId: string) {
        return new Promise((resolve, reject) => {
            // 跨账号授权时会获取不到通道详情，绑定和解绑时需要加上type:available参数
            this.$http
                .channelDetail(channelId, {
                    type: 'available'
                })
                .then((res: any) => {
                    const {status, dcgwId, mainChannel, subChannel} = res;
                    if (status !== 'established' || dcgwId || mainChannel || subChannel?.length) {
                        reject();
                    } else {
                        resolve();
                    }
                });
        });
    }

    async onCreate() {
        if (this.data.get('vpcErr')) {
            return;
        }
        try {
            if (this.data.get('selectVpcDisable')) {
                await this.ref('tagPanel')?.validate(false);
            }
        } catch (error) {
            return;
        }
        await this.ref('form').validateFields();
        let tags = await this.ref('tagPanel')?.getTags();
        if (this.checkSubnet(this.getData(tags))) {
            let submitRequester = null;
            if (!this.data.get('urlQuery.dcgwId')) {
                if (this.data.get('formData.dcphyIdType') === 'bind') {
                    try {
                        await this.bindChannelCheck(this.data.get('formData.channelId'));
                    } catch (err) {
                        Notification.warning('该专线通道不可用');
                    }
                }
                submitRequester = this.$http.dcgwCreate;
            }
            if (this.data.get('urlQuery.dcgwId')) {
                submitRequester = this.$http.dcgwUpdate;
            }
            this.data.set('confirmed', true);
            try {
                let payload = this.getData(tags);
                delete payload.subnets;
                await submitRequester.call(this.$http, payload);
                location.hash = '#/vpc/dcgw/list';
            } catch (err) {
                this.data.set('confirmed', false);
            }
        }
    }

    checkIamStsRole() {
        const AllRegion = window.$context.getEnum('AllRegion');
        const roleName = StsConfig.DCGW.roleName;
        this.$http
            .iamStsRoleQuery(
                {
                    roleName
                },
                {region: AllRegion.BJ}
            )
            .then((data: any) => {
                if (!data || (data && !data.name)) {
                    location.hash = '#/dc/landing';
                }
            });
    }

    cancel() {
        location.hash = '#/vpc/dcgw/list';
    }

    dcgwSpeedQuota() {
        this.$http.dcgwSpeedQuota().then((result: any) => {
            this.data.set('maxSpeed', result.total);
        });
    }

    async attached() {
        if (this.data.get('urlQuery.dcgwId')) {
            this.data.set('pageNav.title', '编辑专线网关');
        }
        this.getVpcList().then(() => this.isEdit());
        this.checkIamStsRole();
        this.dcgwSpeedQuota();
        await this.getAllPhyList();
        this.getDcChannelList();
    }

    resourceChange({value}) {
        this.data.set('formData.resourceGroupId', value.data.groupId);
    }
    // 获取所有专线
    getAllPhyList() {
        const payload = {
            keyword: '',
            keywordType: 'name',
            pageNo: 1,
            pageSize: 10000
        };
        return this.$http.getDcList(payload).then(res => {
            if (!!res?.result?.length) {
                this.data.set('allPhyList', res.result);
            }
        });
    }

    getDcChannelList() {
        this.data.set('dcInputDisable', true);
        this.$http
            .getChannelList({
                type: 'available'
            })
            .then((res: any) => {
                this.data.set('channelRes', res);
                let currentRegion = window.$context.getCurrentRegionId();
                let obj = {};
                let dcphyList = res.infos.reduce((arr, next) => {
                    obj[next.dcphyId]
                        ? ''
                        : (obj[next.dcphyId] =
                              true &&
                              next.region === currentRegion &&
                              arr.push({
                                  text: next.dcphyId,
                                  value: next.dcphyId
                              }));
                    return arr;
                }, []);
                const allPhyList = this.data.get('allPhyList');
                const dcphyListWithName = u.map(dcphyList, item => {
                    const findByPhyId = u.find(allPhyList, (phyItem: any) => phyItem.id === item.value);
                    if (findByPhyId) {
                        return {
                            ...item,
                            text: `${findByPhyId.name}（${item.value}）`
                        };
                    }
                    return item;
                });
                this.data.set('dcInputDisable', false);
                this.data.set('dcphyList', dcphyListWithName);
            })
            .catch(err => {
                this.data.set('dcInputDisable', false);
            });
    }

    dcphyChange(e: any) {
        this.data.set('formData.channelId', '');
        let dcphyId = e.value;
        let res = this.data.get('channelRes');
        let channelList = [];
        res.infos.filter((item: any) => {
            // 状态可用，通道未绑定，没有关联关系
            if (
                item.dcphyId === dcphyId &&
                item.status === 'established' &&
                !item.dcgwId &&
                !item.mainChannel &&
                !item.subChannel
            ) {
                channelList.push({
                    text: `${item.name || ''}（${item.id}）`,
                    value: item.id
                });
            }
        });
        this.data.set('channelList', channelList);
    }
    tagListRequster() {
        return this.$http.getSearchTagList({serviceType: [], region: ['global']});
    }
}
export default San2React(Processor.autowireUnCheckCmpt(DcgwCreate));
