/*
 * @description: 专线网关列表页
 * @file: network/dcgw/pages/List.js
 * @author: p<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {Notification} from '@baidu/sui';
import {
    OutlinedRefresh,
    OutlinedPlus,
    OutlinedExclamation,
    OutlinedEditingSquare,
    OutlinedDownload
} from '@baidu/sui-icon';

import rules from '../../rules';
import {DcGatewayStatus, DocService} from '@/pages/sanPages/common';
import DcphyIdBind from './bindDcphyId';
import Confirm from '@/pages/sanPages/components/confirm';
import {ResourceGroupDialog} from '@baidu/bce-resourcegroup-sdk-san';
import {ResourceGroupSDK} from '@baidu/bce-resourcegroup-sdk';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import testID from '@/testId';
import '../../style/list.less';
import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import DOCUMENT_ICON from '@/img/document_normal.svg?url';
import DOCUMENT_ICON_COLOR from '@/img/document_color.svg?url';

const {invokeSUI, invokeSUIBIZ, template, invokeComp} = decorators;
const tpl = html` <template>
    <s-app-list-page class="{{klass}}">
        <div slot="pageTitle">
            <div class="dcgw-list-header">
                <div class="header-left">
                    <span class="title">{{title}}</span>
                    <vpc-select class="vpc-select" on-int="vpcInt" on-change="vpcChange" />
                </div>
                <div class="header-right">
                    <a
                        s-ref="introduce"
                        href="javascript:void(0)"
                        class="help-file function-introduce"
                        on-click="handleShowCard"
                        on-mouseenter="handleMouseEnter('introduce')"
                        on-mouseleave="handleMouseLeave('introduce')"
                        s-if="{{!FLAG.NetworkSupportXS}}"
                    >
                        <img class="s-icon" src="{{introduceIcon}}" />功能简介
                    </a>
                    <a
                        href="{{DocService.dcgw_helpFile}}"
                        target="_blank"
                        class="help-file"
                        s-if="{{!FLAG.NetworkSupportXS}}"
                    >
                        <s-icon name="warning-new" />帮助文档
                    </a>
                </div>
            </div>
            <introduce-panel
                isShow="{{show}}"
                klass="{{'endpoint-peerconn-wrapper'}}"
                title="{{introduceTitle}}"
                description="{{description}}"
                on-toggle="handleToggle($event)"
            ></introduce-panel>
        </div>
        <div class="list-page-tb-left-toolbar" slot="bulk">
            <s-tooltip trigger="{{createDc.disable || iamPass.disable ? 'hover' : ''}}" placement="top">
                <!--bca-disable-next-line-->
                <div slot="content">{{iamPass.message || createDc.message | raw}}</div>
                <s-button
                    disabled="{{createDc.disable || iamPass.disable}}"
                    skin="primary"
                    on-click="onCreate"
                    data-test-id="${testID.dcgw.listCreateBtn}"
                    track-id="vpc_dcgw_create"
                >
                    <outlined-plus />
                    创建专线网关
                </s-button>
            </s-tooltip>
            <s-tooltip class="left_class" trigger="{{release.disable ? 'hover' : ''}}" placement="top">
                <!--bca-disable-next-line-->
                <div slot="content">{{release.message | raw}}</div>
                <s-button on-click="onRelease" disabled="{{release.disable}}" data-test-id="${testID.dcgw.deleteDcgw}">
                    释放</s-button
                >
            </s-tooltip>
            <edit-tag
                class="left_class"
                selectedItems="{{selectedItems}}"
                on-success="refresh"
                type="DEDICATEDCONN"
            ></edit-tag>
            <span class="selectTip">{{selectTip}}</span>
        </div>
        <div slot="filter" class="toolbar_right">
            <search-tag
                s-ref="search"
                serviceType="DEDICATEDCONN"
                searchbox="{=searchbox=}"
                on-search="onSearch"
            ></search-tag>
            <s-button on-click="refresh" class="s-icon-button"><outlined-refresh class="icon-class" /></s-button>
            <s-button on-click="onDownload" class="s-icon-button" track-id="ti_vpc_security_download" track-name="下载"
                ><outlined-download class="icon-class"
            /></s-button>
        </div>
        <s-table
            columns="{{table.columns}}"
            loading="{{table.loading}}"
            error="{{table.error}}"
            datasource="{{table.datasource}}"
            on-selected-change="tableSelected($event)"
            on-filter="onFilter"
            on-sort="onSort"
            data-test-id="${testID.dcgw.listTable}"
            selection="{=table.selection=}"
        >
            <div slot="empty">
                <s-empty
                    on-click="onCreate"
                    class="{{iamPass.disable ? 'create-disable' : ''}}"
                    track-id="vpc_dcgw_create"
                >
                </s-empty>
            </div>
            <div slot="error">
                啊呀，出错了？
                <a href="javascript:;" on-click="refresh">重新加载</a>
            </div>
            <div slot="c-id" class="dcgw-id-widget">
                <a
                    href="#/vpc/dcgw/detail?vpcId={{row.vpcId}}&dcgwId={{row.id}}"
                    class="truncated"
                    title="{{row.name}}"
                    track-id="ti_dcgw_list"
                    track-name="详情"
                    data-testid="${testID.dcgw.listName}{{rowIndex}}"
                    >{{row.name}}</a
                >
                <s-popover s-ref="popover-name-{{rowIndex}}" placement="top" trigger="click" class="edit-popover-class">
                    <div class="edit-wrap" slot="content">
                        <s-input
                            value="{=edit.name.value=}"
                            width="320"
                            placeholder="请输入名称"
                            on-input="onEditInput($event, rowIndex, 'name')"
                        />
                        <div class="edit-tip">
                            大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65
                        </div>
                        <s-button
                            skin="primary"
                            s-ref="editBtn-name-{{rowIndex}}"
                            disabled="{{true}}"
                            on-click="editConfirm(row, rowIndex, 'name')"
                            >确定</s-button
                        >
                        <s-button on-click="editCancel(rowIndex, 'name')">取消</s-button>
                    </div>
                    <outlined-editing-square class="name-icon" on-click="onInstantEdit(row, rowIndex, 'name')" />
                </s-popover>
                <br />
                <span class="truncated vpn-id" title="{{row.id}}">{{row.id}}</span>
                <s-clip-board class="name-icon" text="{{row.id}}" />
            </div>
            <div slot="c-description">
                <span class="truncated">{{ row.description || '-' }}</span>
                <s-popover
                    s-ref="popover-description-{{rowIndex}}"
                    placement="top"
                    trigger="click"
                    class="edit-popover-class"
                >
                    <div class="edit-wrap" slot="content">
                        <s-input
                            value="{=edit.description.value=}"
                            width="160"
                            maxLength="{{200}}"
                            on-input="onEditInput($event, rowIndex, 'description')"
                        />
                        <div class="edit-tip">描述不能超过200个字符</div>
                        <s-button
                            skin="primary"
                            s-ref="editBtn-description-{{rowIndex}}"
                            disabled="{{true}}"
                            on-click="editConfirm(row, rowIndex, 'description')"
                            >确定</s-button
                        >
                        <s-button on-click="editCancel(rowIndex, 'description')">取消</s-button>
                    </div>
                    <outlined-editing-square class="name-icon" on-click="onInstantEdit(row, rowIndex, 'description')" />
                </s-popover>
            </div>
            <div slot="c-ipv6Subnets">
                <p s-if="row.ipv6Subnets && row.ipv6Subnets.length > 0" s-for="item in row.ipv6Subnets">{{item}}</p>
                <span s-else> - </span>
            </div>
            <div slot="c-resourceGroups">
                <div s-if="row.resourceGroups && row.resourceGroups.length">
                    <p s-for="item in row.resourceGroups">{{item.name}}</p>
                </div>
                <span s-else>-</span>
            </div>
            <div slot="c-healthCheckStatus" class="c-healthCheckStatus">
                <span class="{{row | getClass}}">{{row | getHc}}</span>
                <s-tip s-if="{{row.healthCheckStatus === 'down'}}" class="inline-tip">
                    <s-warning class="warning_class"></s-warning>
                    <div slot="content">
                        <span>异常专线通道列表</span>
                        <div s-for="item in row.unHealthChecks">
                            <a href="javascript:void(0)" on-click="detailHc(row)">{{item.channelId}}</a>
                        </div>
                    </div>
                </s-tip>
            </div>
            <div slot="c-opt">
                <span class="operations">
                    <s-tip-button
                        s-if="row.dcphyId"
                        disabled="{{row | unbindDisable}}"
                        skin="stringfy"
                        data-test-id="${testID.dcgw.unbindDcgw}{{rowIndex}}"
                        isDisabledVisibile="{{true}}"
                        on-click="unbind(row)"
                    >
                        <div slot="content">{{row | unbindDisableText}}</div>
                        解绑
                    </s-tip-button>
                    <s-tip-button
                        s-else
                        disabled="{{row | actionDisable}}"
                        skin="stringfy"
                        data-test-id="${testID.dcgw.bindDcgw}{{rowIndex}}"
                        isDisabledVisibile="{{true}}"
                        on-click="bind(row)"
                    >
                        <div slot="content">{{row | actionDisableText('绑定')}}</div>
                        绑定
                    </s-tip-button>
                    <s-tip-button
                        disabled="{{row | actionDisable}}"
                        skin="stringfy"
                        isDisabledVisibile="{{true}}"
                        on-click="edit(row)"
                        data-test-id="${testID.dcgw.editDcgw}{{rowIndex}}"
                        class="block_class"
                    >
                        <div slot="content">{{row | actionDisableText('编辑')}}</div>
                        编辑
                    </s-tip-button>
                    <s-tip-button
                        disabled="{{row | actionDisable}}"
                        skin="stringfy"
                        data-test-id="${testID.dcgw.dcgwHc}{{rowIndex}}"
                        isDisabledVisibile="{{true}}"
                        on-click="detailHc(row)"
                    >
                        <div slot="content">{{row | actionDisableText('链路探测')}}</div>
                        链路探测
                    </s-tip-button>
                    <s-button
                        skin="stringfy"
                        on-click="changeResourceGroup(row)"
                        data-test-id="${testID.dcgw.editDcgwRes}{{rowIndex}}"
                        >编辑资源分组</s-button
                    >
                </span>
            </div>
        </s-table>
        <s-pagination
            s-if="{{pager.total}}"
            slot="pager"
            layout="{{'total, pageSize, pager, go'}}"
            pageSize="{{pager.size}}"
            total="{{pager.total}}"
            page="{{pager.page}}"
            on-pagerChange="onPagerChange"
            on-pagerSizeChange="onPageSizeChange"
        />
        <resource-group-dialog
            s-if="{{showResource}}"
            sdk="{{resourceSDK}}"
            resource="{{resource}}"
            on-success="oncommit"
            on-cancel="onCancel"
        />
    </s-app-list-page>
</template>`;

@template(tpl)
@invokeComp('@bind-dcphy', '@edit-tag', '@vpc-select', '@search-tag', '@introduce-panel')
@invokeSUI
@invokeSUIBIZ
class DcgwList extends Component {
    static components = {
        'resource-group-dialog': ResourceGroupDialog,
        'outlined-refresh': OutlinedRefresh,
        'outlined-plus': OutlinedPlus,
        's-warning': OutlinedExclamation,
        'outlined-editing-square': OutlinedEditingSquare,
        'outlined-download': OutlinedDownload
    };
    static computed = {
        selectTip() {
            let length = this.data.get('selectedItems').length;
            let total = this.data.get('pager.total');
            return `已选中${length}条/共${total}条`;
        }
    };
    static filters = {
        getHc(row) {
            if (row.healthCheckStatus === 'up') {
                return '正常';
            } else if (row.healthCheckStatus === 'down') {
                return '异常';
            } else {
                return '-';
            }
        },
        getClass(row) {
            if (row.healthCheckStatus === 'down') {
                return 'warning-hc';
            }
        },
        actionDisable(row) {
            if (row.status === 'binding' || row.status === 'unbinding') {
                return true;
            } else {
                return false;
            }
        },
        actionDisableText(row, text) {
            if (row.status === 'binding' || row.status === 'unbinding') {
                return `当前状态不支持${text}`;
            }
        },
        unbindDisable(row) {
            if (row.status === 'binding' || row.status === 'unbinding' || row.associatedChannels?.length) {
                return true;
            } else {
                return false;
            }
        },
        unbindDisableText(row) {
            if (row.status === 'binding' || row.status === 'unbinding') {
                return '当前状态不支持解绑';
            } else if (row.associatedChannels?.length) {
                return '当前专线网关的通道已关联多个通道，请先解除关联关系';
            }
        }
    };

    initData() {
        return {
            klass: ['main-wrap-new', 'vpc-dcgw-list'],
            title: '专线网关',
            vpcId: '',
            vpcList: [
                {
                    text: '所在网络：全部私有网络',
                    value: ''
                }
            ],
            searchbox: {
                keyword: '',
                placeholder: '请输入实例名称进行搜索',
                keywordType: ['name'],
                keywordTypes: [
                    {value: 'name', text: '实例名称'},
                    {value: 'id', text: '实例ID'},
                    {value: 'tag', text: '标签'},
                    {value: 'resGroupId', text: '资源分组'}
                ]
            },
            selectedItems: [],
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                columns: [
                    {
                        name: 'id',
                        label: '网关名称/ID',
                        width: 160,
                        fixed: 'left'
                    },
                    {
                        name: 'status',
                        label: '状态',
                        filter: {
                            options: [
                                {
                                    text: '全部',
                                    value: ''
                                },
                                ...DcGatewayStatus.toArray()
                            ],
                            value: ''
                        },
                        width: 100,
                        render(item) {
                            let config = DcGatewayStatus.fromValue(item.status);
                            return (
                                '<span class="' +
                                (!u.isEmpty(config) ? config.styleClass : 'status unavailable') +
                                '">' +
                                (!u.isEmpty(config) ? DcGatewayStatus.getTextFromValue(item.status) : '不可用') +
                                '</span>'
                            );
                        }
                    },
                    {
                        name: 'vpcId',
                        label: '所在网络',
                        minWidth: 170,
                        render(item, key, col, rowIndex, colIndex, data) {
                            let vpcName = u.escape(item.vpcName) || '-';
                            let vpcShortId = u.escape(item.vpcShortId);
                            let vpcId = u.escape(item.vpcId) || '-';
                            return `
                                <span class="truncated" title="${vpcName}">
                                    <a href="#/vpc/instance/detail?vpcId=${vpcId}" class="text-hidden"
                                        track-id="ti_vpc_instance_detail" track-name="详情"
                                        data-testid="${testID.dcgw.vpcName}${rowIndex}">${vpcName}</a>
                                </span>
                                <br>
                                <span class="truncated" title="${vpcShortId}">${vpcShortId}</span>`;
                        }
                    },
                    {
                        name: 'speed',
                        label: '出口带宽',
                        width: 100,
                        render(item) {
                            return u.escape(item.speed) + 'Mbps';
                        }
                    },
                    {
                        name: 'dcphyId',
                        label: '绑定物理专线',
                        width: 130,
                        render(item) {
                            if (item.status === DcGatewayStatus.UNBOUNDED) {
                                return '-';
                            }
                            let dcphyId = u.escape(item.dcphyId);
                            return '<span class="truncated" title="' + dcphyId + '">' + dcphyId + '</span>';
                        }
                    },
                    {
                        name: 'channelId',
                        label: '绑定专线通道',
                        width: 130,
                        render(item) {
                            if (item.status === DcGatewayStatus.UNBOUNDED) {
                                return '-';
                            }
                            let channelId = u.escape(item.channelId);
                            return '<span class="truncated" title="' + channelId + '">' + channelId + '</span>';
                        }
                    },
                    {
                        name: 'tag',
                        label: '标签',
                        sortable: true,
                        width: 100,
                        render(item) {
                            if (!item.tags || item.tags.length < 1) {
                                return '-';
                            }
                            let tagHtml = '';
                            let tags = '';
                            u.each(item.tags, (item, index) => {
                                let tagKey = u.escape(item.tagKey);
                                let tagValue = u.escape(item.tagValue);
                                tags += '{' + tagKey + ' : ' + tagValue + '} ';
                                if (index < 2) {
                                    tagHtml += tagKey + ' : ' + tagValue + '<br>';
                                }
                            });
                            item.tags.length > 2 && (tagHtml += '...');
                            return '<div title="' + tags + '">' + tagHtml + '</div>';
                        }
                    },
                    {
                        name: 'resourceGroups',
                        label: '资源分组',
                        width: 90
                    },
                    {
                        name: 'description',
                        label: '描述',
                        width: 100,
                        render(item) {
                            let desc = u.escape(item.description) || '-';
                            return `<span class="truncated">${desc}</span>`;
                        }
                    },
                    {
                        name: 'opt',
                        label: '操作',
                        width: 120,
                        fixed: 'right'
                    }
                ],
                datasource: []
            },
            pager: {
                size: 10,
                page: 1,
                total: 0
            },
            order: {},
            filter: {},
            createDc: {},
            release: {},
            resourceSDK: new ResourceGroupSDK(window.$http, window.$context),
            iamPass: {},
            DocService,
            FLAG,
            show: true,
            introduceEle: null,
            introduceTitle: '专线网关',
            description:
                '专线网关是本端VPC连接物理专线的接口，用户在本端VPC内为物理专线连通配置路由表时，下一跳需指向对应专线网关。',
            introduceIcon: INTRODUCE_ICON_COLOR,
            documentIcon: DOCUMENT_ICON,
            urlQuery: getQueryParams()
        };
    }
    inited() {
        this.getIamQuery();
        this.handleJumpFromMirror();
    }
    handleJumpFromMirror() {
        const dcgwId = this.data.get('urlQuery.dcgwId');
        if (dcgwId) {
            this.data.set('searchbox.keywordType', ['id']);
            this.data.set('searchbox.keyword', dcgwId);
        }
    }
    tableSelected(e) {
        this.data.set('selectedItems', e.value.selectedItems);
        let {release} = checker.check(rules, e.value.selectedItems);
        this.data.set('release', release);
    }

    onCreate() {
        location.hash = '#/vpc/dcgw/create?vpcId=' + window.$storage.get('vpcId');
    }

    attached() {
        window.$storage.get('showDcgwIntroduce') === false && this.data.set('show', false);
        let vpcId = this.data.get('urlQuery.vpcId');
        if (vpcId) {
            window.$storage.set('vpcId', vpcId);
        }
        let {createDc, release} = checker.check(rules, []);
        this.data.set('createDc', createDc);
        this.data.set('release', release);
        this.data.set('introduceEle', this.ref('introduce'));
    }

    // 点击修改icon
    onInstantEdit(row, rowIndex, type) {
        this.data.set(`edit.${type}.value`, row[`${type}`]);
        this.data.set(`edit.${type}.error`, false);
        const editWrap = this.ref(`popover-${type}-${rowIndex}`);
        editWrap.data.set('visible', !editWrap.data.get('visible'));
    }

    // 编辑弹框-输入名称/描述
    onEditInput(e, rowIndex, type) {
        let result =
            type === 'name'
                ? e.value === '' || !/^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/.test(e.value)
                : e.value.length > 200;
        this.data.set(`edit.${type}.error`, result);
        this.data.set(`edit.${type}.value`, e.value);
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', result);
    }

    async editConfirm(row, rowIndex, type) {
        let payload = await this.$http.dcgwDetail({dcgwId: row.id});
        let edit = this.data.get(`edit.${type}`);
        if (edit.error) {
            return;
        }
        payload.dcgwId = row.id;
        payload[type] = edit.value;
        this.$http.dcgwUpdate(payload).then(() => {
            this.editCancel(rowIndex, type);
            Notification.success('修改成功');
            this.loadPage();
        });
    }

    editCancel(rowIndex, type) {
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', true);
        this.ref(`popover-${type}-${rowIndex}`).data.set('visible', false);
    }

    onPagerChange(e) {
        this.resetTable();
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }

    onPageSizeChange(e) {
        this.data.set('pager.page', 1);
        this.data.set('pager.size', e.value.pageSize);
        if (this.data.get('pager.page') === 1) {
            this.loadPage();
        }
    }

    onSort(e) {
        this.data.set('order', e.value);
        this.loadPage();
    }

    onFilter(e) {
        let name = e.field.name;
        let value = e[name] ? e[name] : '';
        this.data.set('filter.' + name, value);
        this.loadPage();
    }

    getDcgwTags() {
        this.ref('search').getTags();
    }

    onRelease() {
        let confirm = new Confirm({
            data: {
                open: true,
                content: '确认释放选中的专线网关？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            let id = this.data.get('selectedItems')[0].id;
            this.$http.dcgwDelete({dcgwId: id}).then(() => {
                this.refresh();
                Notification.success('释放成功', {placement: 'topRight'});
            });
        });
    }

    edit(item) {
        location.hash = '#/vpc/dcgw/edit?vpcId=' + item.vpcId + '&dcgwId=' + item.id;
    }

    bind(row) {
        let bind = new DcphyIdBind({
            data: {
                open: true,
                instance: row,
                vpcId: row.vpcId,
                dcgwId: row.id
            }
        });
        bind.attach(document.body);
        bind.on('create', () => {
            this.loadPage();
            Notification.success('绑定成功', {placement: 'topRight'});
        });
    }

    async unbind(row) {
        try {
            const channelDetail = await this.$http.channelDetail(
                row.channelId,
                {type: 'available'},
                {'x-silent': true}
            );
            if (Array.isArray(channelDetail.subChannel) && channelDetail.subChannel.length) {
                let confirm = new Confirm({
                    data: {
                        open: true,
                        content: `专线通道存在关联关系，请在<a href="#/dc/channel/detail?instanceId=${row.dcphyId}&channelId=${row.channelId}&creator=other" target="_blank">专线通道详情页</a>解关联专线通道`
                    }
                });
                confirm.attach(document.body);
                confirm.on('confirm', () => {
                    confirm.detach();
                });
                return;
            }
        } catch {}
        let confirm = new Confirm({
            data: {
                open: true,
                content:
                    '请确认是否解绑，注意解绑专线网关时控制台会同步删除当前专线网关关联的VPC路由表和专线通道路由管理中的路由条目。'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http
                .dcgwUnbind({
                    dcgwId: row.id,
                    dcphyId: row.dcphyId,
                    channelId: row.channelId
                })
                .then(() => {
                    this.loadPage();
                    Notification.success('解绑成功', {placement: 'topRight'});
                });
        });
    }

    onSearch() {
        this.data.set('pager.page', 1);
        this.resetTable();
        return this.loadPage();
    }

    refresh() {
        this.resetTable();
        return this.loadPage();
    }

    // 重置列表
    resetTable() {
        this.data.set('pager.page', 1);
        this.data.set('table.selection.selectedIndex', []);
    }

    getSearchCriteria() {
        let searchParam = this.ref('search') ? this.ref('search').getSearchCriteria() : {};
        const {pager, order, filter} = this.data.get('');
        const vpcId = window.$storage.get('vpcId');
        return u.extend({}, searchParam, order, {vpcId}, {pageNo: pager.page, pageSize: pager.size}, filter);
    }

    loadPage(payload) {
        this.data.set('table.loading', true);
        payload = payload || this.getSearchCriteria();
        if (payload.keywordType === 'resGroupId') {
            payload.keywordType = 'resourceGroupName';
        }
        this.$http
            .dcgwList(payload)
            .then(data => {
                this.data.set('table.datasource', data.result);
                this.data.set('pager.total', data.totalCount);
                this.data.set('table.loading', false);
            })
            .catch(() => this.data.set('table.loading', false));
    }

    vpcInt() {
        this.loadPage();
    }

    vpcChange() {
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    detailHc(row) {
        location.hash = `#/vpc/dcgw/hc?vpcId=${row.vpcId}&dcgwId=${row.id}`;
    }
    oncommit() {
        this.data.set('showResource', false);
        this.loadPage();
    }
    onCancel() {
        this.data.set('showResource', false);
    }
    changeResourceGroup(row) {
        let resource = {
            resourceId: row.id,
            serviceType: 'ETGATEWAY'
        };
        this.data.set('resource', resource);
        this.data.set('showResource', true);
    }
    getIamQuery() {
        this.$http.getInterfaceIam({interfaceName: 'createEtGateway'}).then(res => {
            if (!res.requestId && !res.masterAccount) {
                if (!res.interfacePermission) {
                    this.data.set('iamPass', {
                        disable: true,
                        message: '您没有创建专线网关权限，请联系主用户添加'
                    });
                } else {
                    this.data.set('iamPass', {
                        disable: false,
                        message: ''
                    });
                }
            } else {
                this.data.set('iamPass', {
                    disable: false,
                    message: ''
                });
            }
        });
    }
    // 下载
    onDownload() {
        let ids = [];
        ids = this.data.get('selectedItems').map(item => {
            return item.id;
        });
        let filter = 'ids=' + ids.join(',');
        window.open(`/api/network/v1/dc/download?` + filter);
    }
    /**
     * @description 介绍卡片变化时回调
     * @param {boolean} flag
     */
    handleToggle(flag) {
        this.data.set('show', flag);
        window.$storage.set('showDcgwIntroduce', false);
        this.data.set('introduceIcon', INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#151B26';
    }
    handleShowCard() {
        if (!this.data.get('show')) {
            this.data.set('show', true);
            window.$storage.set('showDcgwIntroduce', true);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type) {
        if (type === 'introduce') {
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
            const ele = this.data.get('introduceEle');
            ele.style.color = '#2468F2';
        } else {
            this.data.set('documentIcon', DOCUMENT_ICON_COLOR);
        }
    }
    handleMouseLeave(type) {
        const isShow = this.data.get('show');
        if (type === 'introduce') {
            this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
            const ele = this.data.get('introduceEle');
            !isShow && (ele.style.color = '#151B26');
        } else {
            this.data.set('documentIcon', DOCUMENT_ICON);
        }
    }
}
export default San2React(Processor.autowireUnCheckCmpt(DcgwList));
