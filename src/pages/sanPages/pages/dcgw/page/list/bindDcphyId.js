/*
 * @description: 绑定物理专线
 * @file: dcgw/pages/bindDcphyId.js
 * @author: p<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {Notification} from '@baidu/sui';

import rule from '@/pages/sanPages/utils/rule';
import {convertCidrToBinary} from '@/pages/sanPages/utils/common';

let flag = 1;
let subnetRule = [
    {required: true, message: '不能为空'},
    {pattern: new RegExp(rule.IP_CIDR), message: 'IP范围不符合规则'},
    {
        validator: (rule, value, callback, source) => {
            let valueString = convertCidrToBinary(value);
            let valueMask = value.split('/')[1] || (value.split('/')[0] === '0.0.0.0' ? 0 : 32);
            if (valueString.substring(+valueMask, valueString.length).indexOf('1') > -1) {
                return callback('CIDR格式不合法');
            }
            return callback();
        }
    }
];
const {asComponent, invokeSUI, invokeSUIBIZ, template, withSidebar, invokeComp} = decorators;
const tpl = html`
    <template>
        <s-dialog class="dcgw-bind-dcphy" open="{=open=}" width="550" title="绑定">
            <s-form
                s-ref="form"
                data="{=formData=}"
                rules="{{rules}}"
                label-col="{{labelCol}}"
                wrapper-col="{{wrapperCol}}"
                label-align="left"
            >
                <s-form-item prop="vpcId" label="所在网络：">
                    <span>{{instance.vpcName}}</span>
                </s-form-item>
                <s-form-item prop="name" label="网关名称：">
                    <span>{{instance.name}}</span>
                </s-form-item>
                <s-form-item prop="speed" label="出口带宽：">
                    <span>{{instance.speed}}</span>
                </s-form-item>
                <s-form-item prop="dcphyIdType" class="radio-box" label="绑定物理专线：">
                    <s-radio-radio-group
                        class="s-doc-radio"
                        disabled="{{true}}"
                        datasource="{{dcphyIdType}}"
                        value="{=formData.dcphyIdType=}"
                        on-change="dcphyIdTypeChange($event)"
                        radioType="radio"
                    >
                    </s-radio-radio-group>
                </s-form-item>
                <div>
                    <s-form-item prop="dcphyId" class="dcphy">
                        <s-select
                            disabled="{{dcInputDisable}}"
                            value="{=formData.dcphyId=}"
                            datasource="{{dcphyList}}"
                            on-change="dcphyChange($event)"
                            width="{{150}}"
                            filterable
                            placeholder="请选择物理专线ID"
                        ></s-select>
                    </s-form-item>
                    <s-form-item prop="channelId" class="channel">
                        <s-select
                            disabled="{{dcInputDisable}}"
                            value="{=formData.channelId=}"
                            datasource="{{channelList}}"
                            width="{{200}}"
                            filterable
                            placeholder="请选择专线通道ID"
                        ></s-select>
                    </s-form-item>
                </div>
            </s-form>
            <div slot="footer">
                <s-button on-click="close">取消</s-button>
                <s-button
                    skin="primary"
                    disabled="{{disableSub}}"
                    on-click="create"
                    track-id="vpc_dcgw_bind_physicalLine"
                >
                    确定
                </s-button>
            </div>
        </s-dialog>
    </template>
`;

@template(tpl)
@asComponent('@bind-dcphy')
@invokeSUI
@invokeSUIBIZ
class DcphyIdBind extends Component {
    initData() {
        let labelCol = {span: 4};
        let wrapperCol = {span: 19};
        if (location.search.indexOf('locale=en') > -1) {
            labelCol = {span: 6};
            wrapperCol = {span: 17};
        }
        return {
            open: false,
            labelCol,
            wrapperCol,
            dcphyIdType: [
                {label: '绑定物理专线', value: 'bind'},
                {label: '暂不绑定', value: 'unbind'}
            ],
            formData: {
                dcphyId: '',
                channelId: '',
                subnet0: '',
                dcphyIdType: 'bind'
            },
            rules: {
                dcphyId: [
                    {required: true, message: '不能为空'},
                    {pattern: /^dcphy-[\w\-\/\.]{12}$/, message: '物理专线ID不符合规则'}
                ],
                channelId: [
                    {required: true, message: '不能为空'},
                    {pattern: /^dedicatedconn-[\w\-\/\.]{12}$/, message: '专线通道ID不符合规则'}
                ],
                subnet0: subnetRule
            },
            disableSub: false,
            dcphyList: [],
            channelList: [],
            dcInputDisable: false
        };
    }

    computed = {
        disableAddSubnet() {
            return this.data.get('subnets').length >= 10;
        }
    };

    attached() {
        this.loadVpcDetail();
        this.getDcChannelList();
    }

    loadVpcDetail() {
        const vpcId = this.data.get('vpcId');
        return this.$http
            .getVpcDetail({
                vpcIds: [vpcId]
            })
            .then(data => {
                const result = data[vpcId] || {};
                this.data.set('vpcInfo', result);
            });
    }

    close() {
        this.data.set('open', false);
    }

    /**
     * 专线网关绑定时的校验,专线通道必须处于可用状态,未绑定专线网关,专线通道底下没有子通道，它也不是其他通道的子通道
     * @param {string} channelId
     */
    bindChannelCheck(channelId) {
        return new Promise((resolve, reject) => {
            // 跨账号授权时会获取不到通道详情，绑定和解绑时需要加上type:available参数
            this.$http
                .channelDetail(channelId, {
                    type: 'available'
                })
                .then(res => {
                    const {status, dcgwId, mainChannel, subChannel} = res;
                    if (status !== 'established') {
                        reject('当前专线通道不可用');
                    } else if (dcgwId || mainChannel || subChannel?.length) {
                        reject('当前专线通道已被关联，不可重复操作。');
                    } else {
                        resolve();
                    }
                });
        });
    }

    async create() {
        let payload = {
            dcphyId: this.data.get('formData.dcphyId'),
            channelId: this.data.get('formData.channelId'),
            dcgwId: this.data.get('instance.id')
        };
        await this.ref('form').validateFields();
        this.bindChannelCheck(this.data.get('formData.channelId'))
            .then(() => {
                let vpcInfo = this.data.get('vpcInfo');
                this.data.set('disableSub', true);
                this.$http.dcgwBind(payload).then(
                    () => {
                        this.data.set('open', false);
                        this.fire('create');
                    },
                    () => {
                        this.data.set('disableSub', false);
                    }
                );
            })
            .catch(msg => {
                Notification.warning(msg);
            });
    }
    getDcChannelList() {
        this.data.set('dcInputDisable', true);
        this.$http
            .getChannelList({
                type: 'available'
            })
            .then(res => {
                this.data.set('channelRes', res);
                let currentRegion = window.$context.getCurrentRegionId();
                let obj = {};
                let dcphyList = res.infos.reduce((arr, next) => {
                    obj[next.dcphyId]
                        ? ''
                        : (obj[next.dcphyId] =
                              true &&
                              next.region === currentRegion &&
                              arr.push({
                                  text: next.dcphyId,
                                  value: next.dcphyId
                              }));
                    return arr;
                }, []);
                this.data.set('dcInputDisable', false);
                this.data.set('dcphyList', dcphyList);
            })
            .catch(err => {
                this.data.set('dcInputDisable', false);
            });
    }

    dcphyChange(e) {
        this.data.set('formData.channelId', '');
        let dcphyId = e.value;
        let res = this.data.get('channelRes');
        let channelList = [];
        res.infos.filter(item => {
            // 状态可用，通道未绑定，没有关联关系
            if (
                item.dcphyId === dcphyId &&
                item.status === 'established' &&
                !item.dcgwId &&
                !item.mainChannel &&
                !item.subChannel
            ) {
                channelList.push({
                    text: item.id,
                    value: item.id
                });
            }
        });
        this.data.set('channelList', channelList);
    }
}
export default Processor.autowireUnCheckCmpt(DcphyIdBind);
