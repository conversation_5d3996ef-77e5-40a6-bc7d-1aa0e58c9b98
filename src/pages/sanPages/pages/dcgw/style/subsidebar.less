/**
 * @file src/network/styles/create.less
 * <AUTHOR>
 */
.dcgw-detail-main-warp {
    width: 100%;
    min-height: 100%;
    .app-tab-page {
        padding: 0;
        background: #f7f7f7 !important;
        .bui-tab-header {
            border-right: 1px solid #ebebeb;
            border-bottom: none;
        }
        .skin-accordion {
            min-height: 540px !important;
        }
        .bui-tab-nav-item {
            width: 150px !important;
        }
    }
    .instance-not-found-class {
        height: 100%;
    }
    .s-detail-page-content {
        margin: 0;
        .app-tab-page {
            padding: 16px;
            border-radius: 6px;
            background: #fff;
            .bui-tab {
                border-radius: 6px;
                border: none;
            }
        }
    }
    .dcgw-detail-head {
        line-height: 50px;
        .backbox {
            height: 30px;
            line-height: 28px;
            border: 1px solid #ccc;
            outline: none;
            font-size: 12px;
            font-weight: 400;
            cursor: pointer;
            text-align: center;
            vertical-align: middle;
            color: #000;
            background-color: #fff;
            font-family:
                Microsoft Yahei,
                微软雅黑,
                <PERSON><PERSON><PERSON>,
                Aria<PERSON>,
                Helvetica,
                STHeiti;
            display: inline-block;
            width: 22px;
            .iconfont {
                font-size: 12px;
            }
        }
        span {
            margin-left: 5px;
            vertical-align: middle;
        }
        span:last-child {
            position: relative;
            top: 1px;
        }
    }
    .title_class {
        display: flex;
        align-items: center;
        .s-icon {
            font-size: 14px;
            color: #84868c;
        }
        .instance-name {
            font-size: 16px;
            font-weight: 500;
            color: #151b26;
            padding: 0px 12px 0px 16px;
        }
    }
}

.dcgw-component-subsidebar {
    width: 140px;
    background: #f5f5f5;
    .menu-item a {
        display: block;
        padding: 10px 20px;
        color: #333;
        &:hover {
            background: #eaf6fe;
        }
    }
    .menu-item.disabled a {
        cursor: not-allowed;

        &:hover {
            background: #f5f5f5;
        }
    }
    .sidebar-current a {
        border-left: 4px solid #2468f2;
        color: #2468f2;
        background: #fff;
        &:hover {
            background: #fff;
        }
    }
}

.status.warning {
    color: #f4b329;
}
