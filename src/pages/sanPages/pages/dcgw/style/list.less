.vpc-dcgw-list {
    height: 100%;
    overflow-y: auto;
    background: #f7f7f9 !important;
    .s-biz-page-header {
        margin: 0px !important;
        background-color: #fff;
        height: auto !important;
        border: none !important;
        line-height: 47px;
        .s-biz-page-title {
            h2 {
                height: 47px !important;
                line-height: 47px !important;
                font-weight: 500 !important;
                font-size: 16px !important;
                margin-left: 16px !important;
            }
        }
    }
    .s-biz-page-content {
        border-radius: 6px;
        margin: 16px !important;
        padding: 24px;
        background: #fff;
        .s-biz-page-toolbar {
            margin: 0px;
        }
        .s-biz-page-body {
            margin-top: 16px;
            .s-table {
                .s-table-body {
                    max-height: calc(~'100vh - 332px');
                }
            }
        }
        .s-biz-page-footer {
            padding-bottom: 0px;
            margin-top: 16px;
        }
    }
    .selectTip {
        margin-left: 5px;
        color: #ccc;
    }
    .dcgw-list-header {
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .header-left {
            .title {
                display: inline-block;
                margin: 0;
                color: #151b26;
                margin-right: 12px;
                height: 47px;
                line-height: 47px;
                font-weight: 500;
                font-size: 16px;
                margin-left: 16px;
            }
        }
        .header-right {
            display: flex;
            align-items: center;
            .help-file {
                margin-right: 16px;
                font-size: 12px;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                color: #151b26;
                line-height: 20px;
                &:hover {
                    color: #2468f2;
                }
                .s-icon {
                    position: relative;
                    top: -1px;
                    right: 4px;
                    margin-right: 0;
                    color: #2468f2;
                    font-size: 14px;
                }
            }
            .function-introduce {
                color: #2468f2;
            }
        }
    }
    .list-page-tb-left-toolbar {
        display: inline-flex;
        align-items: center;
    }
    .toolbar_right {
        display: inline-flex;
    }
    .s-biz-page-tb-left {
        .icon-plus {
            color: #fff;
        }
    }
    .operations {
        .s-button {
            padding: 0;
            margin-right: 16px;
        }
        .block_class {
            display: block;
        }
    }
    .s-table {
        .s-table-row:hover {
            .name-icon {
                display: inline;
            }
        }
        .name-icon {
            position: relative;
            top: -1px;
            fill: #2468f2;
            display: none;
        }
        .s-icon {
            font-size: 12px;
            color: #2468f2;
        }
        .dcgw-id-widget {
            white-space: nowrap;
        }
    }
    .warning-hc {
        color: #f33e3e;
        vertical-align: middle;
    }
    .c-healthCheckStatus {
        display: flex;
        align-items: center;
        .inline-tip {
            top: 3px;
            left: 5px;
            position: relative;
            .s-tip-warning {
                justify-content: center;
                .warning_class {
                    fill: #999;
                }
            }
            .s-tip:hover .s-icon path {
                fill: #2468f2 !important;
            }
        }
    }
    .tip-icon {
        width: 14px;
        height: 14px;
        line-height: 16px;
        margin-left: 2px;
        border: 1px solid #1f8eeb;
        color: #1f8eeb;
        background: #fff;
        box-sizing: content-box;
        overflow: hidden;
        display: inline-block;
        zoom: 1;
        vertical-align: middle;
        cursor: pointer;
        text-align: center;
        font-size: 12px;

        &:hover {
            border-color: #1f8eeb;
            background: #1f8eeb;
            color: #fff;
        }
    }
}
.dcgw-confirm {
    .s-dialog > .s-dialog-wrapper .s-dialog-header {
        background: #fff;
    }
    .s-dialog > .s-dialog-wrapper .s-dialog-content {
        min-height: 50px;
    }
    .icon-warning-new {
        color: #eb7600;
        font-size: 20px;
        vertical-align: middle;
        margin-right: 6px;
    }
    .text {
        display: inline;
        vertical-align: middle;
        line-height: 30px;
    }
    .s-dialog-footer {
        border-top: 0;
        text-align: right;
        padding: 0 24px 24px;
        margin: 0;
    }
}
.dcgw-bind-dcphy {
    .s-form-item {
        margin-bottom: 20px;
        .s-form-item-label {
            height: 30px;
            width: 90px;
        }
        .s-form-item-control-wrapper {
            line-height: 30px;
        }
        .s-form-item-error {
            padding-bottom: 0;
        }
    }
    .subnets_class_bind {
        margin-bottom: 10px;
    }
    .dcphy {
        float: left;
        padding-left: 90px;
        margin-right: 20px;
    }
    .channel {
        float: left;
        .s-form-item-control {
            margin-left: 16px;
        }
    }
    .subnets {
        padding-left: 90px;
        padding-bottom: 20px;
    }
}

.locale-en {
    .dcgw-bind-dcphy {
        .dcphy {
            padding-left: 110px;
        }
    }
}
