.dcgw-detail-v2 {
    position: relative;
    padding: 24px;
    .monitor-trends {
        margin: 0;
        height: 100%;
        overflow-y: auto;
    }
    .content-box {
        h4 {
            display: block;
            color: #151b26;
            line-height: 24px;
            font-weight: 500;
            margin-bottom: 16px;
            font-size: 16px;
        }
        .cell {
            width: 33%;
            display: inline-block;
            margin-bottom: 16px;
            .cell-title {
                display: inline-block;
                vertical-align: top;
                color: #5e626a;
                margin-right: 16px;
                width: 87px;
            }
            .cell-content {
                display: inline-block;
                color: #151a26;
                max-width: 70%;
                word-break: break-all;
                position: relative;
            }
            .name-icon {
                fill: #2468f2;
                font-size: 12px;
                margin-left: 10px;
            }
        }
    }
}

.dcgw-nat-list {
    padding: 24px;
    .s-biz-page-header {
        margin: 0 !important;
        height: 28px !important;
        span {
            display: inline-block;
            margin: 0;
            font-size: 16px;
            color: #151b26;
            line-height: 24px;
            font-weight: 500;
            margin-bottom: 16px;
        }
    }
    .s-biz-page-content {
        margin: 0 !important;
    }
    .icon-faq {
        color: #ccc;
        vertical-align: middle;
    }
    .selectTip {
        float: left;
        margin-top: 10px;
        color: #ccc;
    }
    .inline-tip {
        top: 3px;
        position: relative;
        .s-tip-warning {
            justify-content: center;
            .warning_class {
                fill: #999;
            }
        }
        .s-tip:hover .s-icon path {
            fill: #2468f2 !important;
        }
    }
    .list-page-tb-left-toolbar {
        display: inline-flex;
    }
    .right_bar {
        display: inline-flex;
    }
    .s-biz-page-header {
        border-bottom: none !important;
    }
    .s-biz-page-footer {
        margin-top: 16px 0 0 !important;
        padding: 0px !important;
    }
    .s-table .s-table-body {
        max-height: calc(~'100vh - 340px');
    }
    .s-empty-action-icon {
        display: none;
    }
}

.dcgw-instance-edit {
    padding: 12px;
    max-width: 400px;
    min-height: 100px;
    background: #fff;
    box-shadow: 0 2px 8px 0 rgba(8, 14, 26, 0.06);
    -webkit-box-shadow: 0 2px 8px 0 rgba(8, 14, 26, 0.06);
    border-radius: 4px;
    background: #fff;
    position: absolute;
    .tip {
        color: #83868c;
        padding: 3px;
    }
    .s-button {
        margin: 10px 5px;
    }
}
.dcgw-instance-edit-name {
    top: 16%;
    left: 45%;
}
.dcgw-instance-edit-speed {
    top: 20%;
    left: 15%;
}
.dcgw-instance-edit-desc {
    top: 25%;
    left: 45%;
}

.dcgw-bind-dcphy {
    .network-dcgw-detail-editsubnets {
        display: flex;
    }
    .row {
        margin-bottom: 10px;
    }
}

.dcgw-nat-create {
    input {
        box-sizing: content-box;
    }
    .s-form-item-label {
        width: 80px;
        height: 30px;
    }
}

.dcgw-detail-main-warp {
    .skin-accordion-tab {
        min-height: 560px !important;
    }
}
