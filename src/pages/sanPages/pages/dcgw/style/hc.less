.dcgw-hc-v2 {
    position: absolute;
    left: 181px;
    right: 42px;
    padding: 24px 0px 24px 12px;
    .content-box h4 {
        padding-left: 0px;
        margin-bottom: 16px;
        margin-top: 8px;
    }
    .hc_button_class {
        display: block;
        margin-top: 8px;
    }
    .s-pagination {
        margin-top: 16px;
        float: right;
    }
    .icon-faq {
        color: #ccc;
        vertical-align: middle;
    }
    .person-tip-wrap {
        margin-bottom: 20px;
    }
    .person-tip-item {
        background: #fcf7f1;
        color: #333;
        padding: 5px 15px;
        margin-right: 5px;
        .s-icon {
            color: #f39000;
            font-size: 10px;
        }
    }
    .s-table-cell-text {
        .tip-icon {
            color: #9e9898;
            border: 1px solid #9e9898;
            box-sizing: content-box;
            position: relative;
            top: -2px;
            &:hover {
                border-color: #2468f2;
                color: #2468f2;
            }
        }
    }
    .s-alert {
        margin: 0;
    }
    .s-table .s-table-body {
        max-height: calc(~'100vh - 378px') !important;
    }
    .inline-tip {
        top: 3px;
        position: relative;
        .s-tip-warning {
            justify-content: center;
            .warning_class {
                fill: #999;
            }
        }
        .s-tip:hover .s-icon path {
            fill: #2468f2 !important;
        }
    }
}
