.dcgw-hc-create {
    .s-dialog-wrapper {
        overflow: unset !important;
    }
    .alert-tip {
        height: auto;
        width: 560px;
    }
}

.hc-create {
    .s-form-item-label {
        width: 120px;
        text-align: left;
        .inline-tip {
            top: 3px;
            left: -5px;
            position: relative;
            .s-tip-warning {
                justify-content: center;
                .warning_class {
                    fill: #999;
                }
            }
            .s-tip:hover .s-icon path {
                fill: #2468f2 !important;
            }
        }
    }

    .s-radio-group {
        line-height: 30px;
        .s-radio {
            margin-right: 10px;
        }
    }
    .input-error {
        border-color: #f33e3e;

        &:hover {
            border-color: #f33e3e;
        }
    }

    .s-input input {
        box-sizing: content-box;
    }
    .icon-faq {
        color: #ccc;
        vertical-align: middle;
    }
    .ping-class {
        .s-row {
            align-items: center;
        }
    }
    .ips-wrap {
        .s-form-item-control-wrapper {
            .s-form-item-control {
                margin-left: 120px !important;
            }
        }
        .s-input input {
            text-align: center;
        }
    }
    .input-error {
        border-color: #f33e3e;

        &:hover {
            border-color: #f33e3e;
        }
    }
    .label_class {
        .inline-tip {
            top: 3px;
            position: relative;
            .s-tip-warning {
                justify-content: center;
                .warning_class {
                    fill: #999;
                }
            }
            .s-tip:hover .s-icon path {
                fill: #2468f2 !important;
            }
        }
    }
    .switch-box {
        .s-form-item-control-wrapper {
            .s-form-item-control {
                position: relative;
                top: 5px;
            }
        }
    }
}
.tooltip-class {
    .s-tooltip-content {
        width: 180px;
    }
}
