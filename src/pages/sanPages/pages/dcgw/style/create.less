.dcgw-create-v2 {
    background: #fff;
    width: 100%;
    min-height: 100%;
    padding-bottom: 20px;
    background-color: #f7f7f9;
    .s-alert {
        margin: 16px 16px 0;
        .s-icon {
            margin-right: 0;
        }
    }
    .alert_tip {
        color: #151b26 !important;
    }
    .content-box {
        text-align: left;
        margin: 20px auto;
        padding: 20px;
        .icon-faq {
            color: #ccc;
            vertical-align: middle;
        }
    }
    .s-radio-group {
        .wrapper {
            margin-right: 10px;
            .s-radio {
                margin-right: 10px;
            }
        }
    }
    .icon-x {
        color: #ccc;
        font-size: 12px;
        vertical-align: middle;
        margin-left: 5px;
    }
    .icon-icon-test {
        font-size: 12px;
        margin-right: 5px;
    }
    .speed-unit {
        color: #4a4a4a;
        margin-left: 10px;
    }
    .radio-box {
        .s-form-item-control-wrapper {
            line-height: 30px;
        }
    }
    .dcgw_ipv6 {
        .s-row {
            align-items: center;
        }
        .bind-tip {
            color: #999;
            .s-icon {
                font-size: 12px;
                border: 1px solid #999;
            }
        }
    }
    .dcphy-box {
        .s-form-item {
            margin: 0 !important;
        }
        .dcphy {
            // float: left;
            padding-left: 96px;
            display: inline-block;
            margin-right: 20px;
            .s-form-item-error {
                padding-bottom: 0;
            }
        }
        .channel {
            display: inline-block;
        }
    }

    .subnets {
        padding-left: 96px;
        padding-bottom: 20px;
    }
    .normal {
        color: #2468f2;
        cursor: pointer;
    }
    .disable {
        color: #ccc;
        cursor: not-allowed;
    }
    .resource-form-part-wrap {
        margin: 10px auto 0 auto;
        .resource-group-panel {
            padding: 0;
            margin: 0;
            border: none;
            h4 {
                padding: 0;
            }
        }
    }
    .form-part-wrap {
        border-radius: 6px;
        width: calc(~'100vw - 48px');
        background: #fff;
        margin: 16px;
        padding: 24px;
        h4 {
            display: block;
            border: none;
            zoom: 1;
            color: #151b26;
            font-size: 16px;
            font-weight: 500;
            margin: 0;
            padding: 0;
        }
        .s-form-item-label {
            width: 96px;
            height: 30px;
            .inline-tip {
                top: 3px;
                position: relative;
                .s-tip-warning {
                    justify-content: center;
                    .warning_class {
                        fill: #999;
                    }
                }
                .s-tip:hover .s-icon path {
                    fill: #2468f2 !important;
                }
            }
        }
        .label_class {
            .inline-tip {
                top: 3px;
                position: relative;
                .s-tip-warning {
                    justify-content: center;
                    .warning_class {
                        fill: #999;
                    }
                }
                .s-tip:hover .s-icon path {
                    fill: #2468f2 !important;
                }
            }
        }
        .center_class {
            .s-row {
                .s-form-item-control-wrapper {
                    line-height: 30px;
                }
            }
        }
    }
    .buybucket {
        width: 100%;
        left: 0;
        height: 60px;
        .buybucket-container {
            width: auto;
            float: left;
            height: 48px;
            transform: translateY(12.5%);
            display: flex;
            align-items: center;
        }
    }
    .resource-form-part-wrap {
        margin-bottom: 0px;
    }
    .channel {
        .s-form-item-control {
            margin-left: 16px;
        }
        .s-form-item-error {
            padding: 4px 0 0 16px;
        }
    }
    .tag-edit-panel {
        .s-form-item {
            margin: 0 8px 12px 0 !important;
        }
        .s-form-item-control-wrapper {
            margin-left: 0px !important;
        }
    }
    .tag-v2-panel-container {
        width: 800px !important;
    }
}

.locale-en {
    .dcgw-create-v2 .form-part-wrap .s-form-item-label {
        width: 126px;
    }
    .dcgw-bind-dcphy .dcphy,
    .dcgw-create-v2 .dcphy {
        margin-left: 30px !important;
    }
    .dcgw-create-v2 .resouce-group-select .resouce-group-select-main > label {
        width: 126px;
    }
    .dcgw-create-v2 .resouce-group-select .footer {
        margin-left: 126px;
    }
}
