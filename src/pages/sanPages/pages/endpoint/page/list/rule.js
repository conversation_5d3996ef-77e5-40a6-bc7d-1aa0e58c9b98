import {ContextService, EndpointStatus} from '@/pages/sanPages/common';
const AllRegion = ContextService.getEnum('AllRegion');
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
export default {
    createEndpoint: [
        {
            required: false,
            message: ''
        },
        {
            custom(data, options = {}) {
                if (options.free < 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: '服务网卡配额不足'
                        };
                    }
                    else {
                        return {
                            disable: true,
                            message: `服务网卡配额不足，<a href="${
                                '/quota_center/#/quota/apply/create?serviceType=ENDPOINT&region='
                            }${window.$context.getCurrentRegionId()}&cloudCenterQuotaName=${
                                'endpointVpcQuota'
                            }" target="_blank">去申请配额</a>`
                        };
                    }
                }
            }
        }
    ],
    release: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            isSingle: true,
            message: '不支持批量释放'
        }
    ],
    changeResource: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            isSingle: true,
            message: '不支持批量变更'
        }
    ],
    editTag: [
        {
            required: true,
            message: '请先选择实例对象'
        }
    ],
    endpointSinDisable: [
        {
            required: false
        },
        {
            custom(data) {
                if (window.$context.getCurrentRegionId() === AllRegion.SIN) {
                    return {
                        disable: true,
                        message: '新加坡地域资源售罄，请您切换到其他地域创建'
                    };
                }
            }
        }
    ],
    bindEip: [
        {
            required: false
        },
        {
            custom(data, options = {}) {
                if (options.eipBlackList) {
                    return {
                        disable: true,
                        message: `禁止当前操作，请参照
                            <a href="http://security.baidu.com/ssp/web/#/require/work/detail?id=105&from=page" target="_blank">《百度内部业务上百度公有云安全规范》</a>要求处置`,
                    };
                }
            }
        },
        {
            status: [EndpointStatus.AVAILABLE],
            message(data) {
                return '该状态下的服务网卡不支持绑定eip操作';
            }
        }
    ]
};
