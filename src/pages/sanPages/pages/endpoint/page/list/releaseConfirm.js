/*
 * @description: 确认弹窗
 */
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {eipStatus as Status} from '@/pages/sanPages/common/enum';
import {Dialog, Icon, Button, Alert, Table, Checkbox, Tooltip} from '@baidu/sui';
import './confirm.less';
/* eslint-disable */
const tpl = html`
    <template>
        <s-dialog class="confirm-snic" closeAfterMaskClick="{{false}}" open="{=open=}" title="{{title}}">
            <div>
                <s-alert skin="warning" s-if="type !== 'ipv6'">
                    温馨提示：释放服务网卡可能会影响正常访问，请确定是否要释放。若关联添加了解析，请到内网DNS服务中删除。你可以同时勾选下方选项释放该服务网卡绑定的后付费公网IP。
                </s-alert>
                <s-table columns="{{table.columns}}" datasource="{{table.datasource}}">
                    <div slot="c-status">
                        <span class="{{row | statusStyle}}">{{row | statusText}}</span>
                    </div>
                </s-table>
                <s-tooltip
                    trigger="{{releaseDis ? 'hover' : ''}}"
                    placement="top"
                    s-if="type !== 'ipv6'"
                    content="当前部分实例不满足关联释放的要求"
                >
                    <s-checkbox label="关联释放绑定的后付费公网IP" checked="{=eipRelease=}" disabled="{{releaseDis}}" />
                </s-tooltip>
            </div>
            <div slot="footer">
                <s-button on-click="close">取消</s-button>
                <s-button skin="primary" on-click="dialogConfirm">确定</s-button>
            </div>
        </s-dialog>
    </template>
`;
/* eslint-enable */
export default class BlbReleaseConfirm extends Component {
    static template = tpl;
    static components = {
        's-dialog': Dialog,
        's-icon': Icon,
        's-button': Button,
        's-alert': Alert,
        's-table': Table,
        's-checkbox': Checkbox,
        's-tooltip': Tooltip
    };
    static filters = {
        statusStyle(item) {
            let data = Status.fromValue(item.status);
            return data ? data.kclass : '';
        },
        statusText(item) {
            return Status.getTextFromValue(item.status) || '-';
        }
    };
    initData() {
        return {
            title: '释放前确认',
            content: '',
            open: true,
            table: {
                datasource: [],
                columns: [
                    {name: 'name', label: '待释放的实例'},
                    {name: 'shortId', label: '实例ID'},
                    {name: 'ovip', label: '内网IP'},
                    {name: 'status', label: '状态'}
                ]
            },
            eipRelease: false,
            releaseDis: true
        };
    }
    inited() {
        this.data.set('table.datasource', this.data.get('selectItems'));
        let eips = this.data
            .get('selectItems')
            .map(item => item.eip)
            .filter(item => item);
        let payload = {
            pageNo: 1,
            pageSize: eips.length,
            eips
        };
        if (eips.length && this.data.get('type') !== 'ipv6') {
            this.$http.getEipList(payload).then(res => {
                let eipList = [];
                res.result.forEach(item => {
                    if (item.eipType !== 'shared' && item.taskStatus === 'IDLE' && item.moveStatus !== 'MOVING') {
                        if (!(item.productType === 'prepay' && item.status !== 'expired')) {
                            eipList.push(item.eip);
                        }
                    }
                });
                if (eipList.length !== res.result.length) {
                    this.data.set('releaseDis', true);
                } else {
                    this.data.set('releaseDis', false);
                }
                this.data.set('eipList', eipList);
            });
        }
    }
    dialogConfirm() {
        this.fire('confirm', {
            eipRelease: this.data.get('eipRelease'),
            eipList: this.data.get('eipList')
        });
        this.data.set('open', false);
    }
    close() {
        this.fire('close');
        this.data.set('open', false);
    }
}
