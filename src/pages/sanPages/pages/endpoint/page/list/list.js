/**
 * @file network/endpoint/pages/List.js
 * <AUTHOR>
 */

import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {EipBindDialog} from '@baidu/bce-eip-sdk-san';
import {EipSDK} from '@baidu/bce-eip-sdk';
import {OutlinedRefresh, OutlinedPlus, OutlinedEditingSquare} from '@baidu/sui-icon';

import {EndpointStatus, PayType, eipLineType} from '@/pages/sanPages/common/enum';
import {DocService} from '@/pages/sanPages/common';
import {utcToTime, $flag as FLAG} from '@/pages/sanPages/utils/helper';
import rules from './rule';
import Confirm from '@/pages/sanPages/components/confirm';
import ReleaseConfirm from './releaseConfirm';
import Monitor from './bcmDetail';
import {columns} from './tableFields';
import {ResourceGroupDialog} from '@baidu/bce-resourcegroup-sdk-san';
import {ResourceGroupSDK} from '@baidu/bce-resourcegroup-sdk';
import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import DOCUMENT_ICON from '@/img/document_normal.svg?url';
import DOCUMENT_ICON_COLOR from '@/img/document_color.svg?url';
import testID from '@/testId';

import './style.less';

const {invokeSUI, invokeSUIBIZ, template, service, invokeAppComp, invokeComp} = decorators;

const tpl = html`
<div>
    <s-app-list-page class="{{klass}}">
        <div slot="pageTitle">
            <div class="vpc-eni-header">
                <div class="header-left">
                    <span class="title">{{title}}</span>
                    <vpc-select
                        class="vpc-select"
                        on-change="vpcChange" />
                </div>
                <div class="header-right">
                    <a
                        s-ref="introduce"
                        href="javascript:void(0)"
                        class="help-file function-introduce"
                        on-click="handleShowCard"
                        on-mouseenter="handleMouseEnter('introduce')"
                        on-mouseleave="handleMouseLeave('introduce')"
                        s-if="{{!FLAG.NetworkSupportXS}}">
                        <img class="s-icon" src="{{introduceIcon}}" />功能简介
                    </a>
                    <a href="{{DocService.endpoint_helpFile}}"
                        target="_blank"
                        class="help-file"
                        s-if="{{!FLAG.NetworkSupportXS}}">
                        <s-icon name="warning-new" />帮助文档
                    </a>
                </div>
            </div>
            <introduce-panel
                isShow="{{show}}"
                klass="{{'endpoint-peerconn-wrapper'}}"
                title="{{title}}"
                description="{{description}}"
                introduceOptions="{{introduceOptions}}"
                on-toggle="handleToggle($event)"
            ></introduce-panel>
        </div>
        <div class="list-page-tb-left-toolbar" slot="bulk">
            <s-tip-button
                disabled="{{accountState.disabled || endpointSinDisable.disable || iamPass.disable || createEndpointBtn.disable}}"
                isDisabledVisibile="{{true}}"
                placement="top"
                skin="primary"
                on-click="createEndpoint"
                data-test-id="${testID.snic.listCreateBtn}"
            >
                <div slot="content">
                    <!--bca-disable-next-line-->
                    {{accountState.message || endpointSinDisable.message || iamPass.message || createEndpointBtn.message | raw}}
                </div>
                <outlined-plus/>
                创建服务网卡
            </s-tip-button>
            <s-tip-button
                disabled="{{release.disable}}"
                isDisabledVisibile="{{true}}"
                placement="top"
                on-click="onRelease"
                class="left_class"
                data-test-id="${testID.snic.listReleaseBtn}"
            >
                <div slot="content">
                    <!--bca-disable-next-line-->
                    {{release.message | raw}}
                </div>
                释放
            </s-tip-button>
            <edit-tag s-else
                selectedItems="{{selectedItems}}"
                on-success="refresh"
                type="SNIC"
                class="left_class"
            ></edit-tag>
        </div>
        <div slot="filter">
            <div class="filter-buttons-wrap">
                <search-tag
                    s-ref="search"
                    serviceType="SNIC"
                    searchbox="{=searchbox=}"
                    on-search="onSearch"
                ></search-tag>
                <s-button
                    on-click="refresh" class="s-icon-button"><outlined-refresh class="icon-class"/></s-button>
            </div>
        </div>
        <s-table
            columns="{{table.columns}}"
            loading="{{table.loading}}"
            error="{{table.error}}"
            selection="{=table.selection=}"
            datasource="{{table.datasource}}"
            on-selected-change="tableSelected($event)"
            on-filter="onFilter"
            on-sort="onSort"
            data-test-id="${testID.snic.listTable}"
        >
            <div slot="empty">
                <table-empty
                    actionAuth="{{iamPass}}"
                    desc="暂无服务网卡。"
                    actionText="立即创建"
                    on-click="createEndpoint"
                />
            </div>
            <div slot="error">
                啊呀，出错了
                <a href="javascript:;" on-click="refresh">重新加载</a>
            </div>
            <div slot="c-endpointId">
                <span class="truncated" title="{{row.name}}">
                    <a
                        href="#/vpc/endpoint/detail?vpcId={{row.vpcId}}&shortId={{row.shortId}}"
                        data-testid="${testID.snic.listName}{{rowIndex}}"
                    >
                        {{row.name}}
                    </a>
                </span>
                <s-popover s-ref="{{'nameEdit'+rowIndex}}" placement="right" trigger="click"
                    class="edit-popover-class">
                    <div class="edit-wrap" slot="content">
                        <s-input
                            value="{=instance.name=}"
                            width="320"
                            placeholder="请输入名称"
                            on-input="onEditInput($event, rowIndex, 'name')"/>
                        <div class="edit-tip">支持大小写字母，数字，中文和"-_/.”以字母或者中文开头，不超过65个字</div>
                        <s-button skin="primary" s-ref="{{'nameEditBtn' + rowIndex}}"
                        disabled="{{true}}" on-click="editConfirm(row, rowIndex, 'name')">
                        确定</s-button>
                        <s-button on-click="editCancel(row, rowIndex, 'name')">取消</s-button>
                    </div>
                    <outlined-editing-square s-if="row.name" class="name-icon" on-click="beforeEdit(row, 'name')"/>
                </s-popover>
                <br>
                <span class="truncated">{{row.shortId}}</span>
                <s-clip-board text="{{row.shortId}}" class="name-icon"/>
            </div>
            <div slot="c-status">
                <span class="{{row.status | statusClass}}">{{row.status | statusText}}</span>
            </div>
            <div slot="c-vpcId">
                <span class="truncated">
                    <s-tooltip content="{{row.vpcName || '-'}}">
                        <a href="#/vpc/instance/detail?vpcId={{row.vpcId}}">{{row.vpcName || '-'}}</a>
                    </s-tooltip>
                </span>
                <br>
                <span class="truncated">
                    <s-tooltip content="{{row.vpcShortId || '-'}}">
                        {{row.vpcShortId || '-'}}
                    </s-tooltip>
                </span>
            </div>
            <div slot="c-subnet">
                <a class="truncated" href="#/vpc/subnet/detail?subnetId={{row.subnetId}}"
                    title="{{row.subnetName}}（{{row.subnetCidr}}）">
                    {{row.subnetName}}
                </a>
                <br>
                <span s-f="row.subnetCidr">
                    {{row.subnetCidr ? ('(' + row.subnetCidr +')') : ''}}
                    {{row.subnetIpv6Cidr ? ('(' + row.subnetIpv6Cidr +')') : ''}}
                </span>
            </div>
            <div slot="c-associationNum">
                <a href="javascript:void(0)" on-click="toSecurity(row)">
                    {{row.associationNum}}
                </a>
            </div>
            <!--企业云组织项目flag与资源组字段冲突，目前以公有云为主，私有云使用时需特殊处理-->
            <div slot="c-resourceGroups">
                <div s-if="row.resourceGroups && row.resourceGroups.length">
                    <p s-for="item in row.resourceGroups">
                        {{item.name}}
                    </p>
                </div>
                <span s-else>-</span>
            </div>
            <div slot="c-tag">
                <span s-if="!row.tags || row.tags.length < 1">
                    -
                </span>
                <div s-else s-for="item,index in row.tags">
                    <span s-if="index <= 1">
                        {{item.tagKey + ':' + item.tagValue}}
                    </span>
                    <div s-if="row.tags.length > 2 && index === 1">...</div>
                </div>
            </div>
            <div slot="c-endpointIp">
                <span>{{row.ovip}}</span>
                <span>/
                    <span s-if="row.bandwidth">{{row.bandwidth}}Mbps</span>
                    <span s-else>不限速</span>
                </span>
            </div>
            <div slot="c-eip">
                <span class="remote-text-wrap">
                <s-tooltip class="truncated" content="{{row.eip}}">
                    <a s-if="row.eip"
                        href="/eip/#/eip/instance/list?keywordType=INSTANCE_EIP&keyword={{row.eip}}"
                    >{{row.eip}}</a>
                </s-tooltip>
                <span s-else>-</span>
                <span>/{{row.eipType | getLineType}}</span>
                <span>/{{row.eipBandwidth || '-'}}Mbps</span>
                <span>
                <s-popover s-if="row.eipResourceStatus === 'STOPPED'" placement="top">
                    <div slot="content">
                        {{row | getTipContent}}
                    </div>
                    <s-icon class="tip-icon-wrap" name="warning-mark"/>
                </s-popover>
                <s-icon s-if="row.eip" name="unbind" on-click="unbindEip(row)">
                </s-icon>
                <s-tip-button s-else icon="bind"
                    class="bind_class"
                    skin="stringfy"
                    disabled="{{row.bindEip.disable}}"
                    isDisabledVisibile="{{true}}"
                    on-click="bindEip(row)"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{row.bindEip.message | raw}}
                    </div>
                </s-tip-button>
            </div>
            <div slot="c-service">
                {{row.service}}
            </div>
            <div slot="c-productType">
                {{row | getProductType}}
            </div>
            <div slot="c-description">
                <span class="truncated">{{row.description || '-'}}</span>
                <s-popover s-ref="{{'descEdit'+rowIndex}}" trigger="click" class="edit-popover-class">
                    <div class="edit-wrap" slot="content">
                        <s-input
                            value="{=instance.desc=}"
                            width="160"
                            placeholder="请输入描述"
                            on-input="onEditInput($event, rowIndex, 'desc')"/>
                        <div class="edit-tip">描述不能超过200个字符</div>
                        <s-button skin="primary" s-ref="{{'descEditBtn'+rowIndex}}" disabled="{{true}}"
                            on-click="editConfirm(row, rowIndex, 'desc')">确定</s-button>
                        <s-button on-click="editCancel(row, rowIndex, 'desc')">取消</s-button>
                    </div>
                    <outlined-editing-square class="name-icon" on-click="beforeEdit(row, 'desc')"/>
                </s-popover>
            </div>
            <div slot="c-createTime">{{row | createdTime}}</div>
            <div slot="c-operation" class="operation_class">
                <s-button skin="stringfy" on-click="onMonitor(row)" data-test-id="${testID.snic.listMonitor}{{rowIndex}}">监控</s-button>
                <s-button skin="stringfy" on-click="onUpdateBandWidth(row)" data-test-id="${testID.snic.listBandWidth}">内网带宽调整</s-button>
                <s-button skin="stringfy" on-click="changeResourceGroup(row)">编辑资源分组</s-button>
            </div>
        </s-table>
        <s-pagination
            s-if="{{pager.count}}"
            slot="pager"
            layout="{{'total, pageSize, pager, go'}}"
            pageSize="{{pager.size}}"
            total="{{pager.count}}"
            page="{{pager.page}}"
            resetPageWhenSizeChange="{{true}}"
            on-pagerChange="onPagerChange"/>
        <resource-group-dialog
            s-if="{{showResource}}"
            sdk="{{resourceSDK}}"
            resource="{{resource}}"
            on-success="oncommit"
            on-cancel="onCancel"/>
    </s-app-list-page>
</div>`;

@template(tpl)
@invokeAppComp
@invokeSUI
@invokeSUIBIZ
@invokeComp('@edit-tag', '@vpc-select', '@search-tag', '@introduce-panel', '@table-empty')
class VpcEndPointList extends Component {
    static components = {
        'resource-group-dialog': ResourceGroupDialog,
        'outlined-refresh': OutlinedRefresh,
        'outlined-plus': OutlinedPlus,
        'outlined-editing-square': OutlinedEditingSquare
    };
    static filters = {
        statusClass(value) {
            return EndpointStatus.fromValue(value).styleClass || 'status error';
        },
        statusText(value) {
            return value ? EndpointStatus.getTextFromValue(value) : '异常';
        },
        getProductType(item) {
            return item.productType ? PayType.getTextFromValue(item.productType) : '-';
        },
        createdTime(item) {
            return utcToTime(item.createTime);
        },
        getTipContent(item) {
            return item.eipProductType === 'prepay'
                ? '您的公网 IP 已到期，续费后才能使用。'
                : '您的公网 IP 已欠费，充值后才能使用。';
        },
        getLineType(type) {
            return eipLineType.getTextFromValue(type) || '-';
        }
    };
    initData() {
        // 企业云组织项目flag与资源组字段冲突resourceGroups，目前以公有云为主，私有云使用时需特殊处理
        return {
            DocService,
            klass: 'vpc-endpoint-list',
            title: '服务网卡',
            table: {
                loading: false,
                error: null,
                columns: columns,
                datasource: [],
                selection: {
                    mode: 'multi',
                    selectedIndex: [],
                    selectedItems: []
                }
            },
            searchbox: {
                keyword: '',
                placeholder: '请输入实例名称进行搜索',
                defaultLabel: '实例名称',
                keywordType: ['name'],
                keywordTypes: [
                    {value: 'name', text: '实例名称'},
                    {value: 'endpointId', text: '实例ID'},
                    {value: 'endpointIp', text: '实例内网IP'},
                    {value: 'subnetName', text: '所在子网'},
                    {value: 'tag', text: '标签'}
                ]
            },
            pager: {
                size: 10,
                page: 1,
                count: 0,
                datasource: [
                    {text: '10', value: 10},
                    {text: '20', value: 20},
                    {text: '50', value: 50},
                    {text: '100', value: 100}
                ]
            },
            resourceSDK: new ResourceGroupSDK(window.$http, window.$context),
            createEndpointBtn: {},
            endpointSinDisable: {},
            iamPass: {},
            show: true,
            introduceOptions: [{label: '访问公共服务'}, {label: '构建服务生态'}],
            introduceTitle: '服务网卡简介',
            description:
                '服务网卡（Service Network Interface Card，SNIC）将BOS等VPC外部服务映射到VPC内部，用户可以在VPC内或者混合云对端通过内网便捷、安全地访问这些服务。也可以与服务发布点进行连接，提供更加灵活的网络访问方式。',
            introduceIcon: INTRODUCE_ICON_COLOR,
            documentIcon: DOCUMENT_ICON,
            introduceEle: null,
            FLAG,
            accountState: {
                disabled: false,
                message: ''
            },
            urlQuery: getQueryParams()
        };
    }

    inited() {
        if (this.data.get('urlQuery.subnetName')) {
            this.data.set('searchbox.keywordType', ['subnetName']);
            this.data.set('searchbox.keyword', this.data.get('urlQuery.subnetName'));
        }
        let vpcId = this.data.get('urlQuery.vpcId');
        if (vpcId) {
            window.$storage.set('vpcId', vpcId);
        }
        let {endpointSinDisable} = checker.check(rules, []);
        this.data.set('endpointSinDisable', endpointSinDisable);
        this.getIamQuery();
        this.eipBlackList();
        const accountState = window.$storage.get('accountState');
        this.data.set('accountState', accountState);
    }
    attached() {
        window.$storage.get('showEnicIntroduce') === false && this.data.set('show', false);
        if (!this.$context.isVerifyUser()) {
            if (FLAG.NetworkSupportXS) {
                this.data.set('createEndpointBtn', {
                    disable: true,
                    message: '温馨提示：您还没有实名认证，请先完成实名认证'
                });
            } else {
                this.data.set('createEndpointBtn', {
                    disable: true,
                    message: '温馨提示：您还没有实名认证，请立即去<a href="/qualify/#/qualify/index">认证</a>'
                });
            }
        }
        let subnetId = this.data.get('urlQuery.subnetId');
        if (subnetId) {
            this.$http.getSubnetResourceDetail(subnetId).then(data => {
                this.data.set('subnetDetail', data);
                this.loadPage({subnetId});
            });
        } else {
            this.loadPage();
        }

        this.setSnicSecurityColumns();
        this.data.set('introduceEle', this.ref('introduce'));
    }

    setSnicSecurityColumns() {
        this.data.splice('table.columns', [
            6,
            0,
            {
                name: 'associationNum',
                label: '关联安全组',
                width: 90
            }
        ]);
    }

    eipBlackList() {
        return this.$http.vpcCommonWhiteList({id: 'EipBlackList'}).then(res => {
            this.data.set('eipBlackList', res);
        });
    }

    checkBindAble(data) {
        let eipBlackList = this.data.get('eipBlackList');
        return u.map(data, item => {
            let {bindEip} = checker.check(rules, item, '', {eipBlackList});
            return {
                ...item,
                bindEip
            };
        });
    }

    bindEip(item) {
        let dialog = new EipBindDialog({
            data: {
                listRequester: this.$http.getEipBindList.bind(this.$http),
                submitRequester: query =>
                    this.$http.endpointEipBind(item.shortId, 'bind', {
                        eip: query.eip
                    }),
                EipSDK: new EipSDK({client: window.$http, context: window.$context}),
                instanceType: 'ENDPOINT',
                instanceId: item.shortId
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => this.loadPage());
        dialog.on('create', e => {
            e.params = e.query;
            e.paramSeperator = '~';
            redirect(e);
        });
    }

    unbindEip(item) {
        let confirm = new Confirm({
            data: {
                title: '解绑EIP',
                content: `公网服务网卡需要配合EIP才能正常工作。您确认解绑${item.eip}吗？`
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http
                .endpointEipBind(item.shortId, 'unbind', {
                    eip: item.eip
                })
                .then(() => this.loadPage());
        });
    }

    getSearchCriteria() {
        const searchParam = this.ref('search') ? this.ref('search').getSearchCriteria() : {};
        const {pager, order, filter} = this.data.get('');
        let payload = {
            pageNo: pager.page,
            pageSize: pager.size,
            vpcId: window.$storage.get('vpcId')
        };
        if (!payload.vpcId) {
            delete payload.vpcId;
        }
        return {...payload, ...order, ...filter, ...searchParam};
    }

    vpcChange() {
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    loadPage(subnetId = {}) {
        const payload = this.getSearchCriteria();
        const param = {...payload, ...subnetId};
        this.data.set('param', param);
        this.resetTable();
        this.data.set('table.loading', true);
        if (param.keywordType === 'resGroupId') {
            param.keywordType = 'resourceGroupName';
        }
        if (!param.vpcId && !param.subnetId) {
            delete param.vpcId;
            return this.$http.getAllEndpoint(param).then(data => {
                this.data.set('table.loading', false);
                this.data.set('pager.count', data.totalCount);
                this.data.set('table.datasource', data.result);
            });
        }
        if (param.subnetId && !param.vpcId) {
            const subnetDetail = this.data.get('subnetDetail');
            param.vpcId = subnetDetail.vpcId;
        }
        this.$http
            .getEndpointList(param)
            .then(data => {
                let dataList = this.checkBindAble(data.result);
                this.data.set('pager.count', data.totalCount);
                this.data.set('table.datasource', dataList);
            })
            .finally(() => this.data.set('table.loading', false));
    }

    beforeEdit(row, type) {
        const keyMap = {
            name: 'name',
            desc: 'description'
        };
        this.data.set(`instance.${type}`, row[keyMap[type]]);
    }

    onEditInput(e, rowIndex, type) {
        let result = true;
        if (type === 'name') {
            const pattern = /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{0,64}$/;
            result = pattern.test(e.value);
        } else if (type === 'description') {
            result = e.value.length <= 200;
        }
        this.data.set(`instance.${type}`, e.value);
        this.ref(`${type}EditBtn${rowIndex}`).data.set('disabled', !result);
    }

    editConfirm(row, rowIndex, type) {
        const keyMap = {
            name: 'name',
            desc: 'description'
        };
        const payload = {
            endpointId: row.shortId,
            [keyMap[type]]: this.data.get(`instance.${type}`)
        };
        this.ref(`${type}EditBtn${rowIndex}`).data.set('disabled', true);
        this.$http.updateEndpoint(payload).then(() => {
            this.loadPage();
            this.ref(`${type}EditBtn${rowIndex}`).data.set('disabled', false);
        });
    }

    editCancel(row, rowIndex, type) {
        this.ref(`${type}Edit${rowIndex}`).data.set('visible', false);
    }

    toSecurity(row) {
        location.hash = `#/vpc/endpoint/security?vpcId=${row.vpcId}&shortId=${row.shortId}`;
    }

    tableSelected({value}) {
        const {release} = checker.check(rules, value.selectedItems, '');
        this.data.set('release', release);
        this.data.set('selectedItems', value.selectedItems);
    }

    // 搜索事件
    onSearch(e) {
        this.data.set('pager.page', 1);
        this.loadPage();
    }
    // 改变页数
    onPagerChange({value}) {
        this.data.set('pager.page', value.page);
        this.data.set('pager.size', value.pageSize);
        this.loadPage();
    }

    onRelease() {
        const selectItems = this.data.get('selectedItems');
        const endpointId = selectItems[0].shortId;
        let confirm = new ReleaseConfirm({
            data: {
                open: true,
                selectItems
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', value => {
            let payload = {};
            if (value.eipRelease && value.eipList.length) {
                payload.eips = value.eipList;
            }
            this.$http
                .deleteEndpoint(endpointId, payload)
                .then(() => this.loadPage())
                .catch(() => console.error);
        });
    }

    resetTable() {
        this.data.set('selectedItems', []);
        this.data.set('table.selection.selectedIndex', []);
        this.data.set('table.datasource', []);
    }

    refresh() {
        let param = this.data.get('param');
        let subnetId = this.data.get('urlQuery.subnetId');
        param.subnetId ? this.loadPage({subnetId}) : this.loadPage();
    }

    onMonitor(row) {
        // 不明原因，drawer不加nextTick就直接弹弹弹了。
        this.nextTick(() => {
            const dialog = new Monitor({
                data: {
                    instance: row
                }
            });
            dialog.attach(document.body);
        });
    }

    createEndpoint() {
        window.location.hash = '#/vpc/endpoint/create';
    }
    onRegionChange() {
        window.$storage.set('vpcId', '');
        location.reload();
    }

    onSort(e) {
        this.data.set('order', e.value);
        this.loadPage();
    }
    // 编辑资源分组确定后
    oncommit() {
        this.data.set('showResource', false);
        this.loadPage();
    }
    // 编辑资源分组取消后
    onCancel() {
        this.data.set('showResource', false);
    }
    // 编辑资源分组
    changeResourceGroup(row) {
        let resource = {
            resourceId: row.shortId,
            serviceType: 'SNIC'
        };
        this.data.set('resource', resource);
        this.data.set('showResource', true);
    }
    onFilter(e) {
        let name = e.field.name;
        let value = e[name] ? e[name] : '';
        this.data.set('filter.' + name, value);
        this.loadPage();
    }
    onUpdateBandWidth(row) {
        location.hash = `#/vpc/endpoint/upgrade?vpcId=${row.vpcId}&shortId=${row.shortId}`;
    }
    getIamQuery() {
        this.$http.getInterfaceIam({interfaceName: 'createEndpoint'}).then(res => {
            let message = '';
            !res.interfacePermission && (message += '创建服务网卡权限');
            !res.createOrderPermission && (message += message ? '、创建订单权限' : '创建订单权限');
            !res.payOrderPermission && (message += message ? '、支付订单权限' : '');
            if (!res.requestId && !res.masterAccount) {
                if (message) {
                    this.data.set('iamPass', {
                        disable: true,
                        message: `您没有${message}，请联系主用户添加`
                    });
                } else {
                    this.data.set('iamPass', {
                        disable: false,
                        message: ''
                    });
                }
            } else {
                this.data.set('iamPass', {
                    disable: false,
                    message: ''
                });
            }
        });
    }
    /**
     * @param {boolean} flag
     */
    handleToggle(flag) {
        this.data.set('show', flag);
        window.$storage.set('showEnicIntroduce', false);
        this.data.set('introduceIcon', INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#151B26';
    }
    handleShowCard() {
        if (!this.data.get('show')) {
            this.data.set('show', true);
            window.$storage.set('showEnicIntroduce', true);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type) {
        if (type === 'introduce') {
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
            const ele = this.data.get('introduceEle');
            ele.style.color = '#2468F2';
        } else {
            this.data.set('documentIcon', DOCUMENT_ICON_COLOR);
        }
    }
    handleMouseLeave(type) {
        const isShow = this.data.get('show');
        if (type === 'introduce') {
            this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
            const ele = this.data.get('introduceEle');
            !isShow && (ele.style.color = '#151B26');
        } else {
            this.data.set('documentIcon', DOCUMENT_ICON);
        }
    }
}
export default San2React(Processor.autowireUnCheckCmpt(VpcEndPointList));
