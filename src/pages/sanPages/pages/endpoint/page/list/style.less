.vpc-endpoint-list {
    // width: 0;
    flex: 1;
    .vpc-eni-header {
        display: flex;
        background-color: #fff;
        align-items: center;
        justify-content: space-between;
        .header-left {
            display: flex;
            align-items: center;
            .title {
                display: inline-block;
                margin: 0;
                color: #151b26;
                margin-right: 12px;
                height: 47px;
                line-height: 47px;
                font-weight: 500;
                font-size: 16px;
                margin-left: 16px;
            }
        }
        .header-right {
            display: flex;
            align-items: center;
            .help-file {
                margin-right: 16px;
                font-size: 12px;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                line-height: 20px;
                .s-icon {
                    position: relative;
                    top: -1px;
                    right: 4px;
                    margin-right: 0;
                    color: #2468f2;
                    font-size: 14px;
                }
            }
            .function-introduce {
                color: #2468f2;
            }
        }
    }
    .s-biz-page-toolbar {
        overflow: hidden;
    }
    .s-biz-page-tb-left {
        .icon-plus {
            color: #fff;
        }
    }
    .s-table {
        .s-table-row:hover {
            .name-icon {
                display: inline;
            }
        }
        .name-icon {
            display: none;
            font-size: 12px;
            fill: #2468f2;
        }
        .operation {
            a {
                display: block;
            }
        }
        .s-icon {
            font-size: 12px;
            color: #2468f2;
        }
        .truncated {
            overflow: hidden;
            text-overflow: ellipsis;
            -o-text-overflow: ellipsis;
            white-space: nowrap;
            zoom: 1;
            display: inline-block;
            max-width: 80%;
            vertical-align: middle;
        }
        .s-table-body {
            max-height: calc(~'100vh - 300px');
        }
        .s-table-empty {
            border-bottom: none;
        }
    }
    .filter-buttons-wrap {
        display: flex;
        align-items: center;

        .s-cascader {
            margin-right: 5px;
        }
        .s-cascader-value {
            vertical-align: middle;
            font-size: 12px;
            padding-top: 0;
            padding-bottom: 0;
            line-height: 30px;
        }
        .s-auto-compelete {
            .s-select {
                input {
                    width: 170px !important;
                }
            }
        }
        .button-item {
            margin-right: 5px;
        }
        .search-content {
            display: flex;
            align-items: center;
            position: relative;
            margin-right: 5px;
        }
        .s-icon.search {
            position: absolute;
            right: 5px;
            color: #615a5a;
        }
        .icon-fresh {
            margin-right: 5px;
        }
    }
    .operation_class {
        .s-button {
            padding: 0;
            margin-right: 12px;
        }
    }
}
