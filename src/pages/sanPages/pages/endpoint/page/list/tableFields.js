import {EndpointStatus} from '@/pages/sanPages/common/enum';

export const columns = [
    {
        name: 'endpointId',
        label: '网卡名称/ID',
        width: 160,
        fixed: 'left'
    },
    {
        name: 'status',
        label: '状态',
        width: 100,
        filter: {
            options: [
                {
                    text: '全部',
                    value: ''
                },
                ...EndpointStatus.toArray()
            ],
            value: ''
        }
    },
    {
        name: 'vpcId',
        label: '所在网络',
        width: 160
    },
    {
        name: 'subnet',
        label: '所在子网',
        width: 150
    },
    {
        name: 'endpointIp',
        label: '内网IP/带宽',
        width: 160
    },
    {
        name: 'eip',
        label: '公网IP/线路类型/带宽',
        width: 160
    },
    {
        name: 'service',
        label: '服务域名',
        width: 160
    },
    {
        name: 'tag',
        label: '标签',
        sortable: true,
        width: 150
    },
    {
        name: 'resourceGroups',
        label: '资源分组',
        width: 90
    },
    {
        name: 'productType',
        label: '支付方式',
        width: 150
    },
    {
        name: 'description',
        label: '描述',
        width: 140
    },
    {
        name: 'createTime',
        label: '创建时间',
        width: 140
    },
    {
        name: 'operation',
        label: '操作',
        width: 120,
        fixed: 'right'
    }
];
