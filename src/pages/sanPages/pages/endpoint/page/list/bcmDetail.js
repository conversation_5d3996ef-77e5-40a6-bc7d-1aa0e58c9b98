import u from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {BcmSDK} from '@baidu/new-bcm-sdk';
import {BcmChartPanel} from '@baidu/new-bcm-sdk-san';
import {Drawer} from '@baidu/sui';

import monitorConfig from '@/pages/sanPages/utils/monitorConfig';

const {EndPointMetrics} = monitorConfig;
const tpl = html`
<div>
    <s-drawer open="{{true}}" direction="right"
        class="vpn-list-drawer"
        size="{{450}}"
        otherClose="{{false}}"
    >
        <div slot="title">
            服务网卡名称：{{instance.name}}
            <a href="#/vpc/endpoint/monitor?shortId={{instance.shortId}}&vpcId={{instance.vpcId}}"
                target="_BLANK">
                查看更多</a>
        </div>
        <div class="monitor-wrap">
            <div s-for="item,index in chartConfig">
                <bcm-chart-panel
                    withFilter="{{false}}"
                    scope="{{item.scope}}"
                    dimensions="{{item.dimensions}}"
                    statistics="{{item.statistics}}"
                    title="{{item.title}}"
                    time="{{item.time}}"
                    height="{{230}}"
                    width="{{350}}"
                    options="{{item.options}}"
                    api-type="metricName"
                    period="{{item.period}}"
                    metrics="{{item.metrics}}"
                    unit="{{item.unit}}"
                    bitUnit="{{item.bitUnit}}"
                    sdk="{{bcmSdk}}">
                </bcm-chart-panel>
            </div>
        </div>
    </s-drawer>
</div>
`;


export default class EndPointListMonitor extends Component {
    static template = tpl;
    static components = {
        'bcm-chart-panel': BcmChartPanel,
        's-drawer': Drawer
    }

    initData() {
        return {
            chartConfig: [],
            bcmSdk: new BcmSDK({client: window.$http, context: window.$context})
        };
    }

    inited() {
        this.initChart();
    }

    initChart() {
        let instance = this.data.get('instance');
        let chartConfig = [];
        let options = {
            color: ['#2468f2', '#5FB333'],
            legend: {
                x: 'right',
                y: 'top'
            },
            dataZoom: {start: 0}
        };
        const metricObj = u.cloneDeep(EndPointMetrics);
        u.each(metricObj, item => {
            let config = {
                scope: 'BCE_SERVICE_ENDPOINT',
                time: '1h',
                period: 60,
                statistics: item.statistics || 'average',
                title: item.title,
                unit: item.unit,
                bitUnit: item.bitUnit,
                metrics: item.metrics,
                dimensions: `EndpointId:${instance.shortId}`,
                options
            };
            chartConfig.push(config);
        });
        this.data.set('chartConfig', chartConfig);
    }
}
