/**
 * @file network/eni/bcm/List.js
 * <AUTHOR>
 */

import {Component} from 'san';
import u from 'lodash';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import moment from 'moment';
import {BcmSDK} from '@baidu/new-bcm-sdk';
import {BcmChartPanel} from '@baidu/new-bcm-sdk-san';
import {OutlinedRefresh} from '@baidu/sui-icon';

import {EndpointStatus} from '@/pages/sanPages/common/enum';
import monitorConfig from '@/pages/sanPages/utils/monitorConfig';
import testID from '@/testId';
import './style.less';

const {EndPointMetrics, shortcutItems} = monitorConfig;

const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, withSidebar} = decorators;

const tpl = html`
    <div class="endpoint-detail-main-warp">
        <div class="{{klass}}">
            <div class="monitor-wrap" data-testid="${testID.snic.monitorDisplay}">
                <div class="monitor-item-box">
                    <h4>监控信息</h4>
                    <div class="alarm-info">
                        {{'报警信息：'}}
                        <span class="alarm-state">
                            <span class="status normal"></span>
                            {{alarm.okStateCount}}{{'项状态正常'}}
                        </span>
                        <span class="alarm-state">
                            <span class="status error"></span>
                            {{alarm.alarmStateCount}}{{'项状态异常'}}
                        </span>
                        <span class="alarm-state">
                            <span class="status warning"></span>
                            {{alarm.insufficientStateCount}}{{'项数据不足'}}
                        </span>
                        <s-button
                            class="alarm-detail"
                            track-id="ti_vpc_vpn_monitor_detail"
                            tack-name="弹性网卡/报警详情"
                            on-click="alarmDetail"
                        >
                            {{'报警详情'}}
                        </s-button>
                    </div>
                    <div class="button-wrap">
                        <span class="alarm-state">
                            <span>{{'时间：' }}</span>
                            <s-date-picker-date-range-picker
                                value="{=timeRange=}"
                                width="{{310}}"
                                mode="second"
                                range="{{range}}"
                                on-change="onTimeChange"
                                shortcut="{{shortcutItems}}"
                            />
                        </span>
                        <s-button class="s-icon-button" on-click="onTimeRefresh">
                            <outlined-refresh class="icon-class" />
                        </s-button>
                    </div>
                    <div class="endpoint-monitor-trends">
                        <div class="monitor-trend-box" s-for="item,index in chart">
                            <bcm-chart-panel
                                s-ref="alarm-chart-{{index}}"
                                withFilter="{{false}}"
                                scope="{{item.scope}}"
                                dimensions="{{item.dimensions}}"
                                statistics="{{item.statistics}}"
                                title="{{item.title}}"
                                options="{{options}}"
                                api-type="metricName"
                                startTime="{=startTime=}"
                                endTime="{=endTime=}"
                                period="{{monitorDefaultPeriod}}"
                                metrics="{{item.metrics}}"
                                unit="{{item.unit}}"
                                bitUnit="{{item.bitUnit}}"
                                width="{{'auto'}}"
                                height="{{230}}"
                                sdk="{{bcmSdk}}"
                            >
                            </bcm-chart-panel>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
`;

@template(tpl)
@withSidebar({active: 'vpc-endpoint-monitor'})
@invokeAppComp
@invokeSUI
@invokeSUIBIZ
class EndpointDetailMonitor extends Component {
    static components = {
        'bcm-chart-panel': BcmChartPanel,
        'outlined-refresh': OutlinedRefresh
    };
    static filters = {
        statusStyle(status) {
            let config = EndpointStatus.fromValue(status);
            return config ? config.styleClass : '';
        },
        statusText(status) {
            let config = EndpointStatus.fromValue(status);
            return config ? config.text : '';
        }
    };
    initData() {
        return {
            klass: 'vpc-endpoint-monitor',
            instance: {},
            alarm: {},
            chart: [],
            options: {
                color: ['#2468f2', '#5FB333'],
                legend: {
                    x: 'right',
                    y: 'top'
                },
                dataZoom: {start: 0}
            },
            timeRange: {
                begin: new Date(moment().subtract(1, 'hour').valueOf()),
                end: new Date(moment().valueOf())
            },
            range: {
                begin: new Date(moment().subtract(40, 'day').valueOf()),
                end: new Date(moment().valueOf())
            },
            startTime: moment().subtract(1, 'hour').utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
            endTime: moment().utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z',
            endOriginTime: moment().valueOf(),
            bcmSdk: new BcmSDK({client: window.$http, context: window.$context}),
            shortcutItems
        };
    }

    attached() {
        this.loadDetail();
        this.data.set('loading', false);
        this.watch('timeRange', timeRange => {
            this.onTimeChange({value: timeRange});
        });
    }

    onTimeChange({value}) {
        let startTime = moment(value.begin).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        let endTime = moment(value.end).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        let ms = moment(value.end) - moment(value.begin);
        let hourTime = Math.round(ms / 86400000);
        switch (true) {
            case hourTime <= 1:
                this.data.set('monitorDefaultPeriod', 60);
                break;
            case hourTime <= 3:
                this.data.set('monitorDefaultPeriod', 300);
                break;
            case hourTime <= 7:
                this.data.set('monitorDefaultPeriod', 600);
                break;
            case hourTime <= 14:
                this.data.set('monitorDefaultPeriod', 1800);
                break;
            case hourTime <= 40:
                this.data.set('monitorDefaultPeriod', 3600);
                break;
            default:
                break;
        }
        this.data.set('startTime', startTime);
        this.data.set('endTime', endTime);
        this.onRefresh();
    }

    onRefresh() {
        let chartConfig = this.data.get('chart');
        u.map(chartConfig, (item, i) => {
            this.ref(`alarm-chart-${i}`).loadMetrics();
        });
    }

    onTimeRefresh() {
        if (this.data.get('timeRange.end').valueOf() >= this.data.get('endOriginTime')) {
            this.data.set('timeRange.end', new Date(moment().valueOf()));
        } else {
            this.onRefresh();
        }
    }

    alarmDetail() {
        const region = window.$context.getCurrentRegionId();
        redirect(
            `/bcm/#/bcm/alarm/rule/list~scope=BCE_SERVICE_ENDPOINT&dimensions=EndpointId:${this.data.get('context').shortId}&region=${region}`
        );
    }

    loadDetail() {
        return this.$http
            .getEndpointDetail(
                {
                    endpointId: this.data.get('context').shortId
                },
                {'x-silent-codes': ['NoSuchObject']}
            )
            .then(data => {
                this.data.set('instance', data);
                this.loadAlarmInfo();
                this.initMonitor();
            });
    }

    loadAlarmInfo() {
        this.$http
            .getAlarmSummary({
                dimensions: 'EndpointId:' + this.data.get('context').shortId,
                scope: 'BCE_SERVICE_ENDPOINT'
            })
            .then(data => this.data.set('alarm', data));
    }

    initMonitor() {
        let chartConfig = [];
        const metricObj = u.cloneDeep(EndPointMetrics);
        u.each(metricObj, item => {
            let config = {
                scope: 'BCE_SERVICE_ENDPOINT',
                period: 60,
                statistics: item.statistics || 'average',
                title: item.title,
                unit: item.unit,
                bitUnit: item.bitUnit,
                metrics: item.metrics,
                dimensions: `EndpointId:${this.data.get('context').shortId}`
            };
            chartConfig.push(config);
        });
        this.data.set('chart', chartConfig);
    }
}
export default San2React(Processor.autowireUnCheckCmpt(EndpointDetailMonitor));
