.clearfix {
    *zoom: 1;
}
.clearfix:before,
.clearfix:after {
    display: table;
    content: '';
    line-height: 0;
}
.clearfix:after {
    clear: both;
}
.vpc-endpoint-detail {
    background: #fff !important;
    .s-icon {
        font-size: 12px;
        color: #2468f2;
    }
    .instance-not-found-class {
        height: 100%;
    }
    .instance-name {
        font-size: 16px;
        font-weight: 500;
        color: #151b26;
        padding-right: 12px;
        padding-left: 0px;
    }
    .s-detail-page-title {
        display: none;
    }
    .s-detail-page-content {
        padding: 24px;
        margin: 0px;
    }
    h4 {
        display: block;
        color: #151b26;
        line-height: 24px;
        font-weight: 500;
        margin-bottom: 16px;
        font-size: 16px;
        zoom: 1;
    }
    .content-item {
        width: 33%;
        margin-bottom: 16px;
        display: inline-block;
        .cell-title {
            display: inline-block;
            vertical-align: top;
            color: #5e626a;
            margin-right: 16px;
            width: 60px;
        }
        .cell-content {
            display: inline-block;
            color: #151a26;
            max-width: 80%;
            word-break: break-all;
            position: relative;
        }
    }
}
.vpc-endpoint-main-warp {
    width: 100%;
    .space-header {
        display: flex;
        align-items: center;
    }
    .s-tab-page {
        padding: 0;
        .bui-tab-header {
            height: auto !important;
            align-self: stretch;
            min-height: 600px;
        }
    }
}
