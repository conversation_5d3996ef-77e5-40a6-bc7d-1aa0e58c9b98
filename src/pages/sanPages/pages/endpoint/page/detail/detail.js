/**
 * @file vpc/endpoint/detail/Detail.js
 * <AUTHOR>
 */
import {Component} from 'san';
import {Icon} from '@baidu/sui';
import {ClipBoard} from '@baidu/sui-biz';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {OutlinedEditingSquare} from '@baidu/sui-icon';
import {San2React} from '@baidu/bce-react-toolkit';

import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import {utcToTime} from '@/pages/sanPages/utils/helper';
import {EndpointStatus} from '@/pages/sanPages/common/enum';
import Rule from '@/pages/sanPages/utils/rule';
import testID from '@/testId';
import './detail.less';

const {template, invokeComp, invokeSUI, invokeSUIBIZ, invokeAppComp} = decorators;
/* eslint-disable */
const tpl = html`<template>
    <s-app-detail-page class="{{klass}}">
        <h4>{{subTitle}}</h4>
        <ul>
            <li class="content-item">
                <label class="cell-title">ID：</label>
                <span class="cell-content">{{instance.shortId}}</span>
                <s-clip-board text="{{instance.shortId}}">
                    <s-icon name="copy" />
                </s-clip-board>
            </li>
            <li class="content-item">
                <label class="cell-title">内网IP：</label>
                <span class="cell-content">{{instance.ovip}}</span>
            </li>
            <li class="content-item">
                <label class="cell-title">名称：</label>
                <span class="cell-content" data-testid="${testID.snic.detailName}">
                    {{instance.name}}
                    <edit-popover value="{=instance.name=}" rule="{{NameRule}}" on-edit="updateName">
                        <outlined-editing-square class="name-icon" data-testid="${testID.snic.detailModifyName}" />
                    </edit-popover>
                </span>
            </li>
            <li class="content-item">
                <label class="cell-title">服务域名：</label>
                <span class="cell-content"> {{instance.service}} </span>
            </li>
            <li class="content-item">
                <label class="cell-title">所在网络：</label>
                <span class="cell-content"
                    ><a href="#/vpc/instance/list"
                        >{{vpcInfo.name}}<span s-f="vpcInfo.cidr">（{{vpcInfo.cidr}}）</span></a
                    ></span
                >
            </li>
            <li class="content-item">
                <label class="cell-title">所在子网：</label>
                <span class="cell-content">
                    <a href="#/vpc/subnet/list?vpcId={{vpcInfo.vpcId}}">
                        {{instance.subnetName}}
                        <span s-f="instance.subnetCidr"> （{{instance.subnetCidr}}） </span>
                    </a>
                </span>
            </li>
            <li class="content-item">
                <label class="cell-title">描述：</label>
                <span class="description cell-content">
                    {{instance.description || '-'}}
                    <edit-popover value="{=instance.description=}" rule="{{Rule.DESC}}" on-edit="updateDesc">
                        <outlined-editing-square class="name-icon" />
                    </edit-popover>
                </span>
            </li>
            <li class="content-item">
                <label class="cell-title">运行状态：</label>
                <span class="{{instance.status | statusStyle}} cell-content">{{instance.status | statusText}}</span>
            </li>
        </ul>
    </s-app-detail-page>
</template>`;
/* eslint-enable */

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@invokeComp('@edit-popover')
class EndpointDetail extends Component {
    REGION_CHANGE_LOCATION = '#/vpc/endpoint/list';
    static components = {
        's-icon': Icon,
        's-clip-board': ClipBoard,
        'outlined-editing-square': OutlinedEditingSquare
    };
    static filters = {
        timeFormat(time) {
            return utcToTime(time);
        },
        statusStyle(status) {
            let config = EndpointStatus.fromValue(status);
            return config ? config.styleClass : '';
        },
        statusText(status) {
            let config = EndpointStatus.fromValue(status);
            return config ? config.text : '';
        },
        resourceText(item) {
            if (item && item.resourceGroups && item.resourceGroups.length) {
                let str = [];
                item.resourceGroups.forEach(resourceGroup => {
                    str.push(_.escape(resourceGroup.resourceGroupName)); //eslint-disable-line
                });
                return str.join(' ');
            }
            return '-';
        }
    };
    initData() {
        return {
            klass: 'vpc-endpoint-detail',
            title: '',
            subTitle: '基本信息',
            remark: '',
            flag: FLAG,
            Rule: Rule.DETAIL_EDIT,
            NameRule: Rule.NAME_SUPPORT_CHINESE
        };
    }
    inited() {
        this.children = [];
    }
    attached() {
        this.loadDetail();
        this.loadVpcDetail();
    }
    loadVpcDetail() {
        const vpcId = this.data.get('context').vpcId;
        return this.$http
            .vpcInfo({
                vpcIds: [vpcId]
            })
            .then(data => {
                const result = data[vpcId] || {};
                this.data.set('vpcInfo', result);
            });
    }
    loadDetail() {
        return this.$http
            .getEndpointDetail(
                {
                    endpointId: this.data.get('context').shortId
                },
                {'x-silent-codes': ['NoSuchObject']}
            )
            .then(data => {
                let config = EndpointStatus.fromValue(data.status);
                data.styleClass = config.styleClass;
                data.statusText = config.text;
                this.data.set('instance', data);
            });
    }
    updateName(value) {
        this.$http
            .updateEndpoint({
                endpointId: this.data.get('instance.shortId'),
                name: value
            })
            .then(() => {
                this.data.set('instance.name', value);
                this.data.get('context')?.updateName();
            });
    }
    updateDesc(value) {
        this.$http
            .updateEndpoint({
                endpointId: this.data.get('instance.shortId'),
                description: value
            })
            .then(() => {
                this.data.set('instance.description', value);
            });
    }
    onRegionChange() {
        location.hash = '#/vpc/endpoint/list';
    }
}
export default San2React(Processor.autowireUnCheckCmpt(EndpointDetail));
