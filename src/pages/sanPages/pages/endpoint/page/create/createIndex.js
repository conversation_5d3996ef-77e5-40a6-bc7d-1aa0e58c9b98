import _ from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import './create';

const {template, invokeSUI, invokeAppComp, invokeComp} = decorators;

const tpl = html`
    <template>
        <div s-if="!indexLoading" style="width:100%" class="index-loading-class">
            <s-loading loading style="width:100%">
                <endpoint-create requestNeed="{{requestNeed}}" class="endpoint-create-component" />
            </s-loading>
        </div>
        <endpoint-create s-if="indexLoading" s-ref="vpcEndpoint" class="endpoint-create-component" />
    </template>
`;

@invokeComp('@endpoint-create')
@template(tpl)
@invokeSUI
@invokeAppComp
class CreateIndex extends Component {
    initData() {
        return {
            indexLoading: true,
            requestNeed: true
        };
    }

    inited() {
        window.$framework.events.on(window.$framework.EVENTS.AFTER_REGION_CHANGED, () => {
            let vpcEndpoint = this.ref('vpcEndpoint')?.data;
            const currentRegion = window.$context.getCurrentRegionId();
            if (currentRegion === vpcEndpoint?.get('formData')?.region && !vpcEndpoint?.get('loadNeed')) {
                return;
            }
            this.data.set('indexLoading', false);
            this.nextTick(() => {
                // 等一会再执行
                setTimeout(() => {
                    this.data.set('indexLoading', true);
                }, 100);
            });
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(CreateIndex));
