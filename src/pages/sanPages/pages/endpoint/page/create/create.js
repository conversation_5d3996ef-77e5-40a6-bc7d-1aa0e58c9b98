/**
 * @file network/pages/endpoint/page/Create.js
 * <AUTHOR>
 */
import u from 'lodash';
import {html, decorators, redirect, CreatePage, Processor} from '@baiducloud/runtime';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import {ShoppingCart, OrderConfirm, Protocol, TotalPrice} from '@baiducloud/bce-billing-sdk-san';
import {BillingSdk, OrderItem, UPDATE_STATUS} from '@baiducloud/bce-billing-sdk';
import Client from '@baiducloud/httpclient';
import {OutlinedQuestion} from '@baidu/sui-icon';
import {checker} from '@baiducloud/bce-opt-checker';
import Assist from '@/utils/assist';

import {NatIp, PayType, singleLineType, SUBPRODUCTTYPE} from '@/pages/sanPages/common/enum';
import {ContextService} from '@/pages/sanPages/common';
import {AutoComplete} from '@baidu/sui';

import Rule from '@/pages/sanPages/utils/rule';
import {ResourceGroupPanel} from '@baidu/bce-resourcegroup-sdk-san';
import {ResourceGroupSDK} from '@baidu/bce-resourcegroup-sdk';
import {TagEditPanel} from '@baidu/bce-tag-sdk-san';

import {checkIsInSubnet, isOnline} from '@/pages/sanPages/utils/common';
import {
    getUserId,
    $flag as FLAG,
    convertPrice,
    getMarksByStep,
    eipBlackTip,
    contextPipe,
    getVPCSupportRegion
} from '@/pages/sanPages/utils/helper';
import {StsConfig, enopointServiceMap} from '@/pages/sanPages/utils/config';
import rules from '../list/rule';
import zone from '@/pages/sanPages/utils/zone';
import testID from '@/testId';
import './create.less';

const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent} = decorators;
const AllRegion = ContextService.getEnum('AllRegion');
const kXhrOptions = {'X-silence': true};
const pageTitle = ['创建服务网卡', '确认订单'];

const defaultFormData = {
    type: NatIp.AUTO,
    payType: 'postpay',
    region: ContextService.getCurrentRegionId(),
    name: '',
    subnetId: '',
    description: '',
    serviceId: '',
    service: '',
    ip: '',
    bindPrivateZone: true,
    bandwidth: 500,
    enableEip: false,
    subProductType: 'netraffic',
    securityType: 'normal',
    serviceIdNew: '对象存储'
};
const formValidator = self => ({
    name: [
        {required: true, message: '请填写名称'},
        {pattern: /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{0,64}$/, message: '格式不符合要求'}
    ],
    vpcId: [{required: true, message: '请选择所在网络'}],
    subnetId: [{required: true, message: '请选择所在子网'}],
    serviceId: [{required: true, message: '请选择挂载服务'}],
    ip: [
        {pattern: Rule.IP, message: '格式不符合要求'},
        {
            validator: (rule, value, callback) => {
                const source = self.data.get('formData');
                if (source.type !== NatIp.CUSTOM) {
                    callback();
                    return;
                }
                if (!value) {
                    return callback('请填写指定IP地址');
                }
                if (!u.trim(value)) {
                    return callback('请填写指定IP地址');
                }
                const subnetList = source.subnetList;
                const subnet = u.find(subnetList, item => item.value === source.subnetId);
                if (subnet && !checkIsInSubnet(source.ip + '/32', subnet.cidr)) {
                    return callback('IP地址不在所选子网内');
                }
                callback();
            }
        }
    ],
    service: {
        validator: (rule, value, callback) => {
            const serviceRadioType = self.data.get('serviceRadioType');
            if (serviceRadioType === 'private') {
                if (!value) {
                    return callback('请填写服务域名');
                }

                if (!/[a-zA-Z\w]{1,65}$/.test(value)) {
                    return callback('域名格式不符合要求');
                }
            }
            callback();
        }
    },
    securityGroupUuids: {
        validator: (rule, value, callback) => {
            let securityType = self.data.get('formData').securityType;
            if (value.length === 0) {
                return callback('请选择安全组');
            }
            if (securityType === 'normal' && value && value.length > 10) {
                return callback('最多支持选择10个安全组');
            }
            callback();
        }
    },
    bandwidth: [{required: true, message: '请选择带宽'}],
    eipName: [
        {required: false, message: '请填写名称'},
        {pattern: /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{0,64}$/, message: '格式不符合要求'}
    ]
});
const tpl = html`
    <div class="vpc-endpoint-create">
        <s-app-create-page
            class="{{klass}}"
            backTo="{{pageNav.backTo}}"
            backToLabel="{{pageNav.backToLabel}}"
            pageTitle="{{pageNav.pageTitle}}"
        >
            <div class="s-step-block">
                <s-steps current="{{step + 1}}">
                    <s-steps-step s-for="i in pageNav.steps" title="{{i.title}}" />
                </s-steps>
            </div>
            <div class="form-wrap" s-if="step == 0">
                <s-form s-ref="form" rules="{{rules}}" data="{=formData=}">
                    <div class="form-part-wrap config-content">
                        <h4>付费及地域</h4>
                        <s-form-item label="{{'付费方式：'}}" prop="payType" class="config-paytype">
                            <span>{{payTypeText}}</span>
                        </s-form-item>
                        <s-form-item label="{{'当前地域：'}}" prop="region" class="create-region s-form-item-region">
                            <template slot="label" class="label_class">
                                {{'当前地域：'}}
                                <s-tip placement="top" class="inline-tip">
                                    <s-question class="question-class warning-class"></s-question>
                                    <span slot="content">
                                        如需修改购买其他区域产品，请{{isXSTip}}。
                                        <a
                                            class="assist-tip"
                                            href="javascript:void(0)"
                                            on-click="showAssist('region')"
                                            s-if="FLAG.NetworkSupportAI"
                                            >了解详情</a
                                        >
                                    </span>
                                </s-tip>
                            </template>
                            <s-radio-radio-group
                                enhanced
                                datasource="{{regionSource}}"
                                value="{=formData.region=}"
                                radioType="button"
                                on-change="onRegionChange"
                            />
                        </s-form-item>
                    </div>
                    <div class="form-part-wrap config-content">
                        <h4>{{'配置信息'}}</h4>
                        <s-form-item
                            label="{{'网卡名称：'}}"
                            s-if="!cmcLoading"
                            prop="name"
                            help="{{'支持大小写字母、数字、中文以及-_ /.特殊字符，必须以字母或者中文开头，长度1-65个字符。'}}"
                        >
                            <s-input
                                class="input-with-num"
                                on-input="handleNameInput('name', $event)"
                                width="{{320}}"
                                data-test-id="${testID.snic.createNameInput}"
                                value="{=formData.name=}"
                            />
                            <span class="input-num-statistics">{{nameLength+'/'+'65'}}</span>
                        </s-form-item>
                        <div class="owner-network">
                            <s-form-item label="{{'所在网络：'}}" prop="vpcId" s-if="!cmcLoading">
                                <s-select
                                    width="{{300}}"
                                    datasource="{{vpcs}}"
                                    filterable
                                    value="{=formData.vpcId=}"
                                    class="{{error ? 'error_select' : ''}}"
                                    data-test-id="${testID.snic.createVpcSelect}"
                                />
                                <div class="error_tip" s-if="{{error && !FLAG.NetworkSupportXS}}">
                                    您的私有网络{{formData.vpcMap[formData.vpcId]}}下服务网卡数量已经达到配额，
                                    如需更多服务网卡，可以通过
                                    <a
                                        href="{{ContextService.Domains.ticket}}/#/ticket/create"
                                        target="_blank"
                                        target="_blank"
                                        >工单</a
                                    >申请
                                </div>
                                <div class="error_tip" s-if="{{error && FLAG.NetworkSupportXS}}">
                                    您的私有网络{{formData.vpcMap[formData.vpcId]}}下服务网卡数量已经达到配额
                                </div>
                                <div class="subnetId-wrapper">
                                    <s-form-item label="" prop="subnetId" s-if="!cmcLoading">
                                        <s-select
                                            width="{{300}}"
                                            datasource="{{subnetDatasource}}"
                                            disabled="{{loading}}"
                                            value="{=formData.subnetId=}"
                                            on-change="handleSubnetChange"
                                            placeholder="请选择子网"
                                            data-test-id="${testID.snic.createSubnetSelect}"
                                        />
                                        <div slot="help">
                                            <div s-if="formData.subnetId" class="zone-tip">
                                                {{'当前' + currZone + '，可用IP共' + availableIPs + '个；' +
                                                '如需创建子网，您可以到'}}
                                                <a
                                                    s-if="!FLAG.NetworkSupportXS"
                                                    href="#/vpc/subnet/list"
                                                    target="_blank"
                                                    >私有网络-子网</a
                                                >
                                                <span s-else>私有网络-子网</span>
                                                {{'去创建'}}
                                            </div>
                                        </div>
                                    </s-form-item>
                                </div>
                            </s-form-item>
                        </div>
                        <s-form-item label="{{'IP地址：'}}" class="center_class">
                            <s-radio-radio-group
                                enhanced
                                datasource="{{IpDatasource}}"
                                value="{=formData.type=}"
                                radioType="button"
                            />
                            <s-form-item prop="ip" class="ip-item" s-if="{{formData.type === NatIp.CUSTOM}}">
                                <s-input
                                    width="{{290}}"
                                    placeholder="{{'请输入该子网内可用IP'}}"
                                    value="{=formData.ip=}"
                                    disabled="{{!formData.subnetId}}"
                                    on-input="InputChange"
                                />
                                <div s-if="messageShow" class="s-form-item-invalid-div">{{errorMessage}}</div>
                            </s-form-item>
                        </s-form-item>
                        <s-form-item
                            label="安全组："
                            class="endpoint-radio-btn"
                            prop="sercurityGroups"
                            s-if="!cmcLoading"
                        >
                            <s-radio-radio-group
                                enhanced
                                datasource="{{securityGroupDatasource}}"
                                radioType="button"
                                value="{=formData.securityType=}"
                                on-change="handleRadioTypeChange"
                            >
                            </s-radio-radio-group>
                            <p class="assist-tip-form">
                                <a href="#/vpc/security/list">安全组</a>
                                分为普通安全组和企业安全组。
                                <a
                                    class="assist-tip"
                                    href="javascript:void(0)"
                                    on-click="showAssist('security')"
                                    s-if="FLAG.NetworkSupportAI"
                                    >了解详情</a
                                >
                            </p>
                        </s-form-item>
                        <s-form-item
                            label=" "
                            prop="securityGroupUuids"
                            class="security-class-tip"
                            s-if="formData.securityType === 'normal' && !cmcLoading"
                        >
                            <s-select
                                width="{{300}}"
                                datasource="{{currentSecurityDatasource}}"
                                value="{=formData.securityGroupUuids=}"
                                disabled="{{securityLoading}}"
                                multiple="{{mode}}"
                            />
                            <p class="error_tip" s-if="errorShow">添加了RemoteSG的规则的安全组，不能关联服务网卡</p>
                        </s-form-item>
                        <s-form-item
                            label=" "
                            prop="securityGroupUuids"
                            class="security-class-tip"
                            s-if="formData.securityType !== 'normal' && !cmcLoading"
                        >
                            <s-select
                                width="{{300}}"
                                datasource="{{currentSecurityDatasource}}"
                                value="{=formData.securityGroupUuids=}"
                                disabled="{{securityLoading}}"
                            />
                        </s-form-item>
                        <s-form-item
                            label="{{'挂载服务：'}}"
                            class="center_class"
                            help="{{ serviceRadioType === 'private' ? '需要在服务发布点上开放当前用户访问服务发布点的权限。' : '' }}"
                        >
                            <template slot="label" class="label_class">
                                {{'挂载服务：'}}
                                <s-tip class="inline-tip" placement="top">
                                    <s-question class="question-class warning-class"></s-question>
                                    <span slot="content">
                                        可通过生成
                                        <a on-click="editPoint" href="javascript:void(0)">服务发布点</a>
                                        自定义服务域名
                                    </span>
                                </s-tip>
                            </template>
                            <div class="service-type">
                                <s-radio-radio-group
                                    enhanced
                                    datasource="{{typeRadioDataSource}}"
                                    value="{=serviceRadioType=}"
                                    radioType="button"
                                />
                            </div>
                        </s-form-item>
                        <s-form-item class="image-top-label">
                            <s-form-item
                                prop="serviceIdNew"
                                label="挂载服务："
                                class="image-wrap patch-image-style {{endpointServiceList.length === 1 ? 'image-service-none' : ''}}"
                                s-if="serviceRadioType === 'public' && serviceDatasource.length >= 1"
                            >
                                <ul class="system-wrap">
                                    <li
                                        s-for="item in endpointServiceList"
                                        class="system-item  {{item.name === formData.serviceIdNew ? 'system-selected' : ''}}"
                                        on-click="onServiceSelect(item)"
                                    >
                                        <s-tooltip content="{{item.name}}">
                                            <div class="system-version">
                                                <p class="system-osName">{{item.name}}</p>
                                                <p class="system-desc">{{item.desc}}</p>
                                            </div>
                                        </s-tooltip>
                                    </li>
                                </ul>
                            </s-form-item>
                            <s-form-item
                                class="service-domain"
                                s-if="serviceRadioType === 'public' && !cmcLoading"
                                prop="serviceId"
                            >
                                <s-select
                                    width="{{300}}"
                                    datasource="{{serviceDatasource}}"
                                    value="{=formData.serviceId=}"
                                    disabled="{{loading || serviceDatasource.length < 1}}"
                                />
                            </s-form-item>
                            <s-form-item
                                class="service-domain"
                                s-if="serviceRadioType === 'private' && !cmcLoading"
                                prop="service"
                            >
                                <s-autocomplete
                                    width="{{310}}"
                                    value="{=formData.service=}"
                                    datasource="{{serviceDomainList}}"
                                    placeholder="请选择本账户下或手动输入其他账户的服务发布点域名"
                                ></s-autocomplete>
                                <p class="error_tip" s-if="!showServiceAuthTip">
                                    当前服务不存在或没有服务权限，请创建服务或加上权限后重试。
                                </p>
                            </s-form-item>
                        </s-form-item>
                        <s-form-item label="{{'关联添加解析：'}}" class="center_class association-analysis">
                            <s-radio-radio-group
                                enhanced
                                datasource="{{associationAddAnalysis}}"
                                radioType="button"
                                value="{=formData.bindPrivateZone=}"
                            />
                            <span class="recommend">推荐</span>
                            <p class="assist-tip-form">
                                开启后在内网DNS服务中自动添加解析，可直接通过服务域名访问到所挂载的服务。内网DNS服务为付费产品，可按需使用。
                                <a
                                    class="assist-tip"
                                    href="javascript:void(0)"
                                    on-click="showAssist()"
                                    s-if="FLAG.NetworkSupportAI"
                                    >了解详情</a
                                >
                            </p>
                        </s-form-item>
                        <s-form-item
                            label="{{'内网带宽：'}}"
                            help="{{bandWidthHelp}}"
                            prop="bandwidth"
                            class="form-slider-wrap"
                        >
                            <s-slider
                                ref="drag"
                                parts="{{8}}"
                                width="450"
                                marks="{{bandwidth.marks}}"
                                max="{{bandwidth.max}}"
                                min="{{bandwidth.min}}"
                                value="{=formData.bandwidth=}"
                            />
                            <div class="dragger-input">
                                <s-input-number
                                    value="{=formData.bandwidth=}"
                                    max="{{bandwidth.max}}"
                                    min="{{bandwidth.min}}"
                                    on-input="bandWidthInputChange"
                                />
                                Mbps
                            </div>
                        </s-form-item>
                        <s-form-item label="{{'描述：'}}">
                            <s-input-text-area
                                width="{{400}}"
                                maxLength="{{200}}"
                                height="{{88}}"
                                placeholder="{{'请输入'}}"
                                value="{=formData.description=}"
                            />
                        </s-form-item>
                    </div>
                    <div class="form-part-wrap config-content">
                        <h4>{{'公网访问'}}</h4>
                        <s-form-item
                            label="{{'公网IP:'}}"
                            prop="payType"
                            help="如需公网访问请购买弹性公网IP，或购买成功后绑定已有弹性公网IP。"
                        >
                            <s-tooltip trigger="{{(!eipNotOpen && isSubUser) || eipBlackList ? 'hover' : ''}}">
                                <!--bca-disable-next-line-->
                                <div slot="content">
                                    <template s-if="!eipNotOpen && isSubUser">
                                        <p>该功能需要开通并购买弹性公网IP，当前登录的子账户没有开通产品服务的权限，</p>
                                        <p>请联系主账户授予开通服务权限，或联系主账户开通服务后授权使用。</p>
                                    </template>
                                    <template s-if="eipBlackList">
                                        <!--bca-disable-next-line-->
                                        <span s-html="{{eipBlackTip}}"></span>
                                    </template>
                                </div>
                                <s-radio-radio-group
                                    enhanced
                                    disabled="{{(!eipNotOpen && isSubUser) || eipBlackList}}"
                                    datasource="{{useEipWay}}"
                                    on-change="enableEipSwitch"
                                    value="{=formData.enableEip=}"
                                    radioType="button"
                                />
                            </s-tooltip>
                            <s-alert s-if="!eipNotOpen && !isSubUser"
                                >您的账号当前未开通该产品服务，关联使用该产品，会自动开通。</s-alert
                            >
                        </s-form-item>
                        <div s-if="formData.enableEip" class="config-content">
                            <s-form-item
                                prop="eipName"
                                help="大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头"
                                label="公网 IP 名称："
                                class="name-wrap"
                            >
                                <s-input
                                    type="text"
                                    class="input-with-num"
                                    value="{=formData.eipName=}"
                                    width="{{320}}"
                                    placeholder="{{'请输入名称'}}"
                                    limit-length="65"
                                    on-input="handleNameInput('eipName', $event)"
                                />
                                <span class="input-num-statistics">{{eipNameLength+'/'+'65'}}</span>
                            </s-form-item>
                            <s-form-item
                                label="线路类型："
                                prop="purchaseType"
                                help="{{purchaseTypeTipText}}"
                                s-if="inStaticWhite"
                            >
                                <template slot="label" class="label_class">
                                    {{'线路类型：'}}
                                    <s-tip
                                        class="inline-tip purchase-tip"
                                        placement="top"
                                        skin="question"
                                        s-if="purchaseTypeList.length>0"
                                        content="一旦创建，线路类型不可更改"
                                    />
                                </template>
                                <s-radio-radio-group
                                    enhanced
                                    class="inline-item"
                                    radioType="button"
                                    value="{=formData.purchaseType=}"
                                    datasource="{{purchaseTypeList}}"
                                    on-change="purchaseTypeChange"
                                >
                                </s-radio-radio-group>
                            </s-form-item>
                            <s-form-item required label="公网带宽：" prop="subProductType">
                                <s-radio-radio-group
                                    enhanced
                                    radioType="button"
                                    value="{=formData.subProductType=}"
                                    datasource="{{subProductTypeList}}"
                                    on-change="onSubProductTypeChange"
                                >
                                </s-radio-radio-group>
                            </s-form-item>
                            <s-form-item
                                required
                                label="{{formData.subProductType === 'netraffic' ? '带宽峰值：' : '公网带宽：'}}"
                                help="{{netrafficTip}}"
                                prop="bandwidthInMbps"
                                class="bandwidth-box"
                            >
                                <s-slider
                                    parts="{{8}}"
                                    marks="{{networkTrafficMarks}}"
                                    value="{=formData.bandwidthInMbps=}"
                                    length="{{450}}"
                                    min="{{eipMin}}"
                                    max="{{bandwidthInMbpsMax}}"
                                    on-input="onBandwidthChange($event, formData.subProductType)"
                                />
                                <div class="dragger-input">
                                    <s-input-number
                                        value="{=formData.bandwidthInMbps=}"
                                        min="{{eipMin}}"
                                        max="{{bandwidthInMbpsMax}}"
                                        on-input="onBandwidthChange($event, formData.subProductType)"
                                        on-change="onBandwidthChange($event, formData.subProductType)"
                                    />
                                    Mbps
                                </div>
                            </s-form-item>
                        </div>
                    </div>
                    <div class="form-part-wrap config-content">
                        <h4>{{'标签'}}</h4>
                        <s-form-item prop="tag" label="绑定标签：">
                            <tag-edit-panel
                                create
                                version="v2"
                                instances="{{defaultInstances}}"
                                options="{{tagListRequster}}"
                                s-ref="tagPanel"
                            />
                        </s-form-item>
                    </div>
                    <div class="resource-form-part-wrap">
                        <resource-group-panel
                            refreshAvailable="{{true}}"
                            sdk="{{resourceSDK}}"
                            on-change="resourceChange($event)"
                        />
                    </div>
                </s-form>
            </div>
            <order-confirm
                s-ref="orderConfirm"
                s-if="step === 1"
                sdk="{{newBillingSdk}}"
                items="{{items}}"
                theme="default"
                couponMergeBy="{{false}}"
                mergeBy="{{false}}"
                showAgreementCheckbox
            />
            <div slot="pageFooter" class="buybucket {{step === 1 ? 'bucket-footer-wrap' : ''}}">
                <div class="buybucket-widget" s-if="step === 0">
                    <div class="buybucket-container">
                        <s-tooltip
                            placement="top"
                            trigger="{{endpointSinDisable.disable || confirmTip ? 'hover' : ''}}"
                        >
                            <!--bca-disable-next-line-->
                            <span slot="content" s-html="{{endpointSinDisable.message || confirmTip}}"></span>
                            <s-button
                                skin="primary"
                                size="large"
                                on-click="onNext"
                                disabled="{{endpointSinDisable.disable || confirmDisable || secCheckDis || showServiceAuthTipLoading || cmcLoading}}"
                                data-test-id="${testID.snic.createConfirmOrder}"
                                >{{'确认订单'}}</s-button
                            >
                        </s-tooltip>
                        <s-button size="large" on-click="cancel" data-test-id="${testID.snic.createCancel}"
                            >取消</s-button
                        >
                        <shopping-cart
                            sdk="{{newBillingSdk}}"
                            on-reset="onReset"
                            addItemToCartAvailable="{{addItemToCartAvailable}}"
                            addItemToCartDisable="{=priceLoading=}"
                            on-change="onShoppingCartChange"
                            theme="default"
                            couponMergeBy="{{false}}"
                            mergeBy="{{false}}"
                        ></shopping-cart>
                    </div>
                </div>
                <div class="buybucket-widget" s-if="{{step === 1}}">
                    <billing-protocol s-if="!FLAG.NetworkSupportXS" s-ref="billingProtocol" />
                    <div class="footer-btn-wrap buybucket-container">
                        <s-button size="large" on-click="onPre" data-test-id="${testID.snic.createBackPre}"
                            >{{'上一步'}}</s-button
                        >
                        <s-button size="large" on-click="cancel" data-test-id="${testID.snic.createCancel}"
                            >取消</s-button
                        >
                        <s-button
                            skin="primary"
                            disabled="{{disableSub}}"
                            size="large"
                            on-click="onConfirm"
                            data-test-id="${testID.snic.createSubmitOrder}"
                        >
                            {{'提交订单'}}
                        </s-button>
                        <total-price class="total-price-wrap" sdk="{{newBillingSdk}}" />
                    </div>
                </div>
            </div>
        </s-app-create-page>
    </div>
`;

@template(tpl)
@asComponent('@endpoint-create')
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class CreateEndpoint extends CreatePage {
    REGION_CHANGE_LOCATION = '#/vpc/endpoint/list';
    static components = {
        'order-confirm': OrderConfirm,
        'shopping-cart': ShoppingCart,
        'resource-group-panel': ResourceGroupPanel,
        's-question': OutlinedQuestion,
        'tag-edit-panel': TagEditPanel,
        'billing-protocol': Protocol,
        'total-price': TotalPrice,
        's-autocomplete': AutoComplete
    };
    initData() {
        return {
            FLAG,
            pageNav: {
                pageTitle: '创建服务网卡',
                backTo: '/network/#/vpc/endpoint/list',
                backToLabel: '返回',
                steps: [
                    {title: '基本配置', key: 'SELECT_CONFIG'},
                    {title: '确认订单', key: 'ORDER'}
                ]
            },
            step: 0,
            rules: formValidator(this),
            formData: {
                vpcId: '',
                ...defaultFormData
            },
            subnetDatasource: [],
            initSubnetDatasource: [],
            IpDatasource: NatIp.toArray(),
            useEipWay: [
                {text: '购买弹性公网IP', value: true},
                {text: '暂不需要', value: false}
            ],
            serviceDatasource: [],
            initServiceDatasource: [],
            loading: false,
            NatIp,
            payTypeSource: [
                {
                    label: PayType.getTextFromValue(PayType.POSTPAY),
                    value: PayType.POSTPAY
                }
            ],
            regionSource: [],
            price: [],
            accountPurchaseValidation: null,
            items: [],
            sdk: {},
            ContextService,
            error: false,
            typeRadioDataSource: [
                {label: '公共服务', value: 'public'},
                {label: '自定义服务', value: 'private'}
            ],
            serviceRadioType: 'public',
            resourceSDK: new ResourceGroupSDK(window.$http, window.$context),
            endpointSinDisable: {},
            bandwidth: {
                max: 1000,
                min: 1,
                value: 1
            },
            networkTraffic: 1000,
            eipMin: 1,
            allNetwork: {},
            securityGroupDatasource: [
                {
                    text: '普通安全组',
                    value: 'normal'
                },
                {
                    text: '企业安全组',
                    value: 'enterprise'
                }
            ],
            mode: true,
            endpointServiceList: [],
            associationAddAnalysis: [
                {
                    label: '开启',
                    value: true
                },
                {
                    label: '关闭',
                    value: false
                }
            ],
            nameLength: 0,
            eipNameLength: 0,
            currZone: '',
            availableIPs: 0,
            priceLoading: true,
            addItemToCartAvailable: false,
            loadNeed: false,
            errorShow: false,
            eipBlackTip,
            defaultInstances: [
                {
                    tags: [
                        {
                            tagKey: '默认项目',
                            tagValue: ''
                        }
                    ]
                }
            ],
            tagListRequster: this.tagListRequster.bind(this),
            urlQuery: getQueryParams(),
            serviceDomainList: [],
            showServiceAuthTip: true
        };
    }
    static computed = {
        'confirmDisable'() {
            let accountPurchaseValidation = this.data.get('accountPurchaseValidation');
            const totalQuota =
                (this.data.get('instanceQuota')?.regionTotalBandwidthQuota?.usedBandwidth || 0) +
                this.data.get('formData.bandwidth');
            const quota = this.data.get('instanceQuota')?.regionTotalBandwidthQuota?.totalBandwidth || 4000;
            if (accountPurchaseValidation && !accountPurchaseValidation.status) {
                return true;
            } else if (!ContextService.isVerifyUser()) {
                return true;
            }
            if (totalQuota > quota) {
                return true;
            }
            return false;
        },
        'confirmTip'() {
            let accountPurchaseValidation = this.data.get('accountPurchaseValidation');
            if (!ContextService.isVerifyUser()) {
                if (FLAG.NetworkSupportXS) {
                    return '温馨提示：您还没有实名认证，请先完成实名认证';
                } else {
                    return '温馨提示：您还没有实名认证，请立即去<a href="/qualify/#/qualify/index">认证</a>';
                }
            }
            if (accountPurchaseValidation && !accountPurchaseValidation.status) {
                return accountPurchaseValidation.failReason
                    ? accountPurchaseValidation.failReason +
                          '，请及时<a href="/finance/#/finance/account/recharge" target="_blank">充值</a>'
                    : '';
            }
            const totalQuota =
                (this.data.get('instanceQuota')?.regionTotalBandwidthQuota?.usedBandwidth || 0) +
                this.data.get('formData.bandwidth');
            const quota = this.data.get('instanceQuota')?.regionTotalBandwidthQuota?.totalBandwidth || 4000;
            if (totalQuota > quota) {
                return `当前地域下带宽总配额不足，请提交<a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">工单</a>`;
            }
        },
        'bandwidth.marks'() {
            let min = this.data.get('bandwidth.min');
            let max = this.data.get('bandwidth.max');
            return getMarksByStep(max, min, 4);
        },
        // 带宽
        'networkQuota'() {
            let allNetwork = this.data.get('allNetwork');
            return allNetwork ? allNetwork.networkQuotaDynamic || 200 : 200;
        },
        // 流量
        'networkTraffic'() {
            let allNetwork = this.data.get('allNetwork');
            return allNetwork ? allNetwork.networkTrafficDynamic || 1000 : 1000;
        },
        'networkTrafficMarks'() {
            let max = this.data.get('bandwidthInMbpsMax');
            let min = this.data.get('eipMin');
            return getMarksByStep(max, min, 4);
        },
        'bandWidthHelp'() {
            const totalQuota =
                this.data.get('instanceQuota')?.regionTotalBandwidthQuota?.usedBandwidth +
                this.data.get('formData.bandwidth');
            const quota = this.data.get('instanceQuota')?.regionTotalBandwidthQuota?.totalBandwidth || '-';
            return `单地域所有服务网卡的累计购买带宽峰值总和/配额：${totalQuota}Mbps/${quota}Mbps`;
        },
        'purchaseTypeTipText'() {
            let formData = this.data.get('formData');
            let bandWidth =
                formData.subProductType === 'netraffic'
                    ? this.data.get('networkTraffic')
                    : this.data.get('networkQuota');
            if (formData.purchaseType === 'BGP') {
                return `可购买带宽上限${bandWidth}Mbps，适用于多IP小带宽场景，提供5G DDoS基础原生防护，更大DDoS防护能力需要切换为DDoS高防IP`;
            } else if (formData.purchaseType === 'BGP_S') {
                return `起购带宽100Mbps，可购买带宽上限${bandWidth}Mbps，适用于少IP大带宽场景，并支持流量突发服务包实现百G级别的全出口原生防护无需切换DDoS高防IP`;
            } else if (formData.purchaseType === 'BackToOrigin') {
                return '可用于DDoS高防回源IP和CDN回源IP场景，不支持公网访问，仅收取IP租用费。';
            }
            return '';
        },
        'subProductTypeList'() {
            let inBgpsNetrafficWhite = this.data.get('inBgpsNetrafficWhite');
            let purchaseType = this.data.get('formData.purchaseType');
            let subProductList = [
                {
                    text: '按带宽付费',
                    value: SUBPRODUCTTYPE.BANDWIDTH
                },
                {
                    text: '按流量付费',
                    value: SUBPRODUCTTYPE.NETRAFFIC
                }
            ];
            if ((!inBgpsNetrafficWhite && purchaseType === 'BGP_S') || singleLineType.includes(purchaseType)) {
                return subProductList.filter(item => item.value !== SUBPRODUCTTYPE.NETRAFFIC);
            }
            if (purchaseType === 'Custom') {
                return subProductList.filter(item => item.value !== SUBPRODUCTTYPE.BANDWIDTH);
            }
            return subProductList;
        },
        'bandwidthInMbpsMax'() {
            const subProductType = this.data.get('formData.subProductType');
            const eipPurchaseType = this.data.get('formData.purchaseType');
            const {networkTrafficDynamic, networkQuotaDynamic, networkQuotaStatic, networkTrafficStatic} =
                this.data.get('allNetwork');

            const isNetraffic = subProductType === SUBPRODUCTTYPE.NETRAFFIC;
            const isBgp = !eipPurchaseType || eipPurchaseType === 'BGP';
            const isBgps = eipPurchaseType === 'BGP_S';

            if (isBgp) {
                return isNetraffic ? +networkTrafficDynamic : +networkQuotaDynamic;
            } else if (isBgps && !isNetraffic) {
                return networkQuotaStatic ? +networkQuotaStatic : 5000;
            }
            return isNetraffic ? +networkTrafficStatic : +networkQuotaStatic;
        },
        'payTypeText'() {
            return '按量付费';
        },
        'isXSTip'() {
            return !FLAG.NetworkSupportXS ? '前往主导航进行切换' : '在顶栏重新选择区域';
        }
    };

    inited() {
        if (this.data.get('urlQuery.resourceId')) {
            this.data.set('cmcLoading', true);
            Promise.all([
                this.$http.queryResourceList({
                    pageNo: 1,
                    pageSize: 10000,
                    serviceType: 'endpoint',
                    sourceCloud: 'baiduyun',
                    sourceResourceId: this.data.get('urlQuery.resourceId')
                })
            ])
                .then(res => {
                    let nowDetail = '';
                    try {
                        nowDetail = JSON.parse(res[0].result[0]?.sourceResourceDetail);
                    } catch (error) {}
                    this.data.set('originDetail', res[0].result[0] || {});
                    let formData = {
                        ...defaultFormData,
                        name: nowDetail.name,
                        description: nowDetail.description
                    };
                    this.data.set('defaultInstances', [
                        {
                            tags: nowDetail.tags || [
                                {
                                    tagKey: '默认项目',
                                    tagValue: ''
                                }
                            ]
                        }
                    ]);
                    if (sessionStorage.getItem('fromCmcEndpointData')) {
                        let instanceFormData = JSON.parse(
                            sessionStorage.getItem('fromCmcEndpointData') ||
                                window.$storage.get('fromCmcEndpointData') ||
                                '{}'
                        );
                        instanceFormData.vpcId && (formData.vpcId = instanceFormData.vpcId);
                        instanceFormData.subnetId && (formData.subnetId = instanceFormData.subnetId);
                        instanceFormData.securityType && (formData.securityType = instanceFormData.securityType);
                        instanceFormData.securityGroupUuids &&
                            (formData.securityGroupUuids = instanceFormData.securityGroupUuids);
                        instanceFormData.serviceIdNew && (formData.serviceIdNew = instanceFormData.serviceIdNew);
                        instanceFormData.serviceId && (formData.serviceId = instanceFormData.serviceId);
                        instanceFormData.type && (formData.type = instanceFormData.type);
                        instanceFormData.ip && (formData.ip = instanceFormData.ip);
                        formData.region = instanceFormData.region || ContextService.getCurrentRegionId();
                        instanceFormData.service && (formData.service = instanceFormData.service);
                        if (instanceFormData.serviceRadioType === 'private') {
                            this.data.set('serviceRadioType', 'private');
                        }
                    }
                    this.data.set('formData', formData);
                    formData.name && this.data.set('formData.name', formData.name);
                    formData.description && this.data.set('formData.description', formData.description);
                    formData.vpcId && this.data.set('formData.vpcId', formData.vpcId);
                    formData.subnetId && this.data.set('formData.subnetId', formData.subnetId);
                    formData.securityType && this.data.set('formData.securityType', formData.securityType);
                    formData.securityGroupUuids &&
                        this.data.set('formData.securityGroupUuids', formData.securityGroupUuids);
                    formData.serviceIdNew && this.data.set('formData.serviceIdNew', formData.serviceIdNew);
                    formData.serviceId && this.data.set('formData.serviceId', formData.serviceId);
                    formData.type && this.data.set('formData.type', formData.type);
                    formData.ip && this.data.set('formData.ip', formData.ip);
                    formData.region && this.data.set('formData.region', formData.region);
                    formData.service && this.data.set('formData.service', formData.service);
                })
                .finally(() => {
                    this.data.set('cmcLoading', false);
                });
        }
        this.data.set('isSubUser', window.$context.isSubUser());
        const client = new Client({}, {}.$context);
        // 创建实例
        const sdk = new BillingSdk({
            client,
            AllRegion: window.$context.getEnum('AllRegion'),
            context: contextPipe(this)
        });
        this.data.set('newBillingSdk', sdk);
        // let array = [];
        // endpointServiceList.forEach(item => {
        //     let serviceAll = item.value.find(i => i.region === window.$context.getCurrentRegionId());
        //     if (serviceAll?.serviceList?.length) {
        //         array.push(item);
        //     }
        // });
        // this.data.set('endpointServiceList', array);
        // this.data.set('formData.serviceIdNew', array[0]?.name);
        this.setRegionSource();
        if (this.data.get('requestNeed')) {
            return;
        }
        this.getAllQuota();
        this.loadServiceList();
        this.watch('formData.bandwidth', value => {
            this.getPrice();
        });
        this.watch('serviceRadioType', value => {
            this.getPrice();
        });
        this.watch('formData.vpcId', v => {
            this.loadSubnets();
            this.loadSecurityList();
            this.loadEnterpriseSecurityList();
            this.getEndpointQuota().then(() => {
                this.loadEndpointList();
            });
        });

        this.watch('formData.securityGroupUuids', async () => {
            this.data.set('errorShow', false);
            if (this.data.get('formData').securityType !== 'normal') return;
            await this.checkSecurity();
        });
        this.getVpcList();
        this.setRegionSource();
        this.getPrice();
        this.accountPurchaseValidation();
        this.checkIsOpenService();
        let {endpointSinDisable} = checker.check(rules, []);
        this.data.set('endpointSinDisable', endpointSinDisable);
        this.getPurchaseTypeList();
        this.eipBlackList();
        this.getDomainServiceList();
        this.watch(
            'formData.service',
            u.debounce(value => {
                this.serviceChange({value});
            }, 300)
        );
    }

    attached() {
        this.data.set('eipNotOpen', window.$storage.get('blbSts'));
        if (this.data.get('requestNeed')) {
            return;
        }
    }

    cancel() {
        location.hash = '#/vpc/endpoint/list';
    }

    checkIsOpenService() {
        this.$http.isOpenService().then(res => this.data.set('isOpenService', res));
    }

    loadSecurityList() {
        this.data.set('securityLoading', true);
        !sessionStorage.getItem('fromCmcEndpointData') && this.data.set('formData.securityGroupUuids', []);
        this.$http
            .securityListV3({
                pageNo: 1,
                pageSize: 1000,
                vpcId: this.data.get('formData.vpcId')
            })
            .then(data => {
                let datasource = u.map(data.result || [], item => ({value: item.id, text: item.name}));
                this.data.set('securityDatasource', datasource);
                this.data.set('currentSecurityDatasource', datasource);
                this.nextTick(() => {
                    const defaultSecurity = datasource.filter(item => item.text === '默认安全组');
                    defaultSecurity.length > 0 &&
                        !sessionStorage.getItem('fromCmcEndpointData') &&
                        this.data.set('formData.securityGroupUuids', [defaultSecurity[0].value]);
                });
            })
            .finally(() => this.data.set('securityLoading', false));
    }

    async getVpcList() {
        const data = await this.$http.vpcList();
        const vpcs = u.map(data, item => ({text: `${item.name}（${item.cidr}）`, value: item.vpcId}));
        this.data.set('vpcs', vpcs);
        this.setInitData(vpcs);
    }

    setInitData(vpcs) {
        const vpcMap = {};
        u.each(vpcs, item => {
            vpcMap[item.value] = item.text;
        });
        this.data.set('formData.vpcMap', vpcMap);
        const vpcId = this.data.get('urlQuery.vpcId');
        if (u.find(vpcs, {value: vpcId})) {
            this.data.set('formData.vpcId', vpcId);
        } else if (vpcs.length) {
            if (sessionStorage.getItem('fromCmcEndpointData')) {
                return;
            }
            this.data.set('formData.vpcId', vpcs[0].value);
        }
    }

    setRegionSource() {
        const region = ContextService.getCurrentRegionId();
        this.data.set('regionSource', getVPCSupportRegion(window));
        this.data.set('formData.region', region);
    }

    loadSubnets() {
        let vpcId = this.data.get('formData.vpcId');
        this.data.set('loading', true);
        this.$http
            .getSubnetList({vpcId, attachVm: false})
            .then(data => {
                let datasource = [];
                u.each(data, item =>
                    datasource.push({
                        value: item.subnetId,
                        text: item.name + (item.cidr ? '（' + item.cidr + '）' : ''),
                        cidr: item.cidr,
                        az: item.az
                    })
                );

                this.data.set('subnetDatasource', datasource);
                this.data.set('initSubnetDatasource', datasource);
                this.data.set('formData.subnetList', datasource);
                this.data.set('loading', false);

                if (sessionStorage.getItem('fromCmcEndpointData')) {
                    return;
                }

                this.nextTick(() => {
                    this.data.set('formData.subnetId', '');
                });
            })
            .catch(() => this.data.set('loading', false));
    }

    loadServiceList() {
        this.data.set('loading', true);
        let serviceIdNew = this.data.get('formData.serviceIdNew');
        let endpointServiceList = this.data.get('endpointServiceList');
        if (endpointServiceList.length) {
            let list = endpointServiceList.find(item => item.name === serviceIdNew)?.value;
            list = list.find(item => item.region === window.$context.getCurrentRegionId())?.serviceList || [];
            this.data.set('serviceDatasource', list);
            this.data.set('formData.serviceId', list[0].value);
            this.data.set('loading', false);
        } else {
            this.$http
                .getServiceList()
                .then(data => {
                    let datasource = [];
                    let endpointServiceList = [];
                    u.each(data, item => {
                        if (item.status === 'available') {
                            datasource.push({
                                value: item.shortId,
                                text: item.service
                            });
                            let serviceMap = enopointServiceMap.find(i => i.value === item.serviceType);
                            let serviceIndex = endpointServiceList.findIndex(i => i.serviceType === item.serviceType);
                            if (serviceIndex === -1 && item.serviceType) {
                                endpointServiceList.push({
                                    name: serviceMap ? serviceMap.name : item.serviceType,
                                    desc: serviceMap ? serviceMap.desc : '',
                                    serviceType: item.serviceType
                                });
                            }
                        }
                    });
                    if (endpointServiceList.length) {
                        this.data.set('endpointServiceList', endpointServiceList);
                        let newServiceType = endpointServiceList[0].serviceType;
                        if (!sessionStorage.getItem('fromCmcEndpointData')) {
                            this.data.set('formData.serviceIdNew', endpointServiceList[0].name);
                        } else {
                            newServiceType = endpointServiceList.find(
                                i => i.name === this.data.get('formData.serviceIdNew')
                            )?.serviceType;
                        }
                        let array = data.filter(item => item.serviceType === newServiceType);
                        array = array.map(item => ({
                            value: item.shortId,
                            text: item.service
                        }));
                        this.data.set('serviceDatasource', array);
                        if (!sessionStorage.getItem('fromCmcEndpointData')) {
                            this.data.set('formData.serviceId', array[0]?.value);
                        }
                    } else {
                        this.data.set('serviceDatasource', datasource);
                        this.data.set('formData.serviceId', datasource[0].value);
                    }
                    this.data.set('initServiceDatasource', data);
                    this.data.set('loading', false);
                })
                .catch(() => this.data.set('loading', false));
        }
    }

    // 验证购买资格
    accountPurchaseValidation() {
        return this.$http
            .purchaseValidation(
                {
                    serviceType: 'SNIC',
                    productType: 'postpay'
                },
                kXhrOptions
            )
            .then(data => {
                this.data.set('accountPurchaseValidation', data);
            });
    }

    getEndpointQuota() {
        let vpcId = this.data.get('formData.vpcId');
        if (!vpcId) {
            return Promise.resolve();
        }
        return this.$http.getEndpointQuota({vpcId}).then(data => {
            this.data.set('formData.quotaFree', data.free);
            this.data.set('quota', data.total);
        });
    }

    getEipPayload() {
        let eipConfig = [];
        const formData = this.data.get('formData');
        const region = ContextService.getCurrentRegionId();
        if (formData.subProductType === 'bandwidth') {
            let flavor = [{name: 'bandwidth', value: formData.bandwidthInMbps + 'M', scale: 1}];
            if (formData.purchaseType !== 'BGP') {
                if (formData.purchaseType === 'Static') {
                    flavor.push({
                        name: 'subServiceType',
                        value: 'static',
                        scale: 1
                    });
                } else {
                    flavor.push({
                        name: 'subServiceType',
                        value: formData.purchaseType,
                        scale: 1
                    });
                }
            } else {
                flavor.push({
                    name: 'subServiceType',
                    value: 'default',
                    scale: 1
                });
            }
            let bandwidthConfig = {
                amount: 1,
                count: 1,
                chargeItem: 'RunningTimeMinutesOfBandwidth',
                duration: 1,
                flavor,
                productType: 'postpay',
                region: window.$context.getCurrentRegionId(),
                scene: 'NEW',
                type: 'NEW',
                serviceType: 'EIP',
                subProductType: 'RunningTimeMinutesOfBandwidth',
                timeUnit: 'minute',
                configName: 'EIP带宽费用',
                configDetail: [
                    {
                        label: '地域',
                        value: AllRegion.getTextFromValue(region)
                    },
                    {
                        label: '线路类型',
                        value: this.data.get('purchaseTypeList').filter(item => item.value === formData.purchaseType)[0]
                            .text
                    },
                    {
                        label: '购买配置',
                        value:
                            formData.subProductType === 'netraffic'
                                ? `公网带宽峰值${formData.bandwidthInMbps}Mbps`
                                : `公网带宽${formData.bandwidthInMbps}Mbps`
                    },
                    {
                        label: '购买配额',
                        value: 1
                    }
                ],
                timeUnit: 'MINUTE',
                unitText: '分钟',
                serviceName: 'EIP实例'
            };
            eipConfig = [bandwidthConfig];
        } else if (formData.subProductType === 'netraffic') {
            let idleConfig = {
                amount: 1,
                count: 1,
                duration: 1,
                productType: 'postpay',
                region: window.$context.getCurrentRegionId(),
                scene: 'NEW',
                type: 'NEW',
                serviceType: 'EIP',
                timeUnit: 'minute',
                flavor: [{name: 'subServiceType', value: 'default', scale: 1}],
                configName: 'IP闲置费用：EIP绑定云资源后不收取闲置费用',
                configDetail: [
                    {
                        label: '地域',
                        value: AllRegion.getTextFromValue(region)
                    },
                    {
                        label: '线路类型',
                        value: this.data.get('purchaseTypeList').filter(item => item.value === formData.purchaseType)[0]
                            .text
                    },
                    {
                        label: '购买配置',
                        value:
                            formData.subProductType === 'netraffic'
                                ? `公网带宽峰值${formData.bandwidthInMbps}Mbps`
                                : `公网带宽${formData.bandwidthInMbps}Mbps`
                    },
                    {
                        label: '购买配额',
                        value: 1
                    }
                ],
                chargeItem:
                    formData.purchaseType === 'BGP' || formData.purchaseType === 'Custom'
                        ? 'BgpIdleMinutes'
                        : 'StaticIdleMinutes',
                subProductType:
                    formData.purchaseType === 'BGP' || formData.purchaseType === 'Custom'
                        ? 'BgpIdleMinutes'
                        : 'StaticIdleMinutes',
                unitText: '分钟',
                serviceName: 'EIP实例'
            };
            let netConfig = {
                amount: 1024 * 1024 * 1024,
                count: 1,
                duration: 1,
                productType: 'postpay',
                region: window.$context.getCurrentRegionId(),
                scene: 'NEW',
                type: 'NEW',
                serviceType: 'EIP',
                timeUnit: 'MINUTE',
                flavor: [{name: 'subServiceType', value: 'default', scale: 1}],
                configName: 'EIP流量费用',
                unitText: 'GB'
            };
            if (formData.purchaseType === 'BGP') {
                netConfig.chargeItem = 'WebOutBytes';
                netConfig.subProductType = 'WebOutBytes';
            } else if (formData.purchaseType === 'Static' || formData.purchaseType === 'BGP_S') {
                netConfig.chargeItem = 'StaticWebOutBytes';
                netConfig.subProductType = 'StaticWebOutBytes';
            }
            eipConfig = [idleConfig, netConfig];
        }
        return eipConfig;
    }

    enableEipSwitch(e) {
        if (e.value && !this.data.get('eipNotOpen')) {
            const roleName = 'BceServiceRole_BLB';
            const isOnline = window.$context.isOnline();
            this.$http
                .iamStsRoleActivate(
                    {
                        roleName,
                        accountId: window.$context.getUserId(),
                        policyId: isOnline ? 'fa53c3d5549d43e69cf6ee754932d191' : '76cec5dfd58f4c20a95a718f81c64b7c',
                        serviceId: isOnline ? '9159cff30acf4b18b6b9d33c86c29c09' : 'b46290268d9f45648d69320d9d5b8cb1'
                    },
                    {region: AllRegion.BJ}
                )
                .then(res => this.data.set('eipNotOpen', true));
        }
        this.setBandwidth();
        this.data.set('formData.enableEip', e.value);
        this.getPrice();
    }

    getPrice() {
        const {formData, newBillingSdk} = this.data.get();
        const region = ContextService.getCurrentRegionId();
        newBillingSdk.clearItems();
        let configs = {
            serviceType: 'SNIC',
            serviceName: '服务网卡',
            productType: 'postpay',
            type: 'NEW',
            scene: 'NEW',
            region: window.$context.getCurrentRegionId(),
            chargeItem: 'RunningTimeMinutes',
            flavor: this.getFlavor(),
            subServiceType: 'default',
            timeUnit: 'MINUTE',
            configDetail: [
                {
                    label: '地域',
                    value: AllRegion.getTextFromValue(region)
                },
                {
                    label: '内网带宽',
                    value: `${formData.bandwidth}Mbps`,
                    showInConfirm: false
                }
            ],
            configName: 'SNIC实例费用'
        };
        const configItems = new OrderItem(configs);
        let bucketItems = [configItems];
        this.data.set('items', bucketItems);
        newBillingSdk.addItems(bucketItems);
        if (
            (formData.serviceIdNew !== '对象存储' && formData.serviceIdNew !== '百度网盘') ||
            this.data.get('serviceRadioType') === 'private'
        ) {
            let configsNew = {
                serviceType: 'SNIC',
                serviceName: '服务网卡',
                productType: 'postpay',
                type: 'NEW',
                scene: 'NEW',
                region: window.$context.getCurrentRegionId(),
                chargeItem: 'NetworkTotalBytes',
                flavor: [],
                timeUnit: 'MINUTE',
                unitText: 'GB',
                amount: 1024 * 1024 * 1024,
                configName: 'SNIC流量费用'
            };
            let configsNewItems = new OrderItem(configsNew);
            bucketItems.push(configsNewItems);
            this.data.set('items', bucketItems);
            newBillingSdk.addItems(bucketItems);
        }
        if (formData.enableEip) {
            let eipPayload = this.getEipPayload();
            if (eipPayload.length === 1) {
                let bandwidthConfigItems = new OrderItem(eipPayload[0]);
                bucketItems.push(bandwidthConfigItems);
            } else {
                let idleConfigItems = new OrderItem(eipPayload[0]);
                let netConfigItems = new OrderItem(eipPayload[1]);
                bucketItems.push(idleConfigItems);
                bucketItems.push(netConfigItems);
            }
            this.data.set('items', bucketItems);
            newBillingSdk.addItems(bucketItems);
        }
    }
    getFlavor() {
        let flavor = [
            {
                name: 'subServiceType',
                value: 'default',
                scale: 1
            },
            {name: 'serviceCard', value: 'default', scale: 1}
        ];
        return flavor;
    }

    onNext() {
        this.ref('form')
            .validateFields()
            .then(async () => {
                try {
                    await this.ref('tagPanel').validate(false);
                } catch (error) {
                    return;
                }
                let formData = this.data.get('formData');
                formData.bindPrivateZone && (await this.activeZone());
                if (formData.quotaFree <= 0) {
                    this.data.set('error', true);
                } else {
                    this.data.set('error', false);
                }
                if (this.data.get('error') || this.data.get('errorShow') || !this.data.get('showServiceAuthTip')) {
                    return;
                }
                let type = this.data.get('formData.type');
                let tagData = await this.ref('tagPanel').getTags();
                this.data.set('tagData', tagData || {});
                if (type === NatIp.CUSTOM) {
                    let params = {
                        vpcId: this.data.get('formData.vpcId'),
                        vpcPrivateIpAddresses: this.data.get('formData.ip')
                    };
                    await this.$http.vpcCheckPrivateIp(params).then(res => {
                        if (res.vpcPrivateIpAddresses && res.vpcPrivateIpAddresses.length) {
                            this.data.set('messageShow', true);
                            let ipValidMessage = res.vpcPrivateIpAddresses[0].privateIpAddressType;
                            this.data.set('errorMessage', 'IP被' + ipValidMessage + '占用');
                        } else {
                            this.data.set('messageShow', false);
                        }
                    });
                    if (!this.data.get('messageShow')) {
                        this.setBillingSdk();
                        this.data.set('step', 1);
                        this.data.set('pageNav.pageTitle', pageTitle[1]);
                    }
                } else {
                    this.setBillingSdk();
                    this.data.set('step', 1);
                    this.data.set('pageNav.pageTitle', pageTitle[1]);
                }
            });
    }

    onPriceChange({value}, type) {
        this.data.set(`formData.${type}`, value);
        this.getPrice();
    }

    onPre() {
        this.data.set('step', 0);
        this.data.set('pageNav.pageTitle', pageTitle[0]);
        this.getPrice();
    }

    loadEndpointList() {
        if (!this.data.get('formData.vpcId')) {
            return;
        }
        this.data.set('loading', true);
        this.$http
            .getEndpointList({
                pageNo: 1,
                pageSize: this.data.get('quota'),
                vpcId: this.data.get('formData.vpcId')
            })
            .then(data => this.data.set('endpointList', data.result))
            .finally(() => this.data.set('loading', false));
    }

    async onConfirm() {
        !FLAG.NetworkSupportXS && (await this.ref('billingProtocol').validateAgreement());
        this.data.set('disableSub', true);
        let {newBillingSdk, items, formData} = this.data.get();
        let noFlow = formData.serviceIdNew !== '对象存储' && formData.serviceIdNew !== '百度网盘';
        if (this.data.get('isOpenService') && this.data.get('formData.bindPrivateZone')) {
            this.$http.openZoneDns();
        }
        let paymentMethod = [];
        if (items[0].couponId) {
            paymentMethod.push([{type: 'coupon', values: [items[0].couponId]}]);
        }
        let params = {
            items: [
                {
                    config: this.getConfig(),
                    paymentMethod: items[0].couponId ? [{type: 'coupon', values: [items[0].couponId]}] : []
                }
            ],
            paymentMethod: []
        };
        if (formData.enableEip) {
            params.items.push({
                config: this.getEipConfig(),
                paymentMethod: noFlow
                    ? items[2].couponId
                        ? [{type: 'coupon', values: [items[2].couponId]}]
                        : []
                    : items[1].couponId
                      ? [{type: 'coupon', values: [items[1].couponId]}]
                      : []
            });
            if (noFlow ? items[2].couponId : items[1].couponId) {
                paymentMethod.push([{type: 'coupon', values: [noFlow ? items[2].couponId : items[1].couponId]}]);
            }
        }
        try {
            let confirmUrl = '/api/snic/order/confirm/new?orderType=NEW';
            const data = await this.$http.newConfirmOrder(confirmUrl, params);
            let originDetail = this.data.get('originDetail') || {};
            let editPayload = {
                sourceCloud: originDetail.sourceCloud,
                sourceRegion: originDetail.sourceRegion,
                sourceResourceId: originDetail.sourceResourceId, // 以上字段必填，唯一确定一个资源
                upstreamId: originDetail.upstreamId,
                targetRegion: formData.region,
                targetResourceId: '',
                targetResourceOrder: data.orderId,
                targetResourceDetail: JSON.stringify({
                    ...params.items[0]?.config,
                    nowTargetStep: 0
                }),
                status: 'migrated'
            };
            if (this.data.get('urlQuery.resourceId')) {
                this.$http.editResource({resourceList: [editPayload]}).then(async res => {
                    let url = '';
                    try {
                        const info = await newBillingSdk.checkPayInfo(data);
                        url = info.url + '&fromService=SNIC';
                        info.url && (location.href = url);
                    } catch (info) {
                        // 跳转到相应页面
                        url = info.url + '&fromService=SNIC';
                        info.url && (location.href = url);
                    }
                });
            } else {
                let url = '';
                try {
                    const info = await newBillingSdk.checkPayInfo(data);
                    url = info.url + '&fromService=SNIC';
                    info.url && (location.href = url);
                } catch (info) {
                    // 跳转到相应页面
                    url = info.url + '&fromService=SNIC';
                    info.url && (location.href = url);
                }
            }
        } catch (err) {}
    }

    getConfig() {
        const formData = this.data.get('formData');
        const payload = {
            orderServiceType: 'SNIC',
            region: ContextService.getCurrentRegionId(),
            name: formData.name,
            productType: formData.payType,
            vpcId: formData.vpcId,
            subnetId: formData.subnetId,
            description: formData.description,
            securityGroupIds: formData.securityGroupUuids,
            bindPrivateZone: formData.bindPrivateZone,
            billing: {
                billingMethod: formData.payType
            },
            bandwidth: formData.bandwidth,
            resourceGroupId: formData.resourceGroupId
        };

        if (formData.type === NatIp.CUSTOM) {
            payload.ovip = formData.ip;
        }
        let serviceRadioType = this.data.get('serviceRadioType');
        if (serviceRadioType === 'private') {
            payload.serviceType = 'specific';
            payload.service = formData.service;
        } else {
            payload.serviceId = formData.serviceId;
            const selectedItem = u.find(this.data.get('serviceDatasource'), item => item.value === formData.serviceId);
            payload.service = selectedItem ? selectedItem.text : '';
        }
        if (formData.securityType === 'enterprise') {
            payload.esgIds = [formData.securityGroupUuids];
            delete payload.securityGroupIds;
        }
        payload.tags = this.data.get('tagData');
        return payload;
    }

    getEipConfig() {
        const formData = this.data.get('formData');
        const payload = {
            orderServiceType: 'EIP',
            productType: formData.payType,
            region: ContextService.getCurrentRegionId(),
            name: formData.eipName,
            bandwidthInMbps: formData.bandwidthInMbps,
            resourceGroupId: formData.resourceGroupId,
            subProductType: formData.subProductType,
            purchaseType: formData.purchaseType,
            billing: {
                billingMethod: formData.payType
            },
            serviceType: 'EIP'
        };
        return payload;
    }

    initFormData() {
        const formData = this.data.get('formData');
        this.data.set('formData', {...formData, ...defaultFormData});
    }

    onReset() {
        this.initFormData();
        this.getPrice();
    }

    onCancel() {
        location.hash = '#/vpc/endpoint/list';
    }

    InputChange() {
        this.data.set('messageShow', false);
    }
    editPoint() {
        redirect('/blb/#/blb/service/list');
    }
    resourceChange({value}) {
        this.data.set('formData.resourceGroupId', value.data.groupId);
    }
    activeZone() {
        const roleName = StsConfig.DNS.roleName;
        return this.$http
            .iamStsRoleQuery({roleName}, {region: AllRegion.BJ})
            .then(data => {
                if (!data || (data && !data.name)) {
                    this.$http.iamStsRoleActivate(
                        u.extend(
                            {
                                roleName,
                                accountId: getUserId()
                            },
                            isOnline() ? StsConfig.DNS.online : StsConfig.DNS.sandbox
                        ),
                        {region: AllRegion.BJ}
                    );
                }
            })
            .catch(error => {});
    }
    setBillingSdk() {
        const {formData, newBillingSdk} = this.data.get();
        const price = this.data.get('items');
        const bucketItems = this.data.get('items')[0];
        let noFlow = formData.serviceIdNew !== '对象存储' && formData.serviceIdNew !== '百度网盘';
        const detailPrice =
            (+price[0]?.unitPrice * 60 || 0).toFixed(3) +
            '/小时（SNIC实例费用）' +
            (noFlow ? '、' + (+price[1]?.unitPrice || 0).toFixed(3) + '/GB（SNIC流量费用）' : '');
        bucketItems.updatePriceError(detailPrice, null);
        noFlow && newBillingSdk.removeItems([price[1]]);
        const eipBucketItems = noFlow ? this.data.get('items')[2] : this.data.get('items')[1];
        if (formData.enableEip) {
            let eipDetailPrice;
            if (formData.subProductType === 'netraffic') {
                eipDetailPrice =
                    convertPrice(noFlow ? +price[2].unitPrice : +price[1].unitPrice).getPriceOfMinute() +
                    '/分钟（IP闲置费用）、' +
                    convertPrice(noFlow ? +price[3].unitPrice : +price[2].unitPrice).getPriceOfMinute() +
                    '/GB（EIP流量费用）';
            } else {
                eipDetailPrice =
                    convertPrice(noFlow ? +price[2].unitPrice : +price[1].unitPrice).getPriceOfMinute() +
                    '/分钟（EIP带宽费用）';
            }
            eipBucketItems.updatePriceError(eipDetailPrice, null);
            formData.subProductType === 'netraffic' && newBillingSdk.removeItems(noFlow ? [price[3]] : [price[2]]);
        }
    }
    bandWidthInputChange(e) {
        let val = +e.value;
        let bandwidth = this.data.get('bandwidth');
        if (isNaN(val)) {
            return;
        }
        val = val < bandwidth.min ? bandwidth.min : val;
        val = val > bandwidth.max ? bandwidth.max : val;
        this.data.set('formData.bandwidth', val);
    }

    // 获取总配额
    getAllQuota() {
        this.$http.enicBandWidthQuota().then(res => {
            this.data.set('instanceQuota', res);
            this.data.set('formData.bandwidth', Math.floor((1 + res?.perSnicBandwidthMaxSize || 1000) / 2));
            this.data.set('bandwidth.max', res?.perSnicBandwidthMaxSize || 1000);
        });
    }

    getStaticWhiteList() {
        return new Promise(resolve => {
            this.$http
                .eipGetStaticRouteWhiteList(['Static', 'ChinaTelcom', 'ChinaUnicom', 'ChinaMobile'])
                .then(result => {
                    let staticWhiteList = [];
                    if (result.Static) {
                        staticWhiteList.push({text: '静态BGP', value: 'Static'});
                    }
                    if (result.ChinaMobile) {
                        staticWhiteList.push({text: '移动单线', value: 'ChinaMobile'});
                    }
                    if (result.ChinaTelcom) {
                        staticWhiteList.push({text: '电信单线', value: 'ChinaTelcom'});
                    }
                    if (result.ChinaUnicom) {
                        staticWhiteList.push({text: '联通单线', value: 'ChinaUnicom'});
                    }
                    resolve(staticWhiteList);
                })
                .catch(() => {
                    resolve([]);
                });
        });
    }

    getBGP_SWhite() {
        return this.$http
            .getBgpsWhiteList()
            .then(res => {
                return res;
            })
            .catch(() => {
                Promise.resolve();
            });
    }

    getEipbgpBlackList() {
        return this.$http.eipbgpBlackList().then(result => {
            this.data.set('inbgpBlack', result);
        });
    }

    getBgpsNetrafficWhiteList() {
        return this.$http
            .getBgpsNetrafficWhiteList()
            .then(res => {
                this.data.set('inBgpsNetrafficWhite', res.inWhiteList);
            })
            .catch(err => {
                this.data.set('inBgpsNetrafficWhite', false);
            });
    }

    getPurchaseTypeList() {
        let all = [
            this.getStaticWhiteList(),
            this.getBGP_SWhite(),
            this.getEipbgpBlackList(),
            this.getBgpsNetrafficWhiteList(),
            this.getNetwork()
        ];
        return Promise.all(all).then(result => {
            let staticWhiteList = result[0];
            let inBgp_sWhite = result[1];
            let bgpBlack = result[3];
            let quotaType2quota = result[4];
            if (inBgp_sWhite) {
                staticWhiteList.unshift({text: '增强型BGP', value: 'BGP_S'});
            }
            if (!bgpBlack) {
                let bgpType = {text: '标准型BGP', value: 'BGP'};
                staticWhiteList.unshift(bgpType);
            }
            this.data.set('inStaticWhite', staticWhiteList.length > 0);
            this.data.set('purchaseTypeList', staticWhiteList);
            if (staticWhiteList && staticWhiteList.length !== 0) {
                this.data.set('formData.purchaseType', staticWhiteList[0].value);
            }

            let networkQuota = quotaType2quota.eipBandwidthQuotaByBand ? quotaType2quota.eipBandwidthQuotaByBand : 200;
            let networkTraffic = quotaType2quota.eipBandwidthQuotaByTraffic
                ? quotaType2quota.eipBandwidthQuotaByTraffic
                : 1000;
            let networkQuotaStatic = quotaType2quota.staticEipBandwidthQuotaByBand
                ? quotaType2quota.staticEipBandwidthQuotaByBand
                : 200;
            let networkTrafficStatic = quotaType2quota.staticEipBandwidthQuotaByTraffic
                ? quotaType2quota.staticEipBandwidthQuotaByTraffic
                : 1000;
            let eipInBandwidthQuota = quotaType2quota.eipInBandwidthQuota ? quotaType2quota.eipInBandwidthQuota : 1000;
            let eipBandwidthQuotaByIpRental = quotaType2quota.eipBandwidthQuotaByIpRental
                ? quotaType2quota.eipBandwidthQuotaByIpRental
                : 1000;
            let customEipBandwidthQuota = quotaType2quota.customEipBandwidthQuota
                ? quotaType2quota.customEipBandwidthQuota
                : 5000;
            let allNetwork = {
                networkQuotaStatic: Number(networkQuotaStatic),
                networkTrafficStatic: Number(networkTrafficStatic),
                networkQuotaDynamic: Number(networkQuota),
                networkTrafficDynamic: Number(networkTraffic),
                eipInBandwidthQuota: Number(eipInBandwidthQuota),
                eipBandwidthQuotaByIpRental: Number(eipBandwidthQuotaByIpRental),
                customEipBandwidthQuota: Number(customEipBandwidthQuota)
            };
            this.data.set('allNetwork', allNetwork);
        });
    }

    /**
     * 线路类型切换
     */
    purchaseTypeChange(e) {
        let purchaseType = e.value;
        this.data.set('formData.purchaseType', purchaseType);
        let networkQuota = this.data.get('allNetwork.networkQuotaDynamic'); // 带宽上限
        let networkTraffic = this.data.get('allNetwork.networkTrafficDynamic'); // 流量上限
        if (purchaseType !== 'BGP') {
            networkQuota = this.data.get('allNetwork.networkQuotaStatic');
            networkTraffic = this.data.get('allNetwork.networkTrafficStatic');
        }
        if (
            (!this.data.get('inBgpsNetrafficWhite') && purchaseType === 'BGP_S') ||
            singleLineType.includes(purchaseType)
        ) {
            this.data.set('formData.subProductType', 'bandwidth');
        }
        if (purchaseType === 'BackToOrigin') {
            networkQuota = this.data.get('allNetwork.eipBandwidthQuotaByIpRental');
        }
        if (purchaseType === 'Custom') {
            networkTraffic = this.data.get('allNetwork.customEipBandwidthQuota');
            this.data.set('formData.subProductType', 'netraffic');
        }
        this.data.set('networkQuota', networkQuota);
        this.data.set('networkTraffic', networkTraffic);
        this.setBandwidth();
        this.getPrice();
    }

    // 初始化带宽宽度
    getNetwork() {
        return new Promise(resolve => {
            this.$http
                .eipOrdermaxBandQuota([
                    'eipBandwidthQuotaByBand',
                    'eipBandwidthQuotaByTraffic',
                    'staticEipBandwidthQuotaByBand',
                    'staticEipBandwidthQuotaByTraffic',
                    'eipInBandwidthQuota',
                    'eipBandwidthQuotaByIpRental',
                    'customEipBandwidthQuota'
                ])
                .then(({quotaType2quota}) => {
                    resolve(quotaType2quota);
                });
        });
    }

    setBandwidth() {
        if (this.data.get('formData.purchaseType') === 'BGP_S') {
            this.data.set('eipMin', 100);
            this.data.set('formData.bandwidthInMbps', 100);
        } else {
            this.data.set('eipMin', 1);
            this.data.set('formData.bandwidthInMbps', 1);
        }
    }

    onSubProductTypeChange(e) {
        this.data.set('formData.subProductType', e.value);
        this.setBandwidth();
        this.getPrice();
    }

    onBandwidthChange(e, type) {
        let val = +e.value;
        if (isNaN(val)) {
            return;
        }
        let max = type === 'netraffic' ? this.data.get('networkTraffic') : this.data.get('networkQuota');
        val = val < 1 ? 1 : val;
        val = val > max ? max : val;
        this.data.set('formData.bandwidthInMbps', val);
        this.getPrice();
    }
    loadEnterpriseSecurityList() {
        return this.$http
            .enterpriseSecurityList({
                pageNo: 1,
                pageSize: 10000
            })
            .then(data => {
                let datasource = [];
                u.each(data.result || [], item =>
                    datasource.push({
                        value: item.esgUuid,
                        text: item.name
                    })
                );
                this.data.set('enterpriseSecurityDatasource', datasource);
                if (this.data.get('formData')?.securityType !== 'normal') {
                    this.data.set('currentSecurityDatasource', datasource);
                }
            });
    }
    handleRadioTypeChange() {
        this.data.set('errorShow', false);
        this.data.set('currentSecurityDatasource', []);
        this.nextTick(() => {
            let value = this.data.get('formData.securityType');
            let securityDatasource = this.data.get('securityDatasource');
            let enterpriseSecurityDatasource = this.data.get('enterpriseSecurityDatasource');
            let currentSecurityDatasource = value === 'enterprise' ? enterpriseSecurityDatasource : securityDatasource;
            this.data.set('currentSecurityDatasource', currentSecurityDatasource);
            this.data.set('formData.securityGroupUuids', value === 'enterprise' ? '' : []);
            // 企业安全组只能绑定一个
            this.data.set('mode', value === 'enterprise' ? false : true);
        });
    }
    // 选择镜像
    onServiceSelect(item) {
        this.data.set('formData.serviceIdNew', item.name);
        this.data.set('serviceDatasource', []);
        this.data.set('formData.serviceId', '');
        let initServiceDatasource = this.data.get('initServiceDatasource');
        let array = initServiceDatasource.filter(i => i.serviceType === item.serviceType);
        array = array.map(i => ({
            value: i.shortId,
            text: i.service
        }));
        this.data.set('serviceDatasource', array);
        this.data.set('formData.serviceId', array[0]?.value);
        // this.data.set('formData.selectedImage', item);
        this.getPrice();
    }
    // 输入网关名称时统计字符时（近似统计）
    handleNameInput(type, {value}) {
        if (type === 'eipName') {
            this.data.set('formData.eipName', value);
        }
        // 设置网卡名称、公网IP名称长度
        const currNameLength = type === 'name' ? 'nameLength' : 'eipNameLength';
        if (value?.length <= 65) {
            this.data.set(currNameLength, value.length);
        } else {
            this.data.set(currNameLength, 65);
        }
    }
    // 获取当前子网可用区
    handleSubnetChange(e) {
        const subnetId = e.value;
        const subnetDatasource = this.data.get('subnetDatasource');
        const currSubnet = subnetDatasource.find(item => item.value === subnetId);
        this.data.set('currZone', zone.getLabel(currSubnet.az) || '');
        this.$http.getSubnetDetail(subnetId).then(res => {
            if (res?.subnets) {
                const currResSubnets = res.subnets[0];
                this.data.set('availableIPs', currResSubnets?.totalIps - currResSubnets?.usedIps || null);
            }
        });
    }

    // 新增onShoppingCartChange
    onShoppingCartChange(e) {
        this.data.set('priceLoading', UPDATE_STATUS[e] !== UPDATE_STATUS.DONE);
        if (UPDATE_STATUS[e] === UPDATE_STATUS.DONE) {
            const bucketItems = this.data.get('items');
            const {newBillingSdk} = this.data.get();
            let unitPrice = bucketItems[0]?.unitPrice;
            let priceError = bucketItems[0]?.priceError;
            if (unitPrice && !priceError) {
                newBillingSdk.clearItems();
                let price = (+bucketItems[0]?.unitPrice * 60).toFixed(3);
                bucketItems[0].priceError = '￥' + price + '/小时';
                this.data.set('items', bucketItems);
                newBillingSdk.addItems(bucketItems);
            }
        }
    }
    // 切换地域
    onRegionChange(e) {
        let value = e.value || e.id;
        if (!value || value === window.$context.getCurrentRegionId()) {
            return;
        }
        this.data.set('formData.region', value);
        window.$context.setRegion(value);
        this.data.set('loadNeed', true);
    }
    checkSecurity() {
        this.data.set('secCheckDis', true);
        let formData = this.data.get('formData');
        if (formData.securityGroupUuids?.length > 0) {
            return this.$http
                .getUseNestedSgRule({securityGroupUuids: formData.securityGroupUuids})
                .then(res => {
                    this.data.set('errorShow', res.value);
                    this.data.set('secCheckDis', false);
                })
                .catch(e => {
                    this.data.set('secCheckDis', false);
                });
        } else {
            this.data.set('secCheckDis', false);
        }
    }
    eipBlackList() {
        return this.$http.vpcCommonWhiteList({id: 'EipBlackList'}).then(res => {
            this.data.set('eipBlackList', res);
        });
    }
    tagListRequster() {
        return this.$http.getSearchTagList({serviceType: [], region: ['global']});
    }
    showAssist(type) {
        Assist.sendMessageToAssist({
            sceneLabel: 'snic_create',
            message:
                type === 'security'
                    ? '普通安全组和企业安全组的区别是什么？'
                    : type === 'region'
                      ? '什么是地域？'
                      : 'DNS解析在服务网卡中的作用是什么？'
        });
    }
    getDomainServiceList() {
        this.$http
            .blbServiceList({
                pageNo: 1,
                pageSize: 10000,
                status: 'available'
            })
            .then(res => {
                let serviceList = res.result
                    .filter(item => item.status === 'available')
                    .map(item => {
                        return {
                            label: item.service,
                            value: item.service
                        };
                    });
                this.data.set('serviceDomainList', serviceList);
            });
    }
    serviceChange({value}) {
        if (value) {
            this.data.set('showServiceAuthTipLoading', true);
            this.$http
                .getUserServiceAuth(value)
                .then(res => {
                    this.data.set('showServiceAuthTip', !!res.hasPermission);
                })
                .catch(err => {
                    // 做兼容，检查服务权限报错，也允许下单;
                    this.data.set('showServiceAuthTip', true);
                })
                .finally(() => {
                    this.data.set('showServiceAuthTipLoading', false);
                });
        } else {
            this.data.set('showServiceAuthTip', true);
        }
    }
}
export default Processor.autowireUnCheckCmpt(CreateEndpoint);
