.vpc-endpoint-create {
    height: 100%;
    width: 100%;
    .s-create-page {
        height: 100%;
    }
    .steps-wrap {
        margin: 20px 0;
    }
    .s-step-block {
        width: 316px !important;
    }
    .form-wrap {
        margin-top: 16px;
        min-width: 1020px;
        .form-part-wrap {
            border-radius: 6px;
            width: calc(~'100vw - 260px');
            background: #fff;
            &:first-child {
                padding: 24px;
            }
            padding: 16px 24px 24px;
            min-width: 1020px;
            h4 {
                display: inline-block;
                zoom: 1;
                margin: 0;
                font-family: PingFangSC-Medium;
                font-size: 16px;
                color: #151b26;
                line-height: 24px;
                font-weight: 500;
            }
            .payType {
                display: flex;
                align-items: center;
            }
            .s-form-item-invalid-div {
                padding: 3px;
                color: #f33e3e;
                font-size: 12px;
            }
            .service-type {
                display: flex;
                margin-bottom: 10px;
                .tip-icon {
                    color: #9e9898;
                    border: 1px solid #9e9898;
                    box-sizing: content-box;
                    position: relative;
                    top: -2px;
                    &:hover {
                        border-color: #2468f2;
                        color: #2468f2;
                    }
                }
            }
            .endpoint-radio-btn {
                .s-radio {
                    margin-right: unset;
                }
            }
            .owner-network {
                .s-form-item-control {
                    display: flex;
                }
            }
            .config-paytype {
                line-height: 30px;
            }
            .input-with-num {
                input {
                    padding-right: 52px;
                }
            }
            .input-num-statistics {
                font-family: PingFangSC-Regular;
                font-size: 12px;
                color: #84868c;
                text-align: right;
                line-height: 20px;
                font-weight: 400;
                margin-left: -46px;
            }
            .security-class-tip {
                .error_tip {
                    margin-top: 4px;
                }
            }
        }
        .resource-form-part-wrap {
            border-radius: 6px;
            width: calc(~'100vw - 260px');
            background: #fff;
            padding: 16px 24px 24px;
            .resource-group-panel {
                padding: 0;
                margin: 0;
                border: none;
                dt {
                    margin-bottom: 18px;
                    h4 {
                        padding: 0;
                        font-family: PingFangSC-Medium;
                        font-size: 16px;
                        color: #151b26;
                        line-height: 24px;
                        font-weight: 500;
                    }
                }
                .wrapper {
                    margin-left: 8px;
                    .s-button {
                        border-color: #e8e9eb;
                    }
                }
                .footer {
                    .tip {
                        margin-left: 8px;
                        font-family: PingFangSC-Regular;
                        font-size: 12px;
                        color: #84868c;
                        font-weight: 400;
                    }
                }
                .resouce-group-select {
                    label {
                        padding-left: 7px;
                    }
                    .s-button-skin-stringfy {
                        padding: 0 4px;
                    }
                    .footer {
                        line-height: 20px;
                        height: 20px;
                        margin-top: 8px;
                        .tip {
                            color: #84868c;
                            line-height: 20px;
                        }
                    }
                }
            }
        }
        .s-form-item,
        .margin-wrap {
            margin: 20px 0;
        }
        .tip-content {
            color: #666;
        }
    }
    .s-form-item {
        .s-row {
            .s-form-item-control-wrapper {
                margin-left: 10px;
            }
        }
    }
    .s-form-item-label {
        width: 96px;
        text-align: left;
    }
    .s-input {
        border-color: #e8e9eb !important;
        .s-input-area {
            input {
                box-sizing: border-box;
            }
        }
    }
    .s-textarea textarea {
        border-color: #e8e9eb !important;
    }
    // 订单确认页样式统一设置 目前仅考虑1280 需适配其他宽度可媒体查询
    .order-confirm {
        margin-top: 16px;
        width: calc(~'100vw - 260px');
        min-width: 1020px;
    }
    .shopping-cart {
        display: inline-flex;
        vertical-align: top;
        flex: none;
    }
    .bui-toastlabel-warning {
        width: 100%;
        box-sizing: border-box;
    }
    .create-region .s-form-item-control {
        display: flex;
        align-items: center;
    }
    .footer-right {
        float: right;
        display: inline-flex;
        .s-button {
            margin-right: 16px;
        }
    }
    .s-form-item-label-required {
        position: relative;
        label::before {
            position: absolute;
            left: -9.5px;
        }
    }
    .service-domain {
        display: block;
        margin-left: 86px !important;
    }
    .error_select {
        .s-input-suffix-container {
            border-color: #f33e3e;
        }
    }
    .error_tip {
        color: #f33e3e;
    }
    .subnetId-wrapper {
        display: inline-block;
        margin-left: 8px;
        position: relative;
        .zone-tip {
            position: absolute;
            top: 40px;
            white-space: nowrap;
            font-family: PingFangSC-Regular;
            font-size: 12px;
            color: #84868c;
            line-height: 20px;
            font-weight: 400;
        }
        .s-form-item {
            margin-top: 0px !important;
            .s-form-item-control-wrapper {
                margin-left: 0px;
            }
        }
    }
    .config-content {
        .s-form-item-label {
            height: 30px;
        }
        .s-form-item-help {
            width: 350px;
        }
        .create-region {
            .inline-tip {
                position: absolute;
                .s-tip {
                    position: relative;
                    top: 3px;
                }
                .warning-class {
                    position: relative;
                    left: -1px;
                    .s-icon {
                        path {
                            fill: #83868c;
                        }
                    }
                }
                &:hover {
                    .warning-class {
                        .s-icon {
                            path {
                                fill: #2468f2;
                            }
                        }
                    }
                }
            }
            .s-radio-text {
                width: 63px !important;
            }
        }
        .s-radio-button-group {
            .s-radio-button {
                .s-radio-text {
                    width: 95px;
                    border-color: #e8e9eb !important;
                }
            }
            .s-radio-checked {
                .s-radio-text {
                    border-color: #2468f2 !important;
                }
            }
        }
        .inline-item {
            .s-radio-button-group {
                .s-radio-button {
                    .s-radio-text {
                        width: auto;
                    }
                }
            }
        }
        .purchase-tip {
            position: absolute;
            top: 2px;
        }
    }
    .center_class {
        .s-row {
            .s-form-item-control-wrapper {
                line-height: 30px;
            }
        }
        .inline-tip {
            position: absolute;
            .s-tip {
                position: relative;
                top: 3px;
            }
            .warning-class {
                margin-left: -1px;
                svg {
                    fill: #999;
                }
            }
        }
        .ip-item {
            .s-form-item-control-wrapper {
                margin-left: 0px;
            }
        }
    }
    .association-analysis {
        .s-form-item-control-wrapper {
            position: relative;
            .recommend {
                position: absolute;
                top: -8px;
                left: 93px;
                z-index: 999;
                display: inline-block;
                background-image: linear-gradient(90deg, #f3413f 22%, #f86454 100%);
                border-radius: 1.6px;
                color: #ffffff;
                font-size: 12px;
                width: 36px;
                height: 16px;
                line-height: 16px;
                text-align: center;
            }
        }
    }
    .label_class {
        .inline-tip {
            top: 3px;
            position: relative;
            .s-tip-warning {
                margin-left: -1px;
                .warning_class {
                    fill: #999;
                }
            }
        }
    }
    .buybucket {
        width: 100%;
        left: 0;
        height: 60px;
        display: flex;
        justify-content: center;
        .buybucket-widget {
            width: calc(~'100vw - 214px');
            height: 100%;
            // margin-left: 30px;
            .buybucket-container {
                width: auto;
                // float: left;
                height: 48px;
                transform: translateY(12.5%);
                display: flex;
                align-items: center;
                .s-button {
                    margin-left: 16px;
                }
            }
        }
    }
    .dragger-input {
        display: inline-block;
        position: relative;
        left: 5px;
        top: -10px;
    }
    .form-slider-wrap {
        .s-form-item-control-wrapper {
            top: 6px;
        }
    }
    .image-wrap {
        // width: 1500px;
        display: flex;
        flex-wrap: nowrap;
        .s-form-item-control-wrapper {
            margin-left: 10px !important;
        }
        .s-form-item-label {
            color: #ffffff;
        }
    }
    .patch-image-style .s-row-flex {
        flex-wrap: nowrap;
    }
    .system-wrap {
        display: flex;
        flex-wrap: wrap;
        .system-item {
            width: 188px;
            height: 94px;
            background: #ffffff;
            border: 1px solid #e8e9eb;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            padding: 12px 16px;
            margin-right: 16px;
            margin-bottom: 16px;
            cursor: pointer;
            &:hover {
                border: 1px solid #2468f2;
            }
            .system-version {
                line-height: 20px;

                .system-osName {
                    line-height: 22px;
                    font-size: 14px;
                    color: #151b26;
                    font-weight: 500;
                    margin-bottom: 8px;
                    overflow: hidden;
                    width: 154px;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    font-family: PingFangSC-Medium;
                }
                .system-desc {
                    font-family: PingFangSC-Regular;
                    font-size: 12px;
                    color: #5e626a;
                    line-height: 20px;
                    font-weight: 400;
                }
            }
        }
        .system-selected {
            background: #eef3fe;
            border: 1px solid #2468f2;
        }
    }
    .image-top-label {
        margin-top: -16px !important;
    }
    .patch-image-style .system-wrap {
        border-bottom-width: 0;
        padding-bottom: 0;
    }
    .bucket-footer-wrap {
        height: 89px;
        .billing-sdk-protocol-wrapper .buy-agreement {
            margin-bottom: 0px;
            .agreement-wrap {
                margin-top: 10px;
                margin-left: 16px;
            }
        }
    }
    .total-price-wrap {
        margin-left: 16px;
    }
    .billing-sdk-order-confirm-wrapper-default {
        width: calc(~'100vw - 260px');
        min-width: 1020px;
        padding: 16px 0;
    }
    .tag-edit-panel {
        .s-form-item {
            margin: 0 8px 12px 0 !important;
        }
        .s-form-item-control-wrapper {
            margin-left: 0px !important;
        }
    }
    .tag-v2-panel-container {
        width: 800px !important;
    }
}
.inline-tip {
    .s-tip:hover {
        background: #fff;
        path {
            fill: #2468f2;
        }
    }
}
.locale-en {
    .vpc-endpoint-create .config-content .s-form-item-label {
        width: 190px;
        height: 30px;
    }
    .vpc-endpoint-create .s-form-item-help {
        max-width: 720px;
    }
    .vpc-endpoint-create {
        .resouce-group-select .resouce-group-select-main > label {
            width: 190px;
        }
        .resouce-group-select .footer {
            margin-left: 190px;
        }
    }
}
.bui-layer {
    .title {
        color: #151b26;
        font-weight: 500;
    }
    .bui-tiplayer {
        border-radius: 4px;
        .bui-button-label {
            color: #2468f2;
        }
    }
}
