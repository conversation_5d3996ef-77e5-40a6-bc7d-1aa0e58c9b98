.page-sub-title() {
    display: inline-block;
    color: #333;
    font-size: 16px;
    margin: 8px 0;
    border-left: solid 4px #2468f2;
    padding-left: 14px;
}
.vpc-enic-upgrade {
    background-color: #f7f7f9;
    height: 100%;
    overflow: auto;
    width: 100%;
    .body-part-content {
        border: 1px solid #ebebeb;
        width: 980px;
        margin: 10px auto 0 auto;
        padding: 20px;
    }
    .bui-form {
        .bui-form-item {
            margin: 20px 0 0 0;
            .bui-form-item-label {
                width: 80px;
            }
        }
    }
    .vpc-select-box {
        margin-top: -50px;
        margin-left: -100px;
    }
    .upgrade-table {
        width: 100%;
    }
    .form-item {
        line-height: 30px;
    }
    .bui-biz-page-content.bui-biz-page-center-content {
        .bui-biz-page-body {
            margin: 0 auto !important;
            border: none !important;
            box-sizing: content-box;
        }
        .bui-toastlabel-warning {
            width: 100%;
        }
    }
    .form-part-wrap {
        border-radius: 6px;
        width: calc(~'100vw - 32px');
        background: #fff !important;
        margin: 16px;
        padding: 24px;
        h4 {
            display: inline-block;
            border: none;
            zoom: 1;
            font-size: 16px;
            color: #151b26;
            font-weight: 500;
            margin: 0 0 16px;
            padding: 0;
        }
        .s-form-item {
            margin: 0 0 20px !important;
        }
        .s-form-item-label {
            width: 118px;
            height: 30px;
        }
        .label_class {
            .inline-tip {
                top: 3px;
                position: relative;
                .s-tip-warning {
                    justify-content: center;
                    .warning_class {
                        fill: #999;
                    }
                }
                .s-tip:hover .s-icon path {
                    fill: #2468f2 !important;
                }
            }
        }
        .center_class {
            .s-row {
                .s-form-item-control-wrapper {
                    line-height: 30px;
                }
            }
        }
        .vpc-nat-eip-opt {
            margin-top: 0px;
        }
    }
    .buybucket {
        width: 100%;
        left: 0;
        height: 60px;
        .buybucket-container {
            width: auto;
            float: left;
            height: 48px;
            transform: translateY(12.5%);
            display: flex;
            align-items: center;
        }
    }
    .dragger-input {
        display: inline-block;
        position: relative;
        left: 5px;
        top: -10px;
    }
    .content-item-box {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        .content-item {
            margin-bottom: 16px;
            width: 33%;
            label {
                color: #666;
                vertical-align: middle;
            }
        }
    }
    .cell-title {
        display: inline-block;
        vertical-align: top;
        color: #5e626a;
        margin-right: 16px;
        width: 60px;
    }
    .cell-content {
        display: inline-block;
        color: #151a26;
        max-width: 80%;
        word-break: break-all;
        position: relative;
    }
    .alert_class {
        color: #84868c;
        margin-left: 118px;
        a {
            margin-left: -2px;
            margin-right: 1px;
        }
    }
    .help_class {
        color: #84868c;
        margin-top: 16px;
        margin-left: 118px;
    }
}
