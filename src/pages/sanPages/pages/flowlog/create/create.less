.vpc-flowlog-create {
    background: #fff;
    width: 100%;
    min-height: 100%;
    background-color: #f7f7f9;
    padding-bottom: 20px;
    .content-box {
        text-align: left;
        margin: 20px auto;
        padding: 20px;
        .form-resource {
            margin-left: 92px !important;
        }
        .flowlog-name-wrapper {
            .s-input-area {
                input {
                    padding-left: 10px;
                }
            }
        }
        .flowlog-csn-type {
            height: 30px;
            .s-radio-group {
                height: 100%;
                .s-wrapper {
                    height: 100%;
                    display: flex;
                    align-items: center;
                }
            }
        }
        .connect-region {
            display: flex;
            align-items: center;
            .region-current {
                margin-right: 12px;
            }
            .region-select {
                margin: 0 0 0 12px !important;
            }
        }
        .connect-region-tip {
            margin-top: 8px;
            color: #84868c;
        }
        .resource-button-wrap {
            .s-radio-text {
                width: 72px;
            }
        }
        .flowlog-net-wrap {
            .s-form-item {
                display: inline-block;
                .s-form-item-control-wrapper {
                    width: auto;
                }
            }
            .s-select {
                margin-right: 10px;
            }
        }
        .s-form-item-label > label {
            margin-right: 0;
        }
        .s-form-item-label {
            width: 92px;
        }
        .collector-button {
            height: 30px;
            box-sizing: border-box;
        }
        .s-form-item-with-help {
            margin-bottom: 24px;
        }
        .logStoreName {
            .add-icon {
                color: #2468f2;
                &:hover {
                    cursor: pointer;
                }
                .icon_plus {
                    fill: #2468f2;
                    margin-left: 10px;
                    font-size: 12px;
                    position: relative;
                    top: -1px;
                }
                .bos_class {
                    margin-left: 60px;
                }
            }
        }
    }
    .s-biz-page-content {
        .s-biz-page-fixed-footer {
            width: 100%;
        }
    }
    .form-part-wrap {
        border-radius: 6px;
        width: calc(~'100vw - 32px');
        background: #fff;
        margin: 16px;
        padding: 24px;
        h4 {
            display: inline-block;
            border: none;
            zoom: 1;
            font-size: 16px;
            color: #151b26;
            font-weight: 500;
            margin: 0;
            padding: 0;
        }
        .s-form-item-label {
            width: 92px;
            height: 30px;
        }
        .label_class {
            .inline-tip {
                top: 3px;
                position: relative;
                .s-tip-warning {
                    justify-content: center;
                    .warning_class {
                        fill: #999;
                    }
                }
                .s-tip:hover .s-icon path {
                    fill: #2468f2 !important;
                }
            }
        }
        .center_class {
            .s-row {
                .s-form-item-control-wrapper {
                    line-height: 30px;
                }
            }
        }
    }
    .buybucket {
        width: 100%;
        left: 0;
        height: 60px;
        .buybucket-container {
            width: auto;
            float: left;
            height: 48px;
            transform: translateY(12.5%);
            display: flex;
            align-items: center;
        }
    }
    .csn-vpc {
        .s-form-item-label {
            label {
                display: none;
            }
        }
        .csn-vpc-son {
            display: inline-block;
            margin-top: 0 !important;
            margin-left: 6px !important;
            .s-form-item-label {
                display: none;
                label {
                    display: none;
                }
            }
        }
    }
    .kafka-select {
        display: flex;
    }
    .cluster-list-class {
        margin-right: 20px;
    }
    .kafka_class {
        fill: #2468f2;
    }
    .kafka_wrap {
        display: inline-block;
        vertical-align: middle;
        line-height: normal;
    }
    .kafka_link {
        position: relative;
        top: 28px;
    }
    .class-cover {
        .s-input {
            border-color: #d4d6d9;
        }
        .s-form-item-error {
            display: none;
        }
    }
}

.locale-en {
    .vpc-flowlog-create .form-part-wrap .s-form-item-label {
        width: 238px;
    }
    .vpc-flowlog-create .content-box .form-resource {
        margin-left: 238px !important;
    }
}
