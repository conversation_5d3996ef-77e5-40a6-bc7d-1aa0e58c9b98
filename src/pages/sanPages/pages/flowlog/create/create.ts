import {Label} from '@baidu/xicon-san';
/*
 * @Description: 流日志创建页
 * @Author: wang<PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2021-09-07 16:45:04
 */
import {html, decorators, redirect, CreatePage, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {Radio, Input} from '@baidu/sui';
import {OutlinedPlus} from '@baidu/sui-icon';
import u from 'lodash';

import {createBucket, BucketSelect} from '@baiducloud/bos-sdk/san';
import {ResourceGroupSDK} from '@baidu/bce-resourcegroup-sdk';
import CreateLog from '../Log/log';
import {ResourceType, ResourceTypeRegionList, Action} from '@/pages/sanPages/common/enum';
import {ContextService} from '@/pages/sanPages/common';
import {serviceTypeUrl, checkSts, StsConfig} from '@/pages/sanPages/utils/config';
import {$flag, kXhrOptions} from '@/pages/sanPages/utils/helper';
import Assist from '@/utils/assist';
import {parseQuery} from '@/utils';
import {Automaticity} from '@baidu/xicon-san';
import './create.less';

const {asPage, invokeSUI, invokeSUIBIZ, invokeAppComp, template} = decorators;

const AllRegion = window.$context.getEnum('AllRegion');

const region = [
    {region: 'bj', text: '华北-北京'},
    {region: 'su', text: '华东-苏州'},
    {region: 'gz', text: '华南-广州'},
    {region: 'fwh', text: '金融华中-武汉'},
    {region: 'bd', text: '华北-保定'}
];

const requestUrl = {
    vpc: '/api/network/v1/flowlog/vpcList',
    subnet: '/api/network/v1/flowlog/subnetList',
    port: '/api/network/v1/flowlog/eniList',
    device: '/api/network/v1/flowlog/bccList'
};

// kafka只支持 bj gz su fwh bd
const kafkaRegion = ['bj', 'gz', 'su', 'fwh', 'bd'];

const tpl = html`
    <template>
        <s-app-create-page
            class="vpc-flowlog-create"
            backTo="{{pageNav.backUrl}}"
            backToLabel="{{pageNav.backLabel}}"
            pageTitle="{{pageNav.title}}"
        >
            <s-form
                s-ref="form"
                data="{=formData=}"
                rules="{{rules}}"
                label-col="{{labelCol}}"
                wrapper-col="{{wrapperCol}}"
                label-align="left"
            >
                <div class="content-box form-part-wrap">
                    <h4>配置信息</h4>
                    <s-form-item
                        class="flowlog-name-wrapper"
                        prop="name"
                        label="名称："
                        help="命名规范：1.只能包含大小写字母，数字，中文和'-_ /.'；2.必须以字母或者中文开头；3.长度限制在1-65之间。"
                    >
                        <s-input value="{=formData.name=}" placeholder="请输入流日志名称" width="400">
                            <template slot="suffix">{{formData.name.length}}/65</template>
                        </s-input>
                    </s-form-item>
                    <s-form-item
                        prop="resourceType"
                        label="资源类型："
                        class="flowlog_resource_wrap"
                        help="{{formData.resourceType === 'blb' ? '负载均衡四层流日志仅对专属集群上的负载均衡实例开放。' : ''}}"
                    >
                        <s-radio-group
                            width="180"
                            radioType="button"
                            on-change="getResourceIds"
                            class="no-res-wrap"
                            value="{{formData.resourceType}}"
                        >
                            <span s-for="item, index in resourceTypes">
                                <s-popover trigger="{{item.disabled ? 'hover' : ''}}">
                                    <div slot="content">
                                        <!--bca-disable-next-line-->
                                        {{item.value | noOpenTip | raw}}
                                    </div>
                                    <s-radio
                                        value="{{item.value}}"
                                        disabled="{{item.disabled}}"
                                        class="{{(index === 0 && 'radius-left') || (index === resourceTypes.length - 1 && 'radius-right')}}"
                                        label="{{item.text}}"
                                    >
                                    </s-radio>
                                </s-popover>
                            </span>
                        </s-radio-group>
                        <!--<p class="assist-tip-form">
                            支持哪些资源类型创建流日志？有哪些区别？
                            <a class="assist-tip" href="javascript:void(0)" on-click="showAssist('resourceType')"
                                >了解详情</a
                            >
                        </p>-->
                    </s-form-item>
                    <s-form-item s-if="{{resourceKeyWhite}}" prop="resourceKey" label="采集字段：">
                        <s-radio-group
                            width="180"
                            radioType="button"
                            datasource="{{resourceKeys}}"
                            value="{=formData.resourceKey=}"
                        >
                        </s-radio-group>
                    </s-form-item>
                    <div class="flowlog-net-wrap">
                        <s-form-item
                            s-if="{{isNetType}}"
                            prop="resourceVpc"
                            label="资源所在网络："
                            class="{{changeSource ? 'class-cover' : ''}}"
                        >
                            <s-select
                                filterable
                                width="200"
                                placeholder="请选择所在网络"
                                datasource="{{resourceIds['vpc']}}"
                                value="{{formData.resourceVpc}}"
                                on-change="resourceVpcChange"
                                on-visible-change="selectVisibleChange"
                            />
                        </s-form-item>
                        <s-form-item
                            s-if="{{isNetType && (formData.resourceType !== 'subnet'
                            && formData.resourceType !== 'nat' && formData.resourceType !== 'peerconn'
                            && formData.resourceType !== 'et' && formData.resourceType !== 'eip'
                            && formData.resourceType !== 'ipv6gw' && formData.resourceType !== 'vpn')}}"
                            prop="resourceNet"
                            class="{{changeSource ? 'class-cover' : ''}}"
                        >
                            <s-select
                                width="200"
                                placeholder="请选择所在子网"
                                datasource="{{resourceIds['subnet']}}"
                                value="{{formData.resourceNet}}"
                                on-change="resourceNetChange"
                                on-visible-change="selectVisibleChange"
                            />
                        </s-form-item>
                    </div>
                    <div class="flowlog-net-wrap">
                        <s-form-item prop="resourceId" label="资源实例：" class="{{changeSource ? 'class-cover' : ''}}">
                            <s-select
                                width="200"
                                value="{=formData.resourceId=}"
                                filterable
                                placeholder="请选择{{formData.resourceType | resourceTypeName}}"
                                on-change="resourceTypehange"
                                on-visible-change="selectVisibleChange"
                            >
                                <s-select-option
                                    s-for="item in resourceIds[formData.resourceType]"
                                    value="{{item.value}}"
                                    label="{{item.text}}"
                                    disabled="{{item.disabled}}"
                                >
                                    <s-tooltip placement="right">
                                        <div slot="content">
                                            <template s-if="!item.disabled">{{item.text}}</template>
                                            <template s-else>{{item.disabledMessage}}</template>
                                        </div>
                                        <div>{{item.text}}</div>
                                    </s-tooltip>
                                </s-select-option>
                            </s-select>
                        </s-form-item>
                        <s-form-item s-if="{{formData.resourceType === 'blb'}}" prop="lbPort">
                            <s-select
                                width="200"
                                filterable
                                datasource="{{blbListeners}}"
                                value="{=formData.lbPort=}"
                                placeholder="请选择监听"
                            />
                        </s-form-item>
                    </div>
                    <s-form-item prop="csnType" label="类型：" s-if="formData.resourceType === 'csnvpc'">
                        <s-radio-group
                            class="flowlog-csn-type"
                            value="{=formData.csnType=}"
                            datasource="{{csnTypeOptions}}"
                            on-change="handleCsnTypeChange"
                        ></s-radio-group>
                    </s-form-item>
                    <s-form-item label="资源所在网络：" class="csn-vpc" s-if="{{isShowCsnVpc}}" prop="csnInsType">
                        <!--<s-select
                            width="200"
                            filterable
                            datasource="{{csnVpcList}}"
                            value="{=formData.csnVpc=}"
                            placeholder="请选择私有网络"
                        />-->
                        <s-select
                            width="140"
                            datasource="{{csnSuppInsTypeList}}"
                            value="{=formData.csnInsType=}"
                            on-change="handleInsTypeChange"
                            placeholder="请选择实例"
                        >
                        </s-select>
                        <s-form-item class="csn-vpc-son" label=" " prop="csnVpc">
                            <s-select
                                width="200"
                                filterable
                                datasource="{{csnVpcList}}"
                                value="{=formData.csnVpc=}"
                                placeholder="{{csnInsTypePlaceholder}}"
                            />
                        </s-form-item>
                    </s-form-item>
                    <!--<s-form-item label="实例类型：" s-if="{{formData.resourceType === 'csnvpc'}}" prop="csnInsType">
                        <s-select
                            width="140"
                            datasource="{{csnSuppInsTypeList}}"
                            value="{=formData.csnInsType=}"
                            on-change="handleInsTypeChange"
                            placeholder="请选择实例"
                        >
                        </s-select>
                        <s-form-item class="csn-vpc" label=" " prop="csnVpc">
                            <s-select
                                width="200"
                                filterable
                                datasource="{{csnVpcList}}"
                                value="{=formData.csnVpc=}"
                                placeholder="{{csnInsTypePlaceholder}}"
                            />
                        </s-form-item>
                    </s-form-item>-->
                    <div class="flowlog-csn-crossRegion" s-if="isShowCsnCross">
                        <s-form-item prop="peerCloud" label="互通类型：">
                            <s-radio-group
                                radioType="button"
                                value="{=formData.peerCloud=}"
                                on-change="handlePeerCloudChange"
                                datasource="{{connectTypeOptions}}"
                            ></s-radio-group>
                        </s-form-item>
                        <s-form-item label="互通地域：">
                            <div class="connect-region">
                                <span class="region-current">{{currentRegion}}</span
                                ><x-automaticity theme="line" color="#000" size="{{24}}" strokeLinejoin="round" />
                                <s-form-item class="region-select" prop="peerRegion">
                                    <s-select value="{=formData.peerRegion=}" datasource="{{peerRegionList}}" />
                                </s-form-item>
                            </div>
                            <p class="connect-region-tip">仅展示源地域出入向流量</p>
                        </s-form-item>
                    </div>
                    <s-form-item prop="action" label="流量类型：">
                        <s-select
                            datasource="{{actions}}"
                            width="200"
                            value="{=formData.action=}"
                            placeholder="请选择"
                        />
                    </s-form-item>
                    <s-form-item prop="resourceType" label="目的地类型：">
                        <s-radio-group
                            width="200"
                            class="resource-button-wrap"
                            radioType="button"
                            on-change="resourceTypeChange($event)"
                            datasource="{{logStoreTypeData}}"
                            value="{=formData.logStoreType=}"
                        >
                        </s-radio-group>
                        <!--<p class="assist-tip-form" s-if="storeTypeHelper">
                            {{storeTypeHelper}}
                            <a class="assist-tip" href="javascript:void(0)" on-click="showAssist('logStoreType')" s-if="formData.logStoreType === 'bls'">了解详情</a>
                        </p>-->
                    </s-form-item>
                    <s-form-item prop="collector" label="目的地：" class="logStoreName" help="{{bucketHelper}}">
                        <s-select
                            s-if="formData.logStoreType === 'bls'"
                            width="200"
                            value="{=formData.collector=}"
                            loading="{{selectLoading}}"
                            filterable="{{true}}"
                        >
                            <s-select-option
                                s-for="item in collectors"
                                value="{{item.value}}"
                                label="{{item.text}}"
                                disabled="{{item.disabled}}"
                            >
                                <s-tooltip placement="right">
                                    <div slot="content">
                                        <template s-if="!item.disabled">{{item.text}}</template>
                                        <template s-else>{{item.disabledMessage}}</template>
                                    </div>
                                    <div>{{item.text}}</div>
                                </s-tooltip>
                            </s-select-option>
                        </s-select>
                        <ui-bucketselect
                            s-else-if="formData.logStoreType === 'bos'"
                            readonly
                            supportSearch
                            value="{=bucketlogUri=}"
                            bucket="{=formData.collector=}"
                            bucket-list-api="{{bucketListApi}}"
                            folder-list-api="{{folderListApi}}"
                            url-prefix="bos://"
                        >
                        </ui-bucketselect>
                        <div class="kafka_wrap" s-else-if="formData.logStoreType === 'kafka'">
                            <s-radio-group
                                width="200"
                                class="resource-button-wrap"
                                radioType="button"
                                datasource="{{kafkaTypeData}}"
                                value="{=formData.collector=}"
                            >
                            </s-radio-group>
                            <div class="kafka-select">
                                <s-form-item prop="clusterId">
                                    <s-select
                                        class="cluster-list-class"
                                        width="200"
                                        filterable
                                        value="{{formData.clusterId}}"
                                        on-change="clusterIdChange($event)"
                                        placeholder="请选择Kafka"
                                    >
                                        <s-select-option
                                            s-for="item in kafkaList"
                                            value="{{item.value}}"
                                            label="{{item.text}}"
                                        >
                                            <s-tooltip>
                                                <div slot="content">{{item.text}}</div>
                                                <div>{{item.text}}</div>
                                            </s-tooltip>
                                        </s-select-option>
                                    </s-select>
                                </s-form-item>
                                <s-form-item prop="topicId">
                                    <s-select
                                        width="200"
                                        filterable
                                        datasource="{{kafkaTopicList}}"
                                        value="{{formData.topicId}}"
                                        on-change="topicIdChange($event)"
                                        placeholder="请选择topic"
                                    ></s-select>
                                </s-form-item>
                            </div>
                        </div>
                        <span class="add-icon" on-click="addLogCollection" s-if="formData.logStoreType !== 'kafka'">
                            <outlined-plus class="icon_plus {{formData.logStoreType === 'bos' ? 'bos_class' : ''}}" />
                            <!--<s-icon name="plus" class="{{formData.logStoreType === 'bos' ? 'bos_class' : ''}}"/>-->
                            {{addTypeText}}
                        </span>
                        <a class="kafka_link" s-else on-click="toKafkaList">
                            <outlined-plus class="icon_plus kafka_class" />
                            {{addTypeText}}
                        </a>
                    </s-form-item>
                    <s-form-item
                        s-if="{{
                            (formData.resourceType === 'csnvpc' ||
                            formData.resourceType === 'et' ||
                            formData.resourceType === 'peerconn' ||
                            formData.resourceType === 'blbSeven') &&
                            formData.logStoreType !== 'kafka'
                        }}"
                        class="flowlog-name-wrapper"
                        prop="logPrefix"
                        label="日志文件前缀："
                        help="日志前缀可以包含小写字母、数字，以字母开头，长度1-32位"
                    >
                        <s-input value="{=formData.logPrefix=}" placeholder="请输入日志文件前缀" width="400"> </s-input>
                    </s-form-item>
                    <s-form-item prop="description" label="描述：">
                        <s-textarea
                            value="{=formData.description=}"
                            width="366"
                            height="80"
                            class="text-area"
                            placeholder="请输入"
                            maxLength="{{200}}"
                        />
                    </s-form-item>
                </div>
            </s-form>
            <div slot="pageFooter" class="buybucket">
                <div class="buybucket-container">
                    <s-tooltip
                        placement="top"
                        trigger="{{isVerifyTip || flowLogSinDisable.disable || confirmTip ? 'hover' : ''}}"
                    >
                        <div slot="content">
                            <!--bca-disable-next-line-->
                            {{(isVerifyTip || flowLogSinDisable.message) | raw}}
                        </div>
                        <s-button
                            size="large"
                            disabled="{{isVerifyTip || flowLogSinDisable.disable || confirmed}}"
                            skin="primary"
                            on-click="createFlowLog"
                        >
                            确定
                        </s-button>
                    </s-tooltip>
                    <s-button size="large" on-click="cancel"> 取消 </s-button>
                </div>
            </div>
        </s-app-create-page>
    </template>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class FlowlogCreate extends CreatePage {
    static components = {
        's-textarea': Input.TextArea,
        's-radio-group': Radio.RadioGroup,
        's-radio': Radio,
        'ui-bucketselect': BucketSelect,
        'outlined-plus': OutlinedPlus,
        'x-automaticity': Automaticity
    };

    initData() {
        return {
            labelCol: {span: 3},
            wrapperCol: {span: 19},
            resourceTypes: [],
            resourceKeys: [
                {text: '两元组', value: 'TWO'},
                {text: '五元组', value: 'FIVE'}
            ],
            actions: Action.toArray(),
            pageNav: {
                title: '创建流日志',
                backUrl: '/network/#/vpc/flowlog/list',
                backLabel: '返回流日志列表'
            },
            resourceIds: {
                vpc: [],
                subnet: [],
                port: [],
                device: [],
                nat: [],
                et: [],
                blb: [],
                peerconn: [],
                ipv6gw: [],
                eip: [],
                csn: []
            },
            blbListeners: [],
            collectors: [],
            originCollectors: [], // 原始采集日志列表
            formErrors: {},
            logStoreTypeData: [
                {
                    label: 'BLS',
                    value: 'bls'
                },
                {
                    text: 'BOS',
                    value: 'bos'
                },
                {
                    text: 'Kafka',
                    value: 'kafka'
                }
            ],
            formData: {
                logStoreType: 'bls',
                name: '',
                resourceType: 'nat',
                resourceId: '',
                action: '',
                collector: '',
                description: '',
                resourceKey: 'TWO',
                clusterId: '',
                topicId: '',
                logPrefix: '',
                csnType: 'netInstance',
                peerCloud: 'cloudInter',
                peerRegion: '',
                csnInsType: 'vpc'
            },
            rules: {
                resourceVpc: [{required: true, message: '请选择'}],
                resourceNet: [{required: true, message: '请选择'}],
                collector: [{required: true, message: '请选择'}],
                resourceType: [{required: true, message: '请选择'}],
                logPrefix: [
                    {
                        validator: (rule, value, callback) => {
                            let pattern = /^[a-z][a-z0-9]{0,32}$/;
                            if (value && !pattern.test(value)) {
                                return callback('支持小写字母、数字，以字母开头，长度1-32位');
                            }
                            callback();
                        }
                    }
                ],
                resourceId: [{required: true, message: '请选择'}],
                action: [{required: true, message: '请选择'}],
                lbPort: [{required: true, message: '请选择'}],
                csnVpc: [{required: true, message: '请选择'}],
                csnType: [{required: true, message: '请选择'}],
                peerCloud: [{required: true, message: '请选择'}],
                peerRegion: [{required: true, message: '请选择'}],
                resourceKey: [{required: true, message: '请选择'}],
                name: [
                    {required: true, message: '请输入名称'},
                    {
                        validator: (rule, value, callback) => {
                            let pattern = /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{0,64}$/;
                            if (!pattern.test(value)) {
                                return callback(
                                    '支持大小写字母、数字、中文以及-_ /.特殊字符，必须以字母或者中文开头，长度1-65'
                                );
                            }
                            callback();
                        }
                    }
                ],
                description: [{min: 0, max: 200, message: '长度0到200个字符'}],
                clusterId: [{required: true, message: '请选择'}],
                topicId: [{required: true, message: '请选择'}]
            },
            bucketListApi: '',
            folderListApi: '',
            bucketlogUri: '',
            csnVpcList: [],
            flowLogSinDisable: {},
            resourceKeyWhiteList: false,
            kafkaList: [],
            kafkaTopicList: [],
            kafkaTypeData: [{text: '专享版', value: 'proKafka'}],
            changeSource: false,
            resourceTypeList: ResourceTypeRegionList,
            urlQuery: parseQuery(location.hash),
            csnTypeOptions: [
                {label: '网络实例', value: 'netInstance'},
                {label: '跨地域连接', value: 'crossRegion'}
            ],
            connectTypeOptions: [
                {label: '云间互通', value: 'cloudInter'},
                {label: '云边互通', value: 'cloudEdge'}
            ],
            peerRegionList: [],
            csnEdgeNodes: [],
            csnSuppInsTypeList: [
                {text: '私有网络VPC', value: 'vpc'},
                {text: '专线通道', value: 'channel'}
            ]
        };
    }

    static computed = {
        isNetType() {
            let type = this.data.get('formData.resourceType');
            return (
                type !== this.data.get('resourceTypeList').VPC &&
                type !== this.data.get('resourceTypeList').EIP &&
                type !== this.data.get('resourceTypeList').CSN
            );
        },
        storeTypeHelper() {
            let {logStoreType} = this.data.get('formData');
            if (logStoreType === 'bls') {
                return '您可以使用日志服务BLS分析日志，及创建指标和警报。';
            } else if (logStoreType === 'bos') {
                return '您可以使用对象存储BOS长期存档日志，价格比较经济实惠，但延迟通常较高。';
            } else if (logStoreType === 'kafka') {
                return '您可以使用消息服务for Kafka将日志实时流式传输到Elasticsearch或其他应用程序。';
            }
        },
        bucketHelper() {
            let {logStoreType} = this.data.get('formData');
            if (logStoreType === 'bls') {
                return '';
            } else if (logStoreType === 'bos') {
                return 'bos://<bucket-name>/<folder>/，bucket必须存在，如不存在，请新建';
            }
        },
        addTypeText() {
            let {logStoreType} = this.data.get('formData');
            if (logStoreType === 'bls') {
                return '新建日志集';
            } else if (logStoreType === 'bos') {
                return '新建Bucket';
            } else if (logStoreType === 'kafka') {
                return '新建Kafka';
            }
        },
        resourceKeyWhite() {
            let resourceKeyWhiteList = this.data.get('resourceKeyWhiteList');
            let formData = this.data.get('formData');
            return resourceKeyWhiteList && ['et', 'peerconn', 'csnvpc'].includes(formData.resourceType);
        },
        isVerifyTip() {
            if (!ContextService.isVerifyUser()) {
                if ($flag.NetworkSupportXS) {
                    return '温馨提示：您还没有实名认证，请先完成实名认证';
                }
                return '温馨提示：您还没有实名认证，请立即去<a href="/qualify/#/qualify/index">认证</a>';
            }
            return '';
        },
        isShowCsnVpc() {
            const {csnType, resourceType} = this.data.get('formData');
            return csnType === 'netInstance' && resourceType === 'csnvpc';
        },
        isShowCsnCross() {
            const {csnType, resourceType} = this.data.get('formData');
            return csnType === 'crossRegion' && resourceType === 'csnvpc';
        },
        currentRegion() {
            return window.$context.getCurrentRegion().label || '-';
        },
        csnInsTypePlaceholder() {
            const csnInsType: 'vpc' | 'channel' = this.data.get('formData.csnInsType');
            const placeholderMap = {
                vpc: '请选择私有网络',
                channel: '请选择专线通道'
            };
            return placeholderMap[csnInsType];
        }
    };

    static filters = {
        resourceTypeName(value) {
            if (value === 'blb' || value === 'blbSeven') {
                return '负载均衡';
            }
            return this.data.get('resourceTypeList').getTextFromValue(value);
        },
        noOpenTip(value: string) {
            const isSubUser = window.$context.isSubUser();
            let serviceText = this.data
                .get('resourceTypeList')
                .toArray()
                .find((item: any) => item.value === value)?.text;
            let url = serviceTypeUrl[value];
            let str = `您的账号当前未开通${serviceText}服务，请前往${serviceText}控制台开通。<a href="${url}">去开通</a>`;
            if (isSubUser) {
                str = `该功能需要开通${serviceText}服务，当前登录的子账号没有开通产品服务的权限，请联系主账号授予开通服务权限，或联系主账户开通服务后授权使用。`;
            }
            return str;
        }
    };

    inited() {
        this.getResourceTypeWhite();
        if (!this.data.get('isVerifyTip')) {
            this.loadRegionCollctors().then(() => {
                this.setCollectors();
            });
        }
        this.data.set('bucketListApi', this.getBucketList());
        this.data.set('folderListApi', this.folderListApi());
        if (window.$context.getCurrentRegionId() === AllRegion.SIN) {
            this.data.set('flowLogSinDisable.disable', true);
            this.data.set('flowLogSinDisable.message', '新加坡地域资源售罄，请您切换到其他地域创建');
        }
        this.loadCsnEdgeNodes();
    }
    getResourceTypeWhite() {
        let resourceList = this.data.get('resourceTypeList').toArray();
        let whiteList = [
            'FlowlogPcWhiteList',
            'FlowlogEtWhiteList',
            'FlowlogNeutronWhiteList',
            'FlowlogPcDenyWhiteList',
            'FlowlogEtDenyWhiteList',
            'FlowlogCsnVpcWhiteList',
            'FlowlogCsnVpcDenyWhiteList',
            'FlowlogEnableFiveTupleWhiteList'
        ];

        let res = window.$storage.get('commonWhite');

        whiteList.forEach(item => {
            if (!res[item]) {
                resourceList = u.filter(resourceList, value => value.hidden !== item);
            }
        });
        resourceList = u.filter(resourceList, item => !['vpc', 'subnet'].includes(item.value));
        if (!res.FlowlogPcWhiteList && !res.FlowlogPcDenyWhiteList) {
            resourceList = u.filter(resourceList, item => item.value !== 'peerconn');
        }
        if (!res.FlowlogEtWhiteList && !res.FlowlogEtDenyWhiteList) {
            resourceList = u.filter(resourceList, item => item.value !== 'et');
        }

        if (!res.FlowlogCsnVpcWhiteList && !res.FlowlogCsnVpcDenyWhiteList) {
            resourceList = u.filter(resourceList, item => item.value !== 'csnvpc');
        }
        if (!res.FlowlogNeutronWhiteList) {
            let array = ['device', 'port'];
            resourceList = u.filter(resourceList, item => !array.includes(item.value));
        }
        if (resourceList.length === 0) {
            resourceList = this.data.get('resourceTypeList').toArray('NAT');
        }
        if (res.FlowlogEnableFiveTupleWhiteList) {
            this.data.set('resourceKeyWhiteList', true);
        }
        resourceList = checkSts(resourceList);
        this.data.set('FlowLogWhiteList', res);
        this.data.set('FlowlogPcDenyWhiteList', res.FlowlogPcDenyWhiteList);
        this.data.set('FlowlogEtDenyWhiteList', res.FlowlogEtDenyWhiteList);
        this.data.set('FlowlogCsnVpcWhiteList', res.FlowlogCsnVpcWhiteList);
        this.data.set('FlowlogCsnVpcDenyWhiteList', res.FlowlogCsnVpcDenyWhiteList);

        this.data.set('resourceTypes', resourceList);
        let index = resourceList.findIndex((item: any) => !item.disabled);
        if (index > -1) {
            let resourceType = resourceList[index].value;
            let indexBlbFour = resourceList.findIndex((item: any) => item.value === 'blb' && !item.disabled);
            let indexBlbSeven = resourceList.findIndex((item: any) => item.value === 'blbSeven' && !item.disabled);
            if (
                (this.data.get('urlQuery')?.resourceType === 'blb' && indexBlbFour > -1) ||
                (this.data.get('urlQuery')?.resourceType === 'blbSeven' && indexBlbSeven > -1)
            ) {
                this.getResourceIds({value: this.data.get('urlQuery')?.resourceType});
                let userLocalObj = JSON.parse(localStorage.getItem(window.$context.getUserId()) || '{}');
                if (userLocalObj?.blbFlowlogId) {
                    let blbFlowlogRes = JSON.parse(userLocalObj.blbFlowlogId || '{}');
                    this.data.set('blbFlowlogRes', blbFlowlogRes);
                }
                return;
            }
            this.getResourceIds({value: resourceType});
        }
    }

    getBucketList() {
        return () =>
            this.$http.getBucketList().then(data => {
                this.data.set('bucketList', data.buckets);
                return {result: data.buckets};
            });
    }

    resourceTypeChange({value}) {
        this.data.set('bucketlogUri', '');
        this.data.set('formData.clusterId', '');
        this.data.set('formData.topicId', '');
        if (value === 'kafka') {
            if (!this.data.get('kafkaFir')) {
                this.checkKafkaRegion();
                this.data.set('kafkaFir', true);
            }
            this.data.set('formData.collector', 'proKafka');
        } else {
            this.data.set('formData.collector', '');
        }
    }

    folderListApi() {
        return payload => {
            let param: any = {
                bucketName: payload.bucketName,
                prefix: payload.prefix
            };
            if (payload.searchFix) {
                param.prefix = payload.searchFix;
            }

            if (!payload.searchFix && payload.marker) {
                param.marker = payload.marker;
            }
            if (!this.data.get('bucketList')) {
                return this.data
                    .get('bucketListApi')()
                    .then(() => {
                        let item = this.getCurrentBucket(param.bucketName);
                        if (!item) {
                            return Promise.resolve([]);
                        }
                        param.location = item.location;
                        return this.$http.flowlogBuckets(param).then(data => {
                            return {...data, truncated: data.isTruncated, marker: data.nextMarker};
                        });
                    });
            }
            let item = this.getCurrentBucket(param.bucketName);
            if (!item) {
                return Promise.resolve([]);
            }
            param.location = item.location;
            return this.$http.flowlogBuckets(param).then(data => {
                return {...data, truncated: data.isTruncated, marker: data.nextMarker};
            });
        };
    }

    getCurrentBucket(bucketName) {
        return u.find(this.data.get('bucketList'), item => item.name === bucketName);
    }

    addLogCollection() {
        let {logStoreType} = this.data.get('formData');
        if (logStoreType === 'bls') {
            this.createCollector();
        } else if (logStoreType === 'bos') {
            this.onCreateBucket();
        }
    }

    onCreateBucket() {
        const config = {
            data: {
                client: this.$http,
                isSubUser: this.$context.isSubUser(),
                userId: this.$context.getUserId(),
                cdnActive: this.$context.isServiceActive('CDN'),
                regionEnum: this.$context.getEnum('AllRegion'),
                // Region黑名单：用于过滤一些下线的region
                regionBlackList: this.getRegionBlackList(),
                // 是否为国际化状态
                isI18N: this.isI18N(),
                // 多版本控制支持的region
                multiVersionEnabledRegion: [AllRegion.SU, AllRegion.GZ],
                $flag,
                // 资源组SDK
                resourceSDK: $flag.BosAccessResourceGroup ? new ResourceGroupSDK(this.$http, this.$context) : {},
                // 展示形式为抽屉
                pageType: 'drawer',
                // 文档域名
                portal: this.$context?.Domains?.portal || 'https://cloud.baidu.com'
            }
        };
        createBucket(config).then(() => {
            this.data.set('bucketlogUri', '');
            this.data.set('formData.collector', '');
        });
    }
    // /**
    //  * 获取Region黑名单
    //  */
    getRegionBlackList() {
        return AllRegion.toArray('SZFSG').map(item => item.value);
    }
    isI18N() {
        return this.$context.currentLanguage !== 'zh-cn';
    }
    getPayload() {
        let type = this.data.get('formData.resourceType');
        let payload = {pageNo: 1, pageSize: 1000, keywordType: '', keyword: '', vpcId: '', subnetId: ''};
        let subnetId = this.data.get('formData.resourceNet');
        let vpcId = this.data.get('formData.resourceVpc');
        payload.vpcId = vpcId;
        if (type === this.data.get('resourceTypeList').PORT || type === this.data.get('resourceTypeList').DEVICE) {
            payload.subnetId = subnetId;
        }
        return payload;
    }
    getResourceIds({value}) {
        this.data.set('changeSource', true);
        this.data.set('formData.resourceId', '');
        this.data.set('formData.resourceVpc', '');
        this.data.set('formData.resourceNet', '');
        this.data.set('formData.action', '');
        this.data.set('formData.resourceType', value);
        this.data.set(`resourceIds['subnet']`, []);
        let logStoreTypeData = [
            {
                label: 'BLS',
                value: 'bls'
            },
            {
                text: 'BOS',
                value: 'bos'
            },
            {
                text: 'Kafka',
                value: 'kafka'
            }
        ];
        if (!kafkaRegion.includes(window.$context.getCurrentRegionId())) {
            logStoreTypeData.splice(2, 1);
        }
        if (['vpc', 'subnet', 'device', 'port'].includes(value)) {
            logStoreTypeData = u.filter(logStoreTypeData, item => item.value === 'bls');
        }
        if (value === 'blbSeven') {
            logStoreTypeData = u.filter(logStoreTypeData, item => item.value !== 'kafka');
        }
        this.data.set('logStoreTypeData', logStoreTypeData);
        this.data.set('formData.logStoreType', logStoreTypeData[0].value);
        if (value !== this.data.get('resourceTypeList').VPC) {
            this.data.set(`resourceIds['${value}']`, []);
        }
        // 目前流日志的创建 nat支持允许和拒绝，拒绝为白名单
        // eip、ipv6gw、vpn类型支持拒绝，其他类型支持全部、允许、拒绝
        // 2024.06.24 EIP,专线，对等，csn,nat  只保留允许
        if (
            [
                this.data.get('resourceTypeList').IPV6,
                this.data.get('resourceTypeList').VPC,
                this.data.get('resourceTypeList').SUBNET,
                this.data.get('resourceTypeList').DEVICE,
                this.data.get('resourceTypeList').PORT
            ].includes(value)
        ) {
            this.data.set('actions', Action.toArray());
        } else if (value === 'blbSeven') {
            this.data.set('actions', [{text: '全部流量', value: 'ALL'}]);
            this.nextTick(() => {
                this.data.set('formData.action', 'ALL');
            });
        } else {
            this.data.set('actions', [{text: '允许', value: 'ACCEPT'}]);
            this.nextTick(() => {
                this.data.set('formData.action', 'ACCEPT');
            });
        }
        // if (value === this.data.get('resourceTypeList').BLB) {
        //     this.data.set('formData.lbPort', '');
        // }
        this.initResourceIds();
        this.setCollectors();
    }

    setCollectors() {
        let cloneCollectors = this.data.get('cloneCollectors');
        this.data.set('collectors', cloneCollectors);
    }

    // vpc更改对应子网列表
    resourceVpcChange({value}) {
        let resourceType = this.data.get('formData.resourceType');
        this.data.set('formData.resourceVpc', value);
        let type = this.data.get('resourceTypeList').SUBNET;
        if (
            resourceType === 'nat' ||
            resourceType === 'peerconn' ||
            resourceType === 'et' ||
            resourceType === 'ipv6gw' ||
            resourceType === 'subnet'
        ) {
            this.loadEnableResource(resourceType);
        } else if (resourceType === 'blb' || resourceType === 'blbSeven') {
            this.loadBlbSubnet();
        } else {
            this.loadResourceIds(type, requestUrl[type]);
        }
        this.data.set('formData.resourceNet', '');
        this.data.set('formData.resourceId', '');
    }

    // 子网更改对应port与服务端列表
    resourceNetChange({value}) {
        let type = this.data.get('formData.resourceType');
        this.data.set('formData.resourceNet', value);
        if (type === 'blb' || type === 'blbSeven') {
            this.loadBlbResource();
        } else {
            this.loadEnableResource(type);
        }
        this.data.set('formData.resourceId', '');
    }

    getAvailableCsnCrossRegion(
        resourceId: string,
        csnType: 'netInstance' | 'crossRegion',
        peerCloud: 'cloudInter' | 'cloudEdge'
    ) {
        if (resourceId && csnType === 'crossRegion') {
            const peerCloudBool = peerCloud === 'cloudInter' ? true : false;
            const params: Record<string, any> = {
                csnId: resourceId,
                peerCloud: peerCloudBool
            };
            this.$http.getCsnCrossPeerRegion(params).then(res => {
                const csnEdgeNodes = this.data.get('csnEdgeNodes');
                const regionWithTextList = (res || []).map(item => {
                    const regionLabel = (window as any).$context.getEnum('AllRegion')?.getTextFromValue(item)
                        ? (window as any).$context.getEnum('AllRegion')?.getTextFromValue(item)
                        : u.find(csnEdgeNodes, node => item === node.nodeId)?.nodeName;
                    return {
                        label: regionLabel,
                        value: item
                    };
                });
                this.data.set('peerRegionList', regionWithTextList);
                this.data.set('formData.peerRegion', res[0]);
                if (res[0]) {
                    this.nextTick(() => {
                        this.ref('form').validateFields(['peerRegion']);
                    });
                }
            });
        }
    }
    getEnableResourceData(payload: Record<string, any>) {
        this.$http.getEnableResource(payload).then((res: any) => {
            const csnVpcList = u.map(res.result, item => {
                return {
                    text: `${item.name}(${item.resourceId})`,
                    value: item.resourceId
                };
            });
            this.data.set('csnVpcList', csnVpcList);
        });
    }

    // blb更改对应监听列表
    resourceTypehange({value}) {
        let type = this.data.get('formData.resourceType');
        this.data.set('formData.resourceId', value);
        if (type === 'blb') {
            this.data.set('formData.lbPort', '');
            let param = {
                pageNo: 1,
                pageSize: 1000,
                blbId: value
            };
            let blbType = u.find(this.data.get('resourceIds.blb'), item => item.value === value).blbType;
            let requester =
                blbType === 'application' || blbType === 'ipv6Application'
                    ? 'getFlowlogAppBlbPortList'
                    : 'getFlowlogBlbPortList';
            this.$http[requester](param).then(res => {
                let blbListeners = u.filter(res.result, item => item.type === 'TCP' || item.type === 'UDP');
                blbListeners = u.map(blbListeners, item => {
                    return {
                        text: `${item.type}:${item.port}`,
                        value: `${item.type}:${item.port}`,
                        filterInfo: `${value}:${item.type}:${item.port}`
                    };
                });
                this.$http
                    .getFlowlogUsedResource({
                        resourceType: 'blb'
                    })
                    .then(data => {
                        blbListeners = u.filter(
                            blbListeners,
                            item => !u.find(data, info => item.filterInfo === info.resourceId)
                        );
                        this.data.set('blbListeners', blbListeners);
                    });
            });
        }
        if (type === 'csnvpc') {
            this.data.set('formData.csnVpc', '');
            this.data.set('formData.peerRegion', '');
            const {csnType, peerCloud} = this.data.get('formData');
            this.getAvailableCsnCrossRegion(value, csnType, peerCloud);
            const csnInsType = this.data.get('formData.csnInsType');
            const resourceType = csnInsType === 'vpc' ? type : 'csnet';
            let payload = {
                resourceType,
                csnId: value
            };
            this.getEnableResourceData(payload);
        }
    }

    // csn实例类型变化字段
    handleInsTypeChange({value}: any) {
        this.data.set('formData.csnVpc', '');
        const csnId = this.data.get('formData.resourceId');
        const resourceType = value === 'vpc' ? 'csnvpc' : 'csnet';
        const payload = {
            resourceType,
            csnId
        };
        if (csnId) {
            this.getEnableResourceData(payload);
        }
    }

    loadResourceIds(resourceType: string, url: string) {
        let payload = this.getPayload();
        this.$http.loadResourceIds(url, payload).then(res => {
            const resourceIds = u.map(res, item => {
                if (resourceType === this.data.get('resourceTypeList').PORT) {
                    return {
                        text: `${item.name}(${item.eniId})`,
                        value: item.eniUuid
                    };
                } else if (resourceType === this.data.get('resourceTypeList').VPC) {
                    return {
                        text: `${item.name}(${item.shortId})`,
                        value: item.vpcId
                    };
                } else if (resourceType === this.data.get('resourceTypeList').SUBNET) {
                    return {
                        text: `${item.name}(${item.cidr})`,
                        value: item.subnetId
                    };
                } else if (resourceType === this.data.get('resourceTypeList').DEVICE) {
                    return {
                        text: `${item.name}(${item.instanceId})`,
                        value: item.instanceUuid
                    };
                }
                return {
                    text: `${item.name}(${item.shortId})`,
                    value: item.vpcId
                };
            });
            this.data.set(`resourceIds["${resourceType}"]`, resourceIds);
            if (this.data.get('blbFlowlogRes') && !this.data.get('setBlbFlowlogVpc')) {
                if (
                    resourceIds.find(item => item.value === this.data.get('blbFlowlogRes').vpcId) &&
                    this.data.get('blbFlowlogRes').vpcId
                ) {
                    this.data.set('formData.resourceVpc', this.data.get('blbFlowlogRes').vpcId);
                    this.resourceVpcChange({value: this.data.get('blbFlowlogRes').vpcId});
                    this.data.set('setBlbFlowlog', true);
                }
            }
        });
    }

    // 分region查日志集
    loadRegionCollctors() {
        const logStoreType = this.data.get('formData.logStoreType');
        let regionPromises = u.map(region, item => {
            let param = {
                pageNo: 1,
                pageSize: 100
            };
            return this.$http.getCollectors(param, item.region);
        });
        return Promise.all(regionPromises).then(res => {
            let allCollectors = u.reduce(
                res,
                (total, item, index) => {
                    let itemInfo = u.map(item.result, info => {
                        return {
                            ...info,
                            region: region[index].region,
                            text: region[index].text
                        };
                    });
                    return total.concat(itemInfo);
                },
                []
            );
            const collectors = u.map(allCollectors, item => {
                // 先不支持选自定义 bls 日志组
                const disabled = logStoreType === 'bls' && item.project !== 'default';
                return {
                    value: item.logStoreName + '+' + item.region,
                    text: item.logStoreName + '（' + item.text + ')',
                    disabled,
                    disabledMessage: disabled ? '暂不支持选择自定义日志组里的日志集' : ''
                };
            });
            this.data.set('collectors', collectors);
            this.data.set('cloneCollectors', collectors);
            this.data.set('originCollectors', allCollectors);
        });
    }

    loadBlbSubnet() {
        // this.data.set('formData.lbPort', '');
        let param = {
            pageNo: 1,
            pageSize: 1000,
            vpcId: this.data.get('formData.resourceVpc')
        };
        this.$http.vpcSubnetPageList(param).then(res => {
            const resourceIds = u.map(res.result, item => {
                return {
                    text: `${item.name}(${item.cidr})`,
                    value: item.subnetId
                };
            });
            this.data.set('resourceIds.subnet', resourceIds);
            if (this.data.get('blbFlowlogRes') && !this.data.get('setBlbFlowlogSubnet')) {
                if (
                    resourceIds.find(item => item.value === this.data.get('blbFlowlogRes').subnetId) &&
                    this.data.get('blbFlowlogRes').subnetId
                ) {
                    this.data.set('formData.resourceNet', this.data.get('blbFlowlogRes').subnetId);
                    this.data.set('setBlbFlowlogSubnet', true);
                    this.loadBlbResource();
                }
            }
        });
    }

    loadBlbResource() {
        // this.data.set('formData.lbPort', '');
        let param = {
            vpcId: this.data.get('formData.resourceVpc'),
            subnetId: this.data.get('formData.resourceNet')
        };
        this.$http.getFlowlogBlbList(param).then(res => {
            const resourceIds = u.map(res.allBlbVOList, item => {
                return {
                    blbType: item.blbType,
                    text: `${item.name}(${item.blbShortId})`,
                    value: item.blbShortId,
                    disabled: this.data.get('formData.resourceType') === 'blb' ? !item.layer4ClusterExclusive : false,
                    disabledMessage: '仅专属集群上的负载均衡实例支持流日志能力。'
                };
            });
            this.data.set(`resourceIds.${this.data.get('formData.resourceType')}`, resourceIds);
            if (this.data.get('blbFlowlogRes') && !this.data.get('setBlbFlowlogInstance')) {
                if (
                    resourceIds.find(item => item.value === this.data.get('blbFlowlogRes').shortId && !item.disabled) &&
                    this.data.get('blbFlowlogRes').shortId
                ) {
                    this.data.set('formData.resourceId', this.data.get('blbFlowlogRes').shortId);
                    this.data.set('setBlbFlowlogInstance', true);
                    this.resourceTypehange({value: this.data.get('blbFlowlogRes').shortId});
                }
            }
        });
    }

    loadEnableResource(type) {
        let payload = {
            resourceType: type,
            vpcId: this.data.get('formData.resourceVpc')
        };
        if (type === 'csnvpc') {
            payload.resourceType = 'csn';
            delete payload.vpcId;
        } else if (type === 'vpc') {
            delete payload.vpcId;
        } else if (type === 'device') {
            payload.subnetId = this.data.get('formData.resourceNet');
        } else if (type === 'blb' || type === 'blbSeven') {
            payload.resourceType = 'blb';
        }
        if (type === 'csnvpc') {
            this.$http.csnList('pageNo=1&pageSize=1000&keyword=&keywordType=name').then(res => {
                const resourceIds = u.map(
                    u.filter(res?.result || [], it => it.status === 'active'),
                    item => {
                        return {
                            text: `${item.name}(${item.csnId})`,
                            value: item.csnId
                        };
                    }
                );
                this.data.set(`resourceIds.${type}`, resourceIds);
            });
        } else {
            this.$http
                .getEnableResource(payload, type === 'csnvpc' ? kXhrOptions.customSilent : {})
                .then(res => {
                    const resourceIds = u.map(res?.result || [], item => {
                        if (['et', 'nat', 'vpc', 'subnet', 'device', 'port'].includes(type)) {
                            return {
                                text: `${item.name}(${item.showResourceId})`,
                                value: item.resourceId
                            };
                        } else if (type === 'ipv6gw' || type === 'csnvpc') {
                            return {
                                text: `${item.name}(${item.resourceId})`,
                                value: item.resourceId
                            };
                        }
                        return {
                            text: item.showResourceId,
                            value: item.resourceId
                        };
                    });
                    this.data.set(`resourceIds.${type}`, resourceIds);
                })
                .catch(e => {
                    this.data.set(`resourceIds.${type}`, []);
                });
        }
    }

    initResourceIds() {
        if (this.data.get('formData.resourceType') === this.data.get('resourceTypeList').EIP) {
            this.getEipList();
        } else if (
            this.data.get('formData.resourceType') === this.data.get('resourceTypeList').CSN ||
            this.data.get('formData.resourceType') === this.data.get('resourceTypeList').VPC
        ) {
            this.loadEnableResource(this.data.get('formData.resourceType'));
        } else {
            this.loadResourceIds('vpc', requestUrl['vpc']); //eslint-disable-line
        }
    }

    createFlowLog() {
        this.checkFrom()
            .then(() => {
                let formData = this.data.get('formData');
                this.data.set('confirmed', true);
                let resourceType = formData.resourceType;
                let resourceId = '';
                let showResourceId = '';
                let logStoreId = '';
                let logStoreRegion = '';
                let project = '';
                if (formData.logStoreType === 'bls') {
                    let collector = formData.collector.split('+');
                    logStoreId = collector[0];
                    logStoreRegion = collector[1];
                    // 查找日志集所在的日志组
                    const originCollectors = this.data.get('originCollectors');
                    const collectorObj = u.filter(originCollectors, item => item.logStoreName === logStoreId && item.region === logStoreRegion);
                    project = collectorObj?.[0]?.project;
                } else if (formData.logStoreType === 'bos') {
                    let bucketlogUriArray = this.data.get('bucketlogUri').split('//');
                    logStoreId = bucketlogUriArray[1].split('/');
                    if (logStoreId[1]) {
                        logStoreId = logStoreId.shift() + ':' + logStoreId.join('/');
                    } else {
                        logStoreId = logStoreId[0];
                    }
                    logStoreRegion = this.data
                        .get('bucketList')
                        .filter(item => item.name + '/' === formData.collector)[0].location;
                } else if (formData.logStoreType === 'kafka') {
                    let clusterId = formData.clusterId.split('/')[0];
                    logStoreId = clusterId + ':' + formData.topicId;
                    logStoreRegion = formData.clusterId.split('/')[1];
                }
                if (resourceType === 'peerconn') {
                    showResourceId = u.filter(
                        this.data.get('resourceIds.peerconn'),
                        item => item.value === formData.resourceId
                    )[0].text;
                    resourceId = formData.resourceId;
                } else if (resourceType === 'blb') {
                    showResourceId = `${formData.resourceId}:${formData.lbPort}`;
                    resourceId = showResourceId;
                } else if (resourceType === 'csnvpc') {
                    if (formData.csnType === 'netInstance') {
                        showResourceId = `${formData.resourceId}:${formData.csnVpc}`;
                        resourceId = `${formData.resourceId}:${formData.csnVpc}`;
                    } else if (formData.csnType === 'crossRegion') {
                        const suffix = window.$context.getCurrentRegionId() + '_' + formData.peerRegion;
                        showResourceId = `${formData.resourceId}:${suffix}`;
                        resourceId = `${formData.resourceId}:${suffix}`;
                    }
                } else {
                    showResourceId = formData.resourceId;
                    resourceId = formData.resourceId;
                }
                let payload: any = {
                    flowlogName: formData.name,
                    resourceType: formData.resourceType,
                    resourceId: resourceId,
                    showResourceId: showResourceId,
                    action: formData.action,
                    logStoreType: formData.logStoreType,
                    // logStoreId: project ? `${project}:${logStoreId}` : logStoreId, // 拼接日志组和日志集
                    logStoreId: logStoreId,
                    logStoreRegion: logStoreRegion,
                    description: formData.description
                };
                if (formData.csnType === 'crossRegion') {
                    payload.resourceType = 'csn_tgw_peer';
                }
                if (
                    formData.logPrefix &&
                    (resourceType === 'csnvpc' || resourceType === 'et' || resourceType === 'peerconn') &&
                    formData.logStoreType !== 'kafka'
                ) {
                    payload.logPrefix = formData.logPrefix;
                }
                if (['device', 'port'].includes(resourceType)) {
                    payload.subnetId = formData.resourceNet;
                }
                if (['et', 'peerconn', 'csnvpc'].includes(resourceType) && this.data.get('resourceKeyWhiteList')) {
                    payload.enableFiveTuple = formData.resourceKey === 'FIVE';
                }
                if (formData.resourceType === 'blb' || formData.resourceType === 'blbSeven') {
                    payload.resourceType = formData.resourceType === 'blb' ? 'blb' : 'blb_7layer';
                }
                const csnInsType = this.data.get('formData.csnInsType');
                if (csnInsType === 'channel') {
                    payload.resourceType = 'csnet';
                }
                return this.$http.createNatFlowlogs(payload);
            })
            .then(() => {
                location.hash = '#/vpc/flowlog/list';
            })
            .catch(() => this.data.set('confirmed', false));
    }

    cancel() {
        location.hash = '#/vpc/flowlog/list';
    }

    checkRepeat() {
        this.data.set('changeSource', false);
        return this.ref('form')
            .validateFields()
            .then(() => {
                let formData = this.data.get('formData');
                let tableData = this.data.get('tableData') || [];
                if (
                    tableData.some(
                        item => item.resourceId === formData.resourceId && item.collector === formData.collector
                    )
                ) {
                    Notification.error('已存在当前资源对应的日志集');
                    return Promise.reject();
                }
            });
    }

    checkFrom() {
        return this.checkRepeat();
    }

    createCollector() {
        const Dialog = new CreateLog();
        Dialog.on('created', () => {
            // 未实名认证不加载日志列表 避免报错
            if (!this.data.get('isVerifyTip')) {
                this.loadRegionCollctors().then(() => {
                    this.setCollectors();
                });
            }
        });
        Dialog.attach(document.body);
    }
    onRegionChange() {
        location.hash = '#/vpc/flowlog/list';
    }
    getEipList() {
        let payload = {
            pageNo: 1,
            pageSize: 1000
        };
        this.$http.getEipList(payload).then(data => {
            let eips = u.map(data.result || [], item => {
                return {
                    text: `${item.name}（${item.shortId}）`,
                    value: item.shortId
                };
            });
            this.$http
                .getFlowlogUsedResource({
                    resourceType: 'eip'
                })
                .then(data => {
                    eips = u.filter(eips, item => !u.find(data, info => item.value === info.resourceId));
                    this.data.set('resourceIds["eip"]', eips);
                });
        });
    }

    // kafka
    checkKafkaRegion() {
        const region = window.$context.getCurrentRegionId();
        const roleName = StsConfig.FLOWLOG.roleName;
        this.$http
            .iamStsRoleQuery(
                {
                    roleName
                },
                {region: AllRegion.BJ}
            )
            .then((data: any) => {
                if (data?.name) {
                    this.getKafkaList(region);
                }
            });
    }

    getKafkaList(region) {
        let param = {
            pageNo: 1,
            pageSize: 1000,
            region
        };
        this.$http.getKafkaList(param, kXhrOptions.silent).then(res => {
            let kafkaList = u.map(res.clusters, item => {
                return {
                    text: `${item.name}(${AllRegion.getTextFromValue(region)})`,
                    value: item.clusterId + '/' + region
                };
            });
            this.data.set('kafkaList', kafkaList);
        });
    }

    getKafkaTopic(cluster) {
        let clusterId = cluster.split('/')[0];
        let region = cluster.split('/')[1];
        this.$http
            .getKafkaTopic({
                clusterId,
                region,
                keyword: '',
                keywordType: 'topicName'
            })
            .then(res => {
                let topicList = u.map(res.topics, item => {
                    return {
                        text: item.topicName,
                        value: item.topicName
                    };
                });
                this.data.set('kafkaTopicList', topicList);
            });
    }

    clusterIdChange({value}) {
        this.data.set('formData.clusterId', value);
        this.getKafkaTopic(value);
    }
    topicIdChange({value}) {
        this.data.set('formData.topicId', value);
    }
    toKafkaList() {
        this.$context.setRegion('fwh');
        window.open('/kafka/#/kafka/cluster/list');
    }
    selectVisibleChange() {
        this.data.set('changeSource', false);
    }
    showAssist(type: string) {
        Assist.sendMessageToAssist({
            sceneLabel: 'flowlog_create',
            message: type === 'resourceType' ? '支持哪些资源类型创建流日志？有哪些区别？' : ''
        });
    }
    handleCsnTypeChange(target: any) {
        const {value} = target;
        this.data.set('formData.peerRegion', '');
        const {resourceId, peerCloud} = this.data.get('formData');
        this.getAvailableCsnCrossRegion(resourceId, value, peerCloud);
    }
    handlePeerCloudChange(target) {
        const {value} = target;
        this.data.set('formData.peerRegion', '');
        const {resourceId, csnType} = this.data.get('formData');
        this.getAvailableCsnCrossRegion(resourceId, csnType, value);
    }
    async loadCsnEdgeNodes() {
        // 请求csn接口之前判断csn是否授权
        if (window.$storage.get('csnSts') && window.$storage.get('becSts')) {
            try {
                if (!window.$storage.get('bec-resource-open')) {
                    const {result} = await this.$http.getResource();
                    window.$storage.set('bec-resource-open', !!result);
                    if (!!result) {
                        this.$http.getEdgeNode().then(res => {
                            this.data.set('csnEdgeNodes', res.edgeNodes || []);
                        });
                    }
                } else {
                    this.$http.getEdgeNode().then(res => {
                        this.data.set('csnEdgeNodes', res.edgeNodes || []);
                    });
                }
            } catch (error) {
                window.$storage.set('bec-resource-open', false);
            }
        }
    }
}
export default San2React(Processor.autowireUnCheckCmpt(FlowlogCreate));
