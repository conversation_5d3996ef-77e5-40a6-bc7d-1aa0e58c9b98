/*
 * @description: 日志集创建
 * @file: flowlog/Log/log.js
 * @author: <EMAIL>
 */
import {defineComponent} from 'san';
import {Form, Input, Dialog, Select, Button, Notification, InputNumber} from '@baidu/sui';
import {html} from '@baiducloud/runtime';

import './style.less';

const AllRegion = window.$context.getEnum('AllRegion');


const template = html`
<div>
    <s-dialog class="flowlog-log-wrapper" open="{=open=}" title="新建日志集">
        <s-form s-ref="form"
            data="{=formData=}"
            rules="{{rules}}"
            label-align="left"> 
            <s-form-item
                label="{{'地域：'}}"
                class="region"
                prop="region">
                <s-select value="{=formData.region=}" width="300" datasource="{{blsRegions}}"/>
            </s-form-item>
            <s-form-item prop="logStoreName" label="名称：" help="规则：1-128位字符、数字、英文和符号，符号仅限：_-.">
                <s-input width="300" value="{=formData.logStoreName=}"
                    placeholder="名称创建后不可修改，同一region下名称需唯一"/>
            </s-form-item>
            <s-form-item prop="retention" label="存储周期：" help="存储周期上限180天">
                <s-input-number value="{=formData.retention=}" max="{{180}}" min="{{1}}" />
            </s-form-item>
        </s-form>
        <div slot="footer">
            <s-button on-click="closeDialog">取消</s-button>
            <s-button skin="primary" disabled="{{confirmed}}" on-click="createLog">确定</s-button>
        </div>
    </s-dialog>
</div>
`;

export default defineComponent({
    template,
    components: {
        's-form': Form,
        's-form-item': Form.Item,
        's-input': Input,
        's-select': Select,
        's-input-number': InputNumber,
        's-dialog': Dialog,
        's-button': Button
    },
    initData() {
        return {
            open: true,
            formData: {
                logStoreName: '',
                retention: 1
            },
            blsRegions: [],
            confirmed: false,
            rules: {
                logStoreName: [
                    {required: true, message: '请输入名称'},
                    {
                        validator: (rule, value, callback) => {
                            let pattern  = /^[\w\-\.]{1,128}$/;
                            if (!pattern.test(value)) {
                                return callback('1-128位字符、数字、英文和符号，符号仅限：_-.');
                            }
                            callback();
                        }
                    }
                ],
                region: [
                    {
                        validator: (rules, value, callback) => {
                            value = value || this.data.get('formData.region');
                            if (!value) {
                                return callback('请选择地域');
                            }
                            callback();
                        }
                    }
                ]
            }
        };
    },

    inited() {
        this.queryRegionList();
    },

    async createLog() {
        await this.ref('form').validateFields();
        this.data.set('confirmed', true);
        let payload = this.data.get('formData');
        const region = payload.region;
        this.$http.createFlowlogCollector(payload, region)
        .then((res) => {
            Notification.success('创建成功');
            this.data.set('confirmed', false);
            this.fire('created');
            this.closeDialog();
        })
        .catch(({message}) => {
            this.data.set('confirmed', false);
            let errText = (message && message.detail) || '创建失败';
            Notification.error(errText);
            this.fire('error');
        });
    },

    queryRegionList() {
        return this.$http.getLogRegionList().then((res) => {
            let blsRegions = [];
            if (res.BLS && res.BLS.length) {
                res.BLS.forEach((item) => {
                    blsRegions.push({
                        text: AllRegion.getTextFromValue(item),
                        value: item
                    });
                });
            }
            this.data.set('blsRegions', blsRegions);
        });
    },

    closeDialog() {
        this.dispose();
    }
});
