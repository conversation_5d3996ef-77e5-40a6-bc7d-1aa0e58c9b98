/*
 * @description: 流日志列表页
 * @file: flowlog/List.js
 * @author: <EMAIL>
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {OutlinedRefresh, OutlinedPlus, OutlinedEditingSquare, OutlinedDocument} from '@baidu/sui-icon';
import {Input} from '@baidu/sui';
import Confirm from '@/pages/sanPages/components/confirm';
import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import IntroducePanel from '@/pages/sanPages/components/introducePanel/introducePanel';
import {
    ResourceType,
    ResourceTypeRegionList,
    Action,
    FlowlogSearchType,
    FlowLogStatus
} from '@/pages/sanPages/common/enum';
import {utcToTime, $flag as FLAG} from '@/pages/sanPages/utils/helper';
import './list.less';
import {ContextService, DocService} from '@/pages/sanPages/common';
import {StsConfig} from '@/pages/sanPages/utils/config';
import {columns} from './tableFields';

const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, invokeComp} = decorators;
const AllRegion = window.$context.getEnum('AllRegion');
const kXhrOptions = {'x-silent': true};
// kafka只支持 bj gz su fwh bd
const kafkaRegion = ['bj', 'gz', 'su', 'fwh', 'bd'];

// blb支持流日志的当前的区域只有yq

const tpl = html` <div>
    <s-biz-page class="{{klass}}">
        <div class="vpc-flowlog-header" slot="header">
            <div class="header-wrap">
                <span class="title">{{title}}</span>
                <div class="link-widget">
                    <a
                        s-ref="introduce"
                        href="javascript:void(0)"
                        class="help-file function-introduce"
                        on-click="handleShowCard"
                        on-mouseenter="handleMouseEnter('introduce')"
                        on-mouseleave="handleMouseLeave('introduce')"
                        s-if="{{!FLAG.NetworkSupportXS}}"
                    >
                        <img class="s-icon" src="{{introduceIcon}}" />功能简介
                    </a>
                    <a
                        href="{{DocService.flowlog_helpFile}}"
                        target="_blank"
                        class="help-file"
                        s-if="{{!FLAG.NetworkSupportXS}}"
                    >
                        <s-icon name="warning-new" />帮助文档
                    </a>
                </div>
            </div>
            <introduce-panel
                isShow="{{show}}"
                klass="{{'endpoint-peerconn-wrapper'}}"
                title="{{introduceTitle}}"
                markerDesc="{{markerDesc}}"
                introduceOptions="{{introduceOptions}}"
                on-toggle="handleToggle($event)"
            ></introduce-panel>
        </div>
        <div class="list-page-tb-left-toolbar" slot="tb-left">
            <s-tip-button
                skin="primary"
                isDisabledVisibile="{{true}}"
                disabled="{{createFlowlog || !enableCreate}}"
                on-click="onCreate"
            >
                <outlined-plus />
                创建流日志
                <div slot="content">{{createFlowlogMessage || enableCreateTip}}</div>
            </s-tip-button>
            <s-tip-button
                class="left_class"
                content="请先选中实例"
                isDisabledVisibile="{{true}}"
                disabled="{{!enableDelete}}"
                on-click="onDelete"
                >删除</s-tip-button
            >
        </div>
        <div slot="tb-right" class="toolbar_class">
            <s-search
                width="{{230}}"
                class="search-warp"
                value="{=payload.keyword=}"
                placeholder="{{searchholder}}"
                on-search="enterSearch($event)"
            >
                <s-select
                    slot="options"
                    width="120"
                    datasource="{{searchType}}"
                    value="{=payload.keywordType=}"
                    on-change="searchTypeChange"
                >
                </s-select>
            </s-search>
            <s-button on-click="refresh" class="left_class s-icon-button"
                ><outlined-refresh class="icon-class"
            /></s-button>
            <custom-column
                class="left_class"
                columnList="{{customColumn.datasource}}"
                initValue="{{customColumn.value}}"
                type="flowlog"
                on-init="initColumns"
                on-change="onCustomColumns"
            >
            </custom-column>
        </div>
        <s-table
            s-ref="table"
            on-filter="filterTable"
            columns="{{table.columns}}"
            loading="{{table.loading}}"
            error="{{table.error}}"
            datasource="{{table.datasource}}"
            selection="{=table.selection=}"
            select
        >
            <div slot="empty">
                <s-empty
                    emptyTitle="您还没有创建任何实例"
                    emptyText="创建流日志可以为用户提供流量分析、可视化、故障诊断/定位以及网络架构调优的能力。"
                    on-click="onCreate"
                />
            </div>
            <div slot="error">
                啊呀，出错了？
                <a href="javascript:;" on-click="refresh">重新加载</a>
            </div>
            <div slot="c-id" class="flowlog-id-widget">
                <span class="text-hidden flow-name">{{row.flowlogName ? row.flowlogName : '-'}}</span>
                <s-popover
                    class="edit-popover-class"
                    s-ref="{{'instanceNameEdit' + rowIndex}}"
                    placement="top"
                    trigger="click"
                >
                    <div class="edit-wrap" slot="content">
                        <s-input
                            value="{=instance.name=}"
                            width="320"
                            placeholder="请输入"
                            on-input="onNameInput($event,row, rowIndex, 'Name')"
                        />
                        <div class="edit-tip">
                            支持大小写字母、数字、中文以及-_ /.特殊字符，必须以字母开头，长度1-65
                        </div>
                        <s-button
                            skin="primary"
                            s-ref="{{'editBtnName' + rowIndex}}"
                            disabled="{{true}}"
                            on-click="onEdit(row,rowIndex)"
                            >确定</s-button
                        >
                        <s-button on-click="editCancel(row,rowIndex,'Name')">取消</s-button>
                    </div>
                    <outlined-editing-square class="name-icon" on-click="beforeEdit(row)" />
                </s-popover>
                <br />
                {{row.flowlogId ? row.flowlogId : '-'}}
                <s-clip-board class="name-icon" text="{{row.flowlogId}}" />
            </div>
            <div slot="c-enable">
                <span class="{{row.enable | statusClass}}">{{row.enable | statusText}}</span>
            </div>
            <div slot="c-logStoreType">
                <span>{{row.logStoreType | logStoreTypeText}}</span>
            </div>
            <div slot="c-collector">
                <a
                    class="flowlog-link"
                    href="javascript:;"
                    on-click="toCollectorDetail(row)"
                    s-if="row.logStoreType !== 'kafka'"
                >
                    {{row | kafkaLogStoreText}}
                </a>
                <s-tooltip placement="top" s-else>
                    <!--bca-disable-next-line-->
                    <div slot="content">{{(row | kafkaLogStoreText) | raw}}</div>
                    <a class="flowlog-link" target="_BLANK" on-click="toKafkaDetail(row)" s-if="{{kafkaLink}}">
                        {{row | kafkaLogStoreText}}
                    </a>
                    <span s-else>{{row | kafkaLogStoreText}}</span>
                </s-tooltip>
            </div>
            <div slot="c-createTime">{{row.createTime | timeFormat}}</div>
            <div slot="c-description">
                <s-tooltip content="{{row.description || '-'}}" class="flow-desc">
                    <span class="text-hidden flow-desc">{{row.description || '-'}}</span>
                </s-tooltip>
                <s-popover
                    s-ref="{{'instanceDescEdit' + rowIndex}}"
                    placement="top"
                    trigger="click"
                    class="edit-popover-class edit-flowlog"
                >
                    <div class="edit-wrap" slot="content">
                        <s-textarea
                            value="{=instance.desc=}"
                            width="200"
                            height="60"
                            placeholder="请输入描述，最多200个字符"
                            maxLength="200"
                            on-input="onNameInput($event,row, rowIndex, 'Desc')"
                        />
                        <s-button
                            skin="primary"
                            s-ref="{{'editBtnDesc' + rowIndex}}"
                            disabled="{{true}}"
                            on-click="onEdit(row,rowIndex)"
                            >确定</s-button
                        >
                        <s-button on-click="editCancel(row,rowIndex, 'Desc')">取消</s-button>
                    </div>
                    <outlined-editing-square class="name-icon" on-click="beforeEdit(row)" />
                </s-popover>
            </div>
            <div slot="c-logPrefix">{{row.logPrefix || '-'}}</div>
            <div slot="c-opt">
                <span class="operations">
                    <s-button s-if="{{row.enable === true}}" skin="stringfy" on-click="onStop(row, 'False')">
                        暂停</s-button
                    >
                    <s-button s-else skin="stringfy" on-click="onStop(row, 'True')"> 开启</s-button>
                    <s-button skin="stringfy" on-click="onDelete(row)">删除</s-button>
                </span>
            </div>
        </s-table>
        <s-pagination
            s-if="{{pager.total}}"
            slot="footer"
            layout="{{'total, pageSize, pager, go'}}"
            pageSize="{{pager.size}}"
            total="{{pager.total}}"
            page="{{pager.page}}"
            on-pagerChange="onPageChange"
            on-pagerSizeChange="onPageSizeChange"
        />
    </s-biz-page>
</div>`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@invokeComp('@custom-column')
class VpcFlowLogList extends Component {
    static components = {
        'outlined-refresh': OutlinedRefresh,
        'introduce-panel': IntroducePanel,
        'outlined-plus': OutlinedPlus,
        'outlined-editing-square': OutlinedEditingSquare,
        'outlined-doc': OutlinedDocument,
        's-textarea': Input.TextArea
    };
    static filters = {
        timeFormat(value) {
            return value ? utcToTime(value) : '-';
        },
        statusClass(value) {
            return FlowLogStatus.fromValue(value).styleClass || '';
        },
        statusText(value) {
            return FlowLogStatus.getTextFromValue(value) || '';
        },
        collectorHref(row) {
            if (row.logStoreType === 'bls') {
                return `/bls/#/bls/log/detail?logStoreName=${row.logStoreId}&logStreamName`;
            } else if (row.logStoreType === 'bos') {
                let bosArray = row.logStoreId.split(':');
                return (
                    `/bos/#/bos/bucket?name=${bosArray[0]}&location=${row.logStoreRegion}&tab=object` +
                    (bosArray[1] ? '&path=' + bosArray[1] : '')
                );
            }
        },
        kafkaLogStoreText(row) {
            if (row.logStoreType === 'bls') {
                // 根据:拆分出日志组和日志集
                const logStoreIdArray = row.logStoreId.split(':');
                const logStoreId = logStoreIdArray?.length > 1 ? logStoreIdArray[1] : row.logStoreId;
                return logStoreId + '(' + AllRegion.getTextFromValue(row.logStoreRegion) + ')';
            } else if (row.logStoreType === 'bos') {
                return row.logStoreId;
            } else if (row.logStoreType === 'kafka') {
                let topicName = row.logStoreId.split(':')[1];
                let cluster = this.data.get('kafkaList').find(item => {
                    return item.clusterId === row.logStoreId.split(':')[0];
                });
                if (cluster) {
                    this.data.set('kafkaLink', true);
                    return `${cluster.name}-${topicName}(${AllRegion.getTextFromValue(row.logStoreRegion)})`;
                }
                this.data.set('kafkaLink', false);
                return row.logStoreId;
            }
        },
        logStoreTypeText(logStoreType) {
            const textMap = {
                bls: 'BLS',
                bos: 'BOS',
                kafka: 'Kafka'
            };
            return textMap[logStoreType];
        }
    };
    static computed = {
        enableCreate() {
            const quota = this.data.get('quota');
            return quota && quota.free > 0;
        },
        enableCreateTip() {
            const quota = this.data.get('quota');
            return quota && quota.free > 0 ? '' : '流日志配额不足。如需提升配额，请提交工单';
        },
        enableDelete() {
            const table = this.data.get('table');
            return table.selection && table.selection.selectedIndex && table.selection.selectedIndex.length === 1;
        }
    };
    initData() {
        const allColumns = columns.slice();
        const customColumnDb = allColumns.map(item => ({
            text: item.label,
            value: item.name,
            disabled: item.name === 'id' || item.name === 'opt'
        }));
        return {
            klass: ['main-wrap-new flowlog-wrap'],
            title: '流日志',
            show: true,
            introduceTitle: '流日志简介',
            markerDesc: [
                '流日志用于记录弹性公网IP、NAT网关、IPv6网关、对等连接、专线网关 和云智能网实例发送和接受的网络流信息，可以为用户提供流量分析、可视化、故障诊断/定位以及网络架构调优的能力。',
                ' 流日志的核心字段为两元组、统计信息、时间戳，以及流量类型（允许）等。'
            ],
            introduceIcon: INTRODUCE_ICON_COLOR,
            introduceEle: null,
            filterKeys: {
                action: '',
                resourceType: '',
                logStoreType: ''
            },
            searchType: FlowlogSearchType.toArray(),
            searchholder: '请输入实例名称进行搜索',
            payload: {
                keyword: '',
                keywordType: 'flowlogName'
            },
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                columns: [
                    {name: 'id', label: '实例名称/ID', width: 160, fixed: 'left'},
                    {
                        name: 'resourceType',
                        label: '资源类型',
                        width: 150,
                        filter: {
                            options: [{text: '全部', value: ''}, ...ResourceTypeRegionList.toArray()],
                            value: 'all'
                        },
                        render(item, key, col, rowIndex, colIndex, data) {
                            let resourceType = item.resourceType;
                            if (resourceType === 'csnet') {
                                resourceType = 'csnvpc';
                            }
                            return (
                                ResourceType.getTextFromValue(resourceType) ||
                                (resourceType === 'blb_7layer'
                                    ? '负载均衡-七层'
                                    : resourceType === 'blb'
                                      ? '负载均衡-四层'
                                      : '-')
                            );
                        }
                    },
                    {
                        name: 'resourceId',
                        key: 'resourceShortId',
                        label: '资源ID',
                        width: 140,
                        render(item, key, col, rowIndex, colIndex, data) {
                            return `<span class="">${item.showResourceId}</span>`;
                        }
                    },
                    {name: 'enable', label: '状态', width: 100},
                    {
                        name: 'action',
                        label: '流量类型',
                        width: 120,
                        filter: {
                            options: [{text: '全部', value: ''}, ...Action.toArray()],
                            value: 'all'
                        },
                        render(item, key, col, rowIndex, colIndex, data) {
                            return Action.getTextFromValue(item.action) || '-';
                        }
                    },
                    {
                        name: 'logStoreType',
                        label: '目的地类型',
                        width: 140,
                        filter: {
                            options: [
                                {text: '全部', value: ''},
                                {text: 'bls', value: 'bls'},
                                {text: 'bos', value: 'bos'},
                                {text: 'kafka', value: 'kafka'}
                            ],
                            value: 'all'
                        }
                    },
                    {name: 'collector', label: '目的地', width: 140},
                    {name: 'createTime', label: '创建时间', width: 150},
                    {name: 'description', label: '描述', width: 160},
                    {name: 'logPrefix', label: '日志前缀', width: 160},
                    {name: 'opt', label: '操作', fixed: 'right', width: 120}
                ],
                allColumns,
                datasource: []
            },
            instance: {
                name: '',
                desc: ''
            },
            pager: {
                size: 10,
                page: 1,
                total: 0
            },
            customColumn: {
                value: [
                    'id',
                    'resourceType',
                    'resourceId',
                    'enable',
                    'action',
                    'logStoreType',
                    'collector',
                    'createTime',
                    'description',
                    'logPrefix',
                    'opt'
                ],
                datasource: customColumnDb
            },
            enableCreate: false,
            createFlowlog: false,
            kafkaList: [],
            kafkaLink: true,
            DocService,
            FLAG,
            showIntroduce: true
        };
    }
    inited() {
        const AllRegion = ContextService.getEnum('AllRegion');
        if (window.$context.getCurrentRegionId() === AllRegion.SIN) {
            this.data.set('createFlowlog', true);
            this.data.set('createFlowlogMessage', '新加坡地域资源售罄，请您切换到其他地域创建');
        }
    }
    attached() {
        window.$storage.get('showFlowlogIntroduce') === false && this.data.set('showIntroduce', false);
        this.getQuota();
        if (kafkaRegion.includes(window.$context.getCurrentRegionId())) {
            this.checkKafkaSup();
        } else {
            this.loadPage();
        }
        this.data.set('introduceEle', this.ref('introduce'));
    }

    getQuota() {
        this.$http.getFlowlogQuota().then(res => {
            this.data.set('quota', res);
        });
    }

    loadPage(payload) {
        this.data.set('table.loading', true);
        this.resetTable();
        payload = payload || this.getSearchCriteria();
        if (payload.resourceType === 'csnvpc') {
            payload.resourceType = 'csn';
        }
        this.$http.flowlogList(payload).then(data => {
            this.getQuota();
            this.data.set('table.datasource', data.result);
            this.data.set('pager.total', data.totalCount);
            this.data.set('table.loading', false);
        });
    }

    filterTable({action, resourceType, logStoreType}) {
        let filterKeys = this.data.get('filterKeys');
        let payload = {
            action: action !== undefined ? action : filterKeys.action,
            resourceType: resourceType !== undefined ? resourceType : filterKeys.resourceType
        };
        logStoreType &&
            ((payload.keywordType = 'logStoreType'),
            (payload.keyword = logStoreType !== undefined ? logStoreType : filterKeys.logStoreType));
        payload.resourceType === 'blbSeven' && (payload.resourceType = 'blb_7layer');
        this.data.set('filterKeys', payload);
        this.loadPage();
    }

    onCreate() {
        location.hash = '#/vpc/flowlog/create';
    }

    onDelete(item = {}) {
        if (!item.flowlogId) {
            let select = this.ref('table').getSelectedItems();
            item.flowlogId = select[0].flowlogId;
            item.flowlogName = select[0].flowlogName;
        }
        const confirm = new Confirm({
            data: {
                open: true,
                title: '删除流日志',
                content: `您确定要删除流日志：${item.flowlogName} / ${item.flowlogId} 吗？`
            }
        });
        confirm.on('confirm', () => {
            let payload = {
                flowlogIds: [item.flowlogId]
            };
            this.$http.deleteFlowlog(payload).then(() => this.loadPage());
        });
        confirm.attach(document.body);
    }

    beforeEdit(row) {
        this.data.set('instance.name', row.flowlogName);
        this.data.set('instance.desc', row.description);
    }

    onEdit(item) {
        let enableMap = {
            false: 'False',
            true: 'True'
        };
        let instance = this.data.get('instance');
        let payload = {
            flowlogId: item.flowlogId,
            flowlogName: instance.name,
            description: instance.desc,
            enable: enableMap[item.enable]
        };
        return this.$http.updateFlowlog(payload).then(() => this.loadPage());
    }

    editCancel(row, rowIndex, type) {
        this.ref(`instance${type}Edit${rowIndex}`).data.set('visible', false);
    }

    onNameInput(e, row, rowIndex, type) {
        let result;
        let editType = type.toLocaleLowerCase();
        if (type === 'Name') {
            result = e.value.length <= 128 && /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\/\.]{0,64}$/.test(e.value);
        } else {
            result = e.value.length <= 200;
        }
        this.data.set(`instance.${editType}`, e.value);
        this.ref(`editBtn${type}${rowIndex}`).data.set('disabled', !result);
    }

    onStop(item, enable) {
        let payload = {enable, flowlogId: item.flowlogId};
        if (enable === 'False') {
            const confirm = new Confirm({
                data: {
                    open: true,
                    title: '暂停流日志',
                    content: `您确定要暂停流日志：${item.flowlogName} / ${item.flowlogId} 吗？`
                }
            });
            confirm.on('confirm', () => {
                this.$http.enableFlowlog(payload).then(() => this.loadPage());
            });
            confirm.attach(document.body);
            return;
        }
        return this.$http.enableFlowlog(payload).then(() => this.loadPage());
    }

    searchTypeChange({value}) {
        let tip = `请输入${FlowlogSearchType.getTextFromValue(value)}进行搜索`;
        this.data.set('searchholder', tip);
    }

    onSearch() {
        this.data.set('pager.page', 1);
        return this.loadPage();
    }

    refresh() {
        return this.loadPage();
    }

    handleInputChange({value}) {
        this.data.set('payload.keyword', value);
    }

    enterSearch(e) {
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    // 重置列表
    resetTable() {
        this.data.set('table.selection.selectedIndex', []);
    }

    getSearchCriteria() {
        const pager = this.data.get('pager');
        const payload = this.data.get('payload');
        const filter = this.data.get('filterKeys');
        return u.extend({}, payload, filter, {pageNo: pager.page, pageSize: pager.size});
    }

    onPageChange({value}) {
        this.data.set('pager.page', value.page);
        this.loadPage();
    }

    onPageSizeChange({value}) {
        this.data.set('pager.size', value.pageSize);
        this.data.set('pager.page', 1);
        this.loadPage();
    }
    onRegionChange() {
        location.reload();
    }
    // kafka
    // 判断kafka是否开通
    checkKafkaSup() {
        this.checkKafkaRegion();
    }
    checkKafkaRegion() {
        const region = window.$context.getCurrentRegionId();
        const roleName = StsConfig.FLOWLOG.roleName;
        this.$http
            .iamStsRoleQuery(
                {
                    roleName
                },
                {region: AllRegion.BJ}
            )
            .then((data: any) => {
                if (data?.name) {
                    this.getKafkaList(region);
                } else {
                    this.loadPage();
                }
            });
    }
    getKafkaList(region) {
        let param = {
            pageNo: 1,
            pageSize: 1000,
            region
        };
        this.$http
            .getKafkaList(param, kXhrOptions)
            .then(res => {
                let kafkaList = u.map(res.clusters, item => {
                    return {
                        text: `${item.name}(${AllRegion.getTextFromValue(region)})`,
                        value: item.clusterId + '/' + region,
                        clusterId: item.clusterId,
                        name: item.name
                    };
                });
                this.data.set('kafkaList', kafkaList);
                this.loadPage();
            })
            .catch(() => {
                this.loadPage();
            });
    }
    toKafkaDetail(row) {
        this.$context.setRegion(row.logStoreRegion);
        let topicName = row.logStoreId.split(':')[1];
        let cluster = this.data.get('kafkaList').find(item => {
            return item.clusterId === row.logStoreId.split(':')[0];
        });
        window.open(
            `/kafka/#/kafka/cluster/detail/topic/info?name=${cluster.name}&topic=${topicName}&clusterId=${cluster.clusterId}`
        );
    }
    toCollectorDetail(row) {
        if (row.logStoreType === 'bls') {
            this.$context.setRegion(row.logStoreRegion);
            // 根据:拆分出日志组和日志集
            const logStoreIdArray = row.logStoreId.split(':');
            const logStoreId = logStoreIdArray?.length > 1 ? logStoreIdArray[1] : row.logStoreId;
            const project = logStoreIdArray?.length > 1 ? logStoreIdArray[0] : 'default';
            window.open(`/bls/#/bls/search/log?logStoreName=${logStoreId}&logStreamName&project=${project}`);
        } else if (row.logStoreType === 'bos') {
            let bosArray = row.logStoreId.split(':');
            window.open(
                `/bos/#/bos/bucket?name=${bosArray[0]}&location=${row.logStoreRegion}&tab=object` +
                    (bosArray[1] ? '&path=' + bosArray[1] : '')
            );
        }
    }
    hideDesc() {
        this.data.set('showIntroduce', false);
        window.$storage.set('showFlowlogIntroduce', false);
    }
    showDesc() {
        this.data.set('showIntroduce', true);
        window.$storage.set('showFlowlogIntroduce', true);
    }
    // 设置表格列
    setTableColumns(customColumnNames) {
        customColumnNames = customColumnNames ? customColumnNames : this.data.get('customColumn.value');
        const allColumns = this.data.get('table.allColumns');
        let columns = [];
        allColumns.forEach(item => customColumnNames.indexOf(item.name) > -1 && columns.push(item));
        this.data.set('table.columns', columns);
    }

    initColumns(value) {
        this.setTableColumns(value);
    }
    // 自定义表格列
    onCustomColumns(value) {
        this.data.set('customColumn.value', value);
        this.setTableColumns(value);
    }
    handleToggle(flag) {
        this.data.set('show', flag);
        window.$storage.set('showProbeIntroduce', flag);
        this.data.set('introduceIcon', INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#151B26';
    }
    handleShowCard() {
        if (!this.data.get('show')) {
            this.data.set('show', true);
            window.$storage.set('showProbeIntroduce', true);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type) {
        if (type === 'introduce') {
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
            const ele = this.data.get('introduceEle');
            ele.style.color = '#2468F2';
        }
    }
    handleMouseLeave(type) {
        const isShow = this.data.get('show');
        if (type === 'introduce') {
            this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
            const ele = this.data.get('introduceEle');
            !isShow && (ele.style.color = '#151B26');
        }
    }
}
export default San2React(Processor.autowireUnCheckCmpt(VpcFlowLogList));
