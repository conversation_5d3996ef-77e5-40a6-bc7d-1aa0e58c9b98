import {
    ResourceType,
    Action,
    flowlogCollectType,
    flowlogInstanceType,
    flowlogInterworkType
} from '@/pages/sanPages/common/enum';

export const columns = [
    {name: 'id', label: '实例名称/ID', width: 160, fixed: 'left'},
    {
        name: 'resourceType',
        label: '资源类型',
        width: 150,
        filter: {
            options: [{text: '全部', value: ''}, ...ResourceType.toArray()],
            value: 'all'
        },
        render(item, key, col, rowIndex, colIndex, data) {
            let resourceType = item.resourceType;
            if (['csnet', 'csn_tgw_peer'].includes(resourceType)) {
                resourceType = 'csn';
            }
            if (resourceType === 'csn') {
                resourceType = 'csnvpc';
            }
            return ResourceType.getTextFromValue(resourceType) || '-';
        }
    },
    {
        name: 'resourceId',
        key: 'resourceShortId',
        label: '资源ID',
        width: 380,
        render(item, key, col, rowIndex, colIndex, data) {
            return `<span class="">${item.showResourceId}</span>`;
        }
    },
    {name: 'enable', label: '状态', width: 100},
    {
        name: 'action',
        label: '流量类型',
        width: 120,
        filter: {
            options: [{text: '全部', value: ''}, ...Action.toArray()],
            value: 'all'
        },
        render(item, key, col, rowIndex, colIndex, data) {
            return Action.getTextFromValue(item.action) || '-';
        }
    },
    {
        name: 'logStoreType',
        label: '目的地类型',
        width: 140,
        filter: {
            options: [
                {text: '全部', value: ''},
                {text: 'BLS', value: 'bls'},
                {text: 'BOS', value: 'bos'},
                {text: 'Kafka', value: 'kafka'}
            ],
            value: 'all'
        }
    },
    {name: 'collector', label: '目的地', width: 140},
    {name: 'createTime', label: '创建时间', width: 150},
    {name: 'description', label: '描述', width: 160},
    {name: 'logPrefix', label: '日志前缀', width: 160},
    {name: 'opt', label: '操作', fixed: 'right', width: 120}
];
