.flowlog-create-wrapper {
    .form-resource {
        margin-left: 83px;
    }
    .flowlog-net-wrap {
        .s-form-item {
            display: inline-block;
            .s-form-item-control-wrapper {
                width: auto;
            }
        }
        .s-form-item-label > label {
            margin-right: 0;
        }
        .s-form-item-label {
            width: auto;
        }
    }
    .s-selectdropdown {
        max-width: 150px;
    }
    .text-area {
        .s-textarea-wraper textarea {
            box-sizing: border-box;
        }
    }
    .collector-button {
        height: 30px;
        box-sizing: border-box;
    }
}
.flow-search-input {
    .s-input-addon-before {
        padding: 0 !important;
        .s-input-suffix-container {
            border: none;
        }
    }
    .s-input-area {
        height: 100%;
    }
    .s-icon {
        font-size: 14px;
    }
}
.flowlog-wrap {
    height: 100%;
    overflow-y: auto;
    background: #f7f7f9 !important;
    .s-biz-page-header {
        margin: 0px !important;
        background-color: #fff;
        height: auto !important;
        line-height: 47px;
        border: none !important;
        .s-biz-page-title {
            h2 {
                height: 47px !important;
                color: #151b26 !important;
                line-height: 47px !important;
                font-weight: 500 !important;
                font-size: 16px !important;
                margin-left: 16px !important;
            }
        }
    }
    .s-biz-page-content {
        border-radius: 6px;
        margin: 16px !important;
        padding: 24px;
        background: #fff;
        .s-biz-page-toolbar {
            margin: 0px;
        }
        .s-biz-page-body {
            margin-top: 16px;
            .s-table {
                .s-table-body {
                    max-height: calc(~'100vh - 334px');
                    .flowlog-id-widget {
                        white-space: nowrap;
                    }
                }
            }
        }
        .s-biz-page-footer {
            padding-bottom: 0px;
            margin-top: 16px;
        }
    }
    .flow-blue-tit {
        color: #209bfd;
    }
    .flow-icon {
        display: inline-block;
        margin-right: 5px;
    }
    .name-icon {
        font-size: 12px;
        position: relative;
        top: -1px;
        fill: #2468f2;
        color: #2468f2;
        display: none;
    }
    .s-table-cell:hover {
        .name-icon {
            display: inline;
        }
    }

    .text-hidden {
        display: inline-block;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        max-width: 120px;
        vertical-align: middle;
    }
    .flow-search-select {
        .s-select-option-list {
            text-align: left;
        }
    }
    .list-page-tb-left-toolbar,
    .toolbar_class {
        display: inline-flex;
    }
    .operations {
        .s-button {
            padding: 0;
            margin-right: 12px;
        }
    }
    .vpc-flowlog-header {
        align-items: center;
        background-color: #f7f7f7;
        .title {
            display: inline-block;
            margin: 0;
            color: #151b26;
            margin-right: 12px;
            height: 47px;
            line-height: 47px;
            font-weight: 500;
            font-size: 16px;
        }

        .header-wrap {
            background-color: #fff;
            padding-left: 16px;
            display: flex;
            justify-content: space-between;
            .link-widget {
                display: flex;
                .help-file {
                    margin-right: 16px;
                    font-size: 12px;
                    font-weight: 400;
                    .s-icon {
                        position: relative;
                        top: -1px;
                        right: 4px;
                        font-size: 14px;
                        margin-right: 0;
                        color: #2468f2;
                    }
                }
            }
        }
    }
    .diagnosis-intro {
        @media screen and (max-width: 1280px) {
            background:
                url('https://bce.bdstatic.com/network-frontend/eip-bg-1280.png') no-repeat,
                linear-gradient(140deg, #f3f7ff 0%, #cddbf3 100%);
        }
        @media screen and (max-width: 1440px) {
            background:
                url('https://bce.bdstatic.com/network-frontend/eip-bg-1440.png') no-repeat,
                linear-gradient(140deg, #f3f7ff 0%, #cddbf3 100%);
        }
        @media screen and (min-width: 1440px) {
            background:
                url('https://bce.bdstatic.com/network-frontend/eip-bg-1920.png') no-repeat,
                linear-gradient(140deg, #f3f7ff 0%, #cddbf3 100%);
        }
        background-size: 100% 120px;
        display: flex;
        flex-direction: column;
        padding: 24px;
        margin: 16px;
        border-radius: 6px;
        .diagnosis-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .title {
                color: #151b26;
                font-weight: 500;
                font-size: 20px;
                line-height: 28px;
                height: 28px;
            }
            .hide-desc {
                align-self: flex-end;
                padding: 0px;
            }
        }
        .diagnosis-desc {
            margin-top: 16px;
            font-size: 12px;
            color: #5c5f66;
            font-weight: 400;
            line-height: 20px;
        }
    }
    .flowlog-desc-class {
        line-height: 28px;
        .marker {
            border: 2px solid #2468f2;
            display: inline-block;
            border-radius: 50%;
            width: 8px;
            height: 8px;
            background-color: transparent;
            margin-right: 4px;
        }
        .flowlog-word-desc {
            display: inline-block;
            margin-top: 12px;
            color: #151b26;
            height: 18px;
            line-height: 18px;
        }
    }
    .introduce-desc {
        float: right;
        margin-right: 90px;
        .outlined-doc {
            position: relative;
            top: -1px;
            .s-icon path {
                fill: #2468f2;
            }
        }
    }
    .introduce-desc-grey {
        float: right;
        margin-right: 90px;
        .outlined-doc {
            position: relative;
            top: -1px;
            .s-icon path {
                fill: #151b26;
            }
        }
        span {
            color: #151b26;
        }
    }
    .introduce-panel .introduce-desc-class {
        line-height: 16px;
    }
}

.flowlog-confirm {
    .s-dialog > .s-dialog-wrapper .s-dialog-header {
        background: #fff;
    }
    .s-dialog > .s-dialog-wrapper .s-dialog-content {
        min-height: 50px;
        .s-icon {
            margin-right: 15px;
        }
    }
    .icon-warning-new {
        color: #fbb515;
        font-size: 22px;
        vertical-align: middle;
        margin-right: 20px;
    }
}
.flowlog-pop {
    .s-popover-content {
        width: 290px;
    }
}

.edit-flowlog {
    .s-popover-content {
        .edit-wrap {
            .s-input {
                display: block !important;
            }
            .s-button {
                float: right;
            }
            .s-textarea-limit {
                background-color: #fff;
            }
        }
    }
}
.flow-desc {
    .s-tooltip-content {
        width: 180px;
        word-break: break-all;
    }
}
