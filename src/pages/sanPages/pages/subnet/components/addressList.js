import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import u from 'lodash';
import {OutlinedRefresh, OutlinedSetting, OutlinedPlus} from '@baidu/sui-icon';
import {Notification} from '@baidu/sui';

import CreateAddress from './createAddress';
import '../style/tab.less';

const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, asComponent, invokeComp} = decorators;

const tpl = html`
    <div>
        <s-app-list-page class="{{klass}}">
            <div slot="bulk">
                <s-button skin="primary" disabled="{{addressCreateDisable}}" on-click="onCreate">
                    <outlined-plus />
                    {{'添加地址'}}
                </s-button>
                <s-button class="left_class" on-click="delete" disabled="{{disabledDelete}}">删除</s-button>
            </div>
            <div slot="filter">
                <s-button class="s-icon-button" on-click="refresh" track-name="刷新"
                    ><outlined-refresh class="icon-class"
                /></s-button>
                <s-table-column-toggle
                    class="custom-column-qos s-button icon-column left_class"
                    datasource="{{customColumn.datasource}}"
                    value="{=customColumn.value=}"
                    on-change="onCustomColumns"
                ></s-table-column-toggle>
            </div>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                class="subnet-address-list-table"
                datasource="{{table.datasource}}"
                on-selected-change="tableSelected($event)"
                on-filter="onFilter"
                on-sort="onSort"
                selection="{=table.selection=}"
                track-id="ti_vpc_instance_table"
                track-name="列表操作"
            >
                <div slot="empty">
                    <s-empty on-click="onCreate"> </s-empty>
                </div>
            </s-table>
        </s-app-list-page>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class VpnConnList extends Component {
    static components = {
        'outlined-refresh': OutlinedRefresh,
        'outlined-plus': OutlinedPlus,
        'outlined-setting': OutlinedSetting
    };
    initData() {
        const allColumns = [
            {name: 'slaveIpAddress', label: 'IP地址'},
            {name: 'slaveMacAddress', label: 'MAC地址'},
            {name: 'instanceId', label: '关联BBC'}
        ];
        const customColumnDb = allColumns.map(item => ({text: item.label, value: item.name}));
        return {
            klass: 'subnet-address-wrap',
            editTag: {},
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                allColumns,
                datasource: [],
                selectedItems: []
            },
            customColumn: {
                value: ['slaveIpAddress', 'slaveMacAddress', 'instanceId'],
                datasource: customColumnDb
            },
            addressCreateDisable: false,
            disabledDelete: true,
            bindBBCList: [],
            disableBindBBC: [] //当前地址池不可绑定的bbc列表
        };
    }

    inited() {
        this.setTableColumns();
    }

    attached() {
        this.loadPage();
    }

    async loadPage() {
        await this.getBBCBindingInfos();
        this.resetTable();
        this.data.set('table.loading', true);
        let instance = this.data.get('instance');
        let payload = {
            pageNo: 1,
            pageSize: 1000
        };
        this.$http
            .getSubnetReserveportpoolBindList(instance.subnetUuid, instance.reservePortPoolId, payload)
            .then(res => {
                this.data.set('table.datasource', res.result);
                this.data.set('table.loading', false);
            });
        this.bbcFilter();
    }

    resetTable() {
        this.data.set('table.selection', {
            mode: 'multi',
            selectedIndex: []
        });
        this.data.set('table.selectedItems', []);
    }

    refresh() {
        this.loadPage();
    }

    // 设置表格列
    setTableColumns(customColumnNames) {
        customColumnNames = customColumnNames ? customColumnNames : this.data.get('customColumn.value');
        const allColumns = this.data.get('table.allColumns');
        let columns = [];
        allColumns.forEach(item => customColumnNames.indexOf(item.name) > -1 && columns.push(item));
        this.data.set('table.columns', columns);
    }

    // 自定义表格列
    onCustomColumns({value}) {
        this.data.set('customColumn.value', value);
        this.setTableColumns(value);
    }

    tableSelected(e) {
        this.data.set('disabledDelete', e.value.selectedItems.length <= 0);
        this.data.set('table.selectedItems', e.value.selectedItems);
    }

    //绑定bbc
    onCreate() {
        const dialog = new CreateAddress({
            data: {
                subnetId: this.data.get('instance').subnetUuid,
                id: this.data.get('instance').reservePortPoolId,
                vpcId: this.data.get('instance').vpcUuid,
                disableBindBBC: this.data.get('disableBindBBC')
            }
        });
        dialog.on('create', () => {
            this.refresh();
            this.fire('updateList', true);
        });
        dialog.attach(document.body);
    }

    //解绑bbc
    delete() {
        let selectedItems = this.data.get('table.selectedItems');
        let subnetId = this.data.get('instance').subnetUuid;
        let id = this.data.get('instance').reservePortPoolId;
        let slaveInfos = [];
        u.each(selectedItems, item => {
            slaveInfos.push({
                slaveIpAddress: item.slaveIpAddress,
                slaveMacAddress: item.slaveMacAddress,
                deviceId: item.instanceUuid
            });
        });
        let payload = {slaveInfos};
        this.$http
            .subnetReserveportpoolBindBBC(subnetId, id, 'unbind', payload)
            .then(res => {
                this.refresh();
                this.fire('updateList', true);
                Notification.success('删除成功');
            })
            .catch(err => {
                this.refresh();
                Notification.error('删除失败');
            });
    }

    // 过滤bbc,地址池与bbc是一对多，bbc对地址池为一对一
    bbcFilter() {
        // 所有已经被绑定的bbc
        let bindBBCList = this.data.get('bindBBCList');
        // 当前实例已绑定的bbc
        let instanceBindBBCInfos = [];
        if (this.data.get('instance')?.bindingInfos) {
            instanceBindBBCInfos = this.data.get('instance').bindingInfos.map(item => {
                return item.instanceUuid;
            });
        }
        let disableBindBBC = u.difference(bindBBCList, instanceBindBBCInfos);
        this.data.set('disableBindBBC', disableBindBBC);
    }

    // 每次绑定成功后，更新bindBBCList
    getBBCBindingInfos() {
        let payload = {
            keywordType: 'name',
            keyword: '',
            pageNo: 1,
            pageSize: 10000,
            subnetId: this.data.get('instance').subnetUuid
        };
        return this.$http.getSubnetReserveportpools(payload).then(async data => {
            let bindBBCList = [];
            data.result.forEach(item => {
                item?.bindingInfos && bindBBCList.push(...item.bindingInfos);
            });
            // 子网下地址池已经绑定的所有bbc
            this.data.set(
                'bindBBCList',
                bindBBCList.map(item => {
                    return item.instanceUuid;
                })
            );
        });
    }
}
export default Processor.autowireUnCheckCmpt(VpnConnList);
