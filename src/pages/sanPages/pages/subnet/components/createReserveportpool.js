import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';

import {kXhrOptions} from '@/pages/sanPages/utils/helper';
import '../style/tab.less';

const {asComponent, invokeSUI, invokeSUIBIZ, template} = decorators;

const formValidator = self => ({
    name: [
        {required: true, message: '请填写名称'},
        {pattern: /^[a-zA-Z\u4e00-\u9fa5][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/, message: '格式不符合要求'}
    ],
    description: [{maxLength: 200}]
});
const tpl = html`
    <div>
        <s-dialog class="subnet-reserveportpools-create" open="{{true}}" title="{{title}}" width="{{600}}">
            <s-form s-ref="form" data="{=formData=}" class="reserveportpools-item" rules="{{rules}}">
                <s-form-item label="网段类型：">
                    <s-radio-radio-group
                        enhanced
                        disabled="{{isEdit}}"
                        radioType="button"
                        class="reserveportpools-radio"
                        datasource="{{datasource}}"
                        value="{=formData.type=}"
                    >
                    </s-radio-radio-group>
                </s-form-item>
                <s-form-item
                    label="名称："
                    prop="name"
                    help="大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65"
                >
                    <s-input
                        class="text-item"
                        value="{=formData.name=}"
                        width="{{300}}"
                        placeholder="请输入地址池名称"
                    />
                </s-form-item>
                <s-form-item label="自建DHCP：">
                    <s-select
                        datasource="{{bccList}}"
                        value="{{formData.dhcpServer}}"
                        width="{{300}}"
                        disabled="{{BCCloading}}"
                        on-change="bccChange($event)"
                        placeholder="请选择BCC实例"
                    />
                </s-form-item>
                <s-form-item label="描述：" prop="description">
                    <s-input-text-area
                        class="text-item"
                        value="{=formData.desc=}"
                        maxLength="{{200}}"
                        height="{{100}}"
                        width="{{300}}"
                        placeholder="{{'最多200个字符'}}"
                    />
                </s-form-item>
            </s-form>
            <div slot="footer">
                <s-button on-click="onClose">取消</s-button>
                <s-button skin="primary" disabled="{{disableSub}}" on-click="doSubmit">确定</s-button>
            </div>
        </s-dialog>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
class Create extends Component {
    initData() {
        return {
            rules: formValidator(this),
            formData: {
                type: 'ipv4',
                name: '',
                dhcpServer: '',
                desc: ''
            },
            datasource: [
                {
                    value: 'ipv4',
                    text: 'IPv4'
                }
            ],
            nameErr: '',
            bccList: [],
            title: '新建地址池',
            isEdit: false,
            BCCloading: false
        };
    }

    inited() {
        this.editReserveportpool();
    }

    async attached() {
        if (!this.data.get('isEdit')) {
            let subnetId = this.data.get('subnetId');
            let {vpcId, subnetUuid} = await this.$http.getSubnetResourceDetail(subnetId, kXhrOptions.customSilent);
            this.data.set('vpcId', vpcId);
            this.data.set('subnetUuid', subnetUuid);
        }
        this.getBCCList();
    }

    editReserveportpool() {
        if (!this.data.get('instance')) {
            return;
        } else {
            this.data.set('title', '修改地址池');
            this.data.set('isEdit', true);
            let instance = this.data.get('instance');
            this.data.set('formData.name', instance.name);
            this.data.set('formData.desc', instance.description);
        }
    }

    bccChange({value}) {
        this.data.set('formData.dhcpServer', value);
    }

    getPayload() {
        let formData = this.data.get('formData');
        let isEdit = this.data.get('isEdit');
        let payload = {
            name: formData.name,
            description: formData.desc
        };
        if (!isEdit && formData.dhcpServer) {
            payload.dhcpServers = [formData.dhcpServer];
        } else if (isEdit) {
            payload.dhcpServers = formData.dhcpServer ? [formData.dhcpServer] : [];
        }
        return payload;
    }

    getBCCList() {
        this.data.set('BCCloading', true);
        this.data.set('disableSub', true);
        let payload = {
            pageNo: 1,
            pageSize: 1000
        };
        let region = window.$context.getCurrentRegionId();
        let isEdit = this.data.get('isEdit');
        let vpcId = isEdit ? this.data.get('instance').vpcUuid : this.data.get('vpcId');
        let bindBBCList = [];
        u.each(this.data.get('reserveportpools'), data => {
            bindBBCList.push(data.dhcpServers[0]?.instanceUuid);
        });
        this.$http.getBccList(payload, {headers: {region}}).then(res => {
            let datasource = [];
            u.each(res.result, item => {
                if (!isEdit && item.vpcId === vpcId) {
                    datasource.push({
                        value: item.id,
                        text: `${item.name} / ${item.instanceId}`
                    });
                }
                if (
                    isEdit &&
                    item.vpcId === vpcId &&
                    item.id !== this.data.get('instance').dhcpServers[0]?.instanceUuid
                ) {
                    datasource.push({
                        value: item.id,
                        text: `${item.name} / ${item.instanceId}`
                    });
                }
            });
            this.data.set('bccList', datasource);
            this.data.set('BCCloading', false);
            this.data.set('disableSub', false);
        });
    }

    doSubmit() {
        const form = this.ref('form');
        return form.validateFields().then(() => {
            this.data.set('disableSub', true);
            let payload = this.getPayload();
            let isEdit = this.data.get('isEdit');
            if (isEdit) {
                let subnetId = this.data.get('subnetId');
                let id = this.data.get('instance').reservePortPoolId;
                return this.$http
                    .updateSubnetReserveportpool(subnetId, id, payload)
                    .then(() => {
                        this.fire('create');
                        this.dispose();
                        this.data.set('disableSub', false);
                    })
                    .catch(() => this.data.set('disableSub', false));
            } else {
                payload.vpcUuid = this.data.get('vpcId');
                payload.subnetUuid = this.data.get('subnetUuid');
                return this.$http
                    .createSubnetReserveportpool(payload)
                    .then(() => {
                        this.fire('create');
                        this.dispose();
                        this.data.set('disableSub', false);
                    })
                    .catch(() => this.data.set('disableSub', false));
            }
        });
    }

    onClose() {
        this.dispose();
    }
}
export default Processor.autowireUnCheckCmpt(Create);
