import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Button, Form, Select, Tooltip} from '@baidu/sui';
import zone from '@/pages/sanPages/utils/zone';
import '../style/tab.less';

/* eslint-disable */
const tpl = html`
    <template>
        <s-dialog class="change-az-dialog" closeAfterMaskClick="{{false}}" open="{=open=}" title="{{title}}">
            <div>
                <s-form s-ref="form" data="{=formData=}" rules="{{rules}}">
                    <s-form-item label="{{'当前可用区：'}}" class="item-context">
                        <span>{{currentZoneAz | az}}</span>
                    </s-form-item>
                    <s-form-item label="{{'变更为：'}}" prop="targetZoneAz">
                        <s-select
                            width="120"
                            datasource="{{azList}}"
                            value="{=formData.targetZoneAz=}"
                            class="az-select"
                        >
                        </s-select>
                    </s-form-item>
                </s-form>
            </div>
            <div slot="footer">
                <s-button on-click="close">取消</s-button>
                <s-tooltip content="请选择新的可用区" trigger="{{clickDisable ? 'hover': ''}}">
                    <s-button skin="primary" on-click="dialogConfirm" disabled="{{clickDisable}}">确定</s-button>
                </s-tooltip>
            </div>
        </s-dialog>
    </template>
`;
/* eslint-enable */
export default class ChangeAZDialog extends Component {
    static template = tpl;
    static components = {
        's-dialog': Dialog,
        's-button': Button,
        's-form': Form,
        's-form-item': Form.Item,
        's-select': Select,
        's-tooltip': Tooltip
    };
    initData() {
        return {
            title: '变更可用区',
            open: true,
            formData: {targetZoneAz: ''},
            rules: {targetZoneAz: {required: true, message: '请选择可用区'}},
            targetZoneAz: '',
            azList: []
        };
    }
    static filters = {
        az(currentZoneAz: string) {
            if (!currentZoneAz || currentZoneAz === 'UNKNOWN_AZ') {
                return '-';
            }
            return zone.getLabel(currentZoneAz);
        }
    };
    static computed = {
        clickDisable() {
            let currentZoneAz = this.data.get('currentZoneAz');
            let targetZoneAz = this.data.get('formData.targetZoneAz');
            return currentZoneAz === targetZoneAz;
        }
    };
    inited() {
        let azList = this.data.get('azList');
        this.data.set(
            'azList',
            azList.map((item: string) => {
                return {
                    value: item,
                    text: zone.getLabel(item)
                };
            })
        );
        this.data.set('formData.targetZoneAz', azList[0]);
    }
    async dialogConfirm() {
        let form = this.ref('form');
        await form.validateFields();
        let targetZoneAz = this.data.get('formData.targetZoneAz');
        this.fire('confirm', targetZoneAz);
        this.data.set('open', false);
    }
    close() {
        this.fire('close');
        this.data.set('open', false);
    }
}
