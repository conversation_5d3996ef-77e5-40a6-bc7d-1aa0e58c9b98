import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {Notification} from '@baidu/sui';

import RULE from '@/pages/sanPages/utils/rule';
import {checkIsInSubnet} from '@/pages/sanPages/utils/common';
import '../style/tab.less';

const {asComponent, invokeSUI, invokeSUIBIZ, template} = decorators;

const formValidator = self => ({
    slaveIpAddress: [
        {required: true, message: 'IP地址必填'},
        {pattern: RULE.IP, message: '格式不符合要求'},
        {
            validator: (rule, value, callback) => {
                const formData = self.data.get('formData');
                const subnetId = self.data.get('subnetId');
                const subnetList = self.data.get('subnetList');
                if (!u.trim(value)) {
                    return callback('请填写指定IP地址');
                }
                const subnet = u.find(subnetList, item => item.value === subnetId);
                if (subnet && !checkIsInSubnet(formData.slaveIpAddress + '/32', subnet.cidr)) {
                    return callback('IP地址不在所选子网内');
                }
                callback();
            }
        }
    ],
    slaveMacAddress: [
        {required: true, message: 'MAC地址必填'},
        {pattern: /^[a-f\d]{2}:[a-f\d]{2}:[a-f\d]{2}:[a-f\d]{2}:[a-f\d]{2}:[a-f\d]{2}$/, message: '格式不符合要求'}
    ],
    deviceId: [{required: true, message: 'BBC必选'}]
});
const tpl = html`
    <div>
        <s-dialog class="subnet-reserveportpools-address" open="{{true}}" title="添加地址">
            <s-form s-ref="form" data="{=formData=}" class="reserveportpools-item" rules="{{rules}}">
                <s-form-item label="IP地址：" prop="slaveIpAddress">
                    <s-input width="{{300}}" placeholder="请输入该子网内可用IP" value="{=formData.slaveIpAddress=}" />
                </s-form-item>
                <s-form-item label="MAC地址：" prop="slaveMacAddress" help="MAC地址仅支持小写字母">
                    <s-input
                        class="text-item"
                        value="{=formData.slaveMacAddress=}"
                        width="{{300}}"
                        placeholder="请输入MAC地址"
                    />
                </s-form-item>
                <s-form-item label="关联BBC：" prop="deviceId">
                    <s-select
                        datasource="{{bbcList}}"
                        value="{=formData.deviceId=}"
                        width="{{300}}"
                        disabled="{{BBCloading}}"
                        placeholder="请选择BBC实例"
                        on-change="bbcChange($event)"
                    />
                </s-form-item>
            </s-form>
            <div slot="footer">
                <s-button on-click="onClose">取消</s-button>
                <s-button skin="primary" disabled="{{disableSubmit}}" on-click="doSubmit">确定</s-button>
            </div>
        </s-dialog>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
class CreateAddress extends Component {
    initData() {
        return {
            rules: formValidator(this),
            formData: {
                deviceId: '',
                slaveIpAddress: '',
                slaveMacAddress: ''
            },
            nameErr: '',
            bbcList: [],
            subnetList: [],
            disableSubmit: false
        };
    }

    attached() {
        this.getBBCList();
        this.loadSubnets();
    }

    loadSubnets() {
        this.$http.getSubnetList(this.data.get('vpcId')).then(data => {
            let datasource = [];
            u.each(data, item => {
                let text = '';
                if (item.ipv6Cidr) {
                    text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}(${item.ipv6Cidr})`;
                } else {
                    text = `${item.name} ${item.cidr ? '(' + item.cidr + ')' : ''}`;
                }
                datasource.push({
                    value: item.subnetId,
                    text: text,
                    cidr: item.cidr,
                    ipv6Cidr: item.ipv6Cidr
                });
            });
            this.data.set('subnetList', datasource);
        });
    }

    getBBCList() {
        this.data.set('BBCloading', true);
        this.data.set('disableSubmit', true);
        let payload = {
            pageNo: 1,
            pageSize: 10000
        };
        let vpcId = this.data.get('vpcId');
        let region = window.$context.getCurrentRegionId();
        let disableBindBBC = this.data.get('disableBindBBC');
        this.$http.getBbcList(payload, {headers: {region}}).then(res => {
            let datasource = [];
            u.each(res.result, item => {
                if (item.vpcId === vpcId && disableBindBBC.indexOf(item.id) < 0) {
                    datasource.push({
                        value: item.id,
                        text: `${item.instanceId} / ${item.name}`
                    });
                }
            });
            this.data.set('bbcList', datasource);
            this.data.set('BBCloading', false);
            this.data.set('disableSubmit', false);
        });
    }

    bbcChange({value}) {
        this.data.set('formData.deviceId', value);
    }

    doSubmit() {
        const form = this.ref('form');
        return form.validateFields().then(() => {
            this.data.set('disableSubmit', true);
            let subnetId = this.data.get('subnetId');
            let id = this.data.get('id');
            let formData = this.data.get('formData');
            let payload = {
                slaveInfos: [
                    {
                        slaveIpAddress: formData.slaveIpAddress,
                        slaveMacAddress: formData.slaveMacAddress,
                        deviceId: formData.deviceId
                    }
                ]
            };
            this.$http
                .subnetReserveportpoolBindBBC(subnetId, id, 'bind', payload)
                .then(() => {
                    this.fire('create');
                    this.dispose();
                    this.data.set('disableSubmit', false);
                    Notification.success('添加成功');
                })
                .catch(() => {
                    this.data.set('disableSubmit', false);
                    Notification.error('添加失败');
                });
        });
    }

    onClose() {
        this.dispose();
    }
}

export default Processor.autowireUnCheckCmpt(CreateAddress);
