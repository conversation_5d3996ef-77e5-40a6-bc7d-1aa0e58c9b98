/*
 * @Description:
 * @Author: <EMAIL>
 * @Date: 2022-03-23 21:01:04
 */
/**
 * @file tab.js
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import {OutlinedLeft} from '@baidu/sui-icon';
import '../style/tab.less';

const {asPage, invokeComp, invokeSUI, invokeSUIBIZ, invokeAppComp, template} = decorators;
const tpl = html`
    <div>
        <s-app-detail-page class="{{klass}} siderbar-tab">
            <div class="instance-info" slot="pageTitle">
                <app-link to="/vpc/subnet/list" class="page-title-nav"><icon-left />返回</app-link>
                <h4 class="instance-name">{{instanceName || '-'}}</h4>
            </div>
            <app-tab-page active="{= active =}" position="left" skin="accordion">
                <app-tab-page-panel title="子网详情" url="#/vpc/subnet/detail?subnetId={{urlQuery.subnetId}}">
                    <vpc-subnet-detail
                        on-changeName="onChangeName"
                        route="{{route}}"
                        instance="{{instance}}"
                        subnetId="{{urlQuery.subnetId}}"
                    />
                </app-tab-page-panel>
                <app-tab-page-panel title="IP地址管理" url="#/vpc/subnet/ip?subnetId={{urlQuery.subnetId}}">
                    <vpc-subnet-ip route="{{route}}" subnetId="{{urlQuery.subnetId}}" />
                </app-tab-page-panel>
                <app-tab-page-panel title="预留网段" url="#/vpc/subnet/reserve?subnetId={{urlQuery.subnetId}}">
                    <vpc-subnet-reserve route="{{route}}" subnetId="{{urlQuery.subnetId}}" />
                </app-tab-page-panel>
                <app-tab-page-panel
                    s-if="inReserveportpoolsWhite"
                    title="嵌套地址池"
                    url="#/vpc/subnet/reserveportpools?subnetId={{urlQuery.subnetId}}"
                >
                    <vpc-subnet-reserveportpools route="{{route}}" subnetId="{{urlQuery.subnetId}}" />
                </app-tab-page-panel>
            </app-tab-page>
        </s-app-detail-page>
    </div>
`;

@template(tpl)
@invokeComp('@vpc-subnet-detail', '@vpc-subnet-ip', '@vpc-subnet-reserve', '@vpc-subnet-reserveportpools')
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class Tab extends Component {
    static components = {
        'icon-left': OutlinedLeft
    };
    initData() {
        return {
            klass: 'subnet-detail-wrap',
            urlQuery: getQueryParams()
        };
    }

    inited() {
        if (_.includes(window.location.href, 'vpc/subnet/reserveportpools')) {
            this.data.set('inReserveportpoolsWhite', true);
        }
        const whiteList = window.$storage.get('commonWhite');
        this.data.set('inReserveportpoolsWhite', whiteList?.ReservePortPoolWhiteList);
        const subnetId = this.data.get('urlQuery.subnetId');
        this.$http
            .getSubnetResourceDetail(subnetId, {'x-silent-codes': ['Subnet.SubnetNotFoundException']})
            .then(data => {
                this.data.set('instance', data);
                this.data.set('instanceName', data.name);
            });
    }

    onChangeName(value) {
        this.data.set('instanceName', value);
    }

    onRegionChange() {
        location.hash = '#/vpc/subnet/list';
    }

    onBack() {
        location.hash = '#/vpc/subnet/list';
    }
}
export default Processor.autowireUnCheckCmpt(Tab);
