/**
 * @file detail.js
 * <AUTHOR>
 */

import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import Rule from '@/pages/sanPages/utils/rule';
import zone from '@/pages/sanPages/utils/zone';
import {utcToTime, $flag as FLAG} from '@/pages/sanPages/utils/helper';
import {Notification, Input} from '@baidu/sui';
import {OutlinedEditingSquare} from '@baidu/sui-icon';
import ChangeAZDialog from '../components/changeAZ';
import '../style/tab.less';

const {asComponent, invokeSUI, invokeComp, invokeSUIBIZ, invokeAppComp, template} = decorators;
const tpl = html`
    <template>
        <div class="{{kclass}}">
            <div class="{{kclass}}-item">
                <h4>基本信息</h4>
                <ul>
                    <li class="content-item relative_class">
                        <label class="cell-title">名称：</label>
                        <span class="cell-content"> {{instance.name}} </span>
                        <edit-popover
                            class="edit_popover_class"
                            value="{=instance.name=}"
                            rule="{{NameRule}}"
                            tip="大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65"
                            on-edit="updateName"
                        >
                            <outlined-editing-square color="#2468f2" />
                        </edit-popover>
                    </li>
                    <li class="content-item relative_class">
                        <label class="cell-title">ID：</label>
                        <span class="cell-content">{{instance.shortId}}</span>
                        <s-clip-board
                            class="name-icon copy_icon"
                            text="{{instance.shortId}}"
                            successMessage="已复制到剪贴板"
                        />
                    </li>
                    <li class="content-item">
                        <label class="cell-title">可用区：</label>
                        <span class="cell-content">{{instance | az}}</span>
                        <s-button s-if="{{showChangeAZ}}" class="change-btn" skin="stringfy" on-click="changeAZ"
                            >变更</s-button
                        >
                    </li>
                    <li class="content-item">
                        <label class="cell-title">创建时间：</label>
                        <span class="cell-content">{{instance.createdTime | getTime}}</span>
                    </li>
                    <li class="content-item">
                        <label class="cell-title">IPv4网段：</label>
                        <span class="cell-content">{{instance.cidr | defaultSet}}</span>
                    </li>
                    <li class="content-item">
                        <label class="cell-title">IPv6网段：</label>
                        <span class="cell-content">{{instance.ipv6Cidr | defaultSet}}</span>
                    </li>
                    <li class="content-item" s-if="{{allowBroadcast && instance.broadcast !== -1}}">
                        <label class="cell-title">子网广播：</label>
                        <s-switch
                            checked="{{instance.broadcast}}"
                            on-change="broadcastChange($event)"
                            disabled="{{broadcastDisabled}}"
                        />
                    </li>
                    <li class="content-item">
                        <label class="cell-title">描述：</label>
                        <span class="cell-content">{{instance.description}}</span>
                        <edit-popover
                            value="{=instance.description=}"
                            rule="{{Rule.DESC}}"
                            on-edit="updateDesc"
                            tip="描述不能超过200个字符"
                            compType="textarea"
                        >
                            <outlined-editing-square color="#2468f2" />
                        </edit-popover>
                    </li>
                    <li class="content-item">
                        <label class="cell-title">可用IP数：</label>
                        <span class="cell-content">{{instance.availableIp}}</span>
                    </li>
                </ul>
            </div>
            <div class="{{kclass}}-item">
                <h4>包含资源</h4>
                <div class="main">
                    <h5>基础云资源</h5>
                    <div class="list-wrap">
                        <a href="{{listDetail[item].link}}" s-for="item in baseList">
                            <span>{{listDetail[item].text}}</span>
                            <span>{{numList[item]}}</span>
                        </a>
                    </div>
                </div>
                <div class="main">
                    <h5>网络资源</h5>
                    <div class="list-wrap">
                        <a
                            class="{{listDetail[item].link | linkClass}}"
                            href="{{listDetail[item].link | linkHref}}"
                            s-for="item in netList"
                        >
                            <span>{{listDetail[item].text}}</span>
                            <span>{{numList[item]}}</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </template>
`;

@template(tpl)
@asComponent('@vpc-subnet-detail')
@invokeComp('@edit-popover')
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class Detail extends Component {
    static components = {
        'outlined-editing-square': OutlinedEditingSquare,
        's-textarea': Input.TextArea
    };
    initData() {
        return {
            kclass: 'subnet-detail',
            Rule: Rule.DETAIL_EDIT,
            NameRule: Rule.NAME_SUPPORT_CHINESE,
            instance: {},
            baseList: ['bcc'],
            netList: ['enic', 'snic', 'blb'],
            listDetail: {
                bcc: {
                    text: 'BCC',
                    link: '/bcc/#/bcc/instance/list'
                },
                dcc: {
                    text: 'DCC',
                    link: '/bcc/#/dcc/instance/list'
                },
                bbc: {
                    text: 'BBC',
                    link: '/bbc/#/bbc/instance/list'
                },
                bci: {
                    text: 'BCI',
                    link: '/bci/#/bci/pod/list'
                },
                enic: {
                    text: '弹性网卡',
                    link: '/network/#/vpc/eni/list?subnetId='
                },
                snic: {
                    text: '服务网卡',
                    link: '/network/#/vpc/endpoint/list?subnetId='
                },
                blb: {
                    text: '负载均衡',
                    link: '/blb/#/blb/list?subnetId='
                }
            },
            numList: {},
            allowBroadcast: false,
            broadcastDisabled: false,
            showChangeAZ: false,
            isInitInfo: false
        };
    }

    static filters = {
        az(item) {
            if (!item.az || item.az === 'UNKNOWN_AZ') {
                return '-';
            }
            return zone.getLabel(item.az);
        },
        getTime(value) {
            return value ? utcToTime(value) : '-';
        },
        defaultSet(value) {
            return value || '-';
        },
        linkClass(value) {
            return value ? '' : 'forbid-redirect';
        },
        linkHref(value) {
            return value ? value + this.data.get('context').subnetId : 'javascript:void(0)';
        }
    };

    static computed = {
        showChangeAZ() {
            let instance = this.data.get('instance');
            return instance?.needTransferAz;
        }
    };

    inited() {
        this.getBroadcastWhitelist();
        this.watch('instance', value => {
            if (!!Object.keys(value).length) {
                if (!this.data.get('isInitInfo')) {
                    this.initDetailInfo();
                }
            }
        });
        this.data.set('instance', this.data.get('context').instance);
    }

    async postInstanceName(type) {
        const editText = type === 'name' ? '名称' : '描述';
        const res = await this.data.get('context')?.updateName();
        if (res) {
            Notification.success(`${editText}编辑成功`);
        }
    }

    initDetailInfo() {
        let services = window.$context.getAvailableService();
        let availableServiceArray = services && services.split(',');
        const {bccNum, dccNum, bbcNum, enicNum, snicNum, blbNum, bciNum, enicSecondaryIpNum} =
            this.data.get('instance');
        this.data.set('numList', {
            bcc: bccNum,
            dcc: dccNum,
            bbc: bbcNum,
            enic: enicNum,
            snic: snicNum,
            blb: blbNum,
            bci: bciNum
        });
        let bciRegionList = Object.keys(window.$context.SERVICE_TYPE['BCI'].region);
        let regionEnable = bciRegionList.indexOf(window.$context.getCurrentRegionId()) > -1;
        const enableBBC = availableServiceArray.indexOf('BBC') > -1 && !FLAG.NetworkSupportXS;
        const enableDCC = availableServiceArray.indexOf('DCC') > -1;
        const enableBCI = availableServiceArray.indexOf('BCI') > -1 && !FLAG.NetworkSupportXS;
        if (enableDCC) {
            this.data.push('baseList', 'dcc');
        }
        if (enableBBC) {
            this.data.push('baseList', 'bbc');
        }
        if (enableBCI && regionEnable) {
            this.data.push('baseList', 'bci');
        }
        this.data.set('isInitInfo', true);
    }

    updateName(value) {
        this.$http
            .subnetUpdate({
                id: this.data.get('context').subnetId,
                name: value
            })
            .then(() => {
                this.postInstanceName('name');
            });
    }

    updateDesc(value) {
        this.$http
            .subnetUpdate({
                id: this.data.get('context').subnetId,
                description: value
            })
            .then(() => {
                this.postInstanceName('desc');
            });
    }
    broadcastChange(e) {
        this.data.set('broadcastDisabled', true);
        this.$http
            .subnetBroadcastUpdate({
                id: this.data.get('context').subnetId,
                name: this.data.get('instance.name'),
                description: this.data.get('instance.description'),
                broadcast: e.value ? 1 : 0
            })
            .then(() => {
                this.data.set('broadcastDisabled', false);
                this.data.set('instance.broadcast', e.value);
                Notification.success('修改成功。');
            })
            .catch(() => {
                Notification.error('修改失败。');
                this.data.set('instance.broadcast', !e.value);
            });
    }
    async getBroadcastWhitelist() {
        const region = this.$context.getCurrentRegion();
        let res = await this.$http.broadcastWhitelist();
        const allowBroadcast = res.regions.indexOf(region.id) > -1;
        this.data.set('allowBroadcast', allowBroadcast);
    }
    // 变更az
    changeAZ() {
        let instance = this.data.get('instance');
        let confirm = new ChangeAZDialog({
            data: {
                open: true,
                currentZoneAz: instance.az,
                azList: instance.canTransferAz || []
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', targetZoneAz => {
            this.$http
                .changeAz({
                    subnetUuid: this.data.get('context').subnetId,
                    targetZoneAz
                })
                .then(async () => {
                    const res = await this.data.get('context')?.updateName();
                    if (res) {
                        Notification.success('可用区变更成功');
                    }
                });
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(Detail));
