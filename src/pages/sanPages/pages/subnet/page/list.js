/*
 * @description: VPC实例列表页
 * @file: network/subnet/pages/List.js
 * @author: p<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams, request} from '@baidu/bce-react-toolkit';
import {Notification, Input} from '@baidu/sui';
import {OutlinedRefresh, OutlinedPlus, OutlinedEditingSquare, OutlinedDownload} from '@baidu/sui-icon';
import {checker} from '@baiducloud/bce-opt-checker';
import {ResourceGroupDialog} from '@baidu/bce-resourcegroup-sdk-san';
import {AnalysisSDKProcessor} from '@baidu/bce-vpc-sdk-react';
import {ResourceGroupSDK} from '@baidu/bce-resourcegroup-sdk';

import {ServiceType, DocService} from '@/pages/sanPages/common';
import rules from '../rule';
import zone from '@/pages/sanPages/utils/zone';
import DeleteCheckConfirm from '@/pages/sanPages/components/deleteCheck';
import Confirm from '@/pages/sanPages/components/confirm';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import {resetNewRegion} from '@/utils/helper';
import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import IntroducePanel from '@/pages/sanPages/components/introducePanel/introducePanel';
import '../style/list.less';
import '@baidu/bce-vpc-sdk-react/lib/style.css';

const processorPath = new AnalysisSDKProcessor();
const {invokeComp, invokeSUI, invokeSUIBIZ, invokeAppComp, template, service} = decorators;
const tpl = html`
<template>
<s-biz-page class="{{klass}}">
    <div class="vpc-subnet-header" slot="header">
        <div class="subnet-header-widget">
            <div class="widget-left">
                <span class="title">{{title}}</span>
                <vpc-select
                    class="vpc-select"
                    on-int="vpcInt"
                    on-change="vpcChange" />
                <div class="tip-header-wrap" s-if="showTip">
                    <s-icon name="huodongicon"/>
                    <span class="text-hidden tip-text" >通用型子网已支持使用NAT网关，不再支持新增NAT专属子网</span>
                    <s-icon name="close" on-click="closeTip"/>
                </div>
            </div>
            <div class="widget-right">
                <a
                    s-ref="introduce"
                    href="javascript:void(0)"
                    class="help-file function-introduce"
                    on-click="handleShowCard"
                    on-mouseenter="handleMouseEnter('introduce')"
                    on-mouseleave="handleMouseLeave('introduce')"
                    s-if="{{!FLAG.NetworkSupportXS}}"
                >
                    <img class="s-icon" src="{{introduceIcon}}" />功能简介
                </a>
                <a href="{{DocService.subnet_helpFile}}"
                target="_blank"
                class="help-file"
                s-if="{{!FLAG.NetworkSupportXS}}">
                <s-icon name="warning-new"/>帮助文档
                </a>
            </div>
        </div>
        <introduce-panel
            isShow="{{show}}"
            klass="{{'endpoint-peerconn-wrapper'}}"
            title="{{introduceTitle}}"
            description="{{description}}"
            introduceOptions="{{introduceOptions}}"
            on-toggle="handleToggle($event)"
        ></introduce-panel>
    </div>
    <div class="list-page-tb-left-toolbar" slot="tb-left">
        <s-tooltip trigger="{{createSubnet.disable || subnetSinDisable.disable || iamPass.disable ? 'hover' : ''}}" placement="top">
            <!--bca-disable-next-line-->
            <div slot="content">{{subnetSinDisable.message || iamPass.message || createSubnet.message | raw}}</div>
            <s-button disabled="{{subnetSinDisable.disable || iamPass.disable || createSubnet.disable}}"
                skin="primary"
                on-click="onCreate"
                track-id="ti_vpc_instance_create"
                track-name="创建VPC"><outlined-plus/>{{'创建子网'}}
            </s-button>
        </s-tooltip>
        <s-tooltip
            trigger="{{deleteSubnet.disable ? 'hover' : ''}}"
            placement="top"
            class="left_class"
        >
            <!--bca-disable-next-line-->
            <div slot="content">{{deleteSubnet.message | raw}}</div>
            <s-button on-click="onDelete" disabled="{{deleteSubnet.disable || checkSourceLoading}}"
                track-id="ti_vpc_instance_delete" track-name="删除">{{'删除'}}</s-button>
        </s-tooltip>
        <edit-tag
            class="left_class"
            selectedItems="{{selectedItems}}"
            on-success="refresh"
            type="SUBNET"
        ></edit-tag>
    </div>
    <div slot="tb-right" class="toolbar_right">
        <search-tag
            s-ref="search"
            serviceType="SUBNET"
            searchbox="{=searchbox=}"
            on-search="onSearch"
        ></search-tag>
        <s-button on-click="refresh" class="s-icon-button left_class" track-id="ti_vpc_instance_refresh" track-name="刷新"><outlined-refresh class="icon-class"/></s-button>
        <s-button on-click="onDownload" class="s-icon-button" track-id="ti_vpc_subnet_download" track-name="下载"><outlined-download class="icon-class"/></s-button>
        <custom-column
            class="left_class"
            columnList="{{customColumn.datasource}}"
            initValue="{{customColumn.value}}"
            type="subnet"
            on-init="initColumns"
            on-change="onCustomColumns">
        </custom-column>
    </div>
    <s-table
        columns="{{table.columns}}"
        loading="{{table.loading}}"
        error="{{table.error}}"
        datasource="{{table.datasource}}"
        on-selected-change="tableSelected($event)"
        on-command="onTableCommand($event)"
        on-filter="onFilter"
        on-sort="onSort"
        selection="{=table.selection=}"
        track-id="ti_vpc_instance_table"
        track-name="列表操作">
        <div slot="empty">
            <s-empty on-click="onCreate" class="{{iamPass.disable ? 'create-disable' : ''}}">
            </s-empty>
        </div>
        <div slot="error">
            {{table.error ? table.error : '啊呀，出错了？'}}
            <a href="javascript:;" on-click="refresh">{{'重新加载'}}</a>
        </div>
        <div slot="c-id" class="subnet-id-widget">
            <a class="truncated" title="{{row.name | getVpcName}}"
                href="#/vpc/subnet/detail?subnetId={{row.subnetId}}"
                track-id="ti_subnet_list" track-name="子网列表">
                {{row.name | getSubnetName}}
            </a>
            <s-popover s-ref="popover-name-{{rowIndex}}" placement="top" trigger="click" class="edit-popover-class">
                <div class="edit-wrap" slot="content">
                    <s-input
                        value="{=edit.name.value=}"
                        width="320"
                        placeholder="{{'请输入名称'}}"
                        on-input="onEditInput($event, rowIndex, 'name')"/>
                    <div class="edit-tip">{{'大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65'}}</div>
                    <s-button skin="primary" s-ref="editBtn-name-{{rowIndex}}" disabled="{{true}}"
                        on-click="editConfirm(row, rowIndex, 'name')">{{'确定'}}</s-button>
                    <s-button on-click="editCancel(rowIndex, 'name')">{{'取消'}}</s-button>
                </div>
                <outlined-editing-square class="name-icon" on-click="onInstantEdit(row, rowIndex, 'name')"/>
            </s-popover>
            <br>
            <span class="truncated" title="{{row.shortId}}">{{row.shortId}}</span>
            <s-clip-board class="name-icon" text="{{row.shortId}}"/>
        </div>
        <div slot="c-resourceGroups">{{row | resourceText | raw}}</div>
        <div slot="c-sourceVpc">
            <div class="text-hidden"><a href="#/vpc/instance/detail?vpcId={{row.vpcId}}"
                class="list-link">{{row.vpcName || '-'}}</a>
            </div>
            <br>
            <span class="text-hidden">{{row.vpcShortId || '-'}}</span>
        </div>
        <div slot="c-cidr">{{row | getCidr | raw}}</div>
        <div slot="c-showUseIp">{{row.totalIps - row.usedIps}}</div>
        <div slot="c-ipv6Cidr">{{row.ipv6Cidr || '-'}}</div>
        <div slot="c-bccNum">
            <s-tooltip trigger="{{row.bccNum>0||row.dccNum||row.bbcNum > 0?'hover':''}}" placement="right">
                <div slot="content">
                    <span s-if="{{row.bccNum > 0}}">
                        云服务器BCC：<a class="pointer-link" href="javascript:void(0)"
                            on-click="redirectToBcc(row, 'bcc')">{{row.bccNum}}<a/>
                    </span>
                    <span s-if="{{row.dccNum > 0}}">
                        <br s-if="row.bccNum > 0" />
                        专属服务器DCC：<a class="pointer-link" href="javascript:void(0)"
                            on-click="redirectToBcc(row, 'dcc')">{{row.dccNum}}</a>
                    </span>
                    <span s-if="{{row.bbcNum > 0}}">
                        <br s-if="row.bccNum > 0 || row.dccNum > 0"/>
                        弹性裸金属服务器BBC：<a class="pointer-link" href="javascript:void(0)"
                            on-click="redirectToBcc(row, 'bbc')">{{row.bbcNum}}</a>
                    </span>
                </div>
                <a href="javascript:void(0)" s-if="row.bccNum + row.dccNum + row.bbcNum  === 0"
                    on-click="redirectToBcc(row)">{{row.bccNum + row.dccNum + row.bbcNum}}</a>
                <a s-else href="javascript:void(0)">{{row.bccNum + row.dccNum + row.bbcNum}}</a>
            </s-tooltip>
        </div>
        <div slot="c-az">{{row | az | raw}}</div>
        <div slot="c-resourceGroups">
            <div s-if="row.resourceGroups && row.resourceGroups.length">
                <p s-for="item in row.resourceGroups">
                    {{item.name}}
                </p>
            </div>
            <span s-else>-</span>
        </div>
        <div slot="c-resource">
            <div s-if="row.resourceGroups && row.resourceGroups.length">
                <p s-for="item in row.resourceGroups">
                    {{item.name}}
                </p>
            </div>
            <span s-else>-</span>
        </div>
        <div slot="c-tag">
            <span s-if="!row.tags || row.tags.length < 1">
                -
            </span>
            <div s-else s-for="item,index in row.tags">
                <span s-if="index <= 1" class="truncated">
                    {{item.tagKey + ':' + item.tagValue}}
                </span>
                <div s-if="row.tags.length > 2 && index === 1">...</div>
            </div>
        </div>
        <div slot="c-subnetType">{{row | subnetType}}</div>
        <div slot="c-description">
            <span class="truncated" title="{{row.description}}">{{row.description}}</span>
            <s-popover s-ref="popover-description-{{rowIndex}}" placement="top" trigger="click" class="edit-popover-class">
                <div class="edit-wrap" slot="content">
                    <s-textarea
                        value="{=edit.description.value=}"
                        width="200"
                        height="48"
                        placeholder="{{'请输入'}}"
                        on-input="onEditInput($event, rowIndex, 'description')"/>
                    <div class="edit-tip">{{'描述不能超过200个字符'}}</div>
                    <s-button skin="primary" s-ref="editBtn-description-{{rowIndex}}" disabled="{{true}}"
                        on-click="editConfirm(row, rowIndex, 'description')">{{'确定'}}</s-button>
                    <s-button on-click="editCancel(rowIndex, 'description')">{{'取消'}}</s-button>
                </div>
                <outlined-editing-square class="name-icon" on-click="onInstantEdit(row, rowIndex, 'description')"/>
            </s-popover>
        </div>
        <div slot="h-diagnose">
            <span
                >路径分析
                <div class="new-tag new-instance-tag">new</div></span
            >
        </div>
        <div slot="c-diagnose">
            <s-button skin="stringfy" on-click="pathAnalysis(row)">路径分析</s-button>
        </div>
        <div slot="c-opt" class="operation">
            <s-button skin="stringfy" on-click="changeResourceGroup(row)">编辑资源分组</s-button>
            <div>
                <s-button s-if="row.ipv6Cidr" skin="stringfy" on-click="onManageIPv6(row)"
                    track-id="ti_vpc_instance_ipv6" track-name="删除IPv6网段">{{'删除IPv6网段'}}</s-button>
                <s-popover s-else trigger="{{!row.vpcIpv6Cidr ? 'hover' : ''}}">
                    <div slot="content">
                        {{'所属vpc实例未分配ipv6网段'}}
                    </div>
                    <s-button on-click="onManageIPv6(row)" skin="stringfy"
                        disabled="{{!row.vpcIpv6Cidr}}"
                        track-id="ti_vpc_instance_ipv6" track-name="分配IPv6网段">{{'分配IPv6网段'}}</s-button>
                </s-popover>
            </div>
            <s-tooltip
                s-else
                content="{{'该子网所在VPC未分配IPv6网段，请先给该VPC分配IPv6网段。'}}"
                trigger="hover"
                placement="right"
            >
                <a s-else href="javascript:void(0)" style="color: #999"
                    track-id="ti_vpc_instance_ipv6" track-name="分配IPv6网段">{{'分配IPv6网段'}}</a>
            </s-tooltip>
        </div>
    </s-table>
    <s-pagination
        s-if="{{FLAG.NetworkSubnetSupportPage && pager.total}}"
        slot="footer"
        layout="{{'total, pageSize, pager, go'}}"
        pageSize="{{pager.pageSize}}"
        total="{{pager.total}}"
        page="{{pager.page}}"
        on-pagerChange="onPagerChange"
        on-pagerSizeChange="onPagerSizeChange" />
    <resource-group-dialog
        s-if="{{showResource}}"
        sdk="{{resourceSDK}}"
        resource="{{resource}}"
        on-success="oncommit"
        on-cancel="onCancel"/>
</s-biz-page>
<div id="satisfactionNew" s-if="drawerVisible"></div>
</template>`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@invokeComp('@search-tag', '@edit-tag', '@vpc-select', '@custom-column')
class SubnetList extends Component {
    static components = {
        'resource-group-dialog': ResourceGroupDialog,
        'outlined-refresh': OutlinedRefresh,
        'outlined-plus': OutlinedPlus,
        'outlined-editing-square': OutlinedEditingSquare,
        'outlined-download': OutlinedDownload,
        'introduce-panel': IntroducePanel,
        's-textarea': Input.TextArea
    };
    static filters = {
        getSubnetName(name) {
            if (!name) {
                return '系统预定义子网';
            }
            return u.escape(name);
        },
        resourceText(item) {
            if (item.resourceGroups && item.resourceGroups.length) {
                let str = [];
                item.resourceGroups.forEach(resourceGroup => {
                    str.push(u.escape(resourceGroup.resourceGroupName)); //eslint-disable-line
                });
                return str.join('<br>');
            }
            return '-';
        },
        getCidr(item) {
            if (!item) {
                return;
            }
            return u.escape(item.cidr);
        },
        az(item) {
            if (!item.az || item.az === 'UNKNOWN_AZ') {
                return '-';
            }
            return zone.getLabel(item.az);
        },
        subnetType(item) {
            return ServiceType.getTextFromValue(item.subnetType);
        }
    };
    initData() {
        let allColumns = [
            {name: 'id', label: '子网名称/ID', fixed: 'left', minWidth: 200},
            {name: 'sourceVpc', label: '所在网络', minWidth: 140},
            {name: 'cidr', label: 'IPv4网段', width: 120},
            {name: 'ipv6Cidr', label: 'IPv6网段', width: 220},
            {name: 'bccNum', label: '主机个数', width: 80},
            {name: 'diagnose', label: '路径分析', minWidth: 160},
            {name: 'showUseIp', label: '可用IP', width: 80},
            {name: 'az', label: '可用区', width: 80},
            {name: 'subnetType', label: '设备类型', width: 100},
            {name: 'tag', label: '标签', sortable: true, width: 140},
            {name: 'resource', label: '资源组', width: 100},
            {name: 'description', label: '描述', minWidth: 160},
            {name: 'opt', label: '操作', fixed: 'right', width: 110}
        ];
        const customColumnDb = allColumns.map(item => ({
            text: item.label,
            value: item.name,
            disabled: item.name === 'id' || item.name === 'opt'
        }));
        return {
            FLAG,
            klass: ['subnet-list'],
            title: '子网',
            createSubnet: {},
            deleteSubnet: {},
            searchbox: {
                keyword: '',
                placeholder: '请输入实例名称进行搜索',
                defaultLabel: '实例名称',
                keywordType: ['name'],
                keywordTypes: [
                    {value: 'name', text: '实例名称'},
                    {value: 'shortId', text: '实例ID'},
                    {value: 'tag', text: '标签'},
                    {value: 'resGroupId', text: '资源组'}
                ]
            },
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                allColumns,
                datasource: []
            },
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            customColumn: {
                value: [
                    'id',
                    'resourceGroups',
                    'sourceVpc',
                    'cidr',
                    'ipv6Cidr',
                    'bccNum',
                    'diagnose',
                    'showUseIp',
                    'az',
                    'subnetType',
                    'tag',
                    'resource',
                    'description',
                    'opt'
                ],
                datasource: customColumnDb
            },
            order: {
                orderBy: 'createTime',
                order: 'desc'
            },
            edit: {
                name: {
                    value: '',
                    error: true,
                    visible: false
                },
                description: {
                    value: '',
                    error: true,
                    visible: false
                }
            },
            vpcList: [],
            vpcId: '',
            vpcInfo: {},
            subnetList: [],
            resourceSDK: new ResourceGroupSDK(window.$http, window.$context),
            subnetSinDisable: {},
            iamPass: {},
            DocService,
            sourceList: [],
            checkSourceLoading: false,
            urlQuery: getQueryParams(),
            show: true,
            introduceTitle: '子网简介',
            description:
                '子网是VPC网络中的一个重要组成部分，它是对VPC IP地址范围的进一步划分，用于将VPC内的资源组织成不同的逻辑分区，以便更好地管理和隔离网络流量。',
            introduceIcon: INTRODUCE_ICON_COLOR,
            introduceEle: null,
            drawerVisible: true,
            visibleDraw: true,
            openDrawer: false
        };
    }
    inited() {
        this.getIamQuery();
        let vpcId = this.data.get('urlQuery.vpcId');
        if (vpcId) {
            window.$storage.set('vpcId', vpcId);
        }
        this.data.set('table.loading', true);
        const subnetId = this.data.get('urlQuery.id');
        if (subnetId) {
            this.data.set('searchbox.keywordType', ['shortId']);
            this.data.set('searchbox.keyword', subnetId);
        }
        // const isCmcInited = sessionStorage.getItem('cmcSubnetInit');
        // if (!isCmcInited) {
        //     const region = this.data.get('urlQuery.region');
        //     resetNewRegion(region);

        //     sessionStorage.setItem('cmcSubnetInit', true);
        // }
    }
    attached() {
        this.data.set('introduceEle', this.ref('introduce'));
    }
    vpcInt() {
        let {createSubnet, deleteSubnet, subnetSinDisable} = checker.check(rules, []);
        this.data.set('createSubnet', createSubnet);
        this.data.set('deleteSubnet', deleteSubnet);
        this.data.set('subnetSinDisable', subnetSinDisable);
        this.loadPage();
    }
    vpcChange() {
        this.data.set('pager.page', 1);
        this.loadPage();
    }
    // 设置表格列
    setTableColumns(customColumnNames) {
        customColumnNames = customColumnNames ? customColumnNames : this.data.get('customColumn.value');
        const allColumns = this.data.get('table.allColumns');
        let columns = [];
        allColumns.forEach(item => customColumnNames.indexOf(item.name) > -1 && columns.push(item));
        this.data.set('table.columns', columns);
    }
    // 加载页面数据
    async loadPage() {
        this.getSubnetList();
        this.subnetQuota();
    }
    // 获取列表数据
    getSubnetList() {
        this.data.set('table.loading', true);
        this.data.set('table.selection.selectedIndex', []);
        const param = this.getSearchCriteria();
        if (!FLAG.NetworkSubnetSupportPage) {
            // 后端还不支持分页
            delete param.pageNo;
            delete param.pageSize;
            this.$http.vpcSubnetList(param).then(data => {
                this.data.set('subnetList', data);
                this.data.set('table.datasource', data);
                this.data.set('table.loading', false);
            });
        } else {
            this.$http.vpcSubnetPageList(param).then(data => {
                this.data.set('subnetList', data.result);
                this.data.set('table.datasource', data.result);
                this.data.set('pager.total', data.totalCount);
                this.data.set('table.loading', false);
            });
        }
    }
    // 检查配额
    async subnetQuota() {
        if (!window.$storage.get('vpcId')) {
            return;
        }
        const data = await this.$http.subnetQuota({vpcId: window.$storage.get('vpcId')});
        let {createSubnet} = checker.check(rules, data, 'createSubnet', {quota: data.free});
        this.data.set('createSubnet', createSubnet);
    }
    filterIpv6Column() {
        let columns = this.data.get('table.columns');
        let customColumn = this.data.get('customColumn');
        let cidrIndex = this.getColumnIndexByName('cidr');
        let ipv6Index = this.getColumnIndexByName('ipv6Cidr');
        columns[cidrIndex].label = '网段';
        customColumn.datasource[cidrIndex].text = '网段';
        customColumn.datasource.splice(ipv6Index, 1);
        columns = u.filter(columns, item => item.name !== 'ipv6Cidr');
        this.data.set('table.columns', columns);
        this.data.set('customColumn', customColumn);
    }

    getColumnIndexByName(name) {
        let customColumn = this.data.get('customColumn');
        return customColumn.datasource.findIndex(item => item.value === name);
    }

    // 获取标签数据源
    getTags() {
        this.ref('search').getTags();
    }

    getSearchCriteria() {
        const {order, pager} = this.data.get('');
        const searchPayload = this.ref('search') ? this.ref('search').getSearchCriteria() : {};
        let searchParam = {
            pageNo: pager.page,
            pageSize: pager.pageSize,
            ...searchPayload
        };
        searchParam.vpcId = window.$storage.get('vpcId');
        if (searchParam.keywordType === 'resGroupId') {
            searchParam.keywordType = 'resourceGroupName';
        }
        if (FLAG.NetworkSubnetSupportOrganization) {
            const organizationId = window.$framework.organization.getCurrentOrganization().id;
            const currentResourceGroupIds = window.$framework.organization.getCurrentResourceGroup().id;
            const resourceGroupIds = currentResourceGroupIds === 'all' ? [] : [currentResourceGroupIds];
            u.assign(searchParam, {
                organizationId,
                resourceGroupIds
            });
        }
        return u.extend({}, searchParam, order);
    }
    initColumns(value) {
        this.setTableColumns(value);
        this.getSubnetShowIpUsedNumWhiteList();
    }
    // 自定义表格列
    onCustomColumns(value) {
        this.data.set('customColumn.value', value);
        this.setTableColumns(value);
    }
    tableSelected(e) {
        this.data.set('selectedItems', e.value.selectedItems);
        let {deleteSubnet} = checker.check(rules, e.value.selectedItems);
        this.data.set('deleteSubnet', deleteSubnet);
    }
    onSort(e) {
        this.data.set('order', e.value);
        this.loadPage();
    }
    // 点击修改icon
    onInstantEdit(row, rowIndex, type) {
        this.data.set(`edit.${type}.value`, row[type]);
        this.data.set(`edit.${type}.error`, false);
        const a = this.ref(`popover-${type}-${rowIndex}`);
        a.data.set('visible', !a.data.get('visible'));
    }
    // 编辑弹框-输入名称/描述
    onEditInput(e, rowIndex, type) {
        let result =
            type === 'name'
                ? e.value === '' || !/^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/.test(e.value)
                : e.value.length > 200;
        this.data.set(`edit.${type}.error`, result);
        this.data.set(`edit.${type}.value`, e.value);
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', result);
    }
    // 编辑弹框-提交
    editConfirm(row, rowIndex, type) {
        let edit = this.data.get(`edit.${type}`);
        if (edit.error) {
            return;
        }
        const key = type === 'name' ? 'description' : 'name';
        this.$http
            .subnetUpdate({
                id: row.subnetId,
                [type]: edit.value,
                [key]: row[`${key}`]
            })
            .then(() => {
                this.editCancel(rowIndex, type);
                Notification.success('修改成功');
                this.getSubnetList();
            });
    }
    // 编辑弹框-取消
    editCancel(rowIndex, type) {
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', true);
        this.ref(`popover-${type}-${rowIndex}`).data.set('visible', false);
    }
    // 创建实例
    async onCreate() {
        location.hash = '#/vpc/subnet/create';
    }

    // 改变页数
    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }

    // 改变每页显示个数
    onPagerSizeChange(e) {
        this.data.set('pager.pageSize', e.value.pageSize);
        this.data.set('pager.page', 1);
        this.loadPage();
    }
    // 删除实例
    async onDelete() {
        this.data.set('checkSourceLoading', true);
        await this.checkSource();
        let confirm = new DeleteCheckConfirm({
            data: {
                open: true,
                selectedItems: this.data.get('selectedItems'),
                sourceList: this.data.get('sourceList'),
                type: 'subnet'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            let subnetIds = u.map(this.data.get('selectedItems'), o => o.subnetId);
            this.$http.subnetDelete({subnetIds}).then(() => {
                this.refresh();
                Notification.success('删除成功');
            });
        });
    }
    checkSource() {
        let selectedItems = this.data.get('selectedItems');
        let payload = {
            subnetIds: u.map(selectedItems, o => o.subnetId)
        };
        return this.$http
            .checkSubnetBeforeDelete(payload)
            .then(res => {
                let sourceList = [];
                selectedItems.forEach(item => {
                    let id = item.subnetId;
                    if (Object.keys(res.resourceIpCheck[id]).length > 0) {
                        let arr = Object.keys(res.resourceIpCheck[id]).map(i => {
                            return {
                                sourceName: i,
                                sourceNum: res.resourceIpCheck[id][i]
                            };
                        });
                        sourceList.push({
                            name: item.name,
                            source: arr,
                            id
                        });
                    }
                });
                this.data.set('sourceList', sourceList);
                this.data.set('checkSourceLoading', false);
            })
            .catch(err => {
                this.data.set('sourceList', []);
                this.data.set('checkSourceLoading', false);
            });
    }
    // 管理IPv6网段
    onManageIPv6(row) {
        let enableIpv6 = row.ipv6Cidr ? 'false' : 'true';
        if (enableIpv6 === 'false') {
            let confirm = new Confirm({
                data: {
                    open: true,
                    title: '提示',
                    content: '请确定是否要删除该IPv6网段？'
                }
            });
            confirm.attach(document.body);
            confirm.on('confirm', () => {
                this.$http.subnetUpdate({enableIpv6, id: row.subnetId}).then(() => {
                    Notification.success('删除成功');
                    this.loadPage();
                });
            });
        } else {
            this.$http.subnetUpdate({enableIpv6, id: row.subnetId}).then(() => {
                Notification.success('分配成功');
                this.loadPage();
            });
        }
    }
    // 编辑资源分组确定后
    oncommit() {
        this.data.set('showResource', false);
        this.loadPage();
    }
    // 编辑资源分组取消后
    onCancel() {
        this.data.set('showResource', false);
    }
    // 编辑资源分组
    changeResourceGroup(row) {
        let resource = {
            resourceId: row.shortId,
            serviceType: 'SUBNET'
        };
        this.data.set('resource', resource);
        this.data.set('showResource', true);
    }
    changeSearch({value}) {
        let key = value[0];
        let placeHolder = {
            name: '请输入实例名称进行搜索',
            shortId: '请输入实例ID进行搜索',
            tag: '请输入标签名进行搜索'
        };
        this.data.set('searchbox.placeholder', placeHolder[key]);
    }
    onSearch() {
        this.data.set('pager.page', 1);
        this.loadPage();
    }
    redirectToBcc(row, type) {
        redirect({
            module: 'bcc', // 模块名
            path: `/${type ? type : 'bcc'}/instance/list`, // 路径
            paramSeperator: '?', // 指明query分隔符
            params: {
                keyword: row.shortId,
                keywordType: 'subnetId'
            }
        });
    }
    refresh() {
        this.loadPage();
    }
    onRegionChange() {
        location.reload();
    }
    getSubnetShowIpUsedNumWhiteList() {
        const whiteList = window.$storage.get('commonWhite');
        if (!whiteList?.SubnetShowIpUsedNumWhiteList) {
            this.filterShowIpColumn();
        }
    }
    filterShowIpColumn() {
        let columns = this.data.get('table.columns');
        let customColumn = this.data.get('customColumn');
        let showUseIpIndex = this.getColumnIndexByName('showUseIp');
        showUseIpIndex > -1 && customColumn.datasource.splice(showUseIpIndex, 1);
        columns = u.filter(columns, item => item.name !== 'showUseIp');
        this.data.set('table.columns', columns);
        this.data.set('customColumn', customColumn);
    }
    getIamQuery() {
        this.$http.getInterfaceIam({interfaceName: 'createSubnet'}).then(res => {
            if (!res.requestId && !res.masterAccount) {
                if (!res.interfacePermission) {
                    this.data.set('iamPass', {
                        disable: true,
                        message: '您没有创建子网权限，请联系主用户添加'
                    });
                } else {
                    this.data.set('iamPass', {
                        disable: false,
                        message: ''
                    });
                }
            } else {
                this.data.set('iamPass', {
                    disable: false,
                    message: ''
                });
            }
        });
    }
    // 下载
    onDownload() {
        let ids = [];
        ids = this.data.get('selectedItems').map(item => {
            return item.shortId;
        });
        let filter = 'ids=' + ids.join(',');
        window.open(`/api/network/v1/subnet/download?` + filter);
    }
    handleToggle(flag) {
        this.data.set('show', flag);
        this.data.set('introduceIcon', INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#151B26';
    }
    handleShowCard() {
        if (!this.data.get('show')) {
            this.data.set('show', true);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type) {
        this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#2468F2';
    }
    handleMouseLeave(type) {
        const isShow = this.data.get('show');
        this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        !isShow && (ele.style.color = '#151B26');
    }
    pathAnalysis(e) {
        this.data.set('openDrawer', true);
        this.nextTick(() => {
            this.data.set('drawerVisible', true);
            this.data.set('visibleDraw', true);
            if (document.getElementById('satisfactionNew')) {
                this.loadSatisfactionPath(e);
            } else {
                this.pathAnalysis(e);
            }
        });
    }
    loadSatisfactionPath() {
        this.nextTick(() => {
            processorPath.applyComponent(
                'AnalysisGraphDrawer',
                {
                    visible: this.data.get('visibleDraw'),
                    scoreTipTitle: '子网',
                    onCloseDrawer: () => {
                        this.data.set('openDrawer', false);
                        this.data.set('drawerVisible', false);
                        this.data.set('visibleDraw', false);
                    },
                    http: request
                },
                '#satisfactionNew'
            );
        });
    }
    // 定义一个定时器函数，用于检查元素是否存在
}
export default San2React(Processor.autowireUnCheckCmpt(SubnetList));
