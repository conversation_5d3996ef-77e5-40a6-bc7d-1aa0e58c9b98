import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {OutlinedRefresh, OutlinedPlus} from '@baidu/sui-icon';
import {Notification} from '@baidu/sui';
import SubnetAddressList from '../components/addressList';
import u from 'lodash';

import Create from '../components/createReserveportpool';

const {asComponent, invokeSUI, invokeSUIBIZ, invokeAppComp, template, invokeComp} = decorators;
const tpl = html`
    <template>
        <div class="{{kclass}}">
            <h4>嵌套地址池</h4>
            <div class="{{kclass}}-toolbar">
                <s-button skin="primary" disabled="{{createDisable}}" on-click="onCreate"
                    ><outlined-plus />{{'新建地址池'}}
                </s-button>
                <div class="toolbar-right {{kclass}}-search-wrap">
                    <s-search-box
                        datasource="{{searchbox.datasource}}"
                        value="{=searchbox.keyword=}"
                        on-search="onSearch"
                        placeholder="{{searchbox.placeholder}}"
                        keyword-type="{=searchbox.keywordType=}"
                    />
                    <s-button
                        on-click="refresh"
                        class="s-icon-button left_class"
                        track-id="ti_subnet_ip_refresh"
                        track-name="刷新"
                        ><outlined-refresh class="icon-class"
                    /></s-button>
                </div>
            </div>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                has-Expand-Row="{{true}}"
                expandedIndex="{{expandIndex}}"
                on-exprow-expand="onRowExpand"
                track-id="ti_subnet_ip_table"
                track-name="列表操作"
            >
                <div slot="empty">
                    <s-empty on-click="onCreate"> </s-empty>
                </div>
                <div slot="error">
                    {{table.error ? table.error : '啊呀，出错了？'}}
                    <a href="javascript:;" on-click="refresh">{{'重新加载'}}</a>
                </div>
                <div slot="expanded-row">
                    <subnet-address-list instance="{{row}}" on-updateList="refresh"></subnet-address-list>
                </div>
                <div slot="c-id">
                    <span>{{row.name}}</span>
                    <br />
                    <span>{{row.reservePortPoolShortId}}</span>
                </div>
                <div slot="c-dhcp">
                    <span>{{row.dhcpServers[0].instanceId || '-'}}</span>
                </div>
                <div slot="c-description">
                    <span>{{row.description || '-'}}</span>
                </div>
                <div slot="c-opt">
                    <s-button skin="stringfy" on-click="onEdit(row)">修改DHCP</s-button>
                    <s-button skin="stringfy" on-click="deleteReserveportpool(row)" disabled="{{row.hasBindings}}"
                        >删除</s-button
                    >
                </div>
            </s-table>
            <div class="{{kclass}}-pager-wrap">
                <s-pagination
                    s-if="{{pager.total}}"
                    layout="{{'total, pageSize, pager, go'}}"
                    pageSize="{{pager.pageSize}}"
                    total="{{pager.total}}"
                    page="{{pager.page}}"
                    on-pagerChange="onPagerChange"
                    on-pagerSizeChange="onPagerSizeChange"
                />
            </div>
        </div>
    </template>
`;

@template(tpl)
@asComponent('@vpc-subnet-reserveportpools')
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class Detail extends Component {
    static components = {
        'outlined-refresh': OutlinedRefresh,
        'outlined-plus': OutlinedPlus,
        'subnet-address-list': SubnetAddressList
    };
    initData() {
        return {
            kclass: 'subnet-reserveportpools',
            table: {
                columns: [
                    {name: 'id', label: '地址池名称/ID'},
                    {name: 'dhcp', label: '自建DHCP'},
                    {name: 'description', label: '描述'},
                    {name: 'opt', label: '操作'}
                ],
                loading: true,
                datasource: []
            },
            searchbox: {
                datasource: [
                    {value: 'name', text: '地址池名称'},
                    {value: 'reservePortPoolShortId', text: '地址池短ID'}
                ],
                keywordType: ['name'],
                keyword: '',
                placeholder: '请输入地址池名称进行搜索'
            },
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            createDisable: false,
            expandIndex: []
        };
    }

    attached() {
        this.getQuota();
        this.loadPage();
        this.watch('searchbox.keywordType', value => {
            this.data.set(
                'searchbox.placeholder',
                value[0] === 'name' ? '请输入地址池名称进行搜索' : '请输入地址池短ID进行搜索'
            );
        });
    }

    refresh() {
        this.loadPage();
    }

    onSearch() {
        this.loadPage();
    }

    loadPage() {
        this.data.set('table.loading', true);
        const payload = this.getPayload();
        this.$http
            .getSubnetReserveportpools(payload, {'x-silent-codes': ['Subnet.SubnetNotFoundException']})
            .then(data => {
                this.data.set('table.datasource', data.result);
                this.data.set('pager.total', data.totalCount);
                this.data.set('table.loading', false);
            });
    }

    getPayload() {
        const {keywordType, keyword} = this.data.get('searchbox');
        const {page, pageSize} = this.data.get('pager');
        const params = {
            keywordType: keywordType.join(''),
            keyword,
            pageNo: page,
            pageSize,
            subnetId: this.data.get('context').subnetId
        };
        return params;
    }

    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }

    onPagerSizeChange(e) {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', e.value.pageSize);
        this.loadPage();
    }

    // 当点开其他展开项时收起之前的展开项
    onRowExpand(e) {
        const {rowIndex} = e.value;
        this.data.set('expandIndex', [rowIndex]);
    }

    onCreate() {
        const dialog = new Create({
            data: {
                subnetId: this.data.get('context').subnetId,
                reserveportpools: this.data.get('table.datasource') || []
            }
        });
        dialog.on('create', () => {
            this.getQuota();
            this.refresh();
        });
        dialog.attach(document.body);
    }

    onEdit(instance) {
        const dialog = new Create({
            data: {
                subnetId: this.data.get('context').subnetId,
                instance,
                reserveportpools: this.data.get('table.datasource') || []
            }
        });
        dialog.on('create', () => {
            this.getQuota();
            this.refresh();
        });
        dialog.attach(document.body);
    }

    getQuota() {
        this.$http
            .getSubenetReserveportpoolQuota(this.data.get('context').subnetId, {
                'x-silent-codes': ['Subnet.SubnetNotFoundException']
            })
            .then(res => {
                this.data.set('createDisable', res.reservePortPoolFree < 0);
            });
    }

    deleteReserveportpool(row) {
        this.$http
            .deleteSubnetReserveportpool(this.data.get('context').subnetId, row.reservePortPoolId)
            .then(res => {
                this.getQuota();
                this.refresh();
                Notification.success('删除成功');
            })
            .catch(err => {
                this.refresh();
                Notification.error('删除失败');
            });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(Detail));
