import {html, decorators, CreatePage, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import u from 'lodash';
import {TagEditPanel} from '@baidu/bce-tag-sdk-san';
import {Dialog, Input} from '@baidu/sui';
import {ResourceGroupPanel} from '@baidu/bce-resourcegroup-sdk-san';
import {ResourceGroupSDK} from '@baidu/bce-resourcegroup-sdk';
import {OutlinedPlus} from '@baidu/sui-icon';
import {checker} from '@baiducloud/bce-opt-checker';

import rules from '../rule';
import zone from '@/pages/sanPages/utils/zone';
import {RouteSourceAll, Netseg, ServiceType} from '@/pages/sanPages/common';
import {
    getDifferenceAvaliableNew,
    checkIsInSubnet,
    getMaskNum,
    getAvaliableContent,
    convertCidrToBinary
} from '@/pages/sanPages/utils/common';
import '../style/create.less';
import RULE from '@/pages/sanPages/utils/rule';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';

const {invokeSUI, invokeSUIBIZ, invokeAppComp, template} = decorators;

const tpl = html`
    <template>
        <s-app-create-page
            class="vpc-subnets-create"
            backTo="{{pageNav.backUrl}}"
            backToLabel="{{pageNav.backLabel}}"
            pageTitle="{{pageNav.title}}"
        >
            <s-form s-ref="form" data="{=formData=}" label-align="left">
                <div class="form-part-wrap">
                    <h4 class="title_vpc">配置信息</h4>
                    <s-form-item label="所在网络:" prop="vpcName">
                        <label class="inline-col" s-if="{{!vpcList}}">{{vpcInfo.name}}</label>
                        <s-select
                            filterable
                            s-else
                            width="{{300}}"
                            value="{=currentVpcId=}"
                            on-change="vpcInfoChange($event)"
                        >
                            <s-select-option s-for="item in vpcInfoList" value="{{item.value}}" label="{{item.text}}">
                                <s-tooltip>
                                    <div slot="content">
                                        <!--bca-disable-next-line-->
                                        {{item.text | raw}}
                                    </div>
                                    <div>{{item.text}}</div>
                                </s-tooltip>
                            </s-select-option>
                        </s-select>
                    </s-form-item>
                    <s-form-item>
                        <s-table
                            style="overflow: visible"
                            columns="{{table.columns}}"
                            datasource="{{table.datasource}}"
                        >
                            <div slot="empty">
                                <s-empty on-click="addRule" />
                            </div>
                            <div slot="h-name">
                                {{col.label}}
                                <s-tip
                                    class="inline-tip"
                                    content="大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头，长度1-65"
                                    skin="warning"
                                />
                            </div>
                            <div slot="h-broadcast">
                                {{col.label}}
                                <s-tip class="inline-tip" content="BBC型暂不支持子网广播" skin="warning" />
                            </div>
                            <div slot="h-enableIpv6">
                                {{col.label}}
                                <s-tip
                                    class="inline-tip"
                                    content="为该子网统一分配掩码为64的IPv6 CIDR块，
                                        用户不可自定义网段，默认不分配。"
                                    skin="warning"
                                />
                            </div>
                            <div slot="c-name" prop="name">
                                <s-input
                                    width="100"
                                    value="{=name[rowIndex]=}"
                                    track-id="ti_vpc_subnet_create"
                                    on-input="dataInput('name', rowIndex, $event)"
                                    track-name="子网名称"
                                />
                                <p class="cidr-tip-err" s-if="nameErr[rowIndex]">{{nameErr[rowIndex]}}</p>
                            </div>
                            <div slot="c-vpcCidr" prop="vpcCidr">
                                <s-select
                                    width="140"
                                    datasource="{{vpcCidrList}}"
                                    value="{=vpcCidr[rowIndex]=}"
                                    on-change="vpcCidrChange(rowIndex, $event)"
                                    track-id="ti_vpc_subnet_create"
                                    track-name="VPC边界"
                                />
                            </div>
                            <div slot="c-CIDR">
                                <div class="cidr-flex">
                                    <div prop="CIDR0" class="CIDR-class">
                                        <s-select
                                            width="{{80}}"
                                            datasource="{{cidr0List}}"
                                            disable="{{disableCidr0}}"
                                            value="{=cidr0[rowIndex]=}"
                                            on-change="cidr0Change(rowIndex, $event)"
                                            track-id="ti_vpc_subnet_create"
                                            track-name="CIDR"
                                        />
                                        <span class="span-class"> . </span>
                                    </div>
                                    <div prop="cidr1" class="CIDR-class">
                                        <s-input
                                            width="50"
                                            value="{{cidr1[rowIndex]}}"
                                            on-input="cidrInput(rowIndex, 1, $event)"
                                            on-focus="cidrFocus(1, rowIndex)"
                                            on-blur="cidrBlur(rowIndex)"
                                            name="CIDR1"
                                            disabled="{{disableCidr1}}"
                                            track-id="ti_vpc_subnet_create"
                                            track-name="CIDR"
                                            maxLength="3"
                                        />
                                        <span class="span-class"> . </span>
                                    </div>
                                    <div prop="cidr2" class="CIDR-class">
                                        <s-input
                                            width="50"
                                            value="{{cidr2[rowIndex]}}"
                                            on-input="cidrInput(rowIndex, 2, $event)"
                                            on-focus="cidrFocus(2, rowIndex)"
                                            on-blur="cidrBlur(rowIndex)"
                                            name="CIDR2"
                                            disabled="{{disableCidr2}}"
                                            track-id="ti_vpc_subnet_create"
                                            track-name="CIDR"
                                            maxLength="3"
                                        />
                                        <span class="span-class"> . </span>
                                    </div>
                                    <div prop="cidr3" class="CIDR-class">
                                        <s-input
                                            width="50"
                                            value="{{cidr3[rowIndex]}}"
                                            on-input="cidrInput(rowIndex, 3, $event)"
                                            on-focus="cidrFocus(3, rowIndex)"
                                            on-blur="cidrBlur(rowIndex)"
                                            name="CIDR3"
                                            disabled="{{disableCidr3}}"
                                            track-id="ti_vpc_subnet_create"
                                            track-name="CIDR"
                                            maxLength="3"
                                        />
                                        <span class="span-class slash-class"> / </span>
                                    </div>
                                    <div prop="mask">
                                        <s-select
                                            width="{{60}}"
                                            datasource="{{maskList}}"
                                            value="{=mask[rowIndex]=}"
                                            on-change="maskChange(rowIndex, $event)"
                                            track-id="ti_vpc_subnet_create"
                                            track-name="CIDR"
                                        />
                                    </div>
                                </div>
                                <div>
                                    <div class="cidr-tip-new">{{ipTotalTip[rowIndex]}}</div>
                                    <div class="cidr-tip-new" s-if="showCidrTip[rowIndex]">{{cidrTip[rowIndex]}}</div>
                                    <!--bca-disable-next-line-->
                                    <div class="cidr-tip-err" s-if="cidrErr[rowIndex]">{{cidrErr[rowIndex] | raw}}</div>
                                </div>
                            </div>
                            <div slot="c-zone" prop="zone">
                                <s-select
                                    width="90"
                                    datasource="{{zoneList}}"
                                    disabled="{{haveSet}}"
                                    value="{=zone[rowIndex]=}"
                                    on-change="dataChange('zone', rowIndex, $event)"
                                    track-id="ti_vpc_subnet_create"
                                    track-name="可用区"
                                />
                                <p class="cidr-tip-err" s-if="zoneErr[rowIndex]">{{zoneErr[rowIndex]}}</p>
                            </div>
                            <div slot="c-subnetType" prop="subnetType">
                                <s-select
                                    width="80"
                                    datasource="{{subnetTypeList}}"
                                    value="{=subnetType[rowIndex]=}"
                                    track-id="ti_vpc_subnet_create"
                                    on-change="dataChange('subnetType', rowIndex, $event)"
                                    track-name="设备类型"
                                />
                            </div>
                            <div slot="c-enableIpv6">
                                <s-switch
                                    checked="{=enableIpv6[rowIndex]=}"
                                    track-id="ti_vpc_subnet_create_ipv6"
                                    disabled="{{!vpcInfo.ipv6Cidr}}"
                                    on-change="dataChange('enableIpv6', rowIndex, $event)"
                                    track-name="分配IPv6网段"
                                />
                            </div>
                            <div slot="c-broadcast" prop="broadcast">
                                <s-switch
                                    checked="{=broadcast[rowIndex]=}"
                                    track-id="ti_vpc_subnet_create_broadcast"
                                    on-change="broadcastChange(rowIndex, $event)"
                                    disabled="{=broadcastDisable[rowIndex]=}"
                                    track-name="子网广播"
                                />
                            </div>
                            <div slot="c-description" prop="description">
                                <s-input
                                    maxLength="200"
                                    placeholder="请输入"
                                    width="130"
                                    value="{=description[rowIndex]=}"
                                    on-input="dataInput('description', rowIndex, $event)"
                                    track-id="ti_vpc_subnet_create"
                                    track-name="描述"
                                />
                            </div>
                            <div slot="c-opt">
                                <s-button skin="stringfy" on-click="deleteRule(rowIndex)">删除</s-button>
                            </div>
                        </s-table>
                        <div style="padding:10px 0 0 10px" s-if="!isEdit">
                            <s-tooltip trigger="{{createSubnetBatch.disable ? 'hover' : ''}}" placement="right">
                                <!--bca-disable-next-line-->
                                <div slot="content">{{createSubnetBatch.message | raw}}</div>
                                <s-button
                                    skin="primary"
                                    disabled="{{createSubnetBatch.disable || overQuota}}"
                                    on-click="addRule"
                                >
                                    <outlined-plus />新增一行
                                </s-button>
                            </s-tooltip>
                        </div>
                    </s-form-item>
                </div>
                <div class="form-part-wrap">
                    <div class="resource-form-part-wrap">
                        <resource-group-panel
                            refreshAvailable="{{true}}"
                            sdk="{{resourceSDK}}"
                            on-change="resourceChange($event)"
                        />
                    </div>
                </div>
                <div class="form-part-wrap">
                    <h4 s-if="!FLAG.NetworkVpcSupOrganization" class="title_vpc">标签</h4>
                    <s-form-item prop="tag" s-if="!FLAG.NetworkVpcSupOrganization" label="绑定标签：">
                        <tag-edit-panel
                            class="tag-class"
                            create
                            instances="{{defaultInstances}}"
                            options="{{tagListRequster}}"
                            s-ref="tagPanel"
                        />
                    </s-form-item>
                </div>
            </s-form>
            <div slot="pageFooter" class="buybucket">
                <div class="buybucket-container">
                    <s-tooltip content="{{iamPass.message || ''}}">
                        <s-button
                            size="large"
                            on-click="onCreate"
                            skin="primary"
                            disabled="{{iamPass.disable || disableSubmit || overQuota || !table.datasource.length}}"
                        >
                            <s-tooltip content="{{disableTip}}">确定</s-tooltip>
                        </s-button>
                    </s-tooltip>
                    <s-button size="large" on-click="close"> 取消 </s-button>
                </div>
            </div>
        </s-app-create-page>
    </template>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class SubnetCreate extends CreatePage {
    static template = tpl;
    static components = {
        's-textarea': Input.TextArea,
        'outlined-plus': OutlinedPlus,
        'tag-edit-panel': TagEditPanel,
        'resource-group-panel': ResourceGroupPanel
    };

    static messages = {
        projectChange({value}) {
            value && this.data.set('resourceGroupId', value);
        }
    };

    initData() {
        return {
            FLAG,
            pageNav: {
                title: '创建子网',
                backUrl: '/network/#/vpc/subnet/list',
                backLabel: '返回子网列表'
            },
            defaultInstances: [
                {
                    tags: [
                        {
                            tagKey: '默认项目',
                            tagValue: ''
                        }
                    ]
                }
            ],
            tagListRequster: this.tagListRequster.bind(this),
            zoneList: [],
            vpcCidrList: [],
            subnetList: [],
            vpcList: [],
            subnetTypeList: [],
            haveSet: false,
            disableCidr0: false,
            disableCidr1: false,
            disableCidr2: false,
            disableCidr3: false,
            cidrTip: [],
            showCidrTip: [],
            disableSubmit: false,
            open: true,
            cidrErr: [],
            nameErr: [],
            zoneErr: [],
            ipTotalTip: [],
            docUrl: '',
            resourceGroupId: '',
            resourceSDK: new ResourceGroupSDK(window.$http, window.$context),
            table: {
                columns: [
                    {name: 'name', label: '子网名称', width: 50},
                    {name: 'vpcCidr', label: 'VPC边界', width: 60},
                    {name: 'zone', label: '可用区', width: 40},
                    {name: 'subnetType', label: '设备类型', width: 45},
                    {name: 'description', label: '描述', width: 55},
                    {name: 'opt', label: '操作', width: 40}
                ],
                datasource: []
            },
            name: [],
            vpcCidr: [],
            zone: [],
            subnetType: [],
            enableIpv6: [],
            description: [],
            cidr0: [],
            cidr1: [],
            cidr2: [],
            cidr3: [],
            mask: [],
            cidr0List: [],
            tableCidrList: [],
            createSubnetBatch: {},
            broadcast: [],
            allowBroadcast: false,
            broadcastDisable: [],
            needValidate: true,
            iamPass: {},
            cidrErrIndex: 0
        };
    }

    async inited() {
        this.getIamQuery();
        this.data.set('subnetTypeList', [ServiceType.fromAlias('BCC')]);
        const payload = await this.getSubnetPayload();
        this.data.set('availableService', payload.availableService);
        this.data.set('vpcInfo', payload.vpcInfo);
        this.data.set('vpcList', payload.vpcList);
        const vpcList = this.data.get('vpcList');
        const vpcInfo = vpcList ? vpcList[0] : this.data.get('vpcInfo');
        this.data.set('currentVpcId', vpcInfo.vpcId);
        if (vpcInfo) {
            this.checkVpcQuota(vpcInfo.vpcId);
        }
        // 设置设备类型数据源
        this.setSubnetTypeList();
        // 如果调用方传了zone，将可用区设置为该值，且不可选其它值
        if (this.data.get('zone').length) {
            this.data.set('zone[0]', this.data.get('zone'));
            this.data.set('haveSet', true);
        }
        this.addRule();
        this.getBroadcastWhitelist();
    }
    getVpcList() {
        return this.$http.vpcList();
    }
    attached() {
        this.$http.getZoneList().then(data => {
            const zoneList = u.map(u.map(data, 'zone'), item => {
                return {
                    value: item,
                    text: zone.getLabel(item)
                };
            });
            this.data.set('zoneList', zoneList);
        });
        this.data.splice('table.columns', [
            3,
            0,
            {
                name: 'CIDR',
                label: 'IPv4 CIDR',
                width: 130
            }
        ]);
        this.data.splice('table.columns', [
            5,
            0,
            {
                name: 'enableIpv6',
                label: '分配IPv6网段',
                width: 50
            }
        ]);
        this.$http.subnetBatchCreateQuota().then(res => {
            this.data.set('subnetBatchCreateQuota', res.total);
        });
    }

    static computed = {
        vpcInfoList() {
            let vpcList = this.data.get('vpcList');
            return u.map(vpcList, item => {
                return {
                    text: `${item.name}（${item.cidr}）`,
                    value: item.vpcId
                };
            });
        },
        disableTip() {
            let overQuota = this.data.get('overQuota');
            return overQuota ? '当前VPC实例子网配额不足' : '';
        }
    };
    tagListRequster() {
        return this.$http.getSearchTagList({serviceType: [], region: ['global']});
    }
    async getSubnetPayload() {
        let vpcId = window.$storage.get('vpcId');
        let vpcInfo = window.$storage.get('vpcInfo');
        let payload: Record<string, any> = {
            vpcInfo,
            availableService: window.$context.getAvailableService().split(','),
            supportOrg: FLAG.NetworkVpcSupOrganization
        };
        if (!vpcId) {
            const vpcList = await this.getVpcList();
            payload.vpcList = vpcList;
            payload.vpcInfo = vpcList?.[0];
        }
        return payload;
    }
    setSubnetTypeList() {
        // 设置所选类型数据源
        if (u.indexOf(this.data.get('availableService'), 'BBC') > -1) {
            this.data.push('subnetTypeList', ServiceType.fromAlias('BBC'));
        }
        const subnetTypes = this.data.get('subnetTypes');
        if (subnetTypes) {
            let subnetTypeList = this.data.get('subnetTypeList');
            subnetTypeList = u.filter(subnetTypeList, item => u.includes(subnetTypes, item.value));
            this.data.set('subnetTypeList', subnetTypeList);
        }
    }
    setVpcCidrList(rowIndex) {
        let vpcInfo = this.data.get('vpcInfo');
        // 设置vpc边界数据源
        let vpcCidrList = [
            {
                value: vpcInfo.cidr,
                text: vpcInfo.cidr + '（主）'
            }
        ];
        u.each(vpcInfo.auxiliaryCidr || [], cidr =>
            vpcCidrList.push({
                value: cidr,
                text: cidr + '（辅）'
            })
        );
        this.data.set('vpcCidrList', vpcCidrList);
        if (rowIndex) {
            this.updateCidrData(rowIndex, vpcInfo.cidr);
            this.ipTotal(rowIndex);
        }
        if (!rowIndex) {
            this.nextTick(() => {
                let tableLength = this.data.get('table.datasource').length;
                if (tableLength) {
                    this.data.get('table.datasource').forEach((item, index) => {
                        this.data.set(`cidrErr[${index}]`, '');
                        this.data.set(`vpcCidr[${index}]`, vpcCidrList[0].value);
                        this.data.set(`enableIpv6[${index}]`, false);
                        this.updateCidrData(index, vpcInfo.cidr);
                        this.ipTotal(index);
                    });
                }
            });
        }
    }
    vpcInfoChange(e) {
        let value = e.value;
        if (this.data.get('allowBroadcast')) {
            this.checkBroadcast(value);
        }
        let vpcInfoList = this.data.get('vpcList');
        let currentVpc = vpcInfoList.filter(item => item.vpcId === value);
        this.data.set('vpcInfo', currentVpc[0]);
        this.checkVpcQuota(value);
        this.setVpcCidrList();
    }
    updateCidrData(rowIndex, cidr) {
        const array = cidr.split('/');
        const cidrPart = array[0].split('.');
        if (cidr === RouteSourceAll.ALL) {
            this.data.set('cidr0List', [Netseg.fromAlias('SEG192'), Netseg.fromAlias('SEG172')]);
            this.data.set(`cidr0[${rowIndex}]`, this.data.get('cidr0List[0].value'));
            this.data.set(`cidr1[${rowIndex}]`, 168);
            this.data.set(`cidr2[${rowIndex}]`, 0);
            this.data.set(`cidr3[${rowIndex}]`, 0);
            this.data.set('cidrMask', this.getSubnetMask(16));
            this.data.set(`mask[${rowIndex}]`, 24);
            this.data.set('maskList', this.data.get('cidrMask'));
            this.cidrCheck();
        } else {
            const cidr0Value = cidrPart[0] || 0;
            this.data.set('cidr0List', [
                {
                    text: cidr0Value,
                    value: cidr0Value
                }
            ]);
            this.data.set(`cidr0[${rowIndex}]`, this.data.get('cidr0List[0].value'));
            this.data.set(`cidr1[${rowIndex}]`, cidrPart[1] || 0);
            this.data.set(`cidr2[${rowIndex}]`, cidrPart[2] || 0);
            this.data.set(`cidr3[${rowIndex}]`, cidrPart[3] || 0);
            const vpcMask = +array[1];
            this.data.set('cidrMask', this.getSubnetMask(vpcMask) || []);
            const final = this.data.get('cidrMask').length;
            if (vpcMask < 23) {
                this.data.set(`mask[${rowIndex}]`, 24);
            } else if (vpcMask < this.data.get(`cidrMask[${final - 1}].value`)) {
                this.data.set(`mask[${rowIndex}]`, vpcMask + 1);
            } else {
                this.data.set(`mask[${rowIndex}]`, this.data.get(`cidrMask[${final - 1}].value`));
            }
            this.data.set('maskList', this.data.get('cidrMask'));
            this.updateCidrInput(rowIndex, this.data.get(`mask[${rowIndex}]`), cidr);
        }
    }

    ipTotal(rowIndex) {
        const num = 32 - this.data.get(`mask[${rowIndex}]`);
        let totalIp = 1;
        for (let i = 0; i < num; i++) {
            totalIp = totalIp * 2;
        }
        this.data.set(`ipTotalTip[${rowIndex}]`, `当前子网可用IP数${totalIp - 3}个`);
    }

    getSubnetMask(mask) {
        mask = +mask;
        const result = [];
        for (let i = mask; i <= 29; i++) {
            result.push({
                value: i,
                text: i
            });
        }
        return result;
    }
    updateCidrInput(rowIndex, mask, cidr) {
        if (!cidr) {
            cidr = this.data.get('vpcCidr[0]');
        }
        const vpcCidrArray = cidr.split('/');
        const vpcMask = parseInt(vpcCidrArray[1], 10);
        const subnetMask = parseInt(mask, 10);
        const vpcMaskNum = getMaskNum(vpcMask);
        const subnetMaskNum = getMaskNum(subnetMask);
        const cidrPart = vpcCidrArray[0].split('.');

        for (let i = 1; i < 4; i++) {
            if (i <= vpcMaskNum) {
                if (i === vpcMaskNum && vpcMask < (i + 1) * 8 && vpcMask !== subnetMask) {
                    this.data.set(`disableCidr${i}`, false);
                } else {
                    this.data.set(`disableCidr${i}`, true);
                    this.data.set(`formCidr${i}`, cidrPart[i]);
                }
            } else if (i > vpcMaskNum && i <= subnetMaskNum) {
                this.data.set(`disableCidr${i}`, false);
            } else if (i > subnetMaskNum) {
                this.data.set(`disableCidr${i}`, true);
                this.data.set(`formCidr${i}`, 0);
            }
        }
        if (!this.data.get(`cidrErr[${rowIndex}]`)) {
            this.cidrCheck();
        }
    }
    checkVpcQuota(vpcId) {
        this.$http.subnetQuota({vpcId}).then(res => {
            this.data.set('overQuotaNum', res.free);
            this.data.set('overQuota', res.free <= 0);
        });
    }
    resourceChange({value}) {
        this.data.set('formData.resourceGroupId', value.data.groupId);
    }
    addRule() {
        this.data.set('needValidate', true);
        let index = this.data.get('table.datasource').length;
        this.data.push('vpcCidr', this.data.get('vpcCidrList[0].value'));
        this.data.push('subnetType', this.data.get('subnetTypeList[0].value'));
        this.data.set(`cidr0[${index}]`, this.data.get('cidr0List[0].value'));
        this.data.set(`nameErr[${index}]`, '名称必填');
        this.data.set(`zoneErr[${index}]`, '可用区必选');
        this.data.push('table.datasource', {});
        this.setVpcCidrList(index);
        let subnetBatchCreateQuota = this.data.get('subnetBatchCreateQuota');
        let {createSubnetBatch} = checker.check(rules, index + 1, 'createSubnetBatch', {
            quotaBatch: subnetBatchCreateQuota,
            quotaTotal: this.data.get('overQuotaNum')
        });
        this.data.set('createSubnetBatch', createSubnetBatch);
    }
    vpcCidrChange(rowIndex, e) {
        this.data.set('needValidate', true);
        this.updateCidrData(rowIndex, e.value);
        this.data.set(`vpcCidr[${rowIndex}]`, e.value);
        this.cidrCheck(); // 切换VPC边界重新校验cidr
    }
    validateCidrLegal(rowIndex: number) {
        const rowCidr = this.getCidrByRowIndex(rowIndex);
        const valueString = convertCidrToBinary(rowCidr);
        const mask = this.data.get(`mask[${rowIndex}]`);
        if (valueString.substring(+mask, valueString.length).includes('1')) {
            this.data.set(`cidrErr[${rowIndex}]`, 'CIDR不合法');
        }

        return !valueString.substring(+mask, valueString.length).includes('1');
    }
    getAvailableInput = (rowIndex: number, cidrIndex: number) => {
        const mask = this.data.get(`mask[${rowIndex}]`);
        const maskList = this.data.get('maskList');
        const vpcCidr = this.data.get(`vpcCidr[${rowIndex}]`);
        let result = [];
        if (cidrIndex === 1) {
            const cidr0 = this.data.get(`cidr0[${rowIndex}]`);
            result = getDifferenceAvaliableNew(mask, maskList, cidrIndex, vpcCidr, cidr0);
        } else {
            result = getDifferenceAvaliableNew(mask, maskList, cidrIndex, vpcCidr);
        }
        return result;
    };
    cidrInput(rowIndex, index, e) {
        this.data.set('needValidate', true);
        // 如果存在不合法的校验直接返回
        const cidrErrIndex = this.data.get('cidrErrIndex');
        const isExistErr = this.data.get(`cidrErr[${rowIndex}]`);
        if (isExistErr === 'CIDR不合法' && cidrErrIndex !== index) {
            return;
        }
        if (!e.value) {
            this.data.set(`cidrErr[${rowIndex}]`, '不能为空');
        } else if (!/^[0-9]*$/.test(e.value)) {
            this.data.set(`cidrErr[${rowIndex}]`, '请填写数字');
        } else if (index === 1) {
            const result = this.getAvailableInput(rowIndex, index);
            if (u.indexOf(result, parseInt(e.value, 10)) > -1) {
                this.data.set(`cidr${index}[${rowIndex}]`, Number(e.value));
                const isCidrLegal = this.validateCidrLegal(rowIndex);
                if (isCidrLegal) {
                    this.data.set(`cidrErr[${rowIndex}]`, '');
                    this.cidrCheck();
                }
            } else {
                this.data.set(`cidr${index}[${rowIndex}]`, Number(e.value));
                this.data.set('cidrErrIndex', index);
                this.data.set(`cidrErr[${rowIndex}]`, 'CIDR不合法');
            }
        } else if (index === 2 || index === 3) {
            const result = this.getAvailableInput(rowIndex, index);
            if (u.indexOf(result, parseInt(e.value, 10)) > -1) {
                this.data.set(`cidr${index}[${rowIndex}]`, Number(e.value));
                const isCidrLegal = this.validateCidrLegal(rowIndex);
                if (isCidrLegal) {
                    this.data.set(`cidrErr[${rowIndex}]`, '');
                    this.cidrCheck();
                }
            } else {
                this.data.set(`cidr${index}[${rowIndex}]`, Number(e.value));
                this.data.set('cidrErrIndex', index);
                this.data.set(`cidrErr[${rowIndex}]`, 'CIDR不合法');
            }
        }
    }
    dataChange(type, rowIndex, e) {
        this.data.set(`${type}[${rowIndex}]`, e.value);
        let hasBroadcast = this.data
            .get('table.columns')
            .map(item => item.name)
            .indexOf('broadcast');
        if (type === 'subnetType' && e.value === 2 && hasBroadcast > -1) {
            this.data.set(`broadcastDisable[${rowIndex}]`, true);
            this.data.set(`broadcast[${rowIndex}]`, 0);
        } else if (type === 'subnetType' && e.value !== 2) {
            hasBroadcast > -1 ? this.data.set(`broadcastDisable[${rowIndex}]`, false) : '';
        }
        if (type === 'zone' && e.value) {
            this.data.set(`zoneErr[${rowIndex}]`, '');
        }
    }
    cidr0Change(rowIndex, e) {
        this.data.set('needValidate', true);
        // 当有环回地址时切换再更新cidr1
        let cidr = this.data.get(`vpcCidr${rowIndex}`);
        if (!this.data.get('disableCidr0') && cidr === RouteSourceAll.ALL) {
            const mask = this.data.get(`mask${rowIndex}`);
            const value = getPartDatasource(e.value, mask)[0].value;
            this.data.set(`cidr1${rowIndex}`, value);
        }
    }
    getCidrByRowIndex(rowIndex: number) {
        const cidr0 = this.data.get(`cidr0[${rowIndex}]`);
        const cidr1 = this.data.get(`cidr1[${rowIndex}]`);
        const cidr2 = this.data.get(`cidr2[${rowIndex}]`);
        const cidr3 = this.data.get(`cidr3[${rowIndex}]`);
        const cidr = cidr0 + '.' + cidr1 + '.' + cidr2 + '.' + cidr3 + '/' + this.data.get(`mask[${rowIndex}]`);
        return cidr;
    }
    maskChange(rowIndex, e) {
        this.data.set('needValidate', true);
        this.data.set(`mask[${rowIndex}]`, e.value);
        const cidr = this.getCidrByRowIndex(rowIndex);
        let reg = new RegExp(RULE.IP_CIDR);
        let valueString = convertCidrToBinary(cidr);

        if (!reg.test(cidr)) {
            this.data.set(`cidrErr[${rowIndex}]`, 'CIDR不合法');
        } else if (valueString.substring(+e.value, valueString.length).includes('1')) {
            this.data.set(`cidrErr[${rowIndex}]`, 'CIDR不合法');
        } else {
            // 切换掩码每个cidr位重新校验一遍
            const result = this.getAvailableInput(rowIndex, 1);
            const result2 = this.getAvailableInput(rowIndex, 2);
            const result3 = this.getAvailableInput(rowIndex, 3);

            const cidr1 = this.data.get(`cidr1[${rowIndex}]`);
            const cidr2 = this.data.get(`cidr2[${rowIndex}]`);
            const cidr3 = this.data.get(`cidr3[${rowIndex}]`);

            const isCidr1Legal = u.indexOf(result, parseInt(cidr1, 10)) > -1;
            const isCidr2Legal = u.indexOf(result2, parseInt(cidr2, 10)) > -1;
            const isCidr3Legal = u.indexOf(result3, parseInt(cidr3, 10)) > -1;

            if (!isCidr1Legal || !isCidr2Legal || !isCidr3Legal) {
                this.data.set(`cidrErr[${rowIndex}]`, 'CIDR不合法');
            } else {
                this.data.set(`cidrErr[${rowIndex}]`, '');
            }
        }
        this.ipTotal(rowIndex);
        this.updateCidrInput(rowIndex, e.value);
    }
    cidrCheck() {
        let datasource = this.data.get('table.datasource');
        let tableCidrList = [];
        datasource.forEach((item, index) => {
            const cidr0 = this.data.get(`cidr0[${index}]`);
            const cidr1 = this.data.get(`cidr1[${index}]`);
            const cidr2 = parseInt(this.data.get(`cidr2[${index}]`), 10);
            const cidr3 = parseInt(this.data.get(`cidr3[${index}]`), 10);
            const cidr = cidr0 + '.' + cidr1 + '.' + cidr2 + '.' + cidr3 + '/' + this.data.get(`mask[${index}]`);
            tableCidrList.push(cidr);
        });
        let vpcInfo = this.data.get('vpcInfo');
        // 先校验表格内的网段是否有重叠，再判断和vpc内的子网是否有重叠
        if (tableCidrList.length) {
            if (tableCidrList.length > 1) {
                tableCidrList.forEach((item, index, arr) => {
                    let overlapSubnet = arr.filter((sub, subIndex) => {
                        // 重置校验结果
                        this.data.set(`cidrErr[${index}]`, '');
                        // 不和自己本身校验
                        if (index === subIndex) {
                            return false;
                        }
                        return checkIsInSubnet(item, sub) || checkIsInSubnet(sub, item);
                    });

                    if (overlapSubnet && overlapSubnet.length) {
                        let subnetStr = '';
                        if (overlapSubnet.length > 5) {
                            overlapSubnet = overlapSubnet.slice(0, 5);
                            subnetStr = overlapSubnet.map(item => '（' + item + '）').join('<br>') + '...';
                        } else {
                            subnetStr = overlapSubnet.map(item => '（' + item + '）').join('<br>');
                        }
                        this.data.set(
                            `cidrErr[${index}]`,
                            `该子网cidr与当前网段冲突，冲突网段：${overlapSubnet.length > 1 ? '<br>' : ''}${subnetStr}`
                        );
                    } else {
                        this.data.set(`cidrErr[${index}]`, '');
                    }
                });
            } else {
                this.data.set(`cidrErr[${0}]`, '');
            }
            let hasErr = this.data.get('cidrErr').filter(err => err).length > 0;
            if (!hasErr) {
                this.$http
                    .subnetConflictValidate({
                        vpcUuid: vpcInfo.vpcId,
                        cidr: tableCidrList
                    })
                    .then(res => {
                        let conflictList = res.subnetConflictResponse;
                        if (conflictList) {
                            // 返回结果和传入参数是一一对应的，因此可以用来判断哪一行的网段有冲突
                            tableCidrList.forEach((item, index) => {
                                let conflictItem = conflictList[index];
                                if (conflictItem.conflict) {
                                    let subnetStr = `${conflictItem.conflictSubnetName}（${conflictItem.conflictSubnetCidr}）`;
                                    this.data.set(
                                        `cidrErr[${index}]`,
                                        `该子网cidr与已有子网冲突，冲突网段：${subnetStr}`
                                    );
                                } else {
                                    if (!['CIDR不合法', '不能为空'].includes(this.data.get(`cidrErr[${index}]`))) {
                                        this.data.set(`cidrErr[${index}]`, '');
                                    }
                                }
                            });
                        }
                    });
            }
        }
    }
    cidrFocus(index, rowIndex) {
        const cidr = this.data.get(`vpcCidr[${rowIndex}]`);
        const mask = this.data.get(`mask[${rowIndex}]`);
        const maskList = this.data.get('maskList');
        if (index === 1) {
            const cidr0 = this.data.get(`cidr0[${rowIndex}]`);
            const result = getDifferenceAvaliableNew(mask, maskList, index, cidr, cidr0);
            this.data.set(`cidrTip[${rowIndex}]`, getAvaliableContent(result));
            this.data.set(`showCidrTip[${rowIndex}]`, true);
        } else {
            const result = getDifferenceAvaliableNew(mask, maskList, index, cidr);
            this.data.set(`cidrTip[${rowIndex}]`, getAvaliableContent(result));
            this.data.set(`showCidrTip[${rowIndex}]`, true);
        }
        const isExistErr = this.data.get(`cidrErr[${rowIndex}]`);
        if (!isExistErr) {
            this.cidrInput(rowIndex, index, {value: this.data.get(`cidr${index}[${rowIndex}]`)});
        }
    }

    cidrBlur(rowIndex) {
        this.data.set(`showCidrTip[${rowIndex}]`, false);
    }

    deleteRule(index) {
        this.data.set('needValidate', true);
        this.nextTick(() => {
            let tableLength = this.data.get('table.datasource').length;
            let subnetBatchCreateQuota = this.data.get('subnetBatchCreateQuota');
            let {createSubnetBatch} = checker.check(rules, tableLength, 'createSubnetBatch', {
                quotaBatch: subnetBatchCreateQuota,
                quotaTotal: this.data.get('overQuotaNum')
            });
            this.data.set('createSubnetBatch', createSubnetBatch);
        });
        let editItem = [
            'name',
            'vpcCidr',
            'zone',
            'subnetType',
            'enableIpv6',
            'broadcast',
            'description',
            'cidr0',
            'cidr1',
            'cidr2',
            'cidr3',
            'mask'
        ]; //eslint-disable-line
        this.data.splice('tableCidrList', [index, 1]);
        this.data.splice('table.datasource', [index, 1]);
        editItem.forEach(item => {
            this.data.splice(`${item}`, [index, 1]);
            this.data.splice(`${item}Err`, [index, 1]);
        });
        this.data.splice('cidrErr', [index, 1]);
        this.cidrCheck();
    }

    close() {
        location.hash = '#/vpc/subnet/list';
    }

    async onCreate() {
        try {
            await this.ref('tagPanel').validate(false);
        } catch (error) {
            return;
        }
        let tags = await this.ref('tagPanel').getTags();
        let resourceGroupId = this.data.get('formData.resourceGroupId');
        let vpcInfo = this.data.get('vpcInfo');
        let payload = {};
        let subnetList = [];
        let tableDatasource = this.data.get('table.datasource');
        let hasBroadcast = this.data
            .get('table.columns')
            .map(item => item.name)
            .indexOf('broadcast');
        for (let index = 0; index < tableDatasource.length; index++) {
            if (
                this.data.get(`cidrErr[${index}]`) ||
                this.data.get(`nameErr[${index}]`) ||
                this.data.get(`zoneErr[${index}]`)
            ) {
                return false;
            }
            let broadcast = -1;
            if (hasBroadcast > -1 && this.data.get(`subnetType[${index}]`) !== 2) {
                broadcast = this.data.get(`broadcast[${index}]`) ? 1 : 0;
            }
            let cidr0 = this.data.get(`cidr0[${index}]`);
            let cidr1 = this.data.get(`cidr1[${index}]`);
            let cidr2 = this.data.get(`cidr2[${index}]`);
            let cidr3 = this.data.get(`cidr3[${index}]`);
            let mask = this.data.get(`mask[${index}]`);
            let cidr = cidr0 + '.' + cidr1 + '.' + cidr2 + '.' + cidr3 + '/' + mask;
            let subnet = {
                name: this.data.get(`name[${index}]`),
                vpcCidr: this.data.get(`vpcCidr[${index}]`),
                az: this.data.get(`zone[${index}]`),
                cidr: cidr,
                subnetType: this.data.get(`subnetType[${index}]`),
                enableIpv6: this.data.get(`enableIpv6[${index}]`) || false,
                description: this.data.get(`description[${index}]`) || '',
                vpcId: vpcInfo.vpcId,
                tags: tags,
                resourceGroupId: resourceGroupId,
                broadcast: broadcast,
                needValidate: this.data.get('needValidate')
            };
            subnetList.push(subnet);
        }
        payload = {
            subnets: subnetList
        };
        this.data.set('disableSubmit', true);
        this.$http
            .createSubnets(payload)
            .then(res => {
                location.hash = '#/vpc/subnet/list';
            })
            .catch(err => {
                this.data.set('disableSubmit', false);
                if (err?.message?.field?.conflictWithRouteException) {
                    let conflictRoute = err.message.field.conflictWithRouteException
                        ?.split(',')
                        .slice(0, 10)
                        .join('，');
                    Dialog.confirm({
                        content: `该子网与所属VPC路由表目的网段有重叠${conflictRoute ? `（重叠网段：${conflictRoute}）` : ''}，建议使用其他网段或者修改对应路由表条目。请确认是否继续创建？`,
                        onOk: () => {
                            this.data.set('needValidate', false);
                            this.onCreate();
                        },
                        onCancel: () => this.data.set('needValidate', true)
                    });
                }
            });
    }

    dataInput(type, rowIndex, e) {
        if (type === 'name') {
            if (!e.value) {
                this.data.set(`nameErr[${rowIndex}]`, '名称必填');
            } else if (e.value === 'default') {
                this.data.set(`nameErr[${rowIndex}]`, '不能为默认名称');
            } else if (!/^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/.test(e.value)) {
                this.data.set(`nameErr[${rowIndex}]`, '名称不符合规则');
            } else {
                this.data.set(`nameErr[${rowIndex}]`, '');
            }
        }
        this.data.set(`${type}[${rowIndex}]`, e.value);
    }
    onRegionChange() {
        location.hash = '#/vpc/subnet/list';
    }
    // 子网广播region白名单
    async getBroadcastWhitelist() {
        const vpcList = this.data.get('vpcList');
        const vpcInfo = vpcList ? vpcList[0] : this.data.get('vpcInfo');
        const region = this.$context.getCurrentRegion();
        let result = await this.$http.broadcastWhitelist();
        const allowBroadcast = result.regions.indexOf(region.id) > -1;
        this.data.set('allowBroadcast', allowBroadcast);
        if (allowBroadcast) {
            this.checkBroadcast(vpcInfo.vpcId);
        }
    }
    checkBroadcast(vpcId) {
        this.$http.checkBroadcast(vpcId).then(res => {
            let hasBroadcast = this.data
                .get('table.columns')
                .map(item => item.name)
                .indexOf('broadcast');
            if (res.enableSubnetBroadcast && hasBroadcast === -1) {
                this.data.splice('table.columns', [
                    5,
                    0,
                    {
                        name: 'broadcast',
                        label: '子网广播',
                        width: 40
                    }
                ]);
            } else if (!res.enableSubnetBroadcast && hasBroadcast > -1) {
                this.data.splice('table.columns', [hasBroadcast, 1]);
            }
        });
    }
    broadcastChange(rowIndex, e) {
        this.data.set(`broadcast[${rowIndex}]`, e.value);
    }
    getIamQuery() {
        this.$http.getInterfaceIam({interfaceName: 'createSubnet'}).then(res => {
            if (!res.requestId && !res.masterAccount) {
                if (!res.interfacePermission) {
                    this.data.set('iamPass', {
                        disable: true,
                        message: '您没有创建子网权限，请联系主用户添加'
                    });
                } else {
                    this.data.set('iamPass', {
                        disable: false,
                        message: ''
                    });
                }
            } else {
                this.data.set('iamPass', {
                    disable: false,
                    message: ''
                });
            }
        });
    }
}
export default San2React(Processor.autowireUnCheckCmpt(SubnetCreate));
