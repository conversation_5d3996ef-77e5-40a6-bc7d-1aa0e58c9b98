/**
 * @file ip.js
 * <AUTHOR>
 */

import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import _ from 'lodash';
import RULE from '@/pages/sanPages/utils/rule';
import {ipResourceType} from '@/pages/sanPages/common/enum';
import {initIPResourcePieEcharts} from './echarts';
import {OutlinedRefresh, OutlinedDownload} from '@baidu/sui-icon';

const {asComponent, invokeSUI, invokeSUIBIZ, invokeAppComp, template} = decorators;
const echartsColorMapLess = [
    '#2468F2',
    '#A5E693',
    '#FAD000',
    '#F33E3E',
    '#A985FF',
    '#005C99',
    '#87D26D',
    '#FF8E52',
    '#E62E6B',
    '#95B8FF'
];
const tpl = html`
    <template>
        <div class="{{kclass}} ip-resource-class">
            <div class="{{kclass}}-flex-wrap ip-min-width">
                <h4>IP使用Top10</h4>
                <div class="ip-resource-wrapper">
                    <div s-ref="attack-echarts-ip" class="attack-graph"></div>
                </div>
            </div>
            <div class="{{kclass}}-flex-wrap">
                <h4>IP地址管理</h4>
                <div class="{{kclass}}-search-wrap">
                    <s-button skin="primary" on-click="handleExport" class="margin-right"
                        ><outlined-download class="button-icon mr4" is-button="{{false}}" />导出</s-button
                    >
                    <s-search-box
                        datasource="{{searchbox.datasource}}"
                        value="{=searchbox.keyword=}"
                        on-search="onSearch"
                        placeholder="{{searchbox.placeholder}}"
                        keyword-type="{=searchbox.keywordType=}"
                    />
                    <s-button
                        on-click="refresh"
                        class="s-icon-button left_class"
                        track-id="ti_subnet_ip_refresh"
                        track-name="刷新"
                        ><outlined-refresh class="icon-class"
                    /></s-button>
                </div>
                <p s-if="errorTip" class="tip-error-class">请输入精确IP地址</p>
                <s-table
                    columns="{{table.columns}}"
                    loading="{{table.loading}}"
                    error="{{table.error}}"
                    datasource="{{table.datasource}}"
                    on-filter="onFilter"
                    track-id="ti_subnet_ip_table"
                    track-name="列表操作"
                >
                    <div slot="empty">
                        <s-empty>
                            <div slot="action"></div>
                        </s-empty>
                    </div>
                    <div slot="error">
                        {{table.error ? table.error : '啊呀，出错了？'}}
                        <a href="javascript:;" on-click="refresh">{{'重新加载'}}</a>
                    </div>
                    <div slot="c-resourceType">{{row.resourceType | getResourceType}}</div>
                </s-table>
                <div class="{{kclass}}-pager-wrap">
                    <s-pagination
                        s-if="{{pager.total}}"
                        layout="{{'total, pageSize, pager, go'}}"
                        pageSize="{{pager.pageSize}}"
                        total="{{pager.total}}"
                        page="{{pager.page}}"
                        on-pagerChange="onPagerChange"
                        on-pagerSizeChange="onPagerSizeChange"
                    />
                </div>
            </div>
        </div>
    </template>
`;

@template(tpl)
@asComponent('@vpc-subnet-ip')
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class Detail extends Component {
    static components = {
        'outlined-refresh': OutlinedRefresh,
        'outlined-download': OutlinedDownload
    };
    initData() {
        return {
            kclass: 'subnet-ip',
            table: {
                columns: [
                    {name: 'ip', label: 'IP地址'},
                    {name: 'resourceType', label: '用途'}
                ],
                loading: true,
                datasource: []
            },
            searchbox: {
                datasource: [
                    {value: 'ip', text: 'IP地址'}
                    // {value: 'resourceType', text: '用途'}
                ],
                keywordType: ['ip'],
                keyword: '',
                placeholder: '请输入精确IP地址'
            },
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            echartsData: [],
            colorArr: echartsColorMapLess,
            resourceKeyword: '',
            subnetInfo: {}
        };
    }

    static filters = {
        getResourceType(value) {
            // ipResourceType只维护与v3中不一致资源
            if (ipResourceType.getTextFromValue(value)) {
                return ipResourceType.getTextFromValue(value);
            }
            let config = this.$context.SERVICE_TYPE[value.toUpperCase()];
            return config?.name || '';
        },
        getText(item) {
            return item.value + '个';
        },
        getColor(index) {
            return echartsColorMapLess[index];
        }
    };

    async attached() {
        this.watch('searchbox.keyword', value => {
            if (value && !RULE.IP.test(value)) {
                this.data.set('errorTip', true);
            } else {
                this.data.set('errorTip', false);
            }
        });
        await this.loadPage();
        await this.subnetDetail();
        this.initChart();
        await this.loadIPResource();
    }

    refresh() {
        this.loadPage();
    }

    onSearch() {
        let lastStr = this.data.get('searchbox.keyword');
        if (lastStr && !RULE.IP.test(lastStr)) {
            return;
        }
        this.loadPage();
    }

    loadPage() {
        this.data.set('table.loading', true);
        const payload = this.getPayload();
        return this.$http
            .getSubnetIpList(payload, {'x-silent-codes': ['Subnet.SubnetNotFoundException']})
            .then(data => {
                this.data.set('table.datasource', data.result);
                this.data.set('pager.total', data.totalCount);
                this.data.set('table.loading', false);
            });
    }

    getPayload() {
        const {keyword} = this.data.get('searchbox');
        const {page, pageSize} = this.data.get('pager');
        const resourceType = this.data.get('resourceKeyword');
        const subnetUuid = this.data.get('context').subnetId;
        const params = {
            dcphyId: this.data.get('instanceId'),
            ip: keyword,
            subnetUuid,
            resourceType,
            pageNo: page,
            pageSize
        };
        return params;
    }

    onFilter(e) {
        this.data.set('resourceKeyword', e.resourceType);
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', 10);
        this.loadPage();
    }

    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }

    onPagerSizeChange(e) {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', e.value.pageSize);
        this.loadPage();
    }

    subnetDetail() {
        const subnetId = this.data.get('context').subnetId;
        return this.$http
            .getSubnetResourceDetail(subnetId, {'x-silent-codes': ['Subnet.SubnetNotFoundException']})
            .then(data => {
                this.data.set('subnetInfo', data);
            });
    }

    initChart() {
        let usedIPcount = this.data.get('pager.total');
        this.data.set('echartsData', []);
        this.initEcharts(usedIPcount);
    }

    loadIPResource() {
        // 已用IP数
        let usedIPcount = this.data.get('pager.total');
        const subnetId = this.data.get('context').subnetId;
        const subnetInfo = this.data.get('subnetInfo');
        // 总IP数
        let totalIpcount = subnetInfo.availableIp + usedIPcount;
        return this.$http.getSubnetIPResource(subnetId, {'x-silent-codes': ['SubnetIdInvalid']}).then(res => {
            let resourceList = [];
            let ipResource = [];
            Object.getOwnPropertyNames(res.ipResourceStatistic)
                .filter(key => key !== 'bae')
                .forEach(key => {
                    // ip使用资源中去掉bae的显示（使用baepro）
                    let chName =
                        ipResourceType.getTextFromValue(key) ||
                        this.$context.SERVICE_TYPE[key.toUpperCase()]?.name ||
                        '';
                    ipResource.push({
                        value: res.ipResourceStatistic[key], // 数量
                        text: key, // 资源英文
                        name: chName.length > 12 ? chName.substring(0, 12) + '...' : chName, // 资源中文
                        fullName: chName,
                        percentValue: Math.round((res.ipResourceStatistic[key] / usedIPcount) * 10000) / 100.0 // 占比
                    });
                    res.ipResourceStatistic[key] &&
                        resourceList.push({
                            text:
                                ipResourceType.getTextFromValue(key) ||
                                this.$context.SERVICE_TYPE[key.toUpperCase()]?.name,
                            value: key
                        });
                });
            let top10List = ipResource.sort((a, b) => {
                return b.value - a.value;
            });
            this.data.set('echartsData', top10List.slice(0, 10));
            this.data.splice('table.columns', [
                1,
                1,
                {
                    name: 'resourceType',
                    label: '用途',
                    filter: {
                        options: [
                            {
                                text: '全部',
                                value: ''
                            },
                            ...resourceList
                        ],
                        value: ''
                    }
                }
            ]);
            this.initEcharts(usedIPcount, totalIpcount);
        });
    }

    initEcharts(usedIp, totalIpcount) {
        var charIPtDom = this.ref('attack-echarts-ip');
        var echartsData = this.data.get('echartsData');
        initIPResourcePieEcharts(charIPtDom, echartsData, usedIp, totalIpcount);
    }
    handleExport() {
        const subnetId = this.data.get('context').subnetId;
        const {page, pageSize} = this.data.get('pager');
        const resourceType = this.data.get('resourceKeyword');
        const {keyword: ip} = this.data.get('searchbox');
        window.open(
            `/api/network/v1/subnet/ipUsedInfo/download?subnetUuid=${subnetId}&ip=${ip}&resourceType=${resourceType}&pageNo=${page}&pageSize=${pageSize}`
        );
    }
}
export default San2React(Processor.autowireUnCheckCmpt(Detail));
