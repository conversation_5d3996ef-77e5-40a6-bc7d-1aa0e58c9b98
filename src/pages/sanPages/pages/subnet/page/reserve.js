import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {OutlinedRefresh, OutlinedPlus} from '@baidu/sui-icon';
import {San2React} from '@baidu/bce-react-toolkit';
import {Notification} from '@baidu/sui';
import {checker} from '@baiducloud/bce-opt-checker';

import Confirm from '@/pages/sanPages/components/confirm';
import {ContextService} from '@/pages/sanPages/common';
import {checkIsInSubnet} from '@/pages/sanPages/utils/common';
import RULE from '@/pages/sanPages/utils/rule';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import {IpVersion} from '@/pages/sanPages/common/enum';

const rules = {
    createReserve: [
        {
            custom(data, options = {}) {
                if (options.quota <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: '预览网段配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `预览网段配额不足，如需增加配额请提交
                            <a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">工单</a>`
                        };
                    }
                }
            }
        }
    ]
};

const formateRules = self => ({
    ipCidr: [
        {required: true, message: '网段必填'},
        {
            validator(rule, value, callback) {
                let cidr = self.data.get('instance.cidr');
                let ipv6cidr = self.data.get('instance.ipv6Cidr');
                let ipVersion = self.data.get('formData.ipVersion');
                if (!value) {
                    return callback('不能为空');
                } else {
                    if (ipVersion === 4) {
                        if (!RULE.SEG.test(value)) {
                            return callback('网段格式不合法');
                        } else if (!checkIsInSubnet(value, cidr)) {
                            return callback('预留网段不在子网网段内');
                        }
                    } else {
                        if (!RULE.IPV6_SEG.test(value)) {
                            return callback('网段格式不合法');
                        } else if (!checkIsInSubnet(value, ipv6cidr)) {
                            return callback('预留网段不在子网网段内');
                        }
                    }
                }
                return callback();
            }
        }
    ],
    description: [
        {
            validator(rule, value, callback) {
                if (value !== undefined && value.length > 200) {
                    return callback('最大输入200个字符');
                }
                return callback();
            }
        }
    ]
});

const {asComponent, invokeSUI, invokeSUIBIZ, invokeAppComp, template} = decorators;
const tpl = html`
    <template>
        <div class="{{kclass}}">
            <h4>预留网段</h4>
            <s-tooltip trigger="{{createReserve.disable || createReserve.disable ? 'hover' : ''}}" placement="top">
                <!--bca-disable-next-line-->
                <div slot="content">{{createReserve.message | raw}}</div>
                <s-button disabled="{{createReserve.disable}}" skin="primary" on-click="onCreate"
                    ><outlined-plus />{{'新建预留网段'}}
                </s-button>
            </s-tooltip>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                on-filter="onFilter"
                on-sort="onSort"
                track-id="ti_subnet_ip_table"
                track-name="列表操作"
            >
                <div slot="empty">
                    <s-empty>
                        <div slot="action"></div>
                    </s-empty>
                </div>
                <div slot="c-ipVersion">{{row.ipVersion | ipText}}</div>
                <div slot="error">
                    {{table.error ? table.error : '啊呀，出错了？'}}
                    <a href="javascript:;" on-click="refresh">{{'重新加载'}}</a>
                </div>
                <div slot="c-opt" class="operation">
                    <s-button on-click="delete(row)" skin="stringfy"> {{'删除'}} </s-button>
                </div>
            </s-table>
            <div class="{{kclass}}-pager-wrap">
                <s-pagination
                    s-if="{{pager.total}}"
                    layout="{{'total, pageSize, pager, go'}}"
                    pageSize="{{pager.pageSize}}"
                    total="{{pager.total}}"
                    page="{{pager.page}}"
                    on-pagerChange="onPagerChange"
                    on-pagerSizeChange="onPagerSizeChange"
                />
            </div>
            <s-dialog title="新建预留网段" on-close="close" open="{=dialogDisplay=}">
                <div class="gateway-control-form-container">
                    <s-form s-ref="rules-ref" data="{=formData=}" rules="{{checkRules}}" label-align="left">
                        <s-form-item label="网段类型：" prop="ipVersion">
                            <s-radio-radio-group
                                datasource="{{ipVersionDatasource}}"
                                radioType="button"
                                value="{=formData.ipVersion=}"
                                on-change="ipVersionChange($event)"
                            >
                            </s-radio-radio-group>
                        </s-form-item>
                        <s-form-item label="网段：" prop="ipCidr">
                            <s-input value="{=formData.ipCidr=}" placeholder="请输入预留网段" />
                        </s-form-item>
                        <s-form-item label="描述：" prop="description">
                            <s-input-text-area
                                placeholder="请输入描述"
                                value="{=formData.description=}"
                                width="400"
                                height="80"
                                maxLength="{{200}}"
                            />
                        </s-form-item>
                    </s-form>
                </div>
                <div slot="footer">
                    <s-button on-click="close">取消</s-button>
                    <s-button skin="primary" disabled="{{confirmDisabled}}" on-click="dialogConfirm">确定</s-button>
                </div>
            </s-dialog>
        </div>
    </template>
`;

@template(tpl)
@asComponent('@vpc-subnet-reserve')
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
class Detail extends Component {
    static components = {
        'outlined-refresh': OutlinedRefresh,
        'outlined-plus': OutlinedPlus
    };
    initData() {
        return {
            kclass: 'subnet-ip',
            table: {
                columns: [
                    {name: 'ipReserveId', label: '实例ID'},
                    {
                        name: 'ipVersion',
                        label: '类型',
                        filter: {
                            options: [
                                {
                                    text: '全部',
                                    value: ''
                                },
                                {
                                    text: 'IPv4',
                                    value: 4
                                },
                                {
                                    text: 'IPv6',
                                    value: 6
                                }
                            ],
                            value: ''
                        }
                    },
                    {name: 'ipCidr', label: '网段', sortable: true},
                    {name: 'description', label: '描述'},
                    {name: 'opt', label: '操作'}
                ],
                loading: false,
                datasource: []
            },
            order: {},
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            ipVersionDatasource: [
                {
                    text: 'IPv4',
                    value: 4
                },
                {
                    text: 'IPv6',
                    value: 6
                }
            ],
            createReserve: {},
            dialogDisplay: false,
            formData: {
                ipVersion: 4,
                ipCidr: '',
                description: ''
            },
            instance: {},
            checkRules: formateRules(this)
        };
    }

    static filters = {
        ipText(ip) {
            return ip ? IpVersion.getTextFromValue(ip) : '-';
        }
    };

    attached() {
        this.loadPage();
    }

    refresh() {
        this.loadPage();
    }

    getDetail() {
        const subnetId = this.data.get('context').subnetId;
        return this.$http
            .getSubnetResourceDetail(subnetId, {'x-silent-codes': ['Subnet.SubnetNotFoundException']})
            .then(data => {
                this.data.set('instance', data);
                if (!data.ipv6Cidr) {
                    this.data.set('ipVersionDatasource[1].disabled', true);
                }
            });
    }

    ipVersionChange() {
        this.data.set('formData.ipCidr', '');
    }

    onFilter(e) {
        let name = e.field.name;
        let value = e[name] ? e[name] : '';
        this.data.set('filters.' + name, value);
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    // 检查配额
    async reserveQuota() {
        const data = await this.$http.reserveQuota(this.data.get('context').subnetId);
        let {createReserve} = checker.check(rules, data, 'createReserve', {quota: data.free});
        this.data.set('createReserve', createReserve);
    }

    onSort(e) {
        this.data.set('order', e.value);
        this.loadPage();
    }

    loadPage() {
        this.data.set('table.loading', true);
        const payload = this.getPayload();
        const subnetId = this.data.get('context').subnetId;
        this.getDetail();
        this.$http.getSubnetReserveList(subnetId, payload).then(data => {
            this.data.set('table.datasource', data.result);
            this.data.set('pager.total', data.totalCount);
            this.data.set('table.loading', false);
        });
        this.reserveQuota();
    }

    onCreate() {
        const subnetId = this.data.get('context').subnetId;
        this.$http
            .getSubnetReserveList(subnetId, {
                pageNo: 1,
                pageSize: 1000
            })
            .then(data => {
                let list = data.result.map(item => ({
                    ipVersion: item.ipVersion,
                    ipCidr: item.ipCidr
                }));
                this.data.set('allCidrList', list);
                this.data.set('dialogDisplay', true);
            });
    }

    async dialogConfirm() {
        let formData = this.data.get('formData');
        let param = {
            subnetUuid: this.data.get('context').subnetId,
            ...formData
        };
        await this.ref('rules-ref').validateFields();
        this.data.set('confirmDisabled', true);

        this.$http
            .addReserve(param)
            .then(res => {
                this.close();
                this.data.set('confirmDisabled', false);
                this.loadPage();
            })
            .catch(() => {
                this.data.set('confirmDisabled', false);
            });
    }

    close() {
        this.data.set('dialogDisplay', false);
        this.data.set('formData', {
            ipVersion: 4,
            ipCidr: '',
            description: ''
        });
    }

    delete(row) {
        let confirm = new Confirm({
            data: {
                open: true,
                content: '确认删除预留网段？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http.deleteReserve(row.ipReserveId).then(() => {
                this.loadPage();
                Notification.success('删除成功');
            });
        });
    }

    getPayload() {
        const {pager, order, filters} = this.data.get('');
        const params = {
            pageNo: pager.page,
            pageSize: pager.pageSize
        };
        if (filters?.ipVersion) {
            params.ipVersion = filters.ipVersion;
        }
        if (order?.order) {
            params.orderBy = order.orderBy;
            params.order = order.order;
        }
        return params;
    }

    onPagerChange(e) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }

    onPagerSizeChange(e) {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', e.value.pageSize);
        this.loadPage();
    }
}
export default San2React(Processor.autowireUnCheckCmpt(Detail));
