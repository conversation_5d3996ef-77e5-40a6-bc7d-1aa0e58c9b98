@bce-error-color: #eb5252;
.subnet-list {
    height: 100%;
    overflow-y: auto;
    background: #f7f7f9 !important;
    // width: 0;
    flex: 1;
    .s-biz-page-header {
        margin: 0px !important;
        background-color: #fff;
        height: auto !important;
        border-bottom: none !important;
    }
    .s-biz-page-content {
        background-color: #fff;
        border-radius: 6px;
        margin: 16px !important;
        padding: 24px;
        .s-biz-page-toolbar {
            margin: 0px;
            .list-page-tb-left-toolbar {
                display: inline-flex;
            }
            .toolbar_right {
                display: inline-flex;
            }
        }
        .s-biz-page-body {
            margin-top: 16px;
            .s-table {
                .s-table-body {
                    max-height: calc(~'100vh - 332px');
                }
            }
        }
        .s-biz-page-footer {
            padding-bottom: 0;
            margin-top: 16px;
        }
    }
    .vpc-subnet-header {
        background-color: #f7f7f9;
        .subnet-header-widget {
            display: flex;
            align-items: center;
            padding-left: 16px;
            justify-content: space-between;
            background-color: #ffffff;
            .widget-left {
                .title {
                    display: inline-block;
                    margin: 0;
                    color: #151b26;
                    margin-right: 12px;
                    height: 47px;
                    line-height: 47px;
                    font-weight: 500;
                    font-size: 16px;
                }
            }
            .widget-right {
                display: flex;
                align-items: center;
                .help-file {
                    margin-right: 16px;
                    font-size: 12px;
                    font-weight: 400;
                    .s-icon {
                        position: relative;
                        top: -1px;
                        right: 4px;
                        font-size: 14px;
                        margin-right: 0;
                        color: #2468f2;
                    }
                }
            }
        }
        .network {
            font-size: 12px;
            color: #999;
            vertical-align: middle;
        }
    }
    .quota-tip {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 5px;
        color: #f38900;
        background: #fcf7f1;
    }
    .s-cascader {
        font-size: 0;
    }
    .s-cascader-value {
        vertical-align: middle;
        border: none;
        font-size: 12px;
        padding-top: 0;
        padding-bottom: 0;
        line-height: 30px;
    }
    .s-table {
        .s-table-row:hover {
            .name-icon {
                display: inline;
            }
        }
        .name-icon {
            display: none;
            font-size: 12px;
            fill: #2468f2;
        }
        .operation {
            .s-button {
                padding: 0;
                margin-right: 12px;
            }
        }
        .subnet-id-widget {
            white-space: nowrap;
        }
    }
    .text-hidden {
        display: inline-block;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        max-width: 100%;
        vertical-align: middle;
    }
    .list-link {
        display: inline-block;
    }
    .tip-header-wrap {
        min-width: 300px;
        display: flex;
        flex: 1;
        height: 30px;
        margin-left: 100px;
        align-items: center;
        .s-notice-carousel {
            flex: 1;
        }
        .s-icon {
            color: #2468f2;
        }
        .tip-text {
            color: #2468f2;
            padding: 5px;
        }
    }
    .pointer-link {
        cursor: pointer;
    }
    .new-tag {
        display: inline-block;
        background-color: #f72e32;
        border-radius: 16px;
        line-height: 16px;
        min-width: 40px;
        text-align: center;
        font-size: 12px;
        color: #fff;
    }
    .new-instance-tag {
        background-color: #f33e3e;
        border-radius: 2px;
        line-height: 17px;
    }
}

.drawer-open-class {
    display: none !important;
}
