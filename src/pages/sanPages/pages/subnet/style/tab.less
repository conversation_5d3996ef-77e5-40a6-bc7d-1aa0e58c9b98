.s-detail-page-content {
    margin: 0;
    .app-tab-page {
        padding: 16px;
        border-radius: 6px;
        background: #f7f7f7;
        .bui-tab {
            border-radius: 6px;
            border: none;
            height: 100%;
            .bui-tab-content {
                padding: 0;
            }
        }
    }
}
.instance-info {
    display: flex;
    align-items: center;
    .s-button {
        .iconfont {
            font-size: 14px;
            color: #84868c;
        }
    }
    .instance-name {
        padding-left: 16px;
        font-size: 16px !important;
        font-weight: 500;
        color: #151b26;
    }
}

.s-tabpane-wrapper {
    flex: 1;
}

.select-tip {
    color: #999;
    font-size: 12px;
    display: inline-block;
    margin: 0 10px;
    vertical-align: middle;
}
.s-tabs-vertical {
    .s-tabnav-nav {
        display: block;
    }
    .s-tabnav-nav-item {
        text-align: left;
        margin: 0;
        padding: 15px;
    }
}

.subnet-detail,
.subnet-ip,
.subnet-reserveportpools {
    width: 100%;
    padding: 24px;
    height: 100%;
    overflow-y: scroll;
    h4 {
        display: block;
        font-size: 16px;
        color: #151b26;
        line-height: 24px;
        font-weight: 500;
        margin-bottom: 16px;
    }
}

.subnet-detail {
    .subnet-detail-item {
        margin-bottom: 40px;
        .relative_class {
            position: relative;
        }
        .edit_popover_class {
            position: relative;
            top: 3px;
        }
        .copy_icon {
            position: relative;
            top: 3px;
        }
    }
    ul {
        overflow: auto;
    }
    .cell-title {
        display: inline-block;
        vertical-align: top;
        color: #5e626a;
        margin-right: 16px;
        width: 62px;
    }
    .cell-content {
        display: inline-block;
        color: #151a26;
        max-width: 80%;
        word-break: break-all;
        position: relative;
        vertical-align: top;
    }
    .center_class {
        display: flex;
        align-items: center;
    }
    .copy_icon {
        margin-left: 5px;
    }
    li.content-item {
        width: 33%;
        display: inline-block;
        margin-bottom: 16px;
        vertical-align: top;
    }
    .s-icon {
        font-size: 12px;
        color: #2468f2;
        display: list-item;
    }
    .main {
        h5 {
            font-size: 14px;
            font-weight: bold;
            margin-top: 25px;
        }
        .list-wrap {
            display: flex;
            flex-wrap: wrap;
            > a {
                display: block;
                width: calc(~'20% - 20px');
                border-radius: 6px;
                background: #f5f5f5;
                margin: 20px 20px 0 0;
                padding: 10px 20px;
                box-sizing: border-box;
                display: flex;
                justify-content: space-between;
                span:nth-child(1) {
                    color: #000;
                }
            }
            .forbid-redirect {
                cursor: default;
            }
        }
    }
}
.subnet-ip-pager-wrap,
.subnet-ip-search-wrap,
.subnet-reserveportpools-search-wrap,
.subnet-reserveportpools-pager-wrap {
    display: flex;
    justify-content: flex-end;
    margin: 20px 0 5px 0;
    .s-search-box {
        height: 34px;
    }
    .s-cascader-value {
        border-bottom-right-radius: 0%;
    }
    .s-input-suffix {
        padding-top: 4px;
    }
    .s-button {
        margin-left: 8px;
        height: 32px;
    }
    .margin-right {
        margin-right: 8px;
    }
}
.subnet-ip,
.subnet-reserveportpools {
    .s-table {
        margin-top: 16px;
    }
    .s-table .s-table-body {
        max-height: calc(~'100vh - 376px');
    }
    .s-search-box {
        .s-cascader {
            height: 32px;
        }
    }
    .s-pagination-pagersize-wrapper {
        .s-input-suffix {
            padding-top: unset;
        }
    }
}
.subnet-ip-pager-wrap {
    margin-top: 16px;
}
.subnet-reserveportpools-toolbar {
    display: flex;
    justify-content: space-between;
    .subnet-reserveportpools-search-wrap {
        margin: unset;
    }
}
.subnet-ip {
    .tip-error-class {
        position: relative;
        float: right;
        padding-bottom: 12px;
        margin-right: 178px;
        color: #e8684a;
    }
}
.change-btn {
    position: relative;
    top: -2px;
}
.ip-resource-class {
    display: flex;
    .ip-min-width {
        min-width: 526px;
    }
    .subnet-ip-flex-wrap {
        flex: 1;
    }
    .attack-graph {
        flex: 1;
        margin-right: 16px;
    }
    .ip-resource-wrapper {
        display: flex;
        position: relative;
        top: 15%;
    }
    .ip-resource-list {
        flex: 1;
        .ip-list-wrap {
            display: flex;
            height: 20px;
            line-height: 20px;
            margin-bottom: 8px;
        }
    }
    .none-resource-class {
        font-size: 14px;
        color: #b8babf;
        line-height: 22px;
        font-weight: 400;
        text-align: center;
        height: 200px;
        line-height: 200px;
    }
    .label-class {
        &::before {
            font-family: iconfont;
            content: '\e632';
            font-size: inherit;
            position: relative;
            margin-right: 8px;
        }
    }
    .label-content {
        font-size: 10px;
        color: #303540;
        line-height: 20px;
        font-weight: 400;
        margin-right: 26px;
        display: inline-block;
        width: 140px;
    }
    .text-class {
        font-size: 12px;
        color: #151b26;
        font-weight: 400;
        margin-right: 14px;
        display: inline-block;
        width: 30px;
    }
    .percent-class {
        font-size: 10px;
        color: #84868c;
        font-weight: 400;
        display: inline-block;
        width: 22px;
    }
}
.region-list-wrap {
    .icon-sort-normal {
        display: inline-block;
        color: #666;
        transform: rotate(90deg);
        vertical-align: middle;
        margin: 0 10px;
    }
}

.attribute-content {
    p {
        > span:nth-child(1) {
            color: #999;
        }
    }
}

.app-detail-page-content {
    margin: 20px 0;
    .bui-tab {
        height: 100%;
    }
}

.subnet-reserveportpools-create,
.subnet-reserveportpools-address {
    .s-form-item-label {
        width: 83px;
        text-align: left;
    }
    .s-form-item-help {
        width: 160px;
    }
    .s-form-item-error {
        width: 160px;
    }
}

.locale-en {
    .siderbar-tab .skin-accordion-tab > .bui-tab-header > .bui-tab-nav-wrapper > .bui-tab-nav-bar .bui-tab-nav-item {
        width: 180px;
    }
}

.change-az-dialog {
    .item-context {
        margin-top: 0;
        .s-form-item-control-wrapper {
            height: 30px;
            line-height: 30px;
        }
    }
    .az-select {
        margin-left: 24px;
    }
    .s-form-item-error {
        margin-left: 24px;
    }
}
