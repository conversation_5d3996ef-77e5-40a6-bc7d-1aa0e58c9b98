import u from 'lodash';
import {ContextService} from '@/pages/sanPages/common';
const AllRegion = ContextService.getEnum('AllRegion');
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';

const currentMoudle = 'SUBNET';
export default {
    createSubnet: [
        {
            required: false
        },
        {
            custom(data, options = {}) {
                let vpcInfo = window.$storage.get('vpcInfo');
                let vpcList = window.$storage.get('vpcInfoList');
                vpcInfo = vpcList?.length && vpcList?.length !== 0 ? vpcList[0] : vpcInfo;
                if (!vpcInfo?.vpcId) {
                    return {
                        disable: true,
                        message: '当前没有可用私有网络，请先进行创建'
                    };
                }
                if (options.quota <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: '子网配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `子网配额不足，<a href="${
                                '/quota_center/#/quota/apply/create?serviceType='
                            }${currentMoudle}&region=${
                                window?.$context?.getCurrentRegionId()
                            }&cloudCenterQuotaName=subnetQuota" target="_blank">去申请配额</a>`
                        };
                    }
                }
            }
        }
    ],
    deleteSubnet: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            custom(data) {
                if (u.some(data, item => item.bbcNum || item.bccNum || item.dccNum)) {
                    return {
                        disable: true,
                        message: '当前子网不支持删除，请先删除子网内的计算设备、存储设备、网络设备或者路由相关设置。'
                    };
                }
            }
        }
    ],
    editTag: [
        {
            required: true,
            message: '请先选择实例对象'
        }
    ],
    changeResource: [
        {
            required: true,
            message: '请先选择实例对象'
        }
    ],
    createSubnetBatch: [
        {
            custom(data, options = {}) {
                if (data >= options.quotaTotal) {
                    return {
                        disable: true,
                        message: `子网配额不足，如需增加配额请提交
                        <a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">工单</a>`
                    };
                }
                if (data >= options.quotaBatch) {
                    return {
                        disable: true,
                        message: `最多批量创建${options.quotaBatch}个子网`
                    };
                }
            }
        }
    ],
    subnetSinDisable: [
        {
            required: false
        },
        {
            custom(data) {
                if (window.$context.getCurrentRegionId() === AllRegion.SIN) {
                    return {
                        disable: true,
                        message: '新加坡地域资源售罄，请您切换到其他地域创建'
                    };
                }
            }
        }
    ]
};
