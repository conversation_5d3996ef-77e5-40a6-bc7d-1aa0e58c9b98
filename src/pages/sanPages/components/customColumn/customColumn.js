/*
 * @Description:
 * @Author: <EMAIL>
 * @Date: 2022-03-23 21:01:03
 */
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {OutlinedSetting} from '@baidu/sui-icon';
import './customColumn.less';

const {asComponent, invokeSUIBIZ, template} = decorators;
const tpl = html`
    <template>
        <s-table-column-toggle
            class="custom-column-qos s-button icon-column custom-column"
            datasource="{{columnList}}"
            value="{=columnValue=}"
            on-change="onCustomColumns"
        ></s-table-column-toggle>
    </template>
`;
@template(tpl)
@invokeSUIBIZ
@asComponent('@custom-column')
class CustomColumn extends Component {
    static components = {
        'outlined-setting': OutlinedSetting
    };
    initData() {
        return {
            columnValue: []
        };
    }

    inited() {
        this.getColumnList();
        this.watch('initValue', initValue => {
            this.data.set('columnValue', initValue);
            this.fire('init', initValue);
        });
    }

    getColumnList() {
        let type = this.data.get('type');
        let listStorage = window.$storage.get(`${type}.columnList`);
        let columnList = [];
        if (listStorage) {
            columnList = listStorage;
        } else {
            columnList = this.data.get('initValue');
        }
        this.data.set('columnValue', columnList);
        this.fire('init', columnList);
    }

    onCustomColumns(e) {
        window.$storage.set(`${this.data.get('type')}.columnList`, e.value);
        this.fire('change', e.value);
    }
}
export default Processor.autowireUnCheckCmpt(CustomColumn);
