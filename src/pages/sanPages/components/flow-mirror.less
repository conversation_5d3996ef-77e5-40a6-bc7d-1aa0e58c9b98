.mirror-header {
    height: 100%;
    .app-tab-page {
        padding-left: 0;
    }
    h2 {
        border: none;
        padding-top: 12px;
        padding-bottom: 4px;
        height: 40px;
        line-height: 24px;
        color: #151b26;
    }
    .vpc-mirror-header {
        display: flex;
        align-items: center;
        background-color: #ffffff;
        justify-content: space-between;
        position: absolute;
        top: -40px;
        width: 100%;
        .header-left {
            display: flex;
            align-items: center;
            .title {
                display: inline-block;
                margin: 0;
                color: #151b26;
                margin-right: 12px;
                height: 47px;
                line-height: 47px;
                font-weight: 500;
                font-size: 16px;
                margin-left: 16px;
            }
        }
        .header-right {
            display: flex;
            align-items: center;
            .help-file {
                margin-right: 16px;
                font-size: 12px;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                color: #151b26;
                line-height: 20px;
                &:hover {
                    color: #2468f2;
                }
                .s-icon {
                    position: relative;
                    top: -1px;
                    right: 4px;
                    margin-right: 0;
                    color: #2468f2;
                    font-size: 14px;
                }
            }
            .function-introduce {
                color: #2468f2;
            }
            .help-file-docs {
                color: #2468f2;
            }
        }
    }
}
