/*
 * @Description:
 * @Author: <EMAIL>
 * @Date: 2021-09-18 11:28:18
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';

const {asComponent, invokeSUI, template} = decorators;
const tpl = html` <template>
    <s-select width="{{240}}" value="{=vpcId=}" filterable on-change="vpcChange($event)">
        <s-select-option s-for="item in vpcList" value="{{item.value}}" label="{{item.text}}">
            <s-tooltip>
                <div slot="content">{{item.text}}</div>
                <div>{{item.text}}</div>
            </s-tooltip>
        </s-select-option>
    </s-select>
</template>`;

@template(tpl)
@invokeSUI
@asComponent('@vpc-select')
export default class EditPopover extends Component {
    initData() {
        return {
            vpcId: '',
            rule: {},
            btnDisabled: true
        };
    }
    inited() {
        this.getVpclist();
    }

    getVpclist() {
        this.$http.vpcList().then(data => {
            let vpcs = u.map(data, item => ({
                text: `${item.name}（${item.cidr}）${item.ipv6Cidr ? '(' + item.ipv6Cidr + ')' : ''}`,
                value: item.vpcId,
                vpcInfo: item
            }));
            if (!this.data.get('notSupportAllVpc')) {
                vpcs.unshift({
                    text: '所在网络：全部私有网络',
                    value: ''
                });
            }
            this.data.set('vpcList', vpcs);
            this.data.set('vpcInfoList', data);

            window.$storage.set('vpcInfoList', data);
            window.$storage.set('vpcList', vpcs);
            if (window.$storage.get('vpcId') && vpcs.find(item => item.value === window.$storage.get('vpcId'))) {
                this.data.set('vpcId', window.$storage.get('vpcId'));
                this.data.get('vpcInfoList').forEach(item => {
                    if (window.$storage.get('vpcId') === item.vpcId) {
                        window.$storage.set('vpcInfo', item);
                    }
                });
            } else {
                let vpcId = vpcs[0].value;
                this.data.set('vpcId', vpcId);
                window.$storage.set('vpcId', vpcId);
                window.$storage.set('vpcInfo', vpcId ? data[0] : {});
            }
            this.fire('int');
        });
    }
    vpcChange(e) {
        window.$storage.set('vpcId', e.value);
        if (!e.value) {
            window.$storage.set('vpcInfo', {});
        } else {
            this.data.get('vpcInfoList').forEach(item => {
                if (e.value === item.vpcId) {
                    window.$storage.set('vpcInfo', item);
                }
            });
        }
        this.fire('change');
    }
}
