/*
 * @description: 编辑标签
 * @author: <EMAIL>
 */

import u from 'lodash';
import {Component, DataTypes} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import {checker} from '@baiducloud/bce-opt-checker';
import {TagEditDialog} from '@baidu/bce-tag-sdk-san';

import {DocService} from '@/pages/sanPages/common';
import './editTag.less';

const serviceTypeKey = {
    VPC: 'VPC',
    SUBNET: 'SUBNET',
    ENI: 'ENIC',
    SNIC: 'SNIC',
    SECURITY_GROUP: 'SECURITY_GROUP',
    ENTERPRISE_SECURITY_GROUP: 'ENTERPRISE_SECURITY_GROUP',
    NAT: 'NAT',
    PRIVATE_NAT: 'NAT',
    DEDICATEDCONN: 'DEDICATEDCONN',
    VPN: 'VPN',
    PEERCONN: 'PEERCONN',
    ET: 'ET',
    ET_CHANNEL: 'ET_CHANNEL'
};
const idKey = {
    VPC: 'vpcId',
    SUBNET: 'subnetId',
    ENI: 'eniUuid',
    SNIC: 'shortId',
    SECURITY_GROUP: 'id',
    ENTERPRISE_SECURITY_GROUP: 'esgUuid',
    NAT: 'id',
    PRIVATE_NAT: 'natId',
    DEDICATEDCONN: 'id',
    VPN: 'vpnId',
    PEERCONN: 'peerConnId',
    ET: 'id',
    ET_CHANNEL: 'id'
};
const resourceIdKey = {
    VPC: 'shortId',
    SUBNET: 'shortId',
    ENI: 'eniId',
    SNIC: 'shortId',
    SECURITY_GROUP: 'securityGroupId',
    ENTERPRISE_SECURITY_GROUP: 'esgId',
    NAT: 'id',
    PRIVATE_NAT: 'natId',
    DEDICATEDCONN: 'id',
    VPN: 'vpnId',
    PEERCONN: 'peerConnId',
    ET: 'id',
    ET_CHANNEL: 'id'
};
const rules = {
    editTag: [
        {
            required: true,
            message: '请先选中实例'
        }
    ]
};
const {asComponent, invokeSUI, template} = decorators;
const tpl = html`
    <template>
        <s-tooltip
            s-if="isShowTooltip"
            content="{{editTag.message}}"
            trigger="{{editTag.disable ? 'hover' : ''}}"
            placement="top"
        >
            <s-button skin="{{skin}}" on-click="editTag" disabled="{{editTag.disable}}">{{actionText}}</s-button>
        </s-tooltip>
        <s-button skin="{{skin}}" class="edit-tag" s-else on-click="editTag">{{actionText}}</s-button>
    </template>
`;

@template(tpl)
@asComponent('@edit-tag')
@invokeSUI
export default class EditTag extends Component {
    initData() {
        return {
            type: 'VPC',
            isShowTooltip: true,
            skin: 'primary',
            editTag: {},
            actionText: '编辑标签',
            selectedItems: []
        };
    }

    inited() {
        this.data.set('editTag', this.checkRule());
        this.watch('selectedItems', value => {
            this.data.set('editTag', this.checkRule(value));
        });
    }

    checkRule(value) {
        let {editTag} = checker.check(rules, value || []);
        return editTag;
    }

    // 编辑便签
    editTag() {
        let instances = this.data.get('selectedItems');
        const type = this.data.get('type');
        const title = this.data.get('actionText');
        const actionOptions = {
            title,
            serviceType: serviceTypeKey[type],
            instances: instances,
            options: () => {
                return this.$http
                    .getSearchTagList({
                        serviceType: serviceTypeKey[type],
                        region: [window.$context.getCurrentRegionId()]
                    })
                    .then(result => {
                        return result;
                    });
            },
            parentAction: this,
            submitHandler: tags => {
                const param = {
                    insertTags: tags,
                    resources: u.map(instances, item => ({
                        id: item[idKey[type]],
                        resourceId: item[resourceIdKey[type]],
                        serviceType: serviceTypeKey[type],
                        tags: instances.length > 1 && item.tags ? tags.concat(item.tags) : tags
                    }))
                };
                if (type === 'ET' || type === 'ET_CHANNEL') {
                    delete param.insertTags;
                }
                return this.$http.vpcTagAssign(param);
            },
            helpDocUrl: DocService.vpcTag
        };
        const dialog = new TagEditDialog({
            data: actionOptions
        });
        dialog.attach(document.body);
        dialog.on('success', () => {
            this.fire('success');
            dialog.dispose && dialog.dispose();
        });
        dialog.on('cancel', e => {
            dialog.dispose && dialog.dispose();
        });
    }
}
