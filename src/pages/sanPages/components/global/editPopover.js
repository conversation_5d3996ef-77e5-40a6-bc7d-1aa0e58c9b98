import _ from 'lodash';
import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import {Input} from '@baidu/sui';
import './editPopover.less';
import testID from '@/testId';

const {asComponent, invokeSUI, template} = decorators;
const tpl = html` <template>
    <s-popover
        class="edit-detail-pop {{customClass}}"
        s-ref="edit"
        placement="top"
        trigger="click"
        data-test-id="${testID.security.detailEditName}"
    >
        <div class="edit-content" slot="content">
            <s-input
                s-if="compType==='input'"
                value="{=valueOther=}"
                width="{{width}}"
                placeholder="{{rule.placeholder}}"
                on-input="onInput($event)"
                data-test-id="${testID.security.detailEditNameInput}"
            />
            <s-textarea
                s-else
                value="{=valueOther=}"
                width="{{width}}"
                height="48"
                placeholder="{{rule.placeholder}}"
                on-input="onInput($event)"
                data-test-id="${testID.security.detailEditNameInput}"
            />
            <p class="edit-error">{{error}}</p>
            <p class="edit-tip" s-if="{{tip}}">{{tip}}</p>
            <s-button
                skin="primary"
                disabled="{{btnDisabled}}"
                on-click="onEdit"
                data-test-id="${testID.security.detailEditNameSub}"
                >确定</s-button
            >
            <s-button on-click="editCancel">取消</s-button>
        </div>
        <slot></slot>
    </s-popover>
</template>`;

@template(tpl)
@invokeSUI
@asComponent('@edit-popover')
export default class EditPopover extends Component {
    static components = {
        's-textarea': Input.TextArea
    };
    initData() {
        return {
            value: '',
            valueOther: '',
            rule: {},
            btnDisabled: true,
            customClass: '',
            width: 160,
            compType: 'input'
        };
    }
    inited() {
        this.data.set('valueOther', this.data.get('value'));
        this.data.set('compType', this.data.get('compType'));
    }

    attached() {
        this.watch('value', () => {
            this.data.set('valueOther', this.data.get('value'));
        });
    }

    onInput({value}) {
        const result = this.validate(value);
        this.data.set('btnDisabled', !result);
    }

    onEdit() {
        this.fire('edit', this.data.get('valueOther'));
        this.editCancel();
    }

    validate(value) {
        value === undefined ? value : this.data.get('valueOther');
        const rule = this.data.get('rule');
        if (rule.required && !_.trim(value)) {
            this.data.set('error', rule.requiredErrorMessage);
            return false;
        }
        if (rule.pattern && !rule.pattern.test(value)) {
            this.data.set('error', rule.patternErrorMessage);
            return false;
        }
        if (rule.maxLength && value.length > rule.maxLength) {
            this.data.set('error', rule.patternErrorMessage);
            return false;
        }
        this.data.set('error', '');
        return true;
    }

    reset() {
        this.data.set('error');
        this.data.set('valueOther', this.data.get('value'));
    }

    editCancel(type) {
        this.reset();
        this.ref('edit').data.set('visible', false);
    }
}
