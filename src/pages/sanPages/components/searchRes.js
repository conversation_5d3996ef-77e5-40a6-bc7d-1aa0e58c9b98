import u from 'lodash';
import { Component } from 'san';
import { html, decorators } from '@baiducloud/runtime';
import {
    Select,
    Cascader,
    Icon,
    AutoComplete
} from '@baidu/sui';
import { 
    OutlinedSearch,
} from '@baidu/sui-icon';

import './searchRes.less';

const { asComponent, template } = decorators;
const tpl = html`
<template>
    <div class="{{klass}}">
        <s-cascader slot="options"
            width="120"
            defaultLabel="{{searchbox.defaultLabel}}"
            on-change="onSearchboxChange"
            datasource="{{searchbox.keywordTypes}}"
            value="{=searchbox.keywordType=}"
            trigger="hover">
        </s-cascader>
        <div class="search-res-right">
            <s-autocompelete
                s-ref="autoCompelete"
                value="{=searchbox.keyword=}"
                placeholder="{{searchbox.placeholder}}"
                on-blur="onBlur($event)"
                class="autocompelete-res"
            >
                <s-option
                    s-for="item in tagList"
                    value="{{item.value}}"
                    label="{{item.text}}">
                    <span style="float: left">{{ item.text }}</span>
                </s-option>
            </s-autocompelete>
            <outlined-search on-click="onSearch" class="search"/>
        </div>
    </div>
</template>`;

@template(tpl)
@asComponent('@search-res')
export default class DcphyIdBind extends Component {
    components = {
        's-option': Select.Option,
        's-cascader': Cascader,
        's-icon': Icon,
        's-autocompelete': AutoComplete,
        'outlined-search': OutlinedSearch,
    }

    initData() {
        return {
            klass: 'search-res',
            tagList: [],
            searchbox: {},
        };
    }

    attached() {
        this.ref('autoCompelete').el.addEventListener('keydown', ({ keyCode }) => {
            // 如果是回车，则触发查询
            if (keyCode && keyCode === 13) {
                this.fire('search');
            }
        });
    }

    inited() {
        this.getTags();
    }

    // 获取标签数据源
    getTags() {
        const serviceType = [this.data.get('serviceType')];
        this.$http.getSearchTagList({ serviceType, region: [this.$context.getCurrentRegionId()] })
            .then(result => {
                let tags = u.groupBy(result, 'tagKey');
                let tagTypes = [];
                u.each(tags, (tag, key) => {
                    tags[key] = u.map(tag, item => {
                        return {
                            text: item.tagValue || '空值',
                            value: item.tagValue || '空值'
                        };
                    });
                    tags[key].unshift({ text: '所有值', value: '所有值' });
                    tagTypes.push({ text: key, value: key });
                });
                const ADD_TAG = [
                    { text: '(全部)', value: '@@@' },
                    { text: '(空标签)', value: '' }
                ];
                tagTypes = [...ADD_TAG, ...tagTypes];
                this.data.set('tags', tags);
                const keywordType = this.data.get('searchbox.keywordTypes')
                    .filter(item => item.value !== 'tag');
                keywordType.push({
                    value: 'tag',
                    text: '标签',
                    children: tagTypes
                });
                this.data.set('searchbox.keywordTypes', keywordType);
            });
    }

    

    onSearch() {
        this.fire('search');
    }

    onSearchboxChange({ value }) {
        const selectSearch = u.find(
            this.data.get('searchbox.keywordTypes'),
            { value: value[0] }
        ) || {};
        this.data.set('searchbox.keyword', '');
        this.data.set('searchbox.placeholder',
            selectSearch.placeholder
                ? selectSearch.placeholder
                : `请输入${selectSearch.text}进行搜索`
        );
        if (value[0] === 'tag') {
            const tagType = value[1];
            this.data.set('tagList', this.data.get('tags')[tagType] || []);
        } else {
            this.data.set('tagList', []);
        }
    }

    getSearchCriteria() {
        const searchbox = this.data.get('searchbox');
        let { keywordType, keyword } = searchbox;
        let searchParam = {};
        if (keywordType[0] === 'tag') {
            keyword === '空值' && (keyword = '');
            keyword === '所有值' && (keyword = '@@@');
            keywordType[1] === '@@@' && (keyword = '@@@');
            searchParam.subKeywordType = keywordType[1];
            searchParam.keyword = keyword;
            searchParam.keywordType = keywordType[0];
            if (!searchParam.subKeywordType) {
                delete searchParam.keywordType;
                delete searchParam.subKeywordType;
                delete searchParam.keyword;
            }
        } else {
            searchParam.keyword = keyword;
            searchParam.keywordType = keywordType[0];
        }
        return searchParam;
    }

    onBlur({ value }) {
        this.data.set('searchbox.keyword', value);
    }
}