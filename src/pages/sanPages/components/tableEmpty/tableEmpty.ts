/*
 * @Description: 表格无数据时新样式
 * @Author: <EMAIL>
 */

import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import './tableEmpty.less';

const {asComponent, template, invokeSUI, invokeSUIBIZ} = decorators;
const tpl = html`
    <template>
        <s-empty data-test-id="{{dataTestId}}" vertical class="empty-wrapper">
            <p class="empty-desc common" slot="desc">{{desc}}</p>
            <div s-if="!actionAuth.disable && showAction" class="empty-action-widget" slot="action" on-click="onCreate">
                <span class="empty-plus">+</span>
                <s-button class="empty-action" skin="stringfy">{{actionText}}</s-button>
            </div>
            <s-tooltip s-else-if="showAction" content="{{actionAuth.message}}">
                <span class="empty-tip common">{{actionText}}</span>
            </s-tooltip>
        </s-empty>
    </template>
`;
@template(tpl)
@asComponent('@table-empty')
@invokeSUI
@invokeSUIBIZ
export default class TableEmpty extends Component {
    initData() {
        return {
            desc: '暂无数据',
            actionText: '立即添加',
            actionAuth: {},
            showAction: true,
            dataTestId: ''
        };
    }

    inited() {
        const {desc, actionText, actionAuth, showAction, dataTestId} = this.data.get('');
        this.data.set('desc', desc);
        this.data.set('actionText', actionText);
        this.data.set('showAction', showAction);
        this.data.set('dataTestId', dataTestId);
        this.data.set('actionAuth', actionAuth || {disable: false});
    }

    onCreate() {
        this.fire('click');
    }
}
