import u from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Icon, Button} from '@baidu/sui';

import {ipResourceType} from '@/pages/sanPages/common';
import './deleteCheck.less';

/* eslint-disable */
const tpl = html`
    <template>
        <s-dialog class="delete-check" closeAfterMaskClick="{{false}}" open="{=open=}" title="{{title}}" width="500">
            <div class="confirm-wrapper">
                <s-icon name="warning-new" />
                <div s-if="{{sourceList.length <= 0}}">{{content | getDeleteContent}}</div>
                <div s-else>
                    <span>选中的{{type | getType}}无法删除：</span>
                    <div s-for="item in sourceList">
                        <div class="content-wrap">
                            {{type | getType}}
                            <span class="item-name">"{{item.name}}"</span>
                            存在以下资源(包括回收站)，在释放这些资源前，无法删除{{type | getType}}
                        </div>
                        <ul s-for="sourceItem in item.source">
                            <li class="content-wrap">{{sourceItem | getSourceText}}</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div slot="footer">
                <s-button on-click="close">取消</s-button>
                <s-button skin="primary" on-click="dialogConfirm" disabled="{{sourceList.length > 0}}">确定</s-button>
            </div>
        </s-dialog>
    </template>
`;
/* eslint-enable */
export default class deleteCheckConfirm extends Component {
    static template = tpl;
    static components = {
        's-dialog': Dialog,
        's-icon': Icon,
        's-button': Button
    };
    static filters = {
        getDeleteContent() {
            const selectedItems = this.data.get('selectedItems');
            const nameWithId = u.map(selectedItems, item => {
                const {name, shortId} = item;
                return `${name || '系统预定义子网'}/${shortId}`;
            });
            return `确认删除所选中的${
                this.data.get('type') === 'vpc' ? 'VPC：' + nameWithId.join('、') : '子网：' + nameWithId.join('、')
            }？`;
        },
        getType() {
            return this.data.get('type') === 'vpc' ? '私有网络' : '子网';
        },
        getSourceText(sourceItem) {
            // ipResourceType只维护与v3中不一致资源
            let name = ipResourceType.getTextFromValue(sourceItem.sourceName)
                ? ipResourceType.getTextFromValue(sourceItem.sourceName)
                : this.$context.SERVICE_TYPE[sourceItem.sourceName.toUpperCase()]?.name || sourceItem.sourceName;
            return name;
        }
    };
    initData() {
        return {
            title: '提示',
            content: '',
            open: true,
            sourceList: []
        };
    }
    dialogConfirm() {
        this.fire('confirm');
        this.data.set('open', false);
    }
    close() {
        this.fire('close');
        this.data.set('open', false);
    }
}
