.search-res {
    display: inline-flex;
    align-items: center;
    margin: 0 10px;
    vertical-align: middle;
    position: relative;
    .s-cascader {
        margin: 0 5px;
        .s-cascader-value {
            border: 1px solid #ccc;
            box-sizing: border-box;
        }
    }
    .s-select {
        input {
            width: 170px !important;
        }
    }
    .search {
        margin-left: -25px;
        margin-right: 5px;
        position: relative;
        z-index: 100;
    }
    .s-cascader-column {
        width: 120px;
    }
}
.search_tag {
    .resource-group-search {
        .s-input-suffix :active{
            .s-icon {
                fill: #144bcc !important;
            }
        }
        .s-input-suffix :hover{
            .s-icon {
                fill: #2468f2 !important;
            }
        }
    }
    .s-cascader-value .s-icon-button-able {
        fill: #84868C !important;
    }
    .s-cascader-value .s-icon-button-able:hover {
        fill: #84868C !important;
    }
}