/*
 * @description: 确认弹窗
 * @author: p<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Icon, Button} from '@baidu/sui';
import './confirm.less';
/* eslint-disable */
const tpl = html`
    <template>
        <s-dialog
            type="warning"
            class="dcgw-confirm confirm-dialog-widget"
            closeAfterMaskClick="{{false}}"
            open="{=open=}"
            title="{{title}}"
        >
            <div>
                <!--bca-disable-next-line-->
                {{content | raw}}
            </div>
            <div slot="footer">
                <s-button on-click="close">取消</s-button>
                <s-button skin="primary" on-click="dialogConfirm">确定</s-button>
            </div>
        </s-dialog>
    </template>
`;
/* eslint-enable */
export default class BccInstanceCreateNetworkConfig extends Component {
    static template = tpl;
    static components = {
        's-dialog': Dialog,
        's-icon': Icon,
        's-button': But<PERSON>
    };
    initData() {
        return {
            title: '提示',
            content: '',
            open: true
        };
    }
    dialogConfirm() {
        this.fire('confirm');
        this.data.set('open', false);
    }
    close() {
        this.fire('close');
        this.data.set('open', false);
    }
}
