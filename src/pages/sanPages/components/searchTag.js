import u from 'lodash';
import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import {ResourceGroupSearch} from '@baidu/bce-resourcegroup-sdk-san';
import {ResourceGroupSDK} from '@baidu/bce-resourcegroup-sdk';
import {getSdk} from '@/pages/sanPages/utils/sdk';
import './searchTag.less';

const {asComponent, template} = decorators;
const tpl = html` <template>
    <resource-group-search
        sdk="{{resourceSdk}}"
        placeholder="{=searchbox.placeholder=}"
        value="{=searchbox.keyword=}"
        keyword-type="{=searchbox.keywordType=}"
        datasource="{{searchbox.keywordTypes}}"
        text-datasource="{{searchbox.textDS}}"
        hasReourceGroup="{{isShowResGroup}}"
        on-keywordTypeChange="onKeywordTypeChange"
        on-search="onSearch"
        class="search_tag"
    />
</template>`;

@template(tpl)
@asComponent('@search-tag')
export default class DcphyIdBind extends Component {
    components = {
        'resource-group-search': ResourceGroupSearch
    };

    static computed = {
        isShowResGroup() {
            const isShowResGroup = this.data.get('isShowResGroup');
            let flag = true;
            if (isShowResGroup !== undefined && !isShowResGroup) {
                flag = false;
            }
            return flag;
        }
    };

    initData() {
        return {
            searchbox: {},
            resGroupDatasource: [],
            resourceSdk: getSdk('RESOURCE')
        };
    }

    inited() {
        this.getTags();
    }

    // 获取标签数据源
    getTags() {
        const serviceType = [this.data.get('serviceType')];
        this.data.get('serviceType') &&
            this.$http.getSearchTagList({serviceType, region: [this.$context.getCurrentRegionId()]}).then(result => {
                let tags = u.groupBy(result, 'tagKey');
                let tagTypes = [];
                u.each(tags, (tag, key) => {
                    tags[key] = u.map(tag, item => {
                        return {
                            text: item.tagValue || '空值',
                            value: item.tagValue || '空值'
                        };
                    });
                    tags[key].unshift({text: '所有值', value: '所有值'});
                    tagTypes.push({text: key, value: key});
                });
                const ADD_TAG = [
                    {text: '(全部)', value: '@@@'}
                    // {text: '(空标签)', value: ''}
                ];
                tagTypes = [...ADD_TAG, ...tagTypes];
                this.data.set('tags', tags);
                const keywordType = this.data.get('searchbox.keywordTypes').filter(item => item.value !== 'tag');
                if (!this.data.get('noSupportTag')) {
                    keywordType.push({
                        value: 'tag',
                        text: '标签',
                        children: tagTypes
                    });
                }
                this.data.set('searchbox.keywordTypes', keywordType);
            });
    }
    onSearch() {
        this.fire('search');
    }

    onKeywordTypeChange({value}) {
        const selectSearch = u.find(this.data.get('searchbox.keywordTypes'), {value: value[0]}) || {};
        this.data.set('searchbox.keyword', '');
        this.data.set(
            'searchbox.placeholder',
            selectSearch.placeholder ? selectSearch.placeholder : `请输入${selectSearch.text}进行搜索`
        );
        if (value[0] === 'tag') {
            const tagType = value[1];
            this.data.set('searchbox.textDS', this.data.get('tags')[tagType] || []);
        } else if (value[0] === 'resGroupId') {
            this.data.set('searchbox.textDS', ['resGroupId']);
        } else {
            this.data.set('searchbox.textDS', '');
        }
    }

    getSearchCriteria() {
        const searchbox = this.data.get('searchbox');
        let {keywordType, keyword} = searchbox;
        let searchParam = {};
        if (keywordType[0] === 'tag') {
            keyword === '空值' && (keyword = '');
            keyword === '所有值' && (keyword = '@@@');
            keywordType[1] === '@@@' && (keyword = '@@@');
            searchParam.subKeywordType = keywordType[1];
            searchParam.keyword = keyword;
            searchParam.keywordType = keywordType[0];
            if (!searchParam.subKeywordType) {
                delete searchParam.keywordType;
                delete searchParam.subKeywordType;
                delete searchParam.keyword;
            }
        } else {
            searchParam.keyword = keyword;
            searchParam.keywordType = keywordType[0];
        }
        return searchParam;
    }

    onBlur({value}) {
        this.data.set('searchbox.keyword', value);
    }
}
