/*
 * @Description: 通用搜索框，支持输入框和下拉搜索
 * @Author: <EMAIL>
 * @Date: 2022-04-18 20:01:02
 */
import u from 'lodash';
import { Component } from 'san';
import { html, decorators } from '@baiducloud/runtime';

const { asComponent, invokeSUI, template } = decorators;

import './searchType.less';

const tpl = html`
<div class="{{klass}}">
    <s-select
        class="select-key-wrapper"
        datasource="{{searchbox.keywordTypes}}"
        value="{=searchbox.keywordType=}"
        on-change="onSearchboxChange"
    >
    </s-select>
    <div class="search-option-container">
        <div s-if="selectedItem.selectDataSource">
            <s-select
                datasource="{{selectedItem.selectDataSource}}"
                value="{=searchbox.keyword=}"
                on-change="onSearch"
            >
            </s-select>
        </div>
        <div class="input-wrapper" s-else>
            <s-search
                s-ref="search"
                placeholder="{{searchbox.placeholder}}"
                value="{=searchbox.keyword=}"
                on-search="onSearch">
            </s-search>
        </div>
    </div>
</div>
`;


@template(tpl)
@invokeSUI
@asComponent('@search-type')
export default class SearchType extends Component {
  initData() {
    return {
        klass: 'search-type-wrapper',
        searchbox: {}
    }
  }

  static computed = {
    selectedItem() {
        const selectList = this.data.get('searchbox').keywordTypes;
        let value = this.data.get('searchbox.keywordType');
        return u.find(selectList, (item) => item.value === value);
    }
  }
  
  getSearchCriteria() {
    const searchbox = this.data.get('searchbox');
    let { keywordType, keyword } = searchbox;
    return {
        keywordType,
        keyword
    }
  }

  onSearchboxChange({ value }) {
    const selectSearch = u.find(
        this.data.get('searchbox.keywordTypes'),
        { value: value }
    ) || {};
    this.data.set('searchbox.keyword', '');
    this.data.set('searchbox.placeholder',
        selectSearch.placeholder
            ? selectSearch.placeholder
            : `请输入${selectSearch.text}进行搜索`
    );
  }

  onSearch( {value} ) {
    this.data.set('searchbox.keyword', value);
    this.nextTick(() => {
        this.fire('search');
    })
  }
}