/*
 * @description: 实例诊断确认弹窗
 */
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Icon, Button} from '@baidu/sui';
import './confirm.less';
/* eslint-disable */
const tpl = html`
    <template>
        <s-dialog
            type="warning"
            class="dcgw-confirm confirm-dialog-widget"
            closeAfterMaskClick="{{false}}"
            open="{=open=}"
            title="{{title}}"
            on-close="close"
        >
            <div style="display:flex;align-items: center;">
                <span style="display:inline-block;">请确认是否发起诊断操作？</span>
                <span s-if="jumpHref" style="display:inline-block;">还是</span
                ><s-button s-if="jumpHref" skin="stringfy" on-click="jumpDiagnose" style="padding: 0px;"
                    >查看历史诊断</s-button
                >
                <span s-if="jumpHref" style="display:inline-block;">？</span>
            </div>
            <div slot="footer">
                <s-button on-click="close">取消</s-button>
                <s-button skin="primary" on-click="dialogConfirm" disabled="{{tableLoading}}">发起诊断</s-button>
            </div>
        </s-dialog>
    </template>
`;
/* eslint-enable */
export default class BccInstanceCreateNetworkConfig extends Component {
    static template = tpl;
    static components = {
        's-dialog': Dialog,
        's-icon': Icon,
        's-button': Button
    };
    inited() {
        this.data.set('tableLoading', true);
        let payload = {
            pageNo: 1,
            pageSize: 10,
            order: 'desc',
            orderBy: 'lastDiagnoseTime',
            keywordType: 'instanceId',
            keyword: this.data.get('instanceId')
        };
        this.$http
            .getDiagnoseList(payload)
            .then(res => {
                if (res?.result && res?.result[0]) {
                    this.data.set('jumpHref', true);
                }
            })
            .finally(() => this.data.set('tableLoading', false));
    }
    initData() {
        return {
            title: '提示',
            content: '您确定发起实例诊断吗？',
            open: true,
            jumpHref: false
        };
    }
    jumpDiagnose() {
        this.data.set('open', false);
        let linkHref = `#/vpc/diagnosis/detail?instanceType=${this.data.get('instanceType')}`;
        linkHref = linkHref + `&region=${this.data.get('region')}&instanceId=${this.data.get('instanceId')}`;
        window.location.hash = linkHref;
    }
    dialogConfirm() {
        this.fire('confirm');
        this.data.set('open', false);
    }
    close() {
        this.fire('close');
        this.data.set('open', false);
    }
}
