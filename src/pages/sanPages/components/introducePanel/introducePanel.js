/*
 * @Description: 产品介绍面板
 * @Author: <EMAIL>
 */

import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import './introducePanel.less';

const {asComponent, template} = decorators;
const tpl = html`
    <div s-if="show" class="introduce-panel {{klass}}">
        <span class="introduce-panel-hide" on-click="hidePanel">{{show ? '隐藏' : '展示'}}</span>
        <h4 class="introduce-panel-title">{{title}}</h4>
        <!--bca-disable-next-line-->
        <div class="introduce-panel-description" s-if="description">{{description | raw}}</div>
        <div s-if="markerDesc" class="introduce-desc-class">
            <div s-for="item,index in markerDesc" class="introduce-desc-class-line">
                <div class="marker"></div>
                <!--bca-disable-next-line-->
                <p class="introduce-word-desc">{{item | raw}}</p>
                <br s-if="index === 0" />
            </div>
        </div>
        <slot name="iconShowIntroduce" />
        <div class="introduce-panel-footer">
            <div s-for="item in introduceOptions" class="introduce-panel-footer-item">
                <span class="dot"></span>
                {{item.label}}
            </div>
        </div>
    </div>
`;
@template(tpl)
@asComponent('@introduce-panel')
class IntroducePanel extends Component {
    initData() {
        return {
            show: true,
            title: '',
            description: '',
            bgUrl: 'http://bce.bdstatic.com/network-frontend/vpn-bg-1280.png',
            introduceOptions: []
        };
    }

    inited() {
        const {title, description, introduceOptions, bgUrl} = this.data.get('');
        this.data.set('title', title);
        this.data.set('description', description);
        this.data.set('introduceOptions', introduceOptions);
        this.watch('isShow', value => {
            this.data.set('show', value);
        });
        if (bgUrl) {
            this.data.set('bgUrl', bgUrl);
        }
    }

    hidePanel() {
        this.data.set('show', !this.data.get('show'));
        this.fire('toggle', this.data.get('show'));
    }
}
export default Processor.autowireUnCheckCmpt(IntroducePanel);
