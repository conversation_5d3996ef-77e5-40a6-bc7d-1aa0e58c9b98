.introduce-panel {
    position: relative;
    margin: 16px;
    padding: 24px;
    border-radius: 6px;
    .introduce-panel-hide {
        font-family: PingFangSC-Regular;
        position: absolute;
        display: inline-block;
        line-height: 17px;
        color: #2468f2;
        right: 24px;
        font-size: 12px;
        top: 24px;
        cursor: pointer;
    }
    .introduce-panel-title {
        font-size: 20px;
        color: #151b26;
        font-weight: 500;
        line-height: 28px;
    }
    .introduce-panel-description {
        margin: 16px 0px 24px 0px;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #5c5f66;
        line-height: 24px;
        font-weight: 400;
    }
    .introduce-panel-footer {
        display: flex;
        .introduce-panel-footer-item {
            margin-right: 32px;
            .dot {
                display: inline-block;
                width: 8px;
                height: 8px;
                border-radius: 100%;
                background-color: #ffffff;
                border: 2px solid #2468f2;
                margin-right: 4px;
            }
        }
    }
    .introduce-desc-class-line {
        display: flex;
        align-items: center;
        margin-top: 6px;
    }
    .introduce-desc-class {
        margin-top: 24px;
        .marker {
            border: 2px solid #2468f2;
            display: inline-block;
            border-radius: 50%;
            width: 8px;
            height: 8px;
            background-color: transparent;
            margin-right: 8px;
        }
        .introduce-word-desc {
            display: inline-block;
            color: #151b26;
            line-height: 16px;
        }
    }
}
@media screen and (max-width: 1280px) {
    .IPv6-vpn-wrapper {
        background:
            url('http://bce.bdstatic.com/network-frontend/IPv6-bg-1280.png') no-repeat,
            linear-gradient(180deg, rgba(230, 240, 255, 0) 0%, #ffffff 63%);
        background-size: 100% 100%;
    }
    .endpoint-peerconn-wrapper {
        background:
            url('http://bce.bdstatic.com/network-frontend/endpoint-bg-1280.png') no-repeat,
            linear-gradient(180deg, rgba(230, 240, 255, 0) 0%, #ffffff 63%);
        background-size: 100% 100%;
    }
}
@media screen and (min-width: 1280px) and (max-width: 1440px) {
    .IPv6-vpn-wrapper {
        background:
            url('http://bce.bdstatic.com/network-frontend/IPv6-bg-1440.png') no-repeat,
            linear-gradient(180deg, rgba(230, 240, 255, 0) 0%, #ffffff 63%);
        background-size: 100% 100%;
    }
    .endpoint-peerconn-wrapper {
        background:
            url('http://bce.bdstatic.com/network-frontend/endpoint-bg-1440.png') no-repeat,
            linear-gradient(180deg, rgba(230, 240, 255, 0) 0%, #ffffff 63%);
        background-size: 100% 100%;
    }
}
@media screen and (min-width: 1440px) {
    .IPv6-vpn-wrapper {
        background:
            url('http://bce.bdstatic.com/network-frontend/IPv6-bg-1920.png') no-repeat,
            linear-gradient(180deg, rgba(230, 240, 255, 0) 0%, #ffffff 63%);
        background-size: 100% 100%;
    }
    .endpoint-peerconn-wrapper {
        background:
            url('http://bce.bdstatic.com/network-frontend/endpoint-bg-1920.png') no-repeat,
            linear-gradient(180deg, rgba(230, 240, 255, 0) 0%, #ffffff 63%);
        background-size: 100% 100%;
    }
}
