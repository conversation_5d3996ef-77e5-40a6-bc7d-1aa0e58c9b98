/**
 * @file index.js 安全组入口
 *
 * <AUTHOR>

import _ from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';

const {invokeComp, withSidebar, invokeAppComp, template, invokeSUIBIZ, invokeSUI} = decorators;

import {DocService} from '@/pages/sanPages/common';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import VpcMirrorList from '@/pages/sanPages/pages/flowmirror/mirrowSession/list/List';
import FilterRuleGroupList from '@/pages/sanPages/pages/flowmirror/filterRulegroup/List';
import IntroducePanel from '@/pages/sanPages/components/introducePanel/introducePanel';
import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import DOCUMENT_ICON from '@/img/document_normal.svg?url';
import DOCUMENT_ICON_COLOR from '@/img/document_color.svg?url';
import './flow-mirror.less';

const tpl = html`
    <template>
        <div class="mirror-header">
            <s-app-list-page class="s-tab-list-page">
                <div slot="pageTitle">
                    <div class="vpc-mirror-header">
                        <div class="header-left">
                            <span class="title">流量镜像</span>
                        </div>
                        <div class="header-right">
                            <a
                                s-ref="introduce"
                                href="javascript:void(0)"
                                class="help-file function-introduce"
                                on-click="handleShowCard"
                                on-mouseenter="handleMouseEnter('introduce')"
                                on-mouseleave="handleMouseLeave('introduce')"
                                s-if="{{!FLAG.NetworkSupportXS}}"
                            >
                                <img class="s-icon" src="{{introduceIcon}}" />功能简介
                            </a>
                            <a
                                href="{{DocService.mirror_helpFile}}"
                                target="_blank"
                                class="help-file help-file-docs"
                                s-if="{{!FLAG.NetworkSupportXS}}"
                            >
                                <s-icon name="warning-new" />帮助文档
                            </a>
                        </div>
                    </div>
                </div>
                <vpc-mirror-list s-if="showMirrorSession">
                    <template slot="introducePanel">
                        <introduce-panel
                            isShow="{{show}}"
                            klass="{{'IPv6-vpn-wrapper'}}"
                            title="{{introduceTitle}}"
                            markerDesc="{{markerDesc}}"
                            introduceOptions="{{introduceOptions}}"
                            on-toggle="handleToggle($event)"
                        ></introduce-panel>
                    </template>
                </vpc-mirror-list>
                <filter-rule-group-list s-else></filter-rule-group-list>
            </s-app-list-page>
        </div>
    </template>
`;
@template(tpl)
@invokeComp('@introduce-panel', '@vpc-mirror-list', '@filter-rule-group-list')
@invokeAppComp
@invokeSUIBIZ
@invokeSUI
class Mirror extends Component {
    static componnets = {
        'vpc-mirror-list': VpcMirrorList,
        'filter-rule-group-list': FilterRuleGroupList,
        'introduce-panel': IntroducePanel
    };
    static computed = {
        showMirrorSession() {
            const hash = window.location.hash;
            return hash.startsWith('#/vpc/mirror/list');
        }
    };
    initData() {
        return {
            DocService,
            FLAG,
            introduceTitle: '流量镜像 ',
            markerDesc: [
                '流量镜像用于记录弹性公网IP、云服务器、弹性网卡、增强型NAT网关、对等连接、专线网关和云智能网实例按五元组等条件过滤，并将流量复制转发到BLB实例，用于安全审计、问题定位和业务分析等场景。',
                '流量镜像的核心字段为镜像源、筛选条件、镜像目的、指定VNI等。'
            ],
            introduceIcon: INTRODUCE_ICON_COLOR,
            documentIcon: DOCUMENT_ICON,
            introduceEle: null
        };
    }
    attached() {
        this.data.set('introduceEle', this.ref('introduce'));
    }
    onRegionChange() {
        location.reload();
    }
    /**
     * @description 介绍卡片变化时回调
     * @param {boolean} flag
     */
    handleToggle(flag) {
        this.data.set('show', flag);
        this.data.set('introduceIcon', INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#151B26';
    }
    handleShowCard() {
        if (!this.data.get('show')) {
            this.data.set('show', true);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type) {
        if (type === 'introduce') {
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
            const ele = this.data.get('introduceEle');
            ele.style.color = '#2468F2';
        } else {
            this.data.set('documentIcon', DOCUMENT_ICON_COLOR);
        }
    }
    handleMouseLeave(type) {
        const isShow = this.data.get('show');
        if (type === 'introduce') {
            this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
            const ele = this.data.get('introduceEle');
            !isShow && (ele.style.color = '#151B26');
        } else {
            this.data.set('documentIcon', DOCUMENT_ICON);
        }
    }
}
export default San2React(Processor.autowireUnCheckCmpt(Mirror));
