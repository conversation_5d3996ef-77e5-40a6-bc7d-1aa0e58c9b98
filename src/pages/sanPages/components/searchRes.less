.search-res {
    display: inline-flex;
    align-items: center;
    margin: 0 8px;
    vertical-align: middle;
    position: relative;
    .s-cascader {
        // margin: 0 8px;
        .s-cascader-panel{
            display: flex;
            height: auto;
            background: none;
            box-shadow: none;
            .s-cascader-column {
                background: #fff;
                box-shadow: 0 1px 6px #dadbdd;
                max-height: 150px;
            }
        }
        .s-cascader-value {
            border: 1px solid #dadbdd;
            box-sizing: border-box;
        }
        .s-cascader-multiple-items {
            width: 80%;
        }
    }
    .s-select {
        input {
            width: 170px !important;
        }
    }
    .search {
        top: 8px;
        position: absolute;
        z-index: 100;
    }
    .s-cascader-column {
        width: 120px;
    }
    .search-res-right:hover {
        .search {
            .s-icon {
                fill: #108CEE !important;
            }
        }
    }
    .search-res-right:active {
        .search {
            .s-icon {
                fill: #0D77CA !important;
            }
        }
    }
}