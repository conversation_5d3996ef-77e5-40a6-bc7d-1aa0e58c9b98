import moment from 'moment';

export default {
    shortcutItems: [
        {
            text: '近1小时',
            onClick(picker) {
                picker.setValueByShortCut({
                    begin: new Date(moment().subtract(1, 'hour').valueOf()),
                    end: new Date(moment().valueOf())
                });
            }
        },
        {
            text: '近6小时',
            onClick(picker) {
                picker.setValueByShortCut({
                    begin: new Date(moment().subtract(6, 'hour').valueOf()),
                    end: new Date(moment().valueOf())
                });
            }
        },
        {
            text: '今天',
            onClick(picker) {
                picker.setValueByShortCut({
                    begin: new Date(moment().startOf('day').valueOf()),
                    end: new Date(moment().valueOf())
                });
            }
        },
        {
            text: '昨天',
            onClick(picker) {
                picker.setValueByShortCut({
                    begin: new Date(moment().subtract(1, 'day').startOf('day').valueOf()),
                    end: new Date(moment().subtract(1, 'day').endOf('day').valueOf())
                });
            }
        },
        {
            text: '近7天',
            onClick(picker) {
                picker.setValueByShortCut({
                    begin: new Date(moment().subtract(7, 'day').valueOf()),
                    end: new Date(moment().valueOf())
                });
            }
        },
        {
            text: '近30天',
            onClick(picker) {
                picker.setValueByShortCut({
                    begin: new Date(moment().subtract(30, 'day').valueOf()),
                    end: new Date(moment().valueOf())
                });
            }
        }
    ],
    VpnMetric: {
        bytes: {
            title: '网络带宽',
            unit: 'bps',
            bitUnit: 1000,
            metrics: [
                {
                    name: '入向流量',
                    value: 'InBytes'
                },
                {
                    name: '出向流量',
                    value: 'OutBytes'
                }
            ]
        },
        packets: {
            title: '包速率',
            unit: 'pps',
            metrics: [
                {
                    name: '入向包速率',
                    value: 'InPackets'
                },
                {
                    name: '出向包速率',
                    value: 'OutPackets'
                }
            ]
        },
        iplr: {
            title: '丢包率',
            unit: '百分比',
            metrics: [
                {
                    name: '丢包率',
                    value: 'Iplr'
                }
            ]
        },
        latency: {
            title: '时延',
            unit: 'ms',
            metrics: [
                {
                    name: '时延',
                    value: 'Latency'
                }
            ]
        }
    },
    greVpnMetric: {
        bytes: {
            title: '网络带宽',
            unit: 'Bps',
            bitUnit: 1000,
            metrics: [
                {
                    name: '入向带宽',
                    value: 'InBps'
                },
                {
                    name: '出向带宽',
                    value: 'OutBps'
                }
            ]
        },
        packets: {
            title: '包速率',
            unit: 'pps',
            metrics: [
                {
                    name: '入向包速率',
                    value: 'InPps'
                },
                {
                    name: '出向包速率',
                    value: 'OutPps'
                }
            ]
        }
    },
    SslVpnConnMetric: {
        inBps: {
            title: '入向带宽',
            unit: 'Bps',
            bitUnit: 1000,
            metrics: [
                {
                    name: '入向带宽',
                    value: 'InBps'
                }
            ]
        },
        outBps: {
            title: '入向带宽',
            unit: 'Bps',
            bitUnit: 1000,
            metrics: [
                {
                    name: '入向带宽',
                    value: 'OutBps'
                }
            ]
        },
        SSLClientConnectionCount: {
            title: '客户端在线连接数',
            unit: '个',
            bitUnit: 1000,
            metrics: [
                {
                    name: '客户端在线连接数',
                    value: 'SSLClientConnectionCount'
                }
            ]
        }
    },
    VpnEipMetric: {
        bit: {
            title: '网络带宽',
            unit: 'bps',
            bitUnit: 1000,
            metrics: [
                {
                    name: '出口带宽',
                    value: 'WebOutBitsPerSecond'
                },
                {
                    name: '入口带宽',
                    value: 'WebInBitsPerSecond'
                }
            ]
        },
        bytes: {
            title: '网络流量',
            unit: '字节',
            metrics: [
                {
                    name: '出口流量',
                    value: 'WebOutBytes'
                },
                {
                    name: '入口流量',
                    value: 'WebInBytes'
                }
            ]
        },
        package: {
            title: '网络数据包',
            unit: '个',
            metrics: [
                {
                    name: '出口数据包数',
                    value: 'WebOutPkgCount'
                },
                {
                    name: '入口数据包',
                    value: 'WebInPkgCount'
                }
            ]
        },
        bandwidth: {
            title: '带宽使用率',
            unit: '百分比',
            metrics: [
                {
                    name: '出口带宽使用率',
                    value: 'OutBandwidthUsedPercent'
                },
                {
                    name: '入口带宽使用率',
                    value: 'InBandwidthUsedPercent'
                }
            ]
        }
    },
    EniMetrics: {
        bandwidth: {
            title: '带宽',
            metrics: [
                {
                    value: 'VNicInBPS',
                    name: '入口带宽'
                },
                {
                    value: 'VNicOutBPS',
                    name: '出口带宽'
                }
            ],
            unit: 'bps',
            bitUnit: 1000
        },
        flow: {
            title: '流量',
            metrics: [
                {
                    value: 'VNicInBytes',
                    name: '入口流量'
                },
                {
                    value: 'VNicOutBytes',
                    name: '出口流量'
                }
            ],
            unit: '字节'
        },
        package: {
            title: '包速率',
            metrics: [
                {
                    value: 'VNicInPPS',
                    name: '入向包速率'
                },
                {
                    value: 'VNicOutPPS',
                    name: '出向包速率'
                }
            ],
            unit: 'pps'
        }
    },
    ProbeMetrics: {
        lossPacket: {
            title: '丢包率',
            unit: '百分比',
            metrics: [
                {
                    name: '丢包率',
                    value: 'PktDropPercent'
                }
            ]
        },
        latency: {
            title: '时延',
            unit: 'ms',
            metrics: [
                {
                    name: '时延',
                    value: 'Rtt'
                }
            ]
        },
        DNSSuccesRate: {
            title: 'DNS解析成功率',
            unit: '%',
            metrics: [
                {
                    name: 'DNS解析成功率',
                    value: 'SuccessRateDNS'
                }
            ],
            type: 'DNS'
        }
    },
    EndPointMetrics: {
        bandwidth: {
            title: '网络带宽',
            metrics: [
                {
                    value: 'NetworkInbps',
                    name: '入口带宽'
                },
                {
                    value: 'NetworkOutbps',
                    name: '出口带宽'
                }
            ],
            unit: 'bps',
            bitUnit: 1000
        },
        flow: {
            title: '网络流量',
            metrics: [
                {
                    value: 'NetworkInBytes',
                    name: '入口流量'
                },
                {
                    value: 'NetworkOutBytes',
                    name: '出口流量'
                }
            ],
            unit: '字节'
        },
        package: {
            title: '数据包',
            metrics: [
                {
                    value: 'NetworkInPkgCount',
                    name: '入口数据包数'
                },
                {
                    value: 'NetworkOutPkgCount',
                    name: '出口数据包数'
                }
            ],
            unit: '个'
        },
        dropFlow: {
            title: '丢弃流量',
            metrics: [
                {
                    value: 'DropInBytes',
                    name: '丢弃入向流量'
                },
                {
                    value: 'DropOutBytes',
                    name: '丢弃出向流量'
                }
            ],
            unit: '字节'
        },
        dropPackage: {
            title: '丢弃数据包',
            metrics: [
                {
                    value: 'DropInPkgCount',
                    name: '丢弃入向数据包数'
                },
                {
                    value: 'DropOutPkgCount',
                    name: '丢弃出向数据包数'
                }
            ],
            unit: '个'
        }
    },
    natMetrics: {
        bandwidth: {
            title: '网络带宽',
            metrics: `OutBandwith(${'出口带宽'}),InBandwith(${'入口带宽'})`,
            unit: 'bps',
            bitUnit: 1000
        },
        flow: {
            title: '网络流量',
            metrics: `OutBytes(${'出口流量'}),InBytes(${'入口流量'})`,
            statistics: 'sum',
            unit: '字节'
        },
        package: {
            title: '包速率',
            metrics: `OutPps(${'出向包速率'}),InPps(${'入向包速率'})`,
            unit: 'pps'
        },
        conn: {
            title: '连接数',
            metrics: `ConnNumber(${'连接数'})`,
            unit: '个'
        }
    },
    natClusterMetrics: {
        bwRatioEntry: {
            title: '带宽使用率',
            unit: '%',
            metrics: [
                {
                    value: 'InBWRatioEntry',
                    name: '入口带宽使用率'
                },
                {
                    value: 'OutBWRatioEntry',
                    name: '出口带宽使用率'
                }
            ]
        },
        connsRatioEntry: {
            title: '并发连接数使用率',
            unit: '%',
            metrics: [
                {
                    value: 'ConnsRatioEntry',
                    name: '并发连接数使用率'
                }
            ]
        },
        createConnsEntry: {
            title: '新建连接速率',
            unit: 'cps',
            metrics: [
                {
                    value: 'CreateConnsEntry',
                    name: '新建连接速率'
                }
            ]
        },
        createConnsRatioEntry: {
            title: '新建连接使用率',
            unit: '%',
            metrics: [
                {
                    value: 'CreateConnsRatioEntry',
                    name: '新建连接使用率'
                }
            ]
        },
        pktLossPercentEntry: {
            title: '丢包率',
            unit: '%',
            metrics: [
                {
                    value: 'InPktLossPercentEntry',
                    name: '入方向丢包率'
                },
                {
                    value: 'OutPktLossPercentEntry',
                    name: '出方向丢包率'
                }
            ]
        }
    },
    serverMetrics: {
        bandwidthIn: {
            title: '入口带宽',
            unit: 'bps',
            bitUnit: 1000,
            metrics: [
                {
                    value: 'InBandwith',
                    name: '入口带宽'
                }
            ]
        },
        bandwidthOut: {
            title: '出口带宽',
            unit: 'bps',
            bitUnit: 1000,
            metrics: [
                {
                    value: 'OutBandwith',
                    name: '出口带宽'
                }
            ]
        },
        bytesIn: {
            title: '入口流量',
            statistics: 'sum',
            unit: '字节',
            metrics: [
                {
                    value: 'InBytes',
                    name: '入口流量'
                }
            ]
        },
        bytesOut: {
            title: '出口流量',
            statistics: 'sum',
            unit: '字节',
            metrics: [
                {
                    value: 'OutBytes',
                    name: '出口流量'
                }
            ]
        },
        packageIn: {
            title: '入口包速率',
            unit: '个',
            metrics: [
                {
                    value: 'InPps',
                    name: '入向包速率'
                }
            ]
        },
        packageOut: {
            title: '出向包速率',
            unit: '个',
            metrics: `OutPps(${'出向包速率'}`
        },
        ConnNumber: {
            title: '并发连接数',
            metrics: `ConnNumber(${'并发连接数'})`,
            unit: '个'
        }
    },
    peerconnMetrics: {
        bandwidthIn: {
            title: '入向带宽',
            unit: 'bps',
            bitUnit: 1000,
            metrics: [
                {
                    value: 'DetailWebInBitsPerSecond',
                    name: '入向带宽'
                }
            ]
        },
        bandwidthOut: {
            title: '出向带宽',
            unit: 'bps',
            bitUnit: 1000,
            metrics: [
                {
                    value: 'DetailWebOutBitsPerSecond',
                    name: '出向带宽'
                }
            ]
        },
        bytesIn: {
            title: '入向流量',
            statistics: 'sum',
            unit: '字节',
            metrics: [
                {
                    value: 'DetailWebInBytes',
                    name: '入向流量'
                }
            ]
        },
        bytesOut: {
            title: '出向流量',
            statistics: 'sum',
            unit: '字节',
            metrics: [
                {
                    value: 'DetailWebOutBytes',
                    name: '出向流量'
                }
            ]
        },
        packageIn: {
            title: '入向包速率',
            unit: '个',
            metrics: [
                {
                    value: 'DetailWebInPkgPerSecond',
                    name: '入向包速率'
                }
            ]
        },
        packageOut: {
            title: '出向包速率',
            unit: '个',
            metrics: [
                {
                    value: 'DetailWebOutPkgPerSecond',
                    name: '出向包速率'
                }
            ]
        }
    },
    Ipv6Metrics: {
        bandwidth: {
            title: 'IPv6公网网关带宽',
            unit: 'bps',
            bitUnit: 1000,
            metrics: [
                {
                    name: 'IPv6公网网关输入带宽',
                    value: 'IPV6GWInbps'
                },
                {
                    name: 'IPv6公网网关输出带宽',
                    value: 'IPV6GWOutbps'
                }
            ]
        },
        flow: {
            title: 'IPv6公网网关流量',
            unit: '字节',
            metrics: [
                {
                    name: 'IPv6公网网关输入流量',
                    value: 'IPV6GWInBytes'
                },
                {
                    name: 'IPv6公网网关输出流量',
                    value: 'IPV6GWOutBytes'
                }
            ]
        },
        package: {
            title: 'IPv6公网网关数据包',
            unit: '个',
            metrics: [
                {
                    name: 'IPv6公网网关输入数据包数',
                    value: 'IPV6GWInPkgCount'
                },
                {
                    name: 'IPv6公网网关输出数据包数',
                    value: 'IPV6GWOutPkgCount'
                }
            ]
        },
        used: {
            name: 'IPv6公网网关带宽使用率',
            unit: '%',
            metrics: [
                {
                    name: 'IPv6公网网关输入带宽使用率',
                    value: 'IPV6GWInBandwidthUsedPercent'
                },
                {
                    name: 'IPv6公网网关输出带宽使用率',
                    value: 'IPV6GWOutBandwidthUsedPercent'
                }
            ]
        },
        iplr: {
            name: 'IPv6公网网关丢包率',
            unit: '百分比',
            metrics: [
                {
                    name: 'IPv6公网网关输入丢包率',
                    value: 'IPV6GWInPacketLossPercent'
                },
                {
                    name: 'IPv6公网网关输出丢包率',
                    value: 'IPV6GWOutPacketLossPercent'
                }
            ]
        }
    },
    DcgwTopNMetrics: {
        bandwidthIn: {
            title: '入口带宽',
            unit: 'bps',
            bitUnit: 1000,
            metrics: [
                {
                    value: 'DetailWebInBitsPerSecond',
                    name: '入口带宽'
                }
            ]
        },
        bandwidthOut: {
            title: '出口带宽',
            unit: 'bps',
            bitUnit: 1000,
            metrics: [
                {
                    value: 'DetailWebOutBitsPerSecond',
                    name: '出口带宽'
                }
            ]
        },
        bytesIn: {
            title: '入口流量',
            statistics: 'sum',
            unit: '字节',
            metrics: [
                {
                    value: 'DetailWebInBytes',
                    name: '入口流量'
                }
            ]
        },
        bytesOut: {
            title: '出口流量',
            statistics: 'sum',
            unit: '字节',
            metrics: [
                {
                    value: 'DetailWebOutBytes',
                    name: '出口流量'
                }
            ]
        },
        packageIn: {
            title: '入口包速率',
            unit: '个',
            metrics: [
                {
                    value: 'DetailWebInPkgPerSecond',
                    name: '入向包速率'
                }
            ]
        },
        packageOut: {
            title: '出向包速率',
            unit: '个',
            metrics: `DetailWebOutPkgPerSecond(${'出向包速率'}`
        }
    },
    DcgwMetrics: {
        bandwidth: {
            title: '带宽',
            unit: 'bps',
            bitUnit: 1000,
            metrics: [
                {
                    name: '出口带宽',
                    value: 'WebOutBitsPerSecond'
                },
                {
                    name: '入口带宽',
                    value: 'WebInBitsPerSecond'
                }
            ]
        },
        flow: {
            title: '流量',
            unit: '字节',
            metrics: [
                {
                    name: '出口流量',
                    value: 'WebOutBytes'
                },
                {
                    name: '入口流量',
                    value: 'WebInBytes'
                }
            ]
        },
        package: {
            title: '包速率',
            unit: 'pps',
            metrics: [
                {
                    name: '出向包速率',
                    value: 'WebOutPkgPerSecond'
                },
                {
                    name: '入向包速率',
                    value: 'WebInPkgPerSecond'
                }
            ]
        },
        losePackage: {
            title: '限速丢包率',
            unit: '%',
            metrics: [
                {
                    name: '出向限速丢包率',
                    value: 'WebOutPkgDropPercent'
                }
            ]
        },
        limitSpeedBandwidth: {
            title: '限速丢包带宽',
            unit: 'bps',
            metrics: [
                {
                    name: '出向限速丢包带宽',
                    value: 'WebOutDropBitsPerSecond'
                }
            ]
        },
        limitSpeedRate: {
            title: '限速丢包速率',
            unit: 'pps',
            metrics: [
                {
                    name: '出向限速丢包速率',
                    value: 'WebOutDropPkgPerSecond'
                }
            ]
        }
    },
    peerMetrics: {
        bandwidth: {
            name: '带宽',
            unit: 'bps',
            bitUnit: 1000,
            metrics: [
                {
                    name: '出口带宽',
                    value: 'WebOutBitsPerSecond'
                },
                {
                    name: '入口带宽',
                    value: 'WebInBitsPerSecond'
                }
            ]
        },
        flow: {
            title: '流量',
            unit: '字节',
            metrics: [
                {
                    name: '出口流量',
                    value: 'WebOutBytes'
                },
                {
                    name: '入口流量',
                    value: 'WebInBytes'
                }
            ]
        },
        package: {
            title: '包速率',
            unit: 'pps',
            metrics: [
                {
                    name: '出向包速率',
                    value: 'WebOutPkgPerSecond'
                },
                {
                    name: '入向包速率',
                    value: 'WebInPkgPerSecond'
                }
            ]
        },
        used: {
            name: '带宽使用率',
            unit: '%',
            metrics: [
                {
                    name: '入向带宽使用率',
                    value: 'WebInUtilityRate'
                },
                {
                    name: '出向带宽使用率',
                    value: 'WebOutUtilityRate'
                }
            ]
        },
        losePackage: {
            name: '限速丢包率',
            unit: '%',
            metrics: [
                {
                    name: '出向限速丢包率',
                    value: 'WebOutPkgDropPercent'
                }
            ]
        },
        limitSpeedBandwidth: {
            name: '限速丢包带宽',
            unit: 'bps',
            metrics: [
                {
                    name: '出向限速丢包带宽',
                    value: 'WebOutDropBitsPerSecond'
                }
            ]
        },
        limitSpeedRate: {
            name: '限速丢包速率',
            unit: 'pps',
            metrics: [
                {
                    name: '出向限速丢包速率',
                    value: 'WebOutDropPkgPerSecond'
                }
            ]
        }
    },
    monitorMetric: {
        bandwidth: {
            name: '带宽',
            unit: 'bps',
            bitUnit: 1000,
            metrics: [
                {
                    name: '入口带宽',
                    value: 'RXbps'
                },
                {
                    name: '出口带宽',
                    value: 'TXbps'
                }
            ]
        },
        flow: {
            name: '流量',
            unit: '字节',
            metrics: [
                {
                    name: '入口流量',
                    value: 'WebInBytes'
                },
                {
                    name: '出口流量',
                    value: 'WebOutBytes'
                }
            ]
        },
        package: {
            name: '包速率',
            unit: 'pps',
            metrics: [
                {
                    name: '出向包速率',
                    value: 'WebOutPkgPerSecond'
                },
                {
                    name: '入向包速率',
                    value: 'WebInPkgPerSecond'
                }
            ]
        },
        porterr: {
            name: '端口错包',
            unit: '个/minute',
            metrics: [
                {
                    name: '出向端口错包',
                    value: 'TXErrPkts'
                },
                {
                    name: '入向端口错包',
                    value: 'RXErrPkts'
                }
            ]
        },
        lossPkg: {
            name: '丢包数',
            unit: '个/minute'
        }
    },
    dcgwMonitorMetric: {
        bandwidth: {
            name: '带宽',
            unit: 'bps',
            bitUnit: 1000,
            metrics: [
                {
                    name: '出口带宽',
                    value: 'WebOutBitsPerSecond'
                },
                {
                    name: '入口带宽',
                    value: 'WebInBitsPerSecond'
                }
            ]
        },
        flow: {
            name: '流量',
            unit: '字节',
            metrics: [
                {
                    name: '出口流量',
                    value: 'WebOutBytes'
                },
                {
                    name: '入口流量',
                    value: 'WebInBytes'
                }
            ]
        },
        package: {
            name: '包速率',
            unit: 'pps',
            metrics: [
                {
                    name: '出向包速率',
                    value: 'WebOutPkgPerSecond'
                },
                {
                    name: '入向包速率',
                    value: 'WebInPkgPerSecond'
                }
            ]
        },
        limitSpeedBandwidth: {
            name: '限速丢包带宽',
            unit: 'bps',
            metrics: [
                {
                    name: '出向限速丢包带宽',
                    value: 'WebOutDropBitsPerSecond'
                }
            ]
        },
        limitSpeedRate: {
            name: '限速丢包速率',
            unit: 'pps',
            metrics: [
                {
                    name: '出向限速丢包速率',
                    value: 'WebOutDropPkgPerSecond'
                }
            ]
        },
        iplr: {
            name: '限速丢包率',
            unit: '百分比',
            metrics: [
                {
                    name: '出向限速丢包率',
                    value: 'WebOutPkgDropPercent'
                },
                {
                    name: '入向丢包率',
                    value: 'WebInPkgDropPercent'
                }
            ]
        }
    },
    l2gwMetric: {
        bytes: {
            title: '网络带宽',
            unit: 'Bps',
            bitUnit: 1000,
            metrics: [
                {
                    name: '入向带宽',
                    value: 'InBps'
                },
                {
                    name: '出向带宽',
                    value: 'OutBps'
                }
            ]
        },
        packets: {
            title: '包速率',
            unit: 'pps',
            metrics: [
                {
                    name: '入向包速率',
                    value: 'InPps'
                },
                {
                    name: '出向包速率',
                    value: 'OutPps'
                }
            ]
        }
    }
};
