/**
 * 校验规则
 */

import {convertCidrToBinary} from './common';

export default {
    FILTER: {
        SECURITY: {
            NAME: {name: '安全组名称', value: 'SECURITY_NAME'},
            ID: {name: '安全组ID', value: 'SECURITY_ID'}
        }
    },
    DETAIL_EDIT: {
        NAME: {
            placeholder: '大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65',
            required: true,
            requiredErrorMessage: '名称必填',
            /* eslint-disable */
            pattern: /^[a-zA-Z][\w\-\_\/\.]{0,64}$/,
            /* eslint-enable */
            patternErrorMessage: '支持大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65'
        },
        DESC: {
            placeholder: '描述不能超过200个字符',
            maxLength: 200,
            maxLengthErrorMessage: '描述不能超过200个字符'
        }
    },
    NAME_SUPPORT_CHINESE: {
        placeholder: '大小写字母、数字，中文以及-_/.特殊字符，必须以字母、中文开头，但不能以default开头，长度1-65',
        required: 'required',
        requiredErrorMessage: '名称必填',
        /* eslint-disable */
        pattern: /^(?!default)[a-zA-Z\u4e00-\u9fa5][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/,
        /* eslint-enable */
        patternErrorMessage:
            '名称不符合规则：大小写字母、数字，中文以及-_/.特殊字符，必须以字母、中文开头，但不能以default开头，长度1-65'
    },
    SECURITY_DESCRIPTION: {
        placeholder: '最多200个字符',
        pattern: /^.{0,200}$/,
        patternErrorMessage: '最多200个字符'
    },
    VPN: {
        NAME: {
            placeholder: '大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65',
            required: true,
            requiredErrorMessage: '名称必填',
            /* eslint-disable */
            pattern: /^[a-zA-Z][\w\-\_\/\.]{0,64}$/,
            /* eslint-enable */
            patternErrorMessage: '支持大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65'
        },
        SECURITY_KEY: {
            placeholder: '8～17位字符，英文、数字和符号必须同时存在，符号仅限!@#$%^*(_',
            required: true,
            requiredErrorMessage: '共享密钥必填',
            custom(value) {
                let length = value.length;
                let valid =
                    length > 7 &&
                    length < 18 &&
                    /* eslint-disable */
                    /[a-zA-Z]/.test(value) &&
                    /\d/.test(value) &&
                    /[!@#$%^*()_]/.test(value) &&
                    /^[a-zA-Z\d!@#$%^*()_]+$/.test(value);
                /* eslint-enable */
                return valid;
            },
            customErrorMessage: '格式不正确'
        },
        DESC: {
            placeholder: '描述不能超过200个字符',
            maxLength: 200
        } // 描述的规则应同NAT
    },
    NAT: {
        NAME: {
            placeholder: '大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65',
            required: true,
            requiredErrorMessage: '名称必填',
            /* eslint-disable */
            pattern: /^[a-zA-Z][\w\-\_\/\.]{0,64}$/,
            /* eslint-enable */
            patternErrorMessage: '支持大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65'
        },
        DESC: {
            placeholder: '描述不能超过200个字符',
            maxLength: 200,
            maxLengthErrorMessage: '描述不能超过200个字符'
        }
    },
    DCGW: {
        NAME: {
            placeholder: '大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65',
            required: true,
            requiredErrorMessage: '名称必填',
            /* eslint-disable */
            pattern: /^[a-zA-Z][\w\-\_\/\.]{0,64}$/,
            /* eslint-enable */
            patternErrorMessage: '支持大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65'
        }
    },
    PEERCONN: {
        NAME: {
            placeholder: '以字母开头，支持大小写字母、数字以及 -_/. 特殊字符',
            /* eslint-disable */
            pattern: /^[a-zA-Z][\w\-\_\/\.]{0,64}$/,
            /* eslint-enable */
            patternErrorMessage: '以字母开头，支持大小写字母、数字以及 -_/. 特殊字符'
        }
    },
    DC: {
        NAME: {
            placeholder: '大小写字母、数字以及-_ /.特殊字符，必须以字母开头，长度1-65',
            required: true,
            requiredErrorMessage: '名称必填',
            pattern: /^[a-zA-Z][\w\-\_\/\.]{0,64}$/,
            patternErrorMessage: '名称不符合规则',
            custom(value) {
                return value !== 'default';
            },
            customErrorMessage: '专线名称不能是default'
        },
        LOCATION: {
            required: true,
            requiredErrorMessage: '请填写您的详细地址'
        },
        USERNAME: {
            required: true,
            requiredErrorMessage: '联系人姓名必填'
        },
        MOBILE: {
            required: true,
            requiredErrorMessage: '联系人手机必填',
            pattern: /^(13[0-9]|14[5-9]|15[012356789]|166|17[0-8]|18[0-9]|19[8-9])[0-9]{8}$/,
            patternErrorMessage: '手机号格式不正确'
        },
        EMAIL: {
            required: true,
            requiredErrorMessage: '联系人邮箱必填',
            pattern: /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/,
            patternErrorMessage: '邮箱格式不正确'
        }
    },
    CHANNEL: {
        USER_ID: {
            required: true,
            requiredErrorMessage: '请输入账户ID',
            pattern: /^[\da-zA-Z]+$/,
            patternErrorMessage: '请输入正确的账户ID'
        },
        IP_H: {
            custom(value, mask) {
                let reg = /^[0-9]\d*$/;
                mask = Number(mask);
                // 掩码为31特殊处理
                if (mask === 31) {
                    return value > -1 && value < 256 && reg.test(value);
                } else {
                    return value > 0 && value < 256 && reg.test(value);
                }
            },
            required: true,
            requiredErrorMessage: '请输入正确的ip地址',
            customErrorMessage: '请输入正确的ip地址',
            repeatMessage: '互联IP不能重复',
            rangeMessage: '请输入范围内的ip地址'
        },
        IP_M: {
            custom(value) {
                let reg = /^[0-9]\d*$/;
                return value > -1 && value < 256 && reg.test(value);
            },
            required: true,
            requiredErrorMessage: '请输入正确的ip地址',
            customErrorMessage: '请输入正确的ip地址'
        },
        NETWORK: {
            required: true,
            requiredErrorMessage: '不能为空',
            custom(value) {
                let valueString = convertCidrToBinary(value);
                let valueMask = value.split('/')[1];

                if (valueString.substring(+valueMask, valueString.length).indexOf('1') > -1) {
                    return false;
                }
                return true;
            },
            customErrorMessage: '路由参数格式不合法',
            pattern: new RegExp(
                '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}' +
                    '([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\\/(\\d|[1-2]\\d|3[0-2]))?$'
            ),
            patternErrorMessage: '路由参数不符合规则'
        },
        IP_CIDR:
            '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}' +
            '([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\\/(\\d|[1-2]\\d|3[0-2]))?$'
    },
    /* eslint-disable */
    DOMAIN: /(\*|[a-zA-Z0-9\u4e00-\u9fa5][-a-zA-Z0-9\u4e00-\u9fa5]{0,62})(\.[a-zA-Z0-9\u4e00-\u9fa5][-a-zA-Z0-9\u4e00-\u9fa5]{0,62})+\.?/i,
    IP: /^(([01]?\d?\d|2[0-4]\d|25[0-5])\.){3}([01]?\d?\d|2[0-4]\d|25[0-5])$/,
    SEG: /^(([01]?\d?\d|2[0-4]\d|25[0-5])\.){3}([01]?\d?\d|2[0-4]\d|25[0-5])\/(\d{1}|[0-2]{1}\d{1}|3[0-2])$/,
    IP_CIDR:
        '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}' +
        '([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\\/(\\d|[1-2]\\d|3[0-2]))?$',
    VPN_IP_CIDR:
        '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([1-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-4])(\\/(\\d|[1-2]\\d|3[0-2]))?$',
    VPN_IP_CIDR_UNLIMIT:
        '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\\/(\\d|[1-2]\\d|3[0-2]))?$',
    IPV6: /^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/,
    IPV6_SEG:
        /^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*\/(\d{1}|[0-9]{1}\d{1}|3[0-2]|1[01]\d|12[0-8])$/,
    DomainRule: /^(\*|[a-zA-Z0-9][-a-zA-Z0-9]{0,62})(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?$/,
    Sharedkey: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d!@#$%^*()_.]{8,17}$/, //数字、英文字符必须同时存在，可输特殊字符且8-17位
    qosQueueService: /^([0-9]|([1-5][0-9]{1})|(6[0-3]))$/,
    DNAT_INTERNALIP:
        '^(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\\.((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\\.)' +
        '{2}(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])$'
    /* eslint-enable */
};
