import {router} from 'san-router';
import Client from '../common/client';
import Assist from './assist';

const $http = new Client();
export default () => {
    window.addEventListener('hashchange', () => {
        // 针对nat、vpn、对等连接的直接访问路由做重定向
        if (
            /^#\/vpc\/peerconn/.test(location.hash) &&
            !window.$storage.get('peerConnSts') &&
            location.hash !== '#/vpc/peerconn/auth'
        ) {
            location.hash = '#/vpc/peerconn/auth';
        }
        if (
            (/^#\/vpc\/vpn/.test(location.hash) ||
                /^#\/vpc\/sslvpn/.test(location.hash) ||
                /^#\/vpc\/grevpn/.test(location.hash)) &&
            !window.$storage.get('vpnSts') &&
            location.hash !== '#/vpc/vpn/auth'
        ) {
            location.hash = '#/vpc/vpn/auth';
        }
        if (
            /^#\/vpc\/nat/.test(location.hash) &&
            !window.$storage.get('natSts') &&
            location.hash !== '#/vpc/nat/auth'
        ) {
            location.hash = '#/vpc/nat/auth';
        }
    });
    return router.listen((e, config) => {
        // 在路由发生变化时触发
        if (/^\/dc/.test(e.path)) {
            window.$framework.events.fire(window.$framework.EVENTS.ENTER_ACTION_COMPLETE, {
                region: {
                    id: null,
                    globalOnly: true
                }
            });
        }
        if (
            (/^\/dc\/in/.test(e.referrer) || /^\/dc\/chan/.test(e.referrer) || /^\/dc\/landing/.test(e.referrer)) &&
            !/^\/dc\/in/.test(e.path) &&
            !/^\/dc\/landing/.test(e.path) &&
            !/^\/dc\/chan/.test(e.path)
        ) {
            location.reload();
        }
        if (/^\/dc\/instance\/create/.test(e.path)) {
            $http
                .dcIsEnterprise()
                .then(res => {
                    if (!res.enterprise) {
                        location.hash = '#/dc/instance/list';
                    }
                })
                .catch(() => {
                    location.hash = '#/dc/instance/list';
                });
        }
        try {
            Assist.onCloseAIAssist();
            if (/^\/vpc\/subnet/.test(e.path)) {
                Assist.init('subnet_console');
            } else if (/^\/vpc\/route/.test(e.path)) {
                Assist.init('route_console');
            } else if (/^\/vpc\/eni/.test(e.path)) {
                Assist.init('snic_console');
            } else if (/^\/vpc\/endpoint/.test(e.path)) {
                Assist.init('enic_console');
            } else if (/^\/vpc\/havip/.test(e.path)) {
                Assist.init('havip_console');
            } else if (/^\/vpc\/security/.test(e.path)) {
                Assist.init('security_console');
            } else if (/^\/vpc\/acl/.test(e.path)) {
                Assist.init('acl_console');
            } else if (/^\/vpc\/set/.test(e.path) || /^\/vpc\/group/.test(e.path) || /^\/vpc\/param/.test(e.path)) {
                Assist.init('param_console');
            } else if (/^\/vpc\/nat/.test(e.path)) {
                Assist.init('nat_console');
            } else if (/^\/vpc\/ipv6gw/.test(e.path)) {
                Assist.init('ipv6gw_console');
            } else if (/^\/vpc\/vpn/.test(e.path) || /^\/vpc\/grevpn/.test(e.path) || /^\/vpc\/sslvpn/.test(e.path)) {
                Assist.init('vpn_console');
            } else if (/^\/vpc\/peerconn/.test(e.path)) {
                Assist.init('peerconn_console');
            } else if (/^\/vpc\/dcgw/.test(e.path)) {
                Assist.init('dcgw_console');
            } else if (/^\/vpc\/ipv6gw/.test(e.path)) {
                Assist.init('ipv6gw_console');
            } else if (/^\/vpc\/flowlog/.test(e.path)) {
                Assist.init('flowlog_console');
            } else if (/^\/vpc\/gateway/.test(e.path)) {
                Assist.init('gateway_console');
            } else if (/^\/vpc\/mirror/.test(e.path) || /^\/vpc\/filterRuleGroup/.test(e.path)) {
                Assist.init('flowmirror_console');
            } else if (/^\/vpc\/probe/.test(e.path)) {
                Assist.init('probe_console');
            } else if (/^\/vpc\/pathanalise/.test(e.path)) {
                Assist.init('path_console');
            } else if (/^\/vpc\/l2gw/.test(e.path)) {
                Assist.init('l2gw_console');
            } else if (/^\/dc\/in/.test(e.path) || /^\/dc\/landing/.test(e.path)) {
                Assist.init('et_console');
            } else if (/^\/dc\/chan/.test(e.path)) {
                Assist.init('channel_console');
            } else {
                Assist.init();
            }
        } catch (error) {}
    });
};
