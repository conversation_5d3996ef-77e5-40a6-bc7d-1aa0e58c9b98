/**
 * @file  mtools.js
 *
 * <AUTHOR>
 */
import _ from 'lodash';
import moment from 'moment';
import formatter from './formatter';
import * as echarts from 'echarts';
export const chartTheme = [
    '#4aaaff', '#f2605d', '#01B09B', '#E74684',
    '#6EC50F', '#FE863D',
    '#A45BFF', '#F6D622', '#0AC1D7', '#B569D4'
];
export const defaultChartOptions = {
    color: chartTheme,
    calculable: false,
    animation: false,
    grid: {
        x: 65,
        x2: 35,
        y: 50,
        y2: /^3\./.test(echarts.version) ? 60 : 50
    }
};
export const statisticsMap = {
    average: '平均值',
    maximum: '最大值',
    minimum: '最小值',
    sum: '和值',
    sampleCount: '样本数'
};

/**
 * 将utc时间转换为本地时间
 *
 * @param {number} time 时间戳
 * @param {string} pattern 格式化字符串
 * @return {string}
 */
export const utc2local = (time, pattern) => {
    pattern = pattern || 'MM-DD HH:mm';
    return moment(time).format(pattern);
};

/**
 * 将本地时间戳转换为utc时间字符串
 *
 * @param {number} time 时间戳
 * @param {string} pattern 格式化字符串
 * @return {string}
 */
export const local2utc = (time, pattern) => {
    pattern = pattern || 'YYYY-MM-DDTHH:mm:ss';
    return moment(time).utc().format(pattern) + 'Z';
};

/**
 * 获取当前的utc时间区间
 *
 * @param {string} timeRange 时间段标识
 * @param {number} period 时间区间
 * @return {Object}
 */
export const getUTCTimeRange = (timeRange, period) => {
    var mt = timeRange.match(/(\d+)(\w+)/);
    var value = mt[1];
    var unit = mt[2];
    var m = moment();
    var endTime = local2utc(m);
    var startTime = '';
    if (arguments.length === 1) {
        // 格式化时间：秒为0，起始时间加1分钟，解决时间区间超过范围问题
        startTime = local2utc(m.add('m', 1).subtract(unit, value), 'YYYY-MM-DDTHH:mm:00');
    }
    else {
        if (period < 60 * 60) {
            startTime = local2utc(moment(m.add('m', 1).subtract(unit, value).format('YYYY-MM-DDTHH:mm:00')));
        }
        else if (period < 60 * 60 * 24) {
            startTime = local2utc(moment(m.add('h', 1).subtract(unit, value).format('YYYY-MM-DDTHH:00:00')));
        }
        else {
            startTime = local2utc(moment(m.add('d', 1).subtract(unit, value).format('YYYY-MM-DDT00:00:00')));
        }
    }
    return {
        endTime: endTime,
        startTime: startTime
    };
};

/**
 * 转换数据为按图分组
 *
 * @param {Array} metrics 监控项列表
 * @return {Object}
 */
export const adjustToChartMetric = (metrics) => {
    var chartMetric = {};
    var defaultStatistics = 'average';
    _.each(metrics, function (item, key) {
        chartMetric[key] = {
            metrics: [],
            unit: item.unit,
            names: {},
            nullPointMode: item.nullPointMode || null, // 可选值： 0， null(显示为 '-')
            statistics: item.statistics || defaultStatistics
        };
        item.chartType && (chartMetric[key].type = item.chartType);
        _.each(item.metrics, function (metric, i) {
            var value = metric.value;
            chartMetric[key].metrics.push(value);
            chartMetric[key].names[value] = metric.name;
        });
    });
    return chartMetric;
};

/**
 * 数据转换，只获取data中属性为key的数值
 *
 * @param {Object} result 目标对象
 * @param {string} key 过滤值
 * @return {*}
 */
export const adjustSeriesData = (result, key) => {
    _.each(result.series, function (item) {
        item.data = _.pluck(item.data, key);
    });
    return result;
};

/**
 * 与默认配置进行合并，得到最终的配置信息
 *
 * @param {Object} opt 扩充对象
 * @return {Object} 最终配置
 */
export const mergeChartOptions = (opt) => {
    return _.extend({}, defaultChartOptions, opt);
};

/**
 * 对目标字符串按gbk编码截取字节长度
 *
 * @param {string} source 目标字符串
 * @param {number} length 需要截取的字节长度
 * @param {string} [tail] 追加字符串,可选.
 * @return {string}
 */
export const truncate = (source, length, tail) => {
    source = String(source);
    tail = tail || '';
    var byteLength = source.replace('/[^\x00-\xff]/g', '**').length;
    if (length < 0 || byteLength <= length) {
        return source;
    }
    length = length - 2;
    source = source.substr(0, length).replace(/([^\x00-\xff])/g, '\x241 ') // 双字节字符替换成两个
        .substr(0, length) // 截取长度
        .replace(/[^\x00-\xff]$/, '') // 去掉临界双字节字符
        .replace(/([^\x00-\xff]) /g, '\x241'); // 还原
    return source + tail;
};

/**
 * 深度扩展
 *
 * @param {Object} src 目标对象
 * @param {Object} opt 扩充对象
 */
export const deepExtend = (src, opt) => {
    _.each(opt, function (item, key) {
        if (_.isArray(item)) {
            deepExtend(src[key] = src[key] || [], item);
        }
        else if (_.isObject(item)) {
            deepExtend(src[key] = src[key] || {}, item);
        }
        else {
            src[key] = item;
        }
    });
};

/**
 * 将后端返回的数据，转换为echarts可以使用的配置信息
 *
 * @param {Object} data
 *  {series:[{name:'监控项名称', data:[数据点]}], category:[]}
 * @param {Object} metric 监控项配置
 *  - metric.unit {String}
 *  - metric.statistics {String}
 *  - metric.names {Object}
 *  - metric.names[value] = name
 *  - metric.metrics {Array}
 *  - metric.src 针对枚举类型的数值
 * @param {Object} opt
 *  - opt.type 图表类型line, bar, pie ...
 *  - opt.chart 图表特殊配置
 * @param {Object} addition 附加条件
 * @return {Object}
 */
export const getChartOptions = (data, metric, opt, addition) => {
    var category = [];
    var unit = metric.unit;
    var zoomStart = 0;
    var legend = [];
    var tmpData = {};
    var seriesOpt = {};
    var seriesData = [];
    var connectNulls = metric.connectNulls || false;
    opt = opt || {
        type: 'line'
    };
    if (opt.type === 'line') {
        seriesOpt = {
            type: 'line',
            smooth: true,
            symbol: 'none',
            symbolSize: 2,
            showAllSymbol: true,
            itemStyle: {
                borderWidth: 1,
                normal: {
                    // areaStyle: {
                    //     type: 'default'
                    // },
                    lineStyle: {
                        width: 1
                    }
                }
            }
        };
    }
    else if (opt.type === 'bar') {
        seriesOpt = {
            type: 'bar'
        };
    }
    seriesOpt = _.extend(seriesOpt, opt.chart || {});
    var spliteNumber = 5;
    var yAxisFormatter;
    var yAxisMax;
    var tooltipFormatter = function (params) {
        var arr = [];
        if (params.length > 0) {
            arr.push(params[0].name + ' (' + statisticsMap[metric.statistics] + ')');
        }
        _.each(params, function (item) {
            var label = truncate(item.seriesName, 22, '…');
            arr.push(label + '：' + item.value);
        });
        return arr.join('<br/>');
    };
    if (data.category.length === 0) {
        category.push(utc2local()); // .replace(' ', '\n'));
        category.push(utc2local()); // .replace(' ', '\n'));
    }
    else {
        var pattern = 'MM-DD HH:mm';
        if (addition && addition.period) {
            if (addition.period >= 60 * 60 * 24) {
                pattern = 'MM-DD';
            }
            // 精确到毫秒的格式
            if (addition.period < 1) {
                pattern = 'MM-DD HH:mm:ss.SSS';
            }
        }
        _.each(data.category, function (item) {
            category.push(utc2local(item, pattern));
        });
    }
    if (metric.nullPointMode === 0) {
        _.each(data.series, function (item) {
            var values = [];
            _.each(item.data, function (value) {
                if (typeof value === 'undefined') {
                    value = 0;
                }
                values.push(value);
            });
            if (values.length === 0) {
                for (var i = 0, len = category.length; i < len; i++) {
                    values[i] = 0;
                }
            }
            tmpData[item.name] = values;
        });
    }
    else {
        _.each(data.series, function (item) {
            var values = [];
            var tmpvalue = '';
            _.each(item.data, function (value) {
                if (typeof value === 'undefined') {
                    value = '-';
                }
                // 对于孤立数据点，以圆点的方式展示
                if (tmpvalue === '-' && value !== '-') {
                    values.push({
                        value: value,
                        symbol: 'emptyCircle'
                    });
                }
                else {
                    values.push(value);
                    if (value !== '-' && values.length > 1) {
                        values[values.length - 2] = tmpvalue;
                    }
                }
                tmpvalue = value;
            });
            if (values.length === 0) {
                for (var i = 0, len = category.length; i < len; i++) {
                    values[i] = '-';
                }
            }
            tmpData[item.name] = values;
        });
    }
    if (addition && addition.dataZoom) {
        zoomStart = addition.dataZoom.start;
    }
    else {
        zoomStart = Math.max((1 - 100 / category.length) * 100, 0);
    }
    var metricNames = {};
    _.each(metric.metrics, function (item, key) {
        metricNames[item.value] = item.name;
    });
    _.each(tmpData, function (item, key) {
        var name = metricNames[key] || key;
        var obj = $.extend(true, {}, seriesOpt, {
            name: name,
            data: item,
            connectNulls: connectNulls
        });
        legend.push(name);
        seriesData.push(obj);
    });
    if (metric.statistics === 'sampleCount') {
        unit = '个';
        tooltipFormatter = function (params) {
            var str = '';
            if (params.length > 0) {
                str += params[0].name + ' (' + statisticsMap[metric.statistics] + ')<br/>';
            }
            _.each(params, function (item, index) {
                str += truncate(item.seriesName, 22, '…') + '：';
                var value = item.value;
                if (isNaN(value)) {
                    value = 0;
                }
                str += value + '<br/>';
            });
            return str;
        };
    }
    // 状态的监控项需对返回值映射成文本
    else if (unit === 'enum') {
        unit = '';
        var enums = metric.src || {};
        var count = enums.value().length;
        if (count < 6) {
            spliteNumber = count - 1;
        }
        tooltipFormatter = function (params) {
            var str = '';
            if (params.length > 0) {
                str += params[0].name;
            }
            str += ' (' + statisticsMap[metric.statistics] + ')';
            _.each(params, function (item, index) {
                var value = item.value;
                str += '<br/>' + truncate(item.seriesName, 22, '…') + '：' + value;
                if (value !== '-') {
                    str += '（' + (enums[value] || value) + '）';
                }
            });
            return str;
        };
    }
    else if (unit === '字节' || unit === '字节/秒') {
        var suffix = (unit === '字节' ? '' : '/s');
        tooltipFormatter = function (params) {
            var str = '';
            if (params.length > 0) {
                str += params[0].name + ' (' + statisticsMap[metric.statistics] + ')<br/>';
            }
            _.each(params, function (item, index) {
                str += truncate(item.seriesName, 22, '…') + '：';
                var value = item.value;
                var prefix;
                var valueStr = '';
                if (isNaN(value)) {
                    valueStr = '-';
                }
                else {
                    prefix = value < 0 ? '-' : '';
                    value = Math.abs(value);
                    value = formatter.bytes(value, 2, metric.byteUnit);
                    valueStr = prefix + value + suffix;
                }
                str += valueStr + '<br/>';
            });
            return str;
        };
        yAxisFormatter = function (value) {
            var prefix = value < 0 ? '-' : '';
            value = Math.abs(value);
            value = formatter.bytes(value, 1, metric.byteUnit);
            return prefix + value + suffix;
        };
    }
    else if (unit === 'bps') {
        tooltipFormatter = function (params) {
            var str = '';
            if (params.length > 0) {
                str += params[0].name + ' (' + statisticsMap[metric.statistics] + ')<br/>';
            }
            _.each(params, function (item, index) {
                str += truncate(item.seriesName, 22, '…') + '：';
                var value = item.value;
                var prefix;
                var valueStr = '';
                if (isNaN(value)) {
                    valueStr = '-';
                }
                else {
                    prefix = value < 0 ? '-' : '';
                    value = Math.abs(value);
                    value = formatter.bits(value, 2, metric.bitUnit).toUpperCase();
                    valueStr = prefix + value + unit;
                }
                str += valueStr + '<br/>';
            });
            return str;
        };
        yAxisFormatter = function (value) {
            var prefix = value < 0 ? '-' : '';
            value = Math.abs(value);
            // 保留1位小数的话
            // 当纵坐标是0 0.125 0.15 0.175 0.2的时候
            // 就会出现三个0.1
            value = formatter.bits(value, 2, metric.bitUnit).toUpperCase();
            return prefix + value;
        };
    }
    else if (unit === '百分比') {
        tooltipFormatter = function (params) {
            var str = '';
            if (params.length > 0) {
                str += params[0].name + ' (' + statisticsMap[metric.statistics] + ')<br/>';
            }
            _.each(params, function (item, index) {
                str += truncate(item.seriesName, 22, '…')
                    + '：' + item.value + (item.value === '-' ? '' : '%') + '<br/>';
            });
            return str;
        };
        yAxisFormatter = '{value}%';
        yAxisMax = 'yAxisMax' in addition ? addition.yAxisMax : 100;
    }
    else if (formatter.isByteUnit(unit)) {
        tooltipFormatter = function (params) {
            var str = '';
            if (params.length > 0) {
                str += params[0].name + ' (' + statisticsMap[metric.statistics] + ')<br/>';
            }
            _.each(params, function (item, index) {
                str += truncate(item.seriesName, 22, '…') + '：';
                var value = item.value;
                var prefix;
                var valueStr = '';
                if (isNaN(value)) {
                    valueStr = '-';
                }
                else {
                    prefix = value < 0 ? '-' : '';
                    value = Math.abs(value);
                    value = formatter.bytes(value, 2, metric.byteUnit, unit);
                    valueStr = prefix + value;
                }
                str += valueStr + '<br/>';
            });
            return str;
        };
    }
    else {
        tooltipFormatter = function (params) {
            var str = '';
            if (params.length > 0) {
                str = params[0].name + ' (' + statisticsMap[metric.statistics] + ')<br/>';
            }
            _.each(params, function (item) {
                str += truncate(item.seriesName, 22, '…') + '：';
                var value = item.value;
                var valueStr = '';
                if (isNaN(value)) {
                    valueStr = '-';
                }
                else {
                    valueStr = item.value;
                }
                str += valueStr + '<br/>';
            });
            return str;
        };
        yAxisFormatter = function (value) {
            return formatter.number(value, 0);
        };
    }

    /**
     * 2019-02-15 huangyunzhi
     *
     * 支持多行legend显示，用于解决一行显示不下的问题
     * 通过添加 addition.maxLegendPerLine 来控制一行显示legend个数
     */
    var multiLegend = [];
    var commonLegend = {
        x: 'center',
        padding: 2,
        itemGap: 2
    };
    if (addition && addition.maxLegendPerLine) {
        var lpr = addition.maxLegendPerLine;
        for (var i = 0; i < legend.length / lpr; i ++) {
            var perLegend = {data: legend.slice(i * lpr, (i + 1) * lpr)};
            deepExtend(perLegend, commonLegend);
            if (i !== 0) {
                perLegend.top = '20';
            }
            multiLegend.push(perLegend);
        }
    }
    else {
        multiLegend.push(_.extend(commonLegend, {data: legend}));
    }
    var opts = {
        legend: multiLegend,
        tooltip: {
            trigger: 'axis',
            formatter: tooltipFormatter,
            textStyle: {
                fontSize: 12
            }
        },
        dataZoom: {
            start: zoomStart,
            show: true,
            height: 20,
            filterColor: 'rgba(74,170,255, 0.3)'
        },
        xAxis: [
            {
                type: 'category',
                boundaryGap: opt.type === 'bar',
                data: category,
                splitLine: {
                    lineStyle: {
                        color: '#eee'
                    }
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: unit || '',
                max: yAxisMax,
                splitNumber: spliteNumber,
                axisLabel: {
                    formatter: yAxisFormatter
                },
                splitLine: {
                    lineStyle: {
                        color: '#eee'
                    }
                }
            }
        ],
        series: seriesData
    };
    var forceOpt = opt.chartOptions || {};
    deepExtend(opts, forceOpt);
    return mergeChartOptions(opts);
};
