/**
 * @file sdk.js sdk
 *
 * <AUTHOR>
 */

// import {TagSDK} from '@baidu/bce-tag-sdk';
// import {BillingSDK} from '@baiducloud/billing-sdk';
import {HttpClient} from '@baiducloud/runtime';
import {ResourceGroupSDK} from '@baidu/bce-resourcegroup-sdk';

const contextPipe = window.$context;

export const getSdk = (name, options = {}) => {
    switch (name) {
        // case 'TAG':
        //     return new TagSDK(options, contextPipe);
        // case 'BILLING':
        //     return new BillingSDK(options, contextPipe);
        case 'RESOURCE':
            return new ResourceGroupSDK(new HttpClient(), contextPipe);
        default:
            return null;
    }
};
