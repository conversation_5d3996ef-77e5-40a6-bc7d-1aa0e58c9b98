export const StsConfig = {
    VPN: {
        roleName: 'BceServiceRole_VPN',
        sandbox: {
            policyId: '********************************',
            serviceId: 'c631091db99148058e2e0c1efc4dd44e'
        },
        online: {
            policyId: 'a3c43f4ae4654a50a38c5644e2c9a6b7',
            serviceId: '5abee8fb4bcf427ca5a5fd8162157631'
        }
    },
    NAT: {
        roleName: 'BceServiceRole_NETWORK_GATEWAYS',
        sandbox: {
            policyId: '********************************',
            serviceId: '1684a0cd21364600a774adb00c763454'
        },
        online: {
            policyId: '41e8221b50754ecda5093af80ca7a55a',
            serviceId: '585a00df2ada40afb3d643d1648aecde'
        }
    },
    PEERCONN: {
        roleName: 'BceServiceRole_PeerConn',
        sandbox: {
            policyId: '********************************',
            serviceId: '3adbb59c10f545db890b2bb3f4e3cad5'
        },
        online: {
            policyId: '0212647004ee4435ba2d572e79bb2289',
            serviceId: 'fb5d3910e65d487fa64b372ebd324cd6'
        }
    },
    DCGW: {
        roleName: 'BceServiceRole_et',
        sandbox: {
            policyId: '********************************',
            serviceId: 'fd29883c10fc42ba81ea97b7dcc196d8'
        },
        online: {
            policyId: 'dac940325ce24f07b56e4313d1d20537',
            serviceId: 'e1c510f3d3234ec3a9ca939c02fdec41'
        }
    },
    DNS: {
        roleName: 'BceServiceRole_console_dns',
        sandbox: {
            policyId: '********************************',
            serviceId: '50030bc17a814d148ff0ba2a92defbfc'
        },
        online: {
            policyId: 'ed04c83691c942d3acf674ee3bd6df89',
            serviceId: '11103e2df69c4d86b36f66f4c5e91913'
        }
    },
    CSN: {
        roleName: 'BceServiceRole_logical_csn',
        sandbox: {
            policyId: '********************************',
            serviceId: 'ac8b23565e114f9ba9af3cdb69a2301d'
        },
        online: {
            policyId: 'ba206b851faa472b85b4daa79e0fff00',
            serviceId: 'ae41f5ed92d04c59951b98570f92b316'
        }
    },
    BLB: {
        roleName: 'BceServiceRole_BLB'
    },
    L2GW: {
        roleName: 'BceServiceRole_ltgw',
        sandbox: {
            policyId: '********************************',
            serviceId: 'dfa76c63da1f48139201e06cad07a0ff'
        },
        online: {
            policyId: 'c1057f4c387745ecb5ed39f9202ae153',
            serviceId: 'a8def5e85b4f4b8b8b5bde46ef85ed0a'
        }
    },
    FLOWLOG: {
        roleName: 'BceServiceRole_KAFKA',
        sandbox: {
            policyId: '********************************',
            serviceId: 'f7ba5aacef7e46b380b21b18d5ace9fb'
        },
        online: {
            policyId: '********************************',
            serviceId: '1b0b33380b6b427c8ed50f1ce99ec505'
        }
    }
};

export const kXhrOptions = {'x-silent': true};

export const API_NETWORK_PREFIX = '/api/network/v1';
export const API_NETWORK_PREFIX_V2 = '/api/network/v2';

// 维护一个服务名称 + 描述的集合
export const enopointServiceMap = [
    {
        name: '对象存储',
        value: 'bos',
        desc: '稳定、安全、高效、高可扩展的云存储服务'
    },
    {
        name: '消息服务 for Kafka',
        value: 'kafka',
        desc: '分布式、高可扩展、高通量的消息托管服务'
    },
    {
        name: '百度网盘',
        value: 'baidupan',
        desc: '提供云端备份等服务，便捷安全的管理数据'
    },
    {
        name: '百度地图开放平台',
        value: 'lbsyun',
        desc: '提供基于百度地图的应用程序接口'
    },
    {
        name: '百度智驾地图',
        value: 'automap',
        desc: '最懂自动驾驶的高精地图服务'
    },
    {
        name: 'EasyPack鉴权服务',
        value: 'easypack',
        desc: ''
    },
    {
        name: '百度云虚拟网关监控场景管理平台',
        value: 'cprom',
        desc: ''
    },
    {
        name: 'API网关',
        value: 'apigateway',
        desc: '提供API全方面的管理能力'
    },
    {
        name: 'Security Token Service',
        value: 'sts',
        desc: '百度智能云提供的临时授权服务'
    },
    {
        name: '容器镜像服务',
        value: 'registry',
        desc: '提供分布式全托管的容器镜像管理服务'
    },
    {
        name: '容器镜像服务企业版认证服务',
        value: 'ccrauth',
        desc: '提供企业版在镜像推拉等操作中提供认证、鉴权生成access tokens。'
    },
    {
        name: '云安全中心',
        value: 'csc',
        desc: '提供云外用户公共服务'
    },
    {
        name: '云桌面服务',
        value: 'bvd',
        desc: '云桌面服务是基于云计算技术的高效、便捷、低成本办公解决方案。'
    },
    {
        name: '百度智能云千帆ModelBuilder',
        value: 'qianfan',
        desc: '一站式企业大模型开发平台，助力AI应用落地'
    }
];

export const pathReasonList = [
    {text: '源、目的变更', value: 'SOURCE_OR_DEST_IS_INVALID'},
    {text: '无法到达指定VPC', value: 'UNABLE_TO_REACH_THE_SPECIFIED_VPC'},
    {text: '实际到达其他VPC', value: 'REACH_ANOTHER_VPC'},
    {text: 'VPC隧道、专线通道无法进VPC', value: 'VPNCONN_OR_DC_CANNOT_ENTER_VPC'},
    {text: 'IDC侧响应无法返回VPC', value: 'IDC_RESPONSE_UNABLE_TO_RETURN_VPC'},
    {text: '无法通过指定节点', value: 'UNABLE_TO_ACCESS_THE_SPECIFY_NODE'},
    {text: '无法到IDC', value: 'CAN_NOT_REACH_IDC'},
    {text: '响应无法从源通道返回', value: 'RESPONSE_CANNOT_BE_RETURNED_FROM_THE_SOURCE_CHANNEL'},
    {text: '请求方向不通', value: 'REQUEST_DIRECTION_UNACCESSIBLE'},
    {text: '路由配置错误', value: 'ROUTING_CONFIGURATION_ERROR'},
    {text: '安全组规则阻断', value: 'SG_RULE_BLOCK'},
    {text: 'CFW规则阻断', value: 'CFW_RULE_BLOCK'},
    {text: 'ACL规则阻断', value: 'ACL_RULE_BLOCK'},
    {text: '未绑定eip', value: 'NOT_BIND_EIP'},
    {text: '无法到达公网', value: 'CAN_NOT_TO_INTERNET'},
    {text: '公网无法入云', value: 'CAN_NOT_TO_CLOUD'},
    {text: '无法到云外ip', value: 'CAN_NOT_REACH_OUT_OF_CLOUD_IP'}
];

export const serviceTypeUrl = {
    eip: '/eip/#/eip/instance/list',
    EIP: '/eip/#/eip/instance/list',
    blb: '/blb/#/blb/list',
    blbFour: '/blb/#/blb/list',
    blbSeven: '/blb/#/blb/list',
    nat: '/network/#/vpc/nat/auth',
    NAT: '/network/#/vpc/nat/auth',
    vpn: '/network/#/vpc/vpn/auth',
    peerconn: '/network/#/vpc/peerconn/auth',
    peerConn: '/network/#/vpc/peerconn/auth',
    PEERCONN: '/network/#/vpc/peerconn/auth',
    csnvpc: '/csn/#/csn/instance/list',
    csn: '/csn/#/csn/instance/list',
    CSN: '/csn/#/csn/instance/list',
    et: '/network/#/dc/landing',
    dcgw: '/network/#/dc/landing',
    DCGW: '/network/#/dc/landing',
    dcGateway: '/network/#/dc/landing',
    VPN_CONN: '/network/#/vpc/vpn/auth',
    DC: '/network/#/dc/landing'
};

const serviceSts = {
    peerconn: 'peerConnSts',
    peerConn: 'peerConnSts',
    PEERCONN: 'peerConnSts',
    nat: 'natSts',
    NAT: 'natSts',
    eip: 'blbSts',
    EIP: 'blbSts',
    blb: 'blbSts',
    vpn: 'vpnSts',
    csnvpc: 'csnSts',
    csn: 'csnSts',
    CSN: 'csnSts',
    et: 'etSts',
    dcgw: 'etSts',
    DCGW: 'etSts',
    dcGateway: 'etSts',
    VPN_CONN: 'vpnSts',
    blbFour: 'blbSts',
    blbSeven: 'blbSts',
    DC: 'etSts'
};

export const checkSts = array => {
    let stsList = [
        'eip',
        'blb',
        'nat',
        'vpn',
        'peerconn',
        'csnvpc',
        'dcgw',
        'csn',
        'dcGateway',
        'peerConn',
        'et',
        'PEERCONN',
        'CSN',
        'DCGW',
        'EIP',
        'NAT',
        'DC',
        'VPN_CONN',
        'blbFour',
        'blbSeven'
    ];
    stsList.forEach(item => {
        let index = array.findIndex(i => i.value === item);
        if (!window.$storage.get(serviceSts[item]) && index > -1) {
            array[index].disabled = true;
        }
    });
    return array;
};

export const instanceDiagnosisMap = {
    csn: [
        {
            name: 'config',
            text: '配置诊断',
            diagnosisArray: [
                {
                    name: 'cross_region_bw_check',
                    text: '跨地域限速配置检查',
                    suggestion: {
                        critical: {
                            tipContent: '检查各地域间跨地域连接是否配置了限速带宽。',
                            suggest: `请检查并配置地域带宽，更多信息详情，请参见：<a href="${
                                'https://cloud.baidu.com/doc/CSN/s/Wm0aeytzs'
                            }">地域带宽管理</a>。`,
                            effect: '跨地域连接未设置地域带宽，默认带宽仅有10Kbps，可能会影响跨地域流量。'
                        }
                    }
                },
                {
                    name: 'propagation_check',
                    text: '路由学习检查',
                    suggestion: {
                        critical: {
                            tipContent: '网络实例未建立学习关系，CSN无法自动学习网络实例的路由，可能会导致网络不通。',
                            suggest: `请检查并创建学习关系，更多信息详情，请参见：<a href="${
                                'https://cloud.baidu.com/doc/CSN/s/fkya1yv0g#%E5%88%9B%E5%BB%BA%E5%AD%A6%E4%B9%A0%E5%85%B3%E7%B3%BB'
                            }">创建路由学习</a>。`,
                            effect: '检查网络实例是否与CSN路由表建立了学习关系。'
                        }
                    }
                },
                {
                    name: 'association_check',
                    text: '关联转发检查',
                    suggestion: {
                        normal: {
                            tipContent: '检查网络实例是否与CSN路由表建立了关联关系。',
                            suggest: `请检查并创建关联关系，更多信息详情，请参见：<a href="${
                                'https://cloud.baidu.com/doc/CSN/s/fkya1yv0g#%E5%88%9B%E5%BB%BA%E5%85%B3%E8%81%94%E5%85%B3%E7%B3%BB'
                            }">创建关联关系</a>。`,
                            effect: '网络实例未建立关联关系，CSN将按照默认路由表进行流量转发，可能会导致网络不通或流量转发不符合预期。'
                        }
                    }
                }
            ]
        },
        {
            name: 'capacity',
            text: '容量超限诊断',
            diagnosisArray: [
                {
                    name: 'cross_region_bw_utilization_check',
                    text: '跨地域带宽利用率检查',
                    suggestion: {
                        normal: {
                            tipContent: '检查跨地域连接近15分钟内带宽利用率是否已达80%。',
                            suggest: `请检查并调高地域带宽，或合理使用带宽，减少不必要的流量，更多信息请参见：<a href="${
                                'https://cloud.baidu.com/doc/CSN/s/Wm0aeytzs'
                            }">创建地域带宽</a>。`,
                            effect: '地域带宽利用率较高，流量存在风险，若达到100%可能会导致丢包。'
                        }
                    }
                },
                {
                    name: 'member_bw_utilization_check',
                    text: '网络实例带宽利用率检查',
                    suggestion: {
                        normal: {
                            tipContent: '检查网络实例近15分钟内带宽利用率是否已达80%。',
                            suggest: `请检查并调高网络实例带宽，或合理使用带宽，减少不必要的流量，更多信息请参见：<a href="${
                                'https://cloud.baidu.com/doc/CSN/s/Uklrk4o7b#%E6%B7%BB%E5%8A%A0%E7%BD%91%E7%BB%9C%E5%AE%9E%E4%BE%8B'
                            }">网络实例带宽管理</a>进行带宽调整。`,
                            effect: '网络实例带宽利用率较高，流量存在风险，若达到100%可能会导致丢包。'
                        }
                    }
                },
                {
                    name: 'et_ecmp_route_check',
                    text: '专线ECMP路由数量检查',
                    suggestion: {
                        normal: {
                            tipContent: '检查专线ECMP路由数量是否达到80%，最大值512。',
                            suggest:
                                '请进行路由聚合，减少不必要的明细路由，如可通过调整IP地址网段的网络位和掩码来聚合路由。',
                            effect: '专线ECMP路由数量较多，超过512条后多余路由将无法下发，可能导致网络不通。'
                        }
                    }
                }
            ]
        },
        {
            name: 'cost',
            text: '费用诊断',
            diagnosisArray: [
                {
                    name: 'csn_bp_billing_check',
                    text: '带宽包费用状态检查',
                    suggestion: {
                        critical: {
                            tipContent: '检查云智能网绑定的带宽包实例是否处于欠费/7天内到期状态。',
                            suggest:
                                '请及时续费带宽包，或保证足够的账户余额，更多信息请参见：<a href="https://cloud.baidu.com/doc/CSN/s/kkkdsr40p">续费</a>。',
                            effect: '带宽包到期、欠费将导致地域带宽不可用，带宽会降为默认的10Kbps，影响跨地域流量。'
                        },
                        normal: {
                            tipContent: '检查云智能网绑定的带宽包实例是否处于欠费/7天内到期状态。',
                            suggest:
                                '请及时续费带宽包，或保证足够的账户余额，更多信息请参见：<a href="https://cloud.baidu.com/doc/CSN/s/kkkdsr40p">续费</a>。',
                            effect: '带宽包到期、欠费将导致地域带宽不可用，带宽会降为默认的10Kbps，影响跨地域流量。'
                        }
                    }
                }
            ]
        },
        {
            name: 'route',
            text: '路由诊断',
            diagnosisArray: [
                {
                    name: 'route_conflict_check',
                    text: '网络实例路由冲突检查',
                    suggestion: {
                        critical: {
                            tipContent: '检查网络实例之间是否存在路由冲突。',
                            suggest: '请合理规划网段，确保网络实例发布路由无冲突。',
                            effect: '网络实例发布的冲突路由将不会生效，可能会影响其余网络实例访问该网络实例的流量。'
                        }
                    }
                },
                {
                    name: 'vpc_route_check',
                    text: 'VPC路由检查',
                    suggestion: {
                        critical: {
                            tipContent: '检查VPC网络实例的路由表中是否配置了TGW路由。',
                            suggest: '请在VPC路由表添加TGW路由。',
                            effect: 'VPC路由表缺少TGW路由，VPC的流量无法进入CSN，VPC无法与其余CSN网络实例互访。'
                        }
                    }
                },
                {
                    name: 'et_ingress_route_check',
                    text: '专线通道上云路由检查',
                    suggestion: {
                        normal: {
                            tipContent: '检查专线通道是否从CSN路由表中学习到路由。',
                            suggest: '专线通道未建立关联关系或关联的路由表不存在路由，请检查关联关系与路由表路由。',
                            effect: '专线通道缺少指向CSN的路由，将导致流量不通。'
                        }
                    }
                },
                {
                    name: 'et_egress_route_check',
                    text: '专线通道下云路由检查',
                    suggestion: {
                        critical: {
                            tipContent: '检查专线通道是否从对端学习到路由。',
                            suggest: '专线通道未收到对端宣告的路由，请检查并在IDC侧网络设备上宣告路由。',
                            effect: '专线通道缺少指向云下的路由，将导致流量不通。'
                        }
                    }
                }
            ]
        }
    ],
    eip: [
        {
            name: 'safety_inspection',
            text: '安全策略检测',
            diagnosisArray: [
                {
                    name: 'ddos_status_check',
                    text: 'DDoS防护检查',
                    suggestion: {
                        critical: {
                            tipContent: '对EIP实例默认绑定DDoS防护进行检查，包括DDOS和TBSP。',
                            suggest: `配置DDOS防护或流量突发服务包，参考文档：<a href="${
                                'https://cloud.baidu.com/doc/EIP/s/rlus7qknu'
                            }">DDoS基础防护</a>/<a href="${
                                'https://cloud.baidu.com/doc/EIP/s/Pkye1uqus'
                            }">实例管理</a>。`,
                            effect: 'EIP易被攻击。'
                        }
                    }
                },
                {
                    name: 'cfw_status_check',
                    text: '云防火墙检查',
                    suggestion: {
                        critical: {
                            tipContent: '对EIP实例的关联的云防火墙进行检查。',
                            suggest: `配置CFW防护，参考文档：<a href="${
                                'https://cloud.baidu.com/doc/CFW/s/8lwrgvj15'
                            }">云防火墙CFW</a>。`,
                            effect: 'EIP无防护，暴露公网，容易被攻击。'
                        }
                    }
                },
                {
                    name: 'pause_mask_check',
                    text: '风控锁定检查',
                    suggestion: {
                        normal: {
                            tipContent: '对EIP实例进行风控锁定检查。',
                            suggest: `如果EIP被DDOS封禁，可自行前往<a href="${
                                '/eip/#/unblock/ddos/list'
                            }">申请解封</a>；如果EIP被安全风控，则不可使用，请提交工单获取风控原因。`,
                            effect: 'EIP暂时无法使用。'
                        }
                    }
                }
            ]
        },
        {
            name: 'cost',
            text: '费用诊断',
            diagnosisArray: [
                {
                    name: 'eip_billing_check',
                    text: '费用状态检查',
                    suggestion: {
                        normal: {
                            tipContent: '检查EIP实例是否处于欠费/7天内到期状态。',
                            suggest: `请及时续费，或保证足够的账户余额，更多信息请参见：<a href="${
                                'https://cloud.baidu.com/doc/EIP/s/Qkedzqh2g'
                            }">续费</a>。`,
                            effect: 'EIP实例到期、欠费将导致EIP状态不可用，将影响EIP绑定的云服务实例公网访问不通。'
                        },
                        critical: {
                            tipContent: '检查EIP实例是否处于欠费/7天内到期状态。',
                            suggest: `请及时续费，或保证足够的账户余额，更多信息请参见：<a href="${
                                'https://cloud.baidu.com/doc/EIP/s/Qkedzqh2g'
                            }">续费</a>。`,
                            effect: 'EIP实例到期、欠费将导致EIP状态不可用，将影响EIP绑定的云服务实例公网访问不通。'
                        }
                    }
                }
            ]
        },
        {
            name: 'configuration',
            text: '配置诊断',
            diagnosisArray: [
                {
                    name: 'bind_check',
                    text: 'EIP分配状态检查',
                    suggestion: {
                        critical: {
                            tipContent: '检查EIP实例是否绑定云服务实例。',
                            suggest: '请把IP绑定实例，或释放闲置的EIP实例。',
                            effect: 'EIP存在闲置费用。'
                        }
                    }
                },
                {
                    name: 'status_check',
                    text: '实例业务状态检查',
                    suggestion: {
                        critical: {
                            tipContent: '检查EIP实例状态是否正常。',
                            suggest: '检查EIP是否欠费或者被封禁。',
                            effect: 'EIP状态异常，无法正常使用。'
                        }
                    }
                }
            ]
        },
        {
            name: 'capacity_overrun',
            text: '容量超限诊断',
            diagnosisArray: [
                {
                    name: 'bandwidth_water_level_check',
                    text: 'EIP带宽水位检查',
                    suggestion: {
                        normal: {
                            tipContent: '检查EIP实例近2小时内的带宽平均利用率是否达到当前购买带宽值的80%。',
                            suggest: '请调大EIP实例带宽。',
                            effect: 'EIP带宽占用高，易发生带宽限速丢包。'
                        }
                    }
                },
                {
                    name: 'packet_loss_check',
                    text: 'EIP带宽限速丢包检查',
                    suggestion: {
                        critical: {
                            tipContent: '检查EIP实例近2小时内是否由于带宽超限触发丢包。',
                            suggest: '请调大EIP实例带宽。',
                            effect: 'EIP已发生丢包，将影响您的业务。'
                        }
                    }
                }
            ]
        }
    ],
    etChannel: [
        {
            name: 'health_check',
            text: '健康检查诊断',
            diagnosisArray: [
                {
                    name: 'health_check_config',
                    text: '健康检查配置',
                    suggestion: {
                        warning: {
                            tipContent: '专线网关是否已经配置健康检查。',
                            suggest: '在专线网关的详情页，配置专线网关的链路探测。',
                            effect: '专线网关未配置链路探测，将导致专线通道不通时，VPC去往专线通道方向流量无法快速切换。'
                        }
                    }
                }
            ]
        },
        {
            name: 'config',
            text: '配置诊断',
            diagnosisArray: [
                {
                    name: 'interface_status_check',
                    text: '物理端口状态异常',
                    suggestion: {
                        critical: {
                            tipContent: '检查接入设备物理专线的物理端口状态是否异常。',
                            suggest:
                                '请尽快联系机房人员排查交换机端口、配置、线路等情况，必要时可联系百度智能云工作人员协助排查，尽快恢复物理端口状态正常。',
                            effect: '物理专线端口异常，将导致该物理专线下所有专线通道流量转发异常。'
                        }
                    }
                },
                {
                    name: 'ping_remote_ip_check',
                    text: '专线直连地址可达性检查',
                    suggestion: {
                        critical: {
                            tipContent: '检查专线通道直连地址是否可以ping通。',
                            suggest:
                                '请尽快联系机房人员排查交换机端口、配置、线路等情况，必要时可联系百度智能云工作人员协助排查，尽快恢复专线通道互联地址连通性正常。',
                            effect: '专线通道互联地址连通性异常，将导致该专线通道上的流量转发异常。'
                        }
                    }
                },
                {
                    name: 'bgp_status_check',
                    text: 'BGP连接状态检查',
                    suggestion: {
                        critical: {
                            tipContent: '检查专线通道的BGP邻居状态是否为已建立状态。',
                            suggest:
                                '请尽快联系机房人员排查交换机端口、配置、线路等情况，必要时可联系百度智能云工作人员协助排查，尽快恢复专线通道BGP状态正常。',
                            effect: '专线通道BGP状态异常，将导致该专线通道流量转发异常。'
                        }
                    }
                },
                {
                    name: 'bfd_status_check',
                    text: 'BFD会话状态检查',
                    suggestion: {
                        critical: {
                            tipContent: '检查专线通道的BFD会话状态是否为已建立状态。',
                            suggest:
                                '请尽快联系机房人员排查交换机端口、配置、线路等情况，必要时可联系百度智能云工作人员协助排查，尽快恢复专线通道BFD会话状态正常。',
                            effect: '专线通道可靠性检测异常，将导致该专线通道BFD状态异常，可能引发路由撤销等情况导致流量转发异常。'
                        }
                    }
                },
                {
                    name: 'ecmp_and_association_check',
                    text: '专线通道冗余关系检查',
                    suggestion: {
                        warning: {
                            tipContent: '检查专线通道是否关联关系，绑定的专线网关是否有主备或ecmp的网关。',
                            suggest:
                                '请检查云上专线通道的关联关系是否取消或VPC路由表专线网关多线路由配置或检查线路等情况，必要时可联系百度智能云工作人员协助排查，尽快恢复专线通道冗余状态正常。',
                            effect: '专线通道冗余性检测异常，将导致专线的冗余性大大降低，请尽快解除风险。'
                        }
                    }
                },
                {
                    name: 'lldp_info_check',
                    text: '物理端口接线检查',
                    suggestion: {
                        warning: {
                            tipContent: '依赖开lldp，云默认开启。',
                            suggest:
                                '请尽快联系机房人员排查交换机端口模块是否匹配、是否配置强制全双工、配置是否开启LLDP、线路等情况，必要时可联系百度智能云工作人员协助排查，尽快解决物理端口接线异常问题。',
                            effect: '物理端口接线问题，将导致该物理专线无法正常工作，请尽快解除风险。'
                        }
                    }
                }
            ]
        },
        {
            name: 'capacity',
            text: '容量超限诊断',
            diagnosisArray: [
                {
                    name: 'bgp_route_limit_check',
                    text: 'BGP路由超限检查',
                    suggestion: {
                        warning: {
                            tipContent: '检查动态专线上的BGP路由是否超过限制。',
                            suggest:
                                '请用户做好IDC端路由网段聚合或联系百度智能云工作人员评估后调整该配额值，尽快解除该风险。',
                            effect: 'BGP类型专线通道收路由异常，百度智能云端对超出默认路由条目数路由将丢弃，请尽快解除风险。'
                        }
                    }
                },
                {
                    name: 'gateway_speed_limit_check',
                    text: '专线网关限速检查',
                    suggestion: {
                        critical: {
                            tipContent: '检查流量是否超出限速配额导致丢包。',
                            suggest:
                                '请用户登录百度智能云控制台，调整专线网关的出口带宽值，尽快解决专线网关限速丢包异常问题。',
                            effect: '专线网关限速丢包，将导致专线网关绑定的专线通道流量转发异常，请尽快解除该问题。'
                        }
                    }
                }
            ]
        },
        {
            name: 'cost',
            text: '费用诊断',
            diagnosisArray: [
                {
                    name: 'dcphy_port_billing_check',
                    text: '物理端口欠费检查',
                    suggestion: {
                        critical: {
                            tipContent: '检查物理专线的物理端口是否处于欠费状态。',
                            suggest: '请尽快充值并保障账号有足额的余额来应对未来的消费。',
                            effect: '物理端口欠费，将导致该物理端口上所有专线通道流量断流，影响业务。'
                        }
                    }
                }
            ]
        }
    ],
    nat: [
        {
            name: 'link',
            text: '连通性诊断',
            diagnosisArray: [
                {
                    name: 'packet_loss_check',
                    text: '限速丢包',
                    suggestion: {
                        critical: {
                            tipContent: '检查NAT网关实例是否因超规格使用而导致限速丢包。',
                            suggest: '请在百度智能云控制台对NAT网关进行网关升级操作。',
                            effect: 'NAT网关关联的云服务实例可能会受到影响。'
                        }
                    }
                }
            ]
        },
        {
            name: 'configuration',
            text: '配置诊断',
            diagnosisArray: [
                {
                    name: 'status_check',
                    text: '实例业务状态检查',
                    suggestion: {
                        critical: {
                            tipContent: '检查NAT网关实例的状态。',
                            suggest: '检查NAT网关是否欠费，请进行续费或充值。',
                            effect: 'NAT网关状态异常，无法正常使用。'
                        }
                    }
                },
                {
                    name: 'bind_check',
                    text: 'NAT绑定状态检查',
                    suggestion: {
                        normal: {
                            tipContent: '检查NAT网关实例是否已经绑定EIP或NAT IP。',
                            suggest: '请对NAT网关进行绑定EIP。',
                            effect: 'NAT网关未绑定EIP，无法访问公网。'
                        }
                    }
                },
                {
                    name: 'rule_check',
                    text: 'NAT策略配置检查',
                    suggestion: {
                        normal: {
                            tipContent: '检查NAT网关实例是否已经配置SNAT和DNAT规则。',
                            suggest: '请对NAT网关配置SNAT或DNAT规则。',
                            effect: 'NAT网关关联的云服务实例无法连接公网。'
                        }
                    }
                }
            ]
        },
        {
            name: 'capacity_overrun',
            text: '容量超限诊断',
            diagnosisArray: [
                {
                    name: 'connections_ratio_check',
                    text: 'NAT实例并发连接利用率检查',
                    suggestion: {
                        normal: {
                            tipContent: '检查NAT网关实例并发连接利用率是否超过80%。',
                            suggest: '请在百度智能云控制台对NAT网关进行网关升级操作。',
                            effect: 'NAT网关实例并发连接率高，可能会导致无法建立新的连接。'
                        }
                    }
                },
                {
                    name: 'create_connections_ratio_check',
                    text: 'NAT新建连接利用率检查',
                    suggestion: {
                        normal: {
                            tipContent: '检查NAT网关实例的新建连接利用率是否超过80%。',
                            suggest: '请在百度智能云控制台对NAT网关进行网关升级操作。',
                            effect: 'NAT网关新增连接数高，可能会导致新建连接无法成功建立。'
                        }
                    }
                },
                {
                    name: 'bandwidth_ratio_check',
                    text: 'NAT带宽利用率检查',
                    suggestion: {
                        normal: {
                            tipContent: '检查NAT网关实例的带宽利用率是否超过80%。',
                            suggest: '请在百度智能云控制台对NAT网关绑定的EIP实例进行带宽调整。',
                            effect: '可能会导致NAT网关关联的云服务实例访问丢包，影响用户业务使用。'
                        }
                    }
                }
            ]
        },
        {
            name: 'cost',
            text: '费用诊断',
            diagnosisArray: [
                {
                    name: 'nat_billing_check',
                    text: '费用状态检查',
                    suggestion: {
                        critical: {
                            tipContent: '检查公网NAT网关实例是否处于欠费/7天内到期状态。',
                            suggest:
                                '请及时续费，或保证足够的账户余额，更多信息请参见：<a href="https://cloud.baidu.com/doc/VPC/s/Njwvyuqa3">续费</a>。',
                            effect: '公网NAT网关实例到期、欠费将导致公网NAT网关状态不可用，将影响公网NAT网关后端绑定的云服务实例公网访问不通。'
                        },
                        warning: {
                            tipContent: '检查公网NAT网关实例是否处于欠费/7天内到期状态。',
                            suggest:
                                '请及时续费，或保证足够的账户余额，更多信息请参见：<a href="https://cloud.baidu.com/doc/VPC/s/Njwvyuqa3">续费</a>。',
                            effect: '公网NAT网关实例到期、欠费将导致公网NAT网关状态不可用，将影响公网NAT网关后端绑定的云服务实例公网访问不通。'
                        }
                    }
                }
            ]
        }
    ],
    privateNat: [
        {
            name: 'link',
            text: '连通性诊断',
            diagnosisArray: [
                {
                    name: 'packet_loss_check',
                    text: '限速丢包',
                    suggestion: {
                        critical: {
                            tipContent: '检查NAT网关实例是否因超规格使用而导致限速丢包。',
                            suggest: '请在百度智能云控制台对NAT网关进行性能容量变配操作。',
                            effect: 'NAT网关关联的云服务实例可能会受到影响。'
                        }
                    }
                }
            ]
        },
        {
            name: 'configuration',
            text: '配置诊断',
            diagnosisArray: [
                {
                    name: 'status_check',
                    text: '实例业务状态检查',
                    suggestion: {
                        critical: {
                            tipContent: '检查NAT网关实例的状态。',
                            suggest: '检查NAT网关是否欠费，请进行充值。',
                            effect: 'NAT网关状态异常，无法正常使用。'
                        }
                    }
                },
                {
                    name: 'bind_check',
                    text: 'NAT绑定状态检查',
                    suggestion: {
                        normal: {
                            tipContent: '检查NAT网关实例是否已经绑定EIP或NAT IP。',
                            suggest: '请对NAT网关进行绑定NAT IP。',
                            effect: 'NAT网关未绑定NAT IP，无法访问私网。'
                        }
                    }
                },
                {
                    name: 'rule_check',
                    text: 'NAT策略配置检查',
                    suggestion: {
                        normal: {
                            tipContent: '检查NAT网关实例是否已经配置SNAT和DNAT规则。',
                            suggest: '请对NAT网关配置SNAT或DNAT规则。',
                            effect: 'NAT网关关联的云服务实例无法连接私网。'
                        }
                    }
                }
            ]
        },
        {
            name: 'capacity_overrun',
            text: '容量超限诊断',
            diagnosisArray: [
                {
                    name: 'connections_ratio_check',
                    text: 'NAT实例并发连接利用率检查',
                    suggestion: {
                        normal: {
                            tipContent: '检查NAT网关实例并发连接利用率是否超过80%。',
                            suggest: '请在百度智能云控制台对NAT网关进行性能容量变配操作。',
                            effect: 'NAT网关实例并发连接率高，可能会导致无法建立新的连接。'
                        }
                    }
                },
                {
                    name: 'create_connections_ratio_check',
                    text: 'NAT新建连接利用率检查',
                    suggestion: {
                        normal: {
                            tipContent: '检查NAT网关实例的新建连接利用率是否超过80%。',
                            suggest: '请在百度智能云控制台对NAT网关进行性能容量变配操作。',
                            effect: 'NAT网关新增连接数高，可能会导致新建连接无法成功建立。'
                        }
                    }
                },
                {
                    name: 'bandwidth_ratio_check',
                    text: 'NAT带宽利用率检查',
                    suggestion: {
                        normal: {
                            tipContent: '检查NAT网关实例的带宽利用率是否超过80%。',
                            suggest: '请在百度智能云控制台对私网NAT网关进行性能容量变配操作。',
                            effect: '可能会导致NAT网关关联的云服务实例访问丢包，影响用户业务使用。'
                        }
                    }
                }
            ]
        },
        {
            name: 'cost',
            text: '费用诊断',
            diagnosisArray: [
                {
                    name: 'nat_billing_check',
                    text: '费用状态检查',
                    suggestion: {
                        critical: {
                            tipContent: '检查私网NAT网关实例是否处于欠费状态。',
                            suggest:
                                '请及时续费，或保证足够的账户余额，更多信息请参见：<a href="https://cloud.baidu.com/doc/VPC/s/Njwvyuqa3">续费</a>。',
                            effect: '私网NAT网关欠费将导致私网NAT网关状态不可用，将影响私网NAT网关关联的云服务实例私网访问不通。'
                        },
                        warning: {
                            tipContent: '检查私网NAT网关实例是否处于欠费状态。',
                            suggest:
                                '请及时续费，或保证足够的账户余额，更多信息请参见：<a href="https://cloud.baidu.com/doc/VPC/s/Njwvyuqa3">续费</a>。',
                            effect: '私网NAT网关欠费将导致私网NAT网关状态不可用，将影响私网NAT网关关联的云服务实例私网访问不通。'
                        }
                    }
                }
            ]
        }
    ]
};
