/**
 * @file BCE zone
 * <AUTHOR>
 */

import u from 'lodash';

function getLabel(value = '-') {
    if (!value) {
        return '-';
    }

    // 如果value中有逗号，则为多可用区
    if (value.match(',')) {
        return '多可用区' + value.replace(/,/g, '+').replace(/zone/g, '');
    }
    return value.replace(/zone/g, '可用区');
}

export default {
    getLabel,
    getZoneList(list = []) {
        return u.map(list, item => {
            return {
                value: item,
                name: getLabel(item)
            };
        });
    }
};
