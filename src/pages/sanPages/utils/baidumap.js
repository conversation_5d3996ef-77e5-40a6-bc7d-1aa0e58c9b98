/**
 * @file 百度地图
 * <AUTHOR>
 */

export function loadBaiduMap() {
    const bmp = window.btoa('LnXPPmvnMKTDX3AnZ2XCEEIGmiW4yq1m');
    const BMAP_URL = `https://api.map.baidu.com/api?v=3.0&ak=${window.atob(bmp)}&callback=onBMapCallback`;
    return new Promise((resolve, reject) => {
        // 如果已经加载，直接返回
        if (typeof window.BMap !== 'undefined') {
            resolve(window.BMap);
            return true;
        }

        // 百度地图异步加载回调处理
        window.onBMapCallback = function () {
            resolve(window.BMap);
        };

        // 插入script脚本
        let script = document.createElement('script');
        script.src = BMAP_URL;
        script.type = 'text/javascript';
        document.body.appendChild(script);
    });
}
