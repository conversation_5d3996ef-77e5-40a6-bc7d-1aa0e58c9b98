/**
 * @file src/network/common.es6
 * <AUTHOR>
 */
import u from 'lodash';

import {Netseg} from '../common/enum';
import rule from './rule';
import {disableCidr} from './constants';

const AllRegion = window.$context.getEnum('AllRegion');
// 根据ip（xxx.xxx.xxx.xxx）来确定掩码
export let getMaskByIp = (ip = '') => {
    let total = '';
    let part = '';
    let array = ip.split('.');
    u.each(array, item => {
        part = parseInt(item, 10).toString(2);
        let append = '';
        for (let i = 0; i < 8 - part.length; i++) {
            append += '0';
        }
        total += part + append;
    });
    return total.replace(/0*$/, '').length;
};
export let getMaskNum = mask => Math.floor(mask / 8) + (mask % 8 === 0 ? -1 : 0);
export let getSum = array => {
    let count = Math.pow(2, array.length);
    let result = [];
    for (let i = 0; i < count; i++) {
        let binary = i.toString(2).split('');
        let total = 0;
        if (binary.length < array.length) {
            binary = [...new Array(array.length - binary.length), ...binary];
        }
        u.each(array, (data, index) => {
            if (binary[index] === '1') {
                total += data;
            }
        });
        result.push(total);
    }
    return result;
};
// 根据当前mask以及当前是第几段判断这一段有几个0
export let getAvaliableByMask = (mask, index, vpcMask) => {
    let min = index * 8;
    let num = 8 - (mask - min);
    if (num < 0) {
        num = 0;
    }
    let vpcNum = vpcMask ? 8 - (vpcMask - min) : 8;
    if (vpcNum < 0) {
        vpcNum = 0;
    }
    let nums = [];
    for (let i = num; i < vpcNum; i++) {
        nums.unshift(Math.pow(2, i));
    }
    return getSum(nums);
};
export let getDifferenceAvaliable = (maskSelect, index, cidr = '', cidr0 = '') => {
    let mask = maskSelect.getRawValue();
    if (cidr.indexOf('0.0.0.0') === 0 && index === 1 && cidr0 !== Netseg.SEG10) {
        return u.map(getPartDatasource(cidr0, mask), item => +item.value);
    }
    cidr = cidr.split('/')[0].split('.');
    let minMask = u.min(u.pluck(maskSelect.get('datasource'), 'value'));
    let vpcMaskNum = getMaskNum(minMask);
    if (index === vpcMaskNum && minMask < (index + 1) * 8 && mask !== minMask) {
        let result = getAvaliableByMask(mask, index, minMask);
        let nums = [];
        u.each(result, data => nums.push(data + +cidr[index]));
        return nums;
    }
    return getAvaliableByMask(mask, index);
};
export let getAvaliableContent = result => {
    let prefix = '可输入';
    if (result.length > 5) {
        return prefix + result.slice(0, 4).join('、') + ' ... ' + result[result.length - 1];
    }
    return prefix + result.join('、');
};
export let getPartDatasource = (seg, mask) => {
    let datasource = [];
    if (seg === Netseg.SEG172) {
        let result = getAvaliableByMask(mask, 1);
        for (let i = 16; i <= 31; i++) {
            if (u.indexOf(result, i) > 0) {
                datasource.push({
                    value: i + '',
                    text: i + ''
                });
            }
        }
    } else if (seg === Netseg.SEG10) {
        datasource.push({
            value: '0',
            text: '0'
        });
    } else {
        datasource.push({
            value: '168',
            text: '168'
        });
    }
    return datasource;
};
export let convertPartToBinary = part => {
    part = +part;
    let binary = part.toString(2);
    let pre = '';
    for (let i = 0; i < 8 - binary.length; i++) {
        pre += '0';
    }
    return pre + binary;
};
export let convertCidrToBinary = cidr => {
    let array = cidr.split('/');
    let result = [];
    let valueArray = [];
    let index = cidr.indexOf(':');
    // 16进制
    if (index >= 0) {
        // ::只会出现一次
        valueArray = array[0].split('::');
        for (let i = 0; i < valueArray.length; i++) {
            let part = valueArray[i];
            let partTotal = '';
            if (part) {
                let partArray = part.split(':');
                for (let j = 0; j < partArray.length; j++) {
                    let numArray = partArray[j].split('');
                    let total = '';
                    for (let k = 0; k < numArray.length; k++) {
                        let num = parseInt(numArray[k], 16);
                        if (u.isNaN(num)) {
                            total += convertCidrToBinary(numArray[k]);
                        } else {
                            let binary = num.toString(2);
                            let pre = '';
                            /* eslint-disable */
                            for (let l = 0; l < 4 - binary.length; l++) {
                                pre += '0';
                            }
                            total += pre + binary;
                            /* eslint-enable */
                        }
                    }
                    if (total.length < 16) {
                        let remain = '';
                        for (let l = 0; l < 16 - total.length; l++) {
                            remain += '0';
                        }
                        total = remain + total;
                    }
                    partTotal += total;
                }
            }
            result.push(partTotal);
        }
        let totalLength = result.join('').length;
        let append = '';
        if (totalLength < 128) {
            for (let i = 0; i < 128 - totalLength; i++) {
                append += '0';
            }
        }
        if (result.length > 1) {
            return result.join(append);
        }
        return index === 0 ? append + result : result + append;
    }
    // 10进制
    valueArray = array[0].split('.');
    for (let i = 0; i < valueArray.length; i++) {
        result.push(convertPartToBinary(valueArray[i]));
    }
    return result.join('');
};
export let checkIsInSubnet = (cidr, subnetCidr) => {
    let subnetArray = subnetCidr.split('/');
    let subnetMask = 0;
    if (subnetArray.length > 1) {
        subnetMask = +subnetArray[1];
    }
    let cidrArray = cidr.split('/');
    let cidrMask = 0;
    if (cidrArray.length > 1) {
        cidrMask = +cidrArray[1];
    }
    let cidrString = convertCidrToBinary(cidr);
    let subnetString = convertCidrToBinary(subnetCidr);
    let mask = Math.min(cidrMask, subnetMask);
    if (cidrString.substring(0, mask) === subnetString.substring(0, mask) && cidrMask >= subnetMask) {
        return true;
    }
    return false;
};
export let checkIpInCidr = (cidr, ip) => {
    let cidrArray = cidr.split('/');
    let cidrMask = 0;
    if (cidrArray.length > 1) {
        cidrMask = +cidrArray[1];
    }
    let cidrString = convertCidrToBinary(cidr);
    let ipString = convertCidrToBinary(ip);
    if (cidrString.substring(0, cidrMask) === ipString.substring(0, cidrMask)) {
        return true;
    }
    return false;
};
// 检查输入的cidr是否在禁用网段内
export const checkCidrInDisable = str => {
    let res = false;
    for (let i = 0; i < disableCidr.length; i++) {
        res = checkIsInSubnet(str, disableCidr[i]) || checkIsInSubnet(disableCidr[i], str);
        if (res) {
            break;
        }
    }
    return res;
};
export let checkIpv6Cidr = value => {
    value = value?.trim();
    // 对于多个//的不通过
    if (value.match(/\//g) && value.match(/\//g).length > 1) {
        return false;
    }
    let valueArray = value.split('/');
    if (!rule.IPV6.test(valueArray[0])) {
        return false;
    }
    // 不允许同时出现:和.，虽然IPv6的正则支持，但是后端不支持
    if (valueArray[0].indexOf(':') > -1 && valueArray[0].indexOf('.') > -1) {
        return false;
    }
    if (value.match(/\//g) && u.isNaN(parseInt(valueArray[1], 10))) {
        return false;
    }
    let valueMask = valueArray[1] ? +valueArray[1] : 128;
    if (valueMask < 0 || valueMask > 128 || u.isNaN(valueMask)) {
        return false;
    }
    let valueString = convertCidrToBinary(valueArray[0]);
    if (valueMask === 0 && valueString.indexOf('1') >= 0) {
        return false;
    }
    if (valueString.substring(+valueMask, valueString.length).indexOf('1') > -1) {
        return false;
    }
    return true;
};
export let isMainRegion = region =>
    u.indexOf([AllRegion.HK, AllRegion.HKG, AllRegion.HK02, AllRegion.SIN], region) === -1;
let isAbroadRegion = region => u.indexOf([AllRegion.HK, AllRegion.HKG, AllRegion.HK02, AllRegion.SIN], region) > -1;

// 对等连接不支持的region
export const PeerConnDisableRegion = [AllRegion.YQ, AllRegion.NJ];

// 用来区分是否跨境
export let isCrossRegion = (localRegion, peerRegion) => {
    if (isMainRegion(localRegion)) {
        return isAbroadRegion(peerRegion);
    } else if (isAbroadRegion(localRegion)) {
        return isMainRegion(peerRegion);
    }
};
export let isOnline = () => {
    const hostname = location.hostname;
    return hostname === 'console.bce.baidu.com' || hostname === 'console.vcp.baidu.com';
};
export let getVpcName = name => {
    if (!name || name === 'default') {
        return '默认私有网络';
    }
    return u.escape(name);
};
export let getDifferenceAvaliableNew = (mask, maskList, index, cidr = '', cidr0 = '') => {
    if (cidr.indexOf('0.0.0.0') === 0 && index === 1 && cidr0 !== Netseg.SEG10) {
        return u.map(getPartDatasource(cidr0, mask), item => +item.value);
    }
    cidr = cidr.split('/')[0].split('.');

    const minMask = u.min(u.map(maskList, 'value'));

    const vpcMaskNum = getMaskNum(minMask);

    if (index === vpcMaskNum && minMask < (index + 1) * 8 && mask !== minMask) {
        const result = getAvaliableByMask(mask, index, minMask);

        const nums = [];
        u.each(result, data => nums.push(data + +cidr[index]));

        return nums;
    }

    return getAvaliableByMask(mask, index);
};

export const getXlsx = () => {
    return import('xlsx');
};
// 校验cidr重叠
export const isNetworkOverlap = (cidr1, cidr2) => {
    // 将CIDR转换为网络地址和主机数量
    const parts1 = cidr1.split('/');
    const parts2 = cidr2.split('/');

    // 计算网络地址和广播地址
    const ip1 = parts1[0];
    const mask1 = parts1[1];
    const network1 = ip1.split('.').map((octet, index) => {
        parseInt(octet, 10) & parseInt('11111111'.substring(0, mask1 - (7 - index)), 2);
    });
    const broadcast1 = ip1.split('.').map((octet, index) => {
        parseInt(octet, 10) | parseInt('00000000'.substring(0, 7 - index), 2);
    });

    const ip2 = parts2[0];
    const mask2 = parts2[1];
    const network2 = ip2.split('.').map((octet, index) => {
        parseInt(octet, 10) & parseInt('11111111'.substring(0, mask2 - (7 - index)), 2);
    });
    const broadcast2 = ip2.split('.').map((octet, index) => {
        parseInt(octet, 10) | parseInt('00000000'.substring(0, 7 - index), 2);
    });

    // 检查范围是否重叠
    return !(
        network1.every((octet, index) => octet > broadcast2[index]) ||
        broadcast1.every((octet, index) => octet < network2[index])
    );
};

const ipToInt = ip => {
    return ip.split('.').reduce((int, octet) => (int << 8) + parseInt(octet, 10), 0) >>> 0;
};

export const isOverlapping = (cidr1, cidr2) => {
    const [ip1, mask1] = cidr1.split('/');
    const [ip2, mask2] = cidr2.split('/');

    const intIp1 = ipToInt(ip1);
    const intIp2 = ipToInt(ip2);

    const start1 = intIp1 & ~((1 << (32 - mask1)) - 1);
    const end1 = start1 + ((1 << (32 - mask1)) - 1);

    const start2 = intIp2 & ~((1 << (32 - mask2)) - 1);
    const end2 = start2 + ((1 << (32 - mask2)) - 1);

    return start1 <= end2 && end1 >= start2;
};
