/**
 * @file  helper.js
 *
 * <AUTHOR>
 */

import _ from 'lodash';
import moment from 'moment';
import Big from 'big.js';
import {defineComponent} from 'san';
import {isSanCmpt, ServiceFactory, redirect} from '@baiducloud/runtime';
import * as SanUI from '@baiducloud/bce-ui/san';
import Confirm from '@/pages/sanPages/components/confirm';
import FLAG_CONFIG from '@/flags';

const AllRegion = window.$context.getEnum('AllRegion');

// 注册服务
export const $flag = FLAG_CONFIG;

// UTC时间转为本地时间
export const utcToTime = (utcTimeStr: string) => moment(utcTimeStr).format('YYYY-MM-DD HH:mm:ss');

// UTC时间转为本地时间
export const utcToDate = (utcTimeStr: string) => moment(utcTimeStr).format('YYYY-MM-DD');

// 本地时间转UTC时间
export const timeToUtc = (timeStr: string) => moment(timeStr).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';

// 获取当天零点
export const getStartTime = date => moment(date).format('YYYY-MM-DD 00:00:00');

// 获取当天24点
export const getEndTime = date => moment(date).format('YYYY-MM-DD 23:59:59');

// 13位时间戳转换格式
export const timestampFormat = value => (value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-');

// 所有时间相关值需要转换成13位时间戳
export const getDateValue = value => {
    if (!moment.isMoment(value)) {
        value = moment(value);
    }
    return value.valueOf() + '';
};

export const toTime = utcTimeStr => {
    return utcTimeStr ? moment(utcTimeStr).format('YYYY-MM-DD HH:mm:ss') : '';
};

export const getTicketLink = (text = '提交工单') => `
    <a href="http://ticket.bce.baidu.com/#/ticket/create" target="_blank">
        ${text}
    </a>
`;

// 获取当前用户id
export const getUserId = () => window.$context.getUserId();

// 焦点定位
export function focusTo(selector, pos) {
    $(selector)[0].scrollIntoView({behavior: 'smooth', block: pos});
}

// 获取vpc名称
export const getVpcName = name => {
    if (!name || name === 'default') {
        return '默认私有网络';
    }
    return _.escape(name);
};

// 获取子网名称
export const getSubnetName = name => {
    if (!name) {
        return '系统预定义子网';
    }

    return _.escape(name);
};
export const monitorDefaultPeriod = {
    '1h': 60,
    '6h': 300,
    '1d': 1200,
    '7d': 3600,
    '14d': 21600,
    '40d': 43200
};

/**
 * asDialog 兼容弹窗，舍弃er的url方式，通过传入component渲染弹窗，兼容bce-ui
 *
 * @export asDialg
 * @param {Object} Klass san component
 * @param {Object} options 弹窗属性
 * @param {boolean} options.needFoot 是否需要foot @default true
 * @param {string} options.title 弹窗标题
 * @param {any} options.width 弹窗宽度 @default auto
 * @param {Object} data 父组件需要传的参数
 * @return {Object} san 对象
 */
export function asDialog(Klass, options, data) {
    const dataTypes = _.keys(data || {});
    const klassTemplate =
        dataTypes.length <= 0
            ? '<x-biz s-ref="biz" on-close="onClose" data="{{data}}" />'
            : '<x-biz s-ref="biz" on-close="onClose" ' + _.map(dataTypes, prop => `${prop}="{{data.${prop}}}"`) + ' />';

    // 插入所有ui组件，兼容以继承Component的形式定义的组件
    const allComps = [];
    _.map(SanUI, (Com, name) => {
        let comp = {};
        if (isSanCmpt(Com)) {
            comp[`ui-${_.kebabCase(name)}`] = Com;
            allComps.push(comp);
        }
    });

    const WrappedComponent = defineComponent({
        template: `<template>
            <ui-dialog s-if="open"
                open="{{open}}"
                class="form_dialog"
                width="{{options.width}}"
                s-ref="dialog"
                foot="{{options.needFoot}}">
                <span slot="head">{{options.title}}</span>
                ${klassTemplate}
                <div slot="foot">
                    <ui-button on-click="onConfirmDialog" skin="primary">确定</ui-button>
                    <ui-button on-click="onCancel">取消</ui-button>
                </div>
            </ui-dialog>
        </template>`,
        components: _.extend(
            {
                'x-biz': Klass
            },
            ...allComps
        ),
        initData() {
            return {
                options: options,
                open: true,
                data: data
            };
        },
        messages: {
            resize() {
                this.ref('dialog').__resize();
            }
        },
        onConfirmDialog() {
            this.fire('confirm');
            const bizComponent = this.ref('biz');
            if (bizComponent.doSubmit) {
                bizComponent
                    .doSubmit()
                    .then(() => {
                        this.data.set('open', false);
                        this.fire('success');
                        this.dispose && this.dispose();
                    })
                    .catch(error => Promise.reject(error));
            } else {
                this.data.set('open', false);
            }
        },
        onCancel() {
            this.onClose();
        },
        onClose() {
            this.fire('close');
            this.data.set('open', false);
            this.dispose && this.dispose();
        }
    });

    return WrappedComponent;
}

// 兼容弹窗，包装没有dialog包裹的组件
export function waitActionDialog(component, dialogOptions, actionOptions) {
    const myOptions = _.extend(
        {
            head: true,
            width: 'auto',
            title: 'Dialog Title'
        },
        dialogOptions
    );

    const DialogComponent = asDialog(component, myOptions, actionOptions);
    const dialog = new DialogComponent();
    dialog.attach(document.body);
    return dialog;
}

// URL参数序列化为字符串
export const urlSerialize = query => {
    if (!query) {
        return '';
    }

    let search = '';
    for (let key in query) {
        if (query.hasOwnProperty(key)) {
            let value = query[key];

            // 如果`value`是数组，需要转一下
            if (Array.isArray(value)) {
                value = JSON.stringify(value);
            }

            // 如果`value`是数组，其`toString`会自动转为逗号分隔的字符串
            search += '&' + encodeURIComponent(key) + '=' + encodeURIComponent(value);
        }
    }

    return search.slice(1);
};

export const getQueryString = name => {
    let reg = new RegExp('(^|&|\\?)' + name + '=([^&]*)(&|$)');
    if (reg.test(window.location.href)) {
        return unescape(RegExp.$2.replace(/\+/g, ' '));
    }
    return '';
};

// 变更计费方式
export let alterProductType = (serviceType, ids, type, region, confirmV2Url) => {
    let param = {
        serviceType,
        instanceIds: ids.join(','),
        type: type || 'TO_PREPAY',
        region: region || window.$context.getCurrentRegionId()
    };
    if (confirmV2Url) {
        param.confirmV2Url = confirmV2Url;
    }
    redirect({
        module: 'billing',
        path: '/billing/alter/productTypeList',
        paramSeperator: '~',
        params: param
    });
};

// url序列化
export const serializeQuery = query => {
    if (!query) {
        return '';
    }

    let search = '';
    for (const key in query) {
        if (query.hasOwnProperty(key)) {
            const value = query[key];

            // 如果`value`是数组，其`toString`会自动转为逗号分隔的字符串
            search += '&' + encodeURIComponent(key) + '=' + encodeURIComponent(value.toString());
        }
    }

    return search.slice(1);
};

export let recharge = (serviceType, ids, region, confirmV2Url, extraOptions) => {
    let param = {
        serviceType,
        instanceIds: ids.join(','),
        // 该参数一般用于产品（通常是global）续费时需要特殊指定资源所处区域的情况
        // 目前仅 BCH 携带 region 参数，其余产品按照全局的 region 设置处理
        region: region || window.$context.getCurrentRegionId()
    };

    if (confirmV2Url) {
        param.confirmV2Url = confirmV2Url;
    }

    // 把续费的配置完全交给各产品处理，防止类似 rechargeType 这种写死的情况，导致产品和common的上线依赖
    if (_.isObject(extraOptions)) {
        _.extend(param, extraOptions);
    }
    redirect({
        module: 'billing',
        path: '/billing/recharge/list',
        paramSeperator: '~',
        params: param
    });
};

// 下单成功跳转地址
export let getSuccessUrlParam = (serviceType, isRecharge, orderType = 'NORMAL') => {
    const orderSuccessUrl = window.$context.getOrderSuccessUrl();
    if (isRecharge) {
        return orderSuccessUrl && orderSuccessUrl.rechargeUrl
            ? orderSuccessUrl.rechargeUrl
            : {module: 'billing', path: '/billing/recharge/success'};
    }
    // 计费变更成功页
    if (orderType === 'TO_PREPAY' || orderType === 'TO_POSTPAY') {
        return orderSuccessUrl && orderSuccessUrl.alterUrl
            ? orderSuccessUrl.alterUrl
            : {module: 'billing', path: '/billing/alter/success'};
    }
    let config =
        orderSuccessUrl && orderSuccessUrl.customUrl && orderSuccessUrl.customUrl[serviceType]
            ? orderSuccessUrl.customUrl[serviceType]
            : {};

    return config[orderType] || {module: 'billing', path: '/order/success'};
};

/**
 * 根据`query`规则解析字符串并返回参数对象
 *
 * @param {string} str query字符串， *不能* 有起始的`?`或`#`字符
 * @return {Object} 从`str`解析得到的参数对象
 * @static
 */
// export const parseQuery = str => {
//     const pairs = str.split('&');
//     let query = {};
//     for (let i = 0; i < pairs.length; i++) {
//         // 考虑到有可能因为未处理转义问题，
//         // 导致value中存在**=**字符，因此不使用`split`函数
//         const pair = pairs[i];
//         if (!pair) {
//             continue;
//         }
//         const index = pair.indexOf('=');
//         // 没有**=**字符则认为值是**true**
//         const key = index < 0
//             ? decodeURIComponent(pair)
//             : decodeURIComponent(pair.slice(0, index));
//         const value = index < 0
//             ? true
//             : decodeURIComponent(pair.slice(index + 1));

//         // 已经存在这个参数，且新的值不为空时，把原来的值变成数组
//         if (query.hasOwnProperty(key)) {
//             if (value !== true) {
//                 query[key] = [].concat(query[key], value);
//             }
//         }
//         else {
//             query[key] = value;
//         }
//     }
//     return query;
// };

// 较为精确的加法
// export function accAdd(arg1, arg2) {
//     let r1;
//     let r2;
//     let m;
//     let c;

//     try {
//         r1 = arg1.toString().split('.')[1].length;
//     }
//     catch (e) {
//         r1 = 0;
//     }

//     try {
//         r2 = arg2.toString().split('.')[1].length;
//     }
//     catch (e) {
//         r2 = 0;
//     }

//     c = Math.abs(r1 - r2);
//     m = Math.pow(10, Math.max(r1, r2));
//     if (c > 0) {
//         let cm = Math.pow(10, c);
//         if (r1 > r2) {
//             arg1 = Number(arg1.toString().replace('.', ''));
//             arg2 = Number(arg2.toString().replace('.', '')) * cm;
//         }
//         else {
//             arg1 = Number(arg1.toString().replace('.', '')) * cm;
//             arg2 = Number(arg2.toString().replace('.', ''));
//         }
//     }
//     else {
//         arg1 = Number(arg1.toString().replace('.', ''));
//         arg2 = Number(arg2.toString().replace('.', ''));
//     }
//     return (arg1 + arg2) / m;

// }

// const resolveVariable = (path, data) => {
//     if (!path) {
//         return undefined;
//     }

//     if (path[0] === '$') {
//         path = path.substring(1);
//     }

//     if (typeof data[path] !== 'undefined') {
//         return data[path];
//     }

//     let parts = path.replace(/^{|}$/g, '').split('.');
//     return parts.reduce((data, path) => {
//         if ((_.isObject(data) || Array.isArray(data)) && path in data) {
//             return data[path];
//         }
//         return undefined;
//     }, data);
// };

export const safeMulti = (lhs, rhs) => {
    let value = new Big(lhs).times(rhs).toString();
    return +value;
};

/**
 * 适配单价 时分秒的转换
 *
 * @param {number} price 单价
 * @param {string} unit 单位（second、minute、hour）默认按照minute处理
 * @return {Object} 返回三个转换函数
 */
export const convertPrice = (price, unit) => {
    // 根据单位统一转换为 元/分,如果非数字（NaN也划为非数字）则显示-
    price = parseFloat(price);
    price = price === 0 ? 0 : price || '-';
    let isValid = _.isNumber(price);

    if (isValid) {
        switch (unit) {
            case 'second':
                price = safeMulti(price, 60);
                break;
            case 'minute':
                break;
            case 'hour':
                price = price / 60;
                break;
        }
    }
    // 返回三个转换函数，分别对象 元/分 元/秒 元/时
    // suffix 返回后缀 你可以不写，考虑展现形式的不同 例如 ￥1元/分 ￥1/Min 等等
    // 规则是：保留所有效小数位，至少保留两位
    return {
        getPriceOfMinute(suffix) {
            if (isValid) {
                let decimal = price.toString().split('.')[1] || '';
                if (decimal.length < 2) {
                    price = price.toFixed(2);
                }

                return suffix ? price + suffix : price;
            }

            return suffix ? '-' + suffix : '-';
        },
        getPriceOfHour(suffix) {
            price = safeMulti(price, 60);

            if (isValid) {
                let decimal = price.toString().split('.')[1] || '';

                if (decimal.length < 2) {
                    price = price.toFixed(2);
                }

                return suffix ? price + suffix : price;
            }

            return suffix ? '-' + suffix : '-';
        }
    };
};

export const showMoney = (value, optPrecision) => {
    let valueF = parseFloat(value);
    if (isNaN(valueF)) {
        return '-';
    }
    // 对于负数需要先转为正数，否则后面的处理方法会有问题
    let sign = valueF >= 0 ? 1 : -1;
    valueF = valueF * sign;

    let precision = optPrecision || 2;
    let E = Math.pow(10, precision);

    // 对于丢失精度的情况，加上一个极小的数值，**尽量**的恢复精度
    // 一般来说，对于精度不准确的时候，我们面临2中选择，+ 还是 -
    // 例如：
    // 0.1 + 0.2 = 0.30000000000000004
    // 22.9 * 11 = 251.89999999999998
    // 第一种 case 需要减去某个值，第二种 case 需要加上某个值
    // 不过这里统一按照加上某个值来处理，因为不会影响最后的结果
    valueF = valueF + 1e-10;

    let rv = new Big(valueF)
        .times(E) // valueF * E
        .round(0, 0) // 舍弃小数点后的内容
        .div(E); // valueF / E

    // 之前转换为正数的负数需要转换回去
    return (rv * sign).toFixed(precision);
};

export let getConfig = key => {
    return new Promise((resolve, reject) => {
        let moduleId = window.$context.isOnline ? 'config/online' : 'config/qasandbox';
        // 真正的配置内容在 config[key].data 里面
        // config[key].meta 信息主要记录一下更改记录
        window.require([moduleId], config => resolve(config[key] ? config[key].data : null));
    });
};

export let getEtAvaliableRegion = (context, allApAddr) => {
    let serviceConfig = context.SERVICE_TYPE['ET']; // eslint-disable-line
    let supportRegion = {};
    if (serviceConfig) {
        supportRegion = serviceConfig.region;
    }
    const apaddrs = _.groupBy(allApAddr, 'region');
    const regionList = [];
    _.each(_.keys(apaddrs), item => {
        if (supportRegion[item]) {
            regionList.push({
                text: AllRegion.getTextFromValue(item),
                value: item
            });
        }
    });
    return {regionList, apaddrs};
};

export let getVpcAvaliableRegion = () => {
    let serviceConfig = window.$context.SERVICE_TYPE['NETWORK']; // eslint-disable-line
    let supportRegion = {};
    if (serviceConfig) {
        supportRegion = serviceConfig.region;
    }
    const regionList = [];
    _.each(_.keys(supportRegion), item => {
        regionList.push({
            text: AllRegion.getTextFromValue(item),
            value: item
        });
    });
    return {regionList};
};

// 请求配置
export const kXhrOptions = {
    silent: {'x-silent': true}, // 无loading、无弹窗报错
    silence: {'X-silence': true}, // 无loading、有弹窗报错
    customSilent: {
        // 切换账号跳转不报错
        'x-silent': true,
        'custom-silence': true
    }
};

// 判断数组内元素是否完全相同
export const isSameArray = array => {
    if (Array.isArray(array)) {
        return new Set(array).size === 1;
    }
    return false;
};

/**
 * @description 根据最大、最小及间隔数返回每步展示值
 * @param {number} max 最大值
 * @param {number} min 最小值
 * @param {number} step 间隔数
 */
export const getMarksByStep = (max: number, min: number, step: number) => {
    const marks = {};
    const stepNum = _.round(max / step);
    for (let i = min; i < max; i += stepNum) {
        if (i > min) {
            marks[i - min] = i - min + 'Mbps';
        } else {
            marks[i] = i + 'Mbps';
        }
    }
    marks[max] = max + 'Mbps';
    return marks;
};

export const eipBlackTip = `禁止当前操作，请参照
<a href="http://security.baidu.com/ssp/web/#/require/work/detail?id=105&from=page" target="_blank">《百度内部业务上百度公有云安全规范》</a>要求处置`;

export const contextPipe = data => {
    return {
        currentLanguage: window.$context.currentLanguage,
        SERVICE_TYPE: window.$context.SERVICE_TYPE,
        getCsrfToken() {
            return window.$context.getCsrfToken();
        },
        getCurrentRegion: window.$context.getCurrentRegion.bind(data)
    };
};

export const getVPCSupportRegion = window => {
    let regionKeys = Object.keys(window.$context.SERVICE_TYPE['NETWORK'].region); // eslint-disable-line
    let array = regionKeys.map(item => {
        return {
            text: window.$context.SERVICE_TYPE['NETWORK'].region[item], // eslint-disable-line
            value: item
        };
    });
    return array;
};

export const confirmValidate = (
    data: {title: string; content: string},
    confirmCallback?: Function,
    cancelCallback?: Function
) => {
    const confirm = new Confirm({
        data
    });
    confirm.attach(document.body);
    confirm.on('confirm', confirmCallback);
    confirm.on('close', cancelCallback);
};

export const validateAccountIsSame = (account: string) => {
    if (!account || typeof account !== 'string') return;
    const currentUser = getUserId();
    let flag = false;
    const sliceFroFiveBackFive = (acc: string) => {
        return acc.slice(0, 5) + acc.slice(27);
    };
    if (account.length === 32) {
        flag = sliceFroFiveBackFive(currentUser) === sliceFroFiveBackFive(account);
    }
    return flag;
};

// 模拟lodash uniqBy方法
export const uniqBy = (arr: any[], filterKey: string) => {
    const result = [];
    const seen = new Set();

    for (const item of arr) {
        const keyVal = item[filterKey];
        if (!seen.has(keyVal)) {
            seen.add(keyVal);
            result.push(item);
        }
    }
    return result;
};

export function svgToBase64(svgElement) {
    // 序列化SVG元素
    const serializer = new XMLSerializer();
    const svgString = serializer.serializeToString(svgElement);
    // 对SVG字符串进行Base64编码
    const base64String = btoa(svgString);
    // 返回Base64编码过的字符串
    return 'data:image/svg+xml;base64,' + base64String;
}

export const removeStrAllSpace = (str: string) => {
    if (!str) return;
    return str.replace(/\s+/g, '');
};

// 网络诊断服务路由匹配
export const netDiagnosisMatchUrl = (url: string) => {
    const matchUrlArr = [
        '#/vpc/flowlog/list',
        '#/vpc/mirror/list',
        '#/vpc/filterRuleGroup/list',
        '#/vpc/probe/list',
        '#/vpc/pathanalise/list',
        '#/vpc/instance/diagnosis'
    ];
    let flag = false;
    if (url) {
        flag = matchUrlArr.some(item => url.includes(item));
    }
    return flag;
};

/** 解密url 参数 */
export const decryptUrlBtoa = (query: string) => {
    if (typeof query !== 'string') {
        return {};
    }
    try {
        let decryptQuery = atob(query);
        decryptQuery = decodeURIComponent(decryptQuery);
        return JSON.parse(decryptQuery);
    } catch (err) {
        return {};
    }
};

/** 替换字符串中所有空格 */
export const replaceAllSpaceStr = (str: string) => {
    if (typeof str !== 'string') {
        return str;
    }
    return str.replace(/\s+/g, '');
};

/** 两个对象数组找交集 hack 当前lodash版本无intersectionBy */
export const intersectionBy = (preArr: Array<any>, currArr: Array<any>, key: string) => {
    return preArr.filter(item => currArr.some(curr => item[key] === curr[key]));
};
