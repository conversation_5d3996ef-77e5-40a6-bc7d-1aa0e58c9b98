import React, {useState, useEffect, useMemo} from 'react';
import {parseQuery} from '@/utils';
import {getSecurityDetail} from '@/apis';
import {useGlobalContext} from '@/context';
import Detail from '@/pages/sanPages/pages/security/detail/detail';
import InstanceList from '@/pages/sanPages/pages/security/detail/instanceList/instance-list';
import DetailPage from '@/components/DetailPage';
import {cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
import './index.less';

const SecurityDetail = () => {
    const params: any = parseQuery(location.hash);
    const [{globalState}] = useGlobalContext() as any;
    const [detail, setDetail] = useState<Record<string, any>>({});
    const [inDdcWhite, setInDdcWhite] = useState<boolean>(true);
    const [inGaiaDbWhite, setInGaiaDbWhite] = useState<boolean>(true);

    const handleUpdate = () => {
        getDetail();
    };
    const panesData = [
        {
            key: '/vpc/security/detail',
            tab: i18n.t('安全组详情'),
            content: <Detail context={{...params, instance: detail, updateName: handleUpdate}} />
        },
        {
            key: '/vpc/security/detail/bcc',
            tab: i18n.t('关联云服务器'),
            content: <InstanceList context={{...params, type: 'bcc'}} />
        },
        {
            key: '/vpc/security/detail/eni',
            tab: i18n.t('关联弹性网卡'),
            content: <InstanceList context={{...params, type: 'eni'}} />
        },
        {
            key: '/vpc/security/detail/snic',
            tab: i18n.t('关联服务网卡'),
            content: <InstanceList context={{...params, type: 'snic'}} />
        },
        {
            key: '/vpc/security/detail/bbc',
            tab: i18n.t('关联弹性裸金属服务器'),
            content: <InstanceList context={{...params, type: 'bbc'}} />
        },
        {
            key: '/vpc/security/detail/blb',
            tab: i18n.t('关联负载均衡'),
            content: <InstanceList context={{...params, type: 'blb'}} />
        },
        {
            key: '/vpc/security/detail/ddc',
            tab: i18n.t('关联云数据库专属集群'),
            content: <InstanceList context={{...params, type: 'ddc'}} />
        },
        {
            key: '/vpc/security/detail/scs',
            tab: i18n.t('关联云数据库 Redis'),
            content: <InstanceList context={{...params, type: 'scs'}} />
        },
        {
            key: '/vpc/security/detail/rds',
            tab: i18n.t('关联云数据库RDS'),
            content: <InstanceList context={{...params, type: 'rds'}} />
        },
        {
            key: '/vpc/security/detail/gaiadb',
            tab: i18n.t('关联云数据库GaiaDB-S'),
            content: <InstanceList context={{...params, type: 'gaiadb'}} />
        }
    ];
    const checkWhiteRegion = () => {
        const whiteList = globalState.commonWhite;
        setInDdcWhite(whiteList?.DdcSgWhiteList);
        setInGaiaDbWhite(whiteList?.GaiaDbSgRegionWhiteList);
    };
    const getDetail = () => {
        getSecurityDetail({id: params.id})
            .then(res => {
                if (res) {
                    setDetail(res);
                }
                checkWhiteRegion();
            })
            .catch(e => {});
    };
    useEffect(() => {
        getDetail();
    }, []);
    const inDdcWhitePanesData = useMemo(() => {
        const newPanesData = [...panesData];
        if (!inDdcWhite) {
            newPanesData.splice(6, 1);
        }
        return newPanesData;
    }, [inDdcWhite, detail]);
    const inGaiaDbWhitePanesData = useMemo(() => {
        const newPanesData = [...inDdcWhitePanesData];
        if (!inGaiaDbWhite) {
            newPanesData.splice(newPanesData.length - 1, 1);
        }
        return newPanesData;
    }, [inGaiaDbWhite, inDdcWhitePanesData, detail]);
    return (
        <>
            <DetailPage
                mode="vertical"
                headerName={detail.name}
                backUrl="#/vpc/security/list"
                panesData={inGaiaDbWhitePanesData}
            />
        </>
    );
};
export default SecurityDetail;
