import React, {useState, useEffect} from 'react';
import {parseQuery} from '@/utils';
import {useRequest} from 'ahooks';
import {getEnterpriseSecDetail} from '@/apis';
import Detail from '@/pages/sanPages/pages/enterpriseSecurity/detail/detail';
import InstanceList from '@/pages/sanPages/pages/enterpriseSecurity/detail/instanceList/instance-list';
import DetailPage from '@/components/DetailPage';
import {cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
import './index.less';

const EnterpriseSecDetail = () => {
    const params: any = parseQuery(location.hash);
    const {data = {} as any, loading, error, run} = useRequest(() => getEnterpriseSecDetail({esgUuid: params.id}));

    const handleUpdate = () => {
        run();
    };
    const panesData = [
        {
            key: '/vpc/enterpriseSecurity/detail',
            tab: i18n.t('安全组详情'),
            content: <Detail context={{...params, instance: data, updateName: handleUpdate}} />
        },
        {
            key: '/vpc/enterpriseSecurity/detail/bcc',
            tab: i18n.t('关联云服务器'),
            content: <InstanceList context={{...params, type: 'bcc'}} />
        },
        {
            key: '/vpc/enterpriseSecurity/detail/eni',
            tab: i18n.t('关联弹性网卡'),
            content: <InstanceList context={{...params, type: 'eni'}} />
        },
        {
            key: '/vpc/enterpriseSecurity/detail/snic',
            tab: i18n.t('关联服务网卡'),
            content: <InstanceList context={{...params, type: 'snic'}} />
        },
        {
            key: '/vpc/enterpriseSecurity/detail/bbc',
            tab: i18n.t('关联弹性裸金属服务器'),
            content: <InstanceList context={{...params, type: 'bbc'}} />
        },
        {
            key: '/vpc/enterpriseSecurity/detail/blb',
            tab: i18n.t('关联负载均衡'),
            content: <InstanceList context={{...params, type: 'blb'}} />
        }
    ];
    return (
        <>
            <DetailPage
                mode="vertical"
                headerName={data.name}
                backUrl="#/vpc/enterpriseSecurity/list"
                panesData={panesData}
            />
        </>
    );
};
export default EnterpriseSecDetail;
