import React, {useCallback, useRef, useState, useMemo} from 'react';
import {OutlinedInfoCircle} from 'acud-icon';
import SecurityTabs from '@/components/TabPage';
import {DocService} from '@/pages/sanPages/common';
import Security from '@/pages/sanPages/pages/security/list/list';
import EnterpriseSecurity from '@/pages/sanPages/pages/enterpriseSecurity/list/list';
import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import {cbaI18nInstance as i18n,Trans} from '@baidu/bce-react-toolkit';
import './index.less';

const SecurityList = props => {
    const securityRef = useRef<Function>();
    const enterpriseRef = useRef<Function>();
    const [secHide, setSecHide] = useState(false);
    const [entHide, setEntHide] = useState(false);
    const type = location.hash.includes('/vpc/enterpriseSecurity/list') ? 'enterprise' : 'security';

    const handleSaveCallback = useCallback((fn: Function) => {
        if (type === 'security') {
            securityRef.current = fn;
        } else {
            enterpriseRef.current = fn;
        }
    }, []);
    const handleShowIntroduce = () => {
        if (type === 'security') {
            securityRef.current();
            if (!secHide) {
                setSecHide(true);
            }
        } else {
            enterpriseRef.current();
            if (!entHide) {
                setEntHide(true);
            }
        }
    };
    const handleToggle = () => {
        if (type === 'security') {
            setSecHide(!secHide);
        } else {
            setEntHide(!entHide);
        }
    };
    const currHide = useMemo(() => {
        return type === 'security' ? secHide : entHide;
    }, [secHide, entHide]);

    const panesData = useMemo(() => {
        return [
            {
                key: '/vpc/security/list',
                tab: i18n.t('普通安全组'),
                content: <Security context={{callbackFn: handleSaveCallback, handleToggle}} />
            },
            {
                key: '/vpc/enterpriseSecurity/list',
                tab: i18n.t('企业安全组'),
                content: <EnterpriseSecurity context={{callbackFn: handleSaveCallback, handleToggle}} />
            }
        ];
    }, []);
    return (
        <div className="security-group-widget">
            <div className="security-header">
                <Trans>
                <h2 className="title">安全组</h2>
                </Trans>
                <div className="header-right">
                    {/* <div className="common">
                        <a onClick={handleShowIntroduce} target="_blank" className="help-file">
                            <img src={currHide ? INTRODUCE_ICON : INTRODUCE_ICON_COLOR} />
                            功能简介
                        </a>
                    </div> */}
                    <div className="common">
                        <Trans>
                        <a href={DocService.security_helpFile} target="_blank" className="help-file">
                            <OutlinedInfoCircle color="#2468F2" width="16" height="16" />
                            帮助文档
                        </a>
                        </Trans>
                    </div>
                </div>
            </div>
            <SecurityTabs panesData={panesData} />
        </div>
    );
};
export default SecurityList;
