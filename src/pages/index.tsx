/* eslint-disable prettier/prettier */
import {MenuItem, recursiveMenus,cbaI18nInstance as i18n,} from '@baidu/bce-react-toolkit';
import React, {lazy} from 'react';
import {vpcModulePages, dcPageUrl, ndsPageUrl, crossDcPageUrl, publcServcie, addLocaleToLink} from '@/utils';
import FLAG from '@/flags';
import MenuIcon from '@/components/MenuIcon';
import './sanPages';

const isXSConsole = FLAG.NetworkSupportXS;
const linkTarget = isXSConsole ? '_self' : '_blank';

/** vpc控制台 */
const Overview = lazy(
    () =>
        import(/* webpackChunkName: "Overview" */ '@/pages/overview') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const SelfProblemDiagnosis = lazy(
    () =>
        import(
            /* webpackChunkName: "SelfProblemDiagnosis" */ '@/pages/sanPages/pages/diagnosis/page/list'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const NetworkToPo = lazy(
    () =>
        import(/* webpackChunkName: "NetworkToPo" */ '@/pages/sanPages/pages/topology/topology') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const VpcCreate = lazy(
    () =>
        import(
            /* webpackChunkName: "VpcCreate" */ '@/pages/sanPages/pages/instance/page/create/create'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const VpcList = lazy(
    () =>
        import(/* webpackChunkName: "VpcList" */ '@/pages/sanPages/pages/instance/page/list') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const VpcDetail = lazy(
    () =>
        import(
            /* webpackChunkName: "VpcDetail" */ '@/pages/sanPages/pages/instance/page/detail'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const SubnetCreate = lazy(
    () =>
        import(
            /* webpackChunkName: "SubnetCreate" */ '@/pages/sanPages/pages/subnet/page/create'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const SubnetList = lazy(
    () =>
        import(/* webpackChunkName: "SubnetList" */ '@/pages/sanPages/pages/subnet/page/list') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const SubnetDetail = lazy(
    () =>
        import(/* webpackChunkName: "SubnetDetail" */ '@/pages/subnet/detail') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const RouteList = lazy(
    () =>
        import(/* webpackChunkName: "RouteList" */ '@/pages/sanPages/pages/route/page/list') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const RouteCreate = lazy(
    () =>
        import(/* webpackChunkName: "RouteCreate" */ '@/pages/sanPages/pages/route/page/create') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const RouteDetail = lazy(
    () =>
        import(/* webpackChunkName: "RouteDetail" */ '@/pages/route/detail') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const EniCreate = lazy(
    () =>
        import(
            /* webpackChunkName: "EniCreate" */ '@/pages/sanPages/pages/eni/page/create/create'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const EniList = lazy(
    () =>
        import(/* webpackChunkName: "EniList" */ '@/pages/sanPages/pages/eni/page/list/list') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const EniDetail = lazy(
    () =>
        import(/* webpackChunkName: "EniDetail" */ '@/pages/eni/detail') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const SnicCreate = lazy(
    () =>
        import(
            /* webpackChunkName: "SnicCreate" */ '@/pages/sanPages/pages/endpoint/page/create/createIndex'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const SnicUpgrade = lazy(
    () =>
        import(
            /* webpackChunkName: "SnicUpgrade" */ '@/pages/sanPages/pages/endpoint/page/upgrade/upgrade'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const SnicList = lazy(
    () =>
        import(
            /* webpackChunkName: "SnicList" */ '@/pages/sanPages/pages/endpoint/page/list/list'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const SnicDetail = lazy(
    () =>
        import(/* webpackChunkName: "SnicDetail" */ '@/pages/snic/detail') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const HavipList = lazy(
    () =>
        import(
            /* webpackChunkName: "HavipList" */ '@/pages/sanPages/pages/havip/page/list/list'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const HavipDetail = lazy(
    () =>
        import(/* webpackChunkName: "HavipDetail" */ '@/pages/havip/detail') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const SecurityList = lazy(
    () =>
        import(/* webpackChunkName: "SecurityList" */ '@/pages/security/list') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const SecurityCreate = lazy(
    () =>
        import(
            /* webpackChunkName: "SecurityCreate" */ '@/pages/sanPages/pages/security/create/create'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const SecurityDetail = lazy(
    () =>
        import(/* webpackChunkName: "SecurityDetail" */ '@/pages/security/securityDetail') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const EnterpriseSecCreate = lazy(
    () =>
        import(
            /* webpackChunkName: "EnterpriseSecCreate" */ '@/pages/sanPages/pages/enterpriseSecurity/create/create'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const EnterpriseSecDetail = lazy(
    () =>
        import(
            /* webpackChunkName: "EnterpriseSecDetail" */ '@/pages/security/enterpriseSecDetail'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const AclList = lazy(
    () =>
        import(/* webpackChunkName: "AclList" */ '@/pages/sanPages/pages/acl/page/list') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const AclManage = lazy(
    () =>
        import(/* webpackChunkName: "AclManage" */ '@/pages/sanPages/pages/acl/page/manage') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const NatAuth = lazy(
    () =>
        import(/* webpackChunkName: "NatAuth" */ '@/pages/sanPages/pages/nat/auth/auth') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const NatCreate = lazy(
    () =>
        import(
            /* webpackChunkName: "NatCreate" */ '@/pages/sanPages/pages/nat/create/createIndex'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const NatList = lazy(
    () =>
        import(/* webpackChunkName: "NatList" */ '@/pages/nat/list') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const NatUpgrade = lazy(
    () =>
        import(
            /* webpackChunkName: "NatUpgrade" */ '@/pages/sanPages/pages/nat/upgrade/upgrade'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const NatDetail = lazy(
    () =>
        import(/* webpackChunkName: "NatDetail" */ '@/pages/nat/detail') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const IPv6List = lazy(
    () =>
        import(/* webpackChunkName: "IPv6List" */ '@/pages/sanPages/pages/ipv6gw/list/list') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const IPv6Create = lazy(
    () =>
        import(
            /* webpackChunkName: "IPv6Create" */ '@/pages/sanPages/pages/ipv6gw/create/createIndex'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const IPv6Detail = lazy(
    () =>
        import(
            /* webpackChunkName: "IPv6Detail" */ '@/pages/sanPages/pages/ipv6gw/detail/detail'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const IPv6Upgrade = lazy(
    () =>
        import(
            /* webpackChunkName: "IPv6Upgrade" */ '@/pages/sanPages/pages/ipv6gw/upgrade/upgrade'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const VpnList = lazy(
    () =>
        import(/* webpackChunkName: "VpnList" */ '@/pages/vpn/list') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const VpnDetail = lazy(
    () =>
        import(/* webpackChunkName: "VpnDetail" */ '@/pages/vpn/detail') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const VpnConnCreate = lazy(
    () =>
        import(
            /* webpackChunkName: "VpnConnCreate" */ '@/pages/sanPages/pages/vpn/conn/create/create'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const SslVpnConnDetail = lazy(
    () =>
        import(/* webpackChunkName: "SslVpnConnDetail" */ '@/pages/vpn/sslVpnConnDetail') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const SslVpnUgrade = lazy(
    () =>
        import(
            /* webpackChunkName: "SslVpnUgrade" */ '@/pages/sanPages/pages/vpn/updateClient/upgrade'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const VpnCreate = lazy(
    () =>
        import(
            /* webpackChunkName: "VpnCreate" */ '@/pages/sanPages/pages/vpn/create/createIndex'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const VpnAuth = lazy(
    () =>
        import(/* webpackChunkName: "VpnAuth" */ '@/pages/sanPages/pages/vpn/auth/auth') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const PeerconnCreate = lazy(
    () =>
        import(
            /* webpackChunkName: "PeerconnCreate" */ '@/pages/sanPages/pages/peerconn/create/create'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const PeerconnUpgrade = lazy(
    () =>
        import(
            /* webpackChunkName: "PeerconnUpgrade" */ '@/pages/sanPages/pages/peerconn/upgrade/upgrade'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const PeerconnRecharge = lazy(
    () =>
        import(
            /* webpackChunkName: "PeerconnRecharge" */ '@/pages/sanPages/pages/peerconn/recharge/recharge'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const PeerconnUpgradeSuccess = lazy(
    () =>
        import(
            /* webpackChunkName: "PeerconnUpgradeSuccess" */ '@/pages/sanPages/pages/peerconn/upgrade/success'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const PeerconnList = lazy(
    () =>
        import(/* webpackChunkName: "PeerconnList" */ '@/pages/peerconn/list') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const PeerconnCrossAudit = lazy(
    () =>
        import(
            /* webpackChunkName: "PeerconnCrossAudit" */ '@/pages/sanPages/pages/peerconn/create/audit/audit'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const PeerconnCrossAuditing = lazy(
    () =>
        import(
            /* webpackChunkName: "PeerconnCrossAuditing" */ '@/pages/sanPages/pages/peerconn/create/audit/auditing'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const PeerconnAuth = lazy(
    () =>
        import(
            /* webpackChunkName: "PeerconnAuth" */ '@/pages/sanPages/pages/peerconn/auth/auth'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const PeerconnDetail = lazy(
    () =>
        import(/* webpackChunkName: "PeerconnDetail" */ '@/pages/peerconn/detail') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const DcgwCreate = lazy(
    () =>
        import(
            /* webpackChunkName: "DcgwCreate" */ '@/pages/sanPages/pages/dcgw/page/create/create'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const DcgwList = lazy(
    () =>
        import(/* webpackChunkName: "DcgwList" */ '@/pages/sanPages/pages/dcgw/page/list/list') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const DcgwDetail = lazy(
    () =>
        import(/* webpackChunkName: "DcgwDetail" */ '@/pages/dcgw/detail') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const L2gwCreate = lazy(
    () =>
        import(
            /* webpackChunkName: "L2gwCreate" */ '@/pages/sanPages/pages/l2gw/page/create/create'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const L2gwList = lazy(
    () =>
        import(/* webpackChunkName: "L2gwList" */ '@/pages/sanPages/pages/l2gw/page/list/list') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const L2gwDetail = lazy(
    () =>
        import(/* webpackChunkName: "L2gwDetail" */ '@/pages/l2gw/detail') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);

const L2gwChannelDetail = lazy(
    () =>
        import(/* webpackChunkName: "L2gwChannelDetail" */ '@/pages/l2gw/l2gwChannelDetail') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);

const L2gwConnCreate = lazy(
    () =>
        import(
            /* webpackChunkName: "L2gwConnCreate" */ '@/pages/sanPages/pages/l2gw/page/conn/create/create'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);

const MulticastingList = lazy(
    () =>
        import(
            /* webpackChunkName: "MulticastingList" */ '@/pages/sanPages/pages/multicasting/list/list'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);

const MulticastingCreate = lazy(
    () =>
        import(
            /* webpackChunkName: "MulticastingCreate" */ '@/pages/sanPages/pages/multicasting/create/create'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);

const MulticastingDetail = lazy(
    () =>
        import(/* webpackChunkName: "MulticastingDetail" */ '@/pages/multicasting/detail') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);

const QosList = lazy(
    () =>
        import(/* webpackChunkName: "QosList" */ '@/pages/sanPages/pages/qos/list/list') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const QosCreate = lazy(
    () =>
        import(/* webpackChunkName: "QosCreate" */ '@/pages/sanPages/pages/qos/create/create') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const QosDetail = lazy(
    () =>
        import(/* webpackChunkName: "QosDetail" */ '@/pages/sanPages/pages/qos/detail/detail') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const GatewayLimit = lazy(
    () =>
        import(/* webpackChunkName: "GatewayLimit" */ '@/pages/gateway/list') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const GatewayCreate = lazy(
    () =>
        import(
            /* webpackChunkName: "GatewayCreate" */ '@/pages/sanPages/pages/gateway/create/create'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const ParamTemplate = lazy(
    () =>
        import(/* webpackChunkName: "ParamTemplate" */ '@/pages/param/list') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const ParamsIpDetail = lazy(
    () =>
        import(/* webpackChunkName: "ParamsIpDetail" */ '@/pages/param/ipAddrDetail') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const ParamsIpGroupDetail = lazy(
    () =>
        import(/* webpackChunkName: "ParamsIpGroupDetail" */ '@/pages/param/ipGroupAddrDetail') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const DcDrill = lazy(
    () =>
        import(/* webpackChunkName: "DcDrill" */ '@/pages/dc/drill/index') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const DcDrillDetail = lazy(
    () =>
        import(/* webpackChunkName: "DcDrill" */ '@/pages/dc/drill/components/detail') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
/** 菜单定义 */
export const menuList: MenuItem[] = [
    {
        menuName: i18n.t('概览'),
        key: vpcModulePages.overview.index,
        isNavMenu: true,
        Component: Overview,
        icon: <MenuIcon />,
        isPageWrapperNotRequired: true,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('自助问题诊断'),
        key: vpcModulePages.selfProblemDiagnose.index,
        isNavMenu: true,
        Component: SelfProblemDiagnosis,
        isPageWrapperNotRequired: true,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('网络拓扑'),
        key: vpcModulePages.topo.index,
        isNavMenu: true,
        Component: NetworkToPo,
        isPageWrapperNotRequired: true,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('创建私有网络'),
        key: vpcModulePages.vpc.vpcCreate,
        Component: VpcCreate,
        isPageWrapperNotRequired: true
    },
    {
        menuName: i18n.t('私有网络'),
        key: vpcModulePages.vpc.index,
        isNavMenu: true,
        Component: VpcList,
        isPageWrapperNotRequired: true,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('私有网络详情'),
        key: vpcModulePages.vpc.vpcDetail,
        Component: VpcDetail,
        isPageWrapperNotRequired: true
    },
    {
        menuName: i18n.t('创建子网'),
        key: vpcModulePages.subnet.subnetCreate,
        Component: SubnetCreate,
        isPageWrapperNotRequired: true
    },
    {
        menuName: i18n.t('子网'),
        key: vpcModulePages.subnet.index,
        isNavMenu: true,
        Component: SubnetList,
        isPageWrapperNotRequired: true,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('子网详情'),
        key: vpcModulePages.subnet.subnetDetail,
        Component: SubnetDetail,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('IP地址管理'),
        key: vpcModulePages.subnet.subnetIP,
        Component: SubnetDetail,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('预留网段'),
        key: vpcModulePages.subnet.subnetReverse,
        Component: SubnetDetail,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('嵌套地址管理'),
        key: vpcModulePages.subnet.subnetPool,
        Component: SubnetDetail,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('路由表'),
        key: vpcModulePages.route.index,
        isNavMenu: true,
        Component: RouteList,
        isPageWrapperNotRequired: true,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('创建路由表'),
        key: vpcModulePages.route.routeCreate,
        Component: RouteCreate,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('路由表详情'),
        key: vpcModulePages.route.routeDetail,
        Component: RouteDetail,
        isPageWrapperNotRequired: true,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('绑定TGW'),
        key: vpcModulePages.route.routeBindTgw,
        Component: RouteDetail,
        isPageWrapperNotRequired: true,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('高可用虚拟IP'),
        key: vpcModulePages.havip.index,
        isNavMenu: true,
        Component: HavipList,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('网卡'),
        key: vpcModulePages.eni.eniParent,
        isNavMenu: true,
        isDefaultOpened: true,
        children: [
            {
                menuName: i18n.t('创建弹性网卡'),
                key: vpcModulePages.eni.eniCreate,
                Component: EniCreate,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('弹性网卡'),
                key: vpcModulePages.eni.index,
                isNavMenu: true,
                Component: EniList,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('弹性网卡详情'),
                key: vpcModulePages.eni.eniDetail,
                Component: EniDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('弹性网卡IPv4地址'),
                key: vpcModulePages.eni.eniIpDetail,
                Component: EniDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('弹性网卡IPv6地址'),
                key: vpcModulePages.eni.eniIpv6Detail,
                Component: EniDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('弹性网卡安全组'),
                key: vpcModulePages.eni.eniSecurity,
                Component: EniDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('弹性网卡监控'),
                key: vpcModulePages.eni.eniMonitor,
                Component: EniDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('创建服务网卡'),
                key: vpcModulePages.endpoint.endPointCreate,
                Component: SnicCreate,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('服务网卡变更带宽'),
                key: vpcModulePages.endpoint.endPointUpgrade,
                Component: SnicUpgrade,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('服务网卡'),
                key: vpcModulePages.endpoint.index,
                isNavMenu: true,
                Component: SnicList,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('服务网卡详情'),
                key: vpcModulePages.endpoint.endpointDetail,
                Component: SnicDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('服务网卡安全组'),
                key: vpcModulePages.endpoint.endpointSecurity,
                Component: SnicDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('服务网卡监控'),
                key: vpcModulePages.endpoint.endpointMonitor,
                Component: SnicDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('高可用虚拟IP详情'),
                key: vpcModulePages.havip.havipDetail,
                Component: HavipDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('高可用虚拟IP绑定服务器'),
                key: vpcModulePages.havip.havipServer,
                Component: HavipDetail,
                isPageLayoutCustomized: true
            }
        ]
    },
    {
        menuName: i18n.t('访问控制'),
        key: 'control',
        isNavMenu: true,
        isDefaultOpened: true,
        children: [
            {
                menuName: i18n.t('创建安全组'),
                key: vpcModulePages.security.securityCreate,
                Component: SecurityCreate,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('安全组'),
                key: vpcModulePages.security.index,
                isNavMenu: true,
                Component: SecurityList,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('安全组详情'),
                key: vpcModulePages.security.securityDetail,
                Component: SecurityDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('关联云服务器'),
                key: vpcModulePages.security.securityDetail + '/bcc',
                Component: SecurityDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('关联弹性网卡'),
                key: vpcModulePages.security.securityDetail + '/eni',
                Component: SecurityDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('关联服务网卡'),
                key: vpcModulePages.security.securityDetail + '/snic',
                Component: SecurityDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('关联弹性裸金属服务器'),
                key: vpcModulePages.security.securityDetail + '/bbc',
                Component: SecurityDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('关联负载均衡'),
                key: vpcModulePages.security.securityDetail + '/blb',
                Component: SecurityDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('关联云数据库专属集群'),
                key: vpcModulePages.security.securityDetail + '/ddc',
                Component: SecurityDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('关联云数据库 Redis'),
                key: vpcModulePages.security.securityDetail + '/scs',
                Component: SecurityDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('关联云数据库RDS'),
                key: vpcModulePages.security.securityDetail + '/rds',
                Component: SecurityDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('关联消息服务 for RabbitMQ'),
                key: vpcModulePages.security.securityDetail + '/rabbitmq',
                Component: SecurityDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('关联云数据库GaiaDB-S'),
                key: vpcModulePages.security.securityDetail + '/gaiadb',
                Component: SecurityDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('创建企业安全组'),
                key: vpcModulePages.enterpriseSec.enterpriseSecCreate,
                Component: EnterpriseSecCreate,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('企业安全组'),
                key: vpcModulePages.enterpriseSec.index,
                activeMenuKey: vpcModulePages.security.index,
                Component: SecurityList,
                isPageLayoutCustomized: true
            },

            {
                menuName: i18n.t('企业安全组详情'),
                key: vpcModulePages.enterpriseSec.enterpriseSecDetail,
                Component: EnterpriseSecDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('关联云服务器'),
                key: vpcModulePages.enterpriseSec.enterpriseSecDetail + '/bcc',
                Component: EnterpriseSecDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('关联弹性网卡'),
                key: vpcModulePages.enterpriseSec.enterpriseSecDetail + '/eni',
                Component: EnterpriseSecDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('关联服务网卡'),
                key: vpcModulePages.enterpriseSec.enterpriseSecDetail + '/snic',
                Component: EnterpriseSecDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('关联弹性裸金属服务器'),
                key: vpcModulePages.enterpriseSec.enterpriseSecDetail + '/bbc',
                Component: EnterpriseSecDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('关联负载均衡'),
                key: vpcModulePages.enterpriseSec.enterpriseSecDetail + '/blb',
                Component: EnterpriseSecDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: 'ACL',
                key: vpcModulePages.acl.index,
                isNavMenu: true,
                Component: AclList,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('ACL管理'),
                key: vpcModulePages.acl.aclManage,
                Component: AclManage,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('参数模版'),
                key: vpcModulePages.param.index,
                isNavMenu: true,
                Component: ParamTemplate,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('IP地址组详情页'),
                key: vpcModulePages.param.paramsIpDetail,
                Component: ParamsIpDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('IP地址管理'),
                key: vpcModulePages.param.paramsIpDetailMana,
                Component: ParamsIpDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('IP地址关联实例'),
                key: vpcModulePages.param.paramsIpDetailAssoc,
                Component: ParamsIpDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('IP地址族'),
                key: vpcModulePages.param.paramsIpGroupList,
                activeMenuKey: vpcModulePages.param.index,
                Component: ParamTemplate,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('IP地址族详情页'),
                key: vpcModulePages.param.paramsGroupDetail,
                Component: ParamsIpGroupDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('IP地址族管理'),
                key: vpcModulePages.param.paramsIpGroupMana,
                Component: ParamsIpGroupDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('IP地址族关联实例'),
                key: vpcModulePages.param.paramsIpGroupAssoc,
                Component: ParamsIpGroupDetail,
                isPageLayoutCustomized: true
            }
        ]
    },
    {
        menuName: i18n.t('网络连接'),
        key: 'connect',
        isNavMenu: true,
        isDefaultOpened: true,
        children: [
            {
                menuName: i18n.t('创建NAT网关'),
                key: vpcModulePages.nat.natCreate,
                Component: NatCreate,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('NAT网关详情'),
                key: vpcModulePages.nat.natDetail,
                Component: NatDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('SNAT列表'),
                key: vpcModulePages.nat.natSnat,
                Component: NatDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('DNAT列表'),
                key: vpcModulePages.nat.natDnat,
                Component: NatDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('监控'),
                key: vpcModulePages.nat.natMonitor,
                Component: NatDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('网关流控'),
                key: vpcModulePages.nat.natGatewayMonitor,
                Component: NatDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('NAT网关'),
                key: vpcModulePages.nat.index,
                isNavMenu: true,
                Component: NatList,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('私网NAT网关'),
                key: vpcModulePages.privateNat.index,
                activeMenuKey: vpcModulePages.nat.index,
                Component: NatList,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('NAT网关变配'),
                key: vpcModulePages.nat.natUpgrade,
                Component: NatUpgrade,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('私网NAT IP'),
                key: vpcModulePages.privateNat.natIp,
                Component: NatDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('NAT网关权限页'),
                key: vpcModulePages.nat.natAuth,
                activeMenuKey: vpcModulePages.nat.index,
                Component: NatAuth,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('创建IPv6网关'),
                key: vpcModulePages.ipv6.IPv6Create,
                Component: IPv6Create,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('IPv6网关'),
                key: vpcModulePages.ipv6.IPv6List,
                isNavMenu: true,
                Component: IPv6List,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('IPv6网关详情'),
                key: vpcModulePages.ipv6.IPv6Detail,
                Component: IPv6Detail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('IPv6网关带宽调整'),
                key: vpcModulePages.ipv6.IPv6Upgrade,
                Component: IPv6Upgrade,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('VPN网关'),
                key: vpcModulePages.vpn.index,
                isNavMenu: true,
                Component: VpnList,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('SSL VPN网关'),
                key: vpcModulePages.vpn.vpnSslList,
                activeMenuKey: vpcModulePages.vpn.index,
                Component: VpnList,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('GRE VPN网关'),
                key: vpcModulePages.vpn.vpnGreList,
                activeMenuKey: vpcModulePages.vpn.index,
                Component: VpnList,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('创建VPN网关'),
                key: vpcModulePages.vpn.vpnCreate,
                Component: VpnCreate,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('VPN网关'),
                key: vpcModulePages.vpn.vpnAuth,
                activeMenuKey: vpcModulePages.vpn.index,
                Component: VpnAuth,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('VPN网关详情'),
                key: vpcModulePages.vpn.vpnDetail,
                Component: VpnDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('VPN路由管理'),
                key: vpcModulePages.vpn.vpnRouteMana,
                Component: VpnDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('云端静态NAT'),
                key: vpcModulePages.vpn.vpnDetailNat,
                Component: VpnDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('IDC端静态NAT'),
                key: vpcModulePages.vpn.vpnDetailIdcnat,
                Component: VpnDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('云端IP端口静态NAT'),
                key: vpcModulePages.vpn.vpnDetailIdcdnat,
                Component: VpnDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('云端DNAT'),
                key: vpcModulePages.vpn.vpnDetailVpcdnat,
                Component: VpnDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('监控'),
                key: vpcModulePages.vpn.vpnDetailMonitor,
                Component: VpnDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('创建VPN隧道'),
                key: vpcModulePages.vpn.vpnConnCreate,
                Component: VpnConnCreate,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('SSL VPN 隧道详情'),
                key: vpcModulePages.vpn.sslVpnConnDetail,
                Component: SslVpnConnDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('SSL VPN 隧道用户管理'),
                key: vpcModulePages.vpn.sslVpnConnUser,
                Component: SslVpnConnDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('SSL VPN 隧道监控'),
                key: vpcModulePages.vpn.sslVpnConnMonitor,
                Component: SslVpnConnDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('SSL VPN 连接数升级'),
                key: vpcModulePages.vpn.sslVpnUpgrade,
                Component: SslVpnUgrade,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('对等连接'),
                key: vpcModulePages.peerconn.index,
                isNavMenu: true,
                Component: PeerconnList,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('跨境对等连接'),
                key: vpcModulePages.crossPeerconn.index,
                activeMenuKey: vpcModulePages.peerconn.index,
                Component: PeerconnList,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('跨境对等连接申请'),
                key: vpcModulePages.peerconn.peerconnCrossAudit,
                Component: PeerconnCrossAudit,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('跨境对等连接申请中'),
                key: vpcModulePages.peerconn.peerconnCrossAuditing,
                Component: PeerconnCrossAuditing,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('对等连接创建'),
                key: vpcModulePages.peerconn.peerconnCreate,
                Component: PeerconnCreate,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('跨境对等连接创建'),
                key: vpcModulePages.peerconn.peerconnCreateCross,
                Component: PeerconnCreate,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('对等连接调整带宽'),
                key: vpcModulePages.peerconn.peerconnUpgrade,
                Component: PeerconnUpgrade,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('对等连接续费'),
                key: vpcModulePages.peerconn.peerconnRecharge,
                Component: PeerconnRecharge,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('对等连接带宽调整成功'),
                key: vpcModulePages.peerconn.peerconnUpgradeSuccess,
                Component: PeerconnUpgradeSuccess,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('对等连接权限页'),
                key: vpcModulePages.peerconn.peerconnAuth,
                activeMenuKey: vpcModulePages.peerconn.index,
                Component: PeerconnAuth,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('对等连接详情'),
                key: vpcModulePages.peerconn.peerconnDetail,
                isNavMenu: false,
                Component: PeerconnDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('对等连接监控'),
                key: vpcModulePages.peerconn.peerconnMonitor,
                isNavMenu: false,
                Component: PeerconnDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('创建专线网关'),
                key: vpcModulePages.dcgw.dcgwCreate,
                Component: DcgwCreate,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('编辑专线网关'),
                key: vpcModulePages.dcgw.dcgwEdit,
                Component: DcgwCreate,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('专线网关'),
                key: vpcModulePages.dcgw.index,
                isNavMenu: true,
                Component: DcgwList,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('专线网关详情'),
                key: vpcModulePages.dcgw.dcgwDetail,
                Component: DcgwDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('专线网关链路探测'),
                key: vpcModulePages.dcgw.dcgwDetailHc,
                Component: DcgwDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('云端静态NAT-DCGW'),
                key: vpcModulePages.dcgw.dcgwDetailNat,
                Component: DcgwDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('IDC端静态NAT-DCGW'),
                key: vpcModulePages.dcgw.dcgwDetailIdcnat,
                Component: DcgwDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('云端IP端口静态NAT-DCGW'),
                key: vpcModulePages.dcgw.dcgwDetailIdcdnat,
                Component: DcgwDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('专线网关监控'),
                key: vpcModulePages.dcgw.dcgwMonitor,
                Component: DcgwDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('二层网关'),
                key: vpcModulePages.l2gw.index,
                isNavMenu: true,
                Component: L2gwList,
                icon: <MenuIcon />,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('创建二层网关'),
                key: vpcModulePages.l2gw.l2gwCreate,
                Component: L2gwCreate,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('二层网关详情'),
                key: vpcModulePages.l2gw.l2gwDetail,
                Component: L2gwDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('二层网关监控'),
                key: vpcModulePages.l2gw.l2gwMonitor,
                Component: L2gwDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('二层网关通道实例信息'),
                key: vpcModulePages.l2gw.l2gwChannelDetail,
                Component: L2gwChannelDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('二层网关通道Arp列表'),
                key: vpcModulePages.l2gw.l2gwChannelArp,
                Component: L2gwChannelDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('二层网关通道变更主机内网IP'),
                key: vpcModulePages.l2gw.l2gwChannelIp,
                Component: L2gwChannelDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('二层网关通道监控'),
                key: vpcModulePages.l2gw.l2gwChannelMonitor,
                Component: L2gwChannelDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('创建二层网关连接隧道'),
                key: vpcModulePages.l2gw.l2gwConnCreate,
                Component: L2gwConnCreate,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('组播网关'),
                key: vpcModulePages.multicasting.index,
                isNavMenu: true,
                Component: MulticastingList,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('创建组播网关'),
                key: vpcModulePages.multicasting.multicastingCreate,
                Component: MulticastingCreate,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('组播网关实例信息'),
                key: vpcModulePages.multicasting.multicastingDetail,
                Component: MulticastingDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('组播网关管理组播组'),
                key: vpcModulePages.multicasting.multicastingManage,
                Component: MulticastingDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('云智能网CSN'),
                key: publcServcie.csnLink,
                isNavMenu: true,
                isLink: true,
                target: linkTarget
            },
            {
                menuName: i18n.t('专线接入ET'),
                key: publcServcie.etLink,
                isNavMenu: true,
                isLink: true,
                target: linkTarget
            },
            {
                menuName: i18n.t('智能网络接入服务'),
                key: publcServcie.smartSerLink,
                isNavMenu: true,
                isLink: true,
                target: linkTarget
            }
        ]
    },
    {
        menuName: i18n.t('网络QoS'),
        key: vpcModulePages.qos.qosParent,
        isNavMenu: true,
        isDefaultOpened: true,
        children: [
            {
                menuName: i18n.t('QoS策略'),
                key: vpcModulePages.qos.index,
                isNavMenu: true,
                Component: QosList,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('创建QoS策略'),
                key: vpcModulePages.qos.qosCreate,
                Component: QosCreate,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('QoS策略详情'),
                key: vpcModulePages.qos.qosDetail,
                Component: QosDetail,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('网关限速'),
                key: vpcModulePages.gatewaySpeedLimit.index,
                isNavMenu: true,
                Component: GatewayLimit,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('专线网关'),
                key: vpcModulePages.gatewaySpeedLimit.gatewayEt,
                activeMenuKey: vpcModulePages.gatewaySpeedLimit.index,
                Component: GatewayLimit,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('云智能网'),
                key: vpcModulePages.gatewaySpeedLimit.gatewayCsn,
                activeMenuKey: vpcModulePages.gatewaySpeedLimit.index,
                Component: GatewayLimit,
                isPageLayoutCustomized: true
            },
            {
                menuName: i18n.t('创建网关限速'),
                key: vpcModulePages.gatewaySpeedLimit.gatewayCreate,
                Component: GatewayCreate,
                isPageLayoutCustomized: true
            }
        ]
    },
    {
        menuName: i18n.t('网络诊断服务'),
        key: addLocaleToLink(`/network/#${ndsPageUrl.flowlog.index}`),
        isNavMenu: true,
        isLink: true
    },
    {
        menuName: i18n.t('资源管理'),
        key: publcServcie.resourceLink,
        isNavMenu: true,
        isLink: true,
        target: linkTarget
    },
    {
        menuName: i18n.t('标签管理'),
        key: publcServcie.tagLink,
        isNavMenu: true,
        isLink: true,
        target: linkTarget
    },
    {
        menuName: i18n.t('配额管理'),
        key: publcServcie.quotaLink + '?serviceType=VPC',
        isNavMenu: true,
        isLink: true,
        target: linkTarget
    }
];

/** 打平之后的菜单列表 */
export const flattenedMenuList = recursiveMenus(menuList);

/** 专线(ET)控制台 */
const DcList = lazy(
    () =>
        import(/* webpackChunkName: "DcList" */ '@/pages/sanPages/pages/dc/instance/list/list') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const DcCreate = lazy(
    () =>
        import(
            /* webpackChunkName: "DcCreate" */ '@/pages/sanPages/pages/dc/instance/create/create'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const DcDetail = lazy(
    () =>
        import(/* webpackChunkName: "DcDetail" */ '@/pages/dc/detail') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const DcChannelList = lazy(
    () =>
        import(
            /* webpackChunkName: "DcChannelList" */ '@/pages/sanPages/pages/dc/channel/list/tab'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);

const DcChannelCreate = lazy(
    () =>
        import(
            /* webpackChunkName: "DcChannelCreate" */ '@/pages/sanPages/pages/dc/channel/create/create'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);

const DcChannelDetail = lazy(
    () =>
        import(/* webpackChunkName: "DcChannelDetail" */ '@/pages/channel/detail') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const DcLineList = lazy(
    () =>
        import(/* webpackChunkName: "DcLineList" */ '@/pages/sanPages/pages/dc/info/list') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const DcPortPay = lazy(
    () =>
        import(
            /* webpackChunkName: "DcPortPay" */ '@/pages/sanPages/pages/dc/instance/port/port'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const DcAuth = lazy(
    () =>
        import(/* webpackChunkName: "DcAuth" */ '@/pages/sanPages/pages/dc/landing/landing') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);

export const dcMenuList: MenuItem[] = [
    {
        menuName: i18n.t('专线权限页'),
        key: dcPageUrl.dc.dcAuth,
        activeMenuKey: dcPageUrl.dc.index,
        Component: DcAuth,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('物理专线'),
        key: dcPageUrl.dc.index,
        isNavMenu: true,
        Component: DcList,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('支付端口资源占用费'),
        key: dcPageUrl.dc.dcPortPay,
        Component: DcPortPay,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('创建物理专线'),
        key: dcPageUrl.dc.dcCreate,
        Component: DcCreate,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('物理专线详情'),
        key: dcPageUrl.dc.dcDetail,
        Component: DcDetail,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('物理专线监控'),
        key: dcPageUrl.dc.dcMonitor,
        Component: DcDetail,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('专线通道'),
        key: dcPageUrl.dc.dcChannelList,
        isNavMenu: true,
        Component: DcChannelList,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('专线通道创建'),
        key: dcPageUrl.dc.dcChannelCreate,
        Component: DcChannelCreate,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('专线通道详情-信息管理'),
        key: dcPageUrl.dc.dcChannelDetail,
        Component: DcChannelDetail,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('专线通道详情-路由管理'),
        key: dcPageUrl.dc.dcChannelRoute,
        Component: DcChannelDetail,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('专线通道详情-可靠性检测'),
        key: dcPageUrl.dc.dcChannelCheck,
        Component: DcChannelDetail,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('专线通道详情-监控'),
        key: dcPageUrl.dc.dcChannelMonitor,
        Component: DcChannelDetail,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('线路铺设信息维护'),
        key: dcPageUrl.dc.dcLineList,
        isNavMenu: true,
        Component: DcLineList,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('故障演练'),
        key: dcPageUrl.dc.dcFailoverPlan,
        isNavMenu: true,
        Component: DcDrill,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('演练详情'),
        key: dcPageUrl.dc.dcFailoverDetail,
        isNavMenu: false,
        activeMenuKey: dcPageUrl.dc.dcFailoverPlan,
        Component: DcDrillDetail,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('专线网关'),
        key: addLocaleToLink(`/network/#${vpcModulePages.dcgw.index}`),
        isNavMenu: true,
        isLink: true,
        target: linkTarget
    },
    {
        menuName: i18n.t('网络诊断服务'),
        key: addLocaleToLink(`/network/#${ndsPageUrl.flowlog.index}`),
        isNavMenu: true,
        isLink: true
    },
    {
        menuName: i18n.t('配额管理'),
        key: publcServcie.quotaLink + '?serviceType=ET',
        isNavMenu: true,
        isLink: true,
        target: linkTarget
    },
    {
        menuName: i18n.t('跨境专线管理'),
        key: addLocaleToLink(`/network/#${crossDcPageUrl.crossDc.index}`),
        isNavMenu: true,
        isLink: true,
        target: linkTarget
    }
];
export const dcFlattenedMenuList = recursiveMenus(dcMenuList as any);

/** 网络诊断服务(NDS)控制台 */
const FlowLogList = lazy(
    () =>
        import(/* webpackChunkName: "FlowLogList" */ '@/pages/sanPages/pages/flowlog/List') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const FlowLogCreate = lazy(
    () =>
        import(
            /* webpackChunkName: "FlowLogCreate" */ '@/pages/sanPages/pages/flowlog/create/create'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const FlowMirrorList = lazy(
    () =>
        import(/* webpackChunkName: "FlowMirrorList" */ '@/pages/flowMirror/list') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const FlowMirrorFilterDetail = lazy(
    () =>
        import(
            /* webpackChunkName: "FlowMirrorFilterDetail" */ '@/pages/flowMirror/filterRuleDetail'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const FlowMirrorListCreate = lazy(
    () =>
        import(
            /* webpackChunkName: "FlowMirrorListCreate" */ '@/pages/sanPages/pages/flowmirror/mirrowSession/create/createIndex'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const FlowMirrorFilterCreate = lazy(
    () =>
        import(
            /* webpackChunkName: "FlowMirrorFilterCreate" */ '@/pages/sanPages/pages/flowmirror/filterRulegroup/create'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const ProbeList = lazy(
    () =>
        import(/* webpackChunkName: "ProbeList" */ '@/pages/sanPages/pages/probe/list/list') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const ProbeDetail = lazy(
    () =>
        import(/* webpackChunkName: "ProbeDetail" */ '@/pages/probe/detail') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const ProbeCreate = lazy(
    () =>
        import(
            /* webpackChunkName: "ProbeCreate" */ '@/pages/sanPages/pages/probe/create/create'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const probeEdit = lazy(
    () =>
        import(/* webpackChunkName: "probeEdit" */ '@/pages/sanPages/pages/probe/create/create') as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const PathAnalysisList = lazy(
    () =>
        import(
            /* webpackChunkName: "PathAnalysisList" */ '@/pages/sanPages/pages/pathanalise/list/list'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const InsDiagnosisList = lazy(
    () =>
        import(
            /* webpackChunkName: "InsDiagnosisList" */ '@/pages/sanPages/pages/instanceDiagnosis/list/list'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const InsDiagnosisDetail = lazy(
    () =>
        import(
            /* webpackChunkName: "InsDiagnosisDetail" */ '@/pages/sanPages/pages/instanceDiagnosis/datail/detail'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const PathAnalysisCreate = lazy(
    () =>
        import(
            /* webpackChunkName: "PathAnalysisCreate" */ '@/pages/sanPages/pages/pathanalise/create/create'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const PathAnalysisDetail = lazy(
    () =>
        import(
            /* webpackChunkName: "PathAnalysisDetail" */ '@/pages/sanPages/pages/pathanalise/detail/detail'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);

export const ndsMenuList: MenuItem[] = [
    {
        menuName: i18n.t('流日志'),
        key: ndsPageUrl.flowlog.index,
        isNavMenu: true,
        Component: FlowLogList,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('创建流日志'),
        key: ndsPageUrl.flowlog.flowLogCreate,
        Component: FlowLogCreate,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('流量镜像'),
        key: ndsPageUrl.flowMirror.index,
        isNavMenu: true,
        Component: FlowMirrorList,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('筛选条件'),
        key: ndsPageUrl.flowMirror.flowMirrorFilter,
        activeMenuKey: ndsPageUrl.flowMirror.index,
        Component: FlowMirrorList,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('筛选条件详情'),
        key: ndsPageUrl.flowMirror.flowMirrorFilterDetail,
        Component: FlowMirrorFilterDetail,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('关联镜像会话'),
        key: ndsPageUrl.flowMirror.flowMirrorFilterSession,
        Component: FlowMirrorFilterDetail,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('创建镜像会话'),
        key: ndsPageUrl.flowMirror.flowMirrorCreate,
        Component: FlowMirrorListCreate,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('创建筛选条件'),
        key: ndsPageUrl.flowMirror.flowMirrorFilterCreate,
        Component: FlowMirrorFilterCreate,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('网络探测'),
        key: ndsPageUrl.probe.index,
        isNavMenu: true,
        Component: ProbeList,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('网络探测详情'),
        key: ndsPageUrl.probe.probeDetail,
        Component: ProbeDetail,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('网络探测详情监控'),
        key: ndsPageUrl.probe.probeMonitor,
        Component: ProbeDetail,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('创建网络探测'),
        key: ndsPageUrl.probe.probeCreate,
        Component: ProbeCreate,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('编辑网络探测'),
        key: ndsPageUrl.probe.probeEdit,
        Component: probeEdit,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('路径分析'),
        key: ndsPageUrl.pathAnalysis.index,
        isNavMenu: true,
        Component: PathAnalysisList,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('创建路径分析'),
        key: ndsPageUrl.pathAnalysis.pathAnalysisCreate,
        Component: PathAnalysisCreate,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('路径分析详情'),
        key: ndsPageUrl.pathAnalysis.pathAnalysisDetail,
        Component: PathAnalysisDetail,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('实例诊断'),
        key: ndsPageUrl.diagnosis.index,
        isNavMenu: true,
        Component: InsDiagnosisList,
        isPageLayoutCustomized: true,
        icon: <MenuIcon />
    },
    {
        menuName: i18n.t('实例诊断详情'),
        key: ndsPageUrl.diagnosis.diagnosisDetail,
        Component: InsDiagnosisDetail,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('端口验通'),
        key: addLocaleToLink(ndsPageUrl.pathAnalysis.portVerification),
        isNavMenu: true,
        isLink: true,
        target: linkTarget
    }
];
export const ndsFlattenedMenuList = recursiveMenus(ndsMenuList as any);

/** 跨境专线(crosset)控制台 */
const CrossDcList = lazy(
    () =>
        import(
            /* webpackChunkName: "CrossDcList" */ '@/pages/sanPages/pages/dc/crossdc/dcList/list'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);
const CrossDcUserList = lazy(
    () =>
        import(
            /* webpackChunkName: "CrossDcUserList" */ '@/pages/sanPages/pages/dc/crossdc/userList/userList'
        ) as unknown as Promise<{
            default: React.ComponentClass;
        }>
);

export const crossDcMenuList: MenuItem[] = [
    {
        menuName: i18n.t('待开通专线'),
        key: crossDcPageUrl.crossDc.index,
        isNavMenu: true,
        Component: CrossDcList,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('已开通专线'),
        key: crossDcPageUrl.crossDc.openCrossDc,
        isNavMenu: true,
        Component: CrossDcList,
        isPageLayoutCustomized: true
    },
    {
        menuName: i18n.t('用户列表'),
        key: crossDcPageUrl.crossDc.userList,
        isNavMenu: true,
        Component: CrossDcUserList,
        isPageLayoutCustomized: true
    }
];
export const crossFlattenedMenuList = recursiveMenus(crossDcMenuList);
