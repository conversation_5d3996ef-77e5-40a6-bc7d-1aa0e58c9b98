import React, {useState, useEffect, useMemo} from 'react';
import {useRequest} from 'ahooks';
import {includes, unescape} from 'lodash';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import {getNatDetail, getNatBlackList, getPrivateNatDetail, getIPv6NatDetail} from '@/apis';
import {useStatus} from '@/hooks';
import FLAG from '@/flags';
import {NatStatus} from '@/pages/sanPages/common/enum';
import {useGlobalContext} from '@/context';
import DetailPage from '@/components/DetailPage';
import Detail from '@/pages/sanPages/pages/nat/detail/detail';
import PrivateDetail from '@/pages/sanPages/pages/nat/detail/privateDetail';
import SnatList from '@/pages/sanPages/pages/nat/detail/snat/list';
import DantList from '@/pages/sanPages/pages/nat/detail/dnat/list';
import Monitor from '@/pages/sanPages/pages/nat/detail/bcm/list';
import GatewayMonitor from '@/pages/sanPages/pages/nat/detail/gateway/list';
import PrivateNatIp from '@/pages/sanPages/pages/nat/detail/natIp/list';
import './index.less';
import {cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
interface DetailReqParams {
    natId: string;
    natGatewayId: string;
}

const NatDetail = () => {
    const href = unescape(location.href);
    const params: any = getQueryParams(href);
    const [{globalState}] = useGlobalContext() as any;
    const [inDnatBlack, setInDnatBlack] = useState<boolean>(false);
    const [gatewayWhite, setGatewayWhite] = useState<boolean>(false);

    let queryDetailInterface: Function;
    const reqParams: Partial<DetailReqParams> = {};
    const natId = params.id;
    const isPrivate = params.natType === 'private';
    const isEnhancedIPv6 = params.ipVersion === 'v6';
    if (isPrivate) {
        reqParams.natId = natId;
        queryDetailInterface = getPrivateNatDetail;
    } else if (isEnhancedIPv6) {
        reqParams.natId = natId;
        queryDetailInterface = getIPv6NatDetail;
    } else {
        reqParams.natGatewayId = natId;
        queryDetailInterface = getNatDetail;
    }

    const {data = [] as any, loading, error, run} = useRequest(() => queryDetailInterface(reqParams));
    let detailData: any;
    if (isPrivate) {
        detailData = data || {};
    } else if (isEnhancedIPv6) {
        detailData = data || {};
    } else {
        if (!FLAG.NetworkNatOpt) {
            detailData = data?.natgateways?.length ? data.natgateways[0] : {};
        } else {
            detailData = data?.result?.length ? data.result[0] : {};
        }
    }

    const handleUpdate = () => {
        run();
    };
    const panesData = [
        {
            key: '/vpc/nat/detail',
            tab: i18n.t('实例信息'),
            content: <Detail context={{...params, instance: detailData, updateName: handleUpdate}} />
        },
        {
            key: '/vpc/nat/natIp',
            tab: 'NAT IP',
            content: <PrivateNatIp context={{...params, natInfo: detailData}} />
        },
        {
            key: '/vpc/nat/snat',
            tab: i18n.t('SNAT列表'),
            content: <SnatList context={{...params, isPrivate, natInfo: detailData}} />
        },
        {
            key: '/vpc/nat/dnat',
            tab: i18n.t('DNAT列表'),
            content: <DantList context={{...params, isPrivate, natInfo: detailData}} />
        },
        {
            key: '/vpc/nat/monitor',
            tab: i18n.t('监控'),
            content: <Monitor context={{...params, natInfo: detailData}} />
        },
        {
            key: '/vpc/nat/gateway',
            tab: i18n.t('网关流控'),
            natInfo: detailData,
            content: <GatewayMonitor context={{...params, natInfo: detailData}} />
        }
    ];
    if (isPrivate) {
        panesData[0].content = <PrivateDetail context={{...params, instance: detailData}} />;
    } else {
        panesData.splice(1, 1);
    }

    const handleResolveWhite = async () => {
        const res = await getNatBlackList();
        if (res || !res) {
            setInDnatBlack(res as any);
        }
        if (includes(window.location.href, 'vpc/nat/gateway')) {
            setGatewayWhite(true);
        }
        // 网关流控白名单
        const whiteList = globalState.commonWhite;
        setGatewayWhite(whiteList?.NatLimitRuleWhiteList);
    };

    useEffect(() => {
        if (params.natType !== 'private') {
            handleResolveWhite();
        }
    }, []);

    const dnatBlackPanesData = useMemo(() => {
        let newPanesData = [...panesData];
        if (inDnatBlack) {
            newPanesData = newPanesData.filter(item => item.key !== '/vpc/nat/dnat');
        }
        return newPanesData;
    }, [inDnatBlack, data]);

    const gatewayWhitePanesData = useMemo(() => {
        let newPanesData = [...dnatBlackPanesData];
        if (!gatewayWhite && (detailData?.flavor !== 'enhanced_12c6q' || detailData?.clusterMode)) {
            newPanesData = newPanesData.filter(item => item.key !== '/vpc/nat/gateway');
        }
        return newPanesData;
    }, [dnatBlackPanesData, gatewayWhite]);

    const [text, styleClass] = useStatus({statusEnum: NatStatus, data: detailData});

    const backUrl = isPrivate ? '#/vpc/privateNat/list' : '#/vpc/nat/list';
    return (
        <>
            <DetailPage
                mode="vertical"
                isSwitchReRender={true}
                headerName={detailData?.name}
                backUrl={backUrl}
                statusText={text}
                statusClassName={styleClass}
                panesData={gatewayWhitePanesData}
            />
        </>
    );
};
export default NatDetail;
