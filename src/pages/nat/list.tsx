import React, {useMemo} from 'react';
import u from 'lodash';
import SecurityTabs from '@/components/TabPage';
import NatListComp from '@/pages/sanPages/pages/nat/list/list';
import {cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
import './index.less';

const AllRegion = window.$context.getEnum('AllRegion');

const NatList = () => {
    const region = window.$context.getCurrentRegionId();
    const supportPrivateRegion = [AllRegion.BJ, AllRegion.NJ, AllRegion.SU, AllRegion.FWH, AllRegion.BD, AllRegion.GZ];

    const panesData = [
        {key: '/vpc/nat/list', tab: i18n.t('公网NAT网关'), content: <NatListComp context={{natType: 'public'}} />},
        {key: '/vpc/privateNat/list', tab: i18n.t('私网NAT网关'), content: <NatListComp context={{natType: 'private'}} />}
    ];

    const whitedPanesData = useMemo(() => {
        let newPanesData = [...panesData];
        if (!(u as any).contains(supportPrivateRegion, region)) {
            newPanesData = newPanesData.filter(item => item.key !== '/vpc/privateNat/list');
        }
        return newPanesData;
    }, [region]);
    return (
        <div className="nat-list-widget">
            <SecurityTabs isSwitchReRender={false} panesData={whitedPanesData} />
        </div>
    );
};
export default NatList;
