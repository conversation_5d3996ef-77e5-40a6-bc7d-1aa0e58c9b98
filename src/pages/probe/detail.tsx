import React, {useState, useEffect, useMemo} from 'react';
import {useRequest} from 'ahooks';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import {getProbeDetail} from '@/apis';
import {useStatus} from '@/hooks';
import {ProbeStatus} from '@/pages/sanPages/common/enum';
import DetailPage from '@/components/DetailPage';
import Detail from '@/pages/sanPages/pages/probe/detail/detail';
import Monitor from '@/pages/sanPages/pages/probe/bcm/bcm';
import {cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
import './index.less';

const HavIpDetail = () => {
    const params: any = getQueryParams();
    const {data = {} as any, loading, error, run} = useRequest(() => getProbeDetail({probeId: params.probeId}));
    const handleUpdate = () => {
        run();
    };
    const panesData = [
        {
            key: '/vpc/probe/detail',
            tab: i18n.t('实例信息'),
            content: <Detail context={{...params, instance: data, updateName: handleUpdate}} />
        },
        {key: '/vpc/probe/monitor', tab: i18n.t('监控'), content: <Monitor context={params} />}
    ];
    const whitedPanesData = useMemo(() => {
        const newPanesData = [...panesData];
        return newPanesData;
    }, [data]);
    const [text, styleClass] = useStatus({statusEnum: ProbeStatus, data});
    return (
        <>
            <DetailPage
                mode="vertical"
                headerName={data.name}
                backUrl="#/vpc/probe/list"
                statusText={text}
                statusClassName={styleClass}
                panesData={whitedPanesData}
            />
        </>
    );
};
export default HavIpDetail;
