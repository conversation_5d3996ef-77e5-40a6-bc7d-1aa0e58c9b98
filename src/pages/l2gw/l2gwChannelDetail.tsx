import React, {useMemo} from 'react';
import {getL2gwTunnelDetail} from '@/apis';
import {useRequest} from 'ahooks';
import {l2gwStatus} from '@/pages/sanPages/common';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import L2gwTunnelDetail from '@/pages/sanPages/pages/l2gw/page/conn/detail/detail';
import L2gwTunnelMonitor from '@/pages/sanPages/pages/l2gw/page/conn/detail/monitorChart/monitor';
import TunnelArpList from '@/pages/sanPages/pages/l2gw/page/conn/detail/arp/list';
import TunnelIpList from '@/pages/sanPages/pages/l2gw/page/conn/detail/nat/list';
import {cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
import DetailPage from '@/components/DetailPage';

import './index.less';

const L2gwChannelDetail = () => {
    const params: any = getQueryParams();
    const {data = {} as any, loading, error, run} = useRequest(() => getL2gwTunnelDetail({tunnelId: params.tunnelId}));
    const handleUpdate = () => {
        run();
    };
    const panesData = [
        {
            key: '/vpc/l2gw/tunnel/detail',
            tab: i18n.t('实例信息'),
            content: <L2gwTunnelDetail context={{...params, updateName: handleUpdate}} />
        },
        {
            key: '/vpc/l2gw/tunnel/arp',
            tab: i18n.t('用户端主机ARP列表'),
            content: <TunnelArpList context={params} />
        },
        {
            key: '/vpc/l2gw/tunnel/ip',
            tab: i18n.t('变更主机内网IP'),
            content: <TunnelIpList context={params} />
        },
        {key: '/vpc/l2gw/tunnel/monitor', tab: i18n.t('监控'), content: <L2gwTunnelMonitor context={params} />}
    ];

    const [text, styleClass] = useMemo(() => {
        let config = [];
        if (data?.status) {
            const {text, styleClass} = l2gwStatus.fromValue(data.status);
            config = [text, styleClass];
        }
        return config;
    }, [data]);

    return (
        <>
            <DetailPage
                mode="vertical"
                statusText={text}
                statusClassName={styleClass}
                headerName={data.name}
                backUrl={'#/vpc/l2gw/list'}
                panesData={panesData}
                tabClassName="l2gw-channel-tab-class"
            />
        </>
    );
};

export default L2gwChannelDetail;
