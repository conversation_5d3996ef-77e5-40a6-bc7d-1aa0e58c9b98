import React, {useMemo} from 'react';
import {useRequest} from 'ahooks';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import {getL2gwDetail} from '@/apis';
import {l2gwStatus} from '@/pages/sanPages/common';
import L2gwInstanceDetail from '@/pages/sanPages/pages/l2gw/page/detail/detail';
import L2gwDetailMonitor from '@/pages/sanPages/pages/l2gw/page/detail/monitorChart/monitor';
import {cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
import DetailPage from '@/components/DetailPage';

import './index.less';

const L2gwDetail = () => {
    const params: any = getQueryParams();
    const {data = {} as any, loading, error, run} = useRequest(() => getL2gwDetail({l2gwId: params.l2gwId}));
    const handleUpdate = () => {
        run();
    };
    const panesData = [
        {
            key: '/vpc/l2gw/detail',
            tab: i18n.t('实例信息'),
            content: <L2gwInstanceDetail context={{...params, updateName: handleUpdate}} />
        },
        {key: '/vpc/l2gw/monitor', tab: i18n.t('监控'), content: <L2gwDetailMonitor context={params} />}
    ];

    const [text, styleClass] = useMemo(() => {
        let config = [];
        if (data?.status) {
            const {text, styleClass} = l2gwStatus.fromValue(data.status);
            config = [text, styleClass];
        }
        return config;
    }, [data]);
    return (
        <>
            <DetailPage
                mode="vertical"
                headerName={data.name}
                statusText={text}
                statusClassName={styleClass}
                backUrl={'#/vpc/l2gw/list'}
                panesData={panesData}
                tabClassName="l2gw-tab-class"
            />
        </>
    );
};

export default L2gwDetail;
