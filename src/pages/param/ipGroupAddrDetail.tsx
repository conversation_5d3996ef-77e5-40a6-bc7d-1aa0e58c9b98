import React, {useEffect, useMemo} from 'react';
import {getParamGroupDetail} from '@/apis';
import {useRequest} from 'ahooks';
import {parseQuery} from '@/utils';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import DetailPage from '@/components/DetailPage';
import ParamsDetail from '@/pages/sanPages/pages/param/page/groupDetail/paramDetail';
import IpAddrManage from '@/pages/sanPages/pages/param/page/groupDetail/ipAddress';
import AssociateInstance from '@/pages/sanPages/pages/param/page/groupDetail/associateExamples';
import {cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
import './index.less';

const Detail = () => {
    const params: any = parseQuery(window.location.hash);
    const {
        data = {} as any,
        loading,
        error,
        run
    } = useRequest(() => getParamGroupDetail({ipGroupUuid: params.ipGroupUuid}));

    const handleUpdate = () => {
        run();
    };
    const panesData = [
        {
            key: '/vpc/group/detail',
            tab: i18n.t('实例信息'),
            content: <ParamsDetail context={{...params, instanceDetail: data, updateName: handleUpdate}} />
        },
        {
            key: '/vpc/group/address',
            tab: i18n.t('IP地址组管理'),
            content: <IpAddrManage context={{...params, instanceDetail: data}} />
        },
        {
            key: '/vpc/group/association',
            tab: i18n.t('关联实例'),
            content: <AssociateInstance context={{...params, instanceDetail: data}} />
        }
    ];

    const newPanesData = useMemo(() => {
        const panesDataCopy = [...panesData];
        return panesDataCopy;
    }, [data]);

    return (
        <>
            <DetailPage mode="vertical" headerName={data?.name} backUrl="#/vpc/group/list" panesData={panesData} />
        </>
    );
};
export default Detail;
