import React, {useState, useEffect, useMemo} from 'react';
import {getParamDetail} from '@/apis';
import {useRequest} from 'ahooks';
import {parseQuery} from '@/utils';
import DetailPage from '@/components/DetailPage';
import ParamsDetail from '@/pages/sanPages/pages/param/page/detail/paramDetail';
import IpAddrManage from '@/pages/sanPages/pages/param/page/detail/ipAddress';
import AssociateInstance from '@/pages/sanPages/pages/param/page/detail/associateExamples';
import {cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
import './index.less';

const Detail = () => {
    const params: any = parseQuery(window.location.hash);
    const {data = {} as any, loading, error, run} = useRequest(() => getParamDetail({ipSetUuid: params.ipSetUuid}));

    const handleUpdate = () => {
        run();
    };
    const panesData = [
        {
            key: '/vpc/param/detail',
            tab: i18n.t('实例信息'),
            content: <ParamsDetail context={{...params, instanceDetail: data, updateName: handleUpdate}} />
        },
        {
            key: '/vpc/param/address',
            tab: i18n.t('IP地址管理'),
            content: <IpAddrManage context={{...params, instanceDetail: data}} />
        },
        {
            key: '/vpc/param/association',
            tab: i18n.t('关联实例'),
            content: <AssociateInstance context={{...params, instanceDetail: data}} />
        }
    ];

    const newPanesData = useMemo(() => {
        const panesDataCopy = [...panesData];
        return panesDataCopy;
    }, [data]);

    return (
        <>
            <DetailPage mode="vertical" headerName={data?.name} backUrl="#/vpc/set/list" panesData={panesData} />
        </>
    );
};
export default Detail;
