import React from 'react';
import SecurityTabs from '@/components/TabPage';
import {DocService} from '@/pages/sanPages/common';
import {OutlinedInfoCircle} from 'acud-icon';
import AddressList from '@/pages/sanPages/pages/param/components/addressList';
import AddressGroupList from '@/pages/sanPages/pages/param/components/addressGroupList';
import {cbaI18nInstance as i18n,Trans} from '@baidu/bce-react-toolkit';
import './index.less';

const SecurityList = props => {
    return (
        <div className="param-group-widget">
            <div className="param-header">
                <Trans>
                <h2 className="title">参数模板</h2>
                </Trans>
            </div>
            <SecurityTabs
                isSwitchReRender={false}
                panesData={[
                    {key: '/vpc/set/list', tab: i18n.t('IP 地址组'), content: <AddressList context={{addressType: 'set'}} />},
                    {
                        key: '/vpc/group/list',
                        tab: i18n.t('IP 地址族'),
                        content: <AddressGroupList context={{addressType: 'group'}} />
                    }
                ]}
            />
        </div>
    );
};
export default SecurityList;
