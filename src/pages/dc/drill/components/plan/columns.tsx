import React from 'react';
import { <PERSON>, Badge, Tooltip, Tag } from 'acud';
import moment from 'moment';
import testId from '@/testId';
import { formatSecondsToChinese, getBadgeStatus } from '../../common/util';
import { TASK_STATUS, INSTANCE_TYPE, TASK_STATUS_MAP } from '../../common/const';
import './style.less';
import {cbaI18nInstance as i18n,} from '@baidu/bce-react-toolkit';

const AllRegion = (window as any).$context.getEnum('AllRegion');

export const defaultColumns = [
    {
        title: i18n.t('任务名称/ID'),
        dataIndex: 'taskId',
        key: 'taskId',
        fixed: 'left',
        width: 120,
        render: (text, record, index) => {
            return (
                <>
                    <Link
                        href={`#/dc/failover/detail?id=${record?.id}`}
                        data-testid={`${testId.dcDrill.listName}${index}`}
                    >
                        {record?.name}
                    </Link>
                    <div>{record?.id}</div>
                </>
            )
        }
    },
    {
        title: i18n.t('区域'),
        dataIndex: 'region',
        key: 'region',
        width: 100,
        render: (text) => text ? AllRegion.getTextFromValue(text) : '',
    },
    {
        title: i18n.t('资源类型'),
        dataIndex: 'instanceType',
        key: 'instanceType',
        filterMultiple: false,
        width: 120,
        render: (text) => INSTANCE_TYPE[text]
    },
    {
        title: i18n.t('资源实例'),
        dataIndex: 'instanceIds',
        width: 180,
        render: (text, record) => {
            const { instanceType } = record;
            const list = record?.instanceVos;
            const instanceIds = record?.instanceIds;
            // 部分被删除(diff instanceIds后端存一份和 instanceVos 是 console 查表数据)
            let content = instanceIds?.map((id) => {
                const item = list?.find(item => item?.instanceId === id);
                let href = instanceType === 'dcphy' ? '#/dc/instance/list' : '#/dc/channel/list';
                href += `?id=${id}`;
                return (
                    <div key={id}>
                        <Link
                            href={href}
                            target='_blank'
                        >{item?.instanceName || '-'}</Link>
                        <div>{id}</div>
                        {!item ? <Tag color='warning-status'>{i18n.t('该资源可能已被删除')}</Tag> : null}
                    </div>
                );
            });
            return (
                <Tooltip title={content}>
                    <div className='instance-list'>
                        {content}
                    </div>
                </Tooltip>
            );
        }
    },
    {
        title: i18n.t('任务状态'),
        dataIndex: 'status',
        key: 'status',
        filterMultiple: false,
        width: 120,
        render: (text, record) => {
            const status = record?.status;
            const item = TASK_STATUS.find(_ => _.value === status);
            const type = getBadgeStatus(status);
            return (
                <div>
                    <Badge status={type} />
                    <span>{item.label}</span>
                </div>
            )
        }
    },
    {
        title: i18n.t('演练时长'),
        dataIndex: 'taskDuration',
        key: 'taskDuration',
        width: 80,
        render: minutes => formatSecondsToChinese(minutes * 60)
    },
    {
        title: i18n.t('实际演练时长'),
        dataIndex: 'realDuration',
        width: 100,
        render: (text, record) => {
            const { startTime, endTime } = record;
            if (!startTime || !endTime) return '-';
            const diff = moment(endTime).diff(moment(startTime), 'seconds');
            return formatSecondsToChinese(diff);
        }
    },
    {
        title: i18n.t('开始时间'),
        dataIndex: 'startTime',
        key: 'startTime',
        width: 120,
        render: _ => _ ? moment(_).format('YYYY-MM-DD HH:mm:ss') : '-'
    },
    {
        title: i18n.t('结束时间'),
        dataIndex: 'endTime',
        key: 'endTime',
        width: 120,
        render: _ => _ ? moment(_).format('YYYY-MM-DD HH:mm:ss') : '-'
    },
    {
        title: i18n.t('任务创建时间'),
        dataIndex: 'createTime',
        key: 'createTime',
        width: 120,
        sorter: true,
        render: _ => moment(_).format('YYYY-MM-DD HH:mm:ss')
    },
    {
        title: i18n.t('任务结果'),
        dataIndex: 'conclusion',
        key: 'conclusion',
        width: 80,
        render: (text, record) => {
            if (record?.status !== TASK_STATUS_MAP.DONE || !text) return '-';
            const isNormal = text === 'normal';
            return (
                <>
                    <Badge status={isNormal ? 'success' : 'error'} />
                    <span>{isNormal ? i18n.t('正常') : i18n.t('异常')}</span>
                </>
            )
        }
    }
];

export const defaultColumnList = defaultColumns.map((item) => ({
    value: item.dataIndex,
    label: item.title
}));

export const defaultCustomColumnValues = defaultColumnList.map((item) => item.value);
