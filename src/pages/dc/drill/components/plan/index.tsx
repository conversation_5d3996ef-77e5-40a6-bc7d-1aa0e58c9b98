import React, { useState, useEffect } from 'react';
import {
    Button, Space, Search, Table, DialogBox, Link, Tooltip
} from 'acud';
import { OutlinedPlusNew, OutlinedDownload, OutlinedSetting, OutlinedRefresh } from 'acud-icon';
import DrillGuiding from '../guiding/index';
import CreateModal from '../create';
import StartConfirmModal from '../startConfirm';
import ReportDrawer from '../report';
import CustomColumn from '@/components/CustomColumn';
import { defaultColumns, defaultColumnList, defaultCustomColumnValues } from './columns';
import {
    getFailoverTaskList, deleteFailoverTask, downloadFailoverTask, stopFailoverTask, commonQuota
} from '@/apis/failover';
import testId from '@/testId';
import { INSTANCE_TYPES, TASK_STATUS, TASK_STATUS_MAP, SEARCH_MODES, SEARCH_PLACEHOLDER_MAP } from '../../common/const';
import { showNotification } from '../../common/util';
import { useTableSearch } from '../../useTableSearch';
import { urlSerialize } from '@/pages/sanPages/utils/helper';
import './style.less';
import {cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';

const FailoverPlan = ({
    showGuiding,
    setShowGuiding
}) => {
    const { state, dispatch, fetchDataAsync, getPayload } = useTableSearch(getFailoverTaskList as any);
    const dataSource = state.tableData?.result?.map(item => ({ ...item, key: item?.id }));
    const [allColumns, setAllColumns] = useState<any[]>([]);
    const [columns, setColumns] = useState<any[]>([]);
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [showStartConfirmModal, setShowStartConfirmModal] = useState(false);
    const [record, setRecord] = useState<any>({});
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [showStopModal, setShowStopModal] = useState(false);
    const [showReportDrawer, setShowReportDrawer] = useState(false);
    const [quota, setQuota] = useState(20); // 默认 20，和配额中心默认值一致
    const operationColumn = {
        title: i18n.t('操作'),
        key: 'operation',
        dataIndex: 'operation',
        width: 140,
        fixed: 'right',
        render: (_, row, index) => {
            const status = row?.status;
            const disabled = [TASK_STATUS_MAP.STARTING, TASK_STATUS_MAP.ENDING].indexOf(status) !== -1;
            const showEndDrillBtn = [TASK_STATUS_MAP.STARTING, TASK_STATUS_MAP.RUNNING, TASK_STATUS_MAP.ENDING].indexOf(status) !== -1;
            const disableDelBtn = [TASK_STATUS_MAP.STARTING, TASK_STATUS_MAP.RUNNING, TASK_STATUS_MAP.ENDING].indexOf(status) !== -1;
            return (
                <Space size='middle' className='operations'>
                    { status === TASK_STATUS_MAP.PENDING ? <a onClick={(e) => startDrill(e, row)}>{i18n.t('开始演练')}</a> : null }
                    { showEndDrillBtn ? (
                        <Tooltip title={disabled ? `${status === TASK_STATUS_MAP.STARTING ? i18n.t('开始') : i18n.t('结束')}中不支持结束演练` : ''}>
                            <Link
                                disabled={disabled}
                                onClick={(e) => endDrill(e, row)}
                            >{i18n.t('结束演练')}</Link>
                        </Tooltip>
                    ) : null }
                    { status === TASK_STATUS_MAP.PENDING ? <a onClick={(e) => onEdit(e, row)}>{i18n.t('编辑')}</a> : null }
                    { status === TASK_STATUS_MAP.DONE ? (
                        <Link
                            data-testid={`${testId.dcDrillResult.viewReportBtn}${index}`}
                            onClick={(e) => viewReport(e, row)}
                        >{i18n.t('查看报告')}
                        </Link>
                    ) : null }
                    <Tooltip title={disableDelBtn ? i18n.t('任务正在执行中，不支持删除') : ''}>
                        <Link
                            disabled={disableDelBtn}
                            onClick={(e) => onDelete(e, row)}
                        >{i18n.t('删除')}</Link>
                    </Tooltip>
                </Space>
            )
        }
    };
    const [showDropdown, setShowDropdown] = useState(false);
    const [columnList, setColumnList] = useState<any[]>([...defaultColumnList]);
    const [customColumnValues, setCustomColumnValues] = useState<any[]>([...defaultCustomColumnValues, 'operation']);
    const [isEdit, setIsEdit] = useState(false);
    const ticketLink = '/quota_center/#/quota/apply/create?serviceType=ET&region=global&cloudCenterQuotaName=FailoverTestJobQuota';
    const disableCreate = state.tableData?.totalCount >= quota || quota === 0;

    useEffect(() => {
        initColumns();
        getQuota();
        const value = (window as any).$storage.get('et.drill.guiding');
        value && setShowGuiding(value);
    }, []);

    const getQuota = async () => {
        const res = await commonQuota({
            serviceType: 'ET',
            quotaType: 'FailoverTestJobQuota'
        }) as any;
        setQuota(res);
    };

    // 开始演练
    const startDrill = (e, row) => {
        e.preventDefault();
        setRecord(row);
        setShowStartConfirmModal(true);
    };

    // 结束演练
    const endDrill = async (e, row) => {
        e.preventDefault();
        if (row?.status !== TASK_STATUS_MAP.RUNNING) return;
        setRecord(row);
        setShowStopModal(true);
    };

    // 确认结束演练
    const onConfirmStop = async () => {
        try {
            await stopFailoverTask({ taskId: record?.id });
            showNotification(i18n.t('结束演练成功，请刷新列表查看状态'));
            fetchDataAsync();
        } catch {
            showNotification(i18n.t('结束演练失败，请稍后重试'), 'error');
        } finally {
            setRecord({});
            setShowStopModal(false);
        }
    };

    // 编辑
    const onEdit = (e, row) => {
        e.preventDefault();
        setRecord(row);
        setIsEdit(true);
        setShowCreateModal(true);
    };

    // 查看报告
    const viewReport = (e, row) => {
        e.preventDefault();
        setRecord(row);
        setShowReportDrawer(true);
    };

    // 删除
    const onDelete = (e, row) => {
        e.preventDefault();
        setRecord(row);
        setShowDeleteModal(true);
    };

    const handleDelete = async () => {
        try {
            const res = await deleteFailoverTask({ taskId: record?.id });
            showNotification(i18n.t('删除任务提交成功，请刷新列表查看状态'));
            fetchDataAsync();
        } catch (err) {
            showNotification(i18n.t('删除任务提交失败，请稍后重试'), 'error');
        } finally {
            setShowDeleteModal(false);
            setRecord({});
        }
    };

    // 下载
    const onDownload = () => {
        const payload = getPayload();
        const query = urlSerialize(payload);
        downloadFailoverTask(query);
    };

    // 初始化列
    const initColumns = () => {
        const nextColumns = defaultColumns.map((item) => {
            if (item.dataIndex === 'instanceType') {
                return {
                    ...item,
                    filters: [...INSTANCE_TYPES].map(_ => ({ ..._, text: _.label })),
                }
            }
            if (item.dataIndex === 'status') {
                return {
                    ...item,
                    filters: [...TASK_STATUS].map(_ => ({ ..._, text: _.label })),
                }
            }
            return item;
        });
        const columns = [...nextColumns, operationColumn];
        // 优先从 window.$storage 读取
        let storedColumnList = (window as any).$storage.get('et.plan.columnList');
        let list: any[] = [];
        if (storedColumnList) {
            list = columns.filter((item) => storedColumnList?.includes(item?.dataIndex));
            setCustomColumnValues(storedColumnList);
        } else {
            list = columns;
        }
        setColumns(list);
        setAllColumns(columns);
        setColumnList([...defaultColumnList, { value: 'operation', label: i18n.t('操作'), disabled: true }]);
    };

    // 自定义列变化
    const onCustomColumnChange = (values) => {
        // 同步到 window.$storage
        (window as any).$storage.set('et.plan.columnList', values);
        setCustomColumnValues(values);
        const nextColumns = allColumns.filter((item) => values.includes(item?.dataIndex));
        setColumns(nextColumns);
    };

    // 创建演练任务
    const onCreate = () => {
        setIsEdit(false);
        setShowCreateModal(true);
    };

    // 创建/编辑任务成功
    const handleOk = async (id, startNow = false) => {
        setShowCreateModal(false);
        await fetchDataAsync();
        if (id && startNow) {
            const payload = {
                keywordType: 'id',
                keyword: id,
            };
            const res = await getFailoverTaskList(payload) as any;
            setRecord(res?.result?.[0]);
            setShowStartConfirmModal(true);
        }
    };

    // 搜索
    const onSearch = (value) => {
        dispatch({ type: 'TRIGGER_SEARCH', payload: value });
    };

    // 表格快捷查询变化
    const onChange = (pagination, filters, sorter) => {
        let order = sorter?.order;
        if (order === 'ascend') order = 'asc';
        else if (order === 'descend') order = 'desc';
        order && dispatch({ type: 'SET_SORTER', payload: { orderBy: sorter?.field, order } });
        dispatch({ type: 'SET_PAGINATION', payload: pagination });
        const { instanceType, status } = filters;
        dispatch({ type: 'SET_FILTERS', payload: {
            instanceType: instanceType?.[0],
            status: status?.[0]
        } });
    };

    return (
        <div className='dc-drill-plan'>
            <DrillGuiding
                style={{ marginBottom: 16 }}
                show={showGuiding}
                setShow={(value) => {
                    setShowGuiding(value);
                    (window as any).$storage.set('et.drill.guiding', value);
                }}
            />
            <div className='table-container'>
                <div className='operation-wrap'>
                    <Space className='buttons-wrap'>
                        <Tooltip
                            title={
                                disableCreate ? (
                                    <>
                                        {i18n.t('温馨提示：故障演练任务配额不足，如需增加配额请')}
                                        <Link href={ticketLink} target='_blank'>{i18n.t('申请配额')}</Link>
                                    </>
                                ) : null
                            }
                        >
                            <Button
                                type='primary'
                                data-testid={`button-${testId.dcDrill.listCreateBtn}`}
                                icon={<OutlinedPlusNew />}
                                disabled={disableCreate}
                                onClick={onCreate}
                            >{i18n.t('创建演练任务')}</Button>
                        </Tooltip>
                    </Space>
                    <div className='buttons-quick-wrap'>
                        <Space>
                            <Search
                                data-testid={testId.dcDrill.searchInput}
                                allowClear
                                multipleValue={state.searchMode}
                                placeholder={i18n.t('请输入{{searchType}}', { searchType: SEARCH_PLACEHOLDER_MAP[state.searchMode] })}
                                multipleOption={SEARCH_MODES}
                                value={state.search}
                                onChange={(e) => dispatch({ type: 'SET_SEARCH', payload: e.target.value })}
                                onSearch={(data: any) => {
                                    // 这里没有统一，源码里有个 ifMultiple 判断（ifMultiple = !!multipleOption.length;）
                                    // 清空的时候有 bug，此时却判断出 false 导致返回字符串了
                                    const value = typeof data === 'object' ? data?.value : data;
                                    // 清空的时候 search 比 change 先执行？
                                    onSearch(value);
                                }}
                                onChangeMultiple={(value) => dispatch({ type: 'SET_SEARCH_MODE', payload: value })}
                            />
                            <Button icon={<OutlinedDownload />} onClick={onDownload}></Button>
                            <CustomColumn
                                columnList={columnList}
                                value={customColumnValues}
                                onChange={onCustomColumnChange}
                                visible={showDropdown}
                                setVisible={setShowDropdown}
                            >
                                <Button icon={<OutlinedSetting />}></Button>
                            </CustomColumn>
                            <Button
                                icon={<OutlinedRefresh />}
                                onClick={fetchDataAsync}
                            ></Button>
                        </Space>
                    </div>
                </div>
                <Table
                    rowKey={'id'}
                    data-testid={`table-${testId.dcDrill.listTable}`}
                    components={{
                        body: {
                            wrapper: (props) => (
                                <tbody {...props} data-testid={`table-tbody-${testId.dcDrill.listTable}`} />
                            ),
                            row: ({ children, ...restProps }) => {
                                const index = restProps['data-row-key'];
                                return (
                                    <tr
                                        {...restProps}
                                        key={index}
                                        data-testid={`table-tbody-tr-${index}-${testId.dcDrill.listTable}`}
                                    >
                                        {children}
                                    </tr>
                                );
                            },
                        }
                    }}
                    scroll={{ x: customColumnValues.length <= 6 ? undefined : 1600 }}
                    loading={state.loading}
                    columns={columns}
                    dataSource={dataSource}
                    pagination={{
                        total: state.tableData?.totalCount,
                        showTotal: (total) => i18n.t('共 {{total}} 条', { total }),
                        showQuickJumper: true,
                        showSizeChanger: true,
                    }}
                    onChange={onChange}
                />
            </div>
            {/* 便于销毁弹窗时不用手动重置封装 modal 里的变量 */}
            {showCreateModal ? (
                <CreateModal
                    visible={showCreateModal}
                    onCancel={() => {
                        setShowCreateModal(false);
                        setRecord({});
                    }}
                    onOk={handleOk}
                    isEdit={isEdit}
                    task={record}
                />
            ) : null}
            <StartConfirmModal
                visible={showStartConfirmModal}
                task={record}
                onCancel={() => {
                    setShowStartConfirmModal(false);
                    setRecord({});
                }}
                onOk={() => {
                    setShowStartConfirmModal(false);
                    setRecord({});
                    fetchDataAsync();
                }}
            />
            <DialogBox
                type='warning'
                destroyOnClose={true}
                title={i18n.t('删除任务')}
                content={i18n.t('删除后资源和相关数据将无法恢复，请确认是否删除任务“{{id}}” ？', { id: record?.id })}
                visible={showDeleteModal}
                onCancel={() => {
                    setShowDeleteModal(false);
                    setRecord({});
                }}
                onOk={handleDelete}
            />
            <DialogBox
                type='warning'
                destroyOnClose={true}
                title={i18n.t('结束演练')}
                content={i18n.t('“{{id}}”正在演练中，请确认是否结束演练？', { id: record?.id })}
                visible={showStopModal}
                onCancel={() => {
                    setShowStopModal(false);
                    setRecord({});
                }}
                onOk={onConfirmStop}
            />
            <ReportDrawer
                visible={showReportDrawer}
                onClose={() => setShowReportDrawer(false)}
                record={record}
            />
        </div>
    );
};

export default FailoverPlan;
