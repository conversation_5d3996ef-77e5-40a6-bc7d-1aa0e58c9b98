import React, { useState } from 'react';
import './style.less';
import CreateDrillImg from '@/img/drill/create-drill.png';
import StartDrillImg from '@/img/drill/start-drill.png';
import ViewDrillimg from '@/img/drill/view-drill.png';
import {cbaI18nInstance as i18n,Trans} from '@baidu/bce-react-toolkit';
const ProcessGuiding = ({ style, show = true, setShow }) => {
    if (!show) return null;

    const [steps] = useState([
        {
            img: CreateDrillImg,
            title: i18n.t('创建故障演练任务'),
            content: i18n.t('选择需要演练的专线资源，创建演练任务。')
        },
        {
            img: StartDrillImg,
            title: i18n.t('开始故障演练'),
            content: i18n.t('模拟真实故障，在实际使用之前完成验证。')
        },
        {
            img: ViewDrillimg,
            title: i18n.t('查看演练报告'),
            content: i18n.t('演练结束查看演练过程具体时间、演练资源具体状态。')
        }
    ]);

    return (
        <div className='dc-drill-guiding' style={style}>
            <div className='drill-guiding-header'>
                <Trans>
                <span className='title'>故障演练使用流程引导</span>
               
                <span className='btn-hide' onClick={() => setShow(false)}>隐藏</span>
                 </Trans>
            </div>     
            <div className='drill-guiding-steps'>
                {
                    steps.map((item, index) => (
                        <div className='drill-guiding-step' key={index}>
                            <img className='drill-step-img' src={item.img}></img>
                            <div className='drill-step-title'>
                                <div className='step-title-index'>{index + 1}</div>
                                <div className='step-title'>{item.title}</div>
                            </div>
                            <div className='drill-step-content'>{item.content}</div>
                        </div>
                    ))
                }
            </div>
        </div>
    );
};

export default ProcessGuiding;
