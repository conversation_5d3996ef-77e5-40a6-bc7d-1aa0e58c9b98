import React, { useEffect, useState } from 'react';
import { Link, Space, Badge, Table, Loading, DialogBox, Tooltip } from 'acud';
import { OutlinedLeft } from 'acud-icon';
import { getFailoverTaskReport, deleteTaskReport, getFailoverTaskDetail } from '@/apis/failover';
import { parseQuery } from '@/utils/helper';
import { defaultColumns, defaultReportColumns, infoColumns } from './columns';
import ReportDrawer from '../report';
import { showNotification, getBadgeStatus } from '../../common/util';
import { TASK_STATUS, TASK_STATUS_MAP } from '../../common/const';
import testId from '@/testId';
import './style.less';
import {cbaI18nInstance as i18n,Trans} from '@baidu/bce-react-toolkit';

const BasicInfo = ({ detail = {} }) => {
    return (
        <div
            className='basic-info-wrap'
            data-testid={testId.dcDrill.detail}
        >
            {infoColumns.map((column, index) => (
                <div
                    className='basic-info-item'
                    key={index}
                >
                    <strong>{column?.label}</strong>
                    {column?.render ? column?.render(detail[column?.key]) : detail[column?.key]}
                </div>
            ))}
        </div>
    );
};

const DrillDetail = () => {
    const query = parseQuery(window.location.hash) as any;
    const [loading, setLoading] = useState(true);
    const [detail, setDetail] = useState<any>({});
    const [resourceList, setResourceList] = useState([]);
    const [reportList, setReportList] = useState([]);
    const [columns, setColumns] = useState<any[]>([]);
    const [reportColumns, setReportColumns] = useState<any[]>([]);
    const [showReportDrawer, setShowReportDrawer] = useState(false);
    const [record, setRecord] = useState({});
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [currentReport, setCurrentReport] = useState<any>({});
    const operationColumn = {
        title: i18n.t('操作'),
        key: 'operation',
        dataIndex: 'operation',
        render: (text, record) => {
            const disabled = [TASK_STATUS_MAP.STARTING, TASK_STATUS_MAP.RUNNING, TASK_STATUS_MAP.ENDING].indexOf(detail?.status) > -1;
            return (
                <Space>
                    <Link onClick={(e) => viewReport(e, record)}>{i18n.t('查看报告')}</Link>
                    <Tooltip title={disabled ? i18n.t('任务正在执行中，不支持删除') : ''}>
                        <Link
                            disabled={disabled}
                            onClick={(e) => onDelete(e, record)}
                        >{i18n.t('删除')}</Link>
                    </Tooltip>
                </Space>
            );
        },
    };
    const statusText = TASK_STATUS.find(_ => _.value === detail?.status)?.label;
    const badgeStatus = getBadgeStatus(detail?.status);

    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        setLoading(true);
        const payload= {
            taskId: query?.id
        };
        const res = await getFailoverTaskDetail(payload) as any;
        setDetail(res?.info || {});
        initColumns(res?.info);
        let list = res?.info?.instanceIds || [];
        const instanceVos = res?.info?.instanceVos || [];
        list = list.map(instanceId => {
            const item = instanceVos.find(item => item?.instanceId === instanceId);
            return {
                instanceId,
                instanceType: res?.info?.instanceType,
                detail: item
            };
        });
        setResourceList(list);
        const resp = await getFailoverTaskReport(payload) as any;
        const nextReportList = resp?.infos?.map(item => ({...item, ...res?.info}));
        setReportList(nextReportList || []);
        setLoading(false);
    };

    const initColumns = (info) => {
        let temp = [...defaultColumns];
        // 物理专线显示到期时间，专线通道因为是后付费不展示到期时间
        if (info?.instanceType !== 'dcphy') {
            temp = temp.filter(item => item?.dataIndex !== 'expireTime');
        } else {
            temp = temp.filter(item => ['vlanId', 'ips'].indexOf(item?.dataIndex) === -1);
        }
        setColumns(temp);
        setReportColumns([...defaultReportColumns, operationColumn]);
    };

    // 查看报告
    const viewReport = (e, row) => {
        e.preventDefault();
        setRecord(row);
        setShowReportDrawer(true);
    };

    // 删除报告
    const onDelete = (e, row) => {
        e.preventDefault();
        setCurrentReport(row);
        setShowDeleteModal(true);
    };

    const handleDelete = async () => {
        try {
            const payload = {
                taskId: currentReport?.taskId,
                reportId: currentReport?.reportId,
            };
            const res = await deleteTaskReport(payload);
            showNotification(i18n.t('删除任务提交成功，请刷新列表查看状态'));
            fetchData();
        } catch (err) {
            showNotification(i18n.t('删除任务提交失败，请稍后重试', 'error'));
        } finally {
            setShowDeleteModal(false);
            setCurrentReport({});
        }
    };

    return (
        <div className='dc-drill-detail'>
            <Loading loading={loading}>
                <Space className='nav-header' size={16}>
                    <Link
                        href={`#/dc/failover/${query?.fr ? 'result' : 'plan'}`}
                        target='_self'
                        type='primary'
                        icon={<OutlinedLeft width={16} height={16} fill={'#84868c'} />}
                    >
                        {i18n.t('返回')}
                    </Link>
                    <span className='drill-task-name'>{detail?.name}</span>
                    <span className='drill-task-status'>
                        <Badge status={badgeStatus} />{statusText}
                    </span>
                </Space>
                <main className='detail-content'>
                    <div className='block-info basic-info'>
                        <Trans>
                        <h3 className='title'>基本信息</h3>
                        </Trans>
                        <BasicInfo detail={detail} />
                    </div>
                    <div className='block-info resources-info'>
                        <Trans>
                        <h3 className='title'>演练资源实例</h3>
                       </Trans>
                        <Table
                            className='resources-table'
                            rowKey={'instanceId'}
                            columns={columns}
                            dataSource={resourceList}
                            pagination={{
                                hideOnSinglePage: true,
                                pageSize: 8,
                                total: resourceList?.length,
                                showTotal: (total) => i18n.t('共 {{total}} 条', { total }),
                            }}
                        />
                    </div>
                    <div className='block-info reports-info'>
                        <Trans>
                        <h3 className='title'>报告记录</h3>
                        </Trans>
                        <Table
                            className='reports-table'
                            rowKey={'reportId'}
                            columns={reportColumns}
                            dataSource={reportList}
                            pagination={{
                                hideOnSinglePage: true,
                                pageSize: 8,
                                total: reportList?.length,
                                showTotal: (total) => i18n.t('共 {{total}} 条', { total }),
                            }}
                        />
                    </div>
                </main>
            </Loading>
            <ReportDrawer
                visible={showReportDrawer}
                onClose={() => setShowReportDrawer(false)}
                record={record}
            />
            <DialogBox
                type='warning'
                destroyOnClose={true}
                title={i18n.t('确定要删除全部传输记录吗？')}
                content={i18n.t(' 删除后将无传输记录')}
                visible={showDeleteModal}
                onCancel={() => {
                    setShowDeleteModal(false);
                    setCurrentReport({});
                }}
                onOk={handleDelete}
            />
        </div>
    );
};

export default DrillDetail;
