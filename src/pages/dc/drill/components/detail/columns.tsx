import React from 'react';
import { <PERSON>, Badge, Tag, Space } from 'acud';
import moment from 'moment';
import { INSTANCE_TYPE } from '../../common/const';
import { formatSecondsToChinese, status2Text } from '../../common/util';
import {cbaI18nInstance as i18n,Trans} from '@baidu/bce-react-toolkit';
export const defaultColumns = [
    {
        title: i18n.t('实例名称'),
        dataIndex: 'name',
        render: (text, record) => {
            const { instanceType, instanceId, detail } = record;
            const isDeleted = !detail || Object.keys(detail).length === 0;
            let href = instanceType === 'dcphy' ? '#/dc/instance/list' : '#/dc/channel/list';
            href += `?id=${instanceId}`;
            return (
                <Space>
                    <Link href={href}>{detail?.instanceName || '-'}</Link>
                    {isDeleted ? <Tag color='warning-status'>{i18n.t('该资源可能已被删除')}</Tag> : null}
                </Space>
            )
        }
    },
    {
        title: i18n.t('实例ID'),
        dataIndex: 'instanceId'
    },
    {
        title: i18n.t('状态'),
        dataIndex: 'interfaceStatus',
        render: (text, record) => {
            const { detail } = record;
            const isDeleted = !detail || Object.keys(detail).length === 0;
            const status = detail?.status === 'established' ? 'success' : 'error';
            return (
                <div>
                    {!isDeleted ? <Badge status={status} /> : null}
                    <span>{status2Text(detail?.status)}</span>
                </div>
            );
        }
    },
    {
        title: 'VLAN ID',
        dataIndex: 'vlanId',
        key: 'vlanId',
        render: (text, record) => record?.detail?.vlanId || '-'
    },
    {
        title: i18n.t('IPV4互联IP'),
        dataIndex: 'ips',
        key: 'ips',
        render: (text, record) => {
            const { detail } = record;
            return (
                <>
               
                    <p>{detail?.localIp || '-'}<Trans>{'（云端网络）'}  </Trans></p>
                    <p>{detail?.remoteIp || '-'} <Trans>{'（IDC端）'}   </Trans></p>
                 
                </>
            )
        }
    },
    {
        title: i18n.t('到期时间'),
        dataIndex: 'expireTime',
        key: 'expireTime',
        render: (_, record) => {
            const expireTime = record?.detail?.expireTime;
            return expireTime ? moment(expireTime).format('YYYY-MM-DD HH:mm:ss') : '-';
        }
    }
];

export const defaultReportColumns = [
    {
        title: i18n.t('报告ID'),
        dataIndex: 'reportId'
    },
    {
        title: i18n.t('报告时间'),
        dataIndex: 'createTime',
        render: _ => _ ? moment(_).format('YYYY-MM-DD HH:mm:ss') : '-'
    }
];

export const infoColumns = [
    { label: i18n.t('任务名称：'), key: 'name' },
    { label:i18n.t('任务ID：'), key: 'id' },
    {
        label: i18n.t('资源类型：'),
    
        key: 'instanceType',
        render: (text) => INSTANCE_TYPE[text]
    },
    { label: i18n.t('开始时间：'), key: 'startTime', render: _ => _ ? moment(_).format('YYYY-MM-DD HH:mm:ss') : '-' },
    { label:i18n.t( '结束时间：'), key: 'endTime', render: _ => _ ? moment(_).format('YYYY-MM-DD HH:mm:ss') : '-' },
    {
        label: i18n.t('演练时间：'),
    
        key: 'taskDuration',
        render: (minutes) => formatSecondsToChinese(minutes * 60)
    },
    { label: i18n.t('描述：'), key: 'description', render: _ => _ || '-' },
];
