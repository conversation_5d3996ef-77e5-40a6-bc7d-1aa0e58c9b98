import React, { useState, useEffect } from 'react';
import { Drawer, Alert, Table, Loading, Tooltip, Button, Space } from 'acud';
import { OutlinedExclamationCircle } from 'acud-icon';
import { dcphyColumns, channelColumns, channelBgpColumns } from './columns';
import { getFailoverTaskReport, updateFailoverTaskReport } from '@/apis/failover';
import { INSTANCE_TYPE, TASK_STATUS_MAP } from '../../common/const';
import { showNotification } from '../../common/util';
import testId from '@/testId';
import './style.less';
import {cbaI18nInstance as i18n,Trans} from '@baidu/bce-react-toolkit';
const noop = () => {};
const columnMap = {
    dcphy: dcphyColumns,
    channel: channelColumns,
    channel_bgp: channelBgpColumns
}

const ReportDrawer = ({
    visible = false,
    onClose = noop,
    record,
}) => {
    const [loading, setLoading] = useState(true);
    const [columns, setColumns] = useState([]);
    const [tableData, setTableData] = useState<any>({});
    const inDetail = window.location.href.indexOf('/dc/failover/detail') !== -1;

    useEffect(() => {
        if (visible && record) {
            getTableData();
            const instanceType = record?.instanceType;
            let nextColumns = columnMap[instanceType] || dcphyColumns;
            const status = record?.status;
            nextColumns = nextColumns?.map((item) => {
                if (
                    item?.dataIndex === 'interfaceStatus'
                    && status === TASK_STATUS_MAP.RUNNING
                ) {
                    item.title = item?.title?.replace(i18n.t('后'), i18n.t('中'));
                }
                return item;
            });
            setColumns(nextColumns);
        }
    }, [visible, record]);

    // 获取演练任务列表
    const getTableData = async () => {
        setLoading(true);
        const params: any = {
            taskId: record?.id,
            reportId: record?.reportId,
            queryType: 'single'
        };
        try {
            const res = await getFailoverTaskReport(params) as any;
            let data = res?.infos?.[0] || {};
            data.dcdtInstanceVos = data?.dcdtInstanceVos?.map((item: any) => ({
                ...item,
                instanceType: data?.instanceType
            }));
            setTableData(data); // 默认取最新的一条数据
            setLoading(false);
        } catch (err) {
            showNotification(i18n.t('获取演练资源列表失败'), 'error');
        }
    };

    // 更新报告
    const updateReport = async () => {
        const payload = { taskId: record?.id };
        const resp = await updateFailoverTaskReport(payload) as any;
        if (resp?.success === false) {
            showNotification(resp?.message?.global, 'warning');
            return;
        }
        showNotification(i18n.t('更新报告中，请稍后查看结果'));
    };

    return (
        <Drawer
            title={i18n.t('演练报告')}
            data-testid={testId.dcDrillResult.reportDrawer}
            width={'54%'}
            visible={visible}
            onClose={onClose}
            destroyOnClose
        >
            <div className='drill-report-drawer'>
                <Loading loading={loading}>
                    {/* <Alert
                        message={
                            <p className='warning'>
                                资源 <span>Missdeew30112、Missdeew30112</span> 已到期，不参与故障演练任务。
                                <a href='/finance/#/finance/account/recharge' target='_blank'>去续费</a>
                            </p>
                        }
                        banner
                    /> */}
                    {!inDetail ? (
                        <Alert
                            type='info'
                            icon={<OutlinedExclamationCircle />}
                            showIcon
                            message={i18n.t('此处只展示最新生成的报告，如要查看历史报告，请到任务详情页面查看。')}
                        />
                    ) : null}
                    <Space size={4} style={{ marginTop: 10 }}>
                        {i18n.t('当前报告数量：')}{record?.reportCount || '-'}
                        <Tooltip
                            title={!record?.canUpdateReport ? i18n.t('超过2天不能重新获取报告') : ''}
                        >
                           
                            <Button
                                style={{ marginLeft: 10 }}
                                disabled={!record?.canUpdateReport}
                                onClick={updateReport}
                            >{i18n.t('重新获取报告')}
                            </Button>
                        </Tooltip>
                    </Space>
                    <div className='task-info'>
                        <Trans>
                        <p>任务ID：{record?.id}</p>
                        <p>任务名称：{record?.name}</p>
                        </Trans>
                        <Trans>
                        <p>报告ID：{tableData?.reportId}</p>
                        <p>资源类型：{INSTANCE_TYPE[record?.instanceType]}</p> 
                        </Trans>                       
                    </div>
                    <Table
                        rowKey={'taskId'}
                        scroll={{ x: 1000 }}
                        loading={loading}
                        columns={columns}
                        dataSource={tableData?.dcdtInstanceVos}
                        pagination={{
                            total: tableData?.dcdtInstanceVos?.length,
                            showTotal: (total) => i18n.t('共 {{total}} 条', { total }),
                            showQuickJumper: true,
                            showSizeChanger: true,
                            onChange: getTableData
                        }}
                    />
                </Loading>
            </div>
        </Drawer>
    );
};

export default ReportDrawer;
