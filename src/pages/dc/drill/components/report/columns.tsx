import React from 'react';
import { <PERSON><PERSON>, <PERSON> } from 'acud';
import moment from 'moment';
import { formatSecondsToChinese } from '../../common/util';
import { INSTANCE_ERROR_TEXT_MAP } from '../../common/const';
import {cbaI18nInstance as i18n,} from '@baidu/bce-react-toolkit';
const commonColumns = [
    {
        title: i18n.t('实例名称/ID'),
        dataIndex: 'taskId',
        width: 156,
        render: (text, record) => {
            const { instanceType, instanceId } = record;
            let href = instanceType === 'dcphy' ? '#/dc/instance/list' : '#/dc/channel/list';
            href += `?id=${instanceId}`;
            return (
                <>
                    <Link href={href} target='_blank'>{record?.instanceName}</Link>
                    <div>{record?.instanceId}</div>
                </>
            )
        }
    },
    {
        title: i18n.t('实际开始时间'),
        dataIndex: 'realStartTime',
        width: 148,
        render: _ => _ ? moment(_).format('YYYY-MM-DD HH:mm:ss') : '-'
    },
    {
        title: i18n.t('实际结束时间'),
        dataIndex: 'realEndTime',
        width: 148,
        render: _ => _ ? moment(_).format('YYYY-MM-DD HH:mm:ss') : '-'
    },
    {
        title: i18n.t('实际演练时长'),
        dataIndex: 'realDuration',
        width: 110,
        render: _ => formatSecondsToChinese(_)
    },
];

// 物理专线
export const dcphyColumns = [
    ...commonColumns,
    {
        title: i18n.t('物理端口状态（演练后）'),
        dataIndex: 'interfaceStatus',
        width: 148,
        render: (text, record) => {
            const interfaceStatus = record?.interfaceStatus;
            const status = interfaceStatus === 'up' ? 'success' : 'error';
            const hasOtherError = ['up', 'down'].indexOf(interfaceStatus) === -1;
            return (
                <div>
                    <Badge status={status} />
                    <span>{hasOtherError ? INSTANCE_ERROR_TEXT_MAP[interfaceStatus] : interfaceStatus}</span>
                </div>
            );
        }
    }
];

// 专线通道
export const channelColumns = [
    ...commonColumns,
    {
        title: i18n.t('互联地址ping结果（演练后）'),
        dataIndex: 'interfaceStatus',
        width: 148,
        render: (text, record) => {
            const interfaceStatus = record?.interfaceStatus;
            const status = interfaceStatus === 'up' ? 'success' : 'error';
            const statusText = interfaceStatus === 'up' ? i18n.t('通') : i18n.t('不通');
            const hasOtherError = ['up', 'down'].indexOf(interfaceStatus) === -1;
            return (
                <div>
                    <Badge status={status} />
                    <span>{hasOtherError ? INSTANCE_ERROR_TEXT_MAP[interfaceStatus] : statusText}</span>
                </div>
            );
        }
    }
]

// 专线 BGP
export const channelBgpColumns = [
    ...commonColumns,
    {
        title: i18n.t('BGP状态（演练后）'),
        dataIndex: 'interfaceStatus',
        width: 148,
        render: (text, record) => {
            const interfaceStatus = record?.interfaceStatus;
            const status = interfaceStatus === 'up' ? 'success' : 'error';
            const statusText = interfaceStatus === 'up' ? 'established' : 'unestablished';
            const hasOtherError = ['up', 'down'].indexOf(interfaceStatus) === -1;
            return (
                <div>
                    <Badge status={status} />
                    <span>{hasOtherError ? INSTANCE_ERROR_TEXT_MAP[interfaceStatus] : statusText}</span>
                </div>
            );
        }
    }
]
