import React from 'react';
import { Bad<PERSON>, <PERSON> } from 'acud';
import testId from '@/testId';
import { status2Text } from '../../common/util';
import {cbaI18nInstance as i18n,} from '@baidu/bce-react-toolkit';
export const defaultColumns = [
    {
        title: i18n.t('实例名称/ID'),
        dataIndex: 'id',
        key: 'id',
        render: (text, record, index) => {
            const { instanceType, id } = record;
            let href = instanceType === 'dcphy' ? '#/dc/instance/list' : '#/dc/channel/list';
            href += `?id=${id}`;
            return (
                <div data-testid={`${testId.dcDrill.resourceList}-${index}`}>
                    <Link href={href} target='_blank'>{record?.name}</Link>
                    <div>{record?.id}</div>
                </div>
            );
        }
    },
    {
        title: i18n.t('状态'),
        dataIndex: 'status',
        key: 'status',
        render: (text, record) => (
            <div>
                <Badge status='success' />
                <span>{status2Text(record?.status)}</span>
            </div>
        )
    },
];

export const startTypes = [
    { label: i18n.t('立即演练'), value: 'StartNow' },
    { label: i18n.t('稍后演练'), value: 'StartLater' }
];

export const timeUnits = [
    {value: 'hour', label: i18n.t('小时')},
    {value: 'minute', label: i18n.t('分钟')},
];
