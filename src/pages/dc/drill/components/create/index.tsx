import React, { useState, useEffect } from 'react';
import {
    Modal, Form, Input, Radio, Transfer, Select,
    Button, Loading, Alert, Tooltip, Space, ConfigProvider
} from 'acud';
import en_US from 'acud/lib/locale/en_US';
import zh_CN from 'acud/lib/locale/zh_CN';
import moment from 'moment';
import uniqBy from 'lodash/uniqBy';
import {defaultColumns, startTypes, timeUnits } from './columns';
import {
    createFailoverTask, updateFailoverTask, getFailoverTaskList,
    getApAddrList, getDcList, getChannelList
} from '@/apis/failover';
import testId from '@/testId';
import { getEtAvaliableRegion } from '@/pages/sanPages/utils/helper';
import { INSTANCE_TYPES } from '../../common/const';
import { showNotification } from '../../common/util';
import './style.less';
import {cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
const noop = (id, startNow = false) => {};
const layout = {
    labelAlign: 'left',
    labelWidth: 66,
};
const { Option } = Select;
const TIME_UNIT = {
    HOUR: 'hour',
    MINUTE: 'minute'
};
const PAGE_SIZE = 10;
const MAX_SELECT_RESOURCE_NUM = 16;

const CreateModal = ({
    visible = false,
    onOk = noop,
    onCancel = noop,
    isEdit = false,
    task // 编辑时任务详情
}) => {
    const [loading, setLoading] = useState(false);
    const [regions, setRegions] = useState([]);
    const [columns] = useState([...defaultColumns]);
    const [dataSource, setDataSource] = useState([]);
    const [targetKeys, setTargetKeys] = useState<string[]>([]);
    const [leftPageNo, setLeftPageNo] = useState(1);
    const [rightPageNo, setRightPageNo] = useState(1);
    const [form] = Form.useForm();
    const [timeUnit, setTimeUnit] = useState(TIME_UNIT.HOUR);
    const INSTANCE_TYPE_API_MAP = {
        dcphy: getDcList,
        channel: getChannelList,
        channel_bgp: getChannelList
    };
    const [disableSubmit, setDisableSubmit] = useState(true);
    const [disableSubmitTip, setDisableSubmitTip] = useState('');
    const startType = Form.useWatch('startType', form);
    const taskDuration = Form.useWatch('taskDuration', form);
    const [hasUndoneTask, setHasUndoneTask] = useState(false); // 所选 region 是否有未完成的任务
    const [creating, setCreating] = useState(false);

    useEffect(() => {
        setLoading(true);
        getRegions()
            .then(initForm)
            .then(() => {
                const { region, instanceType } = form.getFieldsValue();
                getResources(region, instanceType);
                checkUndonTasksByRegion(region);
            });
        document.addEventListener('visibilitychange', handleVisibilityChange);
        return () => {
            document.removeEventListener('visibilitychange', handleVisibilityChange);
        };
    }, []);

    /** 当页面可见时，检查是否有未完成的任务 */
    const handleVisibilityChange = () => {
        if (document.visibilityState === 'visible') {
            const { region } = form.getFieldsValue();
            checkUndonTasksByRegion(region);
        }
    };

    const onLeftPageChange = (current) => {
        setLeftPageNo(current);
    };

    const onRightPageChange = (current) => {
        setRightPageNo(current);
    };

    const resetPageNo = () => {
        setLeftPageNo(1);
        setRightPageNo(1);
    };

    const leftPagination = {
        current: leftPageNo,
        pageSize: PAGE_SIZE,
        total: dataSource?.length,
        showTotal: (total) => i18n.t('共 {{total}} 条', { total }),
        onChange: onLeftPageChange,
    };

    const rightPagination = {
        current: rightPageNo,
        pageSize: 10,
        total: targetKeys.length,
        onChange: onRightPageChange,
    };

    /** 初始化表单数据 */
    const initForm = async () => {
        if (!isEdit) return;
        let taskDuration = task?.taskDuration;
        // 如果能被 60 整除，则以小时为单位，否则以分钟为单位
        if (taskDuration % 60 === 0) {
            setTimeUnit(TIME_UNIT.HOUR);
            taskDuration = taskDuration / 60;
        } else {
            setTimeUnit(TIME_UNIT.MINUTE);
        }
        setTargetKeys(task?.instanceIds);
        setDisableSubmit(false);
        form.setFieldsValue({
            name: task?.name,
            description: task?.description,
            instanceType: task?.instanceType,
            region: task?.region,
            instanceIds: task?.instanceIds,
            startType: task?.startType,
            taskDuration
        });
    };

    // 获取区域列表
    const getRegions = async () => {
        const region = 'all';
        const res = await getApAddrList(region);
        const enableEtRegion = getEtAvaliableRegion((window as any).$context, res);
        let list = enableEtRegion.regionList;
        list = list.map(item => ({
            ...item,
            label: item.text
        }));
        setRegions(list);
        // 初始化第一项 region
        const initRegion = list?.[0]?.value;
        form.setFieldValue('region', initRegion);
    };

    // 获取演练资源，需要根据选择区域过滤
    const getResources = async (region = '', type = 'dcphy', pageNo = leftPageNo) => {
        const requestApi = INSTANCE_TYPE_API_MAP[type];
        let temp = {};
        if (type === 'channel') {
            temp = {
                routeType: 'static-route'
            };
        } else if (type === 'channel_bgp') {
            temp = {
                routeType: 'bgp'
            };
        }
        const payload: any = {
            pageNo,
            pageSize: 10000,
            region,
            status: 'established',
            ...temp,
        };
        let res;
        let list;
        if (type === 'dcphy') {
            res = await requestApi(payload);
            list = res?.result || [];
        } else {
            // 专线通道包含自己创建的和别人创建分配给自己的，此处分开请求
            res = await Promise.all([
                requestApi({ ...payload, creator: 'oneself' }),
                requestApi({ ...payload, creator: 'other' }),
            ]);
            list = [...res?.[0]?.result, ...res?.[1]?.result];
        }

        const instanceType = form.getFieldValue('instanceType');
        list = uniqBy(list, 'id')
            .map((item: any) => ({
                ...item,
                key: item?.id,
                instanceType
            }))
            .filter((item: any) => {
                // 过滤掉：1、分配给其他账户的专线通道资源，2、存在跨账号专线通道的物理专线
                return (type !== 'dcphy' && item?.onlySelfAuthorized)
                    || (type === 'dcphy' && !item?.existCrossAccountChannel);
            });
        setDataSource(list);
        setLoading(false);
    };

    /** 根据 region 判断当前账号当前所选 region 是否存在未完成的演练任务，不能并行 2 个演练任务 */
    const checkUndonTasksByRegion = async (region) => {
        const params = {
            notWithStatus: 'done',
            pageNo: 1,
            pageSize: 1000
        };
        const res = await getFailoverTaskList(params) as any;
        const list = res?.result?.filter(item => item?.region === region);
        setHasUndoneTask(list?.length > 0);
    };

    //  确定
    const onConfirm = async () => {
        try {
            const values = await form.validateFields();
            const params = {
                ...values,
                taskDuration: timeUnit === 'hour' ? +values.taskDuration * 60 : +values.taskDuration,
                ...(isEdit ? { taskId: task?.id } : {})
            };
            // 前置校验，已经加入演练任务的物理专线，禁止用户创建跨账号专线通道，且当前用户创建本账号通道时提示
            const requestApi = isEdit ? updateFailoverTask : createFailoverTask;
            setCreating(true);
            const res = await requestApi(params) as any;
            setCreating(false);
            // 监听创建错误 code
            if (res?.success === false) {
                showNotification(res?.message?.global, 'error');
            }
            const id = isEdit ? task?.id : res?.id;
            const startNow = values?.startType === 'StartNow';
            onOk(id, startNow);
        } catch (error) {
            console.error(error);
            showNotification(i18n.t('创建失败'), 'error');
            setCreating(false);
        }
    };

    // 校验表单
    const handleFormChange = async () => {
        try {
            const requiredFields = ['name', 'region', 'instanceType', 'instanceIds', 'startType', 'taskDuration'];
            const values = await form.validateFields(requiredFields);
            const invalid = values?.instanceIds?.length === 0; // 特殊校验
            setDisableSubmit(invalid);
        } catch (err) {
            // 此处 form 有个 bug？即使 errorFields 为空还是抛出了错误。
            const disable = err?.errorFields?.length > 0;
            setDisableSubmit(disable);
            if (disable) {
                const tip = err?.errorFields?.[0]?.errors?.[0];
                setDisableSubmitTip(tip);
            }
        }
    };

    // 获取预计完成时间
    const estimatedCompletionTime = () => {
        let time = moment().add(3, 'days').format('YYYY-MM-DD HH:mm:ss');
        if (!taskDuration) return time;
        const addUnit = timeUnit === TIME_UNIT.HOUR ? 'hour' : 'minute';
        time = moment().add(taskDuration, addUnit).format('YYYY-MM-DD HH:mm:ss');
        return time;
    };



    // 获取禁用按钮提示文案
    const getDisableSubmitTip = () => {
        if (disableSubmit) return disableSubmitTip || i18n.t('请完善表单信息');
        return !isEdit && hasUndoneTask ? i18n.t('当前所选区域已存在未完成的演练任务，不能并行创建演练任务' ): '';
    };

    const getDisabled = () => {
        if (isEdit) return disableSubmit; // 编辑时根据表单决定是否禁用
        return disableSubmit || hasUndoneTask; // 创建时根据表单和同地域是否存在有未完成的任务决定是否禁用
    };

    // 获取当前语言环境
    const isEn = window.$framework?.i18n?.getCurrentLanguage()?.toLowerCase() !== 'zh-cn';

    return (
        <ConfigProvider locale={isEn ? en_US : zh_CN}>
            <Modal
            title={`${isEdit ? i18n.t('编辑') : i18n.t('创建')}演练任务`}
            modalRender={(node) => (
                <div data-testid={testId.dcDrill.createModal}>{node}</div>
            )}
            className='create-drill-modal'
            width={1000}
            visible={visible}
            destroyOnClose
            zIndex={1000}
            onCancel={onCancel}
            footer={
                <Space>

                    <Button onClick={onCancel}>{i18n.t('取消')}</Button>

                    <Tooltip title={getDisableSubmitTip()}>
                        <Button
                            type='primary'
                            data-testid={testId.dcDrill.createConfirmBtn}
                            disabled={getDisabled()}
                            loading={creating}
                            onClick={onConfirm}
                        >{i18n.t('确定')}</Button>

                    </Tooltip>
                </Space>

            }
        >
            <Loading loading={loading}>
                <Form
                    className='create-drill-form'
                    labelCol={{ span: 2 }}
                    wrapperCol={{ span: 22 }}
                    labelAlign='left'
                    initialValues={{
                        instanceType: 'dcphy',
                        startType: 'StartNow',
                        instanceIds: []
                    }}
                    form={form}
                    onValuesChange={handleFormChange}
                >
                    <Form.Item
                        label={i18n.t("任务名称")}
                        name="name"
                        rules={[
                            {required: true, message: i18n.t('请输入任务名称')},
                            ({ getFieldValue }) => ({
                                validator(_, value) {
                                    if (!value) {
                                        return Promise.resolve();
                                    }
                                    if (value.length > 65) {
                                        return Promise.reject(new Error(i18n.t('任务名称长度不能超过 65 个字符')));
                                    }
                                    const pattern = /^[a-zA-Z\u4e00-\u9fa5][a-zA-Z0-9\u4e00-\u9fa5\-_\/.]{0,64}$/;
                                    if (!pattern.test(value)) {
                                        return Promise.reject(new Error(i18n.t('请输入正确的任务名称')));
                                    }
                                    return Promise.resolve();
                                },
                            })
                        ]}
                        extra={i18n.t('命名规范：1.只能包含大小写字母，数字，中文和{{spChar}}；2.必须以字母或者中文开头；3.长度限制在1-65之间。',{spChar: "-_ /."})}
                        inputMaxWidth={800}
                    >
                        <Input
                            style={{width: '360px'}}
                            placeholder={i18n.t('请输入任务名称')}
                            autoComplete='off'
                            data-testid={testId.dcDrill.createNameInput}
                            allowClear
                        />
                    </Form.Item>
                    <Form.Item
                        label={i18n.t("地域")}
                        name='region'
                        required
                    >
                        <Radio.Group
                            optionType='button'
                            options={regions}
                            disabled={isEdit}
                            onChange={(e: any) => {
                                setTargetKeys([]);
                                form.setFieldValue('instanceIds', []);
                                const instanceType = form.getFieldValue('instanceType');
                                getResources(e.target.value, instanceType, 1);
                                checkUndonTasksByRegion(e.target.value);
                            }}
                        />
                    </Form.Item>
                    <Form.Item
                        label={i18n.t("演练资源")}
                        name='instanceType'
                        required
                    >
                        <Radio.Group
                            optionType='button'
                            disabled={isEdit}
                            options={INSTANCE_TYPES}
                            onChange={(e: any) => {
                                setTargetKeys([]);
                                resetPageNo();
                                form.setFieldValue('instanceIds', []);
                                const region = form.getFieldValue('region');
                                getResources(region, e.target.value, 1);
                            }}
                        />
                    </Form.Item>
                    <Form.Item label=''>
                        <Form.Item
                            noStyle
                            name='instanceIds'
                            required
                            rules={[
                                {
                                    validator: (rule, value) => {
                                        if (!value || value?.length === 0) {
                                            return Promise.reject(new Error(i18n.t('请选择演练资源')));
                                        }
                                        if (value?.length > MAX_SELECT_RESOURCE_NUM) {
                                            return Promise.reject(new Error(i18n.t(`最多支持选择{{max}}个演练资源`, {max: MAX_SELECT_RESOURCE_NUM})));
                                        }
                                        return Promise.resolve();
                                    }
                                }
                            ]}
                        >
                            <Transfer
                                displayType='table'
                                rowKey={(record) => record?.id}
                                showSearch
                                leftStyle={{width: 400}}
                                rightStyle={{width: 400}}
                                targetKeys={targetKeys}
                                leftColumns={columns}
                                rightColumns={columns}
                                dataSource={dataSource}
                                leftPagination={leftPagination}
                                rightPagination={rightPagination}
                                filterOption={(inputValue, option) =>
                                    option.name.indexOf(inputValue) > -1
                                    || option.id.indexOf(inputValue) > -1
                                }
                                onChange={setTargetKeys}
                            />
                        </Form.Item>
                        <Form.Item noStyle shouldUpdate={(prev, cur) => prev.instanceIds !== cur.instanceIds}>
                            {({ getFieldError }) => {
                                const errors = getFieldError('instanceIds');
                                return (
                                    errors.length > 0 && (
                                        <div style={{ color: '#F33E3E', marginTop: 8 }}>{errors[0]}</div>
                                    )
                                );
                            }}
                        </Form.Item>
                    </Form.Item>
                    <Form.Item
                        label={i18n.t("演练方式")}
                        name='startType'
                        required
                        extra={startType === 'StartNow' ? i18n.t('任务创建完成后立即开始执行演练') : i18n.t('仅创建演练计划，暂不执行演练，演练任务创建完成后可在演练计划列表页点击')}
                        inputMaxWidth={800}
                    >
                        <Radio.Group
                            optionType='button'
                            options={startTypes}
                        />
                    </Form.Item>
                    <Form.Item
                        label={i18n.t("演练时长")}
                        name='taskDuration'
                        required
                        rules={[
                            {
                                validator: (rule, value) => {
                                    if (!value) {
                                        return Promise.reject(i18n.t('请输入演练时长'));
                                    }
                                    const REG_POSITIVE_INTEGER = /^[1-9]\d*$/;
                                    if (!REG_POSITIVE_INTEGER.test(value)) {
                                        return Promise.reject(new Error(i18n.t('请输入正整数')));
                                    }
                                    if (timeUnit === 'hour' && (value < 1 || value > 72)) {
                                        return Promise.reject(new Error(i18n.t('演练时长超过 3 天')));
                                    }
                                    if (timeUnit === 'minute' && (value < 1 || value > 4320)) {
                                        return Promise.reject(new Error(i18n.t('演练时长超过 4320 分钟')));
                                    }
                                    return Promise.resolve();
                                },
                            }
                        ]}
                        extra={startType === 'StartNow'
                            ? i18n.t('可设置1分钟～72小时，预计{{time}}结束演练', { time: estimatedCompletionTime() })
                            : i18n.t('可设置1分钟～72小时')
                        }
                        tooltip={i18n.t('演练时长是指演练的模拟时长，如果到期后未完成，演练会自动停止。')}
                    >
                        <Input
                            style={{ width: 170 }}
                            data-testid={testId.dcDrill.durationInput}
                            autoComplete='off'
                            addonAfter={
                                <Select
                                    style={{ width: 120 }}
                                    className='time-unit-select'
                                    dropdownMatchSelectWidth={false}
                                    options={timeUnits}
                                    value={timeUnit}
                                    onChange={(value) => {
                                        setTimeUnit(value);
                                        handleFormChange();
                                    }}
                                />
                            }
                        />
                    </Form.Item>
                    <Form.Item
                        label={i18n.t("描述")}
                        name='description'
                    >
                        <Input.TextArea
                            placeholder={i18n.t('请输入文字描述')}
                            allowClear
                            style={{ height: 88, resize: 'none' }}
                            limitLength={200}
                            forbidIfLimit
                        />
                    </Form.Item>
                </Form>
            </Loading>
        </Modal>
        </ConfigProvider>
    );
};

export default CreateModal;
