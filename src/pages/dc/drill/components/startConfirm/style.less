.start-failover-modal {
    .warning-tip {
        padding: 8px;
        box-sizing: border-box;
        background: #FFF4E6;
        border-radius: 4px;
        overflow: hidden;

        &-title {
            display: flex;
            align-items: center;
            font-family: PingFangSC-Medium;
            font-size: 12px;
            color: #151B26;
            line-height: 22px;
            white-space: wrap;
        }
    }

    .warning-list {
        &-item {
            padding: 8px;
            box-sizing: border-box;
            border-bottom: 1px solid #D4D6D9;
            &:last-child {
                border-bottom: none;
            }
            .title {
                &::before {
                    content: '·';
                }
                font-family: PingFangSC-Regular;
                font-size: 12px;
                color: #5C5F66;
                line-height: 20px;
            }

            .content {
                font-family: PingFangSC-Regular;
                font-size: 12px;
                color: #2468F2;
                line-height: 20px;
            }
        }
    }

    .success-tip-title {
        margin-top: 16px;
        font-family: PingFangSC-Medium;
        font-size: 12px;
        color: #151B26;
        line-height: 20px;
    }

    .resource-list {
        margin-top: 10px;
        max-height: 200px;
        overflow-y: auto;
        &-item {
            padding: 8px 0;
            box-sizing: border-box;
            border-bottom: 1px solid #E8E9EB;
            font-family: PingFangSC-Regular;
            font-size: 12px;
            color: #2468F2;
            line-height: 20px;

            > span:first-child {
                margin-right: 8px;
            }
        }
    }
}
