import React, { useState, useEffect } from 'react';
import { Modal, Skeleton, Table } from 'acud';
import { FilledWarn } from 'acud-icon';
import { startFailoverTask, checkFailoverTask } from '@/apis/failover';
import { showNotification } from '../../common/util';
import { CHECK_DATA_TEXT_MAP } from '../../common/const';
import './style.less';
import {cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
const noop = () => {};
const columns = [
    {
        title: i18n.t('实例名称'),
        dataIndex: 'instanceName',
        key: 'instanceName'
    }, {
        title:i18n.t('实例ID'),
        dataIndex: 'instanceId',
        key: 'instanceId'
    }
];

const WarningList = ({ list = [] }) => {
    const getItemText = (item) => {
        const len = item?.results?.length;
        if (item?.type === 'related') {
            return i18n.t('以下{{len}}个{{instanceType}}实例，可能会出现双断情况，请确认对业务没有影响：', {
                len,
                instanceType: CHECK_DATA_TEXT_MAP[item?.type]
            });
        }
        return i18n.t('以下{{len}}个{{instanceType}}实例，将不涉及本次演练任务：', {
            len,
            instanceType: CHECK_DATA_TEXT_MAP[item?.type]
        });
    };

    return (
        <div className='warning-list'>
            {
                list.map((item, index) => {
                    return (
                        <div className='warning-list-item' key={index}>
                            <div className='title'>
                                {getItemText(item)}
                            </div>
                            <div className='content'>
                                {item?.results?.join('；')}
                            </div>
                        </div>
                    )
                })
            }
        </div>
    );
};

const StartConfirmModal = ({
    visible = false,
    task,
    onOk = noop,
    onCancel = noop,
}) => {
    const [loading, setLoading] = useState(false);
    const [list, setList] = useState([]);
    const [warningList, setWarningList] = useState([]);

    useEffect(() => {
        if (visible) {
            checkBeforeStart();
        }
    }, [visible]);

    // 演练任务检查
    const checkBeforeStart = async () => {
        setLoading(true);
        const payload = {
            instanceType: task?.instanceType,
            instanceIds: task?.instanceIds,
            taskId: task?.id
        };
        const res = await checkFailoverTask(payload) as any;
        setWarningList(res?.datas || []);
        setList(task?.instanceVos || []);
        setLoading(false);
    };

    // 确认开始演练任务
    const onConfirm = async () => {
        setLoading(true);
        const res = await startFailoverTask({ taskId: task?.id }) as any;
        if (res?.code === 'BceException') {
            showNotification(res?.message?.global, 'error', 10);
        }
        setLoading(false);
        onOk();
    };

    const getWarningText = () => {
        let arr = [];
        warningList.forEach((item) => {
            arr.push(CHECK_DATA_TEXT_MAP[item?.type]);
        });
        let str = arr.join('/');
        return i18n.t('检测到存在{{resourceType}}资源的实例，为保证演练任务正常进行请及时处理！', { resourceType: str })
    };

    return (
        <Modal
            title={i18n.t('演练前确认')}
            className='start-failover-modal'
            width={520}
            visible={visible}
            okText={i18n.t('开始演练')}
            onOk={onConfirm}
            onCancel={onCancel}
            destroyOnClose
        >
            <Skeleton loading={loading}>
                { warningList.length ? (
                    <div className='warning-tip'>
                        <h3 className='warning-tip-title'>
                            <FilledWarn
                                color='#FF9326'
                                style={{ marginRight: 4 }}
                                size={14}
                            />
                            {getWarningText()}
                        </h3>
                        <WarningList list={warningList} />
                    </div>
                ) : null }
                <h2 className='success-tip-title'>{i18n.t('本次演练包含如下实例，请确认中断这些实例不会影响业务！')}</h2>
                <div className='resource-list'>
                    <Table
                        columns={columns}
                        dataSource={list}
                        pagination={{
                            hideOnSinglePage: true,
                            total: list.length,
                            showTotal: (total) => i18n.t('共 {{total}} 条', { total })
                        }}
                    />
                </div>
            </Skeleton>
        </Modal>
    );
};

export default StartConfirmModal;
