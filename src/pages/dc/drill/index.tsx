import React, { useState, useEffect } from 'react';
import DrillPlan from './components/plan/index';
import  './style.less';
import {Trans} from '@baidu/bce-react-toolkit';
const Drill = () => {
    const [showGuiding, setShowGuiding] = useState(true);

    useEffect(() => {
        const show = (window as any).$storage.get('et.plan.guiding.show');
        show !== undefined && setShowGuiding(show);
    }, []);

    const onShow = (e) => {
        e.preventDefault();
        if (showGuiding) return;
        (window as any).$storage.set('et.plan.guiding.show', true);
        setShowGuiding(true);
    };

    return (
        <div className='dc-drill-page'>
            <div className='dc-drill-header'>
                <div className='header-left'>
                    <Trans>
                    <span className='title'>故障演练</span>
                     </Trans>
                </div>
                <div className='header-right'>
                    <a
                        className={`process-guiding ${showGuiding ? 'active' : ''}`}
                        onClick={onShow}
                    >
                         <Trans>
                        <i className='ic-circle'></i>流程引导
                         </Trans>                     
                    </a>                 
                </div>
            </div>
            <DrillPlan
                showGuiding={showGuiding}
                setShowGuiding={(val) => {
                    setShowGuiding(val);
                    (window as any).$storage.set('et.plan.guiding.show', val);
                }}
            />
        </div>
    );
};

export default Drill;
