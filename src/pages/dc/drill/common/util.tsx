import { toast } from 'acud';
import { InstanceStatus } from '@/pages/sanPages/common/enum';
import { TASK_STATUS } from './const';
import {cbaI18nInstance as i18n,useTranslation} from '@baidu/bce-react-toolkit';
const taskStatus = TASK_STATUS.map(item => item.value);

export const status2Text = (value) => {
    return value ? InstanceStatus.getTextFromValue(value) : '-';
};

// 秒转化为显示文本
export const formatSecondsToChinese = (seconds = 0) => {
    if (seconds <= 0) return '-';

    const days = Math.floor(seconds / (24 * 60 * 60));
    const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60));
    const minutes = Math.floor((seconds % (60 * 60)) / 60);
    const secs = seconds % 60;
    const { t } = useTranslation();
    let result = '';

     if (days > 0) result += t('{{days}}天', { days });
     if (hours > 0) result += t('{{hours}}小时', { hours });
     if (minutes > 0) result += t('{{minutes}}分钟', { minutes });
     if (secs > 0 || result === '') result += t('{{secs}}秒', { secs });

    return result;
};

export const getBadgeStatus = (status = '') => {
    if (status === 'pending') return 'warning';
    else if (status === 'done') return 'success';
    return 'processing';
};

type NotifyType = 'success' | 'error' | 'info' | 'warning';

export const showNotification = (text = '', type: NotifyType = 'success', duration = 5) => {
    const types = ['success', 'error', 'info', 'warning'];
    if (types.indexOf(type) === -1) {
        console.warn(i18n.t('toast 不支持此 type 类型'));
        return;
    }
    toast[type]({
        message: text,
        duration,
    });
};
