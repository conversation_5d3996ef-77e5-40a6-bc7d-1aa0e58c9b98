import {cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
export const INSTANCE_TYPES = [
    { label: i18n.t('物理专线'), value: 'dcphy' },
    { label: i18n.t('专线通道'), value: 'channel' },
    // { label: '专线通道BGP', value: 'channel_bgp' }
];

// 演练状态
export const TASK_STATUS = [
    { label: i18n.t('待演练'), value: 'pending' },
    { label: i18n.t('开始中'), value: 'starting' },
    { label: i18n.t('演练中'), value: 'running' },
    { label: i18n.t('结束中'), value: 'ending' },
    { label:i18n.t('演练完成'), value: 'done' },
];

export const TASK_STATUS_MAP = {
    PENDING: 'pending',
    STARTING: 'starting',
    RUNNING: 'running',
    ENDING: 'ending',
    DONE: 'done'
};

export const INSTANCE_TYPE = {
    dcphy: i18n.t('物理专线'),
    channel: i18n.t('专线通道'),
    channel_bgp:i18n.t('专线通道BGP'),
};

// 多线类型（合并为 related）
export const CHECK_DATA_TEXT_MAP = {
    associate: i18n.t('多线关联'), // 主子关联
    ecmp: i18n.t('多线负载'), // 多线负载
    ha: i18n.t('多线主备'), // 多线主备
    related: i18n.t('关联资源'), // 合并了前三个类型
    not_found: i18n.t('被删除'),
    not_established: i18n.t('已到期'),
};

// 演练资源实例状态
// 如果任务类型是dcphy，那up down表示物理端口状态：up/down；
// 如果类型是channel，那up down表示vlan端口ping状态：通/不通；
// 如果类型是channel_bgp，那up down表示bgp建立状态：established/unestablished
export const INSTANCE_STATUS_MAP = {
    UP: 'up',
    DOWN: 'down',
    UP_FAIL: 'up_fail', // 实例任务结束失败
    DOWN_FAIL: 'down_fail', // 实例任务开始失败
    NOT_FOUND: 'not_found', // 实例大概率被删除了
    UNKNOWN: 'unknown' // 该实例演练任务结果不清楚，调用 sys 检查失败
};

export const INSTANCE_ERROR_TEXT_MAP = {
    up: '',
    down: '',
    up_fail: i18n.t('实例任务结束失败'),
    down_fail: i18n.t('实例任务开始失败'),
    not_found: i18n.t('实例未找到'),
    unknown: i18n.t('实际任务检查失败')
};

export const SEARCH_MODES = [
    {value: 'name', label: i18n.t('任务名称')},
    {value: 'id', label: i18n.t('任务ID')},
    {value: 'instanceName', label: i18n.t('资源名称')},
    {value: 'instanceId', label: i18n.t('资源ID')},
];

export const SEARCH_PLACEHOLDER_MAP = {
    name: i18n.t('任务名称'),
    id: i18n.t('任务ID'),
    instanceName: i18n.t('资源名称'),
    instanceId: i18n.t('资源ID'),
};
