import { useReducer, useEffect } from 'react';

// 表格查询相关状态
const initialState = {
    search: '',
    searchMode: 'name',
    searchTrigger: '', // 最终触发查询
    filters: {
        instanceType: '',
        status: ''
    },
    pagination: {
        pageSize: 10,
        current: 1,
    },
    sorter: {
        orderBy: 'createTime',
        order: 'desc'
    }, // 排序
    tableData: {},
    loading: false
};

const reducer = (state, action) => {
    switch (action.type) {
        case 'SET_SEARCH_MODE':
            return { ...state, searchMode: action.payload, search: '' }; // 切换时清空
        case 'SET_SEARCH':
            return { ...state, search: action.payload };
        case 'TRIGGER_SEARCH':
            return { ...state, searchTrigger: action.payload };
        case 'SET_FILTERS':
            return { ...state, filters: action.payload };
        case 'SET_SORTER':
            return { ...state, sorter: action.payload };
        case 'SET_PAGINATION':
            return { ...state, pagination: action.payload };
        case 'SET_TABLE_DATA':
            return { ...state, tableData: action.payload };
        case 'SET_LOADING':
            return { ...state, loading: action.payload };
        default:
            return state;
    }
};

export function useTableSearch(fetchData: (params: any) => Promise<any[]>, defaultFilters = {}) {
    const [state, dispatch] = useReducer(reducer, {
        ...initialState,
        filters: { ...initialState.filters, ...defaultFilters }
    });
  
    useEffect(() => {
        fetchDataAsync();
    }, [state.searchTrigger, state.filters, state.pagination, state.sorter]);

    const fetchDataAsync = async () => {
        dispatch({ type: 'SET_LOADING', payload: true });
        const payload = getPayload();
        try {
            const res = await fetchData(payload);
            dispatch({ type: 'SET_TABLE_DATA', payload: res });
        } finally {
            dispatch({ type: 'SET_LOADING', payload: false });
        }
    };

    const getPayload = () => {
        const { searchMode, search, filters, sorter, pagination } = state;
        const payload = {
            ...filters,
            ...sorter,
            pageNo: pagination.current,
            pageSize: pagination.pageSize,
        };
        if (search) {
            payload.keyword = search;
            payload.keywordType = searchMode;
        }
        return payload;
    };
  
    return { state, dispatch, fetchDataAsync, getPayload };
};
