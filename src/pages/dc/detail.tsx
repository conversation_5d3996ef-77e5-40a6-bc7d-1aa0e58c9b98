import React, {useMemo} from 'react';
import {getDcDetail} from '@/apis';
import {useRequest} from 'ahooks';
import {InstanceStatus} from '@/pages/sanPages/common';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import DcInstanceDetail from '@/pages/sanPages/pages/dc/instance/detail/components/instance/instance';
import DcMonitor from '@/pages/sanPages/pages/dc/instance/detail/components/monitorTab/monitorTab';
import {cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
import DetailPage from '@/components/DetailPage';

import './index.less';

const L2gwDetail = () => {
    const params: any = getQueryParams();
    const {data = {} as any, loading, error, run} = useRequest(() => getDcDetail({dcphyId: params.instanceId}));
    const handleUpdate = () => {
        run();
    };
    const panesData = [
        {
            key: '/dc/instance/detail',
            tab: i18n.t('实例信息'),
            content: (
                <DcInstanceDetail context={{instance: data, instanceId: params.instanceId, refresh: handleUpdate}} />
            )
        },
        {key: '/dc/instance/monitor', tab: i18n.t('监控'), content: <DcMonitor context={params} />}
    ];

    const [text, styleClass] = useMemo(() => {
        let config = [];
        if (data?.status) {
            let statusObj = InstanceStatus.fromValue(data.status);
            const {text} = InstanceStatus.fromValue(data.status);
            const styleClass = statusObj.kclass;
            config = [text, styleClass];
        }
        return config;
    }, [data]);
    return (
        <>
            <DetailPage
                mode="vertical"
                headerName={data.name}
                statusText={text}
                statusClassName={styleClass}
                backUrl={'#/dc/instance/list'}
                panesData={panesData}
                tabClassName="dc-tab-class"
            />
        </>
    );
};

export default L2gwDetail;
