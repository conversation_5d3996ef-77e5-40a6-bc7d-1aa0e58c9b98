import React, {useMemo} from 'react';
import {useRequest} from 'ahooks';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import {EniStatus} from '@/pages/sanPages/common/enum';
import {getEniDetail} from '@/apis';
import FLAG from '@/flags';

import DetailPage from '@/components/DetailPage';
import Detail from '@/pages/sanPages/pages/eni/page/detail/detail';
import IpTab from '@/pages/sanPages/pages/eni/page/ip/ipTab';
import Security from '@/pages/sanPages/pages/eni/page/security/security';
import Monitor from '@/pages/sanPages/pages/eni/page/bcm/bcm';
import {cbaI18nInstance as i18n,} from '@baidu/bce-react-toolkit';
import './index.less';

const EniDetail = () => {
    const params: any = getQueryParams();
    const {data = {} as any, loading, error, run} = useRequest(() => getEniDetail({eniId: params.eniId}));
    const handleUpdate = () => {
        run();
    };

    const panesData = [
        {
            key: '/vpc/eni/detail',
            tab: i18n.t('实例信息'),
            content: <Detail context={{...params, instance: data, updateName: handleUpdate}} />
        },
        {key: '/vpc/eni/ip,/vpc/eni/ipv6', tab: i18n.t('IP地址'), content: <IpTab context={params} />},
        {key: '/vpc/eni/security', tab: i18n.t('安全组'), content: <Security context={params} />},
        {key: '/vpc/eni/monitor', tab: i18n.t('监控'), content: <Monitor context={params} />}
    ];

    const whitedPanesData = useMemo(() => {
        let newPanesData = [...panesData];
        if (!FLAG.NetworkEniOpt) {
            newPanesData = newPanesData.filter(item => item.key !== '/vpc/eni/monitor');
        }
        return newPanesData;
    }, [data]);

    const [text, styleClass] = useMemo(() => {
        let config = [];
        if (data?.status) {
            const {text, styleClass} = EniStatus.fromValue(data.status);
            config = [text, styleClass];
        }
        return config;
    }, [data]);

    const backList = () => {
        window.location.href = '/network/#/vpc/eni/list';
    };
    return (
        <>
            <DetailPage
                mode="vertical"
                headerName={data.name}
                backUrl="#/vpc/eni/list"
                statusText={text}
                statusClassName={styleClass}
                panesData={whitedPanesData}
                onBackList={backList}
                tabClassName="eni-tab-class"
            />
        </>
    );
};
export default EniDetail;
