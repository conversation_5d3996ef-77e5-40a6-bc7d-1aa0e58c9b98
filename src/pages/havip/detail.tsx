import React, {useMemo} from 'react';
import {useRequest} from 'ahooks';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import {HaVipStatus} from '@/pages/sanPages/common/enum';
import {getHavipDetail} from '@/apis';
import DetailPage from '@/components/DetailPage';
import Detail from '@/pages/sanPages/pages/havip/page/detail/detail';
import BandServer from '@/pages/sanPages/pages/havip/page/server/list';
import {cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
import './index.less';

const HavIpDetail = () => {
    const params: any = getQueryParams();
    const {data = {} as any, loading, error, run} = useRequest(() => getHavipDetail({haVipUuid: params.haVipId}));
    const handleUpdate = () => {
        run();
    };
    const panesData = [
        {
            key: '/vpc/havip/detail',
            tab: i18n.t('实例信息'),
            content: <Detail context={{...params, instance: data, updateName: handleUpdate}} />
        },
        {key: '/vpc/havip/server', tab: i18n.t('绑定服务器'), content: <BandServer context={params} />}
    ];

    const whitedPanesData = useMemo(() => {
        let newPanesData = [...panesData];
        return newPanesData;
    }, [data]);

    const [text, styleClass] = useMemo(() => {
        let config = [];
        if (data?.status) {
            const {text, styleClass} = HaVipStatus.fromValue(data.status);
            config = [text, styleClass];
        }
        return config;
    }, [data]);

    const backList = () => {
        window.location.href = '/network/#/vpc/havip/list';
    };

    return (
        <>
            <DetailPage
                mode="vertical"
                headerName={data.name}
                onBackList={backList}
                backUrl="#/vpc/havip/list"
                statusText={text}
                statusClassName={styleClass}
                panesData={whitedPanesData}
            />
        </>
    );
};
export default HavIpDetail;
