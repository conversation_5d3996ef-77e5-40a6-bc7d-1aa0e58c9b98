.overview-widget {
    .over-event-widget {
        .acud-tree-treenode {
            height: 32px;
            display: flex;
            border-radius: 4px;
            align-items: center;
            margin-bottom: 8px;
            padding: 0;
            cursor: pointer;
            // background-color: #eef3fe;
            &:hover {
                background-color: #eef3fe;
            }
            &:last-child {
                margin-bottom: 0;
            }
            .acud-tree-switcher {
                display: flex;
                align-items: center;
                cursor: pointer;
            }
            .acud-tree-node-content-wrapper {
                cursor: pointer;
                .acud-tree-title-titleContent {
                    font-size: 14px;
                    font-weight: 400;
                }
                &:hover {
                    background-color: transparent;
                }
            }
            .acud-tree-node-selected {
                background-color: transparent;
            }
        }
        .acud-tree-treenode-leaf {
            height: 100%;
            margin-top: -8px;
            &:hover {
                background-color: transparent;
            }
            .acud-tree-switcher-noop {
                display: none;
            }
            .acud-tree-node-content-wrapper {
                margin: 0;
                padding: 0;
            }
        }
    }
    .acud-loading-loading-context {
        top: -600px;
        background-color: rgba(255, 255, 255, 0.5);
    }
    .acud-table-wrapper {
        .acud-loading-loading-context {
            top: 0;
        }
    }
    .overview-event-center {
        .acud-loading-loading-context {
            top: 0;
        }
    }
}

.overview-timeline-widget {
    .acud-timeline-item-head {
        background-color: #2468f2;
    }
    .acud-timeline-item-content {
        .acud-timeline-item-content-title {
            cursor: pointer;
            &:hover {
                color: #2468f2;
            }
        }
    }
}
.overview-service-recom {
    background-image: url('../../img/overview_serviceRecom.png');
    background-repeat: no-repeat;
    background-size: 100%;
}
