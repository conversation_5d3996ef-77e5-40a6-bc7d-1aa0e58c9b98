import React from 'react';
import Badge from '@/components/Badge';
import {Link} from 'acud';

import OVERVIEW_RESOURCE from '@/img/overview_resource.png';
import OVERVIEW_VPC from '@/img/overview_vpc.svg?url';
import OVERVIEW_SUBNET from '@/img/overview_subnet.svg?url';
import OVERVIEW_ROUTE from '@/img/overview_route.svg?url';
import OVERVIEW_SNIC from '@/img/overview_snic.svg?url';
import OVERVIEW_PARAMS from '@/img/overview_params.svg?url';
import OVERVIEW_PRIVATE_NAT from '@/img/overview_privateNat.svg?url';
import OVERVIEW_FLOWLOG from '@/img/overview_flowlog.svg?url';

import OVERVIEW_CSN from '@/img/overview_csn.svg?url';
import OVERVIEW_PEERCONN from '@/img/overview_peerconn.svg?url';
import OVERVIEW_VPN from '@/img/overview_vpn.svg?url';
import OVERVIEW_DCGW from '@/img/overview_dcgw.svg?url';
import OVERVIEW_ET from '@/img/overview_et.svg?url';
import OVERVIEW_L2GW from '@/img/overview_l2gw.svg?url';

import OVERVIEW_EIPGROUP from '@/img/overview_eipgroup.svg?url';
import OVERVIEW_EIP from '@/img/overview_eip.svg?url';
import OVERVIEW_PUBLIC_NAT from '@/img/overview_publicNat.svg?url';

import OVERVIEW_QUOTA from '@/img/overview_quota.svg?url';
import OVERVIEW_RECHARGE from '@/img/overview_recharge.svg?url';
import OVERVIEW_TICKET from '@/img/overview_ticket.svg?url';
import {cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
/** 资源统计 */
export const resourceOverviewList = [
    {
        icon: <img src={OVERVIEW_RESOURCE} alt="" width="20px" />,
        title: i18n.t('资源总数'),
        value: 'totalNum',
        hoverTip: i18n.t('您目前持有的私有网络资源、网间互联服务和公网访问服务的总数')
    },
    {
        icon: <Badge extraClass="bg-[#FF9326]" />,
        title: i18n.t('即将过期'),
        value: 'expiringNum',
        href: '/billing/renew/list?expireInDays=15',
        hoverTip: i18n.t('15天内即将过期的资源数')
    },
    {
        icon: <Badge extraClass="bg-[#B8BABF]" />,
        title: i18n.t('已过期'),
        value: 'expiredNum',
        href: '/billing/renew/list?expireInDays=0',
        hoverTip: i18n.t('已过期的资源数')
    }
];

/** 网络资源 */
export const METWORK_RESOURCE_LIST = [
    {
        key: 'vpc',
        title: i18n.t('私有网络资源'),
        resourceList: [
            {icon: OVERVIEW_VPC, label: i18n.t('私有网络'), value: 'vpc'},
            {icon: OVERVIEW_SUBNET, label: i18n.t('子网'), value: 'subnet'},
            {icon: OVERVIEW_ROUTE, label: i18n.t('路由表'), value: 'routeTable'},
            {icon: OVERVIEW_SNIC, label: i18n.t('服务网卡'), value: 'snic'},
            {icon: OVERVIEW_PARAMS, label: i18n.t('参数模板'), value: 'ipCollection'},
            {icon: OVERVIEW_PRIVATE_NAT, label: i18n.t('私网NAT网关'), value: 'intranetNat'},
            {icon: OVERVIEW_FLOWLOG, label: i18n.t('流日志'), value: 'flowLog'}
        ]
    },
    {
        key: 'connect',
        title: i18n.t('网络连接服务'),
        resourceList: [
            {icon: OVERVIEW_CSN, label: i18n.t('云智能网CSN'), value: 'csn'},
            {icon: OVERVIEW_PEERCONN, label: i18n.t('对等连接'), value: 'peerConn'},
            {icon: OVERVIEW_VPN, label: i18n.t('VPN网关'), value: 'vpn'},
            {icon: OVERVIEW_DCGW, label: i18n.t('专线网关'), value: 'dcgw'},
            {icon: OVERVIEW_ET, label: i18n.t('物理专线'), value: 'et'},
            {icon: OVERVIEW_L2GW, label: i18n.t('二层网关'), value: 'l2gw'}
        ]
    },
    {
        key: 'visit',
        title: i18n.t('公网访问服务'),
        resourceList: [
            {icon: OVERVIEW_EIPGROUP, label: i18n.t('共享带宽'), value: 'eipGroup'},
            {icon: OVERVIEW_EIP, label: i18n.t('弹性公网IP'), value: 'eip'},
            {icon: OVERVIEW_PUBLIC_NAT, label: i18n.t('公网NAT网关'), value: 'publicNat'}
        ]
    }
];

/** 事件中心通知状态 */
export const EVENT_STATUS_MAP = {
    NOTICE: [i18n.t('通知'), '#B8BABF'],
    WARNING: [i18n.t('警告'), '#FAD000'],
    CRITICAL: [i18n.t('故障'), '#F33E3E'],
    MAJOR: [i18n.t('预警'), '#FF9326']
};

/** 服务推荐 */
export const SERVICE_RECOMMEND_LIST = [
    {label: i18n.t('云智能网CSN'), href: '/csn#/csn/instance/list'},
    {label: i18n.t('专线接入'), href: '/network/#/dc/instance/list'},
    {label: i18n.t('弹性IP'), href: '/eip/#/eip/instance/list'},
    {label: i18n.t('共享带宽'), href: '/eip/#/eip/group/list'},
    {label: i18n.t('智能流量管理'), href: '/dns/#/dns/itm'},
    {label: i18n.t('智能云解析DNS'), href: '/dns/#/dns/manage/list'},
    {label: i18n.t('负载均衡BLB'), href: '/blb/#/blb/list'},
    {label: i18n.t('云服务器BCC'), href: '/bcc/#/bcc/instance/list'}
];

/** 快捷入口 */
export const QUICK_ENTRANCE_LIST = [
    {label: i18n.t('配额管理'), href: '/quota_center/#/quota/network/list?serviceType=VPC', icon: OVERVIEW_QUOTA},
    {label: i18n.t('续费管理'), href: '/billing/renew/list', icon: OVERVIEW_RECHARGE},
    {label: i18n.t('查看账单'), href: '/billing/bill/hour/list', icon: OVERVIEW_TICKET}
];

/** 全局资源 */
export const GLOBAL_RESOURCE = ['csn'];

/** 列表、详情页url跳转链接配置 */
export const LIST_DETAIL_URL = {
    list: {
        vpc: '#/vpc/instance/list',
        subnet: '#/vpc/subnet/list',
        routeTable: '#/vpc/route/list',
        snic: '#/vpc/endpoint/list',
        ipCollection: '#/vpc/set/list',
        intranetNat: '#/vpc/privateNat/list',
        flowLog: '#/vpc/flowlog/list',
        csn: '/csn#/csn/instance/list',
        peerConn: '#/vpc/peerconn/list',
        crossPeerConn: '#/vpc/crossPeerconn/list',
        vpn: '#/vpc/vpn/list',
        dcgw: '#/vpc/dcgw/list',
        et: '#/dc/instance/list',
        l2gw: '#/vpc/l2gw/list',
        eip: '/eip#/eip/instance/list',
        eipGroup: '/eip#/eip/group/list',
        publicNat: '#/vpc/nat/list'
    },
    detail: {
        vpc: '/network/#/vpc/instance/detail?vpcId=',
        subnet: '/network/#/vpc/subnet/detail?subnetId=',
        routeTable: '/network/#/vpc/route/detail?',
        snic: '/network/#/vpc/endpoint/detail?',
        ipCollection: {
            ipSet: '/network/#/vpc/param/detail?',
            ipGroup: '/network/#/vpc/group/detail?'
        },
        intranetNat: '/network/#/vpc/nat/detail?',
        // flowLog: '#/vpc/flowlog/list',
        csn: '/csn#/csn/detail?',
        peerConn: '/network/#/vpc/peerconn/detail?',
        vpn: '/network/#/vpc/vpn/detail?',
        dcgw: '/network/#/vpc/dcgw/detail?',
        et: '/network/#/dc/instance/detail?',
        l2gw: '/network/#/vpc/l2gw/detail?',
        eip: '/eip/#/eip/instance/detail?', // 区分v4和v6
        eipGroup: '/eip/#/eip/group/detail?', // 区分v4和v6
        publicNat: '/network/#/vpc/nat/detail?'
    }
};

/** eip version映射 */
export const EIP_VERSION_MAP = {
    ipv4: 'eipv4',
    ipv6: 'ipv6'
};

/** 参数模板query字段 */
export const IPCOLLECTION_MAP = {
    ipSet: 'ipSetUuid',
    ipGroup: 'ipGroupUuid'
};

/** 筛选项配置 */
export const SEARCHBOX_INIT_CONFIG = {
    placeholder: i18n.t('请输入实例名称进行搜索'),
    keyword: '',
    keywordType: ['NAME'],
    keywordTypes: [
        {value: 'NAME', text: i18n.t('实例名称')},
        {value: 'ID', text: i18n.t('实例ID')}
    ]
};
