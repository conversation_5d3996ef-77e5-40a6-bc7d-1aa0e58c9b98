import React, {useMemo, useState, useEffect} from 'react';
import PageHeader from '@/components/PageHeader';
import moment from 'moment';
import {Tooltip, Link, Table, Tree, Tag, Timeline, Loading} from 'acud';
import {SearchField, useRegion} from '@baidu/bce-react-toolkit';
import {queryResourceNum, queryResourceList, queryProductNews, queryEventCenter} from '@/apis';
import {OutlinedQuestionCircle, OutlinedTreeDown} from 'acud-icon';
import u from 'lodash';
import {
    resourceOverviewList,
    METWORK_RESOURCE_LIST,
    EVENT_STATUS_MAP,
    SERVICE_RECOMMEND_LIST,
    QUICK_ENTRANCE_LIST,
    LIST_DETAIL_URL,
    EIP_VERSION_MAP,
    IPCOLLECTION_MAP,
    GLOBAL_RESOURCE,
    SEARCHBOX_INIT_CONFIG
} from './constant';
import {twMerge} from 'tailwind-merge';
import CountUp from 'react-countup';
import './index.less';
import {cbaI18nInstance as i18n,Trans} from '@baidu/bce-react-toolkit';
const AllRegion = (window as any).$context.getEnum('AllRegion');
const Overview = () => {
    window.$framework.events.fire(window.$framework.EVENTS.HIDE_REGION_SWITCHER);
    const {region, setRegion} = useRegion();
    const [baseInfo, setBaseInfo] = useState({});
    const [resBaseInfo, setResBaseInfo] = useState({});
    const [currResTag, setCurrResTag] = useState('vpc');
    const [resourceList] = useState(() => {
        return METWORK_RESOURCE_LIST.reduce((pre, cur) => {
            const {resourceList} = cur;
            return pre.concat(resourceList);
        }, []);
    });
    const [searchBox, setSearchBox] = useState<Record<string, any>>(SEARCHBOX_INIT_CONFIG);
    const [searchParams, setSearchParams] = useState({});
    const [pageLoading, setPageLoading] = useState(false);
    const [loading, setLoading] = useState(false);
    const [eventLoading, setEventLoading] = useState(false);
    const [eventData, setEventData] = useState([]);
    const [pagination, setPagination] = useState({
        pageNo: 1,
        pageSize: 10,
        total: 0
    });
    const [currResList, setCurrResList] = useState([]);
    const [productNewsList, setProductNewsList] = useState([]);
    const resSelectedClass = 'bg-[#E6F0FF] text-[#2468F2]';
    const firstLevelTitle = 'text-[16px] text-[#151B26] leading-[24px] font-medium';
    const [resTagClass] = useState(
        'text-[12px] cursor-pointer text-[#151B26] leading-[20px] font-[400] px-[16px] py-[2px] bg-[#F7F7F9] rounded-[2px] text-nowrap hover:bg-[#E6F0FF] hover:text-[#528EFF] active:text-[#144BCC]'
    );
    const [isShowSearchField, setIsShowSearchField] = useState(false);

    // 查询资源
    const queryResourceNumber = async () => {
        try {
            setPageLoading(true);
            const res: any = await queryResourceNum();
            if (res) {
                const resourceList = res?.resources ?? [];
                const resBaseInfoMap = {};
                // 资源分布情况
                u.each(resourceList, item => {
                    const {resourceType, regionOverviewMap, totalNum} = item;
                    const regionListMap = regionOverviewMap
                        ? Object.keys(regionOverviewMap)
                              .map(item => {
                                  if (regionOverviewMap?.[item]?.totalNum) {
                                      return {
                                          label: item,
                                          value: regionOverviewMap[item].totalNum
                                      };
                                  }
                                  return null;
                              })
                              .filter(Boolean)
                        : [];
                    resBaseInfoMap[resourceType] = {
                        totalNum,
                        regionListMap
                    };
                });

                // 资源总数信息
                const baseInfoMap = resourceList.reduce((pre, cur) => {
                    const {totalNum: total, expiringNum: expiring, expiredNum: expired} = pre;
                    const {totalNum, expiringNum, expiredNum} = cur;

                    return {
                        totalNum: total + totalNum,
                        expiringNum: expiring + expiringNum,
                        expiredNum: expired + expiredNum
                    };
                });
                setResBaseInfo(resBaseInfoMap);
                setBaseInfo(baseInfoMap);
            }
            if (res) {
            }
        } catch (error) {
        } finally {
            setPageLoading(false);
        }
    };

    // 查询产品动态数据
    const queryProductData = async () => {
        try {
            const res: any = await queryProductNews();
            if (res) {
                const productNews = res.find(item => item.type === 'ProductNews')?.data?.items;
                setProductNewsList(productNews);
            }
        } catch (error) {}
    };

    // 查询事件中心数据
    const queryEventCenterData = async () => {
        try {
            setEventLoading(true);
            const payload: Record<string, any> = {
                pageNo: 1,
                pageSize: 3,
                accountId: (window as any).$context.getUserId(),
                startTime: moment().subtract(30, 'days').utc().format(),
                endTime: moment().utc().format()
            };
            const res: any = await Promise.all([
                queryEventCenter({...payload, serviceName: 'BCE_EIP'}),
                queryEventCenter({...payload, serviceName: 'BCE_NAT'}),
                queryEventCenter({...payload, serviceName: 'BCE_VPN'})
            ]);
            if (res) {
                const serviceNameList = [
                    {key: 'BCE_EIP', title: i18n.t('弹性公网IP')},
                    {key: 'BCE_NAT', title: i18n.t('NAT 网关')},
                    {key: 'BCE_VPN', title: i18n.t('VPN 网关')}
                ];
                const eventData = res.map((item, index) => {
                    const {content} = item;
                    return {
                        key: serviceNameList[index].key,
                        title: serviceNameList[index].title + `（${content?.length || 0}）`,
                        content
                    };
                });
                setEventData(eventData);
            }
        } catch (error) {
        } finally {
            setEventLoading(false);
        }
    };

    useEffect(() => {
        if (!!Object.keys(resBaseInfo).length) {
            handleSearchResource({}, {}, currResTag, false, true);
        }
    }, [JSON.stringify(resBaseInfo)]);

    useEffect(() => {
        queryResourceNumber();
        queryProductData();
        queryEventCenterData();
    }, []);

    // 动态设置表格筛选条件
    const dynamicSetFilter = (value: string) => {
        let currResRegionList = (resBaseInfo?.[value]?.regionListMap ?? []).map(item => {
            const {label} = item;
            return {
                text: AllRegion.getTextFromValue(label),
                value: label
            };
        });
        if (value === 'peerConn') {
            currResRegionList = _.filter(currResRegionList, item => ![AllRegion.NJ, AllRegion.YQ].includes(item.value));
        }
        if (value === 'intranetNat') {
            currResRegionList = _.filter(currResRegionList, item =>
                [AllRegion.BJ, AllRegion.NJ, AllRegion.SU, AllRegion.FWH, AllRegion.BD, AllRegion.GZ].includes(
                    item.value
                )
            );
        }
        let keywordTypes = [
            ...SEARCHBOX_INIT_CONFIG.keywordTypes.filter(item => item.value !== 'region'),
            GLOBAL_RESOURCE.includes(value) || !currResRegionList?.length
                ? null
                : {
                      text:i18n.t ('地域'),
                      value: 'region',
                      placeholder: i18n.t('请选择地域进行搜索'),
                      selectDataSource: currResRegionList
                  }
        ].filter(Boolean);

        let searchboxCopy: any = {
            ...SEARCHBOX_INIT_CONFIG
        };
        // 对等连接没有名称的概念
        if (value === 'peerConn') {
            keywordTypes = keywordTypes.filter(item => item.value !== 'NAME');
            searchboxCopy.keywordType = ['ID'];
            searchboxCopy.keyword = '';
            searchboxCopy.placeholder = i18n.t('请输入实例ID进行搜索');
        } else if (value === 'ipCollection') {
            keywordTypes.push({
                text: i18n.t('参数模板类型'),
                value: 'ipCollection',
                placeholder: i18n.t('请选择参数模板类型'),
                selectDataSource: [
                    {text: i18n.t('IP 地址族'), value: 'ipGroup'},
                    {text: i18n.t('IP 地址组'), value: 'ipSet'}
                ]
            });
        } else {
            searchboxCopy = {
                ...SEARCHBOX_INIT_CONFIG
            };
        }

        const finalSearchBox = {
            ...searchboxCopy,
            keywordTypes
        };
        setSearchBox(finalSearchBox);
    };

    // 选择资源标签
    const handleSelectResource = (value: string) => {
        if (value !== currResTag) {
            setIsShowSearchField(false);
            // 设置当前资源所在的region
            dynamicSetFilter(value);
            setCurrResTag(value);
            handleSearchResource({}, {}, value, false);
        }
    };

    // 搜索资源
    const handleSearchResource = async (
        params,
        paginationInfo?: {pageNo?: number; pageSize?: number},
        resourceType?: string,
        resetPageNo?: boolean,
        isInit: boolean = false
    ) => {
        try {
            setSearchParams(params);
            setLoading(true);
            const {keyword, keywordType} = params;
            const {pageNo, pageSize} =
                paginationInfo && !!Object.keys(paginationInfo).length ? paginationInfo : pagination;
            const targetResourceType = resourceType || currResTag;
            const payload: Record<string, any> = {
                pageNo,
                pageSize,
                resourceType: targetResourceType,
                keyword: keyword || '',
                keywordType: keywordType || ''
            };
            if (isInit) {
                dynamicSetFilter(targetResourceType);
            }
            if (!resetPageNo) {
                payload.pageNo = 1;
            }
            if (payload.keywordType === 'region') {
                payload.region = payload.keyword;
                payload.keyword = '';
                payload.keywordType = '';
            }
            if (payload.keywordType === 'ipCollection') {
                payload.resourceType = 'ipCollection';
                payload.subResourceType = payload.keyword;
                payload.keyword = '';
                payload.keywordType = '';
            }
            const res: any = await queryResourceList(payload);
            const {pageNo: pageNoRes, pageSize: pageSizeRes, totalCount} = res;
            setIsShowSearchField(true);
            setPagination({
                pageNo: pageNoRes,
                pageSize: pageSizeRes,
                total: totalCount || 0
            });
            setCurrResList(res?.result || []);
        } catch (error) {
        } finally {
            setLoading(false);
        }
    };

    // 表格相关查询
    const handleTableChange = (pagination, filters, sorter, extra) => {
        const {action} = extra;
        if (action === 'sort') {
            return;
        }
        const {current, pageSize, total} = pagination;
        handleSearchResource(searchParams, {pageNo: current, pageSize}, currResTag, true);
    };

    // 分页配置
    const paginationConfig = useMemo(() => {
        const {pageNo, pageSize, total} = pagination;
        return total > 10
            ? {
                  current: pageNo,
                  pageSize,
                  total,
                  pageSizeOptions: [10, 20, 50, 100],
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: total => i18n.t('共 「{{total}}」 条', {total})
              }
            : null;
    }, [pagination.pageNo, pagination.pageSize, pagination.total]);

    const eventCenterMess = (item: Record<string, any>) => {
        const {eventLevel, eventAlias, timestamp} = item;
        return (
            <div className="rounded-[4px] p-[8px] hover:bg-[#EEF3FE]">
                <span className="text-[#84868C] text-[12px] leading-[20px] font-[400]">{timestamp}</span>
                <div>
                    <Tag color={EVENT_STATUS_MAP[eventLevel][1]}>
                        <span className="text-[#FFFFFF]">{EVENT_STATUS_MAP[eventLevel][0]}</span>
                    </Tag>
                    {eventAlias}
                </div>
            </div>
        );
    };

    const eventList = useMemo(() => {
        const data = eventData.map((item, idx) => {
            const {key, title, content} = item;
            return {
                key,
                title,
                children: content?.length
                    ? content.map((item, index) => {
                          return {key: idx + index, title: eventCenterMess(item)};
                      })
                    : [
                          {
                              key: idx,
                              title: (
                                <Trans>
                                  <div className="text-center text-[#84868C] text-[12px] cursor-default leading-[32px]">
                                      暂无事件
                                  </div>
                                  </Trans>
                              )
                          }
                      ]
            };
        });
        return data;
    }, [JSON.stringify(eventData)]);

    const handleJumpDetail = (row: Record<string, any>) => {
        const {extra, region: resRegion, resourceId, resourceUuid, subResourceType} = row;
        const {localIfId, localVpcId, vpcUuid, eip, vpnConns, eipType} = extra ? JSON.parse(extra) : {};
        let url: string;
        switch (currResTag) {
            case 'vpc':
                url = LIST_DETAIL_URL['detail'][currResTag] + resourceUuid;
                break;
            case 'subnet':
                url = LIST_DETAIL_URL['detail'][currResTag] + resourceUuid;
                break;
            case 'routeTable':
                url =
                    LIST_DETAIL_URL['detail'][currResTag] +
                    `vpcId=${vpcUuid}&routeTableId=${resourceId}&routeTableUuid=${resourceUuid}`;
                break;
            case 'snic':
                url = LIST_DETAIL_URL['detail'][currResTag] + `vpcId=${vpcUuid}&shortId=${resourceId}`;
                break;
            case 'ipCollection':
                url =
                    LIST_DETAIL_URL['detail'][currResTag][subResourceType] +
                    `${IPCOLLECTION_MAP[subResourceType]}=${resourceUuid}`;
                break;
            case 'intranetNat':
                url = LIST_DETAIL_URL['detail'][currResTag] + `vpcId=${vpcUuid}&id=${resourceId}&natType=private`;
                break;
            case 'csn':
                url = LIST_DETAIL_URL['detail'][currResTag] + `csnId=${resourceId}&current=detail`;
                break;
            case 'peerConn':
                url = LIST_DETAIL_URL['detail'][currResTag] + `vpcId=${localVpcId}&localIfId=${localIfId}`;
                break;
            case 'vpn':
                const showRoute =
                    ['IPSec', 'GRE'].includes(subResourceType) &&
                    JSON.parse(vpnConns).some(it => it.healthStatus === 'reachable');
                url =
                    LIST_DETAIL_URL['detail'][currResTag] +
                    `vpcId=${vpcUuid}&vpnId=${resourceId}&vpnType=${subResourceType.toLowerCase()}&showRoute=${showRoute}`;
                break;
            case 'dcgw':
                url = LIST_DETAIL_URL['detail'][currResTag] + `vpcId=${vpcUuid}&dcgwId=${resourceId}`;
                break;
            case 'et':
                url = LIST_DETAIL_URL['detail'][currResTag] + `instanceId=${resourceId}`;
                break;
            case 'l2gw':
                url = LIST_DETAIL_URL['detail'][currResTag] + `vpcId=${vpcUuid}&l2gwId=${resourceId}`;
                break;
            case 'eip':
                url =
                    LIST_DETAIL_URL['detail'][currResTag] +
                    `eip=${eip}&eipType=${eipType}&ipVersion=${EIP_VERSION_MAP[subResourceType]}`;
                break;
            case 'eipGroup':
                url =
                    LIST_DETAIL_URL['detail'][currResTag] +
                    `id=${resourceId}&current=detail&ipVersion=${EIP_VERSION_MAP[subResourceType]}`;
                break;
            case 'publicNat':
                url = LIST_DETAIL_URL['detail'][currResTag] + `vpcId=${vpcUuid}&id=${resourceId}&natType=public`;
                break;
        }
        if (region !== resRegion && resRegion !== 'global' && currResTag !== 'et') {
            setRegion(resRegion);
        }
        window.open(url);
    };

    // 资源列表
    const dynamicColumns = useMemo((): any => {
        let columns = [
            {
                key: 'region',
                title: i18n.t('地域'),
                dataIndex: 'region',
                width: 140,
                render: t => {
                    return <span>{AllRegion.getTextFromValue(t)}</span>;
                }
            },
            {
                key: 'name',
                title: i18n.t('实例名称'),
                dataIndex: 'name',
                ellipsis: true,
                width: 160,
                render: (t, r) => {
                    const {name} = r;
                    return currResTag === 'flowLog' ? (
                        <span>{name}</span>
                    ) : (
                        <Link onClick={() => handleJumpDetail(r)} target="_blank">
                            {name}
                        </Link>
                    );
                }
            },
            {
                key: 'resourceId',
                title: i18n.t('实例 ID'),
                dataIndex: 'resourceId',
                width: 160,
                render: (t, r) => {
                    const {resourceId} = r;
                    return currResTag === 'peerConn' ? (
                        <Link onClick={() => handleJumpDetail(r)} target="_blank">
                            {resourceId}
                        </Link>
                    ) : (
                        <span>{resourceId}</span>
                    );
                }
            },
            {
                key: 'createTime',
                title: i18n.t('创建时间'),
                dataIndex: 'createTime',
                width: 232,
                sorter: (a, b) => new Date(a.createTime).getTime() - new Date(b.createTime).getTime(),
                render: t => {
                    return <span>{t ? moment(t).format('YYYY-MM-DD HH:MM:ss') : '-'}</span>;
                }
            }
        ];
        if (currResTag === 'peerConn') {
            columns.splice(1, 1);
        }
        if (currResTag === 'eip') {
            columns.splice(3, 0, {
                key: 'subResourceType',
                title: i18n.t('类型'),
                dataIndex: 'subResourceType',
                width: 100,
                sorter: (a, b) => +a?.subResourceType?.slice(3) - +b?.subResourceType?.slice(3),
                render: t => {
                    return <span>{t === 'ipv4' ? 'IPv4' : 'IPv6'}</span>;
                }
            });
        }
        return columns;
    }, [currResTag]);

    // 资源跳转
    const handleJumpWay = (url: string) => {
        if (url.startsWith('#')) {
            window.location.hash = url;
        } else {
            window.open(url);
        }
    };

    // 兼容
    useEffect(() => {
        const isNeedReload = sessionStorage.getItem('isNeedReload');
        const currJumpType = sessionStorage.getItem('currJumpType');
        if (isNeedReload && currJumpType) {
            sessionStorage.removeItem('isNeedReload');
            sessionStorage.removeItem('currJumpType');
            const url = LIST_DETAIL_URL['list'][currJumpType];
            handleJumpWay(url);
        }
    }, []);

    const handleGoResList = (type: string, label: string) => {
        const url = LIST_DETAIL_URL['list'][type];
        if (label === i18n.t('全局资源')) {
            handleJumpWay(url);
        } else if (region !== label) {
            sessionStorage.setItem('isNeedReload', JSON.stringify(true));
            sessionStorage.setItem('currJumpType', type);
            setRegion(label);
            // handleJumpWay(url);
        } else {
            handleJumpWay(url);
        }
    };

    // 资源数量tooltip
    const tooltipTitle = ({label, value, type}) => {
        let labelText = label;
        if (type === 'et' || GLOBAL_RESOURCE.includes(type)) {
            labelText = label;
        } else {
            labelText = AllRegion.getTextFromValue(label);
        }
        // 私网NAT和对等连接过滤掉不支持的region
        [AllRegion.BJ, AllRegion.NJ, AllRegion.SU, AllRegion.FWH, AllRegion.BD, AllRegion.GZ];
        return (
            <div key={label} className="leading-[20px]">
                <span className="mr-[8px] text-[12px] font-[400]">{labelText}</span>
                <span onClick={() => handleGoResList(type, label)} className="text-[#2468F2] cursor-pointer">
                    {value}
                </span>
            </div>
        );
    };

    /** 资源在各region分布情况 */
    const handleRenderResRegionNum = (type: string) => {
        if (!Object.keys(resBaseInfo).length) return;
        const {regionListMap, totalNum} = resBaseInfo[type];
        return (
            <div>
                {totalNum === 0 ? (
                    <Trans>
                    <span>暂无资源</span>
                    </Trans>
                ) : type !== 'et' && regionListMap.length ? (
                    regionListMap
                        .map(item => {
                            const {label, value} = item;
                            const isNotSupIntranetNat =
                                type === 'intranetNat' &&
                                ![
                                    AllRegion.BJ,
                                    AllRegion.NJ,
                                    AllRegion.SU,
                                    AllRegion.FWH,
                                    AllRegion.BD,
                                    AllRegion.GZ
                                ].includes(label);
                            const isNotSupPeerconn =
                                type === 'peerConn' && [AllRegion.NJ, AllRegion.YQ].includes(label);
                            return isNotSupIntranetNat || isNotSupPeerconn ? null : tooltipTitle({label, value, type});
                        })
                        .filter(Boolean)
                ) : (
                    tooltipTitle({label: i18n.t('全局资源'), value: totalNum, type})
                )}
            </div>
        );
    };

    /** 快捷入口跳转 */
    const handleEntrance = (href: string) => {
        window.open(href);
    };

    // 服务推荐
    const handleServiceRecom = href => {
        window.open(href);
    };
    return (
        <div className="w-full overflow-auto overview-widget">
            <PageHeader showVpcSelect={false} title={i18n.t('概览')} />
            <div className="m-[16px] flex">
                {/* /** 左侧布局 */}
                <div className="flex-[7] mr-[16px] min-w-0">
                    <Loading size="small" loading={pageLoading} tip={i18n.t('资源加载中')}>
                        <div className="bg-[#FFFFFF] p-[24px] rounded-[6px]">
                            <h4 className={firstLevelTitle}>{i18n.t('资源概览')}</h4>
                            <div className="mt-[24px] flex">
                                {resourceOverviewList.map(item => {
                                    const {value, icon, hoverTip, href, title} = item;
                                    return (
                                        <div key={value} className="flex flex-[1]">
                                            <div className="w-[20px] mr-[8px] text-right">{icon}</div>
                                            <div className="flex-[1]">
                                                <div
                                                    className={twMerge(
                                                        'text-[14px] text-[#151B26] leading-[20px] font-medium flex items-center',
                                                        value !== 'totalNum'
                                                            ? 'text-[12px] text-[#5C5F66] font-[400]'
                                                            : ''
                                                    )}
                                                >
                                                    {title}
                                                    <Tooltip title={hoverTip}>
                                                        <OutlinedQuestionCircle className="ml-[4px] text-[14px] text-[#222839] cursor-pointer hover:text-[#2468F2]" />
                                                    </Tooltip>
                                                </div>
                                                <div
                                                    className={twMerge(
                                                        'mt-[16px] text-[32px] text-[#151B26] leading-[34px] font-semibold',
                                                        value !== 'totalNum' ? 'text-[26px] leading-[34px]' : ''
                                                    )}
                                                >
                                                    <CountUp duration={1.5} end={baseInfo[value]} />
                                                    {href && (
                                                        <Link
                                                            target="_blank"
                                                            className="ml-[6px] cursor-pointer align-middle"
                                                            href={href}
                                                        >
                                                            {i18n.t('续费')}
                                                        </Link>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                            {/* 网络资源 */}
                            {METWORK_RESOURCE_LIST.map(item => {
                                const {key, title, resourceList} = item;
                                return (
                                    <div className="mt-[24px]" key={key}>
                                        <h4 className="text-[12px] text-[#151B26] font-medium leading-[20px]">
                                            {title}
                                        </h4>
                                        <div className="mt-[12px] overflow-hidden">
                                            <div className="grid md:grid-cols-3 1280x:grid-cols-3 1440x:grid-cols-4 1680x:grid-cols-5 1920x:grid-cols-5 gap-x-[16px] gap-y-[12px]">
                                                {resourceList.map(item => {
                                                    const {icon, label, value} = item;
                                                    return (
                                                        <div key={value}>
                                                            <div className="bg-[#F7F7F9] rounded-[4px] px-[16px] py-[12px] flex-between">
                                                                <div className="mr-[8px] text-nowrap max-w-[calc(100%-48px)]">
                                                                    <span className="bg-[#fff] w-[24px] h-[24px] flex-center rounded-[4px]">
                                                                        <img className="bg-[#fff]" src={icon} alt="" />
                                                                    </span>
                                                                    <span className="ml-[8px] text-[#151B26] max-w-[calc(100%-16px)] leading-[24px] h-[24px] align-top text-nowrap text-ellipsis">
                                                                        {label}
                                                                    </span>
                                                                </div>
                                                                <Tooltip
                                                                    getPopupContainer={() =>
                                                                        document.getElementById(`${value}-resource-num`)
                                                                    }
                                                                    mouseEnterDelay={0}
                                                                    title={handleRenderResRegionNum(value)}
                                                                >
                                                                    <span
                                                                        id={`${value}-resource-num`}
                                                                        onClick={() => handleSelectResource(value)}
                                                                        className="cursor-pointer w-[24px] h-[24px] text-center leading-[24px] inline-block text-[#151B26] hover:text-[#2468F2] font-[500]"
                                                                    >
                                                                        {resBaseInfo?.[value]?.totalNum}
                                                                    </span>
                                                                </Tooltip>
                                                            </div>
                                                        </div>
                                                    );
                                                })}
                                            </div>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                        {/* 资源列表 */}
                        <div className="mt-[16px] bg-[#FFFFFF] rounded-[6px] p-[24px]">
                            <h4 className={firstLevelTitle}>{i18n.t('资源列表')}</h4>
                            <div className="mt-[16px] flex flex-wrap gap-[8px]">
                                {resourceList.map(item => {
                                    const {label, value} = item;
                                    return (
                                        <span
                                            className={twMerge(
                                                resTagClass,
                                                value === currResTag ? resSelectedClass : ''
                                            )}
                                            key={value}
                                            onClick={() => handleSelectResource(value)}
                                        >
                                            {label}
                                        </span>
                                    );
                                })}
                            </div>
                            <div className="mt-[12px]">
                                {isShowSearchField && (
                                    <SearchField allowClear searchbox={searchBox} onSearch={handleSearchResource} />
                                )}
                                <Table
                                    pagination={paginationConfig}
                                    rowKey={(r, i) => `${r.resourceId + i}`}
                                    scroll={{x: '', y: 400}}
                                    loading={{loading, size: 'small'}}
                                    className="mt-[12px]"
                                    columns={dynamicColumns}
                                    dataSource={currResList}
                                    onChange={handleTableChange}
                                />
                            </div>
                        </div>
                    </Loading>
                </div>
                {/* /** 右侧布局 */}
                <div className="flex-[3]">
                    {/* 事件中心 */}
                    <div className="rounded-[4px] p-[24px] bg-[#FFFFFF] overview-event-center">
                        <Loading size="middle" loading={eventLoading}>
                            <div className="flex-between">
                                <div className="flex items-center">
                                    <h4 className={firstLevelTitle}>{i18n.t('事件中心')}</h4>
                                    <span className="text-[12px] text-[#84868C] font-[400] ml-[8px]">
                                        {i18n.t('仅展示最新3条')}
                                    </span>
                                </div>
                                <Link href="/bcm/#/bcm/event/list" target="_blank" className="float-right">
                                    {i18n.t('查看更多')}
                                </Link>
                            </div>
                            <div className="mt-[16px]">
                                <Tree
                                    selectable={false}
                                    className="over-event-widget"
                                    switcherIcon={<OutlinedTreeDown className="text-[#000000!important]" />}
                                    treeData={eventList}
                                />
                            </div>
                        </Loading>
                    </div>
                    {/* 服务推荐 */}
                    <div className={`rounded-[4px] p-[24px] mt-[16px] bg-[#FFFFFF] overview-service-recom`}>
                        <h4 className={firstLevelTitle}>{i18n.t('服务推荐')}</h4>
                        <div className="mt-[16px] grid grid-cols-2 1680x:grid-cols-3 1920x:grid-cols-3 gap-[12px]">
                            {SERVICE_RECOMMEND_LIST.map(item => {
                                const {href, label} = item;
                                return (
                                    <div
                                        className="bg-[#F7F7F9] rounded-[4px] hover:text-[#2468F2] hover:bg-[#EEF3FE] active:text-[#144BCC] cursor-pointer"
                                        key={href}
                                        onClick={() => handleServiceRecom(href)}
                                    >
                                        <div className="w-full px-[16px] py-[8px] text-ellipsis">{label}</div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                    {/* 快捷入口 */}
                    <div className="rounded-[4px] p-[24px] mt-[16px] bg-[#FFFFFF]">
                        <h4 className={firstLevelTitle}>{i18n.t('快捷入口')}</h4>
                        <div className="mt-[16px] grid grid-cols-2 1680x:grid-cols-3 1920x:grid-cols-3 gap-[16px]">
                            {QUICK_ENTRANCE_LIST.map(item => {
                                const {icon, href, label} = item;
                                return (
                                    <div key={href} className="cursor-pointer" onClick={() => handleEntrance(href)}>
                                        <div className="flex items-center cursor-pointer group w-full">
                                            <span className="w-[48px] h-[48px] rounded-[8px] bg-[#F7F7F9] flex-center group-hover:bg-[#EEF3FE]">
                                                <img src={icon} alt="" />
                                            </span>
                                            <span className="ml-[8px] group-hover:text-[#2468F2]">{label}</span>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                    {/* 产品动态 */}
                    <div className="rounded-[4px] p-[24px] mt-[16px] bg-[#FFFFFF]">
                        <div className="flex-between">
                            <h4 className={firstLevelTitle}>{i18n.t('产品动态')}</h4>
                            <Link href="https://cloud.baidu.com/doc/VPC/index.html" target="_blank">
                                {i18n.t('查看更多')}
                            </Link>
                        </div>
                        <div className="mt-[16px] max-h-[500px] overflow-auto">
                            <Timeline mode="vertical" className="overview-timeline-widget">
                                {productNewsList.map((item, index) => {
                                    const {date, title, url} = item;
                                    return (
                                        <Timeline.Item key={index} label={date}>
                                            <a
                                                href={url}
                                                target="_blank"
                                                className="text-[#151B26!important] hover:text-[#2468F2!important]"
                                            >
                                                {title}
                                            </a>
                                        </Timeline.Item>
                                    );
                                })}
                            </Timeline>
                        </div>
                    </div>
                </div>
            </div>
            {/* <DialogBox visible={false} title="温馨提示" content="region切换中...请稍候" footer={<></>} /> */}
        </div>
    );
};
export default Overview;
