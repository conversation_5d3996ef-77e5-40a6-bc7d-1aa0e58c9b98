import React, {useMemo, useState, useEffect} from 'react';
import {useRequest} from 'ahooks';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import {channelDetail} from '@/apis';
import {InstanceStatus} from '@/pages/sanPages/common';
import {useGlobalContext} from '@/context';
import ChannelDetailInstance from '@/pages/sanPages/pages/dc/channel/detail/components/instance/instance';
import ChannelRoute from '@/pages/sanPages/pages/dc/channel/detail/components/routeManage/list';
import ChannelHealthCheck from '@/pages/sanPages/pages/dc/channel/detail/components/reliableCheck/check';
import DcChannelDetailMonitor from '@/pages/sanPages/pages/dc/channel/detail/components/monitor/monitor';
import {cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
import DetailPage from '@/components/DetailPage';

import './index.less';

const L2gwDetail = () => {
    const params: any = getQueryParams();
    const [{globalState}] = useGlobalContext() as any;
    let requestPayload = params.creator === 'other' ? {type: 'available'} : {};
    const [inDdcWhite, setInDdcWhite] = useState<boolean>(true);
    const {data = {} as any, loading, error, run} = useRequest(() => channelDetail(params.channelId, requestPayload));
    const handleUpdate = () => {
        run();
    };
    const panesData = [
        {
            key: '/dc/channel/detail',
            tab: i18n.t('实例信息'),
            content: (
                <ChannelDetailInstance
                    context={{
                        ...params,
                        refresh: handleUpdate,
                        instanceId: params.instanceId,
                        instance: data,
                        creator: params.creator
                    }}
                />
            )
        },
        {
            key: '/dc/channel/routelist',
            tab: i18n.t('路由管理'),
            content: (
                <ChannelRoute
                    context={{...params, instanceId: params.instanceId, instance: data, channelId: params.channelId}}
                />
            )
        },
        {
            key: '/dc/channel/check',
            tab: i18n.t('可靠性检测'),
            content: (
                <ChannelHealthCheck
                    context={{
                        ...params,
                        instanceId: params.instanceId,
                        instance: data,
                        creator: params.creator,
                        route: params,
                        region: data.region
                    }}
                />
            )
        },
        {
            key: '/dc/channel/monitor',
            tab: i18n.t('监控'),
            content: <DcChannelDetailMonitor context={{...params, route: params, dcInstance: data}} />
        }
    ];

    const checkWhiteRegion = () => {
        const whiteList = globalState.commonWhite;
        setInDdcWhite(whiteList?.DcPhyChannelBfd);
    };
    useEffect(() => {
        checkWhiteRegion();
    }, []);

    const [text, styleClass] = useMemo(() => {
        let config = [];
        if (data?.status) {
            let statusObj = InstanceStatus.fromValue(data.status);
            const {text} = InstanceStatus.fromValue(data.status);
            const styleClass = statusObj.kclass;
            config = [text, styleClass];
        }
        return config;
    }, [data]);

    const idcParseData = useMemo(() => {
        const newPanesData = [...panesData];
        if (!inDdcWhite || !data.manager) {
            newPanesData.splice(2, 1);
        }
        if (!data.manager) {
            newPanesData.splice(1, 1);
        }
        const monitorIndex = newPanesData.findIndex(item => item.key === '/dc/channel/monitor');
        if (!data.dcgwId) {
            newPanesData.splice(monitorIndex, 1);
        }
        return newPanesData;
    }, [data]);

    return (
        <>
            <DetailPage
                mode="vertical"
                headerName={data.id}
                statusText={text}
                statusClassName={styleClass}
                backUrl={'#/dc/channel/list'}
                panesData={idcParseData}
                tabClassName="channel-tab-class"
            />
        </>
    );
};

export default L2gwDetail;
