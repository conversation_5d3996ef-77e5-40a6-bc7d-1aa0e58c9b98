import React, {useMemo} from 'react';
import {useRequest} from 'ahooks';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import {getSnicDetail} from '@/apis';
import {EndpointStatus} from '@/pages/sanPages/common/enum';
import DetailPage from '@/components/DetailPage';
import Detail from '@/pages/sanPages/pages/endpoint/page/detail/detail';
import Security from '@/pages/sanPages/pages/eni/page/security/security';
import Monitor from '@/pages/sanPages/pages/endpoint/page/detail/monitor/monitor';
import {cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
import './index.less';

const SecurityDetail = () => {
    const params: any = getQueryParams();
    const {data = {} as any, loading, error, run} = useRequest(() => getSnicDetail({endpointId: params.shortId}));
    const handleUpdate = () => {
        run();
    };
    const panesData = [
        {
            key: '/vpc/endpoint/detail',
            tab: i18n.t('实例信息'),
            content: <Detail context={{...params, instance: data, updateName: handleUpdate}} />
        },
        {key: '/vpc/endpoint/security', tab: i18n.t('安全组'), content: <Security context={params} />},
        {key: '/vpc/endpoint/monitor', tab: i18n.t('监控'), content: <Monitor context={params} />}
    ];

    const whitedPanesData = useMemo(() => {
        const newPanesData = [...panesData];
        return newPanesData;
    }, [data]);
    const [text, styleClass] = useMemo(() => {
        let config = [];
        if (data?.status) {
            const {text, styleClass} = EndpointStatus.fromValue(data.status);
            config = [text, styleClass];
        }
        return config;
    }, [data]);
    const backList = () => {
        window.location.href = '/network/#/vpc/endpoint/list';
    };
    return (
        <>
            <DetailPage
                mode="vertical"
                headerName={data.name}
                backUrl="#/vpc/endpoint/list"
                statusText={text}
                statusClassName={styleClass}
                panesData={whitedPanesData}
                onBackList={backList}
                tabClassName="snic-tab-class"
            />
        </>
    );
};
export default SecurityDetail;
