import React, {useEffect} from 'react';
import Tabs from '@/components/TabPage';
import u from 'lodash';
import MirrorSession from '@/pages/sanPages/pages/flowmirror/mirrowSession/list/List';
import MirrorFilter from '@/pages/sanPages/pages/flowmirror/filterRulegroup/List';
import FlowMirror from '@/pages/sanPages/components/flow-mirror';
import {cbaI18nInstance as i18n,Trans} from '@baidu/bce-react-toolkit';
import './index.less';
const AllRegion = window.$context.getEnum('AllRegion');

const List = props => {
    return (
        <div className="flow-mirror-widget">
            <div className="param-header">
                <Trans>
                <h2 className="title">流量镜像</h2>
                </Trans>
            </div>
            <Tabs
                isSwitchReRender={false}
                panesData={[
                    {
                        key: '/vpc/mirror/list',
                        tab: i18n.t('镜像会话'),
                        content: <FlowMirror />
                    },
                    {
                        key: '/vpc/filterRuleGroup/list',
                        tab: i18n.t('筛选条件'),
                        content: <FlowMirror />
                    }
                ]}
            />
        </div>
    );
};
export default List;
