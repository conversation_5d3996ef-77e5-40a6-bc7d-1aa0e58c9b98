import React, {useState, useEffect, useMemo} from 'react';
import {useRequest} from 'ahooks';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import {getFilterRuleDetail} from '@/apis';
import DetailPage from '@/components/DetailPage';
import RuleFilterDetail from '@/pages/sanPages/pages/flowmirror/filterRulegroup/detail/detail';
import BindMirrorInstance from '@/pages/sanPages/pages/flowmirror/filterRulegroup/detail/instanceList/list';
import {cbaI18nInstance as i18n,} from '@baidu/bce-react-toolkit';
import './index.less';

const Detail = () => {
    const params: any = getQueryParams();
    const {data = {} as any, loading, error, run} = useRequest(() => getFilterRuleDetail({groupId: params.id}));

    const handleUpdate = () => {
        run();
    };
    const panesData = [
        {
            key: '/vpc/filterRuleGroup/detail',
            tab: i18n.t('筛选条件详情'),
            content: <RuleFilterDetail context={{...params, ruleGroupId: params.id, updateName: handleUpdate}} />
        },
        {
            key: '/vpc/filterRuleGroup/detail/mirror',
            tab: i18n.t('关联镜像会话'),
            content: <BindMirrorInstance context={{...params, instance: data}} />
        }
    ];

    const backList = () => {
        window.location.hash = '#/vpc/filterRuleGroup/list';
    };

    const newPanesData = useMemo(() => {
        const panesDataCopy = [...panesData];
        return panesDataCopy;
    }, [data]);

    return (
        <>
            <DetailPage
                mode="vertical"
                headerName={data?.name}
                backUrl="#/vpc/filterRuleGroup/list"
                panesData={newPanesData}
                onBackList={backList}
            />
        </>
    );
};
export default Detail;
