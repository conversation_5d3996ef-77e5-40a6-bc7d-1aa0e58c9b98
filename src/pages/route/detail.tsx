import React, {useState, useEffect, useMemo} from 'react';
import {useRequest} from 'ahooks';
import {includes} from 'lodash';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import {RouteTableStatus} from '@/pages/sanPages/common/enum';
import {getRouteDetail} from '@/apis';
import DetailPage from '@/components/DetailPage';
import Detail from '@/pages/sanPages/pages/route/page/detail/detail';
import BindTgw from '@/pages/sanPages/pages/route/page/detail/bindTgwList';
import {cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
import './index.less';
import {useStatus} from '@/hooks';

const RouteDetail = () => {
    const params: any = getQueryParams();
    const {
        data = {} as any,
        loading,
        error,
        run
    } = useRequest(() =>
        getRouteDetail({pageNo: 1, pageSize: 10, vpcId: params.vpcId, routeTableUuid: params.routeTableUuid})
    );
    const handleUpdate = () => {
        run();
    };
    const panesData = [
        {
            key: '/vpc/route/detail',
            tab: i18n.t('路由表详情'),
            content: <Detail context={{...params, instance: data, updateName: handleUpdate}} />
        },
        {key: '/vpc/route/bindTgw', tab: i18n.t('绑定TGW'), content: <BindTgw context={{...params, detail: data}} />}
    ];

    const whitedPanesData = useMemo(() => {
        let newPanesData = [...panesData];
        return newPanesData;
    }, [data]);
    const backList = () => {
        window.location.href = '/network/#/vpc/route/list';
    };
    const [text, styleClass] = useStatus({statusEnum: RouteTableStatus, data});
    return (
        <>
            <DetailPage
                mode="vertical"
                headerName={data.name}
                backUrl="#/vpc/route/list"
                statusText={text}
                statusClassName={styleClass}
                panesData={whitedPanesData}
                onBackList={backList}
                tabClassName="snic-tab-class"
            />
        </>
    );
};
export default RouteDetail;
