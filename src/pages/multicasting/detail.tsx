import React, {useMemo} from 'react';
import {multicastingDetail} from '@/apis';
import {useRequest} from 'ahooks';
import {DcGatewayStatus} from '@/pages/sanPages/common';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import DetailInstance from '@/pages/sanPages/pages/multicasting/detail/detail';
import IpManage from '@/pages/sanPages/pages/multicasting/detail/ip/list';
import {cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
import DetailPage from '@/components/DetailPage';

import './index.less';

const MulticastingDetail = () => {
    const params: any = getQueryParams();
    const {
        data = {} as any,
        loading,
        error,
        run
    } = useRequest(() => multicastingDetail({vpcUuid: params.vpcId, multicastGroupLongIds: params.id}));
    const handleUpdate = () => {
        run();
    };
    const panesData = [
        {
            key: '/vpc/multicasting/detail',
            tab: i18n.t('实例信息'),
            content: <DetailInstance context={{...params, getDetail: handleUpdate, route: params}} />
        },
        {key: '/vpc/multicasting/ip', tab: i18n.t('管理组播组'), content: <IpManage context={{...params, route: params}} />}
    ];

    const [name = '-'] = useMemo(() => {
        let config = [];
        const instance = data.result ? data.result[0] : {name: '-'};
        config.push(instance.name);
        return config;
    }, [data]);
    return (
        <>
            <DetailPage
                mode="vertical"
                headerName={name}
                backUrl={'#/vpc/multicasting/list'}
                panesData={panesData}
                tabClassName="multicasting-tab-class"
            />
        </>
    );
};

export default MulticastingDetail;
