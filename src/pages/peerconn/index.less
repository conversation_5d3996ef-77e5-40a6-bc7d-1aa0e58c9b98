.peer-instance-wrap,
.vpc-peer-monitor-box {
    padding: 24px;
}

.peerconn-tab-class {
    .tab-widget {
        height: 100%;
        .tab-vertical-widget {
            height: 100%;
        }
    }
}

.peerconn-list-widget {
    overflow: auto;
    .tab-widget {
        padding-top: 54px;
        background-color: #ffffff;
        .acud-tabs {
            overflow: visible;
        }
    }
    .react-main {
        overflow: visible;
    }
}
