import React, {useState, useEffect} from 'react';
import {useRequest} from 'ahooks';
import {parseQuery} from '@/utils';
import {getPeerDetail} from '@/apis';
import PeerConnInstance from '@/pages/sanPages/pages/peerconn/detail/components/instance/instance';
import PeerConnMointor from '@/pages/sanPages/pages/peerconn/detail/components/monitorChart/monitor';
import DetailPage from '@/components/DetailPage';
import {cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
import './index.less';

const PeerconnDetail = () => {
    const params: any = parseQuery(location.hash);
    const {data = {} as any, loading, error, run} = useRequest(() => getPeerDetail({localIfId: params.localIfId}));
    const handleUpdate = () => {
        run();
    };
    const panesData = [
        {
            key: '/vpc/peerconn/detail',
            tab: i18n.t('实例详情'),
            content: (
                <PeerConnInstance
                    context={{
                        vpcId: params.vpcId,
                        localIfId: params.localIfId,
                        instance: data,
                        updateName: handleUpdate
                    }}
                />
            )
        },
        {
            key: '/vpc/peerconn/monitor',
            tab: i18n.t('监控'),
            content: <PeerConnMointor context={{instanceMonitorShow: true, localIfId: params.localIfId}} />
        }
    ];
    return (
        <>
            <DetailPage
                mode="vertical"
                headerName={data.localIfName}
                backUrl="#/vpc/peerconn/list"
                panesData={panesData}
                tabClassName="peerconn-tab-class"
            />
        </>
    );
};
export default PeerconnDetail;
