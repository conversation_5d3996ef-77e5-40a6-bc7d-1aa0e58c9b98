import React, {useMemo, useState} from 'react';
import u from 'lodash';
import SecurityTabs from '@/components/TabPage';
import PeerconnListComp from '@/pages/sanPages/pages/peerconn/list/list';
import {cbaI18nInstance as i18n} from '@baidu/bce-react-toolkit';
import './index.less';
import {useGlobalContext} from '@/context';

const AllRegion = window.$context.getEnum('AllRegion');

const PeerconnList = () => {
    // const [] = useState<>();
    const [{globalState}] = useGlobalContext() as any;
    const whiteMap = globalState.commonWhite;

    const panesData = [
        {
            key: '/vpc/peerconn/list',
            tab: i18n.t('非跨境对等连接'),
            content: <PeerconnListComp context={{peerconnType: 'no_cross_border'}} />
        },
        {
            key: '/vpc/crossPeerconn/list',
            tab: i18n.t('跨境对等连接'),
            content: <PeerconnListComp context={{peerconnType: 'cross_border'}} />
        }
    ];

    const whitedPanesData = useMemo(() => {
        let newPanesData = [...panesData];
        if (!whiteMap?.CrossBorderWhiteList) {
            newPanesData = newPanesData.filter(item => item.key !== '/vpc/crossPeerconn/list');
        }
        return newPanesData;
    }, [whiteMap]);

    return (
        <div className="peerconn-list-widget">
            <SecurityTabs isSwitchReRender={false} panesData={whitedPanesData} />
        </div>
    );
};
export default PeerconnList;
