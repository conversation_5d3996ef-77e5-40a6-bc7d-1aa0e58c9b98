import React, {useState, useEffect, useMemo} from 'react';
import {includes} from 'lodash';
import {getQueryParams, useTranslation, getI18n} from '@baidu/bce-react-toolkit';

import {getSubnetResourceDetail} from '@/apis';
import {useGlobalContext} from '@/context';
import Detail from '@/pages/sanPages/pages/subnet/page/detail';
import IpManagement from '@/pages/sanPages/pages/subnet/page/ip';
import ReverseCidr from '@/pages/sanPages/pages/subnet/page/reserve';
import ReversePortPools from '@/pages/sanPages/pages/subnet/page/reserveportpools';
import DetailPage from '@/components/DetailPage';

import './index.less';

const SecurityDetail = () => {
    const params: any = getQueryParams();
    const [{globalState}] = useGlobalContext() as any;
    const [inPoolsWhite, setInPoolsWhite] = useState<boolean>(false);
    const [detail, setDetail] = useState<Record<string, any>>({});
    const {t} = useTranslation();

    const checkWhiteRegion = () => {
        if (includes(window.location.href, 'vpc/subnet/reserveportpools')) {
            setInPoolsWhite(true);
        }
        const whiteList = globalState.commonWhite;
        setInPoolsWhite(whiteList?.ReservePortPoolWhiteList);
    };

    const getDetail = () => {
        return getSubnetResourceDetail({subnetId: params.subnetId})
            .then(res => {
                if (res) {
                    setDetail(res);
                }
                checkWhiteRegion();
                return true;
            })
            .catch(e => {
                return false;
            });
    };

    useEffect(() => {
        getDetail();
        checkWhiteRegion();
    }, []);

    const handleUpdate = () => {
        return getDetail();
    };

    const panesData = [
        {
            key: '/vpc/subnet/detail',
            tab: t('子网详情'),
            content: <Detail context={{...params, instance: detail, updateName: handleUpdate}} />
        },
        {key: '/vpc/subnet/ip', tab: t('IP地址管理'), content: <IpManagement context={params} />},
        {key: '/vpc/subnet/reserve', tab: t('预留网段'), content: <ReverseCidr context={params} />},
        {key: '/vpc/subnet/reserveportpools', tab: t('嵌套地址池'), content: <ReversePortPools context={params} />}
    ];

    const whitedPanesData = useMemo(() => {
        let newPanesData = [...panesData];
        if (!inPoolsWhite) {
            newPanesData = newPanesData.filter(item => item.key !== '/vpc/subnet/reserveportpools');
        }
        return newPanesData;
    }, [inPoolsWhite, detail]);
    return (
        <>
            <DetailPage
                mode="vertical"
                headerName={detail.name}
                backUrl="#/vpc/subnet/list"
                panesData={whitedPanesData}
            />
        </>
    );
};
export default SecurityDetail;
