import React, {useMemo} from 'react';
import SecurityTabs from '@/components/TabPage';
import GatewayList from '@/pages/sanPages/pages/gateway/components/gatewayList';
import {useGlobalContext} from '@/context';
import './index.less';
import {cbaI18nInstance as i18n,Trans} from '@baidu/bce-react-toolkit';
const AllRegion = window.$context.getEnum('AllRegion');

const List = props => {
    const [{globalState}] = useGlobalContext() as any;
    const panesData = [
        {
            key: '/vpc/gateway/pc/list',
            tab: i18n.t('对等连接'),
            content: <GatewayList context={{serviceType: 'peerconn'}} />
        },
        {
            key: '/vpc/gateway/et/list',
            tab: i18n.t('专线网关'),
            content: <GatewayList context={{serviceType: 'et'}} />
        },
        {
            key: '/vpc/gateway/csn/list',
            tab: i18n.t('云智能网'),
            content: <GatewayList context={{serviceType: 'csn'}} />
        }
    ];
    const whitedPanesData = useMemo(() => {
        let newPanesData = [...panesData];
        const {GlrPcWhiteList, GlrEtWhiteList, GlrCsnWhiteList} = globalState.commonWhite;
        if (!GlrPcWhiteList) {
            newPanesData = newPanesData.filter(item => item.key !== '/vpc/gateway/pc/list');
        }
        if (!GlrEtWhiteList) {
            newPanesData = newPanesData.filter(item => item.key !== '/vpc/gateway/et/list');
        }
        if (!GlrCsnWhiteList) {
            newPanesData = newPanesData.filter(item => item.key !== '/vpc/gateway/csn/list');
        }
        return newPanesData;
    }, [globalState.commonWhite]);
    return (
        <div className="gateway-limit-widget">
            <div className="param-header">
                <Trans>
                <h2 className="title">网关限速</h2>
                </Trans>
            </div>
            <SecurityTabs isSwitchReRender={false} panesData={whitedPanesData} />
        </div>
    );
};
export default List;
