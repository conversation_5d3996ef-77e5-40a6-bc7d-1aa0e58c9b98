/** tailwind 复杂且通用样式定义 */
export const TAILWIND_CONSTANT = {
    // flex 垂直居中 水平分居两侧
    '.flex-between': {
        'display': 'flex',
        'justify-content': 'space-between',
        'align-items': 'center'
    },
    '.flex-center': {
        'display': 'inline-flex',
        'justify-content': 'center',
        'align-items': 'center'
    },
    // 文本超出显示...
    '.text-ellipsis': {
        'display': 'inline-block',
        'max-width': '100%',
        'overflow': 'hidden',
        'white-space': 'nowrap',
        'text-overflow': 'ellipsis',
        'vertical-align': 'middle'
    },
    // 文本超过两行显示...
    '.text-2-ellipsis': {
        'display': '-webkit-box',
        '-webkit-box-orient': 'vertical',
        '-webkit-line-clamp': '2',
        'overflow': 'hidden',
        'text-overflow': 'ellipsis'
    },
    // 阴影
    '.dom-shadow': {
        boxShadow: '0 2px 8px 0 rgba(7,12,20,.12)'
    }
};
