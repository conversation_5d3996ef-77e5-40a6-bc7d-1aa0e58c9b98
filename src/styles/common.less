@tailwind base;
@tailwind components;
@tailwind utilities;

// @layer components {
//     .testmo {
//         color: #fff;
//     }
// }
.main-area {
    min-width: 1280px !important;
    .vpc-main-wrap {
        width: 100%;
        height: 100%;
        overflow-y: scroll;
        .s-detail-page-content {
            margin: 0;
            padding: 24px;
        }
    }
    .react-main {
        flex-shrink: 1;
        flex-grow: 1;
        flex-basis: 0;
        height: 100%;
        overflow: auto;
        a,
        a:visited,
        a:hover,
        a:active {
            color: #2468f2;
        }
        .s-icon-button-able:hover {
            fill: #2468f2 !important;
        }
        .s-empty-vertical .s-empty-desc p {
            margin-right: 0;
        }
        .s-list-page {
            padding: 0;
            .table-full-wrap {
                .operation-wrap {
                    font-size: 0;
                    height: auto;
                }
            }
            h2 {
                border: none;
                height: 47px;
                line-height: 47px;
            }
            .s-input.state-disabled {
                border-color: #e8e9eb;
            }
        }
        .s-app-order-page {
            height: 100%;
            width: 100vw;
            margin: 0;
            .s-app-order-page-relation-item-desc {
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                width: 150px;
            }
            .s-app-order-page-relation-item-icon {
                background: none;
            }
            .s-app-order-page-info {
                border-bottom: none;
            }
        }
        .s-tab-list-page {
            .s-list-content {
                h2 {
                    border: none;
                    padding-top: 12px;
                    padding-bottom: 4px;
                    height: 40px;
                    line-height: 24px;
                    color: #151b26;
                }
            }
            .s-list-content .table-full-wrap {
                border-radius: 0;
                margin: 0;
                padding: 0;
            }
            .s-tab-page-panel .table-full-wrap {
                margin: 16px;
                padding: 24px;
                border-radius: 6px;
            }
            .s-tab-page {
                .s-tabnav {
                    margin: 0;
                    padding-left: 16px;
                    background-color: #fff;
                }
                .s-tabnav-scroll {
                    border: none;
                    height: 40px;
                }
                .s-tabnav-nav {
                    border: none;
                    .s-tabnav-nav-selected:after {
                        bottom: 1px;
                    }
                }
            }
        }
        .s-detail-page {
            .s-detail-page-title {
                border-bottom: none;
                .page-header {
                    .status {
                        top: 1px;
                    }
                }
            }
            .page-title-nav {
                position: unset;
                color: #83868c;
                &:hover {
                    color: #2468f2;
                }
                &:active {
                    color: #1c53c1;
                }
            }
        }
        .s-create-page {
            .page-title-nav {
                position: unset;
                color: #83868c;
                &:hover {
                    color: #2468f2;
                }
                &:active {
                    color: #1c53c1;
                }
            }
            .s-create-page-title {
                border-bottom: none;
                .page-title-text {
                    font-weight: 500;
                }
            }
            .s-create-page-content {
                h4 {
                    font-weight: 500;
                }
                .s-form-item {
                    .s-row {
                        align-items: initial;
                    }
                }
            }
            .s-form-item {
                margin: 24px 0 24px 0;
            }
            .s-form-item-help {
                padding-bottom: 0;
                padding-top: 8px;
            }
            .s-form-item-error {
                padding-bottom: 0;
                padding-top: 8px;
            }
            .s-slider-mark-wrapper {
                margin-top: 6px;
            }
            .network-bandwidth-group {
                margin-bottom: 16px;
            }
            .resouce-group-select-main .s-trigger-container {
                .s-button {
                    display: inline-block;
                }
            }
        }
        .s-tab-page {
            background-color: #f7f7f7;
            padding: 0;
            .bui-tab-content {
                padding: 0;
            }
        }
        .resource-group-search {
            margin-left: 8px;
            height: 32px;
            .s-cascader {
                height: 30px;
                line-height: 30px;
                .s-cascader-value {
                    min-width: 92px;
                    min-height: 30px;
                    padding: 0 0 0 12px;
                    color: #151b26;
                }
                .s-cascader-value-arrow {
                    right: 9px;
                }
            }
            .s-input-prefix {
                margin: 0;
                padding: 0;
            }
            .s-input-area input {
                padding-left: 12px;
            }
            .s-input-suffix {
                height: 30px;
                margin: 0;
            }
            .s-cascader-column-line-arrow {
                margin-top: 7px;
            }
            .s-cascader-multiple-label {
                padding: 0;
            }
        }
        .s-select {
            .s-input-area {
                height: 30px;
            }
        }
        .resource-group-panel {
            padding: 0px;
            border: 0px;
            .rg-label {
                color: #5c5f66;
                .select-tip {
                    margin: 0;
                    .rg-question-icon {
                        svg {
                            width: 18px;
                            height: 18px;
                            fill: #84868c !important;
                            &:hover {
                                fill: #2468f2 !important;
                            }
                        }
                    }
                }
            }
            .footer {
                display: flex;
                a {
                    color: #2468f2;
                }
            }
            h4 {
                border-left: none;
            }
        }
        /**虚商 资源组全局隐藏创建入口*/
        // .xs-hidden-resource-create {
        //     .rg-create-btn {
        //         display: none;
        //     }
        // }
        .shopping-cart {
            .price-item {
                padding-left: 40px !important;
                padding-right: 0 !important;
                display: flex;
                flex-direction: column;
                justify-content: space-around;
                .price {
                    line-height: 20px !important;
                }
            }
            .detail-wrapper {
                padding-left: 40px;
                display: flex;
                flex-direction: column;
                justify-content: space-around;
            }
            .price-error {
                font-size: 16px;
            }
            .grey-text {
                color: #83868c;
            }
        }
        .s-button-skin-primary {
            .s-icon {
                height: 32px;
                margin-right: 4px;
            }
            :hover {
                fill: #fff !important;
                color: #fff;
            }
        }
        .buybucket-container {
            .popover-class {
                .s-button {
                    margin-left: 16px;
                    min-width: 56px;
                }
            }
            .s-button {
                margin-left: 16px;
                min-width: 56px;
            }
        }
        // 步骤条公共样式
        .s-step-block {
            z-index: 1;
            width: 600px;
            margin-top: 24px;
            margin-bottom: 8px;
            .s-step-content-icon {
                background: #f7f7f7 !important;
            }
            .s-step-content-title {
                background: #f7f7f7 !important;
            }
        }
        .s-pagination {
            .s-pagination-total {
                padding-right: 8px;
            }
        }
        .s-tag-radio-button {
            .s-radio-text {
                min-width: 35px;
            }
        }
        .left_class {
            margin-left: 8px;
        }
        .auth {
            .s-new-app-order-page {
                width: unset;
            }
            .s-new-app-order-page-process-step-desc {
                margin-top: 8px;
                font-weight: 400;
                color: #5c5f66;
            }
            .s-new-app-order-page-process {
                height: 175px;
                margin-top: 15px;
                padding-right: 0px;
                padding-left: 0px;
                .s-new-app-order-page-process-step {
                    padding-left: 24px;
                    padding-right: 24px;
                    max-width: unset;
                }
            }
            .s-new-app-order-page-info {
                height: 360px;
            }
            .s-new-app-order-page {
                .s-new-app-order-page-panel {
                    .s-new-app-order-page-panel-title {
                        margin-bottom: 20px;
                        padding-left: 24px;
                    }
                }
            }
            .s-new-app-order-page-panel-title {
                margin-bottom: 20px;
            }
            .s-new-app-order-page-info-main-img {
                width: 280px;
                height: 280px;
                box-sizing: border-box;
                margin-right: 200px;
                img {
                    width: 100%;
                    height: 100%;
                }
            }
            .s-new-app-order-page-info {
                background:
                    url('https://bce.bdstatic.com/network-frontend/auth-background-1920.png') no-repeat right,
                    linear-gradient(140deg, #f3f7ff 0%, #cddbf3 100%);
                background-size: auto 360px !important;
                border-radius: 6px;
            }
            .s-new-app-order-page-info-desc {
                width: 660px;
                color: #151b26;
            }
            .s-new-app-order-page-info-main-content {
                max-width: 660px;
                height: 280px;
            }
        }
        @media screen and (max-width: 1280px) {
            .s-new-app-order-page-info-main-img {
                width: 280px;
                height: 280px;
                box-sizing: border-box;
                margin-right: unset;
            }
            .s-new-app-order-page-info {
                background:
                    url('https://bce.bdstatic.com/network-frontend/auth-background-1280.png') no-repeat right,
                    linear-gradient(140deg, #f3f7ff 0%, #cddbf3 100%);
                background-size: auto 360px !important;
                border-radius: 6px;
            }
        }
        @media screen and (min-width: 1680px) {
            .s-new-app-order-page-info-main-img {
                width: 280px;
                height: 280px;
                box-sizing: border-box;
            }
            .s-new-app-order-page-info-desc {
                width: 900px !important;
            }
        }
        .icon-auto-renew {
            font-size: 12px;
            color: #5fb333;
            position: relative;
            margin-left: 5px;
            cursor: pointer;
            font-family: iconfont !important;
        }
        .tag-text-tip {
            margin-bottom: 0;
        }
    }
    .app-menu-container {
        .acud-menu {
            width: 180px;
            &::-webkit-scrollbar {
                width: 0;
            }
            .acud-menu-inline-header {
                position: sticky;
                top: 0;
                background-color: #ffffff;
                z-index: 999999;
            }
            .acud-menu-item {
                position: relative;
                display: flex;
                .custom-menu-icon {
                    order: 2;
                    margin-left: 4px;
                }
                .acud-menu-item-icon {
                    order: 2;
                    margin-left: 4px;
                }
                .acud-menu-title-content {
                    order: 1;
                    flex-grow: 0 !important;
                    flex-shrink: 0 !important;
                }
            }
        }
    }
}



// 英文环境下的样式适配
body.locale-en {
    // 对等连接列表页面：隐藏连接申请和编辑标签按钮
    .peerconn-list-wrap {
        .hidden {
            display: none !important;
        }
    }

    .acud-menu-inline-header-item-text {
        line-height: 20px;
    };
    // 侧边栏菜单项 (React组件渲染的菜单)
    .acud-menu-item {
        font-size: 12px ;
        // color: #151B26 ;
        font-weight: bold ;
        min-height: 32px;
        line-height: 16px;
        margin-bottom: 0px;
        white-space: normal;
        .acud-menu-title-content{
              max-width:130px ;
              white-space: break-spaces;
             
        }
    }
    .acud-menu-inline.acud-menu-root .acud-menu-item>.acud-menu-title-content, .acud-menu-inline.acud-menu-root .acud-menu-submenu-title>.acud-menu-title-content{
        white-space: break-spaces;
    }
   
    // 一级标题 (有children的父级菜单项)
    .acud-menu-title-content {
        font-size: 12px;     
        font-weight: bold;
        line-height: 16px;
        white-space: break-spaces;
    }
    .acud-menu-inline.acud-menu-root>.acud-menu-submenu-open>.acud-menu-submenu-title, .acud-menu-inline.acud-menu-root>.acud-menu-item-group>.acud-menu-item-group-list>.acud-menu-submenu-open>.acud-menu-submenu-title{
        color: #151B26;
        font-weight: bold;
        line-height: 16px;
        font-size: 12px;
    }

    // 二级菜单项
    .acud-menu-submenu {
        margin-top: 0px;
    }
    .acud-menu-submenu .acud-menu-item {
        font-size: 12px;
        color: #5C5F66;
        font-weight: normal;
        min-height: 32px;
        line-height: 16px;
        margin-bottom: 0px;
        white-space: break-spaces;
    }

    // 二级标题 (children数组中的菜单项标题)
    .acud-menu-submenu .acud-menu-item .acud-menu-title-content {
        font-size: 12px;
        color: #5C5F66;
        font-weight: normal;
        line-height: 16px;
        white-space: normal;
        width: 112px;
    }

    // 双行内容高度42px
    .acud-menu-item.acud-menu-item-multiline {
        min-height: 42px;
    }

    // 两组内容间间距8px
    .acud-menu-item + .acud-menu-item,
    .acud-menu-submenu + .acud-menu-item,
    .acud-menu-item + .acud-menu-submenu {
        margin-top: 8px;
    }

    .acud-menu-item-icon {
                    order: 2;
                    margin-left: 4px;
                    width: 16px;
                    height: 16px;
                    background-color: currentColor;
                    mask-image: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M14,10.0173867 L14,11 C14,12.6568542 12.6568542,14 11,14 L5,14 C3.34314575,14 2,12.6568542 2,11 L2,5 C2,3.34314575 3.34314575,2 5,2 L6.01305709,2' stroke='%23000' stroke-linecap='butt' stroke-width='1' fill='none' stroke-linejoin='round'/%3E%3Cpath d='M10,2 L14,2 M9,7 L14,2 M14,2 L14,6' stroke='%23000' stroke-linecap='butt' stroke-width='1' fill='none' stroke-linejoin='round'/%3E%3C/svg%3E");
                    mask-repeat: no-repeat;
                    mask-position: center;
                    mask-size: contain;
                    -webkit-mask-image: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M14,10.0173867 L14,11 C14,12.6568542 12.6568542,14 11,14 L5,14 C3.34314575,14 2,12.6568542 2,11 L2,5 C2,3.34314575 3.34314575,2 5,2 L6.01305709,2' stroke='%23000' stroke-linecap='butt' stroke-width='1' fill='none' stroke-linejoin='round'/%3E%3Cpath d='M10,2 L14,2 M9,7 L14,2 M14,2 L14,6' stroke='%23000' stroke-linecap='butt' stroke-width='1' fill='none' stroke-linejoin='round'/%3E%3C/svg%3E");
                    -webkit-mask-repeat: no-repeat;
                    -webkit-mask-position: center;
                    -webkit-mask-size: contain;
                }

    .vpc-topology .resourceRelation .content-box .gateways .nat {
        width: 160px;
    }
    .vpc-topology .resourceRelation .content-box .gateways .vpn {
        width: 160px;
    }
    .vpc-topology .resourceRelation .content-box .gateways .peer {
        width: 160px;
    }
    .vpc-topology .resourceRelation .content-box .gateways .dcgw {
        width: 160px;
    }
    .vpc-topology .resourceRelation .content-box .gateways{
        margin: 100px 0 0 20px;
    }

    .vpc-topology .resourceRelation .content-box .subnetList .subnetLine .subnetResouces .bcc{
        width: 160px;
    }
    .vpc-topology .resourceRelation .content-box .subnetList .subnetLine .subnetResouces .endpoint {
        width: 190px;
    }
    .s-drawer>.s-drawer-wrapper .s-drawer-header {
        padding: 16px 16px;
        line-height: 22px;
    }
    //VPC ENI列表操作列
    .vpc-eni-list .operation_class {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;                    // 操作之间间距16px
        justify-content: center;

        .s-button {
            max-width: 120px;         // 操作按钮最长120px
            height: auto;
            line-height: 16px;
        }
    }

    // VPC实例列表操作列
    .vpc-instance-list .s-table .operation {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        align-items: flex-start;

        .s-button {
            max-width: 120px;
            min-width: 100px;
            height: auto;
            line-height: 16px;
            padding: 4px 8px;
        }
    }

    // 子网列表操作列
    .subnet-list .s-table .operation {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        align-items: flex-start;

        .s-button {
            max-width: 120px;
            min-width: 100px;
            height: auto;
            line-height: 16px;
            // padding: 4px 8px;
        }
    }


    // VPC终端节点列表操作列
    .vpc-endpoint-list .operation_class {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        align-items: flex-start;

        .s-button {
            max-width: 120px;
            min-width: 100px;
            height: auto;
            line-height: 16px;
            padding: 4px 8px;
        }
    }

    // VPN列表操作列
    .vpn_list_wrap .vpn-list-wrap .s-table .operations {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        align-items: center;

        .s-button {
            max-width: 120px;
            min-width: 100px;
        }
    }

    // VPC专线网关列表操作列
    .vpc-dcgw-list .operations {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        align-items: flex-start;

        .s-button {
            max-width: 120px;
            min-width: 100px;
            height: auto;
            padding: 4px 8px;
        }
    }

    // 详情页面label宽度动态控制
    // 当列中所有label宽度≤160px时，label宽=最大label宽
    // 当列中有label宽度>160px时，label宽=160px，超过后换行

    .subnet-detail .cell-title {
        max-width: 160px;
        min-width: auto;
        width: auto;
        white-space: normal;
        word-break: break-word;
        line-height: 16px;
    }

    .vpc-instance-detail .content .cell .cell-title {
        max-width: 160px;
        min-width: auto;
        width: auto;
        white-space: normal;
        word-break: break-word;
        line-height: 16px;
    }

    .vpc-havip-detail .cell-title {
        max-width: 160px;
        min-width: auto;
        width: auto;
        white-space: normal;
        word-break: break-word;
        line-height: 16px;
    }

    .vpc-eni-detail .cell-title {
        max-width: 160px;
        min-width: auto;
        width: auto;
        white-space: normal;
        word-break: break-word;
        line-height: 16px;
    }

    .vpc-endpoint-detail .content-item .cell-title {
        max-width: 160px;
        min-width: auto;
        width: auto;
        white-space: normal;
        word-break: break-word;
        line-height: 16px;
    }

    .vpc-security-detail .content-box .cell .cell-title {
        max-width: 160px;
        min-width: auto;
        width: auto;
        white-space: normal;
        word-break: break-word;
        line-height: 16px;
    }

    .vpc-enterpriseSecurity-detail .content-box .cell .cell-title {
        max-width: 160px;
        min-width: auto;
        width: auto;
        white-space: normal;
        word-break: break-word;
        line-height: 16px;
    }

    .instance-detail-wrap .param-content-wrap .cell .cell-title {
        max-width: 160px;
        min-width: auto;
        width: auto;
        white-space: normal;
        word-break: break-word;
        line-height: 16px;
    }

    .vpc-ipv6gw-detail .ipv6gw-detail-content .content-item-box .content-item label {
        max-width: 160px;
        min-width: auto;
        width: auto;
        white-space: normal;
        word-break: break-word;
        line-height: 16px;
    }

    .vpn-detail-wrap .content-item-box .content-item-key {
        max-width: 160px;
        min-width: auto;
        width: auto;
        white-space: normal;
        word-break: break-word;
        line-height: 16px;
    }

    .peer-instance-wrap .instance-item-box .item-box .item-key {
        max-width: 160px;
        min-width: auto;
        width: auto;
        white-space: normal;
        word-break: break-word;
        line-height: 16px;
    }

    .dcgw-detail-v2 .content-box .cell .cell-title {
        max-width: 160px;
        min-width: auto;
        width: auto;
        white-space: normal;
        word-break: break-word;
        line-height: 16px;
    }

    .multicasting-detail-wrap .content-item-box .content-item-key {
        max-width: 160px;
        min-width: auto;
        width: auto;
        white-space: normal;
        word-break: break-word;
        line-height: 16px;
    }

    .vpc-qos-detail .basic-item-wrap .basic-item-box .basic-item-key {
        max-width: 160px;
        min-width: auto;
        width: auto;
        white-space: normal;
        word-break: break-word;
        line-height: 16px;
    }

    .dc-detail-wrap .list-content .detail-parts-table .detail-part-1-col > dd .detail-cell > label {
        max-width: 160px;
        min-width: auto;
        width: auto;
        white-space: normal;
        word-break: break-word;
        line-height: 16px;
    }

    .dc-channel-wrap .list-content .detail-parts-table .detail-part-1-col > dd .detail-cell > label {
        max-width: 160px;
        min-width: auto;
        width: auto;
        white-space: normal;
        word-break: break-word;
        line-height: 16px;
    }

    .vpc-probe-detail li.content-item label {
        max-width: 160px;
        min-width: auto;
        width: auto;
        white-space: normal;
        word-break: break-word;
        line-height: 16px;
    }

    .vpc-path-detail .content .cell .cell-title {
        max-width: 160px;
        min-width: auto;
        width: auto;
        white-space: normal;
        word-break: break-word;
        line-height: 16px;
    }

    .s-button{
        width: auto !important;
        line-height: 16px !important;
    }
// .operation_class {
//     display: flex !important;
//     flex-wrap: wrap !important;
//     justify-content: center;
//     align-items: center ;
//     gap: 16px 16px !important;
// }

// .operation_class .s-button {
//     line-height: 16px !important;
//     max-width: 120px !important;
//     height: auto !important;

// }
// .operation{
//     display: flex !important;
//     flex-wrap: wrap !important;
//     justify-content: center;
//     align-items: center ;
//     gap: 16px 16px !important;
 
// }
// .operation .s-button{
//     line-height: 16px !important;
//     max-width: 120px !important;
//     height: auto !important; 
// }

    .monitor-trend-box .bcm-chart-no-data-bg p {
        bottom: -35px !important;
        left: -110px;
        width: 350px;
    }
    .s-picker-shortcut-item{
        margin-right:9px;
    }
    .tag-edit-panel .inline-form .s-form-item:not(:last-child){
        margin-right: 16px;
    }

    .vpn-create-component .vpn-create-wrap .form-part-wrap .buy-time .s-radio-button .s-radio-text{
        max-width:240px;
        min-width: 72px;
        padding: 0 12px;
    }
    .billing-sdk-shopping-cart-wrapper .shopping-cart-detail-container .shopping-cart-detail-title>h4{
        width: 140px;
    }
    .billing-sdk-shopping-cart-wrapper .shopping-cart-detail-container .shopping-cart-detail-wrapper span {
        width: 90px;
    }
    .shopping-cart-config-detail-wrapper {
        width: 365px;
    }
    .vpc-peerconn-create-v2 .form-widget .peerconn-purchase-notes .billing-instructions .s-button{
        width: 105px;
    }
    .vpc-peerconn-create-v2 .s-form .s-radio-button-group .s-radio-text{
        padding: 0 12px;
        width: 120px;
        min-width: 72px;
        max-width: 240px;
    }
    .tag-edit-panel .reminder{
        width: 592px;
    }
    .s-form-item-label{
        max-width: 160px;
        width: auto;
        line-height: 16px;
        margin-right: 16px;
    }

    .vpc-diagnosis .diagnosis-container .diagnosis-tabs .diagnosis-tab{
        height: 155px;
    }

    .diagnosis-connect .connect-content {
        .suggestion {
            display: flow;
            white-space: normal;
            max-height: 90px;
            height: auto;
        }
        .link{
            white-space: normal;
        }
    }

    // 表格列宽控制样式
    // 默认样式：表格列数不超过9列时
    .s-table colgroup col {
        width: 200px;
        // max-height: 60px;
        height: 60px;
    }

    // 当表格超过9列时的样式
    .s-table:has(colgroup col:nth-child(10)) colgroup col {
        max-width: 260px;
        min-width: 200px;
        // max-height: 60px;
        height: 60px;
        width: auto;
    }

    .s-list-page .operation-wrap .buttons-wrap{
        min-width: 62%;
    }
    // .main-area .react-main .s-select .s-input-area{
    //     width: 150px;
    //     color: red;
    // }
    // .s-search .s-select{
    //     width: 150px;
    // }
    .s-input.s-input-prefix-container .s-input-area input {
        // 将所有此类输入框的 placeholder 统一设置为 "Search"
    }
}

.s-button:active {
    background-color: transparent;
}
.s-radio-button-group {
    label:not(.state-disabled) input[type='radio']:active ~ .s-radio-text {
        background-color: transparent;
    }
}

.s-popup-content-box {
    a,
    a:visited,
    a:hover,
    a:active {
        color: #2468f2;
    }
    .s-popover-content {
        max-width: 400px;
    }
    .s-popover-arrow,
    .s-tooltip-arrow {
        border: none !important;
    }
    .s-menu {
        overflow-y: auto;
    }
}
.s-drawer {
    a,
    a:visited,
    a:hover,
    a:active {
        color: #2468f2;
    }
}

.s-dialog {
    a,
    a:visited,
    a:hover,
    a:active {
        color: #2468f2;
    }
}

.s-icon-button {
    margin-left: 8px;
    padding: 0 7px !important;
}
.button-margin-left {
    margin-left: 8px;
}

.s-button-disabled {
    color: #b4b6ba !important;
}

.s-table .s-table-loading-content {
    background: none !important;
    background-image: none !important;
}
.s-table {
    .s-table-body::-webkit-scrollbar {
        display: none;
    }
}
.s-table .s-table-subrow-wrapper {
    background: #f7f7f9 !important;
}
.s-tabs > .s-tabnav .s-tabnav-nav-selected {
    color: #2468f2 !important;
    fill: #2468f2 !important;
}
.s-checkbox .s-checkbox-input:checked {
    color: #2468f2 !important;
    background-color: #2468f2 !important;
}
.s-checkbox-input-indeterminate {
    color: #2468f2 !important;
    background-color: #2468f2 !important;
}
.siderbar-tab {
    .skin-accordion-tab > .bui-tab-header > .bui-tab-nav-wrapper > .bui-tab-nav-bar {
        background-color: #fff !important;
        padding-top: 12px;
        .bui-tab-nav-item {
            font-size: 14px;
            color: #151b26;
            display: block;
            padding: 0 16px;
            margin-bottom: 4px;
            line-height: 40px;
        }
        .bui-tab-nav-item:hover {
            color: #2468f2;
        }
    }
    .skin-accordion-tab > .bui-tab-header > .bui-tab-nav-wrapper > .bui-tab-nav-bar > .bui-tab-nav-item-active {
        border: 0;
        background-color: #e6f0ff;
        color: #2468f2;
        &:before {
            content: none;
        }
    }
    .bui-tab-header {
        border: none;
        border-right: 1px solid #ebebeb;
        .bui-tab-nav-item {
            color: #151a26;
        }
    }
}
body .s-popover .s-popover-body {
    .s-popover-content {
        max-width: 400px;
        min-width: auto;
    }
}
.edit-name-warp {
    margin-bottom: 40px;
    .s-button {
        float: right;
        margin: 10px 5px;
    }
    .edit-name-tip {
        margin-top: 10px;
        color: #83868c;
    }
}
.bui-tab-header {
    border-bottom: none;
}
.s-form {
    .s-form-item-label {
        color: #5c5f66;
        font-weight: 400;
    }
    .s-form-item-label-required > label:before {
        left: -7px;
        position: absolute;
    }
    .s-form-item-error {
        color: #f33e3e !important;
        padding-bottom: 0 !important;
    }
    .s-form-item-help {
        color: #84868c !important;
        padding-bottom: 0 !important;
    }
}
.status:before {
    margin-right: 8px;
}
.s-search .s-select:hover {
    background-color: #f2f2f4;
}
.s-search .s-select-active .s-input {
    background-color: #f2f2f4 !important;
}
.edit-popover-class {
    .s-popover-content {
        padding: 12px !important;
    }
    .edit-wrap {
        margin-bottom: 40px;
        .edit-tip {
            margin-top: 10px;
            color: #84868c;
        }
        .s-button {
            min-width: 46px;
            float: left;
            margin: 10px 8px 10px 0px;
        }
    }
}
.s-dialog {
    .s-dialog-wrapper {
        overflow: unset !important;
        .s-dialog-header {
            h3 {
                font-weight: 500;
                font-size: 16px;
            }
        }
        .s-dialog-content {
            padding: 24px;
        }
    }
}
.status.normal:before {
    color: #30bf13;
}

.status.error:before {
    color: #f33e3e;
}

.status.unavailable:before {
    color: #b8babf;
}
.status.rolling:after {
    color: #2468f2;
}

.status.warning {
    color: #151b26;
}

.resouce-group-select {
    .tree-select {
        .select-tip {
            display: none;
        }
    }
    .footer {
        .tip {
            color: #999;
            margin: 0;
            line-height: 30px;
        }
        .s-button {
            vertical-align: top;
        }
    }
}

.s-radio-group input[type='radio']:disabled:hover,
.s-radio input[type='radio']:disabled:hover {
    border-color: #e8e9eb;
}

.bui-webuploader {
    .webuploader-pick :hover {
        color: #2468f2 !important;
    }
    .webuploader-pick-hover {
        color: #2468f2 !important;
    }
}
.locale-en,
.locale-zh {
    .name-icon {
        margin-left: 4px !important;
    }
    .clip-board-main {
        .s-icon {
            margin-left: 4px !important;
        }
    }
}

.s-table .s-table-thead {
        background: #f7f7f9 !important;
        color: #5c5f66 !important;
}

.s-table .s-table-row:hover > td {
    background: #e6f0ff !important;
}
.s-table.s-table-fixed .s-table-hcell-fixed {
    background: #f7f7f9 !important;
}

.name-icon {
    .s-icon {
        fill: #2468f2 !important;
    }
}
.name-icon :hover {
    .s-icon {
        fill: #528eff !important;
    }
}

.clip-board-main :hover {
    .s-icon {
        fill: #528eff !important;
    }
}

.edit-popover-class:active {
    .name-icon {
        .s-icon {
            fill: #144bcc !important;
        }
    }
}
.clip-board-main:active {
    .s-icon {
        fill: #144bcc !important;
    }
}
.s-cascader-column {
    max-height: 300px;
}
.s-cascader-column-line-active:hover,
.s-cascader-column-line-normal:hover {
    background-color: #e6f0ff !important;
    color: #2468f2 !important;
}

.app-view .s-detail-page .s-detail-page-title {
    padding-left: 16px !important;
}

.icon-class {
    display: flex;
    align-items: center;
}

.button-icon:active {
    .s-icon {
        fill: #144bcc !important;
    }
}

.resource-group-dialog {
    .s-dialog-footer {
        .s-button {
            width: auto !important;
        }
    }
}

.s-dialog > .s-dialog-wrapper .s-dialog-header > .s-dialog-header-close .s-icon {
    fill: #84868c !important;
}
.s-dialog > .s-dialog-wrapper .s-dialog-header > .s-dialog-header-close .s-icon:hover {
    fill: #5c5f66 !important;
}

.s-alert {
    .s-alert-content {
        color: #151b26 !important;
    }
}
.s-dialog,
.bui-dialog {
    .s-form {
        .s-form-item {
            margin: 20px 0 20px 0 !important;
        }
        .s-form-item-control-wrapper {
            display: flex;
            flex-wrap: wrap;
            min-height: 32px;
            flex-direction: column;
            flex: 1;
        }
    }
}

.bui-notification-msg-global {
    width: 313px;
    height: 70px;
    .content {
        color: #151b26;
        line-height: 20px;
        word-break: break-all;
        font-size: 14px;
        font-weight: 500;
        margin-right: 4px;
    }
    .request-id {
        min-height: 20px;
        margin-top: 6px;
        color: #151b26;
        line-height: 20px;
        word-break: break-all;
    }
    .bui-icon {
        display: none;
    }
}

.locale-en {
    .app-view .resource-group-search .s-cascader .s-cascader-value {
        min-width: 124px;
        padding-right: 32px;
    }
    .vpc-subnet-dialog-wrapper {
        .s-dialog .s-dialog-wrapper .s-dialog-content {
            min-width: 900px !important;
        }
        .s-form .s-form-item-label {
            width: 212px !important;
        }
        .s-form .s-form-item-help {
            max-width: 600px !important;
        }
        .s-dialog-content .vpc-subnet-create .cidr-tip-err {
            padding-left: 212px !important;
        }
        .resource-form-part-wrap .resource-group-panel .resouce-group-select-main label {
            width: 212px !important;
        }
        .s-dialog-content .vpc-subnet-create .resource-form-part-wrap .resource-group-panel .footer {
            margin-left: 212px !important;
        }
        .tag-edit-panel {
            .s-form .s-form-item-label {
                width: 76px !important;
            }
        }
    }
    .vpc-subnet-dialog-wrapper .s-dialog-content .vpc-subnet-create .tag-item .tag-edit-panel {
        width: 600px !important;
    }
}

.create-disable {
    .s-button {
        display: none;
    }
}

.billing-sdk-shopping-cart-wrapper {
    .shopping-cart-item-container .shopping-cart-item-wrapper .shopping-cart-item-price-wrapper > span.price {
        color: #f33d3d;
        line-height: 28px;
        font-size: 24px !important;
    }
    .shopping-cart-item-container .shopping-cart-item-wrapper .shopping-cart-item-config-name > h4 {
        color: #5c5f66;
    }
    .total-price {
        font-size: 24px;
    }
    .shopping-cart-detail-container .shopping-cart-detail-wrapper span.detail-button {
        font-size: 12px;
    }
    .shopping-cart-detail-container .shopping-cart-detail-wrapper span.detail-button > span {
        color: #2468f2;
    }
    .shopping-cart-detail-container .shopping-cart-detail-wrapper span.detail-button {
        .detail-button-icon svg {
            fill: #2468f2 !important;
        }
    }
}

.s-form-item-region {
    .s-radio-button-group .s-wrapper {
        max-width: 1000px;
        .s-radio-button {
            margin-bottom: 12px;
        }
    }
    .s-radio-button-enhanced-group .s-radio-button .s-radio-text {
        width: 130px !important;
    }
}
.first-config-content {
    padding-bottom: 10px;
}
.tag-edit-panel {
    .inline-form .s-form-item {
        margin-top: 0px;
    }
    .tag-text-tip {
        margin-bottom: 0px !important;
    }
}

.no-instance-content {
    .s-detail-page-content {
        padding: 0;
        background: #fff;
    }
}

.no-res-wrap {
    .wrapper {
        display: flex;
    }
    .s-radio-checked {
        .s-radio-text {
            border-right: 1px solid #2468f2;
        }
    }
    label {
        .s-radio-text {
            border-radius: 0 !important;
            border-right-width: 0px;
        }
    }
    .s-radio-text {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .radius-left {
        .s-radio-text {
            border-radius: 4px 0 0 4px !important;
        }
    }
    .radius-right {
        .s-radio-text {
            border-radius: 0 4px 4px 0 !important;
            border-right-width: 1px !important;
        }
    }
}

.global-loading-class {
    position: absolute;
    left: 50%;
    top: 50%;
}
.assist-dialog-wrap {
    z-index: 999999999;
    a,
    a:visited,
    a:hover,
    a:active {
        color: #2468f2;
    }
    height: 600px;
    .message-container {
        border-radius: 8px;
        box-shadow:
            0 4px 6px 0 rgba(8, 14, 26, 0.04),
            0 1px 10px 0 rgba(8, 14, 26, 0.05),
            0 2px 4px -1px rgba(8, 14, 26, 0.06);
    }
}

.tip-inline {
    a,
    p {
        display: inline;
    }
}

.assist-tip {
    cursor: pointer;
}

.assist-tip::after {
    display: inline-block;
    position: relative;
    vertical-align: middle;
    content: '';
    width: 15px;
    top: -1px;
    height: 15px;
    background: #fff;
    background-image: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iNDhweCIgaGVpZ2h0PSI0OHB4IiB2aWV3Qm94PSIwIDAgNDggNDgiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgICA8cGF0aCBkPSJNMTkuMjgzODI3OCwwLjM4OTc5NzY3NiBMMi4xODc3Njc0MSwxMC4wNDYyNzg0IEMxLjUxNTI2NzQzLDEwLjQyNjEzMSAxLjI3ODAyOTg2LDExLjI3OTIzMTMgMS42NTc4ODI0NywxMS45NTE3MzEzIEMxLjc4NTk1MzE3LDEyLjE3ODQ3MDcgMS45NzQ3ODc4MywxMi4zNjQ5NDc5IDIuMjAzMTE4MTYsMTIuNDkwMTYwMiBMMTMuMzc2NTIwNSwxOC42MTc0NTQ2IEwxMy4zNzY1MjA1LDE4LjYxNzQ1NDYgTDMxLjk2MTExODIsOC4wMzE3NTkwNSBDMzIuMjk2NjgyMiw3Ljg0MDYyMzQyIDMyLjQxMzc2NDYsNy40MTM2NDg5NyAzMi4yMjI2Mjg5LDcuMDc4MDg0OTIgQzMyLjE2MDQyNTIsNi45Njg4Nzc5NyAzMi4wNjk5Mzc4LDYuODc4NDU0OCAzMS45NjA2ODY3LDYuODE2MzI4NiBMMjAuNjYyOTEyMiwwLjM5MTc5MDYxMyBDMjAuMjM1NTI2OSwwLjE0ODc1NTc0IDE5LjcxMTkxMzcsMC4xNDc5OTkwNTkgMTkuMjgzODI3OCwwLjM4OTc5NzY3NiBaIiBpZD0icGF0aC0xIj48L3BhdGg+CiAgICAgICAgPGZpbHRlciB4PSItNTAuNSUiIHk9Ii01MS42JSIgd2lkdGg9IjIwMS4xJSIgaGVpZ2h0PSIyMDMuMyUiIGZpbHRlclVuaXRzPSJvYmplY3RCb3VuZGluZ0JveCIgaWQ9ImZpbHRlci0zIj4KICAgICAgICAgICAgPGZlR2F1c3NpYW5CbHVyIHN0ZERldmlhdGlvbj0iNi45OTI0MzI0MyIgaW49IlNvdXJjZUdyYXBoaWMiPjwvZmVHYXVzc2lhbkJsdXI+CiAgICAgICAgPC9maWx0ZXI+CiAgICAgICAgPHBhdGggZD0iTTE5Ljk3MzkzNDcsMCBMMS4wOTM4ODM3LDEwLjY2NDE0NCBDMC43NTc2MzM3MTEsMTAuODU0MDcwMyAwLjYzOTAxNDkyNiwxMS4yODA2MjA0IDAuODI4OTQxMjI5LDExLjYxNjg3MDQgQzAuODkyOTc2NTgxLDExLjczMDI0MDEgMC45ODczOTM5MDgsMTEuODIzNDc4NyAxLjEwMTU1OTA3LDExLjg4NjA4NDkgTDEyLjY5MTE0MDMsMTguMjQxNjA0MyBDMTMuMTE3MDUyNiwxOC40NzUxNjcxIDEzLjYzMzY1NSwxOC40NzA5OTIxIDE0LjA1NTczNjgsMTguMjMwNTc2NCBMMzEuOTYxMTE4Miw4LjAzMTc1OTA1IEMzMi4yOTY2ODIyLDcuODQwNjIzNDIgMzIuNDEzNzY0Niw3LjQxMzY0ODk3IDMyLjIyMjYyODksNy4wNzgwODQ5MyBDMzIuMTYwNDI1Miw2Ljk2ODg3Nzk3IDMyLjA2OTkzNzgsNi44Nzg0NTQ4IDMxLjk2MDY4NjcsNi44MTYzMjg2MSBMMTkuOTczOTM0NywwIEwxOS45NzM5MzQ3LDAgWiIgaWQ9InBhdGgtNCI+PC9wYXRoPgogICAgICAgIDxmaWx0ZXIgeD0iLTUwLjUlIiB5PSItNTEuNiUiIHdpZHRoPSIyMDEuMSUiIGhlaWdodD0iMjAzLjMlIiBmaWx0ZXJVbml0cz0ib2JqZWN0Qm91bmRpbmdCb3giIGlkPSJmaWx0ZXItNiI+CiAgICAgICAgICAgIDxmZUdhdXNzaWFuQmx1ciBzdGREZXZpYXRpb249IjYuOTkyNDMyNDMiIGluPSJTb3VyY2VHcmFwaGljIj48L2ZlR2F1c3NpYW5CbHVyPgogICAgICAgIDwvZmlsdGVyPgogICAgICAgIDxwYXRoIGQ9Ik0xMy4zODc3OTI1LDAgTDEzLjM4Nzc5MjUsMjEuNjU3NDE3OSBDMTMuMzg3NzkyNSwyMi4xNjAyMzEgMTMuMTE3ODY1NiwyMi42MjQzNDAxIDEyLjY4MDgyNTEsMjIuODcyOTY5NSBMMS4wNDUwMDI4MiwyOS40OTI1MTA4IEMwLjcwOTMzNzUxMywyOS42ODM0Njg1IDAuMjgyNDI1MTgxLDI5LjU2NjE1OTkgMC4wOTE0Njc0MzkzLDI5LjIzMDQ5NDYgQzAuMDMxNTIwMjE0OSwyOS4xMjUxMTk0IDUuNjk5NTgzNTFlLTE2LDI5LjAwNTk2ODYgMCwyOC44ODQ3MzUgTDAsOC4zNDEyOTU0MyBDLTEuNjIzNDc3MjRlLTE1LDcuODM1ODQyNDIgMC4yNzI3NDM4MjQsNy4zNjk3MjQyIDAuNzEzMzkxOCw3LjEyMjExMTM5IEwxMy4zODc3OTI1LDAgTDEzLjM4Nzc5MjUsMCBaIiBpZD0icGF0aC03Ij48L3BhdGg+CiAgICAgICAgPGZpbHRlciB4PSItNTAuNSUiIHk9Ii01MS42JSIgd2lkdGg9IjIwMS4xJSIgaGVpZ2h0PSIyMDMuMyUiIGZpbHRlclVuaXRzPSJvYmplY3RCb3VuZGluZ0JveCIgaWQ9ImZpbHRlci05Ij4KICAgICAgICAgICAgPGZlR2F1c3NpYW5CbHVyIHN0ZERldmlhdGlvbj0iNi45OTI0MzI0MyIgaW49IlNvdXJjZUdyYXBoaWMiPjwvZmVHYXVzc2lhbkJsdXI+CiAgICAgICAgPC9maWx0ZXI+CiAgICAgICAgPGZpbHRlciB4PSItNTAuNSUiIHk9Ii01MS42JSIgd2lkdGg9IjIwMS4xJSIgaGVpZ2h0PSIyMDMuMyUiIGZpbHRlclVuaXRzPSJvYmplY3RCb3VuZGluZ0JveCIgaWQ9ImZpbHRlci0xMCI+CiAgICAgICAgICAgIDxmZUdhdXNzaWFuQmx1ciBzdGREZXZpYXRpb249IjYuOTkyNDMyNDMiIGluPSJTb3VyY2VHcmFwaGljIj48L2ZlR2F1c3NpYW5CbHVyPgogICAgICAgIDwvZmlsdGVyPgogICAgICAgIDxwYXRoIGQ9Ik0xMy4zODc3OTI1LDEuMTk1MDA0MiBMMTMuMzg3NzkyNSwyMS42NDc1OTE1IEMxMy4zODc3OTI1LDIyLjE1NTY0MzMgMTMuMTEyMjYxOCwyMi42MjM3MzI2IDEyLjY2ODA2MiwyMi44NzAzMTU4IEwwLDI5LjkwMjU4MzEgTDAsMjkuOTAyNTgzMSBMMCw3LjkzMjE0MTEyIEMyLjE4OTQ5NTk4ZS0xNSw3LjY3OTQxNDYyIDAuMTM2MzcxOTExLDcuNDQ2MzU1NTEgMC4zNTY2OTU4OTgsNy4zMjI1NDkxIEwxMi4zNDYwMDE5LDAuNTg1NDEyMTgzIEMxMi42ODI2NzAyLDAuMzk2MjI4NTExIDEzLjEwODk1NzYsMC41MTU3ODg0ODQgMTMuMjk4MTQxMiwwLjg1MjQ1Njg1OSBDMTMuMzU2OTE4OSwwLjk1NzA1NjYzIDEzLjM4Nzc5MjUsMS4wNzUwMjExOSAxMy4zODc3OTI1LDEuMTk1MDA0MiBaIiBpZD0icGF0aC0xMSI+PC9wYXRoPgogICAgICAgIDxmaWx0ZXIgeD0iLTUwLjUlIiB5PSItNTEuNiUiIHdpZHRoPSIyMDEuMSUiIGhlaWdodD0iMjAzLjMlIiBmaWx0ZXJVbml0cz0ib2JqZWN0Qm91bmRpbmdCb3giIGlkPSJmaWx0ZXItMTMiPgogICAgICAgICAgICA8ZmVHYXVzc2lhbkJsdXIgc3RkRGV2aWF0aW9uPSI2Ljk5MjQzMjQzIiBpbj0iU291cmNlR3JhcGhpYyI+PC9mZUdhdXNzaWFuQmx1cj4KICAgICAgICA8L2ZpbHRlcj4KICAgICAgICA8bGluZWFyR3JhZGllbnQgeDE9IjYzLjQ0MzU1MzclIiB5MT0iNS44NjA5MzUyMyUiIHgyPSI0NS44MzU5NDgxJSIgeTI9IjU1LjMxODU3OTglIiBpZD0ibGluZWFyR3JhZGllbnQtMTQiPgogICAgICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSIjRjc5MTcyIiBvZmZzZXQ9IjAlIj48L3N0b3A+CiAgICAgICAgICAgIDxzdG9wIHN0b3AtY29sb3I9IiNFQzU4M0UiIG9mZnNldD0iMTAwJSI+PC9zdG9wPgogICAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICAgICAgPGZpbHRlciB4PSItNTAuNSUiIHk9Ii01MS42JSIgd2lkdGg9IjIwMS4xJSIgaGVpZ2h0PSIyMDMuMyUiIGZpbHRlclVuaXRzPSJvYmplY3RCb3VuZGluZ0JveCIgaWQ9ImZpbHRlci0xNSI+CiAgICAgICAgICAgIDxmZUdhdXNzaWFuQmx1ciBzdGREZXZpYXRpb249IjYuOTkyNDMyNDMiIGluPSJTb3VyY2VHcmFwaGljIj48L2ZlR2F1c3NpYW5CbHVyPgogICAgICAgIDwvZmlsdGVyPgogICAgPC9kZWZzPgogICAgPGcgaWQ9Iumhtemdoi0xIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8ZyBpZD0i56S65oSPIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgtNzMxLjAwMDAwMCwgLTg1LjAwMDAwMCkiPgogICAgICAgICAgICA8ZyBpZD0i57yW57uELTgiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDczMS4wMDAwMDAsIDg1LjAwMDAwMCkiPgogICAgICAgICAgICAgICAgPHJlY3QgaWQ9IuefqeW9oiIgeD0iMCIgeT0iMCIgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4Ij48L3JlY3Q+CiAgICAgICAgICAgICAgICA8ZyBpZD0i57yW57uELTPlpIfku70tNSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoNC4wMDAwMDAsIDEuNTAwMDAwKSI+CiAgICAgICAgICAgICAgICAgICAgPGcgaWQ9IuakreWchuW9oiI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxtYXNrIGlkPSJtYXNrLTIiIGZpbGw9IndoaXRlIj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx1c2UgeGxpbms6aHJlZj0iI3BhdGgtMSI+PC91c2U+CiAgICAgICAgICAgICAgICAgICAgICAgIDwvbWFzaz4KICAgICAgICAgICAgICAgICAgICAgICAgPHVzZSBpZD0i6JKZ54mIIiBmaWxsLW9wYWNpdHk9IjAiIGZpbGw9IiNGRkZGRkYiIHhsaW5rOmhyZWY9IiNwYXRoLTEiPjwvdXNlPgogICAgICAgICAgICAgICAgICAgICAgICA8ZWxsaXBzZSBmaWxsPSIjNDRFMzgzIiBmaWx0ZXI9InVybCgjZmlsdGVyLTMpIiBtYXNrPSJ1cmwoI21hc2stMikiIGN4PSIyNi45MjkwNTE3IiBjeT0iLTUuMzE5MTAwNDciIHJ4PSIyMC43NTc1MjE5IiByeT0iMjAuMzA3NjE3MiI+PC9lbGxpcHNlPgogICAgICAgICAgICAgICAgICAgIDwvZz4KICAgICAgICAgICAgICAgICAgICA8ZyBpZD0i5qSt5ZyG5b2iIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSg2Ljc0MTc1OSwgMjYuMzgyNTQ1KSI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxtYXNrIGlkPSJtYXNrLTUiIGZpbGw9IndoaXRlIj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx1c2UgeGxpbms6aHJlZj0iI3BhdGgtNCI+PC91c2U+CiAgICAgICAgICAgICAgICAgICAgICAgIDwvbWFzaz4KICAgICAgICAgICAgICAgICAgICAgICAgPHVzZSBpZD0i6JKZ54mIIiBmaWxsLW9wYWNpdHk9IjAiIGZpbGw9IiNGRkZGRkYiIHhsaW5rOmhyZWY9IiNwYXRoLTQiPjwvdXNlPgogICAgICAgICAgICAgICAgICAgICAgICA8ZWxsaXBzZSBmaWxsPSIjRUM1RDNFIiBmaWx0ZXI9InVybCgjZmlsdGVyLTYpIiBtYXNrPSJ1cmwoI21hc2stNSkiIGN4PSItMy45NzcwNTIwNCIgY3k9IjE2LjQ4OTA5MDgiIHJ4PSIyMC43NTc1MjE5IiByeT0iMjAuMzA3NjE3MiI+PC9lbGxpcHNlPgogICAgICAgICAgICAgICAgICAgIDwvZz4KICAgICAgICAgICAgICAgICAgICA8ZyBpZD0i5qSt5ZyG5b2iIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjAwMDAwMCwgMy43NTkwMjMpIj4KICAgICAgICAgICAgICAgICAgICAgICAgPG1hc2sgaWQ9Im1hc2stOCIgZmlsbD0id2hpdGUiPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgPHVzZSB4bGluazpocmVmPSIjcGF0aC03Ij48L3VzZT4KICAgICAgICAgICAgICAgICAgICAgICAgPC9tYXNrPgogICAgICAgICAgICAgICAgICAgICAgICA8dXNlIGlkPSLokpnniYgiIGZpbGw9IiNGRkZGRkYiIHhsaW5rOmhyZWY9IiNwYXRoLTciPjwvdXNlPgogICAgICAgICAgICAgICAgICAgICAgICA8ZWxsaXBzZSBmaWxsPSIjMjQ2OEYyIiBmaWx0ZXI9InVybCgjZmlsdGVyLTkpIiBtYXNrPSJ1cmwoI21hc2stOCkiIGN4PSItNS41NzM3ODQ1IiBjeT0iMjQuMDEyMDc3NyIgcng9IjIwLjc1NzUyMTkiIHJ5PSIyMC4zMDc2MTcyIj48L2VsbGlwc2U+CiAgICAgICAgICAgICAgICAgICAgICAgIDxlbGxpcHNlIGZpbGw9IiM0NEUzODMiIGZpbHRlcj0idXJsKCNmaWx0ZXItMTApIiBtYXNrPSJ1cmwoI21hc2stOCkiIGN4PSIyMC44NjEwMDgzIiBjeT0iLTE1LjA0MTAzMjIiIHJ4PSIyMC43NTc1MjE5IiByeT0iMjAuMzA3NjE3MiI+PC9lbGxpcHNlPgogICAgICAgICAgICAgICAgICAgIDwvZz4KICAgICAgICAgICAgICAgICAgICA8ZyBpZD0i5qSt5ZyG5b2iIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgyNi42MTIyMDgsIDExLjM5NjA3NSkiPgogICAgICAgICAgICAgICAgICAgICAgICA8bWFzayBpZD0ibWFzay0xMiIgZmlsbD0id2hpdGUiPgogICAgICAgICAgICAgICAgICAgICAgICAgICAgPHVzZSB4bGluazpocmVmPSIjcGF0aC0xMSI+PC91c2U+CiAgICAgICAgICAgICAgICAgICAgICAgIDwvbWFzaz4KICAgICAgICAgICAgICAgICAgICAgICAgPHVzZSBpZD0i6JKZ54mIIiBmaWxsPSIjRkZGRkZGIiB4bGluazpocmVmPSIjcGF0aC0xMSI+PC91c2U+CiAgICAgICAgICAgICAgICAgICAgICAgIDxlbGxpcHNlIGZpbGw9IiM0NEUzODMiIGZpbHRlcj0idXJsKCNmaWx0ZXItMTMpIiBtYXNrPSJ1cmwoI21hc2stMTIpIiBjeD0iMTMuNDA5NTkwMiIgY3k9Ii0yLjE5Njg5ODMxIiByeD0iMjAuNzU3NTIxOSIgcnk9IjIwLjMwNzYxNzIiPjwvZWxsaXBzZT4KICAgICAgICAgICAgICAgICAgICAgICAgPGVsbGlwc2UgZmlsbD0idXJsKCNsaW5lYXJHcmFkaWVudC0xNCkiIGZpbHRlcj0idXJsKCNmaWx0ZXItMTUpIiBtYXNrPSJ1cmwoI21hc2stMTIpIiBjeD0iMS41MjI4MDQxOCIgY3k9IjM0Ljk0Njk0ODQiIHJ4PSIyMC43NTc1MjE5IiByeT0iMjAuMzA3NjE3MiI+PC9lbGxpcHNlPgogICAgICAgICAgICAgICAgICAgIDwvZz4KICAgICAgICAgICAgICAgIDwvZz4KICAgICAgICAgICAgPC9nPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+Cg==');
    background-size: cover;
}
.vpc-instance-list-intro {
    font-size: 16px;
    width: 160px;
    background-color: #2468f2;
    box-shadow: none;
    .s-button {
        border-color: #fff;
    }
    .introjs-prevbutton {
        background: #2468f2;
        color: #fff;
    }
    .introjs-tooltipbuttons {
        border: none;
    }
    .introjs-hidden {
        display: none !important;
    }
}

div#driver-page-overlay {
    background: #fff;
    z-index: 370000 !important;
    color: #2468f2;
}
div#driver-highlighted-element-stage {
    z-index: 370001 !important;
    opacity: 0;
}
div#driver-popover-item {
    background-color: #2468f2;
    color: #fff;
    .driver-popover-description {
        color: #fff;
    }
    .driver-popover-tip {
        border: 5px solid #2468f2;
    }
    .driver-popover-tip.left {
        border-color: transparent #2468f2 transparent transparent !important;
    }
    .driver-close-btn {
        background-color: transparent !important;
        position: absolute;
        top: 4px;
        right: 4px;
        color: #fff !important;
        border: none !important;
        text-shadow: none !important;
        font-size: 20px !important;
        margin-top: 0 !important;
    }
    .driver-popover-description {
        font-size: 12px;
    }
}
.assist-driver {
    button {
        float: right;
        margin-top: 24px;
        display: block;
        padding: 8px;
        border-radius: 4px;
        border: 1px solid #2468f2;
        color: #2468f2;
        cursor: pointer;
        &:active {
            opacity: 0.5;
        }
    }
}
.s-daterangepicker .s-trigger-container .s-popover .s-popover-body .s-popover-content {
    width: max-content;
    padding: 8px 12px;
    .s-daterangepicker-popup {
        width: max-content;
        .s-picker-shortcut {
            padding-top: 0px;
        }
    }
    .s-daterangepicker-popup-begin {
        margin: 0px;
        .s-daterangepicker-popup-dates {
            padding-left: 0px;
        }
    }
}
.tag-edit-panel {
    width: 100%;
}

.s-search-box .s-cascader-column {
    max-height: 300px;
    overflow-y: scroll;
}

.tag-dialog {
    .tag-dialog-custom {
        .s-dialog-wrapper {
            .s-dialog-content {
                .tag-edit-panel {
                    width: 100%;
                }
            }
        }
    }
}

.assist-tip-form {
    margin-top: 9px;
    color: #84868c;
}

.non-nav-page-content-container {
    .instance-not-found-class {
        height: 100%;
    }
}

.bui-layer {
    .bui-select-item {
        &:hover {
            color: #2468f2;
        }
    }
    .bui-select-item-disabled {
        &:hover {
            color: #ccc;
        }
    }
}

.acud-table-wrapper {
    .acud-loading-loading-context {
        background-color: transparent !important;
    }
}
.satisfaction-path {
    align-items: flex-start;
    background: #eef3fe;
    border-radius: 4px;
    display: flex;
    height: auto;
    margin: 0 0 16px 0;
    padding: 8px 0;
    .satisfaction-text {
        font-size: 12px;
        color: #151b26;
        font-weight: 400;
        margin-left: 12px;
        margin-right: 12px;
        height: 22px;
        line-height: 22px;
        display: inline-block;
    }
    .like-dislike-widget {
        margin-top: 0px;
        display: flex;
        align-items: center;
        .common {
            background: none;
            border: none;
            width: auto;
            height: 22px;
            line-height: 22px;
            img {
                position: relative;
                top: 0px;
            }
        }
        .dislike {
            margin-left: 8px;
        }
    }
    .suggest-no-satisfied {
        margin-top: 0px;
    }
    .suggest-submit {
        margin-top: 10px;
    }
    .suggest-widget-inner .suggest-no-satisfied .suggest-tag-item-selected {
        background: #ffffff !important;
        border: 1px solid #2468f2 !important;
    }
    .suggest-tag-item {
        background-color: #ffffff !important;
        border-radius: 4px;
        color: #151b26;
        cursor: pointer;
        display: inline-block;
        font-size: 12px;
        padding: 6px 8px;
    }
    .suggest-widget-inner .suggest-no-satisfied .suggest-textarea {
        margin-top: 8px;
    }
    .suggest-widget-inner .suggest-no-satisfied .suggest-tag {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        justify-content: flex-start;
        margin-top: 12px;
    }
    .like-dislike-widget .common:hover {
        background-color: inherit !important;
    }
    .suggest-textarea {
        width: 396px;
    }
    .satisfaction-class {
        min-height: 100px;
    }
}

.diagnosis-header {
    .diagnosis-header-button {
        position: relative;
        top: 2px;
    }
}

.diagnosis-content-custom .detail-part-item {
    width: auto !important;
}

.diagnosis-content-custom .diagnosis-header {
    width: 860px;
    justify-content: space-between;
}

.diagnosis-content-custom .diagnosis-content-top {
    width: 872px;
}

.diagnosis-content-custom .satisfaction-diagnose {
    align-items: flex-start;
    background: #eef3fe;
    border-radius: 4px;
    display: flex;
    height: auto;
    margin: 8px 16px 12px;
    padding: 8px 0;
    width: 838px;
    .suggest-widget-inner .like-dislike-widget {
        display: flex;
        margin-top: 0px;
    }
    .suggest-widget-inner .like-dislike-widget .common {
        background: none;
        border: none;
        height: 22px;
        line-height: 22px;
        width: auto;
    }
    .suggest-widget-inner .suggest-no-satisfied {
        margin-top: 12px;
    }
    .suggest-widget-inner .suggest-submit {
        margin-top: 12px;
    }
}

.diagnosis-content-custom {
    .diagnosis-header .detail-part-id {
        width: 200px !important;
    }
}

.diagnosis-content-custom-path .diagnosis-header {
    width: auto !important;
}

.diagnosis-content-custom .satisfaction-diagnose .like-dislike-widget .common img {
    top: 0px;
}

.auth {
    width: 100%;
    height: 100%;
    background: #f7f7f9 !important;
    border: 1px solid transparent;
    box-sizing: border-box;
    .bui-biz-order
        .bui-biz-order-container
        .bui-biz-order-side
        .bui-biz-order-side-content
        .bui-biz-order-relation
        .bui-biz-order-relation-item
        .bui-biz-order-content {
        height: auto;
        .bui-biz-order-relation-desc {
            line-height: 20px;
        }
    }
    .auth-link {
        color: #333;
        &:hover {
            color: #2468f2;
        }
    }
    .auth-title {
        background: #ffffff;
        width: 100%;
        height: 48px;
        padding-top: 12px;
        padding-bottom: 12px;
        padding-left: 16px;
        font-size: 16px;
        color: #151b26;
        line-height: 24px;
        font-weight: 500;
    }
    .s-new-app-order-page {
        margin: unset;
    }
    .s-new-app-order-page-info-title {
        margin-bottom: 31px;
    }
    .s-new-app-order-page-info-tip {
        display: none;
    }
    .s-new-app-order-page-info-desc {
        margin-bottom: 111px;
        font-size: 12px;
        color: #303540;
        line-height: 22px;
        font-weight: 400;
    }
    .s-new-app-order-page-process-step-index {
        font-weight: 400;
    }
    .s-new-app-order-page-process-step-title {
        line-height: 20px;
    }
    .s-new-app-order-page-process-step-desc {
        margin-top: 15px;
        font-weight: 400;
    }
}
