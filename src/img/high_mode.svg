<?xml version="1.0" encoding="UTF-8"?>
<svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>高可靠模式</title>
    <defs>
        <linearGradient x1="32.2421843%" y1="50%" x2="90.9300262%" y2="88.306196%" id="linearGradient-1">
            <stop stop-color="#2468F2" offset="0%"></stop>
            <stop stop-color="#2468F2" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="68.854825%" y1="89.8712268%" x2="30.9177691%" y2="12.5969681%" id="linearGradient-2">
            <stop stop-color="#6497FF" stop-opacity="0.388300542" offset="0%"></stop>
            <stop stop-color="#99C5FF" stop-opacity="0.466629562" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="56.6052157%" y1="10.1622453%" x2="56.6052157%" y2="110.474856%" id="linearGradient-3">
            <stop stop-color="#327EFF" stop-opacity="0.0530707086" offset="0%"></stop>
            <stop stop-color="#F8FBFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="25.4550916%" y1="0%" x2="96.2356573%" y2="119.554832%" id="linearGradient-4">
            <stop stop-color="#DDFEEB" offset="0%"></stop>
            <stop stop-color="#FFF3F3" offset="43.7663899%"></stop>
            <stop stop-color="#CCE0FF" offset="100%"></stop>
        </linearGradient>
        <filter x="inf%" y="inf%" width="30.0%" height="30.0%" filterUnits="objectBoundingBox" id="filter-5">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.11372549   0 0 0 0 0.349019608   0 0 0 0 0.82745098  0 0 0 0.56 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="26.5243663%" y1="0%" x2="94.2214466%" y2="119.554832%" id="linearGradient-6">
            <stop stop-color="#DDFEEB" offset="0%"></stop>
            <stop stop-color="#FFF3F3" offset="43.7663899%"></stop>
            <stop stop-color="#CCE0FF" offset="100%"></stop>
        </linearGradient>
        <path d="M18.3130334,10.95 C18.5211056,10.95 18.6897815,11.1186759 18.6897815,11.3267482 L18.6897815,14.0738702 C18.6897815,14.2819425 18.5211056,14.4506184 18.3130334,14.4506184 L14.37225,14.45 L14.3727674,15.4441266 C14.5449108,15.540985 14.676009,15.7021868 14.7334478,15.8951178 L15.3302604,15.895003 C16.6728889,15.895003 17.7754817,16.9227184 17.8942873,18.2343978 L18.5092564,18.2342736 C18.7173286,18.2342736 18.8860045,18.4029495 18.8860045,18.6110218 L18.8860045,20.5732518 C18.8860045,20.7813241 18.7173286,20.95 18.5092564,20.95 L16.5470263,20.95 C16.3389541,20.95 16.1702782,20.7813241 16.1702782,20.5732518 L16.1702782,18.6110218 C16.1702782,18.4029495 16.3389541,18.2342736 16.5470263,18.2342736 L17.1362656,18.2338172 C17.0206287,17.3394329 16.2561327,16.6484993 15.3302604,16.6484993 L14.5493537,16.6493728 C14.497603,16.7029335 14.4381453,16.7489999 14.3727674,16.7857855 L14.37225,18.234 L14.9772423,18.2342736 C15.1853146,18.2342736 15.3539905,18.4029495 15.3539905,18.6110218 L15.3539905,20.5732518 C15.3539905,20.7813241 15.1853146,20.95 14.9772423,20.95 L13.0150122,20.95 C12.80694,20.95 12.6382641,20.7813241 12.6382641,20.5732518 L12.6382641,18.6110218 C12.6382641,18.4029495 12.80694,18.2342736 13.0150122,18.2342736 L13.61925,18.234 L13.6194872,16.7857855 C13.5541092,16.7489999 13.4946516,16.7029335 13.4429008,16.6493728 L12.5160455,16.6484993 C11.6209622,16.6484993 10.8898409,17.3505042 10.8432224,18.2339733 L11.4452282,18.2342736 C11.6533005,18.2342736 11.8219764,18.4029495 11.8219764,18.6110218 L11.8219764,20.5732518 C11.8219764,20.7813241 11.6533005,20.95 11.4452282,20.95 L9.48299817,20.95 C9.2749259,20.95 9.10625,20.7813241 9.10625,20.5732518 L9.10625,18.6110218 C9.10625,18.4029495 9.2749259,18.2342736 9.48299817,18.2342736 L10.09025,18.234 L10.092531,18.1639971 C10.17476,16.8971115 11.2283752,15.895003 12.5160455,15.895003 L13.2588068,15.8951178 C13.3162455,15.7021868 13.4473437,15.540985 13.6194872,15.4441266 L13.61925,14.45 L9.67922117,14.4506184 C9.4711489,14.4506184 9.302473,14.2819425 9.302473,14.0738702 L9.302473,11.3267482 C9.302473,11.1186759 9.4711489,10.95 9.67922117,10.95 L18.3130334,10.95 Z M14.5998662,18.98777 L13.3917604,18.98777 L13.3917604,20.1958757 L14.5998662,20.1958757 L14.5998662,18.98777 Z M18.1318803,18.98777 L16.9237745,18.98777 L16.9237745,20.1958757 L18.1318803,20.1958757 L18.1318803,18.98777 Z M11.0678521,18.98777 L9.85974634,18.98777 L9.85974634,20.1958757 L11.0678521,20.1958757 L11.0678521,18.98777 Z M17.9362852,11.7034963 L10.0559693,11.7034963 L10.0559693,13.6964941 L17.9362852,13.6964941 L17.9362852,11.7034963 Z M12.4263432,12.323561 C12.6344155,12.323561 12.8030914,12.4922369 12.8030914,12.7003092 C12.8030914,12.9083815 12.6344155,13.0770574 12.4263432,13.0770574 L11.6414512,13.0770574 C11.4333789,13.0770574 11.264703,12.9083815 11.264703,12.7003092 C11.264703,12.4922369 11.4333789,12.323561 11.6414512,12.323561 L12.4263432,12.323561 Z" id="path-7"></path>
        <filter x="-35.8%" y="-25.0%" width="171.6%" height="170.0%" filterUnits="objectBoundingBox" id="filter-8">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.11372549   0 0 0 0 0.349019608   0 0 0 0 0.82745098  0 0 0 0.56 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="设计方案" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="专线支持高可靠模式购买" transform="translate(-320, -4974)">
            <g id="编组-15备份-12" transform="translate(118, 4705)">
                <g id="编组-7" transform="translate(1, 163)">
                    <g id="编组-10" transform="translate(181, 77)">
                        <g id="高可靠模式" transform="translate(20, 29)">
                            <rect id="矩形" x="0" y="0" width="32" height="32"></rect>
                            <g id="编组-8" transform="translate(2.9999, 3.5)">
                                <g id="编组-7" transform="translate(0, 0)">
                                    <path d="M25.5399068,6.94780059 C25.2698057,6.71788856 24.9152981,6.59472141 24.5439091,6.59472141 C24.5101465,6.59472141 24.4763838,6.59472141 24.4426212,6.59882698 C24.4215195,6.60293255 24.1894014,6.61524927 23.8180124,6.61524927 C23.210285,6.61524927 22.0243725,6.57419355 20.8975445,6.32785924 C19.4457512,6.00762463 17.7660602,4.51730205 17.2807223,4.22170088 C17.0359432,4.07390029 16.7531811,4 16.4704191,4 C16.187657,4 15.9048949,4.07390029 15.6601158,4.22170088 C15.6010312,4.25865103 13.8453742,5.95014663 12.1277002,6.32785924 C11.0050925,6.57419355 9.79385803,6.61524927 9.1861306,6.61524927 C8.81896195,6.61524927 8.58684383,6.60293255 8.56152186,6.59882698 C8.52775922,6.59472141 8.49821692,6.59472141 8.46445428,6.59472141 C8.0930653,6.59472141 7.73855764,6.72199413 7.46423623,6.94780059 C7.16881317,7.1941349 7,7.5431085 7,7.9085044 L7,11.1970674 C7,23.3536657 15.8373696,24.9178886 16.2087586,24.9794721 C16.2931652,24.9917889 16.3817921,25 16.4661987,25 C16.5506053,25 16.6392322,24.9917889 16.7236388,24.9794721 C17.0992481,24.9178886 26,23.3495601 26,11.1970674 L26,7.9085044 C26.004143,7.5431085 25.8353299,7.1941349 25.5399068,6.94780059 Z" id="形状备份-5" fill="url(#linearGradient-1)"></path>
                                    <path d="M10.9657484,0.25 C11.2470503,0.25 11.5285399,0.325103714 11.7713116,0.475814767 C11.8962408,0.554045561 12.0914753,0.705563655 12.3384288,0.896108774 C13.2165566,1.57365733 14.7091311,2.71401896 16.0370714,3.01517597 C17.362972,3.31318535 18.758371,3.36339198 19.4734881,3.36339198 C19.835696,3.36339198 20.0904382,3.35147477 20.1949681,3.34387016 C20.6722386,3.33960513 21.0309298,3.45964585 21.3045456,3.69908595 C21.5890145,3.94296075 21.7540124,4.28776991 21.75,4.65298143 L21.75,8.56793744 C21.75,13.4261131 20.5778253,16.8472147 19.0067651,19.2471073 C16.0630129,23.7438727 11.7281392,24.6370451 11.2471851,24.7237941 C10.4999175,24.6565464 6.13016346,23.8194756 3.13452168,19.4539385 C1.48177712,17.0454002 0.25,13.5678403 0.25,8.56793744 L0.25,4.65298143 C0.25,4.28962465 0.414950097,3.94367453 0.699039352,3.70011851 C0.97036199,3.47049608 1.32053956,3.3408773 1.68746001,3.33897585 C1.83432807,3.34894357 2.10622946,3.36339198 2.53130912,3.36339198 C3.24638777,3.36339198 4.67149704,3.31312396 5.99246694,3.01510663 C7.80384568,2.60558049 9.65594557,0.939148708 10.1000444,0.534359876 C10.4099863,0.326548971 10.6873243,0.25 10.9657484,0.25 Z" id="形状备份-6" stroke="url(#linearGradient-3)" stroke-width="0.5" fill="url(#linearGradient-2)"></path>
                                </g>
                            </g>
                            <g id="CDN访问日志" filter="url(#filter-5)" transform="translate(9, 11)"></g>
                            <g id="形状结合" fill-rule="nonzero">
                                <use fill="black" fill-opacity="1" filter="url(#filter-8)" xlink:href="#path-7"></use>
                                <use fill="url(#linearGradient-6)" xlink:href="#path-7"></use>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>