<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>配额管理</title>
    <g id="图标" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="概览页" transform="translate(-1551, -1375)">
            <g id="配额管理" transform="translate(1551, 1375)">
                <rect id="矩形" x="0" y="0" width="20" height="20"></rect>
                <g id="编组-8" transform="translate(2.5, 2.5965)">
                    <rect id="矩形" stroke="#151B26" x="0" y="0" width="15" height="14.8070438" rx="0.200000003"></rect>
                    <g id="编组-6" transform="translate(7.5, 4.5753) scale(-1, 1) translate(-7.5, -4.5753)translate(2, 3.1343)">
                        <line x1="1" y1="1.44102394" x2="1.95245899" y2="1.44102394" id="路径-12备份-4" stroke="#151B26" stroke-linecap="round"></line>
                        <line x1="4.35554216" y1="1.44102394" x2="10.2578998" y2="1.44102394" id="路径-12备份-5" stroke="#151B26" stroke-linecap="round"></line>
                        <circle id="椭圆形备份-3" stroke="#070304" cx="3.06145313" cy="1.44102394" r="1"></circle>
                    </g>
                    <g id="编组-6" transform="translate(2, 8.7907)">
                        <line x1="1" y1="1.44102394" x2="1.95245899" y2="1.44102394" id="路径-12备份-4" stroke="#151B26" stroke-linecap="round"></line>
                        <line x1="4.35554216" y1="1.44102394" x2="10.2578998" y2="1.44102394" id="路径-12备份-5" stroke="#151B26" stroke-linecap="round"></line>
                        <circle id="椭圆形备份-3" stroke="#070304" cx="3.06145313" cy="1.44102394" r="1"></circle>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>