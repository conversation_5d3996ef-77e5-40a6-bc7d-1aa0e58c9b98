export interface GlobalContext {
    commonWhite: Record<string, boolean>;
    vpnWhite: boolean;
    endPointWhite: boolean;
    enicWhite: boolean;
    peerConnSts: boolean;
    vpnSts: boolean;
    natSts: boolean;
    blbSts: boolean;
    etSts: boolean;
    csnSts: boolean;
    natIntroShow: boolean;
    l2gwSupportRegion: boolean;
}

export enum ActionType {
    SET_INIT_STATE = 'SET_INIT_STATE',
    UPDATE_NAT_STS = 'UPDATE_NAT_STS',
    UPDATE_VPN_STS = 'UPDATE_VPN_STS',
    UPDATE_PEERCONN_STS = 'UPDATE_PEERCONN_STS',
    UPDATE_DC_STS = 'UPDATE_DC_STS',
    UPDATE_WHITE_LIST = 'UPDATE_WHITE_LIST'
}
export interface Action {
    type: ActionType;
    payload?: Partial<GlobalContext>;
}

export type NeedActiveAuthSts = 'peerConnSts' | 'vpnSts' | 'natSts';

// 页面待替换的节点，以及触发节点替换动作的api、code映射关系
export interface IPageApiConfig {
    selector: string;
    backUrl: string;
    isSanPage?: boolean;
    apiCodeMap: Array<{
        path: string;
        code: string;
    }>;
}

export interface ClosurePageConfig {
    region: string;
    hash: string;
}
