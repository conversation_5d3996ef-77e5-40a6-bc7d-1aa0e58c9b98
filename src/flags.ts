// 此文件为功能清单文件，请勿删除
export default sessionStorage.getItem('_flags')
    ? JSON.parse(sessionStorage.getItem('_flags') || '{}')
    : {
          NetworkSupportDcgw: true,
          NetworkSupportTag: true,
          NetworkSubnetSupportPage: true,
          NetworkSecuritySupLead: true,
          NetworkSecuritySupCorssCopy: true,
          NetworkSnicSupAllVpc: true,
          NetworkAclOpt: true,
          NetworkRouteOpt: true,
          NetworkIpv6Opt: true,
          NetworkEniOpt: true,
          NetworkNatOpt: true,
          NetworkSupportEip: true,
          NetworkSupportRDS: true,
          NetworkSupportPeerconn: true,
          NetworkSupportResource: true,
          NetworkSupportFlowlog: true,
          NetworkSupportQos: true,
          NetworkSupportBCC: true,
          NetworkSupportBBC: true,
          NetworkSupportBLB: true,
          NetworkSupportSCS: true,
          NetworkSupportRabbitMQ: true,
          NetworkSupportEipGroup: true,
          NetworkSupportEni: true,
          NetworkSupportBCI: true,
          NetworkSupportEnterpriseSecurity: true,
          NetworkSupportDCC: true,
          NetworkSupportNat: true,
          NetworkSupportEnhanceVpn: true,
          NetworkSupportVpn: true,
          NetworkSupportIPv6: true,
          NetworkSupportPrepay: true,
          NetworkSupportPostpay: true,
          NetworkSupportHaVip: true,
          NetworkSupportAI: true,
          NetworkSupportEndPoint: true
      }
